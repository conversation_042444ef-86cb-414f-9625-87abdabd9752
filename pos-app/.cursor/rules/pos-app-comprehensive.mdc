---
description: 
globs: 
alwaysApply: true
---
# POS App Development Rules

## Project Overview
This is a React Native Point of Sale (POS) application built with TypeScript, Redux Saga, and Realm database. The app handles transactions, printing, customer management, product management, and integrates with various payment systems and external services.

## Architecture & Directory Structure

### Core Directories
- `ts/` - Main TypeScript source directory
- `ts/components/` - Reusable UI components organized by feature
- `ts/containers/` - Screen components and page layouts
- `ts/navigation/` - Navigation configuration and routing
- `ts/actions/` - Redux actions (including `http/` subdirectory for API actions)
- `ts/reducers/` - Redux reducers
- `ts/sagas/` - Redux-Saga business logic
- `ts/dal/` - Data Access Layer with Realm database schemas
- `ts/constants/` - Application constants including themes and configurations
- `ts/utils/` - Utility functions and helpers
- `ts/hooks/` - Custom React hooks
- `ts/models/` - TypeScript interfaces and data models
- `ts/typings/` - Type definitions
- `ts/config/` - Environment and app configuration

### Component Organization
- Components are organized by feature/domain (e.g., `register/`, `customer/`, `products/`, `checkout/`)
- Each component folder contains related components and may have an `index.ts` for exports
- Common reusable components are in `ts/components/common/`
- UI primitives and icons are in `ts/components/ui/`

## Code Style & Structure

### TypeScript Usage
- Use strict TypeScript with interfaces for all props and state
- Prefer interfaces over types for object definitions
- Use `React.FC` for functional components with props
- Enable strict typing in tsconfig.json (noImplicitAny: false is currently set but should move toward strict typing)
- Use proper typing for Redux actions, reducers, and sagas

### Component Patterns
- **Functional Components**: Use arrow functions with proper TypeScript interfaces
- **Class Components**: Legacy components use PureComponent pattern (existing codebase pattern)
- **Component Structure**: Always separate component logic from styles
- **File Naming**: Use PascalCase for component files (e.g., `Register.tsx`, `CustomerList.tsx`)
- **Directory Naming**: Use camelCase for directories (e.g., `customerManagement/`, `productCatalog/`)

### Styling Conventions
- **No Inline Styles**: Always use `StyleSheet.create()` or separate style files
- **Style Files**: Create separate `styles.ts` files in component directories
- **Responsive Design**: Use scaling functions from `constants/themes.ts`:
  - `scaleSizeW()` for width scaling
  - `scaleSizeH()` for height scaling  
  - `setSpText()` for text size scaling
- **Theme System**: Use `CommonColors` and `SharedStyles` from `constants/themes.ts`
- **Consistent Spacing**: Use theme-based spacing and sizing functions

### State Management (Redux Saga)
- **Actions**: Use `redux-actions` with `createAction` helper
- **Action Types**: Define proper TypeScript interfaces for action payloads
- **Sagas**: Handle all async operations and side effects in sagas
- **Selectors**: Use `reselect` for memoized selectors (see `sagas/selector/`)
- **Immutable State**: Use Immutable.js for state management
- **Action Organization**: Group related actions in feature-based files

### Database (Realm)
- **Schemas**: Define Realm schemas in `ts/dal/schema/`
- **DAL Pattern**: Use Data Access Layer pattern with centralized database operations
- **Transactions**: Handle database transactions properly for data consistency

### Navigation
- **React Navigation**: Use React Navigation v6 patterns
- **Navigation Structure**: Organize navigation in `ts/navigation/`
- **Deep Linking**: Support deep linking for various app flows
- **Navigation Props**: Properly type navigation props in components

## Specific Patterns & Conventions

### Component Props & Interfaces
```typescript
interface Props {
  // Redux props
  transactionSession?: TransactionTypeWithDisplay;
  currentEmployeeId?: string;

  // Navigation props
  navigation: any; // TODO: Type properly with navigation types

  // Component props
  onPress?: () => void;
  disabled?: boolean;

  // Actions (from Redux)
  actions: {
    clearTransactionSession(): void;
    addPurchasedItem(payload: AddPurchasedItemType): void;
  };
}
```

### Redux Action Patterns
```typescript
export type AddPurchasedItemType = {
  product: ProductType;
  quantity: number;
  modifiers?: ModifierType[];
};

export const addPurchasedItem = createAction<AddPurchasedItemType>('addPurchasedItem');
```

### Login Credentials

### Component Export Patterns
- Use named exports in index files: `export { default as ComponentName } from './ComponentName';`
- Export types and interfaces alongside components
- Group related exports in feature-based index files

### Error Handling
- Use proper error boundaries for React components
- Handle async errors in Redux sagas
- Implement user-friendly error messages and fallbacks
- Use toast notifications for user feedback (`toggleToastInfo` action)

### Performance Considerations
- Use `PureComponent` or `React.memo` for optimization
- Implement proper list virtualization for large datasets
- Use throttling for user interactions (see `throttle` usage in Register component)
- Optimize images and assets (PNG compression, SVG usage)

### Testing Patterns
- Use Jest for unit testing
- Test Redux actions, reducers, and sagas
- Use React Native Testing Library for component testing
- Mock external dependencies and native modules

### Internationalization
- Use `i18n-js` for internationalization
- Store translations in `constants/localization/`
- Use `t()` function for translated strings

### Platform-Specific Code
- Use `Platform.select()` for platform-specific styling
- Handle iOS and Android differences appropriately
- Use platform-specific constants (`IsIOS`, `IsAndroid`)

## Business Logic Patterns

### Transaction Management
- Handle complex transaction flows with Redux Saga
- Support various payment methods and integrations
- Implement proper transaction state management
- Handle offline/online synchronization

### Printing & Hardware Integration
- Support multiple printer types and configurations
- Handle kitchen display systems (KDS)
- Implement receipt and kitchen docket printing
- Support external display integration

### Customer & Loyalty Management
- Integrate with customer databases and loyalty systems
- Handle customer QR code scanning
- Support various customer identification methods

### Inventory & Product Management
- Handle product catalogs and variations
- Support barcode scanning for products
- Implement stock level management
- Handle product modifiers and customizations

## Development Workflow

### Code Quality
- Use ESLint with TypeScript rules
- Use Prettier for code formatting
- Implement proper Git hooks with Husky
- Follow consistent import ordering and organization

### Environment Management
- Use `react-native-config` for environment variables
- Support multiple environments (FAT, PRO, etc.)
- Handle different API endpoints and configurations

### Build & Deployment
- Support both iOS and Android builds
- Use Fastlane for automated deployment
- Implement proper code signing and certificates
- Support over-the-air updates with CodePush

## Security & Best Practices

### Data Security
- Handle sensitive customer and transaction data properly
- Implement proper authentication and authorization
- Use secure storage for sensitive information
- Handle PCI compliance requirements

### Performance Monitoring
- Integrate with Sentry for error tracking
- Use Mixpanel for analytics
- Monitor app performance and crashes
- Implement proper logging and debugging

### Accessibility
- Implement proper accessibility labels and hints
- Support screen readers and accessibility tools
- Ensure proper color contrast and text sizing
- Handle keyboard navigation appropriately

## Integration Patterns

### External Services
- Handle various payment gateway integrations
- Support mall integration systems
- Integrate with loyalty and customer management systems
- Handle printer and hardware device communications

### API Communication
- Use GraphQL for API communication
- Implement proper error handling and retry logic
- Handle offline scenarios and data synchronization
- Use proper authentication tokens and security headers

This rule should be followed for all development work on the POS application to ensure consistency, maintainability, and adherence to established patterns.

### Login Credentials
- Here's a table of login business and password and sign in PIN.
- Use `kafe123` with `1111` as PIN by default.
- Use specific account based on its comment if needed. 

| Business Name   | Email                                                                           | Password    | PIN  | Comment               |
| --------------- | ------------------------------------------------------------------------------- | ----------- | ---- | --------------------- |
| stephen666      | [<EMAIL>](mdc:mailto:<EMAIL>)                   | 111111      | 2222 |                       |
| kamdar          | [<EMAIL>](mdc:mailto:<EMAIL>)                                 | 123456      | 1111 |                       |
| justcoffee      | [<EMAIL>](mdc:mailto:<EMAIL>)               | 123456      | 2222 |                       |
| feida           | [<EMAIL>](mdc:mailto:<EMAIL>)                                     | 123456      | 1111 |                       |
| ancafe          | [<EMAIL>](mdc:mailto:<EMAIL>)             | 123456      | 1234 |                       |
| kafe123         | [<EMAIL>](mdc:mailto:<EMAIL>)                   | 111111      | 1111 |                       |
| cathy6          | [<EMAIL>](mdc:mailto:<EMAIL>)                           | 123456      | 6721 |                       |
| leonphtestfood  | [<EMAIL>](mdc:mailto:<EMAIL>)                   | 111111      | 1111 | BIR, updated by Grace |
| thteststore     | [<EMAIL>](mdc:mailto:<EMAIL>)                   | Test123     | 3333 | TH                    |
| myteststore     | [<EMAIL>](mdc:mailto:<EMAIL>)                   | Test123     | 1111 |                       |
| birph           | [<EMAIL>](mdc:mailto:<EMAIL>)                 | test123123  | 1111 | FDI                   |
| chunyuanth      | [<EMAIL>](mdc:mailto:<EMAIL>)         | Ccy\@123456 |      |                       |
| onlytestaccount | [<EMAIL>](mdc:mailto:<EMAIL>)                   | test123123  |      |                       |
| kdstest         | [<EMAIL>](mdc:mailto:<EMAIL>)                 | 123456      | 1111 |                       |
| cafe95425676    | [<EMAIL>](mdc:mailto:<EMAIL>) | Divya123    | 2998 |                       |
| productstore    | [<EMAIL>](mdc:mailto:<EMAIL>)                             | 123456      | 1282 |                       |


