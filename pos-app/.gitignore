# OSX
#
.DS_Store

# Xcode
#
build/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.ipa
*.xcuserstate
project.xcworkspace
ios/.xcode.env.local

# Android/IntelliJ
#
build/
.idea
.gradle
local.properties
*.iml
bin/
*.settings
*.apk

# node.js
#
node_modules/
npm-debug.log
yarn-error.log

# BUCK
buck-out/
\.buckd/
*.keystore

# fastlane
#
# It is recommended to not store the screenshots in the git repo. Instead, use fastlane to re-generate the
# screenshots whenever they are needed.
# For more information about the recommended setup visit:
# https://docs.fastlane.tools/best-practices/source-control/

*/fastlane/report.xml
*/fastlane/Preview.html
*/fastlane/screenshots

# Bundle artifact
*.jsbundle

# vs code
.nyc_output/

# crashlytics
*.dSYM.zip

# ts compiled files
src/*

# auto generated file
rn-cli-config-with-links.js

# ignore npm lock file
package-lock.json

# pod
ios/bundles/
ios/Pods/
.bundle/

# dot env file
.env
.env.production
.env.pro
.env.fat
.env.staging

# fastlane
fastlane/report.xml

# tmp
tmp/

# google-service.json
android/app/src/pro/google-services.json

# jest test tmp files
.jest/
coverage/

# test tmp database
default.realm 
default.realm.lock 
default.realm.management/


.vscode/
.cursor
.sentry/
bundle/
.cursor

# Pushy
.update
.pushy/

