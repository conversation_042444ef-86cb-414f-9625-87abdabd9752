# RN POS

[RN POS](https://github.com/storehubnet/pos-v3-mobile) is one of core projects of [our company](https://www.storehub.com/). It covers most of the company's business. It is closely related to [Back Office](https://justcoffee.backoffice.test17.shub.us/login#/?_k=yalm4k) and [BEEP](https://justcoffee.beep.test12.shub.us/dine). The transactions, printer, Beep order, payment, products managemet, employee management and store management are the main features in [RN POS](https://github.com/storehubnet/pos-v3-mobile).

The project is builded by [React Native](https://reactnative.dev/), you should firstly follow the [document ](https://reactnative.dev/docs/0.66/environment-setup) to set up [Android](https://developer.android.com/) or IOS development environment in your computer.

- [yarn](https://yarnpkg.com/) we used this package manager for our code.
- [Node >=12](https://nodejs.org/en/) JavaScript runtime.
- [JDK8](https://docs.oracle.com/javase/8/) Java Platform.
- [Visual Studio Code](https://code.visualstudio.com/) source code editor.

RN POS works on macOS, Windows, and Linux.

If something doesn't work, you should check your System environment variable firstly. Sometime you also can [file an issue](https://github.com/storehubnet/pos-v3-mobile/issues/new) or ask other players for help in workplace.

## Quick Start

1. `yarn install` the node js dependencies

2. `yarn compile` the TypeScript source code to JavaScript source code.

3. **`create .env`** the project configuration. You can get this from [Apollo](https://apollo.shub.us/config.html#/appid=apk-rnpos&env=FAT&cluster=default) and don't forget to change the host name of API_URL and CASH_BACK_URL to your storename. `config/index` too.

4. **`configure Gemini AI`** (optional) - Add your Google Gemini API key to the .env file:
   ```
   GEMINI_API_KEY=your_google_gemini_api_key_here
   ```
   Get your API key from [Google AI Studio](https://makersuite.google.com/app/apikey). If not configured, the AI chat feature will use fallback intent detection.

5. `yarn start` start the package server

**Run Android**

1. **`copy RNPOS.keystore RN-POS/pos-app/android/app/`** to add the Android App's codesign Keystore.You can get it from [Jenkins](https://jenkins.shub.us/job/mobile/job/Android/job/apk-rnpos-dev/ws/pos-app/android/app/) or ask other players .

2. use Android Studio open `pos-app/android/app/build.gradle`

3. `adb reverse tcp:8081 tcp:8081` reverse phone port to host port. metro (development server) 8081

4. Click the Run button to launch the APP.

**Run IOS**

1. `cd RN-POS/pos-app/ios`
2. `pod install`
3. Use XCode to open the iOS workspace, Click the Run button to launch the iOS APP.

**VSCode Plugins**

- TypeScript Hero. This plugin is for TS import formatting.

- TSLint. This plugin is for tslint.

- ESLint.

- Prettier - Code formtter. Select prettier as default code formatter tool.

```json
// settings.json
"editor.codeActionsOnSave": { "source.fixAll.eslint": true },
"typescriptHero.imports.stringQuoteStyle": "'",
```

Also can follow [here](https://storehub.atlassian.net/wiki/spaces/TS/pages/*********/React+Native+POS+Introduction) as a supplement.

You can get [test account](https://docs.google.com/spreadsheets/d/1fLVCwo-zlDXW-1Up5ajgmJIaNK7BQQy4Tem9C0x1T7Y/edit#gid=0) or create a new account in RN POS.

## AI Chat Feature

The POS app includes an AI-powered chat assistant that helps users navigate the app and manage orders. The AI service is powered by Google Gemini and requires a valid API key to function.

### Features
- **Smart Action Execution**: AI executes clear commands directly (e.g., "go to settings", "check printers")
- **Contextual Clarification**: Shows action buttons only when user intent is ambiguous or multiple options are available
- **Navigation assistance**: Ask the AI to navigate to different sections (e.g., "go to products", "show me transactions")
- **Order management**: Add items to orders using natural language (e.g., "add coffee", "order sandwich")
- **System monitoring**: Check printer status, network connectivity, and POS system information
- **Help and guidance**: Get help with POS system features
- **Intelligent intent detection**: The AI understands user intent and provides relevant suggestions

### How It Works

#### Direct Action Execution
When your intent is clear and specific, the AI executes actions immediately:
- **"Navigate to settings"** → AI directly navigates to settings
- **"Check printer status"** → AI immediately displays printer information
- **"Go to products"** → AI navigates to product catalog
- **"Add coffee to order"** → AI adds coffee to the current order

#### Clarification Buttons
When your request is ambiguous or the AI wants to offer multiple options, action buttons appear:
- **"Navigate"** → Shows buttons for Products, Transactions, Customers, Settings
- **"Check system"** → Shows buttons for Printers, Network, POS Status
- **"Help"** → Shows buttons for different help topics
- **"Order"** → Shows buttons for common order actions

#### Button Interaction
Action buttons send specific commands back to the AI for processing:
- Clicking "Products" button sends "Navigate to Products" to the AI
- Clicking "Check Printers" button sends "Check printer status" to the AI
- This ensures consistent AI processing and response formatting

### Interactive Action Buttons

The AI assistant provides contextual action buttons below responses when clarification is needed:

#### Navigation Clarification
When you say "navigate" without specifying where:
- **Products**: Navigate to product catalog
- **Transactions**: View transaction history  
- **Customers**: Access customer management
- **Settings**: Go to system settings

#### System Status Clarification
When you say "check system" without specifying what:
- **Check Printers**: View printer status and connectivity
- **Check Network**: Check network connectivity and details
- **Check POS Status**: Display POS system information

#### Order Management Clarification
When you say "order" without specifying what:
- **Add Coffee**: Quick add popular item
- **View Cart**: Display current order items
- **Checkout**: Start the payment process

#### Help Topic Clarification
When you say "help" without specifying topic:
- **Navigation Help**: Assistance with app navigation
- **Order Help**: Help with order management
- **System Help**: Troubleshooting system issues
- **Contact Support**: Get support contact information

### Real-time System Integration

The AI assistant has access to live system information through agent functions:
- **Printer Status**: Real-time printer connectivity and error states
- **Network Information**: Current network status, IP address, and WiFi details
- **POS System Data**: Register ID, business information, and store settings
- **Store Details**: Complete store configuration and settings

### Configuration
1. Get your Google Gemini API key from [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Add the API key to your `.env` file:
   ```
   GEMINI_API_KEY=your_actual_api_key_here
   ```
3. **Important**: The API key is required. The service will throw an error if no API key is provided.

### Usage Examples

**Clear Commands (Direct Execution):**
- "Go to settings" → Immediately navigates to settings
- "Check printer status" → Displays printer information
- "Navigate to products" → Goes to product catalog
- "Add coffee to order" → Adds coffee to current order
- "Show network status" → Displays network information

**Ambiguous Commands (Shows Clarification Buttons):**
- "Navigate" → Shows navigation options
- "Check system" → Shows system status options
- "Help" → Shows help topic options
- "Order something" → Shows order action options

**System Status Queries:**
- "How many printers are online?" → Direct status display
- "What's my network status?" → Direct network information
- "What's my register ID?" → Direct POS information

### Implementation Details

- **Service**: `ts/services/geminiAIService.ts` - Main AI service with agent functions and smart action execution
- **Actions**: `ts/actions/aiChat.ts` - Redux actions for chat and action buttons with send_message support
- **Saga**: `ts/sagas/aiChatSaga.ts` - Business logic for AI responses, direct action handling, and button interactions
- **Components**: `ts/components/aiChat/` - UI components for chat interface
- **Reducer**: `ts/reducers/aiChat.ts` - State management for chat functionality

## Catalogue

    ├── Gemfile
    ├── Gemfile.lock
    ├── README.md
    ├── __tests__ # unit test cases
    ├── android # Android native codebase
    ├── app.json # configuration for rn project.
    ├── assets # assets
    ├── babel.config.js
    ├── fastlane . # CI scripts
    ├── index.js # app entry file
    ├── ios # iOS native codebase
    ├── jest
    ├── libs # react-native modules
    ├── metro.config.js
    ├── package.json # package.json
    ├── scripts
    ├── sonar-project.properties
    |	├── ts
    |	├── actions # redux actions, we define actions here
    |	├── components # components, our shared components are here
    |	├── config # configuration, we init our env paramaters here
    |	├── constants # constant values, some constants values
    |	├── containers # containers for pages, our main pages are here
    |	├── dal # realm database schemas, our database schemas are here
    |	├── navigation # navigations, our routs are here
    |	├── reducers # redux reducers, we define reducers here
    |	├── reduxStore # redux store configuration
    |	├── sagas # sagas, our core bussiness logic is here
    |	├── services # external services integration (AI, APIs, etc.)
    |	├── typings # typings
    |	└── utils # utils
    ├── tsconfig.json # TypeScript configuration
    ├── tslint.json
    └── yarn.lock

## Libraries

- [React Native v0.66](https://reactnative.dev/docs/0.66/getting-started) the framework we used to create cross-platform application.
- [React v17](https://17.reactjs.org/) the library we used to build user interfaces.
- [ReactNavigation](https://reactnavigation.org/) navigation solution.
- [Immutable](https://immutable-js.com/) redux state type.
- [Lodash](https://lodash.com/) utils library.
- [Realm](https://www.mongodb.com/docs/realm/sdk/react-native/) local database.
- [Reactotron](https://github.com/infinitered/reactotron) debug tool.
- [Jest](https://jestjs.io/) test framework.
- [Redux](https://www.redux.org.cn/) state centralized management.
- [Redux-Saga](https://redux-saga.js.org/) Redux side effect manager.
- [Google Generative AI](https://www.npmjs.com/package/@google/generative-ai) Google Gemini AI integration.

You can find all libraries used in [here](https://drive.google.com/file/d/1amUA-2vdL4aORRPDDCp7bexCUdRlURq4/view?usp=sharing).

## Learning Guideline

You can find detailed instructions and many advices in this [guideline ](https://storehub.atlassian.net/wiki/spaces/TS/pages/2141094248/React+Native+POS+Learning+Guideline).

**Fast Refresh**

can add `"compile-dev": "tsc -w || echo ''"` as scripts in package.json.

when you modify ts files it will compile into js and refresh your application directly.

maybe you also need to read [this offical document](https://reactnative.dev/docs/0.66/fast-refresh).

**Debug**

We use [Reactotron](https://github.com/infinitered/reactotron) as our debug tool. It can view application state, show API requests && responses, subscribe to parts of application state, display log messages, track sagas and so on.

when application is started, follow this:

1. `adb reverse tcp:9090 tcp:9090`
2. reload application

otherwise, you can also follow [here](https://reactnative.dev/docs/0.66/debugging) as a supplement.

**Optimization**

1. put asset resources(png, svg..) to `/assets`, png and svg can be compressed automatically
2. put FAT pages to `ts/components/FAT` and import them by inline require because they are not included in PRO JS bundle
3. png compression, https://tinypng.com/
4. you can use svg directly. svg compression, https://www.svgviewer.dev/

**Test**

We use [Jest](https://jestjs.io/) to test some cases.

You can follow [here](https://storehub.atlassian.net/wiki/spaces/TS/pages/183009425/React+Native+Unit+Test) , to learn some basic rules or norms.

## Business Tips

Some [articles](https://storehub.atlassian.net/wiki/spaces/TS/pages/971440226/Business) about RN POS Business.

Some [charts](https://drive.google.com/file/d/1-Y_DsqJj3HXsnstkeFWEmAJnE-I2Uu91/view?usp=sharing) also can be finded.

## FAQ

You can find some frequently asked questions and [make a comment](https://storehub.atlassian.net/wiki/spaces/TS/pages/2145059213/React+Native+POS+FAQ) in confluence.

## Credits

This project contributed by [core mobile](https://github.com/orgs/storehubnet/teams/mobile) in [storehub](https://github.com/storehubnet).
