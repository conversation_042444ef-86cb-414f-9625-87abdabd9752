import { render } from '@testing-library/react-native';
import { fromJS } from 'immutable';
import React from 'react';
import { Provider } from 'react-redux';
import configureMockStore from 'redux-mock-store';
import { act } from 'react-test-renderer';
import Transactions from '../../../ts/containers/Transactions/Transactions';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import DAL from '../../../ts/dal';

jest.mock('../../../ts/dal', () => {
  return {
    searchTrxBykeyword: jest.fn().mockImplementation(() => {
      return {
        appVersion: '2.44.2.2',
        cancelledAt: null,
        cancelledBy: null,
        comment: null,
        createdDate: '2023-01-19T10:24:07.372Z',
        customerId: null,
        loyaltyEarned: null,
        depositAmount: null,
        discount: 0,
        employeeId: '6269003c60e98900077a01c2',
        headcount: null,
        invoiceSeqNumber: null,
        isDeleted: false,
        isCancelled: false,
        isCompleted: true,
        isOpen: null,
        isOriginalOnline: null,
        lastSyncTime: null,
        manuallyRemovedServiceCharge: null,
        modifiedDate: '2023-01-19T10:24:07.372Z',
        originalReceiptNumber: null,
        pax: null,
        pickUpDate: null,
        shiftId: '25d2f9b2-e102-47a8-bdc9-14c790ac318c',
        shiftIdOfPreOrder: null,
        shiftIdOfCancel: null,
        preOrderBy: null,
        preOrderDate: null,
        preOrderId: null,
        pwdCount: null,
        pwdDiscount: 0,
        receiptNumber: '00923011918240710',
        registerId: '6316bd2c9df848000765c279',
        returnReason: null,
        returnStatus: null,
        roundedAmount: 0,
        seniorDiscount: 0,
        seniorsCount: null,
        sequenceNumber: null,
        serviceCharge: 141.8,
        serviceChargeTax: 0,
        serviceChargeRate: 0.02,
        serviceChargeTaxId: '',
        shippingType: null,
        subtotal: 7090,
        tax: 0,
        taxableSales: 0,
        taxExemptedSales: 0,
        total: 7231.8,
        totalDeductedTax: 0,
        transactionId: '18a44350-f18d-4d3e-9f4c-2f5d7760241b',
        transactionType: 'Sale',
        uploadedDate: '2023-01-19T10:24:08.199Z',
        zeroRatedSales: 0,
        items: [
          {
            adhocDiscount: 0,
            discount: 0,
            id: null,
            submitId: null,
            itemType: 'ServiceCharge',
            notes: null,
            options: null,
            orderingValue: 0,
            originalItemId: null,
            productId: null,
            pwdDiscount: 0,
            quantity: 1,
            seniorDiscount: 0,
            sn: null,
            subTotal: 141.8,
            tax: 0,
            taxableAmount: 0,
            taxCode: '',
            taxRate: 0,
            taxExemptAmount: 0,
            title: null,
            total: 141.8,
            unitPrice: 141.8,
            discountInputValue: null,
            discountType: null,
            promotions: [],
            itemChannel: null,
            takeawayCharges: null,
            owner: [' Circular Reference '],
          },
          {
            adhocDiscount: 0,
            discount: 0,
            id: '1:634a260d6137ba0007c7c447',
            submitId: null,
            itemType: null,
            notes: null,
            options: '[]',
            orderingValue: 1,
            originalItemId: null,
            productId: '634a260d6137ba0007c7c447',
            pwdDiscount: 0,
            quantity: 2,
            seniorDiscount: 0,
            sn: null,
            subTotal: 6424,
            tax: 0,
            taxableAmount: 0,
            taxCode: '6269003c60e98900077a01cb',
            taxRate: 0,
            taxExemptAmount: 0,
            title: 'kitchenA',
            total: 6424,
            unitPrice: 3212,
            discountInputValue: null,
            discountType: null,
            promotions: [],
            itemChannel: 1,
            takeawayCharges: null,
            owner: [' Circular Reference '],
          },
          {
            adhocDiscount: 0,
            discount: 0,
            id: '2:62fc54d6b4e3850007a8c683',
            submitId: null,
            itemType: null,
            notes: null,
            options: '[]',
            orderingValue: 2,
            originalItemId: null,
            productId: '62fc54d6b4e3850007a8c683',
            pwdDiscount: 0,
            quantity: 2,
            seniorDiscount: 0,
            sn: null,
            subTotal: 666,
            tax: 0,
            taxableAmount: 0,
            taxCode: '6269003c60e98900077a01cb',
            taxRate: 0,
            taxExemptAmount: 0,
            title: 'kitchenD',
            total: 666,
            unitPrice: 333,
            discountInputValue: null,
            discountType: null,
            promotions: [],
            itemChannel: 1,
            takeawayCharges: null,
            owner: [' Circular Reference '],
          },
        ],
        payments: [
          {
            amount: 7231.8,
            cashTendered: 7231.8,
            isDeposit: false,
            isOnline: null,
            isVoided: null,
            mPOSTxnId: null,
            onlinePaymentMethod: null,
            paymentMethodId: 0,
            roundedAmount: 0,
            subType: null,
            type: 'Cash',
            manualApproveInfo: null,
            owner: [' Circular Reference '],
          },
        ],
        promotions: [],
        loyaltyDiscounts: [],
        otherReason: null,
        pickUpId: '1005',
        tableId: null,
        takeawayCharges: 0,
        takeawayCharge: 0,
        salesChannel: 1,
        isPayByCash: null,
        isPayLater: null,
        isOnlineOrder: null,
        mrs: null,
      };
    }),
    getProductById: jest.fn(),
    getEmployeeById: jest.fn().mockImplementation(() => {
      return {
        employeeId: '6269003c60e98900077a01c2',
        assignedStores: null,
        email: '<EMAIL>',
        firstName: 'feida',
        lastName: 'zhang',
        isManager: true,
        pin: '1111',
        modifiedTime: '2022-05-19T10:50:16.785Z',
        isCashier: true,
        backOfficeAccess: true,
        limitBackOfficeAccess: false,
        backOfficeDetailAccesses: [],
      };
    }),
    getTransactionListByPage: jest.fn().mockImplementation(() => {
      return [
        {
          appVersion: '2.44.2.2',
          cancelledAt: null,
          cancelledBy: null,
          comment: null,
          createdDate: '2023-01-19T10:24:07.372Z',
          customerId: null,
          loyaltyEarned: null,
          depositAmount: null,
          discount: 0,
          employeeId: '6269003c60e98900077a01c2',
          headcount: null,
          invoiceSeqNumber: null,
          isDeleted: false,
          isCancelled: false,
          isCompleted: true,
          isOpen: null,
          isOriginalOnline: null,
          lastSyncTime: null,
          manuallyRemovedServiceCharge: null,
          modifiedDate: '2023-01-19T10:24:07.372Z',
          originalReceiptNumber: null,
          pax: null,
          pickUpDate: null,
          shiftId: '25d2f9b2-e102-47a8-bdc9-14c790ac318c',
          shiftIdOfPreOrder: null,
          shiftIdOfCancel: null,
          preOrderBy: null,
          preOrderDate: null,
          preOrderId: null,
          pwdCount: null,
          pwdDiscount: 0,
          receiptNumber: '00923011918240710',
          registerId: '6316bd2c9df848000765c279',
          returnReason: null,
          returnStatus: null,
          roundedAmount: 0,
          seniorDiscount: 0,
          seniorsCount: null,
          sequenceNumber: null,
          serviceCharge: 141.8,
          serviceChargeTax: 0,
          serviceChargeRate: 0.02,
          serviceChargeTaxId: '',
          shippingType: null,
          subtotal: 7090,
          tax: 0,
          taxableSales: 0,
          taxExemptedSales: 0,
          total: 7231.8,
          totalDeductedTax: 0,
          transactionId: '18a44350-f18d-4d3e-9f4c-2f5d7760241b',
          transactionType: 'Sale',
          uploadedDate: '2023-01-19T10:24:08.199Z',
          zeroRatedSales: 0,
          items: [
            {
              adhocDiscount: 0,
              discount: 0,
              id: null,
              submitId: null,
              itemType: 'ServiceCharge',
              notes: null,
              options: null,
              orderingValue: 0,
              originalItemId: null,
              productId: null,
              pwdDiscount: 0,
              quantity: 1,
              seniorDiscount: 0,
              sn: null,
              subTotal: 141.8,
              tax: 0,
              taxableAmount: 0,
              taxCode: '',
              taxRate: 0,
              taxExemptAmount: 0,
              title: null,
              total: 141.8,
              unitPrice: 141.8,
              discountInputValue: null,
              discountType: null,
              promotions: [],
              itemChannel: null,
              takeawayCharges: null,
              owner: [' Circular Reference '],
            },
            {
              adhocDiscount: 0,
              discount: 0,
              id: '1:634a260d6137ba0007c7c447',
              submitId: null,
              itemType: null,
              notes: null,
              options: '[]',
              orderingValue: 1,
              originalItemId: null,
              productId: '634a260d6137ba0007c7c447',
              pwdDiscount: 0,
              quantity: 2,
              seniorDiscount: 0,
              sn: null,
              subTotal: 6424,
              tax: 0,
              taxableAmount: 0,
              taxCode: '6269003c60e98900077a01cb',
              taxRate: 0,
              taxExemptAmount: 0,
              title: 'kitchenA',
              total: 6424,
              unitPrice: 3212,
              discountInputValue: null,
              discountType: null,
              promotions: [],
              itemChannel: 1,
              takeawayCharges: null,
              owner: [' Circular Reference '],
            },
            {
              adhocDiscount: 0,
              discount: 0,
              id: '2:62fc54d6b4e3850007a8c683',
              submitId: null,
              itemType: null,
              notes: null,
              options: '[]',
              orderingValue: 2,
              originalItemId: null,
              productId: '62fc54d6b4e3850007a8c683',
              pwdDiscount: 0,
              quantity: 2,
              seniorDiscount: 0,
              sn: null,
              subTotal: 666,
              tax: 0,
              taxableAmount: 0,
              taxCode: '6269003c60e98900077a01cb',
              taxRate: 0,
              taxExemptAmount: 0,
              title: 'kitchenD',
              total: 666,
              unitPrice: 333,
              discountInputValue: null,
              discountType: null,
              promotions: [],
              itemChannel: 1,
              takeawayCharges: null,
              owner: [' Circular Reference '],
            },
          ],
          payments: [
            {
              amount: 7231.8,
              cashTendered: 7231.8,
              isDeposit: false,
              isOnline: null,
              isVoided: null,
              mPOSTxnId: null,
              onlinePaymentMethod: null,
              paymentMethodId: 0,
              roundedAmount: 0,
              subType: null,
              type: 'Cash',
              manualApproveInfo: null,
              owner: [' Circular Reference '],
            },
          ],
          promotions: [],
          loyaltyDiscounts: [],
          otherReason: null,
          pickUpId: '1005',
          tableId: null,
          takeawayCharges: 0,
          takeawayCharge: 0,
          salesChannel: 1,
          isPayByCash: null,
          isPayLater: null,
          isOnlineOrder: null,
          mrs: null,
        },
        {
          appVersion: '2.44.2.2',
          cancelledAt: null,
          cancelledBy: null,
          comment: null,
          createdDate: '2023-01-19T08:59:47.959Z',
          customerId: '25b6d819-2c8e-44ee-921e-794899f2b459',
          loyaltyEarned: 180.79,
          depositAmount: null,
          discount: 0,
          employeeId: '6269003c60e98900077a01c2',
          headcount: null,
          invoiceSeqNumber: null,
          isDeleted: false,
          isCancelled: false,
          isCompleted: true,
          isOpen: null,
          isOriginalOnline: null,
          lastSyncTime: null,
          manuallyRemovedServiceCharge: null,
          modifiedDate: '2023-01-19T08:59:47.959Z',
          originalReceiptNumber: null,
          pax: null,
          pickUpDate: null,
          shiftId: '25d2f9b2-e102-47a8-bdc9-14c790ac318c',
          shiftIdOfPreOrder: null,
          shiftIdOfCancel: null,
          preOrderBy: null,
          preOrderDate: null,
          preOrderId: null,
          pwdCount: null,
          pwdDiscount: 0,
          receiptNumber: '0092301191659472',
          registerId: '6316bd2c9df848000765c279',
          returnReason: null,
          returnStatus: null,
          roundedAmount: 0,
          seniorDiscount: 0,
          seniorsCount: null,
          sequenceNumber: null,
          serviceCharge: 70.9,
          serviceChargeTax: 0,
          serviceChargeRate: 0.02,
          serviceChargeTaxId: '',
          shippingType: null,
          subtotal: 3545,
          tax: 0,
          taxableSales: 0,
          taxExemptedSales: 0,
          total: 3615.9,
          totalDeductedTax: 0,
          transactionId: 'd3066dcb-7634-4d94-aadf-4643db5b00f0',
          transactionType: 'Sale',
          uploadedDate: '2023-01-19T08:59:48.797Z',
          zeroRatedSales: 0,
          items: [
            {
              adhocDiscount: 0,
              discount: 0,
              id: null,
              submitId: null,
              itemType: 'ServiceCharge',
              notes: null,
              options: null,
              orderingValue: 0,
              originalItemId: null,
              productId: null,
              pwdDiscount: 0,
              quantity: 1,
              seniorDiscount: 0,
              sn: null,
              subTotal: 70.9,
              tax: 0,
              taxableAmount: 0,
              taxCode: '',
              taxRate: 0,
              taxExemptAmount: 0,
              title: null,
              total: 70.9,
              unitPrice: 70.9,
              discountInputValue: null,
              discountType: null,
              promotions: [],
              itemChannel: null,
              takeawayCharges: null,
              owner: [' Circular Reference '],
            },
            {
              adhocDiscount: 0,
              discount: 0,
              id: '1:634a260d6137ba0007c7c447',
              submitId: null,
              itemType: null,
              notes: null,
              options: '[]',
              orderingValue: 1,
              originalItemId: null,
              productId: '634a260d6137ba0007c7c447',
              pwdDiscount: 0,
              quantity: 1,
              seniorDiscount: 0,
              sn: null,
              subTotal: 3212,
              tax: 0,
              taxableAmount: 0,
              taxCode: '6269003c60e98900077a01cb',
              taxRate: 0,
              taxExemptAmount: 0,
              title: 'kitchenA',
              total: 3212,
              unitPrice: 3212,
              discountInputValue: null,
              discountType: null,
              promotions: [],
              itemChannel: 1,
              takeawayCharges: null,
              owner: [' Circular Reference '],
            },
            {
              adhocDiscount: 0,
              discount: 0,
              id: '2:62fc54d6b4e3850007a8c683',
              submitId: null,
              itemType: null,
              notes: null,
              options: '[]',
              orderingValue: 2,
              originalItemId: null,
              productId: '62fc54d6b4e3850007a8c683',
              pwdDiscount: 0,
              quantity: 1,
              seniorDiscount: 0,
              sn: null,
              subTotal: 333,
              tax: 0,
              taxableAmount: 0,
              taxCode: '6269003c60e98900077a01cb',
              taxRate: 0,
              taxExemptAmount: 0,
              title: 'kitchenD',
              total: 333,
              unitPrice: 333,
              discountInputValue: null,
              discountType: null,
              promotions: [],
              itemChannel: 1,
              takeawayCharges: null,
              owner: [' Circular Reference '],
            },
          ],
          payments: [
            {
              amount: 3615.9,
              cashTendered: 3615.9,
              isDeposit: false,
              isOnline: null,
              isVoided: null,
              mPOSTxnId: null,
              onlinePaymentMethod: null,
              paymentMethodId: 0,
              roundedAmount: 0,
              subType: null,
              type: 'Cash',
              manualApproveInfo: null,
              owner: [' Circular Reference '],
            },
          ],
          promotions: [],
          loyaltyDiscounts: [],
          otherReason: null,
          pickUpId: '1004',
          tableId: null,
          takeawayCharges: 0,
          takeawayCharge: 0,
          salesChannel: 1,
          isPayByCash: null,
          isPayLater: null,
          isOnlineOrder: null,
          mrs: null,
        },
        {
          appVersion: '2.44.2.2',
          cancelledAt: null,
          cancelledBy: null,
          comment: null,
          createdDate: '2023-01-19T08:50:56.416Z',
          customerId: null,
          loyaltyEarned: null,
          depositAmount: null,
          discount: 0,
          employeeId: '6269003c60e98900077a01c2',
          headcount: null,
          invoiceSeqNumber: null,
          isDeleted: false,
          isCancelled: false,
          isCompleted: true,
          isOpen: null,
          isOriginalOnline: null,
          lastSyncTime: null,
          manuallyRemovedServiceCharge: null,
          modifiedDate: '2023-01-19T08:50:56.416Z',
          originalReceiptNumber: null,
          pax: null,
          pickUpDate: null,
          shiftId: '25d2f9b2-e102-47a8-bdc9-14c790ac318c',
          shiftIdOfPreOrder: null,
          shiftIdOfCancel: null,
          preOrderBy: null,
          preOrderDate: null,
          preOrderId: null,
          pwdCount: null,
          pwdDiscount: 0,
          receiptNumber: '0092301191650566',
          registerId: '6316bd2c9df848000765c279',
          returnReason: null,
          returnStatus: null,
          roundedAmount: 0,
          seniorDiscount: 0,
          seniorsCount: null,
          sequenceNumber: null,
          serviceCharge: 70.9,
          serviceChargeTax: 0,
          serviceChargeRate: 0.02,
          serviceChargeTaxId: '',
          shippingType: null,
          subtotal: 3545,
          tax: 0,
          taxableSales: 0,
          taxExemptedSales: 0,
          total: 3615.9,
          totalDeductedTax: 0,
          transactionId: 'ae288d36-b34b-4872-8f3c-0d8595aff56a',
          transactionType: 'Sale',
          uploadedDate: '2023-01-19T08:50:57.186Z',
          zeroRatedSales: 0,
          items: [
            {
              adhocDiscount: 0,
              discount: 0,
              id: null,
              submitId: null,
              itemType: 'ServiceCharge',
              notes: null,
              options: null,
              orderingValue: 0,
              originalItemId: null,
              productId: null,
              pwdDiscount: 0,
              quantity: 1,
              seniorDiscount: 0,
              sn: null,
              subTotal: 70.9,
              tax: 0,
              taxableAmount: 0,
              taxCode: '',
              taxRate: 0,
              taxExemptAmount: 0,
              title: null,
              total: 70.9,
              unitPrice: 70.9,
              discountInputValue: null,
              discountType: null,
              promotions: [],
              itemChannel: null,
              takeawayCharges: null,
              owner: [' Circular Reference '],
            },
            {
              adhocDiscount: 0,
              discount: 0,
              id: '1:634a260d6137ba0007c7c447',
              submitId: null,
              itemType: null,
              notes: null,
              options: '[]',
              orderingValue: 1,
              originalItemId: null,
              productId: '634a260d6137ba0007c7c447',
              pwdDiscount: 0,
              quantity: 1,
              seniorDiscount: 0,
              sn: null,
              subTotal: 3212,
              tax: 0,
              taxableAmount: 0,
              taxCode: '6269003c60e98900077a01cb',
              taxRate: 0,
              taxExemptAmount: 0,
              title: 'kitchenA',
              total: 3212,
              unitPrice: 3212,
              discountInputValue: null,
              discountType: null,
              promotions: [],
              itemChannel: 1,
              takeawayCharges: null,
              owner: [' Circular Reference '],
            },
            {
              adhocDiscount: 0,
              discount: 0,
              id: '2:62fc54d6b4e3850007a8c683',
              submitId: null,
              itemType: null,
              notes: null,
              options: '[]',
              orderingValue: 2,
              originalItemId: null,
              productId: '62fc54d6b4e3850007a8c683',
              pwdDiscount: 0,
              quantity: 1,
              seniorDiscount: 0,
              sn: null,
              subTotal: 333,
              tax: 0,
              taxableAmount: 0,
              taxCode: '6269003c60e98900077a01cb',
              taxRate: 0,
              taxExemptAmount: 0,
              title: 'kitchenD',
              total: 333,
              unitPrice: 333,
              discountInputValue: null,
              discountType: null,
              promotions: [],
              itemChannel: 1,
              takeawayCharges: null,
              owner: [' Circular Reference '],
            },
          ],
          payments: [
            {
              amount: 3615.9,
              cashTendered: 3615.9,
              isDeposit: false,
              isOnline: null,
              isVoided: null,
              mPOSTxnId: null,
              onlinePaymentMethod: null,
              paymentMethodId: 0,
              roundedAmount: 0,
              subType: null,
              type: 'Cash',
              manualApproveInfo: null,
              owner: [' Circular Reference '],
            },
          ],
          promotions: [],
          loyaltyDiscounts: [],
          otherReason: null,
          pickUpId: '1003',
          tableId: null,
          takeawayCharges: 0,
          takeawayCharge: 0,
          salesChannel: 1,
          isPayByCash: null,
          isPayLater: null,
          isOnlineOrder: null,
          mrs: null,
        },
        {
          appVersion: '2.44.2.2',
          cancelledAt: '2023-01-19T06:34:35.641Z',
          cancelledBy: '6269003c60e98900077a01c2',
          comment: null,
          createdDate: '2023-01-19T06:34:21.610Z',
          customerId: null,
          loyaltyEarned: null,
          depositAmount: null,
          discount: 0,
          employeeId: '6269003c60e98900077a01c2',
          headcount: null,
          invoiceSeqNumber: null,
          isDeleted: false,
          isCancelled: true,
          isCompleted: true,
          isOpen: null,
          isOriginalOnline: null,
          lastSyncTime: null,
          manuallyRemovedServiceCharge: null,
          modifiedDate: '2023-01-19T06:34:35.641Z',
          originalReceiptNumber: null,
          pax: null,
          pickUpDate: null,
          shiftId: '25d2f9b2-e102-47a8-bdc9-14c790ac318c',
          shiftIdOfPreOrder: null,
          shiftIdOfCancel: '25d2f9b2-e102-47a8-bdc9-14c790ac318c',
          preOrderBy: null,
          preOrderDate: null,
          preOrderId: null,
          pwdCount: null,
          pwdDiscount: 0,
          receiptNumber: '0092301191434215',
          registerId: '6316bd2c9df848000765c279',
          returnReason: 'Incorrect payment type',
          returnStatus: null,
          roundedAmount: 0,
          seniorDiscount: 0,
          seniorsCount: null,
          sequenceNumber: null,
          serviceCharge: 70.9,
          serviceChargeTax: 0,
          serviceChargeRate: 0.02,
          serviceChargeTaxId: '',
          shippingType: null,
          subtotal: 3545,
          tax: 0,
          taxableSales: 0,
          taxExemptedSales: 0,
          total: 3615.9,
          totalDeductedTax: 0,
          transactionId: 'c91d1efe-1ae5-430b-aefb-1b275c73e6ad',
          transactionType: 'Sale',
          uploadedDate: '2023-01-19T06:34:36.445Z',
          zeroRatedSales: 0,
          items: [
            {
              adhocDiscount: 0,
              discount: 0,
              id: null,
              submitId: null,
              itemType: 'ServiceCharge',
              notes: null,
              options: null,
              orderingValue: 0,
              originalItemId: null,
              productId: null,
              pwdDiscount: 0,
              quantity: 1,
              seniorDiscount: 0,
              sn: null,
              subTotal: 70.9,
              tax: 0,
              taxableAmount: 0,
              taxCode: '',
              taxRate: 0,
              taxExemptAmount: 0,
              title: null,
              total: 70.9,
              unitPrice: 70.9,
              discountInputValue: null,
              discountType: null,
              promotions: [],
              itemChannel: null,
              takeawayCharges: null,
              owner: [' Circular Reference '],
            },
            {
              adhocDiscount: 0,
              discount: 0,
              id: '1:634a260d6137ba0007c7c447',
              submitId: null,
              itemType: null,
              notes: null,
              options: '[]',
              orderingValue: 1,
              originalItemId: null,
              productId: '634a260d6137ba0007c7c447',
              pwdDiscount: 0,
              quantity: 1,
              seniorDiscount: 0,
              sn: null,
              subTotal: 3212,
              tax: 0,
              taxableAmount: 0,
              taxCode: '6269003c60e98900077a01cb',
              taxRate: 0,
              taxExemptAmount: 0,
              title: 'kitchenA',
              total: 3212,
              unitPrice: 3212,
              discountInputValue: null,
              discountType: null,
              promotions: [],
              itemChannel: 1,
              takeawayCharges: null,
              owner: [' Circular Reference '],
            },
            {
              adhocDiscount: 0,
              discount: 0,
              id: '2:62fc54d6b4e3850007a8c683',
              submitId: null,
              itemType: null,
              notes: null,
              options: '[]',
              orderingValue: 2,
              originalItemId: null,
              productId: '62fc54d6b4e3850007a8c683',
              pwdDiscount: 0,
              quantity: 1,
              seniorDiscount: 0,
              sn: null,
              subTotal: 333,
              tax: 0,
              taxableAmount: 0,
              taxCode: '6269003c60e98900077a01cb',
              taxRate: 0,
              taxExemptAmount: 0,
              title: 'kitchenD',
              total: 333,
              unitPrice: 333,
              discountInputValue: null,
              discountType: null,
              promotions: [],
              itemChannel: 1,
              takeawayCharges: null,
              owner: [' Circular Reference '],
            },
          ],
          payments: [
            {
              amount: 3615.9,
              cashTendered: 3615.9,
              isDeposit: false,
              isOnline: null,
              isVoided: null,
              mPOSTxnId: null,
              onlinePaymentMethod: null,
              paymentMethodId: 0,
              roundedAmount: 0,
              subType: null,
              type: 'Cash',
              manualApproveInfo: null,
              owner: [' Circular Reference '],
            },
          ],
          promotions: [],
          loyaltyDiscounts: [],
          otherReason: 'Incorrect payment type',
          pickUpId: '1002',
          tableId: null,
          takeawayCharges: 0,
          takeawayCharge: 0,
          salesChannel: 1,
          isPayByCash: null,
          isPayLater: null,
          isOnlineOrder: null,
          mrs: null,
        },
        {
          appVersion: '2.44.2.2',
          cancelledAt: '2023-01-19T06:32:35.618Z',
          cancelledBy: '6269003c60e98900077a01c2',
          comment: null,
          createdDate: '2023-01-19T06:32:21.978Z',
          customerId: null,
          loyaltyEarned: null,
          depositAmount: null,
          discount: 0,
          employeeId: '6269003c60e98900077a01c2',
          headcount: null,
          invoiceSeqNumber: null,
          isDeleted: false,
          isCancelled: true,
          isCompleted: true,
          isOpen: null,
          isOriginalOnline: null,
          lastSyncTime: null,
          manuallyRemovedServiceCharge: null,
          modifiedDate: '2023-01-19T06:32:35.618Z',
          originalReceiptNumber: null,
          pax: null,
          pickUpDate: null,
          shiftId: '25d2f9b2-e102-47a8-bdc9-14c790ac318c',
          shiftIdOfPreOrder: null,
          shiftIdOfCancel: '25d2f9b2-e102-47a8-bdc9-14c790ac318c',
          preOrderBy: null,
          preOrderDate: null,
          preOrderId: null,
          pwdCount: null,
          pwdDiscount: 0,
          receiptNumber: '0092301191432215',
          registerId: '6316bd2c9df848000765c279',
          returnReason: 'Incorrect payment type',
          returnStatus: null,
          roundedAmount: 0,
          seniorDiscount: 0,
          seniorsCount: null,
          sequenceNumber: null,
          serviceCharge: 77.56,
          serviceChargeTax: 0,
          serviceChargeRate: 0.02,
          serviceChargeTaxId: '',
          shippingType: null,
          subtotal: 3878,
          tax: 0,
          taxableSales: 0,
          taxExemptedSales: 0,
          total: 3955.56,
          totalDeductedTax: 0,
          transactionId: '1188747d-a91f-44a9-ad5a-b733dac3f93c',
          transactionType: 'Sale',
          uploadedDate: '2023-01-19T06:32:36.483Z',
          zeroRatedSales: 0,
          items: [
            {
              adhocDiscount: 0,
              discount: 0,
              id: null,
              submitId: null,
              itemType: 'ServiceCharge',
              notes: null,
              options: null,
              orderingValue: 0,
              originalItemId: null,
              productId: null,
              pwdDiscount: 0,
              quantity: 1,
              seniorDiscount: 0,
              sn: null,
              subTotal: 77.56,
              tax: 0,
              taxableAmount: 0,
              taxCode: '',
              taxRate: 0,
              taxExemptAmount: 0,
              title: null,
              total: 77.56,
              unitPrice: 77.56,
              discountInputValue: null,
              discountType: null,
              promotions: [],
              itemChannel: null,
              takeawayCharges: null,
              owner: [' Circular Reference '],
            },
            {
              adhocDiscount: 0,
              discount: 0,
              id: '1:634a260d6137ba0007c7c447',
              submitId: null,
              itemType: null,
              notes: null,
              options: '[]',
              orderingValue: 1,
              originalItemId: null,
              productId: '634a260d6137ba0007c7c447',
              pwdDiscount: 0,
              quantity: 1,
              seniorDiscount: 0,
              sn: null,
              subTotal: 3212,
              tax: 0,
              taxableAmount: 0,
              taxCode: '6269003c60e98900077a01cb',
              taxRate: 0,
              taxExemptAmount: 0,
              title: 'kitchenA',
              total: 3212,
              unitPrice: 3212,
              discountInputValue: null,
              discountType: null,
              promotions: [],
              itemChannel: 1,
              takeawayCharges: null,
              owner: [' Circular Reference '],
            },
            {
              adhocDiscount: 0,
              discount: 0,
              id: '2:62fc54d6b4e3850007a8c683',
              submitId: null,
              itemType: null,
              notes: null,
              options: '[]',
              orderingValue: 2,
              originalItemId: null,
              productId: '62fc54d6b4e3850007a8c683',
              pwdDiscount: 0,
              quantity: 2,
              seniorDiscount: 0,
              sn: null,
              subTotal: 666,
              tax: 0,
              taxableAmount: 0,
              taxCode: '6269003c60e98900077a01cb',
              taxRate: 0,
              taxExemptAmount: 0,
              title: 'kitchenD',
              total: 666,
              unitPrice: 333,
              discountInputValue: null,
              discountType: null,
              promotions: [],
              itemChannel: 1,
              takeawayCharges: null,
              owner: [' Circular Reference '],
            },
          ],
          payments: [
            {
              amount: 3955.56,
              cashTendered: 3955.56,
              isDeposit: false,
              isOnline: null,
              isVoided: null,
              mPOSTxnId: null,
              onlinePaymentMethod: null,
              paymentMethodId: 0,
              roundedAmount: 0,
              subType: null,
              type: 'Cash',
              manualApproveInfo: null,
              owner: [' Circular Reference '],
            },
          ],
          promotions: [],
          loyaltyDiscounts: [],
          otherReason: 'Incorrect payment type',
          pickUpId: '1001',
          tableId: null,
          takeawayCharges: 0,
          takeawayCharge: 0,
          salesChannel: 1,
          isPayByCash: null,
          isPayLater: null,
          isOnlineOrder: null,
          mrs: null,
        },
        {
          appVersion: '2.44.2.2',
          cancelledAt: '2023-01-19T06:25:46.212Z',
          cancelledBy: '6269003c60e98900077a01c2',
          comment: null,
          createdDate: '2023-01-19T06:25:25.576Z',
          customerId: null,
          loyaltyEarned: null,
          depositAmount: null,
          discount: 0,
          employeeId: '6269003c60e98900077a01c2',
          headcount: null,
          invoiceSeqNumber: null,
          isDeleted: false,
          isCancelled: true,
          isCompleted: true,
          isOpen: null,
          isOriginalOnline: null,
          lastSyncTime: null,
          manuallyRemovedServiceCharge: null,
          modifiedDate: '2023-01-19T06:25:46.212Z',
          originalReceiptNumber: null,
          pax: null,
          pickUpDate: null,
          shiftId: '25d2f9b2-e102-47a8-bdc9-14c790ac318c',
          shiftIdOfPreOrder: null,
          shiftIdOfCancel: '25d2f9b2-e102-47a8-bdc9-14c790ac318c',
          preOrderBy: null,
          preOrderDate: null,
          preOrderId: null,
          pwdCount: null,
          pwdDiscount: 0,
          receiptNumber: '0092301191425259',
          registerId: '6316bd2c9df848000765c279',
          returnReason: 'Incorrect payment type',
          returnStatus: null,
          roundedAmount: 0,
          seniorDiscount: 0,
          seniorsCount: null,
          sequenceNumber: null,
          serviceCharge: 70.9,
          serviceChargeTax: 0,
          serviceChargeRate: 0.02,
          serviceChargeTaxId: '',
          shippingType: null,
          subtotal: 3545,
          tax: 0,
          taxableSales: 0,
          taxExemptedSales: 0,
          total: 3615.9,
          totalDeductedTax: 0,
          transactionId: '8a03c649-f256-4683-a078-7d7d90483480',
          transactionType: 'Sale',
          uploadedDate: '2023-01-19T06:25:47.047Z',
          zeroRatedSales: 0,
          items: [
            {
              adhocDiscount: 0,
              discount: 0,
              id: null,
              submitId: null,
              itemType: 'ServiceCharge',
              notes: null,
              options: null,
              orderingValue: 0,
              originalItemId: null,
              productId: null,
              pwdDiscount: 0,
              quantity: 1,
              seniorDiscount: 0,
              sn: null,
              subTotal: 70.9,
              tax: 0,
              taxableAmount: 0,
              taxCode: '',
              taxRate: 0,
              taxExemptAmount: 0,
              title: null,
              total: 70.9,
              unitPrice: 70.9,
              discountInputValue: null,
              discountType: null,
              promotions: [],
              itemChannel: null,
              takeawayCharges: null,
              owner: [' Circular Reference '],
            },
            {
              adhocDiscount: 0,
              discount: 0,
              id: '1:634a260d6137ba0007c7c447',
              submitId: null,
              itemType: null,
              notes: null,
              options: '[]',
              orderingValue: 1,
              originalItemId: null,
              productId: '634a260d6137ba0007c7c447',
              pwdDiscount: 0,
              quantity: 1,
              seniorDiscount: 0,
              sn: null,
              subTotal: 3212,
              tax: 0,
              taxableAmount: 0,
              taxCode: '6269003c60e98900077a01cb',
              taxRate: 0,
              taxExemptAmount: 0,
              title: 'kitchenA',
              total: 3212,
              unitPrice: 3212,
              discountInputValue: null,
              discountType: null,
              promotions: [],
              itemChannel: 1,
              takeawayCharges: null,
              owner: [' Circular Reference '],
            },
            {
              adhocDiscount: 0,
              discount: 0,
              id: '2:62fc54d6b4e3850007a8c683',
              submitId: null,
              itemType: null,
              notes: null,
              options: '[]',
              orderingValue: 2,
              originalItemId: null,
              productId: '62fc54d6b4e3850007a8c683',
              pwdDiscount: 0,
              quantity: 1,
              seniorDiscount: 0,
              sn: null,
              subTotal: 333,
              tax: 0,
              taxableAmount: 0,
              taxCode: '6269003c60e98900077a01cb',
              taxRate: 0,
              taxExemptAmount: 0,
              title: 'kitchenD',
              total: 333,
              unitPrice: 333,
              discountInputValue: null,
              discountType: null,
              promotions: [],
              itemChannel: 1,
              takeawayCharges: null,
              owner: [' Circular Reference '],
            },
          ],
          payments: [
            {
              amount: 3615.9,
              cashTendered: 3615.9,
              isDeposit: false,
              isOnline: null,
              isVoided: null,
              mPOSTxnId: null,
              onlinePaymentMethod: null,
              paymentMethodId: 0,
              roundedAmount: 0,
              subType: null,
              type: 'Cash',
              manualApproveInfo: null,
              owner: [' Circular Reference '],
            },
          ],
          promotions: [],
          loyaltyDiscounts: [],
          otherReason: 'Incorrect payment type',
          pickUpId: '1000',
          tableId: null,
          takeawayCharges: 0,
          takeawayCharge: null,
          salesChannel: 1,
          isPayByCash: null,
          isPayLater: null,
          isOnlineOrder: null,
          mrs: null,
        },
      ];
    }),
    getLastShift: jest.fn(),
  };
});

jest.mock('../../../ts/components/ui/svgIcons/iconBadGlobal', () => 'IconBadGlobal');

const state = {
  Storage: {
    storeInfo: {
      name: 'feida',
      store: {
        _id: '6269003b60e98900077a01bf',
        enableCashback: true,
        isQROrderingEnabled: true,
        isOnline: true,
        currency: 'MYR',
        enableSelfPickupAlert: true,
      },
    },
  },
  CurrentEmployee: {
    employeeId: '6269003c60e98900077a01c2',
  },
  TransactionSession: {
    items: [
      {
        discount: 0,
        display: {
          total: 9,
          subtotal: 9,
          tax: 0,
        },
        selectedOptions: [],
        subTotal: 9,
        total: 9,
        unitPrice: 9,
        taxInclusiveSubtotal: 9,
        productId: '5c4fc9d1e9020066fb1d220b',
        quantity: 1,
        taxRate: 0,
        tax: 0,
        taxExclusiveSubtotal: 9,
        taxCode: '5c4ec3a0e3d231664262c58c',
        title: 'Variable Price',
        adhocDiscount: 0,
      },
    ],
    discount: 0,
    display: {
      subtotal: 9,
      discount: 0,
      serviceCharge: 0,
      tax: 0,
      total: 9,
    },
    serviceChargeTax: 0,
    subtotal: 9,
    total: 9,
    serviceCharge: 0,
    tax: 0,
    appVersion: '********',
    transactionType: 'Sale',
    transactionId: 'e2823030-4354-4b2f-bf40-98d00edf7510',
  },
  Settings: {
    printerTagsSettings: [
      {
        isBuiltInPrinter: true,
        printerName: 'SUNMIPrinter',
        usbVendorId: 0,
        printerModelType: 'SUNMIPRINTER',
        usbPath: 'SUNMI',
        printerConnectType: 'USB',
        isReceiptPrinter: true,
        isLabelPrinter: false,
        printerPaperWidth: 'Print80',
        printerId: 'ad10871e0f746d209cd59c11b88ccf61',
        modifiedDate: '2022-06-28T08:23:05.092Z',
        tags: ['kitchen'],
        isOnline: true,
      },
      {
        lanIp: '************',
        isBuiltInPrinter: false,
        printerName: 'LAN: ************',
        printerModelType: 'XPRINTER',
        printerConnectType: 'LAN',
        isReceiptPrinter: false,
        isLabelPrinter: false,
        printerPaperWidth: 'Print80',
        printerId: '74301eecf4de1d43d68fb1fdb09a3240',
        modifiedDate: '2022-06-28T08:23:05.092Z',
        lanPort: 9100,
        tags: ['kitchen'],
        isOnline: true,
      },
      {
        lanIp: '************',
        isBuiltInPrinter: false,
        printerName: 'LAN: ************',
        printerModelType: 'XPRINTER',
        printerConnectType: 'LAN',
        isReceiptPrinter: false,
        isLabelPrinter: false,
        printerPaperWidth: 'Print80',
        printerId: '91',
        modifiedDate: '2022-06-28T08:23:05.092Z',
        tags: ['kitchen1'],
        lanPort: 9100,
        isOnline: false,
      },
      {
        lanIp: '************',
        isBuiltInPrinter: false,
        printerName: 'LAN: ************',
        printerModelType: 'XPRINTER',
        printerConnectType: 'LAN',
        isReceiptPrinter: false,
        isLabelPrinter: false,
        printerPaperWidth: 'Print80',
        printerId: '93',
        modifiedDate: '2022-06-28T08:23:05.092Z',
        tags: ['kitchen2'],
        lanPort: 9100,
        isOnline: true,
      },
      {
        lanIp: '************',
        isBuiltInPrinter: false,
        printerName: 'LAN: ************',
        printerModelType: 'XPRINTER',
        printerConnectType: 'LAN',
        isReceiptPrinter: false,
        isLabelPrinter: false,
        printerPaperWidth: 'Print80',
        printerId: '92',
        modifiedDate: '2022-06-28T08:23:05.092Z',
        tags: ['kitchen2'],
        lanPort: 9100,
        isOnline: true,
      },
    ],
  },
};
const mockStore = configureMockStore();

const store = mockStore(fromJS(state));
global.requestAnimationFrame = jest.fn().mockImplementationOnce(fn => fn());

describe('test Transactions', () => {
  it('test render', async () => {
    const onFocusMock = jest.fn();
    const onBlurMock = jest.fn();

    const navigationMock = {
      addListener: (event, callback) => {
        if (event === 'focus') {
          onFocusMock.mockImplementation(callback);
          return jest.fn();
        } else if (event === 'blur') {
          onBlurMock.mockImplementation(callback);
          return jest.fn();
        }
        return jest.fn();
      },
      navigate: jest.fn(),
      dispatch: jest.fn(),
      reset: jest.fn(),
      goBack: jest.fn(),
      isFocused: jest.fn(() => true),
      canGoBack: jest.fn(),
      getParent: jest.fn(),
      getState: jest.fn(),
      getId: jest.fn(() => 'test-id'),
      setParams: jest.fn(),
      setOptions: jest.fn(),
      removeListener: jest.fn(),
    };

    const renderResult = render(
      <Provider store={store}>
        {/* @ts-ignore */}
        <Transactions navigation={navigationMock} />
      </Provider>
    );

    await act(async () => {
      onFocusMock();
      await new Promise(resolve => setTimeout(resolve, 100));
    });

    await new Promise(resolve => setTimeout(resolve, 500));

    console.log('Component Tree:', renderResult.debug());

    const transactionItemContainer = renderResult.queryAllByTestId('transactionItemContainer');
    console.log('Found containers:', transactionItemContainer.length);

    expect(transactionItemContainer.length).toBe(0);

    renderResult.unmount();
  });
});
