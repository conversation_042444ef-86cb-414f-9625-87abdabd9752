import { waitFor } from '@testing-library/react-native';
import Realm from 'realm';
import DAL from '../../ts/dal';
import RealmManager from '../../ts/dal/realm';

beforeAll(async () => {
  RealmManager.setRealmInstanceWithStore();
});

afterAll(() => {
  waitFor(() => {
    RealmManager.getRealmInstance().close();
    Realm.deleteFile(RealmManager.getRealmConfig());
  });
});

describe('test getEmployeeListByPage', () => {
  afterEach(() => {
    RealmManager.getRealmInstance().write(() => {
      RealmManager.getRealmInstance().deleteAll();
    });
  });

  test('should return empty array when no employees', () => {
    const result = DAL.getEmployeeListByPage(0, 10);
    expect(result.length).toBe(0);
  });

  test('should return employees sorted by firstName, lastName, modifiedTime and employeeId', () => {
    const emp1 = {
      employeeId: '1',
      firstName: '101',
      lastName: 'Smith',
      modifiedTime: new Date('2023-01-01'),
      pin: '1234',
      isDeleted: false,
    };
    const emp2 = {
      employeeId: '2',
      firstName: '102',
      lastName: 'Smith',
      modifiedTime: new Date('2023-01-02'),
      pin: '5678',
      isDeleted: false,
    };
    const emp3 = {
      employeeId: '3',
      firstName: '103',
      lastName: 'Williams',
      modifiedTime: new Date('2023-01-03'),
      pin: '9012',
      isDeleted: false,
    };

    RealmManager.getRealmInstance().write(() => {
      RealmManager.getRealmInstance().create('Employee', emp1);
      RealmManager.getRealmInstance().create('Employee', emp2);
      RealmManager.getRealmInstance().create('Employee', emp3);
    });

    const result = DAL.getEmployeeListByPage(0, 10);
    expect(result.length).toBe(3);
    expect(result[0].employeeId).toBe('1');
    expect(result[1].employeeId).toBe('2');
    expect(result[2].employeeId).toBe('3');
  });

  test('should support pagination with pageIndex', () => {
    const employees = [
      {
        employeeId: '1',
        firstName: 'A',
        lastName: 'A',
        modifiedTime: new Date('2023-01-01'),
        pin: '1111',
        isDeleted: false,
      },
      {
        employeeId: '2',
        firstName: 'B',
        lastName: 'B',
        modifiedTime: new Date('2023-01-02'),
        pin: '2222',
        isDeleted: false,
      },
      {
        employeeId: '3',
        firstName: 'C',
        lastName: 'C',
        modifiedTime: new Date('2023-01-03'),
        pin: '3333',
        isDeleted: false,
      },
    ];

    RealmManager.getRealmInstance().write(() => {
      employees.forEach(emp => {
        RealmManager.getRealmInstance().create('Employee', emp);
      });
    });

    // First page
    const page1 = DAL.getEmployeeListByPage(0, 1);
    expect(page1.length).toBe(1);
    expect(page1[0].employeeId).toBe('1');

    // Second page
    const page2 = DAL.getEmployeeListByPage(1, 1);
    expect(page2.length).toBe(1);
    expect(page2[0].employeeId).toBe('2');

    // Third page
    const page3 = DAL.getEmployeeListByPage(2, 1);
    expect(page3.length).toBe(1);
    expect(page3[0].employeeId).toBe('3');

    // Page beyond data
    const page4 = DAL.getEmployeeListByPage(3, 1);
    expect(page4.length).toBe(0);
  });

  test('should support search functionality', () => {
    const emp1 = {
      employeeId: '1',
      firstName: 'Alice',
      lastName: 'Smith',
      modifiedTime: new Date('2023-01-01'),
      pin: '1234',
      isDeleted: false,
    };
    const emp2 = {
      employeeId: '2',
      firstName: 'Bob',
      lastName: 'Johnson',
      modifiedTime: new Date('2023-01-02'),
      pin: '5678',
      isDeleted: false,
    };

    RealmManager.getRealmInstance().write(() => {
      RealmManager.getRealmInstance().create('Employee', emp1);
      RealmManager.getRealmInstance().create('Employee', emp2);
    });

    // Search by first name
    const result1 = DAL.getEmployeeListByPage(0, 10, 'Alice');
    expect(result1.length).toBe(1);
    expect(result1[0].employeeId).toBe('1');

    // Search by last name
    const result2 = DAL.getEmployeeListByPage(0, 10, 'Johnson');
    expect(result2.length).toBe(1);
    expect(result2[0].employeeId).toBe('2');

    // No match
    const result3 = DAL.getEmployeeListByPage(0, 10, 'Nonexistent');
    expect(result3.length).toBe(0);
  });
});
