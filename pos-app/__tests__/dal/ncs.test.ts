import { waitFor } from '@testing-library/react-native';
import Realm from 'realm';
import { TransactionFlowType } from '../../ts/constants';
import DAL from '../../ts/dal';
import RealmManager from '../../ts/dal/realm';
import { CookingStatus } from '../../ts/utils/kds/status';
import { createKdsTransaction } from '../data/kds';

let startQueryDate;
let t1Date, t2Date, t3Date;
beforeAll(async () => {
  RealmManager.setRealmInstanceWithStore();
  const mockDate = new Date('2023-08-21 00:00:00');
  startQueryDate = new Date(+mockDate - 2 * 60 * 60 * 1000 - 60 * 1000);
  t1Date = new Date(+startQueryDate + 30);
  t2Date = new Date(+startQueryDate - 10);
  t3Date = new Date(+startQueryDate + 10);

  jest.useFakeTimers('modern');
  jest.setSystemTime(mockDate);
});
afterAll(() => {
  jest.useRealTimers();
  waitFor(() => {
    RealmManager.getRealmInstance().close();
    Realm.deleteFile(RealmManager.getRealmConfig());
  });
});

describe('test getNcsTransaction SQL', () => {
  afterAll(() => {
    RealmManager.getRealmInstance().write(() => {
      RealmManager.getRealmInstance().deleteAll();
    });
  });

  beforeAll(() => {
    const trans1 = createKdsTransaction('1', t1Date, '1');
    trans1.cookingStatus = CookingStatus.pending;
    DAL.saveTransaction(trans1);

    const trans11 = createKdsTransaction('1', t1Date, '11');
    trans11.cookingStatus = CookingStatus.pending;
    trans11.isPayLater = true;
    DAL.saveTransaction(trans11);

    const trans12 = createKdsTransaction('1', t1Date, '12');
    trans12.cookingStatus = CookingStatus.pending;
    trans12.isOpen = true;
    DAL.saveTransaction(trans12);

    const trans13 = createKdsTransaction('1', t1Date, '13');
    trans13.cookingStatus = CookingStatus.pending;
    trans13.isOpenOrder = true;
    DAL.saveTransaction(trans13);

    const trans14 = createKdsTransaction('1', t1Date, '14');
    trans14.cookingStatus = CookingStatus.pending;
    trans14.mrs = true;
    DAL.saveTransaction(trans14);

    const trans2 = createKdsTransaction('1', t1Date, '2');
    trans2.cookingStatus = null;
    DAL.saveTransaction(trans2);

    const trans3 = createKdsTransaction('1', t1Date, '3');
    trans3.cookingStatus = CookingStatus.served;
    DAL.saveTransaction(trans3);

    const trans4 = createKdsTransaction('1', t1Date, '4');
    trans4.cookingStatus = CookingStatus.served;
    trans4.transactionType = TransactionFlowType.Return;
    DAL.saveTransaction(trans4);

    const trans5 = createKdsTransaction('1', t2Date, '5');
    trans5.cookingStatus = CookingStatus.served;
    DAL.saveTransaction(trans5);

    const trans6 = createKdsTransaction('1', t2Date, '6');
    trans6.cookingStatus = CookingStatus.pending;
    DAL.saveTransaction(trans6);

    const trans7 = createKdsTransaction('1', t3Date, '7');
    trans7.cookingStatus = CookingStatus.pending;
    DAL.saveTransaction(trans7);

    const trans8 = createKdsTransaction('1', t3Date, '8');
    trans8.cookingStatus = CookingStatus.served;
    DAL.saveTransaction(trans8);
  });
  test('should only get correct preparing orders', () => {
    const result = DAL.getNcsTransaction('1', 1, CookingStatus.pending, startQueryDate);
    expect(result?.list.length).toBe(1);
    const item = result?.list[0];
    expect(item.transactionId).toBe('1');
    expect(result.nextQueryDate).toEqual(t1Date);

    const result2 = DAL.getNcsTransaction('1', 2, CookingStatus.pending, startQueryDate);
    expect(result2?.list.length).toBe(2);
    expect(result2?.list.map(it => it.transactionId)).toEqual(['1', '7']);
  });

  test('should only get correct served orders', () => {
    const result = DAL.getNcsTransaction('1', 1, CookingStatus.served, startQueryDate);
    expect(result?.list.length).toBe(1);
    const item = result?.list[0];
    expect(item.transactionId).toBe('3');
    expect(result.nextQueryDate).toEqual(t1Date);
  });

  test('orders should be sorted by pushKdsDate desc', () => {
    const result2 = DAL.getNcsTransaction('1', 2, CookingStatus.served, startQueryDate);
    expect(result2?.list.length).toBe(2);
    expect(result2?.list.map(it => it.transactionId)).toEqual(['3', '8']);
  });

  test('should support page query', () => {
    const result = DAL.getNcsTransaction('1', 1, CookingStatus.served, startQueryDate);
    expect(result?.list.length).toBe(1);
    const item = result?.list[0];
    expect(item.transactionId).toBe('3');
    expect(result.nextQueryDate).toEqual(t1Date);

    const result2 = DAL.getNcsTransaction('1', 1, CookingStatus.served, startQueryDate, t1Date);
    expect(result2?.list.length).toBe(1);
    const item2 = result2?.list[0];
    expect(item2.transactionId).toBe('8');
    expect(result2.nextQueryDate).toEqual(t3Date);
  });
});
