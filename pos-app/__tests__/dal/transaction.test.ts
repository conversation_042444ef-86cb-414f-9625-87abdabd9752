import { waitFor } from '@testing-library/react-native';
import Realm from 'realm';
import { TransactionFlowType } from '../../ts/constants';
import DAL from '../../ts/dal';
import RealmManager from '../../ts/dal/realm';

let startDate, endDate, registerId;
beforeAll(async () => {
  RealmManager.setRealmInstanceWithStore({});
  const mockDate = new Date('2024-01-01 00:00:00');
  startDate = new Date(+mockDate - 24 * 60 * 60 * 1000); // 1 day before
  endDate = new Date(+mockDate + 24 * 60 * 60 * 1000); // 1 day after
  registerId = 'register-123';

  jest.useFakeTimers();
  jest.setSystemTime(mockDate);
});

afterAll(() => {
  jest.useRealTimers();
  waitFor(() => {
    RealmManager.getRealmInstance().close();
    Realm.deleteFile(RealmManager.getRealmConfig());
  });
});

describe('test getNotPreOrderTransactionsCount', () => {
  afterEach(() => {
    RealmManager.getRealmInstance().write(() => {
      RealmManager.getRealmInstance().deleteAll();
    });
  });

  const createTestTransaction = (overrides = {}) => ({
    transactionId: `test-${Date.now()}-${Math.random()}`,
    createdDate: new Date('2024-01-01 10:00:00'),
    modifiedDate: new Date('2024-01-01 10:00:00'),
    isCancelled: false,
    isOpen: false,
    transactionType: TransactionFlowType.Sale,
    registerId,
    items: [],
    payments: [],
    promotions: [],
    loyaltyDiscounts: [],
    subOrders: [],
    ...overrides,
  });

  it('should return correct count of non-preorder transactions', () => {
    const transactions = [
      createTestTransaction({
        transactionId: 'test-1',
        transactionType: TransactionFlowType.Sale,
      }),
      createTestTransaction({
        transactionId: 'test-2',
        transactionType: TransactionFlowType.Return,
      }),
    ];

    RealmManager.getRealmInstance().write(() => {
      transactions.forEach(transaction => {
        RealmManager.getRealmInstance().create('Transaction', transaction);
      });
    });

    const result = DAL.getNotPreOrderTransactionsCount(startDate, endDate, registerId);
    expect(result).toBe(2);
  });

  it('should exclude cancelled transactions', () => {
    const transactions = [
      createTestTransaction({
        transactionId: 'test-1',
        isCancelled: true,
      }),
      createTestTransaction({
        transactionId: 'test-2',
        isCancelled: false,
      }),
    ];

    RealmManager.getRealmInstance().write(() => {
      transactions.forEach(transaction => {
        RealmManager.getRealmInstance().create('Transaction', transaction);
      });
    });

    const result = DAL.getNotPreOrderTransactionsCount(startDate, endDate, registerId);
    expect(result).toBe(1);
  });

  it('should exclude pre-order transactions', () => {
    const transactions = [
      createTestTransaction({
        transactionId: 'test-1',
        transactionType: TransactionFlowType.PreOrder,
      }),
      createTestTransaction({
        transactionId: 'test-2',
        transactionType: TransactionFlowType.Sale,
      }),
    ];

    RealmManager.getRealmInstance().write(() => {
      transactions.forEach(transaction => {
        RealmManager.getRealmInstance().create('Transaction', transaction);
      });
    });

    const result = DAL.getNotPreOrderTransactionsCount(startDate, endDate, registerId);
    expect(result).toBe(1);
  });

  it('should exclude open transactions', () => {
    const transactions = [
      createTestTransaction({
        transactionId: 'test-1',
        isOpen: true,
      }),
      createTestTransaction({
        transactionId: 'test-2',
        isOpen: false,
      }),
    ];

    RealmManager.getRealmInstance().write(() => {
      transactions.forEach(transaction => {
        RealmManager.getRealmInstance().create('Transaction', transaction);
      });
    });

    const result = DAL.getNotPreOrderTransactionsCount(startDate, endDate, registerId);
    expect(result).toBe(1);
  });

  it('should return 0 when no transactions found', () => {
    const result = DAL.getNotPreOrderTransactionsCount(startDate, endDate, registerId);
    expect(result).toBe(0);
  });
});
