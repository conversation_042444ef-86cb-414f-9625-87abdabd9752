import { waitFor } from '@testing-library/react-native';
import { isArray } from 'lodash';
import Realm from 'realm';
import DAL from '../../ts/dal';
import { TransactionHelper } from '../../ts/dal/helper';
import RealmManager from '../../ts/dal/realm';
import { Save2Transaction } from '../data/transaction';

beforeAll(() => {
  RealmManager.setRealmInstanceWithStore();
  DAL.saveTransaction(Save2Transaction);
});
afterAll(() => {
  waitFor(() => {
    RealmManager.getRealmInstance().close();
    Realm.deleteFile(RealmManager.getRealmConfig());
  });
});

describe('test transactionHelper', () => {
  it('should get correct data when save the transaction', async () => {
    const rt = DAL.getTrancationById(Save2Transaction.transactionId);
    const pt = TransactionHelper.serializeTransaction(rt);
    expect(pt.payments.length).toBe(0);
    const options = pt.items[2].selectedOptions;
    expect(isArray(options)).toBe(true);
    expect(pt.items[2].options).toBeFalsy();
  });
});
