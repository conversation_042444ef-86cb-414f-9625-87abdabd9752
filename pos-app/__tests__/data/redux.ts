import { fromJS } from 'immutable';

const state = {
  ReduxPersist: { isReduxLoaded: true },
  Shift: { ShiftOpenStatus: true },
  Settings: {
    generalSettings: {
      enableCustomerShortCut: true,
      enableCloseLastShiftNotification: true,
      shouldLoadOnlineOpenOrders: true,
      enableOpenOrders: true,
      enableOpenOrdersShortCut: true,
    },
    printerGeneralSettings: { merchantHasPrinter: true },
    printerTagsSettings: [
      {
        lanIp: '************',
        isBuiltInPrinter: false,
        executionPreviousStatus: 'Initial',
        printerName: 'LAN：************',
        macAddress: '',
        printerModelType: 'LANXPRINTER',
        model: 'LANXPRINTER',
        printerConnectType: 'LAN',
        isLabelPrinter: false,
        executionCurrentStatus: 'Initial',
        errorCode: 0,
        printerId: '5d6dc46bf84799c93a8e29eee31e75f3534cf82e',
        modifiedDate: '2023-04-15T13:25:36.192Z',
        tags: [],
        lanPort: 9100,
        isOnline: false,
        executiontEvent: 'FOUND',
      },
      {
        lanIp: '************',
        isBuiltInPrinter: false,
        executionPreviousStatus: 'Initial',
        printerName: 'LAN：************',
        macAddress: '',
        printerModelType: 'LANXPRINTER',
        model: 'LANXPRINTER',
        printerConnectType: 'LAN',
        isLabelPrinter: false,
        executionCurrentStatus: 'Initial',
        errorCode: 0,
        printerId: '2d376ac029a0f3b8788a861fdb36f78e6117d0c8',
        modifiedDate: '2023-04-15T13:25:36.192Z',
        tags: [],
        lanPort: 9100,
        isOnline: false,
        executiontEvent: 'FOUND',
      },
      {
        lanIp: '*************',
        isBuiltInPrinter: false,
        executionPreviousStatus: 'Initial',
        printerName: 'LAN：*************',
        macAddress: '',
        printerModelType: 'LANXPRINTER',
        model: 'LANXPRINTER',
        printerConnectType: 'LAN',
        isLabelPrinter: false,
        executionCurrentStatus: 'Initial',
        errorCode: 0,
        printerId: '14cd4b2a1982799750d5b97e909eb1cee5a3f9b5',
        modifiedDate: '2023-04-15T13:25:36.192Z',
        tags: [],
        lanPort: 9100,
        isOnline: false,
        executiontEvent: 'FOUND',
      },
      {
        lanIp: '************',
        isBuiltInPrinter: false,
        executionPreviousStatus: 'Initial',
        printerName: 'LAN：************',
        macAddress: '',
        printerModelType: 'LANXPRINTER',
        model: 'LANXPRINTER',
        printerConnectType: 'LAN',
        isLabelPrinter: false,
        executionCurrentStatus: 'Initial',
        errorCode: 0,
        printerId: '8934d4b84a8d0bb000b72f91f016586fd1fe5acb',
        modifiedDate: '2023-04-15T13:25:36.192Z',
        tags: [],
        lanPort: 9100,
        isOnline: false,
        executiontEvent: 'FOUND',
      },
      {
        lanIp: '************',
        isBuiltInPrinter: false,
        executionPreviousStatus: 'Initial',
        printerName: 'LABEL：************',
        macAddress: '00:15:94:61:0B:40',
        printerModelType: 'BIXOLON',
        model: 'BIXOLON',
        printerConnectType: 'LAN',
        isReceiptPrinter: false,
        isLabelPrinter: true,
        executionCurrentStatus: 'Initial',
        errorCode: 0,
        printerId: '85a087c1ecc7b92bacc0e571940aa812e2327527',
        modifiedDate: '2023-04-17T07:15:52.545Z',
        tags: [],
        lanPort: 9100,
        isOnline: true,
        executiontEvent: 'FOUND',
      },
      {
        lanIp: '************',
        isBuiltInPrinter: false,
        executionPreviousStatus: 'Initial',
        printerName: 'STAR TSP100LAN: ************',
        macAddress: '00:11:62:08:4c:7d',
        printerModelType: 'STAR',
        model: 'TSP100LAN',
        printerConnectType: 'LAN',
        isLabelPrinter: false,
        executionCurrentStatus: 'Initial',
        errorCode: 0,
        printerId: 'a8be0e1dbf143bac6a1ee1e0483967fd470efb27',
        modifiedDate: '2023-04-13T10:27:03.611Z',
        tags: [],
        lanPort: 9100,
        isOnline: false,
        executiontEvent: 'FOUND',
      },
      {
        lanIp: '***********',
        isBuiltInPrinter: false,
        executionPreviousStatus: 'Initial',
        printerName: 'LAN：***********',
        macAddress: '',
        printerModelType: 'LANXPRINTER',
        model: 'LANXPRINTER',
        printerConnectType: 'LAN',
        isLabelPrinter: false,
        executionCurrentStatus: 'Initial',
        errorCode: 0,
        printerId: 'b2858f2e8d971e4fd4912c716a85073669eac16e',
        modifiedDate: '2023-04-17T07:15:52.545Z',
        lanPort: 9100,
        isOnline: true,
        executiontEvent: 'FOUND',
      },
    ],
    accountSettings: { expired: false, offline: false },
    customerDisplaySettings: {},
    tableLayoutSettings: { enableTableLayout: false, alreadyMigrateTableLayout: true, needShowEditTableLayoutToast: false },
    defaultWifi: {},
  },
  EnvSetting: { testEnvName: 'Default' },
  CurrentEmployee: { employeeId: '6269003c60e98900077a01c2' },
  NetInfo: {
    type: 'wifi',
    isConnected: true,
    details: { subnet: '*************', isConnectionExpensive: false, ssid: null, bssid: null, ipAddress: '************' },
    isInternetReachable: true,
  },
  EditingOpenOrder: { inMerge: false, inSplit: false, inMove: false, onChangingTable: false, splitMasterOrder: {}, splitedOrder: {} },
  BadgeNumber: { beepBadgeNumber: 7 },
  MRS: {
    learn: { isLearning: false, isInitLearning: false },
    clients: [
      { ip: '************', isConnected: true, registerId: '2', registerName: '为企鹅群@！*。.' },
      { ip: '*************', isConnected: false, registerId: '3', registerName: 'IMin D1' },
      { ip: '*************', isConnected: true, registerId: '7', registerName: '打撒打算打算的发达时代撒十大121@' },
      { ip: '*************', isConnected: false, registerId: '4', registerName: 'IMin D4' },
    ],
    pid: 13,
    isMaster: false,
    masterState: 'Offline',
    serverInfo: { ip: '*************', port: 8888 },
    switchMasterList: [{ pid: 13, uuid: '1681705197291', registerId: 7, dataType: 'request', messageSender: 'slave', ip: '*************' }],
    isEnabledMRS: true,
    clientInfo: { registerId: '2', isConnected: false, registerName: '为企鹅群@！*。.', ip: '************', pid: 13, updateTime: '4:42 PM' },
    resetFlag: false,
    serverRunning: false,
    clientConnected: false,
  },
  FetchResult: {
    getClockedInEmployeeList: { payload: { timesheets: [] }, __fetchStatus: 'success' },
    getActivationStatus: { payload: { isActivated: true, subscriptionStatus: 'Active', subscriptionPlan: 2 }, __fetchStatus: 'success' },
    getBadgeNumber: { payload: { backofficeBadgeNumber: 0, beepBadgeNumber: 7 }, __fetchStatus: 'success' },
    queryTimezone: { payload: { business: { timezone: 'Asia/Kuala_Lumpur' } }, __fetchStatus: 'success' },
    queryBeepPauseModeSettings: { payload: { beepPauseModeSettings: { paused: false, pausedUntil: 1675226040671 } }, __fetchStatus: 'success' },
  },
  TransactionSession: {},
  PauseMode: { storeInfo: { paused: false, pausedUntil: 1675226040671 } },
  Printer: { errorPrinters: {}, lockJobs: [] },
  Storage: {
    sequence: { receiptNumberStart: null, receiptDateStart: null, invoiceNumberStart: null },
    syncInfo: {
      lastProductSyncTime: '2023-04-11T07:59:31.453Z',
      lastPriceBookSyncTime: '',
      lastQuickLayoutSyncTime: '2023-04-13T08:12:58.892Z',
      promotionInfoSync: true,
      lastEmployeeSyncTime: '2023-03-21T09:54:23.559Z',
      lastPromotionSyncTime: '2023-03-09T06:08:39.714Z',
      needToSyncAll: false,
      employeeInfoSync: true,
      productInfoSync: true,
      priceBookInfoSync: true,
      onlineSyncTime: '2023-04-17T07:15:46.801Z',
      needFixsequential: false,
    },
    freeTrial: false,
    shareQuickLayoutStores: ['6269003b60e98900077a01bf'],
    storeInfo: {
      store: {
        fulfillmentOptions: ['Delivery', 'Pickup'],
        cfdDisplay: { cfdDisplayId: '62690ddecceab90008725a0c', type: 'images', text: '', name: 'CM-850', ordering: 1 },
        isVATRegistered: false,
        notes: '',
        orderSummaryPrinter: '',
        serviceChargeTax: '',
        isQROrderingEnabled: true,
        enableLoyalty: true,
        showBarcode: true,
        taxNameOnReceipt: '',
        enablePax: true,
        ptuValideUntil: '',
        gstIdNo: '',
        showNotes: true,
        birAccredited: false,
        defaultLoyaltyRatio: 20,
        enableServiceCharge: true,
        enablePayLater: true,
        companyName: '',
        operationHours: 0,
        roundAllTransactions: true,
        disableCashbackFromPOS: false,
        showCustomerInfo: [],
        serviceChargeRate: 0.02,
        takeawayCharge: 0,
        tableLayoutEnabled: true,
        ptuDateIssued: '',
        birAccrInfo:
          '****************************************\r\n\r\nStoreHub Philippines Inc\r\n502 Pacific Center Building, 33 San Miguel Avenue Ortigas Center\r\nSan Antonio Pasig City\r\nVAT REG TIN #: ***********-000\r\nDate Issued:  05/29/2017\r\n',
        city: '',
        name: 'retail',
        tax: 0,
        street1: '',
        enablePreOrder: false,
        phone: '',
        quickSelectLayoutId: '6269003b60e98900077a01c1',
        defaultTableLayoutSection: '24ec8490cd0a11ec931d8f5c9c68aa4b',
        street2: '',
        showStoreName: true,
        enablePayByCash: true,
        qrRegisters: ['63295e17fe19870007243ba9'],
        roundingTo: 0.1,
        enableGroupItem: true,
        sstIdNo: '',
        currency: 'MYR',
        state: '',
        enableSelfPickupAlert: false,
        ptu: '',
        disabledDefaultPayments: [],
        newMRSEnabled: true,
        minNo: '',
        cashierAccesses: {
          discount: '0',
          printReportAfterCloseShift: '0',
          deleteItemOfNew: '0',
          viewEditTransaction: '0',
          deleteItemOfSaved: '0',
          openCashDrawerWhenCloseShift: '0',
          openCloseShift: '0',
          refund: '0',
          cancel: '0',
          reprintReceipt: '0',
        },
        serialNo: '',
        enableMultipleIpads: true,
        country: 'MY',
        defaultKitchenPrinter: 'kitchen',
        includingTaxInDisplay: false,
        defaultTaxCode: '6269003c60e98900077a01cb',
        postalCode: '',
        paymentOptions: [],
        enableTakeaway: true,
        autoOrderId: true,
        assignTableID: true,
        _id: '6269003b60e98900077a01bf',
        website: '',
        taxCodes: [
          { rate: 0, isVatExempted: false, _id: '6269003c60e98900077a01cb', name: 'SR' },
          { rate: 0.1, isVatExempted: false, _id: '626b9a3ccceab9000872d4f4', name: '10SQ' },
        ],
        poweredBy: true,
        email: '<EMAIL>',
        isOnline: false,
        enableCashback: false,
        birAccrNo: '043-0100-36922-201905-1090',
        kitchenPrinters: 'kitchen,dessertbar,food,order summary,bar',
        industry: 0,
        brn: '',
        allowEditProductsOnPOS: false,
        separateKitchenItems: true,
      },
      sequentialReceiptNumber: false,
      subscriptionPlan: 2,
      name: 'feida',
      registerName: '为企鹅群@！*。.',
      subscriptionStatus: 'Active',
      currency: 'MYR',
      allStores: [
        {
          notes: '',
          gstIdNo: '',
          companyName: '',
          city: '',
          name: 'retail',
          street1: '',
          phone: '',
          street2: '',
          sstIdNo: '',
          state: '',
          enableDigital: false,
          postalCode: '',
          _id: '6269003b60e98900077a01bf',
          website: '',
          email: '',
        },
        {
          notes: '',
          gstIdNo: '',
          companyName: '',
          city: '',
          name: 'restaurant',
          street1: '',
          phone: '',
          street2: '',
          sstIdNo: '',
          state: '',
          enableDigital: false,
          postalCode: '',
          _id: '6269003c60e98900077a01cd',
          website: '',
          email: '',
        },
        {
          notes: '',
          gstIdNo: '',
          companyName: '',
          city: '',
          name: 'Digital Store',
          street1: '',
          phone: '',
          street2: '',
          sstIdNo: '',
          state: '',
          enableDigital: true,
          postalCode: '',
          _id: '6274c4b485c4b30008c67938',
          website: '',
          email: '',
        },
      ],
      registerObjectId: '6278898e85c4b30008c6d172',
      registerId: 2,
      apiToken: 'ff6545a0d9d211ed991e935e1e46a26d',
    },
    geo: { longitude: -122.406417, latitude: 37.785834 },
    device: { platform: 'RN-iOS', model: 'iPad', version: '********', systemVersion: '15.2' },
    quickLayout: [
      {
        name: '2',
        order: 0,
        categoryId: '9df70703-d75c-4090-9e23-9f88effb42e0',
        items: [
          { productId: '626b99f9cceab9000872d4e9', row: 0, column: 0, backgroundColor: '' },
          { productId: '62739b0c85c4b30008c648ae', row: 0, column: 1, backgroundColor: '' },
          { productId: '6274a62985c4b30008c66e58', row: 0, column: 2, backgroundColor: '' },
          { productId: '6274a68885c4b30008c66e86', row: 0, column: 3, backgroundColor: '' },
          { productId: '628e2dfc5051f90007bc25be', row: 1, column: 0, backgroundColor: '' },
          { productId: '628e2e275051f90007bc25cf', row: 1, column: 1, backgroundColor: '' },
          { productId: '62c6a45d6229bc0007f1d744', row: 1, column: 2, backgroundColor: '' },
          { productId: '6423b5f446374200070e0fbf', row: 2, column: 0, backgroundColor: '' },
          { productId: '642417b827b9530007832c7c', row: 2, column: 1, backgroundColor: '' },
          { productId: '642a755620c95c0007eb1f38', row: 2, column: 2, backgroundColor: '' },
          { productId: '642fb56fea3bf30008b20393', row: 1, column: 3, backgroundColor: '' },
          { productId: '642fb590ea3bf30008b20507', row: 2, column: 3, backgroundColor: '' },
          { productId: '643377324447980007ce7086', row: 3, column: 1, backgroundColor: '' },
        ],
      },
      {
        name: 'kitchen',
        order: 1,
        categoryId: '300b3fbb-2f0a-4c7c-e4f1-fa7f242ca878',
        items: [
          { productId: '62fc544fb4e3850007a8c305', row: 0, column: 0, backgroundColor: '' },
          { productId: '62fc54aab4e3850007a8c56e', row: 0, column: 1, backgroundColor: '' },
          { productId: '62fc54bfb4e3850007a8c624', row: 0, column: 2, backgroundColor: '#8D90A3' },
          { productId: '62fc54d6b4e3850007a8c683', row: 1, column: 4, backgroundColor: '#8D90A3' },
          { productId: '634a260d6137ba0007c7c447', row: 0, column: 3, backgroundColor: '' },
        ],
      },
      {
        name: '3',
        order: 2,
        categoryId: '61009362-4335-40f3-d794-5a8521ae2e2e',
        items: [{ productId: '634f8f065cf04900078c188a', row: 0, column: 0, backgroundColor: '' }],
      },
      {
        name: '4',
        order: 3,
        categoryId: 'a425903f-701a-4e1b-d017-65fcf0ceeaa5',
        items: [{ productId: '629766c0317f6b000804b075', row: 0, column: 1, backgroundColor: '' }],
      },
      {
        name: '5',
        order: 4,
        categoryId: 'f9898965-0405-4d47-83d2-5c4eaa4745be',
        items: [{ productId: '63905d538bb2010008b8b9bd', row: 0, column: 0, backgroundColor: '' }],
      },
      {
        name: '6',
        order: 5,
        categoryId: 'a8f164ed-fd27-4a1a-8d45-0637acb4227d',
        items: [{ productId: '6374a5a57d6b6b000863bcdc', row: 0, column: 1, backgroundColor: '' }],
      },
      { name: '7', order: 6, categoryId: 'a90a78a4-d991-4857-cadc-f8fd1754bc2b' },
      {
        name: '8',
        order: 7,
        categoryId: '6370941e-d202-4d6e-d7a5-148333a41bc3',
        items: [{ productId: '6269003c60e98900077a01c3', row: 0, column: 1, backgroundColor: '' }],
      },
      {
        name: 'Desserts',
        order: 8,
        categoryId: '160f06da-8953-46eb-cc81-5313c14e5db9',
        items: [{ productId: '6269003c60e98900077a01c6', row: 0, column: 1, backgroundColor: '' }],
      },
      {
        name: '10',
        order: 9,
        categoryId: 'd9b04e0f-4bd0-499a-d18c-0a4e3c6a2312',
        items: [{ productId: '6269003c60e98900077a01c5', row: 0, column: 1, backgroundColor: '' }],
      },
      {
        name: '11',
        order: 10,
        categoryId: '4a36cc6a-6666-4ccf-d80d-59e63f79ee01',
        items: [{ productId: '6269003c60e98900077a01c4', row: 0, column: 2, backgroundColor: '' }],
      },
    ],
    tableLayout: [
      {
        sectionId: '16b9d06c-0f00-48c2-bb7e-1e470a74ff4b',
        sectionName: 'Ground Floor',
        order: 0,
        tables: [
          {
            tableName: '1-3',
            seatingCapacity: 0,
            frame: { width: 120, height: 120, xCoordinate: 788.593740463257, yCoordinate: 264.10542869567877, shape: 'rectangle', rotate: 0 },
          },
          { tableName: '1-1', seatingCapacity: 1, frame: { width: 120, height: 120, xCoordinate: 22.5, yCoordinate: 22.5, shape: 'rectangle', rotate: 0 } },
          {
            tableName: '1-5',
            seatingCapacity: 0,
            frame: { width: 120, height: 120, xCoordinate: 154.38279533386233, yCoordinate: 478.33748245239263, shape: 'rectangle', rotate: 0 },
          },
          {
            tableName: '1-0',
            seatingCapacity: 2,
            frame: { width: 120, height: 120, xCoordinate: 228.75, yCoordinate: 24.375, shape: 'rectangle', rotate: 0 },
          },
          {
            tableName: '1-6',
            seatingCapacity: 0,
            frame: { width: 120, height: 120, xCoordinate: 440.2109413146973, yCoordinate: 470.96248817443853, shape: 'rectangle', rotate: 0 },
          },
          {
            tableName: '1-2',
            seatingCapacity: 3,
            frame: { width: 120, height: 120, xCoordinate: 420.3593788146973, yCoordinate: 206.34999465942386, shape: 'rectangle', rotate: 0 },
          },
          {
            tableName: '1-5-1',
            seatingCapacity: 0,
            frame: { width: 120, height: 120, xCoordinate: 248.15623855590823, yCoordinate: 324.087516784668, shape: 'rectangle', rotate: 0 },
          },
          {
            tableName: '1-4',
            seatingCapacity: 0,
            frame: { width: 120, height: 120, xCoordinate: 738.0904522613066, yCoordinate: 519.3969849246231, shape: 'rectangle', rotate: 0 },
          },
        ],
      },
      {
        sectionId: '4faf9b91-e38f-4c54-93e6-431e85e3ee42',
        sectionName: 'Second',
        order: 1,
        tables: [
          {
            tableName: '2-1',
            seatingCapacity: 0,
            frame: { width: 120, height: 120, xCoordinate: 157.58793969849248, yCoordinate: 115.77889447236181, shape: 'ellipse', rotate: 0 },
          },
          {
            tableName: '2-0',
            seatingCapacity: 0,
            frame: { width: 120, height: 120, xCoordinate: 516.9849246231156, yCoordinate: 165.6281407035176, shape: 'ellipse', rotate: 0 },
          },
        ],
      },
    ],
    version: '1.13.1',
    storeTimezone: 'Asia/Kuala_Lumpur',
  },
  EWallet: {},
  CommonUI: { toastInfo: { text: '', visiable: false }, loadingMaskIsVisible: false, disableInteractions: true },
};
export const testFullReduxStateJSON = JSON.stringify(state);
export const getFullReduxState = () => JSON.parse(testFullReduxStateJSON);
export const getImmutableFullReduxState = () => fromJS(JSON.parse(testFullReduxStateJSON));
