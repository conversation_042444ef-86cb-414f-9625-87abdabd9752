import { renderHook } from '@testing-library/react-hooks';
import { useDispatch, useSelector } from 'react-redux';
import useKds from '../../../ts/hooks/kds/useKds';
import { registerKds, unregisterKds } from '../../../ts/actions/kds';

// Mock react-redux
jest.mock('react-redux', () => ({
  useDispatch: jest.fn(),
  useSelector: jest.fn(),
}));

// Mock actions
jest.mock('../../../ts/actions/kds', () => ({
  registerKds: jest.fn(),
  unregisterKds: jest.fn(),
}));

describe('useKds', () => {
  const mockDispatch = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useDispatch as jest.Mock).mockReturnValue(mockDispatch);
  });

  it('should register KDS when all conditions are met', () => {
    // Mock selector values
    (useSelector as jest.Mock).mockImplementation(selector => {
      if (selector.name === 'selectIsWebsocketLoaded') return true;
      if (selector.name === 'selectIsKDSPurchasedANDAssigned') return true;
      if (selector.name === 'selectAuthToken') return 'mock-token';
      return null;
    });

    renderHook(() => useKds());

    expect(mockDispatch).toHaveBeenCalledWith(registerKds());
  });

  it('should not register KDS when websocket is not loaded', () => {
    // Mock selector values
    (useSelector as jest.Mock).mockImplementation(selector => {
      if (selector.name === 'selectIsWebsocketLoaded') return false;
      if (selector.name === 'selectIsKDSPurchasedANDAssigned') return true;
      if (selector.name === 'selectAuthToken') return 'mock-token';
      return null;
    });

    renderHook(() => useKds());

    expect(mockDispatch).not.toHaveBeenCalledWith(registerKds());
  });

  it('should not register KDS when KDS is not purchased and assigned', () => {
    // Mock selector values
    (useSelector as jest.Mock).mockImplementation(selector => {
      if (selector.name === 'selectIsWebsocketLoaded') return true;
      if (selector.name === 'selectIsKDSPurchasedANDAssigned') return false;
      if (selector.name === 'selectAuthToken') return 'mock-token';
      return null;
    });

    renderHook(() => useKds());

    expect(mockDispatch).not.toHaveBeenCalledWith(registerKds());
  });

  it('should not register KDS when auth token is missing', () => {
    // Mock selector values
    (useSelector as jest.Mock).mockImplementation(selector => {
      if (selector.name === 'selectIsWebsocketLoaded') return true;
      if (selector.name === 'selectIsKDSPurchasedANDAssigned') return true;
      if (selector.name === 'selectAuthToken') return null;
      return null;
    });

    renderHook(() => useKds());

    expect(mockDispatch).not.toHaveBeenCalledWith(registerKds());
  });

  it('should unregister KDS on cleanup', () => {
    // Mock selector values
    (useSelector as jest.Mock).mockImplementation(selector => {
      if (selector.name === 'selectIsWebsocketLoaded') return true;
      if (selector.name === 'selectIsKDSPurchasedANDAssigned') return true;
      if (selector.name === 'selectAuthToken') return 'mock-token';
      return null;
    });

    const { unmount } = renderHook(() => useKds());

    unmount();

    expect(mockDispatch).toHaveBeenCalledWith(unregisterKds());
  });
});
