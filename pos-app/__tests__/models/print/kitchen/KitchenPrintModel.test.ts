import { BEEP_ORDER_SUMMARY_PRINTER_TAG } from '../../../../ts/constants';
import { KitchenPrintModel } from '../../../../ts/models/print/printModel/kitchen/KitchenPrintModel';
import { PrinterSetting } from '../../../../ts/models/print/printer/PrinterSetting';
import { KitchenTransaction } from '../../../../ts/models/transaction/KitchenTransaction';
import { KITCHEN_CONFIG } from '../../../data/kitchen';
import { getTransactionSessionData } from '../../../data/transactionSession';
import mockTransaction from './payLaterOrder.json';
import { kitchen, summary } from './requestData';
const PRODUCT_ID = '62fc54d6b4e3850007a8c683';
jest.mock('../../../../ts/dal', () => {
  return {
    getProductById: jest.fn().mockImplementation(productId => {
      return {
        barcode: null,
        category: null,
        inventoryType: '',
        isChild: true,
        isSerialized: false,
        kitchenPrinter: 'kitchenA',
        modifiedTime: '2024-04-29T04:23:18.761Z',
        hasThumbnail: false,
        lastUpdateThumbnail: '2024-04-29T04:23:18.761Z',
        isBasicNecessitiesPH: false,
        parentProductId: null,
        priceType: 'fixed',
        productId: '62fc54d6b4e3850007a8c683',
        productType: 'Simple',
        skuId: null,
        tags: [],
        taxCode: '',
        title: 'beer-date',
        trackInventory: false,
        unit: null,
        unitPrice: 333,
        variationsJson: '[]',
        variationValuesJson: '[]',
        priceBooks: [],
        beerDocketSettings: {
          isEnable: true,
          docketCount: 2,
          expirationDateDuration: 2,
        },
        maxRefUnitPrice: null,
        minRefUnitPrice: null,
      };
    }),
  };
});
describe('getBeerRequestData', () => {
  const transaction = getTransactionSessionData();
  transaction.items[1].productId = PRODUCT_ID;
  transaction.items[1].quantity = 2;
  const kitchenTransaction = new KitchenTransaction(transaction);
  const kitchenPrintModel = new KitchenPrintModel(kitchenTransaction, KITCHEN_CONFIG);
  kitchenPrintModel.setKitchenItems([transaction.items[1]]);

  const printerModel = new PrinterSetting([
    {
      tags: ['kitchenA'],
      usbPath: 'iMin',
      model: 'IMINPRINTER',
      lastPrintedTime: '2023-11-30T09:50:50.045Z',
      printerPaperWidth: 'Print80',
      printerId: '475e239b53aeb71f158a1a3afcde1999',
      isBuiltInPrinter: true,
      printerName: 'iMinPrinter',
      isOnline: true,
      isReceiptPrinter: true,
      usbVendorId: 0,
    },
  ]);
  it('should return null if kitchenPrintingModels is null', () => {
    const result = kitchenPrintModel.getBeerRequestData(null);
    expect(result).toBeNull();
  });

  it('should return empty array if kitchenPrintingModels is empty', () => {
    const result = kitchenPrintModel.getBeerRequestData([]);
    expect(result).toBeNull();
  });

  it('should return request print data for valid kitchenPrintingModels', () => {
    const kitchenPrintingModels = kitchenPrintModel.getKitchenRequestData(printerModel);
    const result = kitchenPrintModel.getBeerRequestData(kitchenPrintingModels);
    expect(result).toHaveLength(4);
    expect(result[0].data.purchasedItems).toHaveLength(1);
    expect(result[0].data.purchasedItems[0].quantity).toBe(1);
    expect(result[0].data.purchasedItems[0].quantityStr).toBe('1');
    expect(result[0].data.docketCountString).toBe('1/2');
  });

  it('should return null for delete', () => {
    const kitchenPrintingModels = kitchenPrintModel.getKitchenRequestData(printerModel);
    kitchenPrintingModels?.forEach(it => {
      it.data.purchasedItems.forEach(item => {
        item.quantity = -1;
      });
    });
    const result = kitchenPrintModel.getBeerRequestData(kitchenPrintingModels);
    expect(result).toBeNull();
  });
});

test('print on format of suborder', () => {
  const printers = [
    {
      lanIp: '*************',
      isBuiltInPrinter: false,
      prevPrinterId: '1c393e7f08d6ba047b97ab85e8521aae',
      lastPrintedTime: '2024-10-17T11:24:41.516Z',
      pingStatus: 0,
      migrationPrinterIdV3: null,
      printerName: 'LAN: *************',
      macAddress: '00-DB-9D-66-77-64',
      printerModelType: 'XPRINTER',
      model: 'XPRINTER',
      isBeepOrderSummaryPrinter: true,
      printerConnectType: 'LAN',
      isReceiptPrinter: true,
      isLabelPrinter: false,
      printerPaperWidth: 'Print80',
      printerId: '2dcdee38cd7f93f94ff3d1d255a24af3',
      modifiedDate: '2024-10-21T04:15:21.993Z',
      tags: ['order summary', 'kitchenA', 'kitchenB', 'kitchenC'],
      lanPort: 9100,
      isOnline: true,
      pingWorking: false,
    },
    {
      lanIp: '*************',
      isBuiltInPrinter: false,
      prevPrinterId: 'f84851e51eabc78674aff3913c1d4169',
      pingStatus: 0,
      migrationPrinterIdV3: null,
      printerName: 'LAN: *************',
      printerModelType: 'XPRINTER',
      model: 'XPRINTER',
      printerConnectType: 'LAN',
      isReceiptPrinter: false,
      isLabelPrinter: false,
      printerPaperWidth: 'Print80',
      printerId: 'f84851e51eabc78674aff3913c1d4169',
      modifiedDate: '2024-10-21T04:15:21.993Z',
      lanPort: 9100,
      isOnline: true,
      pingWorking: false,
    },
    {
      lanIp: '************',
      isBuiltInPrinter: false,
      prevPrinterId: 'c4451ba35f5cbe139bd9a06d37c52f16',
      pingStatus: 100,
      migrationPrinterIdV3: null,
      printerName: 'LAN: ************',
      printerModelType: 'XPRINTER',
      model: 'XPRINTER',
      printerConnectType: 'LAN',
      isReceiptPrinter: false,
      isLabelPrinter: false,
      printerPaperWidth: 'Print80',
      printerId: 'c4451ba35f5cbe139bd9a06d37c52f16',
      modifiedDate: '2024-10-17T10:07:43.518Z',
      tags: ['order summary'],
      lanPort: 9100,
      isOnline: false,
      pingWorking: false,
    },
  ];
  const modelProps = {
    kitchenItems: [
      {
        optionList: [],
        config: {
          kitchenSeparate: true,
          kitchenDocketVariantIsMultipleLine: false,
          enableTakeaway: true,
          kitchenPrinters: ['order summary', 'kitchenA', 'kitchenB', 'kitchenC'],
          orderSummaryPrinter: 'order summary',
          defaultKitchenPrinter: 'kitchenA',
          printEmployeeName: false,
          employeeId: '6269003c60e98900077a01c2',
          employeeName: 'feida zhang',
          enableReprintTitle: true,
          enableSubOrderFormat: true,
          timezone: 'Asia/Kuala_Lumpur',
          kitchenDocketFontSize: 1,
        },
        id: '1:62fc54aab4e3850007a8c56e',
        productId: '62fc54aab4e3850007a8c56e',
        product: {
          barcode: null,
          category: 'Food',
          inventoryType: '',
          isChild: true,
          isSerialized: false,
          isSoloParentDiscountApplicable: null,
          kitchenPrinter: 'kitchenB',
          modifiedTime: '2024-04-22T14:21:44.363Z',
          hasThumbnail: false,
          lastUpdateThumbnail: null,
          isBasicNecessitiesPH: false,
          parentProductId: null,
          priceType: 'fixed',
          productId: '62fc54aab4e3850007a8c56e',
          productType: 'Simple',
          skuId: null,
          tags: [],
          taxCode: '',
          title: 'kitchenB',
          trackInventory: true,
          unit: null,
          unitPrice: 111,
          variationsJson: '[]',
          variationValuesJson: '[]',
          priceBooks: [],
          beerDocketSettings: null,
          maxRefUnitPrice: null,
          minRefUnitPrice: null,
        },
        title: 'kitchenB',
        notesTitle: '',
        notes: null,
        quantity: 1,
        isQuantityPositive: true,
        quantityStr: '1',
        isUnitPrice: false,
        itemChannel: 1,
        isTakeaway: false,
        submitId: 'A',
        optionDescription: '',
        options: [],
        kitchenPrinter: 'kitchenB',
        bixOrderNumber: '1-2',
        bixReceiptDate: '2024.10.21 12:08',
      },
      {
        optionList: [],
        config: {
          kitchenSeparate: true,
          kitchenDocketVariantIsMultipleLine: false,
          enableTakeaway: true,
          kitchenPrinters: ['order summary', 'kitchenA', 'kitchenB', 'kitchenC'],
          orderSummaryPrinter: 'order summary',
          defaultKitchenPrinter: 'kitchenA',
          printEmployeeName: false,
          employeeId: '6269003c60e98900077a01c2',
          employeeName: 'feida zhang',
          enableReprintTitle: true,
          enableSubOrderFormat: true,
          timezone: 'Asia/Kuala_Lumpur',
          kitchenDocketFontSize: 1,
        },
        id: '2:62fc54bfb4e3850007a8c624',
        productId: '62fc54bfb4e3850007a8c624',
        product: {
          barcode: null,
          category: null,
          inventoryType: '',
          isChild: true,
          isSerialized: false,
          isSoloParentDiscountApplicable: null,
          kitchenPrinter: 'kitchenC',
          modifiedTime: '2024-04-22T14:21:55.877Z',
          hasThumbnail: false,
          lastUpdateThumbnail: null,
          isBasicNecessitiesPH: false,
          parentProductId: null,
          priceType: 'fixed',
          productId: '62fc54bfb4e3850007a8c624',
          productType: 'Simple',
          skuId: null,
          tags: [],
          taxCode: '',
          title: 'kitchenC',
          trackInventory: false,
          unit: null,
          unitPrice: 331.45,
          variationsJson: '[]',
          variationValuesJson: '[]',
          priceBooks: [],
          beerDocketSettings: null,
          maxRefUnitPrice: null,
          minRefUnitPrice: null,
        },
        title: 'kitchenC',
        notesTitle: '',
        notes: null,
        quantity: 1,
        isQuantityPositive: true,
        quantityStr: '1',
        isUnitPrice: false,
        itemChannel: 1,
        isTakeaway: false,
        submitId: 'A',
        optionDescription: '',
        options: [],
        kitchenPrinter: 'kitchenC',
        bixOrderNumber: '1-2',
        bixReceiptDate: '2024.10.21 12:08',
      },
      {
        optionList: [],
        config: {
          kitchenSeparate: true,
          kitchenDocketVariantIsMultipleLine: false,
          enableTakeaway: true,
          kitchenPrinters: ['order summary', 'kitchenA', 'kitchenB', 'kitchenC'],
          orderSummaryPrinter: 'order summary',
          defaultKitchenPrinter: 'kitchenA',
          printEmployeeName: false,
          employeeId: '6269003c60e98900077a01c2',
          employeeName: 'feida zhang',
          enableReprintTitle: true,
          enableSubOrderFormat: true,
          timezone: 'Asia/Kuala_Lumpur',
          kitchenDocketFontSize: 1,
        },
        id: '3:634a260d6137ba0007c7c447',
        productId: '634a260d6137ba0007c7c447',
        product: {
          barcode: null,
          category: 'Coffee',
          inventoryType: '',
          isChild: true,
          isSerialized: false,
          isSoloParentDiscountApplicable: false,
          kitchenPrinter: 'kitchenA',
          modifiedTime: '2024-08-12T03:55:46.226Z',
          hasThumbnail: true,
          lastUpdateThumbnail: '2024-08-12T03:55:46.226Z',
          isBasicNecessitiesPH: false,
          parentProductId: null,
          priceType: 'fixed',
          productId: '634a260d6137ba0007c7c447',
          productType: 'Simple',
          skuId: null,
          tags: [],
          taxCode: '',
          title: 'kitchenA',
          trackInventory: true,
          unit: null,
          unitPrice: 3212,
          variationsJson: '[]',
          variationValuesJson: '[]',
          priceBooks: [],
          beerDocketSettings: null,
          maxRefUnitPrice: null,
          minRefUnitPrice: null,
        },
        title: 'kitchenA',
        notesTitle: '',
        notes: null,
        quantity: 1,
        isQuantityPositive: true,
        quantityStr: '1',
        isUnitPrice: false,
        itemChannel: 1,
        isTakeaway: false,
        submitId: 'B',
        optionDescription: '',
        options: [],
        kitchenPrinter: 'kitchenA',
        bixOrderNumber: '1-2',
        bixReceiptDate: '2024.10.21 12:08',
      },
      {
        optionList: [
          {
            variationId: '6274a62985c4b30008c66e59',
            optionId: '6274a62985c4b30008c66e5c',
            optionValue: 'small',
            quantity: 1,
          },
        ],
        config: {
          kitchenSeparate: true,
          kitchenDocketVariantIsMultipleLine: false,
          enableTakeaway: true,
          kitchenPrinters: ['order summary', 'kitchenA', 'kitchenB', 'kitchenC'],
          orderSummaryPrinter: 'order summary',
          defaultKitchenPrinter: 'kitchenA',
          printEmployeeName: false,
          employeeId: '6269003c60e98900077a01c2',
          employeeName: 'feida zhang',
          enableReprintTitle: true,
          enableSubOrderFormat: true,
          timezone: 'Asia/Kuala_Lumpur',
          kitchenDocketFontSize: 1,
        },
        id: '4:6274a62985c4b30008c66e58:6274a62985c4b30008c66e59:6274a62985c4b30008c66e5c',
        productId: '6274a62985c4b30008c66e58',
        product: {
          barcode: null,
          category: 'Coffee',
          inventoryType: '',
          isChild: true,
          isSerialized: false,
          isSoloParentDiscountApplicable: null,
          kitchenPrinter: 'kitchenA',
          modifiedTime: '2024-05-13T07:07:48.253Z',
          hasThumbnail: true,
          lastUpdateThumbnail: '2022-05-06T04:38:01.949Z',
          isBasicNecessitiesPH: false,
          parentProductId: null,
          priceType: 'fixed',
          productId: '6274a62985c4b30008c66e58',
          productType: 'Configurable',
          skuId: null,
          tags: [],
          taxCode: '',
          title: 'CM-921',
          trackInventory: true,
          unit: null,
          unitPrice: 120,
          variationsJson:
            '[{"_id":"6274a62985c4b30008c66e59","name":"size","order":0,"variationType":"SingleChoice","allowMultiQty":false,"optionValues":[{"priceDiff":10,"markedSoldOut":false,"soldOutStoreIds":[],"_id":"6274a62985c4b30008c66e5c","order":0,"value":"small","isDefault":true,"conditionalPriceDiffs":[],"soldOutStores":[{"_id":"64449fcfc65cda81ae364e25","storeId":"6269003b60e98900077a01bf","duration":0}]},{"priceDiff":10,"markedSoldOut":false,"soldOutStoreIds":[],"_id":"6274a62985c4b30008c66e5b","order":1,"value":"medium","isDefault":false,"conditionalPriceDiffs":[],"soldOutStores":[{"_id":"6539e3f865a374f0e0e1f359","storeId":"6269003b60e98900077a01bf","duration":1698335999}]},{"priceDiff":10,"markedSoldOut":false,"soldOutStoreIds":[],"_id":"6274a62985c4b30008c66e5a","order":2,"value":"large","isDefault":false,"conditionalPriceDiffs":[],"soldOutStores":[{"_id":"6539e3fc65a3743d1be1f35d","storeId":"6269003b60e98900077a01bf","duration":1698335999}]}],"isModifier":true}]',
          variationValuesJson: '[]',
          priceBooks: [],
          beerDocketSettings: null,
          maxRefUnitPrice: null,
          minRefUnitPrice: null,
        },
        title: 'CM-921',
        notesTitle: '',
        notes: null,
        quantity: 1,
        isQuantityPositive: true,
        quantityStr: '1',
        isUnitPrice: false,
        itemChannel: 1,
        isTakeaway: false,
        submitId: 'B',
        optionDescription: 'small',
        options: [],
        kitchenPrinter: 'kitchenA',
        bixOrderNumber: '1-2',
        bixReceiptDate: '2024.10.21 12:08',
      },
      {
        optionList: [
          {
            variationId: '63d61225a929be0008659088',
            optionId: '63d61225a929be000865908a',
            optionValue: 'M',
            quantity: 1,
          },
          {
            variationId: '6274a68885c4b30008c66e87',
            optionId: '6274a68885c4b30008c66e88',
            optionValue: 'mini taro balls with jelly',
            quantity: 1,
          },
          {
            variationId: '6274a68885c4b30008c66e87',
            optionId: '63d61225a929be0008659091',
            optionValue: 'grass jelly boba mango sago balls',
            quantity: 1,
          },
        ],
        config: {
          kitchenSeparate: true,
          kitchenDocketVariantIsMultipleLine: false,
          enableTakeaway: true,
          kitchenPrinters: ['order summary', 'kitchenA', 'kitchenB', 'kitchenC'],
          orderSummaryPrinter: 'order summary',
          defaultKitchenPrinter: 'kitchenA',
          printEmployeeName: false,
          employeeId: '6269003c60e98900077a01c2',
          employeeName: 'feida zhang',
          enableReprintTitle: true,
          enableSubOrderFormat: true,
          timezone: 'Asia/Kuala_Lumpur',
          kitchenDocketFontSize: 1,
        },
        id: '5:6274a68885c4b30008c66e86:63d61225a929be0008659088:63d61225a929be000865908a:6274a68885c4b30008c66e87:6274a68885c4b30008c66e88:6274a68885c4b30008c66e87:63d61225a929be0008659091',
        productId: '6274a68885c4b30008c66e86',
        product: {
          barcode: null,
          category: 'Coffee',
          inventoryType: '',
          isChild: true,
          isSerialized: false,
          isSoloParentDiscountApplicable: false,
          kitchenPrinter: 'kitchenA',
          modifiedTime: '2024-08-14T02:46:54.951Z',
          hasThumbnail: true,
          lastUpdateThumbnail: '2024-08-14T02:46:54.951Z',
          isBasicNecessitiesPH: false,
          parentProductId: null,
          priceType: 'fixed',
          productId: '6274a68885c4b30008c66e86',
          productType: 'Configurable',
          skuId: null,
          tags: [],
          taxCode: '',
          title: 'CM-921-01',
          trackInventory: false,
          unit: null,
          unitPrice: 111,
          variationsJson:
            '[{"_id":"63d61225a929be0008659088","name":"Size","order":0,"variationType":"SingleChoice","allowMultiQty":false,"optionValues":[{"priceDiff":0,"markedSoldOut":false,"soldOutStoreIds":[],"_id":"63d61225a929be0008659089","order":0,"value":"我是真的大大大大大大大大大大大大大","isDefault":true,"conditionalPriceDiffs":[],"soldOutStores":[]},{"priceDiff":0,"markedSoldOut":false,"soldOutStoreIds":[],"_id":"63d61225a929be000865908a","order":1,"value":"M","isDefault":false,"conditionalPriceDiffs":[],"soldOutStores":[]},{"priceDiff":0,"markedSoldOut":false,"soldOutStoreIds":[],"_id":"63d61225a929be000865908b","order":2,"value":"L","isDefault":false,"conditionalPriceDiffs":[],"soldOutStores":[]},{"priceDiff":0,"markedSoldOut":false,"soldOutStoreIds":[],"_id":"63d61225a929be000865908c","order":3,"value":"XL","isDefault":false,"conditionalPriceDiffs":[],"soldOutStores":[]},{"priceDiff":0,"markedSoldOut":false,"soldOutStoreIds":[],"_id":"63d61225a929be000865908d","order":4,"value":"XXL","isDefault":false,"conditionalPriceDiffs":[],"soldOutStores":[]}],"isModifier":true},{"_id":"6274a68885c4b30008c66e87","name":"addon","order":1,"variationType":"MultipleChoice","allowMultiQty":true,"optionValues":[{"priceDiff":10,"markedSoldOut":false,"soldOutStoreIds":[],"_id":"6274a68885c4b30008c66e89","order":0,"value":"我是真的大大大大大大大大大大大大大大大大大大大大大大大大","conditionalPriceDiffs":[],"soldOutStores":[]},{"priceDiff":10,"markedSoldOut":false,"soldOutStoreIds":[],"_id":"6274a68885c4b30008c66e88","order":1,"value":"mini taro balls with jelly","conditionalPriceDiffs":[],"soldOutStores":[]},{"priceDiff":0,"markedSoldOut":false,"soldOutStoreIds":[],"_id":"63d61225a929be0008659091","order":2,"value":"grass jelly boba mango sago balls","conditionalPriceDiffs":[],"soldOutStores":[]},{"priceDiff":0,"markedSoldOut":false,"soldOutStoreIds":[],"_id":"63d61225a929be0008659092","order":3,"value":"sour plum jelly base and pumpkin sago barley mix","conditionalPriceDiffs":[],"soldOutStores":[]},{"priceDiff":0,"markedSoldOut":false,"soldOutStoreIds":[],"_id":"63d61225a929be0008659093","order":4,"value":"drink","conditionalPriceDiffs":[],"soldOutStores":[]}],"isModifier":true,"enableSelectionAmountLimit":false,"minSelectionAmount":0}]',
          variationValuesJson: '[]',
          priceBooks: [],
          beerDocketSettings: null,
          maxRefUnitPrice: null,
          minRefUnitPrice: null,
        },
        title: 'CM-921-01',
        notesTitle: '',
        notes: null,
        quantity: 1,
        isQuantityPositive: true,
        quantityStr: '1',
        isUnitPrice: false,
        itemChannel: 1,
        isTakeaway: false,
        submitId: 'A',
        optionDescription: 'M, mini taro balls with jelly, grass jelly boba mango sago balls',
        options: [],
        kitchenPrinter: 'kitchenA',
        bixOrderNumber: '1-2',
        bixReceiptDate: '2024.10.21 12:08',
      },
    ],
    summaryItems: [
      {
        optionList: [],
        config: {
          kitchenSeparate: true,
          kitchenDocketVariantIsMultipleLine: false,
          enableTakeaway: true,
          kitchenPrinters: ['order summary', 'kitchenA', 'kitchenB', 'kitchenC'],
          orderSummaryPrinter: 'order summary',
          defaultKitchenPrinter: 'kitchenA',
          printEmployeeName: false,
          employeeId: '6269003c60e98900077a01c2',
          employeeName: 'feida zhang',
          enableReprintTitle: true,
          enableSubOrderFormat: true,
          timezone: 'Asia/Kuala_Lumpur',
          kitchenDocketFontSize: 1,
        },
        id: '1:62fc54aab4e3850007a8c56e',
        productId: '62fc54aab4e3850007a8c56e',
        product: {
          barcode: null,
          category: 'Food',
          inventoryType: '',
          isChild: true,
          isSerialized: false,
          isSoloParentDiscountApplicable: null,
          kitchenPrinter: 'kitchenB',
          modifiedTime: '2024-04-22T14:21:44.363Z',
          hasThumbnail: false,
          lastUpdateThumbnail: null,
          isBasicNecessitiesPH: false,
          parentProductId: null,
          priceType: 'fixed',
          productId: '62fc54aab4e3850007a8c56e',
          productType: 'Simple',
          skuId: null,
          tags: [],
          taxCode: '',
          title: 'kitchenB',
          trackInventory: true,
          unit: null,
          unitPrice: 111,
          variationsJson: '[]',
          variationValuesJson: '[]',
          priceBooks: [],
          beerDocketSettings: null,
          maxRefUnitPrice: null,
          minRefUnitPrice: null,
        },
        title: 'kitchenB',
        notesTitle: '',
        notes: null,
        quantity: 1,
        isQuantityPositive: true,
        quantityStr: '1',
        isUnitPrice: false,
        itemChannel: 1,
        isTakeaway: false,
        submitId: 'A',
        optionDescription: '',
        options: [],
        kitchenPrinter: '__BeepQR__',
        bixOrderNumber: '1-2',
        bixReceiptDate: '2024.10.21 12:08',
      },
      {
        optionList: [],
        config: {
          kitchenSeparate: true,
          kitchenDocketVariantIsMultipleLine: false,
          enableTakeaway: true,
          kitchenPrinters: ['order summary', 'kitchenA', 'kitchenB', 'kitchenC'],
          orderSummaryPrinter: 'order summary',
          defaultKitchenPrinter: 'kitchenA',
          printEmployeeName: false,
          employeeId: '6269003c60e98900077a01c2',
          employeeName: 'feida zhang',
          enableReprintTitle: true,
          enableSubOrderFormat: true,
          timezone: 'Asia/Kuala_Lumpur',
          kitchenDocketFontSize: 1,
        },
        id: '2:62fc54bfb4e3850007a8c624',
        productId: '62fc54bfb4e3850007a8c624',
        product: {
          barcode: null,
          category: null,
          inventoryType: '',
          isChild: true,
          isSerialized: false,
          isSoloParentDiscountApplicable: null,
          kitchenPrinter: 'kitchenC',
          modifiedTime: '2024-04-22T14:21:55.877Z',
          hasThumbnail: false,
          lastUpdateThumbnail: null,
          isBasicNecessitiesPH: false,
          parentProductId: null,
          priceType: 'fixed',
          productId: '62fc54bfb4e3850007a8c624',
          productType: 'Simple',
          skuId: null,
          tags: [],
          taxCode: '',
          title: 'kitchenC',
          trackInventory: false,
          unit: null,
          unitPrice: 331.45,
          variationsJson: '[]',
          variationValuesJson: '[]',
          priceBooks: [],
          beerDocketSettings: null,
          maxRefUnitPrice: null,
          minRefUnitPrice: null,
        },
        title: 'kitchenC',
        notesTitle: '',
        notes: null,
        quantity: 1,
        isQuantityPositive: true,
        quantityStr: '1',
        isUnitPrice: false,
        itemChannel: 1,
        isTakeaway: false,
        submitId: 'A',
        optionDescription: '',
        options: [],
        kitchenPrinter: '__BeepQR__',
        bixOrderNumber: '1-2',
        bixReceiptDate: '2024.10.21 12:08',
      },
      {
        optionList: [],
        config: {
          kitchenSeparate: true,
          kitchenDocketVariantIsMultipleLine: false,
          enableTakeaway: true,
          kitchenPrinters: ['order summary', 'kitchenA', 'kitchenB', 'kitchenC'],
          orderSummaryPrinter: 'order summary',
          defaultKitchenPrinter: 'kitchenA',
          printEmployeeName: false,
          employeeId: '6269003c60e98900077a01c2',
          employeeName: 'feida zhang',
          enableReprintTitle: true,
          enableSubOrderFormat: true,
          timezone: 'Asia/Kuala_Lumpur',
          kitchenDocketFontSize: 1,
        },
        id: '3:634a260d6137ba0007c7c447',
        productId: '634a260d6137ba0007c7c447',
        product: {
          barcode: null,
          category: 'Coffee',
          inventoryType: '',
          isChild: true,
          isSerialized: false,
          isSoloParentDiscountApplicable: false,
          kitchenPrinter: 'kitchenA',
          modifiedTime: '2024-08-12T03:55:46.226Z',
          hasThumbnail: true,
          lastUpdateThumbnail: '2024-08-12T03:55:46.226Z',
          isBasicNecessitiesPH: false,
          parentProductId: null,
          priceType: 'fixed',
          productId: '634a260d6137ba0007c7c447',
          productType: 'Simple',
          skuId: null,
          tags: [],
          taxCode: '',
          title: 'kitchenA',
          trackInventory: true,
          unit: null,
          unitPrice: 3212,
          variationsJson: '[]',
          variationValuesJson: '[]',
          priceBooks: [],
          beerDocketSettings: null,
          maxRefUnitPrice: null,
          minRefUnitPrice: null,
        },
        title: 'kitchenA',
        notesTitle: '',
        notes: null,
        quantity: 1,
        isQuantityPositive: true,
        quantityStr: '1',
        isUnitPrice: false,
        itemChannel: 1,
        isTakeaway: false,
        submitId: 'B',
        optionDescription: '',
        options: [],
        kitchenPrinter: '__BeepQR__',
        bixOrderNumber: '1-2',
        bixReceiptDate: '2024.10.21 12:08',
      },
      {
        optionList: [
          {
            variationId: '6274a62985c4b30008c66e59',
            optionId: '6274a62985c4b30008c66e5c',
            optionValue: 'small',
            quantity: 1,
          },
        ],
        config: {
          kitchenSeparate: true,
          kitchenDocketVariantIsMultipleLine: false,
          enableTakeaway: true,
          kitchenPrinters: ['order summary', 'kitchenA', 'kitchenB', 'kitchenC'],
          orderSummaryPrinter: 'order summary',
          defaultKitchenPrinter: 'kitchenA',
          printEmployeeName: false,
          employeeId: '6269003c60e98900077a01c2',
          employeeName: 'feida zhang',
          enableReprintTitle: true,
          enableSubOrderFormat: true,
          timezone: 'Asia/Kuala_Lumpur',
          kitchenDocketFontSize: 1,
        },
        id: '4:6274a62985c4b30008c66e58:6274a62985c4b30008c66e59:6274a62985c4b30008c66e5c',
        productId: '6274a62985c4b30008c66e58',
        product: {
          barcode: null,
          category: 'Coffee',
          inventoryType: '',
          isChild: true,
          isSerialized: false,
          isSoloParentDiscountApplicable: null,
          kitchenPrinter: 'kitchenA',
          modifiedTime: '2024-05-13T07:07:48.253Z',
          hasThumbnail: true,
          lastUpdateThumbnail: '2022-05-06T04:38:01.949Z',
          isBasicNecessitiesPH: false,
          parentProductId: null,
          priceType: 'fixed',
          productId: '6274a62985c4b30008c66e58',
          productType: 'Configurable',
          skuId: null,
          tags: [],
          taxCode: '',
          title: 'CM-921',
          trackInventory: true,
          unit: null,
          unitPrice: 120,
          variationsJson:
            '[{"_id":"6274a62985c4b30008c66e59","name":"size","order":0,"variationType":"SingleChoice","allowMultiQty":false,"optionValues":[{"priceDiff":10,"markedSoldOut":false,"soldOutStoreIds":[],"_id":"6274a62985c4b30008c66e5c","order":0,"value":"small","isDefault":true,"conditionalPriceDiffs":[],"soldOutStores":[{"_id":"64449fcfc65cda81ae364e25","storeId":"6269003b60e98900077a01bf","duration":0}]},{"priceDiff":10,"markedSoldOut":false,"soldOutStoreIds":[],"_id":"6274a62985c4b30008c66e5b","order":1,"value":"medium","isDefault":false,"conditionalPriceDiffs":[],"soldOutStores":[{"_id":"6539e3f865a374f0e0e1f359","storeId":"6269003b60e98900077a01bf","duration":1698335999}]},{"priceDiff":10,"markedSoldOut":false,"soldOutStoreIds":[],"_id":"6274a62985c4b30008c66e5a","order":2,"value":"large","isDefault":false,"conditionalPriceDiffs":[],"soldOutStores":[{"_id":"6539e3fc65a3743d1be1f35d","storeId":"6269003b60e98900077a01bf","duration":1698335999}]}],"isModifier":true}]',
          variationValuesJson: '[]',
          priceBooks: [],
          beerDocketSettings: null,
          maxRefUnitPrice: null,
          minRefUnitPrice: null,
        },
        title: 'CM-921',
        notesTitle: '',
        notes: null,
        quantity: 1,
        isQuantityPositive: true,
        quantityStr: '1',
        isUnitPrice: false,
        itemChannel: 1,
        isTakeaway: false,
        submitId: 'B',
        optionDescription: 'small',
        options: [],
        kitchenPrinter: '__BeepQR__',
        bixOrderNumber: '1-2',
        bixReceiptDate: '2024.10.21 12:08',
      },
      {
        optionList: [
          {
            variationId: '63d61225a929be0008659088',
            optionId: '63d61225a929be000865908a',
            optionValue: 'M',
            quantity: 1,
          },
          {
            variationId: '6274a68885c4b30008c66e87',
            optionId: '6274a68885c4b30008c66e88',
            optionValue: 'mini taro balls with jelly',
            quantity: 1,
          },
          {
            variationId: '6274a68885c4b30008c66e87',
            optionId: '63d61225a929be0008659091',
            optionValue: 'grass jelly boba mango sago balls',
            quantity: 1,
          },
        ],
        config: {
          kitchenSeparate: true,
          kitchenDocketVariantIsMultipleLine: false,
          enableTakeaway: true,
          kitchenPrinters: ['order summary', 'kitchenA', 'kitchenB', 'kitchenC'],
          orderSummaryPrinter: 'order summary',
          defaultKitchenPrinter: 'kitchenA',
          printEmployeeName: false,
          employeeId: '6269003c60e98900077a01c2',
          employeeName: 'feida zhang',
          enableReprintTitle: true,
          enableSubOrderFormat: true,
          timezone: 'Asia/Kuala_Lumpur',
          kitchenDocketFontSize: 1,
        },
        id: '5:6274a68885c4b30008c66e86:63d61225a929be0008659088:63d61225a929be000865908a:6274a68885c4b30008c66e87:6274a68885c4b30008c66e88:6274a68885c4b30008c66e87:63d61225a929be0008659091',
        productId: '6274a68885c4b30008c66e86',
        product: {
          barcode: null,
          category: 'Coffee',
          inventoryType: '',
          isChild: true,
          isSerialized: false,
          isSoloParentDiscountApplicable: false,
          kitchenPrinter: 'kitchenA',
          modifiedTime: '2024-08-14T02:46:54.951Z',
          hasThumbnail: true,
          lastUpdateThumbnail: '2024-08-14T02:46:54.951Z',
          isBasicNecessitiesPH: false,
          parentProductId: null,
          priceType: 'fixed',
          productId: '6274a68885c4b30008c66e86',
          productType: 'Configurable',
          skuId: null,
          tags: [],
          taxCode: '',
          title: 'CM-921-01',
          trackInventory: false,
          unit: null,
          unitPrice: 111,
          variationsJson:
            '[{"_id":"63d61225a929be0008659088","name":"Size","order":0,"variationType":"SingleChoice","allowMultiQty":false,"optionValues":[{"priceDiff":0,"markedSoldOut":false,"soldOutStoreIds":[],"_id":"63d61225a929be0008659089","order":0,"value":"我是真的大大大大大大大大大大大大大","isDefault":true,"conditionalPriceDiffs":[],"soldOutStores":[]},{"priceDiff":0,"markedSoldOut":false,"soldOutStoreIds":[],"_id":"63d61225a929be000865908a","order":1,"value":"M","isDefault":false,"conditionalPriceDiffs":[],"soldOutStores":[]},{"priceDiff":0,"markedSoldOut":false,"soldOutStoreIds":[],"_id":"63d61225a929be000865908b","order":2,"value":"L","isDefault":false,"conditionalPriceDiffs":[],"soldOutStores":[]},{"priceDiff":0,"markedSoldOut":false,"soldOutStoreIds":[],"_id":"63d61225a929be000865908c","order":3,"value":"XL","isDefault":false,"conditionalPriceDiffs":[],"soldOutStores":[]},{"priceDiff":0,"markedSoldOut":false,"soldOutStoreIds":[],"_id":"63d61225a929be000865908d","order":4,"value":"XXL","isDefault":false,"conditionalPriceDiffs":[],"soldOutStores":[]}],"isModifier":true},{"_id":"6274a68885c4b30008c66e87","name":"addon","order":1,"variationType":"MultipleChoice","allowMultiQty":true,"optionValues":[{"priceDiff":10,"markedSoldOut":false,"soldOutStoreIds":[],"_id":"6274a68885c4b30008c66e89","order":0,"value":"我是真的大大大大大大大大大大大大大大大大大大大大大大大大大大","conditionalPriceDiffs":[],"soldOutStores":[]},{"priceDiff":10,"markedSoldOut":false,"soldOutStoreIds":[],"_id":"6274a68885c4b30008c66e88","order":1,"value":"mini taro balls with jelly","conditionalPriceDiffs":[],"soldOutStores":[]},{"priceDiff":0,"markedSoldOut":false,"soldOutStoreIds":[],"_id":"63d61225a929be0008659091","order":2,"value":"grass jelly boba mango sago balls","conditionalPriceDiffs":[],"soldOutStores":[]},{"priceDiff":0,"markedSoldOut":false,"soldOutStoreIds":[],"_id":"63d61225a929be0008659092","order":3,"value":"sour plum jelly base and pumpkin sago barley mix","conditionalPriceDiffs":[],"soldOutStores":[]},{"priceDiff":0,"markedSoldOut":false,"soldOutStoreIds":[],"_id":"63d61225a929be0008659093","order":4,"value":"drink","conditionalPriceDiffs":[],"soldOutStores":[]}],"isModifier":true,"enableSelectionAmountLimit":false,"minSelectionAmount":0}]',
          variationValuesJson: '[]',
          priceBooks: [],
          beerDocketSettings: null,
          maxRefUnitPrice: null,
          minRefUnitPrice: null,
        },
        title: 'CM-921-01',
        notesTitle: '',
        notes: null,
        quantity: 1,
        isQuantityPositive: true,
        quantityStr: '1',
        isUnitPrice: false,
        itemChannel: 1,
        isTakeaway: false,
        submitId: 'A',
        optionDescription: 'M, mini taro balls with jelly, grass jelly boba mango sago balls',
        options: [],
        kitchenPrinter: '__BeepQR__',
        bixOrderNumber: '1-2',
        bixReceiptDate: '2024.10.21 12:08',
      },
    ],
    config: {
      kitchenSeparate: true,
      kitchenDocketVariantIsMultipleLine: false,
      enableTakeaway: true,
      kitchenPrinters: ['order summary', 'kitchenA', 'kitchenB', 'kitchenC'],
      orderSummaryPrinter: 'order summary',
      defaultKitchenPrinter: 'kitchenA',
      printEmployeeName: false,
      employeeId: '6269003c60e98900077a01c2',
      employeeName: 'feida zhang',
      enableReprintTitle: true,
      enableSubOrderFormat: true,
      timezone: 'Asia/Kuala_Lumpur',
      kitchenDocketFontSize: 1,
      trackConfig: {
        trackEnabled: true,
        trackThreshold: 10,
        trackSlope: 0.93,
      },
    },
  };
  const kitchenTransaction = new KitchenTransaction(mockTransaction);
  const printerSetting = new PrinterSetting(printers);
  const model = new KitchenPrintModel(kitchenTransaction, modelProps.config);
  model.receiptDate = '2024.10.21 14:06:56';
  model.bixReceiptDate = '2024.10.21 14:06';
  model.setSubOrderFormat(true);
  model.setIsReprint(true);
  model.setKitchenItems(modelProps.kitchenItems);
  model.setSummaryItems(modelProps.summaryItems);

  const kitchenData = model.getKitchenRequestData(printerSetting, false);
  const summaryData = model.getSummaryRequestData(printerSetting, BEEP_ORDER_SUMMARY_PRINTER_TAG, false);

  expect(kitchenData).toEqual(
    kitchen.map(it => {
      it.data.minContentHeight = 289.088;
      return it;
    })
  );
  expect(summaryData).toEqual(
    summary.map(it => {
      it.data.minContentHeight = 562.88;
      return it;
    })
  );
});
