export const summary = [
  {
    printerId: '2dcdee38cd7f93f94ff3d1d255a24af3',
    businessType: 'KITCHEN_TICKET',
    data: {
      reprintTitle: 'Reprinted',
      previousTableId: null,
      receiptDate: '2024.10.21 14:06:56',
      bixReceiptDate: '2024.10.21 14:06',
      isOpenOrder: false,
      isBeepOrder: true,
      kitchenDocketVariantIsMultipleLine: false,
      orderNumberTitle: 'Table #',
      orderTypeName: null,
      extraOrderNumber: 'Order #: 0006',
      orderNumber: '1-2',
      isTakeaway: false,
      noteTitle: 'Note',
      note: '',
      pickUpId: '0006',
      tableId: '1-2',
      bixOrderNumber: '1-2',
      purchasedItems: null,
      takeawayItems: null,
      takeawayTitle: 'Takeaway',
      employeeName: undefined,
      pax: undefined,
      minContentHeight: 562.88,
      subOrders: [
        {
          purchasedItems: [
            {
              id: '1:62fc54aab4e3850007a8c56e',
              title: 'kitchenB',
              options: [],
              optionDescription: '',
              notesTitle: '',
              notes: null,
              isQuantityPositive: true,
              quantityStr: '1',
              quantity: 1,
              isUnitPrice: false,
              bixOrderNumber: '1-2',
              bixReceiptDate: '2024.10.21 14:06',
              itemChannel: 1,
              isTakeaway: false,
              submitId: 'A',
            },
            {
              id: '2:62fc54bfb4e3850007a8c624',
              title: 'kitchenC',
              options: [],
              optionDescription: '',
              notesTitle: '',
              notes: null,
              isQuantityPositive: true,
              quantityStr: '1',
              quantity: 1,
              isUnitPrice: false,
              bixOrderNumber: '1-2',
              bixReceiptDate: '2024.10.21 14:06',
              itemChannel: 1,
              isTakeaway: false,
              submitId: 'A',
            },
            {
              id: '5:6274a68885c4b30008c66e86:63d61225a929be0008659088:63d61225a929be000865908a:6274a68885c4b30008c66e87:6274a68885c4b30008c66e88:6274a68885c4b30008c66e87:63d61225a929be0008659091',
              title: 'CM-921-01',
              options: [],
              optionDescription: '',
              notesTitle: '',
              notes: null,
              isQuantityPositive: true,
              quantityStr: '1',
              quantity: 1,
              isUnitPrice: false,
              bixOrderNumber: '1-2',
              bixReceiptDate: '2024.10.21 14:06',
              itemChannel: 1,
              isTakeaway: false,
              submitId: 'A',
            },
          ],
          takeawayItems: null,
          isLastSubOrder: false,
          note: 'subOrderAA',
        },
        {
          purchasedItems: [
            {
              id: '3:634a260d6137ba0007c7c447',
              title: 'kitchenA',
              options: [],
              optionDescription: '',
              notesTitle: '',
              notes: null,
              isQuantityPositive: true,
              quantityStr: '1',
              quantity: 1,
              isUnitPrice: false,
              bixOrderNumber: '1-2',
              bixReceiptDate: '2024.10.21 14:06',
              itemChannel: 1,
              isTakeaway: false,
              submitId: 'B',
            },
            {
              id: '4:6274a62985c4b30008c66e58:6274a62985c4b30008c66e59:6274a62985c4b30008c66e5c',
              title: 'CM-921',
              options: [],
              optionDescription: '',
              notesTitle: '',
              notes: null,
              isQuantityPositive: true,
              quantityStr: '1',
              quantity: 1,
              isUnitPrice: false,
              bixOrderNumber: '1-2',
              bixReceiptDate: '2024.10.21 14:06',
              itemChannel: 1,
              isTakeaway: false,
              submitId: 'B',
            },
          ],
          takeawayItems: null,
          isLastSubOrder: true,
          note: 'subOrderBB',
        },
      ],
      receiptFontScale: 1,
      docketTitle: 'Order Summary',
    },
  },
];

export const kitchen = [
  {
    printerId: '2dcdee38cd7f93f94ff3d1d255a24af3',
    businessType: 'KITCHEN_TICKET',
    data: {
      reprintTitle: 'Reprinted',
      previousTableId: null,
      receiptDate: '2024.10.21 14:06:56',
      bixReceiptDate: '2024.10.21 14:06',
      isOpenOrder: false,
      isBeepOrder: true,
      kitchenDocketVariantIsMultipleLine: false,
      orderNumberTitle: 'Table #',
      orderTypeName: null,
      extraOrderNumber: 'Order #: 0006',
      orderNumber: '1-2',
      isTakeaway: false,
      noteTitle: 'Note',
      note: '',
      pickUpId: '0006',
      tableId: '1-2',
      bixOrderNumber: '1-2',
      purchasedItems: null,
      takeawayItems: null,
      takeawayTitle: 'Takeaway',
      employeeName: undefined,
      pax: undefined,
      minContentHeight: 289.088,
      subOrders: [
        {
          purchasedItems: [
            {
              id: '1:62fc54aab4e3850007a8c56e',
              title: 'kitchenB',
              options: [],
              optionDescription: '',
              notesTitle: '',
              notes: null,
              isQuantityPositive: true,
              quantityStr: '1',
              quantity: 1,
              isUnitPrice: false,
              bixOrderNumber: '1-2',
              bixReceiptDate: '2024.10.21 14:06',
              itemChannel: 1,
              isTakeaway: false,
              submitId: 'A',
            },
          ],
          takeawayItems: null,
          isLastSubOrder: true,
          note: 'subOrderAA',
        },
      ],
      receiptFontScale: 1,
    },
  },
  {
    printerId: '2dcdee38cd7f93f94ff3d1d255a24af3',
    businessType: 'KITCHEN_TICKET',
    data: {
      reprintTitle: 'Reprinted',
      previousTableId: null,
      receiptDate: '2024.10.21 14:06:56',
      bixReceiptDate: '2024.10.21 14:06',
      isOpenOrder: false,
      isBeepOrder: true,
      kitchenDocketVariantIsMultipleLine: false,
      orderNumberTitle: 'Table #',
      orderTypeName: null,
      extraOrderNumber: 'Order #: 0006',
      orderNumber: '1-2',
      isTakeaway: false,
      noteTitle: 'Note',
      note: '',
      pickUpId: '0006',
      tableId: '1-2',
      bixOrderNumber: '1-2',
      purchasedItems: null,
      takeawayItems: null,
      takeawayTitle: 'Takeaway',
      employeeName: undefined,
      pax: undefined,
      minContentHeight: 289.088,
      subOrders: [
        {
          purchasedItems: [
            {
              id: '2:62fc54bfb4e3850007a8c624',
              title: 'kitchenC',
              options: [],
              optionDescription: '',
              notesTitle: '',
              notes: null,
              isQuantityPositive: true,
              quantityStr: '1',
              quantity: 1,
              isUnitPrice: false,
              bixOrderNumber: '1-2',
              bixReceiptDate: '2024.10.21 14:06',
              itemChannel: 1,
              isTakeaway: false,
              submitId: 'A',
            },
          ],
          takeawayItems: null,
          isLastSubOrder: true,
          note: 'subOrderAA',
        },
      ],
      receiptFontScale: 1,
    },
  },
  {
    printerId: '2dcdee38cd7f93f94ff3d1d255a24af3',
    businessType: 'KITCHEN_TICKET',
    data: {
      reprintTitle: 'Reprinted',
      previousTableId: null,
      receiptDate: '2024.10.21 14:06:56',
      bixReceiptDate: '2024.10.21 14:06',
      isOpenOrder: false,
      isBeepOrder: true,
      kitchenDocketVariantIsMultipleLine: false,
      orderNumberTitle: 'Table #',
      orderTypeName: null,
      extraOrderNumber: 'Order #: 0006',
      orderNumber: '1-2',
      isTakeaway: false,
      noteTitle: 'Note',
      note: '',
      pickUpId: '0006',
      tableId: '1-2',
      bixOrderNumber: '1-2',
      purchasedItems: null,
      takeawayItems: null,
      takeawayTitle: 'Takeaway',
      employeeName: undefined,
      pax: undefined,
      minContentHeight: 289.088,
      subOrders: [
        {
          purchasedItems: [
            {
              id: '3:634a260d6137ba0007c7c447',
              title: 'kitchenA',
              options: [],
              optionDescription: '',
              notesTitle: '',
              notes: null,
              isQuantityPositive: true,
              quantityStr: '1',
              quantity: 1,
              isUnitPrice: false,
              bixOrderNumber: '1-2',
              bixReceiptDate: '2024.10.21 14:06',
              itemChannel: 1,
              isTakeaway: false,
              submitId: 'B',
            },
          ],
          takeawayItems: null,
          isLastSubOrder: true,
          note: 'subOrderBB',
        },
      ],
      receiptFontScale: 1,
    },
  },
  {
    printerId: '2dcdee38cd7f93f94ff3d1d255a24af3',
    businessType: 'KITCHEN_TICKET',
    data: {
      reprintTitle: 'Reprinted',
      previousTableId: null,
      receiptDate: '2024.10.21 14:06:56',
      bixReceiptDate: '2024.10.21 14:06',
      isOpenOrder: false,
      isBeepOrder: true,
      kitchenDocketVariantIsMultipleLine: false,
      orderNumberTitle: 'Table #',
      orderTypeName: null,
      extraOrderNumber: 'Order #: 0006',
      orderNumber: '1-2',
      isTakeaway: false,
      noteTitle: 'Note',
      note: '',
      pickUpId: '0006',
      tableId: '1-2',
      bixOrderNumber: '1-2',
      purchasedItems: null,
      takeawayItems: null,
      takeawayTitle: 'Takeaway',
      employeeName: undefined,
      pax: undefined,
      minContentHeight: 289.088,
      subOrders: [
        {
          purchasedItems: [
            {
              id: '4:6274a62985c4b30008c66e58:6274a62985c4b30008c66e59:6274a62985c4b30008c66e5c',
              title: 'CM-921',
              options: [],
              optionDescription: '',
              notesTitle: '',
              notes: null,
              isQuantityPositive: true,
              quantityStr: '1',
              quantity: 1,
              isUnitPrice: false,
              bixOrderNumber: '1-2',
              bixReceiptDate: '2024.10.21 14:06',
              itemChannel: 1,
              isTakeaway: false,
              submitId: 'B',
            },
          ],
          takeawayItems: null,
          isLastSubOrder: true,
          note: 'subOrderBB',
        },
      ],
      receiptFontScale: 1,
    },
  },
  {
    printerId: '2dcdee38cd7f93f94ff3d1d255a24af3',
    businessType: 'KITCHEN_TICKET',
    data: {
      reprintTitle: 'Reprinted',
      previousTableId: null,
      receiptDate: '2024.10.21 14:06:56',
      bixReceiptDate: '2024.10.21 14:06',
      isOpenOrder: false,
      isBeepOrder: true,
      kitchenDocketVariantIsMultipleLine: false,
      orderNumberTitle: 'Table #',
      orderTypeName: null,
      extraOrderNumber: 'Order #: 0006',
      orderNumber: '1-2',
      isTakeaway: false,
      noteTitle: 'Note',
      note: '',
      pickUpId: '0006',
      tableId: '1-2',
      bixOrderNumber: '1-2',
      purchasedItems: null,
      takeawayItems: null,
      takeawayTitle: 'Takeaway',
      employeeName: undefined,
      pax: undefined,
      minContentHeight: 289.088,
      subOrders: [
        {
          purchasedItems: [
            {
              id: '5:6274a68885c4b30008c66e86:63d61225a929be0008659088:63d61225a929be000865908a:6274a68885c4b30008c66e87:6274a68885c4b30008c66e88:6274a68885c4b30008c66e87:63d61225a929be0008659091',
              title: 'CM-921-01',
              options: [],
              optionDescription: '',
              notesTitle: '',
              notes: null,
              isQuantityPositive: true,
              quantityStr: '1',
              quantity: 1,
              isUnitPrice: false,
              bixOrderNumber: '1-2',
              bixReceiptDate: '2024.10.21 14:06',
              itemChannel: 1,
              isTakeaway: false,
              submitId: 'A',
            },
          ],
          takeawayItems: null,
          isLastSubOrder: true,
          note: 'subOrderAA',
        },
      ],
      receiptFontScale: 1,
    },
  },
];
