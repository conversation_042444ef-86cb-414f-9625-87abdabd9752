import Intercom from '@intercom/intercom-react-native';
import * as Immutable from 'immutable';
import { runSaga } from 'redux-saga';
import { expectSaga, testSaga } from 'redux-saga-test-plan';
import {
  cleanKdsSetting,
  cleanNcsSetting,
  clearEWalletPay,
  clearEmployee,
  clearLocalCountryMap,
  clearMRSState,
  clearSetting,
  clearStoreInfo,
  getActivationStatus,
  getTableLayout,
  resetAWSConfig,
  saveTableLayoutSections,
  setQuickLayout,
  setQuickLayoutId,
  setShareQuickLayoutStores,
  setShiftStatus,
  setStoreInfo,
  setSyncInfo,
  stopCheckCustomerInfoFromQR,
  syncEmployeeActivity,
  syncEmployeeActivityBegin,
  syncEmployees,
  syncLastZReadingCloseTime,
  syncPriceBook,
  syncProducts,
  syncPromotions,
  syncQuickSelectLayout,
  syncSequentialStartInfo,
  syncShiftReport,
  syncStoreInfo,
  syncTransaction,
  unRegisterMrsAction,
  unregisterWebsocket,
  updateGrowthBook,
  updateSequence,
} from '../../ts/actions';
import { resetCfd } from '../../ts/actions/cfd';
import DAL from '../../ts/dal';
import {
  SYNC_PRODUCTS_LIMIT,
  cleanData,
  dataSync,
  forceDeactivation,
  setPromotionsSaga,
  setStoreInfoSaga,
  setSyncEmployeesSaga,
  setSyncPriceBookSaga,
  setSyncProductsSaga,
  syncActivationStatusSaga,
  syncEmployeeActivitySaga,
  syncEmployeesSaga,
  syncPriceBookSaga,
  syncPrinterSetting,
  syncProductsSaga,
  syncPromotionsSaga,
  syncQuickSelectLayoutSaga,
  syncSaga,
  syncSequentialStartInfoSaga,
  syncShiftsSaga,
  syncStoreInfoSaga,
  syncTableLayoutSaga,
  syncTransactionOnCheckoutSaga,
  syncTransactionsSaga,
  tryToPushStoreInfoToKDSSaga,
  updateProductImage,
} from '../../ts/sagas/dataSync';
import { generateLocation } from '../../ts/sagas/geo';
import {
  selectAuthToken,
  selectBusinessName,
  selectDevice,
  selectEmployeeId,
  selectEnableCashback,
  selectGBEnableTrackDownloadThumbnail,
  selectIsNetConnected,
  selectOnlineSyncTime,
  selectSequence,
  selectStoreId,
  selectStoreInfo,
  selectStoreInfoUnsafe,
  selectSyncInfo,
} from '../../ts/sagas/selector';
import { generateShiftReportSaga } from '../../ts/sagas/shift/common';
import { updateSequenceSaga } from '../../ts/sagas/shift/shiftFlow';
import { deleteThumbnailPromise, s3DownloadThumbnail } from '../../ts/utils/awsS3Helper';
import { getLatestModifiedTime } from '../../ts/utils/datetime';
import LocalLogger from '../../ts/utils/logComponent/local-logging/LocalLogger';
import PrinterManager from '../../ts/utils/printer';
PrinterManager.setStoreInfo = jest.fn();
jest.mock('../../ts/navigation/navigatorService', () => {
  return {
    navigate: jest.fn(),
  };
});

jest.mock('../../ts/utils/awsS3Helper', () => {
  return {
    s3DownloadThumbnail: jest.fn(),
    s3DownloadThumbnailWithResult: jest.fn(),
    deleteThumbnailPromise: jest.fn(),
  };
});

// jest.mock('../../ts/config/paymentOption', () => {
//   return {
//     updatePaymentOptions: jest.fn(),
//   };
// });

jest.mock('../../ts/dal', () => {
  return {
    getProductById: jest.fn().mockImplementation(productId => {
      return {
        productId: '5c4fc9d1e9020066fb1d220b',
        unitPrice: 7,
        selectedOptions: [],
        quantity: 1,
        taxRate: 0,
        title: 'Variable Price',
        total: 7,
        taxCode: '5c4ec3a0e3d231664262c58c',
        taxExclusiveSubtotal: 7,
        taxInclusiveSubtotal: 7,
        adhocDiscount: 0,
        tax: 0,
        discount: 0,
        subTotal: 7,
        display: {
          total: 7,
          subtotal: 7,
          tax: 0,
        },
      };
    }),
    saveTransaction: jest.fn(),
    saveProductInBatch: jest.fn(),
    deletePromotionById: jest.fn(),
    savePromotion: jest.fn(),
    saveSyncPromotions: jest.fn(),
    saveSyncPriceBooks: jest.fn(),
    saveSyncEmployees: jest.fn(),
    deletePriceBookById: jest.fn(),
    savePriceBook: jest.fn(),
    getReturnsTrxByOriginalReceiptNumber: jest.fn().mockImplementation(originalReceiptNumber => {
      return {};
    }),
    updateTransaction: jest.fn(),
    searchTrxBykeyword: jest.fn().mockImplementation(keyword => {
      return {};
    }),
    getTrancationById: jest.fn().mockImplementation(id => {
      return {
        transactionId: id,
        items: [],
        payments: [],
      };
    }),
    getTransactionById: jest.fn().mockImplementation(id => {
      return {
        transactionId: id,
        items: [],
        payments: [],
      };
    }),
    getEmployeeById: jest.fn().mockImplementation(id => {
      return {};
    }),
    getNotUploadedShifts: jest.fn().mockImplementation(() => {
      return [
        {
          shiftId: 1,
          sales: [],
          returns: [],
          deposits: [],
        },
      ];
    }),
    getShiftById: jest.fn().mockImplementation(id => {
      return {
        shiftId: id,
        payins: [],
        payouts: [],
      };
    }),
    getTransactionIdsByTime: jest.fn().mockImplementation(id => {
      return [];
    }),
    getTransactionIdsByShiftId: jest.fn().mockImplementation(id => {
      return [];
    }),
    getTotalAmountByTotalSince: jest.fn().mockImplementation(id => {
      return 0;
    }),
    updateShift: jest.fn(),
    clearDB: jest.fn(),
    deletePromotionById: jest.fn(),
    savePromotion: jest.fn(),
    getEmployeeActivity: jest.fn().mockImplementation(id => {
      return [
        {
          action: 'reduce_item',
          additionalInfo:
            '{"qty": 1, "productId": "5cdd21d41a8792ec88fbf5d8","productName": "product15", "transactionId": "7E93C62B-3677-4CA2-AB4A-E7292582A6BC"}',
          time: new Date('2019-05-21T07:58:31.376Z'),
          user: '5cad7146f76103d10ca28a8f',
        },
      ];
    }),
    getTransactionsWithEmptyReceiptNumber: jest.fn().mockImplementation(() => {
      return [{ transactionId: 1 }];
    }),
    deleteEmployeeActivity: jest.fn(),
    getTransactionIdsFrom: jest.fn().mockImplementation((start, end, isPayByCash) => {
      return [isPayByCash ? 'b2aae94d-6ddc-4b7a-bd10-1199890fc700' : 'fb67d79e-ce2e-4d2b-8753-68e1c63ed96c'];
    }),
    getReceiptNumbersFrom: jest.fn().mockImplementation((start, end, isPayByCash) => {
      return [isPayByCash ? 'b2aae94d-6ddc-4b7a-bd10-1199890fc700' : 'fb67d79e-ce2e-4d2b-8753-68e1c63ed96c'];
    }),
    getNotUploadedTransaction: jest.fn().mockImplementation(() => {
      return [
        {
          transactionId: '',
          items: [],
          payments: [],
        },
      ];
    }),
    deleteEmployeeById: jest.fn(),
    saveEmployee: jest.fn(),
  };
});

jest.mock('../../ts/navigation/navigatorService', () => {
  return {
    navigate: jest.fn(),
  };
});

describe('test dataSync saga', () => {
  const state = {
    Storage: {
      quickLayout: [
        {
          name: 'New Category',
          order: 0,
          categoryId: 'b2aae94d-6ddc-4b7a-bd10-1199890fc700',
          items: [
            {
              productId: '58b7c5bafe2f5a160d1469a4',
              row: 0,
              column: 0,
              backgroundColor: '',
            },
            {
              productId: '5c4fc99fe9020066fb1d2207',
              row: 1,
              column: 0,
              backgroundColor: '',
            },
            {
              productId: '5c4fc9c3e9020066fb1d2209',
              row: 0,
              column: 1,
              backgroundColor: '',
            },
            {
              productId: '5c4fc9d1e9020066fb1d220b',
              row: 0,
              column: 2,
              backgroundColor: '',
            },
          ],
        },
        {
          name: 'New Category',
          order: 1,
          categoryId: 'fb67d79e-ce2e-4d2b-8753-68e1c63ed96c',
          items: [
            {
              productId: '58b7b7bb32d982110ab436c3',
              row: 0,
              column: 0,
              backgroundColor: '',
            },
          ],
        },
      ],
      storeInfo: {
        store: {
          fulfillmentOptions: [],
          cfdDisplay: {
            cfdDisplayId: '5c5022eee9020066fb1d2225',
            type: 'images',
            text: '',
            name: 'Android',
            ordering: 1,
          },
          isVATRegistered: false,
          orderSummaryPrinter: '',
          enableLoyalty: false,
          enableCashback: false,
          showBarcode: true,
          taxNameOnReceipt: '',
          enablePax: false,
          ptuValideUntil: '',
          showNotes: true,
          birAccredited: false,
          enableServiceCharge: false,
          roundAllTransactions: false,
          showCustomerInfo: [],
          tableLayoutEnabled: false,
          ptuDateIssued: '',
          birAccrInfo:
            '****************************************\r\n\r\nJIMAC INC\r\nCordillera Quezon City TC Plaza B\r\nPenthouse, 40 Quezon Ave\r\nVAT REG TIN #: ***********-000\r\nDate Issued:  05/29/2017\r\n',
          name: '门店A',
          tax: 0,
          defaultTableLayoutSection: '',
          showStoreName: true,
          roundingTo: 0,
          currency: 'MYR',
          ptu: '',
          disabledDefaultPayments: [],
          minNo: '',
          cashierAccesses: {
            deleteItem: '0',
            deleteItemOfNew: '0',
            deleteItemOfSaved: '0',
            refund: '0',
            cancel: '1',
            discount: '0',
            openCloseShift: '1',
          },
          serialNo: '',
          enableMultipleIpads: false,
          country: 'MY',
          defaultTaxCode: '5c4ec3a0e3d231664262c58c',
          paymentOptions: [],
          autoOrderId: false,
          _id: '5c4ec3a0e3d231664262c588',
          taxCodes: [
            {
              _id: '5c4ec3a0e3d231664262c58c',
              name: 'SR',
              rate: 0,
            },
          ],
          poweredBy: true,
          email: '<EMAIL>',
          birAccrNo: '039-0027-05362-201610-0606',
          allowEditProductsOnPOS: false,
          separateKitchenItems: false,
        },
        sequentialReceiptNumber: false,
        subscriptionPlan: 2,
        name: 'qsl',
        subscriptionStatus: 'Active',
        currency: 'MYR',
        allStores: [
          {
            _id: '5c4ec3a0e3d231664262c588',
            name: '门店A',
          },
        ],
        registerObjectId: '5c4ec3a0e3d231664262c589',
        registerId: 1,
        apiToken: 'de0331802ab111e98aa68bac170a2358',
      },
      syncInfo: {
        employeeInfoSync: true,
        lastEmployeeSyncTime: '2019-02-07T08:25:10.796Z',
        priceBookInfoSync: true,
        lastPriceBookSyncTime: '2019-02-07T08:25:10.856Z',
        productInfoSync: true,
        lastProductSyncTime: '2019-02-07T08:25:10.826Z',
        onlineSyncTime: '2019-02-12T06:23:28.785Z',
      },
    },
  };

  it('test syncSaga syncInfo undefined', async () => {
    const syncAction = {
      payload: {},
    };
    return testSaga(syncSaga, syncAction)
      .next()
      .select(selectSyncInfo)
      .next(
        Immutable.fromJS({
          needToSyncAll: false,
          productInfoSync: true,
        })
      );
  });

  it('test syncEmployeesSaga success', async () => {
    const onCompleteCallback = jest.fn();
    const syncEmployeesAction = {
      payload: {
        onComplete: {
          callback: onCompleteCallback,
        },
      },
    };
    return expectSaga(syncEmployeesSaga, syncEmployeesAction)
      .withState(
        Immutable.fromJS({
          ...state,
        })
      )
      .select(selectSyncInfo)
      .dispatch({
        type: syncEmployees.toString() + '.success',
        payload: {
          res: {},
        },
      })
      .silentRun();
  });

  it('test syncEmployeesSaga needToSyncAll is true', async () => {
    const onCompleteCallback = jest.fn();
    const syncEmployeesAction = {
      payload: {
        onComplete: {
          callback: onCompleteCallback,
        },
      },
    };
    return expectSaga(syncEmployeesSaga, syncEmployeesAction)
      .withState(
        Immutable.fromJS({
          ...state,
        })
      )
      .select(selectSyncInfo)
      .withState(
        Immutable.fromJS({
          needToSyncAll: true,
        })
      )
      .dispatch({
        type: syncEmployees.toString() + '.success',
        payload: {
          res: {},
        },
      })
      .silentRun();
  });

  it('test syncEmployeesSaga failure', async () => {
    const onComplete = { callback: jest.fn() };
    const syncEmployeesAction = {
      payload: {
        onComplete,
      },
    };
    testSaga(syncEmployeesSaga, syncEmployeesAction)
      .next()
      .select(selectStoreInfoUnsafe)
      .next(
        Immutable.fromJS({
          ...state.Storage.storeInfo,
        })
      )
      .select(selectSyncInfo)
      .next()
      .put(
        syncEmployees({
          business: 'qsl',
          storeId: '5c4ec3a0e3d231664262c588',
          syncTime: '',
        })
      )
      .next()
      .take([syncEmployees.toString() + '.success', syncEmployees.toString() + '.failure'])
      .next({
        type: syncEmployees.toString() + '.failure',
        payload: {
          message: '',
        },
      }).isDone;
    expect(onComplete.callback.mock.calls[0][0]).toEqual({ result: false, message: '' });
  });

  it('test setSyncEmployeesSaga', async () => {
    const payload = [
      { isDeleted: true, _id: 1, modifiedTime: '2019-03-13T05:13:05.809Z' },
      { isDeleted: false, _id: 2, modifiedTime: '2019-03-13T05:13:05.809Z' },
    ];
    testSaga(setSyncEmployeesSaga, payload, '2019-02-07T08:25:10.856Z')
      .next()
      .select(selectEmployeeId)
      .next('1234567')
      .put(
        setSyncInfo({
          employeeInfoSync: true,
          lastEmployeeSyncTime: '2019-03-13T05:13:05.809Z',
        })
      );
  });

  it('test syncProductsSaga', async () => {
    const onComplete = { callback: jest.fn() };
    const syncProductsAction = {
      payload: {
        onComplete,
        isInitial: true,
      },
    };
    testSaga(syncProductsSaga, syncProductsAction)
      .next()
      .select(selectBusinessName)
      .next('qsl')
      .select(selectSyncInfo)
      .next(
        Immutable.fromJS({
          needToSyncAll: true,
          lastProductSyncTime: '',
        })
      )
      .put(
        syncProducts({
          business: 'qsl',
          syncTime: '',
          limit: SYNC_PRODUCTS_LIMIT,
          page: 1,
          isInitial: true,
        })
      )
      .next()
      .take([syncProducts.toString() + '.success', syncProducts.toString() + '.failure'])
      .next({
        type: syncProducts.toString() + '.success',
        payload: {
          asArray: [],
        },
      })
      .call(setSyncProductsSaga, [], '', 'qsl')
      .next()
      .put(
        setSyncInfo({
          lastProductSyncTime: '',
        })
      )
      .next();
    expect(onComplete.callback.mock.calls[0][0]).toEqual({ result: true });
  });

  it('test syncProductsSaga failure', async () => {
    const onComplete = { callback: jest.fn() };
    const syncProductsAction = {
      payload: {
        onComplete,
      },
    };
    testSaga(syncProductsSaga, syncProductsAction)
      .next()
      .select(selectBusinessName)
      .next('qsl')
      .select(selectSyncInfo)
      .next()
      .put(
        syncProducts({
          business: 'qsl',
          syncTime: '',
          limit: SYNC_PRODUCTS_LIMIT,
          page: 1,
          isInitial: undefined,
        })
      )
      .next()
      .take([syncProducts.toString() + '.success', syncProducts.toString() + '.failure'])
      .next({
        type: syncProducts.toString() + '.failure',
        payload: {
          message: '',
        },
      }).isDone;
    expect(onComplete.callback.mock.calls[0][0]).toEqual({ result: false, message: '' });
  });

  it('test syncProductsSaga bussiness is null', async () => {
    const onComplete = { callback: jest.fn() };
    const syncProductsAction = {
      payload: {
        onComplete,
      },
    };
    testSaga(syncProductsSaga, syncProductsAction).next().select(selectBusinessName).next().isDone;
    expect(onComplete.callback.mock.calls[0][0]).toEqual({ result: false, message: 'Business is null' });
  });

  it('test setSyncProductsSaga', async () => {
    const products = [
      { hasThumbnail: true, _id: '1' },
      { hasThumbnail: false, _id: '2' },
      { hasThumbnail: true, _id: '3' },
    ];

    expect(getLatestModifiedTime('', new Date())).toEqual('');
    return testSaga(setSyncProductsSaga, products, '', 'qsl')
      .next()
      .select(selectGBEnableTrackDownloadThumbnail)
      .next(true)
      .call(updateProductImage, products, 'qsl');
  });

  it('test updateProductImage', async () => {
    const products = [{ hasThumbnail: false, _id: '1', lastUpdateThumbnail: '2019-03-13T05:13:05.809Z' }];
    return testSaga(updateProductImage, products, 'qsl').next().select(selectGBEnableTrackDownloadThumbnail).next(true).call(deleteThumbnailPromise, '1');
  });

  it('should delete and download thumbnail correctley', async () => {
    const dispatched = [];
    const setProductsAction = [
      {
        _id: '58b92db7018e879b0e5118e0',
        modifiedTime: '2019-03-13T05:13:05.809Z',
        hasThumbnail: true,
      },
      {
        _id: '58b92db7018e879b0e51181',
        modifiedTime: '2019-03-13T05:13:05.809Z',
        hasThumbnail: false,
      },
      {
        _id: '58b92db7018e879b0e8e0',
        modifiedTime: '2019-03-13T05:13:05.809Z',
        hasThumbnail: true,
      },
    ];
    const result = await runSaga(
      {
        dispatch: action => {
          dispatched.push(action);
        },
        getState: () =>
          Immutable.fromJS({
            ...state,
          }),
      },
      setSyncProductsSaga,
      setProductsAction
    ).done;

    expect(s3DownloadThumbnail).toHaveBeenCalledTimes(2);
    expect(deleteThumbnailPromise).toHaveBeenCalledTimes(1);
  });

  it('test syncQuickSelectLayoutSaga', async () => {
    const onComplete = { callback: jest.fn() };
    const syncQuickSelectLayoutAction = {
      payload: {
        onComplete,
      },
    };
    const categories = [
      {
        name: 'New Category',
        order: 0,
        categoryId: 'b2aae94d-6ddc-4b7a-bd10-1199890fc700',
        items: [],
      },
      {
        name: 'New Category',
        order: 1,
        categoryId: 'fb67d79e-ce2e-4d2b-8753-68e1c63ed96c',
        items: [],
      },
    ];
    const usedInStores = [
      '5b62d783081ccd016185cf08',
      '5b755f66947b670161810a23',
      '5b9249f16ff5fe01837502f5',
      '5bced376864f36018339fb21',
      '5d5bb5ef96322411374dc7b1',
    ];
    const quickSelectLayoutId = '5b62d783081ccd016185cf0a';
    testSaga(syncQuickSelectLayoutSaga, syncQuickSelectLayoutAction)
      .next()
      .select(selectStoreInfoUnsafe)
      .next(
        Immutable.fromJS({
          ...state.Storage.storeInfo,
        })
      )
      .put(
        syncQuickSelectLayout({
          business: 'qsl',
          registerId: '5c4ec3a0e3d231664262c589',
        })
      )
      .next()
      .take([syncQuickSelectLayout.toString() + '.success', syncQuickSelectLayout.toString() + '.failure'])
      .next({
        type: syncQuickSelectLayout.toString() + '.success',
        payload: {
          categories,
          usedInStores,
          quickSelectLayoutId,
        },
      })
      .put(setQuickLayout(categories))
      .next()
      .put(setShareQuickLayoutStores(usedInStores))
      .next()
      .put(setQuickLayoutId(quickSelectLayoutId))
      .next();

    expect(onComplete.callback.mock.calls[0][0]).toEqual({ result: true });
  });

  it('test syncQuickSelectLayoutSaga failure', async () => {
    const onComplete = { callback: jest.fn() };
    const syncQuickSelectLayoutAction = {
      payload: {
        onComplete,
      },
    };
    const categories = [
      {
        name: 'New Category',
        order: 0,
        categoryId: 'b2aae94d-6ddc-4b7a-bd10-1199890fc700',
        items: [],
      },
      {
        name: 'New Category',
        order: 1,
        categoryId: 'fb67d79e-ce2e-4d2b-8753-68e1c63ed96c',
        items: [],
      },
    ];
    const usedInStores = [
      '5b62d783081ccd016185cf08',
      '5b755f66947b670161810a23',
      '5b9249f16ff5fe01837502f5',
      '5bced376864f36018339fb21',
      '5d5bb5ef96322411374dc7b1',
    ];
    const quickSelectLayoutId = '5b62d783081ccd016185cf0a';
    testSaga(syncQuickSelectLayoutSaga, syncQuickSelectLayoutAction)
      .next()
      .select(selectStoreInfoUnsafe)
      .next(
        Immutable.fromJS({
          ...state.Storage.storeInfo,
        })
      )
      .put(
        syncQuickSelectLayout({
          business: 'qsl',
          registerId: '5c4ec3a0e3d231664262c589',
        })
      )
      .next()
      .take([syncQuickSelectLayout.toString() + '.success', syncQuickSelectLayout.toString() + '.failure'])
      .next({
        type: syncQuickSelectLayout.toString() + '.failure',
        payload: {
          message: '',
        },
      })
      .next();

    expect(onComplete.callback.mock.calls[0][0]).toEqual({ result: false, message: '' });
  });

  it('test syncPriceBookSaga', async () => {
    const onComplete = { callback: jest.fn() };
    const syncPriceBookAction = {
      payload: {
        onComplete,
      },
    };
    testSaga(syncPriceBookSaga, syncPriceBookAction)
      .next()
      .select(selectSyncInfo)
      .next(
        Immutable.fromJS({
          syncInfo: {
            needToSyncAll: true,
            lastPriceBookSyncTime: '',
          },
        })
      )
      .put(
        syncPriceBook({
          syncTime: undefined,
        })
      )
      .next()
      .take([syncPriceBook.toString() + '.success', syncPriceBook.toString() + '.failure'])
      .next({
        type: syncPriceBook.toString() + '.success',
        payload: {
          asArray: [],
        },
      })
      .call(setSyncPriceBookSaga, [], undefined)
      .next();
    expect(onComplete.callback.mock.calls[0][0]).toEqual({ result: true });
  });

  it('test syncPriceBookSaga failure', async () => {
    const onComplete = { callback: jest.fn() };
    const syncPriceBookAction = {
      payload: {
        onComplete,
      },
    };
    testSaga(syncPriceBookSaga, syncPriceBookAction)
      .next()
      .select(selectSyncInfo)
      .next(Immutable.fromJS({ needToSyncAll: true }))
      .put(
        syncPriceBook({
          syncTime: '',
        })
      )
      .next()
      .take([syncPriceBook.toString() + '.success', syncPriceBook.toString() + '.failure'])
      .next({
        type: syncPriceBook.toString() + '.failure',
        payload: {
          message: '',
        },
      })
      .next();
    expect(onComplete.callback.mock.calls[0][0]).toEqual({ result: false, message: '' });
  });

  it('test setSyncPriceBookSaga', async () => {
    return testSaga(
      setSyncPriceBookSaga,
      [
        { isDeleted: true, _id: 1, modifiedTime: '2019-03-13T05:13:05.809Z' },
        { isDeleted: false, _id: 2, modifiedTime: '2019-03-13T05:13:05.809Z' },
      ],
      ''
    )
      .next()
      .put(
        setSyncInfo({
          priceBookInfoSync: true,
          lastPriceBookSyncTime: '2019-03-13T05:13:05.809Z',
        })
      );
  });

  it('test setSyncPriceBookSaga payload is {}', async () => {
    return testSaga(setSyncPriceBookSaga, {}, '')
      .next()
      .put(
        setSyncInfo({
          priceBookInfoSync: true,
          lastPriceBookSyncTime: '',
        })
      );
  });

  it('test syncPromotionsSaga', async () => {
    const onComplete = { callback: jest.fn() };
    const syncPromotionsAction = {
      payload: {
        onComplete,
      },
    };
    testSaga(syncPromotionsSaga, syncPromotionsAction)
      .next()
      .select(selectSyncInfo)
      .next(Immutable.fromJS({}))
      .put(syncPromotions({ syncTime: undefined }))
      .next()
      .take([syncPromotions.toString() + '.success', syncPromotions.toString() + '.failure'])
      .next({
        type: syncPromotions.toString() + '.success',
        payload: {
          asArray: [],
        },
      })
      .call(setPromotionsSaga, [], undefined)
      .next();
    expect(onComplete.callback.mock.calls[0][0]).toEqual({ result: true });
  });

  it('test syncPromotionsSaga syncInfo is undefined', async () => {
    const onComplete = { callback: jest.fn() };
    const syncPromotionsAction = {
      payload: {
        onComplete,
      },
    };
    testSaga(syncPromotionsSaga, syncPromotionsAction)
      .next()
      .select(selectSyncInfo)
      .next()
      .put(syncPromotions({ syncTime: '' }))
      .next()
      .take([syncPromotions.toString() + '.success', syncPromotions.toString() + '.failure'])
      .next({
        type: syncPromotions.toString() + '.success',
        payload: {
          asArray: [],
        },
      })
      .call(setPromotionsSaga, [], '')
      .next();
    expect(onComplete.callback.mock.calls[0][0]).toEqual({ result: true });
  });

  it('test syncPromotionsSaga failure', async () => {
    const onComplete = { callback: jest.fn() };
    const syncPromotionsAction = {
      payload: {
        onComplete,
      },
    };
    testSaga(syncPromotionsSaga, syncPromotionsAction)
      .next()
      .select(selectSyncInfo)
      .next(Immutable.fromJS({}))
      .put(syncPromotions({ syncTime: undefined }))
      .next()
      .take([syncPromotions.toString() + '.success', syncPromotions.toString() + '.failure'])
      .next({
        type: syncPromotions.toString() + '.failure',
        payload: {
          message: '',
        },
      }).isDone;
    expect(onComplete.callback.mock.calls[0][0]).toEqual({ result: false, message: '' });
  });

  it('test setPromotionsSaga', async () => {
    return testSaga(
      setPromotionsSaga,
      [
        { isDeleted: true, _id: 1, modifiedTime: '2019-03-13T05:13:05.809Z' },
        { isDeleted: false, _id: 2, modifiedTime: '2019-03-13T05:13:05.809Z' },
      ],
      ''
    )
      .next()
      .put(
        setSyncInfo({
          promotionInfoSync: true,
          lastPromotionSyncTime: '2019-03-13T05:13:05.809Z',
        })
      );
  });

  it('test setPromotionsSaga payload is {}', async () => {
    return testSaga(setPromotionsSaga, {}, '')
      .next()
      .put(
        setSyncInfo({
          promotionInfoSync: true,
          lastPromotionSyncTime: '',
        })
      );
  });

  it('test forceDeactivation', async () => {
    testSaga(forceDeactivation).next().call(cleanData).next();
    expect(Intercom.logout).toHaveBeenCalled();
  });

  it('test cleanData', async () => {
    testSaga(cleanData)
      .next()
      .put(clearStoreInfo())
      .next()
      .put(clearSetting())
      .next()
      .put(clearLocalCountryMap())
      .next()
      .put(clearMRSState({}))
      .next()
      .put(unRegisterMrsAction())
      .next()
      .put(unregisterWebsocket())
      .next()
      .put(cleanKdsSetting())
      .next()
      .put(cleanNcsSetting())
      .next()
      .put(resetCfd())
      .next()
      .put(clearEmployee())
      .next()
      .put(clearEWalletPay())
      .next()
      .put(setShiftStatus(false))
      .next()
      .put(updateGrowthBook())
      .next()
      .put(stopCheckCustomerInfoFromQR({}))
      .next()
      .put(resetAWSConfig())
      .next()
      .call([LocalLogger, LocalLogger.updateLogModel], {
        employeeId: '',
        storeId: '',
        business: '',
        registerId: '',
        registerNumber: -1,
      })
      .next();
    expect(DAL.clearDB).toHaveBeenCalled();
  });

  it('test syncTransactionOnCheckoutSaga', async () => {
    const onComplete = { callback: jest.fn() };
    const syncTransactionsActon = {
      payload: {
        transactionId: 1,
        onComplete,
      },
    };
    testSaga(syncTransactionOnCheckoutSaga, syncTransactionsActon)
      .next()
      .select(selectBusinessName)
      .next(state.Storage.storeInfo.name)
      .select(selectStoreId)
      .next(state.Storage.storeInfo.store._id)
      .call(generateLocation)
      .next()
      .put(
        syncTransaction({
          business: state.Storage.storeInfo.name,
          storeId: state.Storage.storeInfo.store._id,
          lastRegisterId: '',
          recordJSON: {
            isOriginalOnline: undefined,
            total: 0,
            subtotal: 0,
            discount: 0,
            tax: 0,
            amusementTax: 0,
            modifiedTime: '',
            createdTime: '',
            createdDate: undefined,
            modifiedDate: undefined,
            employeeNumber: undefined,
            employeeId: undefined,
            transactionId: 1,
            transactionType: undefined,
            receiptNumber: undefined,
            isCancelled: undefined,
            roundedAmount: undefined,
            appVersion: undefined,
            serviceCharge: undefined,
            serviceChargeTax: undefined,
            serviceChargeRate: undefined,
            tableId: undefined,
            pickUpId: undefined,
            isOpen: undefined,
            registerId: undefined,
            items: [],
            payments: [],
            headcount: undefined,
            pwdCount: undefined,
            seniorsCount: undefined,
            isOnlineOrder: undefined,
            mrs: undefined,
            takeawayId: undefined,
            registerNumber: undefined,
            shippingType: undefined,
            channel: undefined,
          },
          location: undefined,
        })
      )
      .next()
      .take([syncTransaction.toString() + '.success', syncTransaction.toString() + '.failure'])
      .next({
        type: syncTransaction.toString() + '.success',
        payload: { loyaltyInfo: {} },
      }).isDone;
    expect(onComplete.callback.mock.calls[0][0]).toEqual({ loyaltyInfo: {}, result: true });
  });

  it('test syncTransactionOnCheckoutSaga loyaltyInfo undefined', async () => {
    const onComplete = { callback: jest.fn() };
    const syncTransactionsActon = {
      payload: {
        transactionId: 1,
        onComplete,
      },
    };
    testSaga(syncTransactionOnCheckoutSaga, syncTransactionsActon)
      .next()
      .select(selectBusinessName)
      .next(state.Storage.storeInfo.name)
      .select(selectStoreId)
      .next(state.Storage.storeInfo.store._id)
      .call(generateLocation)
      .next()
      .put(
        syncTransaction({
          business: state.Storage.storeInfo.name,
          storeId: state.Storage.storeInfo.store._id,
          lastRegisterId: '',
          recordJSON: {
            isOriginalOnline: undefined,
            total: 0,
            subtotal: 0,
            discount: 0,
            tax: 0,
            amusementTax: 0,
            modifiedTime: '',
            createdTime: '',
            createdDate: undefined,
            modifiedDate: undefined,
            employeeNumber: undefined,
            employeeId: undefined,
            transactionId: 1,
            transactionType: undefined,
            receiptNumber: undefined,
            isCancelled: undefined,
            roundedAmount: undefined,
            appVersion: undefined,
            serviceCharge: undefined,
            serviceChargeTax: undefined,
            serviceChargeRate: undefined,
            tableId: undefined,
            pickUpId: undefined,
            takeawayId: undefined,
            isOpen: undefined,
            registerId: undefined,
            items: [],
            payments: [],
            headcount: undefined,
            pwdCount: undefined,
            seniorsCount: undefined,
            isOnlineOrder: undefined,
            mrs: undefined,
            registerNumber: undefined,
            shippingType: undefined,
            channel: undefined,
          },
          location: undefined,
        })
      )
      .next()
      .take([syncTransaction.toString() + '.success', syncTransaction.toString() + '.failure'])
      .next({
        type: syncTransaction.toString() + '.success',
        payload: {},
      }).isDone;
    expect(onComplete.callback.mock.calls[0][0]).toEqual({ loyaltyInfo: undefined, result: true });
  });

  it('test syncTransactionOnCheckoutSaga failure', async () => {
    const onComplete = { callback: jest.fn() };
    const syncTransactionsActon = {
      payload: {
        transactionId: 1,
        onComplete,
      },
    };
    testSaga(syncTransactionOnCheckoutSaga, syncTransactionsActon)
      .next()
      .select(selectBusinessName)
      .next(state.Storage.storeInfo.name)
      .select(selectStoreId)
      .next(state.Storage.storeInfo.store._id)
      .call(generateLocation)
      .next()
      .put(
        syncTransaction({
          business: state.Storage.storeInfo.name,
          storeId: state.Storage.storeInfo.store._id,
          lastRegisterId: '',
          recordJSON: {
            isOriginalOnline: undefined,
            total: 0,
            subtotal: 0,
            discount: 0,
            tax: 0,
            amusementTax: 0,
            modifiedTime: '',
            createdTime: '',
            createdDate: undefined,
            modifiedDate: undefined,
            employeeNumber: undefined,
            employeeId: undefined,
            transactionId: 1,
            transactionType: undefined,
            receiptNumber: undefined,
            isCancelled: undefined,
            roundedAmount: undefined,
            appVersion: undefined,
            serviceCharge: undefined,
            serviceChargeTax: undefined,
            serviceChargeRate: undefined,
            tableId: undefined,
            takeawayId: undefined,
            pickUpId: undefined,
            isOpen: undefined,
            registerId: undefined,
            items: [],
            payments: [],
            headcount: undefined,
            pwdCount: undefined,
            seniorsCount: undefined,
            isOnlineOrder: undefined,
            mrs: undefined,
            registerNumber: undefined,
            shippingType: undefined,
            channel: undefined,
          },
          location: undefined,
        })
      )
      .next()
      .take([syncTransaction.toString() + '.success', syncTransaction.toString() + '.failure'])
      .next({
        type: syncTransaction.toString() + '.failure',
        payload: { message: '' },
      })
      .select(selectIsNetConnected)
      .next().isDone;
    expect(onComplete.callback.mock.calls[0][0]).toEqual({ result: false, message: '' });
  });

  it('test syncShiftsSaga', async () => {
    const onCompleteCallback = jest.fn();
    const syncShiftsAction = {
      payload: {
        transactionId: 1,
        onComplete: {
          callback: onCompleteCallback,
        },
      },
    };
    return testSaga(syncShiftsSaga, syncShiftsAction)
      .next()
      .put(syncLastZReadingCloseTime())
      .next()
      .put(syncEmployeeActivityBegin({}))
      .next()
      .select(selectStoreInfo)
      .next(
        Immutable.fromJS({
          ...state.Storage.storeInfo,
        })
      )
      .select(selectEnableCashback)
      .next(state.Storage.storeInfo.store.enableCashback)
      .call(generateShiftReportSaga, {
        shiftId: 1,
        sales: [],
        returns: [],
        deposits: [],
      })
      .next({
        shiftId: 1,
        sales: [],
        actualSales: [],
        returns: [],
        deposits: [],
        discounts: {},
        totalNetSales: 100,
        registerTransactions: [{ transactionId: 'fb67d79e-ce2e-4d2b-8753-68e1c63ed96c', transactionType: 'Sale', isCancelled: false }],
        beepTransactions: [{ receiptNumber: 'b2aae94d-6ddc-4b7a-bd10-1199890fc700', transactionType: 'Sale', isCancelled: false }],
        taxSummary: jest.fn(),
        cancelSummary: jest.fn(),
        getLoyaltyDiscount: jest.fn(enableCashback => {
          return 0;
        }),
        getLoyaltyDiscountCount: jest.fn(enableCashback => {
          return 0;
        }),
        getDiscount: jest.fn(enableCashback => {
          return 0;
        }),
      })
      .select(selectEmployeeId)
      .next('123456')
      .put(
        syncShiftReport({
          shiftId: 1,
          closeBy: '123456',
          closeTime: '',
          closingAmount: undefined,
          openBy: '123456',
          openingAmount: undefined,
          enableCashback: false,
          openTime: '',
          registerId: 1,
          storeId: '5c4ec3a0e3d231664262c588',
          registerObjectId: '5c4ec3a0e3d231664262c589',
          salesRoundedAmount: undefined,
          returnsRoundedAmount: undefined,
          cashSalesRoundedAmount: undefined,
          cashReturnsRoundedAmount: undefined,
          totalSalesAmount: 100,
          sales: [
            { paymentMethod: 'Cash', amount: 0, count: 0 },
            { paymentMethod: 'CreditCard', amount: 0, count: 0 },
            { paymentMethod: 'DebitCard', amount: 0, count: 0 },
          ],
          returns: [
            { paymentMethod: 'Cash', amount: 0, count: 0 },
            { paymentMethod: 'CreditCard', amount: 0, count: 0 },
            { paymentMethod: 'DebitCard', amount: 0, count: 0 },
          ],
          taxSummary: undefined,
          deposits: [
            { paymentMethod: 'Cash', amount: 0, count: 0 },
            { paymentMethod: 'CreditCard', amount: 0, count: 0 },
            { paymentMethod: 'DebitCard', amount: 0, count: 0 },
          ],
          loyaltyDiscounts: {
            sales: {
              amount: 0,
              count: 0,
            },
            totalLoyaltyDiscountCount: 0,
          },
          discountedAmount: 0,
          discountedCount: 0,
          cancelledCount: undefined,
          cancelledAmount: undefined,
          expectedDrawer: undefined,
          payouts: [],
          payIns: [],
          registerTransactions: [{ transactionId: 'fb67d79e-ce2e-4d2b-8753-68e1c63ed96c', transactionType: 'Sale', isCancelled: false }],
          beepTransactions: [{ receiptNumber: 'b2aae94d-6ddc-4b7a-bd10-1199890fc700', transactionType: 'Sale', isCancelled: false }],
        })
      )
      .next()
      .take([syncShiftReport.toString() + '.success', syncShiftReport.toString() + '.failure'])
      .next({
        type: syncShiftReport.toString() + '.success',
        payload: {},
      });
  });

  it('test syncShiftsSaga failure', async () => {
    const onComplete = { callback: jest.fn() };
    const syncShiftsAction = {
      payload: {
        transactionId: 1,
        onComplete,
      },
    };
    testSaga(syncShiftsSaga, syncShiftsAction)
      .next()
      .put(syncLastZReadingCloseTime())
      .next()
      .put(syncEmployeeActivityBegin({}))
      .next()
      .select(selectStoreInfo)
      .next(
        Immutable.fromJS({
          ...state.Storage.storeInfo,
        })
      )
      .select(selectEnableCashback)
      .next(state.Storage.storeInfo.store.enableCashback)
      .call(generateShiftReportSaga, {
        shiftId: 1,
        sales: [],
        returns: [],
        deposits: [],
      })
      .next({
        shiftId: 1,
        sales: [],
        actualSales: [],
        returns: [],
        deposits: [],
        discounts: {},
        totalNetSales: 100,
        registerTransactions: [{ transactionId: 'fb67d79e-ce2e-4d2b-8753-68e1c63ed96c', transactionType: 'Sale', isCancelled: false }],
        beepTransactions: [{ receiptNumber: 'b2aae94d-6ddc-4b7a-bd10-1199890fc700', transactionType: 'Sale', isCancelled: false }],
        taxSummary: jest.fn(),
        cancelSummary: jest.fn(),
        getLoyaltyDiscount: jest.fn(enableCashback => {
          return 0;
        }),
        getLoyaltyDiscountCount: jest.fn(enableCashback => {
          return 0;
        }),
        getDiscount: jest.fn(enableCashback => {
          return 0;
        }),
      })
      .select(selectEmployeeId)
      .next('123456')
      .put(
        syncShiftReport({
          shiftId: 1,
          closeBy: '123456',
          closeTime: '',
          closingAmount: undefined,
          openBy: '123456',
          openingAmount: undefined,
          enableCashback: false,
          openTime: '',
          registerId: 1,
          storeId: '5c4ec3a0e3d231664262c588',
          registerObjectId: '5c4ec3a0e3d231664262c589',
          salesRoundedAmount: undefined,
          returnsRoundedAmount: undefined,
          cashSalesRoundedAmount: undefined,
          cashReturnsRoundedAmount: undefined,
          sales: [
            { paymentMethod: 'Cash', amount: 0, count: 0 },
            { paymentMethod: 'CreditCard', amount: 0, count: 0 },
            { paymentMethod: 'DebitCard', amount: 0, count: 0 },
          ],
          returns: [
            { paymentMethod: 'Cash', amount: 0, count: 0 },
            { paymentMethod: 'CreditCard', amount: 0, count: 0 },
            { paymentMethod: 'DebitCard', amount: 0, count: 0 },
          ],
          taxSummary: undefined,
          deposits: [
            { paymentMethod: 'Cash', amount: 0, count: 0 },
            { paymentMethod: 'CreditCard', amount: 0, count: 0 },
            { paymentMethod: 'DebitCard', amount: 0, count: 0 },
          ],
          totalSalesAmount: 100,
          loyaltyDiscounts: {
            sales: {
              amount: 0,
              count: 0,
            },
            totalLoyaltyDiscountCount: 0,
          },
          discountedAmount: 0,
          discountedCount: 0,
          cancelledCount: undefined,
          cancelledAmount: undefined,
          expectedDrawer: undefined,
          payouts: [],
          payIns: [],
          registerTransactions: [{ transactionId: 'fb67d79e-ce2e-4d2b-8753-68e1c63ed96c', transactionType: 'Sale', isCancelled: false }],
          beepTransactions: [{ receiptNumber: 'b2aae94d-6ddc-4b7a-bd10-1199890fc700', transactionType: 'Sale', isCancelled: false }],
        })
      )
      .next()
      .take([syncShiftReport.toString() + '.success', syncShiftReport.toString() + '.failure'])
      .next({
        type: syncShiftReport.toString() + '.failure',
        payload: { message: '' },
      }).isDone;
    expect(onComplete.callback.mock.calls[0][0]).toEqual({ result: false, message: '' });
  });

  it('test syncShiftsSaga getNotUploadedShifts returns []', async () => {
    DAL.getNotUploadedShifts = jest.fn(() => {
      return [];
    });
    const onComplete = { callback: jest.fn() };
    const syncShiftsAction = {
      payload: {
        transactionId: 1,
        onComplete,
      },
    };
    testSaga(syncShiftsSaga, syncShiftsAction)
      .next()
      .put(syncLastZReadingCloseTime())
      .next()
      .put(syncEmployeeActivityBegin({}))
      .next()
      .select(selectStoreInfo)
      .next(
        Immutable.fromJS({
          ...state.Storage.storeInfo,
        })
      );
  });

  it('test syncStoreInfoSaga', async () => {
    const onComplete = { callback: jest.fn() };
    const syncStoreInfoAction = {
      payload: {
        onComplete,
      },
    };
    testSaga(syncStoreInfoSaga, syncStoreInfoAction)
      .next()
      .select(selectStoreInfoUnsafe)
      .next(
        Immutable.fromJS({
          ...state.Storage.storeInfo,
        })
      )
      .put(
        syncStoreInfo({
          business: 'qsl',
          registerId: '5c4ec3a0e3d231664262c589',
          includeAllStores: true,
        })
      )
      .next()
      .take([syncStoreInfo.toString() + '.success', syncStoreInfo.toString() + '.failure'])
      .next({
        type: syncStoreInfo.toString() + '.success',
        payload: {
          allStores: [],
        },
      })
      .put(
        setStoreInfo({
          allStores: [],
          store: {},
          registerName: '',
        })
      )
      .next()
      .call(syncPrinterSetting, {})
      .next()
      .fork(tryToPushStoreInfoToKDSSaga)
      .next();

    expect(onComplete.callback.mock.calls[0][0]).toEqual({ result: true });
  });

  it('test syncStoreInfoSaga failure', async () => {
    const onComplete = { callback: jest.fn() };
    const syncStoreInfoAction = {
      payload: {
        onComplete,
      },
    };
    testSaga(syncStoreInfoSaga, syncStoreInfoAction)
      .next()
      .select(selectStoreInfoUnsafe)
      .next(
        Immutable.fromJS({
          ...state.Storage.storeInfo,
        })
      )
      .put(
        syncStoreInfo({
          business: 'qsl',
          registerId: '5c4ec3a0e3d231664262c589',
          includeAllStores: true,
        })
      )
      .next()
      .take([syncStoreInfo.toString() + '.success', syncStoreInfo.toString() + '.failure'])
      .next({
        type: syncStoreInfo.toString() + '.failure',
        payload: {
          message: '',
        },
      })
      .next();
    expect(onComplete.callback.mock.calls[0][0]).toEqual({ result: false, message: '' });
  });

  it('test syncTableLayoutSaga', async () => {
    const onComplete = { callback: jest.fn() };
    const syncTableLayoutAction = {
      payload: {
        onComplete,
      },
    };
    testSaga(syncTableLayoutSaga, syncTableLayoutAction)
      .next()
      .select(selectStoreInfoUnsafe)
      .next(
        Immutable.fromJS({
          ...state.Storage.storeInfo,
        })
      )
      .put(
        getTableLayout({
          businessName: 'qsl',
          storeId: '5c4ec3a0e3d231664262c588',
        })
      )
      .next()
      .take([getTableLayout.toString() + '.success', getTableLayout.toString() + '.failure'])
      .next({
        type: getTableLayout.toString() + '.success',
        payload: {
          tableLayout: {
            sections: {},
          },
        },
      })
      .put(
        saveTableLayoutSections({
          sections: {},
        })
      )
      .next();
    expect(onComplete.callback.mock.calls[0][0]).toEqual({ result: true });
  });

  it('test syncTableLayoutSaga failure', async () => {
    const onComplete = { callback: jest.fn() };
    const syncStoreInfoAction = {
      payload: {
        onComplete,
      },
    };
    testSaga(syncTableLayoutSaga, syncStoreInfoAction)
      .next()
      .select(selectStoreInfoUnsafe)
      .next(
        Immutable.fromJS({
          ...state.Storage.storeInfo,
        })
      )
      .put(
        getTableLayout({
          businessName: 'qsl',
          storeId: '5c4ec3a0e3d231664262c588',
        })
      )
      .next()
      .take([getTableLayout.toString() + '.success', getTableLayout.toString() + '.failure'])
      .next({
        type: getTableLayout.toString() + '.failure',
        payload: {
          message: '',
        },
      })
      .next();
    expect(onComplete.callback.mock.calls[0][0]).toEqual({ result: false, message: '' });
  });

  it('test syncActivationStatusSaga with isActivated false', async () => {
    const onCompleteCallback = jest.fn();
    const syncActivationStatusAction = {
      payload: {
        onComplete: {
          callback: onCompleteCallback,
        },
      },
    };
    testSaga(syncActivationStatusSaga, syncActivationStatusAction)
      .next()
      .call(generateLocation)
      .next()
      .select(selectDevice)
      .next()
      .select(selectBusinessName)
      .next('qsl')
      .select(selectAuthToken)
      .next('5cad7146f76103d10ca28a8f')
      .put(getActivationStatus({ location: undefined, device: undefined }))
      .next()
      .take([getActivationStatus.toString() + '.success', getActivationStatus.toString() + '.failure'])
      .next({
        type: getActivationStatus.toString() + '.success',
        payload: {
          isActivated: false,
        },
      })
      .call(forceDeactivation)
      .next();
  });

  it('test syncActivationStatusSaga with subscriptionStatus Expired', async () => {
    const onCompleteCallback = jest.fn();
    const syncActivationStatusAction = {
      payload: {
        onComplete: {
          callback: onCompleteCallback,
        },
      },
    };
    return testSaga(syncActivationStatusSaga, syncActivationStatusAction)
      .next()
      .call(generateLocation)
      .next()
      .select(selectDevice)
      .next()
      .select(selectBusinessName)
      .next('qsl')
      .select(selectAuthToken)
      .next('5cad7146f76103d10ca28a8f')
      .put(getActivationStatus({ location: undefined, device: undefined }))
      .next()
      .take([getActivationStatus.toString() + '.success', getActivationStatus.toString() + '.failure'])
      .next({
        type: getActivationStatus.toString() + '.success',
        payload: {
          isActivated: true,
          subscriptionStatus: 'Expired',
        },
      });
  });

  it('test syncActivationStatusSaga with isActivated true', async () => {
    const onCompleteCallback = jest.fn();
    const syncActivationStatusAction = {
      payload: {
        onComplete: {
          callback: onCompleteCallback,
        },
      },
    };

    return testSaga(syncActivationStatusSaga, syncActivationStatusAction)
      .next()
      .call(generateLocation)
      .next({})
      .select(selectDevice)
      .next(Immutable.fromJS({}))
      .select(selectBusinessName)
      .next('qsl')
      .select(selectAuthToken)
      .next('5cad7146f76103d10ca28a8f')
      .put(getActivationStatus({ location: '{}', device: '{}' }))
      .next()
      .take([getActivationStatus.toString() + '.success', getActivationStatus.toString() + '.failure'])
      .next({
        type: getActivationStatus.toString() + '.success',
        payload: {
          isActivated: true,
          subscriptionStatus: '',
        },
      });
  });

  it('test syncActivationStatusSaga with failure', async () => {
    const onCompleteCallback = jest.fn();
    const syncActivationStatusAction = {
      payload: {
        onComplete: {
          callback: onCompleteCallback,
        },
      },
    };
    testSaga(syncActivationStatusSaga, syncActivationStatusAction)
      .next()
      .call(generateLocation)
      .next()
      .select(selectDevice)
      .next()
      .select(selectBusinessName)
      .next('qsl')
      .select(selectAuthToken)
      .next('5cad7146f76103d10ca28a8f')
      .put(getActivationStatus({ location: undefined, device: undefined }))
      .next()
      .take([getActivationStatus.toString() + '.success', getActivationStatus.toString() + '.failure'])
      .next({
        type: getActivationStatus.toString() + '.failure',
        payload: {},
      })
      .select(selectOnlineSyncTime)
      .next({ onlineSyncTime: '2019-05-21T07:58:31.376Z' })
      .next();

    // expect(clearData).toHaveBeenCalled();
  });

  it('test setStoreInfoSaga', async () => {
    const dispatched = [];
    const setStoreInfoAction = {
      payload: {},
    };
    const result = await runSaga(
      {
        dispatch: action => {
          dispatched.push(action);
        },
        getState: () =>
          Immutable.fromJS({
            ...state,
          }),
      },
      setStoreInfoSaga,
      setStoreInfoAction
    ).done;

    expect(PrinterManager.setStoreInfo).toHaveBeenCalledTimes(1);
    // expect(PaymentOptions.updatePaymentOptions).toHaveBeenCalledTimes(1);
  });

  it('test syncEmployeeActivitySaga', async () => {
    return testSaga(syncEmployeeActivitySaga, {})
      .next()
      .select(selectStoreInfoUnsafe)
      .next(
        Immutable.fromJS({
          ...state.Storage.storeInfo,
        })
      )
      .put(
        syncEmployeeActivity({
          storeId: '5c4ec3a0e3d231664262c588',
          registerId: 1,
          userActionLogs: [
            {
              action: 'reduce_item',
              additionalInfo: {
                qty: 1,
                productId: '5cdd21d41a8792ec88fbf5d8',
                productName: 'product15',
                transactionId: '7E93C62B-3677-4CA2-AB4A-E7292582A6BC',
              },
              time: '2019-05-21T07:58:31.376Z',
              user: '5cad7146f76103d10ca28a8f',
            },
          ],
        })
      )
      .next()
      .take([syncEmployeeActivity.toString() + '.success', syncEmployeeActivity.toString() + '.failure'])
      .next({
        type: syncEmployeeActivity.toString() + '.success',
        payload: {},
      })
      .next();
  });

  it('test syncEmployeeActivitySaga failure', async () => {
    return testSaga(syncEmployeeActivitySaga, {})
      .next()
      .select(selectStoreInfoUnsafe)
      .next(
        Immutable.fromJS({
          ...state.Storage.storeInfo,
        })
      )
      .put(
        syncEmployeeActivity({
          storeId: '5c4ec3a0e3d231664262c588',
          registerId: 1,
          userActionLogs: [
            {
              action: 'reduce_item',
              additionalInfo: {
                qty: 1,
                productId: '5cdd21d41a8792ec88fbf5d8',
                productName: 'product15',
                transactionId: '7E93C62B-3677-4CA2-AB4A-E7292582A6BC',
              },
              time: '2019-05-21T07:58:31.376Z',
              user: '5cad7146f76103d10ca28a8f',
            },
          ],
        })
      )
      .next()
      .take([syncEmployeeActivity.toString() + '.success', syncEmployeeActivity.toString() + '.failure'])
      .next({
        type: syncEmployeeActivity.toString() + '.failure',
        payload: {},
      })
      .next();
  });

  it('test syncSequentialStartInfoSaga, sequentialReceiptNumber is false', async () => {
    const onCompleteCallback = jest.fn();
    testSaga(syncSequentialStartInfoSaga, {})
      .next()
      .select(selectStoreInfo)
      .next(
        Immutable.fromJS({
          sequentialReceiptNumber: false,
          name: 'qsl',
          registerObjectId: '12345',
          receiptNumberStart: 20,
          receiptDateStart: '2019-02-10T08:25:10.796Z',
        })
      )
      .put(setSyncInfo({ needFixsequential: false })).isDone;
  });

  it('test syncSequentialStartInfoSaga, hasEmptyReceiptNumberTranasaction is false', async () => {
    DAL.getTransactionsWithEmptyReceiptNumber = jest.fn(() => {
      return [];
    });
    testSaga(syncSequentialStartInfoSaga, {})
      .next()
      .select(selectStoreInfo)
      .next(
        Immutable.fromJS({
          sequentialReceiptNumber: true,
          registerObjectId: '123',
          name: 'qsl',
          receiptNumberStart: 20,
          receiptDateStart: '2019-02-10T08:25:10.796Z',
        })
      )
      .select(selectSequence)
      .next(
        Immutable.fromJS({
          receiptNumberStart: 20,
          receiptDateStart: '2019-02-10T08:25:10.796Z',
        })
      )
      .put(setSyncInfo({ needFixsequential: false }));
  });

  it('test syncSequentialStartInfoSaga', async () => {
    testSaga(syncSequentialStartInfoSaga, {})
      .next()
      .select(selectStoreInfo)
      .next(
        Immutable.fromJS({
          sequentialReceiptNumber: true,
          registerObjectId: '123',
          name: 'qsl',
          receiptNumberStart: 40,
          receiptDateStart: '2019-02-10T08:25:10.796Z',
        })
      )
      .select(selectSequence)
      .next(Immutable.fromJS({}))
      .put(syncSequentialStartInfo({ businessName: 'qsl', registerObjectId: '123' }))
      .next()
      .take([syncSequentialStartInfo.toString() + '.success', syncSequentialStartInfo.toString() + '.failure'])
      .next({
        type: syncSequentialStartInfo.toString() + '.success',
        payload: {
          newReceiptNumberStart: 30,
          newReceiptDateStart: '2019-02-10T08:25:10.796Z',
          receiptNumberStart: 30,
          receiptDateStart: '2019-02-10T08:25:10.796Z',
        },
      })
      .call(
        updateSequenceSaga,
        updateSequence({
          receiptNumberStart: 30,
          receiptDateStart: '2019-02-10T08:25:10.796Z',
          isStart: true,
          from: 'syncSequentialStartInfo2',
        })
      )
      .next()
      .put(
        setSyncInfo({
          needFixsequential: false,
        })
      )
      .next();
  });

  it('test syncSequentialStartInfoSaga transactions.size > 0', async () => {
    const currentDate = new Date('2019-09-20T23:00:00Z');
    Date = class extends Date {
      constructor(date) {
        if (date) {
          return super(date);
        }

        return currentDate;
      }
    };

    DAL.getTransactionsWithEmptyReceiptNumber = jest.fn().mockImplementation(() => {
      return [{ transactionId: 1 }];
    });

    testSaga(syncSequentialStartInfoSaga, {})
      .next()
      .select(selectStoreInfo)
      .next(
        Immutable.fromJS({
          sequentialReceiptNumber: true,
          registerObjectId: '123',
          name: 'qsl',
          receiptNumberStart: 40,
          receiptDateStart: '2019-02-10T08:25:10.796Z',
        })
      )
      .select(selectSequence)
      .next(Immutable.fromJS({}))
      .put(syncSequentialStartInfo({ businessName: 'qsl', registerObjectId: '123' }))
      .next()
      .take([syncSequentialStartInfo.toString() + '.success', syncSequentialStartInfo.toString() + '.failure'])
      .next({
        type: syncSequentialStartInfo.toString() + '.success',
        payload: {
          newReceiptNumberStart: 30,
          newReceiptDateStart: '2019-02-10T08:25:10.796Z',
          receiptNumberStart: 30,
          receiptDateStart: '2019-02-10T08:25:10.796Z',
        },
      })
      .call(
        updateSequenceSaga,
        updateSequence({
          receiptNumberStart: 31,
          receiptDateStart: new Date('2019-09-20T23:00:00Z').toISOString(),
          isStart: true,
          from: 'syncSequentialStartInfo1',
        })
      )
      .next();
  });

  /*
upgrade to "redux-saga-test-plan": "4.0.1"
* Renamed `takeLatestEffect` to `takeLatest` to support the `takeLatest` effect.
Details: https://raw.githubusercontent.com/jfairbank/redux-saga-test-plan/master/CHANGELOG.md
*/

  it('should takeLatestAction', () => {
    testSaga(dataSync)
      .next()
      .takeLatest('syncAllStoreInfo', syncSaga)
      .next()
      .takeLatest('syncTransactionsAction', syncTransactionsSaga)
      .next()
      .takeLatest('syncShiftsAction', syncShiftsSaga)
      .next()
      .takeLatest('syncProductsAction', syncProductsSaga)
      .next()
      .takeLatest('syncEmployeesAction', syncEmployeesSaga)
      .next()
      .takeLatest('syncQuickSelectLayoutAction', syncQuickSelectLayoutSaga)
      .next()
      .takeLatest('syncTableLayoutAction', syncTableLayoutSaga)
      .next()
      .takeLatest('syncStoreInfoAction', syncStoreInfoSaga)
      .next()
      .takeLatest('syncPriceBookAction', syncPriceBookSaga)
      .next()
      .takeLatest('syncPromotionsBegin', syncPromotionsSaga)
      .next()
      .takeLatest('syncActivationStatus', syncActivationStatusSaga)
      .next()
      .takeLatest('setStoreInfo', setStoreInfoSaga)
      .next()
      .takeLatest('syncEmployeeActivityBegin', syncEmployeeActivitySaga)
      .next()
      .takeLatest('syncSequentialStartInfoBegin', syncSequentialStartInfoSaga).isDone;
  });
});
