import { expectSaga, testSaga } from 'redux-saga-test-plan';
import { call, select } from 'redux-saga/effects';
import { DataType, MessageSender, performLearnerAction, proposerCompleteAction, requestProposalAction, toggleLoadingMask } from '../../../../ts/actions';
import {
  DataEmptyError,
  ForbiddenCancelError,
  ForbiddenCheckOutError,
  ForbiddenRefundError,
  IsLearningError,
  NeedLearnError,
  OnlyCheckoutError,
  ProposalTimeOutError,
  ProposerNoNetError,
  ProposerOfflineError,
  ProposerReceivedError,
  SuccessMessage,
} from '../../../../ts/constants';
import { sendMessageToServer } from '../../../../ts/sagas/mrs';
import { checkProposeStateError } from '../../../../ts/sagas/mrs/checkSync';
import { handleChosenMessage } from '../../../../ts/sagas/mrs/roles/acceptor';
import {
  checkLimitBeforeExecuteSaga,
  handleAcceptorMessage,
  handleProposalResult,
  handleProposalTimeout,
  requestProposalActionSaga,
  resetProposalFlags,
} from '../../../../ts/sagas/mrs/roles/proposer';
import { getMRSClientRunning } from '../../../../ts/sagas/mrs/roles/slave';
import { selectClientIp, selectIPAddress, selectIsEnabledMRS, selectIsMaster, selectSlavePid } from '../../../../ts/sagas/selector';
import globalConfig from '../../../../ts/utils/globalConfig';
import { OrderOperationEnum } from '../../../../ts/utils/logComponent';

describe('test handleAcceptorMessage', () => {
  test('RESPONSE', () => {
    const message = { dataType: DataType.RESPONSE };
    testSaga(handleAcceptorMessage, message).next().call(handleProposalResult, message).next().isDone();
  });

  test('CHOSEN', () => {
    const message = { dataType: DataType.CHOSEN };
    testSaga(handleAcceptorMessage, message).next().call(handleChosenMessage, message).next().isDone();
  });

  test('TIMEOUT', () => {
    const message = { dataType: DataType.TIMEOUT };
    testSaga(handleAcceptorMessage, message).next().isDone();
  });
});

describe('test checkLimitBeforeExecuteSaga', () => {
  const historicalTransaction = {
    mrs: false,
    isOnlineOrder: false,
    transactionId: '1',
  };
  it('disable mrs', async () => {
    const transaction = {};
    const orderOperation = '';
    const result = await expectSaga(checkLimitBeforeExecuteSaga, { type: '', payload: { transaction, orderOperation } })
      .provide([[select(selectIsEnabledMRS), false]])
      .silentRun();

    expect(result.returnValue).toEqual(SuccessMessage());
  });

  it('historical local order, master checkout', async () => {
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction: historicalTransaction, orderOperation: OrderOperationEnum.CheckOut },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), true],
        [select(selectIPAddress), '111'],
        [select(selectIsMaster), true],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(SuccessMessage());
  });

  it('historical local open order, client checkout', async () => {
    const transaction = {
      mrs: false,
      isOpen: true,
      isOnlineOrder: false,
    };
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction, orderOperation: OrderOperationEnum.CheckOut },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), false],
        [select(selectIPAddress), ''],
        [select(selectIsMaster), false],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(SuccessMessage());
  });

  it('historical local pre order, client checkout', async () => {
    const transaction = {
      mrs: false,
      isOpen: false,
      isOnlineOrder: false,
      preOrderId: '111',
    };
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction, orderOperation: OrderOperationEnum.CheckOut },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), false],
        [select(selectIPAddress), ''],
        [select(selectIsMaster), false],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(ForbiddenCheckOutError);
  });

  it('online orders client checkout', async () => {
    const transaction = {
      mrs: false,
      isOnlineOrder: true,
    };
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction, orderOperation: OrderOperationEnum.CheckOut },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), false],
        [select(selectIPAddress), ''],
        [select(selectIsMaster), false],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(ForbiddenCheckOutError);
  });

  it('new order client checkout', async () => {
    const transaction = {
      mrs: false,
    };
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction, orderOperation: OrderOperationEnum.CheckOut },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), false],
        [select(selectIPAddress), ''],
        [select(selectIsMaster), false],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(ForbiddenCheckOutError);
  });

  it('new order master checkout', async () => {
    const transaction = {
      mrs: false,
    };
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction, orderOperation: OrderOperationEnum.CheckOut },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), true],
        [select(selectIPAddress), '11'],
        [select(selectIsMaster), true],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(SuccessMessage());
  });

  it('online orders master checkout', async () => {
    const transaction = {
      mrs: false,
      isOnlineOrder: true,
    };
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction, orderOperation: OrderOperationEnum.CheckOut },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), true],
        [select(selectIPAddress), '111'],
        [select(selectIsMaster), true],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(SuccessMessage());
  });

  it('synced local orders, master checkout', async () => {
    const transaction = {
      mrs: true,
      isOnlineOrder: false,
    };
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction, orderOperation: OrderOperationEnum.CheckOut },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), true],
        [select(selectIPAddress), '1'],
        [select(selectIsMaster), true],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(SuccessMessage());
  });

  it('synced local orders, client checkout', async () => {
    const transaction = {
      mrs: true,
      isOnlineOrder: false,
    };
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction, orderOperation: OrderOperationEnum.CheckOut },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), true],
        [select(selectIPAddress), '1'],
        [select(selectIsMaster), false],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(ForbiddenCheckOutError);
  });

  it('historical local order, master refund', async () => {
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction: historicalTransaction, orderOperation: OrderOperationEnum.Refund },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), false],
        [select(selectIPAddress), ''],
        [select(selectIsMaster), true],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(SuccessMessage());
  });

  it('historical local order, client refund', async () => {
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction: historicalTransaction, orderOperation: OrderOperationEnum.Refund },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), false],
        [select(selectIPAddress), ''],
        [select(selectIsMaster), false],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(SuccessMessage());
  });

  it('online orders client refund', async () => {
    const transaction = {
      mrs: false,
      isOnlineOrder: true,
    };
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction, orderOperation: OrderOperationEnum.Refund },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), false],
        [select(selectIPAddress), ''],
        [select(selectIsMaster), false],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(ForbiddenRefundError);
  });

  it('online orders master refund', async () => {
    const transaction = {
      mrs: false,
      isOnlineOrder: true,
    };
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction, orderOperation: OrderOperationEnum.Refund },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), true],
        [select(selectIPAddress), '111'],
        [select(selectIsMaster), true],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(SuccessMessage());
  });

  it('synced local orders, master refund', async () => {
    const transaction = {
      mrs: true,
      isOnlineOrder: false,
    };
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction, orderOperation: OrderOperationEnum.Refund },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), true],
        [select(selectIPAddress), '1'],
        [select(selectIsMaster), true],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(SuccessMessage());
  });

  it('synced local orders, client refund', async () => {
    const transaction = {
      mrs: true,
      isOnlineOrder: false,
    };
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction, orderOperation: OrderOperationEnum.Refund },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), true],
        [select(selectIPAddress), '1'],
        [select(selectIsMaster), false],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(ForbiddenRefundError);
  });

  it('historical local order, master cancel', async () => {
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction: historicalTransaction, orderOperation: OrderOperationEnum.Cancel },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), false],
        [select(selectIPAddress), ''],
        [select(selectIsMaster), true],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(SuccessMessage());
  });

  it('historical local order, client cancel', async () => {
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction: historicalTransaction, orderOperation: OrderOperationEnum.Cancel },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), false],
        [select(selectIPAddress), ''],
        [select(selectIsMaster), false],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(SuccessMessage());
  });

  it('online orders client cancel', async () => {
    const transaction = {
      mrs: false,
      isOnlineOrder: true,
    };
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction, orderOperation: OrderOperationEnum.Cancel },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), false],
        [select(selectIPAddress), ''],
        [select(selectIsMaster), false],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(ForbiddenCancelError);
  });

  it('online orders master cancel', async () => {
    const transaction = {
      mrs: false,
      isOnlineOrder: true,
    };
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction, orderOperation: OrderOperationEnum.Cancel },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), true],
        [select(selectIPAddress), '111'],
        [select(selectIsMaster), true],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(SuccessMessage());
  });

  it('synced local orders, master cancel', async () => {
    const transaction = {
      mrs: true,
      isOnlineOrder: false,
    };
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction, orderOperation: OrderOperationEnum.Cancel },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), true],
        [select(selectIPAddress), '1'],
        [select(selectIsMaster), true],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(SuccessMessage());
  });

  it('synced local orders, client cancel', async () => {
    const transaction = {
      mrs: true,
      isOnlineOrder: false,
    };
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction, orderOperation: OrderOperationEnum.Cancel },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), true],
        [select(selectIPAddress), '1'],
        [select(selectIsMaster), false],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(ForbiddenCancelError);
  });

  it('new local order, isClientRunning, master Save', async () => {
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction: historicalTransaction, orderOperation: OrderOperationEnum.Save },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), true],
        [select(selectIPAddress), '11'],
        [select(selectIsMaster), true],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(SuccessMessage());
  });

  it('new local order,isClientRunning, client Save', async () => {
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction: historicalTransaction, orderOperation: OrderOperationEnum.Save },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), true],
        [select(selectIPAddress), '111'],
        [select(selectIsMaster), false],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(SuccessMessage());
  });

  it('new local order, !isClientRunning, master Save', async () => {
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction: historicalTransaction, orderOperation: OrderOperationEnum.Save },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), false],
        [select(selectIPAddress), '11'],
        [select(selectIsMaster), true],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(ProposerOfflineError);
  });

  it('new local order,!isClientRunning, client Save', async () => {
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction: historicalTransaction, orderOperation: OrderOperationEnum.Save },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), false],
        [select(selectIPAddress), '111'],
        [select(selectIsMaster), false],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(ProposerOfflineError);
  });

  it('historical local orders, !isClientRunning, master update', async () => {
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction: historicalTransaction, orderOperation: OrderOperationEnum.Update },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), false],
        [select(selectIPAddress), '11'],
        [select(selectIsMaster), true],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(ProposerOfflineError);
  });

  it('historical local orders,!isClientRunning, client update', async () => {
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction: historicalTransaction, orderOperation: OrderOperationEnum.Update },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), false],
        [select(selectIPAddress), '111'],
        [select(selectIsMaster), false],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(OnlyCheckoutError);
  });

  it('historical local orders, isClientRunning, master update', async () => {
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction: historicalTransaction, orderOperation: OrderOperationEnum.Update },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), true],
        [select(selectIPAddress), '11'],
        [select(selectIsMaster), true],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(SuccessMessage());
  });

  it('historical local orders, isClientRunning, client update', async () => {
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction: historicalTransaction, orderOperation: OrderOperationEnum.Update },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), true],
        [select(selectIPAddress), '111'],
        [select(selectIsMaster), false],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(SuccessMessage());
  });

  it('online orders, !isClientRunning, master update', async () => {
    const transaction = {
      isOnlineOrder: true,
    };
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction, orderOperation: OrderOperationEnum.Update },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), false],
        [select(selectIPAddress), '11'],
        [select(selectIsMaster), true],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(SuccessMessage());
  });

  it('online orders,!isClientRunning, client update', async () => {
    const transaction = {
      isOnlineOrder: true,
    };

    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction, orderOperation: OrderOperationEnum.Update },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), false],
        [select(selectIPAddress), '111'],
        [select(selectIsMaster), false],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(SuccessMessage());
  });

  it('online orders, isClientRunning, master update', async () => {
    const transaction = {
      isOnlineOrder: true,
    };
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction, orderOperation: OrderOperationEnum.Update },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), true],
        [select(selectIPAddress), '11'],
        [select(selectIsMaster), true],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(SuccessMessage());
  });

  it('online orders, isClientRunning, client update', async () => {
    const transaction = {
      isOnlineOrder: true,
    };
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction, orderOperation: OrderOperationEnum.Update },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), true],
        [select(selectIPAddress), '111'],
        [select(selectIsMaster), false],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(SuccessMessage());
  });

  it('synced orders, !isClientRunning, master update', async () => {
    const transaction = {
      mrs: true,
    };
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction, orderOperation: OrderOperationEnum.Update },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), false],
        [select(selectIPAddress), '11'],
        [select(selectIsMaster), true],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(ProposerOfflineError);
  });

  it('synced orders,!isClientRunning, client update', async () => {
    const transaction = {
      mrs: true,
    };

    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction, orderOperation: OrderOperationEnum.Update },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), false],
        [select(selectIPAddress), '111'],
        [select(selectIsMaster), false],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(ProposerOfflineError);
  });

  it('synced orders, isClientRunning, master update', async () => {
    const transaction = {
      mrs: true,
    };
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction, orderOperation: OrderOperationEnum.Update },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), true],
        [select(selectIPAddress), '11'],
        [select(selectIsMaster), true],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(SuccessMessage());
  });

  it('synced orders, isClientRunning, client update', async () => {
    const transaction = {
      mrs: true,
    };
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction: historicalTransaction, orderOperation: OrderOperationEnum.Update },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), true],
        [select(selectIPAddress), '111'],
        [select(selectIsMaster), false],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(SuccessMessage());
  });

  it('synced orders, no ip, client update', async () => {
    const transaction = {
      mrs: true,
    };
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction, orderOperation: OrderOperationEnum.Update },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), true],
        [select(selectIPAddress), ''],
        [select(selectIsMaster), false],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(ProposerNoNetError);
  });

  it('online orders, no ip, client update', async () => {
    const transaction = {
      isOnlineOrder: true,
    };
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction, orderOperation: OrderOperationEnum.Update },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), true],
        [select(selectIPAddress), ''],
        [select(selectIsMaster), false],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(SuccessMessage());
  });

  it('historical local orders, no ip, client update', async () => {
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction: historicalTransaction, orderOperation: OrderOperationEnum.Update },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), false],
        [select(selectIPAddress), ''],
        [select(selectIsMaster), false],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(OnlyCheckoutError);
  });

  it('historical local orders, no ip, master update', async () => {
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction: historicalTransaction, orderOperation: OrderOperationEnum.Update },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), true],
        [select(selectIPAddress), ''],
        [select(selectIsMaster), true],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(ProposerNoNetError);
  });

  it('synced local orders, no ip, client cancel', async () => {
    const transaction = {
      mrs: true,
      isOnlineOrder: false,
    };
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction, orderOperation: OrderOperationEnum.Cancel },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), true],
        [select(selectIPAddress), ''],
        [select(selectIsMaster), false],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(ForbiddenCancelError);
  });

  it('synced local orders, no ip, master cancel', async () => {
    const transaction = {
      mrs: true,
      isOnlineOrder: false,
    };
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction, orderOperation: OrderOperationEnum.Cancel },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), true],
        [select(selectIPAddress), ''],
        [select(selectIsMaster), true],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(ProposerNoNetError);
  });

  it('online orders, no ip, master checkout', async () => {
    const transaction = {
      isOnlineOrder: true,
    };
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction, orderOperation: OrderOperationEnum.CheckOut },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), true],
        [select(selectIPAddress), ''],
        [select(selectIsMaster), true],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(SuccessMessage());
  });

  it('online orders, no ip, client checkout', async () => {
    const transaction = {
      isOnlineOrder: true,
    };
    const result = await expectSaga(checkLimitBeforeExecuteSaga, {
      type: '',
      payload: { transaction, orderOperation: OrderOperationEnum.CheckOut },
    })
      .provide([
        [select(selectIsEnabledMRS), true],
        [call(getMRSClientRunning), true],
        [select(selectIPAddress), ''],
        [select(selectIsMaster), false],
      ])
      .silentRun();

    expect(result.returnValue).toEqual(ForbiddenCheckOutError);
  });
});

describe('test requestProposalActionSaga', () => {
  test('check false', () => {
    const onComplete = jest.fn();
    const data = [];
    const action = requestProposalAction({ onComplete, data });
    testSaga(requestProposalActionSaga, action)
      .next()
      .select(selectSlavePid)
      .next(1)
      .put(toggleLoadingMask({ visible: true }))
      .next()
      .call(checkProposeStateError, data)
      .next(DataEmptyError)
      .call(onComplete, DataEmptyError)
      .next()
      .put(toggleLoadingMask({ visible: false }))
      .next()
      .isDone();
  });
  test('check true', () => {
    const onComplete = jest.fn();
    const data = [{ transactionId: '12' }];
    const action = requestProposalAction({ onComplete, data });
    Date.now = jest.fn().mockImplementationOnce(() => 1466424490000);

    const message = {
      messageSender: MessageSender.PROPOSER,
      data,
      uuid: '1466424490000',
      pid: 1,
      dataType: DataType.PROPOSAL,
      proposer: '192',
      summary: undefined,
    };
    testSaga(requestProposalActionSaga, action)
      .next()
      .select(selectSlavePid)
      .next(message.pid)
      .put(toggleLoadingMask({ visible: true }))
      .next()
      .call(checkProposeStateError, data)
      .next(SuccessMessage())
      .select(selectClientIp)
      .next(message.proposer)
      .call(sendMessageToServer, message)
      .next()
      .take(proposerCompleteAction.toString())
      .next(proposerCompleteAction(SuccessMessage()))
      .put(toggleLoadingMask({ visible: false }))
      .next()
      .call(onComplete, SuccessMessage())
      .next()
      .isDone();

    expect(globalConfig.proposalId).toBeFalsy();
  });
});

describe('test handleProposalResult', () => {
  test('failed', () => {
    const message = {
      uuid: '12',
    };

    testSaga(handleProposalResult, message)
      .next()
      .put(
        proposerCompleteAction({
          errorCode: ProposerReceivedError.errorCode,
          errorMessage: `globalConfig.proposalId: ${globalConfig.proposalId}, uuid: ${message.uuid}`,
        })
      )
      .next()
      .isDone();
  });

  test('true', () => {
    globalConfig.proposalId = '12';
    const message = {
      uuid: '12',
      data: { isAccepted: true, needSyncUp: true },
    };
    testSaga(handleProposalResult, message)
      .next()
      .put(proposerCompleteAction(SuccessMessage('proposal succeed!')))
      .next()
      .put(performLearnerAction({ source: 'handleProposalResult' }))
      .next()
      .isDone();
  });
});
describe('test try propose', () => {
  test('IsLearningError', () => {
    const onComplete = jest.fn();
    const data = [];
    const action = requestProposalAction({ onComplete, data });
    testSaga(requestProposalActionSaga, action)
      .next()
      .select(selectSlavePid)
      .next(1)
      .put(toggleLoadingMask({ visible: true }))
      .next()
      .call(checkProposeStateError, data)
      .next(IsLearningError)
      .delay(3 * 1000)
      .next()
      .call(requestProposalActionSaga, { type: action.type, payload: { ...action.payload, retryFlag: true } })
      .next()
      .isDone();
  });
  test('NeedLearnError', () => {
    const onComplete = jest.fn();
    const data = [{ transactionId: '12' }];
    const action = requestProposalAction({ onComplete, data });
    Date.now = jest.fn().mockImplementationOnce(() => 1466424490000);

    const message = {
      messageSender: MessageSender.PROPOSER,
      data,
      uuid: '1466424490000',
      pid: 1,
      dataType: DataType.PROPOSAL,
      proposer: '192',
      summary: undefined,
    };
    testSaga(requestProposalActionSaga, action)
      .next()
      .select(selectSlavePid)
      .next(message.pid)
      .put(toggleLoadingMask({ visible: true }))
      .next()
      .call(checkProposeStateError, data)
      .next(SuccessMessage())
      .select(selectClientIp)
      .next(message.proposer)
      .call(sendMessageToServer, message)
      .next()
      .take(proposerCompleteAction.toString())
      .next(proposerCompleteAction(NeedLearnError))
      .delay(3 * 1000)
      .next()
      .call(requestProposalActionSaga, { type: action.type, payload: { ...action.payload, retryFlag: true } })
      .next()
      .isDone();

    expect(globalConfig.proposalId).toBeFalsy();
  });
});

describe('test handleProposalTimeout', () => {
  test('timeout', () => {
    globalConfig.proposalId = '1';
    testSaga(handleProposalTimeout, { uuid: '1' }).next().put(proposerCompleteAction(ProposalTimeOutError)).next().isDone();
    globalConfig.proposalId = '';
  });
});

test('resetProposalFlags', () => {
  resetProposalFlags();
  expect(globalConfig.isProposing).toBe(false);
  expect(globalConfig.proposalId).toBe('');
});
