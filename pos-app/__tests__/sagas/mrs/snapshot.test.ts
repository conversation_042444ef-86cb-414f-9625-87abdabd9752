import { expectSaga, testSaga } from 'redux-saga-test-plan';
import { call, delay, put, select } from 'redux-saga/effects';
import { updateMRSSate } from '../../../ts/actions';
import { INIT_SNAPSHOT_VERSION } from '../../../ts/constants';
import DAL from '../../../ts/dal';
import { broadcastChosenMessageToClients } from '../../../ts/sagas/mrs/roles/acceptor';
import { updateSlaveStatusSaga } from '../../../ts/sagas/mrs/roles/slave';
import { cleanUselessTransactionsLogSaga, getMrsIdsFromDatabase, loopCleanTransactionsLogSaga, saveSnapshot } from '../../../ts/sagas/mrs/snapshot';
import {
  selectEmployeeId,
  selectMrsDeleteInterval,
  selectMrsDeleteLoop,
  selectMrsMaxCommitHeight,
  selectMrsMinCommitHeight,
  selectServerIp,
} from '../../../ts/sagas/selector';

// Mock external dependencies
jest.mock('../../../ts/dal');
jest.mock('../../../ts/sagas/mrs/roles/slave');
jest.mock('../../../ts/sagas/mrs/roles/acceptor');
jest.mock('../../../ts/utils/logComponent');
jest.mock('../../../ts/utils/json', () => ({
  realmObjectToJSObject: jest.fn(),
}));

const mockDAL = DAL as jest.Mocked<typeof DAL>;
const mockRealmObjectToJSObject = require('../../../ts/utils/json').realmObjectToJSObject;

describe('loopCleanTransactionsLogSaga', () => {
  describe('Early return scenarios', () => {
    test('should return early when interval is negative', () => {
      testSaga(loopCleanTransactionsLogSaga)
        .next()
        .select(selectMrsDeleteInterval)
        .next(-1) // negative interval
        .select(selectMrsDeleteLoop)
        .next(true)
        .select(selectEmployeeId)
        .next(null) // not signed in
        .isDone(); // should return early due to interval < 0
    });

    test('should return early when employee is signed in', () => {
      testSaga(loopCleanTransactionsLogSaga)
        .next()
        .select(selectMrsDeleteInterval)
        .next(30) // valid interval
        .select(selectMrsDeleteLoop)
        .next(true)
        .select(selectEmployeeId)
        .next('employee123') // signed in
        .isDone(); // should return early due to signedIn
    });

    test('should return early when both interval is negative and employee is signed in', () => {
      testSaga(loopCleanTransactionsLogSaga)
        .next()
        .select(selectMrsDeleteInterval)
        .next(-1) // negative interval
        .select(selectMrsDeleteLoop)
        .next(true)
        .select(selectEmployeeId)
        .next('employee123') // signed in
        .isDone(); // should return early due to both conditions
    });
  });

  describe('Second early return scenarios', () => {
    test('should return early when enableLoop is false', () => {
      testSaga(loopCleanTransactionsLogSaga)
        .next()
        .select(selectMrsDeleteInterval)
        .next(30) // valid interval
        .select(selectMrsDeleteLoop)
        .next(false) // loop disabled
        .select(selectEmployeeId)
        .next(null) // not signed in
        .call(cleanUselessTransactionsLogSaga)
        .next(true) // cleanUselessTransactionsLogSaga returns true (needClean = true)
        .isDone(); // should return early because enableLoop is false
    });

    test('should return early when needClean is false', () => {
      testSaga(loopCleanTransactionsLogSaga)
        .next()
        .select(selectMrsDeleteInterval)
        .next(30) // valid interval
        .select(selectMrsDeleteLoop)
        .next(true) // loop enabled
        .select(selectEmployeeId)
        .next(null) // not signed in
        .call(cleanUselessTransactionsLogSaga)
        .next(false) // cleanUselessTransactionsLogSaga returns false (needClean = false)
        .isDone(); // should return early because needClean is false
    });

    test('should return early when both enableLoop is false and needClean is false', () => {
      testSaga(loopCleanTransactionsLogSaga)
        .next()
        .select(selectMrsDeleteInterval)
        .next(30) // valid interval
        .select(selectMrsDeleteLoop)
        .next(false) // loop disabled
        .select(selectEmployeeId)
        .next(null) // not signed in
        .call(cleanUselessTransactionsLogSaga)
        .next(false) // cleanUselessTransactionsLogSaga returns false (needClean = false)
        .isDone(); // should return early due to both conditions
    });
  });

  describe('Loop execution scenarios', () => {
    test('should execute loop and break when employee signs in', async () => {
      const interval = 30;
      let callCount = 0;

      const mockCleanUselessTransactionsLogSaga = jest.fn(() => {
        callCount++;
        return true; // always return true (needs cleaning)
      });

      await expectSaga(loopCleanTransactionsLogSaga)
        .provide([
          [select(selectMrsDeleteInterval), interval],
          [select(selectMrsDeleteLoop), true],
          [select(selectEmployeeId), null], // not signed in initially
          [call(cleanUselessTransactionsLogSaga), mockCleanUselessTransactionsLogSaga()],
          [delay(interval * 1000), undefined], // mock delay
          [select(selectEmployeeId), 'employee123'], // employee signs in during loop
        ])
        .call(cleanUselessTransactionsLogSaga)
        .delay(interval * 1000)
        .call(cleanUselessTransactionsLogSaga)
        .run();
    });

    test('should execute loop and break when needClean becomes false', async () => {
      const interval = 45;
      let callCount = 0;

      const mockCleanUselessTransactionsLogSaga = jest.fn(() => {
        callCount++;
        return callCount === 1; // first call returns true, second returns false
      });

      await expectSaga(loopCleanTransactionsLogSaga)
        .provide([
          [select(selectMrsDeleteInterval), interval],
          [select(selectMrsDeleteLoop), true],
          [select(selectEmployeeId), null], // not signed in initially
          [call(cleanUselessTransactionsLogSaga), mockCleanUselessTransactionsLogSaga()],
          [delay(interval * 1000), undefined], // mock delay
          [select(selectEmployeeId), null], // still not signed in
        ])
        .call(cleanUselessTransactionsLogSaga)
        .delay(interval * 1000)
        .call(cleanUselessTransactionsLogSaga)
        .run();
    });

    test('should execute multiple loop iterations before breaking', async () => {
      const interval = 20;
      let callCount = 0;

      const mockCleanUselessTransactionsLogSaga = jest.fn(() => {
        callCount++;
        return callCount < 3; // first 2 calls return true, 3rd returns false
      });

      await expectSaga(loopCleanTransactionsLogSaga)
        .provide([
          [select(selectMrsDeleteInterval), interval],
          [select(selectMrsDeleteLoop), true],
          [select(selectEmployeeId), null], // never signs in
          [call(cleanUselessTransactionsLogSaga), mockCleanUselessTransactionsLogSaga()],
          [delay(interval * 1000), undefined], // mock delay
        ])
        .call(cleanUselessTransactionsLogSaga)
        .delay(interval * 1000)
        .call(cleanUselessTransactionsLogSaga)
        .delay(interval * 1000)
        .call(cleanUselessTransactionsLogSaga)
        .run();
    });
  });

  describe('Edge cases', () => {
    test('should handle zero interval correctly', async () => {
      let callCount = 0;

      const mockCleanUselessTransactionsLogSaga = jest.fn(() => {
        callCount++;
        return callCount === 1; // first call returns true, second returns false
      });

      await expectSaga(loopCleanTransactionsLogSaga)
        .provide([
          [select(selectMrsDeleteInterval), 0],
          [select(selectMrsDeleteLoop), true],
          [select(selectEmployeeId), null],
          [call(cleanUselessTransactionsLogSaga), mockCleanUselessTransactionsLogSaga()],
          [delay(0), undefined], // mock zero delay
        ])
        .call(cleanUselessTransactionsLogSaga)
        .delay(0)
        .call(cleanUselessTransactionsLogSaga)
        .run();
    });

    test('should handle when employee signs in and cleanup is not needed simultaneously', async () => {
      const interval = 60;
      let callCount = 0;

      const mockCleanUselessTransactionsLogSaga = jest.fn(() => {
        callCount++;
        return callCount === 1; // first call returns true, second returns false
      });

      await expectSaga(loopCleanTransactionsLogSaga)
        .provide([
          [select(selectMrsDeleteInterval), interval],
          [select(selectMrsDeleteLoop), true],
          [select(selectEmployeeId), null], // not signed in initially
          [call(cleanUselessTransactionsLogSaga), mockCleanUselessTransactionsLogSaga()],
          [delay(interval * 1000), undefined], // mock delay
          [select(selectEmployeeId), 'employee123'], // employee signs in AND cleanup is done
        ])
        .call(cleanUselessTransactionsLogSaga)
        .delay(interval * 1000)
        .call(cleanUselessTransactionsLogSaga)
        .run();
    });
  });
});

describe('saveSnapshot', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Setup default mocks for DAL functions used by createFirstSnapshot and createOpenOrderSnapshot
    mockDAL.getMrsOpenOrderList.mockReturnValue([{ transactionId: 'order1' }, { transactionId: 'order2' }]);
    // Mock realmObjectToJSObject to return valid transaction objects
    mockRealmObjectToJSObject.mockReturnValue({
      transactionId: 'order1',
      customerId: 'customer1',
      items: [],
    });
    // Mock getSnapshotIdFromTransactionsLog for getMrsIdsFromDatabase
    mockDAL.getSnapshotIdFromTransactionsLog.mockReturnValue({
      snapshotVersion: 1,
      snapshotPid: 100,
      backupPid: 50,
    });
  });

  describe('Early return scenarios', () => {
    test('should return early when maxCommitHeight < minCommitHeight', () => {
      testSaga(saveSnapshot)
        .next()
        .select(selectMrsMaxCommitHeight)
        .next(10) // maxCommitHeight
        .select(selectMrsMinCommitHeight)
        .next(20) // minCommitHeight > maxCommitHeight
        .isDone(); // should return early
    });

    test('should return early when maxPid - maxSnapshotPid < minCommitHeight', async () => {
      const maxPid = 100;
      const maxVersion = 2; // > INIT_SNAPSHOT_VERSION
      const maxSnapshotPid = 95;
      const minCommitHeight = 10;

      mockDAL.getMaxPidFromTransactionsLog.mockReturnValue(maxPid);
      mockDAL.getMaxSnapshotVersionFromTransactionsLog.mockReturnValue(maxVersion);
      mockDAL.getMaxSnapshotPidByVersion.mockReturnValue(maxSnapshotPid);

      await expectSaga(saveSnapshot)
        .provide([
          [select(selectMrsMaxCommitHeight), 50],
          [select(selectMrsMinCommitHeight), minCommitHeight],
        ])
        .run();

      // Should not proceed to save operations
      expect(mockDAL.saveTransactionsLogInBatch).not.toHaveBeenCalled();
    });
  });

  describe('First snapshot creation', () => {
    test('should create first snapshot when maxVersion equals INIT_SNAPSHOT_VERSION', async () => {
      const maxPid = 100;
      const maxVersion = INIT_SNAPSHOT_VERSION;
      const mockMrsIds = { pid: 101, snapshotPid: 101, backupPid: 50, snapshotVersion: 1 };

      mockDAL.getMaxPidFromTransactionsLog.mockReturnValue(maxPid);
      mockDAL.getMaxSnapshotVersionFromTransactionsLog.mockReturnValue(maxVersion);
      mockDAL.saveTransactionsLogInBatch.mockReturnValue(true);
      // Update the snapshot values to match what getMrsIdsFromDatabase will return
      mockDAL.getSnapshotIdFromTransactionsLog.mockReturnValue({
        snapshotVersion: mockMrsIds.snapshotVersion,
        snapshotPid: mockMrsIds.snapshotPid,
        backupPid: mockMrsIds.backupPid,
      });

      await expectSaga(saveSnapshot)
        .provide([
          [select(selectMrsMaxCommitHeight), 50],
          [select(selectMrsMinCommitHeight), 10],
          [select(selectServerIp), '***********'],
          [call(getMrsIdsFromDatabase), mockMrsIds],
          [put(updateMRSSate(mockMrsIds)), undefined],
          [call(updateSlaveStatusSaga), undefined],
        ])
        .put(updateMRSSate(mockMrsIds))
        .call(updateSlaveStatusSaga)
        .run();

      expect(mockDAL.saveTransactionsLogInBatch).toHaveBeenCalled();
      expect(broadcastChosenMessageToClients).toHaveBeenCalledWith({
        pid: mockMrsIds.pid,
        data: null,
        ip: '***********',
      });
    });
  });

  describe('Open order snapshot creation', () => {
    test('should create open order snapshot when conditions are met', async () => {
      const maxPid = 200;
      const maxVersion = 2; // > INIT_SNAPSHOT_VERSION
      const maxSnapshotPid = 150;
      const maxCommitHeight = 40;
      const minCommitHeight = 10;
      const mockMrsIds = { pid: 201, snapshotPid: 201, backupPid: 100, snapshotVersion: 3 };

      mockDAL.getMaxPidFromTransactionsLog.mockReturnValue(maxPid);
      mockDAL.getMaxSnapshotVersionFromTransactionsLog.mockReturnValue(maxVersion);
      mockDAL.getMaxSnapshotPidByVersion.mockReturnValue(maxSnapshotPid);
      mockDAL.saveTransactionsLogInBatch.mockReturnValue(true);
      // Update the snapshot values to match what getMrsIdsFromDatabase will return
      mockDAL.getSnapshotIdFromTransactionsLog.mockReturnValue({
        snapshotVersion: mockMrsIds.snapshotVersion,
        snapshotPid: mockMrsIds.snapshotPid,
        backupPid: mockMrsIds.backupPid,
      });

      await expectSaga(saveSnapshot)
        .provide([
          [select(selectMrsMaxCommitHeight), maxCommitHeight],
          [select(selectMrsMinCommitHeight), minCommitHeight],
          [select(selectServerIp), '***********'],
          [call(getMrsIdsFromDatabase), mockMrsIds],
          [put(updateMRSSate(mockMrsIds)), undefined],
          [call(updateSlaveStatusSaga), undefined],
        ])
        .put(updateMRSSate(mockMrsIds))
        .call(updateSlaveStatusSaga)
        .run();

      expect(mockDAL.saveTransactionsLogInBatch).toHaveBeenCalled();
    });

    test('should handle first snapshot + 1 version case (ignore firstSnapshot)', async () => {
      const maxPid = 300;
      const maxVersion = INIT_SNAPSHOT_VERSION + 1; // special case
      const maxSnapshotPid = 250;
      const maxCommitHeight = 30;
      const minCommitHeight = 15;

      mockDAL.getMaxPidFromTransactionsLog.mockReturnValue(maxPid);
      mockDAL.getMaxSnapshotVersionFromTransactionsLog.mockReturnValue(maxVersion);
      mockDAL.getMaxSnapshotPidByVersion.mockReturnValue(maxSnapshotPid);
      mockDAL.saveTransactionsLogInBatch.mockReturnValue(true);

      // For this test, we need maxPid - INIT_PID >= maxCommitHeight to create snapshot
      // maxPid=300, INIT_PID=1, diff=299, maxCommitHeight=30, so condition is met

      await expectSaga(saveSnapshot)
        .provide([
          [select(selectMrsMaxCommitHeight), maxCommitHeight],
          [select(selectMrsMinCommitHeight), minCommitHeight],
        ])
        .run();

      expect(mockDAL.saveTransactionsLogInBatch).toHaveBeenCalled();
    });

    test('should not create snapshot when maxPid - maxSnapshotPid < maxCommitHeight', async () => {
      const maxPid = 180;
      const maxVersion = 2;
      const maxSnapshotPid = 170;
      const maxCommitHeight = 50; // diff is only 10, less than maxCommitHeight
      const minCommitHeight = 5;

      mockDAL.getMaxPidFromTransactionsLog.mockReturnValue(maxPid);
      mockDAL.getMaxSnapshotVersionFromTransactionsLog.mockReturnValue(maxVersion);
      mockDAL.getMaxSnapshotPidByVersion.mockReturnValue(maxSnapshotPid);

      await expectSaga(saveSnapshot)
        .provide([
          [select(selectMrsMaxCommitHeight), maxCommitHeight],
          [select(selectMrsMinCommitHeight), minCommitHeight],
        ])
        .run();

      expect(mockDAL.saveTransactionsLogInBatch).not.toHaveBeenCalled();
    });
  });

  describe('No snapshot logs scenarios', () => {
    test('should handle when no snapshot logs are created', async () => {
      const maxPid = 100;
      const maxVersion = INIT_SNAPSHOT_VERSION;
      const mockMrsIds = { pid: 101, snapshotPid: 101, backupPid: 50, snapshotVersion: 1 };

      mockDAL.getMaxPidFromTransactionsLog.mockReturnValue(maxPid);
      mockDAL.getMaxSnapshotVersionFromTransactionsLog.mockReturnValue(maxVersion);
      // Mock empty open orders which results in placeholder snapshot
      mockDAL.getMrsOpenOrderList.mockReturnValue([]);
      mockDAL.saveTransactionsLogInBatch.mockReturnValue(true);
      // Update the snapshot values to match what getMrsIdsFromDatabase will return
      mockDAL.getSnapshotIdFromTransactionsLog.mockReturnValue({
        snapshotVersion: mockMrsIds.snapshotVersion,
        snapshotPid: mockMrsIds.snapshotPid,
        backupPid: mockMrsIds.backupPid,
      });

      await expectSaga(saveSnapshot)
        .provide([
          [select(selectMrsMaxCommitHeight), 50],
          [select(selectMrsMinCommitHeight), 10],
          [call(getMrsIdsFromDatabase), mockMrsIds],
          [put(updateMRSSate(mockMrsIds)), undefined],
          [call(updateSlaveStatusSaga), undefined],
        ])
        .put(updateMRSSate(mockMrsIds))
        .call(updateSlaveStatusSaga)
        .run();

      // Even with empty open orders, placeholder snapshot should be created
      expect(mockDAL.saveTransactionsLogInBatch).toHaveBeenCalled();
    });

    test('should handle when snapshot logs are null/undefined', async () => {
      const maxPid = 100;
      const maxVersion = INIT_SNAPSHOT_VERSION;

      mockDAL.getMaxPidFromTransactionsLog.mockReturnValue(maxPid);
      mockDAL.getMaxSnapshotVersionFromTransactionsLog.mockReturnValue(maxVersion);
      // Mock getMrsOpenOrderList to return null to trigger error
      mockDAL.getMrsOpenOrderList.mockReturnValue(null);

      await expectSaga(saveSnapshot)
        .provide([
          [select(selectMrsMaxCommitHeight), 50],
          [select(selectMrsMinCommitHeight), 10],
        ])
        .not.put.actionType('UPDATE_MRS_STATE')
        .run();

      expect(mockDAL.saveTransactionsLogInBatch).not.toHaveBeenCalled();
    });
  });

  describe('Database save failure scenarios', () => {
    test('should throw error when DAL.saveTransactionsLogInBatch fails', async () => {
      const maxPid = 100;
      const maxVersion = INIT_SNAPSHOT_VERSION;

      mockDAL.getMaxPidFromTransactionsLog.mockReturnValue(maxPid);
      mockDAL.getMaxSnapshotVersionFromTransactionsLog.mockReturnValue(maxVersion);
      mockDAL.saveTransactionsLogInBatch.mockReturnValue(false); // save fails

      await expectSaga(saveSnapshot)
        .provide([
          [select(selectMrsMaxCommitHeight), 50],
          [select(selectMrsMinCommitHeight), 10],
        ])
        .not.put.actionType('UPDATE_MRS_STATE')
        .not.call(updateSlaveStatusSaga)
        .run();
    });
  });

  describe('Error handling', () => {
    test('should catch and log errors during execution', async () => {
      const errorMessage = 'Database connection failed';

      mockDAL.getMaxPidFromTransactionsLog.mockImplementation(() => {
        throw new Error(errorMessage);
      });

      await expectSaga(saveSnapshot)
        .provide([
          [select(selectMrsMaxCommitHeight), 50],
          [select(selectMrsMinCommitHeight), 10],
        ])
        .run();
    });

    test('should handle errors without message property', async () => {
      mockDAL.getMaxPidFromTransactionsLog.mockImplementation(() => {
        throw 'String error';
      });

      await expectSaga(saveSnapshot)
        .provide([
          [select(selectMrsMaxCommitHeight), 50],
          [select(selectMrsMinCommitHeight), 10],
        ])
        .run();
    });
  });

  describe('Integration scenarios', () => {
    test('should complete full snapshot creation and update workflow', async () => {
      const maxPid = 500;
      const maxVersion = 3;
      const maxSnapshotPid = 450;
      const mockMrsIds = { pid: 501, snapshotPid: 502, backupPid: 400, snapshotVersion: 4 };

      mockDAL.getMaxPidFromTransactionsLog.mockReturnValue(maxPid);
      mockDAL.getMaxSnapshotVersionFromTransactionsLog.mockReturnValue(maxVersion);
      mockDAL.getMaxSnapshotPidByVersion.mockReturnValue(maxSnapshotPid);
      mockDAL.saveTransactionsLogInBatch.mockReturnValue(true);
      // Update the snapshot values to match what getMrsIdsFromDatabase will return
      mockDAL.getSnapshotIdFromTransactionsLog.mockReturnValue({
        snapshotVersion: mockMrsIds.snapshotVersion,
        snapshotPid: mockMrsIds.snapshotPid,
        backupPid: mockMrsIds.backupPid,
      });

      await expectSaga(saveSnapshot)
        .provide([
          [select(selectMrsMaxCommitHeight), 40],
          [select(selectMrsMinCommitHeight), 10],
          [select(selectServerIp), '********'],
          [call(getMrsIdsFromDatabase), mockMrsIds],
          [put(updateMRSSate(mockMrsIds)), undefined],
          [call(updateSlaveStatusSaga), undefined],
        ])
        .put(updateMRSSate(mockMrsIds))
        .call(updateSlaveStatusSaga)
        .run();

      // Verify the complete flow
      expect(mockDAL.saveTransactionsLogInBatch).toHaveBeenCalled();
      expect(broadcastChosenMessageToClients).toHaveBeenCalledWith({
        pid: mockMrsIds.pid,
        data: null,
        ip: '********',
      });
    });
  });
});
