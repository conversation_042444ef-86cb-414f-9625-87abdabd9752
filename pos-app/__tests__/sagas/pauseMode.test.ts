import { testSaga } from 'redux-saga-test-plan';
import {
  closeDelivery,
  disablePauseMode,
  enablePauseMode,
  pauseBeepDelivery,
  PauseModeEnum,
  queryBeepPauseModeSettings,
  resumeBeepDelivery,
  setPauseMode,
  updateGeneralSettings,
} from '../../ts/actions';
import { closeDeliverySaga, openForDeliverySaga, updatePauseModeSaga } from '../../ts/sagas/pauseMode';
import { selectBusinessName, selectPauseBeepDelivery, selectStoreId } from '../../ts/sagas/selector';
import { selectIsBeepDeliveryEnabled } from './../../ts/sagas/selector';

it('test updatePauseModeSaga', () => {
  testSaga(updatePauseModeSaga)
    .next()
    .select(selectIsBeepDeliveryEnabled)
    .next(true)
    .select(selectStoreId)
    .next('1')
    .select(selectBusinessName)
    .next('feida')
    .put(
      queryBeepPauseModeSettings({
        storeId: '1',
        businessName: 'feida',
      })
    )
    .next()
    .take([queryBeepPauseModeSettings.toString() + '.success', queryBeepPauseModeSettings.toString() + '.failure'])
    .next({
      type: queryBeepPauseModeSettings.toString() + '.success',
      payload: {
        beepPauseModeSettings: { paused: false },
      },
    })
    .put(setPauseMode({ paused: false }))
    .next()
    .select(selectPauseBeepDelivery)
    .next(PauseModeEnum.MINS_30)
    .put(updateGeneralSettings({ pauseBeepDelivery: null }))
    .next()
    .isDone();
});

it('test openForDeliverySaga', () => {
  testSaga(openForDeliverySaga, { payload: {} })
    .next()
    .select(selectStoreId)
    .next('1')
    .select(selectBusinessName)
    .next('feida')
    .put(
      resumeBeepDelivery({
        storeId: '1',
        businessName: 'feida',
      })
    )
    .next()
    .take([resumeBeepDelivery.toString() + '.success', resumeBeepDelivery.toString() + '.failure'])
    .next({
      type: resumeBeepDelivery.toString() + '.success',
      payload: {},
    })
    .put(setPauseMode({ paused: false }))
    .next()
    .put(
      updateGeneralSettings({
        pauseBeepDelivery: null,
      })
    )
    .next()
    .put(disablePauseMode())
    .next()
    .isDone();
});

it('test closeDeliverySaga', () => {
  testSaga(closeDeliverySaga, closeDelivery({ pauseMode: PauseModeEnum.MINS_30 }))
    .next()
    .select(selectStoreId)
    .next('1')
    .select(selectBusinessName)
    .next('feida')
    .put(
      pauseBeepDelivery({
        storeId: '1',
        businessName: 'feida',
        pauseMode: PauseModeEnum.MINS_30,
      })
    )
    .next()
    .take([pauseBeepDelivery.toString() + '.success', pauseBeepDelivery.toString() + '.failure'])
    .next({
      type: pauseBeepDelivery.toString() + '.success',
      payload: { pauseBeep: { paused: true } },
    })
    .put(setPauseMode({ paused: true }))
    .next()
    .put(
      updateGeneralSettings({
        pauseBeepDelivery: PauseModeEnum.MINS_30,
      })
    )
    .next()
    .put(enablePauseMode(PauseModeEnum.MINS_30))
    .next()
    .isDone();
});
