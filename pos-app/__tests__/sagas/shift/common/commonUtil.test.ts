import { generateShiftReportSaga } from '../../../../ts/sagas/shift/common';
import { selectStore } from '../../../../ts/sagas/selector';
import { testSaga } from 'redux-saga-test-plan';
import * as Immutable from 'immutable';
import { ShiftReport } from '../../../../ts/sagas/shift/common/shiftReport';

jest.mock('../../../../ts/dal', () => {
  return {
    getDiscountedTransactionFrom: jest.fn().mockImplementation((openTime, closeTime) => {
      return {};
    }),
    getLoyaltyTransactionFrom: jest.fn().mockImplementation(id => {
      return {};
    }),
    getLoyaltyDiscountTransactionFrom: jest.fn().mockImplementation(id => {
      return {};
    }),
    getDepositGroupedByPaymentSince: jest.fn().mockImplementation((openTime, closeTime) => {
      return { amount: { totalAmount: 222 }, paymentMethodId: { totalAmount: 111 } };
    }),
    getServiceChargeSince: jest.fn().mockImplementation((openTime, closeTime) => {
      return {
        Sale: 'Sale',
        Return: 'Return',
      };
    }),
    getTransactionIdsFrom: jest.fn().mockImplementation((openTime, closeTime) => {
      return [{ transactionId: 'fb67d79e-ce2e-4d2b-8753-68e1c63ed96c', transactionType: 'Sale', isCancelled: false }];
    }),
    getReceiptNumbersFrom: jest.fn().mockImplementation((openTime, closeTime) => {
      return [{ receiptNumber: 'b2aae94d-6ddc-4b7a-bd10-1199890fc700', transactionType: 'Sale', isCancelled: false }];
    }),
    getRoundedAmountSince: jest.fn().mockImplementation((openTime, closeTime) => {
      return {
        Sale: 'Sale',
        Return: 'Return',
      };
    }),
    getTrxAggregatedByType: jest.fn().mockImplementation((openTime, closeTime) => {
      return {
        Sale: 'Sale',
        Return: 'Return',
      };
    }),
    getSalesGroupedByPaymentSince: jest.fn().mockImplementation((openTime, closeTime) => {
      return {
        amount: { totalAmount: 666 },
        paymentMethodId: { totalAmount: 222 },
      };
    }),
    getActualSalesGroupedByPaymentSince: jest.fn().mockImplementation((openTime, closeTime) => {
      return {
        amount: { totalAmount: 666 },
        paymentMethodId: { totalAmount: 222 },
      };
    }),
    getBeepQRCashSales: jest.fn().mockImplementation((openTime, closeTime) => {
      return {
        amount: { totalAmount: 333 },
        paymentMethodId: { totalAmount: 111 },
      };
    }),
    getSalesGroupedByLoyaltySince: jest.fn().mockImplementation((openTime, closeTime) => {
      return { totalAmount: 0 };
    }),
    getCanncelTransactionSummary: jest.fn().mockImplementation((openTime, closeTime) => {
      return { canceld: true };
    }),
    getNotPreOrderTransactionsCount: jest.fn().mockImplementation((openTime, closeTime) => {
      return 1;
    }),
    getTransactionsInThisShift: jest.fn().mockImplementation((openTime, closeTime) => {
      return [];
    }),
  };
});

describe('test commonUtil', () => {
  it('test func generateShiftReportSaga with null or undefined', async () => {
    expect(generateShiftReportSaga().next().value).toBe(null);
    expect(generateShiftReportSaga(null).next().value).toBe(null);
  });

  it('test func generateShiftReportSaga return object', async () => {
    const store = {
      _id: '5cad7145f76103d10ca28a8c',
      email: '<EMAIL>',
      name: '门店A',
      cashierAccesses: {
        deleteItem: '0',
        deleteItemOfNew: '0',
        deleteItemOfSaved: '0',
        refund: '0',
        cancel: '1',
        discount: '0',
        openCloseShift: '1',
      },
      country: 'MY',
      currency: 'MYR',
      defaultTaxCode: '5c4fd9e84e9e38fee39cd81e',
      tax: 0,
      enableLoyalty: false,
      autoOrderId: false,
      logo: 'logo',
      taxCodes: [
        {
          _id: '5c4fd9e84e9e38fee39cd81e',
          name: 'SR',
          rate: 0,
        },
      ],
      taxNameOnReceipt: '',
      showBarcode: true,
      showStoreName: true,
      showCustomerInfo: [],
      showNotes: true,
      poweredBy: true,
      roundingTo: 0,
      enableServiceCharge: false,
      paymentOptions: [],
      disabledDefaultPayments: [],
      roundAllTransactions: false,
      isVATRegistered: false,
      birAccrInfo:
        '****************************************\r\n\r\nJIMAC INC\r\nCordillera Quezon City TC Plaza B\r\nPenthouse, 40 Quezon Ave\r\nVAT REG TIN #: ***********-000\r\nDate Issued:  05/29/2017\r\n',
      birAccrNo: '039-0027-05362-201610-0606',
      birAccredited: false,
      enableMultipleIpads: false,
      separateKitchenItems: false,
      orderSummaryPrinter: '',
      allowEditProductsOnPOS: false,
      enablePax: false,
      fulfillmentOptions: [],
      tableLayoutEnabled: false,
      defaultTableLayoutSection: '',
    };
    const shift = {
      payins: [{ amount: 100 }],
      payouts: [{ amount: 100 }],
      shiftId: 'shiftId',
      openTime: new Date(new Date().getTime() - 24 * 60 * 60 * 1000),
      openBy: 'openBy',
      openingAmount: 'openingAmount',
      closeTime: new Date(),
      closeBy: 'closeBy',
      closingAmount: 'closingAmount',
    };

    const immutableStore = Immutable.fromJS(store);
    return testSaga(generateShiftReportSaga, shift).next().select(selectStore).next(new ShiftReport(immutableStore, shift));
  });
});
