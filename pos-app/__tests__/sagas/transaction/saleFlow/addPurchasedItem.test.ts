import * as Immutable from 'immutable';
import { runSaga } from 'redux-saga';
import DAL from '../../../../ts/dal';
import { addPurchasedItemSaga, getChildProduct, getMergeablePurchasedItem } from '../../../../ts/sagas/transaction/saleFlow';

const state = {
  Storage: {
    quickLayout: [
      {
        name: 'New Category',
        order: 0,
        categoryId: 'b2aae94d-6ddc-4b7a-bd10-1199890fc700',
        items: [
          {
            productId: '58b7c5bafe2f5a160d1469a4',
            row: 0,
            column: 0,
            backgroundColor: '',
          },
          {
            productId: '5c4fc99fe9020066fb1d2207',
            row: 1,
            column: 0,
            backgroundColor: '',
          },
          {
            productId: '5c4fc9c3e9020066fb1d2209',
            row: 0,
            column: 1,
            backgroundColor: '',
          },
          {
            productId: '5c4fc9d1e9020066fb1d220b',
            row: 0,
            column: 2,
            backgroundColor: '',
          },
        ],
      },
      {
        name: 'New Category',
        order: 1,
        categoryId: 'fb67d79e-ce2e-4d2b-8753-68e1c63ed96c',
        items: [
          {
            productId: '58b7b7bb32d982110ab436c3',
            row: 0,
            column: 0,
            backgroundColor: '',
          },
        ],
      },
    ],
    storeInfo: {
      store: {
        fulfillmentOptions: [],
        cfdDisplay: {
          cfdDisplayId: '5c5022eee9020066fb1d2225',
          type: 'images',
          text: '',
          name: 'Android',
          ordering: 1,
        },
        isVATRegistered: false,
        orderSummaryPrinter: '',
        enableLoyalty: false,
        showBarcode: true,
        taxNameOnReceipt: '',
        enablePax: false,
        ptuValideUntil: '',
        showNotes: true,
        birAccredited: false,
        enableServiceCharge: false,
        roundAllTransactions: false,
        showCustomerInfo: [],
        tableLayoutEnabled: false,
        ptuDateIssued: '',
        birAccrInfo:
          '****************************************\r\n\r\nJIMAC INC\r\nCordillera Quezon City TC Plaza B\r\nPenthouse, 40 Quezon Ave\r\nVAT REG TIN #: ***********-000\r\nDate Issued:  05/29/2017\r\n',
        name: '门店A',
        tax: 0,
        defaultTableLayoutSection: '',
        showStoreName: true,
        roundingTo: 0,
        currency: 'MYR',
        ptu: '',
        disabledDefaultPayments: [],
        minNo: '',
        cashierAccesses: {
          deleteItem: '0',
          deleteItemOfNew: '0',
          deleteItemOfSaved: '0',
          refund: '0',
          cancel: '1',
          discount: '0',
          openCloseShift: '1',
        },
        serialNo: '',
        enableMultipleIpads: false,
        country: 'MY',
        defaultTaxCode: '5c4ec3a0e3d231664262c58c',
        paymentOptions: [],
        autoOrderId: false,
        _id: '5c4ec3a0e3d231664262c588',
        taxCodes: [
          {
            _id: '5c4ec3a0e3d231664262c58c',
            name: 'SR',
            rate: 0,
          },
        ],
        poweredBy: true,
        email: '<EMAIL>',
        birAccrNo: '039-0027-05362-201610-0606',
        allowEditProductsOnPOS: false,
        separateKitchenItems: false,
      },
      sequentialReceiptNumber: false,
      subscriptionPlan: 2,
      name: 'qsl',
      subscriptionStatus: 'Active',
      currency: 'MYR',
      allStores: [
        {
          _id: '5c4ec3a0e3d231664262c588',
          name: '门店A',
        },
      ],
      registerObjectId: '5c4ec3a0e3d231664262c589',
      registerId: 1,
      apiToken: 'de0331802ab111e98aa68bac170a2358',
    },
    syncInfo: {
      employeeInfoSync: true,
      lastEmployeeSyncTime: '2019-02-07T08:25:10.796Z',
      priceBookInfoSync: true,
      lastPriceBookSyncTime: '2019-02-07T08:25:10.856Z',
      productInfoSync: true,
      lastProductSyncTime: '2019-02-07T08:25:10.826Z',
      onlineSyncTime: '2019-02-12T06:23:28.785Z',
    },
  },
};

const transactionSession = {
  TransactionSession: {
    items: [
      {
        discount: 0,
        display: {
          total: 9,
          subtotal: 9,
          tax: 0,
        },
        selectedOptions: [],
        subTotal: 9,
        total: 9,
        unitPrice: 9,
        taxInclusiveSubtotal: 9,
        productId: '5c4fc9d1e9020066fb1d220b',
        quantity: 1,
        taxRate: 0,
        tax: 0,
        taxExclusiveSubtotal: 9,
        taxCode: '5c4ec3a0e3d231664262c58c',
        title: 'Variable Price',
        adhocDiscount: 0,
      },
    ],
    discount: 0,
    display: {
      subtotal: 9,
      discount: 0,
      serviceCharge: 0,
      tax: 0,
      total: 9,
    },
    serviceChargeTax: 0,
    subtotal: 9,
    total: 9,
    serviceCharge: 0,
    tax: 0,
    appVersion: '********',
    transactionType: 'Sale',
    transactionId: 'e2823030-4354-4b2f-bf40-98d00edf7510',
  },
};

jest.mock('../../../../ts/dal', () => {
  return {
    getProductById: jest.fn().mockImplementation(productId => {
      return {
        productId: '5c4fc9d1e9020066fb1d220b',
        unitPrice: 7,
        selectedOptions: [],
        quantity: 1,
        taxRate: 0,
        title: 'Variable Price',
        total: 7,
        taxCode: '5c4ec3a0e3d231664262c58c',
        taxExclusiveSubtotal: 7,
        taxInclusiveSubtotal: 7,
        adhocDiscount: 0,
        tax: 0,
        discount: 0,
        subTotal: 7,
        kitchenPrinter: 'EDKPrinter',
      };
    }),
    getTrancationById: jest.fn().mockImplementation(transactionId => {
      return {
        items: [
          {
            discount: 0,
            display: {
              total: 9,
              subtotal: 9,
              tax: 0,
            },
            selectedOptions: [],
            subTotal: 9,
            total: 9,
            unitPrice: 9,
            taxInclusiveSubtotal: 9,
            productId: '5c4fc9d1e9020066fb1d220b',
            quantity: 1,
            taxRate: 0,
            tax: 0,
            taxExclusiveSubtotal: 9,
            taxCode: '5c4ec3a0e3d231664262c58c',
            title: 'Variable Price',
            adhocDiscount: 0,
          },
        ],
        discount: 0,
        display: {
          subtotal: 9,
          discount: 0,
          serviceCharge: 0,
          tax: 0,
          total: 9,
        },
        serviceChargeTax: 0,
        subtotal: 9,
        total: 9,
        serviceCharge: 0,
        tax: 0,
        appVersion: '********',
        transactionType: 'Sale',
        transactionId: 'e2823030-4354-4b2f-bf40-98d00edf7510',
      };
    }),
    saveTransaction: jest.fn(),
    updateTransaction: jest.fn(),
    getProductByParentId: jest.fn().mockImplementation(id => {
      return [
        {
          barcode: null,
          category: null,
          inventoryType: null,
          isChild: true,
          isSerialized: false,
          kitchenPrinter: null,
          modifiedTime: '2019-02-19T08:31:30.143Z',
          parentProductId: '5c6b98d1621a3967cb021c18',
          priceType: 'fixed',
          productId: '5c6b98d1621a3967cb021c1d',
          productType: 'Configurable',
          skuId: null,
          tags: {},
          taxCode: '',
          title: 'TestVariableProducts-track(small)',
          trackInventory: true,
          unit: null,
          unitPrice: 22,
          variationsJson:
            '[{"_id":"5c6bbea8621a3967cb021c37","name":"colors","variationType":"MultipleChoice","order":0,"allowMultiQty":true,"optionValues":[{"_id":"5c6bbea8621a3967cb021c3a","value":"red","order":0,"priceDiff":4},{"_id":"5c6bbea8621a3967cb021c39","value":"blue","order":1,"priceDiff":5},{"_id":"5c6bbea8621a3967cb021c38","value":"orange","order":2,"priceDiff":6}]}]',
          variationValuesJson: '[{"variationId":"5c6b98d1621a3967cb021c19","value":"small","_id":"5c6b98d1621a3967cb021c1e"}]',
          priceBooks: {},
        },
      ];
    }),
    deleteTransactionById: jest.fn(),
    getPromotions: jest.fn(),
  };
});

describe('test addPurchasedItemSaga', () => {
  it('add purchasedItem succeed', async () => {
    const dispatched = [];
    const addPurchasedItemAction = {
      payload: {
        productId: '5c4fc9d1e9020066fb1d220b',
        quantity: 1,
        variablePrice: 7,
      },
    };
    const result = await runSaga(
      {
        dispatch: action => {
          dispatched.push(action);
        },
        getState: () => Immutable.fromJS({ ...state }),
      },
      addPurchasedItemSaga,
      addPurchasedItemAction
    ).done;

    expect(DAL.getProductById).toHaveBeenCalledWith('5c4fc9d1e9020066fb1d220b');
    expect(dispatched.length).toEqual(4);
    expect(dispatched[1].payload.items).toEqual([
      {
        discount: undefined,
        id: '1:5c4fc9d1e9020066fb1d220b',
        isAmusementTax: false,
        isBasicNecessitiesPH: undefined,
        isDiscountEnable: undefined,
        isTakeaway: false,
        kitchenPrinter: 'EDKPrinter',
        kitchenStation: 'EDKPrinter',
        itemChannel: 1,
        isVatExempted: false,
        notes: undefined,
        maxQuantWithAppliedPriceBook: undefined,
        minQuantWithAppliedPriceBook: undefined,
        isSoloParentDiscountApplicable: undefined,
        options: undefined,
        priceType: undefined,
        product: {
          _id: '5c4fc9d1e9020066fb1d220b',
          category: undefined,
          tags: [],
        },
        productId: '5c4fc9d1e9020066fb1d220b',
        quantity: 1,
        selectedOptions: [],
        taxCode: '5c4ec3a0e3d231664262c58c',
        taxRate: 0,
        targetCustomerType: undefined,
        title: 'Variable Price',
        total: 0,
        trackInventory: undefined,
        unitPrice: 7,
      },
    ]);
  });

  it('test getChildProduct with parent product', async () => {
    const product = {};
    const options = {};
    expect(getChildProduct(product, options)).toEqual({ resultOptions: {}, resultProduct: {} });
  });

  it('test getChildProduct with child product', async () => {
    const product = { productId: '5c6b98d1621a3967cb021c18', trackInventory: true, productType: 'Configurable', isChild: false, parentProductId: null };
    const options = [
      {
        variationId: '5c6b98d1621a3967cb021c19',
        optionId: '5c6b98d1621a3967cb021c1c',
        optionValue: 'small',
        priceDiff: 2,
        quantity: 1,
      },
      {
        variationId: '5c6bbea8621a3967cb021c37',
        optionId: '5c6bbea8621a3967cb021c3a',
        optionValue: 'red',
        priceDiff: 4,
        quantity: 1,
      },
    ];
    expect(getChildProduct(product, options)).toEqual({
      resultOptions: [{ optionId: '5c6bbea8621a3967cb021c3a', optionValue: 'red', priceDiff: 4, quantity: 1, variationId: '5c6bbea8621a3967cb021c37' }],
      resultProduct: {
        barcode: null,
        category: null,
        inventoryType: null,
        isChild: true,
        isSerialized: false,
        kitchenPrinter: null,
        modifiedTime: '2019-02-19T08:31:30.143Z',
        parentProductId: '5c6b98d1621a3967cb021c18',
        priceBooks: {},
        priceType: 'fixed',
        productId: '5c6b98d1621a3967cb021c1d',
        productType: 'Configurable',
        skuId: null,
        tags: {},
        taxCode: '',
        title: 'TestVariableProducts-track(small)',
        trackInventory: true,
        unit: null,
        unitPrice: 22,
        variationValuesJson: '[{"variationId":"5c6b98d1621a3967cb021c19","value":"small","_id":"5c6b98d1621a3967cb021c1e"}]',
        variationsJson:
          '[{"_id":"5c6bbea8621a3967cb021c37","name":"colors","variationType":"MultipleChoice","order":0,"allowMultiQty":true,"optionValues":[{"_id":"5c6bbea8621a3967cb021c3a","value":"red","order":0,"priceDiff":4},{"_id":"5c6bbea8621a3967cb021c39","value":"blue","order":1,"priceDiff":5},{"_id":"5c6bbea8621a3967cb021c38","value":"orange","order":2,"priceDiff":6}]}]',
      },
    });
  });

  it('test getMergeablePurchasedItem return merged item', async () => {
    const transactionSession = {
      transactionId: 'b966f41e-0965-41e6-85ea-2c33172642ca',
      appVersion: '********',
      transactionType: 'Sale',
      items: [
        {
          productId: '5c6b98d1621a3967cb021c1d',
          unitPrice: 22,
          selectedOptions: [],
          options: [],
          quantity: 1,
          taxRate: 0,
          title: 'TestVariableProducts-track(small)',
          total: 22,
          taxCode: '5c4ec3a0e3d231664262c58c',
          notes: '',
          taxExclusiveSubtotal: 22,
          taxInclusiveSubtotal: 22,
          adhocDiscount: 0,
          tax: 0,
          discount: 0,
          subTotal: 22,
          display: {
            total: 22,
            subtotal: 22,
            tax: 0,
          },
        },
      ],
      subtotal: 22,
      discount: 0,
      tax: 0,
      total: 22,
      serviceCharge: 0,
      serviceChargeTax: 0,
      display: {
        subtotal: 22,
        discount: 0,
        serviceCharge: 0,
        tax: 0,
        total: 22,
      },
    };
    const mergedResult = getMergeablePurchasedItem({
      transactionSession,
      product: {
        barcode: null,
        category: null,
        inventoryType: null,
        isChild: true,
        isSerialized: false,
        kitchenPrinter: null,
        modifiedTime: '2019-02-19T08:31:30.143Z',
        parentProductId: '5c6b98d1621a3967cb021c18',
        priceBooks: {},
        priceType: 'fixed',
        productId: '5c6b98d1621a3967cb021c1d',
        productType: 'Configurable',
        skuId: null,
        tags: {},
        taxCode: '',
        title: 'TestVariableProducts-track(small)',
        trackInventory: true,
        unit: null,
        unitPrice: 22,
        variationValuesJson: '[{"variationId":"5c6b98d1621a3967cb021c19","value":"small","_id":"5c6b98d1621a3967cb021c1e"}]',
        variationsJson:
          '[{"_id":"5c6bbea8621a3967cb021c37","name":"colors","variationType":"MultipleChoice","order":0,"allowMultiQty":true,"optionValues":[{"_id":"5c6bbea8621a3967cb021c3a","value":"red","order":0,"priceDiff":4},{"_id":"5c6bbea8621a3967cb021c39","value":"blue","order":1,"priceDiff":5},{"_id":"5c6bbea8621a3967cb021c38","value":"orange","order":2,"priceDiff":6}]}]',
      },
      options: [],
      quantity: 3,
      immutableStore: Immutable.fromJS({}),
      subscriptionPlan: 2,
      enableGroupItem: true,
    });
    expect(mergedResult.purchasedItem.quantity).toBe(4);
  });
});
