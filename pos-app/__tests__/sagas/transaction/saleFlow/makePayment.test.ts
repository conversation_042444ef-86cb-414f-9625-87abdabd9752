import * as Immutable from 'immutable';
import { runSaga } from 'redux-saga';
import { testSaga } from 'redux-saga-test-plan';
import { DefaultPaymentOptionType } from '../../../../ts/config/paymentOption';
import { makePaymentSaga, makeSalePaymentSaga, processOnlineOrderPayment } from '../../../../ts/sagas/transaction/saleFlow';
import { calculatePayAmount } from '../../../../ts/utils/checkout';

const state = {
  CurrentEmployee: {
    employeeId: '5e78c5112dacfe000698e7d2',
  },
  Storage: {
    quickLayout: [
      {
        name: 'New Category',
        order: 0,
        categoryId: 'b2aae94d-6ddc-4b7a-bd10-1199890fc700',
        items: [
          {
            productId: '58b7c5bafe2f5a160d1469a4',
            row: 0,
            column: 0,
            backgroundColor: '',
          },
          {
            productId: '5c4fc99fe9020066fb1d2207',
            row: 1,
            column: 0,
            backgroundColor: '',
          },
          {
            productId: '5c4fc9c3e9020066fb1d2209',
            row: 0,
            column: 1,
            backgroundColor: '',
          },
          {
            productId: '5c4fc9d1e9020066fb1d220b',
            row: 0,
            column: 2,
            backgroundColor: '',
          },
        ],
      },
      {
        name: 'New Category',
        order: 1,
        categoryId: 'fb67d79e-ce2e-4d2b-8753-68e1c63ed96c',
        items: [
          {
            productId: '58b7b7bb32d982110ab436c3',
            row: 0,
            column: 0,
            backgroundColor: '',
          },
        ],
      },
    ],
    storeInfo: {
      store: {
        fulfillmentOptions: [],
        cfdDisplay: {
          cfdDisplayId: '5c5022eee9020066fb1d2225',
          type: 'images',
          text: '',
          name: 'Android',
          ordering: 1,
        },
        isVATRegistered: false,
        orderSummaryPrinter: '',
        enableLoyalty: false,
        showBarcode: true,
        taxNameOnReceipt: '',
        enablePax: false,
        ptuValideUntil: '',
        showNotes: true,
        birAccredited: false,
        enableServiceCharge: false,
        roundAllTransactions: false,
        showCustomerInfo: [],
        tableLayoutEnabled: false,
        ptuDateIssued: '',
        birAccrInfo:
          '****************************************\r\n\r\nJIMAC INC\r\nCordillera Quezon City TC Plaza B\r\nPenthouse, 40 Quezon Ave\r\nVAT REG TIN #: ***********-000\r\nDate Issued:  05/29/2017\r\n',
        name: '门店A',
        tax: 0,
        defaultTableLayoutSection: '',
        showStoreName: true,
        roundingTo: 0,
        currency: 'MYR',
        ptu: '',
        disabledDefaultPayments: [],
        minNo: '',
        cashierAccesses: {
          deleteItem: '0',
          deleteItemOfNew: '0',
          deleteItemOfSaved: '0',
          refund: '0',
          cancel: '1',
          discount: '0',
          openCloseShift: '1',
        },
        serialNo: '',
        enableMultipleIpads: false,
        country: 'MY',
        defaultTaxCode: '5c4ec3a0e3d231664262c58c',
        paymentOptions: [],
        autoOrderId: false,
        _id: '5c4ec3a0e3d231664262c588',
        taxCodes: [
          {
            _id: '5c4ec3a0e3d231664262c58c',
            name: 'SR',
            rate: 0,
          },
        ],
        poweredBy: true,
        email: '<EMAIL>',
        birAccrNo: '039-0027-05362-201610-0606',
        allowEditProductsOnPOS: false,
        separateKitchenItems: false,
      },
      sequentialReceiptNumber: false,
      subscriptionPlan: 2,
      name: 'qsl',
      subscriptionStatus: 'Active',
      currency: 'MYR',
      allStores: [
        {
          _id: '5c4ec3a0e3d231664262c588',
          name: '门店A',
        },
      ],
      registerObjectId: '5c4ec3a0e3d231664262c589',
      registerId: 1,
      apiToken: 'de0331802ab111e98aa68bac170a2358',
    },
    syncInfo: {
      employeeInfoSync: true,
      lastEmployeeSyncTime: '2019-02-07T08:25:10.796Z',
      priceBookInfoSync: true,
      lastPriceBookSyncTime: '2019-02-07T08:25:10.856Z',
      productInfoSync: true,
      lastProductSyncTime: '2019-02-07T08:25:10.826Z',
      onlineSyncTime: '2019-02-12T06:23:28.785Z',
    },
  },
};

const transactionSession = {
  TransactionSession: {
    items: [
      {
        discount: 0,
        display: {
          total: 9,
          subtotal: 9,
          tax: 0,
        },
        selectedOptions: [],
        subTotal: 9,
        total: 9,
        unitPrice: 9,
        taxInclusiveSubtotal: 9,
        productId: '5c4fc9d1e9020066fb1d220b',
        quantity: 1,
        taxRate: 0,
        tax: 0,
        taxExclusiveSubtotal: 9,
        taxCode: '5c4ec3a0e3d231664262c58c',
        title: 'Variable Price',
        adhocDiscount: 0,
      },
    ],
    discount: 0,
    display: {
      subtotal: 9,
      discount: 0,
      serviceCharge: 0,
      tax: 0,
      total: 9,
    },
    serviceChargeTax: 0,
    subtotal: 9,
    total: 9,
    serviceCharge: 0,
    tax: 0,
    appVersion: '********',
    transactionType: 'Sale',
    transactionId: 'e2823030-4354-4b2f-bf40-98d00edf7510',
    serialNumbers: {},
    promotions: [],
  },
};

jest.mock('../../../../ts/dal', () => {
  return {
    getProductById: jest.fn().mockImplementation(productId => {
      return {
        productId: '5c4fc9d1e9020066fb1d220b',
        unitPrice: 7,
        selectedOptions: [],
        quantity: 1,
        taxRate: 0,
        title: 'Variable Price',
        total: 7,
        taxCode: '5c4ec3a0e3d231664262c58c',
        taxExclusiveSubtotal: 7,
        taxInclusiveSubtotal: 7,
        adhocDiscount: 0,
        tax: 0,
        discount: 0,
        subTotal: 7,
        kitchenPrinter: 'EDKPrinter',
      };
    }),
    getTrancationById: jest.fn().mockImplementation(transactionId => {
      return {
        items: [
          {
            discount: 0,
            display: {
              total: 9,
              subtotal: 9,
              tax: 0,
            },
            selectedOptions: [],
            subTotal: 9,
            total: 9,
            unitPrice: 9,
            taxInclusiveSubtotal: 9,
            productId: '5c4fc9d1e9020066fb1d220b',
            quantity: 1,
            taxRate: 0,
            tax: 0,
            taxExclusiveSubtotal: 9,
            taxCode: '5c4ec3a0e3d231664262c58c',
            title: 'Variable Price',
            adhocDiscount: 0,
          },
        ],
        discount: 0,
        display: {
          subtotal: 9,
          discount: 0,
          serviceCharge: 0,
          tax: 0,
          total: 9,
        },
        serviceChargeTax: 0,
        subtotal: 9,
        total: 9,
        serviceCharge: 0,
        tax: 0,
        appVersion: '********',
        transactionType: 'Sale',
        transactionId: 'e2823030-4354-4b2f-bf40-98d00edf7510',
      };
    }),
    saveTransaction: jest.fn(),
    updateTransaction: jest.fn(),
    getProductByParentId: jest.fn().mockImplementation(id => {
      return [
        {
          barcode: null,
          category: null,
          inventoryType: null,
          isChild: true,
          isSerialized: false,
          kitchenPrinter: null,
          modifiedTime: '2019-02-19T08:31:30.143Z',
          parentProductId: '5c6b98d1621a3967cb021c18',
          priceType: 'fixed',
          productId: '5c6b98d1621a3967cb021c1d',
          productType: 'Configurable',
          skuId: null,
          tags: {},
          taxCode: '',
          title: 'TestVariableProducts-track(small)',
          trackInventory: true,
          unit: null,
          unitPrice: 22,
          variationsJson:
            '[{"_id":"5c6bbea8621a3967cb021c37","name":"colors","variationType":"MultipleChoice","order":0,"allowMultiQty":true,"optionValues":[{"_id":"5c6bbea8621a3967cb021c3a","value":"red","order":0,"priceDiff":4},{"_id":"5c6bbea8621a3967cb021c39","value":"blue","order":1,"priceDiff":5},{"_id":"5c6bbea8621a3967cb021c38","value":"orange","order":2,"priceDiff":6}]}]',
          variationValuesJson: '[{"variationId":"5c6b98d1621a3967cb021c19","value":"small","_id":"5c6b98d1621a3967cb021c1e"}]',
          priceBooks: {},
        },
      ];
    }),
    deleteTransactionById: jest.fn(),
    getPromotions: jest.fn(),
  };
});

describe('test makePaymentSaga', () => {
  it('test makePaymentSaga', async () => {
    const dispatched: any[] = [];
    const onCompleteCallback = jest.fn();
    const makePaymentAction = {
      payload: {
        paymentId: 0,
        dueAmount: 9,
        tenderedAmount: 9,
        onComplete: { callback: onCompleteCallback },
      },
    };
    const result = await runSaga(
      {
        dispatch: (action: any) => {
          dispatched.push(action);
        },
        getState: () => Immutable.fromJS({ ...state, TransactionSession: { ...transactionSession.TransactionSession, receiptNumber: '0002407041624485' } }),
      },
      makePaymentSaga,
      makePaymentAction
    ).toPromise();

    expect(onCompleteCallback).toHaveBeenCalledTimes(1);
  });
});

describe('test calculatePayAmount', () => {
  it('test calculatePayAmount isPreOrder', async () => {
    const transactionSession = {
      transactionId: 'b966f41e-0965-41e6-85ea-2c33172642ca',
      appVersion: '********',
      transactionType: 'PreOrder',
      items: [
        {
          productId: '5c6b98d1621a3967cb021c1d',
          unitPrice: 22,
          selectedOptions: [],
          options: [],
          quantity: 1,
          taxRate: 0,
          title: 'TestVariableProducts-track(small)',
          total: 22,
          taxCode: '5c4ec3a0e3d231664262c58c',
          notes: '',
          taxExclusiveSubtotal: 22,
          taxInclusiveSubtotal: 22,
          adhocDiscount: 0,
          tax: 0,
          discount: 0,
          subTotal: 22,
          display: {
            total: 22,
            subtotal: 22,
            tax: 0,
          },
        },
      ],
      subtotal: 22,
      discount: 0,
      depositAmount: 10,
      tax: 0,
      total: 22,
      serviceCharge: 0,
      serviceChargeTax: 0,
      display: {
        subtotal: 22,
        discount: 0,
        serviceCharge: 0,
        tax: 0,
        total: 22,
      },
    };

    const { dueAmount, unRoundedAmount } = calculatePayAmount(transactionSession, DefaultPaymentOptionType.Cash, 0, 0.05, true);
    expect(dueAmount).toBe(10);
    expect(unRoundedAmount).toBe(10);
  });

  it('test calculatePayAmount split with rounding', async () => {
    const transactionSession = {
      transactionId: 'b966f41e-0965-41e6-85ea-2c33172642ca',
      appVersion: '********',
      transactionType: 'Sale',
      items: [
        {
          productId: '5c6b98d1621a3967cb021c1d',
          unitPrice: 22,
          selectedOptions: [],
          options: [],
          quantity: 1,
          taxRate: 0,
          title: 'TestVariableProducts-track(small)',
          total: 22,
          taxCode: '5c4ec3a0e3d231664262c58c',
          notes: '',
          taxExclusiveSubtotal: 22,
          taxInclusiveSubtotal: 22,
          adhocDiscount: 0,
          tax: 0,
          discount: 0,
          subTotal: 22,
          display: {
            total: 22,
            subtotal: 22,
            tax: 0,
          },
        },
      ],
      subtotal: 22,
      discount: 0,
      tax: 0,
      total: 22,
      serviceCharge: 0,
      serviceChargeTax: 0,
      display: {
        subtotal: 22,
        discount: 0,
        serviceCharge: 0,
        tax: 0,
        total: 22,
      },
    };

    const { dueAmount, unRoundedAmount } = calculatePayAmount(transactionSession, DefaultPaymentOptionType.Cash, 10.01, 0.05, true);
    expect(dueAmount).toBe(10);
    expect(unRoundedAmount).toBe(10.01);
  });

  it('test calculatePayAmount split without rounding', async () => {
    const transactionSession = {
      transactionId: 'b966f41e-0965-41e6-85ea-2c33172642ca',
      appVersion: '********',
      transactionType: 'Sale',
      items: [
        {
          productId: '5c6b98d1621a3967cb021c1d',
          unitPrice: 22,
          selectedOptions: [],
          options: [],
          quantity: 1,
          taxRate: 0,
          title: 'TestVariableProducts-track(small)',
          total: 22,
          taxCode: '5c4ec3a0e3d231664262c58c',
          notes: '',
          taxExclusiveSubtotal: 22,
          taxInclusiveSubtotal: 22,
          adhocDiscount: 0,
          tax: 0,
          discount: 0,
          subTotal: 22,
          display: {
            total: 22,
            subtotal: 22,
            tax: 0,
          },
        },
      ],
      subtotal: 22,
      discount: 0,
      tax: 0,
      total: 22,
      serviceCharge: 0,
      serviceChargeTax: 0,
      display: {
        subtotal: 22,
        discount: 0,
        serviceCharge: 0,
        tax: 0,
        total: 22,
      },
    };

    const { dueAmount, unRoundedAmount } = calculatePayAmount(transactionSession, DefaultPaymentOptionType.CreditCard, 10.01, 0.05, false);
    expect(dueAmount).toBe(10.01);
    expect(unRoundedAmount).toBe(10.01);
  });

  it('test calculatePayAmount normal with rounding', async () => {
    const transactionSession = {
      transactionId: 'b966f41e-0965-41e6-85ea-2c33172642ca',
      appVersion: '********',
      transactionType: 'Sale',
      items: [
        {
          productId: '5c6b98d1621a3967cb021c1d',
          unitPrice: 22,
          selectedOptions: [],
          options: [],
          quantity: 1,
          taxRate: 0,
          title: 'TestVariableProducts-track(small)',
          total: 22,
          taxCode: '5c4ec3a0e3d231664262c58c',
          notes: '',
          taxExclusiveSubtotal: 22,
          taxInclusiveSubtotal: 22,
          adhocDiscount: 0,
          tax: 0,
          discount: 0,
          subTotal: 22,
          display: {
            total: 22,
            subtotal: 22,
            tax: 0,
          },
        },
      ],
      subtotal: 22,
      discount: 0,
      tax: 0,
      total: 22,
      totalPaid: 10,
      depositAmount: 9.94,
      serviceCharge: 0,
      serviceChargeTax: 0,
      display: {
        subtotal: 22,
        discount: 0,
        serviceCharge: 0,
        tax: 0,
        total: 22,
      },
    };

    const { dueAmount, unRoundedAmount } = calculatePayAmount(transactionSession, DefaultPaymentOptionType.Cash, undefined, 0.1, false);
    expect(dueAmount).toBe(12);
    expect(unRoundedAmount).toBe(12);
  });

  it('test calculatePayAmount normal without rounding', async () => {
    const transactionSession = {
      transactionId: 'b966f41e-0965-41e6-85ea-2c33172642ca',
      appVersion: '********',
      transactionType: 'Sale',
      items: [
        {
          productId: '5c6b98d1621a3967cb021c1d',
          unitPrice: 22,
          selectedOptions: [],
          options: [],
          quantity: 1,
          taxRate: 0,
          title: 'TestVariableProducts-track(small)',
          total: 22,
          taxCode: '5c4ec3a0e3d231664262c58c',
          notes: '',
          taxExclusiveSubtotal: 22,
          taxInclusiveSubtotal: 22,
          adhocDiscount: 0,
          tax: 0,
          discount: 0,
          subTotal: 22,
          display: {
            total: 22,
            subtotal: 22,
            tax: 0,
          },
        },
      ],
      subtotal: 22,
      discount: 0,
      tax: 0,
      total: 22,
      totalPaid: 10,
      depositAmount: 9.94,
      serviceCharge: 0,
      serviceChargeTax: 0,
      display: {
        subtotal: 22,
        discount: 0,
        serviceCharge: 0,
        tax: 0,
        total: 22,
      },
    };

    const { dueAmount, unRoundedAmount } = calculatePayAmount(transactionSession, DefaultPaymentOptionType.CreditCard, undefined, 0.1, false);
    expect(dueAmount).toBe(12);
    expect(unRoundedAmount).toBe(12);
  });
});

describe('test makeSalePaymentSaga', () => {
  it('test makeSalePaymentSaga', async () => {
    const dispatched: any[] = [];
    const onCompleteCallback = jest.fn();
    const makePaymentAction = {
      payload: {
        paymentId: 0,
        dueAmount: 9,
        tenderedAmount: 9,
        onComplete: { callback: onCompleteCallback },
      },
    };
    const result = await runSaga(
      {
        dispatch: (action: any) => {
          dispatched.push(action);
        },
        getState: () => Immutable.fromJS({ ...state, TransactionSession: { ...transactionSession.TransactionSession, receiptNumber: '0002407041624485' } }),
      },
      makePaymentSaga,
      makePaymentAction
    ).toPromise();

    expect(onCompleteCallback).toHaveBeenCalledTimes(1);
  });

  it('test makeSalePaymentSaga normal', async () => {
    const transactionSession = {
      TransactionSession: {
        items: [
          {
            discount: 0,
            display: {
              total: 9,
              subtotal: 9,
              tax: 0,
            },
            selectedOptions: [],
            subTotal: 9,
            total: 9,
            unitPrice: 9,
            taxInclusiveSubtotal: 9,
            productId: '5c4fc9d1e9020066fb1d220b',
            quantity: 1,
            taxRate: 0,
            tax: 0,
            taxExclusiveSubtotal: 9,
            taxCode: '5c4ec3a0e3d231664262c58c',
            title: 'Variable Price',
            adhocDiscount: 0,
          },
        ],
        discount: 0,
        display: {
          subtotal: 9,
          discount: 0,
          serviceCharge: 0,
          tax: 0,
          total: 9,
        },
        serviceChargeTax: 0,
        subtotal: 9,
        total: 9,
        serviceCharge: 0,
        tax: 0,
        appVersion: '********',
        transactionType: 'Sale',
        transactionId: 'e2823030-4354-4b2f-bf40-98d00edf7510',
        serialNumbers: {},
        promotions: [],
      },
    };
    const onComplete = { callback: () => null };
    testSaga(makeSalePaymentSaga, {
      payload: { paymentId: DefaultPaymentOptionType.Cash, tenderedAmount: 9, tableId: 'A1', paymentType: 'CASH', onComplete, isOnlineOrder: false },
    })
      .next()
      .call(makePaymentSaga, {
        payload: { paymentId: DefaultPaymentOptionType.Cash, tenderedAmount: 9, tableId: 'A1', paymentType: 'CASH', onComplete, isOnlineOrder: false },
      })
      .next()
      .isDone();
  });

  it('test makeSalePaymentSaga ewallet pay', async () => {
    const transactionSession = {
      TransactionSession: {
        items: [
          {
            discount: 0,
            display: {
              total: 9,
              subtotal: 9,
              tax: 0,
            },
            selectedOptions: [],
            subTotal: 9,
            total: 9,
            unitPrice: 9,
            taxInclusiveSubtotal: 9,
            productId: '5c4fc9d1e9020066fb1d220b',
            quantity: 1,
            taxRate: 0,
            tax: 0,
            taxExclusiveSubtotal: 9,
            taxCode: '5c4ec3a0e3d231664262c58c',
            title: 'Variable Price',
            adhocDiscount: 0,
          },
        ],
        discount: 0,
        display: {
          subtotal: 9,
          discount: 0,
          serviceCharge: 0,
          tax: 0,
          total: 9,
        },
        serviceChargeTax: 0,
        subtotal: 9,
        total: 9,
        serviceCharge: 0,
        tax: 0,
        appVersion: '********',
        transactionType: 'Sale',
        transactionId: 'e2823030-4354-4b2f-bf40-98d00edf7510',
        serialNumbers: {},
        promotions: [],
      },
    };
    const onComplete = { callback: () => null };
    testSaga(makeSalePaymentSaga, {
      payload: {
        paymentId: 11,
        tenderedAmount: 9,
        tableId: 'A1',
        paymentType: 'CASH',
        ewalletInfo: { mPOSTransactionId: 'mPOSTransactionId', approveReason: 'approveReason' },
        onComplete,
        isOnlineOrder: false,
      },
    })
      .next()
      .call(makePaymentSaga, {
        payload: {
          paymentId: 11,
          tenderedAmount: 9,
          tableId: 'A1',
          paymentType: 'CASH',
          ewalletInfo: { mPOSTransactionId: 'mPOSTransactionId', approveReason: 'approveReason' },
          onComplete,
          isOnlineOrder: false,
        },
      })
      .next()
      .isDone();
  });

  it('test makeSalePaymentSaga Pay By Cash', async () => {
    const transactionSession = {
      TransactionSession: {
        items: [
          {
            discount: 0,
            display: {
              total: 9,
              subtotal: 9,
              tax: 0,
            },
            selectedOptions: [],
            subTotal: 9,
            total: 9,
            unitPrice: 9,
            taxInclusiveSubtotal: 9,
            productId: '5c4fc9d1e9020066fb1d220b',
            quantity: 1,
            taxRate: 0,
            tax: 0,
            taxExclusiveSubtotal: 9,
            taxCode: '5c4ec3a0e3d231664262c58c',
            title: 'Variable Price',
            adhocDiscount: 0,
          },
        ],
        discount: 0,
        display: {
          subtotal: 9,
          discount: 0,
          serviceCharge: 0,
          tax: 0,
          total: 9,
        },
        serviceChargeTax: 0,
        subtotal: 9,
        total: 9,
        serviceCharge: 0,
        channel: 3,
        isOnlineOrder: true,
        tableId: 'B1',
        receiptNumber: '12345678',
        tax: 0,
        status: 'accepted',
        appVersion: '********',
        transactionType: 'Sale',
        transactionId: 'e2823030-4354-4b2f-bf40-98d00edf7510',
        serialNumbers: {},
        promotions: [],
      },
    };
    const onComplete = { callback: () => null };
    testSaga(makeSalePaymentSaga, {
      payload: { paymentId: DefaultPaymentOptionType.Cash, tenderedAmount: 9, tableId: 'A1', paymentType: 'CASH', onComplete, isOnlineOrder: true },
    })
      .next()
      .call(processOnlineOrderPayment, {
        payload: { paymentId: DefaultPaymentOptionType.Cash, tenderedAmount: 9, tableId: 'A1', paymentType: 'CASH', onComplete, isOnlineOrder: true },
      })
      .next()
      .isDone();
  });

  it('test makeSalePaymentSaga error', async () => {
    const transactionSession = {
      TransactionSession: {
        items: [
          {
            discount: 0,
            display: {
              total: 9,
              subtotal: 9,
              tax: 0,
            },
            selectedOptions: [],
            subTotal: 9,
            total: 9,
            unitPrice: 9,
            taxInclusiveSubtotal: 9,
            productId: '5c4fc9d1e9020066fb1d220b',
            quantity: 1,
            taxRate: 0,
            tax: 0,
            taxExclusiveSubtotal: 9,
            taxCode: '5c4ec3a0e3d231664262c58c',
            title: 'Variable Price',
            adhocDiscount: 0,
          },
        ],
        discount: 0,
        display: {
          subtotal: 9,
          discount: 0,
          serviceCharge: 0,
          tax: 0,
          total: 9,
        },
        serviceChargeTax: 0,
        subtotal: 9,
        total: 9,
        serviceCharge: 0,
        tax: 0,
        appVersion: '********',
        transactionType: 'Sale',
        transactionId: 'e2823030-4354-4b2f-bf40-98d00edf7510',
        serialNumbers: {},
        promotions: [],
      },
    };
    const onComplete = { callback: jest.fn() };
    testSaga(makeSalePaymentSaga, {
      payload: { paymentId: DefaultPaymentOptionType.Cash, tenderedAmount: 8, tableId: 'A1', paymentType: 'CASH', onComplete, isOnlineOrder: false },
    })
      .next()
      .call(makePaymentSaga, {
        payload: { paymentId: DefaultPaymentOptionType.Cash, tenderedAmount: 8, tableId: 'A1', paymentType: 'CASH', onComplete, isOnlineOrder: false },
      })
      .next()
      .isDone();
  });
});
