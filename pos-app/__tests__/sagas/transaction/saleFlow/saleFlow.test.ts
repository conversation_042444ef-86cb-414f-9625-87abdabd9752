import { expectSaga, testSaga } from 'redux-saga-test-plan';
import { setItemSalesperson, setTransactionSession, voidEWalPaymentBegin } from '../../../../ts/actions';
import { getCurrentTransaction } from '../../../../ts/sagas/transaction/common';
import { cancelNextPaymentSaga, getUniqueIdForShoppingCartOrWishlist, setItemSalespersonSaga } from '../../../../ts/sagas/transaction/saleFlow';
import { call } from 'redux-saga/effects';
import { fromJS } from 'immutable';

describe('test getUniqueIdForShoppingCartOrWishlist', () => {
  test('valid', () => {
    const result = getUniqueIdForShoppingCartOrWishlist(
      '628e2dfc5051f90007bc25be',
      [{ variationId: '6274a62985c4b30008c66e59', optionId: '6274a62985c4b30008c66e5c', optionValue: 'small', priceDiff: 10, quantity: 1 }],
      1
    );
    expect(result).toBe('1:628e2dfc5051f90007bc25be:6274a62985c4b30008c66e59:6274a62985c4b30008c66e5c');
  });

  test('valid', () => {
    const result = getUniqueIdForShoppingCartOrWishlist(
      '628e2dfc5051f90007bc25be',
      JSON.stringify([{ variationId: '6274a62985c4b30008c66e59', optionId: '6274a62985c4b30008c66e5c', optionValue: 'small', priceDiff: 10, quantity: 1 }]),
      1
    );
    expect(result).toBe('1:628e2dfc5051f90007bc25be:6274a62985c4b30008c66e59:6274a62985c4b30008c66e5c');
  });

  test('null', () => {
    const result = getUniqueIdForShoppingCartOrWishlist('628e2dfc5051f90007bc25be', null, 1);
    expect(result).toBe('1:628e2dfc5051f90007bc25be');
  });

  test('empty', () => {
    const result = getUniqueIdForShoppingCartOrWishlist('628e2dfc5051f90007bc25be', [], 1);
    expect(result).toBe('1:628e2dfc5051f90007bc25be');
  });
});

test('test cancelNextPaymentSaga', () => {
  const payments = {
    total: 1,
  };
  testSaga(cancelNextPaymentSaga)
    .next()
    .call(getCurrentTransaction)
    .next({
      payments,
      receiptNumber: '1',
      transactionId: '123456',
    })
    .put(
      voidEWalPaymentBegin({
        payments,
        receiptNumber: '1',
        transactionId: '123456',
      })
    )
    .next()
    .put(
      setTransactionSession({
        payments: [],
        receiptNumber: '1',
        roundedAmount: 0,
        totalPaid: 0,
        transactionId: '123456',
      })
    )
    .next()
    .isDone();
});

describe('setItemSalespersonSaga', () => {
  const mockEmployeeId = 'employee123';
  const mockEmployeeName = 'John Doe';
  const mockItemIndex = 1;

  it('should set employeeId and employeeName on an item', () => {
    const action = setItemSalesperson({
      itemIndex: mockItemIndex,
      employeeId: mockEmployeeId,
      employeeName: mockEmployeeName,
    });
    const mockCurrentRecord = {
      transactionId: '1',
      items: [
        { id: 'item1', name: 'Product 1' },
        { id: 'item2', name: 'Product 2', employeeId: null, employeeName: null },
        { id: 'item3', name: 'Product 3' },
      ],
    };
    const expectedRecord = {
      ...mockCurrentRecord,
      items: [
        { id: 'item1', name: 'Product 1' },
        { id: 'item2', name: 'Product 2', employeeId: mockEmployeeId, employeeName: mockEmployeeName },
        { id: 'item3', name: 'Product 3' },
      ],
    };

    return expectSaga(setItemSalespersonSaga, action)
      .withState(
        fromJS({
          TransactionSession: mockCurrentRecord,
        })
      )
      .call(getCurrentTransaction)
      .put(setTransactionSession(expectedRecord))
      .run();
  });

  it('should remove salesperson when employeeId is null', () => {
    const action = setItemSalesperson({
      itemIndex: mockItemIndex,
      employeeId: null,
      employeeName: null,
    });
    const mockCurrentRecord = {
      transactionId: '1',
      items: [
        { id: 'item1', name: 'Product 1' },
        { id: 'item2', name: 'Product 2', employeeId: mockEmployeeId, employeeName: mockEmployeeName },
        { id: 'item3', name: 'Product 3' },
      ],
    };
    const expectedRecord = {
      ...mockCurrentRecord,
      items: [
        { id: 'item1', name: 'Product 1' },
        { id: 'item2', name: 'Product 2', employeeId: null, employeeName: null },
        { id: 'item3', name: 'Product 3' },
      ],
    };

    return expectSaga(setItemSalespersonSaga, action)
      .withState(
        fromJS({
          TransactionSession: mockCurrentRecord,
        })
      )
      .call(getCurrentTransaction)
      .put(setTransactionSession(expectedRecord))
      .run();
  });

  it('should not modify transaction when itemIndex is nil', () => {
    const action = setItemSalesperson({
      itemIndex: null,
      employeeId: mockEmployeeId,
      employeeName: mockEmployeeName,
    });
    const mockCurrentRecord = {
      transactionId: '1',
      items: [
        { id: 'item1', name: 'Product 1' },
        { id: 'item2', name: 'Product 2' },
        { id: 'item3', name: 'Product 3' },
      ],
    };

    return expectSaga(setItemSalespersonSaga, action)
      .withState(
        fromJS({
          TransactionSession: mockCurrentRecord,
        })
      )
      .run();
  });

  it('should not modify transaction when item does not exist', () => {
    const action = setItemSalesperson({
      itemIndex: 999, // Non-existent index
      employeeId: mockEmployeeId,
      employeeName: mockEmployeeName,
    });
    const mockCurrentRecord = {
      transactionId: '1',
      items: [
        { id: 'item1', name: 'Product 1' },
        { id: 'item2', name: 'Product 2' },
        { id: 'item3', name: 'Product 3' },
      ],
    };

    return expectSaga(setItemSalespersonSaga, action)
      .withState(
        fromJS({
          TransactionSession: mockCurrentRecord,
        })
      )
      .call(getCurrentTransaction)
      .not.put(setTransactionSession(expect.anything()))
      .run();
  });

  it('should update salesperson for an item that already has one', () => {
    const newEmployeeId = 'employee456';
    const newEmployeeName = 'Jane Smith';
    const action = setItemSalesperson({
      itemIndex: mockItemIndex,
      employeeId: newEmployeeId,
      employeeName: newEmployeeName,
    });
    const mockCurrentRecord = {
      transactionId: '1',
      items: [
        { id: 'item1', name: 'Product 1' },
        { id: 'item2', name: 'Product 2', employeeId: mockEmployeeId, employeeName: mockEmployeeName },
        { id: 'item3', name: 'Product 3' },
      ],
    };
    const expectedRecord = {
      ...mockCurrentRecord,
      items: [
        { id: 'item1', name: 'Product 1' },
        { id: 'item2', name: 'Product 2', employeeId: newEmployeeId, employeeName: newEmployeeName },
        { id: 'item3', name: 'Product 3' },
      ],
    };

    return expectSaga(setItemSalespersonSaga, action)
      .withState(
        fromJS({
          TransactionSession: mockCurrentRecord,
        })
      )
      .call(getCurrentTransaction)
      .put(setTransactionSession(expectedRecord))
      .run();
  });
});
