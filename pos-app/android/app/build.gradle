apply plugin: "com.android.application"
apply plugin: "com.facebook.react"
apply from: project(':react-native-config').projectDir.getPath() + "/dotenv.gradle"

import com.android.build.OutputFile

project.ext.vectoricons = [
        iconFontNames: ['MaterialIcons.ttf', 'FontAwesome.ttf', 'Ionicons.ttf'] // Name of the font files you want to copy
]
project.ext.react = [
        entryFile: "index.js",
]
project.ext.reanimated = [
        buildFromSource: false
]

def jscFlavor = 'org.webkit:android-jsc:+'

apply from: "../../node_modules/@sentry/react-native/sentry.gradle"
apply from: "../../node_modules/react-native-vector-icons/fonts.gradle"


def enableSeparateBuildPerCPUArchitecture = false

def enableProguardInReleaseBuilds = false

android {
    ndkVersion rootProject.ext.ndkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion
    compileSdk rootProject.ext.compileSdkVersion

    namespace "com.storehub.pos"
    defaultConfig {
        applicationId "com.storehub.pos"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 190
        versionName "1.92.0"
        multiDexEnabled true
        manifestPlaceholders = [
                app_name:"StoreHub"
        ]
        resValue "string", "build_config_package", "com.storehub.pos"
    }
    splits {
        abi {
            reset()
            enable enableSeparateBuildPerCPUArchitecture
            universalApk false  // If true, also generate a universal APK
            include "armeabi-v7a", "x86", "arm64-v8a", "x86_64"
        }
    }

    signingConfigs {
        debug {
            storeFile file(project.env.get("ENV_STORE_FILE"))
            storePassword project.env.get("ENV_STORE_PASSWORD")
            keyAlias project.env.get("ENV_KEY_ALIAS")
            keyPassword project.env.get("ENV_KEY_PASSWORD")
        }
        release {
            storeFile file(project.env.get("ENV_STORE_FILE"))
            storePassword project.env.get("ENV_STORE_PASSWORD")
            keyAlias project.env.get("ENV_KEY_ALIAS")
            keyPassword project.env.get("ENV_KEY_PASSWORD")
        }
    }

    buildTypes {
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
            signingConfig signingConfigs.debug
            ndk {
                abiFilters "armeabi-v7a", "arm64-v8a", "x86", "x86_64"
            }
//            packagingOptions {
//                pickFirst 'lib/armeabi-v7a/libc++_shared.so'
//                pickFirst 'lib/arm64-v8a/libc++_shared.so'
//                pickFirst 'lib/x86/libc++_shared.so'
//                pickFirst 'lib/x86_64/libc++_shared.so'
//            }
        }
        release {
            crunchPngs false
            minifyEnabled enableProguardInReleaseBuilds
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
            signingConfig signingConfigs.release
            ndk {
                abiFilters "armeabi-v7a", "arm64-v8a"
            }
//            packagingOptions {
//                pickFirst 'lib/arm64-v8a/libc++_shared.so'
//                pickFirst 'lib/armeabi-v7a/libc++_shared.so'
//            }
        }
    }


    flavorDimensions "mode"

    productFlavors {
        dev {
            dimension "mode"
            applicationId "com.storehub.pos.test"
            manifestPlaceholders = [
                    app_name:"StoreHub Dev"
            ]
        }
        fat {
            dimension "mode"
            applicationId "com.storehub.pos.test"
            manifestPlaceholders = [
                    app_name:"StoreHub Fat"
            ]
        }
        staging {
            dimension "mode"
            manifestPlaceholders = [
                    app_name: "StoreHub Staging"
            ]
        }
        pro {
            dimension "mode"
            manifestPlaceholders = [
                    app_name: "StoreHub"
            ]
        }
    }

    // applicationVariants are e.g. debug, release
    applicationVariants.all { variant ->
        variant.outputs.each { output ->
            // For each separate APK per architecture, set a unique version code as described here:
            // http://tools.android.com/tech-docs/new-build-system/user-guide/apk-splits
            def versionCodes = ["armeabi-v7a": 1, "x86": 2,"arm64-v8a": 3, "x86_64": 4]
            def abi = output.getFilter(OutputFile.ABI)
            if (abi != null) {  // null for the universal-debug, universal-release variants
                output.versionCodeOverride =
                        versionCodes.get(abi) * 1048576 + defaultConfig.versionCode
            }
        }

        variant.outputs.all {
            def flavor = variant.productFlavors[0].name.capitalize()
            def version = defaultConfig.versionName
            outputFileName = "StoreHub_${flavor}_${version}.apk"
        }
    }
}

configurations {
    devReleaseImplementation {}
    fatReleaseImplementation {}
    stagingReleaseImplementation {}
    proReleaseImplementation {}
}


dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])

    // The version of react-native is set by the React Native Gradle Plugin
    implementation("com.facebook.react:react-android")
    if (hermesEnabled.toBoolean()) {
        implementation("com.facebook.react:hermes-android")
    } else {
        implementation jscFlavor
    }
    // Customized Dependencies
    implementation project(':react-native-picker')
    implementation project(':react-native-printer')
    implementation project(':react-native-log')
    implementation project(':react-native-peripheral')
    implementation project(':react-native-anr-monitor')
    implementation project(':react-native-nfc')
    implementation project(':react-native-scanner')
    implementation project(':react-native-mrs')
    implementation project(':react-native-file-logger')
    implementation project(':react-native-uvc-camera')
    implementation project(':react-native-keyevent')
    implementation project(':react-native-s3')
    implementation project(':react-native-fast-image')
    implementation project(':react-native-permissions')
    implementation project(':react-native-background-timer')
    implementation project(':react-native-timezone')

    implementation project(':storehub-ping')
    implementation project(':storehub-imin-lcd')
    implementation project(':storehub-sunmi-service')

    // 3rd party dependencies
    implementation 'com.liulishuo.filedownloader:library:1.7.5'
    implementation "com.google.firebase:firebase-core:21.1.1"
    implementation "com.google.firebase:firebase-config:21.6.3"
    implementation 'com.google.firebase:firebase-messaging:23.4.1'
    implementation 'com.google.firebase:firebase-auth:22.3.1'
    implementation 'com.google.firebase:firebase-iid:21.1.0'
    implementation "com.google.firebase:firebase-perf:21.0.1"
    implementation "androidx.multidex:multidex:2.0.1"
    implementation "ru.noties.markwon:core:3.0.1"
    implementation "ru.noties.markwon:recycler:3.0.1"
    implementation "ru.noties.markwon:recycler-table:3.0.1"
    implementation 'ru.noties:adapt:1.1.0'
    implementation "com.google.android.gms:play-services-location:21.2.0"
    implementation 'com.jakewharton.timber:timber:4.7.1'
    implementation "org.slf4j:slf4j-api:1.7.25"
}

apply from: "../../node_modules/react-native-code-push/android/codepush.gradle"
apply plugin: 'com.google.gms.google-services'
//apply plugin: 'com.google.firebase.firebase-perf'
apply from: file("../../node_modules/@react-native-community/cli-platform-android/native_modules.gradle"); applyNativeModulesAppBuildGradle(project)
