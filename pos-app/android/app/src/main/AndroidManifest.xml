<manifest xmlns:android="http://schemas.android.com/apk/res/android"
          xmlns:tools="http://schemas.android.com/tools">

    <uses-feature
            android:name="android.hardware.usb.host"
            android:required="true"/>

    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
    <uses-permission android:name="android.hardware.usb.UsbAccessory"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE"
                     tools:ignore="ProtectedPermissions"/>
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>

    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="android.permission.BLUETOOTH"/>
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN"/>
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />

    <uses-sdk tools:overrideLibrary="com.oney.WebRTCModule, my.com.softspace.SSMobileThirdPartyEngine, my.com.softspace.SSMobileUIComponent, my.com.softspace.ssmpossdk,
my.com.softspace.reader, my.com.softspace.SSMobileAndroidUtilEngine, my.com.softspace.ssfasstapsdk, my.com.softspace.SSMobileMPOSCore, my.com.softspace.ssfasstapwrapper,
my.com.softspace.sspog, my.com.softspace.SSMobileReaderEngine" />

    <!-- tools:replace https://developer.android.com/studio/build/manage-manifests -->
    <application
            android:name="com.storehub.pos.MainApplication"
            android:allowBackup="true"
            android:usesCleartextTraffic="true"
            android:icon="@mipmap/app_ic_launcher"
            android:roundIcon="@mipmap/app_ic_launcher_round"
            android:label="${app_name}"
            tools:replace="android:label,android:icon,android:roundIcon"
            android:theme="@style/AppTheme"
            android:networkSecurityConfig="@xml/network_security_config"
            android:largeHeap="true">

        <service android:name="com.storehub.rn.printer.POSPrinterService" android:exported="true">
            <intent-filter>
                <action android:name="com.storehub.rn.printer.POSPrinterService"/>
            </intent-filter>
        </service>

        <service
                android:name=".FCMModuleService"
                android:stopWithTask="false"
                android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT"/>
            </intent-filter>
        </service>

        <service
                android:name="com.amazonaws.mobileconnectors.s3.transferutility.TransferService"
                android:enabled="true"/>

        <activity
                android:name="com.storehub.pos.MainActivity"
                android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
                android:label="${app_name}"
                android:launchMode="singleTask"
                android:screenOrientation="landscape"
                android:theme="@style/WelcomeTheme"
                android:windowSoftInputMode="adjustPan" android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <activity android:name="com.facebook.react.devsupport.DevSettingsActivity"/>

        <provider
                android:name="androidx.core.content.FileProvider"
                android:authorities="${applicationId}.FileProvider"
                android:exported="false"
                android:grantUriPermissions="true">
            <meta-data
                    android:name="android.support.FILE_PROVIDER_PATHS"
                    android:resource="@xml/filepaths"/>
        </provider> <!-- imlib config begin -->

        <uses-library
                android:name="org.apache.http.legacy"
                android:required="false"/>
    </application>

</manifest>
