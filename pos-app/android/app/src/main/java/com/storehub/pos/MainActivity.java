package com.storehub.pos;

import android.annotation.TargetApi;
import android.app.Activity;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.content.*;
import android.graphics.Color;
import android.hardware.input.InputManager;
import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbManager;
import android.media.AudioAttributes;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.InputDevice;
import android.view.KeyEvent;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import com.facebook.react.ReactActivity;
import com.github.kevinejohn.keyevent.KeyEventModule;
import com.liulishuo.filedownloader.FileDownloader;
import com.storehub.eposprinter.utils.PosPrinterDev;
import com.storehub.pos.util.CheckUpdateHelper;
import com.storehub.pos.util.SplashScreen;
import com.storehub.pos.util.UsbManagerUtil;
import com.storehub.rn.peripheral.scanner.ScannerEventEnum;
import com.storehub.rn.printer.PrintingManager;
import com.storehub.rn.printer.printer.connection.impl.USBConnection;
import com.storehub.rn.scanner.RNScannerModule;
import com.storehub.rn.scanner.scanner.Scanner;
import com.storehub.rn.scanner.util.ScannerLogEventUtil;
import com.storehub.rn.util.ToolScanner;
import timber.log.Timber;

import java.lang.ref.WeakReference;
import java.util.Objects;

import static androidx.core.app.NotificationCompat.VISIBILITY_SECRET;

public class MainActivity extends ReactActivity implements ToolScanner.OnScanSuccessListener, InputManager.InputDeviceListener {
    private final static String TAG = MainActivity.class.getSimpleName();

    private ToolScanner toolScanner;
    private RNScannerModule scannerModule;
    private BroadcastReceiver usbBroadcastReceiver;
    private InputManager mInputManager;

    /**
     * Returns the name of the main component registered from JavaScript.
     * This is used to schedule rendering of the component.
     */
    @Override
    protected String getMainComponentName() {
        return "pos";
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        SplashScreen.getInstance().show(this, true);
        super.onCreate(null);
        toolScanner = new ToolScanner(this);
        usbBroadcastReceiver = initUsbBroadcastReceiver();
        mInputManager = (InputManager) getSystemService(Context.INPUT_SERVICE);

        CheckUpdateHelper.getInstance().init(new WeakReference<Activity>(this));

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            Uri beepPaidSound = Uri
                    .parse(ContentResolver.SCHEME_ANDROID_RESOURCE + "://" + getPackageName() + "/" + R.raw.beeppaid);
            createNotificationChannel(NotificationChannelType.BEEP_PAID.getName(),
                    NotificationChannelType.BEEP_PAID.getName(), beepPaidSound);
            Uri beepSound = Uri
                    .parse(ContentResolver.SCHEME_ANDROID_RESOURCE + "://" + getPackageName() + "/" + R.raw.beep);
            createNotificationChannel(NotificationChannelType.BEEP.getName(), NotificationChannelType.BEEP.getName(),
                    beepSound);
            Uri defaultSound = null;
            createNotificationChannel(NotificationChannelType.DEFAULT.getName(),
                    NotificationChannelType.DEFAULT.getName(), defaultSound);
        }
        disableScreenCapture();

        if (BuildConfig.DEBUG) {
            Timber.tag("SystemProperty").w("Model:  %s", Build.MODEL);
            Timber.tag("SystemProperty").w("Brand:  %s", Build.BRAND);
            Timber.tag("SystemProperty").w("Serial: %s", Build.SERIAL);
        }
    }

    /**
     * Required by fasstap SDK in the  nfc library  for transaction security.
     */
    private void disableScreenCapture() {
        // Open to screenshot for QA.
        if ("fat".equalsIgnoreCase(BuildConfig.FLAVOR)) {
            return;
        }
        if ("dev".equalsIgnoreCase(BuildConfig.FLAVOR)) {
            return;
        }
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_SECURE, WindowManager.LayoutParams.FLAG_SECURE);
    }

    /**
     * Init a USB BroadcastReceiver
     *
     * @return BroadcastReceiver
     */
    public BroadcastReceiver initUsbBroadcastReceiver() {
        BroadcastReceiver broadcastReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                String action = intent.getAction();
                // Search current scanner id
                UsbManagerUtil.searchScanner(getApplicationContext());
                UsbDevice device = intent.getParcelableExtra(UsbManager.EXTRA_DEVICE);
                if (device == null) {
                    return;
                }

                Scanner scanner = new Scanner();
                scanner.setVendorId(device.getVendorId());
                scanner.setProductId(device.getProductId());
                scanner.setDeviceId(device.getDeviceId());
                scanner.setDeviceName(device.getDeviceName());
                scanner.setManufacturerName(device.getManufacturerName());
                scanner.setProductName(device.getProductName());

                boolean isHid = UsbManagerUtil.checkHid(context, device);
                if (UsbManager.ACTION_USB_DEVICE_ATTACHED.equals(action)) {
                    boolean isUSBPrinter = PosPrinterDev.CheckUsbVid(device);
                    boolean isBixoLonPrinter = PosPrinterDev.CheckBixolonVid(device);
                    if (isHid) {
                        UsbManagerUtil.checkAndSetScannerId(context, device);
                    }
                    if (isUSBPrinter || isBixoLonPrinter) {
                        // TODO: Attach USB Printer, it's a bit complicated to log this event because the usb printer's path changed everytime when attach
                    }
                } else if (UsbManager.ACTION_USB_DEVICE_DETACHED.equals(action)) {
                    boolean isUSBPRinter = PosPrinterDev.CheckUsbVid(device);
                    boolean isBixoLonPrinter = PosPrinterDev.CheckBixolonVid(device);

                    if (isHid) {
                        if (ToolScanner.isScannerId(device.getVendorId())) {
                            ToolScanner.removeVendorId(device.getVendorId());
                        }
                    } else if (isUSBPRinter || isBixoLonPrinter) {
                        PrintingManager manager = PrintingManager.getInstance(context);
                        manager.detachPrinter(device);
                    }
                } else if (UsbManagerUtil.ACTION_USB_PERMISSION.equals(action)) {
                    if (intent.getBooleanExtra(UsbManager.EXTRA_PERMISSION_GRANTED, false)) {
                        Log.d(TAG, "Scanner Permission granted for device " + device.getDeviceName());
                        ToolScanner.addScanner(scanner);
                    } else {
                        Log.d(TAG, "Scanner Permission denied for device " + device.getDeviceName());
                        // Only log error for scanner module, because the success log is logged in PrinterManager
                        ScannerLogEventUtil.warnScannerEvent(ScannerEventEnum.PERMISSION_DENIED, "Permission denied", scanner);
                    }
                } else if (USBConnection.ACTION_USB_PERMISSION.equals(action)) {
                    if (intent.getBooleanExtra(UsbManager.EXTRA_PERMISSION_GRANTED, false)) {
                        // TODO: USB Printer permission grated
                        Log.d(TAG, "USB Printer Permission granted for device " + device.getDeviceName());
                    } else {
                        Log.d(TAG, "USB Printer Permission denied for device " + device.getDeviceName());
                        // TODO: USB Printer permission denied

                    }
                }
            }
        };
        return broadcastReceiver;
    }

    @Override
    protected void onResume() {
        super.onResume();

        // RegisterReceiver of USB, must before searchScanner
        IntentFilter filter = new IntentFilter(UsbManagerUtil.ACTION_USB_PERMISSION);
        filter.addAction(USBConnection.ACTION_USB_PERMISSION);
        filter.addAction(UsbManager.ACTION_USB_DEVICE_ATTACHED);
        filter.addAction(UsbManager.ACTION_USB_DEVICE_DETACHED);
        registerReceiver(usbBroadcastReceiver, filter);
        // CM-869 Temporary solution
        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.Q)
            mInputManager.registerInputDeviceListener(this, null);
        UsbManagerUtil.searchScanner(getApplicationContext());
    }

    @Override
    protected void onPause() {
        super.onPause();
        // CM-869 Temporary solution
        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.Q)
            mInputManager.unregisterInputDeviceListener(this);
        // UnregisterReceiver of USB
        unregisterReceiver(usbBroadcastReceiver);
    }

    /**
     * Override dispatch KeyEvent for scanner
     *
     * @param event: KeyEvent
     * @return Should handle the KeyEvent
     */
    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        if (event.getDevice().getVendorId() == 12994 && event.getDeviceId() == 13) { // number keyboard
            return super.dispatchKeyEvent(event);
        }

        if (toolScanner.isScannerGunEvent(event)) { // scanner
            if (RNScannerModule.scannerFixFlag == -1 && isScannerBrokenIminFirmware()) { // d1 & falcon 1 & d4
                toolScanner.analysisKeyEvent_ActionUpFallback(event);
            } else if (RNScannerModule.scannerFixFlag == 1) { // force enabled
                toolScanner.analysisKeyEvent_ActionUpFallback(event);
            } else {
                toolScanner.analysisKeyEvent(event);
            }
            return true;
        }
        return super.dispatchKeyEvent(event);
    }

    @Override
    public void onHideKeyBoard() {
        InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
        if (imm != null
                && getWindow().getAttributes().softInputMode != WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN) {
            if (getCurrentFocus() != null) {
                imm.hideSoftInputFromWindow(getCurrentFocus().getWindowToken(), InputMethodManager.HIDE_NOT_ALWAYS);
            }
        }
    }

    @Override
    public void onScanSuccess(String barcode) {
        // onCreate is called before ScannerModdule.initScannerModule, so getInstance
        // here.
        if (scannerModule == null) {
            scannerModule = RNScannerModule.getInstance();
        }
        scannerModule.onScan(barcode);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        FileDownloader.getImpl().unBindServiceIfIdle();
    }

    @TargetApi(Build.VERSION_CODES.O)
    public void createNotificationChannel(String channelId, String channelName, Uri soundUri) {
        NotificationChannel channel = new NotificationChannel(channelId, channelName,
                NotificationManager.IMPORTANCE_DEFAULT);
        // 是否绕过请勿打扰模式
        channel.canBypassDnd();
        // 闪光灯
        channel.enableLights(true);
        // 锁屏显示通知
        channel.setLockscreenVisibility(VISIBILITY_SECRET);
        // 闪关灯的灯光颜色
        channel.setLightColor(Color.RED);
        // 桌面launcher的消息角标
        channel.canShowBadge();
        // 是否允许震动
        channel.enableVibration(true);
        // 获取系统通知响铃声音的配置
        channel.getAudioAttributes();
        // 获取通知取到组
        channel.getGroup();
        // 设置可绕过 请勿打扰模式
        channel.setBypassDnd(true);
        // 设置震动模式
        channel.setVibrationPattern(new long[]{100, 100, 200});
        // 是否会有灯光
        channel.shouldShowLights();
        AudioAttributes audioAttributes = new AudioAttributes.Builder()
                .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
                .setUsage(AudioAttributes.USAGE_NOTIFICATION)
                .build();
        if (soundUri != null && !channel.equals("default")) {
            channel.setSound(soundUri, audioAttributes);
        }

        NotificationManager mManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
        mManager.createNotificationChannel(channel);
    }

    @Override
    public void onInputDeviceAdded(int id) {
        InputDevice device = mInputManager.getInputDevice(id);
        Scanner scanner = new Scanner();
        scanner.setVendorId(device.getVendorId());
        scanner.setProductId(device.getProductId());
        scanner.setDeviceName(device.getName());
        ToolScanner.addScanner(scanner);
    }

    @Override
    public void onInputDeviceRemoved(int id) {
        Log.d(TAG, "onInputDeviceRemoved:  " + id);
    }

    @Override
    public void onInputDeviceChanged(int i) {
        Log.d(TAG, "onInputDeviceChanged:  " + i);
    }


    @Override  // <--- Add this method if you want to react to keyDown
    public boolean onKeyDown(int keyCode, KeyEvent event) {

        // A. Prevent multiple events on long button press
        //    In the default behavior multiple events are fired if a button
        //    is pressed for a while. You can prevent this behavior if you
        //    forward only the first event:
        //        if (event.getRepeatCount() == 0) {
        //            KeyEventModule.getInstance().onKeyDownEvent(keyCode, event);
        //        }
        //
        // B. If multiple Events shall be fired when the button is pressed
        //    for a while use this code:
        //        KeyEventModule.getInstance().onKeyDownEvent(keyCode, event);
        //
        // Using B.
        KeyEventModule.getInstance().onKeyDownEvent(keyCode, event);

        // There are 2 ways this can be done:
        //  1.  Override the default keyboard event behavior
        //    super.onKeyDown(keyCode, event);
        //    return true;

        //  2.  Keep default keyboard event behavior
        //    return super.onKeyDown(keyCode, event);

        // Using method #1 without blocking multiple
        super.onKeyDown(keyCode, event);
        return true;
    }

    @Override  // <--- Add this method if you want to react to keyUp
    public boolean onKeyUp(int keyCode, KeyEvent event) {
        KeyEventModule.getInstance().onKeyUpEvent(keyCode, event);

        // There are 2 ways this can be done:
        //  1.  Override the default keyboard event behavior
        //    super.onKeyUp(keyCode, event);
        //    return true;

        //  2.  Keep default keyboard event behavior
        //    return super.onKeyUp(keyCode, event);

        // Using method #1
        super.onKeyUp(keyCode, event);
        return true;
    }

    @Override
    public boolean onKeyMultiple(int keyCode, int repeatCount, KeyEvent event) {
        KeyEventModule.getInstance().onKeyMultipleEvent(keyCode, repeatCount, event);
        return super.onKeyMultiple(keyCode, repeatCount, event);
    }

    public boolean isScannerBrokenIminFirmware() {
        return Objects.equals(Build.MODEL, "Swan 1") || Objects.equals(Build.MODEL, "D1") || Objects.equals(Build.MODEL, "I22T01") || Objects.equals(Build.MODEL, "I20D01") || Objects.equals(Build.MODEL, "D4-504");
    }

}
