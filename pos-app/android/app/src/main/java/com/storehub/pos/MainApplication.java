package com.storehub.pos;

import android.content.Context;
import android.graphics.Bitmap;

import androidx.multidex.MultiDex;
import androidx.multidex.MultiDexApplication;

import com.beefe.picker.PickerViewPackage;
import com.betomorrow.rnfilelogger.FileLoggerPackage;
import com.facebook.common.util.ByteConstants;
import com.facebook.imagepipeline.backends.okhttp3.OkHttpImagePipelineConfigFactory;
import com.facebook.imagepipeline.cache.MemoryCacheParams;
import com.facebook.imagepipeline.core.ImagePipelineConfig;
import com.facebook.react.PackageList;
import com.facebook.react.ReactApplication;
import com.facebook.react.ReactNativeHost;
import com.facebook.react.ReactPackage;
import com.facebook.react.defaults.DefaultReactNativeHost;
import com.facebook.react.modules.network.OkHttpClientProvider;
import com.facebook.react.shell.MainPackageConfig;
import com.facebook.soloader.SoLoader;
import com.github.kevinejohn.keyevent.KeyEventPackage;
import com.intercom.reactnative.IntercomModule;
import com.liulishuo.filedownloader.FileDownloader;
import com.liulishuo.filedownloader.connection.FileDownloadUrlConnection;
import com.liulishuo.filedownloader.util.FileDownloadLog;
import com.microsoft.codepush.react.CodePush;
import com.mybigday.rns3.RNS3Package;
import com.storehub.components.log.SHLog;
import com.storehub.imin.StoreHubIminLcdPackage;
import com.storehub.pos.uvc.RNCameraPackage;
import com.storehub.sunmi.StoreHubSunmiServicePackage;
import com.storehub.log.RNLogPackage;
import com.storehub.mrs.RNMRSPackage;
import com.storehub.nfc.Env;
import com.storehub.nfc.ReactNativeNfcPackage;
import com.storehub.ping.PingPackage;
import com.storehub.pos.util.MyTimberDebugTree;
import com.storehub.rn.autoupdate.RNAUPackage;
import com.storehub.rn.fcm.FcmPushPackage;
import com.storehub.rn.home.RNHomePackage;
import com.storehub.rn.monitor.RNAnrMonitorPackage;
import com.storehub.rn.peripheral.RNPeripheralPackage;
import com.storehub.rn.printer.RNPrinterModuleConfig;
import com.storehub.rn.printer.RNPrinterPackage;
import com.storehub.rn.scanner.RNScannerPackage;
import com.storehub.rn.splashscreen.RNSCSPackage;
import com.storehub.rn.timezone.RNTimezonePackage;

import cn.reactnative.modules.update.UpdateContext;

import java.util.Arrays;
import java.util.List;

import okhttp3.OkHttpClient;
import timber.log.Timber;

public class MainApplication extends MultiDexApplication implements ReactApplication {
    public static Context CONTEXT;

    private final ReactNativeHost mReactNativeHost = new DefaultReactNativeHost(this) {
        @Override
        public boolean getUseDeveloperSupport() {
            return BuildConfig.DEBUG;
        }

        @Override
        protected List<ReactPackage> getPackages() {
            List<ReactPackage> packages = new PackageList(this, getMainPackageConfig()).getPackages();
            // Packages that cannot be autolinked yet can be added manually here
            List<ReactPackage> list = Arrays.asList(
                    new PickerViewPackage(),
                    new RNPrinterPackage(),
                    new RNScannerPackage(),
                    new RNAUPackage(),
                    new RNHomePackage(),
                    new RNTimezonePackage(),
                    new RNS3Package(),
                    new RNSCSPackage(),
                    new RNLogPackage(),
                    new RNPeripheralPackage(),
                    new FcmPushPackage(),
                    new ReactNativeNfcPackage(),
                    new StoreHubIminLcdPackage(),
                    new StoreHubSunmiServicePackage(),
                    new RNMRSPackage(),
                    new PingPackage(),
                    new RNCameraPackage(),
                    new KeyEventPackage(),
                    new RNAnrMonitorPackage(),
                    new FileLoggerPackage()
                    );
            packages.addAll(list);
            return packages;
        }

        private MainPackageConfig getMainPackageConfig() {
            OkHttpClient client = OkHttpClientProvider.createClient();
            ImagePipelineConfig imagePipelineConfig = OkHttpImagePipelineConfigFactory
                    .newBuilder(getApplicationContext(), client)
                    .setDownsampleEnabled(true)
                    .setBitmapsConfig(Bitmap.Config.RGB_565)
                    .setBitmapMemoryCacheParamsSupplier(() -> {
                        int MAX_MEMORY_CACHE_SIZE = getMaxCacheSize();
                        return new MemoryCacheParams(
                                MAX_MEMORY_CACHE_SIZE,
                                Integer.MAX_VALUE,
                                MAX_MEMORY_CACHE_SIZE,
                                Integer.MAX_VALUE,
                                Integer.MAX_VALUE);
                    }).build();
            return new MainPackageConfig.Builder().setFrescoConfig(imagePipelineConfig).build();
        }

        private int getMaxCacheSize() {
            final int maxMemory = Math.min((int) Runtime.getRuntime().maxMemory(), Integer.MAX_VALUE);
            if (maxMemory < 32 * ByteConstants.MB) {
                return 4 * ByteConstants.MB;
            } else if (maxMemory < 64 * ByteConstants.MB) {
                return 6 * ByteConstants.MB;
            } else {
                return maxMemory / 8;
            }
        }

        // Override the getJSBundleFile method in order to let
        // the Pushy runtime determine where to get the JS
        // bundle location from on each app start
        @Override
        protected String getJSBundleFile() {
            if (BuildConfig.ENVIRONMENT == "FAT") {
                return CodePush.getJSBundleFile();
            }
            return UpdateContext.getBundleUrl(MainApplication.this);
        }

        @Override
        protected String getJSMainModuleName() {
            return "index";
        }

        @Override
        protected boolean isNewArchEnabled() {
            return BuildConfig.IS_NEW_ARCHITECTURE_ENABLED;
        }

        @Override
        protected Boolean isHermesEnabled() {
            return BuildConfig.IS_HERMES_ENABLED;
        }
    };

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        MultiDex.install(this);
    }

    @Override
    public ReactNativeHost getReactNativeHost() {
        return mReactNativeHost;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        SoLoader.init(this, /* native exopackage */ false);
        CONTEXT = this;

        // just for open the log in this demo project.
        FileDownloadLog.NEED_LOG = true;

        // Intercom
        String intercomApiKey = BuildConfig.ANDROID_INTERCOM_API_KEY;
        String intercomAppId = BuildConfig.ANDROID_INTERCOM_APP_ID;
        IntercomModule.initialize(this, intercomApiKey, intercomAppId);

        /**
         * just for cache Application's Context, and ':filedownloader' progress will NOT
         * be launched
         * by below code, so please do not worry about performance.
         *
         * @see FileDownloader#init(Context)
         */
        FileDownloader.setupOnApplicationOnCreate(this)
                .connectionCreator(new FileDownloadUrlConnection.Creator(new FileDownloadUrlConnection.Configuration()
                        .connectTimeout(15_000) // set connection timeout.
                        .readTimeout(15_000) // set read timeout.
                ))
                .commit();

        if (BuildConfig.DEBUG) {
            Timber.plant(new MyTimberDebugTree());
        }


//        if (BuildConfig.DEBUG) {
//            new DoKit.Builder(this)
//                    .productId("e10b8cc93e2f4855121b6d4e09058234")
//                    .build();
//        }
        String url = BuildConfig.LOG_SERVICE_URL;
        String token = BuildConfig.LOG_SERVICE_TOKEN;
        String publicKey = BuildConfig.LOG_PUBLIC_KEY;
        SHLog.INSTANCE.init(CONTEXT, url, token, publicKey, "RNPOSAndroid");

//        AnrMonitor.getDefault().init(this, BuildConfig.DEBUG);

        initEnvForNfc();
        RNPrinterModuleConfig.getInstance().setEnableDumpImages(BuildConfig.ENABLE_DUMP_PRINTING_IMAGES == "TRUE");
    }

    private void initEnvForNfc() {
        Env.INSTANCE.updateEnv(BuildConfig.ATTESTATION_HOST, BuildConfig.ATTESTATION_CERT_PINNING,
                BuildConfig.GOOGLE_API_KEY, BuildConfig.ACCESS_KEY, BuildConfig.SECRET_KEY,
                BuildConfig.FLAVOR_ENVIRONMENT, BuildConfig.DEVELOPER_ID);
    }

    @Override
    public void onTerminate() {
        super.onTerminate();
//        AnrMonitor.getDefault().stop();
        SHLog.INSTANCE.release();
    }

}
