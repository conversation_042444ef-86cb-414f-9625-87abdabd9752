package com.storehub.rn.home;

import android.content.Intent;
import android.provider.Settings;

import androidx.annotation.Nullable;

import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.storehub.pos.MainActivity;
import com.storehub.pos.util.CheckUpdateHelper;

import java.util.HashMap;
import java.util.Map;


public class RNHomeModule extends ReactContextBaseJavaModule {
    private final ReactApplicationContext reactContext;

    public RNHomeModule(ReactApplicationContext reactContext) {
        super(reactContext);
        this.reactContext = reactContext;
    }

    @Override
    public String getName() {
        return "RNHomeModule";
    }

    @ReactMethod
    public void goBackHome() {
        if (this.reactContext != null) {
            Intent home = new Intent(Intent.ACTION_MAIN);
            home.addCategory(Intent.CATEGORY_HOME);
            this.reactContext.startActivity(home);
        }
    }

    @Nullable
    @Override
    public Map<String, Object> getConstants() {
        final Map<String, Object> constants = new HashMap<>();
        if (this.reactContext != null) {
            String bapingAppList = Settings.System.getString(this.reactContext.getContentResolver(), "persist.neo.isbapingapp.list");
            if (bapingAppList != null && bapingAppList.length() != 0) {
                boolean isBaPing = bapingAppList.contains(this.reactContext.getPackageName());
                constants.put("isBaPing", isBaPing);
            }else{
                constants.put("isBaPing", false);
            }
        }

        return constants;
    }

    @ReactMethod
    public void openSystemSettings(String settingName) {
        try {
            MainActivity activity = (MainActivity) reactContext.getCurrentActivity();
            if (activity != null) {
                if("DateSetting".equals(settingName)){
                    Intent intent = new Intent(Settings.ACTION_DATE_SETTINGS);
                    activity.startActivity(intent);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
}

