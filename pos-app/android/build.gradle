buildscript {
    ext {
        buildToolsVersion = "33.0.0"
        minSdkVersion = 23
        compileSdkVersion = 33
        targetSdkVersion = 33
        androidXCore = "1.5.0"
        googlePlayServicesVersion = "21.0.1"
        googlePlayServicesIidVersion = "17.0.0"
        kotlin_version = '1.6.10'
        ndkVersion = "24.0.8215888"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath 'com.google.gms:google-services:4.3.4'
        classpath "com.google.firebase:perf-plugin:1.3.4"
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version" // <- add this line

    }
}

allprojects {
    repositories {
        google()
        mavenLocal()
        mavenCentral()
        maven {
            url "https://jitpack.io"
        }
        flatDir { dirs "$rootDir/../libs/react-native-printer/android/libs" }
        flatDir { dirs "$rootDir/../libs/react-native-nfc/android/libs" }
        flatDir { dirs "$rootDir/../libs/react-native-log/android/libs" }
        flatDir { dirs "$rootDir/../libs/storehub-sunmi-service/android/libs" }
    }

}

apply plugin: "com.facebook.react.rootproject"

