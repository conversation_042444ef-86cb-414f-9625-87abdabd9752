rootProject.name = 'pos'

apply from: file("../node_modules/@react-native-community/cli-platform-android/native_modules.gradle"); applyNativeModulesSettingsGradle(settings)
include ':app'
includeBuild('../node_modules/@react-native/gradle-plugin')

include ':react-native-picker'
project(':react-native-picker').projectDir = new File(rootProject.projectDir, '../libs/react-native-picker/android')

include ':react-native-printer'
project(':react-native-printer').projectDir = new File(rootProject.projectDir, '../libs/react-native-printer/android')

include ':react-native-mrs'
project(':react-native-mrs').projectDir = new File(rootProject.projectDir, 	'../libs/react-native-mrs/android')

include ':react-native-scanner'
project(':react-native-scanner').projectDir = new File(rootProject.projectDir, '../libs/react-native-scanner/android')

include ':react-native-timezone'
project(':react-native-timezone').projectDir = new File(rootProject.projectDir, '../libs/react-native-timezone/android')

include ':react-native-s3'
project(':react-native-s3').projectDir = new File(settingsDir, '../libs/react-native-s3/android')

include ':react-native-geolocation-service'
project(':react-native-geolocation-service').projectDir = new File(rootProject.projectDir, '../node_modules/react-native-geolocation-service/android')

include ':jcore-react-native'
project(':jcore-react-native').projectDir = new File(rootProject.projectDir, '../node_modules/jcore-react-native/android')

include ':react-native-background-timer'
project(':react-native-background-timer').projectDir = new File(rootProject.projectDir, '../node_modules/react-native-background-timer/android')

include ':react-native-log'
project(':react-native-log').projectDir = new File(rootProject.projectDir, '../libs/react-native-log/android')

include ':react-native-anr-monitor'
project(':react-native-anr-monitor').projectDir = new File(rootProject.projectDir, '../libs/react-native-anr-monitor/android')

include ':react-native-peripheral'
project(':react-native-peripheral').projectDir = new File(rootProject.projectDir, '../libs/react-native-peripheral/android')

include ':react-native-code-push'
project(':react-native-code-push').projectDir = new File(rootProject.projectDir, '../node_modules/react-native-code-push/android/app')

include ':react-native-nfc'
project(':react-native-nfc').projectDir = new File(rootProject.projectDir, '../libs/react-native-nfc/android')

include ':storehub-imin-lcd'
project(':storehub-imin-lcd').projectDir = new File(rootProject.projectDir, '../libs/storehub-imin-lcd/android')

include ':storehub-sunmi-service'
project(':storehub-sunmi-service').projectDir = new File(rootProject.projectDir, '../libs/storehub-sunmi-service/android')

include ':storehub-ping'
project(':storehub-ping').projectDir = new File(rootProject.projectDir, '../libs/storehub-ping/android')

include ':react-native-uvc-camera'
project(':react-native-uvc-camera').projectDir = new File(rootProject.projectDir, '../libs/react-native-uvc-camera/android')

include ':react-native-permissions'
project(':react-native-permissions').projectDir = new File(rootProject.projectDir, '../node_modules/react-native-permissions/android')

include ':react-native-keyevent'
project(':react-native-keyevent').projectDir = new File(rootProject.projectDir, '../libs/react-native-keyevent/android')

include ':react-native-file-logger'
project(':react-native-file-logger').projectDir = new File(rootProject.projectDir, '../libs/react-native-file-logger/android')

