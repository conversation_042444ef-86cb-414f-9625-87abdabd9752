#!/bin/bash

# Script to find all .java and .kotlin files in specific directories and generate a single SHA256 hash
# Only searches in ./node_modules ./android ./ios ./libs

# Set the root directory (current directory by default)
ROOT_DIR="${1:-.}"

# Check if the directory exists
if [ ! -d "$ROOT_DIR" ]; then
    echo "Error: Directory '$ROOT_DIR' does not exist."
    exit 1
fi

# Define target directories
TARGET_DIRS=("$ROOT_DIR/android" "$ROOT_DIR/ios" "$ROOT_DIR/libs")

# Find all .java and .kotlin files only in specified directories
FILES=""
for dir in "${TARGET_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        echo "Searching in: $dir"
        DIR_FILES=$(find "$dir" -type f \( -name "*.java" -o -name "*.kotlin" \) 2>/dev/null)
        if [ -n "$DIR_FILES" ]; then
            FILES="$FILES$DIR_FILES"$'\n'
        fi
    else
        echo "Directory not found: $dir"
    fi
done

# Remove trailing newline and sort for consistent ordering
#FILES=$(echo "$FILES" | sed '/^$/d' | sort)

# Check if any files were found
if [ -z "$FILES" ]; then
    echo "No .java or .kotlin files found in the specified directories"
    exit 1
fi

echo ""
echo "Found files:"
#echo "$FILES"
echo ""

# Create a temporary file to store concatenated content
TEMP_FILE=$(mktemp)

# Concatenate all file contents into the temporary file
while IFS= read -r file; do
    if [ -n "$file" ]; then
#        echo "Processing: $file"
        cat "$file" >> "$TEMP_FILE"
    fi
done <<< "$FILES"

# Generate SHA256 hash of the concatenated content
HASH=$(sha256sum "$TEMP_FILE" | cut -d' ' -f1)

# Clean up temporary file
rm "$TEMP_FILE"

echo ""
echo "SHA256 hash of all .java and .kotlin files in specified directories: $HASH"