# Resolve react_native_pods.rb with node to allow for hoisting
require Pod::Executable.execute_command('node', ['-p',
  'require.resolve(
    "react-native/scripts/react_native_pods.rb",
    {paths: [process.argv[1]]},
  )', __dir__]).strip

# merge from release 20240425
require_relative '../node_modules/react-native-permissions/scripts/setup'

platform :ios, min_ios_version_supported
prepare_react_native_project!


# merge from release 20240425
# ⬇️ uncomment wanted permissions (Note that `pod install` must be re-executed each time you update this config)
setup_permissions([
  # 'AppTrackingTransparency',
  # 'Bluetooth',
  # 'Calendars',
  # 'CalendarsWriteOnly',
   'Camera',
  # 'Contacts',
  # 'FaceID',
  # 'LocationAccuracy',
  # 'LocationAlways',
  # 'LocationWhenInUse',
  # 'MediaLibrary',
  # 'Microphone',
  # 'Motion',
   'Notifications',
  # 'PhotoLibrary',
  # 'PhotoLibraryAddOnly',
  # 'Reminders',
  # 'Siri',
  # 'SpeechRecognition',
  # 'StoreKit',
])


# If you are using a `react-native-flipper` your iOS build will fail when `NO_FLIPPER=1` is set.
# because `react-native-flipper` depends on (FlipperKit,...) that will be excluded
#
# To fix this you can also exclude `react-native-flipper` using a `react-native.config.js`
# ```js
# module.exports = {
#   dependencies: {
#     ...(process.env.NO_FLIPPER ? { 'react-native-flipper': { platforms: { ios: null } } } : {}),
# ```
flipper_config = ENV['NO_FLIPPER'] == "1" ? FlipperConfiguration.disabled : FlipperConfiguration.enabled

linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

target 'pos' do
  config = use_native_modules!

  use_react_native!(
    :path => config[:reactNativePath],
    # to enable hermes on iOS, change `false` to `true` and then install pods
    :hermes_enabled => true
  )
  pod 'RNPeripheral', :path=>'../libs/react-native-peripheral'
  pod 'RNPrinter', :path=>'../libs/react-native-printer'
  pod 'RNS3', :path=>'../libs/react-native-s3'
  pod 'RNCfd', :path=>'../libs/react-native-cfd'
  pod 'RNPicker', :path=>'../libs/react-native-picker'
  pod 'RNScanner', :path=>'../libs/react-native-scanner'
  pod 'RNTimezone', :path=>'../libs/react-native-timezone'
  pod 'CocoaAsyncSocket', '~> 7.6.5', :modular_headers => true
  pod 'RNMrs', :path=>'../libs/react-native-mrs'
  pod 'RNFileLogger', :path=>'../libs/react-native-file-logger'
  pod 'LogService', :path=>'./Libraries/LogService'
  pod 'Firebase/RemoteConfig'
  pod 'Firebase/AnalyticsWithoutAdIdSupport'
  pod 'Firebase/Performance'
  pod 'RNCfd', :path=>'../libs/react-native-cfd'
  pod 'react-native-background-timer', :path => '../node_modules/react-native-background-timer'
  pod 'RNCustomKeyboardKit', :path => '../libs/react-native-custom-keyboard-kit/ios'
  pod 'IQKeyboardManager'
  pod 'RNFaceCapture', :path=>'../libs/react-native-face-capture'
  pod 'RNVectorIcons', :path => '../node_modules/react-native-vector-icons'
  pod 'RNPing', :path=>'../libs/storehub-ping'

  pod 'Firebase', :modular_headers => true
  pod 'FirebaseCoreInternal', :modular_headers => true
  pod 'GoogleUtilities', :modular_headers => true
  pod 'FirebaseCore', :modular_headers => true
  pod 'FirebaseABTesting', :modular_headers => true
  pod 'FirebaseInstallations', :modular_headers => true
  
  pod 'FirebaseSessions', :modular_headers => true
  pod 'FirebaseCoreExtension', :modular_headers => true
  pod 'GoogleDataTransport', :modular_headers => true
  pod 'nanopb', :modular_headers => true
  pod 'DoraemonKit/Core', :git => '**************:speam/DoKit.git', :configurations => ['Debug', 'Fat']


  target 'posTests' do
    inherit! :complete
    # Pods for testing
  end

  # Enables Flipper.
  #
  # Note that if you have use_frameworks! enabled, Flipper will not work and
  # you should disable the next line.
#  use_flipper!()

  post_install do |installer|
    # https://github.com/facebook/react-native/blob/main/packages/react-native/scripts/react_native_pods.rb#L197-L202
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false
    )
    # __apply_Xcode_12_5_M1_post_install_workaround(installer)

    installer.generated_projects.each do |project|
      project.targets.each do |target|
        target.build_configurations.each do |config|
          config.build_settings["DEVELOPMENT_TEAM"] = "X78BF7H5HG"
          # Fix Xcode 15 build error
          # RCT-Folly - struct hash_base : std::unary_function<T, std::size_t> {};
          # template <typename T>
          # ^ no template named 'unary_function' in namespace 'std'; did you mean '__unary_function'?
          # https://github.com/facebook/react-native/issues/37748
          config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= ['$(inherited)', '_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION']
          # Fix build error
          # Sandbox: rsync.samba(12698) deny(1) file-write-create
          config.build_settings['ENABLE_USER_SCRIPT_SANDBOXING'] = 'NO'
        end
      end
    end
    
    # Strips bitcode from the specified framework
    bitcode_strip_path = `xcrun --find bitcode_strip`.strip
    frameworks_to_strip = [
      "../libs/react-native-s3/ios/Frameworks/AWSCognito.framework/AWSCognito",
      "../libs/react-native-s3/ios/Frameworks/AWSCore.framework/AWSCore",
      "../libs/react-native-s3/ios/Frameworks/AWSS3.framework/AWSS3",
    ]
    frameworks_to_strip.each do |framework_relative_path|
      framework_path = File.join(Dir.pwd, framework_relative_path)
      command = "#{bitcode_strip_path} #{framework_path} -r -o #{framework_path}"
      puts "Stripping bitcode: #{command}"
      system(command)
    end

  end

#  X78BF7H5HG

#  post_install do |installer|
#    installer.generated_projects.each do |project|
#      project.targets.each do |target|
#          target.build_configurations.each do |config|
#              config.build_settings["DEVELOPMENT_TEAM"] = " Your Team ID  "
#           end
#      end
#    end
#  end

end
