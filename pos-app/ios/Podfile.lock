PODS:
  - AFNetworking (4.0.1):
    - AFNetworking/NSURLSession (= 4.0.1)
    - AFNetworking/Reachability (= 4.0.1)
    - AFNetworking/Security (= 4.0.1)
    - AFNetworking/Serialization (= 4.0.1)
    - AFNetworking/UIKit (= 4.0.1)
  - AFNetworking/NSURLSession (4.0.1):
    - AFNetworking/Reachability
    - AFNetworking/Security
    - AFNetworking/Serialization
  - AFNetworking/Reachability (4.0.1)
  - AFNetworking/Security (4.0.1)
  - AFNetworking/Serialization (4.0.1)
  - AFNetworking/UIKit (4.0.1):
    - AFNetworking/NSURLSession
  - Base64 (1.1.2)
  - boost (1.83.0)
  - BVLinearGradient (2.8.3):
    - React-Core
  - CocoaAsyncSocket (7.6.5)
  - CocoaLumberjack (3.8.5):
    - CocoaLumberjack/Core (= 3.8.5)
  - CocoaLumberjack/Core (3.8.5)
  - CodePush (8.3.1):
    - Base64 (~> 1.1)
    - JWT (~> 3.0.0-beta.12)
    - React-Core
    - SSZipArchive (~> 2.2.2)
  - DoraemonKit/Core (3.1.3):
    - FMDB
    - GCDWebServer
    - GCDWebServer/WebDAV
    - GCDWebServer/WebUploader
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.73.10)
  - FBReactNativeSpec (0.73.10):
    - RCT-Folly (= 2022.05.16.00)
    - RCTRequired (= 0.73.10)
    - RCTTypeSafety (= 0.73.10)
    - React-Core (= 0.73.10)
    - React-jsi (= 0.73.10)
    - ReactCommon/turbomodule/core (= 0.73.10)
  - Firebase (11.3.0):
    - Firebase/Core (= 11.3.0)
  - Firebase/AnalyticsWithoutAdIdSupport (11.3.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics/WithoutAdIdSupport (~> 11.3.0)
  - Firebase/Core (11.3.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 11.3.0)
  - Firebase/CoreOnly (11.3.0):
    - FirebaseCore (= 11.3.0)
  - Firebase/Performance (11.3.0):
    - Firebase/CoreOnly
    - FirebasePerformance (~> 11.3.0)
  - Firebase/RemoteConfig (11.3.0):
    - Firebase/CoreOnly
    - FirebaseRemoteConfig (~> 11.3.0)
  - FirebaseABTesting (11.3.0):
    - FirebaseCore (~> 11.0)
  - FirebaseAnalytics (11.3.0):
    - FirebaseAnalytics/AdIdSupport (= 11.3.0)
    - FirebaseCore (~> 11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/AdIdSupport (11.3.0):
    - FirebaseCore (~> 11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement (= 11.3.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/WithoutAdIdSupport (11.3.0):
    - FirebaseCore (~> 11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement/WithoutAdIdSupport (= 11.3.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseCore (11.3.0):
    - FirebaseCoreInternal (~> 11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreExtension (11.3.0):
    - FirebaseCore (~> 11.0)
  - FirebaseCoreInternal (11.3.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseInstallations (11.3.0):
    - FirebaseCore (~> 11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebasePerformance (11.3.0):
    - FirebaseCore (~> 11.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfig (~> 11.0)
    - FirebaseSessions (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - FirebaseRemoteConfig (11.3.0):
    - FirebaseABTesting (~> 11.0)
    - FirebaseCore (~> 11.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSharedSwift (~> 11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseRemoteConfigInterop (11.3.0)
  - FirebaseSessions (11.3.0):
    - FirebaseCore (~> 11.0)
    - FirebaseCoreExtension (~> 11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesSwift (~> 2.1)
  - FirebaseSharedSwift (11.3.0)
  - FMDB (2.7.12):
    - FMDB/standard (= 2.7.12)
  - FMDB/Core (2.7.12)
  - FMDB/standard (2.7.12):
    - FMDB/Core
  - fmt (6.2.1)
  - GCDWebServer (3.5.4):
    - GCDWebServer/Core (= 3.5.4)
  - GCDWebServer/Core (3.5.4)
  - GCDWebServer/WebDAV (3.5.4):
    - GCDWebServer/Core
  - GCDWebServer/WebUploader (3.5.4):
    - GCDWebServer/Core
  - glog (0.3.5)
  - GoogleAppMeasurement (11.3.0):
    - GoogleAppMeasurement/AdIdSupport (= 11.3.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/AdIdSupport (11.3.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 11.3.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (11.3.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleUtilities (8.0.2):
    - GoogleUtilities/AppDelegateSwizzler (= 8.0.2)
    - GoogleUtilities/Environment (= 8.0.2)
    - GoogleUtilities/Logger (= 8.0.2)
    - GoogleUtilities/MethodSwizzler (= 8.0.2)
    - GoogleUtilities/Network (= 8.0.2)
    - "GoogleUtilities/NSData+zlib (= 8.0.2)"
    - GoogleUtilities/Privacy (= 8.0.2)
    - GoogleUtilities/Reachability (= 8.0.2)
    - GoogleUtilities/SwizzlerTestHelpers (= 8.0.2)
    - GoogleUtilities/UserDefaults (= 8.0.2)
  - GoogleUtilities/AppDelegateSwizzler (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.0.2):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.0.2):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.0.2)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.0.2)
  - GoogleUtilities/Reachability (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/SwizzlerTestHelpers (8.0.2):
    - GoogleUtilities/MethodSwizzler
  - GoogleUtilities/UserDefaults (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - hermes-engine (0.73.10):
    - hermes-engine/Pre-built (= 0.73.10)
  - hermes-engine/Pre-built (0.73.10)
  - Intercom (16.5.9)
  - intercom-react-native (6.8.1):
    - Intercom (~> 16.5.9)
    - React-Core
  - IQKeyboardManager (6.5.19)
  - JitsiWebRTC (124.0.2)
  - JWT (3.0.0-beta.14):
    - Base64 (~> 1.1.2)
  - libevent (2.1.12)
  - libwebp (1.3.2):
    - libwebp/demux (= 1.3.2)
    - libwebp/mux (= 1.3.2)
    - libwebp/sharpyuv (= 1.3.2)
    - libwebp/webp (= 1.3.2)
  - libwebp/demux (1.3.2):
    - libwebp/webp
  - libwebp/mux (1.3.2):
    - libwebp/demux
  - libwebp/sharpyuv (1.3.2)
  - libwebp/webp (1.3.2):
    - libwebp/sharpyuv
  - LogService (1.0.0):
    - LogService/Core (= 1.0.0)
  - LogService/Core (1.0.0):
    - AFNetworking (~> 4.0)
  - Mixpanel-swift (4.2.0):
    - Mixpanel-swift/Complete (= 4.2.0)
  - Mixpanel-swift/Complete (4.2.0)
  - MixpanelReactNative (2.4.1):
    - Mixpanel-swift (= 4.2.0)
    - React-Core
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - RCT-Folly (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2022.05.16.00)
  - RCT-Folly/Default (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Fabric (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Futures (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - libevent
  - RCTRequired (0.73.10)
  - RCTTypeSafety (0.73.10):
    - FBLazyVector (= 0.73.10)
    - RCTRequired (= 0.73.10)
    - React-Core (= 0.73.10)
  - React (0.73.10):
    - React-Core (= 0.73.10)
    - React-Core/DevSupport (= 0.73.10)
    - React-Core/RCTWebSocket (= 0.73.10)
    - React-RCTActionSheet (= 0.73.10)
    - React-RCTAnimation (= 0.73.10)
    - React-RCTBlob (= 0.73.10)
    - React-RCTImage (= 0.73.10)
    - React-RCTLinking (= 0.73.10)
    - React-RCTNetwork (= 0.73.10)
    - React-RCTSettings (= 0.73.10)
    - React-RCTText (= 0.73.10)
    - React-RCTVibration (= 0.73.10)
  - React-callinvoker (0.73.10)
  - React-Codegen (0.73.10):
    - DoubleConversion
    - FBReactNativeSpec
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rncore
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.73.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default (= 0.73.10)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.73.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/Default (0.73.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/DevSupport (0.73.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default (= 0.73.10)
    - React-Core/RCTWebSocket (= 0.73.10)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector (= 0.73.10)
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.73.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.73.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.73.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.73.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.73.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.73.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.73.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.73.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.73.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTWebSocket (0.73.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default (= 0.73.10)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-CoreModules (0.73.10):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety (= 0.73.10)
    - React-Codegen
    - React-Core/CoreModulesHeaders (= 0.73.10)
    - React-jsi (= 0.73.10)
    - React-NativeModulesApple
    - React-RCTBlob
    - React-RCTImage (= 0.73.10)
    - ReactCommon
    - SocketRocket (= 0.6.1)
  - React-cxxreact (0.73.10):
    - boost (= 1.83.0)
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.10)
    - React-debug (= 0.73.10)
    - React-jsi (= 0.73.10)
    - React-jsinspector (= 0.73.10)
    - React-logger (= 0.73.10)
    - React-perflogger (= 0.73.10)
    - React-runtimeexecutor (= 0.73.10)
  - React-debug (0.73.10)
  - React-Fabric (0.73.10):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/animations (= 0.73.10)
    - React-Fabric/attributedstring (= 0.73.10)
    - React-Fabric/componentregistry (= 0.73.10)
    - React-Fabric/componentregistrynative (= 0.73.10)
    - React-Fabric/components (= 0.73.10)
    - React-Fabric/core (= 0.73.10)
    - React-Fabric/imagemanager (= 0.73.10)
    - React-Fabric/leakchecker (= 0.73.10)
    - React-Fabric/mounting (= 0.73.10)
    - React-Fabric/scheduler (= 0.73.10)
    - React-Fabric/telemetry (= 0.73.10)
    - React-Fabric/templateprocessor (= 0.73.10)
    - React-Fabric/textlayoutmanager (= 0.73.10)
    - React-Fabric/uimanager (= 0.73.10)
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/animations (0.73.10):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/attributedstring (0.73.10):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistry (0.73.10):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistrynative (0.73.10):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components (0.73.10):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/components/inputaccessory (= 0.73.10)
    - React-Fabric/components/legacyviewmanagerinterop (= 0.73.10)
    - React-Fabric/components/modal (= 0.73.10)
    - React-Fabric/components/rncore (= 0.73.10)
    - React-Fabric/components/root (= 0.73.10)
    - React-Fabric/components/safeareaview (= 0.73.10)
    - React-Fabric/components/scrollview (= 0.73.10)
    - React-Fabric/components/text (= 0.73.10)
    - React-Fabric/components/textinput (= 0.73.10)
    - React-Fabric/components/unimplementedview (= 0.73.10)
    - React-Fabric/components/view (= 0.73.10)
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/inputaccessory (0.73.10):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/legacyviewmanagerinterop (0.73.10):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/modal (0.73.10):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/rncore (0.73.10):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/root (0.73.10):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/safeareaview (0.73.10):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/scrollview (0.73.10):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/text (0.73.10):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/textinput (0.73.10):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/unimplementedview (0.73.10):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/view (0.73.10):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric/core (0.73.10):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/imagemanager (0.73.10):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/leakchecker (0.73.10):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/mounting (0.73.10):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/scheduler (0.73.10):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/telemetry (0.73.10):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/templateprocessor (0.73.10):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/textlayoutmanager (0.73.10):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/uimanager
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager (0.73.10):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-FabricImage (0.73.10):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired (= 0.73.10)
    - RCTTypeSafety (= 0.73.10)
    - React-Fabric
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsiexecutor (= 0.73.10)
    - React-logger
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - Yoga
  - React-graphics (0.73.10):
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - React-Core/Default (= 0.73.10)
    - React-utils
  - React-hermes (0.73.10):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - RCT-Folly/Futures (= 2022.05.16.00)
    - React-cxxreact (= 0.73.10)
    - React-jsi
    - React-jsiexecutor (= 0.73.10)
    - React-jsinspector (= 0.73.10)
    - React-perflogger (= 0.73.10)
  - React-ImageManager (0.73.10):
    - glog
    - RCT-Folly/Fabric
    - React-Core/Default
    - React-debug
    - React-Fabric
    - React-graphics
    - React-rendererdebug
    - React-utils
  - React-jserrorhandler (0.73.10):
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - React-debug
    - React-jsi
    - React-Mapbuffer
  - React-jsi (0.73.10):
    - boost (= 1.83.0)
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
  - React-jsiexecutor (0.73.10):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-cxxreact (= 0.73.10)
    - React-jsi (= 0.73.10)
    - React-perflogger (= 0.73.10)
  - React-jsinspector (0.73.10)
  - React-logger (0.73.10):
    - glog
  - React-Mapbuffer (0.73.10):
    - glog
    - React-debug
  - react-native-background-timer (2.4.1):
    - React-Core
  - react-native-config (1.4.1):
    - react-native-config/App (= 1.4.1)
  - react-native-config/App (1.4.1):
    - React-Core
  - react-native-geolocation-service (5.3.1):
    - React
  - react-native-get-random-values (1.11.0):
    - React-Core
  - react-native-netinfo (9.5.0):
    - React-Core
  - react-native-pager-view (5.4.25):
    - React-Core
  - react-native-performance-stats (0.2.3):
    - React-Core
  - react-native-safe-area-context (4.9.0):
    - React-Core
  - react-native-splash-screen (3.2.0):
    - React
  - react-native-update (10.28.4):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React
    - React-Core
    - react-native-update/HDiffPatch (= 10.28.4)
    - react-native-update/RCTPushy (= 10.28.4)
    - SSZipArchive
  - react-native-update/HDiffPatch (10.28.4):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React
    - React-Core
    - SSZipArchive
  - react-native-update/RCTPushy (10.28.4):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React
    - React-Core
    - SSZipArchive
  - react-native-view-shot (3.8.0):
    - React-Core
  - react-native-voice (3.2.4):
    - React-Core
  - react-native-webrtc (124.0.5):
    - JitsiWebRTC (~> 124.0.0)
    - React-Core
  - react-native-webview (13.8.4):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - React-nativeconfig (0.73.10)
  - React-NativeModulesApple (0.73.10):
    - glog
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.73.10)
  - React-RCTActionSheet (0.73.10):
    - React-Core/RCTActionSheetHeaders (= 0.73.10)
  - React-RCTAnimation (0.73.10):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTAnimationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTAppDelegate (0.73.10):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-hermes
    - React-nativeconfig
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-RCTNetwork
    - React-runtimescheduler
    - ReactCommon
  - React-RCTBlob (0.73.10):
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Codegen
    - React-Core/RCTBlobHeaders
    - React-Core/RCTWebSocket
    - React-jsi
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCommon
  - React-RCTFabric (0.73.10):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-nativeconfig
    - React-RCTImage
    - React-RCTText
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - Yoga
  - React-RCTImage (0.73.10):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTImageHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCommon
  - React-RCTLinking (0.73.10):
    - React-Codegen
    - React-Core/RCTLinkingHeaders (= 0.73.10)
    - React-jsi (= 0.73.10)
    - React-NativeModulesApple
    - ReactCommon
    - ReactCommon/turbomodule/core (= 0.73.10)
  - React-RCTNetwork (0.73.10):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTNetworkHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTSettings (0.73.10):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTSettingsHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTText (0.73.10):
    - React-Core/RCTTextHeaders (= 0.73.10)
    - Yoga
  - React-RCTVibration (0.73.10):
    - RCT-Folly (= 2022.05.16.00)
    - React-Codegen
    - React-Core/RCTVibrationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-rendererdebug (0.73.10):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - RCT-Folly (= 2022.05.16.00)
    - React-debug
  - React-rncore (0.73.10)
  - React-runtimeexecutor (0.73.10):
    - React-jsi (= 0.73.10)
  - React-runtimescheduler (0.73.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker
    - React-cxxreact
    - React-debug
    - React-jsi
    - React-rendererdebug
    - React-runtimeexecutor
    - React-utils
  - React-utils (0.73.10):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-debug
  - ReactCommon (0.73.10):
    - React-logger (= 0.73.10)
    - ReactCommon/turbomodule (= 0.73.10)
  - ReactCommon/turbomodule (0.73.10):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.10)
    - React-cxxreact (= 0.73.10)
    - React-jsi (= 0.73.10)
    - React-logger (= 0.73.10)
    - React-perflogger (= 0.73.10)
    - ReactCommon/turbomodule/bridging (= 0.73.10)
    - ReactCommon/turbomodule/core (= 0.73.10)
  - ReactCommon/turbomodule/bridging (0.73.10):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.10)
    - React-cxxreact (= 0.73.10)
    - React-jsi (= 0.73.10)
    - React-logger (= 0.73.10)
    - React-perflogger (= 0.73.10)
  - ReactCommon/turbomodule/core (0.73.10):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.10)
    - React-cxxreact (= 0.73.10)
    - React-jsi (= 0.73.10)
    - React-logger (= 0.73.10)
    - React-perflogger (= 0.73.10)
  - RealmJS (12.6.2):
    - React
  - RNCAsyncStorage (1.14.0):
    - React-Core
  - RNCfd (0.1.0):
    - RNCfd/Core (= 0.1.0)
  - RNCfd/Core (0.1.0):
    - React
  - RNCMaskedView (0.1.10):
    - React
  - RNCPushNotificationIOS (1.11.0):
    - React-Core
  - RNCustomKeyboardKit (1.0.0):
    - React
  - RNDeviceInfo (8.4.8):
    - React-Core
  - RNExternalDisplay (0.6.6):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - RNFaceCapture (0.1.0):
    - RNFaceCapture/Core (= 0.1.0)
  - RNFaceCapture/Core (0.1.0):
    - React
  - RNFastImage (8.5.11):
    - React-Core
    - SDWebImage (~> 5.11.1)
    - SDWebImageWebPCoder (~> 0.8.4)
  - RNFileLogger (0.6.0):
    - CocoaLumberjack (~> 3.8.5)
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
    - SSZipArchive (~> 2.2.2)
  - RNFlashList (1.7.2):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - RNFS (2.20.0):
    - React-Core
  - RNGestureHandler (2.16.0):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - RNLanguages (3.0.2):
    - React
  - RNLocalize (2.2.6):
    - React-Core
  - RNMrs (1.0.0):
    - RNMrs/Core (= 1.0.0)
  - RNMrs/Core (1.0.0):
    - React
  - RNPeripheral (1.0.0):
    - RNPeripheral/Core (= 1.0.0)
  - RNPeripheral/Core (1.0.0):
    - React
  - RNPermissions (4.1.5):
    - React-Core
  - RNPicker (4.3.7):
    - RNPicker/Core (= 4.3.7)
  - RNPicker/Core (4.3.7):
    - React
  - RNPing (0.1.0):
    - RNPing/Core (= 0.1.0)
  - RNPing/Core (0.1.0):
    - React
  - RNPrinter (1.0.0):
    - RNPrinter/Core (= 1.0.0)
  - RNPrinter/Core (1.0.0):
    - CocoaAsyncSocket (~> 7.6)
    - React
  - RNReanimated (3.8.1):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
    - ReactCommon/turbomodule/core
  - RNS3 (0.0.29):
    - RNS3/Core (= 0.0.29)
  - RNS3/Core (0.0.29):
    - React
  - RNScanner (1.0.0):
    - RNScanner/Core (= 1.0.0)
  - RNScanner/Core (1.0.0):
    - React
  - RNScreens (3.32.0):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
    - React-RCTImage
  - RNSentry (5.32.0):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
    - React-hermes
    - Sentry/HybridSDK (= 8.36.0)
  - RNSVG (12.5.1):
    - React-Core
  - RNTimezone (1.0.0):
    - React
  - RNVectorIcons (10.1.0):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - SDWebImage (5.11.1):
    - SDWebImage/Core (= 5.11.1)
  - SDWebImage/Core (5.11.1)
  - SDWebImageWebPCoder (0.8.5):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.10)
  - Sentry/HybridSDK (8.36.0)
  - sh-websocket-mdns-bridge (1.0.0):
    - sh-websocket-mdns-bridge/Core (= 1.0.0)
  - sh-websocket-mdns-bridge/Core (1.0.0):
    - React
    - SocketRocket
  - SocketRocket (0.6.1)
  - SSZipArchive (2.2.3)
  - Yoga (1.14.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - BVLinearGradient (from `../node_modules/react-native-linear-gradient`)
  - CocoaAsyncSocket (~> 7.6.5)
  - CodePush (from `../node_modules/react-native-code-push`)
  - "DoraemonKit/Core (from `**************:speam/DoKit.git`)"
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - Firebase
  - Firebase/AnalyticsWithoutAdIdSupport
  - Firebase/Performance
  - Firebase/RemoteConfig
  - FirebaseABTesting
  - FirebaseCore
  - FirebaseCoreExtension
  - FirebaseCoreInternal
  - FirebaseInstallations
  - FirebaseSessions
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - GoogleDataTransport
  - GoogleUtilities
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - "intercom-react-native (from `../node_modules/@intercom/intercom-react-native`)"
  - IQKeyboardManager
  - libevent (~> 2.1.12)
  - LogService (from `./Libraries/LogService`)
  - MixpanelReactNative (from `../node_modules/mixpanel-react-native`)
  - nanopb
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCT-Folly/Fabric (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-Fabric (from `../node_modules/react-native/ReactCommon`)
  - React-FabricImage (from `../node_modules/react-native/ReactCommon`)
  - React-graphics (from `../node_modules/react-native/ReactCommon/react/renderer/graphics`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-ImageManager (from `../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios`)
  - React-jserrorhandler (from `../node_modules/react-native/ReactCommon/jserrorhandler`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector-modern`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - React-Mapbuffer (from `../node_modules/react-native/ReactCommon`)
  - react-native-background-timer (from `../node_modules/react-native-background-timer`)
  - react-native-config (from `../node_modules/react-native-config`)
  - react-native-geolocation-service (from `../node_modules/react-native-geolocation-service`)
  - react-native-get-random-values (from `../node_modules/react-native-get-random-values`)
  - "react-native-netinfo (from `../node_modules/@react-native-community/netinfo`)"
  - react-native-pager-view (from `../node_modules/react-native-pager-view`)
  - react-native-performance-stats (from `../node_modules/react-native-performance-stats`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-splash-screen (from `../node_modules/react-native-splash-screen`)
  - react-native-update (from `../node_modules/react-native-update`)
  - react-native-view-shot (from `../node_modules/react-native-view-shot`)
  - "react-native-voice (from `../node_modules/@react-native-voice/voice`)"
  - react-native-webrtc (from `../node_modules/react-native-webrtc`)
  - react-native-webview (from `../node_modules/react-native-webview`)
  - React-nativeconfig (from `../node_modules/react-native/ReactCommon`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTFabric (from `../node_modules/react-native/React`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rendererdebug (from `../node_modules/react-native/ReactCommon/react/renderer/debug`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - RealmJS (from `../node_modules/realm`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - RNCfd (from `../libs/react-native-cfd`)
  - "RNCMaskedView (from `../node_modules/@react-native-community/masked-view`)"
  - "RNCPushNotificationIOS (from `../node_modules/@react-native-community/push-notification-ios`)"
  - RNCustomKeyboardKit (from `../libs/react-native-custom-keyboard-kit/ios`)
  - RNDeviceInfo (from `../node_modules/react-native-device-info`)
  - RNExternalDisplay (from `../node_modules/react-native-external-display`)
  - RNFaceCapture (from `../libs/react-native-face-capture`)
  - RNFastImage (from `../node_modules/react-native-fast-image`)
  - RNFileLogger (from `../libs/react-native-file-logger`)
  - "RNFlashList (from `../node_modules/@shopify/flash-list`)"
  - RNFS (from `../node_modules/react-native-fs`)
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNLanguages (from `../node_modules/react-native-languages`)
  - RNLocalize (from `../node_modules/react-native-localize`)
  - RNMrs (from `../libs/react-native-mrs`)
  - RNPeripheral (from `../libs/react-native-peripheral`)
  - RNPermissions (from `../node_modules/react-native-permissions`)
  - RNPicker (from `../libs/react-native-picker`)
  - RNPing (from `../libs/storehub-ping`)
  - RNPrinter (from `../libs/react-native-printer`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNS3 (from `../libs/react-native-s3`)
  - RNScanner (from `../libs/react-native-scanner`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - "RNSentry (from `../node_modules/@sentry/react-native`)"
  - RNSVG (from `../node_modules/react-native-svg`)
  - RNTimezone (from `../libs/react-native-timezone`)
  - RNVectorIcons (from `../node_modules/react-native-vector-icons`)
  - sh-websocket-mdns-bridge (from `../node_modules/sh-websocket-mdns-bridge`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - AFNetworking
    - Base64
    - CocoaAsyncSocket
    - CocoaLumberjack
    - Firebase
    - FirebaseABTesting
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebasePerformance
    - FirebaseRemoteConfig
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - FirebaseSharedSwift
    - FMDB
    - fmt
    - GCDWebServer
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleUtilities
    - Intercom
    - IQKeyboardManager
    - JitsiWebRTC
    - JWT
    - libevent
    - libwebp
    - Mixpanel-swift
    - nanopb
    - PromisesObjC
    - PromisesSwift
    - SDWebImage
    - SDWebImageWebPCoder
    - Sentry
    - SocketRocket
    - SSZipArchive

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  BVLinearGradient:
    :path: "../node_modules/react-native-linear-gradient"
  CodePush:
    :path: "../node_modules/react-native-code-push"
  DoraemonKit:
    :git: "**************:speam/DoKit.git"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2024-04-29-RNv0.73.8-644c8be78af1eae7c138fa4093fb87f0f4f8db85
  intercom-react-native:
    :path: "../node_modules/@intercom/intercom-react-native"
  LogService:
    :path: "./Libraries/LogService"
  MixpanelReactNative:
    :path: "../node_modules/mixpanel-react-native"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-Fabric:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricImage:
    :path: "../node_modules/react-native/ReactCommon"
  React-graphics:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/graphics"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-ImageManager:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios"
  React-jserrorhandler:
    :path: "../node_modules/react-native/ReactCommon/jserrorhandler"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  React-Mapbuffer:
    :path: "../node_modules/react-native/ReactCommon"
  react-native-background-timer:
    :path: "../node_modules/react-native-background-timer"
  react-native-config:
    :path: "../node_modules/react-native-config"
  react-native-geolocation-service:
    :path: "../node_modules/react-native-geolocation-service"
  react-native-get-random-values:
    :path: "../node_modules/react-native-get-random-values"
  react-native-netinfo:
    :path: "../node_modules/@react-native-community/netinfo"
  react-native-pager-view:
    :path: "../node_modules/react-native-pager-view"
  react-native-performance-stats:
    :path: "../node_modules/react-native-performance-stats"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-splash-screen:
    :path: "../node_modules/react-native-splash-screen"
  react-native-update:
    :path: "../node_modules/react-native-update"
  react-native-view-shot:
    :path: "../node_modules/react-native-view-shot"
  react-native-voice:
    :path: "../node_modules/@react-native-voice/voice"
  react-native-webrtc:
    :path: "../node_modules/react-native-webrtc"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-nativeconfig:
    :path: "../node_modules/react-native/ReactCommon"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTFabric:
    :path: "../node_modules/react-native/React"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rendererdebug:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/debug"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  RealmJS:
    :path: "../node_modules/realm"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCfd:
    :path: "../libs/react-native-cfd"
  RNCMaskedView:
    :path: "../node_modules/@react-native-community/masked-view"
  RNCPushNotificationIOS:
    :path: "../node_modules/@react-native-community/push-notification-ios"
  RNCustomKeyboardKit:
    :path: "../libs/react-native-custom-keyboard-kit/ios"
  RNDeviceInfo:
    :path: "../node_modules/react-native-device-info"
  RNExternalDisplay:
    :path: "../node_modules/react-native-external-display"
  RNFaceCapture:
    :path: "../libs/react-native-face-capture"
  RNFastImage:
    :path: "../node_modules/react-native-fast-image"
  RNFileLogger:
    :path: "../libs/react-native-file-logger"
  RNFlashList:
    :path: "../node_modules/@shopify/flash-list"
  RNFS:
    :path: "../node_modules/react-native-fs"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNLanguages:
    :path: "../node_modules/react-native-languages"
  RNLocalize:
    :path: "../node_modules/react-native-localize"
  RNMrs:
    :path: "../libs/react-native-mrs"
  RNPeripheral:
    :path: "../libs/react-native-peripheral"
  RNPermissions:
    :path: "../node_modules/react-native-permissions"
  RNPicker:
    :path: "../libs/react-native-picker"
  RNPing:
    :path: "../libs/storehub-ping"
  RNPrinter:
    :path: "../libs/react-native-printer"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNS3:
    :path: "../libs/react-native-s3"
  RNScanner:
    :path: "../libs/react-native-scanner"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNSentry:
    :path: "../node_modules/@sentry/react-native"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  RNTimezone:
    :path: "../libs/react-native-timezone"
  RNVectorIcons:
    :path: "../node_modules/react-native-vector-icons"
  sh-websocket-mdns-bridge:
    :path: "../node_modules/sh-websocket-mdns-bridge"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

CHECKOUT OPTIONS:
  DoraemonKit:
    :commit: f26c2d487f3c19e40d31f8907612727e251f4651
    :git: "**************:speam/DoKit.git"

SPEC CHECKSUMS:
  AFNetworking: 3bd23d814e976cd148d7d44c3ab78017b744cd58
  Base64: cecfb41a004124895a7bcee567a89bae5a89d49b
  boost: d3f49c53809116a5d38da093a8aa78bf551aed09
  BVLinearGradient: 880f91a7854faff2df62518f0281afb1c60d49a3
  CocoaAsyncSocket: 065fd1e645c7abab64f7a6a2007a48038fdc6a99
  CocoaLumberjack: 6a459bc897d6d80bd1b8c78482ec7ad05dffc3f0
  CodePush: d892f13c5012e9e2cb36640f4929654b7be21fe5
  DoraemonKit: 7448814a80d9ca56bc8db9105c464b7ec2e62623
  DoubleConversion: fea03f2699887d960129cc54bba7e52542b6f953
  FBLazyVector: c039b29a5b130f817a6b07dd7bc33830a969ab27
  FBReactNativeSpec: 0368d296aba294bab9067f575175422a4754db27
  Firebase: 5c575140761e22324806f401e38c483d58db2dec
  FirebaseABTesting: c4559fcd2eba9f6bdaf0599e2c37ded01c343e4c
  FirebaseAnalytics: ce1593872635a5ebd715d0d3937fab195991ecc9
  FirebaseCore: 8542de610f35f86196ba26cdb2544565a5157c8e
  FirebaseCoreExtension: 30bb063476ef66cd46925243d64ad8b2c8ac3264
  FirebaseCoreInternal: ac26d09a70c730e497936430af4e60fb0c68ec4e
  FirebaseInstallations: 58cf94dabf1e2bb2fa87725a9be5c2249171cda0
  FirebasePerformance: 83cea3aa69401248317353646d8b63cb315047c9
  FirebaseRemoteConfig: 5be2ca4f9870d475b39214210955fdaeecf7e5ca
  FirebaseRemoteConfigInterop: c3a5c31b3c22079f41ba1dc645df889d9ce38cb9
  FirebaseSessions: 655ff17f3cc1a635cbdc2d69b953878001f9e25b
  FirebaseSharedSwift: d39c2ad64a11a8d936ce25a42b00df47078bb59c
  FMDB: 728731dd336af3936ce00f91d9d8495f5718a0e6
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  GCDWebServer: 2c156a56c8226e2d5c0c3f208a3621ccffbe3ce4
  glog: c5d68082e772fa1c511173d6b30a9de2c05a69a2
  GoogleAppMeasurement: c8bac5f6ad85d3a0bdf2b9aee7fd484d6615d486
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleUtilities: 26a3abef001b6533cf678d3eb38fd3f614b7872d
  hermes-engine: 42a7a4cbe3e20050fb031f64aaaffbe0c0b4a38f
  Intercom: d9c81b3e45e6ecd9b2db2dc188d0521a40393f56
  intercom-react-native: 267c5e40d45cf663da091c4417b5306d8ecfe4ee
  IQKeyboardManager: c8665b3396bd0b79402b4c573eac345a31c7d485
  JitsiWebRTC: b47805ab5668be38e7ee60e2258f49badfe8e1d0
  JWT: ef71dfb03e1f842081e64dc42eef0e164f35d251
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  libwebp: 1786c9f4ff8a279e4dac1e8f385004d5fc253009
  LogService: 2b6f5127242645ecbeed125797ad4a4ea9726bd1
  Mixpanel-swift: e5dd85295923e6a875acf17ccbab8d2ecb10ea65
  MixpanelReactNative: 0101b8828c2f335c128850e71ab7d3b7adde089a
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  RCT-Folly: 7169b2b1c44399c76a47b5deaaba715eeeb476c0
  RCTRequired: 3c70a68a1acbfe8dec71921ddaf955ddceb6a8bd
  RCTTypeSafety: 6b502ff289b30da23609b707734e9c04089baa4f
  React: 8191d2b9cc390ec0304b6d96c2f9bc58f4397a18
  React-callinvoker: 95b82d703fdac0037485ab4eaca47dafa4258ec6
  React-Codegen: b05c5b21f50f8b67cbae257253d78d2148794457
  React-Core: 6e339dc0fd4de5588c59543ecf8a3ed5b4e2f8b2
  React-CoreModules: 507eaf1e5b73008e31c4de9da54f217a31ff1c20
  React-cxxreact: 1ca0528a6725ded005ae4e5112b55c24649a852c
  React-debug: 460fea66d43ad2c5ba39b6ab3886ec3cbe680ea3
  React-Fabric: 8cd4516a0be8a2b38d70f2e029173a35253e3cfa
  React-FabricImage: cc9c4325fa40eba64b87b1a0670896e1faa5ac99
  React-graphics: 700ba8c0f89634a18025bd1524357230a1922323
  React-hermes: cd9f6f6827ee0970a871dd8e900f9b5053d9c531
  React-ImageManager: 49620e9ff67538597a08d0ac9b8fc4a66a47d8e6
  React-jserrorhandler: 42d672b71f6b9f0bbba6f7b284d5ed2c3ac335bb
  React-jsi: fc680b7d89e7e96be5747684d2a1d25fa50d490c
  React-jsiexecutor: 644b1742d3fe8e30a6afc8c0f441ee6813eb28a2
  React-jsinspector: 71e5fb28b810fa199987e600ec8a6e17abee373e
  React-logger: 82babb1e3fad0e04750981f24e6c38f644a0bf7c
  React-Mapbuffer: 758750eb32350b01dd9c350010aecc46842047bd
  react-native-background-timer: 17ea5e06803401a379ebf1f20505b793ac44d0fe
  react-native-config: d8b45133fd13d4f23bd2064b72f6e2c08b2763ed
  react-native-geolocation-service: 608e1da71a1ac31b4de64d9ef2815f697978c55b
  react-native-get-random-values: 21325b2244dfa6b58878f51f9aa42821e7ba3d06
  react-native-netinfo: 48c5f79a84fbc3ba1d28a8b0d04adeda72885fa8
  react-native-pager-view: da490aa1f902c9a5aeecf0909cc975ad0e92e53e
  react-native-performance-stats: 6b797310a9f72412b8f9f010e0cf1de0274d1c42
  react-native-safe-area-context: b97eb6f9e3b7f437806c2ce5983f479f8eb5de4b
  react-native-splash-screen: 200d11d188e2e78cea3ad319964f6142b6384865
  react-native-update: f7226225cdef8a8d4bf313fad1f85c9fea027ac6
  react-native-view-shot: 6b7ed61d77d88580fed10954d45fad0eb2d47688
  react-native-voice: f5e8eec2278451d0017eb6a30a6ccc726aca34e0
  react-native-webrtc: 0f1c94069ff1eb9d8fb1618c2dc71f73542c8cfa
  react-native-webview: 9395e82c917d81407deb9b1fe53158dd6c8880ff
  React-nativeconfig: a596be0c98c050b80bc7e262d46a53d4df4ff712
  React-NativeModulesApple: 4829cef81c8a322029300b6ff1c5f28768fca85b
  React-perflogger: 27b852c78b7be9e0b326e3102c6551529f371f95
  React-RCTActionSheet: a9af61d47af3f2facfd7d8b6f913d60956779fc6
  React-RCTAnimation: ed02ba9e344aa38426660b73512988d86b31d398
  React-RCTAppDelegate: 0e85ca8ba0a451987b6627566490cdc82cdcff21
  React-RCTBlob: e766d412c0b500e87f368f3066701db6eae6a8e5
  React-RCTFabric: 26de835bb8ad95cb0153eee2efdcf147e8369b24
  React-RCTImage: 8721486d3e76e5fe8d15b5545a9a397174011297
  React-RCTLinking: 23e25137b35564a61ae30269b51b2eecc5e5aaa6
  React-RCTNetwork: f99ce4135920848ece60a9fbd3d3bd4cf1cb015a
  React-RCTSettings: c6eb7710df4b5a3306872ab2c65fcdcae3428459
  React-RCTText: 3fc5f2629ba3732e2c92c4c2046be2171e8b14d3
  React-RCTVibration: 491df80515fa282bac06bb8652cbf73118b15d8e
  React-rendererdebug: 6811c7970cd72f747833775b8de47c9d0d63df20
  React-rncore: 589b9a8a6cf7fe5a472bb54c73ba91c98d241b5d
  React-runtimeexecutor: 493b548fb6f1aaf94ba57cc9208ad5b89046e2be
  React-runtimescheduler: 639000d08f94ee18017fad4c691c572bb6dfbe50
  React-utils: c3a9bede4e9aa5c0a8184958cd4c09ff461fd60f
  ReactCommon: 92194c3d756b82bff5b75450544b2863b4df0d5f
  RealmJS: 385df5ee940d96f1de26b1dab153e325633d3052
  RNCAsyncStorage: 3cb5fb395208a87c49ca446c569c52d379476081
  RNCfd: db5e0c4894d10d21e5b3ae08c5bf2edd98249d1f
  RNCMaskedView: 5a8ec07677aa885546a0d98da336457e2bea557f
  RNCPushNotificationIOS: 64218f3c776c03d7408284a819b2abfda1834bc8
  RNCustomKeyboardKit: 28f1cf5506a852bd01c6e78ed51a71ef82b2dc0a
  RNDeviceInfo: 0400a6d0c94186d1120c3cbd97b23abc022187a9
  RNExternalDisplay: b021617d34707968afbd43e19f9d7ae6e238ac81
  RNFaceCapture: 4746a32b07295d798c299bae90c11e11df3f2dfc
  RNFastImage: 1f2cab428712a4baaf78d6169eaec7f622556dd7
  RNFileLogger: fefb7c7cf8ba45ed1b9bcd9ffe7a41626e145592
  RNFlashList: 556074dc4bc8b89e60635bd02447c3338bf88191
  RNFS: 4ac0f0ea233904cb798630b3c077808c06931688
  RNGestureHandler: bc2cdb2dc42facdf34992ae364b8a728e19a3686
  RNLanguages: 962e562af0d34ab1958d89bcfdb64fafc37c513e
  RNLocalize: d4b8af4e442d4bcca54e68fc687a2129b4d71a81
  RNMrs: 5d579d451db779f0fbd1101846bcb086c6e1ed1b
  RNPeripheral: 28c35463a73a54d11e815a6d72e9fe7de6da5250
  RNPermissions: 9fa74223844f437bc309e112994859dc47194829
  RNPicker: b4d5c9dc77f5e0e8475ae0004efc619ef83a7b70
  RNPing: 06d1f7a19c8d66fe8cb86f29fde6bdfac2c03106
  RNPrinter: 3f172b536d4bbff412ba313619746e15e0432027
  RNReanimated: 8a4d86eb951a4a99d8e86266dc71d7735c0c30a9
  RNS3: 56cb37b53595d691b267efc54ce44652987e363b
  RNScanner: 9234221d9d7ddafe815dac34173e8c2e100ab3b1
  RNScreens: e842cdccb23c0a084bd6307f6fa83fd1c1738029
  RNSentry: 3feba366b62cbf306d9f8be629beb516ed811f65
  RNSVG: d7d7bc8229af3842c9cfc3a723c815a52cdd1105
  RNTimezone: 2b684f742b5517b5dc11d288e869e2ef55ca9ee7
  RNVectorIcons: 96e8c5c45609932bb2f8347e1c709bcca0e95654
  SDWebImage: a7f831e1a65eb5e285e3fb046a23fcfbf08e696d
  SDWebImageWebPCoder: 908b83b6adda48effe7667cd2b7f78c897e5111d
  Sentry: f8374b5415bc38dfb5645941b3ae31230fbeae57
  sh-websocket-mdns-bridge: ca24574e95a7bbd50252721a58eec8b38880acbe
  SocketRocket: f32cd54efbe0f095c4d7594881e52619cfe80b17
  SSZipArchive: 62d4947b08730e4cda640473b0066d209ff033c9
  Yoga: 66a97477b94264cc4e49990c8fe6b153260d871d

PODFILE CHECKSUM: d3532c9f69a35bf38969242068e15ee46f7becfc

COCOAPODS: 1.15.2
