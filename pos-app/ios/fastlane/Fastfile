#########################################################################################################
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# Author  : Scott
# Dtae    : 20220317

def print_color(option)
#   30:黑                don't like it
#   31:红    for stderr
#   32:绿                don't like it
#   33:棕                don't like it
#   34:蓝    for stdout
#   35:紫红  for title
#   36:青                just so so
#   37:灰                can't see it
    puts "\033[" + option[:color] + "m" + option[:text] + "\033[0m"
end

default_platform(:ios)

# Global Parameter
      $project = "pos"
 $current_path = File.expand_path("..")

lane :package do |option| 
    if not option[:manual]
        # Auto increse build number
        increment_build_number
        version_number = get_version_number(xcodeproj: "#{$project}" + ".xcodeproj",target: ENV['scheme'])
        build_number = get_build_number(xcodeproj: "#{$project}" + ".xcodeproj")
        commit=last_git_commit
        notelogs=commit[:message]
        branch_name=git_branch
    else
        # Manual Control build number
        increment_version_number(
            version_number: option[:version]
        )
        increment_build_number(
            build_number: option[:build]
        )
        version_number=option[:version]
        build_number=option[:build]
        commit=last_git_commit
        notelogs=commit[:message]
        branch_name=git_branch
    end
    
    print_color(color:"31",text:">>> Version :" + version_number)
    print_color(color:"31",text:">>> Build   :" + build_number)
    # Package
    print_color(color:"31",text:">>> Archive")
    if ENV['export_method'] == "app-store"
        match(type: ENV['type'], app_identifier: ENV['identifier'])
        gym(
                scheme: ENV['scheme'],
         configuration: ENV['configuration'],
         export_method: ENV['export_method'],
                 clean: true,
                silent: true,
         export_xcargs: "-allowProvisioningUpdates",
        export_options: { compileBitcode: false},
 suppress_xcode_output: true)
    end
    if ENV['export_method'] == "ad-hoc"
        match(type: ENV['type'], force_for_new_devices: true, app_identifier: ENV['identifier'])
        gym(
                scheme: ENV['scheme'],
         configuration: ENV['configuration'],
         export_method: ENV['export_method'],
                 clean: true,
                silent: true,
       include_symbols: false,
       include_bitcode: false,
        export_xcargs: "-allowProvisioningUpdates",
        export_options: { compileBitcode: false},
 suppress_xcode_output: true)
    end
    if ENV['export_method'] == "development"
        match(type: ENV['type'], force_for_new_devices: true, app_identifier: ENV['identifier'])
        gym(
                scheme: ENV['scheme'],
         configuration: ENV['configuration'],
         export_method: ENV['export_method'],
                 clean: true,
                silent: true,
       include_symbols: false,
       include_bitcode: false,
        export_xcargs: "-allowProvisioningUpdates",
        export_options: { compileBitcode: false},
 suppress_xcode_output: true)
    end
    if ENV['export_method'] == "app-store"
        print_color(color:"31",text:">>> Submit to Apple Store")
        deliver(
            ipa: "#{$current_path}/pos.ipa",
            app_version: version_number,
            submit_for_review: false,
            phased_release: true,
            automatic_release: false,
            force: true,
            metadata_path:"./fastlane/metadata",
            skip_screenshots: true,
            skip_metadata: true,
            api_key_path: "./fastlane/RAAKV9CPXR.json",
            precheck_include_in_app_purchases: false
        )
    else
        print_color(color:"31",text:">>> Upload to pgyer")
        pgyer(
                api_key: "6e59832faa713db7de5df99d6b1eb509",
        update_description: "branch_name: " + branch_name + "\n" + " commit message:" +notelogs)
    end
end
