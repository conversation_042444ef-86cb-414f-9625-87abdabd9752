// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		00E356F31AD99517003FC87E /* posTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 00E356F21AD99517003FC87E /* posTests.m */; };
		13B07FBC1A68108700A75B9A /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.m */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		76335C5FC2DE74E1D69ADF28 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = D79980812E44EA236F6427CE /* PrivacyInfo.xcprivacy */; };
		7F2536B7695174997D4844FA /* libPods-pos.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 116C15363F117977554B8AF8 /* libPods-pos.a */; };
		81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		C44D92A4282B93A000D142C7 /* softScanBeep.wav in Resources */ = {isa = PBXBuildFile; fileRef = C44D9286282B93A000D142C7 /* softScanBeep.wav */; };
		C44D92A5282B93A000D142C7 /* softScanBeep.wav in Resources */ = {isa = PBXBuildFile; fileRef = C44D9286282B93A000D142C7 /* softScanBeep.wav */; };
		C44D92A6282B93A000D142C7 /* Debug.m in Sources */ = {isa = PBXBuildFile; fileRef = C44D929E282B93A000D142C7 /* Debug.m */; };
		C44D92A7282B93A000D142C7 /* Debug.m in Sources */ = {isa = PBXBuildFile; fileRef = C44D929E282B93A000D142C7 /* Debug.m */; };
		C44D92A8282B93A000D142C7 /* libScanApi.a in Frameworks */ = {isa = PBXBuildFile; fileRef = C44D92A1282B93A000D142C7 /* libScanApi.a */; };
		C44D92A9282B93A000D142C7 /* libScanApi.a in Frameworks */ = {isa = PBXBuildFile; fileRef = C44D92A1282B93A000D142C7 /* libScanApi.a */; };
		C44D92AA282B93A000D142C7 /* DeviceInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = C44D92A2282B93A000D142C7 /* DeviceInfo.m */; };
		C44D92AB282B93A000D142C7 /* DeviceInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = C44D92A2282B93A000D142C7 /* DeviceInfo.m */; };
		C44D92AC282B93A000D142C7 /* ScanApiHelper.mm in Sources */ = {isa = PBXBuildFile; fileRef = C44D92A3282B93A000D142C7 /* ScanApiHelper.mm */; };
		C44D92AD282B93A000D142C7 /* ScanApiHelper.mm in Sources */ = {isa = PBXBuildFile; fileRef = C44D92A3282B93A000D142C7 /* ScanApiHelper.mm */; };
		C4817A6D282BC1B300F79A64 /* ExternalAccessory.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C4817A6C282BC1B300F79A64 /* ExternalAccessory.framework */; };
		C4817A6F282BC1D200F79A64 /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C4817A6E282BC1D200F79A64 /* AVFoundation.framework */; };
		C4817A71282BC1DD00F79A64 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C4817A70282BC1DC00F79A64 /* AudioToolbox.framework */; };
		C4817A73282CB7B900F79A64 /* CoreBluetooth.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C4817A72282CB7B900F79A64 /* CoreBluetooth.framework */; };
		C4ABAE1C27BE68FC00C7B7F0 /* libresolv.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = C4ABAE1B27BE68D900C7B7F0 /* libresolv.tbd */; };
		C4B2E634285259080025BE9B /* CoreBluetooth.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C4B2E633285259080025BE9B /* CoreBluetooth.framework */; };
		C4B2E636285259110025BE9B /* ExternalAccessory.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C4B2E635285259110025BE9B /* ExternalAccessory.framework */; };
		C4CDD31B280D4E7F009FC72F /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = C4CDD319280D41CA009FC72F /* Localizable.strings */; };
		C4D63A1227EB30BD0033AEC5 /* beep.caf in Resources */ = {isa = PBXBuildFile; fileRef = C4D63A0B27EB30BD0033AEC5 /* beep.caf */; };
		C4D63A1327EB30BD0033AEC5 /* beepPaid.caf in Resources */ = {isa = PBXBuildFile; fileRef = C4D63A0C27EB30BD0033AEC5 /* beepPaid.caf */; };
		C4D63A1427EB30BD0033AEC5 /* Chalkboard-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C4D63A0D27EB30BD0033AEC5 /* Chalkboard-Bold.ttf */; };
		C4D63A1527EB30BD0033AEC5 /* LaunchImage.png in Resources */ = {isa = PBXBuildFile; fileRef = C4D63A0E27EB30BD0033AEC5 /* LaunchImage.png */; };
		C4D63A1627EB30BD0033AEC5 /* Roboto-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C4D63A0F27EB30BD0033AEC5 /* Roboto-Bold.ttf */; };
		C4D63A1727EB30BD0033AEC5 /* Noteworthy-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C4D63A1027EB30BD0033AEC5 /* Noteworthy-Bold.ttf */; };
		C4D63A2127EB30C70033AEC5 /* GoogleService-Info-RELEASE.plist in Resources */ = {isa = PBXBuildFile; fileRef = C4D63A1C27EB30C70033AEC5 /* GoogleService-Info-RELEASE.plist */; };
		C4D63A2227EB30C70033AEC5 /* GoogleService-Info-FAT.plist in Resources */ = {isa = PBXBuildFile; fileRef = C4D63A1D27EB30C70033AEC5 /* GoogleService-Info-FAT.plist */; };
		C4D63A2327EB30C70033AEC5 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = C4D63A1E27EB30C70033AEC5 /* GoogleService-Info.plist */; };
		C4D63A2427EB30C70033AEC5 /* FirebaseRemoteConfigService.m in Sources */ = {isa = PBXBuildFile; fileRef = C4D63A1F27EB30C70033AEC5 /* FirebaseRemoteConfigService.m */; };
		C4D63A2927EB49930033AEC5 /* AutoUpdate.m in Sources */ = {isa = PBXBuildFile; fileRef = C4D63A2827EB49930033AEC5 /* AutoUpdate.m */; };
		C4FF657227ECB53B0002D081 /* AppDelegate+Update.m in Sources */ = {isa = PBXBuildFile; fileRef = C4FF657127ECB53B0002D081 /* AppDelegate+Update.m */; };
		C841CA989FDF1CF312165B86 /* libPods-pos-posTests.a in Frameworks */ = {isa = PBXBuildFile; fileRef = AF83F4239D99EA6DCDF577C3 /* libPods-pos-posTests.a */; };
		F993ABAA29BC6A4700E442BA /* OpenSans-SemiboldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F993ABA029BC6A4600E442BA /* OpenSans-SemiboldItalic.ttf */; };
		F993ABAB29BC6A4700E442BA /* OpenSans-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F993ABA129BC6A4600E442BA /* OpenSans-Bold.ttf */; };
		F993ABAC29BC6A4700E442BA /* OpenSans-ExtraBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F993ABA229BC6A4600E442BA /* OpenSans-ExtraBold.ttf */; };
		F993ABAD29BC6A4700E442BA /* OpenSans-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F993ABA329BC6A4600E442BA /* OpenSans-Regular.ttf */; };
		F993ABAE29BC6A4700E442BA /* OpenSans-LightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F993ABA429BC6A4600E442BA /* OpenSans-LightItalic.ttf */; };
		F993ABAF29BC6A4700E442BA /* OpenSans-BoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F993ABA529BC6A4700E442BA /* OpenSans-BoldItalic.ttf */; };
		F993ABB029BC6A4700E442BA /* OpenSans-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F993ABA629BC6A4700E442BA /* OpenSans-Light.ttf */; };
		F993ABB129BC6A4700E442BA /* OpenSans-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F993ABA729BC6A4700E442BA /* OpenSans-Italic.ttf */; };
		F993ABB229BC6A4700E442BA /* OpenSans-ExtraBoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F993ABA829BC6A4700E442BA /* OpenSans-ExtraBoldItalic.ttf */; };
		F993ABB329BC6A4700E442BA /* OpenSans-Semibold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F993ABA929BC6A4700E442BA /* OpenSans-Semibold.ttf */; };
		F9EDC37B2B32A98400C908CB /* SHKeyChainManager.m in Sources */ = {isa = PBXBuildFile; fileRef = F9EDC37A2B32A98400C908CB /* SHKeyChainManager.m */; };
		F9F637EA2AEE3E4200E439D8 /* SHUserDefaultsManager.m in Sources */ = {isa = PBXBuildFile; fileRef = F9F637E92AEE3E4200E439D8 /* SHUserDefaultsManager.m */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		00E356F41AD99517003FC87E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 13B07F861A680F5B00A75B9A;
			remoteInfo = pos;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		C4ABAE5227C371BF00C7B7F0 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		00E356EE1AD99517003FC87E /* posTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = posTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		00E356F11AD99517003FC87E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		00E356F21AD99517003FC87E /* posTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = posTests.m; sourceTree = "<group>"; };
		116C15363F117977554B8AF8 /* libPods-pos.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-pos.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07F961A680F5B00A75B9A /* pos.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = pos.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = pos/AppDelegate.h; sourceTree = "<group>"; };
		13B07FB01A68108700A75B9A /* AppDelegate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AppDelegate.m; path = pos/AppDelegate.m; sourceTree = "<group>"; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = pos/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = pos/Info.plist; sourceTree = "<group>"; };
		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = pos/main.m; sourceTree = "<group>"; };
		37525FF54A124E27460D3788 /* Pods-pos-posTests.uat.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-pos-posTests.uat.xcconfig"; path = "Target Support Files/Pods-pos-posTests/Pods-pos-posTests.uat.xcconfig"; sourceTree = "<group>"; };
		5324750F5339972608A0289C /* Pods-pos.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-pos.release.xcconfig"; path = "Target Support Files/Pods-pos/Pods-pos.release.xcconfig"; sourceTree = "<group>"; };
		54869C1D8CD73596B85F9F35 /* Pods-pos.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-pos.debug.xcconfig"; path = "Target Support Files/Pods-pos/Pods-pos.debug.xcconfig"; sourceTree = "<group>"; };
		6036824F5C50578DE98C47BF /* Pods-pos.uat.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-pos.uat.xcconfig"; path = "Target Support Files/Pods-pos/Pods-pos.uat.xcconfig"; sourceTree = "<group>"; };
		79C68740CB7A92EA83BE4740 /* Pods-pos-posTests.fat.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-pos-posTests.fat.xcconfig"; path = "Target Support Files/Pods-pos-posTests/Pods-pos-posTests.fat.xcconfig"; sourceTree = "<group>"; };
		81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = LaunchScreen.storyboard; path = pos/LaunchScreen.storyboard; sourceTree = "<group>"; };
		A27C589BD98E9BC3C30582E9 /* Pods-pos-posTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-pos-posTests.debug.xcconfig"; path = "Target Support Files/Pods-pos-posTests/Pods-pos-posTests.debug.xcconfig"; sourceTree = "<group>"; };
		AF83F4239D99EA6DCDF577C3 /* libPods-pos-posTests.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-pos-posTests.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		AFEA007918495F5AEA6D702A /* Pods-pos.fat.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-pos.fat.xcconfig"; path = "Target Support Files/Pods-pos/Pods-pos.fat.xcconfig"; sourceTree = "<group>"; };
		BF8CAF1E5927C549F01F3F9A /* Pods-pos-posTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-pos-posTests.release.xcconfig"; path = "Target Support Files/Pods-pos-posTests/Pods-pos-posTests.release.xcconfig"; sourceTree = "<group>"; };
		C44D9285282B93A000D142C7 /* Debug.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Debug.h; sourceTree = "<group>"; };
		C44D9286282B93A000D142C7 /* softScanBeep.wav */ = {isa = PBXFileReference; lastKnownFileType = audio.wav; path = softScanBeep.wav; sourceTree = "<group>"; };
		C44D9288282B93A000D142C7 /* SktScanTypes.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SktScanTypes.h; sourceTree = "<group>"; };
		C44D9289282B93A000D142C7 /* SktScanPropIds.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SktScanPropIds.h; sourceTree = "<group>"; };
		C44D928A282B93A000D142C7 /* ISktScanObject.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ISktScanObject.h; sourceTree = "<group>"; };
		C44D928B282B93A000D142C7 /* SktScanAPI.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SktScanAPI.h; sourceTree = "<group>"; };
		C44D928C282B93A000D142C7 /* ISktScanSymbology.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ISktScanSymbology.h; sourceTree = "<group>"; };
		C44D928D282B93A000D142C7 /* SktClassFactory.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SktClassFactory.h; sourceTree = "<group>"; };
		C44D928E282B93A000D142C7 /* BtHelperWm.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BtHelperWm.h; sourceTree = "<group>"; };
		C44D928F282B93A000D142C7 /* ISktScanVersion.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ISktScanVersion.h; sourceTree = "<group>"; };
		C44D9290282B93A000D142C7 /* ScanAPI.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ScanAPI.h; sourceTree = "<group>"; };
		C44D9291282B93A000D142C7 /* ISktScanDecodedData.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ISktScanDecodedData.h; sourceTree = "<group>"; };
		C44D9292282B93A000D142C7 /* ISktScanMsg.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ISktScanMsg.h; sourceTree = "<group>"; };
		C44D9293282B93A000D142C7 /* ISktScanApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ISktScanApi.h; sourceTree = "<group>"; };
		C44D9294282B93A000D142C7 /* SktScanDeviceType.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SktScanDeviceType.h; sourceTree = "<group>"; };
		C44D9295282B93A000D142C7 /* SktScanCore.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SktScanCore.h; sourceTree = "<group>"; };
		C44D9296282B93A000D142C7 /* BtHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BtHelper.h; sourceTree = "<group>"; };
		C44D9297282B93A000D142C7 /* ISktScanEvent.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ISktScanEvent.h; sourceTree = "<group>"; };
		C44D9298282B93A000D142C7 /* ISktScanDevice.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ISktScanDevice.h; sourceTree = "<group>"; };
		C44D9299282B93A000D142C7 /* ISktScanProperty.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ISktScanProperty.h; sourceTree = "<group>"; };
		C44D929A282B93A000D142C7 /* GenBarcode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GenBarcode.h; sourceTree = "<group>"; };
		C44D929B282B93A000D142C7 /* SktScanErrors.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SktScanErrors.h; sourceTree = "<group>"; };
		C44D929C282B93A000D142C7 /* ScanApiHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ScanApiHelper.h; sourceTree = "<group>"; };
		C44D929D282B93A000D142C7 /* DeviceInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DeviceInfo.h; sourceTree = "<group>"; };
		C44D929E282B93A000D142C7 /* Debug.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = Debug.m; sourceTree = "<group>"; };
		C44D929F282B93A000D142C7 /* ScanApiIncludes.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ScanApiIncludes.h; sourceTree = "<group>"; };
		C44D92A1282B93A000D142C7 /* libScanApi.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = libScanApi.a; sourceTree = "<group>"; };
		C44D92A2282B93A000D142C7 /* DeviceInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DeviceInfo.m; sourceTree = "<group>"; };
		C44D92A3282B93A000D142C7 /* ScanApiHelper.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = ScanApiHelper.mm; sourceTree = "<group>"; };
		C4817A6C282BC1B300F79A64 /* ExternalAccessory.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = ExternalAccessory.framework; path = System/Library/Frameworks/ExternalAccessory.framework; sourceTree = SDKROOT; };
		C4817A6E282BC1D200F79A64 /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = System/Library/Frameworks/AVFoundation.framework; sourceTree = SDKROOT; };
		C4817A70282BC1DC00F79A64 /* AudioToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AudioToolbox.framework; path = System/Library/Frameworks/AudioToolbox.framework; sourceTree = SDKROOT; };
		C4817A72282CB7B900F79A64 /* CoreBluetooth.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreBluetooth.framework; path = System/Library/Frameworks/CoreBluetooth.framework; sourceTree = SDKROOT; };
		C4ABAE1B27BE68D900C7B7F0 /* libresolv.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libresolv.tbd; path = usr/lib/libresolv.tbd; sourceTree = SDKROOT; };
		C4ABAE4927C371BF00C7B7F0 /* AWSS3.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AWSS3.framework; path = "../libs/react-native-s3/ios/Frameworks/AWSS3.framework"; sourceTree = "<group>"; };
		C4ABAE4A27C371BF00C7B7F0 /* AWSCognito.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AWSCognito.framework; path = "../libs/react-native-s3/ios/Frameworks/AWSCognito.framework"; sourceTree = "<group>"; };
		C4ABAE4B27C371BF00C7B7F0 /* AWSCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AWSCore.framework; path = "../libs/react-native-s3/ios/Frameworks/AWSCore.framework"; sourceTree = "<group>"; };
		C4B2E633285259080025BE9B /* CoreBluetooth.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreBluetooth.framework; path = System/Library/Frameworks/CoreBluetooth.framework; sourceTree = SDKROOT; };
		C4B2E635285259110025BE9B /* ExternalAccessory.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = ExternalAccessory.framework; path = System/Library/Frameworks/ExternalAccessory.framework; sourceTree = SDKROOT; };
		C4B5239B27D7364E00ABF0A4 /* pos.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; name = pos.entitlements; path = pos/pos.entitlements; sourceTree = "<group>"; };
		C4CDD318280D41CA009FC72F /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/Localizable.strings; sourceTree = "<group>"; };
		C4CDD31A280D41CC009FC72F /* th */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = th; path = th.lproj/Localizable.strings; sourceTree = "<group>"; };
		C4D63A0B27EB30BD0033AEC5 /* beep.caf */ = {isa = PBXFileReference; lastKnownFileType = file; path = beep.caf; sourceTree = "<group>"; };
		C4D63A0C27EB30BD0033AEC5 /* beepPaid.caf */ = {isa = PBXFileReference; lastKnownFileType = file; path = beepPaid.caf; sourceTree = "<group>"; };
		C4D63A0D27EB30BD0033AEC5 /* Chalkboard-Bold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Chalkboard-Bold.ttf"; sourceTree = "<group>"; };
		C4D63A0E27EB30BD0033AEC5 /* LaunchImage.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = LaunchImage.png; sourceTree = "<group>"; };
		C4D63A0F27EB30BD0033AEC5 /* Roboto-Bold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Roboto-Bold.ttf"; sourceTree = "<group>"; };
		C4D63A1027EB30BD0033AEC5 /* Noteworthy-Bold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Noteworthy-Bold.ttf"; sourceTree = "<group>"; };
		C4D63A1C27EB30C70033AEC5 /* GoogleService-Info-RELEASE.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "GoogleService-Info-RELEASE.plist"; sourceTree = "<group>"; };
		C4D63A1D27EB30C70033AEC5 /* GoogleService-Info-FAT.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "GoogleService-Info-FAT.plist"; sourceTree = "<group>"; };
		C4D63A1E27EB30C70033AEC5 /* GoogleService-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		C4D63A1F27EB30C70033AEC5 /* FirebaseRemoteConfigService.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FirebaseRemoteConfigService.m; sourceTree = "<group>"; };
		C4D63A2027EB30C70033AEC5 /* FirebaseRemoteConfigService.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FirebaseRemoteConfigService.h; sourceTree = "<group>"; };
		C4D63A2727EB49930033AEC5 /* AutoUpdate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AutoUpdate.h; sourceTree = "<group>"; };
		C4D63A2827EB49930033AEC5 /* AutoUpdate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AutoUpdate.m; sourceTree = "<group>"; };
		C4FF657027ECB53B0002D081 /* AppDelegate+Update.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = "AppDelegate+Update.h"; path = "pos/AppDelegate+Update.h"; sourceTree = "<group>"; };
		C4FF657127ECB53B0002D081 /* AppDelegate+Update.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = "AppDelegate+Update.m"; path = "pos/AppDelegate+Update.m"; sourceTree = "<group>"; };
		C4FF657527ECC69B0002D081 /* FontAwesome.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = FontAwesome.ttf; sourceTree = "<group>"; };
		C4FF657627ECC69B0002D081 /* MaterialIcons.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = MaterialIcons.ttf; sourceTree = "<group>"; };
		D79980812E44EA236F6427CE /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xml; name = PrivacyInfo.xcprivacy; path = pos/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		F993ABA029BC6A4600E442BA /* OpenSans-SemiboldItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "OpenSans-SemiboldItalic.ttf"; sourceTree = "<group>"; };
		F993ABA129BC6A4600E442BA /* OpenSans-Bold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "OpenSans-Bold.ttf"; sourceTree = "<group>"; };
		F993ABA229BC6A4600E442BA /* OpenSans-ExtraBold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "OpenSans-ExtraBold.ttf"; sourceTree = "<group>"; };
		F993ABA329BC6A4600E442BA /* OpenSans-Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "OpenSans-Regular.ttf"; sourceTree = "<group>"; };
		F993ABA429BC6A4600E442BA /* OpenSans-LightItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "OpenSans-LightItalic.ttf"; sourceTree = "<group>"; };
		F993ABA529BC6A4700E442BA /* OpenSans-BoldItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "OpenSans-BoldItalic.ttf"; sourceTree = "<group>"; };
		F993ABA629BC6A4700E442BA /* OpenSans-Light.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "OpenSans-Light.ttf"; sourceTree = "<group>"; };
		F993ABA729BC6A4700E442BA /* OpenSans-Italic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "OpenSans-Italic.ttf"; sourceTree = "<group>"; };
		F993ABA829BC6A4700E442BA /* OpenSans-ExtraBoldItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "OpenSans-ExtraBoldItalic.ttf"; sourceTree = "<group>"; };
		F993ABA929BC6A4700E442BA /* OpenSans-Semibold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "OpenSans-Semibold.ttf"; sourceTree = "<group>"; };
		F9EDC3792B32A98400C908CB /* SHKeyChainManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SHKeyChainManager.h; sourceTree = "<group>"; };
		F9EDC37A2B32A98400C908CB /* SHKeyChainManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SHKeyChainManager.m; sourceTree = "<group>"; };
		F9F637E82AEE3E4200E439D8 /* SHUserDefaultsManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SHUserDefaultsManager.h; sourceTree = "<group>"; };
		F9F637E92AEE3E4200E439D8 /* SHUserDefaultsManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SHUserDefaultsManager.m; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		00E356EB1AD99517003FC87E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C44D92A9282B93A000D142C7 /* libScanApi.a in Frameworks */,
				C841CA989FDF1CF312165B86 /* libPods-pos-posTests.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C4B2E634285259080025BE9B /* CoreBluetooth.framework in Frameworks */,
				C4B2E636285259110025BE9B /* ExternalAccessory.framework in Frameworks */,
				C4ABAE1C27BE68FC00C7B7F0 /* libresolv.tbd in Frameworks */,
				C4817A6F282BC1D200F79A64 /* AVFoundation.framework in Frameworks */,
				C4817A6D282BC1B300F79A64 /* ExternalAccessory.framework in Frameworks */,
				C4817A73282CB7B900F79A64 /* CoreBluetooth.framework in Frameworks */,
				C44D92A8282B93A000D142C7 /* libScanApi.a in Frameworks */,
				C4817A71282BC1DD00F79A64 /* AudioToolbox.framework in Frameworks */,
				7F2536B7695174997D4844FA /* libPods-pos.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00E356EF1AD99517003FC87E /* posTests */ = {
			isa = PBXGroup;
			children = (
				00E356F21AD99517003FC87E /* posTests.m */,
				00E356F01AD99517003FC87E /* Supporting Files */,
			);
			path = posTests;
			sourceTree = "<group>";
		};
		00E356F01AD99517003FC87E /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				00E356F11AD99517003FC87E /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		13B07FAE1A68108700A75B9A /* pos */ = {
			isa = PBXGroup;
			children = (
				F9EDC3752B32A92600C908CB /* KeyChain */,
				F993AB9F29BC6A3400E442BA /* Fonts */,
				C4D63A2627EB49780033AEC5 /* RNAutoUpdate */,
				C4D63A1927EB30C70033AEC5 /* FireBaseRemoteConfig */,
				C4D63A0A27EB30BD0033AEC5 /* Resource */,
				C4B5239B27D7364E00ABF0A4 /* pos.entitlements */,
				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
				13B07FB01A68108700A75B9A /* AppDelegate.m */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */,
				13B07FB71A68108700A75B9A /* main.m */,
				C4FF657027ECB53B0002D081 /* AppDelegate+Update.h */,
				C4FF657127ECB53B0002D081 /* AppDelegate+Update.m */,
				C4CDD314280D4182009FC72F /* Localization */,
				D79980812E44EA236F6427CE /* PrivacyInfo.xcprivacy */,
			);
			name = pos;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				C4817A72282CB7B900F79A64 /* CoreBluetooth.framework */,
				C4817A70282BC1DC00F79A64 /* AudioToolbox.framework */,
				C4817A6E282BC1D200F79A64 /* AVFoundation.framework */,
				C4817A6C282BC1B300F79A64 /* ExternalAccessory.framework */,
				C44D9284282B93A000D142C7 /* ScanAPI */,
				C4ABAE4A27C371BF00C7B7F0 /* AWSCognito.framework */,
				C4ABAE4B27C371BF00C7B7F0 /* AWSCore.framework */,
				C4ABAE4927C371BF00C7B7F0 /* AWSS3.framework */,
				C4ABAE1B27BE68D900C7B7F0 /* libresolv.tbd */,
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				116C15363F117977554B8AF8 /* libPods-pos.a */,
				AF83F4239D99EA6DCDF577C3 /* libPods-pos-posTests.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		5FD02DAE26B8CA35E9C07679 /* Pods */ = {
			isa = PBXGroup;
			children = (
				54869C1D8CD73596B85F9F35 /* Pods-pos.debug.xcconfig */,
				5324750F5339972608A0289C /* Pods-pos.release.xcconfig */,
				A27C589BD98E9BC3C30582E9 /* Pods-pos-posTests.debug.xcconfig */,
				BF8CAF1E5927C549F01F3F9A /* Pods-pos-posTests.release.xcconfig */,
				AFEA007918495F5AEA6D702A /* Pods-pos.fat.xcconfig */,
				6036824F5C50578DE98C47BF /* Pods-pos.uat.xcconfig */,
				79C68740CB7A92EA83BE4740 /* Pods-pos-posTests.fat.xcconfig */,
				37525FF54A124E27460D3788 /* Pods-pos-posTests.uat.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				13B07FAE1A68108700A75B9A /* pos */,
				00E356EF1AD99517003FC87E /* posTests */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				5FD02DAE26B8CA35E9C07679 /* Pods */,
				C41D3F8F286D8C1A006D3699 /* Recovered References */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* pos.app */,
				00E356EE1AD99517003FC87E /* posTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		C41D3F8F286D8C1A006D3699 /* Recovered References */ = {
			isa = PBXGroup;
			children = (
				C4B2E633285259080025BE9B /* CoreBluetooth.framework */,
				C4B2E635285259110025BE9B /* ExternalAccessory.framework */,
			);
			name = "Recovered References";
			sourceTree = "<group>";
		};
		C44D9284282B93A000D142C7 /* ScanAPI */ = {
			isa = PBXGroup;
			children = (
				C44D9285282B93A000D142C7 /* Debug.h */,
				C44D9286282B93A000D142C7 /* softScanBeep.wav */,
				C44D9287282B93A000D142C7 /* include */,
				C44D929C282B93A000D142C7 /* ScanApiHelper.h */,
				C44D929D282B93A000D142C7 /* DeviceInfo.h */,
				C44D929E282B93A000D142C7 /* Debug.m */,
				C44D929F282B93A000D142C7 /* ScanApiIncludes.h */,
				C44D92A0282B93A000D142C7 /* lib */,
				C44D92A2282B93A000D142C7 /* DeviceInfo.m */,
				C44D92A3282B93A000D142C7 /* ScanApiHelper.mm */,
			);
			name = ScanAPI;
			path = pos/ScanAPI;
			sourceTree = "<group>";
		};
		C44D9287282B93A000D142C7 /* include */ = {
			isa = PBXGroup;
			children = (
				C44D9288282B93A000D142C7 /* SktScanTypes.h */,
				C44D9289282B93A000D142C7 /* SktScanPropIds.h */,
				C44D928A282B93A000D142C7 /* ISktScanObject.h */,
				C44D928B282B93A000D142C7 /* SktScanAPI.h */,
				C44D928C282B93A000D142C7 /* ISktScanSymbology.h */,
				C44D928D282B93A000D142C7 /* SktClassFactory.h */,
				C44D928E282B93A000D142C7 /* BtHelperWm.h */,
				C44D928F282B93A000D142C7 /* ISktScanVersion.h */,
				C44D9290282B93A000D142C7 /* ScanAPI.h */,
				C44D9291282B93A000D142C7 /* ISktScanDecodedData.h */,
				C44D9292282B93A000D142C7 /* ISktScanMsg.h */,
				C44D9293282B93A000D142C7 /* ISktScanApi.h */,
				C44D9294282B93A000D142C7 /* SktScanDeviceType.h */,
				C44D9295282B93A000D142C7 /* SktScanCore.h */,
				C44D9296282B93A000D142C7 /* BtHelper.h */,
				C44D9297282B93A000D142C7 /* ISktScanEvent.h */,
				C44D9298282B93A000D142C7 /* ISktScanDevice.h */,
				C44D9299282B93A000D142C7 /* ISktScanProperty.h */,
				C44D929A282B93A000D142C7 /* GenBarcode.h */,
				C44D929B282B93A000D142C7 /* SktScanErrors.h */,
			);
			path = include;
			sourceTree = "<group>";
		};
		C44D92A0282B93A000D142C7 /* lib */ = {
			isa = PBXGroup;
			children = (
				C44D92A1282B93A000D142C7 /* libScanApi.a */,
			);
			path = lib;
			sourceTree = "<group>";
		};
		C4CDD314280D4182009FC72F /* Localization */ = {
			isa = PBXGroup;
			children = (
				C4CDD319280D41CA009FC72F /* Localizable.strings */,
			);
			name = Localization;
			path = pos/Localization;
			sourceTree = "<group>";
		};
		C4D63A0A27EB30BD0033AEC5 /* Resource */ = {
			isa = PBXGroup;
			children = (
				C4FF657527ECC69B0002D081 /* FontAwesome.ttf */,
				C4FF657627ECC69B0002D081 /* MaterialIcons.ttf */,
				C4D63A0B27EB30BD0033AEC5 /* beep.caf */,
				C4D63A0C27EB30BD0033AEC5 /* beepPaid.caf */,
				C4D63A0D27EB30BD0033AEC5 /* Chalkboard-Bold.ttf */,
				C4D63A0E27EB30BD0033AEC5 /* LaunchImage.png */,
				C4D63A0F27EB30BD0033AEC5 /* Roboto-Bold.ttf */,
				C4D63A1027EB30BD0033AEC5 /* Noteworthy-Bold.ttf */,
			);
			name = Resource;
			path = pos/Resource;
			sourceTree = "<group>";
		};
		C4D63A1927EB30C70033AEC5 /* FireBaseRemoteConfig */ = {
			isa = PBXGroup;
			children = (
				C4D63A1A27EB30C70033AEC5 /* Settings */,
				C4D63A1F27EB30C70033AEC5 /* FirebaseRemoteConfigService.m */,
				C4D63A2027EB30C70033AEC5 /* FirebaseRemoteConfigService.h */,
				F9F637E82AEE3E4200E439D8 /* SHUserDefaultsManager.h */,
				F9F637E92AEE3E4200E439D8 /* SHUserDefaultsManager.m */,
			);
			name = FireBaseRemoteConfig;
			path = pos/FireBaseRemoteConfig;
			sourceTree = "<group>";
		};
		C4D63A1A27EB30C70033AEC5 /* Settings */ = {
			isa = PBXGroup;
			children = (
				C4D63A1B27EB30C70033AEC5 /* Original */,
				C4D63A1E27EB30C70033AEC5 /* GoogleService-Info.plist */,
			);
			path = Settings;
			sourceTree = "<group>";
		};
		C4D63A1B27EB30C70033AEC5 /* Original */ = {
			isa = PBXGroup;
			children = (
				C4D63A1C27EB30C70033AEC5 /* GoogleService-Info-RELEASE.plist */,
				C4D63A1D27EB30C70033AEC5 /* GoogleService-Info-FAT.plist */,
			);
			path = Original;
			sourceTree = "<group>";
		};
		C4D63A2627EB49780033AEC5 /* RNAutoUpdate */ = {
			isa = PBXGroup;
			children = (
				C4D63A2727EB49930033AEC5 /* AutoUpdate.h */,
				C4D63A2827EB49930033AEC5 /* AutoUpdate.m */,
			);
			name = RNAutoUpdate;
			path = pos/FireBaseRemoteConfig/RNAutoUpdate;
			sourceTree = "<group>";
		};
		F993AB9F29BC6A3400E442BA /* Fonts */ = {
			isa = PBXGroup;
			children = (
				F993ABA129BC6A4600E442BA /* OpenSans-Bold.ttf */,
				F993ABA529BC6A4700E442BA /* OpenSans-BoldItalic.ttf */,
				F993ABA229BC6A4600E442BA /* OpenSans-ExtraBold.ttf */,
				F993ABA829BC6A4700E442BA /* OpenSans-ExtraBoldItalic.ttf */,
				F993ABA729BC6A4700E442BA /* OpenSans-Italic.ttf */,
				F993ABA629BC6A4700E442BA /* OpenSans-Light.ttf */,
				F993ABA429BC6A4600E442BA /* OpenSans-LightItalic.ttf */,
				F993ABA329BC6A4600E442BA /* OpenSans-Regular.ttf */,
				F993ABA929BC6A4700E442BA /* OpenSans-Semibold.ttf */,
				F993ABA029BC6A4600E442BA /* OpenSans-SemiboldItalic.ttf */,
			);
			path = Fonts;
			sourceTree = "<group>";
		};
		F9EDC3752B32A92600C908CB /* KeyChain */ = {
			isa = PBXGroup;
			children = (
				F9EDC3792B32A98400C908CB /* SHKeyChainManager.h */,
				F9EDC37A2B32A98400C908CB /* SHKeyChainManager.m */,
			);
			path = KeyChain;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		00E356ED1AD99517003FC87E /* posTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "posTests" */;
			buildPhases = (
				053530ACE6DC108BDE76448A /* [CP] Check Pods Manifest.lock */,
				00E356EA1AD99517003FC87E /* Sources */,
				00E356EB1AD99517003FC87E /* Frameworks */,
				00E356EC1AD99517003FC87E /* Resources */,
				D678ED662B8CDE0B7B37B063 /* [CP] Embed Pods Frameworks */,
				94CD0923D91B42CD07138E33 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				00E356F51AD99517003FC87E /* PBXTargetDependency */,
			);
			name = posTests;
			productName = posTests;
			productReference = 00E356EE1AD99517003FC87E /* posTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		13B07F861A680F5B00A75B9A /* pos */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "pos" */;
			buildPhases = (
				E1C3B47447CC52E9FAE9073D /* [CP] Check Pods Manifest.lock */,
				FD10A7F022414F080027D42C /* Start Packager */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				C48C245027E483E9003FE4D0 /* Select GoogleService-Info.plist */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				0D446A9200C6B1374606E608 /* [CP] Embed Pods Frameworks */,
				C4ABAE5227C371BF00C7B7F0 /* Embed Frameworks */,
				C4B523DA27D85B6800ABF0A4 /* Upload Debug Symbols to Sentry */,
				2A1EB17501F001DFB7FB8943 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = pos;
			productName = pos;
			productReference = 13B07F961A680F5B00A75B9A /* pos.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1210;
				TargetAttributes = {
					00E356ED1AD99517003FC87E = {
						CreatedOnToolsVersion = 6.2;
						TestTargetID = 13B07F861A680F5B00A75B9A;
					};
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1120;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "pos" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				th,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* pos */,
				00E356ED1AD99517003FC87E /* posTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		00E356EC1AD99517003FC87E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C44D92A5282B93A000D142C7 /* softScanBeep.wav in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F993ABAA29BC6A4700E442BA /* OpenSans-SemiboldItalic.ttf in Resources */,
				C4D63A2227EB30C70033AEC5 /* GoogleService-Info-FAT.plist in Resources */,
				C4D63A1727EB30BD0033AEC5 /* Noteworthy-Bold.ttf in Resources */,
				F993ABAF29BC6A4700E442BA /* OpenSans-BoldItalic.ttf in Resources */,
				F993ABAB29BC6A4700E442BA /* OpenSans-Bold.ttf in Resources */,
				C4D63A2127EB30C70033AEC5 /* GoogleService-Info-RELEASE.plist in Resources */,
				C4CDD31B280D4E7F009FC72F /* Localizable.strings in Resources */,
				F993ABB229BC6A4700E442BA /* OpenSans-ExtraBoldItalic.ttf in Resources */,
				C4D63A1627EB30BD0033AEC5 /* Roboto-Bold.ttf in Resources */,
				C4D63A2327EB30C70033AEC5 /* GoogleService-Info.plist in Resources */,
				C4D63A1427EB30BD0033AEC5 /* Chalkboard-Bold.ttf in Resources */,
				F993ABB129BC6A4700E442BA /* OpenSans-Italic.ttf in Resources */,
				F993ABAD29BC6A4700E442BA /* OpenSans-Regular.ttf in Resources */,
				C4D63A1227EB30BD0033AEC5 /* beep.caf in Resources */,
				F993ABAC29BC6A4700E442BA /* OpenSans-ExtraBold.ttf in Resources */,
				C4D63A1527EB30BD0033AEC5 /* LaunchImage.png in Resources */,
				F993ABAE29BC6A4700E442BA /* OpenSans-LightItalic.ttf in Resources */,
				81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */,
				F993ABB329BC6A4700E442BA /* OpenSans-Semibold.ttf in Resources */,
				C44D92A4282B93A000D142C7 /* softScanBeep.wav in Resources */,
				C4D63A1327EB30BD0033AEC5 /* beepPaid.caf in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				F993ABB029BC6A4700E442BA /* OpenSans-Light.ttf in Resources */,
				76335C5FC2DE74E1D69ADF28 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\nWITH_ENVIRONMENT=\"../node_modules/react-native/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"../node_modules/react-native/scripts/react-native-xcode.sh\"\nSENTRY_XCODE=\"../node_modules/@sentry/react-native/scripts/sentry-xcode.sh\"\nBUNDLE_REACT_NATIVE=\"/bin/sh $SENTRY_XCODE $REACT_NATIVE_XCODE\"\n# RN 0.69+\n/bin/sh -c \"$WITH_ENVIRONMENT \\\"$BUNDLE_REACT_NATIVE\\\"\"\n";
		};
		053530ACE6DC108BDE76448A /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-pos-posTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		0D446A9200C6B1374606E608 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-pos/Pods-pos-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-pos/Pods-pos-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-pos/Pods-pos-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		2A1EB17501F001DFB7FB8943 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-pos/Pods-pos-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-pos/Pods-pos-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-pos/Pods-pos-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		94CD0923D91B42CD07138E33 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-pos-posTests/Pods-pos-posTests-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-pos-posTests/Pods-pos-posTests-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-pos-posTests/Pods-pos-posTests-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		C48C245027E483E9003FE4D0 /* Select GoogleService-Info.plist */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Select GoogleService-Info.plist";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\n# Type a script or drag a script file from your workspace to insert its path.\nrm \"$SRCROOT/pos/FireBaseRemoteConfig/Settings/GoogleService-Info.plist\"\ncp \"$SRCROOT/pos/FireBaseRemoteConfig/Settings/Original/\"$FIREBASE_CONFIG_FILE_NAME\"\" \"$SRCROOT/pos/FireBaseRemoteConfig/Settings/GoogleService-Info.plist\"\n";
		};
		C4B523DA27D85B6800ABF0A4 /* Upload Debug Symbols to Sentry */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Upload Debug Symbols to Sentry";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# Type a script or drag a script file from your workspace to insert its path.\nexport SENTRY_PROPERTIES=sentry.properties\n../node_modules/@sentry/cli/bin/sentry-cli debug-files upload \"$DWARF_DSYM_FOLDER_PATH\"\n";
		};
		D678ED662B8CDE0B7B37B063 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-pos-posTests/Pods-pos-posTests-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-pos-posTests/Pods-pos-posTests-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-pos-posTests/Pods-pos-posTests-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		E1C3B47447CC52E9FAE9073D /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-pos-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		FD10A7F022414F080027D42C /* Start Packager */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Start Packager";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export RCT_METRO_PORT=\"${RCT_METRO_PORT:=8081}\"\necho \"export RCT_METRO_PORT=${RCT_METRO_PORT}\" > \"${SRCROOT}/../node_modules/react-native/scripts/.packager.env\"\nif [ -z \"${RCT_NO_LAUNCH_PACKAGER+xxx}\" ] ; then\n  if nc -w 5 -z localhost ${RCT_METRO_PORT} ; then\n    if ! curl -s \"http://localhost:${RCT_METRO_PORT}/status\" | grep -q \"packager-status:running\" ; then\n      echo \"Port ${RCT_METRO_PORT} already in use, packager is either not running or not running correctly\"\n      exit 2\n    fi\n  else\n    open \"$SRCROOT/../node_modules/react-native/scripts/launchPackager.command\" || echo \"Can't start packager automatically\"\n  fi\nfi\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		00E356EA1AD99517003FC87E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C44D92AB282B93A000D142C7 /* DeviceInfo.m in Sources */,
				00E356F31AD99517003FC87E /* posTests.m in Sources */,
				C44D92A7282B93A000D142C7 /* Debug.m in Sources */,
				C44D92AD282B93A000D142C7 /* ScanApiHelper.mm in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				13B07FBC1A68108700A75B9A /* AppDelegate.m in Sources */,
				C44D92AA282B93A000D142C7 /* DeviceInfo.m in Sources */,
				F9EDC37B2B32A98400C908CB /* SHKeyChainManager.m in Sources */,
				13B07FC11A68108700A75B9A /* main.m in Sources */,
				C4D63A2927EB49930033AEC5 /* AutoUpdate.m in Sources */,
				C44D92A6282B93A000D142C7 /* Debug.m in Sources */,
				C4FF657227ECB53B0002D081 /* AppDelegate+Update.m in Sources */,
				F9F637EA2AEE3E4200E439D8 /* SHUserDefaultsManager.m in Sources */,
				C4D63A2427EB30C70033AEC5 /* FirebaseRemoteConfigService.m in Sources */,
				C44D92AC282B93A000D142C7 /* ScanApiHelper.mm in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		00E356F51AD99517003FC87E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 13B07F861A680F5B00A75B9A /* pos */;
			targetProxy = 00E356F41AD99517003FC87E /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		C4CDD319280D41CA009FC72F /* Localizable.strings */ = {
			isa = PBXVariantGroup;
			children = (
				C4CDD318280D41CA009FC72F /* en */,
				C4CDD31A280D41CC009FC72F /* th */,
			);
			name = Localizable.strings;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		00E356F61AD99517003FC87E /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A27C589BD98E9BC3C30582E9 /* Pods-pos-posTests.debug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = posTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"$(inherited)",
					"$(PROJECT_DIR)/pos/ScanAPI/lib",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/pos.app/pos";
			};
			name = Debug;
		};
		00E356F71AD99517003FC87E /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = BF8CAF1E5927C549F01F3F9A /* Pods-pos-posTests.release.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				COPY_PHASE_STRIP = NO;
				INFOPLIST_FILE = posTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"$(inherited)",
					"$(PROJECT_DIR)/pos/ScanAPI/lib",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/pos.app/pos";
			};
			name = Release;
		};
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 54869C1D8CD73596B85F9F35 /* Pods-pos.debug.xcconfig */;
			buildSettings = {
				APP_DISPLAY_NAME = Debug_StoreHub;
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon-1";
				CLANG_ENABLE_MODULES = YES;
				CODEPUSH_KEY = "";
				CODE_SIGN_ENTITLEMENTS = pos/pos.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = X78BF7H5HG;
				ENABLE_BITCODE = NO;
				FIREBASE_CONFIG_FILE_NAME = "GoogleService-Info-FAT.plist";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_ROOT}/../../node_modules/realm/react-native/ios\"",
					"\"${PODS_ROOT}/Flipper-DoubleConversion/Frameworks\"",
					"\"${PODS_ROOT}/Intercom\"",
					"\"${PODS_ROOT}/OpenSSL-Universal/Frameworks\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/Flipper-DoubleConversion\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/Intercom\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/OpenSSL-Universal\"",
				);
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_ROOT}/Headers/Public\"",
					"\"${PODS_ROOT}/Headers/Public/CocoaAsyncSocket\"",
					"\"${PODS_ROOT}/Headers/Public/DoubleConversion\"",
					"\"${PODS_ROOT}/Headers/Public/FBLazyVector\"",
					"\"${PODS_ROOT}/Headers/Public/FBReactNativeSpec\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-Fmt\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-Folly\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-Glog\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-PeerTalk\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-RSocket\"",
					"\"${PODS_ROOT}/Headers/Public/FlipperKit\"",
					"\"${PODS_ROOT}/Headers/Public/GCDWebServer\"",
					"\"${PODS_ROOT}/Headers/Public/JCore\"",
					"\"${PODS_ROOT}/Headers/Public/RCT-Folly\"",
					"\"${PODS_ROOT}/Headers/Public/RCTRequired\"",
					"\"${PODS_ROOT}/Headers/Public/RCTTypeSafety\"",
					"\"${PODS_ROOT}/Headers/Public/RNCAsyncStorage\"",
					"\"${PODS_ROOT}/Headers/Public/RNCMaskedView\"",
					"\"${PODS_ROOT}/Headers/Public/RNCPushNotificationIOS\"",
					"\"${PODS_ROOT}/Headers/Public/RNDeviceInfo\"",
					"\"${PODS_ROOT}/Headers/Public/RNFS\"",
					"\"${PODS_ROOT}/Headers/Public/RNFastImage\"",
					"\"${PODS_ROOT}/Headers/Public/RNGestureHandler\"",
					"\"${PODS_ROOT}/Headers/Public/RNLanguages\"",
					"\"${PODS_ROOT}/Headers/Public/RNLocalize\"",
					"\"${PODS_ROOT}/Headers/Public/RNReanimated\"",
					"\"${PODS_ROOT}/Headers/Public/RNSVG\"",
					"\"${PODS_ROOT}/Headers/Public/RNScreens\"",
					"\"${PODS_ROOT}/Headers/Public/RNSentry\"",
					"\"${PODS_ROOT}/Headers/Public/RNVectorIcons\"",
					"\"${PODS_ROOT}/Headers/Public/React-Core\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTBlob\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTText\"",
					"\"${PODS_ROOT}/Headers/Public/React-callinvoker\"",
					"\"${PODS_ROOT}/Headers/Public/React-cxxreact\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsi\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsiexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsinspector\"",
					"\"${PODS_ROOT}/Headers/Public/React-logger\"",
					"\"${PODS_ROOT}/Headers/Public/React-perflogger\"",
					"\"${PODS_ROOT}/Headers/Public/React-runtimeexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/ReactCommon\"",
					"\"${PODS_ROOT}/Headers/Public/SDWebImage\"",
					"\"${PODS_ROOT}/Headers/Public/SDWebImageWebPCoder\"",
					"\"${PODS_ROOT}/Headers/Public/Sentry\"",
					"\"${PODS_ROOT}/Headers/Public/Yoga\"",
					"\"${PODS_ROOT}/Headers/Public/YogaKit\"",
					"\"${PODS_ROOT}/Headers/Public/fmt\"",
					"\"${PODS_ROOT}/Headers/Public/glog\"",
					"\"${PODS_ROOT}/Headers/Public/intercom-react-native\"",
					"\"${PODS_ROOT}/Headers/Public/libevent\"",
					"\"${PODS_ROOT}/Headers/Public/libwebp\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-config\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-geolocation-service\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-netinfo\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-pager-view\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-safe-area-context\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-splash-screen\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-webview\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/RealmJS/Headers\"",
					"\"$(PODS_ROOT)/DoubleConversion\"",
					"\"$(PODS_ROOT)/boost\"",
					"\"$(PODS_ROOT)/Headers/Private/React-Core\"",
					"\"$(PODS_TARGET_SRCROOT)/include/\"",
				);
				INFOPLIST_FILE = pos/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"$(inherited)",
					"$(PROJECT_DIR)/pos/ScanAPI/lib",
				);
				MARKETING_VERSION = 1.92.0;
				Multi_Deployment_Config = "";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.storehub.pos.fat;
				PRODUCT_NAME = pos;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 2;
				VALIDATE_WORKSPACE = NO;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5324750F5339972608A0289C /* Pods-pos.release.xcconfig */;
			buildSettings = {
				APP_DISPLAY_NAME = StoreHub;
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon-1";
				CLANG_ENABLE_MODULES = YES;
				CODEPUSH_KEY = ws9umqodVSMqK4MMIPjnzmiJI_nLFuAzBSWyn;
				CODE_SIGN_ENTITLEMENTS = pos/pos.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = X78BF7H5HG;
				ENABLE_BITCODE = NO;
				FIREBASE_CONFIG_FILE_NAME = "GoogleService-Info-RELEASE.plist";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_ROOT}/../../node_modules/realm/react-native/ios\"",
					"\"${PODS_ROOT}/Intercom\"",
					"\"${PODS_ROOT}/OpenSSL-Universal/Frameworks\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/Intercom\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/OpenSSL-Universal\"",
				);
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_ROOT}/Headers/Public\"",
					"\"${PODS_ROOT}/Headers/Public/CocoaAsyncSocket\"",
					"\"${PODS_ROOT}/Headers/Public/DoubleConversion\"",
					"\"${PODS_ROOT}/Headers/Public/FBLazyVector\"",
					"\"${PODS_ROOT}/Headers/Public/FBReactNativeSpec\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-Fmt\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-Folly\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-Glog\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-PeerTalk\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-RSocket\"",
					"\"${PODS_ROOT}/Headers/Public/FlipperKit\"",
					"\"${PODS_ROOT}/Headers/Public/GCDWebServer\"",
					"\"${PODS_ROOT}/Headers/Public/JCore\"",
					"\"${PODS_ROOT}/Headers/Public/RCT-Folly\"",
					"\"${PODS_ROOT}/Headers/Public/RCTRequired\"",
					"\"${PODS_ROOT}/Headers/Public/RCTTypeSafety\"",
					"\"${PODS_ROOT}/Headers/Public/RNCAsyncStorage\"",
					"\"${PODS_ROOT}/Headers/Public/RNCMaskedView\"",
					"\"${PODS_ROOT}/Headers/Public/RNCPushNotificationIOS\"",
					"\"${PODS_ROOT}/Headers/Public/RNDeviceInfo\"",
					"\"${PODS_ROOT}/Headers/Public/RNFS\"",
					"\"${PODS_ROOT}/Headers/Public/RNFastImage\"",
					"\"${PODS_ROOT}/Headers/Public/RNGestureHandler\"",
					"\"${PODS_ROOT}/Headers/Public/RNLanguages\"",
					"\"${PODS_ROOT}/Headers/Public/RNLocalize\"",
					"\"${PODS_ROOT}/Headers/Public/RNReanimated\"",
					"\"${PODS_ROOT}/Headers/Public/RNSVG\"",
					"\"${PODS_ROOT}/Headers/Public/RNScreens\"",
					"\"${PODS_ROOT}/Headers/Public/RNSentry\"",
					"\"${PODS_ROOT}/Headers/Public/RNVectorIcons\"",
					"\"${PODS_ROOT}/Headers/Public/React-Core\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTBlob\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTText\"",
					"\"${PODS_ROOT}/Headers/Public/React-callinvoker\"",
					"\"${PODS_ROOT}/Headers/Public/React-cxxreact\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsi\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsiexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsinspector\"",
					"\"${PODS_ROOT}/Headers/Public/React-logger\"",
					"\"${PODS_ROOT}/Headers/Public/React-perflogger\"",
					"\"${PODS_ROOT}/Headers/Public/React-runtimeexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/ReactCommon\"",
					"\"${PODS_ROOT}/Headers/Public/SDWebImage\"",
					"\"${PODS_ROOT}/Headers/Public/SDWebImageWebPCoder\"",
					"\"${PODS_ROOT}/Headers/Public/Sentry\"",
					"\"${PODS_ROOT}/Headers/Public/Yoga\"",
					"\"${PODS_ROOT}/Headers/Public/YogaKit\"",
					"\"${PODS_ROOT}/Headers/Public/fmt\"",
					"\"${PODS_ROOT}/Headers/Public/glog\"",
					"\"${PODS_ROOT}/Headers/Public/intercom-react-native\"",
					"\"${PODS_ROOT}/Headers/Public/libevent\"",
					"\"${PODS_ROOT}/Headers/Public/libwebp\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-config\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-geolocation-service\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-netinfo\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-pager-view\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-safe-area-context\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-splash-screen\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-webview\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/RealmJS/Headers\"",
					"\"$(PODS_ROOT)/DoubleConversion\"",
					"\"$(PODS_ROOT)/boost\"",
					"\"$(PODS_ROOT)/Headers/Private/React-Core\"",
					"\"$(PODS_TARGET_SRCROOT)/include/\"",
				);
				INFOPLIST_FILE = pos/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"$(inherited)",
					"$(PROJECT_DIR)/pos/ScanAPI/lib",
				);
				MARKETING_VERSION = 1.92.0;
				Multi_Deployment_Config = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.storehub.pos;
				PRODUCT_NAME = pos;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 2;
				VALIDATE_WORKSPACE = NO;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
					_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION,
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = "$(inherited)";
				OTHER_LDFLAGS = "$(inherited)";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION,
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = "$(inherited)";
				OTHER_LDFLAGS = "$(inherited)";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		C48C244327E46E65003FE4D0 /* Fat */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION,
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = "$(inherited)";
				OTHER_LDFLAGS = "$(inherited)";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
			};
			name = Fat;
		};
		C48C244427E46E65003FE4D0 /* Fat */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = AFEA007918495F5AEA6D702A /* Pods-pos.fat.xcconfig */;
			buildSettings = {
				APP_DISPLAY_NAME = FAT_StoreHub;
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon-1";
				CLANG_ENABLE_MODULES = YES;
				CODEPUSH_KEY = "";
				CODE_SIGN_ENTITLEMENTS = pos/pos.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = X78BF7H5HG;
				ENABLE_BITCODE = NO;
				FIREBASE_CONFIG_FILE_NAME = "GoogleService-Info-FAT.plist";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_ROOT}/../../node_modules/realm/react-native/ios\"",
					"\"${PODS_ROOT}/Flipper-DoubleConversion/Frameworks\"",
					"\"${PODS_ROOT}/Intercom\"",
					"\"${PODS_ROOT}/OpenSSL-Universal/Frameworks\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/Flipper-DoubleConversion\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/Intercom\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/OpenSSL-Universal\"",
				);
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_ROOT}/Headers/Public\"",
					"\"${PODS_ROOT}/Headers/Public/CocoaAsyncSocket\"",
					"\"${PODS_ROOT}/Headers/Public/DoubleConversion\"",
					"\"${PODS_ROOT}/Headers/Public/FBLazyVector\"",
					"\"${PODS_ROOT}/Headers/Public/FBReactNativeSpec\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-Fmt\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-Folly\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-Glog\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-PeerTalk\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-RSocket\"",
					"\"${PODS_ROOT}/Headers/Public/FlipperKit\"",
					"\"${PODS_ROOT}/Headers/Public/GCDWebServer\"",
					"\"${PODS_ROOT}/Headers/Public/JCore\"",
					"\"${PODS_ROOT}/Headers/Public/RCT-Folly\"",
					"\"${PODS_ROOT}/Headers/Public/RCTRequired\"",
					"\"${PODS_ROOT}/Headers/Public/RCTTypeSafety\"",
					"\"${PODS_ROOT}/Headers/Public/RNCAsyncStorage\"",
					"\"${PODS_ROOT}/Headers/Public/RNCMaskedView\"",
					"\"${PODS_ROOT}/Headers/Public/RNCPushNotificationIOS\"",
					"\"${PODS_ROOT}/Headers/Public/RNDeviceInfo\"",
					"\"${PODS_ROOT}/Headers/Public/RNFS\"",
					"\"${PODS_ROOT}/Headers/Public/RNFastImage\"",
					"\"${PODS_ROOT}/Headers/Public/RNGestureHandler\"",
					"\"${PODS_ROOT}/Headers/Public/RNLanguages\"",
					"\"${PODS_ROOT}/Headers/Public/RNLocalize\"",
					"\"${PODS_ROOT}/Headers/Public/RNReanimated\"",
					"\"${PODS_ROOT}/Headers/Public/RNSVG\"",
					"\"${PODS_ROOT}/Headers/Public/RNScreens\"",
					"\"${PODS_ROOT}/Headers/Public/RNSentry\"",
					"\"${PODS_ROOT}/Headers/Public/RNVectorIcons\"",
					"\"${PODS_ROOT}/Headers/Public/React-Core\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTBlob\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTText\"",
					"\"${PODS_ROOT}/Headers/Public/React-callinvoker\"",
					"\"${PODS_ROOT}/Headers/Public/React-cxxreact\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsi\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsiexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsinspector\"",
					"\"${PODS_ROOT}/Headers/Public/React-logger\"",
					"\"${PODS_ROOT}/Headers/Public/React-perflogger\"",
					"\"${PODS_ROOT}/Headers/Public/React-runtimeexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/ReactCommon\"",
					"\"${PODS_ROOT}/Headers/Public/SDWebImage\"",
					"\"${PODS_ROOT}/Headers/Public/SDWebImageWebPCoder\"",
					"\"${PODS_ROOT}/Headers/Public/Sentry\"",
					"\"${PODS_ROOT}/Headers/Public/Yoga\"",
					"\"${PODS_ROOT}/Headers/Public/YogaKit\"",
					"\"${PODS_ROOT}/Headers/Public/fmt\"",
					"\"${PODS_ROOT}/Headers/Public/glog\"",
					"\"${PODS_ROOT}/Headers/Public/intercom-react-native\"",
					"\"${PODS_ROOT}/Headers/Public/libevent\"",
					"\"${PODS_ROOT}/Headers/Public/libwebp\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-config\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-geolocation-service\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-netinfo\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-pager-view\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-safe-area-context\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-splash-screen\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-webview\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/RealmJS/Headers\"",
					"\"$(PODS_ROOT)/DoubleConversion\"",
					"\"$(PODS_ROOT)/boost\"",
					"\"$(PODS_ROOT)/Headers/Private/React-Core\"",
					"\"$(PODS_TARGET_SRCROOT)/include/\"",
					"$(SRCROOT)/../../libs/react-native-printer/ios",
				);
				INFOPLIST_FILE = pos/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"$(inherited)",
					"$(PROJECT_DIR)/pos/ScanAPI/lib",
				);
				MARKETING_VERSION = 1.92.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				Multi_Deployment_Config = "";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.storehub.pos.fat;
				PRODUCT_NAME = pos;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 2;
				VALIDATE_WORKSPACE = NO;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Fat;
		};
		C48C244527E46E65003FE4D0 /* Fat */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 79C68740CB7A92EA83BE4740 /* Pods-pos-posTests.fat.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = posTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"$(inherited)",
					"$(PROJECT_DIR)/pos/ScanAPI/lib",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/pos.app/pos";
			};
			name = Fat;
		};
		C48C244627E46E7C003FE4D0 /* Uat */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION,
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = "$(inherited)";
				OTHER_LDFLAGS = "$(inherited)";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = Uat;
		};
		C48C244727E46E7C003FE4D0 /* Uat */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 6036824F5C50578DE98C47BF /* Pods-pos.uat.xcconfig */;
			buildSettings = {
				APP_DISPLAY_NAME = Staging_StoreHub;
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon-1";
				CLANG_ENABLE_MODULES = YES;
				CODEPUSH_KEY = q2rxP7fiVkoNWq3DKo3mH4MCCSgxqnvvhyx_Q;
				CODE_SIGN_ENTITLEMENTS = pos/pos.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = X78BF7H5HG;
				ENABLE_BITCODE = NO;
				FIREBASE_CONFIG_FILE_NAME = "GoogleService-Info-RELEASE.plist";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_ROOT}/../../node_modules/realm/react-native/ios\"",
					"\"${PODS_ROOT}/Intercom\"",
					"\"${PODS_ROOT}/OpenSSL-Universal/Frameworks\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/Intercom\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/OpenSSL-Universal\"",
				);
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_ROOT}/Headers/Public\"",
					"\"${PODS_ROOT}/Headers/Public/CocoaAsyncSocket\"",
					"\"${PODS_ROOT}/Headers/Public/DoubleConversion\"",
					"\"${PODS_ROOT}/Headers/Public/FBLazyVector\"",
					"\"${PODS_ROOT}/Headers/Public/FBReactNativeSpec\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-Fmt\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-Folly\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-Glog\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-PeerTalk\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-RSocket\"",
					"\"${PODS_ROOT}/Headers/Public/FlipperKit\"",
					"\"${PODS_ROOT}/Headers/Public/GCDWebServer\"",
					"\"${PODS_ROOT}/Headers/Public/JCore\"",
					"\"${PODS_ROOT}/Headers/Public/RCT-Folly\"",
					"\"${PODS_ROOT}/Headers/Public/RCTRequired\"",
					"\"${PODS_ROOT}/Headers/Public/RCTTypeSafety\"",
					"\"${PODS_ROOT}/Headers/Public/RNCAsyncStorage\"",
					"\"${PODS_ROOT}/Headers/Public/RNCMaskedView\"",
					"\"${PODS_ROOT}/Headers/Public/RNCPushNotificationIOS\"",
					"\"${PODS_ROOT}/Headers/Public/RNDeviceInfo\"",
					"\"${PODS_ROOT}/Headers/Public/RNFS\"",
					"\"${PODS_ROOT}/Headers/Public/RNFastImage\"",
					"\"${PODS_ROOT}/Headers/Public/RNGestureHandler\"",
					"\"${PODS_ROOT}/Headers/Public/RNLanguages\"",
					"\"${PODS_ROOT}/Headers/Public/RNLocalize\"",
					"\"${PODS_ROOT}/Headers/Public/RNReanimated\"",
					"\"${PODS_ROOT}/Headers/Public/RNSVG\"",
					"\"${PODS_ROOT}/Headers/Public/RNScreens\"",
					"\"${PODS_ROOT}/Headers/Public/RNSentry\"",
					"\"${PODS_ROOT}/Headers/Public/RNVectorIcons\"",
					"\"${PODS_ROOT}/Headers/Public/React-Core\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTBlob\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTText\"",
					"\"${PODS_ROOT}/Headers/Public/React-callinvoker\"",
					"\"${PODS_ROOT}/Headers/Public/React-cxxreact\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsi\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsiexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsinspector\"",
					"\"${PODS_ROOT}/Headers/Public/React-logger\"",
					"\"${PODS_ROOT}/Headers/Public/React-perflogger\"",
					"\"${PODS_ROOT}/Headers/Public/React-runtimeexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/ReactCommon\"",
					"\"${PODS_ROOT}/Headers/Public/SDWebImage\"",
					"\"${PODS_ROOT}/Headers/Public/SDWebImageWebPCoder\"",
					"\"${PODS_ROOT}/Headers/Public/Sentry\"",
					"\"${PODS_ROOT}/Headers/Public/Yoga\"",
					"\"${PODS_ROOT}/Headers/Public/YogaKit\"",
					"\"${PODS_ROOT}/Headers/Public/fmt\"",
					"\"${PODS_ROOT}/Headers/Public/glog\"",
					"\"${PODS_ROOT}/Headers/Public/intercom-react-native\"",
					"\"${PODS_ROOT}/Headers/Public/libevent\"",
					"\"${PODS_ROOT}/Headers/Public/libwebp\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-config\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-geolocation-service\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-netinfo\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-pager-view\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-safe-area-context\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-splash-screen\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-webview\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/RealmJS/Headers\"",
					"\"$(PODS_ROOT)/DoubleConversion\"",
					"\"$(PODS_ROOT)/boost\"",
					"\"$(PODS_ROOT)/Headers/Private/React-Core\"",
					"\"$(PODS_TARGET_SRCROOT)/include/\"",
				);
				INFOPLIST_FILE = pos/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"$(inherited)",
					"$(PROJECT_DIR)/pos/ScanAPI/lib",
				);
				MARKETING_VERSION = 1.92.0;
				Multi_Deployment_Config = "$(BUILD_DIR)/Release$(EFFECTIVE_PLATFORM_NAME)";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.storehub.pos;
				PRODUCT_NAME = pos;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 2;
				VALIDATE_WORKSPACE = NO;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Uat;
		};
		C48C244827E46E7C003FE4D0 /* Uat */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 37525FF54A124E27460D3788 /* Pods-pos-posTests.uat.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				COPY_PHASE_STRIP = NO;
				INFOPLIST_FILE = posTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"$(inherited)",
					"$(PROJECT_DIR)/pos/ScanAPI/lib",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/pos.app/pos";
			};
			name = Uat;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "posTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				00E356F61AD99517003FC87E /* Debug */,
				C48C244527E46E65003FE4D0 /* Fat */,
				00E356F71AD99517003FC87E /* Release */,
				C48C244827E46E7C003FE4D0 /* Uat */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "pos" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				C48C244427E46E65003FE4D0 /* Fat */,
				13B07F951A680F5B00A75B9A /* Release */,
				C48C244727E46E7C003FE4D0 /* Uat */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "pos" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				C48C244327E46E65003FE4D0 /* Fat */,
				83CBBA211A601CBA00E9B192 /* Release */,
				C48C244627E46E7C003FE4D0 /* Uat */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
