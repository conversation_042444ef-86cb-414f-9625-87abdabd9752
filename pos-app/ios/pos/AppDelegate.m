#import "AppDelegate.h"

#import <React/RCTBridge.h>
#import <React/RCTBundleURLProvider.h>
#import <React/RCTRootView.h>

#import <UserNotifications/UserNotifications.h>

#import "RNS3TransferUtility.h"
#import "RNSplashScreen.h"
#import <RNCPushNotificationIOS.h>
#import "ReactNativeConfig.h"
#import <IntercomModule.h>
#import "FirebaseRemoteConfigService.h"
#import "RNScanner.h"
#import "ScanApiHelper.h"
#import <IQKeyboardManager.h>
#import <LogManager+Scanner.h>
#import <LogManager+Common.h>
// Pushy
#import "RCTPushy.h"
#import <RNPrinter/MPOPManager.h>
#import <FirebasePerformance.h>
#import "DoraemonKit/DoraemonKit.h"
#import <MetricKit/MetricKit.h>


// These imports should always be placed at the bottom
#ifdef FB_SONARKIT_ENABLED
#import <FlipperKit/FlipperClient.h>
#import <FlipperKitLayoutPlugin/FlipperKitLayoutPlugin.h>
#import <FlipperKitUserDefaultsPlugin/FKUserDefaultsPlugin.h>
#import <FlipperKitNetworkPlugin/FlipperKitNetworkPlugin.h>
#import <SKIOSNetworkPlugin/SKIOSNetworkAdapter.h>
#import <FlipperKitReactPlugin/FlipperKitReactPlugin.h>

static void InitializeFlipper(UIApplication *application) {
  FlipperClient *client = [FlipperClient sharedClient];
  SKDescriptorMapper *layoutDescriptorMapper = [[SKDescriptorMapper alloc] initWithDefaults];
  [client addPlugin:[[FlipperKitLayoutPlugin alloc] initWithRootNode:application withDescriptorMapper:layoutDescriptorMapper]];
  [client addPlugin:[[FKUserDefaultsPlugin alloc] initWithSuiteName:nil]];
  [client addPlugin:[FlipperKitReactPlugin new]];
  [client addPlugin:[[FlipperKitNetworkPlugin alloc] initWithNetworkAdapter:[SKIOSNetworkAdapter new]]];
  [client start];
}
#endif

@import Firebase;

@interface AppDelegate() <ScanApiHelperDelegate, StarIoExtManagerDelegate, MXMetricManagerSubscriber>

@end

@implementation AppDelegate

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions
{
#ifdef FB_SONARKIT_ENABLED
  InitializeFlipper(application);
#endif
  [FIRApp configure];

  RCTBridge *bridge = [[RCTBridge alloc] initWithDelegate:self launchOptions:launchOptions];
  RCTRootView *rootView = [[RCTRootView alloc] initWithBridge:bridge
                                                   moduleName:@"pos"
                                            initialProperties:nil];
  self.window = [[UIWindow alloc] initWithFrame:[UIScreen mainScreen].bounds];
  UIViewController *rootViewController = [UIViewController new];
  rootViewController.view = rootView;
  self.window.rootViewController = rootViewController;
  
  // Intercom
  NSString *intercomApiKey = [ReactNativeConfig envFor:@"IOS_INTERCOM_API_KEY"];
  NSString *intercomAppId = [ReactNativeConfig envFor:@"IOS_INTERCOM_APP_ID"];
  [IntercomModule initialize: intercomApiKey withAppId: intercomAppId];
  
  // iOS10 + foreground notification
  UNUserNotificationCenter *center = [UNUserNotificationCenter currentNotificationCenter];
  center.delegate = self;
  
  // ScanApiHelper
  [[ScanApiHelper sharedScanApiHelper] pushDelegate:self];
  [[ScanApiHelper sharedScanApiHelper] open];
  [NSTimer scheduledTimerWithTimeInterval:.5 target:self selector:@selector(scannerCheckTimer:) userInfo:nil repeats:YES];
  
  // observer update notification
  [[NSNotificationCenter defaultCenter] addObserver:self selector: @selector(handleUpdate) name:@"updateNotification" object:nil];
  
  [self.window makeKeyAndVisible];
  [RNSplashScreen show];
  
  IQKeyboardManager.sharedManager.enable = NO;
  IQKeyboardManager.sharedManager.enableAutoToolbar = NO;
  IQKeyboardManager.sharedManager.shouldResignOnTouchOutside = YES;
  
  [MPOPManager sharedInstance].delegate = self;
  
#if DEBUG
  // DoraemonEntryWindow.m
  // _kEntryViewSize = 58;
  CGFloat kEntryViewWidth = 58.0;
  CGFloat kEntryViewHeight = 58.0;
  CGFloat margin = 0.0;
  CGFloat screenWidth = [UIScreen mainScreen].bounds.size.width;
  CGFloat screenHeight = [UIScreen mainScreen].bounds.size.height;
  CGFloat x = screenWidth - kEntryViewWidth - margin;
  CGFloat y = screenHeight - kEntryViewHeight - margin;
  [[DoraemonManager shareInstance] installWithStartingPosition:CGPointMake(x, y)];
  
  [[DoraemonManager shareInstance] installWithPid:@"1be4c664751a32c04696b05f47da646e"];
  [DoraemonManager shareInstance].supportedInterfaceOrientations = UIInterfaceOrientationMaskLandscapeLeft | UIInterfaceOrientationMaskLandscapeRight;
#endif
  
  [[MXMetricManager sharedManager] addSubscriber:self];
  
//  [self excludeAsyncStorageFromBackup];
  
  return YES;
}

- (void)applicationDidBecomeActive:(UIApplication *)application {
  [[MPOPManager sharedInstance] connect];
}

- (void)application:(UIApplication *)application handleEventsForBackgroundURLSession:(NSString *)identifier completionHandler:(void (^)(void))completionHandler {
  [RNS3TransferUtility interceptApplication:application
        handleEventsForBackgroundURLSession:identifier
                          completionHandler:completionHandler];
}

- (void)applicationWillEnterForeground:(UIApplication *)application {
  
  // if need force update should pop update alert again when app enter forgound
  NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
  BOOL isForceUpdate = [userDefaults boolForKey: RN_POS_iOS_IS_FORCE_UPDATE];
  if (isForceUpdate) {
    [[NSNotificationCenter defaultCenter] postNotificationName:@"updateNotification" object:self];
  }
}

- (NSURL *)sourceURLForBridge:(RCTBridge *)bridge
{
#if DEBUG
  return [[RCTBundleURLProvider sharedSettings] jsBundleURLForBundleRoot:@"index"];
#else
  return [RCTPushy bundleURL];
#endif
}


#pragma mark - ScanApiHelperDelegate (SocketMobile)
- (void)onDecodedData:(DeviceInfo *)device decodedData:(id <ISktScanDecodedData>)decodedData {
    NSString *scannedBarcode = [NSString stringWithUTF8String:(char *)decodedData.getData];
    scannedBarcode = [scannedBarcode stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
  [[RNScanner shareInstance] handleScannedBarcode:scannedBarcode];
}

- (void)scannerCheckTimer:(NSTimer *)timer {
  [[ScanApiHelper sharedScanApiHelper] doScanApiReceive];
}

- (void)onDeviceArrival:(SKTRESULT)result device:(DeviceInfo *)deviceInfo {
  [[LogManager sharedInstance] logScannerEvent:@"ATTACH" name:[deviceInfo getName] type:[deviceInfo getTypeString]];
}

- (void)onDeviceRemoval:(DeviceInfo *)deviceRemoved {
  [[LogManager sharedInstance] logScannerEvent:@"DETACH" name:[deviceRemoved getName] type:[deviceRemoved getTypeString]];
}

#pragma mark - Push Notification
// Required for the register event.
- (void)application:(UIApplication *)application didRegisterForRemoteNotificationsWithDeviceToken:(NSData *)deviceToken
{
 [RNCPushNotificationIOS didRegisterForRemoteNotificationsWithDeviceToken:deviceToken];
}

// Required for the notification event. You must call the completion handler after handling the remote notification.
- (void)application:(UIApplication *)application didReceiveRemoteNotification:(NSDictionary *)userInfo
fetchCompletionHandler:(void (^)(UIBackgroundFetchResult))completionHandler
{
  [RNCPushNotificationIOS didReceiveRemoteNotification:userInfo fetchCompletionHandler:completionHandler];
}

// Required for the registrationError event.
- (void)application:(UIApplication *)application didFailToRegisterForRemoteNotificationsWithError:(NSError *)error
{
  [RNCPushNotificationIOS didFailToRegisterForRemoteNotificationsWithError:error];
}

// Required for localNotification event
- (void)userNotificationCenter:(UNUserNotificationCenter *)center didReceiveNotificationResponse:(UNNotificationResponse *)response
         withCompletionHandler:(void (^)(void))completionHandler
{
  [RNCPushNotificationIOS didReceiveNotificationResponse:response];
}

//Called when a notification is delivered to a foreground app.
- (void)userNotificationCenter:(UNUserNotificationCenter *)center willPresentNotification:(UNNotification *)notification withCompletionHandler:(void (^)(UNNotificationPresentationOptions options))completionHandler
{
  NSString *title = notification.request.content.title;
  NSDictionary *userInfo = notification.request.content.userInfo;
  if (NonEmptyDictionary(userInfo)){
    NSDictionary *data = userInfo[@"data"];
    if (NonEmptyDictionary(data)) {
      NSDictionary *payload = data[@"payload"];
      if (NonEmptyDictionary(payload)) {
        NSNumber *notifySystemNumber = payload[@"notifySystem"];
        if (notifySystemNumber && [notifySystemNumber isKindOfClass:[NSNumber class]]) {
          BOOL notifySystem = [notifySystemNumber boolValue];
          if (notifySystem && ![title isEqualToString:@"PayLaterAddedonlineOpenOrderUpdated"]) {
            completionHandler(UNNotificationPresentationOptionSound | UNNotificationPresentationOptionAlert | UNNotificationPresentationOptionBadge);
            return;
          }
        }
      }
    }
  }
  
  completionHandler(UNNotificationPresentationOptionNone);
}

#pragma mark - StarIoExtManagerDelegate (Mpop Scanner)

- (void)didBarcodeDataReceive:(NSData *)data {
  NSString *scannedBarcode = [MPOPManager decodeBarcodeData:data];
  NSLog(@"============scannedBarcode:%@", scannedBarcode);
  [[RNScanner shareInstance] handleScannedBarcode:scannedBarcode];
}

#pragma mark - MetricKit Delegate Methods
// Receive daily metrics.
- (void)didReceiveMetricPayloads:(NSArray<MXMetricPayload *> *)payloads {
  if (payloads.count == 0) {
    return;
  }
  
  NSDate *now = [NSDate date];
  
  for (NSInteger i = 0; i < payloads.count; i++) {
    MXMetricPayload *payload = payloads[i];
    NSDictionary *payloadDict = [payload DictionaryRepresentation];
    
    NSMutableDictionary *singleLog = [NSMutableDictionary dictionary];
    singleLog[@"metrics_data"] = payloadDict;
    singleLog[@"collection_date"] = now.description;
    singleLog[@"payload_index"] = @(i);
    singleLog[@"payload_count"] = @(payloads.count);
    
    [[LogManager sharedInstance] logCustomizedEvent:@"MetricKit_Performance_SinglePayload" privateDataDict:singleLog];
  }
}


// Diagnosis data
// Receive diagnostics immediately when available.
// sample mobileDict:
/**
 {
   timestamp_end = "2025-02-26 03:56:52 +0000";
   timestamp_begin = "2025-02-26 03:56:52 +0000";
   received_at = "2025-02-26 03:56:52 +0000";
   diagnostic_data = {
     hangDiagnostics = (
       {
         callStackTree = {
           callStacks = (
             {
               threadAttributed = 1;
               callStackRootFrames = (
                 {
                   binaryUUID = "0F6F848F-EC07-4E05-A483-D44A7C5EA8C6";
                   offsetIntoBinaryTextSegment = 123;
                   sampleCount = 20;
                   binaryName = "testBinaryName";
                   address = 74565;
                 }
               );
             }
           );
           callStackPerThread = 1;
         };
         diagnosticMetaData = {
           appBuildVersion = "1.0";
           isTestFlightApp = 0;
           regionFormat = "CN";
           appVersion = "1.83.1";
           osVersion = "macOS 15.3.1 (24D70)";
           deviceType = "Mac14,7";
           bundleIdentifier = "com.storehub.pos.fat";
           pid = 123;
           hangDuration = "20 sec";
           platformArchitecture = "arm64e";
           lowPowerModeEnabled = 1;
         };
         hangType = "Main Runloop Hang";
         version = "1.0.0";
       }
     );
     diskWriteExceptionDiagnostics = (
       {
         version = "1.0.0";
         callStackTree = {
           callStacks = (
             {
               callStackRootFrames = (
                 {
                   binaryUUID = "8DDE1E51-6D8E-4D8F-878F-4F03ED39D104";
                   offsetIntoBinaryTextSegment = 123;
                   sampleCount = 20;
                   binaryName = "testBinaryName";
                   address = 74565;
                 }
               );
             }
           );
           callStackPerThread = 0;
         };
         diagnosticMetaData = {
           appBuildVersion = "1.0";
           isTestFlightApp = 0;
           regionFormat = "CN";
           appVersion = "1.83.1";
           osVersion = "macOS 15.3.1 (24D70)";
           deviceType = "Mac14,7";
           bundleIdentifier = "com.storehub.pos.fat";
           pid = 123;
           writesCaused = "2000 byte";
           platformArchitecture = "arm64e";
           lowPowerModeEnabled = 1;
         };
       }
     );
     cpuExceptionDiagnostics = (
       {
         version = "1.0.0";
         callStackTree = {
           callStacks = (
             {
               callStackRootFrames = (
                 {
                   binaryUUID = "8DDE1E51-6D8E-4D8F-878F-4F03ED39D104";
                   offsetIntoBinaryTextSegment = 123;
                   sampleCount = 20;
                   binaryName = "testBinaryName";
                   address = 74565;
                 }
               );
             }
           );
           callStackPerThread = 0;
         };
         diagnosticMetaData = {
           platformArchitecture = "arm64e";
           lowPowerModeEnabled = 1;
           totalCPUTime = "20 sec";
           appBuildVersion = "1.0";
           isTestFlightApp = 0;
           osVersion = "macOS 15.3.1 (24D70)";
           bundleIdentifier = "com.storehub.pos.fat";
           deviceType = "Mac14,7";
           regionFormat = "CN";
           appVersion = "1.83.1";
           pid = 123;
           totalSampledTime = "20 sec";
         };
       }
     );
   };
 }
 */
//- (void)didReceiveDiagnosticPayloads:(NSArray<MXDiagnosticPayload *> *)payloads API_AVAILABLE(ios(14.0)) {
//  if (payloads.count == 0) {
//    return;
//  }
//
//  for (MXDiagnosticPayload *payload in payloads) {
//    NSMutableDictionary *diagnosticDict = [NSMutableDictionary dictionary];
//
//    diagnosticDict[@"timestamp_begin"] = payload.timeStampBegin.description;
//    diagnosticDict[@"timestamp_end"] = payload.timeStampEnd.description;
//    diagnosticDict[@"received_at"] = [NSDate date].description;
//
//    NSDictionary *fullDiagnosticData;
//
//    fullDiagnosticData = [payload dictionaryRepresentation];
//
//    NSMutableDictionary *filteredDiagnosticData = [NSMutableDictionary dictionary];
//
//    // 1. CPU 异常诊断
//    if (fullDiagnosticData[@"cpuExceptionDiagnostics"]) {
//      filteredDiagnosticData[@"cpuExceptionDiagnostics"] = fullDiagnosticData[@"cpuExceptionDiagnostics"];
//    }
//
//    // 2. 磁盘写入异常诊断
//    if (fullDiagnosticData[@"diskWriteExceptionDiagnostics"]) {
//      filteredDiagnosticData[@"diskWriteExceptionDiagnostics"] = fullDiagnosticData[@"diskWriteExceptionDiagnostics"];
//    }
//
//    // 3. 卡顿诊断
//    if (fullDiagnosticData[@"hangDiagnostics"]) {
//      filteredDiagnosticData[@"hangDiagnostics"] = fullDiagnosticData[@"hangDiagnostics"];
//    }
//
//    diagnosticDict[@"diagnostic_data"] = filteredDiagnosticData;
//
//    BOOL hasDiagnosticData = (filteredDiagnosticData.count > 0);
//    if (hasDiagnosticData) {
//      [[LogManager sharedInstance] logCustomizedEvent:@"MetricKit_Selected_Diagnostics" mobileDict:diagnosticDict];
//    }
//  }
//}

#pragma mark - iCloud backup
//- (BOOL)addSkipBackupAttributeToItemAtURL:(NSURL *)URL {
//  if (![[NSFileManager defaultManager] fileExistsAtPath:[URL path]]) {
//    NSLog(@"[iCloud] The path does not exist: %@", [URL path]);
//    return NO;
//  }
//  
//  NSError *error = nil;
//  BOOL success = [URL setResourceValue:@YES
//                                forKey:NSURLIsExcludedFromBackupKey
//                                 error:&error];
//  if (!success) {
//    NSLog(@"[iCloud] An error occurred in setting the non-backup property %@: %@", [URL lastPathComponent], error);
//    
//    UNMutableNotificationContent *content = [[UNMutableNotificationContent alloc] init];
//    content.title = @"Backup 属性设置失败";
//    content.body = [NSString stringWithFormat:@"路径: %@，错误信息: %@", [URL lastPathComponent], error.localizedDescription];
//    content.sound = [UNNotificationSound defaultSound];
//    UNTimeIntervalNotificationTrigger *trigger = [UNTimeIntervalNotificationTrigger triggerWithTimeInterval:1 repeats:NO];
//    UNNotificationRequest *request = [UNNotificationRequest requestWithIdentifier:@"BackupFailNotification" content:content trigger:trigger];
//    [[UNUserNotificationCenter currentNotificationCenter] addNotificationRequest:request withCompletionHandler:nil];
//  } else {
//    NSLog(@"[iCloud] The non-backup attribute was successfully set: %@", [URL path]);
//    
//    UNMutableNotificationContent *content = [[UNMutableNotificationContent alloc] init];
//    content.title = @"Backup 属性设置成功";
//    content.body = [NSString stringWithFormat:@"路径: %@", [URL lastPathComponent]];
//    content.sound = [UNNotificationSound defaultSound];
//    UNTimeIntervalNotificationTrigger *trigger = [UNTimeIntervalNotificationTrigger triggerWithTimeInterval:1 repeats:NO];
//    UNNotificationRequest *request = [UNNotificationRequest requestWithIdentifier:@"BackupSuccessNotification" content:content trigger:trigger];
//    [[UNUserNotificationCenter currentNotificationCenter] addNotificationRequest:request withCompletionHandler:nil];
//  }
//  return success;
//}

// 设置AsyncStorage目录不备份
//- (void)excludeAsyncStorageFromBackup {
//  NSString *libraryDir = [NSSearchPathForDirectoriesInDomains(NSApplicationSupportDirectory, NSUserDomainMask, YES) objectAtIndex:0];
//  NSString *asyncStoragePath = [libraryDir stringByAppendingPathComponent:@"com.storehub.pos/RCTAsyncLocalStorage_V1"];
//  NSURL *asyncStorageURL = [NSURL fileURLWithPath:asyncStoragePath];
//  
//  if ([[NSFileManager defaultManager] fileExistsAtPath:asyncStoragePath]) {
//    [self addSkipBackupAttributeToItemAtURL:asyncStorageURL];
//  } else {
//    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//      if ([[NSFileManager defaultManager] fileExistsAtPath:asyncStoragePath]) {
//        [self addSkipBackupAttributeToItemAtURL:asyncStorageURL];
//      } else {
//        NSLog(@"[iCloud] The AsyncStorage directory does not exist: %@", asyncStoragePath);
//        
//        UNMutableNotificationContent *content = [[UNMutableNotificationContent alloc] init];
//        content.title = @"Backup 路径没有找到";
//        content.body = [NSString stringWithFormat:@"路径: %@", asyncStoragePath];
//        content.sound = [UNNotificationSound defaultSound];
//        UNTimeIntervalNotificationTrigger *trigger = [UNTimeIntervalNotificationTrigger triggerWithTimeInterval:1 repeats:NO];
//        UNNotificationRequest *request = [UNNotificationRequest requestWithIdentifier:@"BackupSuccessNotification" content:content trigger:trigger];
//        [[UNUserNotificationCenter currentNotificationCenter] addNotificationRequest:request withCompletionHandler:nil];
//      }
//    });
//  }
//}

@end

