//
//  FirebaseRemoteConfig.m
//  pos
//
//  Created by <PERSON> on 2022/3/14.
//

#import "FirebaseRemoteConfigService.h"
#import <FirebaseRemoteConfig.h>

@implementation FirebaseRemoteConfigService

NSString *const CASH_BACK_TEXT_ABOVE_QR = @"CASH_BACK_TEXT_ABOVE_QR_SINCE_229";
NSString *const CASH_BACK_TEXT_UNDER_QR = @"CASH_BACK_TEXT_UNDER_QR_SINCE_229";
NSString *const RN_POS_iOS_LATEST_VERSION = @"RN_POS_iOS_LATEST_VERSION";
NSString *const RN_POS_iOS_LATEST_VERSION_RELEASE_NOTE = @"RN_POS_iOS_LATEST_VERSION_RELEASE_NOTE";
NSString *const RN_POS_iOS_ITUNES_URL = @"RN_POS_iOS_ITUNES_URL";
NSString *const RN_POS_iOS_IS_FORCE_UPDATE = @"RN_POS_iOS_IS_FORCE_UPDATE";
NSString *const RN_POS_iOS_NEED_SOFT_UPDATE = @"RN_POS_iOS_NEED_SOFT_UPDATE";
NSString * const RN_POS_iOS_BLACK_VERSIONS = @"RN_POS_iOS_BLACK_VERSIONS";
NSString *const RN_iOS_USE_NEW_LAN_CONNECTION = @"RN_iOS_USE_NEW_LAN_CONNECTION";
NSString *const RN_iOS_USE_NEW_HTML_TO_IMAGE = @"RN_iOS_USE_NEW_HTML_TO_IMAGE";
NSString *const RN_iOS_NEW_LAN_PRINTING_TIMEOUT = @"RN_iOS_NEW_LAN_PRINTING_TIMEOUT";

NSTimeInterval CACHED_EXPIRATION_RELEASE_MODE = 60;
NSTimeInterval CACHED_EXPIRATION_DEVELOPER_MODE = 0;

static FirebaseRemoteConfigService* _instance = nil;

+(instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      _instance = [[super allocWithZone:NULL] init];
      [_instance initReomteConfig];
    });

    return _instance;
}

+(id)allocWithZone:(struct _NSZone *)zone {
    return [FirebaseRemoteConfigService shareInstance];
}

-(id)copyWithZone:(struct _NSZone *)zone {
    return [FirebaseRemoteConfigService shareInstance];
}

- (void)initReomteConfig {
  self.remoteConfig = [FIRRemoteConfig remoteConfig];
  FIRRemoteConfigSettings *remoteConfigSettings = [[FIRRemoteConfigSettings alloc] init];
  #ifdef DEBUG
  remoteConfigSettings.minimumFetchInterval = CACHED_EXPIRATION_DEVELOPER_MODE;
  #else
  remoteConfigSettings.minimumFetchInterval = CACHED_EXPIRATION_RELEASE_MODE;
  #endif
  self.remoteConfig.configSettings = remoteConfigSettings;
}

- (void)fetchConfigSuccess: (success)success fail: (fail) fail {
  [self.remoteConfig fetchWithCompletionHandler:^(FIRRemoteConfigFetchStatus status, NSError *error) {
      if (status == FIRRemoteConfigFetchStatusSuccess) {
        [self.remoteConfig activateWithCompletion:^(BOOL changed, NSError * _Nullable error) {
          NSString *cashBackTextAboveQR = self.remoteConfig[CASH_BACK_TEXT_ABOVE_QR].stringValue;
          NSString *cashBackTextUnderQR = self.remoteConfig[CASH_BACK_TEXT_UNDER_QR].stringValue;
          NSString *latestVersion = self.remoteConfig[RN_POS_iOS_LATEST_VERSION].stringValue;
          NSString *releaseNote = self.remoteConfig[RN_POS_iOS_LATEST_VERSION_RELEASE_NOTE].stringValue;
          NSString *updateUrl = self.remoteConfig[RN_POS_iOS_ITUNES_URL].stringValue;
          BOOL isForceUpdate = self.remoteConfig[RN_POS_iOS_IS_FORCE_UPDATE].boolValue;
          BOOL isNeedSoftUpdate = self.remoteConfig[RN_POS_iOS_NEED_SOFT_UPDATE].boolValue;
          NSString *blackVersions = self.remoteConfig[RN_POS_iOS_BLACK_VERSIONS].stringValue;
          BOOL useNewLanConnection = self.remoteConfig[RN_iOS_USE_NEW_LAN_CONNECTION].boolValue;
          BOOL useNewHtmlToImage = self.remoteConfig[RN_iOS_USE_NEW_HTML_TO_IMAGE].boolValue;
          NSNumber *newLanPrintingTimeout = self.remoteConfig[RN_iOS_NEW_LAN_PRINTING_TIMEOUT].numberValue;

          NSLog(@"FireBaseConfig fetched");
          NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
          [userDefaults setObject:cashBackTextAboveQR forKey: CASH_BACK_TEXT_ABOVE_QR];
          [userDefaults setObject:cashBackTextUnderQR forKey: CASH_BACK_TEXT_UNDER_QR];
          [userDefaults setObject:latestVersion forKey: RN_POS_iOS_LATEST_VERSION];
          [userDefaults setObject:releaseNote forKey: RN_POS_iOS_LATEST_VERSION_RELEASE_NOTE];
          [userDefaults setObject:updateUrl forKey: RN_POS_iOS_ITUNES_URL];
          [userDefaults setBool:isForceUpdate forKey: RN_POS_iOS_IS_FORCE_UPDATE];
          [userDefaults setBool:isNeedSoftUpdate forKey: RN_POS_iOS_NEED_SOFT_UPDATE];
          [userDefaults setObject:blackVersions forKey: RN_POS_iOS_BLACK_VERSIONS];
          [userDefaults setBool:useNewLanConnection forKey:RN_iOS_USE_NEW_LAN_CONNECTION];
          [userDefaults setBool:useNewHtmlToImage forKey:RN_iOS_USE_NEW_HTML_TO_IMAGE];
          [userDefaults setDouble:newLanPrintingTimeout.doubleValue forKey:RN_iOS_NEW_LAN_PRINTING_TIMEOUT];
          [userDefaults synchronize];
          success();
        }];
      } else {
          NSLog(@"FireBaseConfig not fetched");
          NSLog(@"Error %@", error.localizedDescription);
          fail();
      }
  }];
}
 
@end
