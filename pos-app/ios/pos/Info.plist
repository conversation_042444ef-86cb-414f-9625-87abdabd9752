<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>$(APP_DISPLAY_NAME)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>com.storehub.rnpos</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>rnpos</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>1</string>
	<key>CodePushDeploymentKey</key>
	<string>$(CODEPUSH_KEY)</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>App uses this to scan barcodes or print receipts.</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>App uses this to scan barcodes or print receipts.</string>
	<key>NSBonjourServices</key>
	<array>
		<string>_storehubmrs._tcp</string>
		<string>_storehubmrs._udp</string>
		<string>_udp._tcp</string>
	</array>
	<key>NSCameraUsageDescription</key>
	<string>This lets you take photos on the app</string>
	<key>NSLocalNetworkUsageDescription</key>
	<string>App requests permission to sync orders with iPads connected to your local network.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>App uses this to identify your store&apos;s location for on-site sales and servicing purposes.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>App uses this to identify your store&apos;s location for on-site sales and servicing purposes.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>App uses microphone for voice input to the AI assistant</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>This allows you send photos to support center</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This allows you send photos to support center</string>
	<key>UIAppFonts</key>
	<array>
		<string>OpenSans-Bold.ttf</string>
		<string>OpenSans-Light.ttf</string>
		<string>OpenSans-Regular.ttf</string>
		<string>OpenSans-Semibold.ttf</string>
		<string>FontAwesome.ttf</string>
		<string>MaterialIcons.ttf</string>
		<string>Ionicons.ttf</string>
		<string>Noteworthy-Bold.ttf</string>
		<string>Chalkboard-Bold.ttf</string>
		<string>Roboto-Bold.ttf</string>
		<string>AntDesign.ttf</string>
		<string>Entypo.ttf</string>
		<string>EvilIcons.ttf</string>
		<string>Feather.ttf</string>
		<string>FontAwesome5_Brands.ttf</string>
		<string>FontAwesome5_Regular.ttf</string>
		<string>FontAwesome5_Solid.ttf</string>
		<string>Foundation.ttf</string>
		<string>MaterialCommunityIcons.ttf</string>
		<string>SimpleLineIcons.ttf</string>
		<string>Octicons.ttf</string>
		<string>Zocial.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
		<string>bluetooth-central</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>UISupportedExternalAccessoryProtocols</key>
	<array>
		<string>com.socketmobile.chs</string>
		<string>jp.star-m.starpro</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>RCTAsyncStorageExcludeFromBackup</key>
	<true/>
</dict>
</plist>
