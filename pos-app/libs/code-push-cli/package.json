{"name": "code-push-cli", "version": "0.0.1", "description": "Management CLI for the CodePush service", "main": "./script/cli.js", "scripts": {"start": "node ./bin/script/cli.js", "build": "tsc", "postinstall": "npm run build", "prettier": "prettier --write \"./**/*.ts\"", "lint": "npx eslint ./script/**/*.ts", "lint:fix": "npx eslint ./script/**/*.ts --fix"}, "bin": {"code-push-standalone": "./bin/script/cli.js"}, "repository": {"type": "git", "url": "https://github.com/microsoft/code-push-server"}, "author": "Microsoft", "dependencies": {"backslash": "^0.2.0", "chalk": "^4.1.2", "cli-table": "^0.3.11", "email-validator": "^2.0.4", "gradle-to-js": "2.0.1", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "opener": "^1.5.2", "parse-duration": "^1.1.0", "plist": "^3.0.6", "progress": "^2.0.3", "prompt": "^1.3.0", "properties": "^1.2.1", "q": "~1.5.1", "recursive-fs": "2.1.0", "rimraf": "^2.5.1", "semver": "^7.5.3", "simctl": "^2.0.3", "slash": "1.0.0", "superagent": "^8.0.9", "temp": "^0.9.4", "which": "^1.2.7", "wordwrap": "1.0.0", "xcode": "^3.0.1", "xml2js": "^0.6.0", "yargs": "^17.7.2", "yazl": "^2.5.1"}, "devDependencies": {"@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/mocha": "^10.0.1", "@types/node": "^20.3.1", "@types/q": "^1.5.5", "@types/sinon": "^10.0.15", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.45.0", "express": "^4.19.2", "mkdirp": "^3.0.1", "prettier": "^2.8.8", "sinon": "15.1.2", "superagent-mock": "^4.0.0", "typescript": "^5.1.3", "which": "^3.0.1"}}