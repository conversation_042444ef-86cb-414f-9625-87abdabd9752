import com.android.Version

def DEFAULT_COMPILE_SDK_VERSION = 30
def DEFAULT_BUILD_TOOLS_VERSION = '30.0.2'
def DEFAULT_MIN_SDK_VERSION = 16
def DEFAULT_TARGET_SDK_VERSION = 30

buildscript {
    if (project == rootProject) {
        repositories {
            google()
            mavenCentral()
            jcenter()
        }

        dependencies {
            classpath 'com.android.tools.build:gradle'
        }
    }
}

apply plugin: 'com.android.library'

def safeExtGet(prop, fallback) {
    rootProject.ext.has(prop) ? rootProject.ext.get(prop) : fallback
}

def isNewArchitectureEnabled() {
  return rootProject.hasProperty("newArchEnabled") &&  rootProject.getProperty("newArchEnabled") == "true"
}
if (isNewArchitectureEnabled()) {
  apply plugin: 'com.facebook.react'
}

// https://github.com/react-native-community/discussions-and-proposals/issues/671
// https://developer.android.com/build/releases/past-releases/agp-8-0-0-release-notes#namespace-dsl
// Namespace is required in module-level when AGP version >=7.3
def supportsNamespace() {
    def parsed = Version.ANDROID_GRADLE_PLUGIN_VERSION.tokenize('.')
    def major = parsed[0].toInteger()
    def minor = parsed[1].toInteger()

    // Namespace support was added in 7.3.0
    return (major == 7 && minor >= 3) || major >= 8
}

android {
    if (supportsNamespace()) {
        namespace "com.betomorrow.rnfilelogger"

        sourceSets {
            main {
                manifest.srcFile "src/main/AndroidManifestNew.xml"
            }
        }
    }

    compileSdkVersion safeExtGet('compileSdkVersion', DEFAULT_COMPILE_SDK_VERSION)
    buildToolsVersion safeExtGet('buildToolsVersion', DEFAULT_BUILD_TOOLS_VERSION)
    defaultConfig {
        minSdkVersion safeExtGet('minSdkVersion', DEFAULT_MIN_SDK_VERSION)
        targetSdkVersion safeExtGet('targetSdkVersion', DEFAULT_TARGET_SDK_VERSION)
        buildConfigField "boolean", "IS_NEW_ARCHITECTURE_ENABLED", isNewArchitectureEnabled().toString()
        versionCode 1
        versionName "1.0"
        consumerProguardFiles 'logback-rules.pro'
    }
    lintOptions {
        abortOnError false
    }
    sourceSets {
        main {
        if (isNewArchitectureEnabled()) {
            java.srcDirs += [
            "src/turbo",
            // This is needed to build Kotlin project with NewArch enabled
            "${project.buildDir}/generated/source/codegen/java"
            ]
        } else {
            java.srcDirs += ["src/legacy"]
        }
        }
    }
}

repositories {
    mavenLocal()
    maven {
        // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
        url("$rootDir/../node_modules/react-native/android")
    }
    google()
    mavenCentral()
    jcenter()
}

dependencies {
    //noinspection GradleDynamicVersion
    implementation "com.facebook.react:react-native:+"
    implementation 'org.slf4j:slf4j-api:1.7.33'
    implementation 'com.github.tony19:logback-android:2.0.0'
    implementation project(':react-native-peripheral')
    implementation 'com.google.code.gson:gson:2.8.5'
}

if (isNewArchitectureEnabled()) {
  react {
    jsRootDir = file("../src/")
    libraryName = "FileLogger"
    codegenJavaPackageName = "com.betomorrow.rnfilelogger"
  }
}