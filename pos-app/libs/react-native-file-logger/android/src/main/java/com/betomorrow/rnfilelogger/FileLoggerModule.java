package com.betomorrow.rnfilelogger;

import static android.content.Context.MODE_PRIVATE;

import android.content.Intent;
import android.content.SharedPreferences;
import android.net.Uri;
import android.util.Log;

import androidx.core.content.FileProvider;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import com.google.gson.Gson;
import com.betomorrow.rnfilelogger.model.LogEntryModel;
import com.betomorrow.rnfilelogger.model.LogEventModel;
import com.betomorrow.rnfilelogger.model.LogHeaderModel;
import com.betomorrow.rnfilelogger.model.LogLevel;
import com.betomorrow.rnfilelogger.model.LogSettingModel;
import com.betomorrow.rnfilelogger.util.ReadableMapUtil;
import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.bridge.WritableArray;
import com.storehub.rn.peripheral.PeripheralManager;
import com.storehub.rn.peripheral.model.LogMobileData;
import com.storehub.rn.peripheral.model.LogPrivateData;
import com.storehub.rn.peripheral.util.TimeUtil;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FilenameFilter;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.UUID;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.classic.encoder.PatternLayoutEncoder;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.Appender;
import ch.qos.logback.core.rolling.FixedWindowRollingPolicy;
import ch.qos.logback.core.rolling.RollingFileAppender;
import ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy;
import ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy;
import ch.qos.logback.core.util.FileSize;

public class FileLoggerModule extends FileLoggerSpec {
    public static final String NAME = "FileLogger";
    public static final String APPENDER_NAME = "FileLoggerAppender";
    public static final int LOG_LEVEL_DEBUG = LogLevel.Debug.ordinal();
    public static final int LOG_LEVEL_INFO = LogLevel.Info.ordinal();
    public static final int LOG_LEVEL_WARNING = LogLevel.Warning.ordinal();
    public static final int LOG_LEVEL_ERROR = LogLevel.Error.ordinal();
    private static final Gson gson = PeripheralManager.getInstance().getGson();
    private static final LogHeaderModel logHeaderModel = new LogHeaderModel();
    private static final LogSettingModel logSettingModel = new LogSettingModel();
    private boolean isConfigured = false;
    private final SharedPreferences sp;

    private static final Logger logger = LoggerFactory.getLogger(FileLoggerModule.class);

    private final ReactApplicationContext reactContext;
    private String logsDirectory;
    private static volatile FileLoggerModule instance;
    private static final ExecutorService logWriteExecutor = Executors.newSingleThreadExecutor();

    public static FileLoggerModule getInstance(ReactApplicationContext reactContext) {
        if (instance == null) {
            synchronized (FileLoggerModule.class) {
                if (instance == null) {
                    instance = new FileLoggerModule(reactContext);
                }
            }
        }
        return instance;
    }

    public FileLoggerModule(ReactApplicationContext reactContext) {
        super(reactContext);
        this.reactContext = reactContext;
        sp = reactContext.getSharedPreferences("file-logger", MODE_PRIVATE);
        initLogSetting();
    }

    private void initLogSetting() {
        File externalCacheDir = reactContext.getExternalCacheDir();
        File logsDir;

        if (externalCacheDir != null) {
            logsDir = new File(externalCacheDir, "logs");
        } else {
            File internalCacheDir = reactContext.getCacheDir();
            logsDir = new File(internalCacheDir, "logs");
        }

        if (!logsDir.exists()) {
            logsDir.mkdirs();
        }

        logsDirectory = logsDir.getAbsolutePath();

        logSettingModel.setEnabled(sp.getBoolean("enabled", false));
        if (!logSettingModel.isEnabled()) {
            return;
        }
        logSettingModel.setLogLevel(sp.getInt("logLevel", LOG_LEVEL_ERROR));
        logSettingModel.setMaximumFileSize(sp.getInt("maximumFileSize", 1024 * 1024 * 5));
        logSettingModel.setMaximumNumberOfFiles(sp.getInt("maximumNumberOfFiles", 15));
    }

    private void configureByLogSetting() {
        if (!logSettingModel.isEnabled() || isConfigured) {
            return;
        }
        try {
            if (logsDirectory == null || logsDirectory.isEmpty()) {
                Log.e(NAME, "Logs directory is not initialized");
                return;
            }

            File logsDir = new File(logsDirectory);
            if (!logsDir.exists() && !logsDir.mkdirs()) {
                Log.e(NAME, "Failed to create logs directory: " + logsDirectory);
                return;
            }

            if (!logsDir.canWrite()) {
                Log.e(NAME, "Cannot write to logs directory: " + logsDirectory);
                return;
            }

            boolean dailyRolling = true;
            int maximumFileSize = logSettingModel.getMaximumFileSize();
            int maximumNumberOfFiles = logSettingModel.getMaximumNumberOfFiles();
            String logPrefix = reactContext.getPackageName();
            configureLogger(dailyRolling, maximumFileSize, maximumNumberOfFiles, logsDirectory, logPrefix);
            isConfigured = true;
            // persist configuration
            SharedPreferences.Editor editor = sp.edit();
            editor.putBoolean("enabled", logSettingModel.isEnabled());
            editor.putInt("logLevel", logSettingModel.getLogLevel());
            editor.putInt("maximumFileSize", logSettingModel.getMaximumFileSize());
            editor.putInt("maximumNumberOfFiles", logSettingModel.getMaximumNumberOfFiles());
            editor.apply();
        } catch (Exception ex) {
            Log.e(NAME, "Error configuring logger", ex);
            ex.printStackTrace();
        }
    }

    public String getName() {
        return NAME;
    }

    @ReactMethod
    public void updateLogSetting(ReadableMap map, Promise promise) {
        try {
            Boolean enabledValue = (Boolean) ReadableMapUtil.getValueFromMap(map, "enabled");
            if (enabledValue != null) {
                logSettingModel.setEnabled(enabledValue);
            }
            if (!logSettingModel.isEnabled()) {
                return;
            }
            Double logLevelValue = (Double) ReadableMapUtil.getValueFromMap(map, "logLevel");
            if (logLevelValue != null) {
                logSettingModel.setLogLevel(logLevelValue.intValue());
            }

            Double maximumFileSize = (Double) ReadableMapUtil.getValueFromMap(map, "maximumFileSize");
            if (maximumFileSize != null) {
                if (maximumFileSize.intValue() != logSettingModel.getMaximumFileSize()) {
                    isConfigured = false;
                }
                logSettingModel.setMaximumFileSize(maximumFileSize.intValue());
            }
            Double maximumNumberOfFiles = (Double) ReadableMapUtil.getValueFromMap(map, "maximumNumberOfFiles");
            if (maximumNumberOfFiles != null) {
                if (maximumNumberOfFiles.intValue() != logSettingModel.getMaximumNumberOfFiles()) {
                    isConfigured = false;
                }
                logSettingModel.setMaximumNumberOfFiles(maximumNumberOfFiles.intValue());
            }
            if (!isConfigured) {
                configureByLogSetting();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        promise.resolve(true);
    }

    @ReactMethod
    public void updateLogModel(ReadableMap map, Promise promise) {
        if (!logSettingModel.isEnabled()) {
            promise.resolve(true);
            return;
        }
        try {
            String storeId = (String) ReadableMapUtil.getValueFromMap(map, "storeId");
            if (storeId != null) {
                logHeaderModel.setStoreId(storeId);
            }
            String business = (String) ReadableMapUtil.getValueFromMap(map, "business");
            if (business != null) {
                logHeaderModel.setBusiness(business);
            }
            String employeeId = (String) ReadableMapUtil.getValueFromMap(map, "employeeId");
            if (employeeId != null) {
                logHeaderModel.setEmployeeId(employeeId);
            }
            String registerId = (String) ReadableMapUtil.getValueFromMap(map, "registerId");
            if (registerId != null) {
                logHeaderModel.setRegisterId(registerId);
            }
            Double registerNumber = (Double) ReadableMapUtil.getValueFromMap(map, "registerNumber");
            if (registerNumber != null) {
                logHeaderModel.setRegisterNumber(registerNumber.intValue());
            }
            String version = (String) ReadableMapUtil.getValueFromMap(map, "version");
            if (version != null) {
                logHeaderModel.setVersion(version);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        promise.resolve(true);
    }

    public static void configureLogger(boolean dailyRolling, int maximumFileSize, int maximumNumberOfFiles, String logsDirectory, String logPrefix) {
        LoggerContext loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();

        RollingFileAppender<ILoggingEvent> rollingFileAppender = new RollingFileAppender<>();
        rollingFileAppender.setContext(loggerContext);
        rollingFileAppender.setName(APPENDER_NAME);
        rollingFileAppender.setFile(logsDirectory + "/" + logPrefix + "-latest.log");

        if (dailyRolling) {
            SizeAndTimeBasedRollingPolicy<ILoggingEvent> rollingPolicy = new SizeAndTimeBasedRollingPolicy<>();
            rollingPolicy.setContext(loggerContext);
            rollingPolicy.setFileNamePattern(logsDirectory + "/" + logPrefix + "-%d{yyyy-MM-dd}.%i.log");
            rollingPolicy.setMaxFileSize(new FileSize(maximumFileSize));
            rollingPolicy.setTotalSizeCap(new FileSize(maximumNumberOfFiles * maximumFileSize));
            rollingPolicy.setMaxHistory(maximumNumberOfFiles);
            rollingPolicy.setParent(rollingFileAppender);
            rollingPolicy.start();
            rollingFileAppender.setRollingPolicy(rollingPolicy);

        } else if (maximumFileSize > 0) {
            FixedWindowRollingPolicy rollingPolicy = new FixedWindowRollingPolicy();
            rollingPolicy.setContext(loggerContext);
            rollingPolicy.setFileNamePattern(logsDirectory + "/" + logPrefix + "-%i.log");
            rollingPolicy.setMinIndex(1);
            rollingPolicy.setMaxIndex(maximumNumberOfFiles);
            rollingPolicy.setParent(rollingFileAppender);
            rollingPolicy.start();
            rollingFileAppender.setRollingPolicy(rollingPolicy);

            SizeBasedTriggeringPolicy triggeringPolicy = new SizeBasedTriggeringPolicy();
            triggeringPolicy.setContext(loggerContext);
            triggeringPolicy.setMaxFileSize(new FileSize(maximumFileSize));
            triggeringPolicy.start();
            rollingFileAppender.setTriggeringPolicy(triggeringPolicy);
        }

        PatternLayoutEncoder encoder = new PatternLayoutEncoder();
        encoder.setContext(loggerContext);
        encoder.setCharset(Charset.forName("UTF-8"));
        encoder.setPattern("%msg%n");
        encoder.start();

        rollingFileAppender.setEncoder(encoder);
        rollingFileAppender.start();

        renewAppender(rollingFileAppender);
    }

    private static void renewAppender(Appender appender) {
        ch.qos.logback.classic.Logger root = (ch.qos.logback.classic.Logger) LoggerFactory.getLogger(Logger.ROOT_LOGGER_NAME);
        root.setLevel(Level.DEBUG);

        // Stopping the previous appender to release any resources it might be holding (file handles) and to ensure a clean shutdown.
        Appender previousFileLoggerAppender = root.getAppender(APPENDER_NAME);
        if (previousFileLoggerAppender != null) {
            previousFileLoggerAppender.stop();
            root.detachAppender(APPENDER_NAME);
        }
        root.addAppender(appender);
    }

    @ReactMethod
    public void write(double level, String str) {
        if (!logSettingModel.isEnabled()) {
            return;
        }
        if (!isConfigured) {
            configureByLogSetting();
        }
        int levelInt = (int) level;
        if (levelInt == LOG_LEVEL_INFO) {
            logger.info(str);
        } else if (levelInt == LOG_LEVEL_DEBUG) {
            logger.debug(str);
        } else if (levelInt == LOG_LEVEL_WARNING) {
            logger.warn(str);
        } else if (levelInt == LOG_LEVEL_ERROR) {
            logger.error(str);
        }

    }

    @ReactMethod
    public void testNativeLogs(String level, String message) {
        LogEntryModel model = new LogEntryModel.Builder().action("TEST").level(LogLevel.fromValue(level)).message(message).build();
        writeNativeLogAsync(model);
    }

    @ReactMethod
    public void getLogFilePaths(Promise promise) {
        try {
            WritableArray result = Arguments.createArray();
            File[] files = getLogFiles();
            if (files != null) {
                for (File logFile : files) {
                    result.pushString(logFile.getAbsolutePath());
                }
            }
            promise.resolve(result);
        } catch (Exception e) {
            e.printStackTrace();
            promise.reject(e);
        }
    }

    @ReactMethod
    public void deleteLogFiles(Promise promise) {
        try {
            for (File file : getLogFiles()) {
                file.delete();
            }
            isConfigured = false;
            if (logSettingModel.isEnabled()) {
                configureByLogSetting();
            }
            promise.resolve(null);
        } catch (Exception e) {
            e.printStackTrace();
            promise.reject(e);
        }
    }

    @ReactMethod
    public void compressLogs(Promise promise) {
        try {
            File[] logFiles = getLogFiles();
            if (logFiles.length == 0) {
                promise.resolve(null);
                return;
            }

            // Create a zip file containing all log files
            File zipFile = new File(logsDirectory, "logs.zip");
            try (FileOutputStream fos = new FileOutputStream(zipFile);
                 ZipOutputStream zos = new ZipOutputStream(fos)) {

                byte[] buffer = new byte[1024];
                for (File logFile : logFiles) {
                    ZipEntry zipEntry = new ZipEntry(logFile.getName());
                    zos.putNextEntry(zipEntry);

                    // Use traditional I/O for Android 6+ compatibility
                    try (FileInputStream fis = new FileInputStream(logFile)) {
                        int length;
                        while ((length = fis.read(buffer)) > 0) {
                            zos.write(buffer, 0, length);
                        }
                    }
                    zos.closeEntry();
                }
            }
            promise.resolve(zipFile.getAbsolutePath());
        } catch (Exception e) {
            e.printStackTrace();
            promise.resolve(null);
        }
    }

    @ReactMethod
    public void sendLogFilesByEmail(ReadableMap options, Promise promise) {
        try {
            ReadableArray to = options.hasKey("to") ? options.getArray("to") : null;
            String subject = options.hasKey("subject") ? options.getString("subject") : null;
            String body = options.hasKey("body") ? options.getString("body") : null;
            boolean compressFiles = options.hasKey("compressFiles") && options.getBoolean("compressFiles");

            Intent intent = new Intent(Intent.ACTION_SEND_MULTIPLE, Uri.parse("mailto:"));
            intent.setType("plain/text");

            if (to != null) {
                intent.putExtra(Intent.EXTRA_EMAIL, readableArrayToStringArray(to));
            }
            if (subject != null) {
                intent.putExtra(Intent.EXTRA_SUBJECT, subject);
            }
            if (body != null) {
                intent.putExtra(Intent.EXTRA_TEXT, body);
            }

            ArrayList<Uri> uris = new ArrayList<>();
            File[] logFiles = getLogFiles();

            if (compressFiles && logFiles.length > 0) {
                // Create a zip file containing all log files
                File zipFile = new File(logsDirectory, "logs.zip");
                try (FileOutputStream fos = new FileOutputStream(zipFile);
                     ZipOutputStream zos = new ZipOutputStream(fos)) {

                    byte[] buffer = new byte[1024];
                    for (File logFile : logFiles) {
                        ZipEntry zipEntry = new ZipEntry(logFile.getName());
                        zos.putNextEntry(zipEntry);

                        // Use traditional I/O for Android 6+ compatibility
                        try (FileInputStream fis = new FileInputStream(logFile)) {
                            int length;
                            while ((length = fis.read(buffer)) > 0) {
                                zos.write(buffer, 0, length);
                            }
                        }
                        zos.closeEntry();
                    }
                }

                Uri zipUri = FileProvider.getUriForFile(
                        reactContext,
                        reactContext.getApplicationContext().getPackageName() + ".filelogger.provider",
                        zipFile);
                uris.add(zipUri);
            } else {
                // Send individual log files
                for (File file : logFiles) {
                    Uri fileUri = FileProvider.getUriForFile(
                            reactContext,
                            reactContext.getApplicationContext().getPackageName() + ".filelogger.provider",
                            file);
                    uris.add(fileUri);
                }
            }

            intent.putParcelableArrayListExtra(Intent.EXTRA_STREAM, uris);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_GRANT_READ_URI_PERMISSION);

            reactContext.startActivity(intent);

            promise.resolve(null);
        } catch (Exception e) {
            promise.reject(e);
        }
    }

    private File[] getLogFiles() {
        File directory = new File(logsDirectory);
        return directory.listFiles(new FilenameFilter() {
            @Override
            public boolean accept(File dir, String name) {
                return name.endsWith(".log");
            }
        });
    }

    private String[] readableArrayToStringArray(ReadableArray r) {
        int length = r.size();
        String[] strArray = new String[length];
        for (int i = 0; i < length; i++) {
            strArray[i] = r.getString(i);
        }
        return strArray;
    }

    public static void writeNativeLog(LogEntryModel entry) {
        writeNativeLogAsync(entry, false);
    }
    
    public static void writeNativeLogAsync(LogEntryModel entry) {
        writeNativeLogAsync(entry, true);
    }
    
    private static void writeNativeLogAsync(LogEntryModel entry, boolean async) {
        if (instance == null) {
            return;
        }
        if (!logSettingModel.isEnabled() || entry == null || entry.getLevel() == null) {
            return;
        }
        if (entry.getLevel().ordinal() < logSettingModel.getLogLevel()) {
            return;
        }
        
        if (async) {
            logWriteExecutor.submit(() -> processLogEntry(entry));
        } else {
            processLogEntry(entry);
        }
    }
    
    private static void processLogEntry(LogEntryModel entry) {

        LogEventModel model = new LogEventModel();
        model.setUuid(UUID.randomUUID().toString());
        model.setTs(TimeUtil.getUnixTimestamp());
        model.setAction(entry.getAction());
        model.setBusiness(logHeaderModel.getBusiness());
        model.setTags(entry.getTags());

        LogLevel level = entry.getLevel();
        if (level != null) {
            model.setLevel(level.getValue());
        }

        LogMobileData mobileData = new LogMobileData();
        mobileData.setStoreId(logHeaderModel.getStoreId());
        mobileData.setRegisterId(logHeaderModel.getRegisterId());
        Integer registerNumber = logHeaderModel.getRegisterNumber();
        if (registerNumber != null) {
            mobileData.setRegisterNumber(registerNumber.toString());
        }
        mobileData.setEmployeeId(logHeaderModel.getEmployeeId());
        mobileData.setVersion(logHeaderModel.getVersion());
        mobileData.setResult(entry.getResult());
        mobileData.setWorkflowId(entry.getWorkflowId());
        mobileData.setReason(entry.getReason());
        mobileData.setTime(TimeUtil.now());
        model.setMobileData(mobileData);

        LogEventModel.PrivateDataModel privateDataModel = new LogEventModel.PrivateDataModel();
        privateDataModel.setEvent(entry.getEvent());
        privateDataModel.setMessage(entry.getMessage());
        privateDataModel.setPayload(entry.getPayload());
        Exception error = entry.getError();
        if (error != null) {
            privateDataModel.setErrorMessage(error.getMessage());
            privateDataModel.setErrorStack(Arrays.toString(error.getStackTrace()));
        }

        model.setPrivateData(new LogPrivateData(privateDataModel));
        
        // Additional safety check before writing
        if (instance != null) {
            try {
                instance.write(LOG_LEVEL_INFO, gson.toJson(model));
            } catch (Exception e) {
                Log.e(NAME, "Error writing native log", e);
            }
        }
    }
}
