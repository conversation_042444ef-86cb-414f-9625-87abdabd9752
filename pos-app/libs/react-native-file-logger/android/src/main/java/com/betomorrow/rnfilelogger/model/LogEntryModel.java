package com.betomorrow.rnfilelogger.model;

public class LogEntryModel {
    private String action;
    private String event;
    private LogLevel level;
    private String result;
    private String workflowId;
    private String message;

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    private String reason;
    private Exception error;
    private Object payload;
    private String tags;

    // Default constructor
    public LogEntryModel() {}

    // Constructor with required fields
    public LogEntryModel(String action, LogLevel level) {
        this.action = action;
        this.level = level;
    }

    // Getters
    public String getAction() {
        return action;
    }

    public String getEvent() {
        return event;
    }

    public LogLevel getLevel() {
        return level;
    }

    public String getResult() {
        return result;
    }

    public String getWorkflowId() {
        return workflowId;
    }

    public String getMessage() {
        return message;
    }

    public Exception getError() {
        return error;
    }

    public Object getPayload() {
        return payload;
    }

    public String getTags() {
        return tags;
    }

    // Setters
    public void setAction(String action) {
        this.action = action;
    }

    public void setEvent(String event) {
        this.event = event;
    }

    public void setLevel(LogLevel level) {
        this.level = level;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public void setWorkflowId(String workflowId) {
        this.workflowId = workflowId;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public void setError(Exception error) {
        this.error = error;
    }

    public void setPayload(Object payload) {
        this.payload = payload;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    // Builder pattern for easier object creation
    public static class Builder {
        private LogEntryModel logEntry = new LogEntryModel();

        public Builder action(String action) {
            logEntry.action = action;
            return this;
        }

        public Builder event(String event) {
            logEntry.event = event;
            return this;
        }

        public Builder level(LogLevel level) {
            logEntry.level = level;
            return this;
        }

        public Builder result(String result) {
            logEntry.result = result;
            return this;
        }

        public Builder workflowId(String workflowId) {
            logEntry.workflowId = workflowId;
            return this;
        }

        public Builder message(String message) {
            logEntry.message = message;
            return this;
        }

        public Builder error(Exception error) {
            logEntry.error = error;
            return this;
        }

        public Builder payload(Object payload) {
            logEntry.payload = payload;
            return this;
        }

        public Builder tags(String tags) {
            logEntry.tags = tags;
            return this;
        }

        public Builder reason(String reason) {
            logEntry.reason = reason;
            return this;
        }

        public LogEntryModel build() {
            return logEntry;
        }
    }

    @Override
    public String toString() {
        return "LogEntryModel{" +
                "action='" + action + '\'' +
                ", event='" + event + '\'' +
                ", level=" + level +
                ", result='" + result + '\'' +
                ", workflowId='" + workflowId + '\'' +
                ", message='" + message + '\'' +
                ", error=" + error +
                ", payload=" + payload +
                ", tags=" + tags +
                '}';
    }
}