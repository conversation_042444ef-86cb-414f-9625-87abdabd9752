package com.betomorrow.rnfilelogger.model;

import com.storehub.rn.peripheral.model.BasePublicDataModel;
import com.storehub.rn.peripheral.model.LogPrivateData;

public class LogEventModel extends BasePublicDataModel {
    private LogPrivateData privateData;

    public LogPrivateData getPrivateData() {
        return privateData;
    }

    public void setPrivateData(LogPrivateData privateData) {
        this.privateData = privateData;
    }

    public static class PrivateDataModel {
        public String getEvent() {
            return event;
        }

        public void setEvent(String event) {
            this.event = event;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public String getErrorStack() {
            return errorStack;
        }

        public void setErrorStack(String errorStack) {
            this.errorStack = errorStack;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public Object getPayload() {
            return payload;
        }

        public void setPayload(Object payload) {
            this.payload = payload;
        }

        private String event;
        private String message;
        private String errorStack;
        private String errorMessage;
        private Object payload;
    }
}
