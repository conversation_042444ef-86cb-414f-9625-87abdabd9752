package com.betomorrow.rnfilelogger.model;


public enum LogLevel {
    Debug("debug"),
    Info("info"),
    Warning("warn"),
    Error("error");

    private final String value;

    LogLevel(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    // Method to get enum from string value
    public static LogLevel fromValue(String value) {
        for (LogLevel level : LogLevel.values()) {
            if (level.value.equals(value)) {
                return level;
            }
        }
        throw new IllegalArgumentException("Unknown LogLevel value: " + value);
    }

    @Override
    public String toString() {
        return value;
    }
}
