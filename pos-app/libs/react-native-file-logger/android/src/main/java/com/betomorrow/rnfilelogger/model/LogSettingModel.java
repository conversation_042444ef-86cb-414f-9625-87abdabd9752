package com.betomorrow.rnfilelogger.model;

import com.betomorrow.rnfilelogger.FileLoggerModule;

public class LogSettingModel {
    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public int getLogLevel() {
        return logLevel;
    }

    public void setLogLevel(int logLevel) {
        this.logLevel = logLevel;
    }

    public int getMaximumFileSize() {
        return maximumFileSize;
    }

    public void setMaximumFileSize(int maximumFileSize) {
        this.maximumFileSize = maximumFileSize;
    }

    public int getMaximumNumberOfFiles() {
        return maximumNumberOfFiles;
    }

    public void setMaximumNumberOfFiles(int maximumNumberOfFiles) {
        this.maximumNumberOfFiles = maximumNumberOfFiles;
    }

    private boolean enabled = false;
    private int logLevel = FileLoggerModule.LOG_LEVEL_ERROR;
    private int maximumFileSize = 1024 * 1024 * 5;
    private int maximumNumberOfFiles = 10;
}
