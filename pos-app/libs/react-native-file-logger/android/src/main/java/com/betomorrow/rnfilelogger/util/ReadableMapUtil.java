package com.betomorrow.rnfilelogger.util;

import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.bridge.ReadableType;

import javax.annotation.Nullable;

public class ReadableMapUtil {
  public static Object getValueFromMap(@Nullable ReadableMap readableMap, String key) {
    if (readableMap == null || !readableMap.hasKey(key)) {
      return null;
    }
    Object result;

    ReadableType readableType = readableMap.getType(key);
    switch (readableType) {
      case Null:
        result = null;
        break;
      case Boolean:
        result = readableMap.getBoolean(key);
        break;
      case Number:
        result = readableMap.getDouble(key);
        break;
      case String:
        result = readableMap.getString(key);
        break;
      case Map:
        result = readableMap.getMap(key);
        break;
      case Array:
        result = readableMap.getArray(key);
        break;
      default:
        throw new IllegalArgumentException("Could not covert object with key: " + key + '.');
    }
    return result;
  }

  public static Object getValueFromMapWithDefaultValue(@Nullable ReadableMap readableMap, String key, Object defaultValue) {
    Object result = getValueFromMap(readableMap, key);
    if (result != null) {
      return result;
    } else {
      return defaultValue;
    }
  }

  public static Integer getIntegerValueFromMapWithDefaultValue(@Nullable ReadableMap readableMap, String key, Integer defaultValue) {
    if (readableMap == null || !readableMap.hasKey(key)) {
      return defaultValue;
    }

    Integer result = readableMap.getInt(key);
    return result;
  }
}
