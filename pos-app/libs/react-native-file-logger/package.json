{"name": "react-native-file-logger", "title": "React Native File Logger", "version": "0.6.0", "description": "A simple file-logger for React Native", "main": "", "types": "", "source": "", "files": ["README.md", "android", "ios", "dist", "src", "RNFileLogger.podspec"], "scripts": {"build": "rimraf dist && tsc", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git+https://github.com/BeTomorrow/react-native-file-logger.git", "baseUrl": "https://github.com/BeTomorrow/react-native-file-logger"}, "homepage": "https://github.com/BeTomorrow/react-native-file-logger#readme", "keywords": ["react-native"], "author": "BeTomorrow <<EMAIL>> (https://github.com/BeTomorrow)", "license": "MIT", "licenseFilename": "LICENSE", "readmeFilename": "README.md", "peerDependencies": {"react": ">=16.0.0", "react-native": ">=0.60.0"}, "devDependencies": {"@babel/preset-env": "^7.26.9", "@release-it/conventional-changelog": "^10.0.0", "prettier": "^3.5.3", "react": "^19.1.0", "react-native": "0.79.1", "release-it": "^18.1.2", "rimraf": "^6.0.1", "typescript": "^5.8.3"}, "dependencies": {"util": "^0.12.5"}, "codegenConfig": {"name": "FileLoggerSpec", "type": "modules", "jsSrcsDir": "src", "android": {"javaPackageName": "com.betomorrow.rnfilelogger"}}, "release-it": {"hooks": {"before:init": ["npm run build"]}, "git": {"commitMessage": "chore: release ${version}", "tagName": "v${version}"}, "npm": {"publish": true}, "github": {"release": true}, "plugins": {"@release-it/conventional-changelog": {"preset": {"name": "conventionalcommits", "types": [{"type": "feat", "section": "✨ Features"}, {"type": "fix", "section": "🐛 Bug Fixes"}, {"type": "chore(deps)", "section": "🛠️ Dependency Upgrades"}, {"type": "chore", "section": "🧹 Chore"}, {"type": "docs", "section": "📚 Documentation"}]}}}}}