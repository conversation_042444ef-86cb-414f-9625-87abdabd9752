package com.storehub.mrs;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.WritableArray;
import com.facebook.react.bridge.WritableMap;
import com.storehub.mrs.mdns.MDNSListener;
import com.storehub.mrs.mdns.MDNSManager;
import com.storehub.mrs.utils.AesUtils;
import com.storehub.mrs.utils.CloseCons;
import com.storehub.mrs.utils.KeyCons;
import com.storehub.mrs.utils.MRSLogEvent;
import com.storehub.mrs.utils.MRSLogManager;
import com.storehub.mrs.ws.WsClient;
import com.storehub.mrs.ws.WsServer;
import com.storehub.mrs.ws.interfaces.OnClientsChangeListener;
import com.storehub.mrs.ws.interfaces.WsListener;
import com.storehub.mrs.ws.interfaces.WsPingPongListener;
import com.storehub.rn.peripheral.util.LogDestination;
import com.storehub.rn.peripheral.util.LogResult;

import org.java_websocket.WebSocket;
import org.java_websocket.framing.CloseFrame;

import java.net.InetSocketAddress;
import java.net.URI;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import timber.log.Timber;

/**
 * Created By: sqq
 * Created Time: 2020/6/16 18:27.
 */
@SuppressWarnings("SpellCheckingInspection")
public class NetWorkingNode implements WsListener, OnClientsChangeListener, WsPingPongListener {


    private volatile WsClient mClient;
    private volatile WsServer mServer;

    private static NetWorkingNode mInstance = null;
    private volatile boolean isServerRunning;
    public static final String MESSAGE_RECEIVED_EVENT_KEY = "mrs_message_received";
    private final Context ctx;
    private volatile String serverIp;
    private volatile int serverPort;
    private final HashMap<String, String> mHeaders = new HashMap<>();

    private NetWorkingNode(Context context) {
        this.ctx = context.getApplicationContext();
    }

    public static NetWorkingNode getInstance(Context context) {
        if (mInstance == null) {
            synchronized (NetWorkingNode.class) {
                if (mInstance == null) {
                    mInstance = new NetWorkingNode(context);
                }
            }
        }
        return mInstance;
    }

    public void writeToClient(String ip, String message) {
        if (mServer != null) {
            WebSocket conn = mServer.getClientSocket(ip);
            if (conn != null) {
                conn.send(AesUtils.encrypt(getBusinessName(), message));
            }
        }
    }

    public void closeClient(String ip, int code, String reason) {
        if (mServer != null && ip != null) {
            WebSocket conn = mServer.getClientSocket(ip);
            conn.close(code, reason);
        }
    }

    public boolean isServerRunning() {
        return isServerRunning;
    }

    public synchronized void writeToServer(String message) {
        if (isClientRunning()) {
            mClient.send(AesUtils.encrypt(getBusinessName(), message));
        }
    }

    public boolean isClientRunning() {
        return mClient != null && mClient.isOpen();
    }

    /**
     * Firstly, discover server ip and port by mdns,
     * Secondly, if the client is not open, connect to server use the ip and port;
     */
    public synchronized void discoverAndConnectServer(final Map<String, String> headers) {
        if (mHandler == null) {
            mHandler = new Handler(Looper.getMainLooper());
        }
        if (headers != null && !headers.isEmpty()) {
            MRSLogManager.getInstance().initMRSHeader(headers);
            this.mHeaders.putAll(headers);
        }
        if (isClientRunning()) {
            MRSLogManager.getInstance().logInfo(MRSLogEvent.START_DISCOVERY, "", "discoverAndConnectServer: the client is already running", LogDestination.LOCAL);
            return;
        }
        // MRSLogManager.getInstance().logInfo(MRSLogEvent.START_DISCOVERY, LogResult.SUCCEED, "discoverAndConnectServer: ");
        MDNSManager.getInstance(ctx).setListener(new MDNSListener() {
            @Override
            public void onServerFound(String ip, int port) {

                Timber.d("onServerFound() called with: " + "ip = [" + ip + "], port = [" + port + "]");
                if (isClientRunning()) {
                    MRSLogManager.getInstance().logInfo(MRSLogEvent.FOUND_MDNS_SERVICE, LogResult.SUCCEED, String.format("onServerFound: the client:%s is already running", ip + ":" + port + ",, " + this));
                    Timber.i("onServerFound: the client:%s is already running", ip + ":" + port + ",, " + this);
                } else {
                    serverIp = ip;
                    serverPort = port;
                    sendEvent("UPDATE_SERVER_IP", ip, port);
                    MRSLogManager.getInstance().updateServerIp(ip);
                    MRSLogManager.getInstance().logInfo(MRSLogEvent.FOUND_MDNS_SERVICE, LogResult.SUCCEED, "onServerFound() called with: " + "ip = [" + ip + "], port = [" + port + "]");
                    if (isServerRunning()) {
                        // no need client connection if it is the local server
                        return;
                    }
                    try {
                        URI uri = new URI("ws://" + ip + ":" + port);
                        mClient = new WsClient(uri, mHeaders, NetWorkingNode.this);
                        // run on NsdManager Thread
                        mClient.connectBlocking(3, TimeUnit.SECONDS);
                    } catch (Exception e) {
                        MRSLogManager.getInstance().logInfo(MRSLogEvent.START_WS_CLIENT, LogResult.Failed, "first time failed", LogDestination.BOTH);
                        try {
                            URI uri = new URI("ws://" + ip + ":" + port);
                            mClient = new WsClient(uri, mHeaders, NetWorkingNode.this);
                            // run on NsdManager Thread
                            mClient.connectBlocking(3, TimeUnit.SECONDS);
                        } catch (Exception ex) {
                            serverIp = null;
                            mClient = null;
                            ex.printStackTrace();
                            MRSLogManager.getInstance().logError(MRSLogEvent.START_WS_CLIENT, LogResult.Failed, ex);
                        }


                    }
                }
            }
        }).discoverNsdServer(getMDNSServiceName());
    }

    private String getBusinessName() {
        return mHeaders.get(KeyCons.KEY_BUSINESS);
    }

    private String getMDNSServiceName() {
        return mHeaders.get(KeyCons.KEY_STORE_ID);
    }

    public void closeClient() {
        if (isClientRunning()) {
            mClient.close();
        }
    }

    public void startServer(Map<String, String> headers) {
        Timber.i("startServer() called with: " + "headers = [" + headers + "]");
        MRSLogManager.getInstance().initMRSHeader(headers);
        if (mServer == null || !mServer.isRunning()) {
            mServer = new WsServer("0.0.0.0", 0, headers, this);
            mServer.setOnClientsChangeListener(this);
            mServer.setPingPongListener(this);
            mServer.start();
        } else {
            MRSLogManager.getInstance().logInfo(MRSLogEvent.START_WS_SERVER, LogResult.Failed, "startServer: Server is already running", LogDestination.BOTH);
        }
    }

    public void stopServer() {
        MDNSManager.getInstance(ctx).stopNsdServer();
        if (mServer != null) {
            try {
                mServer.stop(3000);
                MRSLogManager.getInstance().logInfo(MRSLogEvent.STOP_WS_SERVER, LogResult.SUCCEED, "");
            } catch (InterruptedException e) {
                MRSLogManager.getInstance().logInfo(MRSLogEvent.STOP_WS_SERVER, LogResult.Failed, e.getMessage(), LogDestination.BOTH);
                e.printStackTrace();
            } finally {
                Timber.d("stopServer: the WebSocket Server stop manually");
            }
        }
    }

    public void broadcast(String message) {
        if (mServer != null) {
            mServer.broadcast(AesUtils.encrypt(getBusinessName(), message));
        }
    }


    @Override
    public synchronized void onStart(boolean fromServer, int port) {
        Timber.i("onStart() called with: " + "fromServer = [" + fromServer + "], port = [" + port + "]");
        if (fromServer) {
            isServerRunning = true;
            sendEvent("SERVER_START", serverIp, port);
//            MRSLogManager.getInstance().updateRole(true);
            MRSLogManager.getInstance().logInfo(MRSLogEvent.START_WS_SERVER, LogResult.SUCCEED, "");
            MDNSManager.getInstance(ctx).registerNsdService(port, mServer.getFieldValue(KeyCons.KEY_STORE_ID));
        } else {
            sendEvent("CLIENT_CONNECTED", mClient.getIp(), mClient.getPort());
            MRSLogManager.getInstance().updateIp(mClient.getIp(), mClient.getPort());
            MRSLogManager.getInstance().logInfo(MRSLogEvent.START_WS_CLIENT, LogResult.SUCCEED, "");
        }
    }

    @Override
    public void read(boolean fromServer, WebSocket conn, String data) {
        Timber.d("read() called with: " + "fromServer = [" + fromServer + "], conn = [" + conn + "], data = [" + data + "]");
        if (!"com.storehub.mrs.test".equals(ctx.getPackageName())) {
            WritableMap map = Arguments.createMap();
            map.putBoolean("isServerReceived", fromServer);
            if (fromServer && conn != null) {
                map.putString("ip", conn.getRemoteSocketAddress().getAddress().getHostAddress());
            } else {
                map.putString("ip", serverIp == null ? "" : serverIp);
            }
            map.putString("event", "READ");
            map.putString("message", AesUtils.decrypt(getBusinessName(), data));
            RNMRSModule.senEventToRN(MESSAGE_RECEIVED_EVENT_KEY, map);
        }

    }

    private void sendEvent(String event, String ip, int port) {
        if (!"com.storehub.mrs.test".equals(ctx.getPackageName())) {
            WritableMap map = Arguments.createMap();
            map.putString("event", event);
            map.putString("ip", ip);
            map.putInt("port", port);
            RNMRSModule.senEventToRN(MESSAGE_RECEIVED_EVENT_KEY, map);
        }
    }

    private void sendEvent(String event, String ip, int port, int code) {
        if (!"com.storehub.mrs.test".equals(ctx.getPackageName())) {
            WritableMap map = Arguments.createMap();
            map.putString("event", event);
            map.putString("ip", ip);
            map.putInt("port", port);
            map.putInt("code", code);
            RNMRSModule.senEventToRN(MESSAGE_RECEIVED_EVENT_KEY, map);
        }
    }

    private volatile Handler mHandler = new Handler(Looper.getMainLooper());

    @Override
    public synchronized void onComplete(boolean fromServer, int code) {
        Timber.d("onComplete() called with: " + "fromServer = [" + fromServer + "], code = [" + code + "]");
        if (fromServer) {
            isServerRunning = false;

            try {
                MDNSManager.getInstance(ctx).stopNsdServer();
                if (mServer != null) {
                    mServer.stop();
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                sendEvent("SERVER_STOP", serverIp, serverPort);
                mServer = null;
            }
        } else {
            if (mClient != null) {
                sendEvent("CLIENT_CLOSE", mClient.getIp(), mClient.getPort(), code);
                Timber.d("onComplete: retry discover" + "], code = [" + code + "], " + mHandler);
                if (code != CloseCons.INVALID_AUTHENTICATION && code != CloseCons.DUPLICATED_REGISTER_ID && mHandler != null) {
                    // need stop discover before post new discover. Cos WIFI on/off
                    MDNSManager.getInstance(ctx).stopDiscover();
                    mHandler.removeCallbacksAndMessages(null);
                    mHandler.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            MDNSManager.getInstance(ctx).discoverNsdServer(getMDNSServiceName());
                        }
                    }, 5000);
                }
            } else {
                sendEvent("CLIENT_CLOSE", "", -1, CloseFrame.NEVER_CONNECTED);
            }
            mClient = null;

        }
    }

    @Override
    public void onClientJoined(WebSocket conn) {
        sendClientsEvent();
    }

    @Override
    public void onClientClosed(WebSocket conn) {
        sendClientsEvent();
    }

    private void sendClientsEvent() {
        if (mServer == null) return;
        if (!"com.storehub.mrs.test".equals(ctx.getPackageName())) {
            WritableMap map = Arguments.createMap();
            map.putString("event", "CLIENTS_CHANGED");
            WritableArray array = Arguments.createArray();
            Map<String, WebSocket> allClients = mServer.getAllClients();

            for (String ip : allClients.keySet()) {
                WebSocket conn = allClients.get(ip);
                WritableMap _map = Arguments.createMap();
                if (conn != null) {
                    Map<String, String> attachment = conn.getAttachment();
                    String registerId = attachment.get(KeyCons.KEY_REGISTER_ID);
                    String registerName = attachment.get(KeyCons.KEY_REGISTER_NAME);
                    _map.putString(KeyCons.KEY_REGISTER_ID, registerId);
                    _map.putString(KeyCons.KEY_REGISTER_NAME, registerName);
                }
                _map.putString("ip", ip);
                array.pushMap(_map);
            }
            map.putArray("clients", array);
            RNMRSModule.senEventToRN(MESSAGE_RECEIVED_EVENT_KEY, map);
        }
    }

    @Override
    public void onPing(WebSocket conn) {

    }

    @Override
    public void onPong(WebSocket conn) {
        if (conn != null) {
            InetSocketAddress inetSocketAddress = conn.getRemoteSocketAddress();
            if (inetSocketAddress != null && inetSocketAddress.getAddress() != null) {
                String ip = inetSocketAddress.getAddress().getHostAddress();
                sendEvent("SERVER_PONG", ip, 0);
            }
        }
    }

    public void destroy() {
        stopServer();
        closeClient();
        MDNSManager.getInstance(ctx).stopDiscover();
        if (mHandler != null) {
            mHandler.removeCallbacksAndMessages(null);
        }
        MRSLogManager.getInstance().logInfo(MRSLogEvent.COMMON_EVENT, "", "destroy() called with: ");
        mHandler = null;
    }
}
