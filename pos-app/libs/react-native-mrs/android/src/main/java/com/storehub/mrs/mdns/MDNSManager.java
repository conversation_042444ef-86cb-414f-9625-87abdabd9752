package com.storehub.mrs.mdns;

import android.content.Context;
import android.net.nsd.NsdManager;
import android.net.nsd.NsdServiceInfo;
import android.text.TextUtils;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.WritableMap;
import com.storehub.mrs.NetWorkingNode;
import com.storehub.mrs.RNMRSModule;
import com.storehub.mrs.inernal.ISearchServerCallback;
import com.storehub.mrs.utils.MRSLogEvent;
import com.storehub.mrs.utils.MRSLogManager;
import com.storehub.mrs.utils.MRSThreadFactory;
import com.storehub.rn.peripheral.util.LogDestination;
import com.storehub.rn.peripheral.util.LogResult;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import timber.log.Timber;

/**
 * Created By: sqq
 * Created Time: 2020/6/19 18:02.
 * <p>
 * All listener callbacks running in the NsdManager Thread
 */
@SuppressWarnings("SpellCheckingInspection")
public class MDNSManager {

    public static final String MDNS_SERVICE_TYPE = "_storehubmrs._tcp.";
    //    public static final String MDNS_SERVICE_TYPE = "_websocket._tcp.";
    public static final String MDNS_SERVICE_NAME = "WebSocketService_";

    public static final String MULTIPLE_MDNS_SERVICE = "MULTIPLE_MDNS_SERVICE";
    private static MDNSManager mInstance = null;

    private final NsdManager mNsdManager;

    private MDNSListener mListener = null;
    private String mDiscoverServerName;

    private MDNSManager(Context context) {
        mNsdManager = ((NsdManager) context.getSystemService(Context.NSD_SERVICE));
    }

    public MDNSManager setListener(MDNSListener mListener) {
        this.mListener = mListener;
        return this;
    }

    public static MDNSManager getInstance(Context ctx) {
        if (mInstance == null) {
            synchronized (MDNSManager.class) {
                if (mInstance == null) {
                    mInstance = new MDNSManager(ctx);
                }
            }
        }
        return mInstance;
    }

    private NsdManager.RegistrationListener mRegistrationListener;

    /**
     * only server use
     * <p>
     * the correct called chance is when the WsServer called onStart, there can get the WebSokct’s port
     */
    public void registerNsdService(int port, String mdnsServiceName) {
        Timber.d("registerNsdService() called with: " + "port = [" + port + "], mdnsServiceName = [" + mdnsServiceName + "]");
        NsdServiceInfo info = new NsdServiceInfo();
        info.setServiceName(MDNS_SERVICE_NAME + mdnsServiceName);
        info.setServiceType(MDNS_SERVICE_TYPE);
        info.setPort(port);
        mRegistrationListener = new NsdManager.RegistrationListener() {
            @Override
            public void onRegistrationFailed(NsdServiceInfo serviceInfo, int errorCode) {
                MRSLogManager.getInstance().logInfo(MRSLogEvent.START_MDNS_SERVICE, LogResult.Failed, "onRegistrationFailed() called with: " + "serviceInfo = [" + serviceInfo + "], errorCode = [" + errorCode + "]", LogDestination.BOTH);

            }

            @Override
            public void onUnregistrationFailed(NsdServiceInfo serviceInfo, int errorCode) {
                MRSLogManager.getInstance().logInfo(MRSLogEvent.STOP_MDNS_SERVICE, LogResult.Failed, "onUnregistrationFailed() called with: " + "serviceInfo = [" + serviceInfo + "], errorCode = [" + errorCode + "]", LogDestination.BOTH);
            }

            @Override
            public void onServiceRegistered(NsdServiceInfo serviceInfo) {
                MRSLogManager.getInstance().logInfo(MRSLogEvent.START_MDNS_SERVICE, LogResult.SUCCEED, "onServiceRegistered() called with: " + "serviceInfo = [" + serviceInfo + "]");
            }

            @Override
            public void onServiceUnregistered(NsdServiceInfo serviceInfo) {
                MRSLogManager.getInstance().logInfo(MRSLogEvent.STOP_MDNS_SERVICE, LogResult.SUCCEED, "onServiceUnregistered() called with: " + "serviceInfo = [" + serviceInfo + "]");
            }
        };
        try {
            mNsdManager.registerService(info, NsdManager.PROTOCOL_DNS_SD, mRegistrationListener);
        } catch (Exception e) {
            MRSLogManager.getInstance().logError(MRSLogEvent.START_MDNS_SERVICE, LogResult.Failed, e);
            Timber.w(e);
        }
    }

    private final NsdManager.DiscoveryListener mDiscoverListener = new NsdManager.DiscoveryListener() {
        @Override
        public void onStartDiscoveryFailed(String serviceType, int errorCode) {
            Timber.d("onStartDiscoveryFailed() called with: " + "serviceType = [" + serviceType + "], errorCode = [" + errorCode + "]");
            isDiscovering = false;
            MRSLogManager.getInstance().logInfo(MRSLogEvent.START_DISCOVERY, LogResult.Failed, "onStartDiscoveryFailed() called with: " + "serviceType = [" + serviceType + "], errorCode = [" + errorCode + "], will start again right now!", LogDestination.BOTH);
            mNsdManager.discoverServices(MDNS_SERVICE_TYPE, NsdManager.PROTOCOL_DNS_SD, this);
            isDiscovering = true;
        }

        @Override
        public void onStopDiscoveryFailed(String serviceType, int errorCode) {
            Timber.d("onStopDiscoveryFailed() called with: " + "serviceType = [" + serviceType + "], errorCode = [" + errorCode + "]");
            isDiscovering = false;
            MRSLogManager.getInstance().logInfo(MRSLogEvent.STOP_DISCOVERY, LogResult.Failed, "onStopDiscoveryFailed() called with: " + "serviceType = [" + serviceType + "], errorCode = [" + errorCode + "]", LogDestination.BOTH);
//            if (shouldStartDiscover) {
//                startDiscover();
//                shouldStartDiscover = false;
//            }
        }

        @Override
        public void onDiscoveryStarted(String serviceType) {
            Timber.d("onDiscoveryStarted() called with: " + "serviceType = [" + serviceType + "]");
            isDiscovering = true;
            // MRSLogManager.getInstance().logInfo(MRSLogEvent.START_DISCOVERY, LogResult.SUCCEED, "onDiscoveryStarted() called with: " + "serviceType = [" + serviceType + "]");
        }

        @Override
        public void onDiscoveryStopped(String serviceType) {
            Timber.d("onDiscoveryStopped() called with: " + "serviceType = [" + serviceType + "]");
            isDiscovering = false;
            MRSLogManager.getInstance().logInfo(MRSLogEvent.STOP_DISCOVERY, LogResult.SUCCEED, "onDiscoveryStopped() called with: " + "serviceType = [" + serviceType + "]");
//            if (shouldStartDiscover) {
//                startDiscover();
//                shouldStartDiscover = false;
//            }
        }

        @Override
        public void onServiceFound(NsdServiceInfo serviceInfo) {
            Timber.d("onServiceFound() called with: " + "serviceInfo = [" + serviceInfo + "]");
            if (serviceInfo != null && !TextUtils.isEmpty(serviceInfo.getServiceName())
                    && MDNS_SERVICE_TYPE.equals(serviceInfo.getServiceType())) {
                if (serviceInfo.getServiceName().equals(MDNS_SERVICE_NAME + mDiscoverServerName)) {
                    resolveDiscover(serviceInfo);
                } else if (serviceInfo.getServiceName().startsWith(MDNS_SERVICE_NAME + mDiscoverServerName)) {
                    mNsdManager.resolveService(serviceInfo, new NsdManager.ResolveListener() {
                        @Override
                        public void onResolveFailed(NsdServiceInfo nsdServiceInfo, int errorCode) {
                            Timber.d("onResolveFailed() called with: " + "serviceInfo = [" + nsdServiceInfo + "], errorCode = [" + errorCode + "]");
                            MRSLogManager.getInstance().logInfo(MRSLogEvent.RESOLVE_MDNS_SERVICE, LogResult.Failed, String.format("errorCode: %s", errorCode), LogDestination.BOTH);
                        }

                        @Override
                        public void onServiceResolved(NsdServiceInfo nsdServiceInfo) {
                            Timber.d("MULTIPLE_MDNS_SERVICE: " + "serviceInfo = [" + nsdServiceInfo + "], " + this);
                            String ip = "";
                            if (nsdServiceInfo.getHost() != null) {
                                ip = nsdServiceInfo.getHost().getHostAddress();
                                MRSLogManager.getInstance().logInfo(MRSLogEvent.MULTIPLE_MDNS_SERVICE, "", String.format("multiple serviceName: %s, ip: %s, port: %s", nsdServiceInfo.getServiceName(), ip, nsdServiceInfo.getPort()), LogDestination.BOTH);
                            }
                        }
                    });
                    WritableMap map = Arguments.createMap();
                    map.putString("event", MULTIPLE_MDNS_SERVICE);
                    RNMRSModule.senEventToRN(NetWorkingNode.MESSAGE_RECEIVED_EVENT_KEY, map);
                }
            }
        }

        @Override
        public void onServiceLost(NsdServiceInfo serviceInfo) {
            Timber.d("onServiceLost() called with: " + "serviceInfo = [" + serviceInfo + "]");
            try {
                MRSLogManager.getInstance().logInfo(MRSLogEvent.LOST_MDNS_SERVICE, LogResult.Failed, "onServiceLost() called with: " + "serviceInfo = [" + serviceInfo + "]");
            } catch (Throwable e) {
            }

        }
    };

    private volatile boolean isDiscovering = false;

    /**
     * if always running, the discover service will consume much performance.
     * <p>
     * So, after service found, should stop the discover service
     * <p>
     * <>UPDATE</>
     * 2022-09-30
     * Now need to make the discover listener always on
     */
    public void discoverNsdServer(String discoverServerName) {
        if (!isDiscovering) {
            try {
                this.mDiscoverServerName = discoverServerName;
                mNsdManager.discoverServices(MDNS_SERVICE_TYPE, NsdManager.PROTOCOL_DNS_SD, mDiscoverListener);
                isDiscovering = true;
                Timber.d("discoverNsdServer: discoverServerName: %s", discoverServerName);
                MRSLogManager.getInstance().logInfo(MRSLogEvent.START_DISCOVERY, "", String.format("discoverNsdServer: discoverServerName: %s", discoverServerName));
            } catch (Exception ex) {
                MRSLogManager.getInstance().logError(MRSLogEvent.START_DISCOVERY, LogResult.Failed, ex);
                Timber.w(ex);
            }
        } else {
//            MRSLogManager.getInstance().logInfo(MRSLogEvent.START_DISCOVERY, LogResult.Failed, "Discover is alreay running");
            Timber.i("Discover is alreay running");
        }
    }

    private ExecutorService mSearchExecutor;
    private volatile boolean isFoundServer;

    /**
     * check whether have a server in network, generally invoked by RN side
     */
    public void searchServer(final String serviceName, final long timeout, final ISearchServerCallback callback) {
        if (mSearchExecutor == null) {
            mSearchExecutor = Executors.newSingleThreadExecutor(MRSThreadFactory.getInstance());
        }
        Timber.d("searchServer() called with: " + "serviceName = [" + serviceName + "], timeout = [" + timeout + "], callback = [" + callback + "]");
        mSearchExecutor.execute(new Runnable() {
            @Override
            public void run() {
                final CountDownLatch downLatch = new CountDownLatch(1);
                isFoundServer = false;
                NsdManager.DiscoveryListener listener = new NsdManager.DiscoveryListener() {
                    @Override
                    public void onStartDiscoveryFailed(String serviceType, int errorCode) {

                    }

                    @Override
                    public void onStopDiscoveryFailed(String serviceType, int errorCode) {

                    }

                    @Override
                    public void onDiscoveryStarted(String serviceType) {

                    }

                    @Override
                    public void onDiscoveryStopped(String serviceType) {

                    }

                    @Override
                    public void onServiceFound(NsdServiceInfo serviceInfo) {
                        Timber.d("onServiceFound() called with: " + "serviceInfo = [" + serviceInfo + "]");
                        if (serviceInfo != null && !TextUtils.isEmpty(serviceInfo.getServiceName())
                                && serviceInfo.getServiceName().startsWith(MDNS_SERVICE_NAME + serviceName)
                                && MDNS_SERVICE_TYPE.equals(serviceInfo.getServiceType())) {
                            MRSLogManager.getInstance().logInfo(MRSLogEvent.SEARCH_MDNS_SERVICE, LogResult.SUCCEED, "onServiceFound() called with: " + "serviceInfo = [" + serviceInfo + "]");
                            isFoundServer = true;
                            downLatch.countDown();
                        }

                    }

                    @Override
                    public void onServiceLost(NsdServiceInfo serviceInfo) {

                    }
                };
                mNsdManager.discoverServices(MDNS_SERVICE_TYPE, NsdManager.PROTOCOL_DNS_SD, listener);

                try {
                    downLatch.await(timeout, TimeUnit.SECONDS);
                } catch (InterruptedException e) {
                    MRSLogManager.getInstance().logError(MRSLogEvent.SEARCH_MDNS_SERVICE, LogResult.Failed, e);
                    e.printStackTrace();
                } finally {
                    try {
                        mNsdManager.stopServiceDiscovery(listener);
                    } catch (Exception e) {
                        MRSLogManager.getInstance().logError(MRSLogEvent.SEARCH_MDNS_SERVICE, LogResult.Failed, e);
                        e.printStackTrace();
                    } finally {
                        callback.callback(isFoundServer);
                    }
                }
            }
        });
    }


    public void resolveDiscover(NsdServiceInfo serviceInfo) {
        try {
            mNsdManager.resolveService(serviceInfo, new NsdManager.ResolveListener() {
                @Override
                public void onResolveFailed(NsdServiceInfo serviceInfo, int errorCode) {
                    MRSLogManager.getInstance().logInfo(MRSLogEvent.RESOLVE_MDNS_SERVICE, LogResult.Failed, String.format("errorCode: %s", errorCode));
                    Timber.d("onResolveFailed() called with: " + "serviceInfo = [" + serviceInfo + "], errorCode = [" + errorCode + "]");
                }

                @Override
                public void onServiceResolved(NsdServiceInfo serviceInfo) {
                    Timber.d("onServiceResolved() called with: " + "serviceInfo = [" + serviceInfo + "], " + this);
                    // serviceType is

                    // change equals to startWith.
                    // in abnormal case, maybe there are more than one servers launched,
                    // here’s MDNS_SERVICE_NAME will change to MDNS_SERVICE_NAME（2）...
                    // so in case of this, use startWith to avoid the clients can't find server info
                    if (serviceInfo != null && !TextUtils.isEmpty(serviceInfo.getServiceName())
                            && serviceInfo.getServiceName().startsWith(MDNS_SERVICE_NAME + mDiscoverServerName)) {
                        int port = serviceInfo.getPort();
                        String ip = serviceInfo.getHost().getHostAddress();
                        Timber.i("onServiceResolved: %s:%s", ip, port);
                        if (mListener != null) {
                            mListener.onServerFound(ip, port);
                        }
                        try {
                            MRSLogManager.getInstance().logInfo(MRSLogEvent.RESOLVE_MDNS_SERVICE, LogResult.SUCCEED, "onServiceResolved() called with" + serviceInfo);
                        } catch (Throwable e) {
                        }
                    }
                }
            });
        } catch (Exception e) {
            MRSLogManager.getInstance().logError(MRSLogEvent.FOUND_MDNS_SERVICE, LogResult.Failed, e);
            Timber.w(e);
        }
    }


    public void stopNsdServer() {
        try {
            if (mRegistrationListener != null) {
                mNsdManager.unregisterService(mRegistrationListener);
            }
        } catch (Exception e) {
            MRSLogManager.getInstance().logError(MRSLogEvent.STOP_MDNS_SERVICE, LogResult.Failed, e);
            Timber.w(e);
        }
    }

    public void stopDiscover() {
        try {
            mNsdManager.stopServiceDiscovery(mDiscoverListener);
        } catch (Exception e) {
            MRSLogManager.getInstance().logError(MRSLogEvent.STOP_DISCOVERY, LogResult.Failed, e);
            Timber.w(e);
        }
    }


}
