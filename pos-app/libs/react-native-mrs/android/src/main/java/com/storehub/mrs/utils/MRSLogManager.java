package com.storehub.mrs.utils;

import com.betomorrow.rnfilelogger.FileLoggerModule;
import com.betomorrow.rnfilelogger.model.LogEntryModel;
import com.storehub.rn.peripheral.model.LogMobileData;
import com.storehub.rn.peripheral.model.mrs.MRSExecutionPrivateData;
import com.storehub.rn.peripheral.model.mrs.MRSMDNSWebSocketTrackLogModel;
import com.storehub.rn.peripheral.util.LogDestination;
import com.storehub.rn.peripheral.util.LogDestinationHelper;
import com.storehub.rn.peripheral.util.LogLevel;
import com.storehub.rn.peripheral.util.SHLogManager;

import org.java_websocket.WebSocket;

import java.net.InetAddress;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import javax.annotation.Nonnull;

/**
 * Created By: sqq
 * Created Time: 2022/11/28 15:30.
 */
public class MRSLogManager {

    private static MRSLogManager instance;
    private final MRSExecutionPrivateData.MRSRegister mRegister;


    public static MRSLogManager getInstance() {
        if (instance == null) {
            synchronized (MRSLogManager.class) {
                if (instance == null) {
                    instance = new MRSLogManager();
                }
            }
        }
        return instance;
    }

    public MRSLogManager() {
        mRegister = new MRSExecutionPrivateData.MRSRegister();
    }

    public void logError(MRSExecutionPrivateData privateData, Exception ex, LogDestination destination) {

        if (LogDestinationHelper.shouldLogRemotely(destination)) {
            MRSMDNSWebSocketTrackLogModel mDnsLogModel = new MRSMDNSWebSocketTrackLogModel();

            mDnsLogModel.setLevel(LogLevel.ERROR);
            SHLogManager.getInstance().initGeneralData(mDnsLogModel);
            mDnsLogModel.setPrivateData(new MRSMDNSWebSocketTrackLogModel.PrivateData(privateData));

            LogMobileData mobileData = mDnsLogModel.getMobileData();
            if (null != mobileData) {
                mobileData.setReason(ex.getMessage());
                mobileData.setResult(privateData.getResult());
            }
            privateData.setErrorDetail(Arrays.toString(ex.getStackTrace()));
            SHLogManager.getInstance().log(mDnsLogModel);
        }
        
        if (LogDestinationHelper.shouldLogLocally(destination)) {
            LogEntryModel model = new LogEntryModel.Builder()
                    .level(com.betomorrow.rnfilelogger.model.LogLevel.Error)
                    .action("MRS_Set_Up")
                    .tags("mrs")
                    .reason(ex.getMessage())
                    .result(privateData.getResult())
                    .event(privateData.getEvent())
                    .message(privateData.getMessage())
                    .error(ex)
                    .payload(privateData)
                    .build();
            // 错误日志保持同步，确保关键错误能被立即记录
            FileLoggerModule.writeNativeLog(model);
        }
    }

    public void logError(String event, String result, Exception ex) {
        MRSExecutionPrivateData privateData = getDataWithMessage(event, result, ex.getMessage());
        logError(privateData, ex, LogDestination.BOTH);
    }

    public void logError(String event, String result, int code, Exception ex) {
        MRSExecutionPrivateData privateData = getDataWithMessage(event, result, ex.getMessage());
        privateData.setCode(String.valueOf(code));
        logError(privateData, ex, LogDestination.BOTH);
    }


    public void logInfo(MRSExecutionPrivateData privateData, LogDestination destination) {
        if (LogDestinationHelper.shouldLogRemotely(destination)) {
            MRSMDNSWebSocketTrackLogModel mDnsLogModel = new MRSMDNSWebSocketTrackLogModel();

            mDnsLogModel.setLevel(LogLevel.INFO);
            SHLogManager.getInstance().initGeneralData(mDnsLogModel);
            mDnsLogModel.setPrivateData(new MRSMDNSWebSocketTrackLogModel.PrivateData(privateData));

            LogMobileData mobileData = mDnsLogModel.getMobileData();
            if (mobileData != null) {
                mobileData.setResult(privateData.getResult());
            }

            SHLogManager.getInstance().log(mDnsLogModel);
        }

        if (LogDestinationHelper.shouldLogLocally(destination)) {
            LogEntryModel model = new LogEntryModel.Builder()
                    .level(com.betomorrow.rnfilelogger.model.LogLevel.Info)
                    .action("MRS_Set_Up")
                    .tags("mrs")
                    .result(privateData.getResult())
                    .event(privateData.getEvent())
                    .message(privateData.getMessage())
                    .payload(privateData)
                    .build();
            FileLoggerModule.writeNativeLogAsync(model);
        }
    }

    public void logInfo(String event, String result, String message, LogDestination destination) {
        MRSExecutionPrivateData privateData = getDataWithMessage(event, result, message);
        logInfo(privateData, destination);
    }

    public void logInfo(String event, String result, String message) {
        MRSExecutionPrivateData privateData = getDataWithMessage(event, result, message);
        logInfo(privateData, LogDestination.LOCAL);
    }

    public void logClientsChanged(String event, String result, int code, String message, WebSocket newClient, WebSocket removed) {
        MRSExecutionPrivateData privateData = getDataWithMessage(event, result, message);
        privateData.setNewClient(webSocket2MRSRegister(newClient));
        privateData.setRemovedClient(webSocket2MRSRegister(removed));
        privateData.setCode(String.valueOf(code));
        logInfo(privateData, LogDestination.BOTH);
    }

    private MRSExecutionPrivateData.MRSRegister webSocket2MRSRegister(WebSocket conn) {
        MRSExecutionPrivateData.MRSRegister register = new MRSExecutionPrivateData.MRSRegister();
        if (conn == null) return register;
        InetAddress inetAddress = conn.getRemoteSocketAddress().getAddress();
        if (inetAddress != null) {
            String ip = inetAddress.getHostAddress();
            register.setIp(ip);
        }
        Map<String, String> attachment = conn.getAttachment();
        if (attachment != null && !attachment.isEmpty()) {
            register.setRegisterId(attachment.get(KeyCons.KEY_REGISTER_ID));
            register.setRegisterName(attachment.get(KeyCons.KEY_REGISTER_NAME));
            register.setStoreName(attachment.get(KeyCons.KEY_STORE_NAME));
            register.setBusinessName(attachment.get(KeyCons.KEY_BUSINESS));
        }
        return register;
    }

    public void logInfo(String event, String result, int code, String message) {
        MRSExecutionPrivateData privateData = getDataWithMessage(event, result, message);
        privateData.setCode(String.valueOf(code));
        logInfo(privateData, LogDestination.BOTH);
    }


    public MRSExecutionPrivateData getDataWithMessage(String event, String result, String message) {
        MRSExecutionPrivateData privateData = new MRSExecutionPrivateData();
        privateData.setMessage(message);
        privateData.setEvent(event);
        privateData.setResult(result);
        privateData.setRegister(mRegister);
        return privateData;
    }

    public MRSExecutionPrivateData.MRSRegister initMRSHeader(@Nonnull Map<String, String> headers) {
        if (headers == null) return mRegister;
        mRegister.setRegisterId(headers.get(KeyCons.KEY_REGISTER_ID));
        mRegister.setBusinessName(headers.get(KeyCons.KEY_BUSINESS));
        mRegister.setRegisterName(headers.get(KeyCons.KEY_REGISTER_NAME));
        mRegister.setStoreName(headers.get(KeyCons.KEY_STORE_NAME));
        mRegister.setStoreId(headers.get(KeyCons.KEY_STORE_ID));
        return mRegister;
    }

    public void updateIp(String ip, int port) {
        mRegister.setIp(ip);
        mRegister.setPort(String.valueOf(port));
    }

    public void updateServerIp(String ip) {
        mRegister.setServerIp(ip);
    }

    public void updateClients(Map<String, WebSocket> clients) {
        List<MRSExecutionPrivateData.MRSRegister> mClients = new ArrayList<>();
        if (clients != null && !clients.isEmpty()) {
            for (WebSocket conn : clients.values()) {
                mClients.add(webSocket2MRSRegister(conn));
            }
        }
        mRegister.setClients(mClients);
    }

//    public void updateRole(boolean isLocalServer) {
//        mRegister.setLocalServer(isLocalServer);
//    }

}
