//
//  NetWorkingNode.swift
//  RNMrs
//
//  Created by <PERSON> on 2022/10/19.
//

import Foundation

let MESSAGE_RECEIVED_EVENT_KEY = "mrs_message_received"
class NetWorkingNode {
    
    weak var eventDelegate: EventSender?
    
    private var mClient: WsClient?
    private var mServer: WsServer?
        
    private var serverIp: String?
    private var serverPort: Int?
    
    private var isSearchMDNServer: Bool = false
    
    private var mhaders: [String: String] = [:]
    
    public static let instance = NetWorkingNode()
    
    // MDDNS
    private var serverFoundCallback: ((String, Int) -> Void)?
    private var serverDuplicationCallback: ((String, Int) -> Void)?
    private var discoverMDNSTimer: Timer?

    private init() {
        serverFoundCallback = {[unowned self] ip, port in
            debugPrint("📢 MDNS client found server \(ip):\(port)")
            
            if isSearchMDNServer {
                isSearchMDNServer = false
                eventDelegate?.searchFoundServer()
                return
            }
                        
            if serverIp == nil {
                // self is not the master(server), so just bonjour ip and port
                serverIp = ip
                serverPort = port
            }
            
            // self is master(server), just master's ip and port. beacuase when self is master, bonjour's ip is wrong.
            
            let dic = [
                "ip": serverIp ?? "",
                "port": serverPort ?? -1,
                "event": "UPDATE_SERVER_IP"
            ] as [String : Any]
            eventDelegate?.sendEventToRN(dic: dic)
            
            if (isClientRunning()) {
                MRSLog.instance.logInfo(event: .FOUND_MDNS_SERVICE, result: .Succeed, message: "👹 onServerFound: the client \(serverIp ?? ""):\(serverPort ?? 0) is already running")
                debugPrint("📢 MDNS client found server, client is already conect to server \(ip):\(port)")
            } else {
                
                MRSLog.instance.updateServerIp(ip: serverIp ?? "")
                MRSLog.instance.logInfo(event: .FOUND_MDNS_SERVICE, result: .Succeed, message: "👹 found server \(ip):\(port)")
                if (isServerRunning()) {
                    // no need client connection if it is the local server
                    return
                }
                let uri = URL(string: "ws://\(serverIp ?? ""):\(serverPort ?? -1)")
                
                if let uri = uri {
                    mClient = WsClient(url: uri, header: mhaders, wsListenerDelegate: self)
                    mClient?.pingPongDelegate = self
                } else {
                    MRSLog.instance.logError(event: .START_WS_CLIENT, result: .Failed, code: WebSocketClientError.INVALID_SERVER_IP, errorMsg: "👹 client init uri failed \(serverIp)")
                    debugPrint("❌ MDNS startDiscoverServer: uri is not exist")
                }
            }
        }
        
        serverDuplicationCallback = { [unowned self] ip, port in
            debugPrint("📢 MDNS server duplication \(ip):\(port)")
            MRSLog.instance.logError(event: .MULTIPLE_MDNS_SERVICE, result: .Failed, code: MDNSError.MULTIPLE_SERVICE, errorMsg: "👹 multiple service ip: \(ip) port: \(port)")
            let dic = ["event": "MULTIPLE_MDNS_SERVICE"]
            eventDelegate?.sendEventToRN(dic: dic)
        }
    }
    
    func writeToClient(ip: String, message: String) {
        debugPrint("👹NetworkingNode writeToClient ip = \(ip), reason = \(message)")
        if let mServer = mServer {
            let text = AESUtil.encrypt(content: message, key: getBussinessName())
            if let text = text {
                mServer.writeClient(ip, msg: text)
            } else {
                MRSLog.instance.logError(event: .AES_ENCRYPT_ERROR, code: AESError.AES_ENCRYPT_ERROR, errorMsg: "👹 write to client: msg encrypt failed! msg=\(message)")
                debugPrint("❌ write to client: msg encrypt failed!")
            }
        }
    }
    
    func closeClient(ip: String, code: Int, reason: String) {
        debugPrint("👹NetworkingNode closeClient ip = \(ip), reason = \(reason)")
        if let mServer = mServer {
            let clientSocket = mServer.getClientSocket(ip: ip)
            if let clientSocket = clientSocket {
                clientSocket.close(withCode: code, reason: reason)
            } else {
                debugPrint("❌ closeClient: client doesn't exist!")
            }
        }
    }
    
    func isServerRunning() -> Bool {
        let bool = mServer?.isServerRunning() ?? false
        debugPrint("👹NetworkingNode isServerRunning = \(bool)")
        return bool
    }
    
    
    func writeToServer(message: String) {
        debugPrint("👹NetworkingNode writeToServer msg = \(message)")
        if (isClientRunning()) {
            let text = AESUtil.encrypt(content: message, key: getBussinessName())
            if let text = text {
                mClient?.send(msg: text)
            } else {
                MRSLog.instance.logError(event: .AES_ENCRYPT_ERROR, code: AESError.AES_ENCRYPT_ERROR, errorMsg: "👹 write to server: msg encrypt failed! msg=\(message)")
                debugPrint("❌ write to server: msg encrypt failed!")
            }
        } else {
            debugPrint("❌ write to server: Client wasn't running!")
        }
    }
    
    func isClientRunning() -> Bool {
        let bool = mClient?.isClientRunning() ?? false
        debugPrint("👹NetworkingNode isClientRunning = \(bool)")
        return bool
    }
    

    func discoverAndConnectServer(headers: [String:String]) {
        debugPrint("👹 discoverAndConnectServer header = \(headers)")
        
        if (isClientRunning()) {
            MRSLog.instance.logInfo(event: .START_DISCOVERY, message: "👹 discoverAndConnectServer: the client is already running")
            debugPrint("❌ discoverAndConnectServer: the client is already running")
            return
        }
        
        if (!headers.isEmpty) {
            mhaders = headers
            MRSLog.instance.initMRSHeader(headers: headers)
        }
        self.discoveryMDNS()
    }
    
    
    /// discoveryMDNS
    /// description:
    /// because the MDNS client can't redisover the MDNS server  when the MDNS that from offline to online
    /// so need to a timer to discover the server, the rediscover time is 30s
    func discoveryMDNS(isSearch: Bool = false) {
        self.isSearchMDNServer = isSearch
        // let message = isSearch ? "👹 search server to discover" : "👹 discoverAndConnectServer started to discover"
        // MRSLog.instance.logInfo(event: .START_DISCOVERY, result: .Succeed, message: message)
        MDNSManager.instance.startDiscoverServer(self.getMDNSServiceName(), serverFoundCallback: self.serverFoundCallback, serverDuplicationCallback: serverDuplicationCallback)
    }
    
    func closeClient(sendEvent: Bool = false) {
        debugPrint("👹NetworkingNode closeClient")
        if sendEvent {
            let dic = [
                "event": "CLIENT_CLOSE",
                "ip": mClient?.mIp ?? "",
                "port": mClient?.mPort ?? -1,
                "code": -2
            ] as [String : Any]
            eventDelegate?.sendEventToRN(dic: dic)
        }
        
        serverIp = nil
        serverPort = nil
        mClient?.close()
        mClient?.pingPongDelegate = nil
        mClient?.wsListerDelegate = nil
        mClient = nil
    }
    
    func starServer(header: [String: String]) {
        debugPrint("👹NetworkingNode starServer header = \(header)")
        debugPrint("isServerRunning == \(isServerRunning())")
        debugPrint("ip ===== \(header["ip"])")
        if !isServerRunning(), let ip = header["ip"] {
            MRSLog.instance.initMRSHeader(headers: header)
            debugPrint("👹NetworkingNode starServer header = \(header)")
            serverIp = ip
            serverPort = 8888
            mhaders = header
            mServer = WsServer(ip:ip, port: 8888, header: header)
            mServer?.wsListerDelegate = self
            mServer?.onClientChangedDelegate = self
        } else {
            if header["ip"] == nil {
                MRSLog.instance.logError(event: .START_WS_SERVER, result: .Failed, code: WebSocketServerError.INVALID_IP, errorMsg: "👹 NetworkingNode starServer failed, ip is not exist")
                debugPrint("👹NetworkingNode starServer failed, ip is not exist")
            } else {
                MRSLog.instance.logInfo(event: .START_WS_SERVER, message: "👹 NetworkingNode starServer already running")
                debugPrint("👹NetworkingNode starServer already running = \(header)")
            }
        }
    }
    
    func stopServer() {
        debugPrint("👹NetworkingNode stopServer")
        MRSLog.instance.logInfo(event: .STOP_WS_SERVER, result: .Succeed, message: "👹 stop Server")
        MDNSManager.instance.stopServer();
        stopWSServer()
    }
    
    private func stopWSServer() {
        mServer?.stop()
        mServer?.wsListerDelegate = nil
        mServer?.onClientChangedDelegate = nil
        serverIp = nil
        serverPort = nil
    }
    
    func broadcast(message: String) {
        debugPrint("👹NetworkingNode starServer broadcast message = \(message)")
        let text = AESUtil.encrypt(content: message, key: getBussinessName())
        if let text = text {
            mServer?.broadcast(message: text)
        } else {
            MRSLog.instance.logError(event: .AES_ENCRYPT_ERROR, code: AESError.AES_ENCRYPT_ERROR, errorMsg: "👹 broadcast: msg encrypt failed! msg=\(message)")
            debugPrint("❌ sever broadcast: msg = \(message) encrypt failed!")
        }
    }
    
    func destroy() {
        debugPrint("👹NetworkingNode destroy")
        discoverMDNSTimer?.invalidate()
        stopServer()
        closeClient(sendEvent: true)
        MDNSManager.instance.stopDiscover()
        MRSLog.instance.logInfo(event: .COMMON_EVENT, message: "👹 destroy() called with:")
    }
}

extension NetWorkingNode {
    private func getBussinessName() -> String {
        return mhaders[KeyCons.KEY_BUSINESS] ?? ""
    }
    
    private func getMDNSServiceName() -> String {
        return mhaders[KeyCons.KEY_STORE_ID] ?? ""
    }
}

// MARK: - OnClientChangedListener
extension NetWorkingNode: OnClientChangedListener {
    
    func onClientJoined() {
        sendClientsEvent()
    }
    
    func onClientClosed() {
        sendClientsEvent()
    }
    
    private func sendClientsEvent() {
        if let mServer = mServer {
            var dic = [
                "event": "CLIENTS_CHANGED",
            ] as [String : Any]
            
            var clients = [[String: Any]]()
            
            for clientRequest in mServer.getClientRequests() {
                let registerId = clientRequest.allHTTPHeaderFields?[KeyCons.KEY_REGISTER_ID]
                let registerName = clientRequest.allHTTPHeaderFields?[KeyCons.KEY_REGISTER_NAME]
                let ip = clientRequest.allHTTPHeaderFields?["ip"]
                var subDic = [String: Any]()
                if let registerId = registerId, let ip = ip, let registerName = registerName {
                    subDic[KeyCons.KEY_REGISTER_ID] = registerId
                    subDic[KeyCons.KEY_REGISTER_NAME] = registerName
                    subDic["ip"] = ip
                }
                
                clients.append(subDic)
            }
            
            dic["clients"] = clients
            
            eventDelegate?.sendEventToRN(dic: dic)
        }
    }
}

// MARK: - WsListener
extension NetWorkingNode: WsListener {
    func onStar(isFromServer: Bool, ip:String, port: Int) {
        debugPrint("👹NetworkingNode onStar \(isFromServer ? "server": "client"), ip = \(ip), port = \(port)")

        if (isFromServer) {
            MRSLog.instance.logInfo(event: .START_WS_SERVER, result: .Succeed, message: "👹 server started \(ip):\(port)")
            
            let dic = [
                "event": "SERVER_START",
                "ip": ip,
                "port": port,
            ] as [String : Any]
            self.serverIp = ip
            self.serverPort = port
            eventDelegate?.sendEventToRN(dic: dic)
            
            let storeId = mServer?.getFieldValue(key: KeyCons.KEY_STORE_ID) ?? ""

            MDNSManager.instance.startServer(port: port, mdnsServiceName: "\(storeId)")
            let uri = URL(string: "ws://\(serverIp ?? ""):\(serverPort ?? -1)")
            
            if let uri = uri {
                closeClient()
                mClient = WsClient(url: uri, header: mhaders, wsListenerDelegate: self)
                mClient?.pingPongDelegate = self
            } else {
                MRSLog.instance.logError(event: .START_WS_CLIENT, result: .Failed, code: WebSocketClientError.INVALID_SERVER_IP, errorMsg: "👹 client init uri failed \(serverIp)")
                debugPrint("❌ MDNS startDiscoverServer: uri is not exist")
            }
            
            DispatchQueue.global().asyncAfter(deadline: .now() + 1) {
                self.discoveryMDNS()
            }
        } else {
            MRSLog.instance.updateIp(ip: ip, port: port)
            MRSLog.instance.logInfo(event: .START_WS_CLIENT, result: .Succeed, message: "👹 Client started \(ip):\(port)")
            
            let dic = [
                "event": "CLIENT_CONNECTED",
                "ip": ip,
                "port": port,
            ] as [String : Any]
            eventDelegate?.sendEventToRN(dic: dic)
        }
    }
    
    func read(isFromServer: Bool, webSocket: PSWebSocket, data: String) {
        
        let logMessage = "👹NetworkingNode read \(isFromServer ? "server": "client"), message = \(AESUtil.decrypt(content: data, key: getBussinessName()))"
        debugPrint(logMessage)

        let ip = isFromServer ? mServer?.getIPByWebsocket(webSocket) : serverIp
        
        if let decryptMsg = AESUtil.decrypt(content: data, key: getBussinessName()) {
            let dic = [
                "event": "READ",
                "isServerReceived": isFromServer,
                "ip": ip ?? "",
                "message": decryptMsg
            ] as [String: Any]
            eventDelegate?.sendEventToRN(dic: dic)
        } else {
            MRSLog.instance.logError(event: .AES_DECRYPT_ERROR, code: AESError.AES_ENCRYPT_ERROR, errorMsg: "👹 NetworkingNode read \(isFromServer ? "server": "client"), message =\(data)")
        }
    }
    
    func onComplete(isFromServer: Bool, code: Int) {
        debugPrint("👹NetworkingNode onComplete \(isFromServer ? "server": "client"), code = \(code)")
        
        if (isFromServer) {
            MDNSManager.instance.stopServer()
            if let mServer = mServer {
                mServer.stop()
            }
            
            let dic = [
                "event": "SERVER_STOP",
                "ip": serverIp ?? "",
                "port": serverPort ?? -1
            ] as [String: Any]
            
            stopWSServer()
            eventDelegate?.sendEventToRN(dic: dic)
        } else {
            var dic: [String: Any] = [:]
            
            dic = [
                "event": "CLIENT_CLOSE",
                "ip": mClient?.mIp ?? "",
                "port": mClient?.mPort ?? -1,
                "code": code
            ]

            MDNSManager.instance.stopDiscover()
            DispatchQueue.global().asyncAfter(deadline: .now() + 3) {
                self.discoveryMDNS()
            }
            closeClient()
            eventDelegate?.sendEventToRN(dic: dic)
        }
    }
}

extension NetWorkingNode: WsPingPongListener {
    func onPong() {
        let dic = [
            "event": "SERVER_PONG",
            "ip": UIDevice.getIP() ?? "",
            "port": 0
        ] as [String : Any]
        eventDelegate?.sendEventToRN(dic: dic)
    }
}
