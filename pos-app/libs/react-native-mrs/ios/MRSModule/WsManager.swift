//
//  WsManager.swift
//  RNMrs
//
//  Created by <PERSON> on 2022/10/18.
//  Copyright © 2022 Facebook. All rights reserved.
//

import Foundation

open class WsManager: NSObject {
    @objc
    open var sendEventCallback: (([String: Any]) -> ())?
    
    private var searchResultCallback: ((Bool) -> Void)?
    
    private var executor: DispatchQueue = DispatchQueue(label: "com.storehubmrs.queue")
    
    @objc
    public static let instance = WsManager()
    
    private override init() {
        super.init()
        NetWorkingNode.instance.eventDelegate = self
    }
    
    @objc
    open func isServerRuning() -> Bool {
       return NetWorkingNode.instance.isServerRunning()
    }
    
    @objc
    open func isClientRunning() -> Bool {
        return NetWorkingNode.instance.isClientRunning()
    }
    
    @objc
    open func startServer(headers: [String: String]) {
        debugPrint("🥸 WsManager startServer")
        MRSLog.instance.logInfo(event: .START_WS_SERVER, message: "🥸 startWebSocketServer() called with: \(headers)")
        executor.async {
            NetWorkingNode.instance.starServer(header: headers)
        }
    }
    
    @objc
    open func stopServer() {
        debugPrint("🥸 WsManager stopServer")
        MRSLog.instance.logInfo(event: .STOP_WS_SERVER, message: "🥸 stopWebSocketServer() called")
        NetWorkingNode.instance.stopServer()
    }
    
    @objc
    open func writeToClient(ip: String, message: String) {
        NetWorkingNode.instance.writeToClient(ip: ip, message: message)
    }
    
    @objc
    open func writeToServer(message: String) {
        NetWorkingNode.instance.writeToServer(message: message)
    }
    
    @objc
    open func broadcast(message: String) {
        NetWorkingNode.instance.broadcast(message: message)
    }
    
    @objc
    open func discoverAndConnectServer(headers: [String: String]) {
        MRSLog.instance.logInfo(event: .COMMON_EVENT, message: "🥸 discoverAndConnectServer() called with: \(headers)")
        executor.async {
            NetWorkingNode.instance.discoverAndConnectServer(headers: headers)
        }
    }
    
    @objc
    open func closeClient() {
        debugPrint("🥸 WsManager closeClient")
        MRSLog.instance.logInfo(event: .STOP_WS_CLIENT, message: "🥸 closeWebSocketClient() called")
        executor.async {
            NetWorkingNode.instance.closeClient(sendEvent: true)
        }
    }
    
    @objc
    open func searchServer(name: String, timeout: Int, resultCallback: @escaping (Bool) -> ()) {
        MRSLog.instance.logInfo(event: .SEARCH_MDNS_SERVICE, message: "🥸 searchServer() called with name=\(name) timeout=\(timeout)")
        self.searchResultCallback = resultCallback
        debugPrint("🥸 searchServer name = \(name), timeout = \(timeout)")
        executor.async {
            NetWorkingNode.instance.discoveryMDNS(isSearch: true)
        }
                
        let timeOut = DispatchTimeInterval.seconds(timeout)
        executor.asyncAfter(deadline: .now() + timeOut) {
                debugPrint("🥸 searchServer name is found = \(MDNSManager.instance.isFoundServer)")
                if !MDNSManager.instance.isFoundServer {
                    self.searchResultCallback?(false)
                    MRSLog.instance.logInfo(event: .SEARCH_MDNS_SERVICE, message: "🥸 searchServer() called result = false")
                } else {
                    self.searchResultCallback?(true)
                    MRSLog.instance.logInfo(event: .SEARCH_MDNS_SERVICE, message: "🥸 searchServer() called result = true")
                    MDNSManager.instance.stopDiscover()
                }
            }
    }
    
    @objc
    open func stopMDNSService() {
        debugPrint("🥸 WsManager stopMDNSService")
        MRSLog.instance.logInfo(event: .STOP_MDNS_SERVICE, message: "🥸 stopMDNSService() called")
        MDNSManager.instance.stopServer()
    }
    
    @objc
    open func stopDiscover() {
        debugPrint("🥸 WsManager stopDiscover")
        MRSLog.instance.logInfo(event: .STOP_DISCOVERY, message: "🥸 stopDiscover() called")
        MDNSManager.instance.stopDiscover()
    }
    
    @objc
    open func destroy() {
        debugPrint("🥸 WsManager onExitApp - destroy")
        MRSLog.instance.logInfo(event: .STOP_MDNS_SERVICE, message: "🥸 onExitApp() - destroy called")
        executor.async {
            NetWorkingNode.instance.destroy()
        }
    }
}

extension WsManager: EventSender {
    func sendEventToRN(dic: [String : Any]) {
        sendEventCallback?(dic)
        debugPrint("🧜‍♀️ send event to RN dic = \(dic)")
    }
    
    func searchFoundServer() {
//        debugPrint("🥸 searchFoundServer is found = \(MDNSManager.instance.isFoundServer)")
//        searchResultCallback?(true)
    }
}
