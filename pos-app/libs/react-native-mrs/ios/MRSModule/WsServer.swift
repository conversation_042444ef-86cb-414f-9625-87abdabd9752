//
//  WsServer.swift
//  RNMrs
//
//  Created by <PERSON> on 2022/10/19.
//

import Foundation

class WsServer: NSObject {
    let SERVER_CONNECTION_LOST_TIMEOUT = 30
    
    weak var wsListerDelegate: WsListener?
    weak var onClientChangedDelegate: OnClientChangedListener?
    
    private var isRunning = false
    private var mServerHeadersInfo: [String: String] = [:]
    private var mClientSockets: [String: PSWebSocket] = [:]
    private var psServer: PSWebSocketServer?
    private var ip: String?
    private var port: Int?
    
    private var timer:Timer?
    private var executor = DispatchQueue(label: "com.wsserver.queue")
    private var isCheckingClientStatus = false
    
    // when ios is server, ios server can't detect whether the client is offline or online,
    // once the client is change the network, the client can't be connect server anymore, becuase server already has the client record, will response DuplicatedRegisterId Error.
    // so iOS will not check the register id
    private var isDuplicatedRegisterId = false
    
    private var mUrlRequests = [URLRequest]()
    
    private override init() {
        
    }

    convenience init(ip: String, port: Int, header: [String: String]? = nil) {
        self.init()
        self.ip = ip
        self.port = port
        if let header = header {
            mServerHeadersInfo = header
        }
        psServer = PSWebSocketServer(host: ip, port: UInt(port))
        psServer?.delegate = self
        psServer?.start()
        isRunning = true
        debugPrint("😍 websocket server init ip \(ip) prot \(port)")
    }
    
    public func getFieldValue(key: String) -> String? {
        return mServerHeadersInfo[key]
    }

    func isServerRunning() -> Bool {
        return isRunning
    }
    
    func stop() {
        timer?.invalidate()
        isCheckingClientStatus = false
        
        isRunning = false
        psServer?.stop()
        mServerHeadersInfo.removeAll()
        mClientSockets.removeAll()
        mUrlRequests.removeAll()
        psServer?.delegate = nil
        psServer?.delegateQueue = nil
    }
    
    func broadcast(message: String) {
        for (_, clientSocket) in mClientSockets {
            clientSocket.send(message)
        }
    }
    
    func writeClient(_ Clientip: String, msg: String) {
        debugPrint("Clientip == \(Clientip)")
        if let webSocket = getClientSocket(ip: Clientip) {
            webSocket.send(msg)
        } else {
            debugPrint("❌ writeClient client doesn't exist")
        }
    }
    
    func getClientSocket(ip: String) -> PSWebSocket? {
        return mClientSockets[ip]
    }
    
    func getIPByWebsocket(_ websocket: PSWebSocket) -> String? {
        for (key, value) in mClientSockets {
            if value == websocket {
                return key
            }
        }
        return nil
    }
    
    func getClientRequests() -> [URLRequest] {
        return mUrlRequests
    }


    ///  append new client that is connected the server
    /// - Parameters:
    ///   - webSocket
    ///   - handshake
    ///   WARN: 安装加了 synchronized 需要确认
    private func addNewClient(webSocket: PSWebSocket, ip: String, urlRequest: URLRequest) {
        var removedURLRequest: URLRequest?
        // add websocket
        if mClientSockets.keys.contains(ip) {
            // remove old
            removedURLRequest = removeOldClient(ip: ip)
        }
        
        MRSLog.instance.logClientsChangedInfo(event: .ADD_NEW_CLIENT, message: "😍 addNewClient() called with ip \(ip)", newClient: urlRequest, removed: removedURLRequest)
        
        mClientSockets[ip] = webSocket
        printAllClientSockets()
        
        MRSLog.instance.updateClients(urlRequests: mUrlRequests)
        
        onClientChangedDelegate?.onClientJoined()
    }
    
    // WARN: 安装加了 synchronized 需要确认
    private func removeOldClient(ip: String) -> URLRequest? {
        let webSocket = mClientSockets[ip]
        mClientSockets.removeValue(forKey: ip)
        let removedURLRequest = removeURLRequest(ip: ip)
        
        if let _ = webSocket {
            debugPrint("🤖 removed Client \(ip)  onClientClosed")
            onClientChangedDelegate?.onClientClosed()
        }
        
        printAllClientSockets()
        return removedURLRequest
    }
    
    // WARN: 安装加了 synchronized 需要确
    private func removeOldClient(webSocket: PSWebSocket) {
        
        for (key, value) in mClientSockets {
            if value.isEqual(webSocket) {
                debugPrint("remove \(key) === \(value)")
                mClientSockets.removeValue(forKey: key)
                removeURLRequest(ip: key)
                onClientChangedDelegate?.onClientClosed()
                break
            }
        }
        MRSLog.instance.updateClients(urlRequests: mUrlRequests)
    }

    private func printAllClientSockets() {
        if !mClientSockets.isEmpty {
            for (key, value) in mClientSockets {
                debugPrint("AllClientSockets size: \(mClientSockets.count)")
                debugPrint("\(key) === \(value)")
            }
        } else {
            debugPrint("AllClientSockets size: 0")
        }
    }
    
    private func isContainRegisterId(_ regsitserId: String) -> Bool {
        for mUrlRequest in mUrlRequests {
            let mRegisterId = mUrlRequest.allHTTPHeaderFields?[KeyCons.KEY_REGISTER_ID]
            return regsitserId == mRegisterId
        }
        return false
    }
    
    @discardableResult
    private func removeURLRequest(ip: String) -> URLRequest? {
        for (index,mUrlRequest) in mUrlRequests.enumerated() {
            let mIP = mUrlRequest.allHTTPHeaderFields?["ip"]
            if mIP != nil {
                if mIP == ip {
                    return mUrlRequests.remove(at: index)
                }
            }
        }
        return nil
    }
    
    // MARK: - check client status
    private func checkClientStatus(webSocket: PSWebSocket?, statusClosure: @escaping (_ isPingSuccess: Bool) -> ()) {
        let heartMsg = "heartMsg"
        let heartData = heartMsg.data(using: .utf8)
        
        var isPingSuccess = false
        webSocket?.ping(heartData, handler: { _ in
            isPingSuccess = true
        })
        
        DispatchQueue.global().asyncAfter(deadline: .now() + 3) {
            statusClosure(isPingSuccess)
        }
    }
    
    func checkAllClientsStatus() {
        isCheckingClientStatus = true
        let tempClientSockets = mClientSockets
        let lock = NSLock()
        for (key, value) in tempClientSockets {
            lock.lock()
            checkClientStatus(webSocket: value) { isPingSuccess in
                debugPrint("🤖 Client \(key) ping sucess is \(isPingSuccess)")
                if !isPingSuccess {
                    self.removeOldClient(ip: key)
                }
                lock.unlock()
            }
        }
        
        isCheckingClientStatus = false
    }
    
    func startTimerToCheckClientsStatus() {
        timer = Timer.scheduledTimer(withTimeInterval: 40, repeats: true) {[unowned self] timer in
            debugPrint("😍 checking clients status")
            if (!self.isCheckingClientStatus) {
                executor.async {
                    self.checkAllClientsStatus()
                }
            }
        }
    }
}

// MARK: - PSWebSocketServerDelegate
extension WsServer: PSWebSocketServerDelegate {
    func serverDidStart(_ server: PSWebSocketServer!) {
        debugPrint("😍 websocket server did start")
        isRunning = true
        wsListerDelegate?.onStar(isFromServer: true, ip: ip ?? "", port: port ?? 0)
        
        //
        startTimerToCheckClientsStatus()
    }
    
    func server(_ server: PSWebSocketServer!, didFailWithError error: Error!) {
        debugPrint("😍 websocket server didFailWithError")
        isRunning = false
        MRSLog.instance.logError(event: .START_WS_SERVER, result: LogResult.Failed, code: -2, errorMsg: "😍 \(String(describing: error))")
    }
    
    func serverDidStop(_ server: PSWebSocketServer!) {
        debugPrint("😍 websocket server serverDidStop")
        isRunning = false
    }
    
    func server(_ server: PSWebSocketServer!, webSocketDidOpen webSocket: PSWebSocket!) {
        debugPrint("😍 websocket server opened")
        if (!mServerHeadersInfo.isEmpty) {
            let request = mUrlRequests.last
            let serverBusiness = getFieldValue(key: KeyCons.KEY_BUSINESS)
            let serverStoreName = getFieldValue(key: KeyCons.KEY_STORE_NAME)
            
            let header = request?.allHTTPHeaderFields
            let clientBusiness = header?[KeyCons.KEY_BUSINESS]
            let clientStoreName = header?[KeyCons.KEY_STORE_NAME]
//            let registerId = header?[KeyCons.KEY_REGISTER_ID] ?? ""
            let ip = header?[KeyCons.KEY_IP]
            
            debugPrint("😍 websocket server webSocket \(header)")
            
            guard let clientBusiness = clientBusiness, let clientStoreName = clientStoreName, let ip = ip  else {
                
                mUrlRequests.removeLast()
                webSocket.close(withCode: CloseCons.INVALID_AUTHENTICATION, reason: "Invalid Client Authentication")
                
                MRSLog.instance.logClientsChangedInfo(event: .ADD_NEW_CLIENT, result: LogResult.Failed, message: "😍 ADD_NEW_CLIENT failed INVALID_AUTHENTICATION", newClient: request)
            
                debugPrint("😍 websocket Server Invalid Client Authentication")
                return
            }
            
            if clientBusiness == serverBusiness && serverStoreName == clientStoreName {
                if let request = request {
                    addNewClient(webSocket: webSocket, ip: ip, urlRequest: request)
                } else {
                    MRSLog.instance.logClientsChangedInfo(event: .ADD_NEW_CLIENT, result: LogResult.Failed, message: "😍 ADD_NEW_CLIENT failed request url is not existed", newClient: request)
                }
            } else {
                // Add connection closure logic when validation fails
                mUrlRequests.removeLast()
                webSocket.close(withCode: CloseCons.INVALID_AUTHENTICATION, reason: "Business or Store Name mismatch")
                
                MRSLog.instance.logClientsChangedInfo(event: .ADD_NEW_CLIENT, result: LogResult.Failed,
                                                      message: "😍 ADD_NEW_CLIENT failed due to business or store name mismatch. Server: \(serverBusiness ?? "")/\(serverStoreName ?? ""), Client: \(clientBusiness)/\(clientStoreName)",
                                                      newClient: request)
                
                debugPrint("😍 websocket Server rejected client due to business/store name mismatch")
            }
        } else {
            // remove the last request
            mUrlRequests.removeLast()
            
            // TODO: Android CloseFrame.UNEXPECTED_CONDITION 是个啥？ 110 我要报警了
            webSocket.close(withCode: 110, reason: "Server Params Error")
            MRSLog.instance.logError(event: .ADD_NEW_CLIENT, result: LogResult.Failed, code: CloseCons.SERVER_PARAMS_ERROR, errorMsg: "😍 ADD_NEW_CLIENT failed SERVER_PARAMS_ERROR, no server header")
            debugPrint("😍 websocket Server Params Error")
        }
    }
    
    func server(_ server: PSWebSocketServer!, webSocket: PSWebSocket!, didReceiveMessage message: Any!) {
    
        let ip = getIPByWebsocket(webSocket) ?? ""
        debugPrint("😍 websocket Server didReceiveMessage \(ip) = \(webSocket) = \(message)")
        
        wsListerDelegate?.read(isFromServer: true, webSocket: webSocket, data: message as? String ?? "")
    }
    
    func server(_ server: PSWebSocketServer!, webSocket: PSWebSocket!, didFailWithError error: Error!) {
        // TODO: need to check whether can webSocket.isEqual(webSocket) identify webSocket
        debugPrint("😍 websocket Server WebSocket didFailWithError \(webSocket)")
        removeOldClient(webSocket: webSocket)
    }
    
    func server(_ server: PSWebSocketServer!, webSocket: PSWebSocket!, didCloseWithCode code: Int, reason: String!, wasClean: Bool) {
        debugPrint("😍 websocket Server webSocket didCloseWithCode \(code) = \(reason)")
    }
    
    func server(_ server: PSWebSocketServer!, acceptWebSocketWith request: URLRequest!) -> Bool {
        debugPrint("😍 websocket acceptWebSocketWith request = \(request.allHTTPHeaderFields) ")
        mUrlRequests.append(request)
        return true
    }
}
