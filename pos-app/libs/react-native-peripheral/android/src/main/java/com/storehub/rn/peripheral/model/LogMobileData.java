package com.storehub.rn.peripheral.model;

import com.storehub.rn.peripheral.util.TimeUtil;

public class LogMobileData {
  private String time;
  private String appId;
  private String appName;
  private String version;
  private String osVersion;
  private String deviceModel;
  private String storeId;
  private String registerId;
  private String businessCountry;
  private Printer printer;
  private Host host;
  private String result;
  private String reason;
  private String buildNumber;

  private String workflowId;
  private String employeeId;

  public String getRegisterNumber() {
    return registerNumber;
  }

  public void setRegisterNumber(String registerNumber) {
    this.registerNumber = registerNumber;
  }

  public String getEmployeeId() {
    return employeeId;
  }

  public void setEmployeeId(String employeeId) {
    this.employeeId = employeeId;
  }

  private String registerNumber;

  public LogMobileData() {
    this.time = TimeUtil.now();
  }

  public String getResult() {
    return result;
  }

  public void setResult(String result) {
    this.result = result;
  }

  public String getReason() {
    return reason;
  }

  public void setReason(String reason) {
    this.reason = reason;
  }

  public String getAppId() {
    return appId;
  }

  public void setAppId(String appId) {
    this.appId = appId;
  }

  public String getAppName() {
    return appName;
  }

  public void setAppName(String appName) {
    this.appName = appName;
  }

  public String getVersion() {
    return version;
  }

  public void setVersion(String version) {
    this.version = version;
  }

  public String getOsVersion() {
    return osVersion;
  }

  public void setOsVersion(String osVersion) {
    this.osVersion = osVersion;
  }

  public String getDeviceModel() {
    return deviceModel;
  }

  public void setDeviceModel(String deviceModel) {
    this.deviceModel = deviceModel;
  }

  public String getStoreId() {
    return storeId;
  }

  public void setStoreId(String storeId) {
    this.storeId = storeId;
  }

  public String getRegisterId() {
    return registerId;
  }

  public void setRegisterId(String registerId) {
    this.registerId = registerId;
  }

  public String getBusinessCountry() {
    return businessCountry;
  }

  public void setBusinessCountry(String businessCountry) {
    this.businessCountry = businessCountry;
  }

  public String getTime() {
    return time;
  }

  public void setTime(String time) {
    this.time = time;
  }

  public String getBuildNumber() {
    return buildNumber;
  }

  public void setBuildNumber(String buildNumber) {
    this.buildNumber = buildNumber;
  }

  public String getWorkflowId() {
    return workflowId;
  }

  public void setWorkflowId(String workflowId) {
    this.workflowId = workflowId;
  }

  public static class Printer {
    private String printerId;
    private String status;
    private String previousStatus;
    private String printerEvent;
    private Integer errorCode;
    private String errorMessage;
    private String exceptionMessage;
    private String subNetChecking;

    public String getPrinterId() {
      return printerId;
    }

    public void setPrinterId(String printerId) {
      this.printerId = printerId;
    }

    public String getStatus() {
      return status;
    }

    public void setStatus(String status) {
      this.status = status;
    }

    public String getPreviousStatus() {
      return previousStatus;
    }

    public void setPreviousStatus(String previousStatus) {
      this.previousStatus = previousStatus;
    }

    public String getPrinterEvent() {
      return printerEvent;
    }

    public void setPrinterEvent(String printerEvent) {
      this.printerEvent = printerEvent;
    }

    public Integer getErrorCode() {
      return errorCode;
    }

    public void setErrorCode(Integer errorCode) {
      this.errorCode = errorCode;
    }

    public String getErrorMessage() {
      return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
      this.errorMessage = errorMessage;
    }

    public String getExceptionMessage() {
      return exceptionMessage;
    }

    public void setExceptionMessage(String exceptionMessage) {
      this.exceptionMessage = exceptionMessage;
    }

    public String getSubNetChecking() {
      return subNetChecking;
    }

    public void setSubNetChecking(String subNetChecking) {
      this.subNetChecking = subNetChecking;
    }
  }

  public Printer getPrinter() {
    return printer;
  }

  public void setPrinter(Printer printer) {
    this.printer = printer;
  }

  public static class Host {
    private String hostIP;
    private Boolean isNetworkAvailable;
    private Boolean isWifiConnected;
    private Boolean isEthernetConnected;

    public String getHostIP() {
      return hostIP;
    }

    public void setHostIP(String hostIP) {
      this.hostIP = hostIP;
    }

    public Boolean getNetworkAvailable() {
      return isNetworkAvailable;
    }

    public void setNetworkAvailable(Boolean networkAvailable) {
      isNetworkAvailable = networkAvailable;
    }

    public Boolean getWifiConnected() {
      return isWifiConnected;
    }

    public void setWifiConnected(Boolean wifiConnected) {
      isWifiConnected = wifiConnected;
    }

    public Boolean getEthernetConnected() {
      return isEthernetConnected;
    }

    public void setEthernetConnected(Boolean ethernetConnected) {
      isEthernetConnected = ethernetConnected;
    }
  }

  public Host getHost() {
    return host;
  }

  public void setHost(Host host) {
    this.host = host;
  }

}
