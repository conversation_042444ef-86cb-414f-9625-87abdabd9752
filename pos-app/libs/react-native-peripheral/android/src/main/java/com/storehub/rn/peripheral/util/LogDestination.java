package com.storehub.rn.peripheral.util;

public enum LogDestination {
    LOCAL("local"),
    REMOTE("remote"),
    BOTH("both");

    private final String value;

    LogDestination(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static LogDestination fromValue(String value) {
        for (LogDestination destination : values()) {
            if (destination.value.equals(value)) {
                return destination;
            }
        }
        return BOTH; // Default fallback
    }
}
