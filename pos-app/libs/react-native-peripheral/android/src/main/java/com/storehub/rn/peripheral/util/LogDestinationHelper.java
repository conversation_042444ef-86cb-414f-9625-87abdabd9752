package com.storehub.rn.peripheral.util;

/**
 * Helper class for LogDestination enum operations
 * Provides backward compatibility and utility methods
 */
public class LogDestinationHelper {

    // Legacy string constants for backward compatibility
    public static final String LOCAL_STRING = "local";
    public static final String REMOTE_STRING = "remote";
    public static final String BOTH_STRING = "both";

    /**
     * Convert string to LogDestination enum
     * @param destination string representation
     * @return LogDestination enum value
     */
    public static LogDestination fromString(String destination) {
        if (destination == null) {
            return LogDestination.BOTH;
        }

        switch (destination.toLowerCase()) {
            case LOCAL_STRING:
                return LogDestination.LOCAL;
            case REMOTE_STRING:
                return LogDestination.REMOTE;
            case BOTH_STRING:
                return LogDestination.BOTH;
            default:
                return LogDestination.BOTH; // Default fallback
        }
    }

    /**
     * Check if destination should log locally
     * @param destination LogDestination enum
     * @return true if should log locally
     */
    public static boolean shouldLogLocally(LogDestination destination) {
        return destination == LogDestination.LOCAL || destination == LogDestination.BOTH;
    }

    /**
     * Check if destination should log remotely
     * @param destination LogDestination enum
     * @return true if should log remotely
     */
    public static boolean shouldLogRemotely(LogDestination destination) {
        return destination == LogDestination.REMOTE || destination == LogDestination.BOTH;
    }
}
