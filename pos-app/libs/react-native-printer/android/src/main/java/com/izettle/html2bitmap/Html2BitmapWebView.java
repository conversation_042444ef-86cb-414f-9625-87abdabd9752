package com.izettle.html2bitmap;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.PaintFlagsDrawFilter;
import android.net.Uri;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.view.View;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebResourceResponse;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import androidx.annotation.AnyThread;
import androidx.annotation.MainThread;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;

import com.izettle.html2bitmap.content.ProgressChangedListener;
import com.izettle.html2bitmap.content.WebViewContent;
import com.storehub.rn.peripheral.fsm.printer.PrinterEventEnum;
import com.storehub.rn.peripheral.fsm.printer.PrinterRenderData;
import com.storehub.rn.printer.RNPrinterModule;
import com.storehub.rn.printer.log.LogLevel;
import com.storehub.rn.printer.printer.queue.PrinterThreadFactory;
import com.storehub.rn.printer.util.PrinterLogEventUtil;

import java.lang.ref.WeakReference;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

class Html2BitmapWebView implements ProgressChangedListener {
    private static final int MSG_MEASURE = 2;
    private static final int MSG_SCREENSHOT = 5;
    @NonNull
    private final Handler mainHandler;
    private final int measureDelay;
    private final int screenshotDelay;
    @Nullable
    private final Html2BitmapConfigurator html2BitmapConfigurator;
    @NonNull
    private final WebViewContent content;
    private final int bitmapWidth;
    @Nullable
    private final Integer textZoom;
    @NonNull
    private final Context context;
    private BitmapCallback callback;
    private WebView webView;
    private int progress;
    private boolean isCleanUp;
    private boolean isPageFinished;

    private static ExecutorService screenShootExecutor;

    private static ExecutorService getScreenShootExecutor() {
        if (screenShootExecutor == null) {
            synchronized (Html2BitmapWebView.class) {
                if (screenShootExecutor == null) {
                    /* 1. in order to avoid the slowdown of efficiency of html2bitmap is due to wait for thread, so setup the coreSize is 5
                        2. 5 is enough, since the pure generate screen shot bitmap is not quite slow, about 10ms. And currently approximately 20 printers are maximum for merchants.
                     */
                    screenShootExecutor = new ThreadPoolExecutor(5, 5, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new PrinterThreadFactory("Html2BitmapPool"));
                }
            }
        }
        return screenShootExecutor;
    }

    @AnyThread
    Html2BitmapWebView(@NonNull final Context context, @NonNull final WebViewContent content, final int bitmapWidth, final int measureDelay, final int screenshotDelay, final boolean strictMode, @Nullable final Integer textZoom, @Nullable Html2BitmapConfigurator html2BitmapConfigurator) {
        this.context = context;
        this.content = content;
        this.bitmapWidth = bitmapWidth;
        this.measureDelay = measureDelay;
        this.screenshotDelay = screenshotDelay;
        this.textZoom = textZoom;
        this.html2BitmapConfigurator = html2BitmapConfigurator;

        mainHandler = new Handler(Looper.getMainLooper()) {
            @Override
            public void handleMessage(Message msg) {
                if (msg == null) return;
                switch (msg.what) {
                    case MSG_MEASURE:
                        handleMeasureMessage();
                        break;
                    case MSG_SCREENSHOT:
                        handleScreenShotMessage();
                        break;
                }
            }
        };
    }

    private void handleMeasureMessage() {
        if (!content.isDone()) {
            return;
        }

        // Polling to check if the page has finished rendering
        int contentHeight = webView.getContentHeight();
        if (!isPageFinished || contentHeight == 0) {
            if (contentHeight > 0) {
                PrinterRenderData renderData = new PrinterRenderData();
                renderData.setEvent(PrinterEventEnum.RENDER_NOT_COMPLETE);
                renderData.setContentHeight(contentHeight);
                PrinterLogEventUtil.logRenderEvent(LogLevel.info, "Success", "Page not Finished", renderData);
            }
            pageFinished(measureDelay);
            return;
        }

        // set the correct height of the webview and do measure and layout using it before taking the screenshot
        int widthMeasureSpec = View.MeasureSpec.makeMeasureSpec(bitmapWidth, View.MeasureSpec.EXACTLY);
        int heightMeasureSpec = View.MeasureSpec.makeMeasureSpec(webView.getContentHeight(), View.MeasureSpec.EXACTLY);
        webView.measure(widthMeasureSpec, heightMeasureSpec);
        webView.layout(0, 0, webView.getMeasuredWidth(), webView.getMeasuredHeight());

        mainHandler.removeMessages(MSG_SCREENSHOT);
        mainHandler.sendEmptyMessageDelayed(MSG_SCREENSHOT, screenshotDelay);
    }

    private void handleScreenShotMessage() {
        if (!content.isDone()) {
            return;
        }

        if (webView.getMeasuredHeight() == 0) {
            pageFinished(measureDelay);
            return;
        }
        getScreenShootExecutor().execute(new ScreenShotRunnable(callback, webView));
    }

    private static class ScreenShotRunnable implements Runnable {
        private final BitmapCallback callback;
        private final WeakReference<WebView> webViewWeakReference;

        protected ScreenShotRunnable(BitmapCallback callback, WebView webView) {
            this.callback = callback;
            this.webViewWeakReference = new WeakReference<>(webView);
        }

        @Override
        public void run() {
            try {
                int measuredHeight = webViewWeakReference.get().getMeasuredHeight();
                Bitmap screenshot = screenshot(webViewWeakReference.get());
                callback.finished(screenshot, measuredHeight);
            } catch (Throwable t) {
                callback.error(t);
            }
        }

        private Bitmap screenshot(WebView webView) {
            Bitmap bitmap = Bitmap.createBitmap(webView.getMeasuredWidth(), webView.getMeasuredHeight(), Bitmap.Config.RGB_565);

            Canvas canvas = new Canvas(bitmap);

            canvas.setDrawFilter(new PaintFlagsDrawFilter(Paint.ANTI_ALIAS_FLAG, 0));

            webView.draw(canvas);
            return bitmap;
        }
    }

    @MainThread
    void load(@NonNull final BitmapCallback callback) {
        this.callback = callback;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            WebView.enableSlowWholeDocumentDraw();
        }
        webView = new WebView(context);
        webView.setInitialScale(100);
        webView.setVerticalScrollBarEnabled(false);
        final WebSettings settings = webView.getSettings();
        settings.setBuiltInZoomControls(false);
        settings.setSupportZoom(false);
        if (textZoom != null) {
            settings.setTextZoom(textZoom);
        }

        if (html2BitmapConfigurator != null) {
            html2BitmapConfigurator.configureWebView(webView);
        }

        webView.setWebChromeClient(new WebChromeClient() {

            @Override
            public void onProgressChanged(WebView view, int newProgress) {
                super.onProgressChanged(view, newProgress);
                progress = newProgress;
                progressChanged();
            }
        });

        content.setDoneListener(this);

        webView.setWebViewClient(new WebViewClient() {

            @Nullable
            @Override
            public WebResourceResponse shouldInterceptRequest(WebView view, String url) {

                if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
                    WebResourceResponse webResourceResponse = content.loadResource(view.getContext(), Uri.parse(url));

                    return webResourceResponse != null ? webResourceResponse : super.shouldInterceptRequest(view, url);
                }

                return super.shouldInterceptRequest(view, url);
            }

            @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
            @Nullable
            @Override
            public WebResourceResponse shouldInterceptRequest(WebView view, WebResourceRequest request) {

                WebResourceResponse webResourceResponse = content.loadResource(view.getContext(), request.getUrl());

                return webResourceResponse != null ? webResourceResponse : super.shouldInterceptRequest(view, request);
            }

            @Override
            public void onPageStarted(WebView view, String url, Bitmap favicon) {
                super.onPageStarted(view, url, favicon);
                isPageFinished = false;
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                isPageFinished = true;
            }

            @Override
            public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
                super.onReceivedError(view, request, error);
                if (error.getErrorCode() != -10) {
//                    onReceivedError ] - WebResourceError: -10, Description: net::ERR_UNKNOWN_URL_SCHEME
                    PrinterRenderData renderData = new PrinterRenderData();
                    renderData.setEvent(PrinterEventEnum.RENDER_ERROR);
                    PrinterLogEventUtil.logRenderEvent(LogLevel.error, "Failed", "WebResourceError: " + error.getErrorCode() + ", Description: " + error.getDescription(), renderData);
                }
            }
        });
        // set the correct height of the webview and do measure and layout using it before taking the screenshot
        int widthMeasureSpec = View.MeasureSpec.makeMeasureSpec(bitmapWidth, View.MeasureSpec.EXACTLY);
        int heightMeasureSpec = View.MeasureSpec.makeMeasureSpec(10, View.MeasureSpec.EXACTLY);
        webView.measure(widthMeasureSpec, heightMeasureSpec);
        webView.layout(0, 0, webView.getMeasuredWidth(), webView.getMeasuredHeight());
        content.loadContent(webView);
    }

    @MainThread
    void cleanup() {
        isCleanUp = true;
        webView.stopLoading();
        if (RNPrinterModule.isNewFlow) {
            try {
                webView.destroy();
            } catch (Exception ignored) {
            }
        }
        webView = null;
        mainHandler.removeCallbacksAndMessages(null);
    }

    private void pageFinished(int delay) {
        mainHandler.removeMessages(MSG_SCREENSHOT);
        mainHandler.removeMessages(MSG_MEASURE);
        mainHandler.sendEmptyMessageDelayed(MSG_MEASURE, delay);
    }

    /**
     * If the loading resource timeouts, Html2BitmapWebView will be cleaned up and empty bitmap will
     * be return. later on, the resource is loaded complete, we should reject the notification here.
     */
    @Override
    public void progressChanged() {
        if (isCleanUp) {
            return;
        }
        if (progress == 100 && content.isDone()) {
            pageFinished(measureDelay);
        }
    }
}
