package com.storehub.rn.printer;

import static android.hardware.usb.UsbManager.ACTION_USB_DEVICE_ATTACHED;
import static android.hardware.usb.UsbManager.ACTION_USB_DEVICE_DETACHED;
import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.ServiceConnection;
import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbManager;
import android.os.Build;
import android.os.IBinder;
import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.bridge.WritableArray;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.modules.core.DeviceEventManagerModule;
import com.storehub.rn.printer.internal.RNCallback;
import com.storehub.rn.printer.printer.AbstractPrinter;
import com.storehub.rn.printer.printer.PrinterConnectType;
import com.storehub.rn.printer.printer.PrinterModelType;
import com.storehub.rn.printer.printer.impl.LANXPrinter;
import com.storehub.rn.printer.printer.impl.XPrinter;
import com.storehub.rn.printer.printer.impl.xprinter.XPrinterUdpData;
import com.storehub.rn.printer.printer.model.PrinterTaskResult;
import com.storehub.rn.printer.printer.model.PrintingDataModel;
import com.storehub.rn.printer.util.DeviceModelUtils;
import com.storehub.rn.printer.util.Error;
import com.storehub.rn.printer.util.ErrorUtil;
import com.storehub.rn.printer.util.OSHelper;
import com.storehub.rn.printer.util.ReadableMapUtil;
import com.storehub.rn.printer.util.StoreInfoCache;

import net.posprinter.POSConnect;
import net.posprinter.POSPrinter;
import net.posprinter.model.UdpDevice;
import net.posprinter.posprinterface.UdpCallback;

import org.jetbrains.annotations.NotNull;

import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.SocketAddress;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import hdx.HdxUtil;
import timber.log.Timber;

/**
 * Make sure the {@code Promise.resolve} in react method should be invoked, otherwise, RN will wait
 * for the request and the subsequent codes will not be executed.
 */
public class RNPrinterModule extends ReactContextBaseJavaModule {
    private static String TAG = RNPrinterModule.class.getSimpleName();
    private static ReactApplicationContext mContext;
    private POSPrinterService.PrinterBinder mBinder;
    public static boolean isNewFlow = false;

    private ServiceConnection connection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName componentName, IBinder iBinder) {
            Timber.d("Service Connected");
            mBinder = (POSPrinterService.PrinterBinder) iBinder;
        }

        @Override
        public void onServiceDisconnected(ComponentName componentName) {
            mBinder = null; // Need to set to null, if service is disconnected.
            Timber.d("Service Disconnected");
        }
    };

    public static void senEventToRN(String eventName, Object data) {
        if (mContext != null) {
            try {
                mContext.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class).emit(eventName, data);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

    }

    public RNPrinterModule(ReactApplicationContext reactContext) {
        super(reactContext);
        mContext = reactContext;
        POSConnect.init(mContext);
        BroadcastReceiver receiver = new BroadcastReceiver() {

            @Override
            public void onReceive(Context context, Intent intent) {
                Timber.tag(TAG).d("onReceive%s", intent.getAction());
                PrintingManager manager = PrintingManager.getInstance(reactContext);

                UsbDevice usbDevice = intent.getParcelableExtra(UsbManager.EXTRA_DEVICE);
                new XPrinter.SearchHelper(context).searchUSB(manager.createXPrinterUSBSearchCallback(intent.getExtras().getBoolean("connected"), usbDevice));
            }
        };

        IntentFilter filter = new IntentFilter();
        filter.addAction(ACTION_USB_DEVICE_ATTACHED);
        filter.addAction(ACTION_USB_DEVICE_DETACHED);

        reactContext.registerReceiver(receiver, filter);
    }

    @Override
    public String getName() {
        return "RNPrinter";
    }


    @ReactMethod
    public void bindService() {
        Intent intent = new Intent("com.storehub.rn.printer.POSPrinterService");
        if (mBinder == null && mContext != null && mContext.getApplicationContext() != null) {
            intent.setPackage(mContext.getApplicationContext().getPackageName());
            try {
                mContext.getApplicationContext().bindService(intent, connection, Service.BIND_AUTO_CREATE);
                Timber.d("Bind service");
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            return;
        }
        Timber.d("Bind service: already bind before.");
    }

    @ReactMethod
    public void unbindService() {
        if (mContext != null) {
            mContext.getApplicationContext().unbindService(connection);
        }
        Timber.d("Unbind service");
    }


    /**
     * check the binder is null
     *
     * @param promise promise
     * @return true: binder is null
     */
    private boolean checkBinderNull(Promise promise) {
        if (this.mBinder == null) {
            Timber.i("Printer Service Binder is Null");
            ResolveResultHelper.resolveResult(promise, new Error(ErrorUtil.Printer_SERVICE_BINDER_NULL, mContext.getApplicationContext()));
            return true;
        }
        return false;
    }

    @ReactMethod
    public void requestPrinting(ReadableArray printerData, final Promise promise) {
        Timber.d("requestPrinting");
        if (checkBinderNull(promise)) return;

        LinkedList<PrintingDataModel> requestDataList = new LinkedList<>();
        try {
            int size = printerData.size();
            for (int i = 0; i < size; i++) {
                ReadableMap map = printerData.getMap(i);
                PrintingDataModel model = new PrintingDataModel();
                if (map != null) {
                    String printerId = ReadableMapUtil.getValueFromMap(map, "printerId").toString();
                    model.setPrinterId(printerId);
                    String businessType = ReadableMapUtil.getValueFromMap(map, "businessType").toString();
                    model.setBusinessType(businessType);
                    String workflowId = ReadableMapUtil.getValueFromMapWithDefaultValue(map, "workflowId", "").toString();
                    model.setWorkflowId(workflowId);
                    List<String> uri = new ArrayList<>();
                    if (map.hasKey("uri")) {
                        ReadableArray uriArray = map.getArray("uri");
                        if (uriArray != null) {
                            for (int j = 0; j < uriArray.size(); j++) {
                                uri.add(uriArray.getString(j));
                            }
                        }
                    }
                    model.setUri(uri);
                    if (map.hasKey("data")) {
                        model.setData(map.getMap("data"));
                    }
                    requestDataList.add(model);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (requestDataList.size() == 0) {
            Timber.i("requestPrinting Params Error");
            ResolveResultHelper.resolveResult(promise, new Error(ErrorUtil.PRINTING_PARAMS_ERROR, mContext.getApplicationContext()));
            return;
        }

        mBinder.requestPrinting(getCurrentActivity(), requestDataList, new RNCallback<List<PrinterTaskResult>>() {
            @Override
            public void callback(Error<List<PrinterTaskResult>> error) {
                ResolveResultHelper.resolvePrinterTaskResultList(promise, error);
            }
        });
    }

    @ReactMethod
    public void buzz(ReadableArray array, final Promise promise) {
        Timber.d("buzz");
        if (checkBinderNull(promise)) return;
        LinkedList<PrintingDataModel> requestDataList = new LinkedList<>();
        try {
            int size = array.size();
            for (int i = 0; i < size; i++) {
                ReadableMap map = array.getMap(i);
                PrintingDataModel model = new PrintingDataModel();
                if (map != null) {
                    String printerId = ReadableMapUtil.getValueFromMap(map, "printerId").toString();
                    model.setPrinterId(printerId);
                    String workflowId = ReadableMapUtil.getValueFromMapWithDefaultValue(map, "workflowId", "").toString();
                    model.setWorkflowId(workflowId);
                    List<String> uri = new ArrayList<>();
                    if (map.hasKey("uri")) {
                        ReadableArray uriArray = map.getArray("uri");
                        if (uriArray != null) {
                            for (int j = 0; j < uriArray.size(); j++) {
                                uri.add(uriArray.getString(j));
                            }
                        }
                    }
                    model.setUri(uri);
                    if (map.hasKey("data")) {
                        model.setData(map.getMap("data"));
                    }
                    requestDataList.add(model);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (requestDataList.size() == 0) {
            Timber.i("Buzz Params Error");
            ResolveResultHelper.resolveResult(promise, new Error(ErrorUtil.BUZZ_PARAMS_ERROR, mContext.getApplicationContext()));
            return;
        }

        mBinder.buzz(getCurrentActivity(), requestDataList, new RNCallback<List<PrinterTaskResult>>() {
            @Override
            public void callback(Error<List<PrinterTaskResult>> error) {
                ResolveResultHelper.resolvePrinterTaskResultList(promise, error);
            }
        });
    }


    @ReactMethod
    public void openCashDrawer(ReadableArray printerData, final Promise promise) {
        Timber.d("openCashDrawer");
        if (checkBinderNull(promise)) return;

        LinkedList<PrintingDataModel> requestDataList = new LinkedList<>();
        try {
            int size = printerData.size();
            for (int i = 0; i < size; i++) {
                ReadableMap map = printerData.getMap(i);
                PrintingDataModel model = new PrintingDataModel();
                if (map != null) {
                    String printerId = ReadableMapUtil.getValueFromMap(map, "printerId").toString();
                    model.setPrinterId(printerId);
                    requestDataList.add(model);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        mBinder.openCashDrawer(getCurrentActivity(), requestDataList, new RNCallback<List<PrinterTaskResult>>() {
            @Override
            public void callback(Error<List<PrinterTaskResult>> error) {
                ResolveResultHelper.resolvePrinterTaskResultList(promise, error);
            }
        });
    }

    @ReactMethod
    public void searchPrinters(final String receiptType, final Promise promise) {
        Timber.d("searchPrinters");
        if (checkBinderNull(promise)) return;
        mBinder.searchPrinters(receiptType, new RNCallback<String>() {
            @Override
            public void callback(Error<String> error) {
                ResolveResultHelper.resolveResult(promise, error);
            }
        });
    }

    @ReactMethod
    public void initPrinters(final String receiptType, final ReadableArray printerArray, final Promise promise) {
        Timber.d("initPrinters");
        if (checkBinderNull(promise)) return;
        List<AbstractPrinter> printers = PrinterListHelper.generatePrinterList(printerArray);
        mBinder.initPrinters(receiptType, printers, new RNCallback<String>() {
            @Override
            public void callback(Error<String> error) {
                ResolveResultHelper.resolveResult(promise, error);
            }
        });
    }

    private ThreadPoolExecutor tcpConnectionPool = new ThreadPoolExecutor(10, 10, 30000, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());


    @ReactMethod
    public void connectTcp(ReadableMap map, final Promise promise) {
        Timber.d("connectTcp");
        if (checkBinderNull(promise)) return;

        String printerId = ReadableMapUtil.getValueFromMapWithDefaultValue(map, "printerId", "").toString();
        String ip = ReadableMapUtil.getValueFromMapWithDefaultValue(map, "ip", "").toString();
        int port = ReadableMapUtil.getIntegerValueFromMapWithDefaultValue(map, "port", 0);
        int timeout = ReadableMapUtil.getIntegerValueFromMapWithDefaultValue(map, "timeout", 0);

        try {
            Objects.requireNonNull(PrintingManager.getInstance(mContext).getPrinter(printerId));
        } catch (NullPointerException e) {
            ResolveResultHelper.resolveResult(promise, new Error(ErrorUtil.Printer_ID_NULL, mContext.getApplicationContext()));
            return;
        }

        tcpConnectionPool.execute(() -> {
            try {
                InetAddress inentAddress = InetAddress.getByName(ip);
                SocketAddress socketAddress = new InetSocketAddress(inentAddress, port);
                try (Socket mSocket = new Socket()) {
                    mSocket.connect(socketAddress, timeout);
                    ResolveResultHelper.resolveResult(promise, new Error(ErrorUtil.SUCCESS, mContext.getApplicationContext()));

                }
            } catch (Exception e) {
                e.printStackTrace();
                ResolveResultHelper.resolveResult(promise, new Error(ErrorUtil.TCP_CONNECT_ERROR, mContext.getApplicationContext()));
            }
        });

    }

    @ReactMethod
    public void turnOnPower() {
        try {
            if (checkModel()) {
                HdxUtil.SetPrinterPower(1);
                Timber.d("Turn on power");
            }
        } catch (UnsatisfiedLinkError ex) {
            ex.printStackTrace();
        }
    }

    @ReactMethod
    public void turnOffPower() {
        try {
            if (checkModel()) {
                HdxUtil.SetPrinterPower(0);
                Timber.d("Turn off power");
            }
        } catch (UnsatisfiedLinkError ex) {
            ex.printStackTrace();
        }
    }

    @ReactMethod
    public void setStoreInfo(ReadableMap map, Promise promise) {
        Timber.d("setStoreInfo:%s", Thread.currentThread().getId());
        try {
            if (map.hasKey("logo")) {
                ReadableArray array = map.getArray("logo");
                if (array.size() > 0) {
                    StoreInfoCache.saveLogo(mContext, array);
                } else if (array.size() == 0) {
                    // Delete current logo file and cached bitmap
                    StoreInfoCache.clearLogo();
                    StoreInfoCache.deleteLogo(mContext);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            resolveResult(promise, false, ex.getMessage());
        }
        resolveResult(promise, true, null);
    }

    @ReactMethod
    public void setFlowFlag(boolean newFlow, Promise promise) {
        isNewFlow = newFlow;
        promise.resolve(true);
    }

    @Override
    public Map<String, Object> getConstants() {
        final Map<String, Object> constants = new HashMap<>();
        constants.put("deviceModel", DeviceModelUtils.getDeviceModel());
        return constants;
    }

    private void resolveResult(Promise promise, boolean status, String message) {
        WritableMap result = Arguments.createMap();
        result.putBoolean("status", status);
        result.putString("message", message);
        promise.resolve(result);
    }

    public static boolean checkModel() {
        String currentModel = getModel(mContext);
        String[] builtInModels = new String[]{"GC082", "hdx082", "rk3288", "hdx066"};

        for (String m : builtInModels) {
            if (m.equals(currentModel)) {
                return true;
            }
        }

        return OSHelper.isHdx();
    }

    public static String getModel(Context context) {
        String model = null;
        try {
            model = Build.MODEL;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return model;
    }

    /**
     * the helper that resolve results and invoke promise to return params to RN
     */
    private static class ResolveResultHelper {

        /**
         * resolve results when invoke {@link #requestPrinting(ReadableArray, Promise)} or {@link #openCashDrawer(ReadableArray, Promise)} method
         */
        private static void resolvePrinterTaskResultList(Promise promise, Error<List<PrinterTaskResult>> error) {
            WritableMap result = Arguments.createMap();
            result.putInt("errCode", error.getErrCode());
            result.putString("errMessage", error.getErrMessage());
            List<PrinterTaskResult> data = error.getData();
            if (data != null && data.size() > 0) {
                WritableArray array = Arguments.createArray();
                for (PrinterTaskResult taskResult : data) {
                    WritableMap map = Arguments.createMap();
                    map.putInt("errCode", taskResult.getErrCode());
                    map.putString("errMessage", taskResult.getErrMessage());
                    map.putInt("taskIndex", taskResult.getPrinterTaskId());
                    String printerId = taskResult.getPrinterId();
                    // In rare case, maybe get a null printer object from PrinterMap, due to the corresponding printerId is removed from Map.
                    // Hence, need to do guard logic to handle this case.
                    AbstractPrinter printer = PrintingManager.getInstance(mContext.getApplicationContext()).getPrinter(printerId);
                    WritableMap printerInfo = Arguments.createMap();
                    if (printer == null) {
                        printerInfo.putString("printerId", printerId);
                    } else {
                        printerToWritableMap(printer, printerInfo);
                    }
                    map.putMap("printer", printerInfo);
                    array.pushMap(map);
                }
                result.putArray("data", array);
            }
            promise.resolve(result);
        }

        private static @NotNull WritableMap printerToWritableMap(AbstractPrinter printer, WritableMap map) {
            if (printer == null) {
                return map;
            }
            map.putString("printerId", printer.getPrinterId());
            map.putString("prevPrinterId", printer.getPrevPrinterId());
            map.putString("printerName", printer.getPrinterName());
            map.putString("printerModelType", printer.getPrinterModelType().name());
            PrinterConnectType connectType = printer.getPrinterConnectType();
            map.putString("printerConnectType", connectType.name());
            if (connectType == PrinterConnectType.USB) {
                map.putString("usbPath", printer.getUsbPath());
                map.putBoolean("isBuiltInPrinter", printer.getBuiltInPrinter());
            } else if (connectType == PrinterConnectType.Bluetooth) {
                map.putString("bluetoothMacAddress", printer.getBluetoothMacAddress());
            } else if (connectType == PrinterConnectType.LAN) {
                map.putString("lanIp", printer.getLanIp());
                map.putInt("lanPort", printer.getLanPort());
                map.putString("macAddress", printer.getMacAddress());
            }
            return map;
        }


        private static void resolveResult(Promise promise, Error error) {
            WritableMap result = Arguments.createMap();
            result.putInt("errCode", error.getErrCode());
            result.putString("errMessage", error.getErrMessage());
            promise.resolve(result);
        }


    }

    /**
     * the helper that generate printer instance list when RN invoke initPrinters method
     */
    private static class PrinterListHelper {
        private static List<AbstractPrinter> generatePrinterList(ReadableArray printerArray) {
            LinkedList<AbstractPrinter> printers = new LinkedList<>();
            if (printerArray == null || printerArray.size() == 0) return printers;
            int size = printerArray.size();
            for (int i = 0; i < size; i++) {
                ReadableMap map = printerArray.getMap(i);
                if (map != null) {
                    if (map.hasKey("printerConnectType") && map.hasKey("printerModelType")) {
                        String printerConnectType = map.getString("printerConnectType");
                        if (!PrinterConnectType.LAN.name().equals(printerConnectType))
                            continue; // 非 LAN 连接方式，直接过滤掉
                        String printerModelType = map.getString("printerModelType");
                        if (PrinterModelType.XPRINTER.name().equals(printerModelType)) {
                            // Only init LANXPrinter
                            LANXPrinter printer = new LANXPrinter();
                            setPrinterProperties(map, printer, printers);
                        }
                    }
                }
            }
            return printers;
        }

        private static void setPrinterProperties(ReadableMap map, AbstractPrinter printer, LinkedList<AbstractPrinter> printers) {
            Timber.d("setPrinterProperties() called with: " + "map = [" + map + "], printer = [" + printer + "], printers = [" + printers + "]");
            if (!map.hasKey("printerModelType")) {
                return;
            }
            // TODO: extract the string keys to constants.
            printer.setPrinterId(map.getString("printerId"));
            printer.setPrevPrinterId(map.getString("prevPrinterId"));
            printer.setLanIp(map.getString("lanIp"));
            printer.setLanPort(map.getInt("lanPort"));
            printer.setPrinterName(map.getString("printerName"));
            printer.setStoredMacAddress((String) ReadableMapUtil.getValueFromMap(map, "macAddress"));
            printer.setStoredLastPrintedTime((String) ReadableMapUtil.getValueFromMap(map, "lastPrintedTime"));
            printer.setPrinterModelType(PrinterModelType.valueOf(map.getString("printerModelType")));
            // In case printerId is null
            if (printer.getPrinterId() == null) {
                printer.setPrinterId(printer.generatePrinterId());
            }
            if (printer.getPrevPrinterId() == null) {
                printer.setPrevPrinterId(printer.generatePrevPrinterId());
            }
            printers.add(printer);
        }

    }

    @ReactMethod
    public void restoreLanXPrintersByUDP(String searchId, Promise promise) {
        Timber.d("restoreLanXPrintersByUDP");
        if (checkBinderNull(promise)) return;
        mBinder.restoreLanXPrintersByUDP(searchId, new RNCallback<WritableArray>() {
            @Override
            public void callback(Error<WritableArray> error) {
                WritableMap result = Arguments.createMap();
                result.putInt("errCode", error.getErrCode());
                result.putString("errMessage", error.getErrMessage());
                result.putArray("data", null);
                promise.resolve(result);
            }
        });
    }

    @ReactMethod
    public void searchLANXPrinters(Promise promise) {
        Timber.d("searchLANXPrinters");
        if (checkBinderNull(promise)) return;
        mBinder.searchLANXPrinters(new RNCallback<List<XPrinterUdpData>>() {
            @Override
            public void callback(Error<List<XPrinterUdpData>> error) {
                WritableMap result = Arguments.createMap();
                result.putInt("errCode", error.getErrCode());
                result.putString("errMessage", error.getErrMessage());
                List<XPrinterUdpData> list = error.getData();
                WritableArray array = Arguments.createArray();

                for (XPrinterUdpData printerData : list) {
                    WritableMap map = Arguments.createMap();
                    map.putString("macAddress", printerData.getMacAddress());
                    map.putString("lanIp", printerData.getIP());
                    map.putString("netmask", printerData.getNetmask());
                    map.putString("gateway", printerData.getGateway());
                    map.putBoolean("enabledDHCP", printerData.getEnabledDHCP());
                    array.pushMap(map);
                }
                result.putArray("data", array);
                promise.resolve(result);
            }
        });
    }

    @ReactMethod
    public void setLANXPrinter(ReadableMap map, Promise promise) {
        Timber.d("setLANXPrinter");
        if (checkBinderNull(promise)) return;
        XPrinterUdpData data = new XPrinterUdpData();
        try {
            data.setMacAddress(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "macAddress", "").toString());
            data.setIP(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "lanIp", "").toString());
            data.setNetmask(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "netmask", "").toString());
            data.setGateway(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "gateway", "").toString());
            data.setEnabledDHCP((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(map, "enabledDHCP", true));
        } catch (Exception e) {
            e.printStackTrace();
        }
        mBinder.setLANXPrinter(data, new RNCallback<String>() {
            @Override
            public void callback(Error<String> error) {
                ResolveResultHelper.resolveResult(promise, error);
            }
        });
    }

    @ReactMethod
    public void searchUdpPrinters(int timeout, Promise promise) {
        WritableArray list = Arguments.createArray();

        CountDownLatch countDownLatch = new CountDownLatch(1);
        try {
            POSPrinter.searchNetDevice(new UdpCallback() {
                @Override
                public void receive(UdpDevice udpDevice) {
                    try {
                        WritableMap result = Arguments.createMap();
                        result.putString("ip", udpDevice.getIpStr());
                        result.putString("mask", udpDevice.getMaskStr());
                        result.putString("gateway", udpDevice.getGatewayStr());
                        result.putString("mac", udpDevice.getMacStr());
                        result.putString("isDhcp", String.valueOf(udpDevice.isDhcp()));
                        list.pushMap(result);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            promise.resolve(list);
            return;
        }
        try {
            countDownLatch.await(timeout, TimeUnit.MILLISECONDS);
            promise.resolve(list);
        } catch (InterruptedException e) {
            e.printStackTrace();
            promise.resolve(list);
        }
    }

    @ReactMethod
    public void searchUdpPrinterByIp(String ip, int timeout, Promise promise) {
        CountDownLatch countDownLatch = new CountDownLatch(1);
        try {
            POSPrinter.searchNetDevice(new UdpCallback() {
                @Override
                public void receive(UdpDevice udpDevice) {
                    try {
                        if (ip.equals(udpDevice.getIpStr())) {
                            WritableMap result = Arguments.createMap();
                            result.putString("ip", udpDevice.getIpStr());
                            result.putString("mask", udpDevice.getMaskStr());
                            result.putString("gateway", udpDevice.getGatewayStr());
                            result.putString("mac", udpDevice.getMacStr());
                            result.putString("isDhcp", String.valueOf(udpDevice.isDhcp()));
                            promise.resolve(result);
                            countDownLatch.countDown();
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                }
            });
        } catch (Exception e) {
            promise.reject(e);
            return;
        }
        try {
            countDownLatch.await(timeout, TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            promise.reject(e);
        }
    }

    @ReactMethod
    public void searchUdpPrinterByMacAddress(String mac, int timeout, Promise promise) {
        CountDownLatch countDownLatch = new CountDownLatch(1);
        try {
            POSPrinter.searchNetDevice(new UdpCallback() {
                @Override
                public void receive(UdpDevice udpDevice) {
                    try {
                        if (mac.equals(udpDevice.getMacStr())) {
                            WritableMap result = Arguments.createMap();
                            result.putString("ip", udpDevice.getIpStr());
                            result.putString("mask", udpDevice.getMaskStr());
                            result.putString("gateway", udpDevice.getGatewayStr());
                            result.putString("mac", udpDevice.getMacStr());
                            result.putString("isDhcp", String.valueOf(udpDevice.isDhcp()));
                            promise.resolve(result);
                            countDownLatch.countDown();
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                }
            });
        } catch (Exception e) {
            promise.reject(e);
            return;
        }
        try {
            countDownLatch.await(timeout, TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            promise.reject(e);
        }
    }
}