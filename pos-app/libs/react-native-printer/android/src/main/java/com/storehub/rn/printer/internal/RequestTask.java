package com.storehub.rn.printer.internal;

import android.app.Activity;
import android.content.Context;
import android.util.SparseArray;

import com.facebook.react.bridge.ReadableMap;
import com.imin.library.IminSDKManager;
import com.storehub.rn.printer.BuildConfig;
import com.storehub.rn.printer.PrintingManager;
import com.storehub.rn.printer.RNPrinterModule;
import com.storehub.rn.printer.model.AyalaMallReportHBSModel;
import com.storehub.rn.printer.model.BIRTransactionModel;
import com.storehub.rn.printer.model.DailyReportHBSModel;
import com.storehub.rn.printer.model.DynamicBeepQRModel;
import com.storehub.rn.printer.model.EInvoiceQRModel;
import com.storehub.rn.printer.model.KitchenTicketHBSModel;
import com.storehub.rn.printer.model.OrtigasEODReportHBSModel;
import com.storehub.rn.printer.model.SMEODReportHBSModel;
import com.storehub.rn.printer.model.SMXReadingReportHBSModel;
import com.storehub.rn.printer.model.ShiftReportHBSModel;
import com.storehub.rn.printer.model.TransactionHBSModel;
import com.storehub.rn.printer.model.ZReadingHBSModel;
import com.storehub.rn.printer.modelparser.AbstractModelParser;
import com.storehub.rn.printer.modelparser.impl.AyalaMallReportModelParser;
import com.storehub.rn.printer.modelparser.impl.DailyReportModelParser;
import com.storehub.rn.printer.modelparser.impl.DynamicBeepQRModelParser;
import com.storehub.rn.printer.modelparser.impl.EInvoiceQrModelParser;
import com.storehub.rn.printer.modelparser.impl.KitchenTicketModelParser;
import com.storehub.rn.printer.modelparser.impl.OrtigasEODReportModelParser;
import com.storehub.rn.printer.modelparser.impl.SMEODReportModelParser;
import com.storehub.rn.printer.modelparser.impl.SMXReadingReportModelParser;
import com.storehub.rn.printer.modelparser.impl.ShiftReportModelParser;
import com.storehub.rn.printer.modelparser.impl.TransactionModelParser;
import com.storehub.rn.printer.modelparser.impl.ZReadingShiftReportModelParser;
import com.storehub.rn.printer.printer.AbstractPrinter;
import com.storehub.rn.printer.printer.BusinessType;
import com.storehub.rn.printer.printer.CountryType;
import com.storehub.rn.printer.printer.PrinterOutputType;
import com.storehub.rn.printer.printer.PrinterPaperWidth;
import com.storehub.rn.printer.printer.PrinterRequestType;
import com.storehub.rn.printer.printer.callback.ITaskCallback;
import com.storehub.rn.printer.printer.connection.IDataGenerator;
import com.storehub.rn.printer.printer.impl.SunMiPrinter;
import com.storehub.rn.printer.printer.model.PrinterTask;
import com.storehub.rn.printer.printer.model.PrinterTaskResult;
import com.storehub.rn.printer.printer.model.PrintingDataModel;
import com.storehub.rn.printer.printer.printerparser.EPOSPrinterParser;
import com.storehub.rn.printer.util.AyalaMallReportHBSModelUtil;
import com.storehub.rn.printer.util.BIRTransactionModelUtil;
import com.storehub.rn.printer.util.DailyReportHBSModelUtil;
import com.storehub.rn.printer.util.DynamicBeepQRModelUtil;
import com.storehub.rn.printer.util.EInvoiceQRModelUtil;
import com.storehub.rn.printer.util.Error;
import com.storehub.rn.printer.util.ErrorUtil;
import com.storehub.rn.printer.util.KitchenTicketHBSModelUtil;
import com.storehub.rn.printer.util.OrtigasEODReportHBSModelUtil;
import com.storehub.rn.printer.util.ReadableMapUtil;
import com.storehub.rn.printer.util.SMEODReportHBSModelUtil;
import com.storehub.rn.printer.util.SMXReadingReportHBSModelUtil;
import com.storehub.rn.printer.util.ShiftReportHBSModelUtil;
import com.storehub.rn.printer.util.ThreadUtils;
import com.storehub.rn.printer.util.TransactionHBSModelUtil;
import com.storehub.rn.printer.util.ZReadingHBSModelUtil;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import hdx.HdxUtil;
import timber.log.Timber;

/**
 * Created By: sqq
 * Created Time: 2020-01-23 11:17.
 * <p>
 * 1. one RN request split to multi printer tasks.
 * 2. invoke Printer methods
 * 3. invoke Callback（multi-threads message synchronize
 * <p>
 * multi-printers
 */
public class RequestTask {

    private PrinterRequestType requestType;
    private final String taskId;
    private int totalPrinterTaskCount;
    private RNCallback<List<PrinterTaskResult>> callback;
    private SparseArray<PrinterTaskResult> resultMap;
    private Context ctx;


    public RequestTask(@NotNull Context ctx, PrinterRequestType requestType) {
        this.ctx = ctx;
        this.requestType = requestType;
        taskId = generateTaskId();
    }

    public void execute(Activity activity, List<PrintingDataModel> requestDataList, RNCallback<List<PrinterTaskResult>> callback) {
        this.callback = callback;

        if (requestType == PrinterRequestType.CASH_DRAWER) {
            // SunMi Device can control cash drawer without any printers. It's special.
            SunMiPrinter.openCashDrawer();
            IminSDKManager.opencashBox();
            try {
                if (RNPrinterModule.checkModel()) {
                    // 12C
                    HdxUtil.SetV12Power(1);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        if (requestDataList != null && requestDataList.size() > 0) {
            totalPrinterTaskCount = requestDataList.size();
            splitToPrinterTask(activity, requestDataList);
        } else {
            // params error, then directly return failure
            int errCode = ErrorUtil.SUCCESS;
            switch (requestType) {
                case PRINTING:
                    errCode = ErrorUtil.PRINTING_PARAMS_ERROR;
                    break;
                case BUZZ:
                    errCode = ErrorUtil.BUZZ_PARAMS_ERROR;
                    break;
                case CASH_DRAWER:
                    if (SunMiPrinter.mSunMiPrinterService == null) {
                        errCode = ErrorUtil.OPEN_DRAWER_PARAMS_ERROR;
                    }
                    break;
            }

            Error<List<PrinterTaskResult>> error = new Error<>(errCode, ctx);
            callback.callback(error);
        }

    }

    /**
     * split RequestTask to some PrinterTasks
     *
     * @param requestDataList RN delivered origin data
     */
    private void splitToPrinterTask(Activity activity, @NotNull List<PrintingDataModel> requestDataList) {
        for (int i = 0; i < requestDataList.size(); i++) {
            PrintingDataModel item = requestDataList.get(i);

            AbstractPrinter printer = PrintingManager.getInstance(ctx).getPrinter(item.getPrinterId());
            Timber.d("printerId: %s, workflowId: %s \n %s", item.getPrinterId(), item.getWorkflowId(), printer);
            if (printer == null) {
                resolveCallbackWithError(ErrorUtil.Printer_ID_NULL);
                return;
            }

            PrinterTask printerTask = new PrinterTask(taskId, i);
            printerTask.setWorkflowId(item.getWorkflowId()); // add workflowId to track task
            if (requestType == PrinterRequestType.PRINTING) {
                ReadableMap map = item.getData();
                // business type，here one RN request can support different business types, for ex: can print receipt and Kitchen at the same time
                String businessType = item.getBusinessType();
                boolean birAccredited = (boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(map, "birAccredited", false);

                //  businessType,  layout, output, ( kitchen note)
                AbstractModelParser modelParser;
                if (BusinessType.KITCHEN_TICKET.name().equals(businessType)) {
                    KitchenTicketHBSModel kitchenTicketModel = KitchenTicketHBSModelUtil.parseMapToModel(map);
                    modelParser = new KitchenTicketModelParser(ctx, kitchenTicketModel, printer.getPrinterPaperWidth(), printer.isLabelPrinter());
                } else if (BusinessType.SHIFT_REPORT.name().equals(businessType)) {
                    ShiftReportHBSModel shiftReportModel = ShiftReportHBSModelUtil.parseMapToModel(map);
                    modelParser = new ShiftReportModelParser(ctx, shiftReportModel, printer.getPrinterPaperWidth());
                } else if (BusinessType.Z_READING_REPORT.name().equals(businessType)) {
                    ZReadingHBSModel model = ZReadingHBSModelUtil.parseMapToModel(map);
                    modelParser = new ZReadingShiftReportModelParser(ctx, model, printer.getPrinterPaperWidth());
                } else if (BusinessType.DAILY_REPORT.name().equals(businessType)) {
                    DailyReportHBSModel model = DailyReportHBSModelUtil.parseMapToModel(map);
                    modelParser = new DailyReportModelParser(ctx, model, printer.getPrinterPaperWidth());
                } else if (BusinessType.AYALA_MALL_REPORT.name().equals(businessType)) {
                    AyalaMallReportHBSModel model = AyalaMallReportHBSModelUtil.parseMapToModel(map);
                    modelParser = new AyalaMallReportModelParser(ctx, model, printer.getPrinterPaperWidth());
                } else if (BusinessType.SM_EOD_REPORT.name().equals(businessType)) {
                    SMEODReportHBSModel model = SMEODReportHBSModelUtil.parseMapToModel(map);
                    modelParser = new SMEODReportModelParser(ctx, model, printer.getPrinterPaperWidth());
                } else if (BusinessType.SM_XReading_REPORT.name().equals(businessType)) {
                    SMXReadingReportHBSModel model = SMXReadingReportHBSModelUtil.parseMapToModel(map);
                    modelParser = new SMXReadingReportModelParser(ctx, model, printer.getPrinterPaperWidth());
                } else if (BusinessType.ORTIGAS_EOD_REPORT.name().equals(businessType)) {
                    OrtigasEODReportHBSModel model = OrtigasEODReportHBSModelUtil.parseMapToModel(map);
                    modelParser = new OrtigasEODReportModelParser(ctx, model, printer.getPrinterPaperWidth());
                } else if (BusinessType.TRANSACTION.name().equals(businessType)) {
                    String country = ReadableMapUtil.getValueFromMapWithDefaultValue(map, "country", "").toString();
                    //select model Parser according to country
                    if (CountryType.PH.name().equals(country) && birAccredited) {
                        BIRTransactionModel model = BIRTransactionModelUtil.parseMapToModel(map);
                        modelParser = new TransactionModelParser(ctx, model, printer.getPrinterPaperWidth());
                    } else {
                        TransactionHBSModel transactionModel = TransactionHBSModelUtil.parseMapToModel(map);
                        modelParser = new TransactionModelParser(ctx, transactionModel, printer.getPrinterPaperWidth());
                    }
                } else if (BusinessType.DYNAMIC_BEEP_QR.name().equals(businessType)) {
                    DynamicBeepQRModel model = DynamicBeepQRModelUtil.parseMapToModel(map);
                    modelParser = new DynamicBeepQRModelParser(ctx, model, printer.getPrinterPaperWidth());
                } else if (BusinessType.E_INVOICE_QR.name().equals(businessType)) {
                    EInvoiceQRModel model = EInvoiceQRModelUtil.parseMapToModel(map);
                    modelParser = new EInvoiceQrModelParser(ctx, model, printer.getPrinterPaperWidth());
                } else {
                    resolveCallbackWithError(ErrorUtil.Printer_Business_Type_NULL);
                    return;
                }

                //judge the received data type of printer
                if (printer.getPrinterOutputType() == PrinterOutputType.Bitmap) {
                    printerTask.setBitmapDataGenerators(modelParser.getBitmapGenerators());
                } else if (printer.getPrinterOutputType() == PrinterOutputType.BitmapBuffer) {
                    EPOSPrinterParser printerParser = new EPOSPrinterParser(ctx, BusinessType.valueOf(businessType));
                    int pageWidth = printer.getPrinterPaperWidth() == PrinterPaperWidth.Print58 ? 400 : 580;
                    List<IDataGenerator> generators = printerParser.getGenerators(modelParser, pageWidth, printer.getPrinterOutputType());
                    printerTask.setBitmapBufferGenerators(generators);
                } else if (printer.getPrinterOutputType() == PrinterOutputType.HTMLString) {
                    printerTask.setHtmlGenerators(modelParser.getHtmlGenerators());
                }

                Timber.d("requestType: %s, businessType: %s, birAccredited: %s", requestType, businessType, birAccredited);
            }


            ITaskCallback taskCallback = new ITaskCallback() {
                @Override
                public void callback(PrinterTaskResult result) {
                    updateResult(result);
                }
            };

            if (this.requestType == PrinterRequestType.PRINTING) {
                printer.print(activity, ctx, printerTask, taskCallback);
            } else if (this.requestType == PrinterRequestType.CASH_DRAWER) {
                printer.openCashDrawer(ctx, printerTask, taskCallback);
            } else if (this.requestType == PrinterRequestType.BUZZ) {
                printer.buzz(ctx, printerTask, taskCallback);
            }
        }
    }


    @NotNull
    private String generateTaskId() {
        return System.currentTimeMillis() + UUID.randomUUID().toString();
    }

    private synchronized void updateResult(PrinterTaskResult result) {
        if (resultMap == null) {
            resultMap = new SparseArray<>();
        }
        resultMap.put(result.getPrinterTaskId(), result);
        if (resultMap.size() == totalPrinterTaskCount) {
            resolveCallback();
        }
    }

    private void resolveCallbackWithError(int errCode) {
        Timber.d("errCode = [" + errCode + "]");
        Error<List<PrinterTaskResult>> error = new Error<>(errCode, ctx);
        error.setData(null);
        this.callback.callback(error);

    }

    private void resolveCallback() {

        int size = resultMap.size();
        ArrayList<PrinterTaskResult> list = new ArrayList<>(size);

        boolean hasError = false;
        for (int i = 0; i < size; i++) {
            PrinterTaskResult result = resultMap.valueAt(i);
            if (!hasError && result.getErrCode() != 0) {
                hasError = true;
            }
            list.add(result);
        }

        int errCode = ErrorUtil.SUCCESS;
        if (hasError) {
            switch (requestType) {
                case PRINTING:
                    errCode = ErrorUtil.PRINTING_OCCURRED_ERROR;
                    break;
                case BUZZ:
                    errCode = ErrorUtil.BUZZ_OCCURRED_ERROR;
                    break;
                case CASH_DRAWER:
                    errCode = ErrorUtil.OPEN_DRAWER_ERROR;
                    break;
                default:
                    errCode = ErrorUtil.ERROR;
            }
        }

        Error<List<PrinterTaskResult>> error = new Error<>(errCode, ctx);
        error.setData(list);
        this.callback.callback(error);
        if (BuildConfig.DEBUG) {
            ThreadUtils.printAllThreadsInfo();
        }
    }

    public String getTaskId() {
        return taskId;
    }
}
