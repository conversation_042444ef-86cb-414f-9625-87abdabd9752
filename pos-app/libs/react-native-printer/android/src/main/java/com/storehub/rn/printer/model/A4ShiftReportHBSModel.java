package com.storehub.rn.printer.model;

import java.io.Serializable;
import java.util.List;

public class A4ShiftReportHBSModel extends BaseModel implements Serializable {

    private static final long serialVersionUID = -2470029030961778033L;

    private HeaderTitle openTime;
    private HeaderTitle closeTime;
    private HeaderTitle register;
    private HeaderTitle store;
    private HeaderTitle openBy;
    private HeaderTitle closedBy;

    private String transactionsLocalTitle = "TRANSACTIONS";
    private String amountLocalTitle = "AMOUNT";
    private String refundsLocalTitle = "Refunds";
    private String totalLocalTitle = "Total";
    private String salesLocalTitle = "Sales";
    private String netLocalTitle = "Net";
    private String depositsLocalTitle = "Deposits";

    private SalesSummary salesSummary;
    private TaxSummary taxSummary;
    private CashDrawerSummary cashDrawerSummary;

    public CashDrawerSummary getCashDrawerSummary() {
        return cashDrawerSummary;
    }

    public void setCashDrawerSummary(CashDrawerSummary cashDrawerSummary) {
        this.cashDrawerSummary = cashDrawerSummary;
    }

    public static class CashDrawerSummary {
        private CashDrawerItem payout;
        private String qtyLocalTitle;
        private CashDrawerItem cashSales;
        private CashDrawerItem payin;
        private CashDrawerItem cashRefunds;
        private CashDrawerItem cashDeposits;
        private CashDrawerItem expectedDrawer;
        private String localizedTitle;
        private CashDrawerItem actualDrawer;
        private CashDrawerItem overShort;
        private CashDrawerItem openingAmount;

        public CashDrawerSummary(CashDrawerItem payout, String qtyLocalTitle, CashDrawerItem cashSales,
                                 CashDrawerItem payin, CashDrawerItem cashRefunds,
                                 CashDrawerItem cashDeposits, CashDrawerItem expectedDrawer,
                                 String localizedTitle, CashDrawerItem actualDrawer,
                                 CashDrawerItem overShort, CashDrawerItem openingAmount) {
            this.payout = payout;
            this.qtyLocalTitle = qtyLocalTitle;
            this.cashSales = cashSales;
            this.payin = payin;
            this.cashRefunds = cashRefunds;
            this.cashDeposits = cashDeposits;
            this.expectedDrawer = expectedDrawer;
            this.localizedTitle = localizedTitle;
            this.actualDrawer = actualDrawer;
            this.overShort = overShort;
            this.openingAmount = openingAmount;
        }

        public CashDrawerItem getPayout() {
            return payout;
        }

        public void setPayout(CashDrawerItem payout) {
            this.payout = payout;
        }

        public String getQtyLocalTitle() {
            return qtyLocalTitle;
        }

        public void setQtyLocalTitle(String qtyLocalTitle) {
            this.qtyLocalTitle = qtyLocalTitle;
        }

        public CashDrawerItem getCashSales() {
            return cashSales;
        }

        public void setCashSales(CashDrawerItem cashSales) {
            this.cashSales = cashSales;
        }

        public CashDrawerItem getPayin() {
            return payin;
        }

        public void setPayin(CashDrawerItem payin) {
            this.payin = payin;
        }

        public CashDrawerItem getCashRefunds() {
            return cashRefunds;
        }

        public void setCashRefunds(CashDrawerItem cashRefunds) {
            this.cashRefunds = cashRefunds;
        }

        public CashDrawerItem getCashDeposits() {
            return cashDeposits;
        }

        public void setCashDeposits(CashDrawerItem cashDeposits) {
            this.cashDeposits = cashDeposits;
        }

        public CashDrawerItem getExpectedDrawer() {
            return expectedDrawer;
        }

        public void setExpectedDrawer(CashDrawerItem expectedDrawer) {
            this.expectedDrawer = expectedDrawer;
        }

        public String getLocalizedTitle() {
            return localizedTitle;
        }

        public void setLocalizedTitle(String localizedTitle) {
            this.localizedTitle = localizedTitle;
        }

        public CashDrawerItem getActualDrawer() {
            return actualDrawer;
        }

        public void setActualDrawer(CashDrawerItem actualDrawer) {
            this.actualDrawer = actualDrawer;
        }

        public CashDrawerItem getOverShort() {
            return overShort;
        }

        public void setOverShort(CashDrawerItem overShort) {
            this.overShort = overShort;
        }

        public CashDrawerItem getOpeningAmount() {
            return openingAmount;
        }

        public void setOpeningAmount(CashDrawerItem openingAmount) {
            this.openingAmount = openingAmount;
        }
    }
    public static class CashDrawerItem {
        private String qty;
        private String amount;
        private String localizedTitle;

        public CashDrawerItem(String amount, String localizedTitle) {
            this.amount = amount;
            this.localizedTitle = localizedTitle;
        }

        public CashDrawerItem(String qty, String amount, String localizedTitle) {
            this.qty = qty;
            this.amount = amount;
            this.localizedTitle = localizedTitle;
        }

        public String getQty() {
            return qty;
        }

        public void setQty(String qty) {
            this.qty = qty;
        }

        public String getAmount() {
            return amount;
        }

        public void setAmount(String amount) {
            this.amount = amount;
        }

        public String getLocalizedTitle() {
            return localizedTitle;
        }

        public void setLocalizedTitle(String localizedTitle) {
            this.localizedTitle = localizedTitle;
        }
    }
    private CancelAndDiscountSummary cancelAndDiscountSummary;

    public CancelAndDiscountSummary getCancelAndDiscountSummary() {
        return cancelAndDiscountSummary;
    }

    public void setCancelAndDiscountSummary(CancelAndDiscountSummary cancelAndDiscountSummary) {
        this.cancelAndDiscountSummary = cancelAndDiscountSummary;
    }

    public static class CancelAndDiscountSummary {
        private String localizedTitle;
        private CancelAndDiscountItem cancelledTransactions;
        private CancelAndDiscountItem discount;

        public CancelAndDiscountSummary(String localizedTitle, CancelAndDiscountItem cancelledTransactions, CancelAndDiscountItem discount) {
            this.localizedTitle = localizedTitle;
            this.cancelledTransactions = cancelledTransactions;
            this.discount = discount;
        }

        public String getLocalizedTitle() {
            return localizedTitle;
        }

        public void setLocalizedTitle(String localizedTitle) {
            this.localizedTitle = localizedTitle;
        }

        public CancelAndDiscountItem getCancelledTransactions() {
            return cancelledTransactions;
        }

        public void setCancelledTransactions(CancelAndDiscountItem cancelledTransactions) {
            this.cancelledTransactions = cancelledTransactions;
        }

        public CancelAndDiscountItem getDiscount() {
            return discount;
        }

        public void setDiscount(CancelAndDiscountItem discount) {
            this.discount = discount;
        }
    }

    public static class CancelAndDiscountItem {
        private String amount;
        private String transactions;
        private String localizedTitle;

        public CancelAndDiscountItem(String amount, String transactions, String localizedTitle) {
            this.amount = amount;
            this.transactions = transactions;
            this.localizedTitle = localizedTitle;
        }

        public String getAmount() {
            return amount;
        }

        public void setAmount(String amount) {
            this.amount = amount;
        }

        public String getTransactions() {
            return transactions;
        }

        public void setTransactions(String transactions) {
            this.transactions = transactions;
        }

        public String getLocalizedTitle() {
            return localizedTitle;
        }

        public void setLocalizedTitle(String localizedTitle) {
            this.localizedTitle = localizedTitle;
        }
    }

    public TaxSummary getTaxSummary() {
        return taxSummary;
    }

    public void setTaxSummary(TaxSummary taxSummary) {
        this.taxSummary = taxSummary;
    }

    public static class TaxSummary {
        private String localizedTitle;
        private String taxRateLocalTitle;
        private String total;
        private List<TaxItem> taxes;

        public TaxSummary(String localizedTitle, String taxRateLocalTitle, String total, List<TaxItem> taxes) {
            this.localizedTitle = localizedTitle;
            this.taxRateLocalTitle = taxRateLocalTitle;
            this.total = total;
            this.taxes = taxes;
        }

        public String getLocalizedTitle() {
            return localizedTitle;
        }

        public void setLocalizedTitle(String localizedTitle) {
            this.localizedTitle = localizedTitle;
        }

        public String getTaxRateLocalTitle() {
            return taxRateLocalTitle;
        }

        public void setTaxRateLocalTitle(String taxRateLocalTitle) {
            this.taxRateLocalTitle = taxRateLocalTitle;
        }

        public String getTotal() {
            return total;
        }

        public void setTotal(String total) {
            this.total = total;
        }

        public List<TaxItem> getTaxes() {
            return taxes;
        }

        public void setTaxes(List<TaxItem> taxes) {
            this.taxes = taxes;
        }
    }

    public static class TaxItem {
        private String taxCode;
        private String taxRate;
        private String amount;

        public TaxItem(String taxCode, String taxRate, String amount) {
            this.taxCode = taxCode;
            this.taxRate = taxRate;
            this.amount = amount;
        }

        public String getTaxCode() {
            return taxCode;
        }

        public void setTaxCode(String taxCode) {
            this.taxCode = taxCode;
        }

        public String getTaxRate() {
            return taxRate;
        }

        public void setTaxRate(String taxRate) {
            this.taxRate = taxRate;
        }

        public String getAmount() {
            return amount;
        }

        public void setAmount(String amount) {
            this.amount = amount;
        }
    }

    public SalesSummary getSalesSummary() {
        return salesSummary;
    }

    public void setSalesSummary(SalesSummary salesSummary) {
        this.salesSummary = salesSummary;
    }

    public static class SalesSummary {
        private String localizedTitle;
        private String total;
        private List<Payment> value;

        public SalesSummary(String localizedTitle, String total, List<Payment> value) {
            this.localizedTitle = localizedTitle;
            this.total = total;
            this.value = value;
        }

        public String getLocalizedTitle() {
            return localizedTitle;
        }

        public void setLocalizedTitle(String localizedTitle) {
            this.localizedTitle = localizedTitle;
        }

        public String getTotal() {
            return total;
        }

        public void setTotal(String total) {
            this.total = total;
        }

        public List<Payment> getValue() {
            return value;
        }

        public void setValue(List<Payment> value) {
            this.value = value;
        }
    }

    public static class Payment {
        private String localizedTitle;
        private String net;
        private PayItem refunds;
        private PayItem sales;

        private RoundingItem roundings;

        public Payment(String localizedTitle, String net, PayItem refunds, PayItem sales,RoundingItem roundings) {
            this.localizedTitle = localizedTitle;
            this.net = net;
            this.refunds = refunds;
            this.sales = sales;
            this.roundings = roundings;
        }

        public RoundingItem getRoundings() {
            return roundings;
        }

        public void setRoundings(RoundingItem roundings) {
            this.roundings = roundings;
        }

        public String getLocalizedTitle() {
            return localizedTitle;
        }

        public void setLocalizedTitle(String localizedTitle) {
            this.localizedTitle = localizedTitle;
        }

        public String getNet() {
            return net;
        }

        public void setNet(String net) {
            this.net = net;
        }

        public PayItem getRefunds() {
            return refunds;
        }

        public void setRefunds(PayItem refunds) {
            this.refunds = refunds;
        }

        public PayItem getSales() {
            return sales;
        }

        public void setSales(PayItem sales) {
            this.sales = sales;
        }
    }

    public static class PayItem {
        private String amount;
        private String transactions;

        public PayItem(String amount, String transactions) {
            this.amount = amount;
            this.transactions = transactions;
        }

        public String getAmount() {
            return amount;
        }

        public void setAmount(String amount) {
            this.amount = amount;
        }

        public String getTransactions() {
            return transactions;
        }

        public void setTransactions(String transactions) {
            this.transactions = transactions;
        }
    }

    public static class RoundingItem {
        private String title;
        private String amount;

        public RoundingItem(String title, String amount) {
            this.title = title;
            this.amount = amount;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getAmount() {
            return amount;
        }

        public void setAmount(String amount) {
            this.amount = amount;
        }
    }



    public String getTransactionsLocalTitle() {
        return transactionsLocalTitle;
    }

    public void setTransactionsLocalTitle(String transactionsLocalTitle) {
        this.transactionsLocalTitle = transactionsLocalTitle;
    }

    public String getAmountLocalTitle() {
        return amountLocalTitle;
    }

    public void setAmountLocalTitle(String amountLocalTitle) {
        this.amountLocalTitle = amountLocalTitle;
    }

    public String getRefundsLocalTitle() {
        return refundsLocalTitle;
    }

    public void setRefundsLocalTitle(String refundsLocalTitle) {
        this.refundsLocalTitle = refundsLocalTitle;
    }

    public String getTotalLocalTitle() {
        return totalLocalTitle;
    }

    public void setTotalLocalTitle(String totalLocalTitle) {
        this.totalLocalTitle = totalLocalTitle;
    }

    public String getSalesLocalTitle() {
        return salesLocalTitle;
    }

    public void setSalesLocalTitle(String salesLocalTitle) {
        this.salesLocalTitle = salesLocalTitle;
    }

    public String getNetLocalTitle() {
        return netLocalTitle;
    }

    public void setNetLocalTitle(String netLocalTitle) {
        this.netLocalTitle = netLocalTitle;
    }

    public String getDepositsLocalTitle() {
        return depositsLocalTitle;
    }

    public void setDepositsLocalTitle(String depositsLocalTitle) {
        this.depositsLocalTitle = depositsLocalTitle;
    }

    public HeaderTitle getOpenTime() {
        return openTime;
    }

    public void setOpenTime(HeaderTitle openTime) {
        this.openTime = openTime;
    }

    public HeaderTitle getCloseTime() {
        return closeTime;
    }

    public void setCloseTime(HeaderTitle closeTime) {
        this.closeTime = closeTime;
    }

    public HeaderTitle getRegister() {
        return register;
    }

    public void setRegister(HeaderTitle register) {
        this.register = register;
    }

    public HeaderTitle getStore() {
        return store;
    }

    public void setStore(HeaderTitle store) {
        this.store = store;
    }

    public HeaderTitle getOpenBy() {
        return openBy;
    }

    public void setOpenBy(HeaderTitle openBy) {
        this.openBy = openBy;
    }

    public HeaderTitle getClosedBy() {
        return closedBy;
    }

    public void setClosedBy(HeaderTitle closedBy) {
        this.closedBy = closedBy;
    }

    public static class HeaderTitle {
        private String localizedTitle;
        private String value;

        public HeaderTitle(String localizedTitle, String value) {
            this.localizedTitle = localizedTitle;
            this.value = value;
        }

        public String getLocalizedTitle() {
            return localizedTitle;
        }

        public void setLocalizedTitle(String localizedTitle) {
            this.localizedTitle = localizedTitle;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }
}