package com.storehub.rn.printer.model;

import android.content.Context;

import java.util.ArrayList;
import java.util.List;

public class A4ShiftReportModelAdapter {

    /**
     * Most of string have been defined at resource files, but is initial capitalization. Write the strings
     * for A4 printing in the codes directly, since all resource files use English strings.
     * @param context
     * @param model
     * @return
     */
    public static A4ShiftReportHBSModel ofA4ShiftReportModel(Context context, ShiftReportHBSModel model) {
        A4ShiftReportHBSModel a4Model = new A4ShiftReportHBSModel();
        // Header information
        String title = model.getShiftOpenTime();
        String localizedTitle = "SHIFT OPEN TIME:";
        A4ShiftReportHBSModel.HeaderTitle openTime = new A4ShiftReportHBSModel.HeaderTitle(localizedTitle, extractValue(title));
        a4Model.setOpenTime(openTime);

        title = model.getShiftCloseTime();
        localizedTitle = "SHIFT CLOSE TIME:";
        A4ShiftReportHBSModel.HeaderTitle closeTime = new A4ShiftReportHBSModel.HeaderTitle(localizedTitle, extractValue(title));
        a4Model.setCloseTime(closeTime);

        title = model.getRegisterId();
        localizedTitle = "REGISTER #:";
        A4ShiftReportHBSModel.HeaderTitle register = new A4ShiftReportHBSModel.HeaderTitle(localizedTitle, extractValue(title));
        a4Model.setRegister(register);

        title = model.getStoreName();
        localizedTitle = "STORE";
        A4ShiftReportHBSModel.HeaderTitle store = new A4ShiftReportHBSModel.HeaderTitle(localizedTitle, extractValue(title));
        a4Model.setStore(store);

        title = model.getManager();
        localizedTitle = "OPEN BY";
        A4ShiftReportHBSModel.HeaderTitle opener = new A4ShiftReportHBSModel.HeaderTitle(localizedTitle, extractValue(title));
        a4Model.setOpenBy(opener);

        title = model.getManager();
        localizedTitle = "CLOSE BY";
        A4ShiftReportHBSModel.HeaderTitle closer = new A4ShiftReportHBSModel.HeaderTitle(localizedTitle, extractValue(title));
        a4Model.setClosedBy(closer);

        // Sale summary
        List<ShiftReportHBSModel.Payment> payments = model.getPayments();
        List<A4ShiftReportHBSModel.Payment> convertPayments = new ArrayList<>();
        for (ShiftReportHBSModel.Payment p : payments) {
            title = p.getTitle();
            String net = p.getNetAmount();
            A4ShiftReportHBSModel.PayItem refunds = new A4ShiftReportHBSModel.PayItem(p.getRefundsAmount(), p.getRefundsQuantity());
            A4ShiftReportHBSModel.PayItem sales = new A4ShiftReportHBSModel.PayItem(p.getSalesAmount(), p.getSalesQuantity());
            A4ShiftReportHBSModel.RoundingItem roundings = new A4ShiftReportHBSModel.RoundingItem(p.getRoundingTitle(), p.getRoundingAmount());
            A4ShiftReportHBSModel.Payment pay = new A4ShiftReportHBSModel.Payment(title, net, refunds, sales,roundings);
            convertPayments.add(pay);
        }
        title = "SALES SUMMARY";
        A4ShiftReportHBSModel.SalesSummary salesSummary = new A4ShiftReportHBSModel.SalesSummary(title, model.getSalesSummary().getNetAmount(), convertPayments);
        a4Model.setSalesSummary(salesSummary);

        // Tax summary
        List<ShiftReportHBSModel.TaxSummary> taxSummaries = model.getTaxSummaryArray();
        List<A4ShiftReportHBSModel.TaxItem> convertTax = new ArrayList<>();
        float taxTotal = 0f;
        for (ShiftReportHBSModel.TaxSummary taxSummary: taxSummaries) {
            String taxCode = taxSummary.getTaxName();
            String taxRate = taxSummary.getTaxRate();
            String amount = taxSummary.getAmount();
            taxTotal += Float.valueOf(amount);
            A4ShiftReportHBSModel.TaxItem taxItem = new A4ShiftReportHBSModel.TaxItem(taxCode, taxRate, amount);
            convertTax.add(taxItem);
        }
        title = "TAX SUMMARY";
        localizedTitle = "TAX RATE";
        A4ShiftReportHBSModel.TaxSummary taxSummary = new A4ShiftReportHBSModel.TaxSummary(title, localizedTitle, String.valueOf(taxTotal), convertTax);
        a4Model.setTaxSummary(taxSummary);

        // cancelAndDiscountSummary
        title = "CANCELS / DISC. SUMMARY";
        ShiftReportHBSModel.CancelAndDiscountSummary cancelAndDiscountSummary = model.getCancelAndDiscountSummary();
        localizedTitle = "Cancel Txns";
        A4ShiftReportHBSModel.CancelAndDiscountItem cancel = new A4ShiftReportHBSModel.CancelAndDiscountItem(cancelAndDiscountSummary.getCancelAmount(), cancelAndDiscountSummary.getCancelQuantity(), localizedTitle);
        localizedTitle = "Discount";
        A4ShiftReportHBSModel.CancelAndDiscountItem discount = new A4ShiftReportHBSModel.CancelAndDiscountItem(cancelAndDiscountSummary.getDiscountAmount(), cancelAndDiscountSummary.getDiscountQuantity(), localizedTitle);
        A4ShiftReportHBSModel.CancelAndDiscountSummary a4cancelAndDiscountSummary = new A4ShiftReportHBSModel.CancelAndDiscountSummary(title, cancel, discount);
        a4Model.setCancelAndDiscountSummary(a4cancelAndDiscountSummary);

        // cashDrawerSummary
        ShiftReportHBSModel.CashDrawerSummary cashDrawerSummary = model.getCashDrawerSummary();
        localizedTitle = "Pay Out";
        A4ShiftReportHBSModel.CashDrawerItem payout = new A4ShiftReportHBSModel.CashDrawerItem(cashDrawerSummary.getPayOutQuantity(), cashDrawerSummary.getPayOutAmount(), localizedTitle);
        String qtyTitle = "QUANTITY";
        localizedTitle = "Cash Sales";
        A4ShiftReportHBSModel.CashDrawerItem cashSales = new A4ShiftReportHBSModel.CashDrawerItem(cashDrawerSummary.getCashSalesQuantity(), cashDrawerSummary.getCashSalesAmount(), localizedTitle);
        localizedTitle = "Pay In";
        A4ShiftReportHBSModel.CashDrawerItem payin = new A4ShiftReportHBSModel.CashDrawerItem(cashDrawerSummary.getPayInQuantity(), cashDrawerSummary.getPayInAmount(), localizedTitle);
        localizedTitle = "Cash Refunds";
        A4ShiftReportHBSModel.CashDrawerItem cashRefunds = new A4ShiftReportHBSModel.CashDrawerItem(cashDrawerSummary.getCashRefundsQuantity(), cashDrawerSummary.getCashRefundsAmount(), localizedTitle);
        localizedTitle = "Cash Deposits";
        A4ShiftReportHBSModel.CashDrawerItem cashDeposits = new A4ShiftReportHBSModel.CashDrawerItem(cashDrawerSummary.getCashDepositsQuantity(), cashDrawerSummary.getCashDepositsAmount(), localizedTitle);
        localizedTitle = "Expected Drawer";
        A4ShiftReportHBSModel.CashDrawerItem expectedDrawer = new A4ShiftReportHBSModel.CashDrawerItem(model.getExpectedDrawer(), localizedTitle);
        localizedTitle = "Actual Drawer";
        A4ShiftReportHBSModel.CashDrawerItem actualDrawer = new A4ShiftReportHBSModel.CashDrawerItem(model.getActualDrawer(), localizedTitle);
        localizedTitle = "Over/Short";
        A4ShiftReportHBSModel.CashDrawerItem overShort = new A4ShiftReportHBSModel.CashDrawerItem(model.getOverShort(), localizedTitle);
        localizedTitle = "Opening Amount";
        A4ShiftReportHBSModel.CashDrawerItem openingAmount = new A4ShiftReportHBSModel.CashDrawerItem(model.getCashDrawerSummary().getOpeningAmount(), localizedTitle);
        localizedTitle = "CASH DRAWER SUMMARY";
        A4ShiftReportHBSModel.CashDrawerSummary a4CashDrawerSummary = new A4ShiftReportHBSModel.CashDrawerSummary(payout, qtyTitle, cashSales, payin, cashRefunds, cashDeposits, expectedDrawer, localizedTitle, actualDrawer, overShort, openingAmount);
        a4Model.setCashDrawerSummary(a4CashDrawerSummary);

        return a4Model;
    }

    /**
     * The format of title provided from RN is name: value. take the store as an example, store: JustCoffee.
     * such design is fragile, the implementation depends on outside module. and the format is not
     * always as this. one case is the title is JustCoffee, lacks store: part
     * @param title
     * @return
     */
    private static String extractValue(String title) {
        int index = title.indexOf(':');
        return (index >= 0 && index < title.length() - 1) ? title.substring(title.indexOf(':') + 1) : title;
    }
}
