package com.storehub.rn.printer.model;

import java.io.Serializable;
import java.util.List;

public class BIRTransactionModel extends BaseTransactionModel implements Serializable {
    // store name
    private String storeName;
    // basic store info (not half receipt)
    private String address;
    private String phone;
    private String companyName;
    // under Company Name, will show if BIR is enable (not half receipt)
    private String birCompanyName;
    // customer information part
    private String customerInfo;
    // receipt info (not half receipt)
    private String receiptDate;
    private String voidNo;
    private String reprintDate;
    private String receiptNumber;

    private String orderId;
    // under receiptNumber, will show is BIR is enable (not half receipt)
    private String orderNumber;
    private String minNumber;
    private String serialNumber;
    private String reasonString;
    private String noteString;
    // cashier information
    private String cashierInfo;
    private String registerNumber;
    private String middleOrderTableTitleWithContent;
    private String receiptTitle;
    private List<String> columnTitleString;

    private String taxTitle;
    private String tax;
    // judge bir enable or disable , discount field should be null or not
    private String subtotalTitle;
    private String subtotal;
    private String discountTitle;
    private String a4Discount;
    private String discount;
    private String less12vatTitle;
    private String less12vat;
    private String adhocDiscountTitle;
    private String adhocDiscount;
    private String seniorDiscountTitle;
    private String seniorDiscount;
    private String pwdDiscountTitle;
    private String pwdDiscount;
    private String athleteAndCoachDiscountTitle;
    private String athleteAndCoachDiscount;
    private String soloParentDiscountTitle;
    private String soloParentDiscount;
    private String medalOfValorDiscountTitle;
    private String medalOfValorDiscount;
    private String vatOf12Title;
    private String vatOf12;
    private String amusementTaxTitle;
    private String amusementTax;
    private String serviceChargeTitle;
    private String serviceCharge;
    private String roundingTitle;
    private String rounding;
    private Boolean showOrderSummary;
    private String totalTitle;
    private String total;
    private String smallOrderFeeTxt;
    private String smallOrderFeeValue;
    private String containerFeeTxt;
    private String containerFeeValue;

    private String amountOutStandingTitle;
    private String amountOutStanding;
    private String vatableSalesTitle;
    private String vatableSales;
    private String vatAmountTitle;
    private String vatAmount;
    private String vatExemptSalesTitle;
    private String vatExemptSales;
    private String zeroRatedSalesTitle;
    private String zeroRatedSales;
    private List<BirInfoType> birInfoList;
    private String beepCashbackAmount;
    private String storeCreditAmount;
    private boolean showReceiptStoreCredit;  //boolean
    private List<String> receiptStoreCreditTitleString;  // String[]
    private String loyaltyEarned;
    private String loyaltySpent;
    private String loyaltyBalance;
    private String earnedTitle;
    private String balanceTitle;
    private String spentTitle;
    private String cashbackExpirationDesc;
    // online delivery information
    private String onlineChannelNotesTitle;
    private String onlineChannelNotesContent;
    private String onlinePickUpNoteTitle;
    private String onlineOrderNoteContent;
    private String preOrderNotes;
    private String footerLabelString;
    private String birAccrInfo;
    private String accrNumber;
    private String ptuNumber;
    private String dateIssueNumber;
    private String vatRegisterFooterInfo;
    private String storehubPoweredInfo;
    private List<PaymentType> payment;

    // Pre order
    private Boolean showPreorderSummary;
    private String depositAmountTitle;
    private String unPaidBalanceTitle;
    private String depositAmount;
    private String unPaidBalance;
    private String pickUpDate;

    private boolean enableCashback;
    private boolean showPurchasedItemsDiscount;
    private boolean showVatSummary;

    private String membershipSmallTitle;
    private String membershipBoildTitle;
    private String membershipLargeContentTitle1;
    private String membershipLargeContentTitle2;
    private String membershipSmallBottomTitle;


    public String getA4Discount() {
        return a4Discount;
    }

    public void setA4Discount(String a4Discount) {
        this.a4Discount = a4Discount;
    }
    public String getSmallOrderFeeTxt() {
        return smallOrderFeeTxt;
    }

    public void setSmallOrderFeeTxt(String smallOrderFeeTxt) {
        this.smallOrderFeeTxt = smallOrderFeeTxt;
    }

    public String getSmallOrderFeeValue() {
        return smallOrderFeeValue;
    }

    public void setSmallOrderFeeValue(String smallOrderFeeValue) {
        this.smallOrderFeeValue = smallOrderFeeValue;
    }

    public String getContainerFeeTxt() {
        return containerFeeTxt;
    }

    public void setContainerFeeTxt(String containerFeeTxt) {
        this.containerFeeTxt = containerFeeTxt;
    }


    public Boolean getShowOrderSummary() {
        return showOrderSummary;
    }

    public void setShowOrderSummary(Boolean showOrderSummary) {
        this.showOrderSummary = showOrderSummary;
    }


    public String getContainerFeeValue() {
        return containerFeeValue;
    }

    public void setContainerFeeValue(String containerFeeValue) {
        this.containerFeeValue = containerFeeValue;
    }


    public String getPickUpDate() {
        return pickUpDate;
    }

    public void setPickUpDate(String pickUpDate) {
        this.pickUpDate = pickUpDate;
    }

    public List<BirInfoType> getBirInfoList() {
        return birInfoList;
    }

    public void setBirInfoList(List<BirInfoType> birInfoList) {
        this.birInfoList = birInfoList;
    }

    public Boolean getShowPreorderSummary() {
        return showPreorderSummary;
    }

    public void setShowPreorderSummary(Boolean showPreorderSummary) {
        this.showPreorderSummary = showPreorderSummary;
    }

    public String getDepositAmountTitle() {
        return depositAmountTitle;
    }

    public void setDepositAmountTitle(String depositAmountTitle) {
        this.depositAmountTitle = depositAmountTitle;
    }

    public String getUnPaidBalanceTitle() {
        return unPaidBalanceTitle;
    }

    public void setUnPaidBalanceTitle(String unPaidBalanceTitle) {
        this.unPaidBalanceTitle = unPaidBalanceTitle;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getDepositAmount() {
        return depositAmount;
    }

    public void setDepositAmount(String depositAmount) {
        this.depositAmount = depositAmount;
    }

    public String getUnPaidBalance() {
        return unPaidBalance;
    }

    public String getTaxTitle() {
        return taxTitle;
    }

    public void setTaxTitle(String taxTitle) {
        this.taxTitle = taxTitle;
    }

    public String getTax() {
        return tax;
    }

    public void setTax(String tax) {
        this.tax = tax;
    }

    public void setUnPaidBalance(String unPaidBalance) {
        this.unPaidBalance = unPaidBalance;
    }

    public BIRTransactionModel() {
    }

    public boolean isEnablePrintCashback() {
        return enablePrintCashback;
    }

    public void setEnablePrintCashback(boolean enablePrintCashback) {
        this.enablePrintCashback = enablePrintCashback;
    }

    public boolean isShowReceiptStoreCredit() {
        return showReceiptStoreCredit;
    }

    public void setShowReceiptStoreCredit(boolean showReceiptStoreCredit) {
        this.showReceiptStoreCredit = showReceiptStoreCredit;
    }

    public List<String> getReceiptStoreCreditTitleString() {
        return receiptStoreCreditTitleString;
    }

    public void setReceiptStoreCreditTitleString(List<String> receiptStoreCreditTitleString) {
        this.receiptStoreCreditTitleString = receiptStoreCreditTitleString;
    }

    public boolean isShowVatSummary() {
        return showVatSummary;
    }

    public void setShowVatSummary(boolean showVatSummary) {
        this.showVatSummary = showVatSummary;
    }

    public boolean isShowPurchasedItemsDiscount() {
        return showPurchasedItemsDiscount;
    }

    public void setShowPurchasedItemsDiscount(boolean showPurchasedItemsDiscount) {
        this.showPurchasedItemsDiscount = showPurchasedItemsDiscount;
    }

    public String getEarnedTitle() {
        return earnedTitle;
    }

    public void setEarnedTitle(String earnedTitle) {
        this.earnedTitle = earnedTitle;
    }

    public String getBalanceTitle() {
        return balanceTitle;
    }

    public void setBalanceTitle(String balanceTitle) {
        this.balanceTitle = balanceTitle;
    }

    public String getSpentTitle() {
        return spentTitle;
    }

    public void setSpentTitle(String spentTitle) {
        this.spentTitle = spentTitle;
    }

    public boolean isShowBarcode() {
        return showBarcode;
    }

    public void setShowBarcode(boolean showBarcode) {
        this.showBarcode = showBarcode;
    }

    public String getLogoImage() {
        return logoImage;
    }

    public void setLogoImage(String logoImage) {
        this.logoImage = logoImage;
    }

    public boolean isEnableCashback() {
        return enableCashback;
    }

    public void setEnableCashback(boolean enableCashback) {
        this.enableCashback = enableCashback;
    }

    public String getCashbackUrl() {
        return cashbackUrl;
    }

    public void setCashbackUrl(String cashbackUrl) {
        this.cashbackUrl = cashbackUrl;
    }

    public String getEInvoiceUrl() {
        return eInvoiceUrl;
    }

    public void setEInvoiceUrl(String eInvoiceUrl) {
        this.eInvoiceUrl = eInvoiceUrl;
    }

    public String getEInvoiceDescription() {
        return eInvoiceDescription;
    }

    public void setEInvoiceDescription(String eInvoiceDescription) {
        this.eInvoiceDescription = eInvoiceDescription;
    }

    public boolean getEnablePrintEInvoice() {
        return enablePrintEInvoice;
    }

    public void setEnablePrintEInvoice(boolean enablePrintEInvoice) {
        this.enablePrintEInvoice = enablePrintEInvoice;
    }

    public String getBusiness() {
        return business;
    }

    public void setBusiness(String business) {
        this.business = business;
    }


    public boolean isEnablePrintQRCode() {
        return enablePrintQRCode;
    }

    public void setEnablePrintQRCode(boolean enablePrintQRCode) {
        this.enablePrintQRCode = enablePrintQRCode;
    }

    public boolean isEnablePrintMemberShip() {
        return enablePrintMemberShip;
    }

    public void setEnablePrintMemberShip(boolean enablePrintMemberShip) {
        this.enablePrintMemberShip = enablePrintMemberShip;
    }

    public String getMembershipSource() {
        return membershipSource;
    }

    public void setMembershipSource(String membershipSource) {
        this.membershipSource = membershipSource;
    }

    public String getMembershipUrl() {
        return membershipUrl;
    }

    public void setMembershipUrl(String membershipUrl) {
        this.membershipUrl = membershipUrl;
    }

    public String getQrCodeDesc() {
        return qrCodeDesc;
    }

    public void setQrCodeDesc(String qrCodeDesc) {
        this.qrCodeDesc = qrCodeDesc;
    }

    public String getQrCodeAboveInfo() {
        return qrCodeAboveInfo;
    }

    public void setQrCodeAboveInfo(String qrCodeAboveInfo) {
        this.qrCodeAboveInfo = qrCodeAboveInfo;
    }

    public String getQrCodeUnderInfo() {
        return qrCodeUnderInfo;
    }

    public void setQrCodeUnderInfo(String qrCodeUnderInfo) {
        this.qrCodeUnderInfo = qrCodeUnderInfo;
    }

    public String getReceiptId() {
        return receiptId;
    }

    public void setReceiptId(String receiptId) {
        this.receiptId = receiptId;
    }

    public String getBeepCashbackAmount() {
        return beepCashbackAmount;
    }

    public void setBeepCashbackAmount(String beepCashbackAmount) {
        this.beepCashbackAmount = beepCashbackAmount;
    }

    public String getStoreCreditAmount() {
        return storeCreditAmount;
    }

    public void setStoreCreditAmount(String storeCreditAmount) {
        this.storeCreditAmount = storeCreditAmount;
    }


    public String getAthleteAndCoachDiscountTitle() {
        return athleteAndCoachDiscountTitle;
    }

    public void setAthleteAndCoachDiscountTitle(String athleteAndCoachDiscountTitle) {
        this.athleteAndCoachDiscountTitle = athleteAndCoachDiscountTitle;
    }

    public String getAthleteAndCoachDiscount() {
        return athleteAndCoachDiscount;
    }

    public void setAthleteAndCoachDiscount(String athleteAndCoachDiscount) {
        this.athleteAndCoachDiscount = athleteAndCoachDiscount;
    }

    public String getMedalOfValorDiscountTitle() {
        return medalOfValorDiscountTitle;
    }

    public void setMedalOfValorDiscountTitle(String medalOfValorDiscountTitle) {
        this.medalOfValorDiscountTitle = medalOfValorDiscountTitle;
    }

    public String getMedalOfValorDiscount() {
        return medalOfValorDiscount;
    }

    public void setMedalOfValorDiscount(String medalOfValorDiscount) {
        this.medalOfValorDiscount = medalOfValorDiscount;
    }


    public String getSoloParentDiscountTitle() {
        return soloParentDiscountTitle;
    }

    public void setSoloParentDiscountTitle(String soloParentDiscountTitle) {
        this.soloParentDiscountTitle = soloParentDiscountTitle;
    }

    public String getSoloParentDiscount() {
        return soloParentDiscount;
    }

    public void setSoloParentDiscount(String soloParentDiscount) {
        this.soloParentDiscount = soloParentDiscount;
    }


    public String getLoyaltyEarned() {
        return loyaltyEarned;
    }

    public void setLoyaltyEarned(String loyaltyEarned) {
        this.loyaltyEarned = loyaltyEarned;
    }

    public String getLoyaltySpent() {
        return loyaltySpent;
    }

    public void setLoyaltySpent(String loyaltySpent) {
        this.loyaltySpent = loyaltySpent;
    }

    public String getLoyaltyBalance() {
        return loyaltyBalance;
    }

    public void setLoyaltyBalance(String loyaltyBalance) {
        this.loyaltyBalance = loyaltyBalance;
    }

    public List<PaymentType> getPayment() {
        return payment;
    }

    public void setPayment(List<PaymentType> payment) {
        this.payment = payment;
    }

    public String getSubtotal() {
        return subtotal;
    }

    public String getDiscount() {
        return discount;
    }

    public String getServiceCharge() {
        return serviceCharge;
    }

    public List<String> getColumnTitleString() {
        return columnTitleString;
    }

    public void setColumnTitleString(List<String> columnTitleString) {
        this.columnTitleString = columnTitleString;
    }

    public void setSubtotal(String subtotal) {
        this.subtotal = subtotal;
    }

    public void setDiscount(String discount) {
        this.discount = discount;
    }

    public void setServiceCharge(String serviceCharge) {
        this.serviceCharge = serviceCharge;
    }

    public String getTotal() {
        return total;
    }

    public void setTotal(String total) {
        this.total = total;
    }

    public String getBirCompanyName() {
        return birCompanyName;
    }

    public void setBirCompanyName(String birCompanyName) {
        this.birCompanyName = birCompanyName;
    }

    public String getCustomerInfo() {
        return customerInfo;
    }

    public void setCustomerInfo(String customerInfo) {
        this.customerInfo = customerInfo;
    }

    public String getReceiptNumber() {
        return receiptNumber;
    }

    public void setReceiptNumber(String receiptNumber) {
        this.receiptNumber = receiptNumber;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getMinNumber() {
        return minNumber;
    }

    public void setMinNumber(String minNumber) {
        this.minNumber = minNumber;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getReasonString() {
        return reasonString;
    }

    public void setReasonString(String reasonString) {
        this.reasonString = reasonString;
    }

    public String getCashierInfo() {
        return cashierInfo;
    }

    public void setCashierInfo(String cashierInfo) {
        this.cashierInfo = cashierInfo;
    }

    public String getRegisterNumber() {
        return registerNumber;
    }

    public void setRegisterNumber(String registerNumber) {
        this.registerNumber = registerNumber;
    }

    public String getNoteString() {
        return noteString;
    }

    public void setNoteString(String noteString) {
        this.noteString = noteString;
    }

    public String getMiddleOrderTableTitleWithContent() {
        return middleOrderTableTitleWithContent;
    }

    public void setMiddleOrderTableTitleWithContent(String middleOrderTableTitleWithContent) {
        this.middleOrderTableTitleWithContent = middleOrderTableTitleWithContent;
    }

    public String getReceiptTitle() {
        return receiptTitle;
    }

    public void setReceiptTitle(String receiptTitle) {
        this.receiptTitle = receiptTitle;
    }

    public String getSubtotalTitle() {
        return subtotalTitle;
    }

    public void setSubtotalTitle(String subtotalTitle) {
        this.subtotalTitle = subtotalTitle;
    }

    public String getDiscountTitle() {
        return discountTitle;
    }

    public void setDiscountTitle(String discountTitle) {
        this.discountTitle = discountTitle;
    }

    public String getLess12vatTitle() {
        return less12vatTitle;
    }

    public void setLess12vatTitle(String less12vatTitle) {
        this.less12vatTitle = less12vatTitle;
    }

    public String getLess12vat() {
        return less12vat;
    }

    public void setLess12vat(String less12vat) {
        this.less12vat = less12vat;
    }

    public String getAdhocDiscountTitle() {
        return adhocDiscountTitle;
    }

    public void setAdhocDiscountTitle(String adhocDiscountTitle) {
        this.adhocDiscountTitle = adhocDiscountTitle;
    }

    public String getAdhocDiscount() {
        return adhocDiscount;
    }

    public void setAdhocDiscount(String adhocDiscount) {
        this.adhocDiscount = adhocDiscount;
    }

    public String getSeniorDiscountTitle() {
        return seniorDiscountTitle;
    }

    public void setSeniorDiscountTitle(String seniorDiscountTitle) {
        this.seniorDiscountTitle = seniorDiscountTitle;
    }

    public String getSeniorDiscount() {
        return seniorDiscount;
    }

    public void setSeniorDiscount(String seniorDiscount) {
        this.seniorDiscount = seniorDiscount;
    }

    public String getPwdDiscountTitle() {
        return pwdDiscountTitle;
    }

    public void setPwdDiscountTitle(String pwdDiscountTitle) {
        this.pwdDiscountTitle = pwdDiscountTitle;
    }

    public String getPwdDiscount() {
        return pwdDiscount;
    }

    public void setPwdDiscount(String pwdDiscount) {
        this.pwdDiscount = pwdDiscount;
    }

    public String getVatOf12Title() {
        return vatOf12Title;
    }

    public void setVatOf12Title(String vatOf12Title) {
        this.vatOf12Title = vatOf12Title;
    }

    public String getVatOf12() {
        return vatOf12;
    }

    public void setVatOf12(String vatOf12) {
        this.vatOf12 = vatOf12;
    }

    public String getAmusementTaxTitle() {
        return amusementTaxTitle;
    }

    public void setAmusementTaxTitle(String amusementTaxTitle) {
        this.amusementTaxTitle = amusementTaxTitle;
    }

    public String getAmusementTax() {
        return amusementTax;
    }

    public void setAmusementTax(String amusementTax) {
        this.amusementTax = amusementTax;
    }

    public String getServiceChargeTitle() {
        return serviceChargeTitle;
    }

    public void setServiceChargeTitle(String serviceChargeTitle) {
        this.serviceChargeTitle = serviceChargeTitle;
    }

    public String getRoundingTitle() {
        return roundingTitle;
    }

    public void setRoundingTitle(String roundingTitle) {
        this.roundingTitle = roundingTitle;
    }

    public String getRounding() {
        return rounding;
    }

    public void setRounding(String rounding) {
        this.rounding = rounding;
    }

    public String getTotalTitle() {
        return totalTitle;
    }

    public void setTotalTitle(String totalTitle) {
        this.totalTitle = totalTitle;
    }

    public String getAmountOutstandingTitle() {
        return amountOutStandingTitle;
    }

    public void setAmountOutstandingTitle(String amountOutStandingTitle) {
        this.amountOutStandingTitle = amountOutStandingTitle;
    }

    public String getAmountOutstanding() {
        return amountOutStanding;
    }

    public void setAmountOutstanding(String amountOutStanding) {
        this.amountOutStanding = amountOutStanding;
    }

    public String getVatableSalesTitle() {
        return vatableSalesTitle;
    }

    public void setVatableSalesTitle(String vatableSalesTitle) {
        this.vatableSalesTitle = vatableSalesTitle;
    }

    public String getVatableSales() {
        return vatableSales;
    }

    public void setVatableSales(String vatableSales) {
        this.vatableSales = vatableSales;
    }

    public String getVatAmountTitle() {
        return vatAmountTitle;
    }

    public void setVatAmountTitle(String vatAmountTitle) {
        this.vatAmountTitle = vatAmountTitle;
    }

    public String getVatAmount() {
        return vatAmount;
    }

    public void setVatAmount(String vatAmount) {
        this.vatAmount = vatAmount;
    }

    public String getVatExemptSalesTitle() {
        return vatExemptSalesTitle;
    }

    public void setVatExemptSalesTitle(String vatExemptSalesTitle) {
        this.vatExemptSalesTitle = vatExemptSalesTitle;
    }

    public String getVatExemptSales() {
        return vatExemptSales;
    }

    public void setVatExemptSales(String vatExemptSales) {
        this.vatExemptSales = vatExemptSales;
    }

    public String getZeroRatedSalesTitle() {
        return zeroRatedSalesTitle;
    }

    public void setZeroRatedSalesTitle(String zeroRatedSalesTitle) {
        this.zeroRatedSalesTitle = zeroRatedSalesTitle;
    }

    public String getZeroRatedSales() {
        return zeroRatedSales;
    }

    public void setZeroRatedSales(String zeroRatedSales) {
        this.zeroRatedSales = zeroRatedSales;
    }

    public String getOnlineChannelNotesTitle() {
        return onlineChannelNotesTitle;
    }

    public void setOnlineChannelNotesTitle(String onlineChannelNotesTitle) {
        this.onlineChannelNotesTitle = onlineChannelNotesTitle;
    }

    public String getOnlineChannelNotesContent() {
        return onlineChannelNotesContent;
    }

    public void setOnlineChannelNotesContent(String onlineChannelNotesContent) {
        this.onlineChannelNotesContent = onlineChannelNotesContent;
    }

    public String getOnlinePickUpNoteTitle() {
        return onlinePickUpNoteTitle;
    }

    public void setOnlinePickUpNoteTitle(String onlinePickUpNoteTitle) {
        this.onlinePickUpNoteTitle = onlinePickUpNoteTitle;
    }

    public String getOnlineOrderNoteContent() {
        return onlineOrderNoteContent;
    }

    public void setOnlineOrderNoteContent(String onlineOrderNoteContent) {
        this.onlineOrderNoteContent = onlineOrderNoteContent;
    }

    public String getPreOrderNotes() {
        return preOrderNotes;
    }

    public void setPreOrderNotes(String preOrderNotes) {
        this.preOrderNotes = preOrderNotes;
    }

    public String getFooterLabelString() {
        return footerLabelString;
    }

    public void setFooterLabelString(String footerLabelString) {
        this.footerLabelString = footerLabelString;
    }

    public String getBirAccrInfo() {
        return birAccrInfo;
    }

    public void setBirAccrInfo(String birAccrInfo) {
        this.birAccrInfo = birAccrInfo;
    }

    public String getAccrNumber() {
        return accrNumber;
    }

    public void setAccrNumber(String accrNumber) {
        this.accrNumber = accrNumber;
    }

    public String getPtuNumber() {
        return ptuNumber;
    }

    public void setPtuNumber(String ptuNumber) {
        this.ptuNumber = ptuNumber;
    }

    public String getDateIssueNumber() {
        return dateIssueNumber;
    }

    public void setDateIssueNumber(String dateIssueNumber) {
        this.dateIssueNumber = dateIssueNumber;
    }

    public String getVatRegisterFooterInfo() {
        return vatRegisterFooterInfo;
    }

    public void setVatRegisterFooterInfo(String vatRegisterFooterInfo) {
        this.vatRegisterFooterInfo = vatRegisterFooterInfo;
    }

    public String getStorehubPoweredInfo() {
        return storehubPoweredInfo;
    }

    public void setStorehubPoweredInfo(String storehubPoweredInfo) {
        this.storehubPoweredInfo = storehubPoweredInfo;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    // public static long getSerialVersionUID() {
    // return serialVersionUID;
    // }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getReceiptDate() {
        return receiptDate;
    }

    public void setReceiptDate(String receiptDate) {
        this.receiptDate = receiptDate;
    }

    public String getVoidNo() {
        return voidNo;
    }

    public void setVoidNo(String voidNo) {
        this.voidNo = voidNo;
    }
    
    public String getReprintDate() {
        return reprintDate;
    }

    public void setReprintDate(String reprintDate) {
        this.reprintDate = reprintDate;
    }

    public List<PurchasedItem> getPurchasedItems() {
        return purchasedItems;
    }

    public void setPurchasedItems(List<PurchasedItem> purchasedItems) {
        this.purchasedItems = purchasedItems;
    }

    public static class BirInfoType {
        private String name;
        private String value;
        private Boolean needDedicatedSpace;

        public Boolean getNeedDedicatedSpace() {
            return needDedicatedSpace;
        }

        public void setNeedDedicatedSpace(Boolean needDedicatedSpace) {
            this.needDedicatedSpace = needDedicatedSpace;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }
    public String getCashbackExpirationDesc() {
        return cashbackExpirationDesc;
    }

    public void setCashbackExpirationDesc(String cashbackExpirationDesc) {
        this.cashbackExpirationDesc = cashbackExpirationDesc;
    }

    public String getMembershipSmallTitle() {
        return membershipSmallTitle;
    }

    public void setMembershipSmallTitle(String membershipSmallTitle) {
        this.membershipSmallTitle = membershipSmallTitle;
    }

    public String getMembershipBoildTitle() {
        return membershipBoildTitle;
    }

    public void setMembershipBoildTitle(String membershipBoildTitle) {
        this.membershipBoildTitle = membershipBoildTitle;
    }

    public String getMembershipLargeContentTitle1() {
        return membershipLargeContentTitle1;
    }

    public void setMembershipLargeContentTitle1(String membershipLargeContentTitle1) {
        this.membershipLargeContentTitle1 = membershipLargeContentTitle1;
    }

    public String getMembershipLargeContentTitle2() {
        return membershipLargeContentTitle2;
    }

    public void setMembershipLargeContentTitle2(String membershipLargeContentTitle2) {
        this.membershipLargeContentTitle2 = membershipLargeContentTitle2;
    }

    public String getMembershipSmallBottomTitle() {
        return membershipSmallBottomTitle;
    }

    public void setMembershipSmallBottomTitle(String membershipSmallBottomTitle) {
        this.membershipSmallBottomTitle = membershipSmallBottomTitle;
    }
}
