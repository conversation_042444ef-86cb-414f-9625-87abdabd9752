package com.storehub.rn.printer.model;

import java.util.ArrayList;
import java.util.List;

/**
 * Created By: sqq
 * Created Time: 2020/7/1 4:38 PM.
 */
public class BaseTransactionModel extends BaseModel {
    // image key
    private String key;

    protected String logoImage;
    protected String receiptId;
    protected boolean showBarcode;  //boolean
    protected String cashbackUrl;
    protected String eInvoiceUrl;
    protected String eInvoiceDescription;
    protected boolean enablePrintEInvoice;
    protected boolean enablePrintCashback;
    protected List<PurchasedItem> purchasedItems;

    // memberShip
    protected String business;
    protected boolean enablePrintMemberShip;
    protected boolean enablePrintQRCode;
    protected String membershipSource;
    protected String membershipUrl;
    protected String qrCodeDesc;
    protected String qrCodeAboveInfo;
    protected String qrCodeUnderInfo;

    protected double defaultLoyaltyRatioNumber;
    protected String defaultLoyaltyRatio;
    protected boolean isReceiptTitleBig;
    protected String receiptTitlePH;
    protected List<String> columnWidths;
    protected String cashBackTxt;
    private boolean isPrinterPaperWidth80;
    protected boolean usingDiscountLayout;  //boolean
    private List<String> a4ColumnTitleString;// String[]
    private String orderNo;
    private String orderNoName;
    private String shippingType;
    private String takeawayFeeValue;
    private String takeawayFeeName;
    private String shippingFeeValue;
    private String shippingFeeName;
    private boolean showTaxSummary;  //boolean

    public boolean isShowTaxSummary() {
        return showTaxSummary;
    }

    public void setShowTaxSummary(boolean showTaxSummary) {
        this.showTaxSummary = showTaxSummary;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public List<String> getA4ColumnTitleString() {
        return a4ColumnTitleString;
    }

    public void setA4ColumnTitleString(List<String> a4ColumnTitleString) {
        this.a4ColumnTitleString = a4ColumnTitleString;
    }
    public String getPickUpDate() {
        return "";
    }

    public String getPhone() {
        return "";
    }

    public String getCompanyName() {
        return "";
    }

    public String getAddress() {
        return "";
    }

    public String getReceiptTitle() {
        return "";
    }

    public String getTransactionTitle() {
        return "";
    }

    public String getCustomerInfo() {
        return "";
    }

    public String getReceiptDate() {
        return "";
    }

    public String getReceiptNumber() {
        return "";
    }

    public String getSstIdNo() {
        return "";
    }

    public String getCashierInfo() {
        return "";
    }

    public String getSubtotalTitle() {
        return "";
    }

    public String getSubtotal() {
        return "";
    }

    public String getDiscountTitle() {
        return "";
    }

    public String getDiscount() {
        return "";
    }

    public String getA4Discount() {
        return "";
    }

    public String getServiceChargeTitle() {
        return "";
    }

    public String getServiceCharge() {
        return "";
    }

    public String getTaxTitle() {
        return "";
    }

    public String getTax() {
        return "";
    }

    public String getTotalTitle() {
        return "";
    }

    public String getTotal() {
        return "";
    }

    public List<PaymentType> getPayment() {
        return new ArrayList<>();
    }

    public Boolean getShowPreorderSummary() {
        return false;
    }

    public String getUnPaidBalanceTitle() {
        return "";
    }

    public String getUnPaidBalance() {
        return "";
    }

    public String getDepositAmountTitle() {
        return "";
    }

    public String getDepositAmount() {
        return "";
    }

    public List<String> getTaxSummaryTitleString() {
        return new ArrayList<>();
    }
    /**
     * The getter method name should be getIsPrinterPaperWidth80, otherwise the template can't
     * retrieve the value by reflection.
     * Be cautious, The generated getter method isPrinterPaperWidth80 malfunctions.
     * @return
     */
    public boolean getIsPrinterPaperWidth80() {
        return isPrinterPaperWidth80;
    }

    public void setPrinterPaperWidth80(boolean printerPaperWidth80) {
        isPrinterPaperWidth80 = printerPaperWidth80;
    }

    public String getCashBackTxt() {
        return cashBackTxt;
    }

    public void setCashBackTxt(String cashBackTxt) {
        this.cashBackTxt = cashBackTxt;
    }

    public List<String> getColumnWidths() {
        return columnWidths;
    }

    public void setColumnWidths(List<String> columnWidths) {
        this.columnWidths = columnWidths;
    }

    public String getReceiptTitlePH() {
        return receiptTitlePH;
    }

    public void setReceiptTitlePH(String receiptTitlePH) {
        this.receiptTitlePH = receiptTitlePH;
    }

    public boolean getIsReceiptTitleBig() {
        return isReceiptTitleBig;
    }

    public void setReceiptTitleBig(boolean receiptTitleBig) {
        isReceiptTitleBig = receiptTitleBig;
    }

    public double getDefaultLoyaltyRatioNumber() {
        return defaultLoyaltyRatioNumber;
    }

    public void setDefaultLoyaltyRatioNumber(double defaultLoyaltyRatioNumber) {
        this.defaultLoyaltyRatioNumber = defaultLoyaltyRatioNumber;
    }

    public String getDefaultLoyaltyRatio() {
        return defaultLoyaltyRatio;
    }

    public void setDefaultLoyaltyRatio(String defaultLoyaltyRatio) {
        this.defaultLoyaltyRatio = defaultLoyaltyRatio;
    }

    public String getQrCodeAboveInfo() {
        return qrCodeAboveInfo;
    }

    public void setQrCodeAboveInfo(String qrCodeAboveInfo) {
        this.qrCodeAboveInfo = qrCodeAboveInfo;
    }

    public String getQrCodeUnderInfo() {
        return qrCodeUnderInfo;
    }

    public void setQrCodeUnderInfo(String qrCodeUnderInfo) {
        this.qrCodeUnderInfo = qrCodeUnderInfo;
    }

    public List<PurchasedItem> getPurchasedItems() {
        return purchasedItems;
    }

    public void setPurchasedItems(List<PurchasedItem> purchasedItems) {
        this.purchasedItems = purchasedItems;
    }

    public String getLogoImage() {
        return logoImage;
    }

    public void setLogoImage(String logoImage) {
        this.logoImage = logoImage;
    }

    public String getReceiptId() {
        return receiptId;
    }

    public void setReceiptId(String receiptId) {
        this.receiptId = receiptId;
    }

    public boolean isShowBarcode() {
        return showBarcode;
    }

    public void setShowBarcode(boolean showBarcode) {
        this.showBarcode = showBarcode;
    }

    public String getCashbackUrl() {
        return cashbackUrl;
    }

    public void setCashbackUrl(String cashbackUrl) {
        this.cashbackUrl = cashbackUrl;
    }

    public String getEInvoiceUrl() {
        return eInvoiceUrl;
    }

    public void setEInvoiceUrl(String eInvoiceUrl) {
        this.eInvoiceUrl = eInvoiceUrl;
    }

    public String getEInvoiceDescription() {
        return eInvoiceDescription;
    }

    public void setEInvoiceDescription(String eInvoiceDescription) {
        this.eInvoiceDescription = eInvoiceDescription;
    }

    public boolean getEnablePrintEInvoice() {
        return enablePrintEInvoice;
    }

    public void setEnablePrintEInvoice(boolean enablePrintEInvoice) {
        this.enablePrintEInvoice = enablePrintEInvoice;
    }

    public boolean isEnablePrintCashback() {
        return enablePrintCashback;
    }

    public void setEnablePrintCashback(boolean enablePrintCashback) {
        this.enablePrintCashback = enablePrintCashback;
    }

    public String getBusiness() {
        return business;
    }

    public void setBusiness(String business) {
        this.business = business;
    }


    public boolean isEnablePrintQRCode() {
        return enablePrintQRCode;
    }

    public void setEnablePrintQRCode(boolean enablePrintQRCode) {
        this.enablePrintQRCode = enablePrintQRCode;
    }

    public boolean isEnablePrintMemberShip() {
        return enablePrintMemberShip;
    }

    public void setEnablePrintMemberShip(boolean enablePrintMemberShip) {
        this.enablePrintMemberShip = enablePrintMemberShip;
    }

    public String getMembershipSource() {
        return membershipSource;
    }

    public void setMembershipSource(String membershipSource) {
        this.membershipSource = membershipSource;
    }

    public String getMembershipUrl() {
        return membershipUrl;
    }

    public void setMembershipUrl(String membershipUrl) {
        this.membershipUrl = membershipUrl;
    }

    public String getQrCodeDesc() {
        return qrCodeDesc;
    }

    public void setQrCodeDesc(String qrCodeDesc) {
        this.qrCodeDesc = qrCodeDesc;
    }

    public boolean isUsingDiscountLayout() {
        return usingDiscountLayout;
    }

    public void setUsingDiscountLayout(boolean usingDiscountLayout) {
        this.usingDiscountLayout = usingDiscountLayout;
    }

    public boolean isEnableCashback() {
        return false;
    }

    public void setEnableCashback(boolean enableCashback) {
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOrderNoName() {
        return orderNoName;
    }

    public void setOrderNoName(String orderNoName) {
        this.orderNoName = orderNoName;
    }

    public String getMembershipSmallTitle() {
        return "";
    }

    public String getMembershipBoildTitle() {
        return "";
    }

    public String getMembershipLargeContentTitle1() {
        return "";
    }

    public String getMembershipLargeContentTitle2() {
        return "";
    }

    public String getMembershipSmallBottomTitle() {
        return "";
    }

    public String getShippingType() {
        return shippingType;
    }

    public void setShippingType(String shippingType) {
        this.shippingType = shippingType;
    }

    public String getTakeawayFeeValue() {
        return takeawayFeeValue;
    }

    public void setTakeawayFeeValue(String takeawayFeeValue) {
        this.takeawayFeeValue = takeawayFeeValue;
    }

    public String getTakeawayFeeName() {
        return takeawayFeeName;
    }

    public void setTakeawayFeeName(String takeawayFeeName) {
        this.takeawayFeeName = takeawayFeeName;
    }

    public String getShippingFeeValue() {
        return shippingFeeValue;
    }

    public void setShippingFeeValue(String shippingFeeValue) {
        this.shippingFeeValue = shippingFeeValue;
    }

    public String getShippingFeeName() {
        return shippingFeeName;
    }

    public void setShippingFeeName(String shippingFeeName) {
        this.shippingFeeName = shippingFeeName;
    }

    public static class Promotions {
        private String promotionName;
        private String discount;

        public String getPromotionName() {
            return promotionName;
        }

        public void setPromotionName(String promotionName) {
            this.promotionName = promotionName;
        }

        public String getDiscount() {
            return discount;
        }

        public void setDiscount(String discount) {
            this.discount = discount;
        }
    }

    public static class PaymentType {
        private String paymentMethodId;
        private String paymentMethodName;
        private String cashTendered;
        private String amount;
        private String roundedAmount;
        private String subType;
        private String changeTitle;
        private String changeValue;

        public String getPaymentMethodId() {
            return paymentMethodId;
        }

        public void setPaymentMethodId(String paymentMethodId) {
            this.paymentMethodId = paymentMethodId;
        }

        public String getPaymentMethodName() {
            return paymentMethodName;
        }

        public void setPaymentMethodName(String paymentMethodName) {
            this.paymentMethodName = paymentMethodName;
        }

        public String getCashTendered() {
            return cashTendered;
        }

        public void setCashTendered(String cashTendered) {
            this.cashTendered = cashTendered;
        }

        public String getAmount() {
            return amount;
        }

        public void setAmount(String amount) {
            this.amount = amount;
        }

        public String getRoundedAmount() {
            return roundedAmount;
        }

        public void setRoundedAmount(String roundedAmount) {
            this.roundedAmount = roundedAmount;
        }

        public String getSubType() {
            return subType;
        }

        public void setSubType(String subType) {
            this.subType = subType;
        }

        public String getChangeTitle() {
            return changeTitle;
        }

        public void setChangeTitle(String changeTitle) {
            this.changeTitle = changeTitle;
        }

        public String getChangeValue() {
            return changeValue;
        }

        public void setChangeValue(String changeValue) {
            this.changeValue = changeValue;
        }
    }

    public static class PurchasedItem {
        private String itemType;
        private String discount;
        private String quantity;
        private String subTotal;
        private String itemName;
        private String total;
        private String options;
        private String notes;
        private String sn;
        private String price;
        private List<Promotions> promotions;
        private String itemDiscountName;
        private String itemDiscountValue;
        private boolean enableTakeaway;
        private String takeawayTxt;
        private String a4Total;
        private String takeawayCharge;

        public String getA4Total() {
            return a4Total;
        }

        public void setA4Total(String a4Total) {
            this.a4Total = a4Total;
        }

        public String getTakeawayTxt() {
            return takeawayTxt;
        }

        public void setTakeawayTxt(String takeawayTxt) {
            this.takeawayTxt = takeawayTxt;
        }

        public String getTakeawayCharge() {
            return takeawayCharge;
        }

        public void setTakeawayCharge(String takeawayCharge) {
            this.takeawayCharge = takeawayCharge;
        }

        public boolean isEnableTakeaway() {
            return enableTakeaway;
        }

        public void setEnableTakeaway(boolean enableTakeaway) {
            this.enableTakeaway = enableTakeaway;
        }
        public String getItemDiscountName() {
            return itemDiscountName;
        }

        public void setItemDiscountName(String itemDiscountName) {
            this.itemDiscountName = itemDiscountName;
        }

        public String getItemDiscountValue() {
            return itemDiscountValue;
        }

        public void setItemDiscountValue(String itemDiscountValue) {
            this.itemDiscountValue = itemDiscountValue;
        }

        public List<Promotions> getPromotions() {
            return promotions;
        }

        public void setPromotions(List<Promotions> promotions) {
            this.promotions = promotions;
        }

        public String getItemName() {
            return itemName;
        }

        public void setItemName(String itemName) {
            this.itemName = itemName;
        }

        public String getPrice() {
            return price;
        }

        public void setPrice(String price) {
            this.price = price;
        }

        public String getSn() {
            return sn;
        }

        public void setSn(String sn) {
            this.sn = sn;
        }

        public String getItemType() {
            return itemType;
        }

        public void setItemType(String itemType) {
            this.itemType = itemType;
        }

        public String getOptions() {
            return options;
        }

        public void setOptions(String options) {
            this.options = options;
        }

        public String getNotes() {
            return notes;
        }

        public void setNotes(String notes) {
            this.notes = notes;
        }

        public String getDiscount() {
            return discount;
        }

        public void setDiscount(String discount) {
            this.discount = discount;
        }

        public String getQuantity() {
            return quantity;
        }

        public void setQuantity(String quantity) {
            this.quantity = quantity;
        }

        public String getSubTotal() {
            return subTotal;
        }

        public void setSubTotal(String subtotal) {
            this.subTotal = subtotal;
        }

        public String getTotal() {
            return total;
        }

        public void setTotal(String total) {
            this.total = total;
        }
    }

}
