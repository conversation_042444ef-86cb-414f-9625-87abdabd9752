package com.storehub.rn.printer.model;

import java.util.List;


public class SMXReadingReportHBSModel extends BaseModel {
    // ---------------------第一段---------------------------
    private String businessName;
    private String storeName;
    private String storeAddress;
    private String vatRegTin;
    private String minNo;
    private String serialNo;
    private String reportTitle;

//    private List<CommonLineItem> noLines;
    private List<CommonLineItem> summaryLines;
    private List<CommonLineItem> serviceTypeSaleLines;
    // ---------------------第二段---------------------------
    private List<CommonLineItem> hourlySalesLines;
    private List<CommonLineItem> breakDownOfTenderLines;
    private List<CommonLineItem> incomeHeadSalesLines;
    private List<CommonLineItem> cashDrawerLines;
    private List<CommonLineItem> capitalizedLines;
    // ---------------------第N段---------------------------
    private List<List<MixSummaryLineItem>> mixSummaryLinesGroups;

    // ---------------------最后一段---------------------------
    private List<CommonLineItem> totalLines;
    private List<CommonLineItem> dateLines;
    private List<CommonLineItem> footerLines;


    public static class CommonLineItem {
        private String name;
        private String count;
        private String value;
        private Boolean inRight = false;
        private Boolean hasMargin = false;
        private Boolean isTitle = false;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getCount() {
            return count;
        }

        public void setCount(String count) {
            this.count = count;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public Boolean getInRight() {
            return inRight;
        }

        public void setInRight(Boolean inRight) {
            this.inRight = inRight;
        }

        public Boolean getHasMargin() {
            return hasMargin;
        }

        public void setHasMargin(Boolean hasMargin) {
            this.hasMargin = hasMargin;
        }

        public Boolean getIsTitle() {
            return isTitle;
        }

        public void setTitle(Boolean title) {
            isTitle = title;
        }

        @Override
        public String toString() {
            return "CommonLineItem{" +
                    "name='" + name + '\'' +
                    ", count='" + count + '\'' +
                    ", value='" + value + '\'' +
                    ", inRight=" + inRight +
                    ", hasMargin=" + hasMargin +
                    ", isTitle=" + isTitle +
                    '}';
        }
    }

    public static class MixSummaryLineItem {
        private List<String> names;
        private Boolean isDivider = false;
        private Boolean isTitle = false;
        private Boolean isSingleLine = false;

        public Boolean getIsSingleLine() {
            return isSingleLine;
        }

        public void setSingleLine(Boolean singleLine) {
            isSingleLine = singleLine;
        }

        public List<String> getNames() {
            return names;
        }

        public void setNames(List<String> names) {
            this.names = names;
        }

        public Boolean getIsDivider() {
            return isDivider;
        }

        public void setDivider(Boolean divider) {
            isDivider = divider;
        }

        public Boolean getIsTitle() {
            return isTitle;
        }

        public void setTitle(Boolean title) {
            isTitle = title;
        }

        @Override
        public String toString() {
            return "MixSummaryLineItem{" +
                    "names=" + names +
                    ", isDivider=" + isDivider +
                    ", isTitle=" + isTitle +
                    '}';
        }
    }

    public String getBusinessName() {
        return businessName;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getStoreAddress() {
        return storeAddress;
    }

    public void setStoreAddress(String storeAddress) {
        this.storeAddress = storeAddress;
    }

    public String getVatRegTin() {
        return vatRegTin;
    }

    public void setVatRegTin(String vatRegTin) {
        this.vatRegTin = vatRegTin;
    }

    public String getMinNo() {
        return minNo;
    }

    public void setMinNo(String minNo) {
        this.minNo = minNo;
    }

    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    public List<CommonLineItem> getHourlySalesLines() {
        return hourlySalesLines;
    }

    public void setHourlySalesLines(List<CommonLineItem> hourlySalesLines) {
        this.hourlySalesLines = hourlySalesLines;
    }

    public String getReportTitle() {
        return reportTitle;
    }

    public void setReportTitle(String reportTitle) {
        this.reportTitle = reportTitle;
    }

//    public List<CommonLineItem> getNoLines() {
//        return noLines;
//    }
//
//    public void setNoLines(List<CommonLineItem> noLines) {
//        this.noLines = noLines;
//    }

    public List<CommonLineItem> getSummaryLines() {
        return summaryLines;
    }

    public void setSummaryLines(List<CommonLineItem> summaryLines) {
        this.summaryLines = summaryLines;
    }

    public List<CommonLineItem> getServiceTypeSaleLines() {
        return serviceTypeSaleLines;
    }

    public void setServiceTypeSaleLines(List<CommonLineItem> serviceTypeSaleLines) {
        this.serviceTypeSaleLines = serviceTypeSaleLines;
    }

    public List<CommonLineItem> getBreakDownOfTenderLines() {
        return breakDownOfTenderLines;
    }

    public void setBreakDownOfTenderLines(List<CommonLineItem> breakDownOfTenderLines) {
        this.breakDownOfTenderLines = breakDownOfTenderLines;
    }

    public List<CommonLineItem> getIncomeHeadSalesLines() {
        return incomeHeadSalesLines;
    }

    public void setIncomeHeadSalesLines(List<CommonLineItem> incomeHeadSalesLines) {
        this.incomeHeadSalesLines = incomeHeadSalesLines;
    }

    public List<CommonLineItem> getCashDrawerLines() {
        return cashDrawerLines;
    }

    public void setCashDrawerLines(List<CommonLineItem> cashDrawerLines) {
        this.cashDrawerLines = cashDrawerLines;
    }

    public List<CommonLineItem> getCapitalizedLines() {
        return capitalizedLines;
    }

    public void setCapitalizedLines(List<CommonLineItem> capitalizedLines) {
        this.capitalizedLines = capitalizedLines;
    }

    public List<List<MixSummaryLineItem>> getMixSummaryLinesGroups() {
        return mixSummaryLinesGroups;
    }

    public void setMixSummaryLinesGroups(List<List<MixSummaryLineItem>> mixSummaryLinesGroups) {
        this.mixSummaryLinesGroups = mixSummaryLinesGroups;
    }

    public List<CommonLineItem> getTotalLines() {
        return totalLines;
    }

    public void setTotalLines(List<CommonLineItem> totalLines) {
        this.totalLines = totalLines;
    }

    public List<CommonLineItem> getDateLines() {
        return dateLines;
    }

    public void setDateLines(List<CommonLineItem> dateLines) {
        this.dateLines = dateLines;
    }

    public List<CommonLineItem> getFooterLines() {
        return footerLines;
    }

    public void setFooterLines(List<CommonLineItem> footerLines) {
        this.footerLines = footerLines;
    }

    @Override
    public String toString() {
        return "SMEODReportHBSModel{" +
                "businessName='" + businessName + '\'' +
                ", storeName='" + storeName + '\'' +
                ", storeAddress='" + storeAddress + '\'' +
                ", vatRegTinName='" + vatRegTin + '\'' +
                ", minNoName='" + minNo + '\'' +
                ", serialNoName='" + serialNo + '\'' +
                ", reportTitle='" + reportTitle + '\'' +
//                ", noLines=" + noLines +
                ", summaryLines=" + summaryLines +
                ", serviceTypeSaleLines=" + serviceTypeSaleLines +
                ", breakDownOfTenderLines=" + breakDownOfTenderLines +
                ", incomeHeadSalesLines=" + incomeHeadSalesLines +
                ", cashDrawerLines=" + cashDrawerLines +
                ", capitalizedLines=" + capitalizedLines +
                ", mixSummaryLinesGroups=" + mixSummaryLinesGroups +
                ", totalLines=" + totalLines +
                ", dateLines=" + dateLines +
                ", footerLines=" + footerLines +
                '}';
    }
}
