package com.storehub.rn.printer.model;

import java.io.Serializable;
import java.util.List;

public class ShiftReportHBSModel extends BaseModel implements Serializable {

    private static final long serialVersionUID = -2470029030961778033L;

    private String printedDate;
    private String gstIdNo;
    private String birAccrNo;
    private String serialNo;
    private String minNo;
    private String ptu;
    private String country;
    private boolean birAccredited;
    private String companyName;
    private String storeName;
    private String address;
    private String phone;
    private String reportTitle;
    private String currency;
    private String shiftOpenTime;
    private String shiftCloseTime;
    private String registerId;
    private String reportDate;
    private String manager;
    private String openBy;
    private String closeBy;
    private String printedBy;
    private String cashier;
    private PaymentSummaryTitleStrings paymentSummaryTitleStrings;
    private List<Payment> payments;
    private List<String> shiftReportItemsHeaderStrings;
    private SalesSummaryTitle salesSummaryTitles;
    private SalesSummary salesSummary;
    private List<String> shiftReportDepositssHeaderStrings;
    private List<DepositsItem> depositsSummary;
    private String totalDepositTitle;
    private String totalDepositQuantity;
    private String totalDepositAmount;
    private List<String> storeCreditSummaryHeaderStrings;
    private StoreCreditSummaryTitles storeCreditSummaryTitles;
    private StoreCreditSummary storeCreditSummary;
    private List<String> cancelAndDiscountSummaryHeaderStrings;
    private CancelAndDiscountSummaryTitles cancelAndDiscountSummaryTitles;
    private CancelAndDiscountSummary cancelAndDiscountSummary;
    private List<String> taxSummaryHeaderStrings;
    private List<TaxSummary> taxSummaryArray;
    private List<String> serviceChargeSummaryHeaderStrings;
    private ServiceChargeSummaryTitles serviceChargeSummaryTitles;
    private ServiceChargeSummary serviceChargeSummary;
    private List<String> cashDrawerSummaryHeaderStrings;
    private CashDrawerSummaryTitles cashDrawerSummaryTitles;
    private CashDrawerSummary cashDrawerSummary;
    private String expectedDrawerTitle;
    private String expectedDrawer;
    private String actualDrawerTitle;
    private String actualDrawer;
    private String overShortTitle;
    private String overShort;

    public String getOpenBy() {
        return openBy;
    }

    public void setOpenBy(String openBy) {
        this.openBy = openBy;
    }

    public String getCloseBy() {
        return closeBy;
    }

    public void setCloseBy(String closeBy) {
        this.closeBy = closeBy;
    }

    public String getPrintedBy() {
        return printedBy;
    }

    public void setPrintedBy(String printedBy) {
        this.printedBy = printedBy;
    }

    public String getCashier() {
        return cashier;
    }

    public void setCashier(String cashier) {
        this.cashier = cashier;
    }

    public String getTotalDepositTitle() {
        return totalDepositTitle;
    }

    public void setTotalDepositTitle(String totalDepositTitle) {
        this.totalDepositTitle = totalDepositTitle;
    }

    public String getTotalDepositQuantity() {
        return totalDepositQuantity;
    }

    public void setTotalDepositQuantity(String totalDepositQuantity) {
        this.totalDepositQuantity = totalDepositQuantity;
    }

    public String getTotalDepositAmount() {
        return totalDepositAmount;
    }

    public void setTotalDepositAmount(String totalDepositAmount) {
        this.totalDepositAmount = totalDepositAmount;
    }

    public boolean isBirAccredited() {
        return birAccredited;
    }

    public List<TaxSummary> getTaxSummaryArray() {
        return taxSummaryArray;
    }

    public void setTaxSummaryArray(List<TaxSummary> taxSummaryArray) {
        this.taxSummaryArray = taxSummaryArray;
    }

    public String getPrintedDate() {
        return printedDate;
    }

    public void setPrintedDate(String printedDate) {
        this.printedDate = printedDate;
    }

    public String getGstIdNo() {
        return gstIdNo;
    }

    public void setGstIdNo(String gstIdNo) {
        this.gstIdNo = gstIdNo;
    }

    public String getBirAccrNo() {
        return birAccrNo;
    }

    public List<String> getShiftReportDepositssHeaderStrings() {
        return shiftReportDepositssHeaderStrings;
    }

    public void setShiftReportDepositssHeaderStrings(List<String> shiftReportDepositssHeaderStrings) {
        this.shiftReportDepositssHeaderStrings = shiftReportDepositssHeaderStrings;
    }

    public List<DepositsItem> getDepositsSummary() {
        return depositsSummary;
    }

    public void setDepositsSummary(List<DepositsItem> depositsSummary) {
        this.depositsSummary = depositsSummary;
    }

    public void setBirAccrNo(String birAccrNo) {
        this.birAccrNo = birAccrNo;
    }

    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    public String getMinNo() {
        return minNo;
    }

    public void setMinNo(String minNo) {
        this.minNo = minNo;
    }

    public String getPtu() {
        return ptu;
    }

    public void setPtu(String ptu) {
        this.ptu = ptu;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public boolean getBirAccredited() {
        return birAccredited;
    }

    public void setBirAccredited(boolean birAccredited) {
        this.birAccredited = birAccredited;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getReportTitle() {
        return reportTitle;
    }

    public void setReportTitle(String reportTitle) {
        this.reportTitle = reportTitle;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getShiftOpenTime() {
        return shiftOpenTime;
    }

    public void setShiftOpenTime(String shiftOpenTime) {
        this.shiftOpenTime = shiftOpenTime;
    }

    public String getShiftCloseTime() {
        return shiftCloseTime;
    }

    public void setShiftCloseTime(String shiftCloseTime) {
        this.shiftCloseTime = shiftCloseTime;
    }

    public String getRegisterId() {
        return registerId;
    }

    public void setRegisterId(String registerId) {
        this.registerId = registerId;
    }

    public String getReportDate() {
        return reportDate;
    }

    public void setReportDate(String reportDate) {
        this.reportDate = reportDate;
    }

    public String getManager() {
        return manager;
    }

    public void setManager(String manager) {
        this.manager = manager;
    }

    public PaymentSummaryTitleStrings getPaymentSummaryTitleStrings() {
        return paymentSummaryTitleStrings;
    }

    public void setPaymentSummaryTitleStrings(PaymentSummaryTitleStrings paymentSummaryTitleStrings) {
        this.paymentSummaryTitleStrings = paymentSummaryTitleStrings;
    }

    public List<Payment> getPayments() {
        return payments;
    }

    public void setPayments(List<Payment> payments) {
        this.payments = payments;
    }

    public List<String> getShiftReportItemsHeaderStrings() {
        return shiftReportItemsHeaderStrings;
    }

    public void setShiftReportItemsHeaderStrings(List<String> shiftReportItemsHeaderStrings) {
        this.shiftReportItemsHeaderStrings = shiftReportItemsHeaderStrings;
    }

    public SalesSummaryTitle getSalesSummaryTitles() {
        return salesSummaryTitles;
    }

    public void setSalesSummaryTitles(SalesSummaryTitle salesSummaryTitles) {
        this.salesSummaryTitles = salesSummaryTitles;
    }

    public SalesSummary getSalesSummary() {
        return salesSummary;
    }

    public void setSalesSummary(SalesSummary salesSummary) {
        this.salesSummary = salesSummary;
    }

    public List<String> getStoreCreditSummaryHeaderStrings() {
        return storeCreditSummaryHeaderStrings;
    }

    public void setStoreCreditSummaryHeaderStrings(List<String> storeCreditSummaryHeaderStrings) {
        this.storeCreditSummaryHeaderStrings = storeCreditSummaryHeaderStrings;
    }

    public StoreCreditSummaryTitles getStoreCreditSummaryTitles() {
        return storeCreditSummaryTitles;
    }

    public void setStoreCreditSummaryTitles(StoreCreditSummaryTitles storeCreditSummaryTitles) {
        this.storeCreditSummaryTitles = storeCreditSummaryTitles;
    }

    public StoreCreditSummary getStoreCreditSummary() {
        return storeCreditSummary;
    }

    public void setStoreCreditSummary(StoreCreditSummary storeCreditSummary) {
        this.storeCreditSummary = storeCreditSummary;
    }

    public List<String> getCancelAndDiscountSummaryHeaderStrings() {
        return cancelAndDiscountSummaryHeaderStrings;
    }

    public void setCancelAndDiscountSummaryHeaderStrings(List<String> cancelAndDiscountSummaryHeaderStrings) {
        this.cancelAndDiscountSummaryHeaderStrings = cancelAndDiscountSummaryHeaderStrings;
    }

    public CancelAndDiscountSummaryTitles getCancelAndDiscountSummaryTitles() {
        return cancelAndDiscountSummaryTitles;
    }

    public void setCancelAndDiscountSummaryTitles(CancelAndDiscountSummaryTitles cancelAndDiscountSummaryTitles) {
        this.cancelAndDiscountSummaryTitles = cancelAndDiscountSummaryTitles;
    }

    public CancelAndDiscountSummary getCancelAndDiscountSummary() {
        return cancelAndDiscountSummary;
    }

    public void setCancelAndDiscountSummary(CancelAndDiscountSummary cancelAndDiscountSummary) {
        this.cancelAndDiscountSummary = cancelAndDiscountSummary;
    }

    public List<String> getTaxSummaryHeaderStrings() {
        return taxSummaryHeaderStrings;
    }

    public void setTaxSummaryHeaderStrings(List<String> taxSummaryHeaderStrings) {
        this.taxSummaryHeaderStrings = taxSummaryHeaderStrings;
    }

    public List<String> getServiceChargeSummaryHeaderStrings() {
        return serviceChargeSummaryHeaderStrings;
    }

    public void setServiceChargeSummaryHeaderStrings(List<String> serviceChargeSummaryHeaderStrings) {
        this.serviceChargeSummaryHeaderStrings = serviceChargeSummaryHeaderStrings;
    }

    public ServiceChargeSummaryTitles getServiceChargeSummaryTitles() {
        return serviceChargeSummaryTitles;
    }

    public void setServiceChargeSummaryTitles(ServiceChargeSummaryTitles serviceChargeSummaryTitles) {
        this.serviceChargeSummaryTitles = serviceChargeSummaryTitles;
    }

    public ServiceChargeSummary getServiceChargeSummary() {
        return serviceChargeSummary;
    }

    public void setServiceChargeSummary(ServiceChargeSummary serviceChargeSummary) {
        this.serviceChargeSummary = serviceChargeSummary;
    }


    public List<String> getCashDrawerSummaryHeaderStrings() {
        return cashDrawerSummaryHeaderStrings;
    }

    public void setCashDrawerSummaryHeaderStrings(List<String> cashDrawerSummaryHeaderStrings) {
        this.cashDrawerSummaryHeaderStrings = cashDrawerSummaryHeaderStrings;
    }

    public CashDrawerSummaryTitles getCashDrawerSummaryTitles() {
        return cashDrawerSummaryTitles;
    }

    public void setCashDrawerSummaryTitles(CashDrawerSummaryTitles cashDrawerSummaryTitles) {
        this.cashDrawerSummaryTitles = cashDrawerSummaryTitles;
    }

    public CashDrawerSummary getCashDrawerSummary() {
        return cashDrawerSummary;
    }

    public void setCashDrawerSummary(CashDrawerSummary cashDrawerSummary) {
        this.cashDrawerSummary = cashDrawerSummary;
    }

    public String getExpectedDrawerTitle() {
        return expectedDrawerTitle;
    }

    public void setExpectedDrawerTitle(String expectedDrawerTitle) {
        this.expectedDrawerTitle = expectedDrawerTitle;
    }

    public String getExpectedDrawer() {
        return expectedDrawer;
    }

    public void setExpectedDrawer(String expectedDrawer) {
        this.expectedDrawer = expectedDrawer;
    }

    public String getActualDrawerTitle() {
        return actualDrawerTitle;
    }

    public void setActualDrawerTitle(String actualDrawerTitle) {
        this.actualDrawerTitle = actualDrawerTitle;
    }

    public String getActualDrawer() {
        return actualDrawer;
    }

    public void setActualDrawer(String actualDrawer) {
        this.actualDrawer = actualDrawer;
    }

    public String getOverShortTitle() {
        return overShortTitle;
    }

    public void setOverShortTitle(String overShortTitle) {
        this.overShortTitle = overShortTitle;
    }

    public String getOverShort() {
        return overShort;
    }

    public void setOverShort(String overShort) {
        this.overShort = overShort;
    }

    public static class ServiceChargeSummary {
        private String salesQuantity;
        private String salesAmount;
        private String refundsQuantity;
        private String refundsAmount;

        public String getSalesQuantity() {
            return salesQuantity;
        }

        public void setSalesQuantity(String salesQuantity) {
            this.salesQuantity = salesQuantity;
        }

        public String getSalesAmount() {
            return salesAmount;
        }

        public void setSalesAmount(String salesAmount) {
            this.salesAmount = salesAmount;
        }

        public String getRefundsQuantity() {
            return refundsQuantity;
        }

        public void setRefundsQuantity(String refundsQuantity) {
            this.refundsQuantity = refundsQuantity;
        }

        public String getRefundsAmount() {
            return refundsAmount;
        }

        public void setRefundsAmount(String refundsAmount) {
            this.refundsAmount = refundsAmount;
        }
    }

    public static class CashDrawerSummary {
        private String openingAmount;
        private String cashSalesQuantity;
        private String cashSalesAmount;
        private String cashDepositsQuantity;
        private String cashDepositsAmount;
        private String cashRefundsQuantity;
        private String cashRefundsAmount;
        private String payOutQuantity;
        private String payOutAmount;
        private String payInQuantity;
        private String payInAmount;

        public String getOpeningAmount() {
            return openingAmount;
        }

        public void setOpeningAmount(String openingAmount) {
            this.openingAmount = openingAmount;
        }

        public String getCashSalesQuantity() {
            return cashSalesQuantity;
        }

        public void setCashSalesQuantity(String cashSalesQuantity) {
            this.cashSalesQuantity = cashSalesQuantity;
        }

        public String getCashSalesAmount() {
            return cashSalesAmount;
        }

        public void setCashSalesAmount(String cashSalesAmount) {
            this.cashSalesAmount = cashSalesAmount;
        }

        public String getCashDepositsQuantity() {
            return cashDepositsQuantity;
        }

        public void setCashDepositsQuantity(String cashDepositsQuantity) {
            this.cashDepositsQuantity = cashDepositsQuantity;
        }

        public String getCashDepositsAmount() {
            return cashDepositsAmount;
        }

        public void setCashDepositsAmount(String cashDepositsAmount) {
            this.cashDepositsAmount = cashDepositsAmount;
        }

        public String getCashRefundsQuantity() {
            return cashRefundsQuantity;
        }

        public void setCashRefundsQuantity(String cashRefundsQuantity) {
            this.cashRefundsQuantity = cashRefundsQuantity;
        }

        public String getCashRefundsAmount() {
            return cashRefundsAmount;
        }

        public void setCashRefundsAmount(String cashRefundsAmount) {
            this.cashRefundsAmount = cashRefundsAmount;
        }

        public String getPayOutQuantity() {
            return payOutQuantity;
        }

        public void setPayOutQuantity(String payOutQuantity) {
            this.payOutQuantity = payOutQuantity;
        }

        public String getPayOutAmount() {
            return payOutAmount;
        }

        public void setPayOutAmount(String payOutAmount) {
            this.payOutAmount = payOutAmount;
        }

        public String getPayInQuantity() {
            return payInQuantity;
        }

        public void setPayInQuantity(String payInQuantity) {
            this.payInQuantity = payInQuantity;
        }

        public String getPayInAmount() {
            return payInAmount;
        }

        public void setPayInAmount(String payInAmount) {
            this.payInAmount = payInAmount;
        }
    }

    public static class CashDrawerSummaryTitles {
        private String openingAmountTitle;
        private String cashSalesTitle;
        private String cashDepositsTitle;
        private String cashRefundsTitle;
        private String payOutTitle;
        private String payInTitle;

        public String getOpeningAmountTitle() {
            return openingAmountTitle;
        }

        public void setOpeningAmountTitle(String openingAmountTitle) {
            this.openingAmountTitle = openingAmountTitle;
        }

        public String getCashSalesTitle() {
            return cashSalesTitle;
        }

        public void setCashSalesTitle(String cashSalesTitle) {
            this.cashSalesTitle = cashSalesTitle;
        }

        public String getCashDepositsTitle() {
            return cashDepositsTitle;
        }

        public void setCashDepositsTitle(String cashDepositsTitle) {
            this.cashDepositsTitle = cashDepositsTitle;
        }

        public String getCashRefundsTitle() {
            return cashRefundsTitle;
        }

        public void setCashRefundsTitle(String cashRefundsTitle) {
            this.cashRefundsTitle = cashRefundsTitle;
        }

        public String getPayOutTitle() {
            return payOutTitle;
        }

        public void setPayOutTitle(String payOutTitle) {
            this.payOutTitle = payOutTitle;
        }

        public String getPayInTitle() {
            return payInTitle;
        }

        public void setPayInTitle(String payInTitle) {
            this.payInTitle = payInTitle;
        }
    }

    public static class ServiceChargeSummaryTitles {
        private String salesTitle;

        public String getSalesTitle() {
            return salesTitle;
        }

        public void setSalesTitle(String salesTitle) {
            this.salesTitle = salesTitle;
        }

        public String getRefundsTitle() {
            return refundsTitle;
        }

        public void setRefundsTitle(String refundsTitle) {
            this.refundsTitle = refundsTitle;
        }

        private String refundsTitle;


    }

    public static class TaxSummary {
        private String taxName;
        private String taxRate;
        private String amount;

        public String getTaxName() {
            return taxName;
        }

        public void setTaxName(String taxName) {
            this.taxName = taxName;
        }

        public String getTaxRate() {
            return taxRate;
        }

        public void setTaxRate(String taxRate) {
            this.taxRate = taxRate;
        }

        public String getAmount() {
            return amount;
        }

        public void setAmount(String amount) {
            this.amount = amount;
        }
    }
    public static class SalesSummaryTitle {
        private String totalSalesTitle;
        private String totalRefundsTitle;
        private String totalNetTitle;

        public String getTotalSalesTitle() {
            return totalSalesTitle;
        }

        public void setTotalSalesTitle(String totalSalesTitle) {
            this.totalSalesTitle = totalSalesTitle;
        }

        public String getTotalRefundsTitle() {
            return totalRefundsTitle;
        }

        public void setTotalRefundsTitle(String totalRefundsTitle) {
            this.totalRefundsTitle = totalRefundsTitle;
        }

        public String getTotalNetTitle() {
            return totalNetTitle;
        }

        public void setTotalNetTitle(String totalNetTitle) {
            this.totalNetTitle = totalNetTitle;
        }
    }
    public static class Payment {
        private String paymentId;
        private String title;
        private List<String> headerStrings;
        private String salesQuantity;
        private String salesAmount;
        private String depositsQuantity;
        private String depositsAmount;
        private String refundsQuantity;
        private String refundsAmount;
        private String netQuantity;
        private String netAmount;
        private String roundingTitle;
        private String roundingAmount;

        public String getRoundingTitle() {
            return roundingTitle;
        }

        public void setRoundingTitle(String roundingTitle) {
            this.roundingTitle = roundingTitle;
        }

        public String getRoundingAmount() {
            return roundingAmount;
        }

        public void setRoundingAmount(String roundingAmount) {
            this.roundingAmount = roundingAmount;
        }

        public String getPaymentId() {
            return paymentId;
        }

        public void setPaymentId(String paymentId) {
            this.paymentId = paymentId;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public List<String> getHeaderStrings() {
            return headerStrings;
        }

        public void setHeaderStrings(List<String> headerStrings) {
            this.headerStrings = headerStrings;
        }

        public String getSalesQuantity() {
            return salesQuantity;
        }

        public void setSalesQuantity(String salesQuantity) {
            this.salesQuantity = salesQuantity;
        }

        public String getSalesAmount() {
            return salesAmount;
        }

        public void setSalesAmount(String salesAmount) {
            this.salesAmount = salesAmount;
        }

        public String getDepositsQuantity() {
            return depositsQuantity;
        }

        public void setDepositsQuantity(String depositsQuantity) {
            this.depositsQuantity = depositsQuantity;
        }

        public String getDepositsAmount() {
            return depositsAmount;
        }

        public void setDepositsAmount(String depositsAmount) {
            this.depositsAmount = depositsAmount;
        }

        public String getRefundsQuantity() {
            return refundsQuantity;
        }

        public void setRefundsQuantity(String refundsQuantity) {
            this.refundsQuantity = refundsQuantity;
        }

        public String getRefundsAmount() {
            return refundsAmount;
        }

        public void setRefundsAmount(String refundsAmount) {
            this.refundsAmount = refundsAmount;
        }

        public String getNetQuantity() {
            return netQuantity;
        }

        public void setNetQuantity(String netQuantity) {
            this.netQuantity = netQuantity;
        }

        public String getNetAmount() {
            return netAmount;
        }

        public void setNetAmount(String netAmount) {
            this.netAmount = netAmount;
        }
    }
    public static class SalesSummary {
        private String salesQuantity;
        private String salesAmount;
        private String refundsQuantity;
        private String refundsAmount;
        private String netQuantity;
        private String netAmount;

        public String getSalesQuantity() {
            return salesQuantity;
        }

        public void setSalesQuantity(String salesQuantity) {
            this.salesQuantity = salesQuantity;
        }

        public String getSalesAmount() {
            return salesAmount;
        }

        public void setSalesAmount(String salesAmount) {
            this.salesAmount = salesAmount;
        }

        public String getRefundsQuantity() {
            return refundsQuantity;
        }

        public void setRefundsQuantity(String refundsQuantity) {
            this.refundsQuantity = refundsQuantity;
        }

        public String getRefundsAmount() {
            return refundsAmount;
        }

        public void setRefundsAmount(String refundsAmount) {
            this.refundsAmount = refundsAmount;
        }

        public String getNetQuantity() {
            return netQuantity;
        }

        public void setNetQuantity(String netQuantity) {
            this.netQuantity = netQuantity;
        }

        public String getNetAmount() {
            return netAmount;
        }

        public void setNetAmount(String netAmount) {
            this.netAmount = netAmount;
        }
    }
    public static class DepositsItem{
        private String title;
        private String quantity;
        private String amount;

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getQuantity() {
            return quantity;
        }

        public void setQuantity(String quantity) {
            this.quantity = quantity;
        }

        public String getAmount() {
            return amount;
        }

        public void setAmount(String amount) {
            this.amount = amount;
        }
    }
    public static class StoreCreditSummaryTitles {
        private String discountTitle;

        public String getDiscountTitle() {
            return discountTitle;
        }

        public void setDiscountTitle(String discountTitle) {
            this.discountTitle = discountTitle;
        }
    }
    public static class StoreCreditSummary {
        private String discountQuantity;
        private String discountAmount;


        public String getDiscountAmount() {
            return discountAmount;
        }

        public void setDiscountAmount(String discountAmount) {
            this.discountAmount = discountAmount;
        }

        public String getDiscountQuantity() {
            return discountQuantity;
        }

        public void setDiscountQuantity(String discountQuantity) {
            this.discountQuantity = discountQuantity;
        }
    }
    public static class CancelAndDiscountSummaryTitles {
        private String discountTitle;
        private String cancelTxnsTitle;

        public String getDiscountTitle() {
            return discountTitle;
        }

        public void setDiscountTitle(String discountTitle) {
            this.discountTitle = discountTitle;
        }

        public String getCancelTxnsTitle() {
            return cancelTxnsTitle;
        }

        public void setCancelTxnsTitle(String cancelTxnsTitle) {
            this.cancelTxnsTitle = cancelTxnsTitle;
        }
    }


    public static class CancelAndDiscountSummary {
        private String discountQuantity;
        private String discountAmount;
        private String cancelQuantity;
        private String cancelAmount;

        public String getDiscountQuantity() {
            return discountQuantity;
        }

        public void setDiscountQuantity(String discountQuantity) {
            this.discountQuantity = discountQuantity;
        }

        public String getDiscountAmount() {
            return discountAmount;
        }

        public void setDiscountAmount(String discountAmount) {
            this.discountAmount = discountAmount;
        }

        public String getCancelQuantity() {
            return cancelQuantity;
        }

        public void setCancelQuantity(String cancelQuantity) {
            this.cancelQuantity = cancelQuantity;
        }

        public String getCancelAmount() {
            return cancelAmount;
        }

        public void setCancelAmount(String cancelAmount) {
            this.cancelAmount = cancelAmount;
        }
    }

    public static class PaymentSummaryTitleStrings{
      private String salesTitle;
      private String depositeTitle;
      private String refundsTitle;
      private String netTitle;

        public String getSalesTitle() {
            return salesTitle;
        }

        public void setSalesTitle(String salesTitle) {
            this.salesTitle = salesTitle;
        }

        public String getDepositeTitle() {
            return depositeTitle;
        }

        public void setDepositeTitle(String depositeTitle) {
            this.depositeTitle = depositeTitle;
        }

        public String getRefundsTitle() {
            return refundsTitle;
        }

        public void setRefundsTitle(String refundsTitle) {
            this.refundsTitle = refundsTitle;
        }

        public String getNetTitle() {
            return netTitle;
        }

        public void setNetTitle(String netTitle) {
            this.netTitle = netTitle;
        }
    }
}