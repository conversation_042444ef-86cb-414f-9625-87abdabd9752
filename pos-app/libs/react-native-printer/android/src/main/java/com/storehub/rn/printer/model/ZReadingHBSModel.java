package com.storehub.rn.printer.model;

import java.util.List;

/**
 * Created By: sqq
 * Created Time: 2020-03-19 16:06.
 */
public class ZReadingHBSModel extends BaseModel {

    private String country;
    private String birAccredited;
    private String printedDate;
    private String storeName;
    private String vatRegNo;
    private String accredNo;
    private String serialNo;
    private String minNo;
    private String ptu;
    private String closeTime;
    private String registerInfo;
    private String zCount;
    private String grossSales;
    private String lessDiscount;
    private String lessRefund;
    private String totalDeductedVat;
    private String netSales;
    private List<Payment> paymentSummaryList;
    private String totalSalesCount;
    private String totalSalesAmount;
    private String vatAbleSales;
    private String vatAmount;
    private String vatExemptSales;
    private String zeroRatedSales;
    private String amusementTax;
    private String totalVatRelatedSales;
    private String serviceCharge;
    private String serviceChargeTax;
    private String totalServiceCharge;
    private String scDiscount;
    private String pwdDiscount;
    private String athleteAndCoachDiscountName;
    private String medalOfValorDiscountName;
    private String athleteAndCoachDiscount;
    private String medalOfValorDiscount;
    private String regularDiscount;
    private String soloParentDiscount;
    private String totalDiscount;
    private String startORNumber;
    private String endORNumber;
    private String startTrxNumber;
    private String endTrxNumber;
    private String totalTrxCount;
    private String salesTrxCount;
    private String refundTrxCount;
    private String refundAmount;
    private String changeItemTrx;
    private String changeItemAmount;
    private String oldGross;
    private String newGross;
    private String oldNet;
    private String newNet;
    private Boolean isRetail;

    public Boolean getIsRetail() {
        return isRetail;
    }

    public void setIsRetail(Boolean isRetail) {
        this.isRetail = isRetail;
    }

    public String getzCount() {
        return zCount;
    }

    public void setzCount(String zCount) {
        this.zCount = zCount;
    }

    public String getAthleteAndCoachDiscountName() {
        return athleteAndCoachDiscountName;
    }

    public void setAthleteAndCoachDiscountName(String athleteAndCoachDiscountName) {
        this.athleteAndCoachDiscountName = athleteAndCoachDiscountName;
    }

    public String getMedalOfValorDiscountName() {
        return medalOfValorDiscountName;
    }

    public void setMedalOfValorDiscountName(String medalOfValorDiscountName) {
        this.medalOfValorDiscountName = medalOfValorDiscountName;
    }

    public String getAthleteAndCoachDiscount() {
        return athleteAndCoachDiscount;
    }

    public void setAthleteAndCoachDiscount(String athleteAndCoachDiscount) {
        this.athleteAndCoachDiscount = athleteAndCoachDiscount;
    }

    public String getMedalOfValorDiscount() {
        return medalOfValorDiscount;
    }

    public void setMedalOfValorDiscount(String medalOfValorDiscount) {
        this.medalOfValorDiscount = medalOfValorDiscount;
    }

    public List<Payment> getPaymentSummaryList() {
        return paymentSummaryList;
    }

    public void setPaymentSummaryList(List<Payment> paymentSummaryList) {
        this.paymentSummaryList = paymentSummaryList;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getBirAccredited() {
        return birAccredited;
    }

    public void setBirAccredited(String birAccredited) {
        this.birAccredited = birAccredited;
    }

    public String getPrintedDate() {
        return printedDate;
    }

    public void setPrintedDate(String printedDate) {
        this.printedDate = printedDate;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getVatRegNo() {
        return vatRegNo;
    }

    public void setVatRegNo(String vatRegNo) {
        this.vatRegNo = vatRegNo;
    }

    public String getAccredNo() {
        return accredNo;
    }

    public void setAccredNo(String accredNo) {
        this.accredNo = accredNo;
    }

    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    public String getMinNo() {
        return minNo;
    }

    public void setMinNo(String minNo) {
        this.minNo = minNo;
    }

    public String getPtu() {
        return ptu;
    }

    public void setPtu(String ptu) {
        this.ptu = ptu;
    }

    public String getCloseTime() {
        return closeTime;
    }

    public void setCloseTime(String closeTime) {
        this.closeTime = closeTime;
    }

    public String getRegisterInfo() {
        return registerInfo;
    }

    public void setRegisterInfo(String registerInfo) {
        this.registerInfo = registerInfo;
    }

    public String getZCount() {
        return zCount;
    }

    public void setZCount(String zCount) {
        this.zCount = zCount;
    }

    public String getGrossSales() {
        return grossSales;
    }

    public void setGrossSales(String grossSales) {
        this.grossSales = grossSales;
    }

    public String getLessDiscount() {
        return lessDiscount;
    }

    public void setLessDiscount(String lessDiscount) {
        this.lessDiscount = lessDiscount;
    }

    public String getLessRefund() {
        return lessRefund;
    }

    public void setLessRefund(String lessRefund) {
        this.lessRefund = lessRefund;
    }

    public String getTotalDeductedVat() {
        return totalDeductedVat;
    }

    public void setTotalDeductedVat(String totalDeductedVat) {
        this.totalDeductedVat = totalDeductedVat;
    }

    public String getNetSales() {
        return netSales;
    }

    public void setNetSales(String netSales) {
        this.netSales = netSales;
    }

    public String getTotalSalesCount() {
        return totalSalesCount;
    }

    public void setTotalSalesCount(String totalSalesCount) {
        this.totalSalesCount = totalSalesCount;
    }

    public String getTotalSalesAmount() {
        return totalSalesAmount;
    }

    public void setTotalSalesAmount(String totalSalesAmount) {
        this.totalSalesAmount = totalSalesAmount;
    }

    public String getVatAbleSales() {
        return vatAbleSales;
    }

    public void setVatAbleSales(String vatAbleSales) {
        this.vatAbleSales = vatAbleSales;
    }

    public String getVatAmount() {
        return vatAmount;
    }

    public void setVatAmount(String vatAmount) {
        this.vatAmount = vatAmount;
    }

    public String getVatExemptSales() {
        return vatExemptSales;
    }

    public void setVatExemptSales(String vatExemptSales) {
        this.vatExemptSales = vatExemptSales;
    }

    public String getZeroRatedSales() {
        return zeroRatedSales;
    }

    public void setZeroRatedSales(String zeroRatedSales) {
        this.zeroRatedSales = zeroRatedSales;
    }

    public String getAmusementTax() {
        return amusementTax;
    }

    public void setAmusementTax(String amusementTax) {
        this.amusementTax = amusementTax;
    }
    public String getTotalVatRelatedSales() {
        return totalVatRelatedSales;
    }

    public void setTotalVatRelatedSales(String totalVatRelatedSales) {
        this.totalVatRelatedSales = totalVatRelatedSales;
    }

    public String getServiceCharge() {
        return serviceCharge;
    }

    public void setServiceCharge(String serviceCharge) {
        this.serviceCharge = serviceCharge;
    }

    public String getServiceChargeTax() {
        return serviceChargeTax;
    }

    public void setServiceChargeTax(String serviceChargeTax) {
        this.serviceChargeTax = serviceChargeTax;
    }

    public String getTotalServiceCharge() {
        return totalServiceCharge;
    }

    public void setTotalServiceCharge(String totalServiceCharge) {
        this.totalServiceCharge = totalServiceCharge;
    }

    public String getScDiscount() {
        return scDiscount;
    }

    public void setScDiscount(String scDiscount) {
        this.scDiscount = scDiscount;
    }

    public String getPwdDiscount() {
        return pwdDiscount;
    }

    public void setPwdDiscount(String pwdDiscount) {
        this.pwdDiscount = pwdDiscount;
    }

    public String getRegularDiscount() {
        return regularDiscount;
    }

    public void setRegularDiscount(String regularDiscount) {
        this.regularDiscount = regularDiscount;
    }

    public String getSoloParentDiscount() {
        return soloParentDiscount;
    }

    public void setSoloParentDiscount(String soloParentDiscount) {
        this.soloParentDiscount = soloParentDiscount;
    }

    public String getTotalDiscount() {
        return totalDiscount;
    }

    public void setTotalDiscount(String totalDiscount) {
        this.totalDiscount = totalDiscount;
    }

    public String getStartORNumber() {
        return startORNumber;
    }

    public void setStartORNumber(String startORNumber) {
        this.startORNumber = startORNumber;
    }

    public String getEndORNumber() {
        return endORNumber;
    }

    public void setEndORNumber(String endORNumber) {
        this.endORNumber = endORNumber;
    }

    public String getStartTrxNumber() {
        return startTrxNumber;
    }

    public void setStartTrxNumber(String startTrxNumber) {
        this.startTrxNumber = startTrxNumber;
    }

    public String getEndTrxNumber() {
        return endTrxNumber;
    }

    public void setEndTrxNumber(String endTrxNumber) {
        this.endTrxNumber = endTrxNumber;
    }

    public String getTotalTrxCount() {
        return totalTrxCount;
    }

    public void setTotalTrxCount(String totalTrxCount) {
        this.totalTrxCount = totalTrxCount;
    }

    public String getSalesTrxCount() {
        return salesTrxCount;
    }

    public void setSalesTrxCount(String salesTrxCount) {
        this.salesTrxCount = salesTrxCount;
    }

    public String getRefundTrxCount() {
        return refundTrxCount;
    }

    public void setRefundTrxCount(String refundTrxCount) {
        this.refundTrxCount = refundTrxCount;
    }

    public String getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(String refundAmount) {
        this.refundAmount = refundAmount;
    }

    public String getChangeItemTrx() {
        return changeItemTrx;
    }

    public void setChangeItemTrx(String changeItemTrx) {
        this.changeItemTrx = changeItemTrx;
    }

    public String getChangeItemAmount() {
        return changeItemAmount;
    }

    public void setChangeItemAmount(String changeItemAmount) {
        this.changeItemAmount = changeItemAmount;
    }

    public String getOldGross() {
        return oldGross;
    }

    public void setOldGross(String oldGross) {
        this.oldGross = oldGross;
    }

    public String getNewGross() {
        return newGross;
    }

    public void setNewGross(String newGross) {
        this.newGross = newGross;
    }

    public String getOldNet() {
        return oldNet;
    }

    public void setOldNet(String oldNet) {
        this.oldNet = oldNet;
    }

    public String getNewNet() {
        return newNet;
    }

    public void setNewNet(String newNet) {
        this.newNet = newNet;
    }

    public static class Payment {

        private String name;
        private String count;
        private String amount;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getCount() {
            return count;
        }

        public void setCount(String count) {
            this.count = count;
        }

        public String getAmount() {
            return amount;
        }

        public void setAmount(String amount) {
            this.amount = amount;
        }
    }
}
