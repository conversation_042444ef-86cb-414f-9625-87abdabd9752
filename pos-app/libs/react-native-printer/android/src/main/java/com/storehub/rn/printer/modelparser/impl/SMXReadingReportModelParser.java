package com.storehub.rn.printer.modelparser.impl;

import android.content.Context;
import android.graphics.Bitmap;

import com.github.jknack.handlebars.Template;
import com.google.gson.Gson;
import com.storehub.rn.printer.R;
import com.storehub.rn.printer.model.SMXReadingReportHBSModel;
import com.storehub.rn.printer.modelparser.AbstractModelParser;
import com.storehub.rn.printer.modelparser.IBitmapGenerator;
import com.storehub.rn.printer.modelparser.text.PrintableUnit;
import com.storehub.rn.printer.printer.PrinterPaperWidth;
import com.storehub.rn.printer.util.Html2BitmapUtil;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

import timber.log.Timber;


public class SMXReadingReportModelParser extends AbstractModelParser {
    private SMXReadingReportHBSModel model;

    public SMXReadingReportModelParser(Context context, SMXReadingReportHBSModel model, PrinterPaperWidth printerPaperWidth) {
        super(context);
        this.context = context;
        this.printerPaperWidth = printerPaperWidth;
        this.model = model;
        if (printerPaperWidth == PrinterPaperWidth.Print58) {
            model.setPrinterPaperWidth58(true);
        }
        this.key = String.format("SMXReadingReport-%s-%s", model.getStoreName(), model.getDateLines().get(2).getValue());
    }

    @Override
    public List<PrintableUnit> getPrintableUnitList() {
        return null;
    }

    @Override
    public Bitmap getPrintableBitmap() {
        return null;
    }

    @Override
    public List<IBitmapGenerator> getBitmapGenerators() {
        List<IBitmapGenerator> generators = new LinkedList<>();
        generators.add(new IBitmapGenerator() {
            @Override
            public Bitmap next() {
                return generateHeader(model, R.raw.sm_xreading_report_header);
            }
        });

        generators.add(new IBitmapGenerator() {
            @Override
            public Bitmap next() {
                return generateSecond(model, R.raw.sm_report_second);
            }
        });

        List<List<SMXReadingReportHBSModel.MixSummaryLineItem>> mixSummaryItemGroups = model.getMixSummaryLinesGroups();
        for (List<SMXReadingReportHBSModel.MixSummaryLineItem> mixSummaryItems : mixSummaryItemGroups) {
            generators.add(new IBitmapGenerator() {
                @Override
                public Bitmap next() {
                    return generateMinSummaryItems((ArrayList<SMXReadingReportHBSModel.MixSummaryLineItem>) mixSummaryItems, R.raw.sm_report_items);
                }
            });
        }

        generators.add(new IBitmapGenerator() {
            @Override
            public Bitmap next() {
                return generateFooter(model, R.raw.sm_report_footer);
            }
        });
        return generators;
    }

    private Bitmap generateHeader(SMXReadingReportHBSModel model, int resId) {
        try {
            Template template = Html2BitmapUtil.getTemplate(context, resId);
            String html = template.apply(model);
            return Html2BitmapUtil.html2bitmap(context, html, printerPaperWidth, model.getReceiptFontScale());
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }

    private Bitmap generateSecond(SMXReadingReportHBSModel model, int resId) {
        try {
            Template template = Html2BitmapUtil.getTemplate(context, resId);
            String html = template.apply(model);
            return Html2BitmapUtil.html2bitmap(context, html, printerPaperWidth, model.getReceiptFontScale());
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }

    private Bitmap generateMinSummaryItems(ArrayList<SMXReadingReportHBSModel.MixSummaryLineItem> mixSummaryItems, int resId) {
        try {
            Template template = Html2BitmapUtil.getTemplate(context, resId);
            Timber.v(new Gson().toJson(mixSummaryItems));
            String html = template.apply(mixSummaryItems);
            return Html2BitmapUtil.html2bitmap(context, html, printerPaperWidth, model.getReceiptFontScale());
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }

    private Bitmap generateFooter(SMXReadingReportHBSModel model, int resId) {
        try {
            Template template = Html2BitmapUtil.getTemplate(context, resId);
            Timber.v(new Gson().toJson(model));
            String html = template.apply(model);
            Timber.v(html);
            return Html2BitmapUtil.html2bitmap(context, html, printerPaperWidth, model.getReceiptFontScale());
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }
}
