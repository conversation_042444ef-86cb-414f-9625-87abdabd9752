package com.storehub.rn.printer.modelparser.impl;

import android.content.Context;
import android.graphics.Bitmap;

import com.github.jknack.handlebars.Template;
import com.google.gson.Gson;
import com.storehub.rn.printer.R;
import com.storehub.rn.printer.model.ZReadingHBSModel;
import com.storehub.rn.printer.modelparser.AbstractModelParser;
import com.storehub.rn.printer.modelparser.IBitmapGenerator;
import com.storehub.rn.printer.modelparser.text.PrintableUnit;
import com.storehub.rn.printer.printer.PrinterPaperWidth;
import com.storehub.rn.printer.util.Html2BitmapUtil;

import java.util.LinkedList;
import java.util.List;

import timber.log.Timber;

public class ZReadingShiftReportModelParser extends AbstractModelParser {
    private ZReadingHBSModel model;

    public ZReadingShiftReportModelParser(Context context, ZReadingHBSModel model, PrinterPaperWidth printerPaperWidth) {
        super(context);
        this.context = context;
        this.printerPaperWidth = printerPaperWidth;
        this.model = model;
        if (printerPaperWidth == PrinterPaperWidth.Print58) {
            model.setPrinterPaperWidth58(true);
        }
        this.key = String.format("ZReadingShiftReport-%s", model.getAccredNo());
    }

    @Override
    public List<PrintableUnit> getPrintableUnitList() {
        return null;
    }

    @Override
    public Bitmap getPrintableBitmap() {
        return generateBitmapByHtml();
    }

    @Override
    public List<IBitmapGenerator> getBitmapGenerators() {
        List<IBitmapGenerator> generators = new LinkedList<>();
        generators.add(new IBitmapGenerator() {
            @Override
            public Bitmap next() {
                return generateBitmapByHtml();
            }
        });
        return generators;
    }

    private Bitmap generateBitmapByHtml() {
        try {
            Template template = Html2BitmapUtil.getTemplate(context, R.raw.z_reading);
            Timber.v(new Gson().toJson(model));
            String html = template.apply(model);
            Timber.v(html);
//            StoreInfoCache.saveBytes2File(context,html.getBytes(),"z-reading.html");
            return Html2BitmapUtil.html2bitmap(context, html, printerPaperWidth, model.getReceiptFontScale());
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }
}
