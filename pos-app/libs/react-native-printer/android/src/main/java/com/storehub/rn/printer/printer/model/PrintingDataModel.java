package com.storehub.rn.printer.printer.model;

import com.facebook.react.bridge.ReadableMap;

import java.util.List;

/**
 * Created By: sqq
 * Created Time: 2020-01-22 20:16.
 */
public class PrintingDataModel {

    private String printerId;
    private String businessType;
    private String workflowId;
    private ReadableMap data;
    private List<String> uri;

    public PrintingDataModel() {
    }

    public String getPrinterId() {
        return printerId;
    }

    public PrintingDataModel setPrinterId(String printerId) {
        this.printerId = printerId;
        return this;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public ReadableMap getData() {
        return data;
    }

    public PrintingDataModel setData(ReadableMap data) {
        this.data = data;
        return this;
    }

    public String getWorkflowId() {
        return workflowId;
    }

    public void setWorkflowId(String workflowId) {
        this.workflowId = workflowId;
    }

    public List<String> getUri() {
        return uri;
    }

    public void setUri(List<String> uri) {
        this.uri = uri;
    }
}
