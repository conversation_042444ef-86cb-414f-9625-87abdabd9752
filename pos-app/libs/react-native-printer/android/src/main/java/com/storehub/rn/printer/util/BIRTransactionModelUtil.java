package com.storehub.rn.printer.util;

import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.bridge.ReadableMap;
import com.storehub.rn.printer.model.BIRTransactionModel;

import java.util.ArrayList;
import java.util.List;

public class BIRTransactionModelUtil {

    public static BIRTransactionModel parseMapToModel(ReadableMap map) {
        BIRTransactionModel model = new BIRTransactionModel();
        model.setKey((String) ReadableMapUtil.getValueFromMapWithDefaultValue(map, "key", ""));
        model.setStoreName((String) ReadableMapUtil.getValueFromMap(map, "storeName"));
        model.setAddress((String) ReadableMapUtil.getValueFromMap(map, "address"));
        model.setPhone((String) ReadableMapUtil.getValueFromMap(map, "phone"));

        model.setReceiptDate((String) ReadableMapUtil.getValueFromMap(map, "receiptDate"));
        model.setVoidNo((String) ReadableMapUtil.getValueFromMap(map, "voidNo"));
        model.setReprintDate((String) ReadableMapUtil.getValueFromMap(map, "reprintDate"));

        ReadableArray columnWidthArray = (ReadableArray) ReadableMapUtil.getValueFromMap(map, "columnWidths");
        if (columnWidthArray != null) {
            ArrayList<String> columnWidthList = new ArrayList<>();
            for (int i = 0; i < columnWidthArray.size(); i++) {
                columnWidthList.add(columnWidthArray.getString(i));
            }
            model.setColumnWidths(columnWidthList);
        }

        ReadableArray columnTitleStringArray = (ReadableArray) ReadableMapUtil.getValueFromMap(map,
                "columnTitleString");
        if (columnTitleStringArray != null) {
            ArrayList<String> columnTitleStringList = new ArrayList<>();
            for (int i = 0; i < columnTitleStringArray.size(); i++) {
                columnTitleStringList.add(columnTitleStringArray.getString(i));
            }
            model.setColumnTitleString(columnTitleStringList);
        }

        ReadableArray a4ColumnTitleStringArray = (ReadableArray) ReadableMapUtil.getValueFromMap(map,
                "a4ColumnTitleString");
        if (a4ColumnTitleStringArray != null) {
            ArrayList<String> a4ColumnTitleStringList = new ArrayList<>();
            for (int i = 0; i < a4ColumnTitleStringArray.size(); i++) {
                a4ColumnTitleStringList.add(a4ColumnTitleStringArray.getString(i));
            }
            model.setA4ColumnTitleString(a4ColumnTitleStringList);
        }

        ReadableArray birInfoListArray = (ReadableArray) ReadableMapUtil.getValueFromMap(map,
                "birInfoList");
        if (birInfoListArray != null) {
            List<BIRTransactionModel.BirInfoType> birInfoTypes = new ArrayList<>();
            for (int i = 0; i < birInfoListArray.size(); i++) {
                ReadableMap _map = birInfoListArray.getMap(i);
                BIRTransactionModel.BirInfoType birInfoType = new BIRTransactionModel.BirInfoType();
                if (_map == null)
                    continue;
                birInfoType.setName(
                        ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "name", "").toString());
                birInfoType.setValue(
                        ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "value", "").toString());
                birInfoType.setNeedDedicatedSpace(
                        (Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "needDedicatedSpace", false));
                birInfoTypes.add(birInfoType);
            }
            model.setBirInfoList(birInfoTypes);
        }
        model.setOrderNoName(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "orderNoName", "").toString());
        model.setOrderNo(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "orderNo", "").toString());

        model.setReceiptTitleBig(
                (Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(map, "isReceiptTitleBig", true));
        model.setReceiptTitlePH(
                (ReadableMapUtil.getValueFromMapWithDefaultValue(map, "receiptTitlePH", "")).toString());
        model.setDefaultLoyaltyRatioNumber(
                (Double) ReadableMapUtil.getValueFromMapWithDefaultValue(map, "defaultLoyaltyRatio", 0d));
        model.setEnableCashback(
                ((boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(map, "enableCashback", false)));
        model.setEnablePrintCashback(
                ((boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(map, "enablePrintCashback", false)));
        model.setShowBarcode(((boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(map, "showBarcode", false)));
        model.setShowPurchasedItemsDiscount(
                ((boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(map, "showPurchasedItemsDiscount", false)));
        model.setReceiptId((ReadableMapUtil.getValueFromMapWithDefaultValue(map, "receiptId", "")).toString());
        model.setCashbackUrl((ReadableMapUtil.getValueFromMapWithDefaultValue(map, "cashbackUrl", "")).toString());
        model.setEInvoiceUrl((ReadableMapUtil.getValueFromMapWithDefaultValue(map, "eInvoiceUrl", "")).toString());
        model.setEInvoiceDescription(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "eInvoiceDescription", "").toString());
        model.setEnablePrintEInvoice((boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(map, "enablePrintEInvoice", false));

        model.setShippingFeeValue(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "shippingFeeValue", "").toString());
        model.setShippingFeeName(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "shippingFeeName", "").toString());
        model.setTakeawayFeeName(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "takeawayFeeName", "").toString());
        model.setTakeawayFeeValue(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "takeawayFeeValue", "").toString());
        model.setBusiness((ReadableMapUtil.getValueFromMapWithDefaultValue(map, "business", "")).toString());
        model.setEnablePrintQRCode((boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(map, "enablePrintQRCode", false));
        model.setEnablePrintMemberShip((boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(map, "enablePrintMemberShip", false));
        model.setMembershipSource((ReadableMapUtil.getValueFromMapWithDefaultValue(map, "membershipSource", "")).toString());
        model.setMembershipUrl((ReadableMapUtil.getValueFromMapWithDefaultValue(map, "membershipUrl", "")).toString());
        model.setQrCodeDesc((ReadableMapUtil.getValueFromMapWithDefaultValue(map, "qrCodeDesc", "")).toString());

        model.setCashBackTxt(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "cashBackTxt", "").toString());

        model.setQrCodeAboveInfo(
                (ReadableMapUtil.getValueFromMapWithDefaultValue(map, "qrCodeAboveInfo", "")).toString());
        model.setQrCodeUnderInfo(
                (ReadableMapUtil.getValueFromMapWithDefaultValue(map, "qrCodeUnderInfo", "")).toString());

        model.setBirCompanyName(((String) ReadableMapUtil.getValueFromMap(map, "birCompanyName")));
        model.setCustomerInfo(((String) ReadableMapUtil.getValueFromMap(map, "customerInfo")));
        model.setReceiptNumber(((String) ReadableMapUtil.getValueFromMap(map, "receiptNumber")));
        model.setOrderNumber(((String) ReadableMapUtil.getValueFromMap(map, "orderNumber")));
        model.setMinNumber(((String) ReadableMapUtil.getValueFromMap(map, "minNumber")));
        model.setOrderId(((String) ReadableMapUtil.getValueFromMap(map, "orderId")));
        model.setSerialNumber(((String) ReadableMapUtil.getValueFromMap(map, "serialNumber")));
        model.setReasonString(((String) ReadableMapUtil.getValueFromMap(map, "reasonString")));
        model.setCashierInfo(((String) ReadableMapUtil.getValueFromMap(map, "cashierInfo")));
        model.setRegisterNumber(((String) ReadableMapUtil.getValueFromMap(map, "registerNumber")));
        model.setNoteString(((String) ReadableMapUtil.getValueFromMap(map, "noteString")));
        model.setMiddleOrderTableTitleWithContent(
                ((String) ReadableMapUtil.getValueFromMap(map, "middleOrderTableTitleWithContent")));
        model.setReceiptTitle(((String) ReadableMapUtil.getValueFromMap(map, "receiptTitle")));
        model.setSubtotalTitle(((String) ReadableMapUtil.getValueFromMap(map, "subtotalTitle")));
        model.setDiscountTitle(((String) ReadableMapUtil.getValueFromMap(map, "discountTitle")));
        model.setLess12vatTitle(((String) ReadableMapUtil.getValueFromMap(map, "less12vatTitle")));
        model.setLess12vat(((String) ReadableMapUtil.getValueFromMap(map, "less12vat")));
        model.setAdhocDiscountTitle(((String) ReadableMapUtil.getValueFromMap(map, "adhocDiscountTitle")));
        model.setAdhocDiscount(((String) ReadableMapUtil.getValueFromMap(map, "adhocDiscount")));
        model.setSeniorDiscountTitle(((String) ReadableMapUtil.getValueFromMap(map, "seniorDiscountTitle")));
        model.setSeniorDiscount(((String) ReadableMapUtil.getValueFromMap(map, "seniorDiscount")));
        model.setPwdDiscountTitle(((String) ReadableMapUtil.getValueFromMap(map, "pwdDiscountTitle")));
        model.setPwdDiscount(((String) ReadableMapUtil.getValueFromMap(map, "pwdDiscount")));
        model.setSmallOrderFeeTxt(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "smallOrderFeeTxt", "").toString());
        model.setSmallOrderFeeValue(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "smallOrderFeeValue", "").toString());
        model.setContainerFeeTxt(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "containerFeeTxt", "").toString());
        model.setContainerFeeValue(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "containerFeeValue", "").toString());

        model.setAthleteAndCoachDiscountTitle(((String) ReadableMapUtil.getValueFromMap(map, "athleteAndCoachDiscountTitle")));
        model.setAthleteAndCoachDiscount(((String) ReadableMapUtil.getValueFromMap(map, "athleteAndCoachDiscount")));
        model.setSoloParentDiscountTitle(((String) ReadableMapUtil.getValueFromMap(map, "soloParentDiscountTitle")));
        model.setSoloParentDiscount(((String) ReadableMapUtil.getValueFromMap(map, "soloParentDiscount")));
        model.setMedalOfValorDiscountTitle(((String) ReadableMapUtil.getValueFromMap(map, "medalOfValorDiscountTitle")));
        model.setMedalOfValorDiscount(((String) ReadableMapUtil.getValueFromMap(map, "medalOfValorDiscount")));

        model.setVatOf12Title(((String) ReadableMapUtil.getValueFromMap(map, "vatOf12Title")));
        model.setVatOf12(((String) ReadableMapUtil.getValueFromMap(map, "vatOf12")));
        model.setAmusementTaxTitle(((String) ReadableMapUtil.getValueFromMap(map, "amusementTaxTitle")));
        model.setAmusementTax(((String) ReadableMapUtil.getValueFromMap(map, "amusementTax")));
        model.setServiceChargeTitle(((String) ReadableMapUtil.getValueFromMap(map, "serviceChargeTitle")));
        model.setRoundingTitle(((String) ReadableMapUtil.getValueFromMap(map, "roundingTitle")));
        model.setRounding(((String) ReadableMapUtil.getValueFromMap(map, "rounding")));
        model.setShowOrderSummary((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(map, "showOrderSummary", true));
        model.setTotalTitle(((String) ReadableMapUtil.getValueFromMap(map, "totalTitle")));
        model.setAmountOutstandingTitle(((String) ReadableMapUtil.getValueFromMap(map, "amountOutStandingTitle")));
        model.setAmountOutstanding(((String) ReadableMapUtil.getValueFromMap(map, "amountOutStanding")));
        model.setVatableSalesTitle(((String) ReadableMapUtil.getValueFromMap(map, "vatableSalesTitle")));
        model.setVatableSales(((String) ReadableMapUtil.getValueFromMap(map, "vatableSales")));
        model.setVatAmountTitle(((String) ReadableMapUtil.getValueFromMap(map, "vatAmountTitle")));
        model.setVatAmount(((String) ReadableMapUtil.getValueFromMap(map, "vatAmount")));
        model.setVatExemptSalesTitle(((String) ReadableMapUtil.getValueFromMap(map, "vatExemptSalesTitle")));
        model.setVatExemptSales(((String) ReadableMapUtil.getValueFromMap(map, "vatExemptSales")));
        model.setZeroRatedSalesTitle(((String) ReadableMapUtil.getValueFromMap(map, "zeroRatedSalesTitle")));
        model.setZeroRatedSales(((String) ReadableMapUtil.getValueFromMap(map, "zeroRatedSales")));
        model.setOnlineChannelNotesTitle(((String) ReadableMapUtil.getValueFromMap(map, "onlineChannelNotesTitle")));
        model.setOnlineChannelNotesContent(
                ((String) ReadableMapUtil.getValueFromMap(map, "onlineChannelNotesContent")));
        model.setOnlinePickUpNoteTitle(((String) ReadableMapUtil.getValueFromMap(map, "onlinePickUpNoteTitle")));
        model.setOnlineOrderNoteContent(((String) ReadableMapUtil.getValueFromMap(map, "onlineOrderNoteContent")));
        model.setPreOrderNotes(((String) ReadableMapUtil.getValueFromMap(map, "preOrderNotes")));
        model.setFooterLabelString(((String) ReadableMapUtil.getValueFromMap(map, "footerLabelString")));
        model.setBirAccrInfo(((String) ReadableMapUtil.getValueFromMap(map, "birAccrInfo")));
        model.setAccrNumber(((String) ReadableMapUtil.getValueFromMap(map, "accrNumber")));
        model.setPtuNumber(((String) ReadableMapUtil.getValueFromMap(map, "ptuNumber")));
        model.setDateIssueNumber(((String) ReadableMapUtil.getValueFromMap(map, "dateIssueNumber")));
        model.setVatRegisterFooterInfo(((String) ReadableMapUtil.getValueFromMap(map, "vatRegisterFooterInfo")));
        model.setStorehubPoweredInfo(((String) ReadableMapUtil.getValueFromMap(map, "storehubPoweredInfo")));
        model.setBeepCashbackAmount(((String) ReadableMapUtil.getValueFromMap(map, "beepCashbackAmount")));
        model.setStoreCreditAmount(((String) ReadableMapUtil.getValueFromMap(map, "storeCreditAmount")));
        model.setShowReceiptStoreCredit((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(map, "showReceiptStoreCredit", false));
        model.setLoyaltyEarned(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "loyaltyEarned", "").toString());
        model.setLoyaltyBalance(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "loyaltyBalance", "").toString());
        model.setLoyaltySpent(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "loyaltySpent", "").toString());
        model.setEarnedTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "earnedTitle", "").toString());
        model.setSpentTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "spentTitle", "").toString());
        model.setBalanceTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "balanceTitle", "").toString());
        model.setCashbackExpirationDesc(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "cashbackExpirationDesc", "").toString());
        model.setTaxTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "taxTitle", "").toString());
        model.setTax(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "tax", "").toString());
        model.setShowVatSummary(
                ((boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(map, "showVatSummary", false)));

        //Pre order
        model.setShowPreorderSummary(((boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(map, "showPreorderSummary", false)));
        model.setDepositAmountTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "depositAmountTitle", "").toString());
        model.setDepositAmount(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "depositAmount", "").toString());
        model.setUnPaidBalanceTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "unPaidBalanceTitle", "").toString());
        model.setPickUpDate(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "pickUpDate", "").toString());
        model.setUnPaidBalance(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "unPaidBalance", "").toString());

        List<BIRTransactionModel.PurchasedItem> purchasedItems = new ArrayList<>();
        ReadableArray purchasedItemArray = ((ReadableArray) ReadableMapUtil.getValueFromMap(map, "purchasedItems"));

        if (purchasedItemArray != null) {
            for (int i = 0; i < purchasedItemArray.size(); i++) {
                ReadableMap _map = purchasedItemArray.getMap(i);
                BIRTransactionModel.PurchasedItem item = new BIRTransactionModel.PurchasedItem();
                if (_map == null)
                    continue;

                item.setItemName(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "itemName", "").toString());
                item.setPrice(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "price", "").toString());
                item.setQuantity(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "quantity", "").toString());
                item.setSubTotal(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "subTotal", "").toString());
                item.setOptions(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "options", "").toString());
                item.setNotes(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "notes", "").toString());
                item.setDiscount(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "discount", "").toString());
                item.setTotal(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "total", "").toString());
                item.setA4Total(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "a4Total", "").toString());
                item.setSn(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "sn", "").toString());
                item.setItemType(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "itemType", "").toString());
                item.setItemDiscountValue(
                        ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "itemDiscountValue", "").toString());
                item.setItemDiscountName(
                        ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "itemDiscountName", "").toString());
                item.setEnableTakeaway((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "enableTakeaway", false));
                item.setTakeawayCharge(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "takeawayCharge", "").toString());
                item.setTakeawayTxt(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "takeawayTxt", "").toString());
                List<BIRTransactionModel.Promotions> promotionsItems = new ArrayList<>();

                if (_map.hasKey("promotions")) {
                    ReadableArray promotionItemArray = _map.getArray("promotions");
                    if (promotionItemArray != null) {
                        for (int j = 0; j < promotionItemArray.size(); j++) {
                            ReadableMap _map1 = promotionItemArray.getMap(j);
                            if (_map1 == null)
                                continue;
                            BIRTransactionModel.Promotions promotion = new BIRTransactionModel.Promotions();
                            promotion.setPromotionName(ReadableMapUtil
                                    .getValueFromMapWithDefaultValue(_map1, "promotionName", "").toString());
                            promotion.setDiscount(
                                    ReadableMapUtil.getValueFromMapWithDefaultValue(_map1, "discount", "").toString());
                            promotionsItems.add(promotion);
                        }
                    }
                }
                item.setPromotions(promotionsItems);
                purchasedItems.add(item);
            }
        }

        model.setPurchasedItems(purchasedItems);

        model.setSubtotal(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "subtotal", "").toString());
        model.setDiscount(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "discount", "").toString());
        model.setA4Discount(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "a4Discount", "").toString());
        model.setMembershipSmallTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "membershipSmallTitle", "").toString());
        model.setMembershipBoildTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "membershipBoildTitle", "").toString());
        model.setMembershipLargeContentTitle1(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "membershipLargeContentTitle1", "").toString());
        model.setMembershipLargeContentTitle2(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "membershipLargeContentTitle2", "").toString());
        model.setMembershipSmallBottomTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "membershipSmallBottomTitle", "").toString());

        model.setTotal(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "total", "").toString());

        ReadableArray paymentArray = ((ReadableArray) ReadableMapUtil.getValueFromMap(map, "payment"));
        if (paymentArray != null) {
            List<BIRTransactionModel.PaymentType> payments = new ArrayList<>();
            for (int i = 0; i < paymentArray.size(); i++) {
                ReadableMap _map = paymentArray.getMap(i);

                BIRTransactionModel.PaymentType paymentType = new BIRTransactionModel.PaymentType();
                if (_map == null)
                    continue;
                if (_map.hasKey("paymentMethodId")) {
                    paymentType.setPaymentMethodId(String.valueOf(_map.getInt("paymentMethodId")));
                }
                paymentType.setPaymentMethodName(
                        ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "paymentMethodName", "").toString());
                paymentType.setCashTendered(
                        ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "cashTendered", "").toString());
                paymentType.setAmount(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "amount", "").toString());
                paymentType.setChangeTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "changeTitle", "").toString());
                paymentType.setChangeValue(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "changeValue", "").toString());
                paymentType.setRoundedAmount(
                        ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "roundedAmount", "").toString());
                paymentType.setSubType(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "subType", "").toString());
                payments.add(paymentType);
            }
            model.setPayment(payments);
        }

        ReadableArray receiptStoreCreditTitleStringArray = (ReadableArray) ReadableMapUtil.getValueFromMap(map, "receiptStoreCreditTitleString");
        if (receiptStoreCreditTitleStringArray != null) {
            ArrayList<String> receiptStoreCreditTitleStringList = new ArrayList<>();
            for (int i = 0; i < receiptStoreCreditTitleStringArray.size(); i++) {
                receiptStoreCreditTitleStringList.add(receiptStoreCreditTitleStringArray.getString(i));
            }
            model.setReceiptStoreCreditTitleString(receiptStoreCreditTitleStringList);
        }

        model.setCompanyName(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "companyName", "").toString());
        model.setServiceCharge(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "serviceCharge", "").toString());
        model.setUsingDiscountLayout(((boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(map, "usingDiscountLayout", false)));

        model.setReceiptFontScale(ReadableMapUtil.getDoubleValueFromMapWithDefaultValue(map, "receiptFontScale", 1d).floatValue());
        return model;
    }

}