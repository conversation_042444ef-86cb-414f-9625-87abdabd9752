package com.storehub.rn.printer.util;

import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.bridge.ReadableMap;
import com.storehub.rn.printer.model.SMXReadingReportHBSModel;

import java.util.ArrayList;
import java.util.List;

public class SMXReadingReportHBSModelUtil {

    public static SMXReadingReportHBSModel parseMapToModel(ReadableMap map) {
        SMXReadingReportHBSModel model = new SMXReadingReportHBSModel();
        model.setBusinessName(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "businessName", "").toString());
        model.setStoreName(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "storeName", "").toString());
        model.setStoreAddress(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "storeAddress", "").toString());
        model.setVatRegTin(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "vatRegTin", "").toString());
        model.setMinNo(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "minNo", "").toString());
        model.setSerialNo(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "serialNo", "").toString());
        model.setReportTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "reportTitle", "").toString());

        // noLins
//        ReadableArray noLinesListArray = (ReadableArray) ReadableMapUtil.getValueFromMap(map, "noLines");
//        if (noLinesListArray != null) {
//            List<SMXReadingReportHBSModel.CommonLineItem> list = new ArrayList<>();
//            for (int i = 0; i < noLinesListArray.size(); i++) {
//                ReadableMap _map = noLinesListArray.getMap(i);
//                SMXReadingReportHBSModel.CommonLineItem commonLineItem = new SMXReadingReportHBSModel.CommonLineItem();
//                commonLineItem.setName(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "name", "").toString());
//                commonLineItem.setCount(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "count", "").toString());
//                commonLineItem.setValue(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "value", "").toString());
//                commonLineItem.setHasMargin((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "hasMargin", false));
//                commonLineItem.setInRight((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "inRight", false));
//                commonLineItem.setTitle((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "isTitle", false));
//                list.add(commonLineItem);
//            }
//            model.setNoLines(list);
//        }

        // summaryLines
        ReadableArray summaryLinesListArray = (ReadableArray) ReadableMapUtil.getValueFromMap(map, "summaryLines");
        if (summaryLinesListArray != null) {
            List<SMXReadingReportHBSModel.CommonLineItem> list = new ArrayList<>();
            for (int i = 0; i < summaryLinesListArray.size(); i++) {
                ReadableMap _map = summaryLinesListArray.getMap(i);
                SMXReadingReportHBSModel.CommonLineItem commonLineItem = new SMXReadingReportHBSModel.CommonLineItem();
                commonLineItem.setName(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "name", "").toString());
                commonLineItem.setCount(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "count", "").toString());
                commonLineItem.setValue(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "value", "").toString());
                commonLineItem.setHasMargin((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "hasMargin", false));
                commonLineItem.setInRight((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "inRight", false));
                commonLineItem.setTitle((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "isTitle", false));
                list.add(commonLineItem);
            }
            model.setSummaryLines(list);
        }

        // serviceTypeSaleLines
        ReadableArray serviceTypeSaleLinesListArray = (ReadableArray) ReadableMapUtil.getValueFromMap(map, "serviceTypeSaleLines");
        if (serviceTypeSaleLinesListArray != null) {
            List<SMXReadingReportHBSModel.CommonLineItem> list = new ArrayList<>();
            for (int i = 0; i < serviceTypeSaleLinesListArray.size(); i++) {
                ReadableMap _map = serviceTypeSaleLinesListArray.getMap(i);
                SMXReadingReportHBSModel.CommonLineItem commonLineItem = new SMXReadingReportHBSModel.CommonLineItem();
                commonLineItem.setName(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "name", "").toString());
                commonLineItem.setCount(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "count", "").toString());
                commonLineItem.setValue(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "value", "").toString());
                commonLineItem.setHasMargin((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "hasMargin", false));
                commonLineItem.setInRight((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "inRight", false));
                commonLineItem.setTitle((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "isTitle", false));
                list.add(commonLineItem);
            }
            model.setServiceTypeSaleLines(list);
        }

        // hourlySalesLines
        ReadableArray hourlySalesLinesListArray = (ReadableArray) ReadableMapUtil.getValueFromMap(map, "hourlySalesLines");
        if (hourlySalesLinesListArray != null) {
            List<SMXReadingReportHBSModel.CommonLineItem> list = new ArrayList<>();
            for (int i = 0; i < hourlySalesLinesListArray.size(); i++) {
                ReadableMap _map = hourlySalesLinesListArray.getMap(i);
                SMXReadingReportHBSModel.CommonLineItem commonLineItem = new SMXReadingReportHBSModel.CommonLineItem();
                commonLineItem.setName(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "name", "").toString());
                commonLineItem.setCount(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "count", "").toString());
                commonLineItem.setValue(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "value", "").toString());
                commonLineItem.setHasMargin((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "hasMargin", false));
                commonLineItem.setInRight((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "inRight", false));
                commonLineItem.setTitle((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "isTitle", false));
                list.add(commonLineItem);
            }
            model.setHourlySalesLines(list);
        }

        // breakDownOfTenderLines
        ReadableArray breakDownOfTenderLinesListArray = (ReadableArray) ReadableMapUtil.getValueFromMap(map, "breakDownOfTenderLines");
        if (breakDownOfTenderLinesListArray != null) {
            List<SMXReadingReportHBSModel.CommonLineItem> list = new ArrayList<>();
            for (int i = 0; i < breakDownOfTenderLinesListArray.size(); i++) {
                ReadableMap _map = breakDownOfTenderLinesListArray.getMap(i);
                SMXReadingReportHBSModel.CommonLineItem commonLineItem = new SMXReadingReportHBSModel.CommonLineItem();
                commonLineItem.setName(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "name", "").toString());
                commonLineItem.setCount(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "count", "").toString());
                commonLineItem.setValue(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "value", "").toString());
                commonLineItem.setHasMargin((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "hasMargin", false));
                commonLineItem.setInRight((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "inRight", false));
                commonLineItem.setTitle((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "isTitle", false));
                list.add(commonLineItem);
            }
            model.setBreakDownOfTenderLines(list);
        }

        // cashDrawerLines
        ReadableArray cashDrawerLinesListArray = (ReadableArray) ReadableMapUtil.getValueFromMap(map, "cashDrawerLines");
        if (cashDrawerLinesListArray != null) {
            List<SMXReadingReportHBSModel.CommonLineItem> list = new ArrayList<>();
            for (int i = 0; i < cashDrawerLinesListArray.size(); i++) {
                ReadableMap _map = cashDrawerLinesListArray.getMap(i);
                SMXReadingReportHBSModel.CommonLineItem commonLineItem = new SMXReadingReportHBSModel.CommonLineItem();
                commonLineItem.setName(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "name", "").toString());
                commonLineItem.setCount(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "count", "").toString());
                commonLineItem.setValue(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "value", "").toString());
                commonLineItem.setHasMargin((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "hasMargin", false));
                commonLineItem.setInRight((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "inRight", false));
                commonLineItem.setTitle((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "isTitle", false));
                list.add(commonLineItem);
            }
            model.setCashDrawerLines(list);
        }

        // capitalizedLines
        ReadableArray capitalizedLinesListArray = (ReadableArray) ReadableMapUtil.getValueFromMap(map, "capitalizedLines");
        if (capitalizedLinesListArray != null) {
            List<SMXReadingReportHBSModel.CommonLineItem> list = new ArrayList<>();
            for (int i = 0; i < capitalizedLinesListArray.size(); i++) {
                ReadableMap _map = capitalizedLinesListArray.getMap(i);
                SMXReadingReportHBSModel.CommonLineItem commonLineItem = new SMXReadingReportHBSModel.CommonLineItem();
                commonLineItem.setName(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "name", "").toString());
                commonLineItem.setCount(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "count", "").toString());
                commonLineItem.setValue(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "value", "").toString());
                commonLineItem.setHasMargin((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "hasMargin", false));
                commonLineItem.setInRight((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "inRight", false));
                commonLineItem.setTitle((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "isTitle", false));
                list.add(commonLineItem);
            }
            model.setCapitalizedLines(list);
        }

        // incomeHeadSalesLines
        ReadableArray incomeHeadSalesLinesListArray = (ReadableArray) ReadableMapUtil.getValueFromMap(map, "incomeHeadSalesLines");
        if (incomeHeadSalesLinesListArray != null) {
            List<SMXReadingReportHBSModel.CommonLineItem> list = new ArrayList<>();
            for (int i = 0; i < incomeHeadSalesLinesListArray.size(); i++) {
                ReadableMap _map = incomeHeadSalesLinesListArray.getMap(i);
                SMXReadingReportHBSModel.CommonLineItem commonLineItem = new SMXReadingReportHBSModel.CommonLineItem();
                commonLineItem.setName(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "name", "").toString());
                commonLineItem.setCount(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "count", "").toString());
                commonLineItem.setValue(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "value", "").toString());
                commonLineItem.setHasMargin((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "hasMargin", false));
                commonLineItem.setInRight((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "inRight", false));
                commonLineItem.setTitle((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "isTitle", false));
                list.add(commonLineItem);
            }
            model.setIncomeHeadSalesLines(list);
        }

        // mixSummaryLinesGroups
        ReadableArray mixSummaryLinesGroupsListArray = (ReadableArray) ReadableMapUtil.getValueFromMap(map, "mixSummaryLinesGroups");
        if (mixSummaryLinesGroupsListArray != null) {
            List<List<SMXReadingReportHBSModel.MixSummaryLineItem>> list = new ArrayList<>();
            for (int i = 0; i < mixSummaryLinesGroupsListArray.size(); i++) {
                ReadableArray mixSummaryLinesArray =  mixSummaryLinesGroupsListArray.getArray(i);

                List<SMXReadingReportHBSModel.MixSummaryLineItem> mixSummaryLinesList = new ArrayList<>();
                for (int j = 0; j < mixSummaryLinesArray.size(); j++) {
                    ReadableMap _map = mixSummaryLinesArray.getMap(j);
                    SMXReadingReportHBSModel.MixSummaryLineItem mixSummaryLines = new  SMXReadingReportHBSModel.MixSummaryLineItem();
                    List<String> names = new ArrayList<>();
                    ReadableArray namesArray = (ReadableArray) ReadableMapUtil.getValueFromMap(_map, "names");
                    for (int m = 0; m < namesArray.size(); m++) {
                        names.add(namesArray.getString(m).toString());
                    }
                    mixSummaryLines.setNames(names);
                    mixSummaryLines.setDivider((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "isDivider", false));
                    mixSummaryLines.setTitle((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "isTitle", false));
                    mixSummaryLines.setSingleLine((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "isSingleLine", false));
                    mixSummaryLinesList.add(mixSummaryLines);
                }
                list.add(mixSummaryLinesList);
            }
            model.setMixSummaryLinesGroups(list);
        }

        // totalLines
        ReadableArray totalLinesListArray = (ReadableArray) ReadableMapUtil.getValueFromMap(map, "totalLines");
        if (totalLinesListArray != null) {
            List<SMXReadingReportHBSModel.CommonLineItem> list = new ArrayList<>();
            for (int i = 0; i < totalLinesListArray.size(); i++) {
                ReadableMap _map = totalLinesListArray.getMap(i);
                SMXReadingReportHBSModel.CommonLineItem commonLineItem = new SMXReadingReportHBSModel.CommonLineItem();
                commonLineItem.setName(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "name", "").toString());
                commonLineItem.setCount(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "count", "").toString());
                commonLineItem.setValue(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "value", "").toString());
                commonLineItem.setHasMargin((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "hasMargin", false));
                commonLineItem.setInRight((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "inRight", false));
                commonLineItem.setTitle((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "isTitle", false));
                list.add(commonLineItem);
            }
            model.setTotalLines(list);
        }

        // dateLines
        ReadableArray dateLinesListArray = (ReadableArray) ReadableMapUtil.getValueFromMap(map, "dateLines");
        if (dateLinesListArray != null) {
            List<SMXReadingReportHBSModel.CommonLineItem> list = new ArrayList<>();
            for (int i = 0; i < dateLinesListArray.size(); i++) {
                ReadableMap _map = dateLinesListArray.getMap(i);
                SMXReadingReportHBSModel.CommonLineItem commonLineItem = new SMXReadingReportHBSModel.CommonLineItem();
                commonLineItem.setName(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "name", "").toString());
                commonLineItem.setCount(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "count", "").toString());
                commonLineItem.setValue(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "value", "").toString());
                commonLineItem.setHasMargin((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "hasMargin", false));
                commonLineItem.setInRight((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "inRight", false));
                commonLineItem.setTitle((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "isTitle", false));
                list.add(commonLineItem);
            }
            model.setDateLines(list);
        }

        // footerLines
        ReadableArray footerLinesListArray = (ReadableArray) ReadableMapUtil.getValueFromMap(map, "footerLines");
        if (footerLinesListArray != null) {
            List<SMXReadingReportHBSModel.CommonLineItem> list = new ArrayList<>();
            for (int i = 0; i < footerLinesListArray.size(); i++) {
                ReadableMap _map = footerLinesListArray.getMap(i);
                SMXReadingReportHBSModel.CommonLineItem commonLineItem = new SMXReadingReportHBSModel.CommonLineItem();
                commonLineItem.setName(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "name", "").toString());
                commonLineItem.setCount(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "count", "").toString());
                commonLineItem.setValue(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "value", "").toString());
                commonLineItem.setHasMargin((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "hasMargin", false));
                commonLineItem.setInRight((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "inRight", false));
                commonLineItem.setTitle((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "isTitle", false));
                list.add(commonLineItem);
            }
            model.setFooterLines(list);
        }

        model.setReceiptFontScale(ReadableMapUtil.getDoubleValueFromMapWithDefaultValue(map, "receiptFontScale", 1d).floatValue());
        return model;
    }

}
