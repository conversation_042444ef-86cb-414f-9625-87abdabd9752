package com.storehub.rn.printer.util;

import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.bridge.ReadableMap;
import com.storehub.rn.printer.model.ShiftReportHBSModel;

import java.util.ArrayList;

public class ShiftReportHBSModelUtil {
    public static ShiftReportHBSModel parseMapToModel(ReadableMap map) {
        ShiftReportHBSModel model = new ShiftReportHBSModel();


        model.setAddress(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "address", "").toString());
        model.setActualDrawer(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "actualDrawer", "").toString());
        model.setActualDrawerTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "actualDrawerTitle", "").toString());
        model.setBirAccredited((boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(map, "birAccredited", false));
        model.setBirAccrNo(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "birAccrNo", "").toString());
        model.setCountry(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "country", "").toString());
        model.setCurrency(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "currency", "").toString());
        model.setCompanyName(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "companyName", "").toString());

        ReadableArray cashDrawerSummaryHeaderStringsArray = (ReadableArray) ReadableMapUtil.getValueFromMap(map, "cashDrawerSummaryHeaderStrings");
        if (cashDrawerSummaryHeaderStringsArray != null) {
            ArrayList<String> list = new ArrayList<>();
            for (int i = 0; i < cashDrawerSummaryHeaderStringsArray.size(); i++) {
                list.add(cashDrawerSummaryHeaderStringsArray.getString(i));
            }
            model.setCashDrawerSummaryHeaderStrings(list);
        }

        ReadableMap cashDrawerSummaryMap = (ReadableMap) ReadableMapUtil.getValueFromMap(map, "cashDrawerSummary");
        if (cashDrawerSummaryMap != null) {
            ShiftReportHBSModel.CashDrawerSummary cashDrawerSummary = new ShiftReportHBSModel.CashDrawerSummary();
            cashDrawerSummary.setOpeningAmount(ReadableMapUtil.getValueFromMapWithDefaultValue(cashDrawerSummaryMap, "openingAmount", "").toString());
            cashDrawerSummary.setCashSalesQuantity(ReadableMapUtil.getValueFromMapWithDefaultValue(cashDrawerSummaryMap, "cashSalesQuantity", "").toString());
            cashDrawerSummary.setCashSalesAmount(ReadableMapUtil.getValueFromMapWithDefaultValue(cashDrawerSummaryMap, "cashSalesAmount", "").toString());
            cashDrawerSummary.setCashDepositsQuantity(ReadableMapUtil.getValueFromMapWithDefaultValue(cashDrawerSummaryMap, "cashDepositsQuantity", "").toString());
            cashDrawerSummary.setCashDepositsAmount(ReadableMapUtil.getValueFromMapWithDefaultValue(cashDrawerSummaryMap, "cashDepositsAmount", "").toString());
            cashDrawerSummary.setCashRefundsQuantity(ReadableMapUtil.getValueFromMapWithDefaultValue(cashDrawerSummaryMap, "cashRefundsQuantity", "").toString());
            cashDrawerSummary.setCashRefundsAmount(ReadableMapUtil.getValueFromMapWithDefaultValue(cashDrawerSummaryMap, "cashRefundsAmount", "").toString());
            cashDrawerSummary.setPayOutQuantity(ReadableMapUtil.getValueFromMapWithDefaultValue(cashDrawerSummaryMap, "payOutQuantity", "").toString());
            cashDrawerSummary.setPayOutAmount(ReadableMapUtil.getValueFromMapWithDefaultValue(cashDrawerSummaryMap, "payOutAmount", "").toString());
            cashDrawerSummary.setPayInQuantity(ReadableMapUtil.getValueFromMapWithDefaultValue(cashDrawerSummaryMap, "payInQuantity", "").toString());
            cashDrawerSummary.setPayInAmount(ReadableMapUtil.getValueFromMapWithDefaultValue(cashDrawerSummaryMap, "payInAmount", "").toString());
            model.setCashDrawerSummary(cashDrawerSummary);
        }

        ReadableMap cancelOrDiscountSummaryMap = (ReadableMap) ReadableMapUtil.getValueFromMap(map, "cancelAndDiscountSummary");
        if (cancelOrDiscountSummaryMap != null) {
            ShiftReportHBSModel.CancelAndDiscountSummary cancelOrDiscountSummary = new ShiftReportHBSModel.CancelAndDiscountSummary();
            cancelOrDiscountSummary.setDiscountQuantity(ReadableMapUtil.getValueFromMapWithDefaultValue(cancelOrDiscountSummaryMap, "discountQuantity", "").toString());
            cancelOrDiscountSummary.setDiscountAmount(ReadableMapUtil.getValueFromMapWithDefaultValue(cancelOrDiscountSummaryMap, "discountAmount", "").toString());
            cancelOrDiscountSummary.setCancelQuantity(ReadableMapUtil.getValueFromMapWithDefaultValue(cancelOrDiscountSummaryMap, "cancelQuantity", "").toString());
            cancelOrDiscountSummary.setCancelAmount(ReadableMapUtil.getValueFromMapWithDefaultValue(cancelOrDiscountSummaryMap, "cancelAmount", "").toString());
            model.setCancelAndDiscountSummary(cancelOrDiscountSummary);
        }


        ReadableArray cancelAndDiscountSummaryHeaderStringsArray = (ReadableArray) ReadableMapUtil.getValueFromMap(map, "cancelAndDiscountSummaryHeaderStrings");
        if (cancelAndDiscountSummaryHeaderStringsArray != null) {
            ArrayList<String> list = new ArrayList<>();
            for (int i = 0; i < cancelAndDiscountSummaryHeaderStringsArray.size(); i++) {
                list.add(cancelAndDiscountSummaryHeaderStringsArray.getString(i));
            }
            model.setCancelAndDiscountSummaryHeaderStrings(list);
        }


        ReadableMap cancelAndDiscountSummaryMap = (ReadableMap) ReadableMapUtil.getValueFromMap(map, "cancelAndDiscountSummaryTitles");
        if (cancelAndDiscountSummaryMap != null) {
            ShiftReportHBSModel.CancelAndDiscountSummaryTitles _model = new ShiftReportHBSModel.CancelAndDiscountSummaryTitles();
            _model.setCancelTxnsTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(cancelAndDiscountSummaryMap, "cancelTxnsTitle", "").toString());
            _model.setDiscountTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(cancelAndDiscountSummaryMap, "discountTitle", "").toString());
            model.setCancelAndDiscountSummaryTitles(_model);
        }

        ReadableMap cashDrawerSummaryTitlesMap = (ReadableMap) ReadableMapUtil.getValueFromMap(map, "cashDrawerSummaryTitles");
        if (cashDrawerSummaryTitlesMap != null) {
            ShiftReportHBSModel.CashDrawerSummaryTitles _model = new ShiftReportHBSModel.CashDrawerSummaryTitles();
            _model.setCashDepositsTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(cashDrawerSummaryTitlesMap, "cashDepositsTitle", "").toString());
            _model.setCashRefundsTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(cashDrawerSummaryTitlesMap, "cashRefundsTitle", "").toString());
            _model.setCashSalesTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(cashDrawerSummaryTitlesMap, "cashSalesTitle", "").toString());
            _model.setOpeningAmountTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(cashDrawerSummaryTitlesMap, "openingAmountTitle", "").toString());
            _model.setPayInTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(cashDrawerSummaryTitlesMap, "payInTitle", "").toString());
            _model.setPayOutTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(cashDrawerSummaryTitlesMap, "payOutTitle", "").toString());
            model.setCashDrawerSummaryTitles(_model);
        }


        model.setExpectedDrawer(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "expectedDrawer", "").toString());
        model.setExpectedDrawerTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "expectedDrawerTitle", "").toString());
        model.setGstIdNo(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "gstIdNo", "").toString());
        model.setManager(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "manager", "").toString());

        model.setOpenBy(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "openBy", "").toString());
        model.setCloseBy(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "closeBy", "").toString());
        model.setPrintedBy(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "printedBy", "").toString());
        model.setCashier(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "cashier", "").toString());


        model.setMinNo(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "minNo", "").toString());
        model.setOverShort(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "overShort", "").toString());
        model.setOverShortTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "overShortTitle", "").toString());

        ReadableMap paymentSummaryTitleStringsMap = (ReadableMap) ReadableMapUtil.getValueFromMap(map, "paymentSummaryTitleStrings");
        if (paymentSummaryTitleStringsMap != null) {
            ShiftReportHBSModel.PaymentSummaryTitleStrings _model = new ShiftReportHBSModel.PaymentSummaryTitleStrings();
            _model.setNetTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(paymentSummaryTitleStringsMap, "netTitle", "").toString());
            _model.setRefundsTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(paymentSummaryTitleStringsMap, "refundsTitle", "").toString());
            _model.setSalesTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(paymentSummaryTitleStringsMap, "salesTitle", "").toString());
            model.setPaymentSummaryTitleStrings(_model);
        }

        model.setPhone(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "phone", "").toString());
        model.setPrintedDate(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "printedDate", "").toString());
        model.setPtu(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "ptu", "").toString());


        ReadableArray storeCreditSummaryHeaderStringsArray = (ReadableArray) ReadableMapUtil.getValueFromMap(map, "storeCreditSummaryHeaderStrings");
        if (storeCreditSummaryHeaderStringsArray != null) {
            ArrayList<String> list = new ArrayList<>();
            for (int i = 0; i < storeCreditSummaryHeaderStringsArray.size(); i++) {
                list.add(storeCreditSummaryHeaderStringsArray.getString(i));
            }
            model.setStoreCreditSummaryHeaderStrings(list);
        }

        ReadableArray paymentsArray = (ReadableArray) ReadableMapUtil.getValueFromMap(map, "payments");
        if (paymentsArray != null) {
            ArrayList<ShiftReportHBSModel.Payment> payments = new ArrayList<>();
            for (int i = 0; i < paymentsArray.size(); i++) {
                ReadableMap _map = paymentsArray.getMap(i);
                ShiftReportHBSModel.Payment item = new ShiftReportHBSModel.Payment();
                item.setNetAmount(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "netAmount", "").toString());
                item.setNetQuantity(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "netQuantity", "").toString());
                item.setRefundsAmount(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "refundsAmount", "").toString());
                item.setRefundsQuantity(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "refundsQuantity", "").toString());
                item.setSalesAmount(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "salesAmount", "").toString());
                item.setSalesQuantity(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "salesQuantity", "").toString());
                item.setTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "title", "").toString());
                item.setPaymentId(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "paymentId", "").toString());
                item.setRoundingTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "roundingTitle", "").toString());
                item.setRoundingAmount(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "roundingAmount", "").toString());

                ReadableArray headerStringsArray = (ReadableArray) ReadableMapUtil.getValueFromMap(_map, "headerStrings");
                if (headerStringsArray != null) {
                    ArrayList<String> list = new ArrayList<>();
                    for (int j = 0; j < headerStringsArray.size(); j++) {
                        list.add(headerStringsArray.getString(j));
                    }
                    item.setHeaderStrings(list);
                }
                payments.add(item);
            }
            model.setPayments(payments);
        }

        model.setRegisterId(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "registerId", "").toString());
        model.setReportDate(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "reportDate", "").toString());
        model.setReportTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "reportTitle", "").toString());

        model.setSerialNo(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "serialNo", "").toString());
        model.setShiftCloseTime(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "shiftCloseTime", "").toString());
        model.setStoreName(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "storeName", "").toString());
        model.setShiftOpenTime(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "shiftOpenTime", "").toString());


        ReadableMap storeCreditSummaryTitlesMap = (ReadableMap) ReadableMapUtil.getValueFromMap(map, "storeCreditSummaryTitles");
        if (storeCreditSummaryTitlesMap != null) {
            ShiftReportHBSModel.StoreCreditSummaryTitles _model = new ShiftReportHBSModel.StoreCreditSummaryTitles();
            _model.setDiscountTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(storeCreditSummaryTitlesMap, "discountTitle", "").toString());
            model.setStoreCreditSummaryTitles(_model);
        }


        ReadableMap storeCreditSummaryMap = (ReadableMap) ReadableMapUtil.getValueFromMap(map, "storeCreditSummary");
        if (storeCreditSummaryMap != null) {
            ShiftReportHBSModel.StoreCreditSummary _model = new ShiftReportHBSModel.StoreCreditSummary();
            _model.setDiscountQuantity(ReadableMapUtil.getValueFromMapWithDefaultValue(storeCreditSummaryMap, "discountQuantity", "").toString());
            _model.setDiscountAmount(ReadableMapUtil.getValueFromMapWithDefaultValue(storeCreditSummaryMap, "discountAmount", "").toString());
            model.setStoreCreditSummary(_model);
        }


        ReadableArray shiftReportItemsHeaderStringsArray = (ReadableArray) ReadableMapUtil.getValueFromMap(map, "shiftReportItemsHeaderStrings");
        if (shiftReportItemsHeaderStringsArray != null) {
            ArrayList<String> list = new ArrayList<>();
            for (int i = 0; i < shiftReportItemsHeaderStringsArray.size(); i++) {
                list.add(shiftReportItemsHeaderStringsArray.getString(i));
            }
            model.setShiftReportItemsHeaderStrings(list);
        }

        ReadableMap salesSummaryMap = (ReadableMap) ReadableMapUtil.getValueFromMap(map, "salesSummary");
        if (salesSummaryMap != null) {
            ShiftReportHBSModel.SalesSummary _model = new ShiftReportHBSModel.SalesSummary();
            _model.setNetAmount(ReadableMapUtil.getValueFromMapWithDefaultValue(salesSummaryMap, "netAmount", "").toString());
            _model.setNetQuantity(ReadableMapUtil.getValueFromMapWithDefaultValue(salesSummaryMap, "netQuantity", "").toString());
            _model.setRefundsAmount(ReadableMapUtil.getValueFromMapWithDefaultValue(salesSummaryMap, "refundsAmount", "").toString());
            _model.setRefundsQuantity(ReadableMapUtil.getValueFromMapWithDefaultValue(salesSummaryMap, "refundsQuantity", "").toString());
            _model.setSalesAmount(ReadableMapUtil.getValueFromMapWithDefaultValue(salesSummaryMap, "salesAmount", "").toString());
            _model.setSalesQuantity(ReadableMapUtil.getValueFromMapWithDefaultValue(salesSummaryMap, "salesQuantity", "").toString());
            model.setSalesSummary(_model);
        }

        ReadableMap salesSummaryTitlesMap = (ReadableMap) ReadableMapUtil.getValueFromMap(map, "salesSummaryTitles");
        if (salesSummaryTitlesMap != null) {
            ShiftReportHBSModel.SalesSummaryTitle _model = new ShiftReportHBSModel.SalesSummaryTitle();
            _model.setTotalNetTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(salesSummaryTitlesMap, "totalNetTitle", "").toString());
            _model.setTotalRefundsTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(salesSummaryTitlesMap, "totalRefundsTitle", "").toString());
            _model.setTotalSalesTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(salesSummaryTitlesMap, "totalSalesTitle", "").toString());
            model.setSalesSummaryTitles(_model);
        }

        ReadableArray shiftReportDepositsHeaderStringsArray = (ReadableArray) ReadableMapUtil.getValueFromMap(map, "shiftReportDepositssHeaderStrings");
        if (shiftReportDepositsHeaderStringsArray != null) {
            ArrayList<String> list = new ArrayList<>();
            for (int i = 0; i < shiftReportDepositsHeaderStringsArray.size(); i++) {
                list.add(shiftReportDepositsHeaderStringsArray.getString(i));
            }
            model.setShiftReportDepositssHeaderStrings(list);
        }

        ReadableArray depositsArray = (ReadableArray) ReadableMapUtil.getValueFromMap(map, "depositsSummary");
        if (depositsArray != null) {
            ArrayList<ShiftReportHBSModel.DepositsItem> deposits = new ArrayList<>();
            for (int i = 0; i < depositsArray.size(); i++) {
                ReadableMap _map = depositsArray.getMap(i);
                ShiftReportHBSModel.DepositsItem item = new ShiftReportHBSModel.DepositsItem();
                item.setTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "title", "").toString());
                item.setQuantity(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "quantity", "").toString());
                item.setAmount(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "amount", "").toString());
                deposits.add(item);
            }
            model.setDepositsSummary(deposits);
        }

        model.setTotalDepositTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "totalDepositTitle", "").toString());
        model.setTotalDepositQuantity(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "totalDepositQuantity", "").toString());
        model.setTotalDepositAmount(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "totalDepositAmount", "").toString());

        ReadableArray serviceChargeSummaryHeaderStringsArray = (ReadableArray) ReadableMapUtil.getValueFromMap(map, "serviceChargeSummaryHeaderStrings");
        if (shiftReportItemsHeaderStringsArray != null) {
            ArrayList<String> list = new ArrayList<>();
            for (int i = 0; i < serviceChargeSummaryHeaderStringsArray.size(); i++) {
                list.add(serviceChargeSummaryHeaderStringsArray.getString(i));
            }
            model.setServiceChargeSummaryHeaderStrings(list);
        }

        ReadableMap serviceChargeSummaryTitlesMap = (ReadableMap) ReadableMapUtil.getValueFromMap(map, "serviceChargeSummaryTitles");
        if (serviceChargeSummaryTitlesMap != null) {
            ShiftReportHBSModel.ServiceChargeSummaryTitles _model = new ShiftReportHBSModel.ServiceChargeSummaryTitles();
            _model.setRefundsTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(serviceChargeSummaryTitlesMap, "refundsTitle", "").toString());
            _model.setSalesTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(serviceChargeSummaryTitlesMap, "salesTitle", "").toString());
            model.setServiceChargeSummaryTitles(_model);
        }

        ReadableMap serviceChargeSummaryMap = (ReadableMap) ReadableMapUtil.getValueFromMap(map, "serviceChargeSummary");
        if (serviceChargeSummaryMap != null) {
            ShiftReportHBSModel.ServiceChargeSummary serviceChargeSummary = new ShiftReportHBSModel.ServiceChargeSummary();
            serviceChargeSummary.setSalesQuantity(ReadableMapUtil.getValueFromMapWithDefaultValue(serviceChargeSummaryMap, "salesQuantity", "").toString());
            serviceChargeSummary.setSalesAmount(ReadableMapUtil.getValueFromMapWithDefaultValue(serviceChargeSummaryMap, "salesAmount", "").toString());
            serviceChargeSummary.setRefundsQuantity(ReadableMapUtil.getValueFromMapWithDefaultValue(serviceChargeSummaryMap, "refundsQuantity", "").toString());
            serviceChargeSummary.setRefundsAmount(ReadableMapUtil.getValueFromMapWithDefaultValue(serviceChargeSummaryMap, "refundsAmount", "").toString());
            model.setServiceChargeSummary(serviceChargeSummary);
        }

        ReadableArray taxSummaryHeaderStringsArray = (ReadableArray) ReadableMapUtil.getValueFromMap(map, "taxSummaryHeaderStrings");
        if (shiftReportItemsHeaderStringsArray != null) {
            ArrayList<String> list = new ArrayList<>();
            for (int i = 0; i < taxSummaryHeaderStringsArray.size(); i++) {
                list.add(taxSummaryHeaderStringsArray.getString(i));
            }
            model.setTaxSummaryHeaderStrings(list);
        }

        ReadableArray taxSummaryArrayArray = (ReadableArray) ReadableMapUtil.getValueFromMap(map, "taxSummaryArray");
        if (taxSummaryArrayArray != null) {

            ArrayList<ShiftReportHBSModel.TaxSummary> list = new ArrayList<>();
            for (int i = 0; i < taxSummaryArrayArray.size(); i++) {
                ShiftReportHBSModel.TaxSummary _model = new ShiftReportHBSModel.TaxSummary();
                ReadableMap _map = taxSummaryArrayArray.getMap(i);
                if (_map != null) {
                    _model.setAmount(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "amount", "").toString());
                    _model.setTaxName(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "taxName", "").toString());
                    _model.setTaxRate(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "taxRate", "").toString());
                }
                list.add(_model);
            }
            model.setTaxSummaryArray(list);
        }

        model.setReceiptFontScale(ReadableMapUtil.getDoubleValueFromMapWithDefaultValue(map, "receiptFontScale", 1d).floatValue());
        return model;
    }
}