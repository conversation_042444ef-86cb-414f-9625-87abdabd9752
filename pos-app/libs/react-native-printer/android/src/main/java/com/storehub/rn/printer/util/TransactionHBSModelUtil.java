package com.storehub.rn.printer.util;

import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.bridge.ReadableMap;
import com.storehub.rn.printer.model.TransactionHBSModel;

import java.util.ArrayList;
import java.util.List;

public class TransactionHBSModelUtil {
    public static TransactionHBSModel parseMapToModel(ReadableMap map) {
        TransactionHBSModel model = new TransactionHBSModel();
        model.setKey((String) ReadableMapUtil.getValueFromMapWithDefaultValue(map, "key", ""));
        model.setStoreName(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "storeName", "").toString());
        model.setAddress(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "address", "").toString());
        model.setPhone(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "phone", "").toString());
        model.setReceiptId(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "receiptId", "").toString());
        model.setReceiptDate(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "receiptDate", "").toString());
        model.setReprintDate(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "reprintDate", "").toString());
        model.setEnableCashback((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(map, "enableCashback", false));
        model.setEnablePrintCashback((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(map, "enablePrintCashback", false));
        model.setCashbackUrl(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "cashbackUrl", "").toString());
        model.setEInvoiceUrl(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "eInvoiceUrl", "").toString());
        model.setEInvoiceDescription(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "eInvoiceDescription", "").toString());
        model.setEnablePrintEInvoice((boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(map, "enablePrintEInvoice", false));

        model.setBusiness((ReadableMapUtil.getValueFromMapWithDefaultValue(map, "business", "")).toString());
        model.setEnablePrintQRCode((boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(map, "enablePrintQRCode", false));
        model.setEnablePrintMemberShip((boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(map, "enablePrintMemberShip", false));
        model.setMembershipSource((ReadableMapUtil.getValueFromMapWithDefaultValue(map, "membershipSource", "")).toString());
        model.setMembershipUrl((ReadableMapUtil.getValueFromMapWithDefaultValue(map, "membershipUrl", "")).toString());
        model.setQrCodeDesc((ReadableMapUtil.getValueFromMapWithDefaultValue(map, "qrCodeDesc", "")).toString());

        model.setServiceChargeTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "serviceChargeTitle", "").toString());
        model.setTaxTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "taxTitle", "").toString());
        model.setRoundingTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "roundingTitle", "").toString());
        model.setRounding(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "rounding", "").toString());
        model.setShowOrderSummary((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(map, "showOrderSummary", true));
        model.setRegisterNumber((String) ReadableMapUtil.getValueFromMap(map, "registerNumber"));
        model.setTotalTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "totalTitle", "").toString());
        model.setMinNumber(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "minNumber", "").toString());
        model.setOrderId(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "orderId", "").toString());
        model.setCashierInfo(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "cashierInfo", "").toString());
        model.setReceiptNumber(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "receiptNumber", "").toString());
        model.setSubtotalTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "subtotalTitle", "").toString());
        model.setDiscountTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "discountTitle", "").toString());
        model.setLoyaltyEarned(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "loyaltyEarned", "").toString());
        model.setLoyaltyBalance(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "loyaltyBalance", "").toString());
        model.setLoyaltySpent(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "loyaltySpent", "").toString());
        model.setFooterLabelString(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "footerLabelString", "").toString());
        model.setStorehubPoweredInfo(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "storehubPoweredInfo", "").toString());
        model.setOrderNo(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "orderNo", "").toString());
        model.setReceipt(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "receipt", "").toString());
        model.setCustomerInfo(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "customerInfo", "").toString());
        model.setReceiptTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "receiptTitle", "").toString());
        model.setOrderNoName(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "orderNoName", "").toString());
        model.setServiceCharge(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "serviceCharge", "").toString());
        model.setEarnedTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "earnedTitle", "").toString());
        model.setSpentTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "spentTitle", "").toString());
        model.setBalanceTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "balanceTitle", "").toString());
        model.setCashbackExpirationDesc(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "cashbackExpirationDesc", "").toString());
        model.setCashBackTxt(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "cashBackTxt", "").toString());
        model.setSmallOrderFeeTxt(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "smallOrderFeeTxt", "").toString());
        model.setSmallOrderFeeValue(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "smallOrderFeeValue", "").toString());
        model.setContainerFeeTxt(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "containerFeeTxt", "").toString());
        model.setContainerFeeValue(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "containerFeeValue", "").toString());
        //Pre order
        model.setTransactionTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "transactionTitle", "").toString());
        model.setShowPreorderSummary(((boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(map, "showPreorderSummary", false)));
        model.setDepositAmountTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "depositAmountTitle", "").toString());
        model.setDepositAmount(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "depositAmount", "").toString());
        model.setPickUpDate(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "pickUpDate", "").toString());
        model.setUnPaidBalanceTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "unPaidBalanceTitle", "").toString());
        model.setUnPaidBalance(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "unPaidBalance", "").toString());

        model.setReasonString(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "reasonString", "").toString());
        model.setNoteString(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "noteString", "").toString());
        model.setShowTaxSummary((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(map, "showTaxSummary", false));
        model.setShowReceiptStoreCredit((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(map, "showReceiptStoreCredit", false));
        model.setShowPurchasedItemsDiscount(((boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(map, "showPurchasedItemsDiscount", false)));

        model.setQrCodeAboveInfo((ReadableMapUtil.getValueFromMapWithDefaultValue(map, "qrCodeAboveInfo", "")).toString());
        model.setQrCodeUnderInfo((ReadableMapUtil.getValueFromMapWithDefaultValue(map, "qrCodeUnderInfo", "")).toString());
        model.setReceiptTitleBig((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(map, "isReceiptTitleBig", true));
        model.setReceiptTitlePH((ReadableMapUtil.getValueFromMapWithDefaultValue(map, "receiptTitlePH", "")).toString());
        model.setDefaultLoyaltyRatioNumber((Double) ReadableMapUtil.getValueFromMapWithDefaultValue(map, "defaultLoyaltyRatio", 0d));

        ReadableArray taxSummaryTitleStringArray = (ReadableArray) ReadableMapUtil.getValueFromMap(map, "taxSummaryTitleString");
        if (taxSummaryTitleStringArray != null) {
            ArrayList<String> taxSummaryTitleStringList = new ArrayList<>();
            for (int i = 0; i < taxSummaryTitleStringArray.size(); i++) {
                taxSummaryTitleStringList.add(taxSummaryTitleStringArray.getString(i));
            }
            model.setTaxSummaryTitleString(taxSummaryTitleStringList);
        }

        ReadableArray columnWidthArray = (ReadableArray) ReadableMapUtil.getValueFromMap(map, "columnWidths");
        if (columnWidthArray != null) {
            ArrayList<String> columnWidthList = new ArrayList<>();
            for (int i = 0; i < columnWidthArray.size(); i++) {
                columnWidthList.add(columnWidthArray.getString(i));
            }
            model.setColumnWidths(columnWidthList);
        }

        ReadableArray columnTitleStringArray = (ReadableArray) ReadableMapUtil.getValueFromMap(map, "columnTitleString");
        if (columnTitleStringArray != null) {
            ArrayList<String> columnTitleStringList = new ArrayList<>();
            for (int i = 0; i < columnTitleStringArray.size(); i++) {
                columnTitleStringList.add(columnTitleStringArray.getString(i));
            }
            model.setColumnTitleString(columnTitleStringList);
        }

        ReadableArray a4ColumnTitleStringArray = (ReadableArray) ReadableMapUtil.getValueFromMap(map,
                "a4ColumnTitleString");
        if (a4ColumnTitleStringArray != null) {
            ArrayList<String> a4ColumnTitleStringList = new ArrayList<>();
            for (int i = 0; i < a4ColumnTitleStringArray.size(); i++) {
                a4ColumnTitleStringList.add(a4ColumnTitleStringArray.getString(i));
            }
            model.setA4ColumnTitleString(a4ColumnTitleStringList);
        }

        model.setPreOrderDate(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "preOrderDate", "").toString());
        model.setShippingType(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "shippingType", "").toString());
        model.setChannel(Double.valueOf(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "channel", 0).toString()).intValue());
        model.setShowDeliveryOrContactInfo(((boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(map, "showDeliveryOrContactInfo", false)));
        model.setShippingFeeValue(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "shippingFeeValue", "").toString());
        model.setShippingFeeName(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "shippingFeeName", "").toString());
        model.setTakeawayFeeName(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "takeawayFeeName", "").toString());
        model.setTakeawayFeeValue(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "takeawayFeeValue", "").toString());
        if (map.hasKey("salesChannel") && map.hasKey("takeawayCharge")) {
            model.setChannel(Double.valueOf(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "salesChannel", 0).toString()).intValue());
            model.setTakeawayCharge(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "takeawayCharge", "").toString());
        }

        if (map.hasKey("deliveryOrContactInfo")) {
            ReadableMap contactDetailMap = map.getMap("deliveryOrContactInfo");
            if (contactDetailMap != null) {
                TransactionHBSModel.DeliveryOrContactInfo contactDetail = new TransactionHBSModel.DeliveryOrContactInfo();
                contactDetail.setName(ReadableMapUtil.getValueFromMapWithDefaultValue(contactDetailMap, "name", "").toString());
                contactDetail.setPhone(ReadableMapUtil.getValueFromMapWithDefaultValue(contactDetailMap, "phone", "").toString());
                contactDetail.setAddress(ReadableMapUtil.getValueFromMapWithDefaultValue(contactDetailMap, "address", "").toString());
                contactDetail.setNotes(ReadableMapUtil.getValueFromMapWithDefaultValue(contactDetailMap, "notes", "").toString());
                contactDetail.setNotesName(ReadableMapUtil.getValueFromMapWithDefaultValue(contactDetailMap, "notesName", "").toString());
                contactDetail.setShippingToName(ReadableMapUtil.getValueFromMapWithDefaultValue(contactDetailMap, "shippingToName", "").toString());
                model.setDeliveryOrContactInfo(contactDetail);
            }
        }


        ReadableArray receiptStoreCreditTitleStringArray = (ReadableArray) ReadableMapUtil.getValueFromMap(map, "receiptStoreCreditTitleString");
        if (receiptStoreCreditTitleStringArray != null) {
            ArrayList<String> receiptStoreCreditTitleStringList = new ArrayList<>();
            for (int i = 0; i < receiptStoreCreditTitleStringArray.size(); i++) {
                receiptStoreCreditTitleStringList.add(receiptStoreCreditTitleStringArray.getString(i));
            }
            model.setReceiptStoreCreditTitleString(receiptStoreCreditTitleStringList);
        }

        List<TransactionHBSModel.PurchasedItem> purchasedItems = new ArrayList<>();
        ReadableArray purchasedItemArray = ((ReadableArray) ReadableMapUtil.getValueFromMap(map, "purchasedItems"));

        if (purchasedItemArray != null) {
            for (int i = 0; i < purchasedItemArray.size(); i++) {
                ReadableMap _map = purchasedItemArray.getMap(i);
                TransactionHBSModel.PurchasedItem item = new TransactionHBSModel.PurchasedItem();
                if (_map == null) continue;

                item.setItemName(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "itemName", "").toString());
                item.setPrice(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "price", "").toString());
                item.setQuantity(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "quantity", "").toString());
                item.setSubTotal(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "subTotal", "").toString());
                item.setOptions(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "options", "").toString());
                item.setNotes(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "notes", "").toString());
                item.setDiscount(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "discount", "").toString());
                item.setTotal(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "total", "").toString());
                item.setA4Total(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "a4Total", "").toString());
                item.setSn(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "sn", "").toString());
                item.setItemType(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "itemType", "").toString());
                item.setItemDiscountValue(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "itemDiscountValue", "").toString());
                item.setItemDiscountName(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "itemDiscountName", "").toString());
                item.setEnableTakeaway((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "enableTakeaway", false));
                item.setTakeawayCharge(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "takeawayCharge", "").toString());
                item.setTakeawayTxt(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "takeawayTxt", "").toString());

                List<TransactionHBSModel.Promotions> promotionsItems = new ArrayList<>();

                if (_map.hasKey("promotions")) {
                    ReadableArray promotionItemArray = _map.getArray("promotions");
                    if (promotionItemArray != null) {
                        for (int j = 0; j < promotionItemArray.size(); j++) {
                            ReadableMap _map1 = promotionItemArray.getMap(j);
                            if (_map1 == null) continue;
                            TransactionHBSModel.Promotions promotion = new TransactionHBSModel.Promotions();
                            promotion.setPromotionName(ReadableMapUtil.getValueFromMapWithDefaultValue(_map1, "promotionName", "").toString());
                            promotion.setDiscount(ReadableMapUtil.getValueFromMapWithDefaultValue(_map1, "discount", "").toString());
                            promotionsItems.add(promotion);
                        }
                    }

                }

                item.setPromotions(promotionsItems);
                purchasedItems.add(item);
            }
        }

        model.setPurchasedItems(purchasedItems);


        model.setSubtotal(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "subtotal", "").toString());
        model.setDiscount(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "discount", "").toString());
        model.setA4Discount(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "a4Discount", "").toString());
        model.setTax(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "tax", "").toString());
        model.setTotal(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "total", "").toString());
        model.setShowVatSummary(((boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(map, "showVatSummary", false)));
        model.setMembershipSmallTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "membershipSmallTitle", "").toString());
        model.setMembershipBoildTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "membershipBoildTitle", "").toString());
        model.setMembershipLargeContentTitle1(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "membershipLargeContentTitle1", "").toString());
        model.setMembershipLargeContentTitle2(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "membershipLargeContentTitle2", "").toString());
        model.setMembershipSmallBottomTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "membershipSmallBottomTitle", "").toString());

        List<TransactionHBSModel.PaymentType> payments = new ArrayList<>();
        ReadableArray paymentArray = (ReadableArray) ReadableMapUtil.getValueFromMap(map, "payment");
        if (paymentArray != null) {
            for (int i = 0; i < paymentArray.size(); i++) {
                ReadableMap _map = paymentArray.getMap(i);

                TransactionHBSModel.PaymentType paymentType = new TransactionHBSModel.PaymentType();

                paymentType.setPaymentMethodId(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "paymentMethodId", "").toString());
                paymentType.setPaymentMethodName(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "paymentMethodName", "").toString());
                paymentType.setChangeTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "changeTitle", "").toString());
                paymentType.setChangeValue(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "changeValue", "").toString());

                paymentType.setCashTendered(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "cashTendered", "").toString());
                paymentType.setAmount(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "amount", "").toString());
                paymentType.setRoundedAmount(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "roundedAmount", "").toString());
                paymentType.setSubType(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "subType", "").toString());
                payments.add(paymentType);
            }
        }

        model.setPayment(payments);

        model.setCountry(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "country", "").toString());
        model.setGstIdNo(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "gstIdNo", "").toString());
        model.setCompanyName(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "companyName", "").toString());
        model.setGstIdNo(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "gstIdNo", "").toString());
        model.setSstIdNo(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "sstIdNo", "").toString());

        model.setSstEffective(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "sstEffective", "").toString());

        model.setShowBarcode((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(map, "showBarcode", false));
        model.setBrn(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "brn", "").toString());

        List<TransactionHBSModel.TaxSummary> taxSummaryList = new ArrayList<>();
        if (map.hasKey("taxSummaryItems")) {
            ReadableArray taxSummaryArray = map.getArray("taxSummaryItems");
            if (taxSummaryArray != null) {
                for (int i = 0; i < taxSummaryArray.size(); i++) {
                    ReadableMap _map = taxSummaryArray.getMap(i);
                    TransactionHBSModel.TaxSummary taxSummary = new TransactionHBSModel.TaxSummary();
                    taxSummary.setTitle(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "title", "").toString());
                    taxSummary.setAmount(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "amount", "").toString());
                    taxSummary.setTax(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "tax", "").toString());
                    taxSummaryList.add(taxSummary);
                }
            }

        }
        model.setTaxSummaryItems(taxSummaryList);
        model.setUsingDiscountLayout(((boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(map, "usingDiscountLayout", false)));
        model.setReceiptFontScale(ReadableMapUtil.getDoubleValueFromMapWithDefaultValue(map, "receiptFontScale", 1d).floatValue());

        return model;
    }

}