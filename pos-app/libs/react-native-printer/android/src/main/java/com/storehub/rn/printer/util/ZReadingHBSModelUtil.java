package com.storehub.rn.printer.util;

import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.bridge.ReadableMap;
import com.storehub.rn.printer.model.ZReadingHBSModel;

import java.util.ArrayList;
import java.util.List;

/**
 * Created By: sqq
 * Created Time: 2020-03-19 16:09.
 */
public class ZReadingHBSModelUtil {


    public static ZReadingHBSModel parseMapToModel(ReadableMap map) {
        ZReadingHBSModel model = new ZReadingHBSModel();
        model.setAccredNo(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "accredNo", "").toString());
        model.setBirAccredited(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "birAccredited", "").toString());
        model.setChangeItemAmount(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "changeItemAmount", "").toString());
        model.setChangeItemTrx(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "changeItemTrx", "").toString());
        model.setCloseTime(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "closeTime", "").toString());
        model.setCountry(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "country", "").toString());
        model.setEndORNumber(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "endORNumber", "").toString());
        model.setEndTrxNumber(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "endTrxNumber", "").toString());
        model.setGrossSales(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "grossSales", "").toString());
        model.setLessDiscount(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "lessDiscount", "").toString());
        model.setLessRefund(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "lessRefund", "").toString());
        model.setMinNo(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "minNo", "").toString());
        model.setNetSales(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "netSales", "").toString());
        model.setNewGross(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "newGross", "").toString());
        model.setOldGross(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "oldGross", "").toString());
        model.setNewNet(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "newNet", "").toString());
        model.setOldNet(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "oldNet", "").toString());
        model.setPrintedDate(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "printedDate", "").toString());
        model.setPtu(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "ptu", "").toString());
        model.setPwdDiscount(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "pwdDiscount", "").toString());
        model.setRefundAmount(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "refundAmount", "").toString());
        model.setRefundTrxCount(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "refundTrxCount", "").toString());
        model.setRegisterInfo(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "registerInfo", "").toString());
        model.setRegularDiscount(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "regularDiscount", "").toString());
        model.setSoloParentDiscount(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "soloParentDiscount", "").toString());
        model.setSalesTrxCount(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "salesTrxCount", "").toString());
        model.setScDiscount(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "scDiscount", "").toString());
        model.setAthleteAndCoachDiscountName(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "athleteAndCoachDiscountName", "").toString());
        model.setAthleteAndCoachDiscount(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "athleteAndCoachDiscount", "").toString());
        model.setMedalOfValorDiscountName(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "medalOfValorDiscountName", "").toString());
        model.setMedalOfValorDiscount(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "medalOfValorDiscount", "").toString());
        model.setSerialNo(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "serialNo", "").toString());
        model.setServiceCharge(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "serviceCharge", "").toString());
        model.setServiceChargeTax(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "serviceChargeTax", "").toString());
        model.setStartORNumber(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "startORNumber", "").toString());
        model.setStartTrxNumber(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "startTrxNumber", "").toString());
        model.setStoreName(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "storeName", "").toString());
        model.setTotalDeductedVat(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "totalDeductedVat", "").toString());
        model.setTotalDiscount(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "totalDiscount", "").toString());
        model.setTotalSalesAmount(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "totalSalesAmount", "").toString());
        model.setTotalSalesCount(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "totalSalesCount", "").toString());
        model.setTotalServiceCharge(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "totalServiceCharge", "").toString());
        model.setTotalVatRelatedSales(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "totalVatRelatedSales", "").toString());
        model.setTotalTrxCount(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "totalTrxCount", "").toString());
        model.setVatAbleSales(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "vatAbleSales", "").toString());
        model.setVatAmount(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "vatAmount", "").toString());
        model.setVatExemptSales(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "vatExemptSales", "").toString());
        model.setVatRegNo(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "vatRegNo", "").toString());
        model.setZeroRatedSales(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "zeroRatedSales", "").toString());
        model.setAmusementTax(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "amusementTax", "").toString());
        model.setZCount(ReadableMapUtil.getValueFromMapWithDefaultValue(map, "zCount", "").toString());
        model.setIsRetail((Boolean) ReadableMapUtil.getValueFromMapWithDefaultValue(map, "isRetail", false));

        ReadableArray paymentSummaryListArray = (ReadableArray) ReadableMapUtil.getValueFromMap(map, "paymentSummaryList");
        if (paymentSummaryListArray != null) {
            List<ZReadingHBSModel.Payment> payments = new ArrayList<>();
            for (int i = 0; i < paymentSummaryListArray.size(); i++) {
                ReadableMap _map = paymentSummaryListArray.getMap(i);
                ZReadingHBSModel.Payment payment = new ZReadingHBSModel.Payment();
                payment.setAmount(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "amount", "").toString());
                payment.setName(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "name", "").toString());
                payment.setCount(ReadableMapUtil.getValueFromMapWithDefaultValue(_map, "count", "").toString());
                payments.add(payment);
            }
            model.setPaymentSummaryList(payments);
        }
        model.setReceiptFontScale(ReadableMapUtil.getDoubleValueFromMapWithDefaultValue(map, "receiptFontScale", 1d).floatValue());

        return model;
    }

}
