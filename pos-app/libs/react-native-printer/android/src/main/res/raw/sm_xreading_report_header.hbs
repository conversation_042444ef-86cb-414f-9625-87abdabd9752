<html>

<head>
    <meta charset='utf-8' />
    <title>Print Invoice</title>
    <meta name='viewport' content='width=device-width, initial-scale=1' />
    <style>
        * {
            -webkit-box-sizing: border-box;
            -moz-box-sizing: border-box;
            box-sizing: border-box;
            margin: 0 auto;
            font-size: 16px;
            font-family: Roboto, Ubuntu,
                Helvetica, Arial, sans-serif;
        }

        html,
        body {
            font-size: 18px;
        }

        .text-center {
            text-align: center;
        }

        .text-center .line {
            display: inline-block;
            height: 1px;
            background: #000;
        }

        .pre-line {
            white-space: pre-line;
        }

        .main {
            clear: left;
            padding: 0px 8px;
        }

        .text-right {
            text-align: right;
        }

        .store__name {
            margin: 10px 0;
        }

        .title {
            margin: 10px 0;
        }

        .store__name strong,
        .title strong {
            font-size: 20px;
        }

        .store-info__list {
            margin: 0 0 0px;
            list-style: none;
            padding-left: 0;
        }

        .store-info__item {
            line-height: 1.5em;
        }

        .base-info__item {
            line-height: 1.5em;
            letter-spacing: 1.5px;
        }

        .base-info__item strong {
            font-size: 18px;
        }

        .purchased-title {
            margin-top: 20px;
            padding: 15px 0;
        }

        .bottom-info__wrapper {
            margin-top: 0px;
            padding: 10px 0;
        }

        .purchased-table,
        .purchased-table__row,
        .purchased-table__item {
            border: 0;
        }

        .inner_bottom {
            padding: 30px 0 0 0;
        }

        .purchased-table {
            padding-top: 15px;
        }

        .purchased-table__item-text {
            padding: 4px 0;
            word-break: break-word;
            -webkit-hyphens: auto;
            -ms-hyphens: auto;
            hyphens: auto;
        }

        .purchased-table__item-text-first {
            padding-left: 0;
        }

        .inner_title strong {
            font-size: 20px;
            font-weight: bold;
        }

        .table-bottom-divider {
            padding-top: 2px;
            border-bottom: 2px dashed #000;
            margin: 4px 0;
        }

        .bottom {
            margin: 20px 0px;
            padding: 15px 2%;
            white-space: pre-line;
        }

        .bottom-blank {
            height: 100px;
        }
    </style>
</head>

<body leftmargin='0' marginwidth='0' topmargin='0' marginheight='0' offset='0'>
    <div class='main'>
        <!-- Store Info -->
        <center>
            <div>
                <p class='store__name'><strong>{{businessName}}</strong></p>
                <p class='store__name'><strong>{{storeName}}</strong></p>
                {{#if storeAddress}}
                <p class='store__name'>{{storeAddress}}</p>
                {{/if}}
                <ul class='store-info__list'>
                    {{#if vatRegTin}}
                    <li class='store-info__item pre-line'>{{vatRegTin}}</li>
                    {{/if}}
                    {{#if minNo}}
                    <li class='store-info__item pre-line'>{{minNo}}</li>
                    {{/if}}
                    {{#if serialNo}}
                    <li class='store-info__item pre-line'>{{serialNo}}</li>
                    {{/if}}
                </ul>
                <h2 class='purchased-title text-center'>{{reportTitle}}</h2>
            </div>
        </center>
        <!-- Main -->
        <div>
            <!-- Report Info -->
            <table class='purchased-table' width='100%'>
                <tr class='purchased-table__row'>
                    <td class='purchased-table__item table-bottom-divider' width='60%'></td>
                    <td class='purchased-table__item table-bottom-divider' width='40%'></td>
                </tr>
                <!-- summaryLines -->
                {{#each summaryLines}}
                <tr class='purchased-table__row'>
                    <td class='purchased-table__item {{#if hasMargin}}inner_bottom{{/if}}' width='60%'>
                        <p class='purchased-table__item-text purchased-table__item-text-first'>{{name}}</p>
                    </td>
                    <td class='purchased-table__item {{#if hasMargin}}inner_bottom{{/if}}' width='40%'>
                        <p class='purchased-table__item-text purchased-table__item-text-first'>{{value}}</p>
                    </td>
                </tr>
                {{/each}}

                <tr class='purchased-table__row'>
                    <td class='purchased-table__item table-bottom-divider' width='60%'></td>
                    <td class='purchased-table__item table-bottom-divider' width='40%'></td>
                </tr>
                <!-- serviceTypeSaleLines -->
                {{#each serviceTypeSaleLines}}
                <tr class='purchased-table__row '>
                    <td class='purchased-table__item {{#if hasMargin}}inner_bottom{{/if}}' width='60%'>
                        <p class='purchased-table__item-text purchased-table__item-text-first'>{{name}}</p>
                    </td>
                    <td class='purchased-table__item {{#if hasMargin}}inner_bottom{{/if}}' width='40%'>
                        <p class='purchased-table__item-text purchased-table__item-text-first'>{{value}}</p>
                    </td>
                </tr>
                {{/each}}
            </table>
        </div>
        <!-- End of header -->
    </div>

</body>

</html>