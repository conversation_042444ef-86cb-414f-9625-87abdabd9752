<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>Print Invoice</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        * {
            -webkit-box-sizing: border-box;
            -moz-box-sizing: border-box;
            box-sizing: border-box;
            margin: 0 auto;
            font-size: 18px;
            font-family: Roboto, Ubuntu, Helvetica, Arial, sans-serif;
            ;
        }

        html,
        body {
            font-size: 18px;
        }

        .text-center {
            text-align: center;
        }

        .text-top {
            vertical-align: top;
        }

        .text-right {
            text-align: right;
        }

        .col-6 {
            display: inline-block;
            width: 50%;
        }

        .pre-line {
            white-space: pre-line;
        }

        .main {
            clear: left;
            padding: 40px 8px;
        }

        .logo {
            width: 30%;
            max-width: 120px;
        }

        .store__name {
            margin: 20px 0;
        }

        .title {
            margin: 10px 0;
        }

        .store__name strong,
        .title strong {
            font-size: 24px;
        }

        .store-info__list,
        .base-info__list {
            margin: 0 0 0px;
            list-style: none;
            padding-left: 0;
        }

        .store-info__item,
        .customer-info,
        .base-info__item,
        .base-info__normal,
        .scpwd-Info__text {
            line-height: 1.5em;
        }

        .base-info__item strong {
            font-size: 18px;
        }

        .base-order__item {
            line-height: 1.5em;
            font-size: 34px;
        }

        .base-order__item strong {
            font-size: 44px;
        }

        .customer-info,
        .base-info__wrapper,
        .table-info,
        .purchased-title {
            padding: 15px 0;
        }

        .purchased-table,
        .purchased-table__row,
        .purchased-table__header-item,
        .purchased-table__item,
        .billing-table,
        .billing-table__row,
        .billing-table__item,
        .billing-table__header-item {
            border: 0;
        }

        .purchased-table__header-item {
            border-bottom: 1px dashed #000;
        }

        .purchased-table__header-text,
        .billing-table__header-text {
            margin: 10px 0;
        }

        .purchased-table__item-text,
        .billing-table__item-text {
            padding: 5px 2%;
            word-break: break-word;
            -webkit-hyphens: auto;
            -ms-hyphens: auto;
            hyphens: auto;
        }

        .purchased-table__header-text-first,
        .purchased-table__item-text-first,
        .billing-table__header-text-first,
        .billing-table__item-text-first {
            padding-left: 0;
        }

        .purchased-table__header-text-last,
        .purchased-table__item-text-last,
        .billing-table__header-text-last,
        .billing-table__item-text-last {
            padding-right: 0;
        }

        .table-top-divider {
            padding-bottom: 5px;
            border-top: 1px dashed #000;
        }

        .table-bottom-divider {
            padding-top: 5px;
            border-bottom: 1px dashed #000;
        }

        .bir-scpwd-info__list {
            padding-bottom: 10px;
            padding-left: 0;
            list-style: none;
        }

        .bir-scpwd-info__item {
            padding-top: 10px;
            border-bottom: 1px solid #000;
        }

        .bir-scpwd-info__item-label {
            padding: 10px 0;
            margin-bottom: -1px;
            background-color: #fff;
        }

        .qrcode__wrapper {
            padding: 10px;
            border-bottom: 1px dashed #000;
        }

        .qrcode {
            padding: 5px;
            border: 1px solid #000;
            font-size: 0;
            border-radius: 6px;
        }

        .qrcode-cashback__percentage {
            font-size: 30px;
        }

        .qrcode-cashback__label {
            font-size: 16px;
        }

        .qrcode-image__wrapper {
            position: relative;
        }

        .qrcode-image__wrapper::before {
            content: "";
            display: block;
            width: 100%;
            padding: 50% 0;
        }

        .qrcode-image {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
        }

        .qrcode-info {
            margin: 0;
            padding: 8px;
            white-space: pre-line;
            overflow-wrap: break-word;
        }

         .qrcode-info__80 {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            margin-top: -7px;
            padding: 0 20px;
            background-color: white;
            white-space: nowrap;
            max-width: 90%;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .bottom,
        .pickup-note,
        .extra-info,
        .bir-accr-info,
        .vat-register-footer-info,
        .bar-code-info,
        .powered-by {
            margin: 0;
            padding: 15px 2%;
        }

        .bir-accr-info__list {
            list-style: none;
            padding-left: 0;
        }

        .bir-accr-info__item {
            line-height: 1.5em;
        }

        .bar-code__wrapper {
            width: 100%;
        }

        .bar-code-info {
            width: 100%;
        }

        .delivery-info__wrapper {
            padding: 5px 0px;
            border-bottom: 1px dashed #000;
        }

        .delivery-info__item {
            padding-left: 0;
        }

        .delivery-info__item_blank {
            padding-bottom: 15px;
        }
    </style>
</head>

<body leftmargin="0" marginwidth="0" topmargin="0" marginheight="0" offset="0">
    <div class="main">
        <!-- Store Info -->
        <center>
            {{#if logoImage}}
            <img class="logo" src="{{logoImage}}" alt="">
            {{/if}}
            <div>
                <h1 class="store__name"><strong>{{storeName}}</strong></h1>
                <ul class="store-info__list">
                    <li class="store-info__item pre-line">{{address}}</li>
                    <li class="store-info__item pre-line">{{phone}}</li>
                    <li class="store-info__item pre-line">{{companyName}}</li>
                    {{#if birCompanyName}}
                    <li class="store-info__item pre-line">{{birCompanyName}}</li>
                    {{/if}}
                    {{#if brn}}
                    <li class="store-info__item pre-line">{{brn}}</li>
                    {{/if}}
                    {{#if sstIdNo}}
                    <li class="store-info__item pre-line">{{sstIdNo}}</li>
                    {{/if}}
                    {{#if gstIdNo}}
                    <li class="store-info__item pre-line">{{gstIdNo}}</li>
                    {{/if}}
                    {{#if receipt}}
                    <li class="store-info__item pre-line">{{receipt}}</li>
                    {{/if}}
                </ul>
            </div>
        </center>
        <!-- End of Store Info -->

        <!-- Customer Info -->
        {{#if customerInfo}}
        <div class="customer-info">
            <p class="pre-line">{{customerInfo}}</p>
        </div>
        {{/if}}
        <!-- End of Customer Info -->

        <!-- Basic Info -->
        <div class="base-info__wrapper">
            <h2 class="purchased-title text-center">{{receiptTitle}}</h2>
            <ul class="base-info__list">
                {{#if orderNo}}
                <li class="base-order__item text-center">{{orderNoName}}</li>
                <li class="base-order__item text-center"><strong>{{orderNo}}</strong></li>
                {{/if}}
                {{#if preOrderDate}}
                <li class="base-info__normal pre-line"><strong>{{preOrderDate}}</strong></li>
                {{/if}}
                 {{#if reprintDate}}
                <li class="base-info__item">{{reprintDate}}</li>
                 {{/if}}
                <li class="base-info__item">{{receiptDate}}</li>
                {{#if voidNo}}
                <li class="base-info__item">{{voidNo}}</li>
                {{/if}}
                 {{#if orderId}}
                <li class="base-info__item">{{orderId}}</li>
                 {{/if}}
                {{#if receiptNumber}}
                <li class="base-info__item">{{receiptNumber}}</li>
                {{/if}}
                {{#if orderNumber}}
                <li class="base-info__item">{{orderNumber}}</li>
                {{/if}}
                {{#if minNumber}}
                <li class="base-info__item">{{minNumber}}</li>
                {{/if}}
                {{#if serialNumber}}
                <li class="base-info__item">{{serialNumber}}</li>
                {{/if}}
                {{#if reasonString}}
                <li class="base-info__item">{{reasonString}}</li>
                {{/if}}
                {{#if cashierInfo}}
                <li class="base-info__item">{{cashierInfo}}</li>
                {{/if}}
                <li class="base-info__item">{{registerNumber}}</li>
                {{#if noteString}}
                <li class="base-info__item">{{noteString}}</li>
                {{/if}}
            </ul>
        </div>
        <!-- End of Basic Info -->

        <!-- Table Info -->
        {{#if middleOrderTableTitleWithContent}}
        <div class="table-info text-center">
            <p class="pre-line"><strong>{{middleOrderTableTitleWithContent}}</strong></p>
        </div>
        {{/if}}
        <!-- End of Table Info -->

        <!-- receiptTitlePH -->
        {{#if receiptTitlePH}}
        <div class="text-center">
            <p class="pre-line"><strong>{{receiptTitlePH}}</strong></p>
        </div>
        {{/if}}

        <!-- Main -->
        <div>
            <!-- Purchased List -->
            <table class="purchased-table" width="100%">
                <tr class="purchased-table__row">
                    {{#if columnTitleString}}
                    {{#each columnTitleString}}
                    <td class="purchased-table__header-item" width="{{#if @index}}16%{{else}}52%{{/if}}">
                        <h3 class="purchased-table__header-text {{#if
                                    @index}}text-center{{else}}purchased-table__header-text-first{{/if}} ">
                            <strong>{{this}}</strong>
                        </h3>
                    </td>
                    {{/each}}
                    {{/if}}
                </tr>

                {{#if purchasedItems}}
                {{#each purchasedItems}}
                <tr class="purchased-table__row ">
                    <td class="purchased-table__item text-top" width="52%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            <strong>{{itemName}}</strong>
                        </p>
                    </td>
                    <td class="purchased-table__item text-center text-top" width="16%">
                        <p class="purchased-table__item-text">{{price}}</p>
                    </td>
                    {{#if showPurchasedItemsDiscount}}
                    <td class="purchased-table__item text-center text-top" width="16%">
                        <p class="purchased-table__item-text">{{discount}}</p>
                    </td>
                    {{/if}}
                    <td class="purchased-table__item text-center text-top" width="16%">
                        <p class="purchased-table__item-text">{{quantity}}</p>
                    </td>
                    <td class="purchased-table__item text-center text-top" width="16%">
                        <p class="purchased-table__item-text">{{total}}</p>
                    </td>
                </tr>

                {{#if options}}
                <tr class="purchased-table__row">
                    <td lass="purchased-table__item" colspan="4">
                        <p
                            class="purchased-table__item-text purchased-table__item-text-first purchased-table__item-text-last">
                            {{options}}</p>
                    </td>
                </tr>
                {{/if}}

                {{#if sn}}
                <tr class="purchased-table__row">
                    <td lass="purchased-table__item" colspan="4">
                        <p
                            class="purchased-table__item-text purchased-table__item-text-first purchased-table__item-text-last">
                            {{sn}}</p>
                    </td>
                </tr>
                {{/if}}

                {{#if itemDiscountValue}}
                <tr class="purchased-table__row">
                    <td class="purchased-table__item" colspan="3">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            <i>{{itemDiscountName}}</i>
                        </p>
                    </td>
                    {{#if itemDiscountValue}}
                    <td class="purchased-table__item text-right" colspan="2">
                        <p class="purchased-table__item-text purchased-table__item-text-last">
                            <i>{{itemDiscountValue}}</i>
                        </p>
                    </td>
                    {{/if}}
                </tr>
                {{/if}}

                {{#if promotions}}
                {{#each promotions}}
                <tr class="purchased-table__row">
                    <td class="purchased-table__item" colspan="3">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            <i>*{{promotionName}}</i>
                        </p>
                    </td>
                    <td class="purchased-table__item text-right" colspan="2">
                        <p class="purchased-table__item-text purchased-table__item-text-last">
                            <i>{{discount}}</i>
                        </p>
                    </td>
                </tr>
                {{/each}}
                {{/if}}
                {{#if notes}}
                <tr class="purchased-table__row">
                    <td lass="purchased-table__item" colspan="4">
                        <p
                            class="purchased-table__item-text purchased-table__item-text-first purchased-table__item-text-last">
                            {{notes}}</p>
                    </td>
                </tr>
                {{/if}}
                {{#if enableTakeaway}}
                <tr class="purchased-table__row">
                    <td lass="purchased-table__item" colspan="4">
                        <p
                            class="purchased-table__item-text purchased-table__item-text-first purchased-table__item-text-last">
                            {{takeawayTxt}}</p>
                    </td>
                    <td lass="purchased-table__item" colspan="4">
                        <p
                            class="purchased-table__item-text purchased-table__item-text-first purchased-table__item-text-last">
                            {{takeawayCharge}}</p>
                    </td>
                </tr>
                {{/if}}
                {{/each}}
                {{/if}}

            </table>
            <!-- End of Purchased List -->

            <!-- Billing -->
            <table class="billing-table" width="100%">
                <tr class="billing-table__row">
                    <td class="billing-table__item table-top-divider" width="60%">
                    </td>
                    <td class="billing-table__item table-top-divider" width="40%">
                    </td>
                </tr>

                <tr class="billing-table__row">
                    <td class="billing-table__item text-right" width="60%">
                        <p class="billing-table__item-text">{{subtotalTitle}}</p>
                    </td>
                    <td class="billing-table__item text-right" width="40%">
                        <p class="billing-table__item-text">{{subtotal}}</p>
                    </td>
                </tr>

                {{#if less12vat}}
                <tr class="billing-table__row">
                    <td class="billing-table__item text-right" width="60%">
                        <p class="billing-table__item-text">{{less12vatTitle}}</p>
                    </td>
                    <td class="billing-table__item text-right" width="40%">
                        <p class="billing-table__item-text">{{less12vat}}</p>
                    </td>
                </tr>
                {{/if}}

                {{#if vatOf12}}
                <tr class="billing-table__row">
                    <td class="billing-table__item text-right" width="60%">
                        <p class="billing-table__item-text">{{vatOf12Title}}</p>
                    </td>
                    <td class="billing-table__item text-right" width="40%">
                        <p class="billing-table__item-text">{{vatOf12}}</p>
                    </td>
                </tr>
                {{/if}}

                {{#if discount}}
                <tr class="billing-table__row">
                    <td class="billing-table__item text-right" width="60%">
                        <p class="billing-table__item-text">{{discountTitle}}</p>
                    </td>
                    <td class="billing-table__item text-right" width="40%">
                        <p class="billing-table__item-text">{{discount}}</p>
                    </td>
                </tr>
                {{/if}}

                {{#if adhocDiscount}}
                <tr class="billing-table__row">
                    <td class="billing-table__item text-right" width="60%">
                        <p class="billing-table__item-text">{{adhocDiscountTitle}}</p>
                    </td>
                    <td class="billing-table__item text-right" width="40%">
                        <p class="billing-table__item-text">{{adhocDiscount}}</p>
                    </td>
                </tr>
                {{/if}}

                {{#if seniorDiscount}}
                <tr class="billing-table__row">
                    <td class="billing-table__item text-right" width="60%">
                        <p class="billing-table__item-text">{{seniorDiscountTitle}}</p>
                    </td>
                    <td class="billing-table__item text-right" width="40%">
                        <p class="billing-table__item-text">{{seniorDiscount}}</p>
                    </td>
                </tr>
                {{/if}}

                {{#if pwdDiscount}}
                <tr class="billing-table__row">
                    <td class="billing-table__item text-right" width="60%">
                        <p class="billing-table__item-text">{{pwdDiscountTitle}}</p>
                    </td>
                    <td class="billing-table__item text-right" width="40%">
                        <p class="billing-table__item-text">{{pwdDiscount}}</p>
                    </td>
                </tr>
                {{/if}}

                {{#if athleteAndCoachDiscount}}
                <tr class="billing-table__row">
                    <td class="billing-table__item text-right" width="60%">
                        <p class="billing-table__item-text">{{athleteAndCoachDiscountTitle}}</p>
                    </td>
                    <td class="billing-table__item text-right" width="40%">
                        <p class="billing-table__item-text">{{athleteAndCoachDiscount}}</p>
                    </td>
                </tr>
                {{/if}}
                
                {{#if medalOfValorDiscount}}
                <tr class="billing-table__row">
                    <td class="billing-table__item text-right" width="60%">
                        <p class="billing-table__item-text">{{medalOfValorDiscountTitle}}</p>
                    </td>
                    <td class="billing-table__item text-right" width="40%">
                        <p class="billing-table__item-text">{{medalOfValorDiscount}}</p>
                    </td>
                </tr>
                {{/if}}

                {{#if shippingFeeValue}}
                <tr class="billing-table__row">
                    <td class="billing-table__item text-right" width="60%">
                        <p class="billing-table__item-text">{{shippingFeeName}}</p>
                    </td>
                    <td class="billing-table__item text-right" width="40%">
                        <p class="billing-table__item-text">{{shippingFeeValue}}</p>
                    </td>
                </tr>
                {{/if}}

                {{#if serviceCharge}}
                <tr class="billing-table__row">
                    <td class="billing-table__item text-right" width="60%">
                        <p class="billing-table__item-text">{{serviceChargeTitle}}</p>
                    </td>
                    <td class="billing-table__item text-right" width="40%">
                        <p class="billing-table__item-text">{{serviceCharge}}</p>
                    </td>
                </tr>
                {{/if}}
                {{#if tax}}
                <tr class="billing-table__row">
                    <td class="billing-table__item text-right" width="60%">
                        <p class="billing-table__item-text">{{taxTitle}}</p>
                    </td>
                    <td class="billing-table__item text-right" width="40%">
                        <p class="billing-table__item-text">{{tax}}</p>
                    </td>
                </tr>
                {{/if}}


                {{#if rounding}}
                <tr class="billing-table__row">
                    <td class="billing-table__item text-right" width="60%">
                        <p class="billing-table__item-text">{{roundingTitle}}</p>
                    </td>
                    <td class="billing-table__item text-right" width="40%">
                        <p class="billing-table__item-text">{{rounding}}</p>
                    </td>
                </tr>
                {{/if}}

                {{#if showOrderSummary}}
                <tr class="billing-table__row">
                    <td class="billing-table__item table-bottom-divider" width="60%">
                    </td>
                    <td class="billing-table__item table-bottom-divider" width="40%">
                    </td>
                </tr>
                {{/if}}
            </table>

            <!-- Total -->
            <table class="billing-table" width="100%">
                <tr class="billing-table__row">
                    <td class="billing-table__item text-right" width="60%">
                        <p class="billing-table__item-text">{{totalTitle}}</p>
                    </td>
                    <td class="billing-table__item text-right" width="40%">
                        <p class="billing-table__item-text">{{total}}</p>
                    </td>
                </tr>

                <tr class="billing-table__row">
                    <td class="billing-table__item table-bottom-divider" width="60%">
                    </td>
                    <td class="billing-table__item table-bottom-divider" width="40%">
                    </td>
                </tr>
            </table>
            <!-- End of Billing -->

            <!-- Payment -->
            {{#if payment}}
            <table class="billing-table" width="100%">
                <tr class="billing-table__row">
                    <td class="billing-table__item" width="60%">
                    </td>
                    <td class="billing-table__item" width="40%">
                    </td>
                </tr>
                {{#each payment}}
                <tr class="billing-table__row">
                    <td class="billing-table__item text-right" width="60%">
                        <p class="billing-table__item-text">{{paymentMethodName}}</p>
                    </td>
                    <td class="billing-table__item text-right" width="40%">
                        <p class="billing-table__item-text">{{amount}}</p>
                    </td>
                </tr>
                {{#if changeValue}}
                <tr class="billing-table__row">
                    <td class="billing-table__item text-right" width="60%">
                        <p class="billing-table__item-text">{{changeTitle}}</p>
                    </td>
                    <td class="billing-table__item text-right" width="40%">
                        <p class="billing-table__item-text">{{changeValue}}</p>
                    </td>
                </tr>
                {{/if}}
                {{/each}}

                <tr class="billing-table__row">
                    <td class="billing-table__item table-bottom-divider" width="60%">
                    </td>
                    <td class="billing-table__item table-bottom-divider" width="40%">
                    </td>
                </tr>
            </table>
            {{/if}}
            <!-- End of Payment -->

            <!--   Summary     -->
            {{#if showTaxSummary}}
            <table class="purchased-table" width="100%">
                <tr class="purchased-table__row">
                    {{#if taxSummaryTitleString}}
                    {{#each taxSummaryTitleString}}
                    <td class="purchased-table__item" width="{{#if @index}}30%{{else}}40%{{/if}}">
                        <h3 class="purchased-table__header-text {{#if
                                        @index}}text-right{{else}}purchased-table__header-text-first{{/if}} ">
                            <strong>{{this}}</strong>
                        </h3>
                    </td>
                    {{/each}}
                    {{/if}}
                </tr>
                {{#if taxSummaryItems}}
                {{#each taxSummaryItems}}
                <tr class="purchased-table__row">
                    <td class="purchased-table__item" colspan="1">
                        <p class="purchased-table__item-text">{{title}}</p>
                    </td>
                    <td class="purchased-table__item text-right" colspan="1">
                        <p class="purchased-table__item-text">{{amount}}</p>
                    </td>
                    <td class="purchased-table__item text-right" colspan="1">
                        <p class="purchased-table__item-text">{{tax}}</p>
                    </td>
                </tr>
                {{/each}}
                <tr class="purchased-table__row">
                    <td class="billing-table__item table-bottom-divider" colspan="1">
                    </td>
                    <td class="billing-table__item table-bottom-divider" colspan="1">
                    </td>
                    <td class="billing-table__item table-bottom-divider" width="100%">
                    </td>
                </tr>
                {{/if}}

            </table>

            {{/if}}
            <!--   End of Summary     -->

            <!--   loyalty     -->
            {{#if showReceiptStoreCredit}}
            <table class="purchased-table" width="100%">

                <tr class="purchased-table__row">
                    {{#if receiptStoreCreditTitleString}}
                    {{#each receiptStoreCreditTitleString}}
                    <td class="purchased-table__header-item" width="50%">
                        <h3 class="purchased-table__header-text {{#if
                                        @index}}text-right{{else}}purchased-table__header-text-first{{/if}} ">
                            <strong>{{this}}</strong>
                        </h3>
                    </td>
                    {{/each}}
                    {{/if}}
                </tr>
                <tr class="purchased-table__row">
                    <td class="purchased-table__item" width="50%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">{{earnedTitle}}</p>
                    </td>
                    <td class="purchased-table__item text-right" width="50%">
                        <p class="purchased-table__item-text purchased-table__item-text-last">{{loyaltyEarned}}</p>
                    </td>
                </tr>

                <tr class="purchased-table__row">
                    <td class="purchased-table__item" width="50%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">{{balanceTitle}}</p>
                    </td>
                    <td class="purchased-table__item text-right" width="50%">
                        <p class="purchased-table__item-text purchased-table__item-text-last">{{loyaltyBalance}}</p>
                    </td>
                </tr>

                <tr class="purchased-table__row">
                    <td class="purchased-table__item" width="50%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">{{spentTitle}}</p>
                    </td>
                    <td class="purchased-table__item text-right" width="50%">
                        <p class="purchased-table__item-text purchased-table__item-text-last">{{loyaltySpent}}</p>
                    </td>
                </tr>
            </table>
            {{/if}}

            {{#if showReceiptStoreCredit}}
                <div style="display: flex; align-items: center; margin-bottom: 10px;">
                  <hr style="flex: 1; border: none; border-top: 2px dotted #000;">
                  {{#if cashbackExpirationDesc}}
                  <span style="padding: 0 10px;">{{cashbackExpirationDesc}}</span>
                  {{/if}}
                  <hr style="flex: 1; border: none; border-top: 2px dotted #000;">
                </div>
             {{/if}}
            <!--   End of loyalty     -->


            <!-- Sales Info -->
            {{#if showVatSummary}}
            <table class="billing-table" width="100%">
                <tr class="billing-table__row">
                    <td class="billing-table__item" width="60%">
                    </td>
                    <td class="billing-table__item" width="40%">
                    </td>
                </tr>

                {{#if amountOutStanding}}
                <tr class="billing-table__row">
                    <td class="billing-table__item" width="60%">
                        <p class="billing-table__item-text">{{amountOutStandingTitle}}</p>
                    </td>
                    <td class="billing-table__item" width="40%">
                        <p class="billing-table__item-text">{{amountOutStanding}}</p>
                    </td>
                </tr>
                {{/if}}

                {{#if vatableSales}}
                <tr class="billing-table__row">
                    <td class="billing-table__item" width="60%">
                        <p class="billing-table__item-text">{{vatableSalesTitle}}</p>
                    </td>
                    <td class="billing-table__item" width="40%">
                        <p class="billing-table__item-text">{{vatableSales}}</p>
                    </td>
                </tr>
                {{/if}}

                {{#if vatAmount}}
                <tr class="billing-table__row">
                    <td class="billing-table__item" width="60%">
                        <p class="billing-table__item-text">{{vatAmountTitle}}</p>
                    </td>
                    <td class="billing-table__item" width="40%">
                        <p class="billing-table__item-text">{{vatAmount}}</p>
                    </td>
                </tr>
                {{/if}}

                {{#if vatExemptSales}}
                <tr class="billing-table__row">
                    <td class="billing-table__item" width="60%">
                        <p class="billing-table__item-text">{{vatExemptSalesTitle}}</p>
                    </td>
                    <td class="billing-table__item" width="40%">
                        <p class="billing-table__item-text">{{vatExemptSales}}</p>
                    </td>
                </tr>
                {{/if}}

                {{#if zeroRatedSales}}
                <tr class="billing-table__row">
                    <td class="billing-table__item" width="60%">
                        <p class="billing-table__item-text">{{zeroRatedSalesTitle}}</p>
                    </td>
                    <td class="billing-table__item" width="40%">
                        <p class="billing-table__item-text">{{zeroRatedSales}}</p>
                    </td>
                </tr>
                {{/if}}
                {{#if amusementTax}}
                <tr class="billing-table__row">
                    <td class="billing-table__item" width="60%">
                        <p class="billing-table__item-text">{{amusementTaxTitle}}</p>
                    </td>
                    <td class="billing-table__item" width="40%">
                        <p class="billing-table__item-text">{{amusementTax}}</p>
                    </td>
                </tr>
                {{/if}}
                <tr class="billing-table__row">
                    <td class="billing-table__item table-bottom-divider" width="60%">
                    </td>
                    <td class="billing-table__item table-bottom-divider" width="40%">
                    </td>
                </tr>
            </table>
            {{/if}}
            <!-- End of Sales Info -->

            <!-- BIR SC PWD Info -->
            {{#if birSCPWDInfoFormat}}
            <table class="billing-table" width="100%">
                <tr class="billing-table__row">
                    <td class="billing-table__item">
                    </td>
                </tr>

                <tr class="billing-table__row">
                    <td class="billing-table__item">
                        <ul class="bir-scpwd-info__list">
                            {{#each birSCPWDInfoFormat}}
                            <li class="bir-scpwd-info__item">
                                <label class="bir-scpwd-info__item-label">{{this}}</label>
                            </li>
                            {{/each}}
                        </ul>
                    </td>
                </tr>

                <tr class="billing-table__row">
                    <td class="billing-table__item table-bottom-divider">
                    </td>
                </tr>
            </table>
            {{/if}}
            <!-- End of BIR SC PWD Info -->

            <!-- Shipping Info -->
            {{#if onlineChannelNotesContent}}
            <table class="billing-table" width="100%">
                <tr class="billing-table__row">
                    <td class="billing-table__item">
                    </td>
                </tr>

                <tr class="billing-table__row">
                    <td class="billing-table__item">
                        <h3 class="billing-table__item-text-first">{{onlineChannelNotesTitle}}</h3>
                        <p class="billing-table__item-text billing-table__item-text-first pre-line">
                            {{onlineChannelNotesContent}}</p>
                    </td>
                </tr>

                <tr class="billing-table__row">
                    <td class="billing-table__item table-bottom-divider">
                    </td>
                </tr>
            </table>
            {{/if}}
            <!-- End of Shipping Info -->

            <!-- Pick Up Info -->
            {{#if onlineOrderNoteContent}}
            <table class="billing-table" width="100%">
                <tr class="billing-table__row">
                    <td class="billing-table__item">
                    </td>
                </tr>

                <tr class="billing-table__row">
                    <td class="billing-table__item">
                        <h3 class="billing-table__item-text-first">{{onlinePickUpNoteTitle}}</h3>
                        <p class="billing-table__item-text billing-table__item-text-first pre-line">
                            {{onlineOrderNoteContent}}</p>
                    </td>
                </tr>

                <tr class="billing-table__row">
                    <td class="billing-table__item table-bottom-divider">
                    </td>
                </tr>
            </table>
            {{/if}}

            {{#if showDeliveryOrContactInfo}}
            <div class="delivery-info__wrapper text-left">
                <ul class="store-info__list">
                    {{#if deliveryOrContactInfo.shippingToName}}
                    <li class="delivery-info__item_blank pre-line">
                        <strong>{{deliveryOrContactInfo.shippingToName}}</strong>
                    </li>
                    {{/if}}
                    {{#if deliveryOrContactInfo.name}}
                    <li class="delivery-info__item pre-line">{{deliveryOrContactInfo.name}}</li>
                    {{/if}}
                    {{#if deliveryOrContactInfo.phone}}
                    <li class="delivery-info__item pre-line">{{deliveryOrContactInfo.phone}}</li>
                    {{/if}}
                    {{#if deliveryOrContactInfo.address}}
                    <li class="delivery-info__item_blank pre-line">{{deliveryOrContactInfo.address}}</li>
                    {{/if}}
                    {{#if deliveryOrContactInfo.notes}}
                    <li class="delivery-info__item_blank pre-line"><strong>{{deliveryOrContactInfo.notesName}}</strong>
                    </li>
                    <li class="delivery-info__item pre-line">{{deliveryOrContactInfo.notes}}</li>
                    {{/if}}
                </ul>
            </div>
            {{/if}}

            <!-- End of Pick Up Info -->

            <!-- Beep Cashback Info -->
            <!-- <table class="billing-table" width="100%">
          <tr class="billing-table__row">
            <td class="billing-table__item table-top-divider" colspan="2">
            </td>
            <td class="billing-table__item table-top-divider" colspan="2">
            </td>
          </tr>

          <tr class="billing-table__row">
            <td class="billing-table__header-item" width="50%">
              <h3 class="billing-table__header-text billing-table__header-text-first">
                <strong>
                  Beep Cashback
                </strong>
              </h3>
            </td>
            <td class="billing-table__header-item text-right" width="50%">
              <h3 class="billing-table__header-text billing-table__header-text-last"><strong>Amount</strong></h3>
            </td>
          </tr>

          <tr class="billing-table__row">
            <td class="billing-table__item" width="35%">
              <p class="billing-table__item-text billing-table__header-text-first">Earned</p>
            </td>
            <td class="billing-table__item text-right" width="30%">
              <p class="billing-table__item-text billing-table__item-text-last">5.72</p>
            </td>
          </tr>

          <tr class="billing-table__row">
            <td class="billing-table__item" width="35%">
              <p class="billing-table__item-text billing-table__header-text-first">Spent</p>
            </td>
            <td class="billing-table__item text-right" width="30%">
              <p class="billing-table__item-text billing-table__item-text-last">0.00</p>
            </td>
          </tr>

          <tr class="billing-table__row">
            <td class="billing-table__item" width="35%">
              <p class="billing-table__item-text billing-table__header-text-first">Balance</p>
            </td>
            <td class="billing-table__item text-right" width="30%">
              <p class="billing-table__item-text billing-table__item-text-last">42.43</p>
            </td>
          </tr>

          <tr class="billing-table__item">
            <td class="billing-table__item table-bottom-divider" colspan="2">
            </td>
            <td class="billing-table__item table-bottom-divider" colspan="2">
            </td>
          </tr>
        </table> -->
            <!-- End of Beep Cashback Info -->

            <!-- QR code -->
        {{#if enablePrintQRCode}}

        <div class="{{#if isPrinterPaperWidth80}}qrcode__wrapper__80 {{else}} qrcode__wrapper {{/if}} text-center">
            <strong>
                <p class="{{#if enablePrintMemberShip}} qrcode-info {{else}} {{#if isPrinterPaperWidth80}}qrcode-info__80 {{else}} qrcode-info {{/if}} {{/if}}">{{qrCodeAboveInfo}}</p>
            </strong>
            <div class="qrcode">
                <table class="qrcode-table" width="100%">
                    <tr class="qrcode-table__item">
                        <td class="qrcode-table__item text-center "
                            width="{{#if isPrinterPaperWidth58}}40%{{else}}50%{{/if}}">
                             {{#if enablePrintCashback}}
                                <p class="qrcode-cashback__percentage">{{defaultLoyaltyRatio}}%</p>
                                <label class="qrcode-cashback__label">{{cashBackTxt}}</label>
                            {{else}}
                               <svg width="80" height="80" viewBox="0 0 30 30" fill="none">
                                    <path d="M15 0C6.71564 0 0 6.71591 0 15.0002C0 23.2842 6.71564 30 15 30C23.2837 30 29.9997 23.2842 29.9997 15.0002C29.9997 6.71598 23.2837 0 15 0Z" fill="#F2F2F3"/>
                                    <path d="M14.9999 5C9.47707 5 5 9.47687 5 15C5 20.5229 9.47707 25 14.9999 25C20.5229 25 25 20.5229 25 15C25 9.47687 20.5229 5 14.9999 5Z" fill="#303030"/>
                                    <path d="M20.2397 13.0603L18.5053 18.375C18.5053 18.375 17.25 17.625 15 17.625C12.75 17.625 11.4947 18.375 11.4947 18.375L9.76031 13.0608C9.74783 13.0246 9.74642 12.9856 9.75624 12.9486C9.76607 12.9117 9.7867 12.8785 9.81549 12.8533C9.84428 12.8282 9.87992 12.8122 9.91785 12.8074C9.95579 12.8026 9.99429 12.8092 10.0284 12.8264L12.532 14.0742C12.5754 14.0959 12.6254 14.1004 12.672 14.0868C12.7186 14.0733 12.7583 14.0427 12.7833 14.0011L14.835 10.5937C14.8522 10.5657 14.8764 10.5426 14.9051 10.5265C14.9338 10.5105 14.9662 10.5021 14.9991 10.5021C15.032 10.5021 15.0643 10.5105 15.093 10.5265C15.1217 10.5426 15.1459 10.5657 15.1631 10.5937L17.2148 14.0034C17.2399 14.0448 17.2796 14.0753 17.326 14.0888C17.3724 14.1024 17.4223 14.098 17.4656 14.0765L19.9702 12.8278C20.0042 12.8104 20.0426 12.8036 20.0805 12.8082C20.1185 12.8127 20.1542 12.8285 20.1831 12.8534C20.212 12.8784 20.2328 12.9114 20.2429 12.9482C20.253 12.9851 20.2519 13.0241 20.2397 13.0603Z" fill="white"/>
                                    <path d="M20.43 12.5714C20.3454 12.4976 20.2408 12.4507 20.1295 12.4365C20.0181 12.4223 19.9051 12.4415 19.8047 12.4917L17.4511 13.6636L15.4861 10.3997C15.4356 10.3159 15.3643 10.2466 15.2791 10.1985C15.194 10.1505 15.0978 10.1252 15 10.1252C14.9022 10.1252 14.806 10.1505 14.7209 10.1985C14.6357 10.2466 14.5644 10.3159 14.5139 10.3997L12.5489 13.665L10.1962 12.4931C10.0961 12.4434 9.98348 12.4243 9.87254 12.4383C9.7616 12.4524 9.65728 12.4989 9.57266 12.572C9.48804 12.6451 9.42689 12.7415 9.3969 12.8493C9.36691 12.957 9.3694 13.0712 9.40406 13.1775L11.1384 18.4912C11.1559 18.5446 11.1851 18.5934 11.2239 18.634C11.2627 18.6746 11.3101 18.7059 11.3627 18.7257C11.4153 18.7455 11.4716 18.7533 11.5275 18.7484C11.5835 18.7435 11.6376 18.7261 11.6859 18.6975C11.6977 18.6905 12.8962 18 15 18C17.1037 18 18.3023 18.6905 18.3131 18.697C18.3615 18.7259 18.4157 18.7435 18.4718 18.7486C18.5279 18.7536 18.5844 18.746 18.6371 18.7262C18.6898 18.7065 18.7374 18.6751 18.7764 18.6344C18.8153 18.5937 18.8446 18.5448 18.862 18.4912L20.5964 13.1789C20.6321 13.0725 20.6353 12.958 20.6056 12.8498C20.576 12.7416 20.5149 12.6447 20.43 12.5714ZM18.2812 17.8514C17.7187 17.6128 16.5975 17.25 15 17.25C13.4025 17.25 12.2812 17.6128 11.7187 17.8514L10.252 13.3594L12.3647 14.4126C12.4926 14.4757 12.6394 14.4886 12.7763 14.4488C12.9132 14.4089 13.0303 14.3193 13.1044 14.1975L15 11.0465L16.8956 14.1965C16.9698 14.3182 17.0867 14.4077 17.2235 14.4475C17.3603 14.4873 17.507 14.4745 17.6348 14.4117L19.748 13.3594L18.2812 17.8514ZM17.2439 16.3514C17.2286 16.4381 17.1832 16.5167 17.1158 16.5734C17.0483 16.63 16.9631 16.6611 16.875 16.6612C16.853 16.6612 16.8311 16.6593 16.8094 16.6556C15.6116 16.45 14.3875 16.45 13.1897 16.6556C13.0917 16.6729 12.9909 16.6505 12.9094 16.5935C12.8279 16.5364 12.7724 16.4494 12.7552 16.3514C12.7379 16.2534 12.7602 16.1526 12.8173 16.0711C12.8743 15.9896 12.9614 15.9341 13.0594 15.9169C14.3434 15.6962 15.6557 15.6962 16.9397 15.9169C17.0375 15.934 17.1244 15.9893 17.1815 16.0704C17.2387 16.1516 17.2613 16.2522 17.2444 16.35L17.2439 16.3514Z" fill="white"/>
                                </svg>
                                <p class="qrcode-cashback__label">{{qrCodeDesc}}</p>
                            {{/if}}
                        </td>
                        <td class="qrcode-table__item" width="{{#if isPrinterPaperWidth58}}60%{{else}}50%{{/if}}">
                            <div class="qrcode-image__wrapper">
                                <img class="{{#if isPrinterPaperWidth58}}qrcode-image_12C{{else}}qrcode-image{{/if}}"
                                    src="_qrCodeImage_" alt="">
                            </div>
                        </td>
                    </tr>
                </table>
            </div>
            <strong>
                <p class="qrcode-info margin-bottom-10">{{qrCodeUnderInfo}}</p>
            </strong>

        </div>
        {{/if}}
        <!-- End of QR code -->
        </div>
        <!-- End of Main -->

        <!-- Bottom -->
        <div class="bottom text-center">
            {{#if preOrderNotes}}
            <p class="pickup-note"><strong>{{preOrderNotes}}</strong></p>
            {{/if}}

            <!-- Extra Info -->
            <p class="extra-info"><strong>{{footerLabelString}}</strong></p>
            <!-- End of Extra Info -->

            <!-- Bir Accr Info -->
            {{#if birAccrInfo}}
            <div class="bir-accr-info">
                <p class="pre-line">{{birAccrInfo}}</p>
                <ul class="bir-accr-info__list">
                    <li class="bir-accr-info__item">{{accrNumber}}</li>
                    <li class="bir-accr-info__item">{{ptuNumber}}</li>
                    <li class="bir-accr-info__item">{{dateIssueNumber}}</li>
                </ul>
            </div>
            {{/if}}
            <!-- End of Bir Accr Info -->

            <!-- VAT Register Footer Info -->
            {{#if vatRegisterFooterInfo}}
            <div class="vat-register-footer-info">
                <p class="pre-line"><strong>{{vatRegisterFooterInfo}}</strong></p>
            </div>
            {{/if}}
            <!-- End of VAT Register Footer Info -->

            <!-- Bar Code -->
            {{#if showBarcode}}
            <div class="bar-code__wrapper">
                <img class="bar-code-info" src="_barCodeImage_" alt="">
            </div>
            {{/if}}
            <!-- End of Bar Code -->

            <!-- Power by -->
            {{#if storehubPoweredInfo}}
            <p class="powered-by">
                POWERED BY <strong>STOREHUB.COM</strong> CLOUD POS
            </p>
            {{/if}}
            <!-- End of Power by -->
        </div>
        <!-- End of Bottom -->
    </div>

</body>

</html>