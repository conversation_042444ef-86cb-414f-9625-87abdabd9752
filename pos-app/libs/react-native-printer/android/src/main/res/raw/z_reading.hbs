<html>

  <head>
    <meta charset='utf-8' />
    <title>Print Invoice</title>
    <meta name='viewport' content='width=device-width, initial-scale=1' />
    <style>
      * {
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
        margin: 0 auto;
        font-size: 18px;
        font-family: Roboto, Ubuntu, Helvetica, Arial, sans-serif;
      }

      html,
      body {
        font-size: 18px;
      }

      .text-center {
        text-align: center;
      }

      .text-center .line {
        display: inline-block;
        height: 1px;
        background: #000;
      }

      .text-right {
        text-align: right;
      }

      .col-6 {
        display: inline-block;
        width: 50%;
      }

      .pre-line {
        white-space: pre-line;
      }

      .main {
        clear: left;
        padding: 40px 8px;
      }

      .logo {
        width: 30%;
        max-width: 120px;
      }

      .store__name {
        margin: 20px 0;
      }

      .title {
        margin-top: 20px;
      }

      .report_date {
        margin-bottom: 20px;
      }

      .store__name strong,
      .title strong {
        font-size: 18px;
      }

      .store-info__list,
      .base-info__list {
        margin: 0 0 0px;
        list-style: none;
        padding-left: 0;
      }

      .store-info__item,
      .customer-info,
      .base-info__item,
      .scpwd-Info__text {
        line-height: 1.5em;
      }

      .base-info__item strong {
        font-size: 18px;
      }

      .customer-info,
      .base-info__wrapper,
      .table-info,
      .purchased-title {
        padding: 15px 0;
      }

      .purchased-table,
      .purchased-table__row,
      .purchased-table__header-item,
      .purchased-table__item,
      .billing-table,
      .billing-table__row,
      .billing-table__item,
      .billing-table__header-item {
        border: 0;
      }

      .purchased-table__header-item {
        border-bottom: 1px dashed #000;
      }

      .purchased-table__header-text,
      .billing-table__header-text {
        margin: 10px 2%;
      }

      .purchased-table__item-text,
      .billing-table__item-text {
        padding: 4px 0;
        word-break: break-word;
        -webkit-hyphens: auto;
        -ms-hyphens: auto;
        hyphens: auto;
      }

      .purchased-table__header-text-first,
      .purchased-table__item-text-first,
      .billing-table__header-text-first,
      .billing-table__item-text-first {
        padding-left: 0;
      }

      .purchased-table__header-text-last,
      .purchased-table__item-text-last,
      .billing-table__header-text-last,
      .billing-table__item-text-last {
        padding-right: 0;
      }

      .table-bottom-divider {
        padding-top: 5px;
        border-bottom: 3px dashed #000;
      }

      .table-bottom-holder {
        padding-top: 5px;
      }

      .bir-scpwd-info__list {
        padding-bottom: 10px;
        padding-left: 0;
        list-style: none;
      }

      .bir-scpwd-info__item {
        padding-top: 10px;
        border-bottom: 1px solid #000;
      }

      .bir-scpwd-info__item-label {
        padding: 10px 0;
        margin-bottom: -1px;
        background-color: #fff;
      }

      .qrcode__wrapper {
        padding: 10px;
        border-bottom: 1px dashed #000;
      }

      .qrcode {
        padding: 5px;
        border: 1px solid #000;
        font-size: 0;
        border-radius: 6px;
      }

      .bottom,
      .pickup-note,
      .extra-info,
      .bir-accr-info,
      .vat-register-footer-info,
      .bar-code-info,
      .powered-by {
        margin: 0;
        padding: 15px 2%;
      }

      .bir-accr-info__list {
        list-style: none;
        padding-left: 0;
      }

      .bir-accr-info__item {
        line-height: 1.5em;
      }

      .bar-code__wrapper {
        width: 100%;
      }

      .bar-code-info {
        width: 100%;
      }

      .text-line-dashed {
        position: relative;
        height: 1.875rem;
        line-height: 1.875rem;
        margin: 0 2.5px auto;
        text-align: center;
      }

      .text-line-dashed i {
        display: block;
        height: 1px;
        position: absolute;
        top: 0.9rem;
        width: 100%;
        border-top: 1px dashed #000;
      }

      .text-line-dashed p {
        display: inline-block;
        color: #000;
        background: #fff;
        padding: 0 0.2rem;
        text-align: center;
        margin: 0 auto;
        position: relative;
        z-index: 2;
      }

      .bottom-blank {
        height: 100px;
      }
    </style>
  </head>

  <body leftmargin='0' marginwidth='0' topmargin='0' marginheight='0' offset='0'>
    <div class='main'>
      <div class='base-info__wrapper'>
        <p class='base-info__item'>{{printedDate}}</p>
      </div>
      <!-- Store Info -->
      <center>
        <div>
          <p class='store__name'>{{storeName}}</p>
          <ul class='store-info__list'>
            <li class='store-info__item pre-line'>{{vatRegNo}}</li>
            <li class='store-info__item pre-line'>{{accredNo}}</li>
            <li class='store-info__item pre-line'>{{serialNo}}</li>
            <li class='store-info__item pre-line'>{{minNo}}</li>
            <li class='store-info__item pre-line'>{{ptu}}</li>
          </ul>
          <p class='title'><strong>Z Reading Report</strong></p>
          <p class='report_date'>{{closeTime}}</p>

        </div>
      </center>
      <!-- End of Store Info -->
      <div class='base-info__wrapper'>
        <ul class='base-info__list'>
          <li class='base-info__item'>{{registerInfo}}</li>
          <li class='base-info__item'>{{zCount}}</li>
        </ul>
      </div>
      <!-- Main -->
      <div>
        <!-- Sales Info -->
        <table class='purchased-table' width='100%'>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item' width='60%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>Gross Sales</p>
            </td>
            <td class='purchased-table__item' width='40%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>{{grossSales}}</p>
            </td>
          </tr>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item' width='60%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>Less Discount</p>
            </td>
            <td class='purchased-table__item' width='40%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>{{lessDiscount}}</p>
            </td>
          </tr>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item' width='60%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>Less Refund</p>
            </td>
            <td class='purchased-table__item' width='40%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>{{lessRefund}}</p>
            </td>
          </tr>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item' width='60%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>VAT from SC/PWD/Others
              </p>
            </td>
            <td class='purchased-table__item' width='40%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>{{totalDeductedVat}}</p>
            </td>
          </tr>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item table-bottom-divider' width='60%'>
            </td>
            <td class='purchased-table__item table-bottom-divider' width='40%'>
            </td>
          </tr>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item' width='60%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>Net Sales</p>
            </td>
            <td class='purchased-table__item' width='40%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>{{netSales}}</p>
            </td>
          </tr>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item table-bottom-divider' width='60%'>
            </td>
            <td class='purchased-table__item table-bottom-divider' width='40%'>
            </td>
          </tr>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item' width='60%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>Collections</p>
            </td>
            <td class='purchased-table__item' width='40%'>
            </td>
          </tr>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item table-bottom-holder' width='60%'>
            </td>
            <td class='purchased-table__item table-bottom-holder' width='40%'>
            </td>
          </tr>
        </table>

        <table class='purchased-table' width='100%'>
          {{#if paymentSummaryList}}
            {{#each paymentSummaryList}}
              <tr class='purchased-table__row'>
                <td class='purchased-table__item' width='50%'>
                  <p class='purchased-table__item-text purchased-table__item-text-first'>{{name}}</p>
                </td>
                <td class='purchased-table__item' width='15%'>
                  <p class='purchased-table__item-text purchased-table__item-text-first'>{{count}}</p>
                </td>
                <td class='purchased-table__item' width='35%'>
                  <p class='purchased-table__item-text purchased-table__item-text-first'>{{amount}}</p>
                </td>
              </tr>
            {{/each}}
          {{/if}}
          <tr class='purchased-table__row'>
            <td class='purchased-table__item table-bottom-divider' width='50%'>
            </td>
            <td class='purchased-table__item table-bottom-divider' width='15%'>
            </td>
            <td class='purchased-table__item table-bottom-divider' width='35%'>
            </td>
          </tr>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item' width='50%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>Total Collection</p>
            </td>
            <td class='purchased-table__item' width='15%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>{{totalSalesCount}}</p>
            </td>
            <td class='purchased-table__item' width='35%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>{{totalSalesAmount}}</p>
            </td>
          </tr>
        </table>
        <!-- End of Sales Info -->
        <!-- OtherInfo -->
        <div class='text-line-dashed'>
          <i></i>
          <p>Other Info</p>
        </div>
        <table class='purchased-table' width='100%'>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item' width='60%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>VATable Sales</p>
            </td>
            <td class='purchased-table__item' width='40%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>{{vatAbleSales}}</p>
            </td>
          </tr>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item' width='60%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>VAT Amount</p>
            </td>
            <td class='purchased-table__item' width='40%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>{{vatAmount}}</p>
            </td>
          </tr>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item' width='60%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>VAT-Exempt Sales</p>
            </td>
            <td class='purchased-table__item' width='40%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>{{vatExemptSales}}</p>
            </td>
          </tr>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item' width='60%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>Zero-Rated Sales</p>
            </td>
            <td class='purchased-table__item' width='40%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>{{zeroRatedSales}}</p>
            </td>
          </tr>
          {{#if amusementTax}}
            <tr class='purchased-table__row'>
              <td class='purchased-table__item' width='60%'>
                <p class='purchased-table__item-text purchased-table__item-text-first'>Amusement Tax</p>
              </td>
              <td class='purchased-table__item' width='40%'>
                <p class='purchased-table__item-text purchased-table__item-text-first'>{{amusementTax}}</p>
              </td>
            </tr>
          {{/if}}
          <tr class='purchased-table__row'>
            <td class='purchased-table__item table-bottom-divider' width='60%'>
            </td>
            <td class='purchased-table__item table-bottom-divider' width='40%'>
            </td>
          </tr>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item' width='60%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>Total</p>
            </td>
            <td class='purchased-table__item' width='40%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>{{totalVatRelatedSales}}
              </p>
            </td>
          </tr>
        </table>
        <!-- End of OtherInfo -->
        <!-- Service Charge -->
        <div class='text-line-dashed'>
          <i></i>
          <p>Service Charge</p>
        </div>
        <table class='purchased-table' width='100%'>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item' width='60%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>
                Service Charge Amount</p>
            </td>
            <td class='purchased-table__item' width='40%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>{{serviceCharge}}</p>
            </td>
          </tr>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item' width='60%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>
                Service Charge Tax</p>
            </td>
            <td class='purchased-table__item' width='40%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>{{serviceChargeTax}}</p>
            </td>
          </tr>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item table-bottom-divider' width='60%'>
            </td>
            <td class='purchased-table__item table-bottom-divider' width='40%'>
            </td>
          </tr>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item' width='60%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>Total Service Charge</p>
            </td>
            <td class='purchased-table__item' width='40%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>{{totalServiceCharge}}</p>
            </td>
          </tr>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item table-bottom-holder' width='60%'>
            </td>
            <td class='purchased-table__item table-bottom-holder' width='40%'>
            </td>
          </tr>
        </table>
        <!-- End of serviceCharge -->
        <!-- Discount -->
        <div class='text-line-dashed'>
          <i></i>
          <p>Discount</p>
        </div>
        <table class='purchased-table' width='100%'>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item' width='60%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>SC Discount</p>
            </td>
            <td class='purchased-table__item' width='40%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>{{scDiscount}}</p>
            </td>
          </tr>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item' width='60%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>PWD Discount</p>
            </td>
            <td class='purchased-table__item' width='40%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>{{pwdDiscount}}</p>
            </td>
          </tr>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item' width='60%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>{{athleteAndCoachDiscountName}}</p>
            </td>
            <td class='purchased-table__item' width='40%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>{{athleteAndCoachDiscount}}</p>
            </td>
          </tr>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item' width='60%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>{{medalOfValorDiscountName}}</p>
            </td>
            <td class='purchased-table__item' width='40%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>{{medalOfValorDiscount}}</p>
            </td>
          </tr>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item' width='60%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>Regular Discount</p>
            </td>
            <td class='purchased-table__item' width='40%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>{{regularDiscount}}</p>
            </td>
          </tr>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item' width='60%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>Solo Parent Discount</p>
            </td>
            <td class='purchased-table__item' width='40%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>{{soloParentDiscount}}</p>
            </td>
          </tr>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item table-bottom-divider' width='60%'>
            </td>
            <td class='purchased-table__item table-bottom-divider' width='40%'>
            </td>
          </tr>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item' width='60%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>Total Discount</p>
            </td>
            <td class='purchased-table__item' width='40%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>{{totalDiscount}}</p>
            </td>
          </tr>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item table-bottom-divider' width='60%'>
            </td>
            <td class='purchased-table__item table-bottom-divider' width='40%'>
            </td>
          </tr>
        </table>
        <!-- End of Discount -->
        <!-- OTHER INFO -->
        <table class='purchased-table' width='100%'>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item' width='60%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>Beg.SI</p>
            </td>
            <td class='purchased-table__item' width='40%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>{{startORNumber}}</p>
            </td>
          </tr>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item' width='60%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>End.SI</p>
            </td>
            <td class='purchased-table__item' width='40%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>{{endORNumber}}</p>
            </td>
          </tr>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item' width='60%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>Start Trx NO.</p>
            </td>
            <td class='purchased-table__item' width='40%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>{{startTrxNumber}}</p>
            </td>
          </tr>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item' width='60%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>End Trx NO.</p>
            </td>
            <td class='purchased-table__item' width='40%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>{{endTrxNumber}}</p>
            </td>
          </tr>
        </table>
        <table class='purchased-table' width='100%'>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item' width='60%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>Trx Count</p>
            </td>
            <td class='purchased-table__item' width='40%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>{{totalTrxCount}}</p>
            </td>
          </tr>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item' width='60%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>Sales Count</p>
            </td>
            <td class='purchased-table__item' width='40%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>{{salesTrxCount}}</p>
            </td>
          </tr>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item' width='60%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>Refund Count</p>
            </td>
            <td class='purchased-table__item' width='40%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>{{refundTrxCount}}</p>
            </td>
          </tr>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item' width='60%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>Refund Amount</p>
            </td>
            <td class='purchased-table__item' width='40%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>{{refundAmount}}</p>
            </td>
          </tr>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item' width='60%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>Change Item Trx</p>
            </td>
            <td class='purchased-table__item' width='40%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>{{changeItemTrx}}</p>
            </td>
          </tr>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item' width='60%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>Change Item Amount</p>
            </td>
            <td class='purchased-table__item' width='40%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>{{changeItemAmount}}</p>
            </td>
          </tr>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item' width='60%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>Old Gross</p>
            </td>
            <td class='purchased-table__item' width='40%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>{{oldGross}}</p>
            </td>
          </tr>
          <tr class='purchased-table__row'>
            <td class='purchased-table__item' width='60%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>Accumulated Grand Total Sales</p>
            </td>
            <td class='purchased-table__item' width='40%'>
              <p class='purchased-table__item-text purchased-table__item-text-first'>{{newGross}}</p>
            </td>
          </tr>
          {{#if oldNet}}
            <tr class='purchased-table__row'>
              <td class='purchased-table__item' width='60%'>
                <p class='purchased-table__item-text purchased-table__item-text-first'>Old Net Sales</p>
              </td>
              <td class='purchased-table__item' width='40%'>
                <p class='purchased-table__item-text purchased-table__item-text-first'>{{oldNet}}</p>
              </td>
            </tr>
          {{/if}}
          {{#if newNet}}
            <tr class='purchased-table__row'>
              <td class='purchased-table__item' width='60%'>
                <p class='purchased-table__item-text purchased-table__item-text-first'>Accumulated Net Sales</p>
              </td>
              <td class='purchased-table__item' width='40%'>
                <p class='purchased-table__item-text purchased-table__item-text-first'>{{newNet}}</p>
              </td>
            </tr>
          {{/if}}
        </table>
        <!-- End of OTHER INFO -->

      </div>
      <!-- End of Main -->
      {{#if isPrinterPaperWidth58}}
        <div class='bottom-blank'></div>
      {{/if}}
    </div>

  </body>

</html>