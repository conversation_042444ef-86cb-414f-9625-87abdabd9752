<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="Address">Address</string>
    <string name="Phone">Phone</string>
    <string name="Shift_Open_Time">Shift Open Time</string>
    <string name="Shift_Close_Time">Shift Close Time</string>
    <string name="Register">Register</string>
    <string name="Report_Date">Report Date</string>
    <string name="Manager">Manager</string>
    <string name="Z_Sales_Shift_Report">Z Sales Shift Report</string>
    <string name="Sales_Summary">Sales Summary</string>
    <string name="Total_Sales">Total Sales</string>
    <string name="Total_Refunds">Total Refunds</string>
    <string name="Total_Net">Total Net</string>
    <string name="Sales">Sales</string>
    <string name="Deposits">Deposits</string>
    <string name="Refunds">Refunds</string>
    <string name="Net">Net</string>
    <string name="Cancels_OR_Dis_Summary">Cancels/Dis Summary</string>
    <string name="Discount">Discount</string>
    <string name="Cancel_Txns">Cancel Txns</string>
    <string name="Expected_Drawer">Expected Drawer</string>
    <string name="Actual_Drawer">Actual Drawer</string>
    <string name="Over_OR_Short">Over/Short</string>
    <string name="Qty">Qty</string>
    <string name="Amount">Amount</string>
    <string name="Store_Credit_Summary">Store Credit Summary</string>
    <string name="Tax">Tax</string>
    <string name="Tax_Summary">Tax Summary</string>
    <string name="Rounding">Rounding</string>
    <string name="Tax_Rate">Tax Rate</string>
    <string name="Cash_Drawer_Summary">Cash Drawer Summary</string>
    <string name="Opening_Amount">Opening Amount</string>
    <string name="Cash_Sales">Cash Sales</string>
    <string name="Cash_Deposits">Cash Deposits</string>
    <string name="Cash_Refunds">Cash Refunds</string>
    <string name="Pay_Out">Pay Out</string>
    <string name="Pay_In">Pay In</string>
    <string name="Receipt_Date">Receipt Date</string>
    <string name="Pre_Order_Date">Pre-Order Date</string>
    <string name="Date">Date</string>
    <string name="No">No</string>
    <string name="POS">POS</string>
    <string name="Receipt">Receipt</string>
    <string name="Cashier">Cashier</string>
    <string name="Item">Item</string>
    <string name="Price">Price</string>
    <string name="Subtotal">Subtotal</string>
    <string name="TOTAL">TOTAL</string>
    <string name="CASH">CASH</string>
    <string name="CHANGE">CHANGE</string>
    <string name="Company_Name">Company Name</string>
    <string name="Tax_included">Tax Invoice/VAT included</string>
    <string name="Tax_ABB_included">Tax Invoice (ABB) / VAT included</string>
    <string name="Tax_ID_No">Tax ID No</string>
    <string name="Returns_Receipt">RETURNS RECEIPT</string>
    <string name="Gst_Summary">GST Summary</string>
    <string name="Sst_Summary">SST Summary</string>
    <string name="Invoice">INVOICE</string>
    <string name="Sst_ID_No">SST ID No</string>
    <string name="GST">GST</string>
    <string name="Order">Order</string>
    <string name="Table">Table</string>
    <string name="Service_Charge">Service Charge</string>
    <string name="BRN">Business Registration Number</string>
    <string name="Total_Sales_Exc_Tax">Total Sales (Exc. Tax)</string>
    <string name="Total_Discount">Total Discount</string>
    <string name="Store_Credit">Store Credit</string>
    <string name="Earned">Earned</string>
    <string name="Balance">Balance</string>
    <string name="SN">S/N: </string>
    <string name="Item_Discount">Item Discount</string>
    <string name="Spent">Spent</string>
    <string name="Beep_Order">Beep Order</string>
    <string name="Pre_order">Pre-order Receipt</string>
    <string name="CASH_DEPOSIT">CASH DEPOSIT</string>
    <string name="UNPAID_BALANCE">UNPAID BALANCE</string>
    <string name="Cashback">Cashback</string>
    <string name="Note">Note</string>
    <string name="Service_Charge_Summary">Service Charge Summary</string>
    <string name="Rounding_Summary">Rounding Summary</string>
    <string name="Delivery">Delivery</string>
    <string name="Take_Away">Take Away</string>
    <string name="ShippingTo">Shipping To</string>
    <string name="Name">Name:</string>
    <string name="Phone_Number">Phone Number:</string>
    <string name="address">Address:</string>
    <string name="Address_Details">Address Details:</string>
    <string name="Notes">Notes:</string>
    <string name="Shipping_Fee">Delivery Fee</string>

    <string name="Success">Success</string>
    <string name="Null">Null</string>

    <string name="Printer_Service_Binder_Null">Printer Service Binder is Null</string>
    <string name="Printer_Service_Util_Null">iMin Printer Util is Null</string>
    <string name="Printer_ID_Null">Printer ID is Null</string>
    <string name="Printer_Business_Type_Null">Printer business type is Null</string>
    <string name="Printing_Params_Error">Params error</string>
    <string name="Printing_Occurred_Error">Printing occurred error</string>
    <string name="Printer_Search_Error">Printer search occurred error</string>
    <string name="Printer_Search_Timeout">Printer search timeout</string>
    <string name="Open_Drawer_Params_Error">Params error</string>
    <string name="Open_Drawer_Error">Open cash drawer occurred error</string>
    <string name="Open_Drawer_Timeout">Open cash drawer timeout</string>
    <string name="Buzz_Params_Error">Params error</string>
    <string name="Buzz_Occurred_Error">Buzz occurred error</string>
    <string name="Buzz_Timeout">Buzz timeout</string>

    <string name="Printer_Timeout">Printing Timeout</string>
    <string name="Printer_Connection_Timeout">Printer connection timeout</string>
    <string name="Printer_Connection_Error">Printer connection error</string>
    <string name="Printer_Printing_Timeout">Printer printing timeout</string>
    <string name="SunMi_Printer_Service_Null">SunMi Printer Service is null</string>
    <string name="Printer_Unknown_Error">unknown error</string>
    <string name="Printer_Printing_Error">Printer printing occurred error</string>
    <string name="Thanks_For_Coming">Thank you for coming!</string>
    <string name="Total_Bill">Total Bill</string>
    <string name="You_Paid">You Paid</string>
    <string name="Your_Change">Your Change</string>
    <string name="Total_Remaining">Total Remaining</string>
    <!--    <string name="shift_open_time">SHIFT OPEN TIME:</string>-->
    <!--    <string name="shift_close_time">SHIFT CLOSE TIME:</string>-->
    <!--    <string name="register">REGISTER #:</string>-->
    <string name="store">STORE</string>
    <string name="open_by">OPEN BY</string>
    <string name="close_by">CLOSE BY</string>
    <!--    <string name="sales_summary">SALES SUMMARY</string>-->
    <!--    <string name="tax_summary">TAX SUMMARY</string>-->
    <!--    <string name="tax_rate">TAX RATE</string>-->
    <!--    <string name="cancel_discount_summary">CANCELS / DISC. SUMMARY</string>-->
    <!--    <string name="cancel_txns">Cancel Txns</string>-->
    <!--    <string name="discount">Discount</string>-->
    <!--    <string name="pay_out">Pay Out</string>-->
    <string name="quantity">QUANTITY</string>
    <!--    <string name="cash_sales">Cash Sales</string>-->
    <!--    <string name="pay_in">Pay In</string>-->
    <!--    <string name="cash_refunds">Cash Refunds</string>-->
    <!--    <string name="cash_deposits">Cash Deposits</string>-->
    <!--    <string name="expected_drawer">Expected Drawer</string>-->
    <!--    <string name="actual_drawer">Actual Drawer</string>-->
    <string name="over_short">Over/Short</string>
    <!--    <string name="opening_amount">Opening Amount</string>-->
    <!--    <string name="cash_drawer_summary">CASH DRAWER SUMMARY</string>-->

    <string moduleConfig="true" name="CodePushServerUrl" translatable="false">https://dev.storehubdeoloy.dpdns.org</string>
</resources>

