//
//  LanConnection.swift
//  RNPrinter
//
//  Created by <PERSON> on 2024/12/13.
//

import Foundation
import Network

private actor CompletionGuard {
    private var hasCompleted = false

    func complete(with operation: () throws -> Void) throws {
        guard !hasCompleted else { return }
        hasCompleted = true
        try operation()
    }
}

final class NewLanConnection: LanXPrinterConnectable {
    // MARK: - Connection Properties
    private var connection: NWConnection?
    private var hostAddress: String?
    private var hostPort: UInt16?
    
    // MARK: - Timeout Settings
    private let connectTimeout: TimeInterval = 30
    private var printingTimeout: TimeInterval = 20
    
    // MARK: - Errors
    private enum PrinterCommunicationError: Error {
        // connection
        case connectionInvalid
        case connectionFailed(NWError)
        case connectionCancelled
        case connectionTimeout
        // write
        case writeError(NWError)
        
        case printingTimeout
        case printingConvertDataError
    }
    
    //MARK: - Initialization
    public init() { }
    
    public convenience init(printingTimeout: TimeInterval) {
        self.init()
        self.printingTimeout = printingTimeout > 0 ? printingTimeout : 20
    }
    
    // MARK: - Connection Management
    private func ensureConnected() async throws {
        guard let hostAddress = hostAddress,
            let port = NWEndpoint.Port(rawValue: hostPort ?? 0)
        else {
            throw PrinterCommunicationError.connectionInvalid
        }
        
        if connection?.state == .ready { return }
        
        try await withThrowingTaskGroup(of: Void.self) { group in
            let connection = NWConnection(
                host: .init(hostAddress),
                port: port,
                using: .tcp
            )
            self.connection = connection
            
            group.addTask {
                // TODO: use AsyncStream
                try await withCheckedThrowingContinuation { continuation in
                    let completionGuard = CompletionGuard()
                    connection.stateUpdateHandler = { state in
                        guard !Task.isCancelled else { return }

                        Task {
                            switch state {
                            case .ready:
                                try? await completionGuard.complete {
                                    printWithTime("[NewLanConnection] state ready")
                                    connection.stateUpdateHandler = nil
                                    continuation.resume()
                                }
                            case .failed(let error):
                                try? await completionGuard.complete {
                                    printWithTime("[NewLanConnection] state failed")
                                    connection.stateUpdateHandler = nil
                                    continuation.resume(throwing: PrinterCommunicationError.connectionFailed(error))
                                }
                            case .cancelled:
                                try? await completionGuard.complete {
                                    printWithTime("[NewLanConnection] state cancelled")
                                    connection.stateUpdateHandler = nil
                                    continuation.resume(throwing: PrinterCommunicationError.connectionCancelled)
                                }
                            default:
                                break
                            }
                        }
                    }
                    connection.start(queue: .global())
                }
            }
            
            group.addTask {
                try await Task.sleep(nanoseconds: UInt64(self.connectTimeout * Double(NSEC_PER_SEC)))
                throw PrinterCommunicationError.connectionTimeout
            }
            
            do {
                try await group.next()
                group.cancelAll()
            } catch {
                performDisconnect()
                group.cancelAll()
                throw error
            }
        }
    }
    
    private func write(_ data: Data) async throws {
        guard let connection = connection else {
            throw PrinterCommunicationError.connectionInvalid
        }
        
        try await withCheckedThrowingContinuation {
            (continuation: CheckedContinuation<Void, Error>) in
            let completionGuard = CompletionGuard()
            connection.send(
                content: data,
                completion: .contentProcessed { error in
                    Task {
                        try? await completionGuard.complete {
                            if let error = error {
                                continuation.resume(throwing: PrinterCommunicationError.writeError(error))
                            } else {
                                continuation.resume()
                            }
                        }
                    }
                })
        }
    }
    
    private func performDisconnect() {
        if let conn = connection {
            if conn.state != .cancelled {
                conn.cancel()
            }
            connection = nil
        }
        printWithTime("[NewLanConnection] Disconnected")
    }
    
    func isConnected() -> Bool {
        connection?.state == .ready
    }
    
    // MARK: - Public Interface
    func sendDataWith(
        task: PrinterTask,
        ip: String,
        port: UInt16,
        success: @escaping (() -> Void),
        fail: @escaping ((WrappedPrinterError) -> Void)
    ) {
        Task {
            var callbackCalled = false
            let successWrapper = {
                callbackCalled = true
                success()
            }
            let failWrapper: (WrappedPrinterError) -> Void = { err in
                callbackCalled = true
                fail(err)
            }
            defer {
                if !callbackCalled {
                    callbackCalled = true
                    printWithTime("[NewLanConnection] Warning: no callback called, forcing fail")
                    DispatchQueue.main.async {
                        failWrapper(WrappedPrinterError(errorCode: .PRINTING_OCCURRED_ERROR))
                    }
                }
            }
            self.hostAddress = ip
            self.hostPort = port
            printWithTime("[NewLanConnection] Start printing task: \(ip):\(port)")

            let startTime = Date()

            do {
                try await self.ensureConnected()
                printWithTime("[NewLanConnection] Connection established")
                
                try await withThrowingTaskGroup(of: Void.self) { group in
                    // 打印任务
                    group.addTask {
                        printWithTime("[NewLanConnection] Start sending data")
                        for generator in task.dataGenerators {
                            let data = try await withCheckedThrowingContinuation {
                                (continuation: CheckedContinuation<Data, Error>) in
                                let completionGuard = CompletionGuard()
                                let next = generator.next()
                                next { data in
                                    Task {
                                        try? await completionGuard.complete {
                                            if let data = data {
                                                continuation.resume(returning: data)
                                            } else {
                                                continuation.resume(throwing: PrinterCommunicationError.printingConvertDataError)
                                            }
                                        }
                                    }
                                }
                            }
                            try await self.write(data)
                        }
                        let endTime = Date()
                        let timeInterval = endTime.timeIntervalSince(startTime)
                        printWithTime("[NewLanConnection] All data sent successfully, time taken: \(String(format: "%.2f", timeInterval))s")
                    }
                    
                    // 超时任务
                    group.addTask {
                        printWithTime("[NewLanConnection] Timeout monitor started: \(self.printingTimeout)s")
                        try await Task.sleep(nanoseconds: UInt64(self.printingTimeout * Double(NSEC_PER_SEC)))
                        printWithTime("[NewLanConnection] Printing timeout triggered")
                        self.performDisconnect()
                        throw PrinterCommunicationError.printingTimeout
                    }
                    
                    // 等待任意任务完成
                    try await group.next()
                    group.cancelAll()
                    await MainActor.run { successWrapper() }
                }
            } catch let error as PrinterCommunicationError {
                let endTime = Date()
                let timeInterval = endTime.timeIntervalSince(startTime)
                printWithTime("[NewLanConnection] Connection error after \(String(format: "%.2f", timeInterval))s: \(error)")
                await MainActor.run {
                    let printerError: WrappedPrinterError = mapPrinterCommunicationErrorToPrinterError(error)
                    failWrapper(printerError)
                }
            } catch {
                let endTime = Date()
                let timeInterval = endTime.timeIntervalSince(startTime)
                printWithTime("[NewLanConnection] Unexpected error after \(String(format: "%.2f", timeInterval))s: \(error)")
                // 10110
                let printerError: WrappedPrinterError = WrappedPrinterError(errorCode: .PRINTING_OCCURRED_ERROR)
                await MainActor.run { failWrapper(printerError) }
            }
        }
    }
    
    func disconnect() {
        Task {
            self.performDisconnect()
        }
    }
    
    //MARK: - Private Interface
    private func mapPrinterCommunicationErrorToPrinterError(_ error: PrinterCommunicationError) -> WrappedPrinterError {
        switch error {
        case .writeError(let writeError):
            let errorString = writeError.debugDescription
            // 10110
            return WrappedPrinterError(
                errorCode: .PRINTING_OCCURRED_ERROR,
                additionalMessage: ["errorDetails": errorString])
        case .printingConvertDataError:
            // 10110
            return WrappedPrinterError(errorCode: .PRINTING_OCCURRED_ERROR)
        case .printingTimeout:
            // 10612
            return WrappedPrinterError(errorCode: .PRINTER_PRINTING_TIMEOUT)
        case .connectionInvalid, .connectionCancelled, .connectionTimeout:
            // 10617
            return WrappedPrinterError(errorCode: .PRINTER_CONNECTION_ERROR)
        case .connectionFailed(let connectError):
            // 10617
            let errorString = connectError.debugDescription
            return WrappedPrinterError(
                errorCode: .PRINTER_CONNECTION_ERROR,
                additionalMessage: ["errorDetails": errorString])
        }
    }
}
