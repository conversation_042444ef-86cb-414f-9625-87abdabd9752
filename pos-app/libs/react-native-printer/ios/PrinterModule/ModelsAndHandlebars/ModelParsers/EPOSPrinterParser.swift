//
//  EPOSPrinterParser.swift
//  RNPrinter
//
//  Created by <PERSON> on 2021/10/25.
//  Copyright © 2021 Facebook. All rights reserved.
//

protocol PrinterParser {
    func getGenerators(from parser: BaseModelParser) -> [DataGenerator<Data>]
}

final class EPOSPrinterParser: PrinterParser {
    static func convertImage(_ image: UIImage) -> Data {
        return PosCommand.printRasteBmp(withM: RasterNolmorWH, andImage: image, andType: Dithering)
    }
    
    func getGenerators(from parser: BaseModelParser) -> [DataGenerator<Data>] {
        var dataGenerators: [DataGenerator<Data>] = []

        let dataGenerator = DataGenerator<Data> { dataCallback in
            let tempData = NSMutableData(data: PosCommand.initializePrinter())
            tempData.append(PosCommand.selectAlignment(1))
            dataCallback(tempData as Data)
        }
        dataGenerators.append(dataGenerator)

        let imageGennerators = parser.getImageGennerators()
        print("getImageGennerators count = \(imageGennerators.count)")
        var i = 0;
        for imageGenerator in imageGennerators {
            let tempDataGenerator = DataGenerator<Data> { dataCallback in
                let next = imageGenerator.next()
                next({ image in
                    let tempData = NSMutableData(data: PosCommand.initializePrinter())
                    if let image = image {
                        // save image to local path
                        if(RNPrinterModuleConfig.shareInstance.getEnableDumpImages()){
                            let filename = "\(parser.key)-\(i).png"
                            _ = FileUtil.saveImage(image: image, path: filename)
                            i = i+1;
                        }
                        let data = EPOSPrinterParser.convertImage(image)
                        tempData.append(data)
                    }
                    dataCallback(tempData as Data)
                })
            }
            dataGenerators.append(tempDataGenerator)
        }

        let cutPageDataGenerator = DataGenerator<Data> { dataCallback in
            let tempData = NSMutableData(data: PosCommand.initializePrinter())
            tempData.append(PosCommand.selectCutPageModelAndCutpage(withM: 66, andN: 3))
            dataCallback(tempData as Data)
        }
        dataGenerators.append(cutPageDataGenerator)

        return dataGenerators
    }
}
