//
//  KitchenTicketModelParser.swift
//  RNPrinter
//
//  Created by <PERSON> on 2021/9/10.
//  Copyright © 2021 Facebook. All rights reserved.
//

final class KitchenTicketHBSModelParser: BaseModelParser {

    private let KITCHEN = "kitchen"
    private let KITCHEN_LABEL = "kitchen_label"
    private let KITCHEN_LABEL_NOTE = "kitchen_label_note"

    private var model: KitchenTicketHBSModel!
    private var isLabel: Bool = false

    private let html2Image = Html2ImageWarmUper.shared.dequeue()
    private let newHtmlToImage = NewHtmlToImage()
    private var useNewHtmlToImage = false

    private override init() {
        super.init()
        
        let switchOn = UserDefaults.standard.bool(forKey: "RN_iOS_USE_NEW_HTML_TO_IMAGE")
        if switchOn {
            useNewHtmlToImage = true
        } else {
            useNewHtmlToImage = false
        }
    }

    convenience init(model: KitchenTicketHBSModel, paperWidth: PrinterPaperWidth, isLabel: Bool) {
        self.init()
        self.model = model
        self.isLabel = isLabel
        self.model.isPrinterPaperWidth58 = paperWidth == .Print58
        self.printerPaperwidth = paperWidth
        
        if let docketCountString = model.docketCountString, !docketCountString.isEmpty {
            let replacedStr = docketCountString.replacingOccurrences(of: "/", with: "-")
            self.key = "BeerKitchen-\(self.model.orderNumber ?? "")-\(replacedStr)"
        }else if let title = self.model.docketTitle, !title.isEmpty {
            self.key = "OrderSummary-\(self.model.orderNumber ?? "")"
        }else{
            self.key = "KitchenTicket-\(self.model.orderNumber ?? "")"
        }
    }

    override func getImageGennerators() -> [DataGenerator<UIImage>] {
        var generators: [DataGenerator<UIImage>] = []

        if isLabel {
            if let note = model.note, !note.isEmpty {
                let kitchenLabelNoteHBS = KITCHEN_LABEL_NOTE.getHBSFromBundle(name: SHPOSBundle)
                let noteLabelImageGenerator = DataGenerator<UIImage> {[self] dataCallback in
                    generateImage(model: model, hbs: kitchenLabelNoteHBS, completion: dataCallback)
                }
                generators.append(noteLabelImageGenerator)
            }

            var globalIndex = 0
            if let purchasedItems = model.purchasedItems {
                globalIndex = generateItems(items: purchasedItems, generators: &generators, globalIndex: globalIndex)
            }

            if let takeawayItems = model.takeawayItems {
                globalIndex = generateItems(items: takeawayItems, generators: &generators, globalIndex: globalIndex)
            }
        } else {
            let kitchenHBS = KITCHEN.getHBSFromBundle(name: SHPOSBundle)
            let kitchenImageGenerator = DataGenerator<UIImage> {[self] dataCallback in
                generateImage(model: model, hbs: kitchenHBS, completion: dataCallback)
            }
            generators.append(kitchenImageGenerator)
        }

        return generators
    }

    private func generateItems(items: [KitchenPurchasedItem], generators: inout [DataGenerator<UIImage>], globalIndex: Int) -> Int {
        var index = globalIndex

        for i in 0..<items.count {
            var item = items[i]
            if var quantity = item.quantity {
                quantity = abs(quantity)
                if let previousTotal = item.previousTotal, previousTotal > 0 {
                    index = Int(previousTotal) - Int(quantity)
                }

                for _ in 0..<Int(quantity) {
                    let kitchenLabelHBS = KITCHEN_LABEL.getHBSFromBundle(name: SHPOSBundle)
                    let labelImageGenerator = DataGenerator<UIImage> {[self] dataCallback in
                        index += 1
                        item.index = index
                        generateImage(model: item, hbs: kitchenLabelHBS, completion: dataCallback)
                    }
                    generators.append(labelImageGenerator)
                }
            }
        }

        return index
    }

    private func generateImage(model: Codable, hbs: String, completion: @escaping (UIImage?) -> Void) {
        if let dic = model.toDic() {
            let newHbs = replaceVariantIsMultipleLine(target: hbs)
            guard let htmString = try? HBHandlebars.renderTemplateString(newHbs, withContext: dic) else {
                completion(nil)
                return
            }
            
            print("kitchen htmString-" + htmString)
            
            let fontScale: Double = (dic["receiptFontScale"] as? Double) ?? 1.0
            
            let minContentHeight: Double = dic["minContentHeight"] as? Double ?? 0.0
            
            if useNewHtmlToImage {
                newHtmlToImage.renderHTMLString(htmString, width: printerPaperwidth.rawValue, fontScale: fontScale, minContentHeight: minContentHeight) { image in
                    if let image = image {
                        completion(image)
                    } else {
                        completion(nil)
                    }
                }
            } else {
                html2Image.setWidth(printerPaperwidth.rawValue, fontScale: fontScale).convert(htmlString: htmString) { image in
                    completion(image)
                } fail: {
                    completion(nil)
                }
            }
        }
    }

    /// replaceVariantIsMultipleLine
    /// replace kitchenDocketVariantIsMultipleLine to true
    /// - Parameter target: target
    /// - Returns: replaced string
    private func replaceVariantIsMultipleLine(target: String) -> String {
        if let kitchenDocketVariantIsMultipleLine = model.kitchenDocketVariantIsMultipleLine {
            let isMultipleLineString = kitchenDocketVariantIsMultipleLine ? "true":"false"
            var replacedString = target
            replacedString = replacedString.replacingOccurrences(of: "kitchenDocketVariantIsMultipleLine", with: isMultipleLineString)
            return replacedString
        }
        return target
    }

    deinit {
        print("KitchenTicketHBSModelParser ===== deinit")
    }
}
