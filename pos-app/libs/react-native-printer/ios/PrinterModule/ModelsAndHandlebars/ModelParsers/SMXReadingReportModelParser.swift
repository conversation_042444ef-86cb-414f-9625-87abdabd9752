//
//  SMXReadingReportModelParser.swift
//  RNPrinter
import Foundation


final class SMXReadingReportModelParser: BaseModelParser {
    
    private let HEADER = "sm_xreading_report_header"
    private let SECOND = "sm_report_second"
    private let FOOTER = "sm_report_footer"
    private let ITEMS = "sm_report_items"
    
    private var model: SMXReadingReportModel!
    
    private let html2Image = Html2ImageWarmUper.shared.dequeue()
    private let cashBackUtil = CashBackUtil()
    
    private let newHtmlToImage = NewHtmlToImage()
    
    private override init() {
        super.init()
    }
    
    convenience init(model: SMXReadingReportModel, paperWidth: PrinterPaperWidth) {
        self.init()
        self.model = model
        self.model.isPrinterPaperWidth58 = paperWidth == .Print58
        self.printerPaperwidth = paperWidth
        self.key = "SMXReadingReport-\(String(describing: model.storeName))-\(String(describing: model.dateLines?[2].value))"
    }
    
    override func getImageGennerators() -> [DataGenerator<UIImage>] {
        var generators: [DataGenerator<UIImage>] = []
        
        if let dic = model.toDic() {
            let headerGenerator = DataGenerator<UIImage> { [self] dataCallback in
                generateHeader(dic: dic, completion: dataCallback)
            }
            generators.append(headerGenerator)
            
            let secondGenerator = DataGenerator<UIImage> { [self] dataCallback in
                generateSecond(dic: dic, completion: dataCallback)
            }
            generators.append(secondGenerator)
            
            if let mixSummaryLinesGroups = model.mixSummaryLinesGroups {
                if mixSummaryLinesGroups.count > 0 {
                    for mixSummaryLines in mixSummaryLinesGroups {
                        if let dic2 = mixSummaryLines.toJSON() as? [[String: Any]] {
                            let itemGenerator = DataGenerator<UIImage> { [self] dataCallback in
                                generateItems(dic: dic2, completion: dataCallback)
                            }
                            generators.append(itemGenerator)
                        }
                    }
                }
            }
            
            let footerGenerator = DataGenerator<UIImage> { [self] dataCallback in
                generateFooter(dic: dic, completion: dataCallback)
            }
            generators.append(footerGenerator)
        }
        generators = generateFullReceipet(generators: generators)
        return generators
    }
    
    
    func generateFullReceipet(generators: [DataGenerator<UIImage>]) ->  [DataGenerator<UIImage>] {
        let allGenerator = DataGenerator<UIImage> {dataCallback in
            var images = [UIImage]()
            let lock = NSLock()
            
            let dStartTime = DDTime.getNow()
            
            for i in 0..<generators.count {
                lock.lock()
                let next = generators[i].next()
                next {image in
                    if let image = image {
                        images.append(image)
                    }
                    if (i == generators.count - 1) {
                        let allImage = UIImage.draw(images: images)
                        DDTime.printGapTime(starTime: dStartTime, tip: "整张图片")
                        dataCallback(allImage)
                    }
                    lock.unlock()
                }
            }
            
        }
        return [allGenerator]
    }
    
    
    func generateHeader(dic: [String: Any], completion: @escaping (UIImage?) -> Void) {
        let headerStr = HEADER.getHBSFromBundle(name: SHPOSBundle)
        let modifiedDic = dic
        guard let headerHtmlStr = try? HBHandlebars.renderTemplateString(headerStr, withContext: modifiedDic) else {
            completion(nil)
            return
        }
        
        print("headerHtmlStr-" + headerHtmlStr)
        
        newHtmlToImage.renderHTMLString(headerHtmlStr, width: printerPaperwidth.rawValue) { image in
            completion(image)
        }
    }
    
    func generateSecond(dic: [String: Any], completion: @escaping (UIImage?) -> Void) {
        let secondStr = SECOND.getHBSFromBundle(name: SHPOSBundle)
        let modifiedDic = dic
        guard let secondHtmlStr = try? HBHandlebars.renderTemplateString(secondStr, withContext: modifiedDic) else {
            completion(nil)
            return
        }
        
        print("seconderHtmlStr-" + secondHtmlStr)
        
        newHtmlToImage.renderHTMLString(secondHtmlStr, width: printerPaperwidth.rawValue) { image in
            completion(image)
        }
    }
    
    func generateItems(dic: [Any], completion: @escaping (UIImage?) -> Void) {
        let itemStr = ITEMS.getHBSFromBundle(name: SHPOSBundle)
        guard let itemHtmlStr = try? HBHandlebars.renderTemplateString(itemStr, withContext: dic) else {
            completion(nil)
            return
        }
        
        print("itemHtmlStr:\(itemHtmlStr)")
        
        newHtmlToImage.renderHTMLString(itemHtmlStr, width: printerPaperwidth.rawValue) { image in
            completion(image)
        }
    }
    
    func generateFooter(dic: [String: Any], completion: @escaping (UIImage?) -> Void) {
        let footerStr = FOOTER.getHBSFromBundle(name: SHPOSBundle)
        let modifiedDic = dic
        guard let footerHtmlStr = try? HBHandlebars.renderTemplateString(footerStr, withContext: modifiedDic) else {
            completion(nil)
            return
        }
        
        print("footerHtmlStr-" + footerHtmlStr)
        
        newHtmlToImage.renderHTMLString(footerHtmlStr, width: printerPaperwidth.rawValue) { image in
            completion(image)
        }
    }
    
    
    deinit {
        print("SMXReadingReportHBSModelParser ===== deinit")
    }
}

