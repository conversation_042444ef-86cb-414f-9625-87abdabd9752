//
//  ShiftReportHBSModelParser.swift
//  RNPrinter
//
//  Created by <PERSON> on 2021/9/10.
//  Copyright © 2021 Facebook. All rights reserved.
//

final class ShiftReportHBSModelParser: BaseModelParser {

    private let SHIFTREPORT = "shift_report"
    private let A4_SHIFT_REPORT = "a4ShiftReportTemplate"

    private var model: ShiftReportHBSModel!

    private let html2Image = Html2ImageWarmUper.shared.dequeue()

    private override init() {
        super.init()
    }

    convenience init(model: ShiftReportHBSModel, paperWidth: PrinterPaperWidth) {
        self.init()
        self.model = model
        self.model.isPrinterPaperWidth58 = paperWidth == .Print58
        self.printerPaperwidth = paperWidth
    }

    override func getImageGennerators() -> [DataGenerator<UIImage>] {
        var generators: [DataGenerator<UIImage>] = []

        let shiftReportHBS = SHIFTREPORT.getHBSFromBundle(name: SHPOSBundle)
        let shiftReportHBSImageGenerator = DataGenerator<UIImage> {[self] dataCallback in
            generateImage(model: model, hbs: shiftReportHBS, completion: dataCallback)
        }
        generators.append(shiftReportHBSImageGenerator)

        return generators
    }

    private func generateImage(model: Codable, hbs: String, completion: @escaping (UIImage?) -> Void) {
        if let dic = model.toDic() {
            let newHbs = replacePaymentSummary(target: hbs)
            guard let htmString = try? HBHandlebars.renderTemplateString(newHbs, withContext: dic) else {
                completion(nil)
                return
            }
            
            html2Image.setWidth(printerPaperwidth.rawValue).convert(htmlString: htmString) { image in
                completion(image)
            } fail: {
                completion(nil)
            }
        }
    }
    
    override func getStringGenerators() -> [DataGenerator<String>] {
        var generators: [DataGenerator<String>] = []
        if let dic = model.toDic() {
            let htmlStringGenerator = DataGenerator<String> { [self] dataCallback in
                generateFullHtmlString(dic: dic, completion: dataCallback)
            }
            generators.append(htmlStringGenerator)
        }
        return generators
    }
    
    func generateFullHtmlString(dic: [String: Any], completion: @escaping (String?) -> Void) {
        let htmlTemplateStr = A4_SHIFT_REPORT.getHBSFromBundle(name: SHPOSBundle)
        
        var newDict = [String: Any]()
        newDict["openTime"] = [
            "value": dic.getStringValue(forKey: "shiftOpenTime").substring(from: "Time: "),
            "localizedTitle": "SHIFT OPEN TIME:"
        ]
        
        newDict["closeTime"] = [
            "localizedTitle": "SHIFT CLOSE TIME:",
            "value": dic.getStringValue(forKey: "shiftCloseTime").substring(from: "Time: ")
        ]
        
        newDict["register"] = [
            "value": dic.getStringValue(forKey: "registerId").substring(from: ": "),
            "localizedTitle": "REGISTER #:"
        ]
        
        newDict["store"] = [
            "value": dic.getStringValue(forKey: "storeName"),
            "localizedTitle": "STORE"
        ]
        
        newDict["openBy"] = [
            "value": dic.getStringValue(forKey: "manager").substring(from: "Manager: "),
            "localizedTitle": "OPEN BY"
        ]
        
        newDict["closedBy"] = [
            "value": dic.getStringValue(forKey: "manager").substring(from: "Manager: "),
            "localizedTitle": "CLOSE BY"
        ]
        
        newDict["transactionsLocalTitle"] = "TRANSACTIONS"
        
        newDict["amountLocalTitle"] = "AMOUNT"
        
        newDict["refundsLocalTitle"] = "Refunds"
        
        newDict["totalLocalTitle"] = "Total"
        
        newDict["salesLocalTitle"] = "Sales"
        
        newDict["netLocalTitle"] = "Net"
        
        newDict["depositsLocalTitle"] = "Deposits"
        
        // salesSummary
        var paymentsArr = Array<Dictionary<String, Any>>()
        if let payments = dic["payments"] as? Array<Dictionary<String, Any>> {
            for pay in payments {
                let dic: [String: Any] = [
                    "refunds": [
                        "amount": pay.getStringValue(forKey: "refundsAmount"),
                        "transactions": Int(pay.getStringValue(forKey: "refundsQuantity")) ?? 0
                    ],
                    "net": pay.getStringValue(forKey: "netAmount"),
                    "localizedTitle": pay.getStringValue(forKey: "title").replacingOccurrences(of: " Summary", with: ""),
                    "sales": [
                        "amount": pay.getStringValue(forKey: "salesAmount"),
                        "transactions": Int(pay.getStringValue(forKey: "salesQuantity")) ?? 0
                    ],
                    "roundings": [
                        "title": pay.getStringValue(forKey: "roundingTitle"),
                        "amount": pay.getStringValue(forKey: "roundingAmount"),
                    ]
                ]
                paymentsArr.append(dic)
            }
        }
        newDict["salesSummary"] = [
            "localizedTitle": "SALES SUMMARY",
            "value": paymentsArr,
            "total": dic.getStringFromNestedDict(key: "salesSummary", nestedKey: "netAmount")
        ]
        
        // taxSummary
        var taxSummaryArr = Array<Dictionary<String, Any>>()
        var taxTotal: Float = 0.0
        if let dictArr = dic["taxSummaryArray"] as? Array<Dictionary<String, Any>> {
            for dict in dictArr {
                let targetDict: [String: Any] = [
                    "taxCode": dict.getStringValue(forKey: "taxName"),
                    "taxRate": dict.getStringValue(forKey: "taxRate"),
                    "amount": dict.getStringValue(forKey: "amount")
                ]
                taxSummaryArr.append(targetDict)
                if let amount = Float(dict.getStringValue(forKey: "amount").removeComma()) {
                    taxTotal += amount
                }
            }
        }
        newDict["taxSummary"] = [
            "localizedTitle": "TAX SUMMARY",
            "taxRateLocalTitle": "TAX RATE",
            "total": String(taxTotal),
            "taxes": taxSummaryArr
        ]
        
        // cancelAndDiscountSummary
        newDict["cancelAndDiscountSummary"] = [
            "localizedTitle": "CANCELS / DISC. SUMMARY",
            "cancelledTransactions": [
                "amount": dic.getStringFromNestedDict(key: "cancelAndDiscountSummary", nestedKey: "cancelAmount"),
                "transactions": dic.getStringFromNestedDict(key: "cancelAndDiscountSummary", nestedKey: "cancelQuantity"),
                "localizedTitle": "Cancel Txns"
            ],
            "discount":[
                "amount": dic.getStringFromNestedDict(key: "cancelAndDiscountSummary", nestedKey: "discountAmount"),
                "transactions": dic.getStringFromNestedDict(key: "cancelAndDiscountSummary", nestedKey: "discountQuantity"),
                "localizedTitle": "Discount"
            ]
        ]
        
        // cashDrawerSummary
        newDict["cashDrawerSummary"] = [
            "payout": [
                "qty": dic.getStringFromNestedDict(key: "cashDrawerSummary", nestedKey: "payOutQuantity"),
                "amount": dic.getStringFromNestedDict(key: "cashDrawerSummary", nestedKey: "payOutAmount"),
                "localizedTitle": "Pay Out"
            ],
            "qtyLocalTitle": "QUANTITY",
            "cashSales": [
                "qty": dic.getStringFromNestedDict(key: "cashDrawerSummary", nestedKey: "cashSalesQuantity"),
                "amount": dic.getStringFromNestedDict(key: "cashDrawerSummary", nestedKey: "cashSalesAmount"),
                "localizedTitle": "Cash Sales"
            ],
            "payin": [
                "qty": dic.getStringFromNestedDict(key: "cashDrawerSummary", nestedKey: "payInQuantity"),
                "amount": dic.getStringFromNestedDict(key: "cashDrawerSummary", nestedKey: "payInAmount"),
                "localizedTitle": "Pay In"
            ],
            "cashRefunds": [
                "qty": dic.getStringFromNestedDict(key: "cashDrawerSummary", nestedKey: "cashRefundsQuantity"),
                "amount": dic.getStringFromNestedDict(key: "cashDrawerSummary", nestedKey: "cashRefundsAmount"),
                "localizedTitle": "Cash Refunds"
            ],
            "cashDeposits": [
                "qty": dic.getStringFromNestedDict(key: "cashDrawerSummary", nestedKey: "cashDepositsQuantity"),
                "amount": dic.getStringFromNestedDict(key: "cashDrawerSummary", nestedKey: "cashDepositsAmount"),
                "localizedTitle": "Cash Deposits"
            ],
            "expectedDrawer": [
                "amount": dic.getStringValue(forKey: "expectedDrawer"),
                "localizedTitle": "Expected Drawer"
            ],
            "localizedTitle": "CASH DRAWER SUMMARY",
            "actualDrawer": [
                "amount": dic.getStringValue(forKey: "actualDrawer"),
                "localizedTitle": "Actual Drawer"
            ],
            "overShort": [
                "amount": dic.getStringValue(forKey: "overShort"),
                "localizedTitle": "Over/Short"
            ],
            "openingAmount": [
                "amount": dic.getStringFromNestedDict(key: "cashDrawerSummary", nestedKey: "openingAmount"),
                "localizedTitle": "Opening Amount"
            ]
        ]
        
        guard let resultStr = try? HBHandlebars.renderTemplateString(htmlTemplateStr, withContext: newDict) else {
            completion(nil)
            return
        }

        completion(resultStr)
    }
    
    
    /// Description
    /// paymentSummaryTitleStrings
    /// ../paymentSummaryTitleStrings
    /// {{../paymentSummaryTitleStrings.salesTitle}}
    /// {{../paymentSummaryTitleStrings.refundsTitle}}
    /// {{../paymentSummaryTitleStrings.netTitle}}
    /// - Parameter target: htmlString
    /// - Returns: replacedString
    private func replacePaymentSummary(target: String) -> String {
        
        if let paymentSummaryTitleStrings = model.paymentSummaryTitleStrings {
            var replacedString = target
            
            replacedString = replacedString.replacingOccurrences(of: "{{../paymentSummaryTitleStrings.salesTitle}}", with: paymentSummaryTitleStrings.salesTitle ?? "")
                        
            replacedString = replacedString.replacingOccurrences(of: "{{../paymentSummaryTitleStrings.refundsTitle}}", with: paymentSummaryTitleStrings.refundsTitle ?? "")

            replacedString = replacedString.replacingOccurrences(of: "{{../paymentSummaryTitleStrings.netTitle}}", with: paymentSummaryTitleStrings.netTitle ?? "")
            
            replacedString = replacedString.replacingOccurrences(of: "../paymentSummaryTitleStrings", with: "true")

            return replacedString
        }
        
        return target
    }

    deinit {
        print("ShiftReportHBSModelParser ===== deinit")
    }
}
