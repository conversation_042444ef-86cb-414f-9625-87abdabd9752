//
//  TransactionModelParser.swift
//  RNPrinter
//
//  Created by <PERSON> on 2021/9/7.
//  Copyright © 2021 Facebook. All rights reserved.
//

import Foundation

struct DDTime {
    static func getNow() -> CFAbsoluteTime {
        return CFAbsoluteTimeGetCurrent()
    }

    static func printGapTime(starTime: CFAbsoluteTime, tip: String) {
        let time = String(format: "%.0f", (getNow() - starTime)*1000)
        let temp = "DD >>>>>>>>>> \(tip) cost == \(time) ms  <<<<<<<<<<< DD"
        // debugPrint(temp)
    }
}

let SHPOSBundle = "SHPOSBundle"

final class TransactionHBSModelParser: BaseModelParser {

    private let HEADER = "transaction_header"
    private let FOOTER = "transaction_footer"
    private let ITEMS = "transaction_items"
    private let A4_RECEIPT = "a4ReceiptTemplate"

    private var model: BaseTransactionModel!

    private let barSize = CGSize(width: 250, height: 100)
    private let barCodeImageHtml = #"<img class="bar-code-info" src="_barCodeImage_" alt="">"#
    
    private let html2Image = Html2ImageWarmUper.shared.dequeue()
    private let newHtmlToImage = NewHtmlToImage()
    private var useNewHtmlToImage = false
    
    private let cashBackUtil = CashBackUtil()

    private override init() {
        super.init()
        
        let switchOn = UserDefaults.standard.bool(forKey: "RN_iOS_USE_NEW_HTML_TO_IMAGE")
        if switchOn {
            useNewHtmlToImage = true
        } else {
            useNewHtmlToImage = false
        }
    }

    convenience init(model: BaseTransactionModel, paperWidth: PrinterPaperWidth) {
        self.init()
        self.model = model
        self.model.isPrinterPaperWidth58 = paperWidth == .Print58
        self.printerPaperwidth = paperWidth
        
        self.key = model.key ?? ""
    }

    override func getImageGennerators() -> [DataGenerator<UIImage>] {
        var generators: [DataGenerator<UIImage>] = []

        if let logoImage = getLogo() {
            model.logoImage = logoImage
        }
        let enablePrintCashback = model.enablePrintCashback ?? false
        let enablePrintMemberShip = model.enablePrintMemberShip ?? false

        if enablePrintCashback {
            if let defaultLoyaltyRatioStr = model.defaultLoyaltyRatio {
                let defaultLoyaltyRatio = Double(defaultLoyaltyRatioStr) ?? 0
                if defaultLoyaltyRatio == 0 {
                    model.defaultLoyaltyRatio = "0"
                } else {
                    let formatedStr = String(format: "%.0f",  1 / defaultLoyaltyRatio * 100)
                    model.defaultLoyaltyRatio = formatedStr
                }
            }
            if !enablePrintMemberShip {
                model.qrCodeAboveInfo = cashBackUtil.getFormattedTextAboveQR(defaultLoyaltyRatioStr: model.defaultLoyaltyRatio ?? "0")
                model.qrCodeUnderInfo = cashBackUtil.getFormattedTextUnderQR()
            }
        }

        if let dic = model.toDic() {
            let headerGenerator = DataGenerator<UIImage> { [self] dataCallback in
                generateHeader(dic: dic, completion: dataCallback)
            }
            generators.append(headerGenerator)

            if let purchasedItems = model.purchasedItems {
                if purchasedItems.count > 0 {
                    let chunedArr = purchasedItems.chunked(by: 15)
                    for arr in chunedArr {
                        model.purchasedItems = arr
                        if let dic = model.toDic() {
                            let itemGenerator = DataGenerator<UIImage> { [self] dataCallback in
                                generateItems(dic: dic, completion: dataCallback)
                            }
                            generators.append(itemGenerator)
                        }
                    }
                }
            }

            let footerGenerator = DataGenerator<UIImage> { [self] dataCallback in
                generateFooter(dic: dic, completion: dataCallback)
            }
            generators.append(footerGenerator)
        }

        // generators count <= 3, generate full receipt to print
//        if generators.count <= 3 {
            generators = generateFullReceipet(generators: generators)
//        }

        return generators
    }

    override func getStringGenerators() -> [DataGenerator<String>] {
        var generators: [DataGenerator<String>] = []

        if let logoImage = getLogo() {
            model.logoImage = logoImage
        }

        let enablePrintCashback = model.enablePrintCashback ?? false
        let enablePrintMemberShip = model.enablePrintMemberShip ?? false

        if enablePrintCashback {
            if let defaultLoyaltyRatioStr = model.defaultLoyaltyRatio {
                let defaultLoyaltyRatio = Double(defaultLoyaltyRatioStr) ?? 0
                if defaultLoyaltyRatio == 0 {
                    model.defaultLoyaltyRatio = "0"
                } else {
                    let formatedStr = String(format: "%.0f",  1 / defaultLoyaltyRatio * 100)
                    model.defaultLoyaltyRatio = formatedStr
                }
            }
            if !enablePrintMemberShip {
                model.qrCodeAboveInfo = cashBackUtil.getFormattedTextAboveQR(defaultLoyaltyRatioStr: model.defaultLoyaltyRatio ?? "0")
                model.qrCodeUnderInfo = cashBackUtil.getFormattedTextUnderQR()
            }
        }

        if let dic = model.toDic() {
            let htmlStringGenerator = DataGenerator<String> { [self] dataCallback in
                generateFullHtmlString(dic: dic, completion: dataCallback)
            }
            generators.append(htmlStringGenerator)
        }

        return generators
    }

    func generateFullReceipet(generators: [DataGenerator<UIImage>]) ->  [DataGenerator<UIImage>] {
        let allGenerator = DataGenerator<UIImage> {dataCallback in
            var images = [UIImage]()
            let lock = NSLock()

            let dStartTime = DDTime.getNow()

            for i in 0..<generators.count {
                lock.lock()
                let next = generators[i].next()
                next { image in
                    if let image = image {
                        images.append(image)
                    }
                    if (i == generators.count - 1) {
                        let allImage = UIImage.draw(images: images)
                        DDTime.printGapTime(starTime: dStartTime, tip: "整张图片")
                        dataCallback(allImage)
                    }
                    lock.unlock()
                }
            }
        }
        return [allGenerator]
    }
    
    // 将图片转换为灰度
    func convertToGrayscale(image: UIImage) -> UIImage? {
        guard let ciImage = CIImage(image: image) else { return nil }
        let filter = CIFilter(name: "CIPhotoEffectMono")
        filter?.setValue(ciImage, forKey: kCIInputImageKey)
        
        if let outputImage = filter?.outputImage {
            let context = CIContext(options: nil)
            if let cgImage = context.createCGImage(outputImage, from: outputImage.extent) {
                return UIImage(cgImage: cgImage)
            }
        }
        return nil
    }
    
    // 降低图像对比度
    func adjustBrightness(image: UIImage, brightness: Float) -> UIImage? {
        guard let ciImage = CIImage(image: image) else { return nil }
        let filter = CIFilter(name: "CIColorControls")
        filter?.setValue(ciImage, forKey: kCIInputImageKey)
        filter?.setValue(brightness, forKey: kCIInputBrightnessKey)
        
        if let outputImage = filter?.outputImage {
            let context = CIContext(options: nil)
            if let cgImage = context.createCGImage(outputImage, from: outputImage.extent) {
                return UIImage(cgImage: cgImage)
            }
        }
        return nil
    }

    func generateHeader(dic: [String: Any], completion: @escaping (UIImage?) -> Void) {
        var headerStr = HEADER.getHBSFromBundle(name: SHPOSBundle)
        headerStr = replaceColumnWidths(target: headerStr)
        headerStr = replaceColumnTitleString(target: headerStr)

        var modifiedDic = dic
        if let customerInfo = dic["customerInfo"] as? String {
            let formattedCustomerInfo = formatCustomerInfoAsHTMLParagraphs(customerInfo: customerInfo)
            modifiedDic["customerInfo"] = formattedCustomerInfo
        }

        guard let headerHtmlStr = try? HBHandlebars.renderTemplateString(headerStr, withContext: modifiedDic) else {
            completion(nil)
            return
        }

        print("headerHtmlStr-" + headerHtmlStr)

        let fontScale: Double = (dic["receiptFontScale"] as? Double) ?? 1.0
        
        if useNewHtmlToImage {
            newHtmlToImage.renderHTMLString(headerHtmlStr, width: printerPaperwidth.rawValue, fontScale: fontScale) { image in
                if let image = image {
                    completion(image)
                } else {
                    completion(nil)
                }
            }
        } else {
            html2Image.setWidth(printerPaperwidth.rawValue, fontScale: fontScale).convert(htmlString: headerHtmlStr) { image in
                completion(image)
            } fail: {
                completion(nil)
            }
        }
    }

    func formatCustomerInfoAsHTMLParagraphs(customerInfo: String) -> String {
        let paragraphs = customerInfo.components(separatedBy: "\n")
        let htmlParagraphs = paragraphs.map { "<p>\($0)</p>" }
        return htmlParagraphs.joined()
    }

    func generateItems(dic: [String: Any], completion: @escaping (UIImage?) -> Void) {
        var itemStr = ITEMS.getHBSFromBundle(name: SHPOSBundle)
        itemStr = replaceColumnWidths(target: itemStr, isItem: true)

        guard let itemHtmlStr = try? HBHandlebars.renderTemplateString(itemStr, withContext: dic) else {
            completion(nil)
            return
        }

        let fontScale: Double = (dic["receiptFontScale"] as? Double) ?? 1.0

        if useNewHtmlToImage {
            newHtmlToImage.renderHTMLString(itemHtmlStr, width: printerPaperwidth.rawValue, fontScale: fontScale) { image in
                if let image = image {
                    completion(image)
                } else {
                    completion(nil)
                }
            }
        } else {
            html2Image.setWidth(printerPaperwidth.rawValue, fontScale: fontScale).convert(htmlString: itemHtmlStr) { image in
                completion(image)
            } fail: {
                completion(nil)
            }
        }
    }

    func generateFooter(dic: [String: Any], completion: @escaping (UIImage?) -> Void) {
        let footerStr = FOOTER.getHBSFromBundle(name: SHPOSBundle)

        var newDic = dic
        newDic["isIOS"] = true
        newDic["isPrinterPaperWidth80"] = printerPaperwidth == .Print80

        guard var footerHtmlStr = try? HBHandlebars.renderTemplateString(footerStr, withContext: newDic) else {
            completion(nil)
            return
        }

        let receiptId = model.receiptId ?? ""
        let isExistReceiptId = receiptId != ""

        // Bar Code
        let showBarcode = model.showBarcode ?? false
        if showBarcode && isExistReceiptId {
            let startTime = CFAbsoluteTimeGetCurrent()
            let barCodeBase64 = QRBarCode.crateBarCode(codeString: receiptId, size: CGSize(width: 380, height: 110))
            if let barCodeBase64 = barCodeBase64 {
                footerHtmlStr = footerHtmlStr.replacingOccurrences(of: "_barCodeImage_", with: "data:image/png;base64," + barCodeBase64)
            }

            let endTime = CFAbsoluteTimeGetCurrent()
            let time = String(format: "%.0f", (endTime - startTime) * 1000)
            let temp = "generate _barCodeImage_ base64 cost == \(time) ms"
             debugPrint(temp)
        } else {
            footerHtmlStr = footerHtmlStr.replacingOccurrences(of: barCodeImageHtml, with: "")
        }

        // QR Code
        let enablePrintCashback = model.enablePrintCashback ?? false
        let cashbackUrl = model.cashbackUrl ?? ""
        let isExistCashbackUrl = cashbackUrl != ""

        let enablePrintMemberShip = model.enablePrintMemberShip ?? false
        let membershipUrl = model.membershipUrl ?? ""
        let isExistMembershipUrl = membershipUrl != ""

        if enablePrintCashback && isExistCashbackUrl && isExistReceiptId {
            let startTime = CFAbsoluteTimeGetCurrent()
            let encryptReceiptId = AESUtil.encrypt(content: receiptId, key: cashBackUtil.AES256_ENCODE_KEY) ?? ""
            let newCashBackUrl = cashbackUrl + "/loyalty/claim/?h=" + encryptReceiptId.urlEncode
            let qrCodeBase64 = QRBarCode.createQRCode(codeString: newCashBackUrl, size: CGSize(width: 160, height: 160))

            if let qrCodeBase64 = qrCodeBase64 {
                footerHtmlStr = footerHtmlStr.replacingOccurrences(of: "_qrCodeImage_", with: "data:image/png;base64," + qrCodeBase64)
            }
            let endTime = CFAbsoluteTimeGetCurrent()
            let time = String(format: "%.0f", (endTime - startTime)*1000)
            let temp = "generate qrCodeImage base64 cost == \(time) ms"
             debugPrint(temp)
        } else if enablePrintMemberShip && isExistMembershipUrl {
            let startTime = CFAbsoluteTimeGetCurrent()
            let qrCodeBase64 = QRBarCode.createQRCode(codeString: membershipUrl, size: CGSize(width: 200, height: 200))
            if let qrCodeBase64 = qrCodeBase64 {
                footerHtmlStr = footerHtmlStr.replacingOccurrences(of: "_qrCodeImage_", with: "data:image/png;base64," + qrCodeBase64)
            }
            let endTime = CFAbsoluteTimeGetCurrent()
            let time = String(format: "%.0f", (endTime - startTime)*1000)
            let temp = "generate qrCodeImage base64 cost == \(time) ms"
             debugPrint(temp)
        } else {
            footerHtmlStr = footerHtmlStr.replacingOccurrences(of: "src=\"_qrCodeImage_\"", with: "")
        }
        
        
        let eInvoiceUrl = model.eInvoiceUrl ?? ""
        let isEInvoiceUrlExist = eInvoiceUrl != ""
        if isEInvoiceUrlExist {
            let startTime = CFAbsoluteTimeGetCurrent()
            let qrCodeBase64 = QRBarCode.createQRCode(codeString: eInvoiceUrl, size: CGSize(width: 200, height: 200))
            if let qrCodeBase64 = qrCodeBase64 {
                footerHtmlStr = footerHtmlStr.replacingOccurrences(of: "_eInvoiceQrCodeImage_", with: "data:image/png;base64," + qrCodeBase64)
            }
            let endTime = CFAbsoluteTimeGetCurrent()
            let time = String(format: "%.0f", (endTime - startTime)*1000)
            let temp = "generate qrCodeImage base64 cost == \(time) ms"
             debugPrint(temp)
        }


        print("footerHtmlStr", footerHtmlStr)

        let fontScale: Double = (dic["receiptFontScale"] as? Double) ?? 1.0

        
        if useNewHtmlToImage {
            newHtmlToImage.renderHTMLString(footerHtmlStr, width: printerPaperwidth.rawValue, fontScale: fontScale) { image in
                if let image = image {
                    completion(image)
                } else {
                    completion(nil)
                }
            }
        } else {
            html2Image.setWidth(printerPaperwidth.rawValue, fontScale: fontScale).convert(htmlString: footerHtmlStr) { image in
                completion(image)
            } fail: {
                completion(nil)
            }
        }
    }

    func generateFullHtmlString(dic: [String: Any], completion: @escaping (String?) -> Void) {
        let htmlTemplateStr = A4_RECEIPT.getHBSFromBundle(name: SHPOSBundle)

        let jsonData = try! JSONSerialization.data(withJSONObject: dic, options: .prettyPrinted)
        let jsonString = String(data: jsonData, encoding: .utf8)
        print("map前的数据------：\(jsonString ?? "")")

        var newDict = [String: Any]()
        for (key, value) in dic {
            newDict[key] = value
        }

        // companyName
        newDict.renameKey(oldKey: "companyName", newKey: "companyNameLocalTitle")

        // store
        var store = [String: Any]()
        store["phone"] = dic["phone"]
        store["companyName"] = dic["companyName"]
        store["address1"] = dic["address"]
        newDict["store"] = store

        // receiptType
        var receiptTitle: String
        if let receiptTitleStr = dic["receiptTitle"] as? String {
            receiptTitle = receiptTitleStr
        } else {
            receiptTitle = ""
        }
        var transactionTitle: String
        if let transactionTitleStr = dic["transactionTitle"] as? String {
            transactionTitle = transactionTitleStr
        } else {
            transactionTitle = ""
        }
        let receiptType = receiptTitle.isEmpty ? transactionTitle : receiptTitle
        newDict["receiptType"] = receiptType

        // customerInfo
        newDict["customerInfo"] = (dic["customerInfo"] as? String ?? "")//.replacingOccurrences(of: "\n", with: "<br>")

        // invoice
        var invoice = [String: Any]()
        invoice["date"] = dic["receiptDate"]
        invoice["number"] = dic["receiptNumber"]
        invoice["taxId"] = dic["sstIdNo"]
        newDict["invoice"] = invoice

        newDict.renameKey(oldKey: "cashierInfo", newKey: "cashier")

        // itemsLocalTitle
        var itemsLocalTitle = [String: Any]()
        itemsLocalTitle["title"] = "ITEM"
        itemsLocalTitle["unitPrice"] = "PRICE"
        itemsLocalTitle["qty"] = "QTY"
        itemsLocalTitle["discount"] = "DISCOUNT"
        itemsLocalTitle["amount"] = "AMOUNT"

        if let a4ColumnTitleString = dic["a4ColumnTitleString"] as? Array<String> {
            itemsLocalTitle["title"] = a4ColumnTitleString.first
            if a4ColumnTitleString.count > 1 { itemsLocalTitle["unitPrice"] = a4ColumnTitleString[1] }
            if a4ColumnTitleString.count > 2 { itemsLocalTitle["qty"] = a4ColumnTitleString[2] }
            if a4ColumnTitleString.count > 3 { itemsLocalTitle["discount"] = a4ColumnTitleString[3] }
            if a4ColumnTitleString.count > 4 { itemsLocalTitle["amount"] = a4ColumnTitleString[4] }

        }
        newDict["itemsLocalTitle"] = itemsLocalTitle

        // items
        if let itemArr = dic["purchasedItems"] as? Array<Dictionary<String, Any>> {
            var newItemArr = Array<Dictionary<String, Any>>()
            for item in itemArr {
                var newItem = [String: Any]()
                newItem["title"] = item["itemName"] ?? ""
                newItem["qty"] = item["quantity"] ?? ""
                newItem["unitPrice"] = item["price"] ?? ""
                newItem["discount"] = item["itemDiscountValue"] ?? item["discount"] ?? "0.00";
                newItem["amount"] = item["a4Total"] ?? ""
                newItem["options"] = item["options"] ?? ""
                newItem["notes"] = item["notes"] ?? ""
                newItem["sn"] = item["sn"] ?? ""
                newItemArr.append(newItem)
            }
            newDict["items"] = newItemArr
        }

        // summaries
        var summaries = Array<Dictionary<String, Any>>()
        var subTotal = [String: Any]()
        subTotal["title"] = dic["subtotalTitle"]
        subTotal["value"] = dic["subtotal"]
        if dic["subtotalTitle"] == nil { subTotal["show"] = false } else {
            subTotal["show"] = true
        }
        summaries.append(subTotal)
        var discount = [String: Any]()
        discount["title"] = dic["discountTitle"]
        discount["value"] = dic["a4Discount"]
        if dic["a4Discount"] == nil { discount["show"] = false } else {
            discount["show"] = true
        }
        summaries.append(discount)
        if let serviceChargeTitle = dic["serviceChargeTitle"], let serviceCharge = dic["serviceCharge"] {
            var serviceChargeDict = [String: Any]()
            serviceChargeDict["title"] = serviceChargeTitle
            serviceChargeDict["value"] = serviceCharge
            serviceChargeDict["show"] = true
            summaries.append(serviceChargeDict)
        }
        var tax = [String: Any]()
        tax["title"] = dic["taxTitle"]
        tax["value"] = dic["tax"]
        tax["show"] = true
        summaries.append(tax)

        var containerFee = [String: Any]()
        containerFee["title"] = dic["containerFeeTxt"]
        containerFee["value"] = dic["containerFeeValue"]
        if dic["containerFeeValue"] == nil { containerFee["show"] = false } else {
            containerFee["show"] = true
        }
        summaries.append(containerFee)

        var smallOrderFee = [String: Any]()
        smallOrderFee["title"] = dic["smallOrderFeeTxt"]
        smallOrderFee["value"] = dic["smallOrderFeeValue"]
        if dic["smallOrderFeeValue"] == nil { smallOrderFee["show"] = false } else {
            smallOrderFee["show"] = true
        }
        summaries.append(smallOrderFee)

        newDict["summaries"] = summaries

        // total
        newDict.renameKey(oldKey: "totalTitle", newKey: "totalLocalTitle")

        // payments
        if let payments = dic["payment"] as? Array<Dictionary<String, Any>> {
            var paymentsArr = Array<Dictionary<String, Any>>()
            for payment in payments {
                var paymentDic = [String: Any]()
                paymentDic["payment"] = payment["paymentMethodName"]
                paymentDic["paymentValue"] = payment["amount"]
                paymentDic["changeLocalTitle"] = payment["changeTitle"]
                paymentDic["change"] = payment["changeValue"]
                if let changeStr = paymentDic["change"] as? String {
                    let numbersOnly = changeStr.extractNumbers()
                    if numbersOnly.count > 0 && Float(numbersOnly)! > 0 {
                        paymentDic["hasChange"] = 1
                    }
                }
                paymentsArr.append(paymentDic)
            }
            newDict.removeValue(forKey: "payment")

            if let showPreorderSummary = dic["showPreorderSummary"] as? Bool, showPreorderSummary {
                if let unPaidBalanceTitle = dic["unPaidBalanceTitle"], let unPaidBalance = dic["unPaidBalance"] {
                    var unPaidDic = [
                        "payment": unPaidBalanceTitle,
                        "paymentValue": unPaidBalance
                    ]
                    paymentsArr.insert(unPaidDic, at: 0)
                }

                if let depositAmountTitle = dic["depositAmountTitle"], let depositAmount = dic["depositAmount"] {
                    var depositDic = [
                        "payment": depositAmountTitle,
                        "paymentValue": depositAmount
                    ]
                    paymentsArr.insert(depositDic, at: 0)
                }
            }
            newDict["payments"] = paymentsArr
        }

        // taxSummaries
        if let showTaxSummary = dic["showTaxSummary"] as? Bool, showTaxSummary == true {
            var taxSummaries = [String: Any]()
            if let taxSummaryTitleString = dic["taxSummaryTitleString"] as? Array<String> {
                taxSummaries["title"] = taxSummaryTitleString.first
                if taxSummaryTitleString.count > 1 { taxSummaries["amount"] = taxSummaryTitleString[1] }
                if taxSummaryTitleString.count > 2 { taxSummaries["tax"] = taxSummaryTitleString[2] }
            }
            newDict.removeValue(forKey: "taxSummaryTitleString")
            newDict["taxSummariesLocalTitle"] = taxSummaries

            // taxSummaryItems
            newDict.renameKey(oldKey: "taxSummaryItems", newKey: "taxSummaries")
        }

        // storeCredits
        var storeCreditsInfoLocalTitle = [String: Any]()
        storeCreditsInfoLocalTitle["storeCredits"] = "CASHBACK"
        storeCreditsInfoLocalTitle["earned"] = dic["earnedTitle"]
        storeCreditsInfoLocalTitle["balance"] = dic["balanceTitle"]
        newDict["storeCreditsInfoLocalTitle"] = storeCreditsInfoLocalTitle
        newDict.renameKey(oldKey: "showReceiptStoreCredit", newKey: "hasStoreCreditsinfo")
        if let showReceiptStoreCredit = newDict["hasStoreCreditsinfo"] as? Bool {
            if showReceiptStoreCredit {
                var storeCreditsInfo = [String: Any]()
                storeCreditsInfo["earned"] = dic["loyaltyEarned"]
                storeCreditsInfo["balance"] = dic["loyaltyBalance"]
                newDict["storeCreditsInfo"] = storeCreditsInfo
            }
        }

        // notes
        newDict.renameKey(oldKey: "footerLabelString", newKey: "notes")

        // bar code
        let receiptId = model.receiptId ?? ""
        let isExistReceiptId = receiptId != ""
        let showBarcode = model.showBarcode ?? false
        if showBarcode && isExistReceiptId {
            let startTime = CFAbsoluteTimeGetCurrent()
            let barCodeBase64 = QRBarCode.crateBarCode(codeString: receiptId, size: CGSize(width: 380, height: 100))
            if let barCodeBase64 = barCodeBase64 {
                newDict["barcode"] = "data:image/png;base64," + barCodeBase64
            }
        } else {
            newDict["barcode"] = ""
        }

        guard let resultStr = try? HBHandlebars.renderTemplateString(htmlTemplateStr, withContext: newDict) else {
            completion(nil)
            return
        }

        print("resultStr:" + resultStr)

        completion(resultStr)
    }

    /// replaceColumnWidths
    /// - Parameter target: need to replace string
    /// - Parameter isItem: whether  replace transaction_items
    ///   isItem = true   replace the {{lookup ../columnWidths 0}} to columnWidths 0
    ///   isItem = false   replace the {{lookup columnWidths 0}} to columnWidths 0
    /// - Returns: replaced string
    private func replaceColumnWidths(target: String, isItem: Bool = false) -> String {
        let columnWidths = model.columnWidths ?? []

        var replacedString = target
        let prefix = isItem ? "../" : ""
        for (index, columnWidth) in columnWidths.enumerated() {
            replacedString = replacedString.replacingOccurrences(of: "{{lookup \(prefix)columnWidths \(index)}}", with: columnWidth)
        }
        return replacedString
    }

    /// replaceColumnTitleString
    /// - Parameter target: need to replace string
    ///  replace the {{lookup replaceColumnTitleString 0}} to columnTitleString 0
    /// - Returns: replaced string
    private func replaceColumnTitleString(target: String) -> String {
        let columnTitleStrings = model.columnTitleString ?? []

        var replacedString = target
        for (index, columnTitleString) in columnTitleStrings.enumerated() {
            replacedString = replacedString.replacingOccurrences(of: "{{lookup columnTitleString \(index)}}", with: columnTitleString)
        }
        return replacedString
    }

    deinit {
        // debugPrint("TransactionHBSModelParser ===== deinit")
    }
    
    func addPadding(to image: UIImage, topPadding: CGFloat, bottomPadding: CGFloat) -> UIImage {
        let newSize = CGSize(width: image.size.width, height: image.size.height + topPadding + bottomPadding)
        
        UIGraphicsBeginImageContextWithOptions(newSize, false, image.scale)
        let context = UIGraphicsGetCurrentContext()!
        
        context.setFillColor(UIColor.white.cgColor)
        context.fill(CGRect(origin: .zero, size: newSize))
        
        image.draw(in: CGRect(x: 0, y: topPadding, width: image.size.width, height: image.size.height))
        
        let newImage = UIGraphicsGetImageFromCurrentImageContext()!
        UIGraphicsEndImageContext()
        
        return newImage
    }
}
