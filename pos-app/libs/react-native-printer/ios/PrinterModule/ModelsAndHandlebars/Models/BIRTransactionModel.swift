//
//  BIRTransationModel.swift
//  RNPrinter
//
//  Created by <PERSON> on 2021/8/24.
//  Copyright © 2021 Facebook. All rights reserved.
//  receiptId

struct BIRTransactionModel: BaseTransactionModel {
    var eInvoiceUrl: String?
    
    var eInvoiceDescription: String?
    
    var enablePrintEInvoice: Bool?
    
    var a4ColumnTitleString: [String]?

    var isPrinterPaperWidth58: Bool?
    var key: String?
    var business: String?
    // As android BaseTransactionModel
    var logoImage: String?
    var receiptId: String?
    var showBarcode: Bool?
    var purchasedItems: [TransactionPurchasedItem]?
    @ZYString var defaultLoyaltyRatio: String?
    var qrCodeAboveInfo: String?
    var qrCodeUnderInfo: String?
    var isReceiptTitleBig: Bool?
    var receiptTitlePH: String?
    var columnWidths: [String]?
    var cashBackTxt: String?

    var storeName: String?
    // basic store info  (not half receipt)
    var address: String?
    var phone: String?
    var companyName: String?
    // under Company Name, will show if BIR is enable (not half receipt)
    var birCompanyName: String?
    // customer information part
    var customerInfo: String?
    // receipt info (not half receipt)
    var receiptDate: String?
    var voidNo: String?
    var reprintDate: String?
    var orderId: String?
    var receiptNumber: String?
    // under receiptNumber, will show is BIR is enable (not half receipt)
    var orderNumber: String?
    var minNumber: String?
    var serialNumber: String?
    var reasonString: String?
    var noteString: String?
    var orderNo: String?
    var orderNoName: String?

    // cashier information
    var cashierInfo: String?
    var registerNumber: String?
    var middleOrderTableTitleWithContent: String?
    var receiptTitle: String?
    var columnTitleString: [String]?

    // judge bir enable or disable , discount field should be null or not
    var subtotalTitle: String?
    @ZYString var subtotal: String?
    var discountTitle: String?
    var a4Discount: String?
    @ZYString var discount: String?
    
    var shippingType: String?
    var takeawayFeeValue: String?
    var takeawayFeeName: String?
    var shippingFeeValue: String?
    var shippingFeeName: String?
    
    var less12vatTitle: String?
    var less12vat: String?
    var smallOrderFeeTxt: String?
    var smallOrderFeeValue: String?
    var containerFeeTxt: String?
    var containerFeeValue: String?
    var adhocDiscountTitle: String?
    var adhocDiscount: String?
    var seniorDiscountTitle: String?
    var seniorDiscount: String?
    var pwdDiscountTitle: String?
    var pwdDiscount: String?
    var athleteAndCoachDiscountTitle: String?
    var athleteAndCoachDiscount: String?
    var soloParentDiscountTitle: String?
    var soloParentDiscount: String?
    var medalOfValorDiscountTitle: String?
    var medalOfValorDiscount: String?
    var vatOf12Title: String?
    var vatOf12: String?
    var amusementTaxTitle: String?
    var amusementTax: String?
    var serviceChargeTitle: String?
    var serviceCharge: String?
    var roundingTitle: String?
    var rounding: String?
    var showOrderSummary: Bool?
    var totalTitle: String?
    var total: String?
    var taxTitle: String?
    var tax: String?

    var amountOutStandingTitle: String?
    var amountOutStanding: String?
    var vatableSalesTitle: String?
    var vatableSales: String?
    var vatAmountTitle: String?
    var vatAmount: String?
    var vatExemptSalesTitle: String?
    var vatExemptSales: String?
    var zeroRatedSalesTitle: String?
    var zeroRatedSales: String?
    var birInfoList: [BIRInfoType]?
    var beepCashbackAmount: String?
    var storeCreditAmount: String?
    var showReceiptStoreCredit: Bool?
    var receiptStoreCreditTitleString: [String]?
    var loyaltyEarned: String?
    var loyaltySpent: String?
    var loyaltyBalance: String?
    var earnedTitle: String?
    var balanceTitle: String?
    var spentTitle: String?
    var cashbackExpirationDesc: String?

    // online delivery information
    var onlineChannelNotesTitle: String?
    var onlineChannelNotesContent: String?
    var onlinePickUpNoteTitle: String?
    var onlineOrderNoteContent: String?
    var preOrderNotes: String?
    var footerLabelString: String?
    var birAccrInfo: String?
    var accrNumber: String?
    var ptuNumber: String?
    var dateIssueNumber: String?
    var vatRegisterFooterInfo: String?
    var storehubPoweredInfo: String?
    var payment: [BIRPaymentType]?

    var enablePrintQRCode: Bool?
    var enableCashback: Bool?
    var cashbackUrl: String?
    var enablePrintCashback: Bool?
    var enablePrintMemberShip: Bool?
    var membershipSource: String?
    var membershipUrl: String?
    var qrCodeDesc: String?
    
    var showPurchasedItemsDiscount: Bool?
    var showVatSummary: Bool?
    
    // Pre order
    var showPreorderSummary: Bool?
    var depositAmountTitle: String?
    var unPaidBalanceTitle: String?
    var depositAmount: String?
    var unPaidBalance: String?
    var pickUpDate: String?
    var usingDiscountLayout: Bool?

    var receiptFontScale: Double?
    
    var membershipSmallTitle: String?
    var membershipBoildTitle: String?
    var membershipLargeContentTitle1: String?
    var membershipLargeContentTitle2: String?
    var membershipSmallBottomTitle: String?
}

struct BIRPaymentType: Codable {
    var paymentMethodName: String?
    @ZYString var cashTendered: String?
    @ZYString var amount: String?
    @ZYString var roundedAmount: String?
    var subType: String?
    var changeTitle: String?
    @ZYString var changeValue: String?
}

struct BIRInfoType: Codable {
    var name: String?
    var value: String?
    var needDedicatedSpace: Bool?
}
