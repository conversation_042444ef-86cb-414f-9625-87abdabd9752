//
//  KitchenTicketHBSModel.swift
//  RNPrinter
//
//  Created by <PERSON> on 2021/8/24.
//  Copyright © 2021 Facebook. All rights reserved.
//

struct KitchenTicketHBSModel: Codable {

    var isPrinterPaperWidth58: Bool?

    var isOpenOrder: Bool?
    var subOrders: [SubOrderModel]?
    var purchasedItems: [KitchenPurchasedItem]?
    var takeawayItems: [KitchenPurchasedItem]?
    var receiptDate: String?
    var bixOrderNumber: String?
    var bixReceiptDate: String?
    var pickUpId: String?
    var tableId: String?
    var total: Double?
    var isBeepOrder: Bool?
    var note: String?
    var noteTitle: String?
    /**
     * The tableId before change order table. To show the order has been change from previous table
     * to current one.
     */
    var previousTableId: String?
    var orderNumber: String?
    var orderNumberTitle: String?
    var extraOrderNumber: String?
    var orderTypeName: String?
    var takeawayTitle: String?
    var isTakeaway: Bool?
    var kitchenDocketVariantIsMultipleLine: Bool?
    var pax: String?
    var docketCountString: String?
    var expirationDateString: String?
    var employeeName: String?
    var docketTitle: String?
    var reprintTitle: String?

    var receiptFontScale: Double?
    
    var minContentHeight: Double?
}
