//
//  BaseTransationModel.swift
//  RNPrinter
//
//  Created by <PERSON> on 2021/8/24.
//  Copyright © 2021 Facebook. All rights reserved.
//

/// 将 String Int Double 解析为 String? 的包装器

@propertyWrapper struct ZYString: Codable {

    public var wrappedValue: String?
    
    init() {
        
    }
    
    public init(from decoder: Decoder) {
        var string: String?
        do {
            let container = try decoder.singleValueContainer()
            do {
                string = try container.decode(String.self)
            } catch {
                do {
                    string = String(try container.decode(Int.self))
                } catch {
                    do {
                        string = String(try container.decode(Double.self))
                    } catch {
                        // 如果不想要 String? 可以在此处给 string 赋值  = “”
                        string = nil
                    }
                }
            }
        } catch {
            debugPrint("😷😷😷😷😷😷😷😷❌❌❌ \(error)")
            string = nil
        }
        wrappedValue = string
    }
    
    public func encode(to encoder: Encoder) {
        var container = encoder.singleValueContainer()
        do {
            try container.encode(wrappedValue)
        } catch {
            debugPrint("❌❌❌ encode error: \(error) ")
        }
    }
}

extension KeyedDecodingContainer {
    func decode(_ type: ZYString.Type, forKey key: K) throws -> ZYString {
        if let value = try self.decodeIfPresent(type, forKey: key) {
            return value
        }
        return ZYString()
    }
}

struct TransactionPurchasedItem: Codable {
    var itemType: String?
    @ZYString var discount: String?
    var quantity: String?
    var subTotal: String?
    var itemName: String?
    var total: String?
    var options: String?
    var notes: String?
    var sn: String?
    var price: String?
    var promotions: [Promotions]?
    var itemDiscountName: String?
    var itemDiscountValue: String?
    var enableTakeaway: Bool?
    var takeawayTxt: String?
    var a4Total: String?
    @ZYString var takeawayCharge: String?
}

struct KitchenPurchasedItem: Codable {
    var quantity: Double?
    var options: [KitchenPurchasedItemOptins]?
    var optionDescription: String?
    var notes: String?
    var isUnitPrice: Bool?
    var title: String?
    var notesTitle: String?
    var isQuantityPositive: Bool?
    var quantityStr: String?
    var total: Double?
    var previousTotal: Double?
    var index: Int?
    var bixOrderNumber: String?
    var bixReceiptDate: String?
    var itemChannel: Int?
}

struct SubOrderModel: Codable {
    var purchasedItems: [KitchenPurchasedItem]?
    var takeawayItems: [KitchenPurchasedItem]?
    var note: String?
    var isLastSubOrder: Bool?
}

struct KitchenPurchasedItemOptins: Codable {
    var variationName: String?
    var optionValues: [String]?
}

struct Promotions: Codable {
    var promotionName: String?
    var discount: String?
}

