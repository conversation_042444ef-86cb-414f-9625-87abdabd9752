//
//  SMXReadingReportModel.swift
//  R<PERSON>rinter

struct SMXReadingReportModel: Codable {
    var isPrinterPaperWidth58: Bool?
    // ---------------------第一段---------------------------
    var businessName: String?
    var storeName: String?
    var storeAddress: String?
    var vatRegTin: String?
    var minNo: String?
    var serialNo: String?
    var reportTitle: String?
    var summaryLines: [CommonLineItem]?
    var serviceTypeSaleLines: [CommonLineItem]?
    // ---------------------第二段---------------------------
    var hourlySalesLines: [CommonLineItem]?
    var breakDownOfTenderLines: [CommonLineItem]?
    var incomeHeadSalesLines: [CommonLineItem]?
    var cashDrawerLines: [CommonLineItem]?
    var capitalizedLines: [CommonLineItem]?
    // ---------------------第N段---------------------------
    var mixSummaryLinesGroups: [[MixSummaryLineItem]]?
    // ---------------------最后一段---------------------------
    var totalLines: [CommonLineItem]?
    var dateLines: [CommonLineItem]?
    var footerLines: [CommonLineItem]?
}



