//
//  ShiftReportHBSModel.swift
//  RNPrinter
//
//  Created by <PERSON> on 2021/8/24.
//  Copyright © 2021 Facebook. All rights reserved.
//

struct ShiftReportHBSModel: Codable {

    var isPrinterPaperWidth58: Bool?

    var printedDate: String?
    var gstIdNo: String?
    var birAccrNo: String?
    var serialNo: String?
    var minNo: String?
    var ptu: String?
    var country: String?
    var birAccredited: Bool?
    var companyName: String?
    var storeName: String?
    var address: String?
    var phone: String?
    var reportTitle: String?
    var currency: String?
    var shiftOpenTime: String?
    var shiftCloseTime: String?
    var registerId: String?
    var reportDate: String?
    var manager: String?

    var paymentSummaryTitleStrings: PaymentSummaryTitleStrings?
    var payments: [Payment]?
    var shiftReportItemsHeaderStrings: [String]?
    var salesSummaryTitles: SalesSummaryTitle?
    var salesSummary: SalesSummary?
    var shiftReportDepositssHeaderStrings: [String]?
    var depositsSummary: [DepositsItem]?
    var totalDepositTitle: String?
    var totalDepositQuantity: String?
    var totalDepositAmount: String?
    var storeCreditSummaryHeaderStrings: [String]?
    var storeCreditSummaryTitles: StoreCreditSummaryTitles?
    var storeCreditSummary: StoreCreditSummary?
    var cancelAndDiscountSummaryHeaderStrings: [String]?
    var cancelAndDiscountSummaryTitles: CancelAndDiscountSummaryTitles?
    var cancelAndDiscountSummary: CancelAndDiscountSummary?
    var serviceChargeSummaryHeaderStrings: [String]?
    var serviceChargeSummaryTitles: ServiceChargeSummaryTitles?
    var serviceChargeSummary: ServiceChargeSummary?
    var taxSummaryHeaderStrings: [String]?
    var taxSummaryArray: [ShiftReportTaxSummary]?
    var cashDrawerSummaryHeaderStrings: [String]?
    var cashDrawerSummaryTitles: CashDrawerSummaryTitles?
    var cashDrawerSummary: CashDrawerSummary?

    var expectedDrawerTitle: String?
    var expectedDrawer: String?
    var actualDrawerTitle: String?
    var actualDrawer: String?
    var overShortTitle: String?
    var overShort: String?
    var openBy: String?
    var closeBy: String?
    var printedBy: String?
    var cashier: String?
}

struct PaymentSummaryTitleStrings: Codable {
    var salesTitle: String?
    var refundsTitle: String?
    var netTitle: String?
}

struct Payment: Codable {
    var paymentId: Int?
    var title: String?
    var headerStrings: [String]?
    var salesQuantity: String?
    var salesAmount: String?
    var refundsQuantity: String?
    var refundsAmount: String?
    var roundingTitle: String?
    var roundingAmount: String?
    var netQuantity: String?
    var netAmount: String?
}

struct DepositsItem: Codable {
    var title: String?
    var quantity: String?
    var amount: String?
}

struct SalesSummaryTitle: Codable {
    var totalSalesTitle: String?
    var totalRefundsTitle: String?
    var totalNetTitle: String?
}

struct SalesSummary: Codable {
    var salesQuantity: String?
    var salesAmount: String?
    var refundsQuantity: String?
    var refundsAmount: String?
    var netQuantity: String?
    var netAmount: String?
}

struct StoreCreditSummaryTitles: Codable {
    var discountTitle: String?
}

struct StoreCreditSummary: Codable {
    var discountQuantity: String?
    var discountAmount: String?
}

struct CancelAndDiscountSummaryTitles: Codable {
    var discountTitle: String?
    var cancelTxnsTitle: String?
}

struct ServiceChargeSummaryTitles: Codable {
    var salesTitle: String?
    var refundsTitle: String?
}

struct ServiceChargeSummary: Codable {
    var salesQuantity: String?
    var salesAmount: String?
    var refundsQuantity: String?
    var refundsAmount: String?
}

struct CancelAndDiscountSummary: Codable {
    var discountQuantity: String?
    var discountAmount: String?
    var cancelQuantity: String?
    var cancelAmount: String?
}

struct CashDrawerSummaryTitles: Codable {
    var openingAmountTitle: String?
    var cashSalesTitle: String?
    var cashDepositsTitle: String?
    var cashRefundsTitle: String?
    var payOutTitle: String?
    var payInTitle: String?
}

struct CashDrawerSummary: Codable {
    var openingAmount: String?
    var cashSalesQuantity: String?
    var cashSalesAmount: String?
    var cashDepositsQuantity: String?
    var cashDepositsAmount: String?
    var cashRefundsQuantity: String?
    var cashRefundsAmount: String?
    var payOutQuantity: String?
    var payOutAmount: String?
    var payInQuantity: String?
    var payInAmount: String?
}

struct ShiftReportTaxSummary: Codable {
    var amount: String?
    var taxRate: String?
    var taxName: String?
}
