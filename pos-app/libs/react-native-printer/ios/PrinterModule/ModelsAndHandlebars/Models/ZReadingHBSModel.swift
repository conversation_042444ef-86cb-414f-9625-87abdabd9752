//
//  ZReadingHBSModel.swift
//  RNPrinter
//
//  Created by <PERSON> on 2021/8/25.
//  Copyright © 2021 Facebook. All rights reserved.
//

struct ZReadingHBSModel: Codable {

    var isPrinterPaperWidth58: Bool?

    var country: String?
    var birAccredited: Bool?
    var printedDate: String?
    var storeName: String?
    var vatRegNo: String?
    var accredNo: String?
    var serialNo: String?
    var minNo: String?
    var ptu: String?
    var closeTime: String?
    var registerInfo: String?
    var zCount: String?
    var grossSales: String?
    var lessDiscount: String?
    var lessRefund: String?
    var totalDeductedVat: String?
    var netSales: String?
    var paymentSummaryList: [ZReadingPayment]?
    var totalSalesCount: String?
    var totalSalesAmount: String?
    var vatAbleSales: String?
    var vatAmount: String?
    var vatExemptSales: String?
    var zeroRatedSales: String?
    var amusementTax: String?
    var totalVatRelatedSales: String?
    var serviceCharge: String?
    var serviceChargeTax: String?
    var totalServiceCharge: String?
    var scDiscount: String?
    var pwdDiscount: String?
    var athleteAndCoachDiscountName: String?
    var athleteAndCoachDiscount: String?
    var medalOfValorDiscountName: String?
    var medalOfValorDiscount: String?
    var regularDiscount: String?
    var soloParentDiscount: String?
    var totalDiscount: String?
    var startORNumber: String?
    var endORNumber: String?
    var startTrxNumber: String?
    var endTrxNumber: String?
    var totalTrxCount: String?
    var salesTrxCount: String?
    var refundTrxCount: String?
    var refundAmount: String?
    var changeItemTrx: String?
    var changeItemAmount: String?
    var oldGross: String?
    var newGross: String?
    var oldNet: String?
    var newNet: String?
    var isRetail: Bool?
}

struct ZReadingPayment: Codable {
    var name: String?
    var count: String?
    var amount: String?
}
