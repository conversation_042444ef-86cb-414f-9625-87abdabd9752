//
//  PrinterModule.swift
//  UpUp
//
//  Created by <PERSON> on 2021/8/6.
//

/*
 The PrinterManager Methods and the params details of Method
 url: https://storehub.atlassian.net/wiki/spaces/TS/pages/430866716/Interactive+documentation+of+RN+and+Native+about+printers
 */
@objc
open class PrinterManager: NSObject {
    
    @objc
    public static let instance = PrinterManager()
    let printerSearcher = PrinterSearcher()

    private let searchPrinterQueue = DispatchQueue(label: "com.pos.searchQueue")
    private let semaphore = DispatchSemaphore(value: 1)
    
    let openCashDrawerQueue = DispatchQueue(label: "com.pos.openCashDrawerQueue", qos: .userInitiated)
    let requestToPrintQueue = DispatchQueue(label: "com.pos.requestToPrintQueue", qos: .utility)

    private override init () {
        // Prepare Init
        _ = QRBarCode.crateBarCode(codeString: "0345234523452345", size: CGSize(width: 380, height: 100))
    }

    /// initPrinter
    /// - Parameters:
    ///   - arr: [Any] as to [BasePrinter]
    ///   - resultCallback: resultCallback  Dictionary
    ///     eg:
    ///       [
    ///         errCode: 0,
    ///         printers: [BasePrinter]
    ///       ]
    ///     the details of BasePrinter, please check the BasePrinter Class
    ///   - completion: completion Dictionary
    ///     eg:
    ///       [
    ///         errCode: 0,
    ///         errMessage: Success
    ///       ]
    @objc
    open func initPrinter(receiptType: String, arr: [Any], resultCallback: @escaping ([String: Any]) -> Void, completion: @escaping ([String: Any]) -> Void) {
        searchPrinterQueue.async {[unowned self] in
            semaphore.wait()
            printerSearcher.initPrinters(receiptType: receiptType, arr) { result in
                let searchResult = SearchResult(errCode: PrinterErrorCode.SUCCESS, printers: result)
                if let dic = searchResult.toDic() {
                    resultCallback(dic)
                }
            } searchDone: {
                let result = PrinterErrorResult<String>(code: .SUCCESS).toDic()!
                completion(result)
                self.semaphore.signal()
            }
        }
    }

    /// searchPrinters
    /// - Parameters:
    ///   - resultCallback: resultCallback  Dictionary
    ///     eg:
    ///       [
    ///         errCode: 0,
    ///         printers: [BasePrinter]
    ///       ]
    ///     the details of BasePrinter, please check the BasePrinter Class
    ///   - completion: completion Dictionary
    ///     eg:
    ///       [
    ///         errCode: 0,
    ///         errMessage: Success
    ///       ]
    @objc
    open func searchPrinters(receiptType: String, resultCallback: @escaping ([String: Any]) -> Void, completion: @escaping ([String: Any]) -> Void) {
        if receiptType == "Thermal" {
            searchPrinterQueue.async {[unowned self] in
                semaphore.wait()
                printerSearcher.searchPrinters { result in
                    let searchResult = SearchResult(errCode: PrinterErrorCode.SUCCESS, printers: result)
                    if let dic = searchResult.toDic() {
                        resultCallback(dic)
                    }
                } searchDone: {
                    let result = PrinterErrorResult<String>(code: .SUCCESS).toDic()!
                    completion(result)
                    self.semaphore.signal()
                }
            }
        } else if receiptType == "A4" {
            printerSearcher.searchA4printers { result in
                let searchResult = SearchResult(errCode: PrinterErrorCode.SUCCESS, printers: result)
                if let dic = searchResult.toDic() {
                    resultCallback(dic)
                }
            } searchDone: {
                let result = PrinterErrorResult<String>(code: .SUCCESS).toDic()!
                completion(result)
            }
        }
    }

    /// requestToPrint
    /// - Parameters:
    ///   - arr: [Any]  as to  [PrintingDataModel]
    ///     eg:
    ///      [
    ///        {
    ///          printerId: aejfq1231se,
    ///          businessType:  TRANSACTION / SHIFT_REPORT / KITCHEN_TICKET /  Z_READING_REPORT / DALILY_REPORT
    ///          data:  the data of businessType
    ///        }
    ///      ]
    ///   - printTaskCompletion: printTaskCompletion  Dictionary
    ///     eg:
    ///      [
    ///         errCode: 0,
    ///         errMessage: Success
    ///         data: [PrinterTaskResult]
    ///      ]
    ///     the detail of PrinterTaskResult, please chek the PrinterTaskResult  Class
    @objc
    open func requestToPrint(arr: [Any], printTaskCompletion: @escaping ([String: Any]) -> Void) {
        printWithTime("[PrinterManager] requestToPrint")
        requestToPrintQueue.async {
            do {
                let data = try JSONSerialization.data(withJSONObject: arr, options: .prettyPrinted)
                let jsonString = String(data: data, encoding: .utf8)
                if let jsonString = jsonString {
                    let jsonObj = JSON(parseJSON: jsonString).arrayValue
                    var printingDataArr: [PrintingDataModel] = []
                    for i in 0..<jsonObj.count {
                        let element = jsonObj[i]
                        let printingData = PrintingDataModel(json: element)
                        printingDataArr.append(printingData)
                    }
                    
                    let requestTask = RequestTask(type: .PRINTING)
                    // execute print task
                    requestTask.execute(printingDataArr: printingDataArr) { (printerErrorResult: PrinterErrorResult<[PrinterTaskResult]>) in
                        let result = printerErrorResult.toDic() ?? [:]
                        printTaskCompletion(result)
                        printWithTime("[PrinterManager] printing result:\(result)")
                    }
                } else {
                    let result = PrinterErrorResult<[PrinterTaskResult]>(code: .PRINTING_PARAMS_ERROR, data: []).toDic() ?? [:]
                    printTaskCompletion(result)
                    printWithTime("[PrinterManager] Invalid data")
                }
            } catch {
                let result = PrinterErrorResult<[PrinterTaskResult]>(code: .PRINTING_PARAMS_ERROR, data: []).toDic() ?? [:]
                printTaskCompletion(result)
                printWithTime("[PrinterManager] Catch invalid data")
            }
        }
    }
    
    /// setStoreInfo
    /// - Parameters:
    ///   - dic: Dictionary
    ///    eg:
    ///    [
    ///      logo: [ bitmap arr]
    ///    ]
    ///    the bitmap arr can be save as a image
    ///   - completion: completion Dictionary
    ///    eg:
    ///     [
    ///         errCode: 0,
    ///         errMessage: Success
    ///     ]
    @objc
    open func setStoreInfo(dic: [AnyHashable: Any], completion: ([String: Any]) -> Void) {
        let arr = dic["logo"] as? Array ?? []
        
        if arr.isEmpty {
            if let logoPath = FileUtil.getLogoPath() {
                FileUtil.deleteFile(path: logoPath)
            }
            let result = PrinterErrorResult<String>(code: .SUCCESS).toDic() ?? [:]
            completion(result)
            return
        }
        
        if FileUtil.saveLogo(arr: arr) {
            let result = PrinterErrorResult<String>(code: .SUCCESS).toDic() ?? [:]
            completion(result)
        } else {
            let result = PrinterErrorResult<String>(code: .ERROR).toDic() ?? [:]
            completion(result)
        }
    }

    /// openCashDrawer
    /// - Parameters:
    ///   - arr: [Any]  as to  [PrintingDataModel]
    ///    eg:
    ///     [
    ///         printerId: 123rasdf213radf
    ///     ]
    ///   - completion: completion Dictionary
    ///    eg:
    ///     [
    ///         errCode: 0,
    ///         errMessage: Success
    ///     ]
    @objc
    open func openCashDrawer(arr: [Any], completion: @escaping ([String: Any]) -> Void) {
        openCashDrawerQueue.async { [unowned self] in
            printWithTime("\(Date()) >>>>>>: openCashDrawer")

            do {
                let data = try JSONSerialization.data(withJSONObject: arr, options: .prettyPrinted)
                let jsonString = String(data: data, encoding: .utf8)
                if let jsonString = jsonString {
                    let jsonObj = JSON(parseJSON: jsonString).arrayValue
                    var printingDataArr: [PrintingDataModel] = []
                    for i in 0..<jsonObj.count {
                        let element = jsonObj[i]
                        let printingData = PrintingDataModel(json: element)
                        printingDataArr.append(printingData)
                    }
                    let requestTask = RequestTask(type: .CASH_DRAWER)
                    requestTask.execute(printingDataArr: printingDataArr) { printerErrorResult in
                        let result = printerErrorResult.toDic() ?? [:]
                        completion(result)
                    }
                } else {
                    let result = PrinterErrorResult<[PrinterTaskResult]>(code: .OPEN_DRAWER_PARAMS_ERROR, data: []).toDic() ?? [:]
                    completion(result)
                }
            } catch {
                let result = PrinterErrorResult<[PrinterTaskResult]>(code: .OPEN_DRAWER_PARAMS_ERROR, data: []).toDic() ?? [:]
                completion(result)
            }
        }
    }
    
    /// buzz
    /// - Parameters:
    ///   - arr: [Any]  as to  [PrintingDataModel]
    ///    eg:
    ///     [
    ///         printerId: 123rasdf213radf
    ///     ]
    ///   - completion: completion Dictionary
    ///    eg:
    ///     [
    ///         errCode: 0,
    ///         errMessage: Success
    ///     ]
    @objc
    open func buzz(arr: [Any], completion: @escaping ([String: Any]) -> Void) {
        requestToPrintQueue.async { [unowned self] in
            semaphore.wait()
            printWithTime("\(Date()) >>>>>>: buzz")
            do {
                let data = try JSONSerialization.data(withJSONObject: arr, options: .prettyPrinted)
                let jsonString = String(data: data, encoding: .utf8)
                if let jsonString = jsonString {
                    printWithTime("Buzz JSON === ")
                    let jsonObj = JSON(parseJSON: jsonString).arrayValue
                    var printingDataArr: [PrintingDataModel] = []
                    for i in 0..<jsonObj.count {
                        let element = jsonObj[i]
                        if !element.isEmpty {
                            let printingData = PrintingDataModel(json: element)
                            printingDataArr.append(printingData)
                        }
                    }
                    
                    if !printingDataArr.isEmpty {
                        let requestTask = RequestTask(type: .BUZZ)
                        requestTask.execute(printingDataArr: printingDataArr) { printerErrorResult in
                            let result = printerErrorResult.toDic() ?? [:]
                            completion(result)
                        }
                    } else {
                        let result = PrinterErrorResult<[PrinterTaskResult]>(code: .BUZZ_PARAMS_ERROR, data: []).toDic() ?? [:]
                        completion(result)
                    }
                } else {
                    let result = PrinterErrorResult<[PrinterTaskResult]>(code: .BUZZ_PARAMS_ERROR, data: []).toDic() ?? [:]
                    completion(result)
                }
            } catch {
                let result = PrinterErrorResult<[PrinterTaskResult]>(code: .BUZZ_PARAMS_ERROR, data: []).toDic() ?? [:]
                completion(result)
            }
            semaphore.signal()
        }
    }
    
    @objc
    open func findPrinterById(_ printerId: String, completion: @escaping (_ result: [String: Any], _ didFind: Bool) -> Void) {
        let printers = PrinterManager.instance.printerSearcher.searchedPrinters
        if let printer = printers.first(where: { $0.printerId == printerId }) {
            printWithTime("printerId: " + printerId + " exist")
            completion([:], true)
        } else {
            let result = PrinterErrorResult<[PrinterTaskResult]>(code: .PRINTER_NOT_FOUND, data: []).toDic() ?? [:]
            completion(result, false)
        }
    }

    @objc
    open func connectTcp(ip: String, port: UInt16, timeout: TimeInterval, completion: @escaping ([String: Any]) -> Void) {
        TCPConnectionManager.shared.connect(ip: ip, port: port, timeout: timeout) {
            let result = PrinterErrorResult<String>(code: .SUCCESS).toDic()!
            completion(result)
        } failureCallback: {
            let result = PrinterErrorResult<[PrinterTaskResult]>(code: .TCP_CONNECT_ERROR, data: []).toDic() ?? [:]
            completion(result)
        }
    }
    
    @objc public func udpSearchXPrinters(completion: @escaping ([String: Any]) -> Void) {
        Task {
            let xprinterUdp = XPrinterUdp()
            
            var printers: [XPrinterUdpData] = []
            
            printers = await xprinterUdp.findNetworkPrinters()
            
            let printersArray = printers.map { printer -> [String: Any] in
                return [
                    "macAddress": printer.macAddress,
                    "lanIp": printer.ip,
                    "netmask": printer.netmask,
                    "gateway": printer.gateway,
                    "enabledDHCP": printer.enabledDHCP
                ]
            }
            
            DispatchQueue.main.async {
                let result: [String: Any] = [
                    "errCode": 0,  // 成功
                    "errMessage": "",
                    "data": printersArray
                ]
                completion(result)
            }
        }
    }
    
    @objc public func restoreLanXPrintersByUDPWithCompletion(_ completion: @escaping ([[String : Any]]) -> Void) {
        Task {
            let result = await self.restoreLanXPrintersByUDP()
            DispatchQueue.main.async {
                completion(result)
            }
        }
    }
    
    func restoreLanXPrintersByUDP() async -> [[String : Any]] {
        let xprinterUdp = XPrinterUdp()
        var xprinterUdpArray: [XPrinterUdpData] = []
        xprinterUdpArray = await xprinterUdp.findNetworkPrinters()
        
        let validPrinters = xprinterUdpArray.filter { udpData in
            return !udpData.ip.isEmpty && !udpData.macAddress.isEmpty
        }
        
        var result: [BasePrinter] = []
        for udpPrinter in validPrinters {
            var newLanXPrinter = true
            
            for searchedPrinter in PrinterManager.instance.printerSearcher.searchedPrinters {
                guard let searchedPrinterIp = searchedPrinter.lanIp else { continue }
                let searchedPrinterMacAddress = searchedPrinter.macAddress.uppercased()
                let udpPrinterMacAddress = udpPrinter.macAddress.uppercased()
                let sameMacAddress = searchedPrinterMacAddress == udpPrinterMacAddress
                
                let sameIp = searchedPrinterIp == udpPrinter.ip
                if sameIp || sameMacAddress {
                    newLanXPrinter = false
                }
                
                let isLanXPrinter = searchedPrinter.printerConnectType ==  PrinterConnectType.LAN.rawValue && searchedPrinter.printerModelType == PrinterModelType.LANXPRINTER.rawValue
                if isLanXPrinter && sameMacAddress {
                    if !sameIp {
                        // ipChanged, update LanXPrinter
                        // should not update printerId
                        searchedPrinter.printerName = "LAN：" + udpPrinter.ip
                        searchedPrinter.lanIp = udpPrinter.ip
                        searchedPrinter.dhcp = udpPrinter.enabledDHCP
                        // log
                        searchedPrinter.logFoundByUdpEvent()
                    }
                    result.append(searchedPrinter)
                }
            }
            
            if newLanXPrinter {
                // add new printer
                // in case that the offline printers didn't have the instance
                let lanXPrinter = LANXPrinter()
                lanXPrinter.printerName = "LAN：" + udpPrinter.ip
                lanXPrinter.lanIp = udpPrinter.ip
                lanXPrinter.lanPort = 9100
                lanXPrinter.macAddress = udpPrinter.macAddress.uppercased()
                lanXPrinter.dhcp = udpPrinter.enabledDHCP
                lanXPrinter.printerConnectType = PrinterConnectType.LAN.rawValue
                lanXPrinter.printerModelType = PrinterModelType.LANXPRINTER.rawValue
                lanXPrinter.model = "LANXPRINTER"
                lanXPrinter.generatePrinterId()
                PrinterManager.instance.printerSearcher.searchedPrinters.append(lanXPrinter)
                // log
                lanXPrinter.logFoundByUdpEvent()
                result.append(lanXPrinter)
            }
        }
        
        let resultArray = result.map { printer in
            printer.toDic()!
        }
        
        return resultArray
    }
    
//    @objc public func setXPrinterConfig(_ config: [String: Any], completion: @escaping ([String: Any]) -> Void) {
//        guard let macAddress = config["macAddress"] as? String,
//              let ip = config["ip"] as? String,
//              let netmask = config["netmask"] as? String,
//              let gateway = config["gateway"] as? String,
//              let enabledDHCP = config["enabledDHCP"] as? Bool else {
//            completion(["success": false, "message": "Invalid printer configuration"])
//            return
//        }
//        
//        let printerData = XPrinterUdpData(
//            macAddress: macAddress,
//            ip: ip,
//            netmask: netmask,
//            gateway: gateway,
//            enabledDHCP: enabledDHCP
//        )
//        
//        Task {
//            let printer = XPrinterUdp()
//            await printer.setPrinterData(printerData)
//            
//            DispatchQueue.main.async {
//                completion(["success": true])                
//            }
//        }
//    }
}
