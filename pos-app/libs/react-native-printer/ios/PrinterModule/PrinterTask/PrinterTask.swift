//
//  PrinterTask.swift
//  RNPrinter
//
//  Created by <PERSON> on 2021/10/24.
//  Copyright © 2021 Facebook. All rights reserved.
//

enum GeneratorType {
    case image
    case data
    case cashDrawEpson
    case string
}

enum PrintingTaskType: String {
    // Printing
    case Receipt
    case ShiftReport
    case Kitchen
    case ZReadingReport
    case DailyReport
    case DynamicBeepQR
    case AyalaMallReport
    case SMEODReport
    case SMXReadingReport
    case ORTIGASEODReport
    case EIInvoiceQR
    // Other
    case CashDrawer
    case Buzz
}

final class PrinterTask: BasePrinterTask {

    var imageDataGeneraotors: [DataGenerator<UIImage>] = []
    var dataGenerators: [DataGenerator<Data>] = []
    var stringGenerators: [DataGenerator<String>] = []
    var generatorType: GeneratorType = .image
    var printingTaskType: PrintingTaskType?

    init(taskId: String, printerTaskId: String) {
        super.init()
        self.taskId = taskId
        self.printerTaskId = printerTaskId
    }

    required init(from decoder: Decoder) throws {
        fatalError("init(from:) has not been implemented")
    }

    deinit {
        printWithTime("[PrinterTask] deinit")
    }
}
