//
//  PrintingDataModel.swift
//  RNPrinter
//
//  Created by <PERSON> on 2021/10/24.
//  Copyright © 2021 Facebook. All rights reserved.
//

final class PrintingDataModel: Codable {

    var printerId: String!
    var businessType: String!
    var workflowId: String?
    var data: JSON?
    var uri: [String]?

    init(json: JSON) {
        printerId = json["printerId"].stringValue
        businessType = json["businessType"].stringValue
        workflowId = json["workflowId"].stringValue
        data = json["data"]
        uri = json["uri"].arrayValue.map { $0.stringValue }
    }
}
