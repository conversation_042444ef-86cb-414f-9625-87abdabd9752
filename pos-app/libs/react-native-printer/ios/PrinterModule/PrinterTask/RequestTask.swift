//
//  RequestTask.swift
//  UpUp
//
//  Created by <PERSON> on 2021/8/12.
//

final class RequestTask {

    let lock = NSLock()

    var taskId: String?
    var totalPrinterTaskCount: Int = 0
    var results: [PrinterTaskResult] = []

    private var finishRequestTaskCallback: ((PrinterErrorResult<[PrinterTaskResult]>) -> Void)?
    private var printerTasks: [PrinterTask] = []
    var printerRequestType: PrinterRequestType = .PRINTING

    let dispatchGroup = DispatchGroup()
    let dispatchQueue = DispatchQueue(label: "com.pos.RequestTask.dispatchQueue\(Int(arc4random() % 1000) + 1)", attributes: .concurrent)

    init(type: PrinterRequestType) {
        printerRequestType = type
        generateTaskId()
    }

    func execute(printingDataArr: [PrintingDataModel], completion: @escaping (PrinterErrorResult<[PrinterTaskResult]>) -> Void) {
        finishRequestTaskCallback = completion
        splitToPrinterTask(printingDataArr: printingDataArr)
    }

    private func getUnexpectedResult(data: [PrinterTaskResult] = []) -> PrinterErrorResult<[PrinterTaskResult]> {
        var unexpectedResult: PrinterErrorResult<[PrinterTaskResult]>!
        switch printerRequestType {
        case .PRINTING:
            unexpectedResult = PrinterErrorResult<[PrinterTaskResult]>(code: .PRINTING_OCCURRED_ERROR, data: data)
        case .CASH_DRAWER:
            unexpectedResult = PrinterErrorResult<[PrinterTaskResult]>(code: .OPEN_DRAWER_ERROR, data: data)
        case .BUZZ:
            unexpectedResult = PrinterErrorResult<[PrinterTaskResult]>(code: .BUZZ_OCCURRED_ERROR, data: data)
        }
        return unexpectedResult
    }

    private func splitToPrinterTask(printingDataArr: [PrintingDataModel]) {
        var parserError: PrinterErrorCode?

        for i in 0..<printingDataArr.count {
            let printingData = printingDataArr[i]
            debugPrint("=== printingData businessType === \(String(describing: printingData.businessType))")
            debugPrint(PrinterManager.instance.printerSearcher.searchedPrinters)
            PrinterManager.instance.printerSearcher.searchedPrinters.forEach { printer in
                if printer.printerId == printingData.printerId {
                    let printerTask = PrinterTask(taskId: taskId!, printerTaskId: "\(i)")
                    let workflowId = printingData.workflowId ?? ""

                    var baseModelParser: BaseModelParser!
                    if printerRequestType == .PRINTING {
                        guard let data = printingData.data else {
                            let result = PrinterErrorResult<[PrinterTaskResult]>(code: .PRINTING_PARAMS_ERROR, data: [])
                            finishRequestTaskCallback?(result)
                            return
                        }

                        let businessType = printingData.businessType
                        switch businessType {
                        case PrintingBussinessType.TRANSACTION.rawValue:
                            printerTask.printingTaskType = .Receipt
                            let country = data["country"].stringValue
                            let birAccredited = data["birAccredited"].boolValue
                            debugPrint("country = \(printer.printerPaperWidth.rawValue)")
                            if country == CountryType.PH.rawValue && birAccredited {
                                let birTransactionModel = data.parse(to: BIRTransactionModel.self)
                                if let birTransactionModel = birTransactionModel {
                                    baseModelParser = TransactionHBSModelParser(model: birTransactionModel, paperWidth: printer.printerPaperWidth)
                                }
                            } else {
                                let transactionHBSModel = data.parse(to: TransactionHBSModel.self)
                                if let transactionHBSModel = transactionHBSModel {
                                    baseModelParser = TransactionHBSModelParser(model: transactionHBSModel, paperWidth: printer.printerPaperWidth)
                                }
                            }
                        case PrintingBussinessType.KITCHEN_TICKET.rawValue:
                            printerTask.printingTaskType = .Kitchen
                            let kitchenTicketHBSModel = data.parse(to: KitchenTicketHBSModel.self)
                            if let kitchenTicketHBSModel = kitchenTicketHBSModel {
                                baseModelParser = KitchenTicketHBSModelParser(model: kitchenTicketHBSModel, paperWidth: printer.printerPaperWidth, isLabel: printer.isLabelPrinter)
                            }
                        case PrintingBussinessType.SHIFT_REPORT.rawValue:
                            printerTask.printingTaskType = .ShiftReport
                            let shiftReportHBSModel = data.parse(to: ShiftReportHBSModel.self)
                            if let shiftReportHBSModel = shiftReportHBSModel {
                                baseModelParser = ShiftReportHBSModelParser(model: shiftReportHBSModel, paperWidth: printer.printerPaperWidth)
                            }
                        case PrintingBussinessType.Z_READING_REPORT.rawValue:
                            printerTask.printingTaskType = .ZReadingReport
                            let zReadingHBSModel = data.parse(to: ZReadingHBSModel.self)
                            if let zReadingHBSModel = zReadingHBSModel {
                                baseModelParser = ZReadingReportModelParser(model: zReadingHBSModel, paperWidth: printer.printerPaperWidth)
                            }
                        case PrintingBussinessType.DAILY_REPORT.rawValue:
                            printerTask.printingTaskType = .DailyReport
                            let dailyReportHBSModel = data.parse(to: DailyReportHBSModel.self)
                            if let dailyReportHBSModel = dailyReportHBSModel {
                                baseModelParser = DailyReportHBSModelParser(model: dailyReportHBSModel, paperWidth: printer.printerPaperWidth)
                            }
                        case PrintingBussinessType.AYALA_MALL_REPORT.rawValue:
                            printerTask.printingTaskType = .AyalaMallReport
                            let ayalaMallReportHBSModel = data.parse(to: AyalaMallReportHBSModel.self)
                            if let ayalaMallReportHBSModel = ayalaMallReportHBSModel {
                                baseModelParser = AyalaMallReportHBSModelParser(model: ayalaMallReportHBSModel, paperWidth: printer.printerPaperWidth)
                            }
                        case PrintingBussinessType.SM_EOD_REPORT.rawValue:
                            printerTask.printingTaskType = .SMEODReport
                            let smEodReportModel = data.parse(to: SMEODReportModel.self)
                            print("data:\(data)")
                            if let smEodReportModel = smEodReportModel {
                                baseModelParser = SMEODReportModelParser(model: smEodReportModel, paperWidth: printer.printerPaperWidth)
                            }
                        case PrintingBussinessType.SM_XReading_REPORT.rawValue:
                            printerTask.printingTaskType = .SMXReadingReport
                            let smXReadingReportModel = data.parse(to: SMXReadingReportModel.self)
                            print("data:\(data)")
                            if let smXReadingReportModel = smXReadingReportModel {
                                baseModelParser = SMXReadingReportModelParser(model: smXReadingReportModel, paperWidth: printer.printerPaperWidth)
                            }
                        case PrintingBussinessType.ORTIGAS_EOD_REPORT.rawValue:
                            printerTask.printingTaskType = .ORTIGASEODReport
                            let ortigasEodReportModel = data.parse(to: OrtigasEODReportHBSModel.self)
                            print("data:\(data)")
                            if let ortigasEodReportModel = ortigasEodReportModel {
                                baseModelParser = OrtigasEODReportModelParser(model: ortigasEodReportModel, paperWidth: printer.printerPaperWidth)
                            }
                        case PrintingBussinessType.DYNAMIC_BEEP_QR.rawValue:
                            printerTask.printingTaskType = .DynamicBeepQR
                            let dynamicBeepQRModel = data.parse(to: DynamicBeepQRModel.self)
                            if let dynamicBeepQRModel = dynamicBeepQRModel {
                                baseModelParser = DynamicBeepQRModelParser(model: dynamicBeepQRModel, paperWidth: printer.printerPaperWidth)
                            }
                        case PrintingBussinessType.E_INVOICE_QR.rawValue:
                            printerTask.printingTaskType = .EIInvoiceQR
                            let eInvoiceQRModel = data.parse(to: EInvoiceQRModel.self)
                            if let eInvoiceQRModel = eInvoiceQRModel {
                                baseModelParser = EInvoiceQRModelParser(model: eInvoiceQRModel, paperWidth: printer.printerPaperWidth)
                            }
                        default:
                            break
                        }

                        guard baseModelParser != nil else {
                            parserError = .PRINTING_PARAMS_ERROR
                            return
                        }

                        // Bitmap as UIImage
                        if printer.printerOutputType == .Bitmap { // Epson, LANBixolon
                            printerTask.imageDataGeneraotors = baseModelParser.getImageGennerators()
                            printerTask.generatorType = .image
                        } else if printer.printerOutputType == .HTMLString { // AirPrinter
                            printerTask.generatorType = .string
                            printerTask.stringGenerators = baseModelParser.getStringGenerators()
                        } else { // LANXPrinter, Star
                            // BitmpBuff as commands
                            var printerParser: PrinterParser!
                            switch printer.printerModelType {
                            case PrinterModelType.STAR.rawValue:
                                var isMpop = false
                                if let portName = printer.portName {
                                    if portName.lowercased().contains("mpop") {
                                        isMpop = true
                                    }
                                }
                                printerParser = StarPrinterParser(isMpop: isMpop)
                            default:
                                // default xprinter
                                printerParser = EPOSPrinterParser()
                            }
                            printerTask.dataGenerators = printerParser.getGenerators(from: baseModelParser)
                            printerTask.generatorType = .data
                        }
                    } else if printerRequestType == .CASH_DRAWER {
                        printerTask.printingTaskType = .CashDrawer
                        if printer.printerOutputType == .BitmapBuffer {
                            printerTask.dataGenerators = DataGenerator<Data>.getOpenCashData(printerType: printer.printerModelType)
                        } else {
                            if printer.printerModelType == PrinterModelType.EPSON.rawValue {
                                printerTask.generatorType = .cashDrawEpson
                            } else {
                                parserError = .SUCCESS
                                return
                            }
                        }
                    } else if printerRequestType == .BUZZ {
                        printerTask.printingTaskType = .Buzz
                        // only xprinter has buzz
                        if printer.printerOutputType == .BitmapBuffer && printer.printerModelType == PrinterModelType.LANXPRINTER.rawValue {
                            printerTask.dataGenerators = DataGenerator<Data>.getBuzzData()
                        } else {
                            parserError = .SUCCESS
                            return
                        }
                    }
                    printerTasks.append(printerTask)

                    dispatchGroup.enter()
                    printWithTime("[RequestTask] enter group, taskId:\(self.taskId!)")
                    printer.print(task: printerTask) {[self] printerTaskResult in
                        lock.lock()
                        results.append(printerTaskResult)
                        lock.unlock()
                        dispatchGroup.leave()
                        printWithTime("[RequestTask] leave group, taskId:\(self.taskId!)")

                        logPrintResult(printerTaskResult: printerTaskResult, printer: printer, workflowId: workflowId)
                    }
                }
            }
        }

        dispatchGroup.notify(queue: dispatchQueue) {
            printWithTime("[RequestTask] group finished, taskId:\(self.taskId!)")

            self.printerTasks = []

            var result: PrinterErrorResult<[PrinterTaskResult]>

            if let parserError = parserError {
                result = PrinterErrorResult<[PrinterTaskResult]>(code: parserError, data: self.results)
            } else {
                var hasError = false
                for tempResult in self.results {
                    if(tempResult.errCode != 0) {
                        hasError = true
                        break
                    }
                }

                // Summarizing failure results
                if(hasError) {
                    result = self.getUnexpectedResult(data: self.results)
                } else {
                    result = PrinterErrorResult<[PrinterTaskResult]>(code: .SUCCESS, data: self.results)
                }
            }
            self.finishRequestTaskCallback?(result)
        }
    }

    // ?
    private func generateTaskId() {
        let timeInterval: TimeInterval = Date().timeIntervalSince1970
        let millisecond = CLongLong(round(timeInterval*1000))
        taskId = "R" + "\(millisecond)" + "\(Int(arc4random() % 100) + 1)"
    }

    private func append(result: PrinterTaskResult) {
        totalPrinterTaskCount = totalPrinterTaskCount + 1
        debugPrint("totalPrinterTaskCount ====== \(totalPrinterTaskCount)")
        results.append(result)
    }

    private func logPrintResult(printerTaskResult: PrinterTaskResult, printer: BasePrinter, workflowId: String) {
        if printerTaskResult.errCode == 0 {
            switch printerRequestType {
            case .PRINTING:
                printer.logPrintSuccess(event: PrinterExecutionEvent.PRINT_SUCCEEDED.rawValue, workflowId: workflowId)
            case .CASH_DRAWER:
                printer.logPrintSuccess(event: PrinterExecutionEvent.OPEN_CASHDRAWER_SUCCEEDED.rawValue, workflowId: workflowId)
            case .BUZZ:
                printer.logPrintSuccess(event: PrinterExecutionEvent.BUZZ_SUCCEEDED.rawValue, workflowId: workflowId)
            }
        } else {
            printer.logPrintFailure(printerTaskResult: printerTaskResult, printerRequestType: printerRequestType, workflowId: workflowId)
        }
    }

    deinit {
        debugPrint("R ====== RequestTask deinit")
    }
}
