//
//  BasePrinter.swift
//  UpUp
//
//  Created by <PERSON> on 2021/8/11.
//

/*
 * Enums
 *
 */
enum PrinterSearchType {
    case All

    // INIT Will Except Search LAN Printer
    case INIT
}

enum PrinterConnectType: String {
    case LAN = "LAN"
    case Bluetooth = "Bluetooth"
    case USB = "USB"
}

enum PrinterOutputType: String {
    case TextBase = "TextBase"
    case Bitmap = "Bitmap"
    case BitmapBuffer = "BitmapBuffer"
    case HTMLString = "HTMLString"
}

enum PrinterModelType: String {
    case BIXOLON = "BIXOLON"
    case STAR = "STAR"
    case LANXPRINTER = "LANXPRINTER"
    case EPSON = "EPSON"
    case AIRPRINTER = "AIRPRINTER"
}

enum PrinterState {
    case RUNNING
    case IDLE
}

enum PrinterPaperWidth: CGFloat {
    case Print25 = 250
    case Print58 = 380
    case Print80 = 580
    // epson t88
    case Print545 = 545
    // epson t82
    case Print500 = 500
    case A4PaperWidth = 0
}

enum PrintingBussinessType: String {
    case TRANSACTION = "TRANSACTION"
    case SHIFT_REPORT = "SHIFT_REPORT"
    case KITCHEN_TICKET = "KITCHEN_TICKET"
    case Z_READING_REPORT = "Z_READING_REPORT"
    case DAILY_REPORT = "DAILY_REPORT"
    case DYNAMIC_BEEP_QR = "DYNAMIC_BEEP_QR"
    case AYALA_MALL_REPORT = "AYALA_MALL_REPORT"
    case SM_EOD_REPORT = "SM_EOD_REPORT"
    case SM_XReading_REPORT = "SM_XReading_REPORT"
    case ORTIGAS_EOD_REPORT = "ORTIGAS_EOD_REPORT"
    case E_INVOICE_QR = "E_INVOICE_QR"
}
enum CountryType: String {
    case TH = "TH"
    case MY = "MY"
    case PH = "PH"
}

public enum PrinterRequestType: String {
    case PRINTING = "Printing"
    case CASH_DRAWER = "CashDrawer"
    case BUZZ = "Buzz"
}

enum PrinterExecutionStatus: String {
    case Initial
    case Disabled
    case Available
    case Offline
    case Working
    case IDLE
    case Unusable
}

enum PrinterExecutionEvent: String {
    case FOUND
    case FOUND_BY_INIT
    case FOUND_BY_SEARCH
    case PRINT_SUCCEEDED
    case OPEN_CASHDRAWER_SUCCEEDED
    case BUZZ_SUCCEEDED
    case WRITE_FAILED
    case WRITE_TIMEOUT
    case CONNECT_FAILED
    case WRITE_DONE
    case INNER_ERROR
    case CLOSED
    case EVICTED
    case FOUND_BY_UDP
}

// swiftlint:disable all
class BasePrinter: AbstarctPrinter {

    var printerOutputType: PrinterOutputType!

    var printerPaperWidth: PrinterPaperWidth!

    var printerId: String = ""

    // PrinterId generating agrithmtic is changed to use macAddress, this is the prvious argrithmic using IP,Port
    var prevPrinterId: String = ""

    var migrationPrinterIdV3: String = ""

    var printerName: String = ""

    // star printer use port name to connect
    var portName: String?

    // Epson printer use target to connect
    var target: String = ""

    var lanIp: String?

    var lanPort: Int?

    var bluetoothMacAddress: String?

    var macAddress: String = ""
    
    var dhcp: Bool?

    // Eaual to mac address
    var uniqueId: String?

    var printerConnectType: String = ""

    // Printer brand (e.g., BIXOLON, STAR, LANXPRINTER, EPSON, AIRPRINTER)
    var printerModelType: String = ""

    // Specific model of the printer
    var model: String = ""

    var isLabelPrinter: Bool = false

    var isBuiltInPrinter: Bool = false

    var printerTaskCount = 0

    var printerState: PrinterState = .IDLE

    // Printer execution events logs
    var executionPreviousStatus: String = PrinterExecutionStatus.Initial.rawValue
    var executionCurrentStatus: String = PrinterExecutionStatus.Initial.rawValue {
        willSet {
            executionPreviousStatus = executionCurrentStatus
//            executionCurrentStatus = newValue
        }
    }
    var executiontEvent: String = PrinterExecutionEvent.FOUND.rawValue
    var errorCode: Int = 0
    var taskType: String?
    var additionalMessage: [String: Any]?

    // The fields to decode
    private enum CodingKeys: String, CodingKey {
        case printerId
        case prevPrinterId
        case migrationPrinterIdV3
        case printerName
        case lanIp
        case lanPort
        case macAddress
        case dhcp
        case bluetoothMacAddress
        case printerConnectType
        case printerModelType
        case isLabelPrinter
        case isBuiltInPrinter
        case model
        case executionPreviousStatus
        case executionCurrentStatus
        case executiontEvent
        case errorCode
        case taskType
    }

    func print(task: PrinterTask, completion: ((PrinterTaskResult) -> Void)?) {
    }

    func disconnect() {
    }

    func generatePrinterId() {
        let ip = lanIp ?? ""
        let port = lanPort ?? 0
        let connectType = printerConnectType
        let macAdress = macAddress ?? ""
        let bluetoothMacAddress = bluetoothMacAddress ?? ""
        let portName = portName ?? ""

        if (macAdress != "" || bluetoothMacAddress != "") {
            printerId = "\(macAddress)\(bluetoothMacAddress)\(connectType)".hashed(.sha1) ?? ""
        } else {
            printerId = "\(ip)\(port)\(connectType)\(target)\(portName)".hashed(.sha1) ?? ""
        }
        // This is for the Tags migration
        generatePrevPrinterId()
        generateMigrationPrinterIdV3()
    }

    func generatePrevPrinterId() {
        let ip = lanIp ?? ""
        let port = lanPort ?? 0
        let connectType = printerConnectType
        let macAdress = macAddress

        prevPrinterId = "\(ip)\(port)\(connectType)\(macAddress)\(target)\(portName)".hashed(.sha1) ?? ""
    }

    func generateMigrationPrinterIdV3() {
        var generatedMigrationPrinterIdV3 = ""
        if printerConnectType == PrinterConnectType.LAN.rawValue {
            generatedMigrationPrinterIdV3 = lanIp ?? ""
        } else if printerConnectType == PrinterConnectType.Bluetooth.rawValue {
            generatedMigrationPrinterIdV3 = bluetoothMacAddress ?? ""
        }
        migrationPrinterIdV3 = generatedMigrationPrinterIdV3
    }

    func getBasePrinter() -> BasePrinter {

        let basePrinter = BasePrinter()
        basePrinter.printerId = printerId
        basePrinter.prevPrinterId = prevPrinterId
        basePrinter.migrationPrinterIdV3 = migrationPrinterIdV3
        basePrinter.printerName = printerName
        basePrinter.lanIp = lanIp
        basePrinter.lanPort = lanPort
        basePrinter.macAddress = macAddress
        basePrinter.dhcp = dhcp
        basePrinter.bluetoothMacAddress = bluetoothMacAddress
        basePrinter.printerConnectType = printerConnectType
        basePrinter.printerModelType = printerModelType
        basePrinter.isLabelPrinter = isLabelPrinter
        basePrinter.isBuiltInPrinter = isBuiltInPrinter
        basePrinter.model = model
        basePrinter.executionPreviousStatus = executionPreviousStatus
        basePrinter.executionCurrentStatus = executionCurrentStatus
        basePrinter.executiontEvent = executiontEvent
        basePrinter.errorCode = errorCode

        return basePrinter
    }
}

//MARK: - Log Execution Event
extension BasePrinter {
    func logExecutionEvent(level: String? = "info", result: String? = "Succeed", workflowId: String? = "") {
        if var dict = self.toDic() {
            dict["printerOutputType"] = printerOutputType.rawValue
            dict["printerPaperWidth"] = printerPaperWidth.rawValue
            dict["level"] = level
            dict["result"] = result
            dict["workflowId"] = workflowId
            LogManager.sharedInstance().logPrinterEvent(dict)
        }
    }

    func logFoundEvent() {
        errorCode = 0
        executiontEvent = PrinterExecutionEvent.FOUND.rawValue
        executionCurrentStatus = PrinterExecutionStatus.Available.rawValue
        logExecutionEvent()
    }
    
    func logFoundByUdpEvent() {
        errorCode = 0
        executiontEvent = PrinterExecutionEvent.FOUND_BY_UDP.rawValue
        executionCurrentStatus = PrinterExecutionStatus.Available.rawValue
        logExecutionEvent()
    }

    func logInitEvent() {
        errorCode = 0
        executiontEvent = PrinterExecutionEvent.FOUND_BY_INIT.rawValue
        executionCurrentStatus = PrinterExecutionStatus.Available.rawValue
        logExecutionEvent()
    }

    func logSearchEvent() {
        errorCode = 0
        executiontEvent = PrinterExecutionEvent.FOUND_BY_SEARCH.rawValue
        executionCurrentStatus = PrinterExecutionStatus.Available.rawValue
        logExecutionEvent()
    }

    func logPrintSuccess(event: String, workflowId: String? = "") {
        errorCode = 0
        executiontEvent = event
        executionCurrentStatus = PrinterExecutionStatus.Working.rawValue
        logExecutionEvent(level: "info", result: "Succeed", workflowId: workflowId)
    }

    func logPrintFailure(printerTaskResult: PrinterTaskResult, printerRequestType: PrinterRequestType, workflowId: String) {
        let errorCode = printerTaskResult.errCode ?? 0
        let additionalMessage = printerTaskResult.additionalMessage
        
        self.errorCode = errorCode
        switch errorCode {
        case 10110: // PRINTING_OCCURRED_ERROR
            executiontEvent = PrinterExecutionEvent.WRITE_FAILED.rawValue
            executionCurrentStatus = PrinterExecutionStatus.Unusable.rawValue
        case 10612: // PRINTER_PRINTING_TIMEOUT
            executiontEvent = PrinterExecutionEvent.WRITE_TIMEOUT.rawValue
            executionCurrentStatus = PrinterExecutionStatus.Unusable.rawValue
        case 10617: //PRINTER_CONNECTION_ERROR:
            executiontEvent = PrinterExecutionEvent.CONNECT_FAILED.rawValue
            executionCurrentStatus = PrinterExecutionStatus.Offline.rawValue
        default:
            executiontEvent = PrinterExecutionEvent.INNER_ERROR.rawValue
            executionCurrentStatus = PrinterExecutionStatus.Unusable.rawValue
        }

        taskType = printerRequestType.rawValue
        self.additionalMessage = additionalMessage

        logExecutionEvent(level: "error", result: "Failed", workflowId: workflowId)
    }

    func logEvictedEvent() {
        errorCode = 0
        executiontEvent = PrinterExecutionEvent.EVICTED.rawValue
        executionCurrentStatus = PrinterExecutionStatus.Disabled.rawValue
        logExecutionEvent()
    }
}
