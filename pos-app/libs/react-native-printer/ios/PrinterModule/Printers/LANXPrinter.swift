//
//  LANXPrinter.swift
//  UpUp
//
//  Created by <PERSON> on 2021/8/12.
//

/*
 * LANXPrinter
 */
final class LANXPrinter: BasePrinter {

    private let xprinterSerialQueue = DispatchQueue(label: "com.printer.xprinter")

    private var lanConnection: LanXPrinterConnectable!
    private let semaphore = DispatchSemaphore(value: 1)

    private let taskQueue = DispatchQueue(label: "com.xprinter.taskQueue")

    override init() {
        super.init()

        printerConnectType = PrinterConnectType.LAN.rawValue
        printerPaperWidth = .Print80
        printerOutputType = .BitmapBuffer
        isLabelPrinter = false
        printerModelType = PrinterModelType.LANXPRINTER.rawValue

        let useNewLanConnection = UserDefaults.standard.bool(forKey: "RN_iOS_USE_NEW_LAN_CONNECTION")
        let newLanPrintingTimeout = UserDefaults.standard.double(forKey: "RN_iOS_NEW_LAN_PRINTING_TIMEOUT")

        if useNewLanConnection {
            if newLanPrintingTimeout > 0 {
                lanConnection = NewLanConnection(printingTimeout: newLanPrintingTimeout)
            } else {
                lanConnection = NewLanConnection()
            }
            LogManager.sharedInstance().logXPrinterEvent("use-NewLanConnection")
        } else {
            lanConnection = LanConnection()
            LogManager.sharedInstance().logXPrinterEvent("use-LanConnection")
        }
    }

    required init(from decoder: Decoder) throws {
        fatalError("init(from:) has not been implemented")
    }

    // many printing
    override func print(task: PrinterTask, completion: ((PrinterTaskResult) -> Void)?) {
        increaseTaskCount()

        xprinterSerialQueue.async { [task, completion, weak self] in
            guard let weakSelf = self else { return }

            printWithTime("[LANXPrinter] Start task, printingTaskType:\(task.printingTaskType?.rawValue ?? "") printerId:\(String(weakSelf.printerId.prefix(5)))*, taskId:\(task.taskId ?? "")")

            let ip = weakSelf.lanIp ?? ""
            let port = weakSelf.lanPort ?? 0

            weakSelf.semaphore.wait()
            
            // ensure that the semaphore is released only once
            var signalSent = false
            let signalSemaphore = {
                if !signalSent {
                    signalSent = true
                    weakSelf.semaphore.signal()
                }
            }
            
            weakSelf.lanConnection.sendDataWith(task: task, ip: ip, port: UInt16(port)) { [weak self] in
                guard let weakSelf = self else {
                    signalSemaphore()
                    return
                }
                
                let status = WrappedPrinterError(errorCode: .SUCCESS)
                weakSelf.reduceTaskCount()
                completion?(weakSelf.finished(task: task, wrappedPrinterError: status))
                
                signalSemaphore()
            } fail: { [weak self] (error: WrappedPrinterError) in
                guard let weakSelf = self else {
                    signalSemaphore()
                    return
                }
                weakSelf.reduceTaskCount()
                completion?(weakSelf.finished(task: task, wrappedPrinterError: error))
                
                signalSemaphore()
            }
        }
    }

    private func increaseTaskCount() {
        taskQueue.sync {
            printerState = .RUNNING
            printerTaskCount += 1
            printWithTime("[LANXPrinter] increase printerTaskCount to: \(printerTaskCount)")
        }
    }

    private func reduceTaskCount() {
        var shouldDisconnect = false
        taskQueue.sync {
            printerTaskCount -= 1
            if printerTaskCount == 0 {
                shouldDisconnect = true
            }
        }
        
        printWithTime("[LANXPrinter] reduce printerTaskCount to: \(printerTaskCount)")

        if shouldDisconnect {
            xprinterSerialQueue.async { [weak self] in
                guard let self = self else { return }
                self.lanConnection.disconnect()
                self.printerState = .IDLE
            }
        }
    }

    private func finished(task: PrinterTask, wrappedPrinterError: WrappedPrinterError) -> PrinterTaskResult {
        let errorCode: PrinterErrorCode = wrappedPrinterError.errorCode
        let additionalMessage = wrappedPrinterError.additionalMessage
        
        let printerTaskResult = PrinterTaskResult()
        printerTaskResult.printerId = printerId
        printerTaskResult.taskId = task.taskId
        printerTaskResult.errCode = errorCode.rawValue
        printerTaskResult.errMessage = errorCode.getMessage()
        printerTaskResult.printer = getBasePrinter()
        if let intPrinterTaskId = Int(task.printerTaskId ?? "") {
            printerTaskResult.taskIndex = intPrinterTaskId
        }
        printerTaskResult.additionalMessage = additionalMessage

        printWithTime("[LANXPrinter] finish task, printingTaskType:\(task.printingTaskType?.rawValue ?? ""), printerId:\(String(self.printerId.prefix(5)))*, taskId:\(task.taskId ?? ""), result:\(errorCode.rawValue)")
        return printerTaskResult
    }

    deinit {
        printWithTime("[LANXPrinter] deinit")
    }
}

let lanXPrinterPort = 9100
final class LANXPrinterSearchHelper: NSObject, YLTCPBroadcasterDelegate {

    private var searchCallback: (([BasePrinter]) -> Void)?
    private var isSearchDone = false
    private var macAddressHelperArray: [XPrinterMacAddressFetcher] = []

    func search(callBack: @escaping ([BasePrinter]) -> Void, done: @escaping () -> Void) {
        isSearchDone = false
        searchCallback = callBack

        printWithTime("[LANXPrinter] Search")

        let currentIp = YLTCPUtils.localIp() ?? ""
        let currentSubnetMask = YLTCPUtils.localSubnetMask() ?? ""

        // Error flow
        if currentIp.isEmpty || currentSubnetMask.isEmpty {
            isSearchDone = true
            macAddressHelperArray.removeAll()
            done()
        } else {
            // Normal flow
            let broadcaster = YLTCPBroadcaster(ip: currentIp, subnetMask: currentSubnetMask)
            broadcaster.delegate = self
            broadcaster.scan(withPort: Int32(lanXPrinterPort), timeoutInterval: 1) { [weak self] _ in
                guard let self = self else {
                    done()
                    return
                }
                printWithTime("[LANXPrinter] search done ==")
                isSearchDone = true
                macAddressHelperArray.removeAll()
                done()
            }
        }
    }

    func tcpBroadcaster(_ broadcaster: YLTCPBroadcaster, didFoundHost host: String) {
        let nsHost = NSString(string: host)
        let range = nsHost.range(of: ".", options: .backwards)
        let subnet = nsHost.substring(from: range.location + 1)

        if subnet != "1" && subnet != "255" {
            let lanXPrinter = LANXPrinter()
            lanXPrinter.printerName = "LAN：" + host
            lanXPrinter.lanIp = host
            lanXPrinter.lanPort = lanXPrinterPort
            lanXPrinter.printerModelType = PrinterModelType.LANXPRINTER.rawValue
            lanXPrinter.model = "LANXPRINTER"

            let helper = XPrinterMacAddressFetcher()
            macAddressHelperArray.append(helper)
            helper.getLANXPrinterMacAddress(printer: lanXPrinter) { [weak self] lanXPrinter in
                guard let self = self else { return }
                lanXPrinter.generatePrinterId()
                self.searchCallback?([lanXPrinter])
            }
        }
    }
}
