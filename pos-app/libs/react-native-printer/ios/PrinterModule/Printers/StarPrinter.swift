//
//  StarPrinter.swift
//  RNPrinter
//
//  Created by <PERSON> on 2022/6/6.
//

import Foundation

final class StarPrinter: BasePrinter {
    
    private let executor: DispatchQueue = DispatchQueue(label: "com.printer.epson")
    
    private var starConnection: StarConnection!
    private let lock = NSLock()
    
    override init() {
        super.init()
        printerModelType = PrinterModelType.STAR.rawValue
        printerPaperWidth = .Print80
        
        // Bitmap uses ImageGenerator. BitmapBuffer uses DataGenerator
        printerOutputType = .BitmapBuffer
        isLabelPrinter = false
        
        starConnection = StarConnection()
    }
    
    required init(from decoder: Decoder) throws {
        fatalError("init(from:) has not been implemented")
    }
    
    override func print(task: PrinterTask, completion: ((PrinterTaskResult) -> Void)?) {
        
        increaseTaskCount()
        
        executor.async { [unowned task, unowned self] in
            debugPrint("StarPrinter \(printerId) print task \(task.taskId)")
//            DebugNotificationManager.shared.sendDebugNotification(title: "StarPrinter", message: "开始sendDataWith")
            lock.lock()
            starConnection.sendDataWith(task: task, portName: portName ?? "") {
                self.reduceTaskCount()
                completion?(self.finished(task: task, printerErrorCode: .SUCCESS))
                self.lock.unlock()
            } fail: { errorCode in
                self.reduceTaskCount()
                completion?(self.finished(task: task, printerErrorCode: errorCode))
                self.lock.unlock()
            }
        }
    }
    
    private func increaseTaskCount() {
        printerState = .RUNNING
        printerTaskCount = printerTaskCount + 1
    }
    
    private func reduceTaskCount() {
        printerTaskCount = printerTaskCount - 1
        if (printerTaskCount == 0) {
            starConnection.disconnect()
            printerState = .IDLE
        }
    }
    
    private func finished(task: PrinterTask, printerErrorCode: PrinterErrorCode) -> PrinterTaskResult {
        let printerTaskResult = PrinterTaskResult()
        printerTaskResult.printerId = printerId
        printerTaskResult.taskId = task.taskId
        printerTaskResult.errCode = printerErrorCode.rawValue
        printerTaskResult.errMessage = printerErrorCode.getMessage()
        printerTaskResult.printer = getBasePrinter()
        if let intPrinterTaskId = Int(task.printerTaskId ?? "") {
            printerTaskResult.taskIndex = intPrinterTaskId
        }
        
        debugPrint("StarPrinter finish printing taskID === \(task.taskId) == \(printerErrorCode.rawValue)" )
        return printerTaskResult
    }

    deinit {
        debugPrint("StarPrinter deinit")
    }

}

final class StarPrinterSearchHelper: NSObject {
    func search(callBack: @escaping ([BasePrinter]) -> Void, done: @escaping (PrinterErrorCode) -> Void) {
        debugPrint("Search Star Printer == ")
        
        var searchPrinterResult: [PortInfo]? = nil
        
        do {
            searchPrinterResult = try SMPort.searchPrinter(target: "ALL:") as? [PortInfo]
            debugPrint("Star Printer Version == \(SMPort.starIOVersion())")
        } catch {
            done(PrinterErrorCode.STAR_PRINTER_SEARCH_ERROR)
        }
        
        guard let portInfoArray: [PortInfo] = searchPrinterResult else {
            done(PrinterErrorCode.SUCCESS)
            return
        }
        
        var starPrinters = [StarPrinter]()

        for portInfo: PortInfo in portInfoArray {
            debugPrint("portInfo.portName === \(portInfo.portName ?? "")")
            debugPrint("portInfo.macAddress === \(portInfo.macAddress ?? "")")
            debugPrint("portInfo.modelName === \(portInfo.modelName ?? "")")
            
            let starPrinter = StarPrinter()
            starPrinter.portName = portInfo.portName

            let firmwareInfoModelName = StarCommand.getModelNameFromFirmwareInfo(byPortName: portInfo.portName)
            let tempPrinterName = firmwareInfoModelName != "" ? firmwareInfoModelName : portInfo.portName ?? "STAR"
                        
            if (portInfo.macAddress == "") {
                // bluetooth printers, there's no Mac Address
                starPrinter.printerConnectType = PrinterConnectType.Bluetooth.rawValue
                starPrinter.bluetoothMacAddress = portInfo.portName
                // starPrinter.uniqueId = portInfo.portName
                starPrinter.printerName = "STAR BT: " + tempPrinterName
                starPrinter.macAddress = portInfo.portName
                if let portName = starPrinter.portName {
                    if portName.lowercased().contains("mpop") {
                        starPrinter.printerPaperWidth = .Print58
                        let mPopPortChangedNotificationName = Notification.Name("MPopPortChangedNotification")
                        NotificationCenter.default.post(name: mPopPortChangedNotificationName, object: nil)
                    }
                }
            } else {
                // LAN
                starPrinter.macAddress = portInfo.macAddress
                if (portInfo.portName.split(separator: ":").count > 1) {
                    let ip = portInfo.portName.split(separator: ":")[1]
                    starPrinter.lanIp = String(ip)
                    starPrinter.lanPort = lanXPrinterPort
                }
                let ip = starPrinter.lanIp ?? ""
                starPrinter.printerName = "STAR " + tempPrinterName + ": " + ip
                if portInfo.portName.contains("BT") {
                    starPrinter.printerConnectType = PrinterConnectType.Bluetooth.rawValue
                } else {
                    starPrinter.printerConnectType = PrinterConnectType.LAN.rawValue
                }
            }
            starPrinter.model = tempPrinterName
            
            starPrinter.generatePrinterId()
            starPrinters.append(starPrinter)
        }
        debugPrint("found Star Printer count == \(starPrinters.count)")
        callBack(starPrinters)
        debugPrint("Star Printer search done ==")
        done(PrinterErrorCode.SUCCESS)
    }
}
