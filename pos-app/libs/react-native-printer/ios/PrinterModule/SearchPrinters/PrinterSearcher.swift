//
//  PrinterScanner.swift
//  RNPrinter
//
//  Created by <PERSON> on 2021/10/23.
//  Copyright © 2021 Facebook. All rights reserved.
//

final class PrinterSearcher: NSObject {

    var searchedPrinters: [BasePrinter] = []

    private let lanXPrinterSearchHelper = LANXPrinterSearchHelper()
    private let lanBixolonPrinterSearchHelper = LANBixolonPrinterSearchHelper()
    private let epsonPrinterSearchHelper = EpsonPrinterSearchHelper()
    private let starPrinterSearchHelper = StarPrinterSearchHelper()
    private let airPrinterSearchHelper = AirPrinterSearchHelper()
    private let lock = NSLock()

    private let dispatchGroup = DispatchGroup()
    private let dispatchQueue = DispatchQueue(label: "com.pos.searchTasks", attributes: .concurrent)


    func initPrinters(receiptType:String,
                      _ printerArray: [Any],
                      searchResultCallback: @escaping ([BasePrinter]) -> Void,
                      searchDone: @escaping () -> Void) {
        
        if isSearchedPrintersPrinting() {
            searchResultCallback(searchedPrinters)
            searchDone()
            return
        }
        
        for printer in searchedPrinters {
            printer.logEvictedEvent()
        }
        searchedPrinters = []
        let filteredPrinters = filterAndGeneratePrinterList(printerArray: printerArray)
        searchedPrinters.append(contentsOf: filteredPrinters)
        if receiptType == "A4" {
            searchResultCallback(filteredPrinters)
            searchDone()
        } else if receiptType == "Thermal" {
            search(by: .INIT, searchResultCallback: searchResultCallback, searchDone: searchDone)
        }
    }

    func searchPrinters(searchResultCallback: @escaping ([BasePrinter]) -> Void, searchDone: @escaping () -> Void) {
        
        if isSearchedPrintersPrinting() {
            searchResultCallback(searchedPrinters)
            searchDone()
            printWithTime("[PrinterSearcher] is in searching so trigger search done")
            return
        }

        for printer in searchedPrinters {
            printer.logEvictedEvent()
        }
        searchedPrinters = []
        search(by: .All, searchResultCallback: searchResultCallback, searchDone: searchDone)
    }
    
    func searchA4printers(searchResultCallback: @escaping ([BasePrinter]) -> Void, searchDone: @escaping () -> Void) {
        searchedPrinters = []
        airPrinterSearchHelper.search { [weak self] searchResult in
            guard let self = self else { return }
            self.searchedPrinters.append(contentsOf: searchResult)
            searchResultCallback(searchResult)
        } done: { _ in
            searchDone()
        }
    }
    
    private func isSearchedPrintersPrinting() -> Bool {
        var isWorking = false
        for sp in searchedPrinters {
            if sp.printerTaskCount > 0 {
                isWorking = true
                break
            }
        }
        return isWorking
    }

    private func search(by type: PrinterSearchType,
                        searchResultCallback: @escaping ([BasePrinter]) -> Void,
                        searchDone: @escaping () -> Void) {
    
        // Bixolon
        dispatchQueue.async(group: dispatchGroup, execute: {[unowned self] in
            dispatchGroup.enter()
            lanBixolonPrinterSearchHelper.search { searchResult in
                let result = self.appendAndDeduplicatePrinters(by: type, printers: searchResult)
                searchResultCallback(result)
            } done: { _ in
                self.dispatchGroup.leave()
            }
        })
        
        // EPSON
        dispatchQueue.async(group: dispatchGroup, execute: {[unowned self] in
            dispatchGroup.enter()
            epsonPrinterSearchHelper.search { searchResult in
                let result = self.appendAndDeduplicatePrinters(by: type, printers: searchResult)
                searchResultCallback(result)
            } done: {
                self.dispatchGroup.leave()
            }
        })

        // Star
        dispatchQueue.async(group: dispatchGroup, execute: {[unowned self] in
            dispatchGroup.enter()
            starPrinterSearchHelper.search { searchResult in
                let result = self.appendAndDeduplicatePrinters(by: type, printers: searchResult)
                searchResultCallback(result)
            } done: { _ in
                self.dispatchGroup.leave()
            }
        })

        // Search complete
        dispatchGroup.notify(queue: dispatchQueue) { [weak self] in
    
            guard let self = self else { return }
            
            // ALl means searching LAN XPrinter as well
            if type == .All {
                self.dispatchQueue.async(group: self.dispatchGroup, execute: { [unowned self] in
                    // XPrinter
                    self.lanXPrinterSearchHelper.search { searchResult in
                        let result = self.appendAndDeduplicatePrinters(by: type, printers: searchResult)
                        searchResultCallback(result)
                    } done: {
                        debugPrint("====== all searchDone =======")
                        searchDone()
                        
                        for printer in self.searchedPrinters {
                            printer.logSearchEvent()
                        }
                    }
                })
            } else {
                debugPrint("====== init searchDone =======")
                searchDone()

                for printer in self.searchedPrinters {
                    printer.logInitEvent()
                }
            }
        }
    }

    private func filterAndGeneratePrinterList(printerArray: [Any]) -> [BasePrinter] {
        var printers: [BasePrinter] = []

        for element in printerArray {
            let dic = JSON(element).dictionary
            if let dic = dic {

                let printerConnectType = dic["printerConnectType"]?.stringValue
                let printerModelType = dic["printerModelType"]?.stringValue

                guard let printerConnectType = printerConnectType  else {
                    continue
                }
                guard let printerModelType = printerModelType  else {
                    continue
                }

                let lanType = PrinterConnectType.LAN.rawValue
                let lanXPrinterType = PrinterModelType.LANXPRINTER.rawValue
                if printerConnectType == lanType && printerModelType == lanXPrinterType {
                    let lanXPrinter = LANXPrinter()
                    lanXPrinter.printerId = dic["printerId"]?.string ?? ""
                    lanXPrinter.printerName = dic["printerName"]?.string ?? ""
                    lanXPrinter.lanIp = dic["lanIp"]?.string ?? ""
                    lanXPrinter.lanPort = dic["lanPort"]?.int ?? 0
                    lanXPrinter.printerModelType = dic["printerModelType"]?.string ?? ""
                    lanXPrinter.model = "LANXPRINTER"
                    lanXPrinter.prevPrinterId = dic["prevPrinterId"]?.string ?? ""
                    lanXPrinter.macAddress = dic["macAddress"]?.string ?? ""
                    lanXPrinter.migrationPrinterIdV3 = dic["migrationPrinterIdV3"]?.string ?? ""
                    printers.append(lanXPrinter)
                } else if printerModelType == PrinterModelType.AIRPRINTER.rawValue {
                    let airPrinter = AirPrinter()
                    airPrinter.printerId = dic["printerId"]?.string ?? ""
                    airPrinter.printerName = dic["printerName"]?.string ?? ""
                    airPrinter.lanIp = dic["lanIp"]?.string ?? ""
                    airPrinter.model = dic["model"]?.string ?? "AIRPRINTER"
                    airPrinter.prevPrinterId = dic["prevPrinterId"]?.string ?? ""
                    airPrinter.macAddress = dic["macAddress"]?.string ?? ""
                    airPrinter.migrationPrinterIdV3 = dic["migrationPrinterIdV3"]?.string ?? ""
                    printers.append(airPrinter)
                }
            }
        }
        return printers
    }

    private func appendAndDeduplicatePrinters(by type: PrinterSearchType, printers: [BasePrinter]) -> [BasePrinter] {
        lock.lock()
        // Add directly to the printer array to be searched
        searchedPrinters.append(contentsOf: printers)
        var foundLANPrinters = [BasePrinter]()

        // Filter out LAN's from all searched printers
        foundLANPrinters = searchedPrinters.filter { sp in
            return sp.printerConnectType == PrinterConnectType.LAN.rawValue
        }

        // Filter out Bluetooth and USB-connected printers
        let otherPrinters = searchedPrinters.filter { sp in
            return sp.printerConnectType != PrinterConnectType.LAN.rawValue
        }

        // lan printers count > 1 need dedup the printers
        var needDeleteIndexs = [Int]()
        if foundLANPrinters.count > 1 {
            // Iterate over all LAN printers
            for i in 0..<foundLANPrinters.count {
                let iTemp = foundLANPrinters[i]
                for j in (i + 1)..<foundLANPrinters.count {
                    let jTemp = foundLANPrinters[j]
                    if iTemp.lanIp == jTemp.lanIp &&
                        iTemp.lanPort == jTemp.lanPort {

                        // if iTemp is lanXPrinter, jTemp is not
                        // if iTemp is not lanXPrinter, jTemp is not
                        // The XPrinter with the same IP and port as the Printer of other brands should be deleted
                        if iTemp.printerModelType == PrinterModelType.LANXPRINTER.rawValue {
                            needDeleteIndexs.append(i)
                        } else {
                            needDeleteIndexs.append(j)
                        }
                    }
                }
            }
        }

        // Group together LAN printers that do not need to be deleted
        var newLanPriners = [BasePrinter]()
        for k in 0..<foundLANPrinters.count {
            if !needDeleteIndexs.contains(k) {
                newLanPriners.append(foundLANPrinters[k])
            }
        }

        var result = [BasePrinter]()
        // Add other printers
        result.append(contentsOf: otherPrinters)
        // Add the LAN printer after deduplication
        result.append(contentsOf: newLanPriners)
        searchedPrinters = result

        prepareHtml2Image(with: searchedPrinters)

        lock.unlock()
        return searchedPrinters
    }
}

extension PrinterSearcher {

    /// prepareHtml2Image
    /// prepare Html2Iamge count  by the count of searchedPrinters
    /// - Parameter printers: BasePrinters that follow the Hashable
    private func prepareHtml2Image(with printers: [BasePrinter]) {
        Html2ImageWarmUper.shared.numberOfWamedUpObjects = printers.count
    }
}

