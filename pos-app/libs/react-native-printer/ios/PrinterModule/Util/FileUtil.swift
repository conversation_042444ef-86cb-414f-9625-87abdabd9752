//
//  FileUtil.swift
//  RNPrinter
//
//  Created by <PERSON> on 2021/10/12.
//  Copyright © 2021 Facebook. All rights reserved.
//

import UIKit

final class FileUtil {

    // TODO: - need delete
    static func getTestPath() -> String? {
        let subPath = "/test"
        if let documentPath = getDocumentPath() {
            return documentPath + subPath
        } else {
            return nil
        }
    }

    static func getLogoPath() -> String? {
        let subPath = "/logo"
        if let documentPath = getDocumentPath() {
            return documentPath + subPath
        } else {
            return nil
        }
    }

    static func getLogoBase64() -> String? {
        if let logoPath = getLogoPath() {
            let image = UIImage(contentsOfFile: logoPath)
            return image?.jpegData(compressionQuality: 1)?.base64EncodedString()
        } else {
            return nil
        }
    }

    static func getDocumentPath() -> String? {
        let paths = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true)
        let documentPath = paths.first
        return documentPath
    }
    
    static func getCachePath() -> String? {
        let paths = NSSearchPathForDirectoriesInDomains(.cachesDirectory, .userDomainMask, true)
        let cachePath = paths.first
        return cachePath
    }

    static func createFile(path: String, data: Data) -> Bool {
       return FileManager.default.createFile(atPath: path, contents: data, attributes: nil)
    }

    static func fileExists(path: String?) -> Bool {
        if let path = path {
            return FileManager.default.fileExists(atPath: path)
        } else {
            return false
        }
    }

    static func saveLogo(arr: [Any]) -> Bool {
        let bytes: [UInt8] = arr.map { element in
            return element as? UInt8 ?? 0
        }

        let data = Data.init(bytes: bytes, count: bytes.count)
        
        let image = UIImage(data: data)
        // 160px * height
        let width = image?.size.width ?? 0
        let height = image?.size.height ?? 0
        let newHeight = 160 * height / width
        
        let newData = image?.imageWithNewSize(size: CGSize(width: 160, height: newHeight))?.pngData()
        
        if let logoPath = getLogoPath(), let newData = newData  {
            debugPrint("===== logoPath =====")
            debugPrint(logoPath + " size:\(newData.count)")
            
            return createFile(path: logoPath, data: newData)
        } else {
            return false
        }
    }
    
    static func deleteFile(path: String) {
        do {
            try FileManager.default.removeItem(atPath: path)
        } catch {
            debugPrint("fail to delete logo:\(error)")
        }
    }
    
    static func saveImage(image:UIImage, path: String) -> Bool {
        let data = image.pngData()
        var subPath = path;
        if let cachePath = getCachePath() {
            subPath = "\(cachePath)/\(path)"
        }
        
        debugPrint("subPath:\(subPath)")
        if let data = data {
            return createFile(path: subPath, data: data)
        } else {
            return false
        }
    }
}
