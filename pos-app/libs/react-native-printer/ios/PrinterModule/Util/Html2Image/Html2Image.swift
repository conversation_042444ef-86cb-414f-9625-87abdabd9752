//
//  Html2Image.swift
//  UpUp
//
//  Created by <PERSON> on 2021/9/2.
//

import WebKit

/**
 https://github.com/tsayen/dom-to-image
 */

final class Html2Image: NSObject {
    private let Dome2Image = "dom2image"

    private var webView: WKWebView!

    private var success: ((UIImage) -> Void)?
    private var fail: (() -> Void)?

    private var htmlString = ""
    private var pixelRatio = 1.0
    
    private var fontScale: Double = 1.0

    override init() {
        super.init()
        initWeb()
    }

    convenience init(width: CGFloat) {
        self.init()
        initWeb(width: width)
    }

    private func initWeb(width: CGFloat = 580) {
        DispatchQueue.main.async {[self] in
            let frame = CGRect(x: 0, y: 0, width: width, height: 667)
            webView = WKWebView(frame: frame)
            webView.navigationDelegate = self

            let js = Dome2Image.getJSFromBundle(name: SHPOSBundle)
            let userScript = WKUserScript.init(source: js, injectionTime: .atDocumentEnd, forMainFrameOnly: false)
            webView.configuration.userContentController.addUserScript(userScript)

            webView.addMessageHandler(with: "dom2ImageSuccess") { [weak self] message in
                guard let weakSelf = self else { return }
                // debugPrint("message =========== \(message.name)")
                weakSelf.dom2ImageSuccess(base64: message.body as? String ?? "")
            }

            webView.addMessageHandler(with: "dom2ImageFail") { [weak self] message in
                guard let weakSelf = self else { return }
                // debugPrint("message =========== \(message.body)")
                weakSelf.dom2ImageFail(error: message.body as? String ?? "")
            }
        }
    }

    @discardableResult
    func setWidth(_ width: CGFloat, fontScale: Double = 1.0) -> Self {
        self.fontScale = fontScale
        
        DispatchQueue.main.async {[self] in
            ///  250 is label width
            ///  receipt 380 is the base of 580, then scale up 1.526 ≈ 1.53 to 580
            var realWidth = width
            
            switch width {
            case PrinterPaperWidth.Print80.rawValue:
                // xprinter & star printer
                realWidth = 380
                pixelRatio = 1.53
            case PrinterPaperWidth.Print545.rawValue:
                // epson T82
                realWidth = 380
                pixelRatio = 1.45
            case PrinterPaperWidth.Print500.rawValue:
                // epson T88
                realWidth = 380
                pixelRatio = 1.31
            default:
                break
            }
    
            webView.frame.size.width = realWidth
        }
        return self
    }
    
    func setHeight(_ height: CGFloat) -> Self {
        DispatchQueue.main.async { [self] in
            webView.frame.size.height = height
        }
        return self
    }

    func convert(htmlString: String, success: @escaping ((UIImage) -> Void), fail: @escaping (() -> Void)) {
        self.success = success
        self.fail = fail
        self.htmlString = htmlString
        DispatchQueue.main.async { [weak self] in
            guard let weakSelf = self else { return }
            weakSelf.webView.loadHTMLString(htmlString, baseURL: nil)
        }
    }

    deinit {
        // debugPrint("Html2Image Deinit")
    }
}

extension Html2Image: WKNavigationDelegate {
    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        if htmlString == "" {
            return
        }

        let jsString = #"""
            document.querySelectorAll('p, h1, h2, h3, h4, h5, h6, li, strong, span').forEach(function(el) {
                var style = window.getComputedStyle(el);
                var fontSize = parseFloat(style.fontSize);
                el.style.fontSize = (fontSize * \#(fontScale)) + 'px';  
            });
            const node = document.getElementsByTagName('body')[0];
            htmlToImage.toJpeg(node, {quality: 0.6,backgroundColor:'white',pixelRatio:\#(pixelRatio)}).then((base64Str) => {
            console.log("base64Str === ", base64Str);
            var nativeBase64Str = base64Str.replace(/^data:image\/\w+;base64,/,'');
            window.webkit.messageHandlers.dom2ImageSuccess.postMessage(nativeBase64Str);
            })
            .catch((error) => {
            window.webkit.messageHandlers.dom2ImageFail.postMessage(error);
            console.log("error === ", error);
            });
        """#
        
        // We can ignore error message: 'JavaScript execution returned a result of an unsupported type'
        // Reason: after executing the evaluateJavaScript(jsString), return result is nil.
        // And it won't affect the dom2ImageSuccess/dom2ImageFail callback
        webView.evaluateJavaScript(jsString) { result, error in
            if let error = error {
                print("webView evaluateJavaScript error \(error.localizedDescription)")
                // LogManager.sharedInstance().logWebViewError("evaluateJavaScript error", errorMessage: error.localizedDescription)
            }
        }
    }
}

extension Html2Image {
    private func dom2ImageSuccess(base64: String) {
        let data = Data(base64Encoded: base64)
        if let data = data {
            let image = UIImage(data: data)
            if let image = image {
                ImageDebugUtil.debugImageData(image)
                
                DispatchQueue(label: "com.pos.html").async {[unowned self] in
                    success?(image)
                }
            } else {
                LogManager.sharedInstance().logWebViewError("dom2ImageSuccess", errorMessage: "no image")
                fail?()
            }
        } else {
            LogManager.sharedInstance().logWebViewError("dom2ImageSuccess", errorMessage: "no data")
            fail?()
        }
    }

    private func dom2ImageFail(error: String) {
        LogManager.sharedInstance().logWebViewError("dom2ImageFail", errorMessage: "\(error)")
        fail?()
    }
}

extension Html2Image: WarmUpable {
    func warmUp() {
        DispatchQueue.main.async {
            self.htmlString = ""
            self.webView.loadHTMLString(self.htmlString, baseURL: nil)
        }
    }
}
