//
//  ImageDebugUtil.swift
//  RNPrinter
//
//  Created by <PERSON> on 2025/5/12.
//

import UIKit

final class ImageDebugUtil {
    static func debugImageData(_ image: UIImage) {
        guard let cgImage = image.cgImage else { return }
        
        let props: [String: Any] = [
            "尺寸": "\(image.size.width) x \(image.size.height)",
            "比例": image.scale,
            "方向": image.imageOrientation.rawValue,
            "位深度": cgImage.bitsPerComponent,
            "字节/行": cgImage.bytesPerRow,
            "颜色空间": cgImage.colorSpace?.name ?? "未知",
            "Alpha信息": cgImage.alphaInfo.rawValue,
            "像素格式": cgImage.bitmapInfo.rawValue
        ]
        
        let propsString = props.map { "* \($0.key): \($0.value)" }.joined(separator: "\n")
        let output = """
        \n====== [ImageDebugUtil] ======
        \(propsString)
        ====== [ImageDebugUtil] ======\n
        """
        print(output)
    }
}
