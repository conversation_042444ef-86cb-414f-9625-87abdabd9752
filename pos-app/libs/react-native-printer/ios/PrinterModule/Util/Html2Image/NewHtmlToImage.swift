//
//  NewHtmlToImage.swift
//  RNPrinter
//
//  Created by <PERSON> on 2024/10/12.
//

import WebKit
import UIKit

final class NewHtmlToImage {
    private var webView: WKWebView!
    private var pixelRatio: CGFloat = 1.0
    private var fontScale: Double = 1.0
    private var navigationDelegate: NavigationDelegate?
    private var logMessages: [String] = []
    
    func renderHTMLString(_ htmlString: String, width: CGFloat, fontScale: Double = 1.0, minContentHeight: Double? = 0.0, completion: @escaping (UIImage?) -> Void) {
        let taskId = UUID().uuidString.prefix(8)
        addLog("[NewHtmlToImage] Start rendering HTML [ID:\(taskId)] Width:\(width) FontScale:\(fontScale)")
        
        self.fontScale = fontScale
        
        var realWidth = 0
        switch width {
        case PrinterPaperWidth.Print80.rawValue:
            realWidth = 380
            pixelRatio = 1.53
        case PrinterPaperWidth.Print545.rawValue:
            realWidth = 380
            pixelRatio = 1.45
        case PrinterPaperWidth.Print500.rawValue:
            realWidth = 380
            pixelRatio = 1.31
        default:
            realWidth = Int(width)
        }
        addLog("[NewHtmlToImage] Actual render width: \(realWidth), pixel ratio: \(pixelRatio) [ID:\(taskId)]")

        DispatchQueue.main.async { [weak self] in
            guard let self = self else {
                completion(nil)
                return
            }
            
            self.webView = WKWebView(frame: CGRect(x: 0, y: 0, width: CGFloat(realWidth), height: 1))
            self.webView.loadHTMLString(htmlString, baseURL: nil)
            addLog("[NewHtmlToImage] WebView created and HTML loaded [ID:\(taskId)]")
            
            let navigationDelegate = NavigationDelegate()
            self.navigationDelegate = navigationDelegate
            self.webView.navigationDelegate = navigationDelegate
            
            let timeout = DispatchWorkItem {
                self.addLog("[NewHtmlToImage] Rendering timeout (10 seconds) [ID:\(taskId)]")
                self.recordFailure(taskId: String(taskId), reason: "Timeout")
                completion(nil)
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 10, execute: timeout)

            navigationDelegate.didFinish = { [weak self] in
                timeout.cancel()
                
                self?.addLog("[NewHtmlToImage] HTML loading completed [ID:\(taskId)]")
                
                guard let self = self else {
                    completion(nil)
                    return
                }
                
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    self.addLog("[NewHtmlToImage] Start adjusting font size [ID:\(taskId)]")
                    // Inject JavaScript to adjust font size
                    let jsString = """
                        (function adjustFontSize() {
                            document.querySelectorAll('*').forEach(function(el) {
                                var style = window.getComputedStyle(el);
                                var originalFontSize = parseFloat(style.fontSize);
                                if (!isNaN(originalFontSize)) {
                                    el.style.fontSize = (originalFontSize * \(fontScale)) + 'px';
                                }
                            });
                        })();
                    """
                    self.webView.evaluateJavaScript(jsString) { _, error in
                        if let error = error {
                            self.addLog("[NewHtmlToImage] Font adjustment failed: \(error.localizedDescription) [ID:\(taskId)]")
                            self.recordFailure(taskId: String(taskId), reason: "font adjust")
                            completion(nil)
                            return
                        }
                        self.addLog("[NewHtmlToImage] Font adjustment completed [ID:\(taskId)]")

                        // Get the content height after adjusting font size
                        self.addLog("[NewHtmlToImage] Getting content height [ID:\(taskId)]")
                        self.webView.evaluateJavaScript("document.body.scrollHeight") { (height, error) in
                            guard let realContentHeight = height as? CGFloat else {
                                self.addLog("[NewHtmlToImage] Failed to get content height: \(error?.localizedDescription ?? "Unknown error") [ID:\(taskId)]")
                                self.recordFailure(taskId: String(taskId), reason: "get content height")
                                completion(nil)
                                return
                            }

                            self.addLog("[NewHtmlToImage] Content height: \(realContentHeight) [ID:\(taskId)]")
                            let size = CGSize(width: CGFloat(realWidth), height: realContentHeight)
                            self.webView.frame = CGRect(origin: .zero, size: size)
                            self.webView.scrollView.contentSize = size

                            let snapshotConfig = WKSnapshotConfiguration()
                            snapshotConfig.rect = self.webView.bounds
                            snapshotConfig.snapshotWidth = NSNumber(value: Float(realWidth) * Float(self.pixelRatio))

                            self.addLog("[NewHtmlToImage] Taking snapshot [ID:\(taskId)]")
                            self.webView.takeSnapshot(with: snapshotConfig) { [weak self] image, error in
                                guard let originalImage = image else {
                                    self?.addLog("[NewHtmlToImage] Snapshot failed: \(error?.localizedDescription ?? "Unknown error") [ID:\(taskId)]")
                                    self?.recordFailure(taskId: String(taskId), reason: "Snapshot failed")
                                    completion(nil)
                                    return
                                }
                                self?.addLog("[NewHtmlToImage] Snapshot successful: \(originalImage.size.width)x\(originalImage.size.height) [ID:\(taskId)]")
                                
                                let finalImage = self?.prepareImageForPrinting(originalImage)
                                
                                if let finalImage = finalImage {
                                    ImageDebugUtil.debugImageData(finalImage)
                                    
                                    self?.addLog("[NewHtmlToImage] Rendering completed [ID:\(taskId)]")
                                    self?.recordSuccess(taskId: String(taskId), imageSize: finalImage.size)
                                    
                                    let minContentHeightInt = Int(minContentHeight ?? 0.0)
                                    let realContentHeightInt = Int(realContentHeight)
                                    printWithTime("[NewHtmlToImage] minContentHeight: \(String(describing: minContentHeight))")
                                    printWithTime("[NewHtmlToImage] realContentHeight: \(realContentHeight)")
                                    if (realContentHeightInt < minContentHeightInt) {
                                        DispatchQueue.global().async {
                                            let timestamp = Int(Date().timeIntervalSince1970)
                                            let fileName = "RENDER_NOT_COMPLETE-\(timestamp)-\(realContentHeightInt)-\(minContentHeightInt).png"
                                            _ = FileUtil.saveImage(image: finalImage, path: fileName)
                                            
                                            LogManager.sharedInstance().logCustomizedEvent("Hardware_Printer_Execution", privateDataDict: [
                                                "posV3Mobile": [
                                                    "event": "RENDER_NOT_COMPLETE",
                                                    "contentHeight": realContentHeightInt,
                                                    "minContentHeightInt": minContentHeightInt
                                                ]
                                            ])
                                            
                                            NotificationCenter.default.post(
                                                name: NSNotification.Name(rawValue: "uploadIncompletePrintingImageNotification"),
                                                object: nil,
                                                userInfo: ["fileName": fileName]
                                            )
                                        }
                                    }
                                    
                                    completion(finalImage)
                                } else {
                                    self?.addLog("[NewHtmlToImage] Failed due to add white background [ID:\(taskId)]")
                                    self?.recordFailure(taskId: String(taskId), reason: "Failed to add white background")
                                    completion(nil)
                                }
                            }
                        }
                    }
                }
            }

            navigationDelegate.didFail = { error in
                timeout.cancel()
                
                self.addLog("[NewHtmlToImage] HTML loading failed: \(error.localizedDescription) [ID:\(taskId)]")
                self.recordFailure(taskId: String(taskId), reason: "HTML loading failed")
                completion(nil)
            }
        }
    }
    
    private func prepareImageForPrinting(_ image: UIImage) -> UIImage? {
        guard let jpegData = image.jpegData(compressionQuality: 0.6) else {
            return image
        }
        
        guard let jpegImage = UIImage(data: jpegData) else {
            return image
        }
        
        let targetWidth: CGFloat = jpegImage.size.width / 2
        let targetHeight: CGFloat = jpegImage.size.height / 2
        let targetSize = CGSize(width: targetWidth, height: targetHeight)
        
        let format = UIGraphicsImageRendererFormat.default()
        format.scale = 1.0
        format.opaque = true
        
        let renderer = UIGraphicsImageRenderer(size: targetSize, format: format)
        return renderer.image { context in
            jpegImage.draw(in: CGRect(origin: .zero, size: targetSize))
        }
    }
}


private class NavigationDelegate: NSObject, WKNavigationDelegate {
    var didFinish: (() -> Void)?
    var didFail: ((Error) -> Void)?

    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        didFinish?()
    }

    func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
        didFail?(error)
    }

    func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: Error) {
        didFail?(error)
    }
}

//MARK: - Log
extension NewHtmlToImage {
    private func addLog(_ message: String) {
        logMessages.append(message)
        printWithTime(message)
    }
    
    private func recordSuccess(taskId: String, imageSize: CGSize) {
        // Temporarily disable it to prevent excessive log uploads.
//        LogManager.sharedInstance().logCustomizedEvent("HTML_Rendering_Success",
//                                                       mobileDict: ["taskId": taskId,
//                                                                    "imageWidth": imageSize.width,
//                                                                    "imageHeight": imageSize.height])
    }
    
    private func recordFailure(taskId: String, reason: String) {
        let backtraceLog = logMessages.joined(separator: "\n")
        
        LogManager.sharedInstance().logCustomizedEvent("HTML_Rendering_Failed",
                                                       privateDataDict: [
                                                        "taskId": taskId,
                                                        "reason": reason,
                                                        "backtrace": backtraceLog
                                                       ])
        
        logMessages.removeAll()
    }
}
