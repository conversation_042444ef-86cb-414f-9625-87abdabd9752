
#import "RNPrinter.h"
#import "RNPrinter-Swift.h"

#define UPDATE_PRINTER_LIST @"update_printer_list"
#define UPLOAD_INCOMPLETE_PRINTING_IMAGE @"upload_incomplete_printing_image"


@implementation RNPrinter
{
    bool hasListeners;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(uploadIncompletePrintingImage:) name:@"uploadIncompletePrintingImageNotification" object:nil];
    }
    return self;
}

- (dispatch_queue_t)methodQueue
{
    return dispatch_get_main_queue();
}

- (void)turnOnPower {
}

- (void)bindService {
}

- (void)initPrinters:(NSString *)receiptType printers:(nonnull NSArray *) printers resolve:(RCTPromiseResolveBlock)resolve reject:(RCTPromiseRejectBlock)reject {
    [PrinterManager.instance initPrinterWithReceiptType:receiptType arr:printers resultCallback:^(NSDictionary<NSString *,id> * _Nonnull result) {
        if (self->hasListeners) {
            [self sendEventWithName:UPDATE_PRINTER_LIST body:result];
        }
    } completion:^(NSDictionary<NSString *,id> * _Nonnull result) {
        resolve(result);
    }];
}

- (void)searchPrinters:(NSString *)receiptType resolve:(RCTPromiseResolveBlock)resolve reject:(RCTPromiseRejectBlock)reject {
    [PrinterManager.instance searchPrintersWithReceiptType:receiptType resultCallback:^(NSDictionary<NSString *,id> * _Nonnull result) {
        if (self->hasListeners) {
            [self sendEventWithName:UPDATE_PRINTER_LIST body:result];
        }
    } completion:^(NSDictionary<NSString *,id> * _Nonnull result) {
        resolve(result);
    }];
}

- (void)requestPrinting: (nonnull NSArray *)printerData resolve:(RCTPromiseResolveBlock)resolve reject:(RCTPromiseRejectBlock)reject {
    [PrinterManager.instance requestToPrintWithArr: printerData printTaskCompletion:^(NSDictionary<NSString *,id> * _Nonnull result) {
        resolve(result);
    }];
}

- (void)buzz: (nonnull NSArray *) buzzData resolve:(RCTPromiseResolveBlock)resolve reject:(RCTPromiseRejectBlock)reject {
    [PrinterManager.instance buzzWithArr: buzzData completion:^(NSDictionary<NSString *,id> * _Nonnull result) {
        resolve(result);
    }];
}

- (void)setStoreInfo:(nonnull NSDictionary *)storeInfo resolve:(RCTPromiseResolveBlock)resolve reject:(RCTPromiseRejectBlock)reject {
    [PrinterManager.instance setStoreInfoWithDic:storeInfo completion:^(NSDictionary<NSString *,id> * _Nonnull result) {
        resolve(result);
    }];
}

- (void)openCashDrawer:(nonnull NSArray *)openCashDrawerModel resolve:(RCTPromiseResolveBlock)resolve reject:(RCTPromiseRejectBlock)reject {
    [PrinterManager.instance openCashDrawerWithArr:openCashDrawerModel completion:^(NSDictionary<NSString *,id> * _Nonnull result) {
        resolve(result);
    }];
}

- (void)connectTcp:(NSDictionary *)printerDict resolve:(RCTPromiseResolveBlock)resolve reject:(RCTPromiseRejectBlock)reject {
    NSString *ip = [printerDict objectForKey:@"ip"];
    if (!ip) {
        ip = @"";
    }
    NSString *printerId = [printerDict objectForKey:@"printerId"];
    if (!printerId) {
        printerId = @"";
    }
    int port = [[printerDict objectForKey:@"port"] intValue];
    if (!port) {
        port = 9100;
    }
    int timeout = [[printerDict objectForKey:@"timeout"] intValue];
    if (!timeout) {
        timeout = 1;
    }
    
    [PrinterManager.instance findPrinterById:printerId completion:^(NSDictionary<NSString *,id> * _Nonnull result, BOOL didFind) {
        if (!didFind) {
            resolve(result);
        } else {
            [PrinterManager.instance connectTcpWithIp:ip port:port timeout:timeout completion:^(NSDictionary<NSString *,id> * _Nonnull result) {
                resolve(result);
            }];
        }
    }];
}

- (void)searchLANXPrinters:(RCTPromiseResolveBlock)resolve reject:(RCTPromiseRejectBlock)reject {
    [PrinterManager.instance udpSearchXPrintersWithCompletion:^(NSDictionary<NSString *,id> * _Nonnull result) {
        resolve(result);
    }];
}

- (void)restoreLanXPrintersByUDP:(NSString *)searchId resolve:(RCTPromiseResolveBlock)resolve reject:(RCTPromiseRejectBlock)reject {
    [PrinterManager.instance restoreLanXPrintersByUDPWithCompletion:^(NSArray<NSDictionary<NSString *,id> *> * _Nonnull array) {
        NSDictionary *resultDict = @{
            @"printers": array,
            @"udpRestoreId": searchId
        };
        NSLog(@"[restoreLanXPrintersByUDP] resultDict:%@", resultDict);
        NSDictionary *statusDict = @{
            @"errCode": @0,
            @"errMessage": @"Success"
        };
        resolve(statusDict);
        [self sendEventWithName:UPDATE_PRINTER_LIST body:resultDict];
    }];
}

/**
    RCTEventEmitter
 */
- (NSArray<NSString *> *)supportedEvents {
    return @[UPDATE_PRINTER_LIST, UPLOAD_INCOMPLETE_PRINTING_IMAGE];
}

- (void)startObserving {
    hasListeners = YES;
}

- (void)stopObserving {
    hasListeners = NO;
}

#pragma mark - Notification
- (void)uploadIncompletePrintingImage:(NSNotification *)notification {
    if (self->hasListeners) {
        [self sendEventWithName:UPLOAD_INCOMPLETE_PRINTING_IMAGE body:nil];
    }
}

#pragma mark - RN

RCT_EXPORT_MODULE()

RCT_EXTERN_METHOD(turnOnPower)
RCT_EXTERN_METHOD(bindService)
RCT_EXTERN_METHOD(buzz: (nonnull NSArray *)buzzData resolve:(RCTPromiseResolveBlock)resolve reject:(RCTPromiseRejectBlock)reject)
RCT_EXTERN_METHOD(searchPrinters:(NSString *)receiptType resolve:(RCTPromiseResolveBlock)resolve reject:(RCTPromiseRejectBlock)reject)
RCT_EXTERN_METHOD(initPrinters:(NSString *)receiptType printers:(nonnull NSArray *) printers resolve:(RCTPromiseResolveBlock)resolve reject:(RCTPromiseRejectBlock)reject)
RCT_EXTERN_METHOD(initA4Printer: (nonnull NSArray *)printers resolve:(RCTPromiseResolveBlock)resolve reject:(RCTPromiseRejectBlock)reject)
RCT_EXTERN_METHOD(requestPrinting: (nonnull NSArray *)printerData resolve:(RCTPromiseResolveBlock)resolve reject:(RCTPromiseRejectBlock)reject)
RCT_EXTERN_METHOD(setStoreInfo: (nonnull NSDictionary *)storeInfo resolve:(RCTPromiseResolveBlock)resolve reject:(RCTPromiseRejectBlock)reject)
RCT_EXTERN_METHOD(openCashDrawer: (nonnull NSArray *)openCashDrawerModel resolve:(RCTPromiseResolveBlock)resolve reject:(RCTPromiseRejectBlock)reject)
RCT_EXTERN_METHOD(connectTcp:(NSDictionary *)printerDict resolve:(RCTPromiseResolveBlock)resolve reject:(RCTPromiseRejectBlock)reject)
RCT_EXTERN_METHOD(searchLANXPrinters:(RCTPromiseResolveBlock)resolve
                  reject:(RCTPromiseRejectBlock)reject)
RCT_EXTERN_METHOD(restoreLanXPrintersByUDP:(NSString *)searchId resolve:(RCTPromiseResolveBlock)resolve reject:(RCTPromiseRejectBlock)reject)

@end
  
