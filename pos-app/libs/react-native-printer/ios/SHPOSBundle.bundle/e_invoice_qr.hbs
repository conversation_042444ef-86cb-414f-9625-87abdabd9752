<!DOCTYPE html>
<html>

<head>
  <meta charset='utf-8' />
  <title>Print Invoice</title>
  <meta name='viewport' content='width=device-width, initial-scale=1' />
  <style>
    * {
      -webkit-box-sizing: border-box;
      -moz-box-sizing: border-box;
      box-sizing: border-box;
      margin: 0 auto;
      font-size: 18px;
      font-family: Roboto, Ubuntu,
      Helvetica, Arial, sans-serif;
    }

    html,
    body {
      font-size: 18px;
      margin: 0px;
      padding: 0px;
    }

    .text-center {
      text-align: center;
    }

    .text-top {
      vertical-align: top;
    }

    .text-right {
      text-align: right;
    }

    .main {
      clear: left;
      margin: 0 0;
      padding: 0px 32px;
    }

    .title {
      margin: 0 0;
      font-size: 32px;
    }

    .qrcode__wrapper {
      padding: 10px;
      border-bottom: 1px dashed #000;
    }

    .qrcode {
      padding: 5px;
      font-size: 0;
    }

    .qrcode-image__wrapper {
      position: relative;
    }

    .qrcode-image__wrapper::before {
      content: "";
      display: block;
      width: 100%;
      padding: 50% 0;
    }

    .qrcode-image {
      position: absolute;
      left: 0;
      top: 0;
      width:
        100%;
      height: 100%;
    }

    .table_name {
      margin: 0;
      padding: 8px;
      font-size: 24px;
    }

    .expired_time {
      margin: 0;
      padding: 8px;
      font-size: 14px;
    }


    .bottom {
      margin: 25px 0;
      padding: 15px 2%;
    }

    .powered-by {
      margin: 25px 0;
      font-size: 14px;
    }

    .detail_container {
      clear: left;
      margin: 0 0;
      padding: 16px 16px;
    }

    .store_info_container {
      padding-top: 10px;
      padding-bottom: 10px;
      border-bottom: 1px dashed #000;
      clear: left;
      margin: 0 0;
    }

    .store_name {
      margin: 0;
      padding: 8px;
      font-size: 26px;
      font-weight: bold;
    }

    .store_cred {
      margin: 0;
      font-size: 16px;
    }

    .recepit_detail {
      margin: 0;
      padding: 2px;
      font-size: 16px;
    }

    .recepit_detail_header {
      margin: 0;
      padding: 2px;
      font-size: 16px;
      font-weight: bold;
    }

    .separator {
      margin: 0;
      font-size: 18px;
      white-space: nowrap;
    }

    .qrcode__wrapper {
      padding: 10px;
      border-bottom: 1px dashed #000;
    }

    .qrcode {
      padding: 5px;
      border: 1px solid #000;
      font-size: 0;
      border-radius: 6px;
    }

    .qrcode-qr_left_text {
      padding: 8px;
      font-size: 24px;
      font-weight: bold;
    }

    .qrcode-qr_left_bottom_text {
      padding: 8px;
      font-size: 14px;
      font-weight: bold;
    }

    .qrcode-image__wrapper {
      position: relative;
    }

    .qrcode-image__wrapper::before {
      content: "";
      display: block;
      width: 100%;
      padding: 50% 0;
    }

    .qrcode-image {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
    }

    .qrcode-info {
      margin: 0;
      padding: 8px;
      white-space: pre-line;
      overflow-wrap: break-word;
    }

    .qrcode-info__80 {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      margin-top: -7px;
      padding: 0 20px;
      background-color: white;
      white-space: nowrap;
      max-width: 90%;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  </style>
</head>

<body leftmargin='0' marginwidth='0' topmargin='0' marginheight='0' offset='0'>
<div class='main'>
  <center>
    <div class='store_info_container'>
      <p class='store_name'>{{storeName}}</p>
      <p class='store_cred'>{{storeAddress}}</p>
      <p class='store_cred'>{{storeBrn}}</p>
    </div>
  </center>

  <div class='detail_container'>
    <p class='recepit_detail_header'>Store Receipt Detail</p>
    <p class='recepit_detail'>{{date}}</p>
    <p class='recepit_detail'>{{no}}</p>
    <p class='recepit_detail '>{{total}}</p>
  </div>

  <div>
    <div class="qrcode">
      <table class="qrcode-table" width="100%">
        <tr class="qrcode-table__item">
          <td class="qrcode-table__item text-center " width="50%">
            <p class="qrcode-qr_left_text">{{qrDescription}}</p>
            <p class="qrcode-qr_left_bottom_text">Only available within calendar month of purchase</p>
          </td>
          <td class="qrcode-table__item" width="{{#if isPrinterPaperWidth58}}60%{{else}}50%{{/if}}">
            <div class="qrcode-image__wrapper">
              <img class='qrcode-image' src='_qrCodeImage_' alt='' />
            </div>
          </td>
        </tr>
      </table>
    </div>

  </div>

</div>

</body>

</html>
