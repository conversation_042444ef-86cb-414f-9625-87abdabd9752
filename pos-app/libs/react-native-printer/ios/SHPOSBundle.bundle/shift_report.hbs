<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>Print Invoice</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        * {
            -webkit-box-sizing: border-box;
            -moz-box-sizing: border-box;
            box-sizing: border-box;
            margin: 0 auto;
            font-size: 18px;
            font-family: Roboto, Ubuntu, Helvetica, Arial, sans-serif;
            ;
        }

        html,
        body {
            font-size: 18px;
        }

        .text-center {
            text-align: center;
        }

        .text-center .line {
            display: inline-block;
            height: 1px;
            background: #000;
        }

        .text-right {
            text-align: right;
        }

        .col-6 {
            display: inline-block;
            width: 50%;
        }

        .pre-line {
            white-space: pre-line;
        }

        .main {
            clear: left;
            padding: 20px 8px;
        }

        .logo {
            width: 30%;
            max-width: 120px;
        }

        .store__name {
            margin: 20px 0;
        }

        .title {
            margin: 10px 0;
        }

        .store__name strong,
        .title strong {
            font-size: 20px;
        }

        .store-info__list,
        .base-info__list {
            margin: 0 0 0px;
            list-style: none;
            padding-left: 0;
        }

        .store-info__item,
        .customer-info,
        .base-info__item,
        .scpwd-Info__text {
            line-height: 1.5em;
        }

        .base-info__item strong {
            font-size: 18px;
        }

        .customer-info,
        .base-info__wrapper,
        .table-info,
        .purchased-title {
            padding: 15px 0;
        }

        .purchased-table,
        .purchased-table__row,
        .purchased-table__header-item,
        .purchased-table__item,
        .billing-table,
        .billing-table__row,
        .billing-table__item,
        .billing-table__header-item {
            border: 0;
        }

        .purchased-table_header {
            padding-top: 15px;
        }

        .purchased-table__header-item {
            border-bottom: 1px dashed #000;
        }

        .purchased-table__header-text,
        .billing-table__header-text {
            margin: 10px 2%;
        }

        .purchased-table__item-text,
        .billing-table__item-text {
            padding: 4px 0;
            word-break: break-word;
            -webkit-hyphens: auto;
            -ms-hyphens: auto;
            hyphens: auto;
        }

        .purchased-table__header-text-first,
        .purchased-table__item-text-first,
        .billing-table__header-text-first,
        .billing-table__item-text-first {
            padding-left: 0;
        }

        .purchased-table__header-text-last,
        .purchased-table__item-text-last,
        .billing-table__header-text-last,
        .billing-table__item-text-last {
            padding-right: 0;
        }

        .table-bottom-divider {
            padding-top: 3px;
            border-bottom: 3px dashed #000;
        }

        .bir-scpwd-info__list {
            padding-bottom: 10px;
            padding-left: 0;
            list-style: none;
        }

        .bir-scpwd-info__item {
            padding-top: 10px;
            border-bottom: 1px solid #000;
        }

        .bir-scpwd-info__item-label {
            padding: 10px 0;
            margin-bottom: -1px;
            background-color: #fff;
        }

        .qrcode__wrapper {
            padding: 10px;
            border-bottom: 1px dashed #000;
        }

        .qrcode {
            padding: 5px;
            border: 1px solid #000;
            font-size: 0;
            border-radius: 6px;
        }

        .qrcode-cashback__percentage {
            font-size: 30px;
        }

        .qrcode-cashback__label {
            font-size: 16px;
        }

        .qrcode-image__wrapper {
            position: relative;
        }

        .qrcode-image__wrapper::before {
            content: "";
            display: block;
            width: 100%;
            padding: 50% 0;
        }

        .qrcode-image {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
        }

        .qrcode-info {
            margin: 0;
            padding: 8px;
        }

        .bottom,
        .pickup-note,
        .extra-info,
        .bir-accr-info,
        .vat-register-footer-info,
        .bar-code-info,
        .powered-by {
            margin: 0;
            padding: 15px 2%;
        }

        .bir-accr-info__list {
            list-style: none;
            padding-left: 0;
        }

        .bir-accr-info__item {
            line-height: 1.5em;
        }

        .bar-code__wrapper {
            width: 100%;
        }

        .bar-code-info {
            width: 100%;
        }

        .monospace-font {
            font-family: monospace;
        }

        .text-line-dashed {
            position: relative;
            height: 1.875rem;
            line-height: 1.875rem;
            margin: 0 2.5px auto;
            text-align: center;
        }

        .text-line-dashed i {
            display: block;
            height: 1px;
            position: absolute;
            top: 0.9rem;
            width: 100%;
            border-top: 1px dashed #000;
        }

        .text-line-dashed p {
            display: inline-block;
            color: #000;
            background: #fff;
            padding: 0 0.2rem;
            text-align: center;
            margin: 0 auto;
            position: relative;
            z-index: 2;
        }

        .bottom-blank {
            height: 100px;
        }
    </style>
</head>

<body leftmargin="0" marginwidth="0" topmargin="0" marginheight="0" offset="0">
    <div class="main">
        {{#if printedDate}}
        <div class="base-info__wrapper">
            <p class="base-info__item">{{printedDate}}</p>
        </div>
        {{/if}}
        <!-- Store Info -->
        <center>
            <div>
                <p class="store__name"><strong>{{storeName}}</strong></p>
                <ul class="store-info__list">
                    {{#if address}}
                    <li class="store-info__item pre-line">{{address}}</li>
                    {{/if}}
                    {{#if phone}}
                    <li class="store-info__item pre-line">{{phone}}</li>
                    {{/if}}
                    {{#if gstIdNo}}
                    <li class="store-info__item pre-line">{{gstIdNo}}</li>
                    {{/if}}
                    {{#if birAccrNo}}
                    <li class="store-info__item pre-line">{{birAccrNo}}</li>
                    {{/if}}
                    {{#if serialNo}}
                    <li class="store-info__item pre-line">{{serialNo}}</li>
                    {{/if}}
                    {{#if minNo}}
                    <li class="store-info__item pre-line">{{minNo}}</li>
                    {{/if}}
                    {{#if ptu}}
                    <li class="store-info__item pre-line">{{ptu}}</li>
                    {{/if}}
                </ul>
                <h2 class="purchased-title text-center">{{reportTitle}}</h2>
            </div>
        </center>
        <!-- End of Store Info -->
        <div class="base-info__wrapper">
            <ul class="base-info__list">
                <li class="base-info__item">{{shiftOpenTime}}</li>
                <li class="base-info__item">{{shiftCloseTime}}</li>
                <li class="base-info__item">{{registerId}}</li>
                <li class="base-info__item">{{reportDate}}</li>
                <li class="base-info__item">{{manager}}</li>
                {{#if openBy}}
                <li class="base-info__item">{{openBy}}</li>
                {{/if}}
                {{#if closeBy}}
                <li class="base-info__item">{{closeBy}}</li>
                {{/if}}
                {{#if printedBy}}
                <li class="base-info__item">{{printedBy}}</li>
                {{/if}}
                {{#if cashier}}
                <li class="base-info__item">{{cashier}}</li>
                {{/if}}
            </ul>
        </div>
        <!-- Main -->
        <div>
            <!-- Sales Info -->
            {{#if payments}}
            {{#each payments}}
            <table class="purchased-table_header" width="100%">
                <tr class="purchased-table__row">
                    {{#if headerStrings}}
                    {{#each headerStrings}}
                    <td class="purchased-table__item" width="{{#if @first}}60%{{else}}{{#if @last}}26%{{else}}14%{{/if}}{{/if}}">
                        <p class="purchased-table__item-text purchased-table__item-text-first"><strong>{{this}}</strong>
                        </p>
                    </td>
                    {{/each}}
                    {{/if}}
                </tr>
            </table>
            <table class="purchased-table" width="100%">
                <tr class="purchased-table__row">
                    <td class="purchased-table__item table-bottom-divider" width="40%">
                    </td>
                    <td class="purchased-table__item table-bottom-divider" width="20%">
                    </td>
                    <td class="purchased-table__item table-bottom-divider" width="14%">
                    </td>
                    <td class="purchased-table__item table-bottom-divider" width="26%">
                    </td>
                </tr>
                <tr class="purchased-table__row">
                    {{#if ../paymentSummaryTitleStrings}}
                    <td class="purchased-table__item" width="40%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{../paymentSummaryTitleStrings.salesTitle}}</p>
                    </td>
                    {{/if}}
                    <td class="purchased-table__item" width="20%">
                        <p class="purchased-table__item-text purchased-table__item-text-first monospace-font">(+)</p>
                    </td>
                    <td class="purchased-table__item" width="14%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">{{salesQuantity}}</p>
                    </td>
                    <td class="purchased-table__item" width="26%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">{{salesAmount}}</p>
                    </td>
                </tr>
                <tr class="purchased-table__row">
                    {{#if ../paymentSummaryTitleStrings}}
                    <td class="purchased-table__item" width="40%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{../paymentSummaryTitleStrings.refundsTitle}}</p>
                    </td>
                    {{/if}}
                    <td class="purchased-table__item" width="20%">
                        <p class="purchased-table__item-text purchased-table__item-text-first monospace-font">(-)</p>
                    </td>
                    <td class="purchased-table__item" width="14%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">{{refundsQuantity}}</p>
                    </td>
                    <td class="purchased-table__item" width="26%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">{{refundsAmount}}</p>
                    </td>
                </tr>
                 {{#if roundingTitle}}
                <tr class="purchased-table__row">
                    <td class="purchased-table__item" width="40%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{roundingTitle}}</p>
                    </td>
                     <td class="purchased-table__item" width="20%">
                    </td>
                     <td class="purchased-table__item" width="14%">
                    </td>
                    <td class="purchased-table__item" width="26%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">{{roundingAmount}}</p>
                    </td>
                </tr>
                {{/if}}
                <tr class="purchased-table__row">
                    <td class="purchased-table__item table-bottom-divider" width="40%">
                    </td>
                    <td class="purchased-table__item table-bottom-divider" width="20%">
                    </td>
                    <td class="purchased-table__item table-bottom-divider" width="14%">
                    </td>
                    <td class="purchased-table__item table-bottom-divider" width="26%">
                    </td>
                </tr>
                <tr class="purchased-table__row">
                    {{#if ../paymentSummaryTitleStrings}}
                    <td class="purchased-table__item" width="40%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{../paymentSummaryTitleStrings.netTitle}}</p>
                    </td>
                    {{/if}}
                    <td class="purchased-table__item" width="20%">
                        <p class="purchased-table__item-text purchased-table__item-text-first monospace-font">(=)</p>
                    </td>
                    <td class="purchased-table__item" width="14%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">{{netQuantity}}</p>
                    </td>
                    <td class="purchased-table__item" width="26%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">{{netAmount}}</p>
                    </td>
                </tr>
            </table>
            {{/each}}
            {{/if}}

            <table class="purchased-table_header" width="100%">
                <tr class="purchased-table__row">
                    {{#if shiftReportItemsHeaderStrings}}
                    {{#each shiftReportItemsHeaderStrings}}
                    <td class="purchased-table__item" width="{{#if @first}}60%{{else}}{{#if @last}}26%{{else}}14%{{/if}}{{/if}}">
                        <p class="purchased-table__item-text purchased-table__item-text-first"><strong>{{this}}</strong>
                        </p>
                    </td>
                    {{/each}}
                    {{/if}}
                </tr>
            </table>
            <table class="purchased-table" width="100%">
                <tr class="purchased-table__row">
                    <td class="purchased-table__item table-bottom-divider" width="40%">
                    </td>
                    <td class="purchased-table__item table-bottom-divider" width="20%">
                    </td>
                    <td class="purchased-table__item table-bottom-divider" width="14%">
                    </td>
                    <td class="purchased-table__item table-bottom-divider" width="26%">
                    </td>
                </tr>
                <tr class="purchased-table__row">
                    <td class="purchased-table__item" width="40%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{salesSummaryTitles.totalSalesTitle}}</p>
                    </td>
                    <td class="purchased-table__item" width="20%">
                        <p class="purchased-table__item-text purchased-table__item-text-first monospace-font">(+)</p>
                    </td>
                    <td class="purchased-table__item" width="14%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{salesSummary.salesQuantity}}</p>
                    </td>
                    <td class="purchased-table__item" width="26%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{salesSummary.salesAmount}}</p>
                    </td>
                </tr>
                <tr class="purchased-table__row">
                    <td class="purchased-table__item" width="40%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{salesSummaryTitles.totalRefundsTitle}}</p>
                    </td>
                    <td class="purchased-table__item" width="20%">
                        <p class="purchased-table__item-text purchased-table__item-text-first monospace-font">(-)</p>
                    </td>
                    <td class="purchased-table__item" width="14%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{salesSummary.refundsQuantity}}</p>
                    </td>
                    <td class="purchased-table__item" width="26%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{salesSummary.refundsAmount}}</p>
                    </td>
                </tr>
                <tr class="purchased-table__row">
                    <td class="purchased-table__item table-bottom-divider" width="40%">
                    </td>
                    <td class="purchased-table__item table-bottom-divider" width="20%">
                    </td>
                    <td class="purchased-table__item table-bottom-divider" width="14%">
                    </td>
                    <td class="purchased-table__item table-bottom-divider" width="26%">
                    </td>
                </tr>
                <tr class="purchased-table__row">
                    <td class="purchased-table__item" width="40%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{salesSummaryTitles.totalNetTitle}}</p>
                    </td>
                    <td class="purchased-table__item" width="20%">
                        <p class="purchased-table__item-text purchased-table__item-text-first monospace-font">(=)</p>
                    </td>
                    <td class="purchased-table__item" width="14%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{salesSummary.netQuantity}}</p>
                    </td>
                    <td class="purchased-table__item" width="26%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{salesSummary.netAmount}}</p>
                    </td>
                </tr>
            </table>

            {{#if totalDepositTitle}}
            <table class="purchased-table_header" width="100%">
                <tr class="purchased-table__row">
                    {{#if shiftReportDepositssHeaderStrings}}
                    {{#each shiftReportDepositssHeaderStrings}}
                        <td class="purchased-table__item" width="{{#if @first}}60%{{else}}{{#if @last}}26%{{else}}14%{{/if}}{{/if}}">
                            <p class="purchased-table__item-text purchased-table__item-text-first"><strong>{{this}}</strong>
                            </p>
                        </td>
                    {{/each}}
                    {{/if}}
                </tr>
            </table>
                {{/if}}
                {{#if totalDepositTitle}}
                <table class="purchased-table" width="100%">
                <tr class="purchased-table__row">
                    <td class="purchased-table__item table-bottom-divider" width="60%">
                    </td>
                    <td class="purchased-table__item table-bottom-divider" width="14%">
                    </td>
                    <td class="purchased-table__item table-bottom-divider" width="26%">
                    </td>
                </tr>
                {{#if depositsSummary}}
                {{#each depositsSummary}}
                <tr class="purchased-table__row">
                    <td class="purchased-table__item" width="60%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">{{title}}</p>
                    </td>
                    <td class="purchased-table__item" width="14%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">{{quantity}}</p>
                    </td>
                    <td class="purchased-table__item" width="26%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">{{amount}}</p>
                    </td>
                </tr>
                {{/each}}
                {{/if}}
                <tr class="purchased-table__row">
                    <td class="purchased-table__item table-bottom-divider" width="60%">
                    </td>
                    <td class="purchased-table__item table-bottom-divider" width="14%">
                    </td>
                    <td class="purchased-table__item table-bottom-divider" width="26%">
                    </td>
                </tr>
                <tr class="purchased-table__row">
                    <td class="purchased-table__item" width="60%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">{{totalDepositTitle}}</p>
                    </td>
                    <td class="purchased-table__item" width="14%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">{{totalDepositQuantity}}
                        </p>
                    </td>
                    <td class="purchased-table__item" width="26%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">{{totalDepositAmount}}
                        </p>
                    </td>
                </tr>
            </table>
            {{/if}}

            <table class="purchased-table_header" width="100%">
                <tr class="purchased-table__row">
                    {{#if storeCreditSummaryHeaderStrings}}
                    {{#each storeCreditSummaryHeaderStrings}}
                        <td class="purchased-table__item" width="{{#if @first}}60%{{else}}{{#if @last}}26%{{else}}14%{{/if}}{{/if}}">
                            <p class="purchased-table__item-text purchased-table__item-text-first"><strong>{{this}}</strong>
                            </p>
                        </td>
                    {{/each}}
                    {{/if}}
                </tr>
            </table>
            <table class="purchased-table" width="100%">
                <tr class="purchased-table__row">
                    <td class="purchased-table__item table-bottom-divider" width="40%">
                    </td>
                    <td class="purchased-table__item table-bottom-divider" width="20%">
                    </td>
                    <td class="purchased-table__item table-bottom-divider" width="14%">
                    </td>
                    <td class="purchased-table__item table-bottom-divider" width="26%">
                    </td>
                </tr>
                <tr class="purchased-table__row">
                    <td class="purchased-table__item" width="40%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{storeCreditSummaryTitles.discountTitle}}</p>
                    </td>
                    <td class="purchased-table__item" width="20%">
                        <p class="purchased-table__item-text purchased-table__item-text-first monospace-font">(+)</p>
                    </td>
                    <td class="purchased-table__item" width="14%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{storeCreditSummary.discountQuantity}}</p>
                    </td>
                    <td class="purchased-table__item" width="26%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{storeCreditSummary.discountAmount}}</p>
                    </td>
                </tr>
            </table>
            <table class="purchased-table_header" width="100%">
                <tr class="purchased-table__row">
                    {{#if cancelAndDiscountSummaryHeaderStrings}}
                    {{#each cancelAndDiscountSummaryHeaderStrings}}
                        <td class="purchased-table__item" width="{{#if @first}}60%{{else}}{{#if @last}}26%{{else}}14%{{/if}}{{/if}}">
                            <p class="purchased-table__item-text purchased-table__item-text-first"><strong>{{this}}</strong>
                            </p>
                        </td>
                    {{/each}}
                    {{/if}}
                </tr>
            </table>
            <table class="purchased-table" width="100%">
                <tr class="purchased-table__row">
                    <td class="purchased-table__item table-bottom-divider" width="60%">
                    </td>
                    <td class="purchased-table__item table-bottom-divider" width="14%">
                    </td>
                    <td class="purchased-table__item table-bottom-divider" width="26%">
                    </td>
                </tr>
                <tr class="purchased-table__row">
                    <td class="purchased-table__item" width="60%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{cancelAndDiscountSummaryTitles.discountTitle}}</p>
                    </td>
                    <td class="purchased-table__item" width="14%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{cancelAndDiscountSummary.discountQuantity}}</p>
                    </td>
                    <td class="purchased-table__item" width="26%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{cancelAndDiscountSummary.discountAmount}}</p>
                    </td>
                </tr>
                <tr class="purchased-table__row">
                    <td class="purchased-table__item" width="60%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{cancelAndDiscountSummaryTitles.cancelTxnsTitle}}</p>
                    </td>
                    <td class="purchased-table__item" width="14%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{cancelAndDiscountSummary.cancelQuantity}}</p>
                    </td>
                    <td class="purchased-table__item" width="26%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{cancelAndDiscountSummary.cancelAmount}}</p>
                    </td>
                </tr>
            </table>
            <table class="purchased-table_header" width="100%">
                <tr class="purchased-table__row">
                    {{#if serviceChargeSummaryHeaderStrings}}
                    {{#each serviceChargeSummaryHeaderStrings}}
                        <td class="purchased-table__item" width="{{#if @first}}60%{{else}}{{#if @last}}26%{{else}}14%{{/if}}{{/if}}">
                            <p class="purchased-table__item-text purchased-table__item-text-first"><strong>{{this}}</strong>
                            </p>
                        </td>
                    {{/each}}
                    {{/if}}
                </tr>
            </table>
            <table class="purchased-table" width="100%">
                <tr class="purchased-table__row">
                    <td class="purchased-table__item table-bottom-divider" width="60%">
                    </td>
                    <td class="purchased-table__item table-bottom-divider" width="14%">
                    </td>
                    <td class="purchased-table__item table-bottom-divider" width="26%">
                    </td>
                </tr>
                <tr class="purchased-table__row">
                    <td class="purchased-table__item" width="60%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{serviceChargeSummaryTitles.salesTitle}}</p>
                    </td>
                    <td class="purchased-table__item" width="14%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{serviceChargeSummary.salesQuantity}}</p>
                    </td>
                    <td class="purchased-table__item" width="26%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{serviceChargeSummary.salesAmount}}</p>
                    </td>
                </tr>
                <tr class="purchased-table__row">
                    <td class="purchased-table__item" width="60%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{serviceChargeSummaryTitles.refundsTitle}}</p>
                    </td>
                    <td class="purchased-table__item" width="14%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{serviceChargeSummary.refundsQuantity}}</p>
                    </td>
                    <td class="purchased-table__item" width="26%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{serviceChargeSummary.refundsAmount}}</p>
                    </td>
                </tr>
            </table>
            <table class="purchased-table" width="100%">
                <tr class="purchased-table__row">
                    {{#if taxSummaryHeaderStrings}}
                    {{#each taxSummaryHeaderStrings}}
                    <td class="purchased-table__item" colspan="1">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            <strong>{{this}}</strong>
                        </p>
                    </td>
                    {{/each}}
                    {{/if}}
                </tr>
                <tr class="purchased-table__row">
                    <td class="purchased-table__item table-bottom-divider" width="40%">
                    </td>
                    <td class="purchased-table__item table-bottom-divider" width="35%">
                    </td>
                    <td class="purchased-table__item table-bottom-divider" width="26%">
                    </td>
                </tr>
                {{#if taxSummaryArray}}
                {{#each taxSummaryArray}}
                <tr class="purchased-table__row">
                    <td class="purchased-table__item" width="40%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">{{taxName}}</p>
                    </td>
                    <td class="purchased-table__item" width="35%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">{{taxRate}}</p>
                    </td>
                    <td class="purchased-table__item" width="26%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">{{amount}}</p>
                    </td>
                </tr>
                {{/each}}
                {{/if}}

            </table>
            <table class="purchased-table_header" width="100%">
                <tr class="purchased-table__row">
                    {{#if cashDrawerSummaryHeaderStrings}}
                     {{#each cashDrawerSummaryHeaderStrings}}
                        <td class="purchased-table__item" width="{{#if @first}}60%{{else}}{{#if @last}}26%{{else}}14%{{/if}}{{/if}}">
                            <p class="purchased-table__item-text purchased-table__item-text-first"><strong>{{this}}</strong>
                            </p>
                        </td>
                    {{/each}}
                    {{/if}}
                </tr>
            </table>
            <table class="purchased-table" width="100%">
                <tr class="purchased-table__row">
                    <td class="purchased-table__item table-bottom-divider" width="40%">
                    </td>
                    <td class="purchased-table__item table-bottom-divider" width="20%">
                    </td>
                    <td class="purchased-table__item table-bottom-divider" width="14%">
                    </td>
                    <td class="purchased-table__item table-bottom-divider" width="26%">
                    </td>
                </tr>
                <tr class="purchased-table__row">
                    <td class="purchased-table__item" width="40%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{cashDrawerSummaryTitles.openingAmountTitle}}</p>
                    </td>
                    <td class="purchased-table__item" width="20%">

                    </td>
                    <td class="purchased-table__item" width="14%">

                    </td>
                    <td class="purchased-table__item" width="26%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{cashDrawerSummary.openingAmount}}</p>
                    </td>
                </tr>
                <tr class="purchased-table__row">
                    <td class="purchased-table__item" width="40%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{cashDrawerSummaryTitles.cashSalesTitle}}</p>
                    </td>
                    <td class="purchased-table__item" width="20%">
                        <p class="purchased-table__item-text purchased-table__item-text-first monospace-font">(+)</p>
                    </td>
                    <td class="purchased-table__item" width="14%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{cashDrawerSummary.cashSalesQuantity}}</p>
                    </td>
                    <td class="purchased-table__item" width="26%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{cashDrawerSummary.cashSalesAmount}}</p>
                    </td>
                </tr>
                <tr class="purchased-table__row">
                    <td class="purchased-table__item" width="40%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{cashDrawerSummaryTitles.cashDepositsTitle}}</p>
                    </td>
                    <td class="purchased-table__item" width="20%">
                        <p class="purchased-table__item-text purchased-table__item-text-first monospace-font">(+)</p>
                    </td>
                    <td class="purchased-table__item" width="14%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{cashDrawerSummary.cashDepositsQuantity}}</p>
                    </td>
                    <td class="purchased-table__item" width="26%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{cashDrawerSummary.cashDepositsAmount}}</p>
                    </td>
                </tr>
                <tr class="purchased-table__row">
                    <td class="purchased-table__item" width="40%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{cashDrawerSummaryTitles.cashRefundsTitle}}</p>
                    </td>
                    <td class="purchased-table__item" width="20%">
                        <p class="purchased-table__item-text purchased-table__item-text-first monospace-font">(-)</p>
                    </td>
                    <td class="purchased-table__item" width="14%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{cashDrawerSummary.cashRefundsQuantity}}</p>
                    </td>
                    <td class="purchased-table__item" width="26%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{cashDrawerSummary.cashRefundsAmount}}</p>
                    </td>
                </tr>
                <tr class="purchased-table__row">
                    <td class="purchased-table__item" width="40%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{cashDrawerSummaryTitles.payOutTitle}}</p>
                    </td>
                    <td class="purchased-table__item" width="20%">
                        <p class="purchased-table__item-text purchased-table__item-text-first monospace-font">(-)</p>
                    </td>
                    <td class="purchased-table__item" width="14%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{cashDrawerSummary.payOutQuantity}}</p>
                    </td>
                    <td class="purchased-table__item" width="26%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{cashDrawerSummary.payOutAmount}}</p>
                    </td>
                </tr>
                <tr class="purchased-table__row">
                    <td class="purchased-table__item" width="40%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{cashDrawerSummaryTitles.payInTitle}}</p>
                    </td>
                    <td class="purchased-table__item" width="20%">
                        <p class="purchased-table__item-text purchased-table__item-text-first monospace-font">(+)</p>
                    </td>
                    <td class="purchased-table__item" width="14%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{cashDrawerSummary.payInQuantity}}</p>
                    </td>
                    <td class="purchased-table__item" width="26%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            {{cashDrawerSummary.payInAmount}}</p>
                    </td>
                </tr>
                <tr class="purchased-table__row">
                    <td class="purchased-table__item table-bottom-divider" width="40%">
                    </td>
                    <td class="purchased-table__item table-bottom-divider" width="20%">
                    </td>
                    <td class="purchased-table__item table-bottom-divider" width="14%">
                    </td>
                    <td class="purchased-table__item table-bottom-divider" width="26%">
                    </td>
                </tr>
            </table>
            <table class="purchased-table" width="100%">
                <tr class="purchased-table__row">
                    <td class="purchased-table__item" width="70%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            <strong>{{expectedDrawerTitle}}</strong>
                        </p>
                    </td>
                    <td class="purchased-table__item" width="30%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            <strong>{{expectedDrawer}}</strong>
                        </p>
                    </td>
                </tr>
                <tr class="purchased-table__row">
                    <td class="purchased-table__item" width="70%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            <strong>{{actualDrawerTitle}}</strong>
                        </p>
                    </td>
                    <td class="purchased-table__item" width="30%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            <strong>{{actualDrawer}}</strong>
                        </p>
                    </td>
                </tr>
                <tr class="purchased-table__row">
                    <td class="purchased-table__item" width="70%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            <strong>{{overShortTitle}}</strong>
                        </p>
                    </td>
                    <td class="purchased-table__item" width="30%">
                        <p class="purchased-table__item-text purchased-table__item-text-first">
                            <strong>{{overShort}}</strong>
                        </p>
                    </td>
                </tr>
            </table>
            <!-- End of Sales Info -->
        </div>
        <!-- End of Main -->
        {{#if isPrinterPaperWidth58}}
        <div class="bottom-blank"></div>
        {{/if}}
    </div>

</body>

</html>