<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>Print Invoice</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        * {
            -webkit-box-sizing: border-box;
            -moz-box-sizing: border-box;
            box-sizing: border-box;
            margin: 0 auto;
            font-size: 14.7px;
            font-family: Roboto, Ubuntu, Helvetica, Arial, sans-serif;
            ;
        }

        html,
        body {
            font-size: 14.7px;
        }

        .text-center {
            text-align: center;
        }

        .text-right {
            text-align: right;
        }

        .text-left {
            text-align: left;
        }

        .col-6 {
            display: inline-block;
            width: 50%;
        }

        .pre-line {
            white-space: pre-line;
        }

        .main {
            clear: left;
            padding: 2px 8px 0px 8px;
        }

        .logo {
            width: 30%;
            max-width: 120px;
        }

        .store__name {
            margin: 20px 0;
        }

        .title {
            margin: 10px 0;
        }

        .store__name strong,
        .title strong {
            font-size: 23px;
        }

        .store-info__list,
        .base-info__list {
            margin: 0 0 0px;
            list-style: none;
            padding-left: 0;
        }

        .store-info__item,
        .customer-info,
        .base-info__item,
        .base-info__normal,
        .scpwd-Info__text {
            line-height: 1.5em;
        }

        .base-info__item strong {
            font-size: 17px;
        }

        .base-order__item {
            line-height: 1.5em;
            font-size: 33px;
        }

        .bir-scpwd-info__item2 {
            padding-top: 10px;
        }

        .base-order__item strong {
            font-size: 21px;
        }

        .customer-info,
        .base-info__wrapper,
        .table-info,
        .purchased-title {
            padding: 15px 0;
        }

        .purchased-table,
        .purchased-table__row,
        .purchased-table__header-item,
        .purchased-table__item,
        .billing-table,
        .billing-table__row,
        .billing-table__item,
        .billing-table__header-item {
            border: 0;
        }

        .billing-table__row_bir {
            border: 0;
            margin-top: 20px;
        }

        .bir-scpwd-info__item-label-text {
            padding-top: 10px;
            margin-bottom: -1px;
            background-color: #fff;
            word-break: normal;
            white-space: pre-wrap;
        }

        .table-bottom-divider_bir {
            padding-top: 10px;
            border-bottom: 2px dotted #000;
        }

        .purchased-table__header-item {
            border-bottom: 2px dotted #000;
        }

        .purchased-table__header-text,
        .billing-table__header-text {
            margin: 0px 0;
        }

        .purchased-table__item-text,
        .billing-table__item-text {
            padding: 1px 2%;
            word-break: break-word;
            -webkit-hyphens: auto;
            -ms-hyphens: auto;
            hyphens: auto;
        }

        .billing-table__item-text strong {
            font-size: 14.7px;
        }

        .purchased-table__header-text-first,
        .purchased-table__item-text-first,
        .billing-table__header-text-first,
        .billing-table__item-text-first {
            padding-left: 0;
        }

        .purchased-table__header-text-last,
        .purchased-table__item-text-last,
        .billing-table__header-text-last,
        .billing-table__item-text-last {
            padding-right: 0;
        }

        .table-top-divider {
            padding-bottom: 0px;
            border-top: 2px dotted #000;
        }

        .table-bottom-divider {
            padding-top: 0px;
            border-bottom: 2px dotted #000;
        }

        .bir-scpwd-info__list {
            padding-bottom: 10px;
            padding-left: 0;
            list-style: none;
        }

        .bir-scpwd-info__item {
            padding-top: 10px;
            border-bottom: 2px solid #000;
        }

         .dedicated_space {
            padding-top: 25px;
        }

        .bir-scpwd-info__item-label {
            padding: 10px 0;
            margin-bottom: -1px;
            background-color: #fff;
        }

        .qrcode__wrapper {}

        .qrcode__wrapper__80 {
            position: relative;
            margin-top: 30px;
            margin-bottom: 20px;
        }

        .qrcode {
            border: 2px solid #000;
            font-size: 0;
            border-radius: 6px;
        }

        .qrcode-cashback__percentage {
            font-size: 39px;
        }

        .qrcode-cashback__label {
            font-size: 15px;
        }

        .qrcode-image__wrapper {
            position: relative;
        }

        .qrcode-image__wrapper::before {
            content: "";
            display: block;
            width: 100%;
            padding: 50% 0;
        }

        .qrcode-image {
            position: absolute;
            left: 5%;
            top: 5%;
            width: 90%;
            height: 90%;
        }

        .qrcode-image_12C {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
        }

        .qrcode-info {
            margin: 0;
            padding: 8px;
            white-space: pre-line;
            overflow-wrap: break-word;
        }

         .qrcode-info__80 {
            transform: translateY(8px);
            background-color: white;
            padding: 0 20px;
            max-width: 90%;
            width: fit-content;
            overflow: hidden;
            white-space: pre-line;
            text-overflow: ellipsis;
        }

        .bottom {
            margin: 0;
            padding-top: 10px;
            padding-right: 2%;
            padding-bottom: 0px;
            padding-left: 2%;
        }

        .bottom-ios {
            margin: 0;
            padding-top: 10px;
            padding-right: 2%;
            padding-bottom: 0px;
            padding-left: 2%;
        }

        .bottom-android {
            margin: 0;
            padding-top: 15px;
            padding-right: 2%;
            padding-bottom: 27px;
            padding-left: 2%;
        }

        .pickup-note,
        .bir-accr-info,
        .vat-register-footer-info {
            margin: 0;
            padding: 15px 2%;
        }

        .extra-info {
            margin: 0;
            padding: 0px 0px 0px 0px;
            white-space: pre-line;
            overflow-wrap: break-word;
        }

        .extra-info-ios {
            margin: 0;
            padding: 0px 0px 0px 0px;
            white-space: pre-line;
            overflow-wrap: break-word;
        }

        .extra-info-android {
            margin: 0;
            padding: 0px 0px 30px 0px;
            white-space: pre-line;
            overflow-wrap: break-word;
        }


        .powered-by {
            margin: 0;
            padding: 0px 0px 10px 0px;
            letter-spacing: -1px;
        }

        .bir-accr-info__list {
            list-style: none;
            padding-left: 0;
        }

        .bir-accr-info__item {
            line-height: 1.5em;
        }

        .bar-code__wrapper {
            width: 100%;
        }

        .bar-code-info {
            margin: 0;
            width: 100%;
        }

        .delivery-info__wrapper {
            padding: 5px 0px;
            border-bottom: 2px dotted #000;
        }

        .delivery-info__item {
            padding-left: 0;
        }

        .delivery-info__item_blank {
            padding-bottom: 15px;
        }

        .bottom-blank {
            height: 100px;
        }

        .margin-bottom-10 {
            margin-bottom: 10px;
        }

        .margin-top-10 {
            margin-top: 10px;
        }

        .smallTitle {
            font-size: 20px;
            line-height: 25px;
        }

        .largeContent {
            font-size: 25px;
            font-weight: bold;
        }

        .largePaddedContent {
            font-size: 25px;
            font-weight: bold;
            padding: 8px
        }

        .mediumPaddedContent {
          padding: 8px;
          font-size: 14px;
          font-weight: bold;
        }

        .boildTitle {
            font-size: 25px;
            font-weight: bold;
        }
    </style>
</head>

<body leftmargin="0" marginwidth="0" topmargin="0" marginheight="0" offset="0">
    <div class="main">
        <!-- Main -->
        <!-- Purchased List -->
        <!-- End of Purchased List -->

        <!-- Billing -->
        <table class="billing-table" width="100%">
            <tr class="billing-table__row">
                <td class="billing-table__item table-top-divider" width="60%">
                </td>
                <td class="billing-table__item table-top-divider" width="40%">
                </td>
            </tr>

            {{#if subtotalTitle}}
            <tr class="billing-table__row">
                <td class="billing-table__item text-right" width="60%">
                    <p class="billing-table__item-text">{{subtotalTitle}}</p>
                </td>
                <td class="billing-table__item text-right" width="40%">
                    <p class="billing-table__item-text">{{subtotal}}</p>
                </td>
            </tr>
            {{/if}}

            {{#if less12vat}}
            <tr class="billing-table__row">
                <td class="billing-table__item text-right" width="60%">
                    <p class="billing-table__item-text">{{less12vatTitle}}</p>
                </td>
                <td class="billing-table__item text-right" width="40%">
                    <p class="billing-table__item-text">{{less12vat}}</p>
                </td>
            </tr>
            {{/if}}

            {{#if vatOf12}}
            <tr class="billing-table__row">
                <td class="billing-table__item text-right" width="60%">
                    <p class="billing-table__item-text">{{vatOf12Title}}</p>
                </td>
                <td class="billing-table__item text-right" width="40%">
                    <p class="billing-table__item-text">{{vatOf12}}</p>
                </td>
            </tr>
            {{/if}}

            {{#if discount}}
            <tr class="billing-table__row">
                <td class="billing-table__item text-right" width="60%">
                    <p class="billing-table__item-text">{{discountTitle}}</p>
                </td>
                <td class="billing-table__item text-right" width="40%">
                    <p class="billing-table__item-text">{{discount}}</p>
                </td>
            </tr>
            {{/if}}

            {{#if adhocDiscount}}
            <tr class="billing-table__row">
                <td class="billing-table__item text-right" width="60%">
                    <p class="billing-table__item-text">{{adhocDiscountTitle}}</p>
                </td>
                <td class="billing-table__item text-right" width="40%">
                    <p class="billing-table__item-text">{{adhocDiscount}}</p>
                </td>
            </tr>
            {{/if}}

            {{#if seniorDiscount}}
            <tr class="billing-table__row">
                <td class="billing-table__item text-right" width="60%">
                    <p class="billing-table__item-text">{{seniorDiscountTitle}}</p>
                </td>
                <td class="billing-table__item text-right" width="40%">
                    <p class="billing-table__item-text">{{seniorDiscount}}</p>
                </td>
            </tr>
            {{/if}}

            {{#if pwdDiscount}}
            <tr class="billing-table__row">
                <td class="billing-table__item text-right" width="60%">
                    <p class="billing-table__item-text">{{pwdDiscountTitle}}</p>
                </td>
                <td class="billing-table__item text-right" width="40%">
                    <p class="billing-table__item-text">{{pwdDiscount}}</p>
                </td>
            </tr>
            {{/if}}

            {{#if athleteAndCoachDiscount}}
            <tr class="billing-table__row">
                <td class="billing-table__item text-right" width="60%">
                    <p class="billing-table__item-text">{{athleteAndCoachDiscountTitle}}</p>
                </td>
                <td class="billing-table__item text-right" width="40%">
                    <p class="billing-table__item-text">{{athleteAndCoachDiscount}}</p>
                </td>
            </tr>
            {{/if}}

            {{#if medalOfValorDiscount}}
            <tr class="billing-table__row">
                <td class="billing-table__item text-right" width="60%">
                    <p class="billing-table__item-text">{{medalOfValorDiscountTitle}}</p>
                </td>
                <td class="billing-table__item text-right" width="40%">
                    <p class="billing-table__item-text">{{medalOfValorDiscount}}</p>
                </td>
            </tr>
            {{/if}}

            {{#if soloParentDiscount}}
            <tr class="billing-table__row">
                <td class="billing-table__item text-right" width="60%">
                    <p class="billing-table__item-text">{{soloParentDiscountTitle}}</p>
                </td>
                <td class="billing-table__item text-right" width="40%">
                    <p class="billing-table__item-text">{{soloParentDiscount}}</p>
                </td>
            </tr>
            {{/if}}

             {{#if takeawayFeeValue}}
            <tr class="billing-table__row">
                <td class="billing-table__item text-right" width="60%">
                    <p class="billing-table__item-text">{{takeawayFeeName}}</p>
                </td>
                <td class="billing-table__item text-right" width="40%">
                    <p class="billing-table__item-text">{{takeawayFeeValue}}</p>
                </td>
            </tr>
            {{/if}}

            {{#if shippingFeeValue}}
            <tr class="billing-table__row">
                <td class="billing-table__item text-right" width="60%">
                    <p class="billing-table__item-text">{{shippingFeeName}}</p>
                </td>
                <td class="billing-table__item text-right" width="40%">
                    <p class="billing-table__item-text">{{shippingFeeValue}}</p>
                </td>
            </tr>
            {{/if}}

            {{#if amusementTax}}
             <tr class="billing-table__row">
                <td class="billing-table__item text-right" width="60%">
                    <p class="billing-table__item-text">{{amusementTaxTitle}}</p>
                </td>
                <td class="billing-table__item text-right" width="40%">
                    <p class="billing-table__item-text">{{amusementTax}}</p>
                </td>
            </tr>
            {{/if}}

            {{#if serviceCharge}}
            <tr class="billing-table__row">
                <td class="billing-table__item text-right" width="60%">
                    <p class="billing-table__item-text">{{serviceChargeTitle}}</p>
                </td>
                <td class="billing-table__item text-right" width="40%">
                    <p class="billing-table__item-text">{{serviceCharge}}</p>
                </td>
            </tr>
            {{/if}}

            {{#if smallOrderFeeValue}}
            <tr class="billing-table__row">
                <td class="billing-table__item text-right" width="60%">
                    <p class="billing-table__item-text">{{smallOrderFeeTxt}}</p>
                </td>
                <td class="billing-table__item text-right" width="40%">
                    <p class="billing-table__item-text">{{smallOrderFeeValue}}</p>
                </td>
            </tr>
            {{/if}}

            {{#if containerFeeValue}}
            <tr class="billing-table__row">
                <td class="billing-table__item text-right" width="60%">
                    <p class="billing-table__item-text">{{containerFeeTxt}}</p>
                </td>
                <td class="billing-table__item text-right" width="40%">
                    <p class="billing-table__item-text">{{containerFeeValue}}</p>
                </td>
            </tr>
            {{/if}}

            {{#if tax}}
            <tr class="billing-table__row">
                <td class="billing-table__item text-right" width="60%">
                    <p class="billing-table__item-text">{{taxTitle}}</p>
                </td>
                <td class="billing-table__item text-right" width="40%">
                    <p class="billing-table__item-text">{{tax}}</p>
                </td>
            </tr>
            {{/if}}


            {{#if rounding}}
            <tr class="billing-table__row">
                <td class="billing-table__item text-right" width="60%">
                    <p class="billing-table__item-text">{{roundingTitle}}</p>
                </td>
                <td class="billing-table__item text-right" width="40%">
                    <p class="billing-table__item-text">{{rounding}}</p>
                </td>
            </tr>
            {{/if}}

            {{#if showOrderSummary}}
            <tr class="billing-table__row">
                <td class="billing-table__item table-bottom-divider" width="60%">
                </td>
                <td class="billing-table__item table-bottom-divider" width="40%">
                </td>
            </tr>
            {{/if}}
        </table>

        <!-- Total -->
        <table class="billing-table" width="100%">
            <tr class="billing-table__row">
                <td class="billing-table__item text-right" width="60%">
                    <p class="billing-table__item-text"><strong>{{totalTitle}}</strong></p>
                </td>
                <td class="billing-table__item text-right" width="40%">
                    <p class="billing-table__item-text"><strong>{{total}}</strong></p>
                </td>
            </tr>

            <tr class="billing-table__row">
                <td class="billing-table__item table-bottom-divider" width="60%">
                </td>
                <td class="billing-table__item table-bottom-divider" width="40%">
                </td>
            </tr>
        </table>
        <!-- End of Billing -->

        <!-- Pre order -->
        {{#if showPreorderSummary}}
        <table class="billing-table" width="100%">

            {{#if depositAmountTitle}}
            <tr class="billing-table__row">
                <td class="billing-table__item text-right" width="60%">
                    <p class="billing-table__item-text"><strong>{{depositAmountTitle}}</strong></p>
                </td>
                <td class="billing-table__item text-right" width="40%">
                    <p class="billing-table__item-text"><strong>{{depositAmount}}</strong></p>
                </td>
            </tr>
            {{/if}}

            <tr class="billing-table__row">
                <td class="billing-table__item text-right" width="60%">
                    <p class="billing-table__item-text"><strong>{{unPaidBalanceTitle}}</strong></p>
                </td>
                <td class="billing-table__item text-right" width="40%">
                    <p class="billing-table__item-text"><strong>{{unPaidBalance}}</strong></p>
                </td>
            </tr>

            <tr class="billing-table__row">
                <td class="billing-table__item table-bottom-divider" width="60%">
                </td>
                <td class="billing-table__item table-bottom-divider" width="40%">
                </td>
            </tr>
        </table>
        {{/if}}
        <!-- End of Pre order -->

        <!-- Payment -->
        {{#if payment}}
        <table class="billing-table" width="100%">
            <tr class="billing-table__row">
                <td class="billing-table__item" width="60%">
                </td>
                <td class="billing-table__item" width="40%">
                </td>
            </tr>
            {{#each payment}}
            <tr class="billing-table__row">
                <td class="billing-table__item text-right" width="60%">
                    <p class="billing-table__item-text">{{paymentMethodName}}</p>
                </td>
                <td class="billing-table__item text-right" width="40%">
                    <p class="billing-table__item-text">{{amount}}</p>
                </td>
            </tr>
            {{#if changeValue}}
            <tr class="billing-table__row">
                <td class="billing-table__item text-right" width="60%">
                    <p class="billing-table__item-text">{{changeTitle}}</p>
                </td>
                <td class="billing-table__item text-right" width="40%">
                    <p class="billing-table__item-text">{{changeValue}}</p>
                </td>
            </tr>
            {{/if}}
            {{/each}}

            <tr class="billing-table__row">
                <td class="billing-table__item table-bottom-divider" width="60%">
                </td>
                <td class="billing-table__item table-bottom-divider" width="40%">
                </td>
            </tr>
        </table>
        {{/if}}
        <!-- End of Payment -->

        <!--   Summary     -->
        {{#if showTaxSummary}}
        <table class="purchased-table" width="100%">
            <tr class="purchased-table__row">
                {{#if taxSummaryTitleString}}
                {{#each taxSummaryTitleString}}
                <td class="purchased-table__item" width="{{#if @index}}30%{{else}}40%{{/if}}">
                    <h3 class="purchased-table__header-text {{#if
                                        @index}}text-right{{else}}purchased-table__header-text-first{{/if}} ">
                        <strong>{{this}}</strong>
                    </h3>
                </td>
                {{/each}}
                {{/if}}
            </tr>
            {{#if taxSummaryItems}}
            {{#each taxSummaryItems}}
            <tr class="purchased-table__row">
                <td class="purchased-table__item" colspan="1">
                    <p class="purchased-table__item-text">{{title}}</p>
                </td>
                <td class="purchased-table__item text-right" colspan="1">
                    <p class="purchased-table__item-text">{{amount}}</p>
                </td>
                <td class="purchased-table__item text-right" colspan="1">
                    <p class="purchased-table__item-text">{{tax}}</p>
                </td>
            </tr>
            {{/each}}
            <tr class="purchased-table__row">
                <td class="billing-table__item table-bottom-divider" colspan="1">
                </td>
                <td class="billing-table__item table-bottom-divider" colspan="1">
                </td>
                <td class="billing-table__item table-bottom-divider" width="100%">
                </td>
            </tr>
            {{/if}}

        </table>

        {{/if}}
        <!--   End of Summary     -->

        <!-- Sales Info -->
        {{#if showVatSummary}}
        <table class="billing-table" width="100%">
            <tr class="billing-table__row">
                <td class="billing-table__item" width="60%">
                </td>
                <td class="billing-table__item" width="40%">
                </td>
            </tr>

            {{#if amountOutStanding}}
            <tr class="billing-table__row">
                <td class="billing-table__item" width="60%">
                    <p class="billing-table__item-text">{{amountOutStandingTitle}}</p>
                </td>
                <td class="billing-table__item" width="40%">
                    <p class="billing-table__item-text">{{amountOutStanding}}</p>
                </td>
            </tr>
            {{/if}}

            {{#if vatableSales}}
            <tr class="billing-table__row">
                <td class="billing-table__item" width="60%">
                    <p class="billing-table__item-text">{{vatableSalesTitle}}</p>
                </td>
                <td class="billing-table__item" width="40%">
                    <p class="billing-table__item-text">{{vatableSales}}</p>
                </td>
            </tr>
            {{/if}}

            {{#if vatAmount}}
            <tr class="billing-table__row">
                <td class="billing-table__item" width="60%">
                    <p class="billing-table__item-text">{{vatAmountTitle}}</p>
                </td>
                <td class="billing-table__item" width="40%">
                    <p class="billing-table__item-text">{{vatAmount}}</p>
                </td>
            </tr>
            {{/if}}

            {{#if vatExemptSales}}
            <tr class="billing-table__row">
                <td class="billing-table__item" width="60%">
                    <p class="billing-table__item-text">{{vatExemptSalesTitle}}</p>
                </td>
                <td class="billing-table__item" width="40%">
                    <p class="billing-table__item-text">{{vatExemptSales}}</p>
                </td>
            </tr>
            {{/if}}

            {{#if zeroRatedSales}}
            <tr class="billing-table__row">
                <td class="billing-table__item" width="60%">
                    <p class="billing-table__item-text">{{zeroRatedSalesTitle}}</p>
                </td>
                <td class="billing-table__item" width="40%">
                    <p class="billing-table__item-text">{{zeroRatedSales}}</p>
                </td>
            </tr>
            {{/if}}

            <tr class="billing-table__row">
                <td class="billing-table__item table-bottom-divider" width="60%">
                </td>
                <td class="billing-table__item table-bottom-divider" width="40%">
                </td>
            </tr>
        </table>
        {{/if}}
        <!-- End of Sales Info -->

        <!-- BIR Info -->
        {{#if birInfoList}}
        <table class="billing-table" width="100%">
            <tr class="billing-table__row">
                <td class="billing-table__item">
                </td>
            </tr>

            <tr class="billing-table__row">
                <td class="billing-table__item" width="100%">
                    {{#each birInfoList}}
                    {{#if value}}
                    <p class="bir-scpwd-info__item-label-text">{{name}} {{value}}</p>
                    {{else}}
                    <ul class="bir-scpwd-info__list">
                        <li class="bir-scpwd-info__item {{#if needDedicatedSpace}}dedicated_space{{/if}}">
                            <label class="bir-scpwd-info__item-label">{{name}}</label>
                        </li>
                    </ul>
                    {{/if}}
                    {{/each}}
                </td>
            </tr>

            <tr class="billing-table__row">
                <td class="billing-table__item table-bottom-divider_bir">
                </td>
            </tr>
        </table>
        {{/if}}
        <!-- End of BIR SC PWD Info -->

        <!--   loyalty     -->
        {{#if showReceiptStoreCredit}}
        <table class="purchased-table" width="100%">

            <tr class="purchased-table__row">
                {{#if receiptStoreCreditTitleString}}
                {{#each receiptStoreCreditTitleString}}
                <td class="purchased-table__header-item" width="50%">
                    <h3 class="purchased-table__header-text {{#if
                                        @index}}text-right{{else}}purchased-table__header-text-first{{/if}} ">
                        <strong>{{this}}</strong>
                    </h3>
                </td>
                {{/each}}
                {{/if}}
            </tr>
            <tr class="purchased-table__row">
                <td class="purchased-table__item" width="50%">
                    <p class="purchased-table__item-text purchased-table__item-text-first">{{earnedTitle}}</p>
                </td>
                <td class="purchased-table__item text-right" width="50%">
                    <p class="purchased-table__item-text purchased-table__item-text-last">{{loyaltyEarned}}</p>
                </td>
            </tr>

            <tr class="purchased-table__row">
                <td class="purchased-table__item" width="50%">
                    <p class="purchased-table__item-text purchased-table__item-text-first">{{balanceTitle}}</p>
                </td>
                <td class="purchased-table__item text-right" width="50%">
                    <p class="purchased-table__item-text purchased-table__item-text-last">{{loyaltyBalance}}</p>
                </td>
            </tr>

            <tr class="purchased-table__row">
                <td class="purchased-table__item" width="50%">
                    <p class="purchased-table__item-text purchased-table__item-text-first">{{spentTitle}}</p>
                </td>
                <td class="purchased-table__item text-right" width="50%">
                    <p class="purchased-table__item-text purchased-table__item-text-last">{{loyaltySpent}}</p>
                </td>
            </tr>
        </table>
        {{/if}}

        {{#if showReceiptStoreCredit}}
          <div style="display: flex; align-items: center; margin-bottom: 10px;">
            <hr style="flex: 1; border: none; border-top: 2px dotted #000;">
            {{#if cashbackExpirationDesc}}
            <span style="padding: 0 10px;">{{cashbackExpirationDesc}}</span>
            {{/if}}
            <hr style="flex: 1; border: none; border-top: 2px dotted #000;">
          </div>
        {{/if}}
        <!--   End of loyalty     -->

        <!-- Shipping Info -->
        {{#if onlineChannelNotesContent}}
        <table class="billing-table" width="100%">
            <tr class="billing-table__row">
                <td class="billing-table__item">
                </td>
            </tr>

            <tr class="billing-table__row">
                <td class="billing-table__item">
                    <h3 class="billing-table__item-text-first">{{onlineChannelNotesTitle}}</h3>
                    <p class="billing-table__item-text billing-table__item-text-first pre-line">
                        {{onlineChannelNotesContent}}</p>
                </td>
            </tr>

            <tr class="billing-table__row">
                <td class="billing-table__item table-bottom-divider">
                </td>
            </tr>
        </table>
        {{/if}}
        <!-- End of Shipping Info -->

        <!-- Pick Up Info -->
        {{#if onlineOrderNoteContent}}
        <table class="billing-table" width="100%">
            <tr class="billing-table__row">
                <td class="billing-table__item">
                </td>
            </tr>

            <tr class="billing-table__row">
                <td class="billing-table__item">
                    <h3 class="billing-table__item-text-first">{{onlinePickUpNoteTitle}}</h3>
                    <p class="billing-table__item-text billing-table__item-text-first pre-line">
                        {{onlineOrderNoteContent}}</p>
                </td>
            </tr>

            <tr class="billing-table__row">
                <td class="billing-table__item table-bottom-divider">
                </td>
            </tr>
        </table>
        {{/if}}

        {{#if showDeliveryOrContactInfo}}
        <div class="delivery-info__wrapper text-left">
            <ul class="store-info__list">
                {{#if deliveryOrContactInfo.shippingToName}}
                <li class="delivery-info__item_blank pre-line"><strong>{{deliveryOrContactInfo.shippingToName}}</strong>
                </li>
                {{/if}}
                {{#if deliveryOrContactInfo.name}}
                <li class="delivery-info__item pre-line">{{deliveryOrContactInfo.name}}</li>
                {{/if}}
                {{#if deliveryOrContactInfo.phone}}
                <li class="delivery-info__item pre-line">{{deliveryOrContactInfo.phone}}</li>
                {{/if}}
                {{#if deliveryOrContactInfo.address}}
                <li class="delivery-info__item_blank pre-line">{{deliveryOrContactInfo.address}}</li>
                {{/if}}
                {{#if deliveryOrContactInfo.notes}}
                <li class="delivery-info__item_blank pre-line"><strong>{{deliveryOrContactInfo.notesName}}</strong></li>
                <li class="delivery-info__item pre-line">{{deliveryOrContactInfo.notes}}</li>
                {{/if}}
            </ul>
        </div>
        {{/if}}

        <!-- End of Pick Up Info -->

        <!-- Beep Cashback Info -->
        <!-- <table class="billing-table" width="100%">
          <tr class="billing-table__row">
            <td class="billing-table__item table-top-divider" colspan="2">
            </td>
            <td class="billing-table__item table-top-divider" colspan="2">
            </td>
          </tr>

          <tr class="billing-table__row">
            <td class="billing-table__header-item" width="50%">
              <h3 class="billing-table__header-text billing-table__header-text-first">
                <strong>
                  Beep Cashback
                </strong>
              </h3>
            </td>
            <td class="billing-table__header-item text-right" width="50%">
              <h3 class="billing-table__header-text billing-table__header-text-last"><strong>Amount</strong></h3>
            </td>
          </tr>

          <tr class="billing-table__row">
            <td class="billing-table__item" width="35%">
              <p class="billing-table__item-text billing-table__header-text-first">Earned</p>
            </td>
            <td class="billing-table__item text-right" width="30%">
              <p class="billing-table__item-text billing-table__item-text-last">5.72</p>
            </td>
          </tr>

          <tr class="billing-table__row">
            <td class="billing-table__item" width="35%">
              <p class="billing-table__item-text billing-table__header-text-first">Spent</p>
            </td>
            <td class="billing-table__item text-right" width="30%">
              <p class="billing-table__item-text billing-table__item-text-last">0.00</p>
            </td>
          </tr>

          <tr class="billing-table__row">
            <td class="billing-table__item" width="35%">
              <p class="billing-table__item-text billing-table__header-text-first">Balance</p>
            </td>
            <td class="billing-table__item text-right" width="30%">
              <p class="billing-table__item-text billing-table__item-text-last">42.43</p>
            </td>
          </tr>

          <tr class="billing-table__item">
            <td class="billing-table__item table-bottom-divider" colspan="2">
            </td>
            <td class="billing-table__item table-bottom-divider" colspan="2">
            </td>
          </tr>
        </table> -->
        <!-- End of Beep Cashback Info -->

        <!-- QR code -->
        {{#if enablePrintQRCode}}

        <div class="{{#if isPrinterPaperWidth80}}qrcode__wrapper__80 {{else}} qrcode__wrapper {{/if}} text-center">
            <strong>
                <p class="{{#if isPrinterPaperWidth80}} qrcode-info__80 {{else}} qrcode-info {{/if}}">{{qrCodeAboveInfo}}</p>            </strong>
            <div class="qrcode">
                <table class="qrcode-table" width="100%">
                    <tr class="qrcode-table__item">
                        <td class="qrcode-table__item text-center "
                            width="{{#if isPrinterPaperWidth58}}40%{{else}}50%{{/if}}">
                             {{#if enablePrintCashback}}
                                <p class="qrcode-cashback__percentage">{{defaultLoyaltyRatio}}%</p>
                                <label class="qrcode-cashback__label">{{cashBackTxt}}</label>
                            {{else}}
                                {{#if membershipSmallTitle}}<p class="smallTitle">{{membershipSmallTitle}}</p>{{/if}}
                                {{#if membershipBoildTitle}}<p class="boildTitle">{{membershipBoildTitle}}</p>{{/if}}
                                {{#if membershipLargeContentTitle1}}<p class="largeContent">{{membershipLargeContentTitle1}}</p>{{/if}}
                                {{#if membershipLargeContentTitle2}}<p class="largeContent">{{membershipLargeContentTitle2}}</p>{{/if}}
                                {{#if membershipSmallBottomTitle}}<p class="smallTitle">{{membershipSmallBottomTitle}}</p>{{/if}}
                            {{/if}}
                        </td>
                        <td class="qrcode-table__item" width="{{#if isPrinterPaperWidth58}}60%{{else}}50%{{/if}}">
                            <div class="qrcode-image__wrapper">
                                <img class="{{#if isPrinterPaperWidth58}}qrcode-image_12C{{else}}qrcode-image{{/if}}"
                                    src="_qrCodeImage_" alt="">
                            </div>
                        </td>
                    </tr>
                </table>
            </div>
            <strong>
                <p class="qrcode-info margin-bottom-10">{{qrCodeUnderInfo}}</p>
            </strong>

        </div>
        {{/if}}

        {{#if enablePrintEInvoice}}

        <div class="{{#if isPrinterPaperWidth80}}qrcode__wrapper__80 {{else}} qrcode__wrapper {{/if}} text-center">
            <div class="qrcode">
                <table class="qrcode-table" width="100%">
                    <tr class="qrcode-table__item">
                        <td class="qrcode-table__item text-center "
                            width="{{#if isPrinterPaperWidth58}}40%{{else}}50%{{/if}}">
                            <p class="largePaddedContent">{{eInvoiceDescription}}</p>
                            <p class="mediumPaddedContent">Only available within calendar month of purchase</p>
                        </td>
                        <td class="qrcode-table__item" width="{{#if isPrinterPaperWidth58}}60%{{else}}50%{{/if}}">
                            <div class="qrcode-image__wrapper">
                                <img class="{{#if isPrinterPaperWidth58}}qrcode-image_12C{{else}}qrcode-image{{/if}}"
                                    src="_eInvoiceQrCodeImage_" alt="">
                            </div>
                        </td>
                    </tr>
                </table>
            </div>

        </div>
        {{/if}}

        <!-- End of QR code -->
    </div>
    <!-- End of Main -->

    <!-- Bottom -->
    {{#if isIOS}}
    <div class="bottom-ios text-center">
        {{else}}
    <div class="bottom-android text-center">
            {{/if}}
            {{#if preOrderNotes}}
            <p class="pickup-note"><strong>{{preOrderNotes}}</strong></p>
            {{/if}}

            <!-- Extra Info -->
            {{#if isIOS}}
            <p class="extra-info-ios"><strong>{{footerLabelString}}</strong></p>
            {{else}}
            <p class="extra-info-android"><strong>{{footerLabelString}}</strong></p>
            {{/if}}
            <!-- End of Extra Info -->

            <!-- Bir Accr Info -->
            {{#if birAccrInfo}}
            <div class="bir-accr-info">
                <p class="pre-line">{{birAccrInfo}}</p>
                <ul class="bir-accr-info__list">
                    <li class="bir-accr-info__item">{{accrNumber}}</li>
                    <li class="bir-accr-info__item">{{ptuNumber}}</li>
                    <li class="bir-accr-info__item">{{dateIssueNumber}}</li>
                </ul>
            </div>
            {{/if}}
            <!-- End of Bir Accr Info -->

            <!-- VAT Register Footer Info -->
            {{#if vatRegisterFooterInfo}}
            <div class="vat-register-footer-info">
                <p class="pre-line"><strong>{{vatRegisterFooterInfo}}</strong></p>
            </div>
            {{/if}}
            <!-- End of VAT Register Footer Info -->

            <!-- Bar Code -->
            {{#if showBarcode}}
            <div class="bar-code__wrapper">
                <img class="bar-code-info" src="_barCodeImage_" alt="">
            </div>
            {{/if}}
            <!-- End of Bar Code -->
            <!-- End of Bottom -->
        </div>
        <div class="text-center">
            <!-- Power by -->
            {{#if storehubPoweredInfo}}
            <p class="powered-by">
                <strong>
                    POWERED BY STOREHUB.COM CLOUD POS
                </strong>
            </p>
            {{/if}}
            <!-- End of Power by -->
        </div>
        {{#if isPrinterPaperWidth58}}
        <div class="bottom-blank"></div>
        {{/if}}
</body>

</html>