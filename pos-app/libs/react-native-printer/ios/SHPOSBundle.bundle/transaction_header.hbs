<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>Print Invoice</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        * {
            -webkit-box-sizing: border-box;
            -moz-box-sizing: border-box;
            box-sizing: border-box;
            margin: 0 auto;
            font-size: 14.7px;
            font-family: Roboto, Ubuntu, Helvetica, Arial, sans-serif;
        }

        html,
        body {
            font-size: 14.7px;
        }

        .text-center {
            text-align: center;
        }

        .text-right {
            text-align: right;
        }

        .col-6 {
            display: inline-block;
            width: 50%;
        }

        .pre-line {
            white-space: pre-line;
        }

        .main {
            clear: left;
            padding: 0px 8px 0px 8px;
        }

        /* 
        Create the aspect ratio with percentage padding
        */
        .logoContainer {
            width: 100%;
            position: relative;
        }

        .logoContainer::before {
            content: "";
            display: block;
            padding-top: 50%;
        }

        .logo {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .store__name {
            margin: 10px 0px 0px 0px;
        }

        .title {
            margin: 10px 0;
        }

        .store__name strong,
        .title strong {
            font-size: 24px;
        }

        .store-info__list,
        .base-info__list {
            margin: 0 0 0px;
            list-style: none;
            padding-left: 0;
        }

        .store-info__item,
        .customer-info,
        .base-info__item,
        .base-info__normal,
        .scpwd-Info__text {
            line-height: 1.2em;
        }

        .base-info__item strong {
            font-size: 14.7px;
        }

        .base-order__item {
            line-height: 1.5em;
            font-size: 21.3px;
        }

        .base-order__item_margin {
            margin-bottom: 15px;
        }

        .base-order__item strong {
            font-size: 43.6px;
        }

        .customer-info,
        .table-info {
            padding: 15px 0;
        }

        .base-info__wrapper {
            padding: 5px 0;
        }

        .purchased-title {
            padding: 0px 0px 10px 0px;
        }

        .purchased-table,
        .purchased-table__row,
        .purchased-table__header-item,
        .purchased-table__item,
        .billing-table,
        .billing-table__row,
        .billing-table__item,
        .billing-table__header-item {
            border: 0;
        }

        .purchased-table__header-item {
            border-bottom: 2px dashed #000;
        }



        .purchased-table__header-text,
        .billing-table__header-text {
            margin: 5px 0;
        }

        .purchased-table__item-text,
        .billing-table__item-text {
            padding: 5px 2%;
            word-break: break-word;
            -webkit-hyphens: auto;
            -ms-hyphens: auto;
            hyphens: auto;
        }

        .purchased-table__header-text-first,
        .purchased-table__item-text-first,
        .billing-table__header-text-first,
        .billing-table__item-text-first {
            padding-left: 0;
        }

        .purchased-table__header-text-last,
        .purchased-table__item-text-last,
        .billing-table__header-text-last,
        .billing-table__item-text-last {
            padding-right: 0;
        }

        .bir-scpwd-info__list {
            padding-bottom: 10px;
            padding-left: 0;
            list-style: none;
        }

        .bir-scpwd-info__item {
            padding-top: 10px;
            border-bottom: 2px solid #000;
        }

        .bir-scpwd-info__item-label {
            padding: 10px 0;
            margin-bottom: -1px;
            background-color: #fff;
        }

        .qrcode {
            padding: 5px;
            border: 1px solid #000;
            font-size: 0;
            border-radius: 6px;
        }

        .qrcode-cashback__percentage {
            font-size: 29px;
        }

        .qrcode-cashback__label {
            font-size: 15px;
        }

        .qrcode-image__wrapper {
            position: relative;
        }

        .qrcode-image__wrapper::before {
            content: "";
            display: block;
            width: 100%;
            padding: 50% 0;
        }

        .qrcode-image {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
        }

        .qrcode-info {
            margin: 0;
            padding: 8px;
        }

        .bottom,
        .pickup-note,
        .extra-info,
        .bir-accr-info,
        .vat-register-footer-info,
        .bar-code-info,
        .powered-by {
            margin: 0;
            padding: 15px 2%;
        }

        .bir-accr-info__list {
            list-style: none;
            padding-left: 0;
        }

        .bir-accr-info__item {
            line-height: 1.5em;
        }

        .bar-code__wrapper {
            width: 100%;
        }

        .bar-code-info {
            width: 100%;
        }

        .delivery-info__item {
            padding-left: 0;
        }

        .delivery-info__item_blank {
            padding-bottom: 15px;
        }

        .titleFont {
            font-size: 24px;
        }

        .subTitle {
            font-size: 19px;
            font-weight: 500;
            margin-top: -5px;
            margin-bottom: 20px;
        }

        .normalFont {
            font-size: 14.7px;
        }

        .dash-divider {
            border-bottom: 2px dotted #000;
        }
    </style>
</head>

<body leftmargin="0" marginwidth="0" topmargin="0" marginheight="0" offset="0">
    <div class="main">
        <!-- Store Info -->
        <center>
            {{#if logoImage}}
            <div class="logoContainer">
                <img class="logo" src="{{logoImage}}" alt="">
            </div>
            {{/if}}
            <div>
                <h1 class="store__name"><strong>{{storeName}}</strong></h1>
                <ul class="store-info__list">
                    <li class="store-info__item pre-line">{{address}}</li>
                    <li class="store-info__item pre-line">{{phone}}</li>
                    <li class="store-info__item pre-line">{{companyName}}</li>
                    {{#if birCompanyName}}
                    <li class="store-info__item pre-line">{{birCompanyName}}</li>
                    {{/if}}
                    {{#if brn}}
                    <li class="store-info__item pre-line">{{brn}}</li>
                    {{/if}}
                    {{#if sstIdNo}}
                    <li class="store-info__item pre-line">{{sstIdNo}}</li>
                    {{/if}}
                    {{#if gstIdNo}}
                    <li class="store-info__item pre-line">{{gstIdNo}}</li>
                    {{/if}}
                    {{#if receipt}}
                    <li class="store-info__item pre-line">{{receipt}}</li>
                    {{/if}}
                </ul>
            </div>
        </center>
        <!-- End of Store Info -->

        <!-- Customer Info -->
        {{#if customerInfo}}
        <div class="customer-info">
            {{{customerInfo}}}
        </div>
        {{/if}}
        <!-- End of Customer Info -->

        <!-- Basic Info -->
        <div class="base-info__wrapper">
            <h2 class="purchased-title text-center {{#if isReceiptTitleBig}}titleFont{{/if}}">{{receiptTitle}}</h2>
            {{#if pickUpDate}}<p class="text-center subTitle">{{pickUpDate}}</p>{{/if}}
            <ul class="base-info__list">
                {{#if orderNo}}
                <li class="base-order__item text-center">{{orderNoName}}</li>
                <li class="base-order__item base-order__item_margin  text-center"><strong>{{orderNo}}</strong></li>
                {{/if}}
                {{#if preOrderDate}}
                <li class="base-info__normal pre-line"><strong>{{preOrderDate}}</strong></li>
                {{/if}}
                {{#if reprintDate}}
                <li class="base-info__item">{{reprintDate}}</li>
                {{/if}}
                <li class="base-info__item">{{receiptDate}}</li>
                 {{#if voidNo}}
                <li class="base-info__item">{{voidNo}}</li>
                {{/if}}
                 {{#if orderId}}
                <li class="base-info__item">{{orderId}}</li>
                 {{/if}}
                {{#if receiptNumber}}
                <li class="base-info__item">{{receiptNumber}}</li>
                {{/if}}
                {{#if orderNumber}}
                <li class="base-info__item">{{orderNumber}}</li>
                {{/if}}
                {{#if minNumber}}
                <li class="base-info__item">{{minNumber}}</li>
                {{/if}}
                {{#if serialNumber}}
                <li class="base-info__item">{{serialNumber}}</li>
                {{/if}}
                {{#if reasonString}}
                <li class="base-info__item">{{reasonString}}</li>
                {{/if}}
                {{#if cashierInfo}}
                <li class="base-info__item">{{cashierInfo}}</li>
                {{/if}}
                <li class="base-info__item">{{registerNumber}}</li>
                {{#if noteString}}
                    {{#unless pickUpDate}}
                        <li class="base-info__item">{{noteString}}</li>
                    {{/unless}}
                {{/if}}
            </ul>
        </div>
        <!-- End of Basic Info -->

        <!-- Table Info -->
        {{#if middleOrderTableTitleWithContent}}
        <div class="table-info text-center">
            <p class="pre-line"><strong>{{middleOrderTableTitleWithContent}}</strong></p>
        </div>
        {{/if}}
        <!-- End of Table Info -->

        <!-- receiptTitlePH -->
        {{#if receiptTitlePH}}
        <div class="text-center">
            <p class="pre-line"><strong>{{receiptTitlePH}}</strong></p>
        </div>
        {{/if}}

        {{#if usingDiscountLayout}}
        <!-- Purchased List -->
        <table class="purchased-table" width="100%">
            <tr class="purchased-table__row">
                <td class="purchased-table__header-item dash-divider" width={{lookup columnWidths 0}}>
                    <h3 class="purchased-table__header-text purchased-table__header-text-first">
                        <strong>{{lookup columnTitleString 0}}</strong>
                    </h3>
                </td>
                <td class="purchased-table__header-item dash-divider" width={{lookup columnWidths 1}}>
                    <h3 class="purchased-table__header-text text-right">
                        <strong>{{lookup columnTitleString 1}}</strong>
                    </h3>
                </td>
                <td class="purchased-table__header-item dash-divider" width={{lookup columnWidths 2}}>
                    <h3 class="purchased-table__header-text text-right">
                        <strong>{{lookup columnTitleString 2}}</strong>
                    </h3>
                </td>
                <td class="purchased-table__header-item dash-divider" width={{lookup columnWidths 3}}>
                    <h3 class="purchased-table__header-text text-right">
                        <strong>{{lookup columnTitleString 3}}</strong>
                    </h3>
                </td>
                <td class="purchased-table__header-item dash-divider" width={{lookup columnWidths 4}}>
                    <h3 class="purchased-table__header-text text-right">
                        <strong>{{lookup columnTitleString 4}}</strong>
                    </h3>
                </td>
            </tr>
        </table>
        <div style="height: 15px;"></div>

        {{else}}

        <table class="purchased-table" width="100%">
            <tr class="purchased-table__row">
                <td class="purchased-table__header-item dash-divider" width={{lookup columnWidths 0}}>
                    <h3 class="purchased-table__header-text purchased-table__header-text-first">
                        <strong>{{lookup columnTitleString 0}}</strong>
                    </h3>
                </td>
                <td class="purchased-table__header-item dash-divider" width={{lookup columnWidths 1}}>
                    <h3 class="purchased-table__header-text text-center">
                        <strong>{{lookup columnTitleString 1}}</strong>
                    </h3>
                </td>
                <td class="purchased-table__header-item dash-divider" width={{lookup columnWidths 2}}>
                    <h3 class="purchased-table__header-text text-center">
                        <strong>{{lookup columnTitleString 2}}</strong>
                    </h3>
                </td>
                <td class="purchased-table__header-item dash-divider" width={{lookup columnWidths 3}}>
                    <h3 class="purchased-table__header-text text-right">
                        <strong>{{lookup columnTitleString 3}}</strong>
                    </h3>
                </td>
            </tr>
        </table>
        <div style="height: 15px;"></div>
        {{/if}}
    </div>

</body>

</html>