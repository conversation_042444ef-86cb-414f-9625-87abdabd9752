<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>Print Invoice</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        * {
            -webkit-box-sizing: border-box;
            -moz-box-sizing: border-box;
            box-sizing: border-box;
            margin: 0 auto;
            font-size: 14.7px;
            font-family: Roboto, Ubuntu, Helvetica, Arial, sans-serif;
        ;
        }

        html,
        body {
            font-size: 14.7px;
        }

        .text-center {
            text-align: center;
        }

        .text-top {
            vertical-align: top;
        }

        .text-right {
            text-align: right;
        }

        .col-6 {
            display: inline-block;
            width: 50%;
        }

        .pre-line {
            white-space: pre-line;
        }

        .main {
            clear: left;
            padding: 0px 8px;
        }

        .logo {
            width: 30%;
            max-width: 120px;
        }

        .store__name {
            margin: 20px 0;
        }

        .title {
            margin: 10px 0;
        }

        .store__name strong,
        .title strong {
            font-size: 14.7px;
        }

        .store-info__list,
        .base-info__list {
            margin: 0 0 0px;
            list-style: none;
            padding-left: 0;
        }

        .store-info__item,
        .customer-info,
        .base-info__item,
        .base-info__normal,
        .scpwd-Info__text {
            line-height: 1.5em;
        }

        .base-info__item strong {
            font-size: 17px;
        }

        .base-order__item {
            line-height: 1.5em;
            font-size: 33px;
        }

        .base-order__item strong {
            font-size: 43px;
        }

        .customer-info,
        .base-info__wrapper,
        .table-info,
        .purchased-title {
            padding: 15px 0;
        }

        .purchased-table,
        .purchased-table__row,
        .purchased-table__header-item,
        .purchased-table__item,
        .billing-table,
        .billing-table__row,
        .billing-table__item,
        .billing-table__header-item {
            border: 0;
        }

        .purchased-table__item-main {
            border: 0;
            padding-top: 5px;
        }

        .purchased-table__header-item {
            border-bottom: 2px dashed #000;
        }

        .purchased-table__header-text,
        .billing-table__header-text {
            margin: 10px 0;
        }

        .purchased-table__item-text,
        .billing-table__item-text {
            margin: -3px 0;
            word-break: break-word;
            -webkit-hyphens: auto;
            -ms-hyphens: auto;
            hyphens: auto;
        }

        .purchased-table__header-text-first,
        .purchased-table__item-text-first,
        .billing-table__header-text-first,
        .billing-table__item-text-first {
            padding-left: 0;
        }

        .purchased-table__header-text-last,
        .purchased-table__item-text-last,
        .billing-table__header-text-last,
        .billing-table__item-text-last {
            padding-right: 0;
        }

        .table-top-divider {
            padding-bottom: 5px;
            border-top: 2px dashed #000;
        }

        .table-bottom-divider {
            padding-top: 5px;
            border-bottom: 2px dashed #000;
        }

        .bir-scpwd-info__list {
            padding-bottom: 10px;
            padding-left: 0;
            list-style: none;
        }

        .bir-scpwd-info__item {
            padding-top: 10px;
            border-bottom: 2px solid #000;
        }

        .bir-scpwd-info__item-label {
            padding: 10px 0;
            margin-bottom: -1px;
            background-color: #fff;
        }

        .qrcode__wrapper {
            padding: 10px;
            border-bottom: 2px dashed #000;
        }

        .qrcode {
            padding: 5px;
            border: 1px solid #000;
            font-size: 0;
            border-radius: 6px;
        }

        .qrcode-cashback__percentage {
            font-size: 29px;
        }

        .qrcode-cashback__label {
            font-size: 15px;
        }

        .qrcode-image__wrapper {
            position: relative;
        }

        .qrcode-image__wrapper::before {
            content: "";
            display: block;
            width: 100%;
            padding: 50% 0;
        }

        .qrcode-image {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
        }

        .qrcode-info {
            margin: 0;
            padding: 8px;
        }

        .spacer {
            height: 5px;
        }

        .bottom,
        .pickup-note,
        .extra-info,
        .bir-accr-info,
        .vat-register-footer-info,
        .bar-code-info,
        .powered-by {
            margin: 0;
            padding: 15px 2%;
        }

        .bir-accr-info__list {
            list-style: none;
            padding-left: 0;
        }

        .bir-accr-info__item {
            line-height: 1.5em;
        }

        .bar-code__wrapper {
            width: 100%;
        }

        .bar-code-info {
            width: 100%;
        }

        .delivery-info__item {
            padding-left: 0;
        }

        .delivery-info__item_blank {
            padding-bottom: 15px;
        }

        .dash-divider {
            border-bottom: 2px dashed #000;
        }
    </style>
</head>

<body leftmargin="0" marginwidth="0" topmargin="0" marginheight="0" offset="0">
<div class="main">
    <!-- Main -->
    <div>

        {{#if usingDiscountLayout}}

            <!-- Purchased List -->
            <table class="purchased-table" width="100%">

                {{#if purchasedItems}}
                    {{#each purchasedItems}}
                        <tr class="purchased-table__row ">
                            <td class="purchased-table__item-main text-top" colspan="5">
                                <p class="purchased-table__item-text purchased-table__item-text-first">
                                    <strong>{{itemName}}</strong>
                                </p>
                            </td>
                        </tr>
                        {{#if options}}
                            <tr class="purchased-table__row">
                                <td lass="purchased-table__item" colspan="4">
                                    <p
                                            class="purchased-table__item-text purchased-table__item-text-first purchased-table__item-text-last">
                                        {{options}}</p>
                                </td>
                            </tr>
                        {{/if}}

                        {{#if sn}}
                            <tr class="purchased-table__row">
                                <td lass="purchased-table__item" colspan="4">
                                    <p
                                            class="purchased-table__item-text purchased-table__item-text-first purchased-table__item-text-last">
                                        {{sn}}</p>
                                </td>
                            </tr>
                        {{/if}}

                        {{#if itemDiscountValue}}
                            <tr class="purchased-table__row">
                                <td class="purchased-table__item" colspan="5">
                                    <p class="purchased-table__item-text purchased-table__item-text-first">
                                        <i>{{itemDiscountName}}</i>
                                        {{#if itemDiscountValue}}
                                            <i>({{itemDiscountValue}})</i>
                                        {{/if}}
                                    </p>
                                </td>
                            </tr>
                        {{/if}}

                        {{#if promotions}}
                            {{#each promotions}}
                                <tr class="purchased-table__row">
                                    <td class="purchased-table__item" colspan="5">
                                        <p class="purchased-table__item-text purchased-table__item-text-first">
                                            <i>*{{promotionName}}</i>
                                            {{#if discount}}
                                                <i>({{discount}}）</i>
                                            {{/if}}
                                        </p>
                                    </td>
                                </tr>
                            {{/each}}
                        {{/if}}
                        {{#if enableTakeaway}}
                            <tr class="purchased-table__row">
                                <td lass="purchased-table__item" colspan="3">
                                    <p
                                            class="purchased-table__item-text purchased-table__item-text-first purchased-table__item-text-last">
                                        {{takeawayTxt}}</p>

                                </td>
                            </tr>
                            <tr class="purchased-table__row">
                                <td lass="purchased-table__item" colspan="3">
                                    <p
                                            class="purchased-table__item-text purchased-table__item-text-first purchased-table__item-text-last">
                                        {{takeawayCharge}}</p>
                                </td>
                            </tr>
                        {{/if}}
                        {{#if notes}}
                            <tr class="purchased-table__row">
                                <td lass="purchased-table__item" colspan="4">
                                    <p
                                            class="purchased-table__item-text purchased-table__item-text-first purchased-table__item-text-last">
                                        {{notes}}</p>
                                </td>
                            </tr>
                        {{/if}}

                        <tr class="purchased-table__row1 ">
                            <td class="purchased-table__item-main text-top" width={{lookup ../columnWidths 0}}>
                                <p class="purchased-table__item-text purchased-table__item-text-first">
                                    <strong></strong>
                                </p>
                            </td>
                            <td class="purchased-table__item-main text-right text-top" width={{lookup ../columnWidths 1}}>
                                <p class="purchased-table__item-text">{{price}}</p>
                            </td>
                            <td class="purchased-table__item-main text-right text-top" width={{lookup ../columnWidths 2}}>
                                <p class="purchased-table__item-text">{{quantity}}&nbsp;&nbsp;</p>
                            <td class="purchased-table__item-main text-right text-top" width={{lookup ../columnWidths 3}}>
                                <p class="purchased-table__item-text">{{discount}}&nbsp;&nbsp;</p>
                            </td>
                            <td class="purchased-table__item-main text-right text-top" width={{lookup ../columnWidths 4}}>
                                <p class="purchased-table__item-text">{{total}}</p>
                            </td>
                        </tr>

                        {{#unless @last}}
                            <tr class="purchased-table__row">
                                <td class="purchased-table__item" colspan="4">
                                    <div class="spacer"></div>
                                </td>
                            </tr>
                        {{/unless}}

                    {{/each}}
                {{/if}}
            </table>
            <!-- End of Purchased List -->

            <!-- Billing -->
        {{else}}
            <!-- End of Billing -->

            <table class="purchased-table" width="100%">

                {{#if purchasedItems}}
                    {{#each purchasedItems}}
                        <tr class="purchased-table__row ">
                            <td class="purchased-table__item-main text-top" width={{lookup ../columnWidths 0}}>
                                <p class="purchased-table__item-text purchased-table__item-text-first">
                                    <strong>{{itemName}}</strong>
                                </p>
                            </td>
                            <td class="purchased-table__item-main text-center text-top" width={{lookup ../columnWidths 1}}>
                                <p class="purchased-table__item-text">{{price}}</p>
                            </td>
                            {{#if showPurchasedItemsDiscount}}
                                <td class="purchased-table__item-main text-center text-top" width={{lookup ../columnWidths 2}}>
                                    <p class="purchased-table__item-text">{{discount}}</p>
                                </td>
                            {{/if}}
                            <td class="purchased-table__item-main text-center text-top" width={{lookup ../columnWidths 2}}>
                                <p class="purchased-table__item-text">{{quantity}}</p>
                            </td>
                            <td class="purchased-table__item-main text-right text-top" width={{lookup ../columnWidths 3}}>
                                <p class="purchased-table__item-text">{{total}}</p>
                            </td>
                        </tr>
                        {{#if options}}
                            <tr class="purchased-table__row">
                                <td lass="purchased-table__item" colspan="4">
                                    <p
                                            class="purchased-table__item-text purchased-table__item-text-first purchased-table__item-text-last">
                                        {{options}}</p>
                                </td>
                            </tr>
                        {{/if}}

                        {{#if sn}}
                            <tr class="purchased-table__row">
                                <td lass="purchased-table__item" colspan="4">
                                    <p
                                            class="purchased-table__item-text purchased-table__item-text-first purchased-table__item-text-last">
                                        {{sn}}</p>
                                </td>
                            </tr>
                        {{/if}}

                        {{#if itemDiscountValue}}
                            <tr class="purchased-table__row">
                                <td class="purchased-table__item" colspan="3">
                                    <p class="purchased-table__item-text purchased-table__item-text-first">
                                        <i>{{itemDiscountName}}</i>
                                    </p>
                                </td>
                                {{#if itemDiscountValue}}
                                    <td class="purchased-table__item text-right" colspan="2">
                                        <p class="purchased-table__item-text purchased-table__item-text-last">
                                            <i>{{itemDiscountValue}}</i>
                                        </p>
                                    </td>
                                {{/if}}
                            </tr>
                        {{/if}}

                        {{#if promotions}}
                            {{#each promotions}}
                                <tr class="purchased-table__row">
                                    <td class="purchased-table__item" colspan="3">
                                        <p class="purchased-table__item-text purchased-table__item-text-first">
                                            <i>*{{promotionName}}</i>
                                        </p>
                                    </td>
                                    <td class="purchased-table__item text-right" colspan="2">
                                        <p class="purchased-table__item-text purchased-table__item-text-last">
                                            <i>{{discount}}</i>
                                        </p>
                                    </td>
                                </tr>
                            {{/each}}
                        {{/if}}
                        {{#if enableTakeaway}}
                            <tr class="purchased-table__row">
                                <td lass="purchased-table__item" colspan="3">
                                    <p
                                            class="purchased-table__item-text purchased-table__item-text-first purchased-table__item-text-last">
                                        {{takeawayTxt}}</p>

                                </td>
                            </tr>
                            <tr class="purchased-table__row">
                                <td lass="purchased-table__item" colspan="3">
                                    <p
                                            class="purchased-table__item-text purchased-table__item-text-first purchased-table__item-text-last">
                                        {{takeawayCharge}}</p>
                                </td>
                            </tr>
                        {{/if}}
                        {{#if notes}}
                            <tr class="purchased-table__row">
                                <td lass="purchased-table__item" colspan="4">
                                    <p
                                            class="purchased-table__item-text purchased-table__item-text-first purchased-table__item-text-last">
                                        {{notes}}</p>
                                </td>
                            </tr>
                        {{/if}}
                        {{#unless @last}}
                            <tr class="purchased-table__row">
                                <td class="purchased-table__item" colspan="4">
                                    <div class="spacer"></div>
                                </td>
                            </tr>
                        {{/unless}}
                    {{/each}}
                {{/if}}
            </table>
        {{/if}}

    </div>
    <!-- End of Bottom -->
</div>

</body>

</html>