def safeExtGet(prop, fallback) {
    rootProject.ext.has(prop) ? rootProject.ext.get(prop) : fallback
}

buildscript {
    repositories {
        mavenCentral()
        google()
        gradlePluginPortal()
    }

    dependencies {
        classpath("com.android.tools.build:gradle:7.3.1")
    }
}

apply plugin: 'com.android.library'

android {
    compileSdkVersion safeExtGet('compileSdkVersion', 33)
    //noinspection GradleDependency
    buildToolsVersion safeExtGet('buildToolsVersion', "33.0.0")

    defaultConfig {
        minSdkVersion safeExtGet('minSdkVersion', 21)
        //noinspection OldTargetApi
        targetSdkVersion safeExtGet('targetSdkVersion', 33)
        testInstrumentationRunner 'android.support.test.runner.AndroidJUnitRunner'
    }
    lintOptions {
        abortOnError false
    }
}

repositories {
    mavenCentral()
}
dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])

    testImplementation 'junit:junit:4.12'
    testImplementation "org.powermock:powermock-module-junit4:2.0.2"
    testImplementation "org.powermock:powermock-api-mockito2:2.0.2"
    androidTestImplementation 'com.android.support.test:runner:1.0.2'
    androidTestImplementation 'com.android.support.test.espresso:espresso-core:3.0.2'
    implementation 'com.facebook.react:react-native:+'
    implementation 'com.jakewharton.timber:timber:4.7.1'
    implementation 'com.google.code.gson:gson:2.8.5'
    implementation "org.java-websocket:Java-WebSocket:1.4.1"
}