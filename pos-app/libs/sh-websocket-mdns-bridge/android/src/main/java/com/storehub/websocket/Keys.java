package com.storehub.websocket;

public class Keys {
    public static final String KEY_BUSINESS = "business";
    public static final String KEY_REGISTER_ID = "registerId";
    public static final String KEY_REGISTER_NAME = "registerName";
    public static final String KEY_REGISTER_OBJECT_ID = "registerObjectId";
    public static final String KEY_STORE_NAME = "storeName";
    public static final String KEY_STORE_ID = "storeId";

    public static final String KEY_TIMEOUT = "timeout";
    public static final String KEY_IP = "ip";


    public static final String KEY_SOCKET_ID = "socketId";
    public static final String KEY_SOCKET_EVENT = "socketEvent";
    public static final String KEY_EVENT = "event";
    public static final String KEY_STATE = "state";
    public static final String KEY_EXTRAS = "extras";

    public static final String KEY_SOCKET_LOCAL_ADDRESS = "socketLocalAddress";
    public static final String KEY_SOCKET_LOCAL_PORT = "socketLocalPort";

    public static final String KEY_SOCKET_REMOTE_ADDRESS = "socketRemoteAddress";
    public static final String KEY_SOCKET_REMOTE_PORT = "socketRemotePort";

    public static final String KEY_STATUS = "status";
    public static final String KEY_STATUS_MSG = "statusMsg";
    public static final String KEY_MESSAGE = "message";
    public static final String KEY_CODE = "code";
    public static final String KEY_REASON = "reason";
    public static final String KEY_FROM_REMOTE = "fromRemote";
    public static final String KEY_ERROR = "error";

    // "serverAddress"
    public static final String KEY_REMOTE_ADDRESS = "remoteAddress";
    public static final String KEY_REMOTE_PORT = "remotePort";
    public static final String KEY_PAYLOAD = "payload";

    // "mdnsServiceInfo"
    public static final String KEY_SERVICE_NAME = "serviceName";
    public static final String KEY_SERVICE_ADDRESS = "serviceAddress";
    public static final String KEY_SERVICE_PORT = "servicePort";
    public static final String KEY_SERVICE = "service";


    public static final String HEADER_EXT_SOCKET_ID = "X-StoreHub-Socket-Id";
    public static final String HEADER_EXT_SOCKET_PAYLOAD = "X-StoreHub-Socket-Payload";


}
