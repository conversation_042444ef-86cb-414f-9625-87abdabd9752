package com.storehub.websocket;

import android.net.nsd.NsdManager;
import android.net.nsd.NsdServiceInfo;
import android.util.Log;
import com.facebook.react.bridge.*;
import com.storehub.websocket.mdns.MdnsEvent;
import com.storehub.websocket.mdns.NdsDiscoveryManager;

import javax.annotation.Nonnull;

import java.util.*;


public class MdnsDiscoveryModule extends ReactContextBaseJavaModule implements NsdManager.ResolveListener, LifecycleEventListener {

    private final NdsDiscoveryManager ndsDiscoveryManager;

    public static MdnsDiscoveryModule INSTANCE;

    private static final String NSD_ATTR_SINGLE_LENGTH_ERROR_MESSAGE = "Key length + value length must be < 255 bytes";
    private static final String NSD_ATTR_TOTAL_LENGTH_ERROR_MESSAGE = "Total length of attributes must be < 1300 bytes";

    private static final String NSD_LISTENER_NOT_REGISTERED_ERROR_MESSAGE = "listener not registered";
    private static final String NSD_LISTENER_ALREADY_IN_USE_ERROR_MESSAGE = "listener already in use";
    private static final String NSD_DISCOVERY_NOT_ACTIVE_ERROR_MESSAGE = "service discovery not active on listener";

    public void sendMdnsEvent(MdnsEvent event, Map<String, Object> data) {
        WritableMap params = Arguments.createMap();
        params.putString(Keys.KEY_EVENT, event.getEventName());
        for (Map.Entry<String, Object> entry : data.entrySet()) {
            Object value = entry.getValue();
            if (value instanceof String) {
                params.putString(entry.getKey(), (String) value);
            } else if (value instanceof ReadableMap) {
                params.putMap(entry.getKey(), (ReadableMap) value);
            }
        }
        ReactEvents.emitEventToDevice(getReactApplicationContext(), ReactEvents.MDNS_EVENT_NAME, params);
    }

    private Map<String, Object> resolveNdsServiceInfo(NsdServiceInfo nsdServiceInfo) {
        WritableMap extras = Arguments.createMap();
        int port = nsdServiceInfo.getPort();
        for (Map.Entry<String, byte[]> stringEntry : nsdServiceInfo.getAttributes().entrySet()) {
            extras.putString(stringEntry.getKey(), stringEntry.getValue() == null ? null : new String(stringEntry.getValue()));
            if ("wsServerPort".equals(stringEntry.getKey())) { // for ios
                try {
                    int wsServerPort = Integer.parseInt(new String(stringEntry.getValue()));
                    if (wsServerPort != 0) {
                        port = wsServerPort;
                    }
                } catch (NumberFormatException e) {
                    System.out.println("Failed to parse wsServerPort: " + e.getMessage());
                }
            }
        }
        return Map.of(
                Keys.KEY_SERVICE_NAME, nsdServiceInfo.getServiceName(),
                Keys.KEY_SERVICE_ADDRESS, nsdServiceInfo.getHost() == null ? "" : Objects.requireNonNullElse(nsdServiceInfo.getHost().getHostAddress(), ""),
                Keys.KEY_SERVICE_PORT, port,
                Keys.KEY_EXTRAS, extras
        );
    }

    public MdnsDiscoveryModule(@Nonnull ReactApplicationContext reactContext) {
        super(reactContext);
        this.ndsDiscoveryManager = new NdsDiscoveryManager(reactContext.getApplicationContext());
        INSTANCE = this;
        reactContext.addLifecycleEventListener(this);
    }

    protected static class DiscoveryException extends Exception {

        private final int code;

        DiscoveryException(int code, String reason) {
            super(reason);
            this.code = code;
        }

        DiscoveryException(int code, Throwable e) {
            super(e);
            this.code = code;
        }

        public int getCode() {
            return code;
        }

        public String getReason() {
            return getMessage();
        }
    }

    protected static class DiscoveryServiceRegisterException extends DiscoveryException {
        DiscoveryServiceRegisterException() {
            super(1001, "Discovery service register failed");
        }
    }

    protected static class DiscoveryServiceUnRegisterException extends DiscoveryException {
        DiscoveryServiceUnRegisterException() {
            super(1002, "Discovery service unregister failed");
        }
    }

    protected static class DiscoveryServiceAlreadyRegisteredException extends DiscoveryException {
        DiscoveryServiceAlreadyRegisteredException() {
            super(1003, "Discovery service is already registered");
        }
    }

    protected static class DiscoveryServiceNotRegisteredException extends DiscoveryException {
        DiscoveryServiceNotRegisteredException() {
            super(1004, "Discovery service is not registered");
        }
    }

    protected static class DiscoveryTaskStartException extends DiscoveryException {
        DiscoveryTaskStartException() {
            super(2001, "Discovery task start failed");
        }
    }

    protected static class DiscoveryTaskStopException extends DiscoveryException {
        DiscoveryTaskStopException() {
            super(2002, "Discovery task stop failed");
        }
    }

    protected static class DiscoveryTaskAlreadyStartedException extends DiscoveryException {
        DiscoveryTaskAlreadyStartedException() {
            super(2003, "Discovery task is already started");
        }
    }

    protected static class DiscoveryTaskNotStartedException extends DiscoveryException {
        DiscoveryTaskNotStartedException() {
            super(2004, "Discovery task is not started");
        }
    }

    protected static class DiscoveryUnknownException extends DiscoveryException {
        DiscoveryUnknownException(Throwable e) {
            super(1000, e);
        }
    }

    @Nonnull
    @Override
    public String getName() {
        return "MdnsDiscoveryModule";
    }


    @ReactMethod
    public void addListener(String eventName) {
        // Keep: Required for RN built in Event Emitter Calls.
    }
    @ReactMethod
    public void removeListeners(Integer count) {
        // Keep: Required for RN built in Event Emitter Calls.
    }

    @ReactMethod
    public void startService(String serviceName, int servicePort, ReadableMap extras, Promise promise) {
        NsdManager.RegistrationListener listener = new NsdManager.RegistrationListener() {
            @Override
            public void onServiceRegistered(NsdServiceInfo nsdServiceInfo) {
                promise.resolve(Arguments.makeNativeMap(resolveNdsServiceInfo(nsdServiceInfo)));
            }
            @Override
            public void onServiceUnregistered(NsdServiceInfo nsdServiceInfo) {
            }
            @Override
            public void onRegistrationFailed(NsdServiceInfo nsdServiceInfo, int i) {
                promise.reject(new DiscoveryServiceRegisterException());
            }
            @Override
            public void onUnregistrationFailed(NsdServiceInfo nsdServiceInfo, int i) {
            }
        };
        try {
            ndsDiscoveryManager.registerNsdService(serviceName, servicePort, extras.toHashMap(), listener);
        } catch (Exception e) {
            e.printStackTrace();
            rejectTypeCheck(promise, castException(e));
        }
    }

    @ReactMethod
    public void stopService(Promise promise) {
        NsdManager.RegistrationListener listener = new NsdManager.RegistrationListener() {
            @Override
            public void onServiceRegistered(NsdServiceInfo nsdServiceInfo) {
            }
            @Override
            public void onServiceUnregistered(NsdServiceInfo nsdServiceInfo) {
                promise.resolve(Arguments.makeNativeMap(resolveNdsServiceInfo(nsdServiceInfo)));
            }
            @Override
            public void onRegistrationFailed(NsdServiceInfo nsdServiceInfo, int i) {
            }
            @Override
            public void onUnregistrationFailed(NsdServiceInfo nsdServiceInfo, int i) {
                promise.reject(new DiscoveryServiceUnRegisterException());
            }
        };
        try {
            ndsDiscoveryManager.unregisterNsdService(listener);
        } catch (Exception e) {
            e.printStackTrace();
            rejectTypeCheck(promise, castException(e));
        }
    }

    @ReactMethod
    public void startServiceDiscovery(String serviceName, Promise promise) {
        NsdManager.DiscoveryListener listener = new NsdManager.DiscoveryListener(){
            @Override
            public void onStartDiscoveryFailed(String s, int i) {
                promise.reject(new DiscoveryTaskStartException());
            }
            @Override
            public void onStopDiscoveryFailed(String s, int i) {
            }
            @Override
            public void onDiscoveryStarted(String s) {
                promise.resolve(s);
            }
            @Override
            public void onDiscoveryStopped(String s) {
            }
            @Override
            public void onServiceFound(NsdServiceInfo nsdServiceInfo) {
                try {
                        ndsDiscoveryManager.resolveService(nsdServiceInfo, new NsdManager.ResolveListener() {
                            @Override
                            public void onResolveFailed(NsdServiceInfo nsdServiceInfo, int i) {
                            }

                            @Override
                            public void onServiceResolved(NsdServiceInfo nsdServiceInfo) {
                                sendMdnsEvent(MdnsEvent.SERVICE_FOUND, Map.of(
                                        Keys.KEY_SERVICE, Arguments.makeNativeMap(resolveNdsServiceInfo(nsdServiceInfo))
                                ));
                            }
                        });

                } catch (Exception e) {
                }
            }
            @Override
            public void onServiceLost(NsdServiceInfo nsdServiceInfo) {
                try {
                        ndsDiscoveryManager.resolveService(nsdServiceInfo, new NsdManager.ResolveListener() {
                            @Override
                            public void onResolveFailed(NsdServiceInfo nsdServiceInfo, int i) {
                            }
                            @Override
                            public void onServiceResolved(NsdServiceInfo nsdServiceInfo) {
                                sendMdnsEvent(MdnsEvent.SERVICE_LOST, Map.of(
                                        Keys.KEY_SERVICE, Arguments.makeNativeMap(resolveNdsServiceInfo(nsdServiceInfo))
                                ));
                            }
                        });
                } catch (Exception e) {
                }
            }
        };
        try {
            ndsDiscoveryManager.startNsdDiscovery(listener);
        } catch (Exception e) {
            e.printStackTrace();
            rejectTypeCheck(promise, castException(e));
        }
    }

    @ReactMethod
    public void stopServiceDiscovery(Promise promise) {
        NsdManager.DiscoveryListener listener = new NsdManager.DiscoveryListener(){
            @Override
            public void onStartDiscoveryFailed(String s, int i) {
            }
            @Override
            public void onStopDiscoveryFailed(String s, int i) {
                promise.reject(new DiscoveryTaskStopException());
            }
            @Override
            public void onDiscoveryStarted(String s) {
            }
            @Override
            public void onDiscoveryStopped(String s) {
                promise.resolve(s);
            }
            @Override
            public void onServiceFound(NsdServiceInfo nsdServiceInfo) {
            }
            @Override
            public void onServiceLost(NsdServiceInfo nsdServiceInfo) {
            }
        };
        try {
            ndsDiscoveryManager.stopNsdDiscovery(listener);
        } catch (Exception e) {
            e.printStackTrace();
            rejectTypeCheck(promise, castException(e));
        }
    }

    @ReactMethod
    public void searchService(String serviceName, int timeoutMillis, Promise promise) {
        ndsDiscoveryManager.search(serviceName, timeoutMillis, new NdsDiscoveryManager.DiscoveryCallback() {
            @Override
            public void onSuccess(Collection<NsdServiceInfo> result) {
                WritableArray arr = Arguments.createArray();
                for (NsdServiceInfo nsdServiceInfo : result) {
                    arr.pushMap(Arguments.makeNativeMap(resolveNdsServiceInfo(nsdServiceInfo)));
                }
                promise.resolve(arr);
            }
            @Override
            public void onFailure(Exception e) {
                rejectTypeCheck(promise, castException(e));
            }
        });
    }

    @Override
    public void onServiceResolved(NsdServiceInfo nsdServiceInfo) {

    }

    @Override
    public void onResolveFailed(NsdServiceInfo nsdServiceInfo, int i) {

    }

    public static DiscoveryException castException(Throwable e) {
        if (e instanceof DiscoveryException) {
            return (DiscoveryException) e;
        } else if (e instanceof IllegalArgumentException) {
            if (Objects.equals(e.getMessage(), NSD_LISTENER_NOT_REGISTERED_ERROR_MESSAGE)) {
                return new DiscoveryServiceNotRegisteredException();
            }
            if (Objects.equals(e.getMessage(), NSD_DISCOVERY_NOT_ACTIVE_ERROR_MESSAGE)) {
                return new DiscoveryTaskNotStartedException();
            }
            if (Objects.equals(e.getMessage(), NSD_LISTENER_ALREADY_IN_USE_ERROR_MESSAGE)) {
                for (StackTraceElement stackTraceElement : e.getStackTrace()) {
                    if (stackTraceElement.getClassName().equals("android.net.nsd.NsdManager")) {
                        if (stackTraceElement.getMethodName().equals("discoverServices")) {
                            return new DiscoveryTaskAlreadyStartedException();
                        }
                        if (stackTraceElement.getMethodName().equals("registerService")) {
                            return new DiscoveryServiceAlreadyRegisteredException();
                        }
                    }
                }
            }
        }
        return new DiscoveryUnknownException(e);
    }

    public static void rejectTypeCheck(Promise promise, Throwable e) {
        DiscoveryException socketException = e instanceof DiscoveryException ? (DiscoveryException) e : new DiscoveryUnknownException(e);
        promise.reject(String.valueOf(socketException.getCode()), socketException.getMessage(), socketException, Arguments.createMap());
    }

    @Override
    public void onHostPause() {

    }

    @Override
    public void onHostResume() {

    }


    @Override
    public void onHostDestroy() {
        this.stopService(new PromiseImpl(null, null));
    }
}
