package com.storehub.websocket;

import android.util.Log;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.modules.core.DeviceEventManagerModule;

public class ReactEvents {

    private ReactEvents() {}

    // addEventListener(websocketClientStateChanged)
    // {
    //    "socketId": "3ad134f6-25ef-11ee-be56-0242ac120002",
    //    "socketEvent": "ClientOpen",  // CLIENT_OPEN CLIENT_SERVER_MESSAGE CLIENT_CLOSE
    //    "param1": "content1",
    //    "param2": "content2",
    //    "param3": "content3",
    //    ...
    // }

    // top event name to pass to addEventListener
    public static final String WEBSOCKET_CLIENT_EVENT_NAME = "websocketClientStateChanged";
    public static final String WEBSOCKET_SERVER_EVENT_NAME = "websocketServerStateChanged";
    public static final String MDNS_EVENT_NAME = "mdnsStateChanged";

    public static synchronized void emitEventToDevice(ReactApplicationContext context, String eventName, Object data) {
        try {
            Log.d("WebSocketModule", String.format("emitEventToDevice: %s %s", eventName, data.toString()));
            context.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class).emit(eventName, data);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }



}
