package com.storehub.websocket;

public abstract class SocketCode {

    private SocketCode() {
    }

    public static final int UNKNOWN_ERROR_CODE = 3000;

    public static final int SOCKET_CLIENT_NEVER_CONNECT_CLOSE = 3001;
    public static final int SOCKET_CLIENT_BUGGY_CLOSE = 3002;
    public static final int SOCKET_CLIENT_FLUSH_CLOSE = 3003;

    public static final int SOCKET_URL_INVALID = 3099;

    public static final int SOCKET_DESTROYED = 3100;
    public static final int SOCKET_NOT_ESTABLISHED = 3101;
    public static final int SOCKET_NOT_READY = 3102; // double connect
    public static final int SOCKET_TIMEOUT = 3103; // timeout
    public static final int SOCKET_CLOSING = 3104;
    public static final int SOCKET_CLOSED = 3105;

    public static final int SERVER_NOT_STOP = 3200;
    public static final int SERVER_NOT_STARTED = 3201;

}
