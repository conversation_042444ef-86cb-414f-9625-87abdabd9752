package com.storehub.websocket;

import com.facebook.react.bridge.*;
import com.storehub.websocket.client.ClientEvent;
import com.storehub.websocket.client.WebSocketClientImpl;
import com.storehub.websocket.client.WebsocketClientEventsListener;
import org.java_websocket.WebSocket;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.drafts.Draft_6455;
import org.java_websocket.enums.ReadyState;
import timber.log.Timber;

import javax.annotation.Nonnull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class WebSocketClientModule extends WebSocketModule implements WebsocketClientEventsListener {

    public WebSocketClientModule(@Nonnull ReactApplicationContext reactContext) {
        super(reactContext);
    }

    private final Map<String, WebSocketClient> connections = Collections.synchronizedMap(new HashMap<>());

    private final ThreadPoolExecutor websocketConnectWaitThreadPool = new ThreadPoolExecutor(
        8, 8, 10_000, TimeUnit.MILLISECONDS,  new LinkedBlockingQueue<>(), Executors.defaultThreadFactory(), new ThreadPoolExecutor.DiscardOldestPolicy()
    );

    public void sendClientEvent(WebSocket socket, ClientEvent event, Map<String, String> data) {
        WritableMap params = Arguments.createMap();
        params.putString(Keys.KEY_SOCKET_ID, socket.getAttachment().toString());
        params.putString(Keys.KEY_SOCKET_EVENT, event.getEventName());
        for (Map.Entry<String, String> entry : data.entrySet()) {
            params.putString(entry.getKey(), entry.getValue());
        }
        emitEventToDevice(ReactEvents.WEBSOCKET_CLIENT_EVENT_NAME, params);
    }

    public WebSocketClient requireSocket(String id) throws SocketDestroyedException {
        WebSocketClient socket = connections.get(id);
        if (socket == null) {
            throw new SocketDestroyedException(id);
        }
        return socket;
    }

    public void ensureSocket(String socketId) throws SocketDestroyedException, SocketClosingException, SocketClosedException, SocketNotReadyException {
        WebSocketClient socket = requireSocket(socketId);
        if (socket.isClosing()) {
            throw new SocketClosingException(socketId);
        }
        if (socket.isClosed()) {
            throw new SocketClosedException(socketId);
        }
        if (!socket.isOpen()) {
            throw new SocketNotReadyException(socketId);
        }
    }

    @ReactMethod
    public void addListener(String eventName) {
        // Keep: Required for RN built in Event Emitter Calls.
    }
    @ReactMethod
    public void removeListeners(Integer count) {
        // Keep: Required for RN built in Event Emitter Calls.
    }

    @ReactMethod
    public void create(ReadableMap readableMap, Promise promise) throws URISyntaxException {
        URI uri;
        try {
            uri = new URI(String.format("ws://%s:%s", readableMap.getString(Keys.KEY_REMOTE_ADDRESS), readableMap.getString(Keys.KEY_REMOTE_PORT)));
        } catch (URISyntaxException e) {
            rejectTypeCheck(promise, new SocketUrlInvalidException(null));
            return;
        }
        WebSocketClientImpl client = new WebSocketClientImpl(
                uri,
//                new Draft_6455(Arrays.asList(new PerMessageDeflateExtension(), new PlainEncryptionExtension())),
                new Draft_6455(),
                readableMap.getString(Keys.KEY_PAYLOAD),
                WebSocketClientImpl.CONNECT_TIMEOUT,
                this
        );
        connections.put(client.getAttachment().toString(), client);
        promise.resolve(client.getAttachment().toString());
    }

    @ReactMethod
    public void connect(String socketId, Promise promise) {
        Timber.d("connect() called with: ");
        try {
            websocketConnectWaitThreadPool.execute(() -> {
                try {
                    WebSocketClient client = requireSocket(socketId);
                    if (!client.connectBlocking()) {
                        throw new SocketNotEstablishedException(socketId);
                    }
                    promise.resolve(null);
                } catch (InterruptedException e) {
                    rejectTypeCheck(promise, new SocketTimeoutException(socketId));
                } catch (IllegalStateException e) {
                    rejectTypeCheck(promise, new SocketNotReadyException(socketId));
                } catch (Exception e) {
                    rejectTypeCheck(promise, castException(socketId, e));
                }
            });
        } catch (Exception e) {
            rejectTypeCheck(promise, castException(socketId, e));
        }

    }

    @ReactMethod
    public void send(String socketId, String message, Promise promise) {
        Timber.d("send() called with: socketId = [" + socketId + "], message = [" + message + "]");
        try {
            WebSocketClient client = requireSocket(socketId);
            ensureSocket(socketId);
            client.send(message);
            promise.resolve(null);
        } catch (Exception e) {
            rejectTypeCheck(promise, castException(socketId, e));
        }
    }

    @ReactMethod
    public void close(String socketId, Promise promise) {
        Timber.d("closeWebSocketClient() called with: ");
        try {
            websocketConnectWaitThreadPool.execute(() -> {
                try {
                    WebSocketClient client = requireSocket(socketId);
                    client.closeBlocking();
                    connections.remove(socketId);
                    promise.resolve(null);
                } catch (Exception e) {
                    rejectTypeCheck(promise, castException(socketId, e));
                }
            });
        } catch (Exception e) {
            rejectTypeCheck(promise, castException(socketId, e));
        }
    }

    @ReactMethod
    public void exitApp() {
        for (Map.Entry<String, WebSocketClient> entry : connections.entrySet()) {
            WebSocketClient value = entry.getValue();
            value.close();
        }
        connections.clear();
    }


    @ReactMethod
    public void getSocketState(String socketId, Promise promise) {
        SocketState socketState = SocketState.CLOSED;
        WebSocketClient client = connections.get(socketId);
        if (client == null) {
            socketState = SocketState.DESTROYED;
        } else {
            ReadyState readyState = client.getReadyState();
            if (readyState == ReadyState.NOT_YET_CONNECTED) {
                socketState = SocketState.NOT_CONNECTED;
            } else if (readyState == ReadyState.OPEN) {
                socketState = SocketState.OPEN;
            } else if (readyState == ReadyState.CLOSING) {
                socketState = SocketState.CLOSING;
            } else if (readyState == ReadyState.CLOSED) {
                socketState = SocketState.CLOSED;
            } else {
                socketState = SocketState.DESTROYED;
            }
        }
        promise.resolve(socketState.getValue());
    }

    @ReactMethod
    public void getRemoteHost(String socketId, Promise promise) {
        try {
            WebSocketClient client = requireSocket(socketId);
            promise.resolve(client.getRemoteSocketAddress().getAddress().getHostAddress());
        } catch (Exception e) {
            rejectTypeCheck(promise, castException(socketId, e));
        }
    }

    @ReactMethod
    public void getRemotePort(String socketId, Promise promise) {
        try {
            WebSocketClient client = requireSocket(socketId);
            promise.resolve(client.getRemoteSocketAddress().getPort());
        } catch (Exception e) {
            rejectTypeCheck(promise, castException(socketId, e));
        }
    }

    @ReactMethod
    public void getLocalHost(String socketId, Promise promise) {
        try {
            WebSocketClient client = requireSocket(socketId);
            promise.resolve(client.getLocalSocketAddress().getAddress().getHostAddress());
        } catch (Exception e) {
            rejectTypeCheck(promise, castException(socketId, e));
        }
    }

    @ReactMethod
    public void getLocalPort(String socketId, Promise promise) {
        try {
            WebSocketClient client = requireSocket(socketId);
            promise.resolve(client.getLocalSocketAddress().getPort());
        } catch (Exception e) {
            rejectTypeCheck(promise, castException(socketId, e));
        }
    }

    @Nonnull
    @Override
    public String getName() {
        return "WebSocketClientModule";
    }

    @Override
    public void onConnect(WebSocketClient client, WebSocket socket) {
        if (connections.containsKey(socket.getAttachment().toString())) {
            sendClientEvent(client, ClientEvent.CLIENT_STATUS_CONNECTING, new HashMap<>());
        }
    }
    @Override
    public void onOpen(WebSocketClient client, WebSocket socket, short status, String statusMsg) {
        if (connections.containsKey(socket.getAttachment().toString())) {
            sendClientEvent(socket, ClientEvent.CLIENT_STATUS_CONNECTED, Map.of(
                    Keys.KEY_STATUS, String.valueOf(status),
                    Keys.KEY_STATUS_MSG, statusMsg
            ));
        }
    }

    @Override
    public void onMessage(WebSocketClient client, WebSocket socket, String message) {
        if (connections.containsKey(socket.getAttachment().toString())) {
            sendClientEvent(socket, ClientEvent.RECEIVE_MESSAGE, Map.of(
                    Keys.KEY_MESSAGE, message
            ));
        }
    }

    @Override
    public void onClosing(WebSocketClient client, WebSocket socket, int code, String reason, boolean remote) {
        if (connections.containsKey(socket.getAttachment().toString())) {
            sendClientEvent(socket, ClientEvent.CLIENT_STATUS_CLOSING, Map.of(
                    Keys.KEY_CODE, String.valueOf(remapCode(code)),
                    Keys.KEY_REASON, reason,
                    Keys.KEY_FROM_REMOTE, String.valueOf(remote)
            ));
        }
    }

    @Override
    public void onClose(WebSocketClient client, WebSocket socket, int code, String reason, boolean remote) {
        if (connections.containsKey(socket.getAttachment().toString())) {
            sendClientEvent(socket, ClientEvent.CLIENT_STATUS_CLOSED, Map.of(
                    Keys.KEY_CODE, String.valueOf(remapCode(code)),
                    Keys.KEY_REASON, reason,
                    Keys.KEY_FROM_REMOTE, String.valueOf(remote)
            ));
        }
    }

    @Override
    public void onError(WebSocketClient client, WebSocket socket, Exception ex) {
        if (connections.containsKey(socket.getAttachment().toString())) {
            sendClientEvent(socket, ClientEvent.CLIENT_STATUS_CLOSED, Map.of(
                    Keys.KEY_CODE, getErrorCode(ex), // internal code
                    Keys.KEY_REASON, ex.getMessage(),
                    Keys.KEY_FROM_REMOTE, String.valueOf(false)));
        }
    }

    @Override
    public void onPing(WebSocketClient client, WebSocket socket) {
        if (connections.containsKey(socket.getAttachment().toString())) {
            sendClientEvent(socket, ClientEvent.RECEIVED_PING, Map.of());
        }
    }

    @Override
    public void onPong(WebSocketClient client, WebSocket socket) {
        if (connections.containsKey(socket.getAttachment().toString())) {
            sendClientEvent(socket, ClientEvent.RECEIVED_PONG, Map.of());
        }
    }


}

