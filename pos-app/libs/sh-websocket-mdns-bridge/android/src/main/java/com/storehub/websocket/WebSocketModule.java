package com.storehub.websocket;

import com.facebook.react.bridge.*;
import org.java_websocket.framing.CloseFrame;

import javax.annotation.Nonnull;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.storehub.websocket.SocketCode.*;

public abstract class WebSocketModule extends ReactContextBaseJavaModule {

    protected static ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(10, 10, 30000, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());

    protected WebSocketModule(@Nonnull ReactApplicationContext reactContext) {
        super(reactContext);
    }

    protected static class SocketException extends Exception {
        private final String socketId;
        private final int code;

        SocketException(int code, Throwable throwable) {
            this("server-uuid", code, throwable);
        }

        SocketException(int code, String reason) {
            this("server-uuid", code, reason);
        }

        SocketException(String socketId, int code, Throwable throwable) {
            super(throwable);
            this.code = code;
            this.socketId = socketId;
        }

        SocketException(String socketId, int code, String reason) {
            super(reason);
            this.code = code;
            this.socketId = socketId;
        }

        public String getSocketId() {
            return socketId;
        }

        public int getCode() {
            return code;
        }

        public String getReason() {
            return getMessage();
        }
    }

    protected static class SocketUnknownException extends SocketException {

        SocketUnknownException(Throwable e) {
            super(UNKNOWN_ERROR_CODE, e);
        }

        SocketUnknownException(String socketId, Throwable e) {
            super(socketId, UNKNOWN_ERROR_CODE, e);
        }
    }

    static class SocketUrlInvalidException extends SocketException {
        SocketUrlInvalidException(String socketId) {
            super(socketId, SOCKET_URL_INVALID, "Socket url is invalid");
        }
    }

    static class SocketDestroyedException extends SocketException {
        SocketDestroyedException(String socketId) {
            super(socketId, SOCKET_DESTROYED, "Socket has been destroyed");
        }
    }

    static class SocketNotEstablishedException extends SocketException {
        SocketNotEstablishedException(String socketId) {
            super(socketId, SOCKET_NOT_ESTABLISHED, "Socket is not established");
        }
    }

    static class SocketNotReadyException extends SocketException {
        SocketNotReadyException(String socketId) {
            super(socketId, SOCKET_NOT_READY, "Socket is not ready");
        }
    }

    static class SocketTimeoutException extends SocketException {
        SocketTimeoutException(String socketId) {
            super(socketId, SOCKET_TIMEOUT, "Socket is timeout");
        }
    }

    static class SocketClosingException extends SocketException {
        SocketClosingException(String socketId) {
            super(socketId, SOCKET_CLOSING, "Socket is closing");
        }
    }

    static class SocketClosedException extends SocketException {
        SocketClosedException(String socketId) {
            super(socketId, SOCKET_CLOSED, "Socket is closed");
        }
    }

    protected enum SocketState {
        NOT_CONNECTED("NotConnected"),
        OPEN("Connected"),
        CLOSING("Closing"),
        CLOSED("Closed"),
        DESTROYED("Destroyed");

        private String value;

        SocketState(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    public void emitEventToDevice(String eventName, Object data) {
        ReactEvents.emitEventToDevice(getReactApplicationContext(), eventName, data);
    }

    public static void rejectTypeCheck(Promise promise, Throwable e) {
        if (e instanceof SocketException) {
            SocketException socketException = (SocketException) e;
            WritableMap userInfo = Arguments.createMap();
            userInfo.putString(Keys.KEY_SOCKET_ID, socketException.getSocketId());
            promise.reject(String.valueOf(socketException.getCode()), socketException.getMessage(), socketException, userInfo);
        } else {
            promise.reject(String.valueOf(UNKNOWN_ERROR_CODE), e.getMessage(), e);
        }
    }

    public static SocketException castException(Throwable e) {
        if (e instanceof SocketException) {
            return (SocketException) e;
        } else {
            return new SocketUnknownException(e);
        }
    }

    public static SocketException castException(String socketId, Throwable e) {
        if (e instanceof SocketException) {
            return (SocketException) e;
        } else {
            return new SocketUnknownException(socketId, e);
        }
    }

    public static String getErrorCode(Throwable e) {
        if (e instanceof SocketException) {
            return String.valueOf(((SocketException) e).getCode());
        } else {
            return String.valueOf(UNKNOWN_ERROR_CODE);
        }
    }

    public static int remapCode(int code) {
        switch (code) {
            case CloseFrame.NEVER_CONNECTED:
                return SOCKET_CLIENT_NEVER_CONNECT_CLOSE;
            case CloseFrame.BUGGYCLOSE:
                return SOCKET_CLIENT_BUGGY_CLOSE;
            case CloseFrame.FLASHPOLICY:
                return SOCKET_CLIENT_FLUSH_CLOSE;
            default:
                return code;

        }
    }
}
