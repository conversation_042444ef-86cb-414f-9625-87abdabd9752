package com.storehub.websocket;

import com.facebook.react.bridge.*;
import com.storehub.websocket.server.ServerEvent;
import com.storehub.websocket.server.WebSocketServerImpl;
import com.storehub.websocket.server.WebsocketServerEventsListener;
import com.storehub.websocket.utils.PerMessageDeflateExtension;
import com.storehub.websocket.utils.PlainEncryptionExtension;
import org.java_websocket.WebSocket;
import org.java_websocket.drafts.Draft_6455;
import org.java_websocket.enums.ReadyState;
import org.java_websocket.handshake.ClientHandshake;
import org.java_websocket.server.WebSocketServer;
import org.jetbrains.annotations.NotNull;

import javax.annotation.Nonnull;
import java.util.*;

import static com.storehub.websocket.SocketCode.SERVER_NOT_STARTED;
import static com.storehub.websocket.SocketCode.SERVER_NOT_STOP;

public class WebSocketServerModule extends WebSocketModule implements WebsocketServerEventsListener {

    public WebSocketServerModule(@Nonnull ReactApplicationContext reactContext) {
        super(reactContext);
    }

    public void sendClientEvent(WebSocket socket, ServerEvent event, Map<String, String> data) {
        WritableMap params = Arguments.createMap();
        params.putString(Keys.KEY_SOCKET_EVENT, event.getEventName());
        params.putString(Keys.KEY_SOCKET_ID, socket.getAttachment().toString());
        for (Map.Entry<String, String> entry : data.entrySet()) {
            params.putString(entry.getKey(), entry.getValue());
        }
        emitEventToDevice(ReactEvents.WEBSOCKET_SERVER_EVENT_NAME, params);
    }

    public void sendServerEvent(WebSocketServer server, ServerEvent event, Map<String, String> data) {
        WritableMap params = Arguments.createMap();
        params.putString(Keys.KEY_SOCKET_EVENT, event.getEventName());
        params.putString(Keys.KEY_SOCKET_ID, null);
        params.putString(Keys.KEY_SOCKET_LOCAL_ADDRESS, server.getAddress().getHostName());
        params.putString(Keys.KEY_SOCKET_LOCAL_PORT, String.valueOf(server.getPort()));
        for (Map.Entry<String, String> entry : data.entrySet()) {
            params.putString(entry.getKey(), entry.getValue());
        }
        emitEventToDevice(ReactEvents.WEBSOCKET_SERVER_EVENT_NAME, params);

        //
    }

    @NotNull
    private HashMap<String, String> getHeadersFromRN(ReadableMap readableMap) {
        HashMap<String, String> headers = new HashMap<>();
        if (readableMap.hasKey(Keys.KEY_BUSINESS)) {
            headers.put(Keys.KEY_BUSINESS, readableMap.getString(Keys.KEY_BUSINESS));
        }
        if (readableMap.hasKey(Keys.KEY_REGISTER_ID)) {
            headers.put(Keys.KEY_REGISTER_ID, readableMap.getString(Keys.KEY_REGISTER_ID));
        }
        if (readableMap.hasKey(Keys.KEY_REGISTER_NAME)) {
            headers.put(Keys.KEY_REGISTER_NAME, readableMap.getString(Keys.KEY_REGISTER_NAME));
        }
        if (readableMap.hasKey(Keys.KEY_REGISTER_OBJECT_ID)) {
            headers.put(Keys.KEY_REGISTER_OBJECT_ID, readableMap.getString(Keys.KEY_REGISTER_OBJECT_ID));
        }
        if (readableMap.hasKey(Keys.KEY_STORE_NAME)) {
            headers.put(Keys.KEY_STORE_NAME, readableMap.getString(Keys.KEY_STORE_NAME));
        }
        if (readableMap.hasKey(Keys.KEY_STORE_ID)) {
            headers.put(Keys.KEY_STORE_ID, readableMap.getString(Keys.KEY_STORE_ID));
        }
        if (readableMap.hasKey("ip")) {
            headers.put(Keys.KEY_IP, readableMap.getString(Keys.KEY_IP));
        }
        return headers;
    }

    static class ServerNotStopException extends SocketException {

        public ServerNotStopException() {
            super(SERVER_NOT_STOP, "Server is not stopped");
        }
    }

    static class ServerNotStartedException extends SocketException {

        public ServerNotStartedException() {
            super(SERVER_NOT_STARTED, "Server is not started");
        }
    }

    private void ensureServer() throws ServerNotStartedException {
        if (server == null) {
            throw new ServerNotStartedException();
        }
    }

    private volatile WebSocketServer server;

    @ReactMethod
    public void addListener(String eventName) {
        // Keep: Required for RN built in Event Emitter Calls.
    }
    @ReactMethod
    public void removeListeners(Integer count) {
        // Keep: Required for RN built in Event Emitter Calls.
    }

    @ReactMethod
    public void startServer(int port, Promise promise) {
        try {
            WebSocketServer currentServer = server;
            if (currentServer == null) {
//                currentServer = new WebSocketServerImpl("0.0.0.0", 0, Arrays.asList(new Draft_6455(Arrays.asList(new PerMessageDeflateExtension(), new PlainEncryptionExtension()))), this);
                currentServer = new WebSocketServerImpl("0.0.0.0", port, Arrays.asList(new Draft_6455()), this);
                server = currentServer;
            }
            try {
                currentServer.start();
            } catch (IllegalStateException ignored) {
                // suppressed
            }
            promise.resolve(null);
        } catch (Exception e) {
            rejectTypeCheck(promise, castException(e));
        }
    }


    @ReactMethod
    public void stopServer(Promise promise) {
        try {
            ensureServer();
            WebSocketServer currentServer = server;
            try {
                currentServer.stop();
            } catch (InterruptedException ignored) {
                // FIXME: 24/7/23 double confirm
            }
            promise.resolve(null);
            sendServerEvent(currentServer, ServerEvent.SERVER_CLOSE, new HashMap<>());
        } catch (Exception e) {
            rejectTypeCheck(promise, castException(e));
        } finally {
            server = null;
        }
    }

    @ReactMethod
    public void send(String socketId, String message, Promise promise) {
        try {
            ensureServer();
            for (WebSocket connection : server.getConnections()) {
                if (connection.getAttachment().toString().equals(socketId)) {
                    connection.send(message);
                    break;
                }
            }
            promise.resolve(null);
        } catch (Exception e) {
            rejectTypeCheck(promise, castException(e));
        }
    }

    @ReactMethod
    public void broadcast(String message, Promise promise) {
        try {
            ensureServer();
            for (WebSocket connection : server.getConnections()) {
                connection.send(message);
            }
            promise.resolve(null);
        } catch (Exception e) {
            rejectTypeCheck(promise, castException(e));
        }
    }

    @ReactMethod
    public void close(String socketId, Promise promise) {
        try {
            ensureServer();
            for (WebSocket connection : server.getConnections()) {
                if (connection.getAttachment().toString().equals(socketId)) {
                    connection.close();
                    break;
                }
            }
            promise.resolve(null);
        } catch (Exception e) {
            rejectTypeCheck(promise, castException(e));
        }
    }

    @ReactMethod
    public void exitApp(Promise promise) {
        stopServer(promise);
    }


    @ReactMethod
    public void getSocketState(String socketId, Promise promise) {
        try {
            ensureServer();
            for (WebSocket connection : server.getConnections()) {
                if (connection.getAttachment().toString().equals(socketId)) {
                    SocketState socketState = SocketState.CLOSED;
                    ReadyState readyState = connection.getReadyState();
                    if (readyState == ReadyState.NOT_YET_CONNECTED) {
                        socketState = SocketState.NOT_CONNECTED;
                    } else if (readyState == ReadyState.OPEN) {
                        socketState = SocketState.OPEN;
                    } else if (readyState == ReadyState.CLOSING) {
                        socketState = SocketState.CLOSING;
                    } else if (readyState == ReadyState.CLOSED) {
                        socketState = SocketState.CLOSED;
                    } else {
                        socketState = SocketState.DESTROYED;
                    }
                    promise.resolve(socketState.getValue());
                    return;
                }
            }
            promise.resolve(SocketState.DESTROYED.getValue());
        } catch (Exception e) {
            rejectTypeCheck(promise, castException(e));
        }
    }

    @ReactMethod
    public void getRemoteHost(String socketId, Promise promise) {
        try {
            ensureServer();
            for (WebSocket connection : server.getConnections()) {
                if (connection.getAttachment().toString().equals(socketId)) {
                    promise.resolve(connection.getRemoteSocketAddress().getAddress().getHostAddress());
                    return;
                }
            }
            throw new SocketDestroyedException(socketId);
        } catch (Exception e) {
            rejectTypeCheck(promise, castException(e));
        }
    }

    @ReactMethod
    public void getRemotePort(String socketId, Promise promise) {
        try {
            ensureServer();
            for (WebSocket connection : server.getConnections()) {
                if (connection.getAttachment().toString().equals(socketId)) {
                    promise.resolve(connection.getRemoteSocketAddress().getPort());
                    return;
                }
            }
            throw new SocketDestroyedException(socketId);
        } catch (Exception e) {
            rejectTypeCheck(promise, castException(e));
        }
    }

    @ReactMethod
    public void getLocalHost(Promise promise) {
        try {
            ensureServer();
            promise.resolve(server.getAddress().getHostString());
        } catch (Exception e) {
            rejectTypeCheck(promise, castException(e));
        }
    }

    @ReactMethod
    public void getLocalPort(Promise promise) {
        try {
            ensureServer();
            promise.resolve(String.valueOf(server.getPort()));
        } catch (Exception e) {
            rejectTypeCheck(promise, castException(e));
        }
    }

    @Nonnull
    @Override
    public String getName() {
        return "WebSocketServerModule";
    }


    @Override
    public void onStart(WebSocketServer server) {
        if (server == this.server) {
            sendServerEvent(server, ServerEvent.SERVER_START, new HashMap<>());
        }
    }

    @Override
    public void onOpen(WebSocketServer server, WebSocket socket, ClientHandshake handshake) {
        if (server == this.server) {
            sendClientEvent(socket, ServerEvent.CLIENT_STATUS_CONNECTED, Map.of(
                    Keys.KEY_REMOTE_ADDRESS, socket.getRemoteSocketAddress().getAddress().getHostAddress(),
                    Keys.KEY_REMOTE_PORT, String.valueOf(socket.getRemoteSocketAddress().getPort()),
                    Keys.KEY_PAYLOAD, handshake.getFieldValue(Keys.HEADER_EXT_SOCKET_PAYLOAD)
            ));
        }

    }

    @Override
    public void onMessage(WebSocketServer server, WebSocket socket, String message) {
        if (server == this.server) {
            sendClientEvent(socket, ServerEvent.CLIENT_MESSAGE, Map.of(
                    Keys.KEY_MESSAGE, message
            ));
        }

    }

    @Override
    public void onClosing(WebSocketServer server, WebSocket socket, int code, String reason, boolean remote) {
        if (server == this.server) {
            sendClientEvent(socket, ServerEvent.CLIENT_STATUS_CLOSING, Map.of(
                    Keys.KEY_CODE, String.valueOf(remapCode(code)),
                    Keys.KEY_REASON, reason,
                    Keys.KEY_FROM_REMOTE, String.valueOf(remote)
            ));
        }

    }

    @Override
    public void onClose(WebSocketServer server, WebSocket socket, int code, String reason, boolean remote) {
        if (server == this.server) {
            sendClientEvent(socket, ServerEvent.CLIENT_STATUS_CLOSE, Map.of(
                    Keys.KEY_CODE, String.valueOf(remapCode(code)),
                    Keys.KEY_REASON, reason,
                    Keys.KEY_FROM_REMOTE, String.valueOf(remote)
            ));
        }
    }

    @Override
    public void onError(WebSocketServer server, WebSocket socket, Exception ex) {
        if (server == this.server) {
            if (socket != null) {
                sendClientEvent(socket, ServerEvent.CLIENT_STATUS_CLOSE, Map.of(
                        Keys.KEY_CODE, getErrorCode(ex), // internal code
                        Keys.KEY_REASON, ex.getMessage(),
                        Keys.KEY_FROM_REMOTE, String.valueOf(false)));
            } else {
                sendServerEvent(server, ServerEvent.CLIENT_STATUS_CLOSE, Map.of(
                        Keys.KEY_CODE, getErrorCode(ex), // internal code
                        Keys.KEY_REASON, ex.getMessage(),
                        Keys.KEY_FROM_REMOTE, String.valueOf(true)
                ));
            }
        }
    }

    @Override
    public void onPing(WebSocketServer server, WebSocket socket) {
        if (server == this.server) {
            sendClientEvent(socket, ServerEvent.RECEIVE_PING, new HashMap<>());
        }
    }

    @Override
    public void onPong(WebSocketServer server, WebSocket socket) {
        if (server == this.server) {
            sendClientEvent(socket, ServerEvent.RECEIVE_PONG, new HashMap<>());
        }
    }

}
