package com.storehub.websocket.client;

public enum ClientEvent {

    // addEventListener(websocketClientStateChanged)
    // {
    //    "socketId": "3ad134f6-25ef-11ee-be56-0242ac120002",
    //    "socketEvent": "ClientOpen",  // CLIENT_OPEN CLIENT_SERVER_MESSAGE CLIENT_CLOSE
    //    "status": "connected"
    //    "param1": "content1",
    //    "param2": "content2",
    //    "param3": "content3",
    //    ...
    // },


    // setState to Connecting/Connected
    CLIENT_STATUS_CONNECTING("ClientStatusConnecting"),
    CLIENT_STATUS_CONNECTED("ClientStatusConnected"),

    // setState to Closing/Closed/Destroyed
    CLIENT_STATUS_CLOSING("ClientStatusClosing"),
    CLIENT_STATUS_CLOSED("ClientStatusClosed"),

    RECEIVE_MESSAGE("ReceiveMessage"),
    RECEIVED_PING("ReceivePing"),
    RECEIVED_PONG("ReceivePong");

    private final String eventName;

    ClientEvent(String eventName) {
        this.eventName = eventName;
    }

    public String getEventName() {
        return eventName;
    }

}
