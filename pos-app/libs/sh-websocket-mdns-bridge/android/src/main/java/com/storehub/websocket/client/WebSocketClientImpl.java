package com.storehub.websocket.client;

import com.storehub.websocket.Keys;
import org.java_websocket.WebSocket;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.drafts.Draft;
import org.java_websocket.drafts.Draft_6455;
import org.java_websocket.framing.Framedata;
import org.java_websocket.handshake.ServerHandshake;

import java.net.URI;
import java.util.Collections;
import java.util.UUID;

import timber.log.Timber;

public class WebSocketClientImpl extends WebSocketClient {

    public static final int CONNECT_TIMEOUT = 5 * 1000; //ms
    public static final int CONNECTION_LOST_TIMEOUT = 10; //s

    private final WebsocketClientEventsListener listener;

    public WebSocketClientImpl(URI serverUri, Draft protocolDraft, String payload, int connectTimeout, WebsocketClientEventsListener listener) {
        super(serverUri, protocolDraft, Collections.emptyMap(), connectTimeout);
        this.listener = listener;

        String socketId = UUID.randomUUID().toString();

        addHeader(Keys.HEADER_EXT_SOCKET_ID, socketId);
        addHeader(Keys.HEADER_EXT_SOCKET_PAYLOAD, payload);
        setAttachment(socketId);
        setReuseAddr(true);
        setConnectionLostTimeout(CONNECTION_LOST_TIMEOUT);
    }

    @Override
    public void connect() {
        listener.onConnect(this, this);
        try {
            super.connect();
        } catch (Exception e) {
            onError(e);
            throw e;
        }
    }

    @Override
    public void onOpen(ServerHandshake handshakedata) {
        Timber.d("onOpen() called with: " + "handshakedata = [" + handshakedata + "]");
        listener.onOpen(this, this, handshakedata.getHttpStatus(), handshakedata.getHttpStatusMessage());
    }

    @Override
    public void onMessage(String message) {
        Timber.d("onMessage() called with: " + "message = [" + message + "]");
        listener.onMessage(this, this, message);
    }

    @Override
    public void onClosing(int code, String reason, boolean remote) {
        listener.onClosing(this, this, code, reason, remote);
    }

    @Override
    public void onClose(int code, String reason, boolean remote) {
        Timber.d("onClose() called with: " + "code = [" + code + "], reason = [" + reason + "], remote = [" + remote + "]");
        listener.onClose(this, this, code, reason, remote);
    }

    @Override
    public void onError(Exception ex) {
        Timber.d("onError() called with: " + "ex = [" + ex + "]");
        listener.onError(this, this, ex);
    }

    @Override
    public void onWebsocketPing(WebSocket conn, Framedata f) {
        super.onWebsocketPing(conn, f);
        listener.onPing(this, this);
    }

    @Override
    public void onWebsocketPong(WebSocket conn, Framedata f) {
        super.onWebsocketPong(conn, f);
        listener.onPong(this, this);
    }
}
