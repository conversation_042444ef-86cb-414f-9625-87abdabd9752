package com.storehub.websocket.client;

import org.java_websocket.WebSocket;
import org.java_websocket.client.WebSocketClient;

public interface WebsocketClientEventsListener {

    void onConnect(WebSocketClient client, WebSocket socket);

    void onOpen(WebSocketClient client, WebSocket socket, short status, String statusMsg);

    void onMessage(WebSocketClient client, WebSocket socket, String message);

    void onClosing(WebSocketClient client, WebSocket socket, int code, String reason, boolean remote);

    void onClose(WebSocketClient client, WebSocket socket, int code, String reason, boolean remote);

    void onError(WebSocketClient client, WebSocket socket, Exception ex);

    void onPing(WebSocketClient client, WebSocket socket);

    void onPong(WebSocketClient client, WebSocket socket);
}
