package com.storehub.websocket.mdns;

public enum MdnsEvent {

    // addEventListener(mdnsStateChanged)

    // {
    //    "event": "ServiceStart",
    //    "status": "ServiceStart",
    //    "param1": "content1",
    //    "param2": "content2",
    //    ...
    // }

    // server status change

//    SERVICE_STATE_CHANGE("ServiceStateChange"),
//    DISCOVERY_STATE_CHANGE("DiscoveryStateChange"),

    SERVICE_FOUND("ServiceFound"),
    SERVICE_LOST("ServiceLost");

    public static class Service {
        private Service() {}
        public static final String SERVICE_STARTING = "ServiceStarting";
        public static final String SERVICE_STARTED = "ServiceStarted";
        public static final String SERVICE_STOPPING = "ServiceStopping";
        public static final String SERVICE_STOPPED = "ServiceStopped";
        public static final String SERVICE_START_FAILED = "ServiceStartFailed";
        public static final String SERVICE_STOP_FAILED = "ServiceStopFailed";
    }


    public static class Discovery {
        private Discovery() {}
        public static final String DISCOVERY_STARTING = "DiscoveryStarting";
        public static final String DISCOVERY_STARTED = "DiscoveryStarted";
        public static final String DISCOVERY_STOPPING = "DiscoveryStopping";
        public static final String DISCOVERY_STOPPED = "DiscoveryStopped";
        public static final String DISCOVERY_START_FAILED = "DiscoveryStartFailed";
        public static final String DISCOVERY_STOP_FAILED = "DiscoveryStopFailed";
    }

    private final String eventName;

    MdnsEvent(String eventName) {
        this.eventName = eventName;
    }

    public String getEventName() {
        return eventName;
    }

}
