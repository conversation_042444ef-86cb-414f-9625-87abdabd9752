package com.storehub.websocket.mdns;

import android.content.Context;
import android.net.nsd.NsdManager;
import android.net.nsd.NsdServiceInfo;
//import com.storehub.rn.peripheral.util.LogResult;
import android.util.Log;
import com.storehub.websocket.utils.WebsocketThreadFactory;

import java.util.*;
import java.util.concurrent.*;

public class NdsDiscoveryManager {

    public static final String MDNS_SERVICE_TYPE = "_shws._tcp.";

    private final NsdManager nsdManager;
    private final Context context;

    public NdsDiscoveryManager(Context context) {
        this.context = context;
        this.nsdManager = ((NsdManager) context.getSystemService(Context.NSD_SERVICE));
    }

    private final List<NsdManager.RegistrationListener> childRegisterListeners = new ArrayList<>();
    private final List<NsdManager.RegistrationListener> childUnregisterListeners = new ArrayList<>();

    private final NsdManager.RegistrationListener defaultRegistrationListeners = new NsdManager.RegistrationListener() {
        @Override
        public void onServiceRegistered(NsdServiceInfo nsdServiceInfo) {
            for (NsdManager.RegistrationListener childRegisterListener : childRegisterListeners) {
                childRegisterListener.onServiceRegistered(nsdServiceInfo);
            }
            childRegisterListeners.clear();
        }

        @Override
        public void onServiceUnregistered(NsdServiceInfo nsdServiceInfo) {
            for (NsdManager.RegistrationListener childRegisterListener : childUnregisterListeners) {
                childRegisterListener.onServiceUnregistered(nsdServiceInfo);
            }
            childUnregisterListeners.clear();
        }

        @Override
        public void onRegistrationFailed(NsdServiceInfo nsdServiceInfo, int i) {
            for (NsdManager.RegistrationListener childRegisterListener : childRegisterListeners) {
                childRegisterListener.onRegistrationFailed(nsdServiceInfo, i);
            }
            childRegisterListeners.clear();
        }

        @Override
        public void onUnregistrationFailed(NsdServiceInfo nsdServiceInfo, int i) {
            for (NsdManager.RegistrationListener childRegisterListener : childUnregisterListeners) {
                childRegisterListener.onUnregistrationFailed(nsdServiceInfo, i);
            }
            childUnregisterListeners.clear();
        }
    };

    private final List<NsdManager.DiscoveryListener> childStartDiscoveryListeners = new ArrayList<>();
    private final List<NsdManager.DiscoveryListener> childStopDiscoveryListeners = new ArrayList<>();
    private final List<NsdManager.DiscoveryListener> childEventDiscoveryListeners = new ArrayList<>();

    private NsdManager.DiscoveryListener defaultDiscoveryListener = new NsdManager.DiscoveryListener() {
        @Override
        public void onStartDiscoveryFailed(String s, int i) {
            for (NsdManager.DiscoveryListener childStartDiscoveryListener : childStartDiscoveryListeners) {
                childStartDiscoveryListener.onStartDiscoveryFailed(s, i);
            }
            childStartDiscoveryListeners.clear();
        }

        @Override
        public void onStopDiscoveryFailed(String s, int i) {
            for (NsdManager.DiscoveryListener childStartDiscoveryListener : childStopDiscoveryListeners) {
                childStartDiscoveryListener.onStopDiscoveryFailed(s, i);
            }
            childStopDiscoveryListeners.clear();
        }

        @Override
        public void onDiscoveryStarted(String s) {
            for (NsdManager.DiscoveryListener childStartDiscoveryListener : childStartDiscoveryListeners) {
                childStartDiscoveryListener.onDiscoveryStarted(s);
                childEventDiscoveryListeners.add(childStartDiscoveryListener);
            }
            childStartDiscoveryListeners.clear();

        }

        @Override
        public void onDiscoveryStopped(String s) {
            for (NsdManager.DiscoveryListener childStartDiscoveryListener : childStopDiscoveryListeners) {
                childStartDiscoveryListener.onDiscoveryStopped(s);
            }
            childEventDiscoveryListeners.clear();
            childStopDiscoveryListeners.clear();
        }

        @Override
        public void onServiceFound(NsdServiceInfo nsdServiceInfo) {
            for (NsdManager.DiscoveryListener childEventDiscoveryListener : childEventDiscoveryListeners) {
                childEventDiscoveryListener.onServiceFound(nsdServiceInfo);
            }
        }

        @Override
        public void onServiceLost(NsdServiceInfo nsdServiceInfo) {
            for (NsdManager.DiscoveryListener childEventDiscoveryListener : childEventDiscoveryListeners) {
                childEventDiscoveryListener.onServiceLost(nsdServiceInfo);
            }
        }
    };

    public void registerNsdService(String serviceName, int port, Map<String, Object> attrs, NsdManager.RegistrationListener listener) {
        NsdServiceInfo info = new NsdServiceInfo();
        info.setServiceName(serviceName);
        info.setServiceType(MDNS_SERVICE_TYPE);
        info.setPort(port);

        // entry count < 10
        // key + value length must < 255 bytes
        // total length should < 400 bytes
        // total length must < 1300 bytes

        for (Map.Entry<String, Object> attr : attrs.entrySet()) {
            info.setAttribute(attr.getKey(), attr.getValue().toString());
        }
        try {
            childRegisterListeners.add(listener);
            nsdManager.registerService(info, NsdManager.PROTOCOL_DNS_SD, defaultRegistrationListeners);
        } catch (Exception e) {
            e.printStackTrace();
            childRegisterListeners.remove(listener);
            throw e;
        }
    }

    public void unregisterNsdService(NsdManager.RegistrationListener listener) {
        try {
            childUnregisterListeners.add(listener);
            nsdManager.unregisterService(defaultRegistrationListeners);
        } catch (Exception e) {
            e.printStackTrace();
            childUnregisterListeners.remove(listener);
            throw e;
        }
    }

    public void startNsdDiscovery(NsdManager.DiscoveryListener listener) {
        try {
            childStartDiscoveryListeners.add(listener);
            nsdManager.discoverServices(MDNS_SERVICE_TYPE, NsdManager.PROTOCOL_DNS_SD, defaultDiscoveryListener);
        } catch (Exception e) {
            e.printStackTrace();
            childStartDiscoveryListeners.remove(listener);
            throw e;
        }
    }

    public void stopNsdDiscovery(NsdManager.DiscoveryListener listener) {
        try {
            childStopDiscoveryListeners.add(listener);
            nsdManager.stopServiceDiscovery(defaultDiscoveryListener);
        } catch (Exception e) {
            e.printStackTrace();
            childStopDiscoveryListeners.remove(listener);
            throw e;
        }
    }

    public void resolveService(NsdServiceInfo serviceInfo, NsdManager.ResolveListener listener) {
        nsdManager.resolveService(serviceInfo, listener);
    }

    public void search(String serviceName, int timeoutMillis, DiscoveryCallback callback) {
        ExecutorService executors = Executors.newSingleThreadExecutor(WebsocketThreadFactory.getInstance());
        executors.execute(() -> {
            final CountDownLatch latch = new CountDownLatch(1);
            final LinkedHashMap<String, NsdServiceInfo> list = new LinkedHashMap<String, NsdServiceInfo>();
            final NsdManager service = ((NsdManager) context.getSystemService(Context.NSD_SERVICE));
            final NsdManager.DiscoveryListener listener = new NsdManager.DiscoveryListener() {
                @Override
                public void onStartDiscoveryFailed(String s, int i) {
                    latch.countDown();
                }

                @Override
                public void onStopDiscoveryFailed(String s, int i) {
                    latch.countDown();
                }

                @Override
                public void onDiscoveryStarted(String s) {
                }

                @Override
                public void onDiscoveryStopped(String s) {
                    latch.countDown();
                }

                @Override
                public void onServiceFound(NsdServiceInfo nsdServiceInfo) {
                    if (nsdServiceInfo.getServiceName().startsWith(serviceName)) {
                        service.resolveService(nsdServiceInfo, new NsdManager.ResolveListener() {
                            @Override
                            public void onResolveFailed(NsdServiceInfo nsdServiceInfo, int i) {
                            }

                            @Override
                            public void onServiceResolved(NsdServiceInfo nsdServiceInfo) {
                                list.put(nsdServiceInfo.getHost() + ":" + nsdServiceInfo.getPort(), nsdServiceInfo);
                            }
                        });

                    }
                }
                @Override
                public void onServiceLost(NsdServiceInfo nsdServiceInfo) {
                    if (nsdServiceInfo.getServiceName().startsWith(serviceName)) {
                        service.resolveService(nsdServiceInfo, new NsdManager.ResolveListener() {
                            @Override
                            public void onResolveFailed(NsdServiceInfo nsdServiceInfo, int i) {
                            }

                            @Override
                            public void onServiceResolved(NsdServiceInfo nsdServiceInfo) {
                                list.remove(nsdServiceInfo.getHost() + ":" + nsdServiceInfo.getPort());
                            }
                        });

                    }
                }
            };
            service.discoverServices(MDNS_SERVICE_TYPE, NsdManager.PROTOCOL_DNS_SD, listener);
            try {
                latch.await(timeoutMillis, TimeUnit.MILLISECONDS);
                callback.onSuccess(Collections.unmodifiableCollection(list.values()));
                service.stopServiceDiscovery(listener);
            } catch (Exception e) {
                callback.onFailure(e);
            }
        });
    }

    public interface DiscoveryCallback {
        void onSuccess(Collection<NsdServiceInfo> result);
        void onFailure(Exception e);
    }

}
