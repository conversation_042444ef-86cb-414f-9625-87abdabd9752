package com.storehub.websocket.server;

public enum ServerEvent {

    // addEventListener(websocketClientStateChanged)
    // {
    //    "socketId": "3ad134f6-25ef-11ee-be56-0242ac120002",
    //    "socketEvent": "ClientOpen",  // CLIENT_OPEN CLIENT_SERVER_MESSAGE CLIENT_CLOSE
    //    "param1": "content1",
    //    "param2": "content2",
    //    "param3": "content3",
    //    ...
    // }

    SERVER_START("ServerStart"),
    SERVER_CLOSE("ServerClose"),

    CLIENT_STATUS_CONNECTED("ClientStatusConnected"),
    CLIENT_STATUS_CLOSING("ClientStatusClosing"),
    CLIENT_STATUS_CLOSE("ClientStatusClose"),

    CLIENT_MESSAGE("ClientMessage"),

    RECEIVE_PING("ReceivePing"),
    RECEIVE_PONG("ReceivePong");

    private final String eventName;

    ServerEvent(String eventName) {
        this.eventName = eventName;
    }

    public String getEventName() {
        return eventName;
    }


}
