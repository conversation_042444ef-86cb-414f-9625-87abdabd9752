package com.storehub.websocket.server;

import com.storehub.websocket.Keys;
import org.java_websocket.WebSocket;
import org.java_websocket.drafts.Draft;
import org.java_websocket.framing.Framedata;
import org.java_websocket.handshake.ClientHandshake;
import org.java_websocket.server.DefaultWebSocketServerFactory;
import org.java_websocket.server.WebSocketServer;

import java.net.InetSocketAddress;
import java.util.List;
import java.util.UUID;

public class WebSocketServerImpl extends WebSocketServer {

    public static final int CONNECT_TIMEOUT = 5 * 1000; //ms
    public static final int CONNECTION_LOST_TIMEOUT = 10; //s

    private final WebsocketServerEventsListener listener;
    private final UUID uuid;

    public WebSocketServerImpl(String ip, int port, List<Draft> protocolDrafts, WebsocketServerEventsListener listener) {
        super(new InetSocketAddress(ip, port), protocolDrafts);
        setReuseAddr(true);
//        if (headers != null && !headers.isEmpty()) {
//            mServerHeadersInfo.putAll(headers);
//        }
        this.listener = listener;
        this.uuid = UUID.randomUUID();
        setWebSocketFactory(new DefaultWebSocketServerFactory());
        setConnectionLostTimeout(CONNECTION_LOST_TIMEOUT);
    }

    public UUID getUuid() {
        return uuid;
    }

    @Override
    public void onStart() {
        listener.onStart(this);
    }

    @Override
    public void onOpen(WebSocket conn, ClientHandshake handshake) {
        conn.setAttachment(handshake.getFieldValue(Keys.HEADER_EXT_SOCKET_ID));
        listener.onOpen(this, conn, handshake);
    }

    @Override
    public void onClosing(WebSocket conn, int code, String reason, boolean remote) {
        listener.onClosing(this, conn, code, reason, remote);
    }

    @Override
    public void onClose(WebSocket conn, int code, String reason, boolean remote) {
        listener.onClose(this, conn, code, reason, remote);
    }

    @Override
    public void onMessage(WebSocket conn, String message) {
        listener.onMessage(this, conn, message);
    }

    @Override
    public void onError(WebSocket conn, Exception ex) {
        listener.onError(this, conn, ex);
    }

    @Override
    public void onWebsocketPing(WebSocket conn, Framedata f) {
        super.onWebsocketPing(conn, f);
        listener.onPing(this, conn);
    }

    @Override
    public void onWebsocketPong(WebSocket conn, Framedata f) {
        super.onWebsocketPong(conn, f);
        listener.onPong(this, conn);
    }
}
