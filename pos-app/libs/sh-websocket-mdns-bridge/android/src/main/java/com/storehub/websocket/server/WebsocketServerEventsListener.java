package com.storehub.websocket.server;

import org.java_websocket.WebSocket;
import org.java_websocket.handshake.ClientHandshake;
import org.java_websocket.server.WebSocketServer;

public interface WebsocketServerEventsListener {

    void onStart(WebSocketServer server);

    void onOpen(WebSocketServer server, WebSocket socket, ClientHandshake handshake);

    void onMessage(WebSocketServer server, WebSocket socket, String message);

    void onClosing(WebSocketServer server, WebSocket socket, int code, String reason, boolean remote);

    void onClose(WebSocketServer server, WebSocket socket, int code, String reason, boolean remote);

    void onError(WebSocketServer server, WebSocket socket, Exception ex);

    void onPing(WebSocketServer server, WebSocket socket);

    void onPong(WebSocketServer server, WebSocket socket);
}
