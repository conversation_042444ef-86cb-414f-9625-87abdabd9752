package com.storehub.websocket.utils;

import android.util.Base64;

import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.security.spec.AlgorithmParameterSpec;
import java.util.Arrays;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

public class AesUtils {
    private static final String CIPHER_ALGORITHM = "AES/CBC/PKCS5Padding";
    private static final byte[] INIT_IV = {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};


    public static String encrypt(String key, String source) {
        byte[] bytes = encrypt(key, source.getBytes(StandardCharsets.UTF_8));
        if (bytes == null) {
            return source;
        } else {
            return new String(Base64.encode(bytes, Base64.NO_WRAP), StandardCharsets.UTF_8);
        }
    }

    public static byte[] encrypt(String key, byte[] source) {
        try {
            Key keySpec = generateKey(key);
            Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, getIv());
            return cipher.doFinal(source);
        } catch (Exception e) {
//            WsLogManager.getInstance().logError(WsLogEvent.AES_ENCRYPT_ERROR, "", e);
            e.printStackTrace();
        }
        return null;
    }

    public static String decrypt(String key, String source) {
        try {
            Key keySpec = generateKey(key);
            Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, keySpec, getIv());
            return new String(cipher.doFinal(Base64.decode(source, Base64.NO_WRAP)), StandardCharsets.UTF_8);
        } catch (Exception e) {
//            WsLogManager.getInstance().logError(WsLogEvent.AES_DECRYPT_ERROR, "", e);
            e.printStackTrace();
        }
        return source;
    }

    private static byte[] str2bytes32(String str) {
        byte[] hexChars = new byte[32];
        Arrays.fill(hexChars, (byte) 0x00);
        for (int j = 0; j < Math.min(str.length(),hexChars.length); j++) {
            hexChars[j] = (byte) str.charAt(j);
        }
        return hexChars;
    }

    private static AlgorithmParameterSpec getIv() {
        return new IvParameterSpec(INIT_IV);
    }

    private static Key generateKey(String key) {
        return new SecretKeySpec(str2bytes32(key), "AES");
    }

}
