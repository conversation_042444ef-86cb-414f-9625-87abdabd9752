package com.storehub.websocket.utils;

import android.util.Log;
import org.java_websocket.enums.Opcode;
import org.java_websocket.exceptions.InvalidDataException;
import org.java_websocket.exceptions.InvalidFrameException;
import org.java_websocket.extensions.CompressionExtension;
import org.java_websocket.extensions.IExtension;
import org.java_websocket.framing.*;

import java.io.ByteArrayOutputStream;
import java.nio.ByteBuffer;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.zip.DataFormatException;
import java.util.zip.Deflater;
import java.util.zip.Inflater;

public class PerMessageDeflateExtension extends CompressionExtension {

    private static final String EXTENSION_REGISTERED_NAME = "permessage-deflate";
    private static final String SERVER_NO_CONTEXT_TAKEOVER = "server_no_context_takeover";
    private static final String CLIENT_NO_CONTEXT_TAKEOVER = "client_no_context_takeover";
    private static final byte[] TAIL_BYTES = {(byte) 0x00, (byte) 0x00, (byte) 0xFF, (byte) 0xFF};
    private static final int BUFFER_SIZE = 1 << 10;
    private final Map<String, String> requestedParameters = new LinkedHashMap<>();
    private boolean serverNoContextTakeover = true;
    private boolean clientNoContextTakeover = false;
    private Inflater inflater = new Inflater(true);
    private Deflater deflater = new Deflater(Deflater.DEFAULT_COMPRESSION, true);

    private static boolean endsWithTail(byte[] data) {
        if (data.length < 4) {
            return false;
        }

        int length = data.length;
        for (int i = 0; i < TAIL_BYTES.length; i++) {
            if (TAIL_BYTES[i] != data[length - TAIL_BYTES.length + i]) {
                return false;
            }
        }

        return true;
    }

    public Inflater getInflater() {
        return inflater;
    }

    public void setInflater(Inflater inflater) {
        this.inflater = inflater;
    }

    public Deflater getDeflater() {
        return deflater;
    }

    public void setDeflater(Deflater deflater) {
        this.deflater = deflater;
    }

    public boolean isServerNoContextTakeover() {
        return serverNoContextTakeover;
    }

    public void setServerNoContextTakeover(boolean serverNoContextTakeover) {
        this.serverNoContextTakeover = serverNoContextTakeover;
    }

    public boolean isClientNoContextTakeover() {
        return clientNoContextTakeover;
    }

    public void setClientNoContextTakeover(boolean clientNoContextTakeover) {
        this.clientNoContextTakeover = clientNoContextTakeover;
    }

    @Override
    public void decodeFrame(Framedata inputFrame) throws InvalidDataException {

        if (!(inputFrame instanceof DataFrame)) {
            return;
        }

        if (inputFrame.getOpcode() == Opcode.CONTINUOUS && inputFrame.isRSV1()) {
            throw new InvalidDataException(CloseFrame.POLICY_VALIDATION, "RSV1 bit can only be set for the first frame.");
        }

        ByteArrayOutputStream output = new ByteArrayOutputStream();
        try {
            decompress(inputFrame.getPayloadData().array(), output);

            if (inflater.getRemaining() > 0) {
                inflater = new Inflater(true);
                decompress(inputFrame.getPayloadData().array(), output);
            }

            if (inputFrame.isFin()) {
                decompress(TAIL_BYTES, output);
                if (clientNoContextTakeover) {
                    inflater = new Inflater(true);
                }
            }
        } catch (DataFormatException e) {
            throw new InvalidDataException(CloseFrame.POLICY_VALIDATION, e.getMessage());
        }

        if (inputFrame.isRSV1()) {
            ((DataFrame) inputFrame).setRSV1(false);
        }

        ((FramedataImpl1) inputFrame).setPayload(ByteBuffer.wrap(output.toByteArray(), 0, output.size()));
    }

    private void decompress(byte[] data, ByteArrayOutputStream outputBuffer) throws DataFormatException {
        inflater.setInput(data);
        byte[] buffer = new byte[BUFFER_SIZE];

        int bytesInflated;
        while ((bytesInflated = inflater.inflate(buffer)) > 0) {
            outputBuffer.write(buffer, 0, bytesInflated);
        }
    }

    @Override
    public void encodeFrame(Framedata inputFrame) {
        if (!(inputFrame instanceof DataFrame)) {
            return;
        }

        if (!(inputFrame instanceof ContinuousFrame)) {
            ((DataFrame) inputFrame).setRSV1(true);
        }

        deflater.setInput(inputFrame.getPayloadData().array());
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int bytesCompressed;
        while ((bytesCompressed = deflater.deflate(buffer, 0, buffer.length, Deflater.SYNC_FLUSH)) > 0) {
            output.write(buffer, 0, bytesCompressed);
        }

        byte[] outputBytes = output.toByteArray();
        int outputLength = outputBytes.length;

        if (inputFrame.isFin()) {
            if (endsWithTail(outputBytes)) {
                outputLength -= TAIL_BYTES.length;
            }

            if (serverNoContextTakeover) {
                deflater.end();
                deflater = new Deflater(Deflater.DEFAULT_COMPRESSION, true);
            }
        }

        ((FramedataImpl1) inputFrame).setPayload(ByteBuffer.wrap(outputBytes, 0, outputLength));
    }

    @Override
    public boolean acceptProvidedExtensionAsServer(String inputExtension) {
        String[] requestedExtensions = inputExtension.split(",");
        for (String extension : requestedExtensions) {
            ExtensionRequestData extensionData = ExtensionRequestData.parseExtensionRequest(extension);
            if (!EXTENSION_REGISTERED_NAME.equalsIgnoreCase(extensionData.getExtensionName())) {
                continue;
            }

            Map<String, String> headers = extensionData.getExtensionParameters();
            requestedParameters.putAll(headers);
            if (requestedParameters.containsKey(CLIENT_NO_CONTEXT_TAKEOVER)) {
                clientNoContextTakeover = true;
            }

            return true;
        }

        return false;
    }

    @Override
    public boolean acceptProvidedExtensionAsClient(String inputExtension) {
        String[] requestedExtensions = inputExtension.split(",");
        for (String extension : requestedExtensions) {
            ExtensionRequestData extensionData = ExtensionRequestData.parseExtensionRequest(extension);
            if (!EXTENSION_REGISTERED_NAME.equalsIgnoreCase(extensionData.getExtensionName())) {
                continue;
            }

            Map<String, String> headers = extensionData.getExtensionParameters();
            Log.d("PMDExtension", "acceptProvidedExtensionAsClient: " + true);
            return true;
        }
        Log.d("PMDExtension", "acceptProvidedExtensionAsClient: " + false);

        return false;
    }

    @Override
    public String getProvidedExtensionAsClient() {
        requestedParameters.put(CLIENT_NO_CONTEXT_TAKEOVER, ExtensionRequestData.EMPTY_VALUE);
        requestedParameters.put(SERVER_NO_CONTEXT_TAKEOVER, ExtensionRequestData.EMPTY_VALUE);

        return EXTENSION_REGISTERED_NAME + "; " + SERVER_NO_CONTEXT_TAKEOVER + "; " + CLIENT_NO_CONTEXT_TAKEOVER;
    }

    @Override
    public String getProvidedExtensionAsServer() {
        return EXTENSION_REGISTERED_NAME + "; " + SERVER_NO_CONTEXT_TAKEOVER + (clientNoContextTakeover ? "; " + CLIENT_NO_CONTEXT_TAKEOVER : "");
    }

    @Override
    public IExtension copyInstance() {
        return new PerMessageDeflateExtension();
    }

    @Override
    public void isFrameValid(Framedata inputFrame) throws InvalidDataException {
        if ((inputFrame instanceof TextFrame || inputFrame instanceof BinaryFrame) && !inputFrame.isRSV1()) {
            throw new InvalidFrameException("RSV1 bit must be set for DataFrames.");
        }
        if ((inputFrame instanceof ContinuousFrame) && (inputFrame.isRSV1() || inputFrame.isRSV2() || inputFrame.isRSV3())) {
            throw new InvalidFrameException("bad rsv RSV1: " + inputFrame.isRSV1() + " RSV2: " + inputFrame.isRSV2() + " RSV3: " + inputFrame.isRSV3());
        }
        super.isFrameValid(inputFrame);
    }

    @Override
    public String toString() {
        return "PerMessageDeflateExtension";
    }

    private static class ExtensionRequestData {

        public static final String EMPTY_VALUE = "";

        private final Map<String, String> extensionParameters;
        private String extensionName;

        private ExtensionRequestData() {
            extensionParameters = new LinkedHashMap<>();
        }

        public static ExtensionRequestData parseExtensionRequest(String extensionRequest) {
            ExtensionRequestData extensionData = new ExtensionRequestData();
            String[] parts = extensionRequest.split(";");
            extensionData.extensionName = parts[0].trim();

            for (int i = 1; i < parts.length; i++) {
                String[] keyValue = parts[i].split("=");
                String value = EMPTY_VALUE;

                if (keyValue.length > 1) {
                    String tempValue = keyValue[1].trim();

                    if ((tempValue.startsWith("\"") && tempValue.endsWith("\""))
                            || (tempValue.startsWith("'") && tempValue.endsWith("'"))
                            && tempValue.length() > 2) {
                        tempValue = tempValue.substring(1, tempValue.length() - 1);
                    }

                    value = tempValue;
                }

                extensionData.extensionParameters.put(keyValue[0].trim(), value);
            }

            return extensionData;
        }

        public String getExtensionName() {
            return extensionName;
        }

        public Map<String, String> getExtensionParameters() {
            return extensionParameters;
        }
    }
}
