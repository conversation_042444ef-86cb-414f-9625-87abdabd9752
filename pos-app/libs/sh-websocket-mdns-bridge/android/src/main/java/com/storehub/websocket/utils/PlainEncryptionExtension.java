package com.storehub.websocket.utils;

import org.java_websocket.exceptions.InvalidDataException;
import org.java_websocket.extensions.DefaultExtension;
import org.java_websocket.extensions.IExtension;
import org.java_websocket.framing.CloseFrame;
import org.java_websocket.framing.DataFrame;
import org.java_websocket.framing.Framedata;
import org.java_websocket.framing.FramedataImpl1;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Random;

/**
 * Aes encryption extension for the websocket protocol.
 */
public final class PlainEncryptionExtension extends DefaultExtension {

    private static final String EXTENSION_REGISTERED_NAME = "aes-encryption";
    private static final String SERVER_NO_CONTEXT_TAKEOVER = "server_no_context_takeover";
    private static final String CLIENT_NO_CONTEXT_TAKEOVER = "client_no_context_takeover";
    private static final String CIPHER_ALGORITHM = "AES/CBC/PKCS5Padding";

    private boolean serverNoContextTakeover = true;
    private boolean clientNoContextTakeover = false;

    private final Map<String, String> requestedParameters = new LinkedHashMap<>();

    private Cipher cipher;
    private Random randomSecureRandom;

    public PlainEncryptionExtension() {
        try {
            cipher = Cipher.getInstance(CIPHER_ALGORITHM);
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            randomSecureRandom = new SecureRandom();
        } catch (Exception e) {
            randomSecureRandom = new Random();
        }
    }

    private static byte[] sha256(byte[] key) throws NoSuchAlgorithmException {
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        return digest.digest(key);
    }

    private static Key generateKey(String key) throws NoSuchAlgorithmException {
        return new SecretKeySpec(sha256(sha256(key.getBytes(StandardCharsets.UTF_8))), "AES");
    }

    @Override
    public void decodeFrame(Framedata inputFrame) throws InvalidDataException {

        if (!(inputFrame instanceof DataFrame)) {
            return;
        }

        try {
            Key keySpec = generateKey("test_ket");

            byte[] org = inputFrame.getPayloadData().array();
            byte[] source = Arrays.copyOfRange(org, cipher.getBlockSize(), org.length);
            byte[] iv = Arrays.copyOfRange(org, 0, cipher.getBlockSize());
            cipher.init(Cipher.DECRYPT_MODE, keySpec, new IvParameterSpec(iv));
            byte[] result = cipher.doFinal(source);

            ((FramedataImpl1) inputFrame).setPayload(ByteBuffer.wrap(result, 0, result.length));

        } catch (InvalidAlgorithmParameterException | InvalidKeyException | BadPaddingException | IllegalBlockSizeException | NoSuchAlgorithmException e) {
            e.printStackTrace();
            throw new InvalidDataException(CloseFrame.POLICY_VALIDATION, e.getMessage());
        }

    }

    @Override
    public void encodeFrame(Framedata inputFrame) {
        if (!(inputFrame instanceof DataFrame)) {
            return;
        }

        try {
            Key keySpec = generateKey("test_ket");

            byte[] source = inputFrame.getPayloadData().array();
            byte[] iv = new byte[cipher.getBlockSize()];
            randomSecureRandom.nextBytes(iv);
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, new IvParameterSpec(iv));
            byte[] result = cipher.doFinal(source);

            ((FramedataImpl1) inputFrame).setPayload(ByteBuffer.allocate(iv.length + result.length).put(iv).put(result));

        } catch (InvalidAlgorithmParameterException | InvalidKeyException | IllegalBlockSizeException | BadPaddingException | NoSuchAlgorithmException  e) {
            e.printStackTrace();
        }
    }

    @Override
    public boolean acceptProvidedExtensionAsServer(String inputExtension) {
        String[] requestedExtensions = inputExtension.split(",");
        for (String extension : requestedExtensions) {
            ExtensionRequestData extensionData = ExtensionRequestData.parseExtensionRequest(extension);
            if (!EXTENSION_REGISTERED_NAME.equalsIgnoreCase(extensionData.getExtensionName())) {
                continue;
            }

            Map<String, String> headers = extensionData.getExtensionParameters();
            requestedParameters.putAll(headers);
            if (requestedParameters.containsKey(CLIENT_NO_CONTEXT_TAKEOVER)) {
                clientNoContextTakeover = true;
            }

            return true;
        }

        return false;
    }

    @Override
    public boolean acceptProvidedExtensionAsClient(String inputExtension) {
        String[] requestedExtensions = inputExtension.split(",");
        for (String extension : requestedExtensions) {
            ExtensionRequestData extensionData = ExtensionRequestData.parseExtensionRequest(extension);
            if (!EXTENSION_REGISTERED_NAME.equalsIgnoreCase(extensionData.getExtensionName())) {
                continue;
            }
            return true;
        }

        return false;
    }

    @Override
    public String getProvidedExtensionAsClient() {
        requestedParameters.put(CLIENT_NO_CONTEXT_TAKEOVER, ExtensionRequestData.EMPTY_VALUE);
        requestedParameters.put(SERVER_NO_CONTEXT_TAKEOVER, ExtensionRequestData.EMPTY_VALUE);

        return EXTENSION_REGISTERED_NAME + "; " + SERVER_NO_CONTEXT_TAKEOVER + "; " + CLIENT_NO_CONTEXT_TAKEOVER;
    }

    @Override
    public String getProvidedExtensionAsServer() {
        return EXTENSION_REGISTERED_NAME + "; " + SERVER_NO_CONTEXT_TAKEOVER + (clientNoContextTakeover ? "; " + CLIENT_NO_CONTEXT_TAKEOVER : "");
    }

    @Override
    public IExtension copyInstance() {
        return new PlainEncryptionExtension();
    }

    @Override
    public String toString() {
        return "PlainEncryptionExtension";
    }

}
