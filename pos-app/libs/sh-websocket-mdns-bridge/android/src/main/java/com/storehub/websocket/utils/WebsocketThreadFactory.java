package com.storehub.websocket.utils;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

import timber.log.Timber;

public class WebsocketThreadFactory implements ThreadFactory {
    private static final Map<String, AtomicInteger> poolMap = new HashMap<>();
    private final ThreadGroup group;
    private final AtomicInteger threadNumber = new AtomicInteger(1);
    private final String namePrefix;

    WebsocketThreadFactory() {
        this("websocketPool");
    }

    private static final class InstanceHolder {
        private static final WebsocketThreadFactory instance = new WebsocketThreadFactory();
    }

    public static WebsocketThreadFactory getInstance() {
        return InstanceHolder.instance;
    }

    public WebsocketThreadFactory(String poolName) {
        SecurityManager s = System.getSecurityManager();
        group = (s != null) ? s.getThreadGroup() :
                Thread.currentThread().getThreadGroup();
        AtomicInteger poolNumber = poolMap.get(poolName);
        if (poolNumber == null) {
            synchronized (poolMap) {
                poolNumber = poolMap.get(poolName);
                if (poolNumber == null) poolNumber = new AtomicInteger(1);
                poolMap.put(poolName, poolNumber);
            }
        }
        namePrefix = poolName + "-" +
                poolNumber.getAndIncrement() +
                "-thread-";
        Timber.d("SocketThreadFactory: %s", namePrefix);
    }

    public Thread newThread(Runnable r) {
        String tName = namePrefix + threadNumber.getAndIncrement();
        Timber.i("newThread: thread name: %s", tName);
        Thread t = new Thread(group, r, tName, 0);
        if (t.isDaemon())
            t.setDaemon(false);
        if (t.getPriority() != Thread.NORM_PRIORITY)
            t.setPriority(Thread.NORM_PRIORITY);
        return t;
    }
}
