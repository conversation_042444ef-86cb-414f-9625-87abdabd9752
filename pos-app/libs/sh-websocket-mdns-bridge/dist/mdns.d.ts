import { NativeEventEmitter } from 'react-native';
export declare const MDNS_EVENT = 'mdnsStateChanged';
export declare const MdnsNative: MdnsType;
export declare const MdnsEmitter: NativeEventEmitter;
export type MdnsType = {
  startService(serviceName: string, serverPort: number, extras: MdnsServiceExtras): Promise<any>;
  stopService(): Promise<any>;
  startServiceDiscovery(serviceName: string): Promise<any>;
  stopServiceDiscovery(): Promise<any>;
  searchService(serviceName: string, timeout: number): Promise<MdnsServiceType[]>;
};
export type MdnsServiceExtras = {
  id: string;
  alias: string;
  version: string;
};
export type MdnsServiceType = {
  serviceName: string;
  serviceAddress: string;
  servicePort: number;
  extras: MdnsServiceExtras;
};
export declare enum MdnsEvent {
  serviceFound = 'ServiceFound',
  serviceLost = 'ServiceLost',
}
export type MdnsServiceFoundType = {
  event: MdnsEvent;
  service: MdnsServiceType;
};
export type MdnsServiceLostType = {
  event: MdnsEvent;
  service: MdnsServiceType;
};
export type MdnsMessageType = MdnsServiceFoundType | MdnsServiceLostType;
//# sourceMappingURL=mdns.d.ts.map
