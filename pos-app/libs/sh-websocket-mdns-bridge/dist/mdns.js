"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MdnsEvent = exports.MdnsEmitter = exports.MdnsNative = exports.MDNS_EVENT = void 0;
var react_native_1 = require("react-native");
var MdnsDiscoveryModule = react_native_1.NativeModules.MdnsDiscoveryModule;
exports.MDNS_EVENT = "mdnsStateChanged";
exports.MdnsNative = MdnsDiscoveryModule;
exports.MdnsEmitter = new react_native_1.NativeEventEmitter(MdnsDiscoveryModule);
var MdnsEvent;
(function (MdnsEvent) {
    MdnsEvent["serviceFound"] = "ServiceFound";
    MdnsEvent["serviceLost"] = "ServiceLost";
})(MdnsEvent || (exports.MdnsEvent = MdnsEvent = {}));
//# sourceMappingURL=mdns.js.map