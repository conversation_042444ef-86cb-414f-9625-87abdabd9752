export type WebsocketMessage<D = unknown, M = unknown> = {
  target: MessageTarget | string;
  header: MessageHeader;
  command: M;
  payload: MessagePayload<D>;
};
export type MessagePayload<D = unknown> = {
  code: number;
  data: D;
  errorMessage?: string;
};
export type MessageHeader = MessageTarget & {
  messageId: string;
  version: string;
  timeStamp: number;
  isSharedMessage?: boolean;
  retryTimes?: number;
  socketId?: string;
  responseId?: string;
  ongoingOrders?: number;
  otlpTraceId?: string;
};
export type MessageTarget = MessageIdentifier & {
  role: string;
  tags?: string[];
  type?: string;
};
export type MessageIdentifier = {
  id: string;
  ip: string;
  port?: string;
  registerId?: string;
  registerObjectId?: string;
  kdsName?: string;
  name?: string;
};
//# sourceMappingURL=message.d.ts.map
