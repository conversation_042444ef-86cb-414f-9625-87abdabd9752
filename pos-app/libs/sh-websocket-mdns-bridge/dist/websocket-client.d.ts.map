{"version": 3, "file": "websocket-client.d.ts", "sourceRoot": "", "sources": ["../src/websocket-client.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,kBAAkB,EAAiB,MAAM,cAAc,CAAC;AACjE,OAAO,EAAE,WAAW,EAAE,cAAc,EAAE,MAAM,aAAa,CAAC;AAI1D,eAAO,MAAM,eAAe,gCAAgC,CAAC;AAC7D,eAAO,MAAM,eAAe,qBAA+C,CAAC;AAC5E,eAAO,MAAM,sBAAsB,oBAElC,CAAC;AACF,oBAAY,oBAAoB;IAC9B,UAAU,2BAA2B;IACrC,SAAS,0BAA0B;IACnC,OAAO,wBAAwB;IAC/B,MAAM,uBAAuB;IAC7B,OAAO,mBAAmB;IAE1B,IAAI,gBAAgB;IACpB,IAAI,gBAAgB;CACrB;AAED,MAAM,MAAM,oBAAoB,GAAG;IACjC,WAAW,EAAE,oBAAoB,CAAC,UAAU,CAAC;IAC7C,QAAQ,EAAE,MAAM,CAAC;CAClB,CAAC;AAEF,MAAM,MAAM,mBAAmB,GAAG;IAChC,WAAW,EAAE,oBAAoB,CAAC,SAAS,CAAC;IAC5C,QAAQ,EAAE,MAAM,CAAC;IACjB,MAAM,EAAE,MAAM,CAAC;IACf,SAAS,EAAE,MAAM,CAAC;CACnB,CAAC;AACF,MAAM,MAAM,iBAAiB,GAAG;IAC9B,WAAW,EAAE,oBAAoB,CAAC,OAAO,CAAC;IAC1C,QAAQ,EAAE,MAAM,CAAC;IACjB,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,EAAE,MAAM,CAAC;IACf,UAAU,EAAE,OAAO,CAAC;CACrB,CAAC;AAEF,MAAM,MAAM,gBAAgB,GAAG;IAC7B,WAAW,EAAE,oBAAoB,CAAC,MAAM,CAAC;IACzC,QAAQ,EAAE,MAAM,CAAC;IACjB,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,EAAE,MAAM,CAAC;IACf,UAAU,EAAE,OAAO,CAAC;CACrB,CAAC;AAEF,MAAM,MAAM,wBAAwB,GAAG;IACrC,WAAW,EAAE,oBAAoB,CAAC,OAAO,CAAC;IAC1C,QAAQ,EAAE,MAAM,CAAC;IACjB,OAAO,EAAE,MAAM,CAAC;CACjB,CAAC;AAEF,MAAM,MAAM,cAAc,GAAG;IAC3B,WAAW,EAAE,oBAAoB,CAAC,IAAI,CAAC;IACvC,QAAQ,EAAE,MAAM,CAAC;CAClB,CAAC;AAEF,MAAM,MAAM,cAAc,GAAG;IAC3B,WAAW,EAAE,oBAAoB,CAAC,IAAI,CAAC;IACvC,QAAQ,EAAE,MAAM,CAAC;CAClB,CAAC;AAEF,MAAM,MAAM,iBAAiB,GACzB,oBAAoB,GACpB,mBAAmB,GACnB,iBAAiB,GACjB,gBAAgB,GAChB,cAAc,GACd,cAAc,GACd,wBAAwB,CAAC;AAE7B,MAAM,MAAM,mBAAmB,GAAG;IAChC,MAAM,CAAC,MAAM,EAAE,gBAAgB,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC;IACvD,OAAO,CAAC,QAAQ,EAAE,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAC9C,IAAI,CAAC,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAC5D,KAAK,CAAC,QAAQ,EAAE,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAC5C,OAAO,IAAI,IAAI,CAAC;IAChB,cAAc,CAAC,EAAE,EAAE,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;IACpD,aAAa,CAAC,QAAQ,EAAE,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IACtD,aAAa,CAAC,QAAQ,EAAE,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IACtD,YAAY,CAAC,QAAQ,EAAE,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IACrD,YAAY,CAAC,QAAQ,EAAE,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;CACtD,CAAC;AAEF,MAAM,MAAM,gBAAgB,GAAG;IAC7B,aAAa,EAAE,MAAM,CAAC;IACtB,UAAU,EAAE,MAAM,CAAC;IAEnB,OAAO,EAAE,MAAM,CAAC;CACjB,CAAC;AAEF,MAAM,MAAM,iBAAiB,GAAG;IAC9B,UAAU,EAAE,MAAM,CAAC;IACnB,gBAAgB,EAAE,MAAM,CAAC;IACzB,OAAO,EAAE,MAAM,CAAC;IAChB,QAAQ,EAAE,MAAM,CAAC;IACjB,OAAO,EAAE,MAAM,CAAC;IAChB,YAAY,EAAE,MAAM,CAAC;IACrB,YAAY,EAAE,MAAM,CAAC;CACtB,CAAC"}