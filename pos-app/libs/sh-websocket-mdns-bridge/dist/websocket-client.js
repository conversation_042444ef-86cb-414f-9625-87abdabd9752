"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebsocketClientEvent = exports.WebsocketClientEmitter = exports.WebsocketClient = exports.WS_CLIENT_EVENT = void 0;
var react_native_1 = require("react-native");
var WebSocketClientModule = react_native_1.NativeModules.WebSocketClientModule;
exports.WS_CLIENT_EVENT = "websocketClientStateChanged";
exports.WebsocketClient = WebSocketClientModule;
exports.WebsocketClientEmitter = new react_native_1.NativeEventEmitter(WebSocketClientModule);
var WebsocketClientEvent;
(function (WebsocketClientEvent) {
    WebsocketClientEvent["connecting"] = "ClientStatusConnecting";
    WebsocketClientEvent["connected"] = "ClientStatusConnected";
    WebsocketClientEvent["closing"] = "ClientStatusClosing";
    WebsocketClientEvent["closed"] = "ClientStatusClosed";
    WebsocketClientEvent["message"] = "ReceiveMessage";
    // client server都会主动发起ping
    WebsocketClientEvent["ping"] = "ReceivePing";
    WebsocketClientEvent["pong"] = "ReceivePong";
})(WebsocketClientEvent || (exports.WebsocketClientEvent = WebsocketClientEvent = {}));
//# sourceMappingURL=websocket-client.js.map