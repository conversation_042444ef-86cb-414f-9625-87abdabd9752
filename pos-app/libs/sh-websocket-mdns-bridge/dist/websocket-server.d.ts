import { NativeEventEmitter } from 'react-native';
import { WebsocketId, WebsocketState } from './websocket';
export declare const WebsocketServer: WebsocketServerType;
export declare const WebsocketServerEmitter: NativeEventEmitter;
export declare const WS_SERVER_EVENT = 'websocketServerStateChanged';
export type WebsocketServerType = {
  startServer(port: number): Promise<void>;
  stopServer(): Promise<void>;
  send(clientId: WebsocketId, message: string): Promise<void>;
  broadcast(message: string): Promise<void>;
  close(client: WebsocketId): Promise<void>;
  getSocketState(clientId: WebsocketId): Promise<WebsocketState>;
  exitApp(): Promise<void>;
  getLocalHost(): Promise<string>;
  getLocalPort(): Promise<string>;
  getRemoteHost(clientId: WebsocketId): Promise<string>;
  getRemotePort(clientId: WebsocketId): Promise<string>;
};
export declare enum WebsocketServerEvent {
  start = 'ServerStart',
  close = 'ServerClose',
  clientConnected = 'ClientStatusConnected',
  clientClosing = 'ClientStatusClosing',
  clientClosed = 'ClientStatusClose',
  clientMessage = 'ClientMessage',
  ping = 'ReceivePing',
  pong = 'ReceivePong',
}
export type ServerStartType = {
  socketEvent: WebsocketServerEvent.start;
  socketId: string;
  socketLocalPort: string;
  socketLocalAddress: string;
};
export type ServerCloseType = {
  socketEvent: WebsocketServerEvent.close;
  socketId: null;
};
export type ServerClientConnectedType = {
  socketEvent: WebsocketServerEvent.clientConnected;
  socketId: string;
  remoteAddress: string;
  remotePort: string;
  payload: string;
};
export type ServerClientClosingType = {
  socketEvent: WebsocketServerEvent.clientClosing;
  socketId: string;
  code: string;
  reason: string;
  fromRemote: boolean;
};
export type ServerClientClosedType = {
  socketEvent: WebsocketServerEvent.clientClosed;
  socketId: string;
  code: string;
  reason: string;
  fromRemote: boolean;
};
export type ServerReceiveMessageType = {
  socketEvent: WebsocketServerEvent.clientMessage;
  socketId: string;
  message: string;
};
export type ServerPingType = {
  socketEvent: WebsocketServerEvent.ping;
  socketId: string;
};
export type ServerPongType = {
  socketEvent: WebsocketServerEvent.pong;
  socketId: string;
};
export type ServerMessageType =
  | ServerStartType
  | ServerCloseType
  | ServerClientConnectedType
  | ServerClientClosingType
  | ServerClientClosedType
  | ServerReceiveMessageType
  | ServerPingType
  | ServerPongType;
export declare const WebsocketServerStatusEvent: WebsocketServerEvent[];
export declare const WebsocketClientStatusEvent: WebsocketServerEvent[];
export declare const WebsocketClientMessageEvent: WebsocketServerEvent[];
export declare const WebsocketHeartBeatEvent: WebsocketServerEvent[];
//# sourceMappingURL=websocket-server.d.ts.map
