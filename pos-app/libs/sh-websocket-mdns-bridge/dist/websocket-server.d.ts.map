{"version": 3, "file": "websocket-server.d.ts", "sourceRoot": "", "sources": ["../src/websocket-server.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,kBAAkB,EAAiB,MAAM,cAAc,CAAC;AACjE,OAAO,EAAE,WAAW,EAAE,cAAc,EAAE,MAAM,aAAa,CAAC;AAG1D,eAAO,MAAM,eAAe,qBAA+C,CAAC;AAC5E,eAAO,MAAM,sBAAsB,oBAElC,CAAC;AACF,eAAO,MAAM,eAAe,gCAAgC,CAAC;AAE7D,MAAM,MAAM,mBAAmB,GAAG;IAChC,WAAW,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IACzC,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IAC5B,IAAI,CAAC,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAC5D,SAAS,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAC1C,KAAK,CAAC,MAAM,EAAE,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAE1C,cAAc,CAAC,QAAQ,EAAE,WAAW,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;IAC/D,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IACzB,YAAY,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC;IAChC,YAAY,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC;IAChC,aAAa,CAAC,QAAQ,EAAE,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IACtD,aAAa,CAAC,QAAQ,EAAE,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;CACvD,CAAC;AAEF,oBAAY,oBAAoB;IAC9B,KAAK,gBAAgB;IACrB,KAAK,gBAAgB;IACrB,eAAe,0BAA0B;IACzC,aAAa,wBAAwB;IACrC,YAAY,sBAAsB;IAClC,aAAa,kBAAkB;IAC/B,IAAI,gBAAgB;IACpB,IAAI,gBAAgB;CACrB;AAED,MAAM,MAAM,eAAe,GAAG;IAC5B,WAAW,EAAE,oBAAoB,CAAC,KAAK,CAAC;IACxC,QAAQ,EAAE,MAAM,CAAC;IACjB,eAAe,EAAE,MAAM,CAAC;IACxB,kBAAkB,EAAE,MAAM,CAAC;CAC5B,CAAC;AAEF,MAAM,MAAM,eAAe,GAAG;IAC5B,WAAW,EAAE,oBAAoB,CAAC,KAAK,CAAC;IACxC,QAAQ,EAAE,IAAI,CAAC;CAChB,CAAC;AACF,MAAM,MAAM,yBAAyB,GAAG;IACtC,WAAW,EAAE,oBAAoB,CAAC,eAAe,CAAC;IAClD,QAAQ,EAAE,MAAM,CAAC;IACjB,aAAa,EAAE,MAAM,CAAC;IACtB,UAAU,EAAE,MAAM,CAAC;IACnB,OAAO,EAAE,MAAM,CAAC;CACjB,CAAC;AAEF,MAAM,MAAM,uBAAuB,GAAG;IACpC,WAAW,EAAE,oBAAoB,CAAC,aAAa,CAAC;IAChD,QAAQ,EAAE,MAAM,CAAC;IACjB,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,EAAE,MAAM,CAAC;IACf,UAAU,EAAE,OAAO,CAAC;CACrB,CAAC;AAEF,MAAM,MAAM,sBAAsB,GAAG;IACnC,WAAW,EAAE,oBAAoB,CAAC,YAAY,CAAC;IAC/C,QAAQ,EAAE,MAAM,CAAC;IACjB,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,EAAE,MAAM,CAAC;IACf,UAAU,EAAE,OAAO,CAAC;CACrB,CAAC;AAEF,MAAM,MAAM,wBAAwB,GAAG;IACrC,WAAW,EAAE,oBAAoB,CAAC,aAAa,CAAC;IAChD,QAAQ,EAAE,MAAM,CAAC;IACjB,OAAO,EAAE,MAAM,CAAC;CACjB,CAAC;AAEF,MAAM,MAAM,cAAc,GAAG;IAC3B,WAAW,EAAE,oBAAoB,CAAC,IAAI,CAAC;IACvC,QAAQ,EAAE,MAAM,CAAC;CAClB,CAAC;AAEF,MAAM,MAAM,cAAc,GAAG;IAC3B,WAAW,EAAE,oBAAoB,CAAC,IAAI,CAAC;IACvC,QAAQ,EAAE,MAAM,CAAC;CAClB,CAAC;AAEF,MAAM,MAAM,iBAAiB,GACzB,eAAe,GACf,eAAe,GACf,yBAAyB,GACzB,uBAAuB,GACvB,sBAAsB,GACtB,wBAAwB,GACxB,cAAc,GACd,cAAc,CAAC;AAEnB,eAAO,MAAM,0BAA0B,wBAGtC,CAAC;AACF,eAAO,MAAM,0BAA0B,wBAItC,CAAC;AACF,eAAO,MAAM,2BAA2B,wBAAuC,CAAC;AAEhF,eAAO,MAAM,uBAAuB,wBAGnC,CAAC"}