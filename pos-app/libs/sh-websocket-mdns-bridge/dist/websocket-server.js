"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebsocketHeartBeatEvent = exports.WebsocketClientMessageEvent = exports.WebsocketClientStatusEvent = exports.WebsocketServerStatusEvent = exports.WebsocketServerEvent = exports.WS_SERVER_EVENT = exports.WebsocketServerEmitter = exports.WebsocketServer = void 0;
var react_native_1 = require("react-native");
var WebSocketServerModule = react_native_1.NativeModules.WebSocketServerModule;
exports.WebsocketServer = WebSocketServerModule;
exports.WebsocketServerEmitter = new react_native_1.NativeEventEmitter(WebSocketServerModule);
exports.WS_SERVER_EVENT = "websocketServerStateChanged";
var WebsocketServerEvent;
(function (WebsocketServerEvent) {
    WebsocketServerEvent["start"] = "ServerStart";
    WebsocketServerEvent["close"] = "ServerClose";
    WebsocketServerEvent["clientConnected"] = "ClientStatusConnected";
    WebsocketServerEvent["clientClosing"] = "ClientStatusClosing";
    WebsocketServerEvent["clientClosed"] = "ClientStatusClose";
    WebsocketServerEvent["clientMessage"] = "ClientMessage";
    WebsocketServerEvent["ping"] = "ReceivePing";
    WebsocketServerEvent["pong"] = "ReceivePong";
})(WebsocketServerEvent || (exports.WebsocketServerEvent = WebsocketServerEvent = {}));
exports.WebsocketServerStatusEvent = [
    WebsocketServerEvent.start,
    WebsocketServerEvent.close,
];
exports.WebsocketClientStatusEvent = [
    WebsocketServerEvent.clientConnected,
    WebsocketServerEvent.clientClosing,
    WebsocketServerEvent.clientClosed,
];
exports.WebsocketClientMessageEvent = [WebsocketServerEvent.clientMessage];
exports.WebsocketHeartBeatEvent = [
    WebsocketServerEvent.ping,
    WebsocketServerEvent.pong,
];
//# sourceMappingURL=websocket-server.js.map