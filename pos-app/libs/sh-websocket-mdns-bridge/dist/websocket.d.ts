import { WebsocketClientEvent } from './websocket-client';
import { WebsocketServerEvent } from './websocket-server';
export type WebsocketState = 'Connecting' | 'Connected' | 'Closing' | 'Closed' | 'Destroyed';
export declare enum WebsocketStateEnum {
  Connecting = 'Connecting',
  Connected = 'Connected',
  Closing = 'Closing',
  Closed = 'Closed',
  Destroyed = 'Destroyed',
}
export type WebsocketId = string;
export type WebsocketEventType = WebsocketServerEvent | WebsocketClientEvent;
export declare enum WebsocketErrorCode {
  SocketUnknownError = 3000,
  SocketClientNeverConnectCloseException = 3001,
  SocketClientBuggyCloseException = 3002,
  SocketClientFlushCloseException = 3003,
  SocketUrlInvalidException = 3099,
  SocketDestroyedException = 3100,
  SocketNotEstablished = 3101, // server or network error
  SocketTimeoutException = 3102, // timeout
  SocketNotReadyException = 3103, // double connect
  SocketClosingException = 3104,
  SocketClosedException = 3105,
  ServerNotStopException = 3200,
  ServerNotStartedException = 3201,
}
//# sourceMappingURL=websocket.d.ts.map
