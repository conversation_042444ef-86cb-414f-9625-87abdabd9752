"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebsocketErrorCode = exports.WebsocketStateEnum = void 0;
var WebsocketStateEnum;
(function (WebsocketStateEnum) {
    WebsocketStateEnum["Connecting"] = "Connecting";
    WebsocketStateEnum["Connected"] = "Connected";
    WebsocketStateEnum["Closing"] = "Closing";
    WebsocketStateEnum["Closed"] = "Closed";
    WebsocketStateEnum["Destroyed"] = "Destroyed";
})(WebsocketStateEnum || (exports.WebsocketStateEnum = WebsocketStateEnum = {}));
var WebsocketErrorCode;
(function (WebsocketErrorCode) {
    // socket exceptions
    WebsocketErrorCode[WebsocketErrorCode["SocketUnknownError"] = 3000] = "SocketUnknownError";
    WebsocketErrorCode[WebsocketErrorCode["SocketClientNeverConnectCloseException"] = 3001] = "SocketClientNeverConnectCloseException";
    WebsocketErrorCode[WebsocketErrorCode["SocketClientBuggyCloseException"] = 3002] = "SocketClientBuggyCloseException";
    WebsocketErrorCode[WebsocketErrorCode["SocketClientFlushCloseException"] = 3003] = "SocketClientFlushCloseException";
    WebsocketErrorCode[WebsocketErrorCode["SocketUrlInvalidException"] = 3099] = "SocketUrlInvalidException";
    WebsocketErrorCode[WebsocketErrorCode["SocketDestroyedException"] = 3100] = "SocketDestroyedException";
    WebsocketErrorCode[WebsocketErrorCode["SocketNotEstablished"] = 3101] = "SocketNotEstablished";
    WebsocketErrorCode[WebsocketErrorCode["SocketTimeoutException"] = 3102] = "SocketTimeoutException";
    WebsocketErrorCode[WebsocketErrorCode["SocketNotReadyException"] = 3103] = "SocketNotReadyException";
    WebsocketErrorCode[WebsocketErrorCode["SocketClosingException"] = 3104] = "SocketClosingException";
    WebsocketErrorCode[WebsocketErrorCode["SocketClosedException"] = 3105] = "SocketClosedException";
    // server exceptions
    WebsocketErrorCode[WebsocketErrorCode["ServerNotStopException"] = 3200] = "ServerNotStopException";
    WebsocketErrorCode[WebsocketErrorCode["ServerNotStartedException"] = 3201] = "ServerNotStartedException";
})(WebsocketErrorCode || (exports.WebsocketErrorCode = WebsocketErrorCode = {}));
//# sourceMappingURL=websocket.js.map