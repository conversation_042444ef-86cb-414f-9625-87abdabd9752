# storehub-websocket

[![CI Status](https://img.shields.io/travis/<PERSON>/storehub-websocket.svg?style=flat)](https://travis-ci.org/Stephen Sun/storehub-websocket)
[![Version](https://img.shields.io/cocoapods/v/storehub-websocket.svg?style=flat)](https://cocoapods.org/pods/storehub-websocket)
[![License](https://img.shields.io/cocoapods/l/storehub-websocket.svg?style=flat)](https://cocoapods.org/pods/storehub-websocket)
[![Platform](https://img.shields.io/cocoapods/p/storehub-websocket.svg?style=flat)](https://cocoapods.org/pods/storehub-websocket)

## Example

To run the example project, clone the repo, and run `pod install` from the Example directory first.

## Requirements

## Installation

storehub-websocket is available through [CocoaPods](https://cocoapods.org). To install
it, simply add the following line to your Podfile:

```ruby
pod 'storehub-websocket'
```

## Author

<PERSON>, <EMAIL>

## License

storehub-websocket is available under the MIT license. See the LICENSE file for more info.
