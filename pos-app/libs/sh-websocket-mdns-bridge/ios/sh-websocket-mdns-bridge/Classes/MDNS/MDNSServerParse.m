//
//  MDNSServerParse.m
//  storehub-websocket
//
//  Created by <PERSON> on 2023/11/8.
//

// MDNSServerParse.m

#import "MDNSServerParse.h"
#import <Foundation/Foundation.h>
#import "SHBonjourClient.h"
#import <arpa/inet.h>

@interface MDNSServerParse () <NSNetServiceDelegate>

@property (nonatomic, copy) void (^resultCallback)(NSDictionary * _Nullable);
@property (nonatomic, strong) NSNetService *service;

@end

@implementation MDNSServerParse

- (void)parseService:(NSNetService *)service resultCallback:(void (^)(NSDictionary * _Nullable))resultCallback {
    self.resultCallback = resultCallback;
    self.service = service;
    service.delegate = self;
    [service resolveWithTimeout:10];
}

#pragma mark - NSNetServiceDelegate Methods

- (void)netServiceDidResolveAddress:(NSNetService *)sender {
    NSMutableDictionary *parseInfo = [NSMutableDictionary dictionaryWithDictionary:[SHBonjourClient parseNetService:sender]];
    
    // 解析TXT记录
    NSDictionary *txtDict = [NSNetService dictionaryFromTXTRecordData:[sender TXTRecordData]];
    NSMutableDictionary *txtRecordData = [NSMutableDictionary dictionary];
    
    for (NSString *key in txtDict) {
        NSData *data = txtDict[key];
        NSString *value = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
        if (value) {
            txtRecordData[key] = value;
        }
    }
    parseInfo[@"txtRecord"] = txtRecordData;
    
    for (NSData *addressData in sender.addresses) {
        struct sockaddr_in *socketAddress = (struct sockaddr_in *)[addressData bytes];
        if (socketAddress->sin_family == AF_INET) {
            char ipAddress[INET_ADDRSTRLEN];
            inet_ntop(AF_INET, &(socketAddress->sin_addr), ipAddress, INET_ADDRSTRLEN);
            NSString *ipString = [NSString stringWithUTF8String:ipAddress];
            
            // 解析到的ipv4有时候为 Link-Local 地址，所以过滤 Link-Local 地址，添加新字段
            if (![ipString hasPrefix:@"169.254"]) {
                parseInfo[@"ipAddress"] = ipString;
                break; // 找到一个有效的局域网 IP 地址后退出循环
            }
        }
    }
    
    if (self.resultCallback) {
        self.resultCallback(parseInfo);
    }
}


- (void)netService:(NSNetService *)sender didNotResolve:(NSDictionary<NSString *,NSNumber *> *)errorDict {
    if (self.resultCallback) {
        self.resultCallback(nil);
    }
}

- (void)netServiceDidStop:(NSNetService *)sender {
    if (self.resultCallback) {
        self.resultCallback(nil);
    }
}

@end
