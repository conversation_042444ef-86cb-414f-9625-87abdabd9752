//
//  MdnsDiscoveryModule.m
//  storehub-websocket
//
//  Created by <PERSON> on 2023/11/7.
//

#import "MdnsDiscoveryModule.h"
#import <React/RCTBridgeModule.h>
#import "NewMDNSManager.h"
#import "WebSocketConstants.h"

@interface MdnsDiscoveryModule()

@property (nonatomic, strong) NewMDNSManager *mdnsManager;

@end

@implementation MdnsDiscoveryModule

RCT_EXPORT_MODULE(MdnsDiscoveryModule);

- (dispatch_queue_t)methodQueue {
    return dispatch_get_main_queue();
}

- (NSArray<NSString *> *)supportedEvents {
    return @[MDNS_STATE_CHANGED];
}

- (instancetype)init {
    self = [super init];
    if (self) {
        self.mdnsManager = [NewMDNSManager sharedInstance];
        
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(handleMDNSServiceLostNotification:)
                                                     name:MDNS_SERVICE_LOST_NOTIFICATION
                                                   object:nil];
    }
    return self;
}

#pragma mark - Publish
RCT_EXPORT_METHOD(startService:(NSString *)serviceName servicePort:(int)servicePort extras:(NSDictionary *)extras resolve:(RCTPromiseResolveBlock)resolve reject:(RCTPromiseRejectBlock)reject) {
    [self.mdnsManager registerServiceWithName:serviceName port:servicePort attributes:extras completion:^(NSError * _Nullable error, NSString * _Nonnull serviceName, NSString * _Nonnull serviceAddress, NSInteger servicePort, NSDictionary<NSString *,NSString *> * _Nonnull extras) {
        if (error) {
            reject([NSString stringWithFormat:@"%ld", (long)error.code], error.localizedDescription, error);
        } else {
            NSDictionary *data = @{
                SERVICE_NAME: serviceName,
                SERVICE_ADDRESS: serviceAddress,
                SERVICE_PORT: @(servicePort),
                SERVICE_EXTRAS: extras
            };
            resolve(data);
        }
    }];
}

RCT_EXPORT_METHOD(stopService:(RCTPromiseResolveBlock)resolve reject:(RCTPromiseRejectBlock)reject) {
    [self.mdnsManager stopServiceWithCompletion:^(NSString * _Nonnull serviceName, NSString * _Nonnull serviceAddress, NSInteger servicePort, NSDictionary<NSString *,NSString *> * _Nonnull extras) {
        NSDictionary *data = @{
            SERVICE_NAME: serviceName,
            SERVICE_ADDRESS: serviceAddress,
            SERVICE_PORT: @(servicePort),
            SERVICE_EXTRAS: extras
        };
        resolve(data);
    }];
}

#pragma mark - Discovery
RCT_EXPORT_METHOD(startServiceDiscovery:(NSString *)serviceName resolve:(RCTPromiseResolveBlock)resolve reject:(RCTPromiseRejectBlock)reject) {
    NSLog(@"[MdnsDiscoveryModule] Start Service Discovery: Service Name - %@", serviceName);
    
    [self.mdnsManager startServiceDiscovery:serviceName
                        serverFoundCallback:^(NSString * _Nonnull serviceName,
                                              NSString * _Nonnull serviceAddress,
                                              NSInteger servicePort,
                                              NSDictionary<NSString *,NSString *> * _Nonnull extras) {
        NSLog(@"[MdnsDiscoveryModule] Service Found: %@, Address: %@, Port: %ld", serviceName, serviceAddress, (long)servicePort);
        
        NSDictionary *data = @{
            SERVICE_NAME: serviceName,
            SERVICE_ADDRESS: serviceAddress,
            SERVICE_PORT: @(servicePort),
            SERVICE_EXTRAS: extras
        };
                
        [self sendMdnsEventWithEventName:SERVICE_FOUND data:data];
    }];
    resolve(nil);
}

RCT_EXPORT_METHOD(stopServiceDiscovery:(RCTPromiseResolveBlock)resolve reject:(RCTPromiseRejectBlock)reject) {
    NSLog(@"[MdnsDiscoveryModule] Stop Service Discovery");
    
    [self.mdnsManager stopServiceDiscoveryWithCallback:^(BOOL success, NSString * _Nonnull message) {
        if (success) {
            NSLog(@"[MdnsDiscoveryModule] Service Discovery Stopped Successfully");
            
            resolve(message);
        } else {
            NSLog(@"[MdnsDiscoveryModule] Error Stopping Service Discovery: %@", message);
            
            NSError *error = [NSError errorWithDomain:@"MDNSDiscoveryErrorDomain" code:1002 userInfo:@{NSLocalizedDescriptionKey: message}];
            reject([NSString stringWithFormat:@"%ld", (long)error.code], message, error);
        }
    }];
}

RCT_EXPORT_METHOD(searchService:(NSString *)serviceName
                  timeout:(NSTimeInterval)timeout
                  resolve:(RCTPromiseResolveBlock)resolve
                  reject:(RCTPromiseRejectBlock)reject) {
    NSLog(@"[MdnsDiscoveryModule] Search Service: Service Name - %@, Timeout - %f", serviceName, timeout);
    
    [self.mdnsManager searchService:serviceName timeout:timeout serverFoundCallback:^(NSArray * _Nonnull services) {
        NSLog(@"[MdnsDiscoveryModule] Services Found: %@", services);
        
        NSMutableArray *servicesArr = [NSMutableArray array];
        for (NSDictionary *service in services) {
            NSString *serviceName = [service objectForKey:@"name"];
            NSString *serviceAddress = [service objectForKey:@"ip"];
            long servicePort = [[service objectForKey:@"port"] longValue];
            NSDictionary *extras = [service objectForKey:@"extras"];
            NSDictionary *data = @{
                SERVICE_NAME: serviceName,
                SERVICE_ADDRESS: serviceAddress,
                SERVICE_PORT: @(servicePort),
                SERVICE_EXTRAS: extras
            };
            [servicesArr addObject:data];
            // [self sendMdnsEventWithEventName:SERVICE_FOUND data:data];
        }
        // 以Promise返回为准
        resolve(servicesArr);
    } errorCallback:^(NSError * _Nonnull error) {
        NSString *errorDescription = error.userInfo[NSLocalizedDescriptionKey] ?: @"";
        NSLog(@"[MdnsDiscoveryModule] Error Searching Service: %@", errorDescription);
        
        reject([NSString stringWithFormat:@"%ld", (long)error.code], errorDescription, error);
    }];
}

#pragma mark - Other

- (void)handleMDNSServiceLostNotification:(NSNotification *)notification {
    NSDictionary *info = notification.userInfo;
    NSDictionary *data = @{
        SERVICE_NAME: [info objectForKey:@"name"],
        SERVICE_ADDRESS: [info objectForKey:@"ip"],
        SERVICE_PORT: [info objectForKey:@"port"],
        SERVICE_EXTRAS: [info objectForKey:@"extras"],
    };
    [self sendMdnsEventWithEventName:SERVICE_LOST data:data];
}

- (void)sendMdnsEventWithEventName:(NSString *)eventName data:(NSDictionary *)data {
    if (self.bridge) {
        NSLog(@"[MdnsDiscoveryModule] Sending MDNS Event: %@ with data: %@", eventName, data);
        [self sendEventWithName:MDNS_STATE_CHANGED body:@{@"event": eventName, @"service": data}];
    } else {
        NSLog(@"[MdnsDiscoveryModule] Error: Bridge not found while sending event: %@", eventName);
    }
}

@end
