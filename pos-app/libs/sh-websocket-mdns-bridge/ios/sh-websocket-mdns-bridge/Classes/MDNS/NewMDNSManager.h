//
//  NewMDNSManager.h
//  storehub-websocket
//
//  Created by <PERSON> on 2023/11/8.
//

// NewMDNSManager.h

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef void (^ServerFoundCallbackType)(NSString *serviceName, NSString *serviceAddress, NSInteger servicePort, NSDictionary<NSString *, NSString *> *extras);
typedef void (^ServiceDiscoveryErrorCallbackType)(NSError *error);

@interface NewMDNSManager : NSObject

+ (instancetype)sharedInstance;

- (void)registerServiceWithName:(NSString *)serviceName
                           port:(int)port
                     attributes:(NSDictionary<NSString *, NSString *> *)attrs
                     completion:(void (^)(NSError * _Nullable error, NSString *serviceName, NSString *serviceAddress, NSInteger servicePort, NSDictionary<NSString *, NSString *> *extras))completion;
- (void)stopServiceWithCompletion:(void (^)(NSString *serviceName, NSString *serviceAddress, NSInteger servicePort, NSDictionary<NSString *, NSString *> *extras))completion;

- (void)startServiceDiscovery:(NSString *)serviceName serverFoundCallback:(nullable ServerFoundCallbackType)callback;
- (void)stopServiceDiscoveryWithCallback:(void (^)(BOOL success, NSString *message))callback;
- (void)searchService:(NSString *)serviceName
              timeout:(NSTimeInterval)timeout
  serverFoundCallback:(nullable void (^)(NSArray *))callback
        errorCallback:(nullable ServiceDiscoveryErrorCallbackType)errorCallback;

@end

NS_ASSUME_NONNULL_END

