//
//  NewMDNSManager.m
//  storehub-websocket
//
//  Created by <PERSON> on 2023/11/8.
//

// NewMDNSManager.m

#import "NewMDNSManager.h"
#import "MDNSServerParse.h"
#import "SHBonjourClient.h"
#import "WebSocketConstants.h"
#import "SHBonjourServer.h"

@interface NewMDNSManager () <SHBonjourClientDelegate, SHBonjourServerDelegate>

@property (nonatomic, strong) SHBonjourClient *bonjourClient;
@property (nonatomic, strong) SHBonjourServer *bonjourServer;
@property (nonatomic, copy) NSString *mDiscoverServerName;
@property (nonatomic, assign) BOOL isDiscovering;
@property (nonatomic, copy) ServerFoundCallbackType serverFoundCallback;
@property (nonatomic, copy) void (^publishCallback)(NSError * _Nullable error, NSString *serviceName, NSString *serviceAddress, NSInteger servicePort, NSDictionary<NSString *, NSString *> *extras);
@property (nonatomic, copy) void (^stopPublishCallback)(NSString *serviceName, NSString *serviceAddress, NSInteger servicePort, NSDictionary<NSString *, NSString *> *extras);
@property (nonatomic, strong) NSMutableArray<MDNSServerParse *> *activeParsers;

@property (nonatomic, strong) dispatch_queue_t serverParseQueue;
@property (nonatomic, assign) BOOL isPublished;

@end

@implementation NewMDNSManager

+ (instancetype)sharedInstance {
    static NewMDNSManager *sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sharedInstance = [[self alloc] init];
        sharedInstance.serverParseQueue = dispatch_queue_create("com.mdns.parseServer", NULL);
        sharedInstance.isDiscovering = NO;
        sharedInstance.activeParsers = [NSMutableArray array];
    });
    return sharedInstance;
}

#pragma mark - Publish
- (void)registerServiceWithName:(NSString *)serviceName
                           port:(int)port
                     attributes:(NSDictionary *)attrs
                     completion:(void (^)(NSError * _Nullable error, NSString *serviceName, NSString *serviceAddress, NSInteger servicePort, NSDictionary<NSString *, NSString *> *extras))completion {
    NSMutableDictionary *mutableDict = attrs.mutableCopy;
    [mutableDict setObject:[NSString stringWithFormat:@"%d", port] forKey:@"wsServerPort"];
    
    self.bonjourServer = [[SHBonjourServer alloc] initWithDomainName:MDNS_DOMAIN
                                                                type:MDNS_TYPE
                                                                name:serviceName
                                                                port:0
                                                           txtRecord:mutableDict];
    self.bonjourServer.serverDelegate = self;
    
    BOOL success = [self.bonjourServer start];
    self.isPublished = NO;
    if (!success) {
        // 开启发布服务失败
        NSError *error = [NSError errorWithDomain:@"SHBonjourServerErrorDomain"
                                             code:1001
                                         userInfo:@{NSLocalizedDescriptionKey: @"Failed to start Bonjour service"}];
        if (completion) {
            completion(error, nil, nil, 0, nil);
        }
        return;
    }
    self.publishCallback = completion;
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(5.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        if (!self.isPublished) {
            NSLog(@"[NewMDNSManager] Detected publish failure, retrying...");
            [self.bonjourServer stop];
            [self registerServiceWithName:serviceName port:port attributes:attrs completion:self.publishCallback];
        }
    });
}

- (void)stopServiceWithCompletion:(void (^)(NSString *serviceName, NSString *serviceAddress, NSInteger servicePort, NSDictionary<NSString *, NSString *> *extras))completion {
    if (self.bonjourServer) {
        self.stopPublishCallback = completion;
        [self.bonjourServer stop];
    }
}

#pragma mark - Discovery

// 应用一启动就会一直开启搜索
- (void)startServiceDiscovery:(NSString *)serviceName serverFoundCallback:(nullable ServerFoundCallbackType)callback {
    NSLog(@"🥳 startServiceDiscovery");
    if (!self.isDiscovering) {
        NSLog(@"🥳 Bonjour Client startDiscoverServer() called with name = %@", serviceName);
        
        self.mDiscoverServerName = serviceName;
        self.bonjourClient = [[SHBonjourClient alloc] initWithDomainName:MDNS_DOMAIN type:MDNS_TYPE name:serviceName];
        self.bonjourClient.clientDelegate = self;
        self.isDiscovering = YES;
        self.serverFoundCallback = callback;
        
        [self.bonjourClient start];
    } else {
        NSLog(@"🥳 Bonjour Client is already running, rediscover");
        self.mDiscoverServerName = serviceName;
        self.bonjourClient = [[SHBonjourClient alloc] initWithDomainName:MDNS_DOMAIN type:MDNS_TYPE name:serviceName];
        self.bonjourClient.clientDelegate = self;
        self.serverFoundCallback = callback;
        
        [self.bonjourClient start];
    }
}

- (void)stopServiceDiscoveryWithCallback:(void (^)(BOOL success, NSString *message))callback {
    if (self.isDiscovering) {
        @try {
            [self.bonjourClient stop];
            self.bonjourClient.clientDelegate = nil;
            self.bonjourClient = nil;
            self.isDiscovering = NO;
            
            // 成功停止服务发现
            if (callback) {
                callback(YES, @"Service discovery stopped successfully.");
            }
        }
        @catch (NSException *exception) {
            self.isDiscovering = NO;
            if (callback) {
                callback(NO, [NSString stringWithFormat:@"Error stopping service discovery: %@", exception.reason]);
            }
        }
    } else {
        if (callback) {
            callback(NO, @"Service discovery was not running.");
        }
    }
}

// 位于startServiceDiscovery之后，手动触发的单次搜索
- (void)searchService:(NSString *)serviceName
              timeout:(NSTimeInterval)timeout
  serverFoundCallback:(nullable void (^)(NSArray *))callback
        errorCallback:(nullable ServiceDiscoveryErrorCallbackType)errorCallback {
    
    // 存储所有发现的 services
    NSMutableArray<NSDictionary *> *discoveredServices = [NSMutableArray array];
    
    [self startServiceDiscovery:serviceName serverFoundCallback:^(NSString *name, NSString *ip, NSInteger port, NSDictionary<NSString *, NSString *> *extras) {
        NSDictionary *serviceInfo = @{@"name": name, @"ip": ip, @"port": @(port), @"extras": extras};
        [discoveredServices addObject:serviceInfo];
    }];
    
    dispatch_time_t delay = dispatch_time(DISPATCH_TIME_NOW, (int64_t)(timeout * NSEC_PER_SEC));
    dispatch_after(delay, dispatch_get_main_queue(), ^{
        // 如果仍然在服务发现中，说明已经到达时间
        if (self.isDiscovering) {
            [self stopServiceDiscoveryWithCallback:^(BOOL success, NSString *message) {
                if (!success) {
                    // 停止失败
                    if (errorCallback) {
                        NSError *error = [NSError errorWithDomain:@"MDNSDiscoveryErrorDomain"
                                                             code:1002
                                                         userInfo:@{NSLocalizedDescriptionKey: @"Service discovery has reached time and stop failed"}];
                        errorCallback(error);
                    }
                }
            }];
        }
        if (callback) {
            callback(discoveredServices);
        }
    });
}

#pragma mark - SHBonjourClientDelegate Methods
- (void)foundNetService:(nonnull NSNetService *)service {
    MDNSServerParse *parser = [MDNSServerParse new];
    [self.activeParsers addObject:parser];
    
    __weak typeof(self) weakSelf = self;
    [parser parseService:service resultCallback:^(NSDictionary * _Nullable ipInfo) {
        __strong typeof(weakSelf) strongSelf = weakSelf;
        if (ipInfo) {
            [strongSelf foundServer:ipInfo];
        }
        [strongSelf.activeParsers removeObject:parser];
    }];
}

- (void)serviceLost:(NSNetService *)service {
    MDNSServerParse *parser = [MDNSServerParse new];
    [self.activeParsers addObject:parser];
    
    __weak typeof(self) weakSelf = self;
    [parser parseService:service resultCallback:^(NSDictionary * _Nullable ipInfo) {
        __strong typeof(weakSelf) strongSelf = weakSelf;
        if (ipInfo) {
            NSDictionary *serviceDetails = [self extractServiceDetails:ipInfo];
            NSString *ip = serviceDetails[@"ip"];
            NSString *name = serviceDetails[@"name"];
            int port = [serviceDetails[@"port"] intValue];
            NSDictionary<NSString *, NSString *> *extras = serviceDetails[@"extras"];
            
            [self notifyEventWithName:MDNS_SERVICE_LOST_NOTIFICATION info:@{
                @"ip": ip,
                @"name": name,
                @"port": @(port),
                @"extras": extras
            }];
        }
        [strongSelf.activeParsers removeObject:parser];
    }];
}

- (void)foundServer:(NSDictionary *)ipInfo {
    NSDictionary *serviceDetails = [self extractServiceDetails:ipInfo];
    NSString *ip = serviceDetails[@"ip"];
    NSString *name = serviceDetails[@"name"];
    int port = [serviceDetails[@"port"] intValue];
    NSDictionary<NSString *, NSString *> *extras = serviceDetails[@"extras"];

    if (self.serverFoundCallback) {
        self.serverFoundCallback(name, ip, port, extras);
    }
}

- (NSDictionary *)extractServiceDetails:(NSDictionary *)ipInfo {
    NSString *ip = ipInfo[@"ipAddress"] ?: @"";
    // Android use DNS service's port
    int portFromIpInfo = [ipInfo[@"port"] intValue] ?: 0;
    NSString *name = ipInfo[@"name"] ?: @"";
    NSDictionary<NSString *, NSString *> *extras = ipInfo[@"txtRecord"] ?: @{};
    // iOS use port from txtRecord of DNS service
    int portFromTxtRecord = [[extras objectForKey:@"wsServerPort"] intValue];
    int port = portFromTxtRecord != 0 ? portFromTxtRecord : portFromIpInfo;

    return @{
        @"ip": ip,
        @"name": name,
        @"port": @(port),
        @"extras": extras
    };
}

- (void)notifyEventWithName:(NSString *)name info:(NSDictionary *)info {
    [[NSNotificationCenter defaultCenter] postNotificationName:name object:nil userInfo:info];
}

#pragma mark - SHBonjourServerDelegate
- (void)netServiceDidPublish:(NSNetService *)sender {
    self.isPublished = YES;
    NSLog(@"[NewMDNSManager] ServiceDidPublish ✅");
    
    MDNSServerParse *parser = [MDNSServerParse new];
    __weak typeof(self) weakSelf = self;
    [parser parseService:sender resultCallback:^(NSDictionary * _Nullable ipInfo) {
        __strong typeof(weakSelf) strongSelf = weakSelf;
        if (ipInfo) {
            [strongSelf resolveServer:ipInfo];
        }
    }];
}

- (void)netService:(NSNetService *)sender didNotPublish:(NSDictionary<NSString *,NSNumber *> *)errorDict {
    if (self.publishCallback) {
        NSError *error = [NSError errorWithDomain:@"SHBonjourServerErrorDomain"
                                             code:1001
                                         userInfo:@{NSLocalizedDescriptionKey: @"Failed to start Bonjour service"}];
        self.publishCallback(error, nil, nil, 0, nil);
    }
}

- (void)netServiceDidStop:(NSNetService *)sender {
    MDNSServerParse *parser = [MDNSServerParse new];
    __weak typeof(self) weakSelf = self;
    [parser parseService:sender resultCallback:^(NSDictionary * _Nullable ipInfo) {
        __strong typeof(weakSelf) strongSelf = weakSelf;
        if (ipInfo) {
            [strongSelf resoleNetService:ipInfo completion:^(NSString *name, NSString *ip, NSInteger port, NSDictionary<NSString *,NSString *> *extras) {
                if (self.stopPublishCallback) {
                    self.stopPublishCallback(name, ip, port, extras);
                }
            }];
        }
    }];
}

- (void)resolveServer:(NSDictionary *)ipInfo {
    NSString *ip = ipInfo[@"ipv4"] ?: @"";
    NSNumber *port = ipInfo[@"port"] ?: @(0);
    NSString *name = ipInfo[@"name"] ?: @"";
    NSDictionary<NSString *, NSString *> *extras = ipInfo[@"txtRecord"] ?: @{};
    
    if (self.publishCallback) {
        self.publishCallback(nil, name, ip, [port integerValue], extras);
    }
}

- (void)resoleNetService:(NSDictionary *)ipInfo completion:(void (^)(NSString *name, NSString *ip, NSInteger port, NSDictionary<NSString *, NSString *> *extras))completion {
    NSString *ip = ipInfo[@"ipv4"] ?: @"";
    NSNumber *port = ipInfo[@"port"] ?: @(0);
    NSString *name = ipInfo[@"name"] ?: @"";
    NSDictionary<NSString *, NSString *> *extras = ipInfo[@"txtRecord"] ?: @{};
    
    if (completion) {
        completion(name, ip, [port integerValue], extras);
    }
}

@end

