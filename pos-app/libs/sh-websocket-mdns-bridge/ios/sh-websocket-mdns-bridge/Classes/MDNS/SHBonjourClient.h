//
//  SFBonjourClient.h
//  iOS-Bonjour-Demo
//
//  Created by <PERSON><PERSON> on 2018/11/16.
//  Copyright © 2018 Jakey. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@protocol SHBonjourClientDelegate <NSObject>
- (void)foundNetService:(NSNetService *)service;
- (void)serviceLost:(NSNetService *)service;
@end


@interface SHBonjourClient : NSObject

@property(weak, nonatomic) id<SHBonjourClientDelegate> clientDelegate;

+ (NSDictionary *)parseNetService: (NSNetService *)service;

- (instancetype)init;
- (instancetype)initWithType:(NSString *)type;
- (instancetype)initWithDomainName:(NSString *)domain
                              type:(NSString *)type
                              name:(NSString *)name;
- (BOOL)start;
- (BOOL)stop;
@end

NS_ASSUME_NONNULL_END
