//
//  SFBonjourClient.m
//  iOS-Bonjour-Demo
//
//  Created by <PERSON><PERSON> on 2018/11/16.
//  Copyright © 2018 Jakey. All rights reserved.
//

#import "SHBonjourClient.h"
#include <arpa/inet.h>

NSString * const SHBonjourClientDefaultType = @"_SFBonjour._tcp.";
NSString * const SHBonjourClientErrorDomain = @"ServerErrorDomain";

@interface SHBonjourClient ()<NSNetServiceBrowserDelegate>
@property (nonatomic,copy) NSString *domain;
@property (nonatomic,copy) NSString *type;
@property (nonatomic,copy) NSString *name;
@property (nonatomic,strong) NSNetServiceBrowser *browser;

@end
@implementation SHBonjourClient
- (instancetype)init {
    return [self initWithType:SHBonjourClientDefaultType];
}


- (instancetype)initWithType:(NSString *)type {
    return [self initWithDomainName:@"local."
                               type:type
                               name:@""];
}

- (instancetype)initWithDomainName:(NSString *)domain
                              type:(NSString *)type
                              name:(NSString *)name{
    self = [super init];
    if (self) {
        self.domain = domain;
        self.type = [NSString stringWithFormat:@"_%@._tcp.", type];
        self.name = name;
    }
    return self;
}
- (BOOL)start{
    self.browser =[[NSNetServiceBrowser alloc] init];

    if (self.browser) {
       
        self.browser.delegate = self;
        
        [self.browser scheduleInRunLoop: [NSRunLoop mainRunLoop]
                                forMode:NSRunLoopCommonModes];
        [self.browser searchForServicesOfType:self.type inDomain:self.domain];

        return  YES;
    }
    return NO;
}
- (BOOL)stop{
    [self.browser stop];
    [self.browser removeFromRunLoop:[NSRunLoop mainRunLoop]  forMode:NSRunLoopCommonModes];
    self.browser = nil;
    return YES;
}

#pragma --mark -- Browser delegate

/* Sent to the NSNetServiceBrowser instance's delegate before the instance begins a search. The delegate will not receive this message if the instance is unable to begin a search. Instead, the delegate will receive the -netServiceBrowser:didNotSearch: message.
 */
- (void)netServiceBrowserWillSearch:(NSNetServiceBrowser *)browser{
    NSLog(@"%@",NSStringFromSelector(_cmd));
    

}

/* Sent to the NSNetServiceBrowser instance's delegate when the instance's previous running search request has stopped.
 */
- (void)netServiceBrowserDidStopSearch:(NSNetServiceBrowser *)browser{
    NSLog(@"%@",NSStringFromSelector(_cmd));
    // case STOP_MDNS_SERVICE = "stop mdns";
    // case Succeed = "Succeed"
}

/* Sent to the NSNetServiceBrowser instance's delegate when an error in searching for domains or services has occurred. The error dictionary will contain two key/value pairs representing the error domain and code (see the NSNetServicesError enumeration above for error code constants). It is possible for an error to occur after a search has been started successfully.
 */
- (void)netServiceBrowser:(NSNetServiceBrowser *)browser didNotSearch:(NSDictionary<NSString *, NSNumber *> *)errorDict{
    NSLog(@"%@",NSStringFromSelector(_cmd));
    
    //  case START_DISCOVERY = "start discovery";
    //  case Failed = "Failed"
    NSString *errorStr = [NSString stringWithFormat:@"netServiceBrowser didNotSearch %@", errorDict];
}

/* Sent to the NSNetServiceBrowser instance's delegate for each domain discovered. If there are more domains, moreComing will be YES. If for some reason handling discovered domains requires significant processing, accumulating domains until moreComing is NO and then doing the processing in bulk fashion may be desirable.
 */
- (void)netServiceBrowser:(NSNetServiceBrowser *)browser didFindDomain:(NSString *)domainString moreComing:(BOOL)moreComing{
    NSLog(@"%@",NSStringFromSelector(_cmd));

}

/* Sent to the NSNetServiceBrowser instance's delegate for each service discovered. If there are more services, moreComing will be YES. If for some reason handling discovered services requires significant processing, accumulating services until moreComing is NO and then doing the processing in bulk fashion may be desirable.
 */
- (void)netServiceBrowser:(NSNetServiceBrowser *)browser didFindService:(NSNetService *)service moreComing:(BOOL)moreComing{
    NSLog(@"%@",NSStringFromSelector(_cmd));
    
    if (self.clientDelegate) {
        [self.clientDelegate foundNetService:service];
    }
}

/* Sent to the NSNetServiceBrowser instance's delegate when a previously discovered domain is no longer available.
 */
- (void)netServiceBrowser:(NSNetServiceBrowser *)browser didRemoveDomain:(NSString *)domainString moreComing:(BOOL)moreComing{
    NSLog(@"%@",NSStringFromSelector(_cmd));

}

/* Sent to the NSNetServiceBrowser instance's delegate when a previously discovered service is no longer published.
 */
- (void)netServiceBrowser:(NSNetServiceBrowser *)browser didRemoveService:(NSNetService *)service moreComing:(BOOL)moreComing{
    NSLog(@"%@",NSStringFromSelector(_cmd));
    if (self.clientDelegate) {
        [self.clientDelegate serviceLost:service];
    }
}

- (NSDictionary *)parsingIP:(NSNetService *)sender{
    int sPort = 0;
    NSString *ipv4;
    NSString *ipv6;
    
    for (NSData *address in [sender addresses]) {
        typedef union {
            struct sockaddr sa;
            struct sockaddr_in ipv4;
            struct sockaddr_in6 ipv6;
        } ip_socket_address;
        
        struct sockaddr *socketAddr = (struct sockaddr*)[address bytes];
        if(socketAddr->sa_family == AF_INET) {
            sPort = ntohs(((struct sockaddr_in *)socketAddr)->sin_port);
            struct sockaddr_in* pV4Addr = (struct sockaddr_in*)socketAddr;
            int ipAddr = pV4Addr->sin_addr.s_addr;
            char str[INET_ADDRSTRLEN];
            ipv4 = [NSString stringWithUTF8String:inet_ntop( AF_INET, &ipAddr, str, INET_ADDRSTRLEN )];
        }
        
        else if(socketAddr->sa_family == AF_INET6) {
            sPort = ntohs(((struct sockaddr_in6 *)socketAddr)->sin6_port);
            struct sockaddr_in6* pV6Addr = (struct sockaddr_in6*)socketAddr;
            char str[INET6_ADDRSTRLEN];
            ipv6 = [NSString stringWithUTF8String:inet_ntop( AF_INET6, &pV6Addr->sin6_addr, str, INET6_ADDRSTRLEN )];
        }
        else {
            NSLog(@"Socket Family neither IPv4 or IPv6, can't handle...");
        }
    }
    
    NSDictionary *data = @{@"type": [sender type],
                           @"domain": [sender domain],
                           @"name": [sender name],
                           @"ipv4": ipv4,
                           @"ipv6": ipv6,
                           @"port": [NSNumber numberWithInt:sPort]};
    return data;
}

+ (NSDictionary *)parseNetService: (NSNetService *)service {
    int sPort = 0;
    NSString *ipv4 = @"";
    NSString *ipv6 = @"";
    
    for (NSData *address in [service addresses]) {
        typedef union {
            struct sockaddr sa;
            struct sockaddr_in ipv4;
            struct sockaddr_in6 ipv6;
        } ip_socket_address;
        
        struct sockaddr *socketAddr = (struct sockaddr*)[address bytes];
        if(socketAddr->sa_family == AF_INET) {
            sPort = ntohs(((struct sockaddr_in *)socketAddr)->sin_port);
            struct sockaddr_in* pV4Addr = (struct sockaddr_in*)socketAddr;
            int ipAddr = pV4Addr->sin_addr.s_addr;
            char str[INET_ADDRSTRLEN];
            ipv4 = [NSString stringWithUTF8String:inet_ntop( AF_INET, &ipAddr, str, INET_ADDRSTRLEN )];
        }
        
        else if(socketAddr->sa_family == AF_INET6) {
            sPort = ntohs(((struct sockaddr_in6 *)socketAddr)->sin6_port);
            struct sockaddr_in6* pV6Addr = (struct sockaddr_in6*)socketAddr;
            char str[INET6_ADDRSTRLEN];
            ipv6 = [NSString stringWithUTF8String:inet_ntop( AF_INET6, &pV6Addr->sin6_addr, str, INET6_ADDRSTRLEN )];
        }
        else {
            NSLog(@"Socket Family neither IPv4 or IPv6, can't handle...");
        }
    }
    
    NSNumber *defaultPort = [NSNumber numberWithInt:0];
    NSNumber *newSPort = [NSNumber numberWithInt: sPort];
    if (newSPort != nil) {
        defaultPort = newSPort;
    }
    
    // need to check if key is empty will insert nil that will cause crash
    NSDictionary *data = @{@"type": [service type],
                           @"domain": [service domain],
                           @"name": [service name],
                           @"ipv4": ipv4,
                           @"ipv6": ipv6,
                           @"port": defaultPort};
    return data;
}
@end
