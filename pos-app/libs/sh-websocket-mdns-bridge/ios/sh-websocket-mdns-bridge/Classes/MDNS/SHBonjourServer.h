//
//  SFBonjourServer.h
//  iOS-Bonjour-Demo
//
//  Created by <PERSON><PERSON> on 2018/11/16.
//  Copyright © 2018 Jakey. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@protocol SHBonjourServerDelegate <NSObject>
- (void)netServiceDidPublish:(NSNetService *)sender;
- (void)netService:(NSNetService *)sender didNotPublish:(NSDictionary<NSString *, NSNumber *> *)errorDict;
- (void)netServiceDidStop:(NSNetService *)sender;
@end

@interface SHBonjourServer : NSObject

@property(weak, nonatomic) id<SHBonjourServerDelegate> serverDelegate;

- (instancetype)init;
- (instancetype)initWithType:(NSString *)type;
- (instancetype)initWithDomainName:(NSString *)domain
                              type:(NSString *)type
                              name:(NSString *)name;
- (instancetype)initWithDomainName:(NSString *)domain
                              type:(NSString *)type
                              name:(NSString *)name
                              port:(int)port;
// 新增
- (instancetype)initWithDomainName:(NSString *)domain
                              type:(NSString *)type
                              name:(NSString *)name
                              port:(int)port
                         txtRecord:(NSDictionary<NSString *, NSString *> *)txtRecord;
- (BOOL)start;
- (BOOL)stop;
@end

NS_ASSUME_NONNULL_END
