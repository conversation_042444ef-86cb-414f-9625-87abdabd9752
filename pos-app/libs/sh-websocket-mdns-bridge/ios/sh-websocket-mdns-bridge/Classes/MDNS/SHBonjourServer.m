//
//  SHBonjourServer.m
//  iOS-Bonjour-Demo
//
//  Created by <PERSON><PERSON> on 2018/11/16.
//  Copyright © 2018 Jakey. All rights reserved.
//

#import "SHBonjourServer.h"

NSString * const SHBonjourDefaultType = @"_SFBonjour._tcp.";
NSString * const SHBonjourServerErrorDomain = @"ServerErrorDomain";
int   const SHBonjourDefaultPort = 2333;

@interface SHBonjourServer ()<NSNetServiceDelegate>
@property (nonatomic,copy) NSString *domain;
@property (nonatomic,copy) NSString *type;
@property (nonatomic,copy) NSString *name;
@property (nonatomic,assign) int port;
@property (nonatomic,strong) NSNetService *service;
@property (nonatomic, strong) NSDictionary<NSString *, NSString *> *txtRecordDict;
@end
@implementation SHBonjourServer

- (instancetype)init {
    return [self initWithType:SHBonjourDefaultType];
}

- (instancetype)initWithType:(NSString *)type {
    return [self initWithDomainName:@"local."
                               type:type
                               name:@"SHBonjourServer"];
}

- (instancetype)initWithDomainName:(NSString *)domain
                          type:(NSString *)type
                              name:(NSString *)name {
    return [self initWithDomainName:domain
                               type:type
                               name:name
                               port:SHBonjourDefaultPort];
}
- (instancetype)initWithDomainName:(NSString *)domain
                              type:(NSString *)type
                              name:(NSString *)name
                              port:(int)port {
    self = [super init];
    if (self) {
        self.domain = domain;
        self.type = [NSString stringWithFormat:@"_%@._tcp.", type];
        self.name = name;
        self.port = port;
    }
    return self;
}

- (instancetype)initWithDomainName:(NSString *)domain
                              type:(NSString *)type
                              name:(NSString *)name
                              port:(int)port
                          txtRecord:(NSDictionary<NSString *, NSString *> *)txtRecord {
    self = [super init];
    if (self) {
        self.domain = domain;
        self.type = [NSString stringWithFormat:@"_%@._tcp.", type];
        self.name = name;
        self.port = port;
        self.txtRecordDict = txtRecord;
    }
    return self;
}

- (BOOL)start{
    self.service =[[NSNetService alloc] initWithDomain:self.domain type:self.type name:self.name port:self.port];
    
    if (self.service) {
        [self.service scheduleInRunLoop:[NSRunLoop currentRunLoop]
                                forMode:NSRunLoopCommonModes];
        
        if (self.txtRecordDict) {
            NSMutableDictionary<NSString *, NSData *> *txtRecordDataDict = [NSMutableDictionary dictionary];
            [self.txtRecordDict enumerateKeysAndObjectsUsingBlock:^(NSString *key, NSString *value, BOOL *stop) {
                txtRecordDataDict[key] = [value dataUsingEncoding:NSUTF8StringEncoding];
            }];

            NSData *txtData = [NSNetService dataFromTXTRecordDictionary:txtRecordDataDict];
            [self.service setTXTRecordData:txtData];
        } else {
            NSData *data = [NSNetService dataFromTXTRecordDictionary:@{@"node":[NSNull null]}];
            [self.service setTXTRecordData:data];
        }
        
        [self.service publishWithOptions: NSNetServiceListenForConnections];
        self.service.delegate = self;
        return  YES;
    }
    return NO;
}
- (BOOL)stop{
    [self.service stop];
    [self.service removeFromRunLoop:[NSRunLoop currentRunLoop]  forMode:NSRunLoopCommonModes];
    self.service = nil;
    return YES;
}

#pragma --mark service delegate

/* Sent to the NSNetService instance's delegate prior to advertising the service on the network. If for some reason the service cannot be published, the delegate will not receive this message, and an error will be delivered to the delegate via the delegate's -netService:didNotPublish: method.
 */
- (void)netServiceWillPublish:(NSNetService *)sender{
    NSLog(@"netServiceWillPublish");
}

/* Sent to the NSNetService instance's delegate when the publication of the instance is complete and successful.
 */
- (void)netServiceDidPublish:(NSNetService *)sender{
    NSLog(@"netServiceDidPublish");
    //  case START_MDNS_SERVICE = "start mdns";
    //  case Succeed = "Succeed"
    if (self.serverDelegate) {
        [self.serverDelegate netServiceDidPublish:sender];
    }
}

/* Sent to the NSNetService instance's delegate when an error in publishing the instance occurs. The error dictionary will contain two key/value pairs representing the error domain and code (see the NSNetServicesError enumeration above for error code constants). It is possible for an error to occur after a successful publication.
 */
- (void)netService:(NSNetService *)sender didNotPublish:(NSDictionary<NSString *, NSNumber *> *)errorDict{
    NSLog(@"%@", errorDict);
    NSLog(@"didNotPublish");
    
    //  case START_MDNS_SERVICE = "start mdns";
    //  case Succeed = "Failed"
    NSString *errorStr = [NSString stringWithFormat:@"netService didNotPublish %@", errorDict];
    
    if (self.serverDelegate) {
        [self.serverDelegate netService:sender didNotPublish:errorDict];
    }
}


/* Sent to the NSNetService instance's delegate when the instance's previously running publication or resolution request has stopped.
 */
- (void)netServiceDidStop:(NSNetService *)sender{
    NSLog(@"netServiceDidStop");
    // case STOP_MDNS_SERVICE = "stop mdns";
    // Succeed = "Succeed"
    if (self.serverDelegate) {
        [self.serverDelegate netServiceDidStop:sender];
    }
}

/* Sent to the NSNetService instance's delegate when the instance is being monitored and the instance's TXT record has been updated. The new record is contained in the data parameter.
 */
- (void)netService:(NSNetService *)sender didUpdateTXTRecordData:(NSData *)data{
    NSLog(@"didUpdateTXTRecordData");

}


/* Sent to a published NSNetService instance's delegate when a new connection is
 * received. Before you can communicate with the connecting client, you must -open
 * and schedule the streams. To reject a connection, just -open both streams and
 * then immediately -close them.
 
 * To enable TLS on the stream, set the various TLS settings using
 * kCFStreamPropertySSLSettings before calling -open. You must also specify
 * kCFBooleanTrue for kCFStreamSSLIsServer in the settings dictionary along with
 * a valid SecIdentityRef as the first entry of kCFStreamSSLCertificates.
 */
- (void)netService:(NSNetService *)sender didAcceptConnectionWithInputStream:(NSInputStream *)inputStream outputStream:(NSOutputStream *)outputStream{
    NSLog(@"didAcceptConnectionWithInputStream");

}

@end
