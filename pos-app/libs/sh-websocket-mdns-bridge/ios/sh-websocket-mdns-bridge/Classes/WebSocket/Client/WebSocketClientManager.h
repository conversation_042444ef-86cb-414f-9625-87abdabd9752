//
//  WebSocketClientManager.h
//  storehub-websocket
//
//  Created by <PERSON> on 2023/11/9.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface WebSocketClientManager : NSObject

+ (instancetype)sharedInstance;

- (void)create:(NSDictionary *)options completion:(void (^)(NSString * _Nullable socketID, NSError * _Nullable error))completion;
- (void)connect:(NSString *)socketId completion:(void(^)(NSError *error))completion;
- (void)send:(NSString *)socketID message:(NSString *)message completion:(void(^)(NSError *error))completion;
- (void)close:(NSString *)socketID completion:(void(^)(NSError *error))completion;
- (void)exitApp;
- (NSString *)getSocketState:(NSString *)socketID;

@end

NS_ASSUME_NONNULL_END
