//
//  WebSocketClientManager.m
//  storehub-websocket
//
//  Created by <PERSON> on 2023/11/9.
//

#import "WebSocketClientManager.h"
#import <SocketRocket/SRWebSocket.h>
#import "WebSocketConstants.h"

#define HeartBeatInterval 5.0
#define PongDisconnectInterval 10.0

@interface WebSocketClientManager () <SRWebSocketDelegate>

@property (nonatomic, strong) NSMutableDictionary<NSString *, SRWebSocket *> *connections;
@property (nonatomic, strong) NSMutableDictionary<NSString *, void(^)(NSError *error)> *connectionCompletionBlocks;
@property (nonatomic, strong) NSMutableDictionary<NSString *, void(^)(NSError *error)> *closeCompletionBlocks;
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSTimer *> *heartBeatTimers;
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSTimer *> *pongTimers;

@end

@implementation WebSocketClientManager

+ (instancetype)sharedInstance {
    static WebSocketClientManager *sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sharedInstance = [[self alloc] init];
        sharedInstance.connections = [NSMutableDictionary new];
        sharedInstance.connectionCompletionBlocks = [NSMutableDictionary new];
        sharedInstance.closeCompletionBlocks = [NSMutableDictionary new];
        sharedInstance.heartBeatTimers = [NSMutableDictionary new];
        sharedInstance.pongTimers = [NSMutableDictionary new];
    });
    return sharedInstance;
}

// 创建新的 WebSocket 连接
- (void)create:(NSDictionary *)options completion:(void (^)(NSString * _Nullable socketID, NSError * _Nullable error))completion {
    NSString *remoteAddress = options[@"remoteAddress"];
    NSString *remotePort = options[@"remotePort"];
    NSString *payload = options[@"payload"]; // payload to be included in the headers

    if (remoteAddress && remotePort) {
        // ws 表示 WebSocket 协议
        // wss 表示加密的 WebSocket 协议（WebSocket Secure），类似于 HTTPS
        NSString *urlString = [NSString stringWithFormat:@"ws://%@:%@", remoteAddress, remotePort];
        NSURL *url = [NSURL URLWithString:urlString];

        NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:url
                                                               cachePolicy:NSURLRequestUseProtocolCachePolicy
                                                           timeoutInterval:CONNECT_TIMEOUT];

        NSString *socketId = [[NSUUID UUID] UUIDString];

        // Add payload and socket ID to the headers
        [request setValue:socketId forHTTPHeaderField:X_SOCKETID];
        [request setValue:payload forHTTPHeaderField:X_PAYLOAD];

        // SocketRocket 默认遵循最新标准的WebSocket协议RFC 6455
        SRWebSocket *webSocket = [[SRWebSocket alloc] initWithURLRequest:request];
        
        // Save the socket with its ID
        self.connections[socketId] = webSocket;
        
        if (completion) {
            completion(socketId, nil);
        }
    } else {
        NSError *error = [NSError errorWithDomain:@"WebSocketClientManager" code:3099 userInfo:@{NSLocalizedDescriptionKey: @"Socket url is invalid"}];
        if (completion) {
            completion(nil, error);
        }
    }
}

// 连接 WebSocket
- (void)connect:(NSString *)socketId completion:(void(^)(NSError *error))completion {
    SRWebSocket *webSocket = self.connections[socketId];
    if (webSocket) {
        webSocket.delegate = self;
        self.connectionCompletionBlocks[socketId] = completion;
        // 开始连接
        [webSocket open];
    } else {
        if (completion) {
            NSError *error = [NSError errorWithDomain:@"WebSocketError" code:3100 userInfo:@{NSLocalizedDescriptionKey: @"SOCKET_DESTROYED"}];
            completion(error);
        }
    }
}

// 发送消息
- (void)send:(NSString *)socketID message:(NSString *)message completion:(void(^)(NSError *error))completion {
    SRWebSocket *webSocket = self.connections[socketID];
    if (webSocket) {
        NSError *sendError = nil;
        BOOL sendSuccess = [webSocket sendString:message error:&sendError];
        if (completion) {
            completion(sendError);
        }
    } else {
        if (completion) {
            NSError *error = [NSError errorWithDomain:@"WebSocketError" code:3100 userInfo:@{NSLocalizedDescriptionKey: @"SOCKET_DESTROYED"}];
            completion(error);
        }
    }
}

// 关闭 WebSocket
- (void)close:(NSString *)socketID completion:(void(^)(NSError *error))completion {
    SRWebSocket *webSocket = self.connections[socketID];
    if (webSocket) {
        self.closeCompletionBlocks[socketID] = completion;
        [webSocket close];
    } else {
        if (completion) {
            NSError *error = [NSError errorWithDomain:@"WebSocketError" code:3100 userInfo:@{NSLocalizedDescriptionKey: @"SOCKET_DESTROYED"}];
            completion(error);
        }
    }
}

// 退出应用时关闭所有 WebSocket 连接及清理
- (void)exitApp {
    [self.connections enumerateKeysAndObjectsUsingBlock:^(NSString *key, SRWebSocket *socket, BOOL *stop) {
        [socket close];
    }];
    [self.connections removeAllObjects];
    [self.connectionCompletionBlocks removeAllObjects];
    [self.closeCompletionBlocks removeAllObjects];
}

- (NSString *)getSocketState:(NSString *)socketID {
    SRWebSocket *webSocket = self.connections[socketID];
    if (!webSocket) {
        return DESTROYED;
    }
    
    switch (webSocket.readyState) {
        case SR_CONNECTING:
            return CONNECTING;
        case SR_OPEN:
            return OPEN;
        case SR_CLOSING:
            return CLOSING;
        case SR_CLOSED:
            return CLOSED;
        default:
            return DESTROYED;
    }
}

// SocketRocket 库 并不能直接获取远程地址、端口，本地地址、地端口信息，所以以下方法暂不实现
//public void getRemoteHost(String socketId, Promise promise)
//public void getRemotePort(String socketId, Promise promise)
//public void getLocalHost(String socketId, Promise promise)
//public void getLocalPort(String socketId, Promise promise)

#pragma mark - Heart Beat
// 启动心跳
- (void)startHeartbeat:(NSString *)socketID {
    NSTimer *timer = [NSTimer scheduledTimerWithTimeInterval:HeartBeatInterval
                                                      target:self
                                                    selector:@selector(sendHeartbeat:)
                                                    userInfo:socketID
                                                     repeats:YES];
    self.heartBeatTimers[socketID] = timer;
    [self startPongTimer:socketID];
}

- (void)startPongTimer:(NSString *)socketID {
    NSTimer *pongTimer = [NSTimer scheduledTimerWithTimeInterval:PongDisconnectInterval
                                                          target:self
                                                        selector:@selector(pongTimerFired:)
                                                        userInfo:socketID
                                                         repeats:NO];
    self.pongTimers[socketID] = pongTimer;
}

- (void)pongTimerFired:(NSTimer *)timer {
    NSString *socketID = timer.userInfo;
//    [self stopHeartbeat:socketID];
    [self.pongTimers removeObjectForKey:socketID];

    NSError *error = [NSError errorWithDomain:@"WebSocketError" code:3101 userInfo:@{NSLocalizedDescriptionKey: @"Pong not received within 6 seconds"}];
    [self webSocket:self.connections[socketID] didFailWithError:error];
}

- (void)stopPongTimer:(NSString *)socketID {
    NSTimer *pongTimer = self.pongTimers[socketID];
    if (pongTimer) {
        [pongTimer invalidate];
        [self.pongTimers removeObjectForKey:socketID];
    }
}


// 停止心跳
- (void)stopHeartbeat:(NSString *)socketID {
    NSTimer *timer = self.heartBeatTimers[socketID];
    if (timer) {
        [timer invalidate];
        [self.heartBeatTimers removeObjectForKey:socketID];
    }
}

// 发送心跳包
- (void)sendHeartbeat:(NSTimer *)timer {
    NSString *socketID = timer.userInfo;
    SRWebSocket *webSocket = self.connections[socketID];
    NSDate *date = [NSDate date];
    NSLog(@"[pp] - ping - time:%@", date);
    if (webSocket) {
        [webSocket sendPing:nil error:nil];
    }
}


#pragma mark - SRWebSocketDelegate

// 连接成功
- (void)webSocketDidOpen:(SRWebSocket *)webSocket {
    NSString *socketID = [self keyForWebSocket:webSocket];
    
    [self startHeartbeat:socketID];
    
    void (^completion)(NSError *) = self.connectionCompletionBlocks[socketID];
    if (completion) {
        completion(nil);
        // 等到失败再移除回调
        // [self.connectionCompletionBlocks removeObjectForKey:socketID];
    }
}

// Open失败调用 / 心跳失败调用（比如server切换wifi，但是没那么快，可能发了几次心跳之后才来到这个回调）
- (void)webSocket:(SRWebSocket *)webSocket didFailWithError:(NSError *)error {
    NSString *socketID = [self keyForWebSocket:webSocket];
    
    [self stopHeartbeat:socketID];
    [self stopPongTimer:socketID];
    
    void (^connectionCompletion)(NSError *) = self.connectionCompletionBlocks[socketID];
    if (connectionCompletion) {
        connectionCompletion(error);
        [self.connectionCompletionBlocks removeObjectForKey:socketID];
    }
    
    [self removeWebSocketWithSocketID:socketID];
}

// 关闭成功/失败 / 过程中失败调用(比如server主动断掉)
- (void)webSocket:(SRWebSocket *)webSocket didCloseWithCode:(NSInteger)code reason:(NSString *)reason wasClean:(BOOL)wasClean {
    NSString *socketID = [self keyForWebSocket:webSocket];
    
    [self stopHeartbeat:socketID];
    [self stopPongTimer:socketID];
    
    void (^closeCompletion)(NSError *) = self.closeCompletionBlocks[socketID];
    
    // 清理回调
    void (^connectionCompletion)(NSError *) = self.connectionCompletionBlocks[socketID];
    if (connectionCompletion) {
        [self notifyEventWithName:WEBSOCKET_CLIENT_CLOSED_NOTIFICATION info:@{
            KEY_SOCKET_ID: socketID,
        }];
        [self.connectionCompletionBlocks removeObjectForKey:socketID];
    }
    
    [self removeWebSocketWithSocketID:socketID];
    
    if (closeCompletion) {
        NSError *error = nil;
        if (!wasClean) {
            // 如果关闭不干净
            error = [NSError errorWithDomain:@"WebSocketError" code:code userInfo:@{NSLocalizedDescriptionKey: reason ?: @"Close was not clean"}];
        }
        closeCompletion(error);
        [self.closeCompletionBlocks removeObjectForKey:socketID];
    }
}

// 收到消息
- (void)webSocket:(SRWebSocket *)webSocket didReceiveMessage:(id)message {
    NSString *socketID = [self keyForWebSocket:webSocket];
    if (socketID && [message isKindOfClass:[NSString class]]) {
        [self notifyEventWithName:WEBSOCKET_CLIENT_RECEIVE_MESSAGE_NOTIFICATION info:@{
//            @"type": @"message",
            KEY_SOCKET_ID: socketID,
            KEY_MESSAGE: message
        }];
    }
}

- (void)webSocket:(SRWebSocket *)webSocket didReceivePong:(NSData *)pongData {
    NSDate *date = [NSDate date];
    NSLog(@"[pp] - pong - time:%@", date);
    NSString *socketID = [self keyForWebSocket:webSocket];
    if (socketID) {
        [self resetPongTimer:socketID];
    }
}

- (void)resetPongTimer:(NSString *)socketID {
    NSTimer *pongTimer = self.pongTimers[socketID];
    if (pongTimer) {
        [pongTimer invalidate];
        [self.pongTimers removeObjectForKey:socketID];
    }
    [self startPongTimer:socketID];
}

#pragma mark - Helper Methods

- (NSString *)keyForWebSocket:(SRWebSocket *)webSocket {
    NSArray *keys = [self.connections allKeysForObject:webSocket];
    return keys.firstObject;
}

- (void)notifyEventWithName:(NSString *)name info:(NSDictionary *)info {
    [[NSNotificationCenter defaultCenter] postNotificationName:name object:nil userInfo:info];
}

- (void)removeWebSocketWithSocketID:(NSString *)socketID {
    if (socketID != nil && socketID.length > 0) {
        [self.connections removeObjectForKey:socketID];
    } else {
        NSLog(@"📛: Attempted to remove a WebSocket with a nil or empty socketID");
    }
}

@end
