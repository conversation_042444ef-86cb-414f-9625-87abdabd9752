//
//  WebSocketClientModule.m
//  storehub-websocket
//
//  Created by <PERSON> on 2023/11/9.
//

#import "WebSocketClientModule.h"
#import <React/RCTBridgeModule.h>
#import "WebSocketClientManager.h"
#import "WebSocketConstants.h"

@interface WebSocketClientModule ()

@property (nonatomic, strong) WebSocketClientManager *clientManager;

@end

@implementation WebSocketClientModule

RCT_EXPORT_MODULE(WebSocketClientModule);

- (instancetype)init {
    self = [super init];
    if (self) {
        self.clientManager = [WebSocketClientManager sharedInstance];
        
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(handleWebSocketClientReceiveMessage:)
                                                     name:WEBSOCKET_CLIENT_RECEIVE_MESSAGE_NOTIFICATION
                                                   object:nil];
        
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(handleWebSocketClientClosed:)
                                                     name:WEBSOCKET_CLIENT_CLOSED_NOTIFICATION
                                                   object:nil];
    }
    return self;
}

- (NSArray<NSString *> *)supportedEvents {
    return @[WEBSOCKET_CLIENT_EVENT_NAME];
}

RCT_EXPORT_METHOD(create:(NSDictionary *)options
                  resolve:(RCTPromiseResolveBlock)resolve
                  reject:(RCTPromiseRejectBlock)reject) {
    NSLog(@"[WebSocketClientModule] Create WebSocket with options: %@", options);
    
    [self.clientManager create:options completion:^(NSString * _Nullable socketID, NSError * _Nullable error) {
        if (!error) {
            NSLog(@"[WebSocketClientModule] WebSocket Created: Socket ID - %@", socketID);
            
            resolve(socketID);
        } else {
            NSString *errorDescription = error.localizedDescription;
            NSLog(@"[WebSocketClientModule] Error Creating WebSocket: %@", errorDescription);
            
            reject([NSString stringWithFormat:@"%ld", (long)error.code], errorDescription, error);
        }
    }];
}

RCT_EXPORT_METHOD(connect:(NSString *)socketId
                  resolve:(RCTPromiseResolveBlock)resolve
                  reject:(RCTPromiseRejectBlock)reject) {
    NSLog(@"[WebSocketClientModule] Connect WebSocket: Socket ID - %@", socketId);
    
    [self.clientManager connect:socketId completion:^(NSError * _Nonnull error) {
        if (!error) {
            NSLog(@"[WebSocketClientModule] WebSocket Connected: Socket ID - %@", socketId);
            
            resolve(nil);
            [self sendClientEvent:socketId
                            event:CLIENT_STATUS_CONNECTED
                             data:@{ }];
        } else { // Open失败调用 / 心跳失败调用
            NSString *errorDescription = error.localizedDescription;
            NSLog(@"[WebSocketClientModule] Error Connecting WebSocket: %@", errorDescription);
            
            [self sendClientEvent:socketId event:CLIENT_STATUS_CLOSED data:@{
                KEY_CODE: @(error.code),
                KEY_REASON: errorDescription,
                KEY_FROM_REMOTE: @(NO)
            }];
        }
    }];
}

RCT_EXPORT_METHOD(send:(NSString *)socketID
                  message:(NSString *)message
                  resolve:(RCTPromiseResolveBlock)resolve
                  reject:(RCTPromiseRejectBlock)reject) {
    NSLog(@"[WebSocketClientModule] Send Message: %@ on Socket ID - %@", message, socketID);
    
    [self.clientManager send:socketID message:message completion:^(NSError * _Nonnull error) {
        if (!error) {
            NSLog(@"[WebSocketClientModule] Message Sent: Socket ID - %@", socketID);
            
            resolve(nil);
        } else {
            NSString *errorDescription = error.localizedDescription;
            NSLog(@"[WebSocketClientModule] Error Sending Message: %@", errorDescription);
            
            reject([NSString stringWithFormat:@"%ld", (long)error.code], errorDescription, error);
        }
    }];
}

RCT_EXPORT_METHOD(close:(NSString *)socketID
                  resolve:(RCTPromiseResolveBlock)resolve
                  reject:(RCTPromiseRejectBlock)reject) {
    NSLog(@"[WebSocketClientModule] Close WebSocket: Socket ID - %@", socketID);
    
    [self.clientManager close:socketID completion:^(NSError * _Nonnull error) {
        if (!error) {
            NSLog(@"[WebSocketClientModule] WebSocket Closed: Socket ID - %@", socketID);
            
            resolve(nil);
        } else {
            NSString *errorDescription = error.localizedDescription;
            NSLog(@"[WebSocketClientModule] Error Closing WebSocket: %@", errorDescription);
            
            reject([NSString stringWithFormat:@"%ld", (long)error.code], errorDescription, error);
        }
    }];
}

RCT_EXPORT_METHOD(exitApp) {
    NSLog(@"[WebSocketClientModule] Exiting App - Closing all WebSockets");
    
    [self.clientManager exitApp];
}

RCT_EXPORT_METHOD(getSocketState:(NSString *)socketID
                  resolve:(RCTPromiseResolveBlock)resolve
                  reject:(RCTPromiseRejectBlock)reject) {
    NSLog(@"[WebSocketClientModule] Get Socket State: Socket ID - %@", socketID);
    
    NSString *socketStateStr = [self.clientManager getSocketState:socketID];
    resolve(socketStateStr);
}

#pragma mark - Other

- (void)sendClientEvent:(NSString *)socketId
                  event:(NSString *)eventName
                   data:(NSDictionary *)data {
    NSMutableDictionary *body = [@{
        KEY_SOCKET_ID: socketId,
        KEY_SOCKET_EVENT: eventName,
    } mutableCopy];
    
    [data enumerateKeysAndObjectsUsingBlock:^(id key, id obj, BOOL *stop) {
        body[key] = obj;
    }];
    
    [self sendEventWithName:WEBSOCKET_CLIENT_EVENT_NAME body:body];
}

- (void)handleWebSocketClientReceiveMessage:(NSNotification *)notification {
    NSDictionary *info = notification.userInfo;
    NSString *socketId = [info objectForKey:KEY_SOCKET_ID];
    NSString *message = [info objectForKey:KEY_MESSAGE];
    NSLog(@"[WebSocketClientModule] Received Message: %@ on Socket ID - %@", message, socketId);
    [self sendClientEvent:socketId
                    event:RECEIVE_MESSAGE
                     data:@{KEY_MESSAGE: message}];
}

- (void)handleWebSocketClientClosed:(NSNotification *)notification {
    NSDictionary *info = notification.userInfo;
    NSString *socketId = [info objectForKey:KEY_SOCKET_ID];
    
    [self sendClientEvent:socketId event:CLIENT_STATUS_CLOSED data:@{
        KEY_FROM_REMOTE: @(NO)
    }];
}

@end
