//  Copyright 2014-Present Zwo<PERSON>le Limited
//
//  Licensed under the Apache License, Version 2.0 (the "License");
//  you may not use this file except in compliance with the License.
//  You may obtain a copy of the License at
//
//  http://www.apache.org/licenses/LICENSE-2.0
//
//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//  See the License for the specific language governing permissions and
//  limitations under the License.

#import <Foundation/Foundation.h>
#import "PSSWebSocketTypes.h"

@class PSSWebSocketDriver;

@protocol PSSWebSocketDriverDelegate <NSObject>

@required

- (void)driverDidOpen:(PSSWebSocketDriver *)driver;
- (void)driver:(PSSWebSocketDriver *)driver didReceiveMessage:(id)message;
- (void)driver:(PSSWebSocketDriver *)driver didReceivePing:(NSData *)ping;
- (void)driver:(PSSWebSocketDriver *)driver didReceivePong:(NSData *)pong;
- (void)driver:(PSSWebSocketDriver *)driver didFailWithError:(NSError *)error;
- (void)driver:(PSSWebSocketDriver *)driver didCloseWithCode:(NSInteger)code reason:(NSString *)reason;
- (void)driver:(PSSWebSocketDriver *)driver write:(NSData *)data;

@end
@interface PSSWebSocketDriver : NSObject

#pragma mark - Class Methods

+ (BOOL)isWebSocketRequest:(NSURLRequest *)request;
+ (NSError *)errorWithCode:(NSInteger)code reason:(NSString *)reason;

#pragma mark - Properties

@property (nonatomic, assign, readonly) PSSWebSocketMode mode;
@property (nonatomic, weak) id <PSSWebSocketDriverDelegate> delegate;

@property (nonatomic, strong) NSString *protocol;

#pragma mark - Initialization

+ (instancetype)clientDriverWithRequest:(NSURLRequest *)request;
+ (instancetype)serverDriverWithRequest:(NSURLRequest *)request;

#pragma mark - Actions

- (void)start;
- (void)sendText:(NSString *)text;
- (void)sendBinary:(NSData *)binary;
- (void)sendCloseCode:(NSInteger)code reason:(NSString *)reason;
- (void)sendPing:(NSData *)data;
- (void)sendPong:(NSData *)data;

- (NSUInteger)execute:(void *)bytes maxLength:(NSUInteger)maxLength;

@end
