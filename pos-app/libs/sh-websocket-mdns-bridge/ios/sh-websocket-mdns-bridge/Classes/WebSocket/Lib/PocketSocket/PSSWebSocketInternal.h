//  Copyright 2014-Present Z<PERSON><PERSON><PERSON> Limited
//
//  Licensed under the Apache License, Version 2.0 (the "License");
//  you may not use this file except in compliance with the License.
//  You may obtain a copy of the License at
//
//  http://www.apache.org/licenses/LICENSE-2.0
//
//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//  See the License for the specific language governing permissions and
//  limitations under the License.

#import <Foundation/Foundation.h>
#import <Foundation/Foundation.h>
#import "PSSWebSocketTypes.h"
#import "PSSWebSocketDriver.h"
#import <sys/socket.h>
#import <arpa/inet.h>


typedef NS_ENUM(uint8_t, PSSWebSocketOpCode) {
    PSSWebSocketOpCodeContinuation = 0x0,
    PSSWebSocketOpCodeText = 0x1,
    PSSWebSocketOpCodeBinary = 0x2,
    // 0x3 -> 0x7 reserved
    PSSWebSocketOpCodeClose = 0x8,
    PSSWebSocketOpCodePing = 0x9,
    PSSWebSocketOpCodePong = 0xA,
    // 0xB -> 0xF reserved
};

static const uint8_t PSSWebSocketFinMask = 0x80;
static const uint8_t PSSWebSocketOpCodeMask = 0x0F;
static const uint8_t PSSWebSocketRsv1Mask = 0x40;
static const uint8_t PSSWebSocketRsv2Mask = 0x20;
static const uint8_t PSSWebSocketRsv3Mask = 0x10;
static const uint8_t PSSWebSocketMaskMask = 0x80;
static const uint8_t PSSWebSocketPayloadLenMask = 0x7F;

#define PSSWebSocketSetOutError(e, c, d) if(e){ *e = [PSSWebSocketDriver errorWithCode:c reason:d]; }

static inline void _PSSWebSocketLog(id self, NSString *format, ...) {
    __block va_list arg_list;
    va_start (arg_list, format);
    
    NSString *formattedString = [[NSString alloc] initWithFormat:format arguments:arg_list];
    
    va_end(arg_list);
    
    NSLog(@"[%@]: %@", self, formattedString);
}
#define PSSWebSocketLog(...) _PSSWebSocketLog(self, __VA_ARGS__)

static inline BOOL PSSWebSocketOpCodeIsControl(PSSWebSocketOpCode opcode) {
    return (opcode == PSSWebSocketOpCodeClose ||
            opcode == PSSWebSocketOpCodePing ||
            opcode == PSSWebSocketOpCodePong);
};

static inline BOOL PSSWebSocketOpCodeIsValid(PSSWebSocketOpCode opcode) {
    return (opcode == PSSWebSocketOpCodeClose ||
            opcode == PSSWebSocketOpCodePing ||
            opcode == PSSWebSocketOpCodePong ||
            opcode == PSSWebSocketOpCodeText ||
            opcode == PSSWebSocketOpCodeBinary ||
            opcode == PSSWebSocketOpCodeContinuation);
};


static inline BOOL PSSWebSocketCloseCodeIsValid(NSInteger closeCode) {
    if(closeCode < 1000) {
        return NO;
    }
    if(closeCode >= 1000 && closeCode <= 1011) {
        if(closeCode == 1004 ||
           closeCode == 1005 ||
           closeCode == 1006) {
            return NO;
        }
        return YES;
    }
    if(closeCode >= 3000 && closeCode <= 3999) {
        return YES;
    }
    if(closeCode >= 4000 && closeCode <= 4999) {
        return YES;
    }
    return NO;
}

static inline NSOrderedSet* PSHTTPHeaderFieldValues(NSString *header) {
    NSMutableOrderedSet *components = [NSMutableOrderedSet orderedSet];
    [[header componentsSeparatedByString:@";"] enumerateObjectsUsingBlock:^(NSString * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        NSString *str = obj;
        while ([str hasPrefix:@" "] && str.length > 1) {
            str = [str substringWithRange:NSMakeRange(1, str.length - 1)];
        }
        while ([str hasSuffix:@" "] && str.length > 1) {
            str = [str substringWithRange:NSMakeRange(0, str.length - 1)];
        }
        if ([str length] > 0 && ![str isEqualToString:@" "]) {
            [components addObject:str];
        }
    }];
    return components;
}

static inline NSData* PSPeerAddressOfInputStream(NSInputStream *stream) {
    // First recover the socket handle from the stream:
    NSData* handleData = CFBridgingRelease(CFReadStreamCopyProperty((__bridge CFReadStreamRef)stream,
                                                                    kCFStreamPropertySocketNativeHandle));
    if(!handleData || handleData.length != sizeof(CFSocketNativeHandle)) {
        return nil;
    }
    CFSocketNativeHandle socketHandle = *(const CFSocketNativeHandle*)handleData.bytes;
    struct sockaddr_in addr;
    unsigned addrLen = sizeof(addr);
    if(getpeername(socketHandle, (struct sockaddr*)&addr,&addrLen) < 0) {
        return nil;
    }
    return [NSData dataWithBytes: &addr length: addr.sin_len];
}

static inline NSString* PSPeerHostOfInputStream(NSInputStream *stream) {
    NSData *peerAddress = PSPeerAddressOfInputStream(stream);
    if (!peerAddress) {
        return nil;
    }

    const struct sockaddr_storage *addr = (const struct sockaddr_storage *)peerAddress.bytes;
    char nameBuf[INET6_ADDRSTRLEN];
    
    // 处理 IPv4 地址
    if (addr->ss_family == AF_INET) {
        const struct sockaddr_in *addr_in = (const struct sockaddr_in *)addr;
        if (inet_ntop(AF_INET, &(addr_in->sin_addr), nameBuf, sizeof(nameBuf)) == NULL) {
            return nil;
        }
        NSString *ipString = [NSString stringWithUTF8String:nameBuf];
        // 过滤 Link-Local 地址
        if (![ipString hasPrefix:@"169.254"]) {
            return [NSString stringWithFormat:@"%s:%hu", nameBuf, ntohs(addr_in->sin_port)];
        }
    }
    
    // 处理 IPv6 地址
    if (addr->ss_family == AF_INET6) {
        const struct sockaddr_in6 *addr_in6 = (const struct sockaddr_in6 *)addr;
        if (inet_ntop(AF_INET6, &(addr_in6->sin6_addr), nameBuf, sizeof(nameBuf)) == NULL) {
            return nil;
        }
        NSString *ipString = [NSString stringWithUTF8String:nameBuf];
        // 过滤 Link-Local 地址
        if (![ipString hasPrefix:@"fe80::"]) { // IPv6 Link-Local 地址以 fe80:: 开头
            return [NSString stringWithFormat:@"%s:%hu", nameBuf, ntohs(addr_in6->sin6_port)];
        }
    }
    
    return nil; 
}
