//  Copyright 2014-Present Zwo<PERSON><PERSON> Limited
//
//  Licensed under the Apache License, Version 2.0 (the "License");
//  you may not use this file except in compliance with the License.
//  You may obtain a copy of the License at
//
//  http://www.apache.org/licenses/LICENSE-2.0
//
//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//  See the License for the specific language governing permissions and
//  limitations under the License.

#import <Foundation/Foundation.h>
#import "PSSWebSocket.h"

@class PSSWebSocketServer;

@protocol PSSWebSocketServerDelegate <NSObject>

@required

- (void)serverDidStart:(PSSWebSocketServer *)server;
- (void)server:(PSSWebSocketServer *)server didFailWithError:(NSError *)error;
- (void)serverDidStop:(PSSWebSocketServer *)server;

- (void)server:(PSSWebSocketServer *)server webSocketDidOpen:(PSSWebSocket *)webSocket;
- (void)server:(PSSWebSocketServer *)server webSocket:(PSSWebSocket *)webSocket didReceiveMessage:(id)message;
- (void)server:(PSSWebSocketServer *)server webSocket:(PSSWebSocket *)webSocket didFailWithError:(NSError *)error;
- (void)server:(PSSWebSocketServer *)server webSocket:(PSSWebSocket *)webSocket didCloseWithCode:(NSInteger)code reason:(NSString *)reason wasClean:(BOOL)wasClean;

@optional
- (void)server:(PSSWebSocketServer *)server webSocketDidFlushInput:(PSSWebSocket *)webSocket;
- (void)server:(PSSWebSocketServer *)server webSocketDidFlushOutput:(PSSWebSocket *)webSocket;
- (BOOL)server:(PSSWebSocketServer *)server acceptWebSocketWithRequest:(NSURLRequest *)request;
- (BOOL)server:(PSSWebSocketServer *)server acceptWebSocketWithRequest:(NSURLRequest *)request address:(NSData *)address trust:(SecTrustRef)trust response:(NSHTTPURLResponse **)response;
@end

@interface PSSWebSocketServer : NSObject

#pragma mark - Properties

@property (nonatomic, weak) id <PSSWebSocketServerDelegate> delegate;
@property (nonatomic, strong) dispatch_queue_t delegateQueue;

#pragma mark - Initialization

+ (instancetype)serverWithHost:(NSString *)host port:(NSUInteger)port;
+ (instancetype)serverWithHost:(NSString *)host port:(NSUInteger)port SSLCertificates:(NSArray *)SSLCertificates;

#pragma mark - Actions

- (void)start;
- (void)stop;

#pragma mark - Other
- (NSSet<PSSWebSocket *> *)activeWebSockets;
- (NSUInteger)currentPort;

@end
