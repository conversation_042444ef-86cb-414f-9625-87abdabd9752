//  Copyright 2014-Present Zwo<PERSON><PERSON> Limited
//
//  Licensed under the Apache License, Version 2.0 (the "License");
//  you may not use this file except in compliance with the License.
//  You may obtain a copy of the License at
//
//  http://www.apache.org/licenses/LICENSE-2.0
//
//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//  See the License for the specific language governing permissions and
//  limitations under the License.

#import <Foundation/Foundation.h>

typedef NS_ENUM(NSInteger, PSSWebSocketMode) {
    PSSWebSocketModeClient = 0,
    PSSWebSocketModeServer
};

typedef NS_ENUM(NSInteger, PSSWebSocketErrorCodes) {
    PSSWebSocketErrorCodeUnknown = 0,
    PSSWebSocketErrorCodeTimedOut,
    PSSWebSocketErrorCodeHandshakeFailed,
    PSSWebSocketErrorCodeConnectionFailed
};

typedef NS_ENUM(NSInteger, PSSWebSocketStatusCode) {
    PSSWebSocketStatusCodeNormal = 1000,
    PSSWebSocketStatusCodeGoingAway = 1001,
    PSSWebSocketStatusCodeProtocolError = 1002,
    PSSWebSocketStatusCodeUnhandledType = 1003,
    // 1004 reserved
    PSSWebSocketStatusCodeNoStatusReceived = 1005,
    // 1006 reserved
    PSSWebSocketStatusCodeInvalidUTF8 = 1007,
    PSSWebSocketStatusCodePolicyViolated = 1008,
    PSSWebSocketStatusCodeMessageTooBig = 1009
};

#define PSSWebSocketGUID @"258EAFA5-E914-47DA-95CA-C5AB0DC85B11"
#define PSSWebSocketErrorDomain @"PSSWebSocketErrorDomain"

// NSError userInfo keys, used with PSSWebSocketErrorCodeHandshakeFailed:
#define PSHTTPStatusErrorKey @"HTTPStatus"      // The HTTP status (404, etc.)
#define PSHTTPResponseErrorKey @"HTTPResponse"  // The entire HTTP response as an CFHTTPMessageRef
