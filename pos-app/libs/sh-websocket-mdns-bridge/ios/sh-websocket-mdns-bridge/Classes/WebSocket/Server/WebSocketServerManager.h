//
//  WebSocketServerManager.h
//  sh-websocket-mdns-bridge
//
//  Created by <PERSON> on 2024/1/29.
//

#import <Foundation/Foundation.h>
#import "PSSWebSocketServer.h"

NS_ASSUME_NONNULL_BEGIN

@interface WebSocketServerManager : NSObject

+ (instancetype)sharedInstance;

- (void)startServerWithPort:(int)port completion:(void (^)(NSError *error, PSSWebSocketServer *server))completion;
- (void)stopServerWithCompletion:(void (^)(NSError *error, PSSWebSocketServer *server))completion;
- (void)send:(NSString *)socketId message:(NSString *)message completion:(void (^)(NSError *error))completion;
- (void)broadcast:(NSString *)message completion:(void (^)(NSError *error))completion;
- (void)close:(NSString *)socketId completion:(void (^)(BOOL wasClean, NSString *code, NSString *reason, PSSWebSocket *webSocket))completion;
- (void)exitAppWithCompletion:(void (^)(NSError *error, PSSWebSocketServer *server))completion;
- (void)getSocketState:(NSString *)socketId completion:(void (^)(NSError *error, NSString *state))completion;

@end

NS_ASSUME_NONNULL_END
