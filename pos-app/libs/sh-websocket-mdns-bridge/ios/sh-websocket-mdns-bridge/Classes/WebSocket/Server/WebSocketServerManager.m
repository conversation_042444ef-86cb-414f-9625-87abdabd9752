//
//  WebSocketServerManager.m
//  sh-websocket-mdns-bridge
//
//  Created by <PERSON> on 2024/1/29.
//

#import "WebSocketServerManager.h"
#import "WebSocketConstants.h"
#import <sys/socket.h>
#import <netinet/in.h>

@interface WebSocketServerManager () <PSSWebSocketServerDelegate>

@property (strong, nonatomic) PSSWebSocketServer *server;
@property (copy, nonatomic) void(^startCallback)(NSError *error, PSSWebSocketServer *server);
@property (copy, nonatomic) void(^stopCallback)(NSError *error, PSSWebSocketServer *server);

@end

@implementation WebSocketServerManager

+ (instancetype)sharedInstance {
    static WebSocketServerManager *sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sharedInstance = [[self alloc] init];
    });
    return sharedInstance;
}

- (void)startServerWithPort:(int)port completion:(void (^)(NSError *error, PSSWebSocketServer *server))completion {
    if (!self.server) {
        if (port == 0) {
            port = (int)[self findFreePort];
        }
        [self notifyEventWithName:WEBSOCKET_SERVER_FREE_PORT_NOTIFICATION info:@{
            @"port": @(port)
        }];
        self.server = [PSSWebSocketServer serverWithHost:@"0.0.0.0" port:port];
        self.server.delegate = self;
        self.startCallback = completion;
        [self.server start];
    } else {
        NSError *error = [NSError errorWithDomain:@"WebSocketServerManagerErrorDomain" code:3202 userInfo:@{NSLocalizedDescriptionKey: @"Server is already running"}];
        completion(error, nil);
    }
}

- (void)stopServerWithCompletion:(void (^)(NSError *error, PSSWebSocketServer *server))completion {
    NSError *error = nil;
    if ([self isServerStarted:&error]) {
        self.stopCallback = completion;
        [self.server stop];
        self.server = nil;
    } else {
        completion(error, nil);
    }
}

- (void)send:(NSString *)socketId message:(NSString *)message completion:(void (^)(NSError *error))completion {
    NSError *error = nil;
    if ([self isServerStarted:&error]) {
        NSLog(@"activeWebSockets: %@", [self.server activeWebSockets]);
        for (PSSWebSocket *websocket in [self.server activeWebSockets]) {
            if ([websocket.socketId isEqualToString:socketId]) {
                [websocket send:message];
            }
        }
        completion(nil);
    } else {
        completion(error);
    }
}

- (void)broadcast:(NSString *)message completion:(void (^)(NSError *error))completion {
    NSError *error = nil;
    if ([self isServerStarted:&error]) {
        for (PSSWebSocket *websocket in [self.server activeWebSockets]) {
            [websocket send:message];
        }
        completion(nil);
    } else {
        completion(error);
    }
}

- (void)close:(NSString *)socketId completion:(void (^)(BOOL wasClean, NSString *code, NSString *reason, PSSWebSocket *webSocket))completion {
    NSError *error = nil;
    if ([self isServerStarted:&error]) {
        for (PSSWebSocket *websocket in [self.server activeWebSockets]) {
            if (websocket.socketId == socketId) {
                [websocket close];
            }
        }
        completion(YES, nil, nil, nil);
    } else {
        completion(NO, [NSString stringWithFormat:@"%ld", error.code], error.localizedDescription, nil);
    }
}

- (void)exitAppWithCompletion:(void (^)(NSError *error, PSSWebSocketServer *server))completion {
    [self stopServerWithCompletion:completion];
}

- (BOOL)isServerStarted:(NSError **)error {
    if (!self.server) {
        if (error) {
            *error = [NSError errorWithDomain:@"WebSocketServerManagerErrorDomain" code:3201 userInfo:@{ NSLocalizedDescriptionKey: @"Server not started" }];
        }
        return NO;
    }
    return YES;
}

- (void)getSocketState:(NSString *)socketId completion:(void (^)(NSError *error, NSString *state))completion {
    NSError *error = nil;
    if ([self isServerStarted:&error]) {
        NSString *state = DESTROYED;
        for (PSSWebSocket *websocket in [self.server activeWebSockets]) {
            if (websocket.socketId == socketId) {
                switch (websocket.readyState) {
                    case PSSWebSocketReadyStateConnecting:
                        state = CONNECTING;
                    case PSSWebSocketReadyStateOpen:
                        state = OPEN;
                    case PSSWebSocketReadyStateClosing:
                        state = CLOSING;
                    case PSSWebSocketReadyStateClosed:
                        state = CLOSED;
                    default:
                        state = DESTROYED;
                }
            }
        }
        completion(nil, state);
    } else {
        completion(error, nil);
    }
}

- (void)notifyEventWithName:(NSString *)name info:(NSDictionary *)info {
    [[NSNotificationCenter defaultCenter] postNotificationName:name object:nil userInfo:info];
}


#pragma mark - PSSWebSocketServerDelegate
// 服务器成功启动后调用
- (void)serverDidStart:(PSSWebSocketServer *)server {
    if (self.startCallback) {
        self.startCallback(nil, server);
    }
}

// 服务器启动失败时调用，例如因为网络问题、端口被占用等原因
- (void)server:(PSSWebSocketServer *)server didFailWithError:(NSError *)error {
    if (self.startCallback) {
        NSError *err = [NSError errorWithDomain:@"WebSocketServerManagerErrorDomain" code:3101 userInfo:@{NSLocalizedDescriptionKey: @"Socket is not established"}];
        self.startCallback(err, nil);
    }
}

// 服务器成功停止后调用
- (void)serverDidStop:(PSSWebSocketServer *)server {
    if (self.stopCallback) {
        self.stopCallback(nil, server);
    }
}

// 当新的 WebSocket 连接打开时调用
- (void)server:(PSSWebSocketServer *)server webSocketDidOpen:(PSSWebSocket *)webSocket {
    if (server == self.server) {
        NSString *host = webSocket.remoteHost;
        NSString *ip = [self extractIPAddressFromString:host];
        NSString *port = [self extractPortFromString:host];
        [self notifyEventWithName:WEBSOCKET_SERVER_OPEN_NOTIFICATION info:@{
            KEY_SOCKET_ID: webSocket.socketId,
            KEY_PAYLOAD: webSocket.payload,
            KEY_REMOTE_ADDRESS: ip,
            KEY_REMOTE_PORT: port
        }];
    }
}

- (NSString *)extractIPAddressFromString:(NSString *)addressString {
    NSError *error = nil;
    NSRegularExpression *regex = [NSRegularExpression regularExpressionWithPattern:@"\\b(?:\\d{1,3}\\.){3}\\d{1,3}\\b" options:0 error:&error];
    
    if (error) {
        NSLog(@"Failed to create regex: %@", error);
        return @"";
    }
    
    NSTextCheckingResult *match = [regex firstMatchInString:addressString options:0 range:NSMakeRange(0, [addressString length])];
    if (match) {
        NSRange matchRange = [match range];
        NSString *ipAddress = [addressString substringWithRange:matchRange];
        return ipAddress;
    }
    
    return @"";
}

- (NSString *)extractPortFromString:(NSString *)addressString {
    NSError *error = nil;
    NSRegularExpression *regex = [NSRegularExpression regularExpressionWithPattern:@":(\\d+)" options:0 error:&error];
    
    if (error) {
        NSLog(@"Failed to create regex: %@", error);
        return @"";
    }
    
    NSTextCheckingResult *match = [regex firstMatchInString:addressString options:0 range:NSMakeRange(0, [addressString length])];
    if (match) {
        NSRange matchRange = [match rangeAtIndex:1];
        NSString *port = [addressString substringWithRange:matchRange];
        return port;
    }
    
    return @"";
}

// 当收到来自特定 WebSocket 连接的消息时调用
- (void)server:(PSSWebSocketServer *)server webSocket:(PSSWebSocket *)webSocket didReceiveMessage:(id)message {
    if (server == self.server) {
        [self notifyEventWithName:WEBSOCKET_SERVER_RECEIVE_MESSAGE_NOTIFICATION info:@{
            KEY_MESSAGE: message,
            KEY_SOCKET_ID: webSocket.socketId,
        }];
    }
}

// 当特定 WebSocket 连接因错误而失败时调用，例如网络中断
- (void)server:(PSSWebSocketServer *)server webSocket:(PSSWebSocket *)webSocket didFailWithError:(NSError *)error {
    [self notifyEventWithName:WEBSOCKET_SERVER_ERROR_NOTIFICATION info:@{
        KEY_CODE: @(error.code),
        KEY_REASON: error.localizedDescription,
        KEY_SOCKET_ID: webSocket.socketId
    }];
}

// 当特定 WebSocket 连接关闭时调用
// code 和 reason 参数提供了关闭的原因，wasClean 表示是否是干净（无错误）的关闭。
- (void)server:(PSSWebSocketServer *)server webSocket:(PSSWebSocket *)webSocket didCloseWithCode:(NSInteger)code reason:(NSString *)reason wasClean:(BOOL)wasClean {
    if (server == self.server) {
        [self notifyEventWithName:WEBSOCKET_SERVER_CLOSED_NOTIFICATION info:@{
            KEY_SOCKET_ID: webSocket.socketId,
            KEY_CODE: [NSString stringWithFormat:@"%ld", (long)code],
            KEY_REASON: reason ?: @"",
        }];
    }
}

#pragma mark - optional
// 当 WebSocket 服务器成功地从输入流（接收到的数据）中读取并处理完所有可用数据后调用
- (void)server:(PSSWebSocketServer *)server webSocketDidFlushInput:(PSSWebSocket *)webSocket {
}

// 当 WebSocket 服务器成功地将所有排队的输出数据（待发送的数据）发送出去后调用
- (void)server:(PSSWebSocketServer *)server webSocketDidFlushOutput:(PSSWebSocket *)webSocket {
    NSLog(@"sent msg");
}

// 当有新的连接请求时调用，用于决定是否接受该 WebSocket 连接
- (BOOL)server:(PSSWebSocketServer *)server acceptWebSocketWithRequest:(NSURLRequest *)request {
    return YES; // 返回 YES 接受连接，返回 NO 拒绝连接。
}

// 当有新的连接请求并且需要更复杂的 SSL/TLS 评估时调用
- (BOOL)server:(PSSWebSocketServer *)server acceptWebSocketWithRequest:(NSURLRequest *)request address:(NSData *)address trust:(SecTrustRef)trust response:(NSHTTPURLResponse **)response {
    return YES; // 返回 YES 接受连接，返回 NO 拒绝连接。
}

#pragma mark - Other
- (NSUInteger)findFreePort {
    int sockfd = socket(AF_INET, SOCK_STREAM, 0);
    if (sockfd < 0) {
        NSLog(@"Error: Could not open socket");
        return 0;
    }

    struct sockaddr_in addr;
    memset(&addr, 0, sizeof(addr));
    addr.sin_family = AF_INET;
    addr.sin_addr.s_addr = htonl(INADDR_ANY);
    addr.sin_port = 0;

    if (bind(sockfd, (struct sockaddr *)&addr, sizeof(addr)) < 0) {
        NSLog(@"Error: Could not bind socket");
        close(sockfd);
        return 0;
    }

    socklen_t len = sizeof(addr);
    if (getsockname(sockfd, (struct sockaddr *)&addr, &len) < 0) {
        NSLog(@"Error: Could not get sock name");
        close(sockfd);
        return 0;
    }

    NSUInteger freePort = ntohs(addr.sin_port);
    close(sockfd);

    return freePort;
}

@end
