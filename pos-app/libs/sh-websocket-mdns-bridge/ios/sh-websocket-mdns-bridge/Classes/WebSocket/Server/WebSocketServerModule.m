//
//  WebSocketServerModule.m
//  sh-websocket-mdns-bridge
//
//  Created by <PERSON> on 2024/1/29.
//

#import "WebSocketServerModule.h"
#import <React/RCTBridgeModule.h>
#import "WebSocketServerManager.h"
#import "PSSWebSocketServer.h"
#import "WebSocketConstants.h"

@interface WebSocketServerModule ()

@property (nonatomic, strong) WebSocketServerManager *serverManager;

@end

@implementation WebSocketServerModule

RCT_EXPORT_MODULE(WebSocketServerModule);

- (instancetype)init {
    self = [super init];
    if (self) {
        self.serverManager = [WebSocketServerManager sharedInstance];
        
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(handleWebSocketServerReceiveMessage:)
                                                     name:WEBSOCKET_SERVER_RECEIVE_MESSAGE_NOTIFICATION
                                                   object:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(handleWebSocketServerClosed:)
                                                     name:WEBSOCKET_SERVER_CLOSED_NOTIFICATION
                                                   object:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(handleWebSocketServerError:)
                                                     name:WEBSOCKET_SERVER_ERROR_NOTIFICATION
                                                   object:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(handleWebSocketServerOpen:)
                                                     name:WEBSOCKET_SERVER_OPEN_NOTIFICATION
                                                   object:nil];
    }
    return self;
}

- (NSArray<NSString *> *)supportedEvents {
    return @[WEBSOCKET_SERVER_EVENT_NAME];
}

RCT_EXPORT_METHOD(startServer:(int)port resolve:(RCTPromiseResolveBlock)resolve
                  reject:(RCTPromiseRejectBlock)reject) {
    [self.serverManager startServerWithPort:(int)port completion:^(NSError * _Nonnull error, PSSWebSocketServer *server) {
        if (error) {
            reject([NSString stringWithFormat:@"%ld", (long)error.code], error.localizedDescription, error);
        } else {
            resolve(nil);
            [self sendServerEvent:SERVER_START server:server];
        }
    }];
}

RCT_EXPORT_METHOD(stopServer:(RCTPromiseResolveBlock)resolve
                  reject:(RCTPromiseRejectBlock)reject) {
    [self.serverManager stopServerWithCompletion:^(NSError * _Nonnull error, PSSWebSocketServer *server) {
        if (error) {
            reject([NSString stringWithFormat:@"%ld", (long)error.code], error.localizedDescription, error);
        } else {
            resolve(nil);
            [self sendServerEvent:SERVER_CLOSE server:server];
        }
    }];
}

RCT_EXPORT_METHOD(send:(NSString *)socketId message:(NSString *)message resolve:(RCTPromiseResolveBlock)resolve
                  reject:(RCTPromiseRejectBlock)reject) {
    [self.serverManager send:socketId message:message completion:^(NSError * _Nonnull error) {
        if (error) {
            reject([NSString stringWithFormat:@"%ld", (long)error.code], error.localizedDescription, error);
        } else {
            resolve(nil);
        }
    }];
}

RCT_EXPORT_METHOD(broadcast:(NSString *)message resolve:(RCTPromiseResolveBlock)resolve
                  reject:(RCTPromiseRejectBlock)reject) {
    [self.serverManager broadcast:message completion:^(NSError * _Nonnull error) {
        if (error) {
            reject([NSString stringWithFormat:@"%ld", (long)error.code], error.localizedDescription, error);
        } else {
            resolve(nil);
        }
    }];
}

RCT_EXPORT_METHOD(close:(NSString *)socketId resolve:(RCTPromiseResolveBlock)resolve
                  reject:(RCTPromiseRejectBlock)reject) {
    [self.serverManager close:socketId completion:^(BOOL wasClean, NSString * _Nonnull code, NSString * _Nonnull reason, PSSWebSocket * _Nonnull webSocket) {
        if (wasClean) {
            resolve(nil);
        } else {
            reject(code, reason, nil);
        }
    }];
}

RCT_EXPORT_METHOD(exitApp:(RCTPromiseResolveBlock)resolve
                  reject:(RCTPromiseRejectBlock)reject) {
    [self.serverManager exitAppWithCompletion:^(NSError * _Nonnull error, PSSWebSocketServer * _Nonnull server) {
        if (error) {
            reject([NSString stringWithFormat:@"%ld", (long)error.code], error.localizedDescription, error);
        } else {
            resolve(nil);
            [self sendServerEvent:SERVER_CLOSE server:server];
        }
    }];
}

RCT_EXPORT_METHOD(getSocketState:(NSString *)socketId resolve:(RCTPromiseResolveBlock)resolve
                  reject:(RCTPromiseRejectBlock)reject) {
    [self.serverManager getSocketState:socketId completion:^(NSError * _Nonnull error, NSString * _Nonnull state) {
        if (error) {
            reject([NSString stringWithFormat:@"%ld", (long)error.code], error.localizedDescription, error);
        } else {
            resolve(state);
        }
    }];
}

// getRemoteHost
// getRemotePort
// getLocalHost
// getLocalPort

#pragma mark - Event

- (void)sendServerEvent:(NSString *)eventName server:(PSSWebSocketServer *)server {
    NSUInteger serverPort = [server currentPort];
    NSMutableDictionary *body = [@{
        KEY_SOCKET_EVENT: eventName,
        KEY_SOCKET_LOCAL_PORT: @(serverPort),
    } mutableCopy];
    
    [self sendEventWithName:WEBSOCKET_SERVER_EVENT_NAME body:body];
}

- (void)sendClientEvent:(NSString *)socketId
                  event:(NSString *)eventName
                   data:(NSDictionary *)data {
    NSString *nonnullSocketId = @"";
    if (socketId) {
        nonnullSocketId = socketId;
    }
    NSMutableDictionary *body = [@{
        KEY_SOCKET_ID: nonnullSocketId,
        KEY_SOCKET_EVENT: eventName,
    } mutableCopy];
    
    [data enumerateKeysAndObjectsUsingBlock:^(id key, id obj, BOOL *stop) {
        body[key] = obj;
    }];
    
    [self sendEventWithName:WEBSOCKET_SERVER_EVENT_NAME body:body];
}

- (void)handleWebSocketServerReceiveMessage:(NSNotification *)notification {
    NSDictionary *info = notification.userInfo;
    NSString *socketId = [info objectForKey:KEY_SOCKET_ID];
    NSString *message = [info objectForKey:KEY_MESSAGE];
    [self sendClientEvent:socketId
                    event:CLIENT_MESSAGE
                     data:@{KEY_MESSAGE: message}];
}

- (void)handleWebSocketServerClosed:(NSNotification *)notification {
    NSDictionary *info = notification.userInfo;
    NSString *socketId = [info objectForKey:KEY_SOCKET_ID];
    NSString *code = [info objectForKey:KEY_CODE];
    NSString *reason = [info objectForKey:KEY_REASON];
    
    [self sendClientEvent:socketId event:CLIENT_STATUS_CLOSED data:@{
        KEY_CODE: code,
        KEY_REASON: reason,
    }];
}

- (void)handleWebSocketServerError:(NSNotification *)notification {
    NSDictionary *info = notification.userInfo;
    NSString *socketId = [info objectForKey:KEY_SOCKET_ID];
    NSString *code = [info objectForKey:KEY_CODE];
    NSString *reason = [info objectForKey:KEY_REASON];
    
    [self sendClientEvent:socketId event:CLIENT_STATUS_CLOSED data:@{
        KEY_CODE: code,
        KEY_REASON: reason,
    }];
}

- (void)handleWebSocketServerOpen:(NSNotification *)notification {
    NSDictionary *info = notification.userInfo;
    NSString *socketId = [info objectForKey:KEY_SOCKET_ID];
    NSString *payload = [info objectForKey:KEY_PAYLOAD];
    NSString *ip = [info objectForKey:KEY_REMOTE_ADDRESS];
    NSString *port = [info objectForKey:KEY_REMOTE_PORT];
    
    [self sendClientEvent:socketId event:CLIENT_STATUS_CONNECTED data:@{
        KEY_PAYLOAD: payload,
        KEY_REMOTE_ADDRESS: ip,
        KEY_REMOTE_PORT: port
    }];
}

@end
