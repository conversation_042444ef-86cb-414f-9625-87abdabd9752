//
//  WebSocketConstants.h
//  storehub-websocket
//
//  Created by <PERSON> on 2023/11/9.
//

#import <Foundation/Foundation.h>

extern NSString * const WEBSOCKET_CLIENT_RECEIVE_MESSAGE_NOTIFICATION;

extern NSString * const WEBSOCKET_CLIENT_CLOSED_NOTIFICATION;

extern NSString * const WEBSOCKET_SERVER_RECEIVE_MESSAGE_NOTIFICATION;

extern NSString * const WEBSOCKET_SERVER_CLOSED_NOTIFICATION;

extern NSString * const WEBSOCKET_SERVER_OPEN_NOTIFICATION;

extern NSString * const WEBSOCKET_SERVER_ERROR_NOTIFICATION;

extern NSString * const WEBSOCKET_SERVER_FREE_PORT_NOTIFICATION;

extern NSString * const MDNS_SERVICE_LOST_NOTIFICATION;

extern const int CONNECT_TIMEOUT;

extern NSString * const WEBSOCKET_CLIENT_EVENT_NAME;

extern NSString * const WEBSOCKET_SERVER_EVENT_NAME;

extern NSString * const CLIENT_STATUS_CONNECTED;

extern NSString * const CLIENT_STATUS_CLOSED;

extern NSString * const KEY_SOCKET_ID;

extern NSString * const KEY_PAYLOAD;

extern NSString * const KEY_REMOTE_ADDRESS;

extern NSString * const KEY_REMOTE_PORT;

extern NSString * const KEY_SOCKET_LOCAL_PORT;

extern NSString * const KEY_SOCKET_EVENT;

extern NSString * const KEY_CODE;

extern NSString * const KEY_REASON;

extern NSString * const KEY_FROM_REMOTE;

extern NSString * const KEY_ERROR;

extern NSString * const NOT_CONNECTED;

extern NSString * const CONNECTING;

extern NSString * const OPEN;

extern NSString * const CLOSING;

extern NSString * const CLOSED;

extern NSString * const DESTROYED;

extern NSString * const KEY_MESSAGE;

extern NSString * const RECEIVE_MESSAGE;

extern NSString * const MDNS_DOMAIN;

extern NSString * const MDNS_TYPE;

extern NSString * const MDNS_STATE_CHANGED;

extern NSString * const SERVICE_NAME;

extern NSString * const SERVICE_ADDRESS;

extern NSString * const SERVICE_PORT;

extern NSString * const SERVICE_EXTRAS;

extern NSString * const SERVICE_FOUND;

extern NSString * const SERVICE_LOST;

extern NSString * const X_SOCKETID;

extern NSString * const X_PAYLOAD;

extern NSString * const LOG_LEVEL_INFO;

extern NSString * const LOG_LEVEL_ERROR;

extern NSString * const LOG_RESULT_START;

extern NSString * const LOG_RESULT_SUCCESS;

extern NSString * const LOG_RESULT_FAILED;

extern NSString * const kdsEventStartServiceDiscovery;

extern NSString * const kdsStopServiceDiscovery;

extern NSString * const kdsSearchServiceWithTimeout;

extern NSString * const kdsSendMdnsEvent;

extern NSString * const kdsWSCreate;

extern NSString * const kdsWSConnect;

extern NSString * const kdsWSSend;

extern NSString * const kdsWSClose;

extern NSString * const kdsWSExit;

extern NSString * const kdsWSGetSocketState;

extern NSString * const SERVER_START;

extern NSString * const SERVER_CLOSE;

extern NSString * const CLIENT_MESSAGE;
