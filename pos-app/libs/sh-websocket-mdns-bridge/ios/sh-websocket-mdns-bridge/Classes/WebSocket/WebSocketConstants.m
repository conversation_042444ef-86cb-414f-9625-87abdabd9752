//
//  WebSocketConstants.m
//  storehub-websocket
//
//  Created by <PERSON> on 2023/11/9.
//

#import "WebSocketConstants.h"

NSString * const WEBSOCKET_CLIENT_RECEIVE_MESSAGE_NOTIFICATION = @"WEBSOCKET_CLIENT_RECEIVE_MESSAGE_NOTIFICATION";

NSString * const WEBSOCKET_CLIENT_CLOSED_NOTIFICATION = @"WEBSOCKET_CLIENT_CLOSED_NOTIFICATION";

NSString * const WEBSOCKET_SERVER_RECEIVE_MESSAGE_NOTIFICATION = @"WEBSOCKET_SERVER_RECEIVE_MESSAGE_NOTIFICATION";

NSString * const WEBSOCKET_SERVER_CLOSED_NOTIFICATION = @"WEBSOCKET_SERVER_CLOSED_NOTIFICATION";

NSString * const WEBSOCKET_SERVER_OPEN_NOTIFICATION = @"WEBSOCKET_SERVER_OPEN_NOTIFICATION";

NSString * const WEBSOCKET_SERVER_ERROR_NOTIFICATION = @"WEBSOCKET_SERVER_ERROR_NOTIFICATION";

NSString * const WEBSOCKET_SERVER_FREE_PORT_NOTIFICATION = @"WEBSOCKET_SERVER_FREE_PORT_NOTIFICATION";

NSString * const MDNS_SERVICE_LOST_NOTIFICATION = @"MDNS_SERVICE_LOST_NOTIFICATION";

const int CONNECT_TIMEOUT = 5;

NSString * const WEBSOCKET_CLIENT_EVENT_NAME = @"websocketClientStateChanged";

NSString * const WEBSOCKET_SERVER_EVENT_NAME = @"websocketServerStateChanged";

NSString * const CLIENT_STATUS_CONNECTED = @"ClientStatusConnected";

NSString * const CLIENT_STATUS_CLOSED = @"ClientStatusClosed";

NSString * const KEY_SOCKET_ID = @"socketId";

NSString * const KEY_PAYLOAD = @"payload";

NSString * const KEY_REMOTE_ADDRESS = @"remoteAddress";

NSString * const KEY_REMOTE_PORT = @"remotePort";

NSString * const KEY_SOCKET_LOCAL_PORT = @"socketLocalPort";

NSString * const KEY_SOCKET_EVENT = @"socketEvent";

NSString * const KEY_CODE = @"code";

NSString * const KEY_REASON = @"reason";

NSString * const KEY_FROM_REMOTE = @"fromRemote";

NSString * const KEY_ERROR = @"error";

NSString * const NOT_CONNECTED = @"NotConnected";

NSString * const CONNECTING = @"connecting";

NSString * const OPEN = @"Connected";

NSString * const CLOSING = @"Closing";

NSString * const CLOSED = @"Closed";

NSString * const DESTROYED = @"Destroyed";

NSString * const KEY_MESSAGE = @"message";

NSString * const RECEIVE_MESSAGE = @"ReceiveMessage";

NSString * const MDNS_DOMAIN = @"local.";

NSString * const MDNS_TYPE = @"shws";

NSString * const MDNS_STATE_CHANGED = @"mdnsStateChanged";

NSString * const SERVICE_NAME = @"serviceName";

NSString * const SERVICE_ADDRESS = @"serviceAddress";

NSString * const SERVICE_PORT = @"servicePort";

NSString * const SERVICE_EXTRAS = @"extras";

NSString * const SERVICE_FOUND = @"ServiceFound";

NSString * const SERVICE_LOST = @"ServiceLost";

NSString * const X_SOCKETID = @"X-StoreHub-Socket-Id";

NSString * const X_PAYLOAD = @"X-StoreHub-Socket-Payload";

NSString * const LOG_LEVEL_INFO = @"info";

NSString *const LOG_LEVEL_ERROR = @"error";

NSString *const LOG_RESULT_START = @"start";

NSString *const LOG_RESULT_SUCCESS = @"success";

NSString *const LOG_RESULT_FAILED = @"failed";

NSString * const kdsEventStartServiceDiscovery = @"startServiceDiscovery";

NSString * const kdsStopServiceDiscovery = @"stopServiceDiscovery";

NSString * const kdsSearchServiceWithTimeout = @"searchServiceWithTimeout";

NSString * const kdsSendMdnsEvent = @"sendMdnsEvent";

NSString * const kdsWSCreate = @"kdsWSCreate";

NSString * const kdsWSConnect = @"kdsWSConnect";

NSString * const kdsWSSend = @"kdsWSSend";

NSString * const kdsWSClose = @"kdsWSClose";

NSString * const kdsWSExit = @"kdsWSExit";

NSString * const kdsWSGetSocketState = @"kdsWSGetSocketState";

NSString * const SERVER_START = @"ServerStart";

NSString * const SERVER_CLOSE = @"ServerClose";

NSString * const CLIENT_MESSAGE = @"ClientMessage";
