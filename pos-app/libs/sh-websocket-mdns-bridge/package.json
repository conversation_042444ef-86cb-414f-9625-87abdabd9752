{"name": "sh-websocket-mdns-bridge", "version": "1.0.0", "description": "WebSocket+mDNS", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "tsc -b"}, "author": "<PERSON>", "license": "UNLICENSED", "private": true, "peerDependencies": {"react": "*", "react-native": ">=0.60.0-rc.0 <1.0.x"}, "devDependencies": {"@types/react-native": "0.63.37", "typescript": "^5.3.3", "react": "18.2.0", "react-native": "^0.71.0"}, "dependencies": {"tslib": "^2.6.2"}, "files": ["android", "ios", "dist", "sh-websocket-mdns-bridge.podspec", "src"]}