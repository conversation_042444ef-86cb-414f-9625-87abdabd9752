#
# Be sure to run `pod lib lint sh-websocket-mdns-bridge.podspec' to ensure this is a
# valid spec before submitting.
#
# Any lines starting with a # are optional, but their use is encouraged
# To learn more about a Podspec see https://guides.cocoapods.org/syntax/podspec.html
#

Pod::Spec.new do |s|
  s.name             = 'sh-websocket-mdns-bridge'
  s.version          = '1.0.0'
  s.summary          = 'A short description of sh-websocket-mdns-bridge.'

# This description is used to generate tags and improve search results.
#   * Think: What does it do? Why did you write it? What is the focus?
#   * Try to keep it short, snappy and to the point.
#   * Write the description between the DESC delimiters below.
#   * Finally, don't worry about the indent, <PERSON><PERSON><PERSON><PERSON> strips it!

  s.description      = <<-DESC
TODO: Add long description of the pod here.
                       DESC

  s.homepage         = 'https://github.com/Stephen Sun/sh-websocket-mdns-bridge'
  # s.screenshots     = 'www.example.com/screenshots_1', 'www.example.com/screenshots_2'
  s.license          = { :type => 'MIT', :file => 'LICENSE' }
  s.author           = { '<PERSON>' => '<EMAIL>' }
  s.source           = { :git => 'https://github.com/Stephen Sun/sh-websocket-mdns-bridge.git', :tag => s.version.to_s }
  # s.social_media_url = 'https://twitter.com/<TWITTER_USERNAME>'

  s.ios.deployment_target = '10.0'

  s.subspec 'Core' do |ss|
    ss.source_files = ['ios/**/*.{h,m}', 'ios/**/*.swift']
    ss.exclude_files  = ['ios/Example']
    ss.dependency 'React'
    ss.dependency 'SocketRocket'
  end
  
  # s.resource_bundles = {
  #   'sh-websocket-mdns-bridge' => ['sh-websocket-mdns-bridge/Assets/*.png']
  # }

  # s.public_header_files = 'Pod/Classes/**/*.h'
  # s.frameworks = 'UIKit', 'MapKit'
  # s.dependency 'AFNetworking', '~> 2.3'
end
