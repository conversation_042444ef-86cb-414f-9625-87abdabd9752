import { NativeEventEmitter, NativeModules } from 'react-native';

const { MdnsDiscoveryModule } = NativeModules;

export const MDNS_EVENT = 'mdnsStateChanged';
export const MdnsNative = MdnsDiscoveryModule as MdnsType;

export const MdnsEmitter = new NativeEventEmitter(MdnsDiscoveryModule);

export type MdnsType = {
  // storename
  // port 40405-40840
  // 先启动websocket server, 获取port，再启动mdns，传入serverPort
  startService(serviceName: string, serverPort: number, extras: MdnsServiceExtras): Promise<any>;
  stopService(): Promise<any>;

  startServiceDiscovery(serviceName: string): Promise<any>;
  stopServiceDiscovery(): Promise<any>;
  searchService(serviceName: string, timeout: number): Promise<MdnsServiceType[]>;
};

// entry count < 10
// key + value length must < 255 bytes
// total length should < 400 bytes
// total length must < 1300 bytes
export type MdnsServiceExtras = {
  id: string;
  alias: string;
  version: string;
};

export type MdnsServiceType = {
  serviceName: string;
  serviceAddress: string;
  servicePort: number;
  extras: MdnsServiceExtras;
};

export enum MdnsEvent {
  serviceFound = 'ServiceFound',
  serviceLost = 'ServiceLost',
}

export type MdnsServiceFoundType = {
  event: MdnsEvent;
  service: MdnsServiceType;
};

export type MdnsServiceLostType = {
  event: MdnsEvent;
  service: MdnsServiceType;
};

export type MdnsMessageType = MdnsServiceFoundType | MdnsServiceLostType;
