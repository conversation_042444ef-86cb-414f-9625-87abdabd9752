import { NativeEventEmitter, NativeModules } from 'react-native';
import { WebsocketId, WebsocketState } from './websocket';

const { WebSocketClientModule } = NativeModules;

export const WS_CLIENT_EVENT = 'websocketClientStateChanged';
export const WebsocketClient = WebSocketClientModule as WebsocketClientType;
export const WebsocketClientEmitter = new NativeEventEmitter(WebSocketClientModule);
export enum WebsocketClientEvent {
  connecting = 'ClientStatusConnecting',
  connected = 'ClientStatusConnected',
  closing = 'ClientStatusClosing',
  closed = 'ClientStatusClosed',
  message = 'ReceiveMessage',
  // client server都会主动发起ping
  ping = 'ReceivePing',
  pong = 'ReceivePong',
}

export type ClientConnectingType = {
  socketEvent: WebsocketClientEvent.connecting;
  socketId: string;
};

export type ClientConnectedType = {
  socketEvent: WebsocketClientEvent.connected;
  socketId: string;
  status: string;
  statusMsg: string;
};
export type ClientClosingType = {
  socketEvent: WebsocketClientEvent.closing;
  socketId: string;
  code: string;
  reason: string;
  fromRemote: boolean;
};

export type ClientClosedType = {
  socketEvent: WebsocketClientEvent.closed;
  socketId: string;
  code: string;
  reason: string;
  fromRemote: boolean;
};

export type ClientReceiveMessageType = {
  socketEvent: WebsocketClientEvent.message;
  socketId: string;
  message: string;
};

export type ClientPingType = {
  socketEvent: WebsocketClientEvent.ping;
  socketId: string;
};

export type ClientPongType = {
  socketEvent: WebsocketClientEvent.pong;
  socketId: string;
};

export type ClientMessageType =
  | ClientConnectingType
  | ClientConnectedType
  | ClientClosingType
  | ClientClosedType
  | ClientPingType
  | ClientPongType
  | ClientReceiveMessageType;

export type WebsocketClientType = {
  create(config: ClientCreateType): Promise<WebsocketId>;
  connect(clientId: WebsocketId): Promise<void>;
  send(clientId: WebsocketId, message: string): Promise<void>;
  close(clientId: WebsocketId): Promise<void>;
  exitApp(): void;
  getSocketState(id: string): Promise<WebsocketState>;
  getRemoteHost(clientId: WebsocketId): Promise<string>;
  getRemotePort(clientId: WebsocketId): Promise<string>;
  getLocalHost(clientId: WebsocketId): Promise<string>;
  getLocalPort(clientId: WebsocketId): Promise<string>;
};

export type ClientCreateType = {
  remoteAddress: string;
  remotePort: string;
  // max length 4096bytes
  payload: string;
};

export type ClientPayloadType = {
  registerId: string;
  registerObjectId: string;
  storeId: string;
  deviceId: string;
  version: string;
  registerName: string;
  businessName: string;
};
