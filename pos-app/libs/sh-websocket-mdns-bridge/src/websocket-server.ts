import { NativeEventEmitter, NativeModules } from 'react-native';
import { WebsocketId, WebsocketState } from './websocket';

const { WebSocketServerModule } = NativeModules;
export const WebsocketServer = WebSocketServerModule as WebsocketServerType;
export const WebsocketServerEmitter = new NativeEventEmitter(WebSocketServerModule);
export const WS_SERVER_EVENT = 'websocketServerStateChanged';

export type WebsocketServerType = {
  startServer(port: number): Promise<void>;
  stopServer(): Promise<void>;
  send(clientId: WebsocketId, message: string): Promise<void>;
  broadcast(message: string): Promise<void>;
  close(client: WebsocketId): Promise<void>;
  // get client status
  getSocketState(clientId: WebsocketId): Promise<WebsocketState>;
  exitApp(): Promise<void>;
  getLocalHost(): Promise<string>;
  getLocalPort(): Promise<string>;
  getRemoteHost(clientId: WebsocketId): Promise<string>;
  getRemotePort(clientId: WebsocketId): Promise<string>;
};

export enum WebsocketServerEvent {
  start = 'ServerStart',
  close = 'ServerClose',
  clientConnected = 'ClientStatusConnected',
  clientClosing = 'ClientStatusClosing',
  clientClosed = 'ClientStatusClose',
  clientMessage = 'ClientMessage',
  ping = 'ReceivePing',
  pong = 'ReceivePong',
}

export type ServerStartType = {
  socketEvent: WebsocketServerEvent.start;
  socketId: string;
  socketLocalPort: string;
  socketLocalAddress: string;
};

export type ServerCloseType = {
  socketEvent: WebsocketServerEvent.close;
  socketId: null;
};
export type ServerClientConnectedType = {
  socketEvent: WebsocketServerEvent.clientConnected;
  socketId: string;
  remoteAddress: string;
  remotePort: string;
  payload: string;
};

export type ServerClientClosingType = {
  socketEvent: WebsocketServerEvent.clientClosing;
  socketId: string;
  code: string;
  reason: string;
  fromRemote: boolean;
};

export type ServerClientClosedType = {
  socketEvent: WebsocketServerEvent.clientClosed;
  socketId: string;
  code: string;
  reason: string;
  fromRemote: boolean;
};

export type ServerReceiveMessageType = {
  socketEvent: WebsocketServerEvent.clientMessage;
  socketId: string;
  message: string;
};

export type ServerPingType = {
  socketEvent: WebsocketServerEvent.ping;
  socketId: string;
};

export type ServerPongType = {
  socketEvent: WebsocketServerEvent.pong;
  socketId: string;
};

export type ServerMessageType =
  | ServerStartType
  | ServerCloseType
  | ServerClientConnectedType
  | ServerClientClosingType
  | ServerClientClosedType
  | ServerReceiveMessageType
  | ServerPingType
  | ServerPongType;

export const WebsocketServerStatusEvent = [WebsocketServerEvent.start, WebsocketServerEvent.close];
export const WebsocketClientStatusEvent = [WebsocketServerEvent.clientConnected, WebsocketServerEvent.clientClosing, WebsocketServerEvent.clientClosed];
export const WebsocketClientMessageEvent = [WebsocketServerEvent.clientMessage];

export const WebsocketHeartBeatEvent = [WebsocketServerEvent.ping, WebsocketServerEvent.pong];
