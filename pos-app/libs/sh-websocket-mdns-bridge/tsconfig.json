{"include": ["src/**/*.ts"], "compileOnSave": false, "compilerOptions": {"tsBuildInfoFile": "dist/tsconfig.tsbuildinfo", "target": "es5", "module": "commonjs", "strict": true, "lib": ["es2015", "es2016", "esnext"], "jsx": "react-native", "composite": true, "declaration": true, "declarationMap": true, "pretty": true, "moduleResolution": "node", "downlevelIteration": true, "esModuleInterop": true, "strictNullChecks": true, "noImplicitAny": true, "sourceMap": true, "noEmitOnError": false, "experimentalDecorators": true, "noUnusedLocals": true, "importHelpers": true, "skipLibCheck": true, "resolveJsonModule": true, "outDir": "./dist", "rootDir": "./src"}, "exclude": ["node_modules", "**/*.test.ts?(x)"]}