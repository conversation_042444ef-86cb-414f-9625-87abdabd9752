/**
 * Metro configuration for React Native
 * https://github.com/facebook/react-native
 *
 * @format
 */

const fs = require('fs');
const path = require('path');
const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');
const defaultConfig = getDefaultConfig(__dirname);
const { assetExts, sourceExts } = defaultConfig.resolver;

const { withSentryConfig } = require('@sentry/react-native/metro');

let ENVIRONMENT = 'PRO';
const envs = fs.readFileSync(path.join(__dirname, '.env'), 'utf-8');
const match = envs.match(/\bENVIRONMENT\b\s*=\s*"*(\w+)"*/);
if (match) {
  ENVIRONMENT = match[1];
}
const isProPackage = ENVIRONMENT === 'PRO' || ENVIRONMENT === 'UAT';
function processModuleFilter(module) {
  if (isProPackage && module.path.indexOf('ts/components/test') > -1) {
    return false;
  }
  return true;
}

const customConfig = {
  transformer: {
    babelTransformerPath: require.resolve('react-native-svg-transformer/react-native'),
    getTransformOptions: async () => {
      return {
        transform: {
          experimentalImportSupport: false,
          inlineRequires: true,
        },
      };
    },
    minifierPath: 'metro-minify-terser',
    minifierConfig: {
      compress: {
        toplevel: true,
        dead_code: true,
        drop_console: isProPackage,
        global_defs: isProPackage
          ? {
            __DEV__: false,
            IS_DEV_FAT: false,
          }
          : undefined,
      },
      output: {
        comments: false,
        indent_level: 2,
        quote_style: 0,
      },
    },
  },
  serializer: {
    processModuleFilter,
  },
  resolver: {
    assetExts: assetExts.filter(ext => ext !== 'svg'),
    sourceExts: [...sourceExts, 'svg'],
  },
};

/**
 * Metro configuration
 * https://reactnative.dev/docs/metro
 *
 * @type {import('metro-config').MetroConfig}
 */
const metroConfig = mergeConfig(defaultConfig, customConfig);
const config = withSentryConfig(metroConfig, {
  annotateReactComponents: false,
});

module.exports = config;
