{"name": "pos", "version": "0.0.1", "private": true, "engines": {"node": ">=18.19.0"}, "scripts": {"start": "react-native start", "lint": "eslint ts --ext .ts,.js,.tsx,.jsx", "lint-fix": "eslint ts --ext .ts,.js,.tsx,.jsx --fix", "lint-quiet": "eslint ts --ext .ts,.js,.tsx,.jsx --quiet", "compile": "tsc", "compile-dev": "tsc --watch|| echo ''", "test": "jest --maxWorkers=2", "test:dev": "jest --silent", "test:coverage": "jest --maxWorkers=2 --coverage --silent --coverageDirectory=./coverage", "test:coverage-json": "jest --maxWorkers=2 --coverage --coverageReporters=json-summary --coverageDirectory=./coverage", "uml": "tsuml2 --glob \"./ts/models/**/*.ts\" -o ./uml/uml_diagram.svg -m true", "bundle-android-dev": "rm -rf tmp && mkdir tmp && react-native bundle --platform android --dev true --entry-file index.js  --bundle-output tmp/index.android.bundle  --assets-dest tmp/ --sourcemap-output  tmp/index.android.bundle.map", "bundle-android": "rm -rf tmp && mkdir tmp && react-native bundle --platform android --dev false --entry-file index.js  --bundle-output tmp/index.android.bundle  --assets-dest tmp/ --sourcemap-output  tmp/index.android.bundle.map", "bundle-android-visualizer": "rm -rf bundle && mkdir bundle && react-native-bundle-visualizer --platform android --dev false --entry-file index.js --bundle-output ./bundle/temp.bundle --reset-cache", "find-error-position": "node scripts/source-map-position", "run-android:debug": "react-native run-android --mode FatDebug --appIdSuffix test  --main-activity MainActivity", "run-android:release": "react-native run-android --mode ProRelease", "run-ios:debug": "react-native run-ios --scheme pos-development --simulator='iPad Air 2' --verbose", "code-push": "npx code-push-standalone", "postinstall": "patch-package && bash hash.sh", "patch-package": "patch-package && bash hash.sh"}, "lint-staged": {"*.ts": ["prettier --write", "git add"], "*.tsx": ["prettier --write", "git add"]}, "dependencies": {"@cloudflare/speedtest": "^1.4.0", "@dominicvonk/react-native-apk-installer": "^2.2.2", "@google/generative-ai": "^0.24.1", "@growthbook/growthbook": "^1.5.1", "@intercom/intercom-react-native": "^6.1.0", "@react-native-async-storage/async-storage": "1.14.0", "@react-native-community/masked-view": "0.1.10", "@react-native-community/netinfo": "^9.3.7", "@react-native-community/push-notification-ios": "^1.10.0", "@react-native-voice/voice": "^3.2.4", "@react-native/babel-preset": "0.73.21", "@react-native/eslint-config": "0.73.2", "@react-native/metro-config": "^0.73.5", "@react-native/typescript-config": "0.73.1", "@react-navigation/bottom-tabs": "^6.5.20", "@react-navigation/compat": "^5.3.20", "@react-navigation/drawer": "6.6.15", "@react-navigation/native": "^6.1.17", "@react-navigation/native-stack": "^6.9.26", "@react-navigation/stack": "^6.3.29", "@sentry/react-native": "5.32.0", "@shopify/flash-list": "^1.7.2", "@xstate/fsm": "^2.1.0", "ahooks": "^3.7.0", "buffer": "^4.5.1", "calculator-lib": "3.9.2-beta4", "crypto-js": "^4.1.1", "currency.js": "^1.2.1", "email-validator": "^2.0.4", "graphql": "^15.4.0", "graphql-request": "3.3.0", "hermes-engine": "^0.11.0", "i18n-js": "^3.8.0", "immutable": "3.8.2", "js-base64": "^3.7.2", "libphonenumber-js": "^1.11.9", "mixpanel-react-native": "^2.4.0", "moment": "^2.29.1", "moment-timezone": "^0.5.32", "os-browserify": "^0.3.0", "patch-package": "^5.0.0", "postinstall-postinstall": "^2.1.0", "query-string": "^9.0.0", "react": "^18.0.0", "react-native": "0.73.10", "react-native-action-button": "^2.8.5", "react-native-animatable": "^1.3.3", "react-native-background-timer": "^2.4.1", "react-native-bundle-visualizer": "^3.1.3", "react-native-code-push": "^8.2.1", "react-native-collapsible": "^1.6.0", "react-native-config": "1.4.1", "react-native-device-info": "8.4.8", "react-native-external-display": "^0.6.6", "react-native-fast-image": "8.5.11", "react-native-flash-message": "^0.1.17", "react-native-fs": "^2.16.6", "react-native-geolocation-service": "^5.3.0-beta.4", "react-native-gesture-handler": "^2.16.0", "react-native-get-random-values": "^1.9.0", "react-native-intent-launcher": "^0.2.1", "react-native-keyboard-aware-scroll-view": "^0.9.3", "react-native-languages": "^3.0.2", "react-native-linear-gradient": "^2.8.3", "react-native-localize": "^2.0.1", "react-native-material-menu": "2.0.0", "react-native-pager-view": "^5.4.9", "react-native-performance-stats": "^0.2.3", "react-native-permissions": "^4.1.5", "react-native-progress": "^5.0.1", "react-native-qrcode-svg": "^6.1.2", "react-native-reanimated": "3.8.1", "react-native-reanimated-carousel": "^3.5.1", "react-native-safe-area-context": "^4.9.0", "react-native-screens": "^3.31.0", "react-native-scrollable-tab-view": "https://github.com/storehubnet/react-native-scrollable-tab-view", "react-native-section-list-get-item-layout": "^2.2.3", "react-native-shadow-2": "^5.1.2", "react-native-simple-radio-button": "2.7.4", "react-native-splash-screen": "3.2.0", "react-native-svg": "^12.3.0", "react-native-swipe-list-view": "3.2.5", "react-native-swiper": "^1.6.0", "react-native-table-component": "^1.2.1", "react-native-update": "10.28.4", "react-native-vector-icons": "10.1.0", "react-native-view-shot": "^3.8.0", "react-native-webrtc": "^124.0.5", "react-native-webview": "13.8.4", "react-redux": "^7.2.9", "realm": "12.6.2", "recyclerlistview": "^3.0.5", "redux": "^4.2.1", "redux-actions": "^2.6.5", "redux-async-initial-state": "^0.3.0", "redux-immutable": "^4.0.0", "redux-persist": "^6.0.0", "redux-persist-immutable": "^4.3.1", "redux-saga": "1.3.0", "reselect": "4.0.0", "sh-websocket-mdns-bridge": "file:./libs/sh-websocket-mdns-bridge", "uuid": "3.3.2"}, "devDependencies": {"@babel/core": "^7.24.4", "@babel/plugin-proposal-decorators": "^7.24.1", "@babel/plugin-transform-template-literals": "^7.24.1", "@babel/runtime": "^7.24.4", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-hooks": "^8.0.1", "@testing-library/react-native": "^11.0.0", "@types/crypto-js": "^4.1.1", "@types/jest": "^29.5.12", "@types/lodash": "4.14.178", "@types/react": "^18.2.79", "@types/react-dom": "^18.0.0", "@types/react-native": "0.72.8", "@types/react-native-background-timer": "^2.0.2", "@types/react-native-simple-radio-button": "^2.7.2", "@types/react-redux": "^7.1.24", "@types/react-test-renderer": "^18.0.0", "@types/redux-actions": "^2.6.2", "@typescript-eslint/eslint-plugin": "^8.19.0", "@typescript-eslint/parser": "^8.19.0", "@welldone-software/why-did-you-render": "^8.0.3", "@wojtekmaj/enzyme-adapter-react-17": "^0.6.6", "appcenter-cli": "^2.14.0", "babel-jest": "^29.7.0", "babel-plugin-import": "^1.13.8", "babel-plugin-transform-remove-console": "^6.9.4", "babel-preset-es2015": "^6.24.1", "babel-preset-react": "^6.24.1", "code-push-cli": "file:./libs/code-push-cli", "dl-react-native-mock-render": "https://github.com/storehubnet/dl-react-native-mock-render", "enzyme": "^3.10.0", "enzyme-adapter-react-16": "^1.15.1", "enzyme-to-json": "^3.3.5", "eslint": "^8.19.0", "eslint-import-resolver-typescript": "^3.7.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-react": "^7.37.3", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-native": "^4.1.0", "eslint-plugin-sonarjs": "^1.0.3", "eslint-plugin-state-defaultvalue": "file:./libs/eslint-plugin-state-defaultvalue", "eslint-plugin-test-props": "file:./libs/eslint-plugin-test-props", "eslint-plugin-unused-imports": "^4.1.4", "husky": "^3.1.0", "jest": "^29.7.0", "jetifier": "^1.6.6", "jsdom": "16.4.0", "lint-staged": "^9.5.0", "prettier": "3.5.3", "react-dom": "^18.0.0", "react-native-mock-render": "^0.1.9", "react-native-svg-transformer": "^1.5.0", "react-native-update-cli": "1.44.1", "react-test-renderer": "^18.2.0", "reactotron-react-native": "5.1.6", "reactotron-redux": "^3.1.3", "reactotron-redux-saga": "^4.2.3", "redux-mock-store": "^1.5.4", "redux-saga-test-plan": "4.0.1", "shelljs": "^0.8.3", "ts-jest": "28.0.8", "ts-node": "^10.9.2", "tsuml2": "^0.16.0", "typescript": "5.0.4"}, "resolutions": {"@types/react": "^18.2.79", "@types/react-native": "^0.72.8", "@types/react-dom": "^18.0.0", "graphql": "^16.5.0"}}