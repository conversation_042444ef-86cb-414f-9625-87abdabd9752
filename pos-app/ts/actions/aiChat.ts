import { createAction } from 'redux-actions';

export interface ActionButton {
  label: string;
  message: string; // The message to send to chat when button is clicked
  type?: 'primary' | 'secondary' | 'danger';
}

export interface ToolCall {
  toolName: string;
  parameters?: any;
}

export interface ToolResult {
  toolName: string;
  result: any;
  error?: string;
}

export interface ChatMessage {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
  type?: 'text' | 'navigation' | 'order' | 'error' | 'system' | 'tool_call' | 'tool_result';
  actionButtons?: ActionButton[];
  toolCall?: ToolCall; // For AI requesting tools
  toolResult?: ToolResult; // For app sending tool results back to AI
}

export type SendMessagePayload = {
  message: string;
};

export type ReceiveMessagePayload = {
  message: ChatMessage;
};

export type SetChatVisibilityPayload = {
  isVisible: boolean;
};

export type ProcessAIResponsePayload = {
  userMessage: string;
  response: string;
  intent: 'navigation' | 'order' | 'general' | 'help' | 'system' | 'tool_call';
  actionButtons?: ActionButton[];
  toolCall?: ToolCall;
  params?: {
    route?: string; // Exact route name for navigation intent
  };
};

// Actions
export const sendChatMessage = createAction<SendMessagePayload>('SEND_CHAT_MESSAGE');
export const receiveChatMessage = createAction<ReceiveMessagePayload>('RECEIVE_CHAT_MESSAGE');
export const setChatVisibility = createAction<SetChatVisibilityPayload>('SET_CHAT_VISIBILITY');
export const processAIResponse = createAction<ProcessAIResponsePayload>('PROCESS_AI_RESPONSE');
export const clearChatHistory = createAction('CLEAR_CHAT_HISTORY');
export const toggleChatMinimized = createAction('TOGGLE_CHAT_MINIMIZED');
