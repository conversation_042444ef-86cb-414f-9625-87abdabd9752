import { createAction } from 'redux-actions';
import { AuthorizationType, CashierActions } from '../constants';

export type RequestActionAccessResult<T = any> = { ok: boolean; message?: string; needDoubleConfirm?: boolean; data?: T };

export interface RequestActionAccessCallback {
  callback(payload: RequestActionAccessResult): void;
}

export type OrderType = {
  isNew?: boolean;
  isSaved?: boolean;
};

export interface RequestAuthorizedActionType {
  employeeId?: string;
  type: AuthorizationType;
  name: CashierActions;
  content?: OrderType;
  goHomeWhenClose?: boolean;
  onSuccess?: RequestActionAccessCallback;
  onFailure?: RequestActionAccessCallback;
}

export const requestAuthorizedAction = createAction<RequestAuthorizedActionType>('requestAuthorizedAction');

export const requestCameraPermissionAction = createAction('requestCameraAuthorizedAction');

export const setCameraPermissionStatusAction = createAction<boolean>('setCameraPermissionStatusAction');

export const jumpToSettingsAction = createAction('jumpToSettingsAction');

export interface CheckCloseZReadingAccessType {
  onSuccess?: RequestActionAccessCallback;
  onFailure?: RequestActionAccessCallback;
}

export const checkCloseZReadingAccess = createAction<CheckCloseZReadingAccessType>('checkCloseZReadingAccess');
