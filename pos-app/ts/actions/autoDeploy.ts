import { createAction } from 'redux-actions';

export const checkAutoDeployments = createAction('checkAutoDeployments');

const CODE_PUSH_SERVER = 'https://dev.storehubdeoloy.dpdns.org';

export const getAppsAsync = async (accountId: string) => {
  const url = `${CODE_PUSH_SERVER}/v0.1/public/codepush/internal/apps?account_id=${accountId}`;
  const response = await fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status} ${response.statusText}`);
  }
  return await response.json();
};

export const getDeploymentsAsync = async (accountId: string, appId: string) => {
  const url = `${CODE_PUSH_SERVER}/v0.1/public/codepush/internal/deployments?account_id=${accountId}&app_id=${appId}`;
  const response = await fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status} ${response.statusText}`);
  }
  return await response.json();
};

export const getDeploymentAsync = async (deploymentKey: string) => {
  const url = `${CODE_PUSH_SERVER}/v0.1/public/codepush/internal/deployment?deployment_key=${deploymentKey}`;
  const response = await fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status} ${response.statusText}`);
  }
  return await response.json();
};
