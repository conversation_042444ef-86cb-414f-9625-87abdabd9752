import { createAction } from 'redux-actions';

export type SyncInfoType = {
  employeeInfoSync?: boolean;
  lastEmployeeSyncTime?: string;
  productInfoSync?: boolean;
  lastProductSyncTime?: string;
  lastTrxCancelledFromBOSyncTime?: string;
  priceBookInfoSync?: boolean;
  lastPriceBookSyncTime?: string;
  onlineSyncTime?: string;
  needFixsequential?: boolean;
  needToSyncAll?: boolean;
  promotionInfoSync?: boolean;
  lastPromotionSyncTime?: any;
  lastZReadingCloseTime?: string;
  lastSyncTime?: string;

  lastProductSyncStartedTime?: string;
  lastProductSyncFileSize?: number;
  lastProductSyncPosition?: number;

  lastProductImagesSyncCount?: number;
  lastProductImagesSyncProgress?: number;
  lastProductImagesSyncFailedProgress?: number;
};

export const setSyncInfo = createAction<SyncInfoType>('setSyncInfo');

export const syncAllStoreInfo = createAction<SyncProgressType>('syncAllStoreInfo');
export interface SyncProgressType {
  onSyncProgress?(type, page?): void;
}

export interface SyncInputType {
  isInitial?: boolean;
  onSyncProgress?: any;
  onComplete?: any;
}

export interface SyncTransactionsActionType extends SyncInputType {
  onComplete?: any;
  transactionId?: string;
  isCheckingBeforeCloseZreading?: boolean;
}

export interface MigrateToNewPOSType {
  onSuccess?: any;
  onFailure?: any;
  token: string;
  business: string;
  registerId: string;
}

export type CleanLocalStorageType = boolean; // start or stop

export interface CheckFreeStorageType {
  navigation: any;
  onContinue: any;
}

export const syncTransactionsAction = createAction<SyncTransactionsActionType>('syncTransactionsAction');
export const syncShiftsAction = createAction<SyncInputType>('syncShiftsAction');
export const syncProductsAction = createAction<SyncInputType>('syncProductsAction');
export const syncEmployeesAction = createAction<SyncInputType>('syncEmployeesAction');
export const syncQuickSelectLayoutAction = createAction<SyncInputType>('syncQuickSelectLayoutAction');
export const syncTableLayoutAction = createAction<SyncInputType>('syncTableLayoutAction');
export const syncStoreInfoAction = createAction<SyncInputType>('syncStoreInfoAction');
export const syncPriceBookAction = createAction<SyncInputType>('syncPriceBookAction');
export const syncPromotionsBegin = createAction<SyncInputType>('syncPromotionsBegin');
export const syncEmployeeActivityBegin = createAction<SyncInputType>('syncEmployeeActivityBegin');
export const syncSequentialStartInfoBegin = createAction<SyncInputType>('syncSequentialStartInfoBegin');
export const migrateTableLayoutAction = createAction<SyncInputType>('migrateTableLayoutAction');
export const readFaceCaptureIntro = createAction('readFaceCaptureIntro');
export const setHaveUsedFaceCapture = createAction<boolean>('setHaveUsedFaceCapture');
export const trackLocalSettings = createAction('trackLocalSettings');
export const syncLastZReadingCloseTime = createAction('syncLastZReadingCloseTime');
export const uploadLocalDataToS3 = createAction('uploadLocalDataToS3');
export const migrateToNewPOS = createAction<MigrateToNewPOSType>('migrateToNewPOS');
export const setLocalCountryMap = createAction('setLocalCountryMap');
export const initLocalCountryMap = createAction('initLocalCountryMap');
export const clearLocalCountryMap = createAction('clearLocalCountryMap');
export const logForDefaultNetwork = createAction('logForDefaultNetwork');
export const trackLocalStorage = createAction('trackLocalStorage');
export const cleanLocalStorage = createAction<CleanLocalStorageType>('cleanLocalStorage');
export const measurePerformanceStatus = createAction('measurePerformanceStatus');
export const parseProductJson = createAction('parseProductJson');
export const syncLargeProductsAction = createAction<SyncInputType>('syncLargeProductsAction');
export const syncLargeProductThumbnails = createAction('syncLargeProductThumbnails');
export const deleteAllProductThumbnails = createAction('deleteAllProductThumbnails');
export const voidAllProductThumbnails = createAction('voidAllProductThumbnails');
export const uploadMemoryInformation = createAction('uploadMemoryInformation');
export const checkFreeStorage = createAction<CheckFreeStorageType>('checkFreeStorage');
