import { createAction } from 'redux-actions';

export type EmployeeReduceItemType = {
  transactionId: string;
  productName: string;
  productId: string;
  quantity: number;
};

export const employeeReduceItem = createAction<EmployeeReduceItemType>('employeeReduceItem');

export type EmployeeRemoveItemType = {
  transactionId: string;
  productName: string;
  productId: string;
  quantity: number;
};

export const employeeRemoveItem = createAction<EmployeeRemoveItemType>('employeeRemoveItem');

export type EmployeePrintReceiptType = {
  transactionId: string;
  isReprint: boolean;
};

export const employeePrintReceipt = createAction<EmployeePrintReceiptType>('employeePrintReceipt');

export type EmployeePrintHalfReceiptType = {
  transactionId: string;
};

export const employeePrintHalfReceipt = createAction<EmployeePrintReceiptType>('employeePrintHalfReceipt');

export type EmployeeDeleteTransactionType = {
  transactionId: string;
  isOpenOrder: boolean;
  amount: number;
};

export const employeeDeleteTransaction = createAction<EmployeeDeleteTransactionType>('employeeDeleteTransaction');

export type EmployeeOpenCashDrawerType = {
  reason: string;
};

export const employeeOpenCashDrawer = createAction<EmployeeOpenCashDrawerType>('employeeOpenCashDrawer');

export type EmployeeApprovePaymentManuallyType = {
  transactionId: string;
  paymentId: string;
};

export const employeeApprovePaymentManually = createAction<EmployeeApprovePaymentManuallyType>('employeeApprovePaymentManually');

export const enablePauseMode = createAction('enablePauseMode');

export const disablePauseMode = createAction('disablePauseMode');

export type EmployeeClockInType = {
  employeeId: string;
  storeId: string;
  clockInTime: string;
  onSuccess?: any;
  onError?: any;
  faceCaptureUri?: string;
};
export const employeeClockIn = createAction<EmployeeClockInType>('employeeClockIn');

export type EmployeeClockOutType = {
  employeeId: string;
  storeId: string;
  business: string;
  clockOutTime: string;
  onSuccess?: any;
  onError?: any;
  faceCaptureUri?: string;
};
export const employeeClockOut = createAction<EmployeeClockOutType>('employeeClockOut');

export type EmployeeCancelOrderType = {
  transactionId: string;
};

export const employeeCancelOrder = createAction<EmployeeCancelOrderType>('employeeCancelOrder');

export type EmployeeRefundType = {
  transactionId: string;
};

export const employeeRefund = createAction<EmployeeRefundType>('employeeRefund');

export type EmployeeApplyManualDiscountType = {
  transactionId: string;
};

export const employeeApplyManualDiscount = createAction<EmployeeApplyManualDiscountType>('employeeApplyManualDiscount');

export type EmployeeEInvoiceActivityType = {
  transactionId: string;
};

export const employeeCancelEInvoice = createAction<EmployeeEInvoiceActivityType>('employeeCancelEInvoice');
export const employeeCancelWithoutEInvoice = createAction<EmployeeEInvoiceActivityType>('employeeCancelWithoutEInvoice');
export const employeeRequestRefund = createAction<EmployeeEInvoiceActivityType>('employeeRequestRefund');
export const employeePrintEInvoiceQrReceipt = createAction<EmployeeEInvoiceActivityType>('employeePrintEInvoiceQrReceipt');

export const employeeOOSEnable = createAction('employeeOOSEnable');
export const employeeOOSDisable = createAction('employeeOOSDisable');
export const employeeOOSTemporaryEnableSingleTxn = createAction('employeeOOSTemporaryEnableSingleTxn');
export const employeeOOSTemporaryEnableNext30Min = createAction('employeeOOSTemporaryEnableNext30Min');
export const employeeOOSTemporaryEnableNext60Min = createAction('employeeOOSTemporaryEnableNext60Min');
export const employeeOOSTemporaryEnableRestOfDay = createAction('employeeOOSTemporaryEnableRestOfDay');
