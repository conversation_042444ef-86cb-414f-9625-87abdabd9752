import { AWS_S3_REQUEST_ACCESS_URL } from '../../config';
import { createTypedHttpAction } from './setup';

export const requestAWSConfig = createTypedHttpAction({
  name: 'requestAWSConfig',
  disableLoadingMask: true,
  preservePayload: false,
  disableErrorInfo: true,
  fetchArgs: () => {
    const options = {
      method: 'get',
      isUseFutch: false,
      body: {},
      excludedAuthToken: false,
    };

    return [`${AWS_S3_REQUEST_ACCESS_URL}?source=POS`, options];
  },
});
