import { gql } from 'graphql-request';
import { API_URL } from '../../config';

import { RequestActionAccessCallback } from '../authorization';

import { CustomerType } from '../transaction';
import { createTypedHttpAction } from './setup';

export interface GetAllCustomerListType {
  businessName: string;
  customerId: string;
}

// 查询所有customer信息（暂不支持关键字搜索功能）
export const getAllCustomersList = createTypedHttpAction<GetAllCustomerListType>({
  name: 'getAllCustomersList',
  disableLoadingMask: false,
  disableErrorInfo: false,
  preservePayload: true,
  fetchArgs: ({ businessName }) => {
    const options = {
      method: 'get',
      isGraphQL: true,
      requireEmployeeId: true,
      body: `
      {
        customers( businessName: "${businessName}") {
            customerId
            firstName
            lastName
            email
            phone
            birthday
            memberId
            street1
            street2
            city
            state
            postalCode
            tags
            totalSpent
            lastPurchaseDate
            totalTransactions
            purchasedInStores
            taxIdNo
            storeCreditsBalance
            storeCreditsSpent
        }
    }
    `,
    };
    return [null, options];
  },
});

export interface GetCustomerByIdType {
  bn: string;
  customerId: string;
}
export const getCustomerById = createTypedHttpAction<GetCustomerByIdType>({
  name: 'getCustomerById',
  disableLoadingMask: false,
  disableErrorInfo: false,
  preservePayload: true,
  needTakeEvery: true,
  fetchArgs: ({ bn, customerId }) => {
    const options = {
      method: 'get',
      isGraphQL: false,
      requireEmployeeId: false,
      body: {},
    };
    return [`${API_URL()}/api/customers?bn=${bn}&customerId=${customerId}&history=true`, options];
  },
});

export const getCustomerByIdMulti = createTypedHttpAction<GetCustomerByIdType>({
  name: 'getCustomerByIdMulti',
  disableLoadingMask: false,
  disableErrorInfo: false,
  preservePayload: true,
  needTakeEvery: true,
  fetchArgs: ({ bn, customerId }) => {
    const options = {
      method: 'get',
      isGraphQL: false,
      requireEmployeeId: false,
      body: {},
    };
    return [`${API_URL()}/api/customers?bn=${bn}&customerId=${customerId}&history=true`, options];
  },
});

export interface SearchCustomerType {
  bn: string;
  searchText: string;
}

export const searchCustomer = createTypedHttpAction<SearchCustomerType>({
  name: 'searchCustomer',
  disableLoadingMask: false,
  disableErrorInfo: false,
  preservePayload: true,
  fetchArgs: ({ bn, searchText }) => {
    const options = {
      method: 'get',
      isGraphQL: false,
      requireEmployeeId: false,
      body: {},
    };
    return [`${API_URL()}/api/searchCustomers?bn=${bn}&searchText=${encodeURIComponent(searchText)}&history=true`, options];
  },
});

export interface AddCustomerType {
  business;
  phone: string;
  modifiedTime: string;
  customerId: string;
  firstName: string;
  lastName: string;
  email: string;
  birthday: string;
  memberId: string;
  tags?: string[];
  street1: string;
  street2: string;
  city: string;
  state: string;
  postalCode: string;
  taxIdNo: string;
  signUpByEmployee: string;
  signUpFromStore: string;
}

export const addCustomer = createTypedHttpAction<AddCustomerType>({
  name: 'addCustomer',
  disableLoadingMask: false,
  disableErrorInfo: true,
  preservePayload: true,
  fetchArgs: ({
    business,
    phone,
    modifiedTime,
    customerId,
    firstName,
    lastName,
    email,
    birthday,
    memberId,
    tags,
    street1,
    street2,
    city,
    state,
    postalCode,
    taxIdNo,
    signUpByEmployee,
    signUpFromStore,
  }) => {
    const options = {
      method: 'post',
      isGraphQL: false,
      requireEmployeeId: false,
      body: {
        business,
        phone,
        modifiedTime,
        customerId,
        firstName,
        lastName,
        email,
        birthday,
        memberId,
        tags,
        street1,
        street2,
        city,
        state,
        postalCode,
        taxIdNo,
        signUpByEmployee,
        signUpFromStore,
      },
    };
    return [`${API_URL()}/api/customers`, options];
  },
});

export const getOrderHistoryByCustomerId = createTypedHttpAction<GetCustomerByIdType>({
  name: 'getOrderHistoryByCustomerId',
  disableLoadingMask: false,
  disableErrorInfo: false,
  preservePayload: true,
  fetchArgs: ({ bn, customerId }) => {
    const options = {
      method: 'get',
      isGraphQL: false,
      requireEmployeeId: false,
      body: {},
    };
    return [`${API_URL()}/api/customerOrderHistory?bn=${bn}&customerId=${customerId}`, options];
  },
});

export const mostSoldProducts = createTypedHttpAction<GetAllCustomerListType>({
  name: 'mostSoldProducts',
  disableLoadingMask: false,
  disableErrorInfo: false,
  preservePayload: true,
  fetchArgs: ({ businessName, customerId }) => {
    const options = {
      method: 'get',
      isGraphQL: true,
      requireEmployeeId: true,
      body: `
      {
        mostSoldProducts(
            businessName: "${businessName}"
            customerId: "${customerId}"
            numberOfProducts: 5
        )
        {
            id
            title
            unitPrice
            hasThumbnail
            parentProductId
        }
      }
    `,
    };
    return [null, options];
  },
});

export interface GetOrderLogListType {
  businessName: string;
  customerId: string;
}

export const getOrderLogList = createTypedHttpAction<GetOrderLogListType>({
  name: 'getOrderLogList',
  disableLoadingMask: false,
  disableErrorInfo: false,
  preservePayload: true,
  fetchArgs: ({ businessName, customerId }) => {
    const options = {
      method: 'get',
      isGraphQL: true,
      requireEmployeeId: true,
      body: `
      {
        transactions(
            businessName: "${businessName}"
            filters: { customerIds: ["${customerId}"] }
            page: 0
            pageSize: 5
            sortOptions: { sortField: "createdTime" sortDesc: true}
        )
        {
          receiptNumber
          total
          createdTime
          items {
            id
            total
            quantity
            title
            itemType
          }
        }
      }
    `,
    };
    return [null, options];
  },
});

// #region customer qr
// customer qr -> share info polling

export interface ShareInfoRequest {
  id: string;
  onSuccess?: RequestActionAccessCallback;
  onFailure?: RequestActionAccessCallback;
}

export const shareInfoRequest = createTypedHttpAction<ShareInfoRequest>({
  name: 'shareInfoRequest',
  disableLoadingMask: true,
  disableErrorInfo: true,
  preservePayload: true,
  fetchArgs: variables => {
    const options = {
      method: 'get',
      isGraphQL: true,
      isNewGraphQL: true,
      requireEmployeeId: true,
      body: gql`
        query shareInfoRequest($id: ID!) {
          shareInfoRequest(id: $id) {
            id
            merchantName
            registerId
            expiredDate
            scannedDate
            source
            consumerId
            customer {
              id
              customerId
              firstName
              lastName
              email
              phone
              street1
              street2
              city
              state
              postalCode
              modifiedTime
              createdTime
              tags
              birthday
              memberId
              taxIdNo
              totalSpent
              lastPurchaseDate
              totalTransactions
              storeCreditsBalance
            }
            sharedInfoDate
            isNewCustomer
            isNewMember
          }
        }
      `,
    };
    return [null, options, variables];
  },
});

// create customer qr
export interface CreateShareInfoRequestType {
  merchantName: string;
  registerId: string;
  storeId: string;
}

export interface ShareCustomerInfoType extends CustomerType {
  id: string;
  totalSpent?: number;
  storeCreditsBalance?: number; // same as loyalty
}
export interface ShareInfoResponse {
  id: string;
  merchantName: string;
  registerId?: string;
  expiredDate?: boolean;
  scannedDate?: boolean;
  source?: string;
  consumerId?: string;
  customer?: ShareCustomerInfoType;
  sharedInfoDate?: string;
  QRCode?: string;
}

export const createShareInfoRequest = createTypedHttpAction<CreateShareInfoRequestType>({
  name: 'createShareInfoRequest',
  disableLoadingMask: true,
  disableErrorInfo: true,
  preservePayload: true,
  fetchArgs: ({ registerId, merchantName, storeId }) => {
    const options = {
      method: 'post',
      isGraphQL: true,
      isNewGraphQL: true,
      requireEmployeeId: true,
      body: `mutation
      createShareInfoRequest(
        $merchantName: String!
        $registerId: String
        $storeId: String
      ) {
        createShareInfoRequest(
          merchantName: $merchantName
          registerId: $registerId
          storeId: $storeId
          ){
            id
            QRCode
          }
        }`,
    };
    const variables = {
      registerId,
      merchantName,
      storeId,
    };
    return [null, options, variables];
  },
});

export interface getUniquePromosRequestType {
  businessName: string;
  customerId: string;
}

export const getUniquePromos = createTypedHttpAction<getUniquePromosRequestType>({
  name: 'getUniquePromos',
  disableLoadingMask: false,
  disableErrorInfo: true,
  preservePayload: true,
  fetchArgs: ({ businessName, customerId }) => {
    const options = {
      method: 'post',
      isGraphQL: true,
      isNewGraphQL: true,
      requireEmployeeId: true,
      body: `
        {
          uniquePromos(businessName: "${businessName}", customerId: "${customerId}") {
            id
            appliedStores
            createdTime
            discountType
            discountValue
            appliedStoresName
            appliedSources
            promotionCode
            isEnabled
            maxQuantity
            minQuantity
            modifiedTime
            name
            ordering
            taxCode
            validDays
            validFrom
            validTo
            validTimeFrom
            validTimeTo
            isRepeatable
            isStackable
            conditions {
              entity
              propertyName
              operator
              operand
              minQuantity
              maxQuantity
            }
            requiredProducts {
              entity
              propertyName
              operator
              operand
              minQuantity
              maxQuantity
            }
            uniquePromotionCodeStatus
            uniquePromotionCodeInfo {
              id
              promotionCode
              status
              validFrom
              validTo
            }
          }
        }
      `,
    };
    return [null, options];
  },
});

export interface lockPromoType {
  businessName: string;
  customerId: string;
  promotionId: string;
  promotionCodeId: string;
  transactionId: string;
}
export const lockPromo = createTypedHttpAction<lockPromoType>({
  name: 'lockPromo',
  disableLoadingMask: false,
  disableErrorInfo: false,
  preservePayload: true,
  fetchArgs: ({ promotionId, promotionCodeId, businessName, transactionId, customerId }) => {
    const options = {
      method: 'post',
      isGraphQL: true,
      isNewGraphQL: true,
      requireEmployeeId: true,
      body: `mutation lockPromo(
        $promotionId: String!
        $promotionCodeId: String
        $businessName: String!
        $transactionId: String!
        $customerId: String!
      ) {
        lockPromo(
          promotionId: $promotionId
          promotionCodeId: $promotionCodeId
          businessName: $businessName
          transactionId: $transactionId
          customerId: $customerId
        ){
          success
        }
      }`,
    };
    const variables = {
      promotionId,
      promotionCodeId,
      businessName,
      transactionId,
      customerId,
    };
    return [null, options, variables];
  },
});

export interface returnPromoType {
  promotionId: string;
  promotionCodeId: string;
  businessName: string;
  transactionId: string;
  customerId: string;
}
export const returnPromo = createTypedHttpAction<returnPromoType>({
  name: 'returnPromo',
  disableLoadingMask: false,
  disableErrorInfo: true,
  preservePayload: true,
  fetchArgs: ({ promotionId, promotionCodeId, businessName, transactionId, customerId }) => {
    const options = {
      method: 'post',
      isGraphQL: true,
      isNewGraphQL: true,
      requireEmployeeId: true,
      body: `mutation returnPromo(
        $promotionId: String!
        $promotionCodeId: String
        $businessName: String!
        $transactionId: String!
        $customerId: String!
      ) {
        returnPromo(
          promotionId: $promotionId
          promotionCodeId: $promotionCodeId
          businessName: $businessName
          transactionId: $transactionId
          customerId: $customerId
        ){
          success
        }
      }`,
    };
    const variables = {
      promotionId,
      promotionCodeId,
      businessName,
      transactionId,
      customerId,
    };
    return [null, options, variables];
  },
});

export interface GetCustomerType {
  business: string;
  customerId: string;
  needPointsBalance?: boolean;
  needPointsTotalSpend?: boolean;
  needMembershipDetail?: boolean;
}

export const getCustomer = createTypedHttpAction<GetCustomerType>({
  name: 'getCustomer',
  disableLoadingMask: false,
  disableErrorInfo: false,
  preservePayload: true,
  fetchArgs: ({ business, customerId, needPointsBalance, needPointsTotalSpend, needMembershipDetail }) => {
    const options = {
      method: 'post',
      isGraphQL: true,
      isNewGraphQL: true,
      requireEmployeeId: true,
      body: `
      {
        customer(business: "${business}", customerId: "${customerId}", needPointsBalance: ${needPointsBalance}, needPointsTotalSpend: ${needPointsTotalSpend}, needMembershipDetail: ${needMembershipDetail}) {
          id
          business
          customerId
          tags
          info {
            email
            phone
            city
            state
            street1
            street2
            postalCode
            firstName
            lastName
            birthday
            gender
          }
          taxIdNo
          memberId
          purchaseInfo {
            totalSpent
            lastPurchaseDate
            totalTransactions
            purchasedInStores
          }
          storeCreditInfo {
            storeCreditsBalance
            storeCreditsSpent
            cashbackClaimCount
            lastCashbackClaimDate
            cashbackExpirationDate
          }
          isDeleted
          createdTime
          modifiedTime
          availablePointsBalance
          pointsTotalSpent
          availableUniquePromotionCount
          membershipTierInfo {
            membershipTierId
            membershipSource
            membershipTierName
            membershipTierLevel
            membershipTierTotalSpent
            membershipTierStartTime
            membershipTierNextReviewTime
            membershipJoinTime
          }
        }
      }
      `,
    };
    return [null, options];
  },
});

// #endregion
