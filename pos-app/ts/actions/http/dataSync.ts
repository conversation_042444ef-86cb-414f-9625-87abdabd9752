import { API_URL } from '../../config';
import { CancelledSource } from '../../constants';
import { LocationType } from './auth';
import { createTypedHttpAction } from './setup';

export interface SyncTransactionType {
  business: string;
  registerId?: string;
  lastRegisterId?: string; // record's registerId
  storeId: string;
  recordJSON: any;
  location?: LocationType;
  enableCashback?: boolean;
}

export const syncTransaction = createTypedHttpAction<SyncTransactionType>({
  name: 'syncTransaction',
  disableLoadingMask: true,
  preservePayload: false,
  disableErrorInfo: true,
  timeout: 2 * 60 * 1000,
  fetchArgs: ({ storeId, business, lastRegisterId, recordJSON, location }) => {
    const options = {
      method: 'post',
      body: {
        ...recordJSON,
        storeId,
        business,
        lastRegisterId,
        location,
      },
    };
    return [`${API_URL()}/api/transactionRecords`, options];
  },
});

export interface SyncTransactionCancelledFromBOType {
  storeId: string;
  cancelledAtFrom?: string; // iso string from
  cancelledAtTo?: string; // iso string end
  cancelledSources?: CancelledSource[];
  pageNumber?: number;
  pageSize?: number;
}

export const syncTransactionCancelledFromBO = createTypedHttpAction<SyncTransactionCancelledFromBOType>({
  name: 'syncTransactionCancelledFromBO',
  disableLoadingMask: true,
  preservePayload: false,
  disableErrorInfo: true,
  timeout: 2 * 60 * 1000,
  fetchArgs: ({ storeId, cancelledAtFrom, cancelledAtTo, cancelledSources, pageNumber, pageSize }) => {
    const options = {
      method: 'post',
      body: {
        storeId,
        cancelledAtFrom,
        cancelledAtTo,
        cancelledSources,
        pageNumber,
        pageSize,
      },
    };
    return [`${API_URL()}/api/getCancelledTransactionRecords`, options];
  },
});

export const syncShiftReport = createTypedHttpAction<any>({
  name: 'syncShiftReport',
  disableLoadingMask: true,
  preservePayload: false,
  disableErrorInfo: true,
  timeout: 2 * 60 * 1000,
  fetchArgs: formData => {
    const options = {
      method: 'post',
      body: {
        ...formData,
      },
    };
    return [`${API_URL()}/api/shifts`, options];
  },
});

export const getLastZReadingCloseTime = createTypedHttpAction<any>({
  name: 'getLastZReadingCloseTime',
  disableLoadingMask: true,
  preservePayload: false,
  disableErrorInfo: true,
  fetchArgs: () => {
    const options = {
      method: 'get',
      body: {},
    };
    return [`${API_URL()}/api/lastZReadingCloseTime`, options];
  },
});

export interface SyncEmployeesType {
  business: string;
  storeId: string;
  syncTime: string;
}

export const syncEmployees = createTypedHttpAction<SyncEmployeesType>({
  name: 'syncEmployees',
  disableLoadingMask: true,
  preservePayload: false,
  disableErrorInfo: true,
  timeout: 2 * 60 * 1000,
  fetchArgs: ({ storeId, business, syncTime }) => {
    const options = {
      method: 'get',
      body: {
        storeId,
        bn: business,
        timestamp: syncTime,
      },
    };
    return [`${API_URL()}/api/syncEmployees`, options];
  },
});

export interface SyncProductsType {
  business: string;
  syncTime: string;
  limit?: number;
  page?: number;
  isInitial?: boolean;
}

export const syncProducts = createTypedHttpAction<SyncProductsType>({
  name: 'syncProducts',
  disableLoadingMask: true,
  preservePayload: false,
  disableErrorInfo: true,
  timeout: 2 * 60 * 1000,
  fetchArgs: ({ business, syncTime, limit, page, isInitial }) => {
    const options = {
      method: 'get',
      body: {
        bn: business,
        timestamp: syncTime,
        limit,
        page,
        isInitial,
      },
    };
    return [`${API_URL()}/api/syncProducts`, options];
  },
});

export interface SyncQuickSelectLayoutType {
  business: string;
  registerId: string;
}

export const syncQuickSelectLayout = createTypedHttpAction<SyncQuickSelectLayoutType>({
  name: 'syncQuickSelectLayout',
  disableLoadingMask: true,
  preservePayload: false,
  disableErrorInfo: true,
  timeout: 2 * 60 * 1000,
  fetchArgs: ({ business, registerId }) => {
    const options = {
      method: 'get',
      body: {
        bn: business,
        registerId,
      },
    };
    return [`${API_URL()}/api/quickSelectLayout`, options];
  },
});

export interface SyncStoreInfo {
  includeAllStores: boolean;
  registerId: string;
  business: string;
}

export const syncStoreInfo = createTypedHttpAction<SyncStoreInfo>({
  name: 'syncStoreInfo',
  disableLoadingMask: true,
  preservePayload: false,
  disableErrorInfo: true,
  timeout: 2 * 60 * 1000,
  fetchArgs: ({ business, registerId, includeAllStores }) => {
    const options = {
      method: 'get',
      body: {
        bn: business,
        registerId,
        includeAllStores,
      },
    };
    return [`${API_URL()}/api/syncStoreInfo`, options];
  },
});

export interface SyncPriceBooksType {
  syncTime: string;
}

export const syncPriceBook = createTypedHttpAction<SyncPriceBooksType>({
  name: 'syncPriceBook',
  disableLoadingMask: true,
  preservePayload: false,
  disableErrorInfo: true,
  timeout: 2 * 60 * 1000,
  fetchArgs: ({ syncTime }) => {
    const options = {
      method: 'get',
      body: {
        timestamp: syncTime,
      },
    };
    return [`${API_URL()}/api/priceBooks`, options];
  },
});

export interface SyncEmployeeActivityType {
  userActionLogs: any[];
  registerId: string;
  storeId: string;
}

export const syncEmployeeActivity = createTypedHttpAction<SyncEmployeeActivityType>({
  name: 'syncEmployeeActivity',
  disableLoadingMask: true,
  preservePayload: false,
  disableErrorInfo: true,
  timeout: 2 * 60 * 1000,
  fetchArgs: ({ userActionLogs, registerId, storeId }) => {
    const options = {
      method: 'post',
      body: {
        userActionLogs,
        registerId,
        storeId,
      },
    };
    return [`${API_URL()}/api/syncUserActions`, options];
  },
});

export interface SyncSequentialStartInfoType {
  businessName: string;
  registerObjectId: string;
}

export const syncSequentialStartInfo = createTypedHttpAction<SyncSequentialStartInfoType>({
  name: 'syncSequentialStartInfo',
  disableLoadingMask: true,
  preservePayload: false,
  disableErrorInfo: true,
  timeout: 2 * 60 * 1000,
  fetchArgs: ({ businessName, registerObjectId }) => {
    const options = {
      method: 'get',
      body: { bn: businessName, registerId: registerObjectId },
    };
    return [`${API_URL()}/api/sequentialStartInfo`, options];
  },
});
export interface SyncPromotionsType {
  syncTime: string;
}

export const syncPromotions = createTypedHttpAction<SyncPromotionsType>({
  name: 'syncPromotions',
  disableLoadingMask: true,
  preservePayload: false,
  disableErrorInfo: true,
  timeout: 2 * 60 * 1000,
  fetchArgs: ({ syncTime }) => {
    const options = {
      method: 'get',
      body: {
        timestamp: syncTime,
        includeBindUniquePromoCodePromotions: true, // Adding this field is to allow the new version to sync down promotions associated with UP. The old version, without passing this field, won't be able to sync down promotions associated with UP.
      },
    };
    return [`${API_URL()}/api/promotions`, options];
  },
});

export interface ReplaceTokenType {
  business: string;
  registerId: string;
  token: string;
}
export const replaceToken = createTypedHttpAction<ReplaceTokenType>({
  name: 'replaceToken',
  disableLoadingMask: true,
  preservePayload: true,
  disableErrorInfo: true,
  fetchArgs: ({ business, registerId, token }) => {
    const options = {
      method: 'post',
      body: {
        business,
        registerId,
        token,
      },
    };
    return [`${API_URL()}/api/migratePOS`, options];
  },
});
