import { PAYMENT_API_URL_SWITCHABLE } from '../../config';
import { createTypedHttpAction } from './setup';

export interface CreateEWalletPaymentType {
  additionalInfo?: any;
  currency: string;
  receiptNumber: string;
  amount: string;
  tokenId?: string;
  storeId?: string;
}

export const createEWalletPayment = createTypedHttpAction<CreateEWalletPaymentType>({
  name: 'createEWalletPayment',
  disableLoadingMask: false,
  disableErrorInfo: true,
  preservePayload: false,
  isEWalletPay: true,
  fetchArgs: ({ additionalInfo, currency, receiptNumber, amount, tokenId, storeId }) => {
    const options = {
      method: 'post',
      body: {
        ...additionalInfo,
        currency,
        receiptNumber,
        amount,
        tokenId,
        storeId,
      },
    };
    return [`${PAYMENT_API_URL_SWITCHABLE()}/create`, options];
  },
});

export interface InquiryEWalletPaymentType {
  additionalInfo?: any;
  currency: string;
  paymentId?: string;
  receiptNumber: string;
}

export const inquiryEWalletPayment = createTypedHttpAction<InquiryEWalletPaymentType>({
  name: 'inquiryEWalletPayment',
  disableLoadingMask: true,
  preservePayload: false,
  disableErrorInfo: true,
  isEWalletPay: true,
  timeout: 15 * 1000,
  fetchArgs: ({ additionalInfo, currency, receiptNumber, paymentId }) => {
    const options = {
      method: 'post',
      body: {
        ...additionalInfo,
        currency,
        receiptNumber,
        paymentId,
      },
    };
    return [`${PAYMENT_API_URL_SWITCHABLE()}/inquiry`, options];
  },
});

export interface VoidEWalletPaymentType {
  additionalInfo?: any;
  currency: string;
  paymentId?: string;
  receiptNumber: string;
  remark?: string;
  amount: string;
}

export const voidEWalletPayment = createTypedHttpAction<VoidEWalletPaymentType>({
  name: 'voidEWalletPayment',
  disableLoadingMask: true,
  preservePayload: false,
  disableErrorInfo: true,
  isEWalletPay: true,
  fetchArgs: ({ additionalInfo, currency, receiptNumber, paymentId, remark, amount }) => {
    const options = {
      method: 'post',
      body: {
        ...additionalInfo,
        currency,
        receiptNumber,
        paymentId,
        remark,
        amount,
      },
    };
    return [`${PAYMENT_API_URL_SWITCHABLE()}/void`, options];
  },
});

export interface CancelEWalletPaymentType {
  additionalInfo?: any;
  currency: string;
  paymentId?: string;
  receiptNumber: string;
}

export const cancelEWalletPayment = createTypedHttpAction<CancelEWalletPaymentType>({
  name: 'cancelEWalletPayment',
  disableLoadingMask: true,
  preservePayload: false,
  disableErrorInfo: true,
  isEWalletPay: true,
  fetchArgs: ({ additionalInfo, currency, receiptNumber, paymentId }) => {
    const options = {
      method: 'post',
      body: {
        ...additionalInfo,
        currency,
        receiptNumber,
        paymentId,
      },
    };
    return [`${PAYMENT_API_URL_SWITCHABLE()}/cancel`, options];
  },
});
