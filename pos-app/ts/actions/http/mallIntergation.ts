import { SH_CONNECTOR_URL } from '../../config';
import { MallIntegrationMSG } from '../../constants';
import { createTypedHttpAction } from './setup';

// ------------ <PERSON> Mall ----------------
export interface StartOrEndDayType {
  storeId: string;
  transactionDate: string;
  registerId: string;
}

// start day will be called after open shift
export const startDay = createTypedHttpAction<StartOrEndDayType>({
  name: 'startDay',
  disableLoadingMask: false,
  preservePayload: false,
  fetchArgs: ({ storeId, transactionDate, registerId }) => {
    const options = {
      method: 'post',
      isThirdMiddleWare: true,
      body: {
        store_id: storeId,
        trx_date: transactionDate,
        terminal_number: registerId,
      },
    };
    return ['https://sh-api.excelym.com/rest/v1/start_day', options];
  },
});

// end day will be called after close shift
export const endDay = createTypedHttpAction<StartOrEndDayType>({
  name: 'endDay',
  disableLoadingMask: false,
  preservePayload: false,
  disableErrorInfo: true,
  timeoutMessage: MallIntegrationMSG.ROBINSON_MALL_ERR_MSG,
  cancelMessage: MallIntegrationMSG.ROBINSON_MALL_ERR_MSG,
  fetchArgs: ({ storeId, transactionDate, registerId }) => {
    const options = {
      method: 'post',
      isThirdMiddleWare: true,
      body: {
        store_id: storeId,
        trx_date: transactionDate,
        terminal_number: registerId,
      },
    };
    return ['https://sh-api.excelym.com/rest/v1/end_day', options];
  },
});

export interface GetDailyClosingType {
  registerId: string;
  storeId: string;
}

// partner doesn't support pagination for now.
export const getDailyClosing = createTypedHttpAction<GetDailyClosingType>({
  name: 'getDailyClosing',
  disableLoadingMask: false,
  preservePayload: false,
  fetchArgs: ({ registerId, storeId }) => {
    const options = {
      method: 'get',
      isThirdMiddleWare: true,
    };
    return [`https://sh-api.excelym.com/rest/v1/daily_closings/${storeId}/${registerId}`, options];
  },
});

export interface retryUploadType {
  registerId: string;
  storeId: string;
}

export const retryUpload = createTypedHttpAction<GetDailyClosingType>({
  name: 'retryUpload',
  disableLoadingMask: true,
  preservePayload: false,
  fetchArgs: ({ registerId, storeId }) => {
    const options = {
      method: 'get',
      isThirdMiddleWare: true,
    };
    return [`https://sh-api.excelym.com/rest/v1/retry/${storeId}/${registerId}`, options];
  },
});

// ------------ Ayala Mall ----------------

export interface GetAyalaMallSyncUpHistoryType {
  storeID: string;
  date: string;
}
// partner doesn't support pagination for now.
export const getAyalaMallSyncUpHistory = createTypedHttpAction<GetAyalaMallSyncUpHistoryType>({
  name: 'getAyalaMallSyncUpHistory',
  disableLoadingMask: false,
  preservePayload: false,
  fetchArgs: ({ storeID, date }) => {
    const options = {
      method: 'get',
      isUseFutch: true,
    };
    return [`${SH_CONNECTOR_URL}/getMallSyncUpHistory?storeID=${storeID}&date=${date}`, options];
  },
});

export interface GetAyalaMallDailyReportType {
  storeID: string;
  date: string;
}
// partner doesn't support pagination for now.
export const getAyalaMallDailyReport = createTypedHttpAction<GetAyalaMallDailyReportType>({
  name: 'getAyalaMallDailyReport',
  disableLoadingMask: false,
  preservePayload: false,
  fetchArgs: ({ storeID, date }) => {
    const options = {
      method: 'get',
      isUseFutch: true,
    };
    return [`${SH_CONNECTOR_URL}/getMallDailyReport?storeID=${storeID}&date=${date}`, options];
  },
});

export interface GetCommonIntergationEODReportType {
  storeID: string;
  date: string;
}

export const getSMDailyReport = createTypedHttpAction<GetCommonIntergationEODReportType>({
  name: 'getSMDailyReport',
  disableLoadingMask: false,
  preservePayload: false,
  disableErrorInfo: true,
  fetchArgs: ({ storeID, date }) => {
    const options = {
      method: 'get',
    };
    return [`http://mall.mymyhub.com:3001/sm/daily-report?storeID=${storeID}&date=${date}`, options];
  },
});

export interface GetSMXReadingReportType {
  storeId: string;
  date: string;
  registerId: string;
  shiftId: string;
}

export const getSMXReadingReport = createTypedHttpAction<GetSMXReadingReportType>({
  name: 'getSMXReadingReport',
  disableLoadingMask: true,
  preservePayload: false,
  disableErrorInfo: true,
  fetchArgs: ({ storeId, date, registerId, shiftId }) => {
    const options = {
      method: 'get',
    };
    return [`https://mall.storehubhq.com/sm/x-report?storeID=${storeId}&date=${date}&registerID=${registerId}&shiftId=${shiftId}`, options];
  },
});

export const getOrtigasEODReport = createTypedHttpAction<GetCommonIntergationEODReportType>({
  name: 'getOrtigasEODReport',
  disableLoadingMask: false,
  preservePayload: false,
  disableErrorInfo: true,
  fetchArgs: ({ storeID, date }) => {
    const options = {
      method: 'get',
    };
    return [`http://mall.mymyhub.com:3001/ortigas/zReading?storeID=${storeID}&date=${date}`, options];
  },
});
