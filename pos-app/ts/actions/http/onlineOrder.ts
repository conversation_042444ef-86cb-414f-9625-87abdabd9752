import { gql } from 'graphql-request';
import { API_URL } from '../../config';

import { createTypedHttpAction } from './setup';

export interface GetOnlineOrderDetailsType {
  orderId: string;
}
export const getOnlineOrderDetails = createTypedHttpAction<GetOnlineOrderDetailsType>({
  name: 'getOnlineOrderDetails',
  disableLoadingMask: true,
  preservePayload: false,
  disableErrorInfo: true,
  fetchArgs: ({ orderId }) => {
    const options = {
      method: 'get',
      isUseFutch: true,
      body: {
        orderId,
      },
    };
    return [`${API_URL()}/api/onlineOrderDetails`, options];
  },
});

export const getOnlineOrderStatusList = createTypedHttpAction({
  name: 'getOnlineOrderStatusList',
  disableLoadingMask: true,
  preservePayload: false,
  disableErrorInfo: true,
  fetchArgs: () => {
    const options = {
      method: 'get',
    };
    return [`${API_URL()}/api/onlineOrderStatusList`, options];
  },
});

export interface GetOnlineOrderListType {
  businessName: string;
  status: string[];
  storeId: string;
  channel: number;
  syncTime: string;
  isPreOrder?: boolean;
  fromDate?: string;
  toDate?: string;
}

export const getOnlineOrderList = createTypedHttpAction<GetOnlineOrderListType>({
  name: 'getOnlineOrderList',
  disableLoadingMask: false,
  preservePayload: false,
  disableErrorInfo: true,
  fetchArgs: ({ businessName, status, storeId, channel, syncTime, isPreOrder = false, fromDate, toDate }) => {
    const options = {
      method: 'post',
      isUseFutch: true,
      body: {
        businessName,
        status,
        storeId,
        channel,
        lastQRSyncDate: syncTime,
        isPreOrder,
        fromDate,
        toDate,
      },
    };
    return [`${API_URL()}/api/onlineOrders`, options];
  },
});
/*
  Third part integration: The ‘smallOrderFee’ is only available for ShopeeFood and FoodPanda orders
*/
const TRANSACTION_BFF = `{
  version
  status
  isCancelled
  cancelledDate
  channel
  registerId
  shippingType
  isPayLater
  total
  subtotal
  tax
  pax
  discount
  shippingFee
  shippingFeeDiscount
  takeawayCharges
  storeId
  roundedAmount
  consumerId
  customerId
  sessionId
  createdTime
  modifiedTime
  receiptNumber
  transactionId
  expectDeliveryDateFrom
  expectDeliveryDateTo
  smallOrderFee
  seniorsCount
  pwdCount
  headCount
  totalDeductedTax
  taxableSales
  taxExemptedSales
  zeroRatedSales
  seniorDiscount
  containerFee
  pwdDiscount
  registerNumber
  invoiceSeqNumber
  sequenceNumber
  seqNumberCreatedTime
  voidNumber
  printKitchenDocket
  printReceipt
  amusementTax
  calculation {
    discounts {
      type
      discount
      deductedTax
      subType
      promotionId
    }
    taxes {
      taxCode
      taxRate
      tax
      isVatExempted
      isAmusementTax
    }
    original {
      tax
      subtotal
      total
    }
  }
  display {
    subtotal
    discount
    serviceCharge
    tax
    total
  }
  contactDetail {
    email
    name
    phone
  }
  promotions {
    promotionId
    discount
    tax
    taxCode
    promotionCode
    promotionName
    discountType
    shippingFeeDiscount
  }
  tableId
  pickUpId
  otherReason
  displayDiscount
  paymentMethod
  serviceCharge
  transactionType
  serviceChargeRate
  serviceChargeTax
  createdVoucherCodes
  comment
  loyaltyDiscounts {
    loyaltyType
    displayDiscount
    spentValue
  }
  subOrders {
    comments
    submitId
    submittedTime
    isPrinted
    printedTime
    submittedBy
    submittedByPhone
    submittedFrom
  }
  items {
    id
    submitId
    itemType
    discount
    quantity
    discountType
    discountValue
    productId
    tax
    taxableAmount
    taxExemptAmount
    zeroRatedSales
    totalDeductedTax
    seniorDiscount
    pwdDiscount
    athleteAndCoachDiscount
    medalOfValorDiscount
    taxCode
    taxRate
    total
    title
    subTotal
    isTakeaway
    takeawayCharge
    calculation {
      fullPrice
      discounts {
        type
        discount
        deductedTax
        subType
        promotionId
      }
      taxes {
        taxCode
        taxRate
        tax
        isVatExempted
        isAmusementTax
      }
      original {
        tax
        subtotal
        total
      }
    }
    display{
      subtotal
      total
      tax
    }
    unitPrice
    notes
    promotions {
      promotionId
      discount
      tax
      taxCode
      promotionCode
      promotionName
      discountType
      shippingFeeDiscount
    }
    manualDiscount {
      type
      inputValue
    }
    selectedOptions {
      variationId
      optionId
      optionValue
      quantity
    }
  }
  payments {
    amount
    paymentMethod
    roundedAmount
    isOnline
    paymentGateway
    cardName
  }
  deliveryInformation {
    shippingFee
    comments
    address {
      name
      phone
      address
      city
      addressDetails
      postCode
      state
      country
    }
  }
  appliedVoucher {
    voucherId
    voucherCode
    value
    cost
    purchaseChannel
    coveredBySH
  }
  receipt {
    total
    containerFee
    smallOrderFee
    discount
  }
}`;

const TRANSACTION_BFF_V2 = `{
  isSplittedFromReceiptNumber
  version
  status
  isCancelled
  cancelledDate
  channel
  registerId
  shippingType
  isPayLater
  total
  subtotal
  tax
  pax
  discount
  shippingFee
  shippingFeeDiscount
  takeawayCharges
  storeId
  roundedAmount
  consumerId
  customerId
  sessionId
  createdTime
  modifiedTime
  receiptNumber
  transactionId
  expectDeliveryDateFrom
  expectDeliveryDateTo
  smallOrderFee
  seniorsCount
  pwdCount
  headCount
  totalDeductedTax
  taxableSales
  taxExemptedSales
  zeroRatedSales
  seniorDiscount
  containerFee
  pwdDiscount
  registerNumber
  invoiceSeqNumber
  sequenceNumber
  seqNumberCreatedTime
  voidNumber
  printKitchenDocket
  printReceipt
  amusementTax
  calculation {
    discounts {
      type
      discount
      deductedTax
      subType
      promotionId
    }
    taxes {
        taxCode
        taxRate
        tax
        isVatExempted
        isAmusementTax
    }
    original {
      tax
      subtotal
      total
    }
  }
  display {
    subtotal
    discount
    serviceCharge
    tax
    total
  }
  contactDetail {
    email
    name
    phone
  }
  promotions {
    promotionId
    discount
    tax
    taxCode
    promotionCode
    promotionName
    discountType
    shippingFeeDiscount
  }
  tableId
  pickUpId
  otherReason
  displayDiscount
  paymentMethod
  serviceCharge
  transactionType
  serviceChargeRate
  serviceChargeTax
  createdVoucherCodes
  comment
  loyaltyDiscounts {
    loyaltyType
    displayDiscount
    spentValue
  }
  subOrders {
    comments
    submitId
    submittedTime
    isPrinted
    printedTime
    submittedBy
    submittedByPhone
    submittedFrom
  }
  items {
    id
    submitId
    itemType
    discount
    quantity
    discountType
    discountValue
    productId
    tax
    taxableAmount
    taxExemptAmount
    zeroRatedSales
    totalDeductedTax
    seniorDiscount
    pwdDiscount
    athleteAndCoachDiscount
    medalOfValorDiscount
    taxCode
    taxRate
    total
    title
    subTotal
    isTakeaway
    takeawayCharge
    calculation {
      fullPrice
      discounts {
        type
        discount
        deductedTax
        subType
        promotionId
      }
      taxes {
        taxCode
        taxRate
        tax
        isVatExempted
        isAmusementTax
      }
      original {
        tax
        subtotal
        total
      }
    }
    display{
      subtotal
      total
      tax
    }
    unitPrice
    notes
    promotions {
      promotionId
      discount
      tax
      taxCode
      promotionCode
      promotionName
      discountType
      shippingFeeDiscount
    }
    manualDiscount {
      type
      inputValue
    }
    selectedOptions {
      variationId
      optionId
      optionValue
      quantity
    }
  }
  payments {
    amount
    paymentMethod
    roundedAmount
    isOnline
    paymentGateway
    cardName
  }
  deliveryInformation {
    shippingFee
    comments
    address {
      name
      phone
      address
      city
      addressDetails
      postCode
      state
      country
    }
  }
  appliedVoucher {
    voucherId
    voucherCode
    value
    cost
    purchaseChannel
    coveredBySH
  }
  receipt {
    total
    containerFee
    smallOrderFee
    discount
  }
}`;

export interface PollingType {
  businessName: string;
  storeId: string;
  includePayLater: boolean;
  needKDS: boolean;
}

export const polling = createTypedHttpAction<PollingType>({
  name: 'polling',
  disableLoadingMask: true,
  disableErrorInfo: true,
  preservePayload: true,
  timeout: 2 * 60 * 1000,
  fetchArgs: ({ businessName, storeId, includePayLater, needKDS }) => {
    const options = {
      method: 'get',
      isGraphQL: true,
      isNewGraphQL: true,
      requireEmployeeId: true,
      body: `
      {
        ordersToPrintV2(
          businessName:  "${businessName}"
          storeId:  "${storeId}"
          includePayLater: ${includePayLater}
          needKDS: ${needKDS}
        ) {
          kds ${TRANSACTION_BFF}
          both ${TRANSACTION_BFF}
          kitchen ${TRANSACTION_BFF}
          full ${TRANSACTION_BFF}
        }
      }
        `,
    };
    return [null, options];
  },
});

export interface OnlineOrderDetailType {
  orderId: string;
}

export const getOnlineOrderDetail = createTypedHttpAction<OnlineOrderDetailType>({
  name: 'getOnlineOrderDetail',
  disableLoadingMask: false,
  disableErrorInfo: false,
  preservePayload: true,
  fetchArgs: ({ orderId }) => {
    const options = {
      method: 'get',
      isGraphQL: true,
      isNewGraphQL: true,
      requireEmployeeId: true,
      body: `
      {
        onlineOrder(
          receiptNumber:  "${orderId}"
        ) ${TRANSACTION_BFF_V2}
      }`,
    };
    return [null, options];
  },
});

export const getOnlineOrderDetailNoLoading = createTypedHttpAction<OnlineOrderDetailType>({
  name: 'getOnlineOrderDetailNoLoading',
  disableLoadingMask: true,
  disableErrorInfo: false,
  preservePayload: true,
  fetchArgs: ({ orderId }) => {
    const options = {
      method: 'get',
      isGraphQL: true,
      isNewGraphQL: true,
      requireEmployeeId: true,
      body: `
      {
        onlineOrder(
          receiptNumber:  "${orderId}"
        ) ${TRANSACTION_BFF}
      }`,
    };
    return [null, options];
  },
});

export interface UpdateOnlineOrderStatusType {
  orderId: string;
  status: string;
  businessName: string;
  employeeId: string;
  trackingId?: string;
  courier?: string;
  cancelReason?: string;
  otherReason?: string;
}

export const updateOnlineOrderStatus = createTypedHttpAction<UpdateOnlineOrderStatusType>({
  name: 'updateOnlineOrderStatus',
  disableLoadingMask: false,
  preservePayload: false,
  disableErrorInfo: true,
  fetchArgs: ({ orderId, status, businessName, employeeId, trackingId, courier, cancelReason, otherReason }) => {
    const options = {
      method: 'post',
      isUseFutch: true,
      body: {
        orderId,
        status,
        businessName,
        operatorId: employeeId,
        trackingId,
        courier,
        cancelReason,
        otherReason,
      },
    };
    return [`${API_URL()}/api/onlineOrderStatus`, options];
  },
});

export interface MarkPrintedType {
  orderId: string;
  submitId?: string;
}

export const markPrinted = createTypedHttpAction<MarkPrintedType>({
  name: 'markPrinted',
  disableLoadingMask: true,
  disableErrorInfo: false,
  preservePayload: true,
  fetchArgs: ({ orderId, submitId }) => {
    const options = {
      method: 'post',
      isGraphQL: true,
      isNewGraphQL: true,
      requireEmployeeId: true,
      body: `mutation 
      markPrinted(
        $receiptNumber: String!, 
        $submitId: String, 
      ) {
        markPrinted(
          receiptNumber: $receiptNumber, 
          submitId: $submitId, 
          )
        }`,
    };
    const variables = {
      receiptNumber: orderId,
      submitId,
    };
    return [null, options, variables];
  },
});

// #region KDS
export interface MarkKDSType {
  orderId: string;
  registerObjectId: string;
}

export const markKDS = createTypedHttpAction<MarkKDSType>({
  name: 'markKDS',
  disableLoadingMask: true,
  disableErrorInfo: false,
  preservePayload: true,
  fetchArgs: ({ orderId, registerObjectId }) => {
    const options = {
      method: 'post',
      isGraphQL: true,
      isNewGraphQL: true,
      requireEmployeeId: true,
      body: `mutation 
      markSyncToKds(
        $receiptNumber: String!, 
        $registerId: String!,
      ) {
        markSyncToKds(
          receiptNumber: $receiptNumber, 
          registerId: $registerId
          )
        }`,
    };
    const variables = {
      receiptNumber: orderId,
      registerId: registerObjectId,
    };
    return [null, options, variables];
  },
});

export type KdsMetricsType = {
  businessName: string;
  receiptNumber: string;
  isOnline: boolean;
  pendingTime?: string; // ISO
  servedTime?: string;
};
export const kdsMetrics = createTypedHttpAction<KdsMetricsType>({
  name: 'kdsMetrics',
  disableLoadingMask: true,
  disableErrorInfo: false,
  preservePayload: false,
  fetchArgs: variables => {
    const options = {
      method: 'post',
      isGraphQL: true,
      isNewGraphQL: true,
      requireEmployeeId: true,
      body: gql`
        mutation kdsMetrics($businessName: String!, $receiptNumber: String!, $pendingTime: Date, $servedTime: Date, $isOnline: Boolean) {
          kdsMetrics(businessName: $businessName, receiptNumber: $receiptNumber, pendingTime: $pendingTime, servedTime: $servedTime, isOnline: $isOnline)
        }
      `,
    };

    return [null, options, variables];
  },
});
// #endregion
export interface CancelOrderType {
  receiptNumber: string;
  employeeId: string;
}

export const cancelOrder = createTypedHttpAction<CancelOrderType>({
  name: 'cancelOrder',
  disableLoadingMask: false,
  disableErrorInfo: false,
  preservePayload: true,
  fetchArgs: ({ receiptNumber, employeeId }) => {
    const options = {
      method: 'post',
      isGraphQL: true,
      isNewGraphQL: true,
      requireEmployeeId: true,
      body: `mutation 
      cancelOrder(
        $receiptNumber: String!, 
        $employeeId: String!
      ) {
        cancelOrder(
          receiptNumber: $receiptNumber, 
          employeeId: $employeeId
          ) ${TRANSACTION_BFF}
        }`,
    };
    const variables = {
      receiptNumber,
      employeeId,
    };
    return [null, options, variables];
  },
});

export interface RemoveOrderItemType {
  receiptNumber: string;
  itemId: string;
  quantity: number;
  employeeId: string;
}

export const removeOrderItem = createTypedHttpAction<RemoveOrderItemType>({
  name: 'removeOrderItem',
  disableLoadingMask: false,
  disableErrorInfo: false,
  preservePayload: true,
  fetchArgs: ({ receiptNumber, itemId, quantity, employeeId }) => {
    const options = {
      method: 'post',
      isGraphQL: true,
      isNewGraphQL: true,
      requireEmployeeId: true,
      body: `mutation 
      removeOrderItem(
        $receiptNumber: String!, 
        $itemId: String!, 
        $quantity: Int!,
        $employeeId: String!
      ) {
        removeOrderItem(
          receiptNumber: $receiptNumber, 
          itemId: $itemId, 
          quantity: $quantity,
          employeeId: $employeeId
          ) ${TRANSACTION_BFF}
        }`,
    };
    const variables = {
      receiptNumber,
      itemId,
      quantity,
      employeeId,
    };
    return [null, options, variables];
  },
});

export interface SearchOnlineOrdersType {
  status?: string[];
  orderId?: string;
  pageIndex: number;
  businessName: string;
  storeId: string;
  lastQRSyncDate?: string;
  pageSize: number;
  fromDate?: string;
  toDate?: string;
  isPreOrder?: boolean;
  channels?: number[];
}
export const searchOnlineOrders = createTypedHttpAction<SearchOnlineOrdersType>({
  name: 'searchOnlineOrders',
  disableLoadingMask: false,
  preservePayload: false,
  fetchArgs: ({ status, orderId, pageIndex, businessName, storeId, channels, lastQRSyncDate, pageSize, fromDate, toDate, isPreOrder }) => {
    const options = {
      method: 'post',
      body: {
        status,
        orderId,
        pageNumber: pageIndex,
        businessName,
        storeId,
        channels,
        lastQRSyncDate,
        pageSize,
        fromDate,
        toDate,
        isPreOrder,
      },
    };
    return [`${API_URL()}/api/onlineOrders`, options];
  },
});

export const getBeepMenu = createTypedHttpAction({
  name: 'getBeepMenu',
  disableLoadingMask: false,
  fetchArgs: () => {
    const options = {
      method: 'get',
    };
    return [`${API_URL()}/api/collectionProducts`, options];
  },
});

export interface ModifyBeepItemStatusType {
  businessName: string;
  productIds: string[];
  duration: any;
}

export const modifyBeepItemStatus = createTypedHttpAction<ModifyBeepItemStatusType>({
  name: 'modifyBeepItemStatus',
  disableErrorInfo: false,
  disableLoadingMask: false,
  fetchArgs: ({ businessName, productIds, duration }) => {
    const options = {
      method: 'post',
      body: {
        businessName,
        productIds,
        duration,
      },
    };
    return [`${API_URL()}/api/products/markSoldOut`, options];
  },
});

export interface ModifyBeepVariationStatusType {
  businessName: string;
  productId: string;
  optionIds?: string[];
  duration: any;
}

export const modifyBeepVariationStatus = createTypedHttpAction<ModifyBeepVariationStatusType>({
  name: 'modifyBeepVariationStatus',
  disableErrorInfo: false,
  disableLoadingMask: false,
  fetchArgs: ({ businessName, productId, duration, optionIds }) => {
    const options = {
      method: 'post',
      body: {
        businessName,
        productId,
        duration,
        optionIds,
      },
    };
    return [`${API_URL()}/api/products/markSoldOut`, options];
  },
});
export interface GetOnlineOrderLogsType {
  orderId: string;
}
export const getOnlineOrderLogs = createTypedHttpAction<GetOnlineOrderLogsType>({
  name: 'getOnlineOrderLogs',
  disableLoadingMask: true,
  preservePayload: false,
  fetchArgs: ({ orderId }) => {
    const options = {
      method: 'get',
      body: {
        orderId,
      },
    };
    return [`${API_URL()}/api/onlineOrderLogs`, options];
  },
});

export interface GetBadgeNumberType {
  registerId: string;
  business: string;
}
export const getBadgeNumber = createTypedHttpAction<GetBadgeNumberType>({
  name: 'getBadgeNumber',
  disableLoadingMask: true,
  preservePayload: false,
  fetchArgs: ({ registerId, business }) => {
    const options = {
      method: 'get',
      body: {
        registerId,
        bn: business,
      },
    };
    return [`${API_URL()}/api/badgeNumber`, options];
  },
});

export interface ResetBadgeNumberType {
  registerId: string;
  business: string;
  isOnline: boolean;
}

export const resetBadgeNumber = createTypedHttpAction<ResetBadgeNumberType>({
  name: 'resetBadgeNumber',
  disableLoadingMask: true,
  preservePayload: false,
  fetchArgs: ({ registerId, business, isOnline }) => {
    const options = {
      method: 'post',
      body: {
        registerId,
        business,
        isOnline,
      },
    };
    return [`${API_URL()}/api/resetBadgeNumber`, options];
  },
});

export interface RequestOnlineOrderStatusWithCountType {
  storeId: string;
  businessName: string;
  fromDate: string;
  toDate: string;
  status?: string[];
  channels?: number[];
}

export const requestOnlineOrderStatusWithCount = createTypedHttpAction<RequestOnlineOrderStatusWithCountType>({
  name: 'requestOnlineOrderStatusWithCount',
  disableLoadingMask: true,
  preservePayload: false,
  needTakeEvery: true,
  fetchArgs: ({ businessName, storeId, fromDate, toDate, status, channels }) => {
    const options = {
      method: 'post',
      body: {
        businessName,
        storeId,
        fromDate,
        toDate,
        status,
        channels,
      },
    };
    return [`${API_URL()}/api/onlineOrderStatusWithCount`, options];
  },
});

export interface GetOnlineOpenOrdersType {
  storeId: string;
  businessName: string;
  includePayLater: boolean;
  includePayByCash: boolean;
  filter?: string;
}

export const getOnlineOpenOrders = createTypedHttpAction<GetOnlineOpenOrdersType>({
  name: 'getOnlineOpenOrders',
  disableLoadingMask: false,
  disableInteractions: false,
  disableErrorInfo: true,
  preservePayload: true,
  timeout: 5 * 1000, // max timeout 5 seconds
  fetchArgs: ({ businessName, storeId, includePayLater, includePayByCash, filter }) => {
    const options = {
      method: 'get',
      isGraphQL: true,
      isNewGraphQL: true,
      requireEmployeeId: true,
      body: `
          {
            onlineOpenOrders(
                  businessName: "${businessName}"
                  storeId: "${storeId}"
                  includePayLater: ${includePayLater}
                  includePayByCash: ${includePayByCash}
                  filter: "${filter}"
              ) {
                isPayLater
                createdTime
                modifiedTime
                subtotal
                total
                receiptNumber
                transactionId
                shippingType
                items {
                  id
                  discount
                  quantity
                  productId
                  tax
                  total
                  title
                  subTotal
                  unitPrice
                }
                transactionType
                paymentMethod
                pickUpId
                expectDeliveryDateFrom
                expectDeliveryDateTo
                tableId
                status
                channel
                payments {
                  amount
                  paymentMethod
                  isDeposit
                  roundedAmount
                  subType
                  isOnline
                }
              }
          }
        `,
    };
    return [null, options];
  },
});

export type OnlineOpenOrdersWithoutLoadingType = {
  isPayLater: boolean;
  createdTime: string;
  modifiedTime: string;
  receiptNumber: string;
  transactionId: string;
  pickUpId: string;
  tableId: string;
};
export const getOnlineOpenOrdersWithoutLoading = createTypedHttpAction<GetOnlineOpenOrdersType>({
  name: 'getOnlineOpenOrdersWithoutLoading',
  disableLoadingMask: true,
  disableErrorInfo: true,
  preservePayload: true,
  fetchArgs: ({ businessName, storeId, includePayLater, includePayByCash, filter }) => {
    const options = {
      method: 'get',
      isGraphQL: true,
      isNewGraphQL: true,
      requireEmployeeId: true,
      body: `
          {
            onlineOpenOrders(
                  businessName: "${businessName}"
                  storeId: "${storeId}"
                  includePayLater: ${includePayLater}
                  includePayByCash: ${includePayByCash}
                  filter: "${filter}"
              ) {
                isPayLater
                createdTime
                receiptNumber
                transactionId
                pickUpId
                tableId
                modifiedTime
              }
          }
        `,
    };
    return [null, options];
  },
});

export interface MarkPaidType {
  receiptNumber: string;
  businessName: string;
  paymentId: string;
  paymentMethod: string;
  amount: string;
  rounding: string;
  employeeId: string;
  signature: string;
  isPayLater?: boolean;
  registerId?: string;
  registerNumber?: number;
  invoiceSeqNumber?: number;
  sequenceNumber?: number;
  storeId?: string;
  changeAmount?: string;
}

export const markPaid = createTypedHttpAction<MarkPaidType>({
  name: 'markPaid',
  disableLoadingMask: true,
  disableErrorInfo: true,
  preservePayload: true,
  fetchArgs: ({
    businessName,
    receiptNumber,
    paymentId,
    paymentMethod,
    amount,
    rounding,
    employeeId,
    signature,
    isPayLater,
    registerId,
    registerNumber,
    invoiceSeqNumber,
    sequenceNumber,
    storeId,
    changeAmount,
  }) => {
    const options = {
      method: 'post',
      isGraphQL: true,
      isNewGraphQL: true,
      requireEmployeeId: true,
      body: `mutation 
      markPaid(
        $businessName: String!, 
        $receiptNumber: String!, 
        $paymentId: String!, 
        $paymentMethod: String!, 
        $amount: String!, 
        $rounding: String!,
        $employeeId: String!,
        $signature: String!,
        $isPayLater: Boolean,
        $storeId: String,
        $registerId: String,
        $registerNumber: Int,
        $invoiceSeqNumber: Int,
        $sequenceNumber: Int,
        $changeAmount: String
      ) {
        markPaid(
          businessName: $businessName, 
          receiptNumber: $receiptNumber, 
          paymentId: $paymentId, 
          paymentMethod: $paymentMethod, 
          amount: $amount,
          rounding: $rounding,
          employeeId: $employeeId,
          signature: $signature,
          isPayLater: $isPayLater,
          storeId: $storeId,
          registerId: $registerId,
          registerNumber: $registerNumber,
          invoiceSeqNumber: $invoiceSeqNumber,
          sequenceNumber: $sequenceNumber,
          changeAmount: $changeAmount
          )
          {
            success
            order ${TRANSACTION_BFF}
            cashbackSummary {
              loyaltySpent
              loyaltyEarned
              loyaltyBalance
              cashbackExpirationDate
            }
          }
        }`,
    };

    const variables = {
      businessName,
      receiptNumber,
      paymentId,
      paymentMethod,
      amount,
      rounding,
      employeeId,
      signature,
      isPayLater,
      registerId,
      registerNumber,
      invoiceSeqNumber,
      sequenceNumber,
      storeId,
      changeAmount,
    };
    return [null, options, variables];
  },
});

export interface UnlockOrderType {
  receiptNumber: string;
  employeeId: string;
}

export const unlockOrder = createTypedHttpAction<UnlockOrderType>({
  name: 'unlockOrder',
  disableLoadingMask: false,
  disableErrorInfo: false,
  preservePayload: true,
  fetchArgs: ({ receiptNumber, employeeId }) => {
    const options = {
      method: 'post',
      isGraphQL: true,
      isNewGraphQL: true,
      requireEmployeeId: true,
      body: `mutation 
      unlockOrder(
        $receiptNumber: String!, 
        $employeeId: String!
      ) {
        unlockOrder(
          receiptNumber: $receiptNumber, 
          employeeId: $employeeId
          )${TRANSACTION_BFF}
        }`,
    };

    const variables = {
      receiptNumber,
      employeeId,
    };
    return [null, options, variables];
  },
});

export interface PurchasedItemOptionInputType {
  variationId?: string;
  optionId?: string;
  optionValue?: string;
  quantity?: number;
}
export interface OrderItemInputType {
  id?: string;
  productId: string;
  quantity: number;
  selectedOptions?: PurchasedItemOptionInputType[];
  notes?: string;
  isTakeaway?: boolean;
  itemLevelDiscount?: ManualDiscountInput;
}

export interface ManualDiscountInput {
  inputValue: number;
  type: string;
}

export interface onlineOrderItemDiscountType {
  inputValue: number;
  type: string;
}

export interface OrderPreviewType {
  consumerId?: string;
  businessName: string;
  storeId: string;
  channel: number;
  shippingType: string;
  fulfillDate?: Date;
  fullBillDiscount?: ManualDiscountInput;
  items?: OrderItemInputType[];
}

export const orderPreview = createTypedHttpAction<OrderPreviewType>({
  name: 'orderPreview',
  disableLoadingMask: false,
  disableErrorInfo: false,
  preservePayload: true,
  fetchArgs: ({ consumerId, businessName, storeId, channel, shippingType, fulfillDate, items, fullBillDiscount }) => {
    const options = {
      method: 'get',
      isGraphQL: true,
      isNewGraphQL: true,
      requireEmployeeId: true,
      body: gql`
        query orderPreview(
          $consumerId: String
          $businessName: String!
          $storeId: String!
          $channel: Int!
          $shippingType: String!
          $fulfillDate: Date
          $fullBillDiscount: ManualDiscountInput
          $items: [OrderItemInput]
        ) {
          orderPreview(
            consumerId: $consumerId
            businessName: $businessName
            storeId: $storeId
            channel: $channel
            shippingType: $shippingType
            fulfillDate: $fulfillDate
            fullBillDiscount: $fullBillDiscount
            items: $items
          ) {
            total
            discount
            subTotal
            serviceCharge
            serviceChargeTax
            tax
            maximumDiscountInputValue
            display {
              subtotal
              discount
              serviceCharge
              tax
              total
            }
            items {
              type
              quantity
              subTotal
              total
              discount
              equivalentValue
              tax
              id
              title
              productId
              discountValue
              discountType
              manualDiscount {
                type
                inputValue
                equivalentValue
              }
              display {
                subtotal
                total
                tax
              }
              selectedOptions {
                variationId
                optionId
                optionValue
                quantity
              }
              display_price
              isSelling
              unitPrice
              notes
              isTakeaway
              takeawayCharge
            }
          }
        }
      `,
    };
    const variables = {
      consumerId,
      businessName,
      storeId,
      channel,
      shippingType,
      fulfillDate,
      items,
      fullBillDiscount,
    };
    return [null, options, variables];
  },
});

export interface SaveOnlineOrderType {
  receiptNumber: string;
  modifiedTime: Date;
  employeeId: string;
  fullBillDiscount: ManualDiscountInput;
  items?: OrderItemInputType[];
}

export const editOnlineOrder = createTypedHttpAction<SaveOnlineOrderType>({
  name: 'editOnlineOrder',
  disableLoadingMask: false,
  disableErrorInfo: true,
  preservePayload: true,
  fetchArgs: ({ receiptNumber, modifiedTime, employeeId, items, fullBillDiscount }) => {
    const options = {
      method: 'post',
      isGraphQL: true,
      isNewGraphQL: true,
      requireEmployeeId: true,
      body: `mutation 
      editOrderItems(
        $receiptNumber: String! 
        $modifiedTime: Date!
        $employeeId: String!
        $fullBillDiscount: ManualDiscountInput
        $items: [OrderItemInput]
      ) {
        editOrderItems(
          receiptNumber: $receiptNumber, 
          modifiedTime: $modifiedTime, 
          employeeId: $employeeId, 
          fullBillDiscount: $fullBillDiscount
          items: $items, 
          )${TRANSACTION_BFF}
        }`,
    };
    const variables = {
      receiptNumber,
      modifiedTime,
      employeeId,
      items,
      fullBillDiscount,
    };
    return [null, options, variables];
  },
});

export interface GenerateSeqNumberForOnlineOrderType {
  businessName: string;
  receiptNumber: string;
  registerId: string;
  registerNumber: number;
  invoiceSeqNumber: number;
  sequenceNumber: number;
  storeId: string;
}

// Since the online order also needs to support BIR, the online order also needs to generate sequenceNumber and invoiceSequenceNumber
// The solution is that the POS end calls the API to tell the back end the current lastestSeqNumber when printing,
// and the back end generates sequenceNumber and so on for online order and returns to the POS end, and the POS end updates
// the lastestSequenceNumber and so on in the local record
export const generateSeqNumberForOnlineOrder = createTypedHttpAction<GenerateSeqNumberForOnlineOrderType>({
  name: 'generateSeqNumberForOnlineOrder',
  disableLoadingMask: true,
  disableErrorInfo: true,
  preservePayload: true,
  fetchArgs: ({ businessName, receiptNumber, registerId, registerNumber, invoiceSeqNumber, sequenceNumber, storeId }) => {
    const options = {
      method: 'post',
      isGraphQL: true,
      isNewGraphQL: true,
      requireEmployeeId: true,
      body: `mutation 
      generateInvoiceSequenceNumber(
        $receiptNumber: String! 
        $businessName: String! 
        $registerId: String!
        $registerNumber: Int!
        $invoiceSeqNumber:  Int!
        $sequenceNumber:  Int!
        $storeId:  String!
      ) {
        generateInvoiceSequenceNumber(
          receiptNumber: $receiptNumber, 
          businessName: $businessName, 
          registerId: $registerId, 
          registerNumber: $registerNumber
          invoiceSeqNumber: $invoiceSeqNumber, 
          sequenceNumber: $sequenceNumber, 
          storeId: $storeId, 
          )${TRANSACTION_BFF}
        }`,
    };
    const variables = {
      businessName,
      receiptNumber,
      registerNumber,
      registerId,
      invoiceSeqNumber,
      sequenceNumber,
      storeId,
    };
    return [null, options, variables];
  },
});

export interface EditOnlineOrderTableType {
  receiptNumber: string;
  modifiedTime: string;
  employeeId: string;
  tableId: string;
  businessName: string;
  storeId: string;
}

export const editOrderTable = createTypedHttpAction<EditOnlineOrderTableType>({
  name: 'editOrderTable',
  disableLoadingMask: false,
  disableErrorInfo: true,
  preservePayload: true,
  fetchArgs: ({ receiptNumber, modifiedTime, employeeId, tableId, businessName, storeId }) => {
    const options = {
      method: 'post',
      isGraphQL: true,
      isNewGraphQL: true,
      requireEmployeeId: true,
      body: `mutation
      editOrderTable(
        $businessName: String!
        $storeId: String!
        $receiptNumber: String!
        $modifiedTime: Date!
        $employeeId: String!
        $tableId: String!
      ) {
        editOrderTable(
          businessName: $businessName,
          storeId: $storeId,
          receiptNumber: $receiptNumber,
          modifiedTime: $modifiedTime,
          employeeId: $employeeId,
          tableId: $tableId,
          ){
            success
            code
            message
          }
        }`,
    };
    const variables = {
      receiptNumber,
      modifiedTime,
      employeeId,
      tableId,
      businessName,
      storeId,
    };
    return [null, options, variables];
  },
});

// #region split online order
export type SplitOnlineOrderPreviewType = {
  receiptNumber: string;
  splittedItems: {
    // item.id
    id: string;
    quantity: number;
  }[];
};

export type OrderSplittedPreviewRes = {
  mainOrder: OrderPreview;
  splittedOrder: OrderPreview;
};

export type OrderItemPreview = {
  type: string;
  quantity: number;
  subTotal: number;
  total: number;
  discount: number;
  tax: string;
  id: string;
  title: string;
  productId: string;
  selectedOptions: {
    variationId: string;
    optionId: string;
    optionValue: string;
    quantity: number;
  }[];
  display_price?: number;
  isSelling?: boolean;
  unitPrice: number;
  notes?: string;
};

export type OrderPreview = {
  items: [OrderItemPreview];
  total: number;
  discount: number;
  subTotal: number;
  serviceCharge: number;
  serviceChargeTax: number;
  tax: number;
  modifiedTime: string;
};

const OrderPreview_BFF = `{
  total
  discount
  subTotal
  serviceCharge
  serviceChargeTax
  tax
  modifiedTime
  items {
    type
    quantity
    subTotal
    total
    discount
    tax
    id
    title
    productId
    selectedOptions {
      variationId
      optionId
      optionValue
      quantity
    }
    display_price
    isSelling
    unitPrice
    notes
    isTakeaway
    takeawayCharge
  }
}`;
export const onlineOrderSplittedPreview = createTypedHttpAction<SplitOnlineOrderPreviewType>({
  name: 'onlineOrderSplittedPreview',
  disableLoadingMask: false,
  disableErrorInfo: true,
  preservePayload: true,
  fetchArgs: variables => {
    const options = {
      method: 'get',
      isGraphQL: true,
      isNewGraphQL: true,
      requireEmployeeId: true,
      body: gql`
        query orderSplittedPreview($receiptNumber: String!, $splittedItems: [OrderSplittedPreviewItem]) {
          orderSplittedPreview(receiptNumber: $receiptNumber, splittedItems: $splittedItems) {
            mainOrder ${OrderPreview_BFF}
            splittedOrder ${OrderPreview_BFF}
          }
        }
      `,
    };
    return [null, options, variables];
  },
});

export const onlineOrderSplittedPreviewNoLoading = createTypedHttpAction<SplitOnlineOrderPreviewType>({
  name: 'onlineOrderSplittedPreviewNoLoading',
  disableLoadingMask: true,
  disableErrorInfo: true,
  preservePayload: true,
  fetchArgs: variables => {
    const options = {
      method: 'get',
      isGraphQL: true,
      isNewGraphQL: true,
      requireEmployeeId: true,
      body: gql`
        query orderSplittedPreview($receiptNumber: String!, $splittedItems: [OrderSplittedPreviewItem]) {
          orderSplittedPreview(receiptNumber: $receiptNumber, splittedItems: $splittedItems) {
            mainOrder ${OrderPreview_BFF}
            splittedOrder ${OrderPreview_BFF}
          }
        }
      `,
    };
    return [null, options, variables];
  },
});

export type PaymentInfoInput = {
  // "if having _id, using _id, else using name."
  paymentId: string;
  // "payment name"
  paymentMethod: string;
  // "format: '5.00', '0.01'"
  amount: string;
  // "if no rounding, value is '0.00'"
  rounding: string;
};

export type SplitOnlineOrderItemInput = {
  // item.id
  id: string;
  quantity: string;
};

export type SplitOrderInput = {
  items: SplitOnlineOrderItemInput[];
  tableId: string;
  transactionId: string;
  payment?: PaymentInfoInput;
};

export type GenericOnlineOrderPayType = {
  invoiceSeqNumber?: number;
  sequenceNumber?: number;
};

export type SplitOnlineOrderType = {
  // master order
  receiptNumber: string;
  modifiedTime: string;
  splittedOrders: [SplitOrderInput];
  employeeId: string;
  signature: string;
  registerId?: string;
  registerNumber?: number;
  invoiceSeqNumber?: number;
  sequenceNumber?: number;
  storeId?: string;
};

// save or pay split order
export const splitOnlineOrder = createTypedHttpAction<SplitOnlineOrderType>({
  name: 'splitOnlineOrder',
  disableLoadingMask: false,
  disableErrorInfo: true,
  preservePayload: true,
  fetchArgs: variants => {
    const options = {
      method: 'post',
      isGraphQL: true,
      isNewGraphQL: true,
      requireEmployeeId: true,
      body: gql`
        mutation splitOrder(
          $receiptNumber: String!
          $modifiedTime: Date!
          $splittedOrders: [SplitOrderInput]
          $employeeId: String!
          $signature: String!
          $registerId: String
          $registerNumber: Int
          $invoiceSeqNumber: Int
          $sequenceNumber: Int
          $storeId: String
        ) {
          splitOrder(
            receiptNumber: $receiptNumber
            modifiedTime: $modifiedTime
            splittedOrders: $splittedOrders
            employeeId: $employeeId
            signature: $signature
            registerId: $registerId
            registerNumber: $registerNumber
            invoiceSeqNumber: $invoiceSeqNumber
            sequenceNumber: $sequenceNumber
            storeId: $storeId
          ) {
            success
            code
            message
            splittedReceiptNumbers
          }
        }
      `,
    };

    return [null, options, variants];
  },
});

export const splitOnlineOrderNoLoading = createTypedHttpAction<SplitOnlineOrderType>({
  name: 'splitOnlineOrderNoLoading',
  disableLoadingMask: true,
  disableErrorInfo: true,
  preservePayload: true,
  fetchArgs: variants => {
    const options = {
      method: 'post',
      isGraphQL: true,
      isNewGraphQL: true,
      requireEmployeeId: true,
      body: gql`
        mutation splitOrder(
          $receiptNumber: String!
          $modifiedTime: Date!
          $splittedOrders: [SplitOrderInput]
          $employeeId: String!
          $signature: String!
          $registerId: String
          $registerNumber: Int
          $invoiceSeqNumber: Int
          $sequenceNumber: Int
          $storeId: String
        ) {
          splitOrder(
            receiptNumber: $receiptNumber
            modifiedTime: $modifiedTime
            splittedOrders: $splittedOrders
            employeeId: $employeeId
            signature: $signature
            registerId: $registerId
            registerNumber: $registerNumber
            invoiceSeqNumber: $invoiceSeqNumber
            sequenceNumber: $sequenceNumber
            storeId: $storeId
          ) {
            success
            code
            message
            splittedReceiptNumbers
          }
        }
      `,
    };

    return [null, options, variants];
  },
});
// #endregion

export interface ShareModifiersType {
  businessName: string;
}
export const sharedModifiers = createTypedHttpAction<ShareModifiersType>({
  name: 'sharedModifiers',
  disableLoadingMask: false,
  disableErrorInfo: false,
  preservePayload: true,
  fetchArgs: ({ businessName }) => {
    const options = {
      method: 'get',
      isGraphQL: true,
      isNewGraphQL: true,
      requireEmployeeId: true,
      body: `
      {
        sharedModifiers(
          business:  "${businessName}"
        ) {
          id
          name
        }
      }`,
    };
    return [null, options];
  },
});

export interface ShareModifierType {
  sharedModifierId: string;
  storeId: string;
}

export const sharedModifier = createTypedHttpAction<ShareModifierType>({
  name: 'sharedModifier',
  disableLoadingMask: false,
  disableErrorInfo: false,
  preservePayload: true,
  fetchArgs: ({ sharedModifierId, storeId }) => {
    const options = {
      method: 'get',
      isGraphQL: true,
      isNewGraphQL: true,
      requireEmployeeId: true,
      body: `
      {
        sharedModifier(
          sharedModifierId:  "${sharedModifierId}"
          storeId: "${storeId}"
        ) {
          id
          name
          markedSoldOut
          optionValues {
            markedSoldOut
            id
            value
          }
        }
      }`,
    };
    return [null, options];
  },
});

export interface MarkShareModifierSoldOutType {
  sharedModifierId: string;
  businessName: string;
  optionIds: string[];
  storeId: string;
  duration: number;
}

export const markSharedModifierSoldOut = createTypedHttpAction<MarkShareModifierSoldOutType>({
  name: 'markSharedModifierSoldOut',
  disableLoadingMask: false,
  disableErrorInfo: false,
  preservePayload: true,
  fetchArgs: ({ sharedModifierId, businessName, optionIds, storeId, duration }) => {
    const query = `
      mutation markSharedModifierSoldOut(
        $sharedModifierId: String!,
        $businessName: String!,
        $optionIds: [String]!,
        $storeId: String!,
        $duration: Float!
      ) {
        markSharedModifierSoldOut(
          sharedModifierId: $sharedModifierId,
          business: $businessName,
          optionIds: $optionIds,
          storeId: $storeId,
          duration: $duration
        ) {
          sharedModifier {
            id
            name
            markedSoldOut
            optionValues {
              markedSoldOut
              id
              value
            }
          }
        }
      }
    `;

    const variables = {
      sharedModifierId,
      businessName,
      optionIds,
      storeId,
      duration,
    };

    const options = {
      method: 'post',
      isGraphQL: true,
      isNewGraphQL: true,
      requireEmployeeId: true,
      body: query,
    };

    return [null, options, variables];
  },
});

export interface GenQRCodeExtraType {
  expirationSeconds: number;
  merchantName: string;
  tableName: string;
  storeId: string;
}

export interface GenQRCodeRequestType {
  type: string;
  extraInfo: GenQRCodeExtraType;
}

export const genQRCodeContent = createTypedHttpAction<GenQRCodeRequestType>({
  name: 'genQRCodeContent',
  disableLoadingMask: false,
  disableErrorInfo: true,
  preservePayload: true,
  fetchArgs: variables => {
    const options = {
      method: 'post',
      isGraphQL: true,
      isNewGraphQL: true,
      requireEmployeeId: true,
      body: `mutation
      genQRCodeContent(
        $type: QRCodeType!
        $extraInfo: GenQRCodeExtraInfo
      ) {
        genQRCodeContent(
          type: $type
          extraInfo: $extraInfo
          ){
            content
          }
        }`,
    };
    return [null, options, variables];
  },
});
