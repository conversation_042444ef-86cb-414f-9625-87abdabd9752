import { API_URL } from '../../config';
import { createTypedHttpAction } from './setup';

export const closeZReading = createTypedHttpAction({
  name: 'closeZReading',
  disableLoadingMask: false,
  preservePayload: false,
  disableErrorInfo: true,
  fetchArgs: () => {
    const options = {
      method: 'post',
      body: {},
    };
    return [`${API_URL()}/api/zreading`, options];
  },
});

export interface GetPOSReportType {
  date: string;
}

export const getPOSBookDailyReport = createTypedHttpAction<GetPOSReportType>({
  name: 'getPOSBookDailyReport',
  disableLoadingMask: false,
  preservePayload: false,
  fetchArgs: ({ date }) => {
    const options = {
      method: 'get',
      body: {
        date,
      },
    };
    return [`${API_URL()}/api/posdaily`, options];
  },
});

export const getPOSBookMonthlyReport = createTypedHttpAction<GetPOSReportType>({
  name: 'getPOSBookMonthlyReport',
  disableLoadingMask: false,
  preservePayload: false,
  fetchArgs: ({ date }) => {
    const options = {
      method: 'get',
      body: {
        date,
      },
    };
    return [`${API_URL()}/api/posmonthly`, options];
  },
});

export const getZReadingReport = createTypedHttpAction<GetPOSReportType>({
  name: 'getZReadingReport',
  disableLoadingMask: false,
  preservePayload: false,
  fetchArgs: ({ date }) => {
    const options = {
      method: 'get',
      body: {
        date,
      },
    };
    return [`${API_URL()}/api/zreadings`, options];
  },
});

export interface GetOnlineTransactionDailyReportType {
  businessName: string;
  storeId: string;
}

export const getOnlineTransactionDailyReport = createTypedHttpAction<GetOnlineTransactionDailyReportType>({
  name: 'getOnlineTransactionDailyReport',
  disableLoadingMask: true,
  disableErrorInfo: false,
  preservePayload: true,
  errorMessage: 'Failed to sync report',
  fetchArgs: ({ businessName, storeId }) => {
    const options = {
      method: 'get',
      isGraphQL: true,
      isNewGraphQL: true,
      requireEmployeeId: true,
      body: `
          {
            dailySalesReport(
                  businessName: "${businessName}"
                  storeId: "${storeId}"
              ) {
                reportDate
                salesByTransactionChannel {
                  channel
                  qty
                  amount
                }
              }
          }
        `,
    };
    return [null, options];
  },
});

export type getServerTimeType = unknown;

export const getServerTime = createTypedHttpAction<getServerTimeType>({
  name: 'getServerTime',
  disableLoadingMask: true,
  preservePayload: false,
  fetchArgs: () => {
    const options = {
      method: 'get',
    };
    return [`${API_URL()}/api/serverTimeInString`, options];
  },
});

export type GetShiftsByDateType = {
  date: string;
  registerObjectId: string;
};
export const getShiftsByDate = createTypedHttpAction<GetShiftsByDateType>({
  name: 'getShiftsByDate',
  disableLoadingMask: false,
  disableInteractions: false,
  disableErrorInfo: true,
  needTakeEvery: false,
  fetchArgs: ({ date, registerObjectId }) => {
    const options = {
      method: 'get',
      body: {
        date,
        registerId: registerObjectId,
      },
    };

    return [`${API_URL()}/api/shifts`, options];
  },
});
