import { createAction } from 'redux-actions';

export * from './aiChat';
export * from './app';
export * from './auth';
export * from './authorization';
export * from './autoUpdate';
export * from './aws';
export * from './badgeNumber';
export * from './beepQR';
export * from './cfdConfig';
export * from './customer';
export * from './dataSync';
export * from './devPanel';
export * from './eInvoice';
export * from './employeeActivity';
export * from './ewallet';
export * from './geo';
export * from './ghl';
export * from './growthBook';
export * from './http';
export * from './http/setup';
export * from './kds';
export * from './log';
export * from './mallIntergration';
export * from './mrs';
export * from './ncs';
export * from './netInfo';
export * from './openOrder';
export * from './pauseMode';
export * from './printer';
export * from './printing';
export * from './quickLayout';
export * from './s3';
export * from './sequentialReceiptNumber';
export * from './settings';
export * from './shift';
export * from './tableOrdering';
export * from './timezone';
export * from './transaction';
export * from './uniquePromos';
export * from './version';
export * from './websocket';
export * from './networkQuality';
export * from './printing2';
export * from './autoDeploy';
export * from './testing';

export type LoginType = {
  email: string;
  password: string;
};

export type ToggleToastInfoType = {
  visible: boolean;
  text?: string;
  delayTime?: number;
};
export const toggleToastInfo = createAction<ToggleToastInfoType>('toggleToast');

export type ToggleLoadingType = {
  visible: boolean;
  disableInteractions?: boolean;
};
export const toggleLoadingMask = createAction<ToggleLoadingType>('toggleLoadingMask');

type OnCompleteCallback = {
  callback(payload: { ok: boolean; error?: string }): void;
};

export type fixSequenceNumberTransactionBeginType = {
  onProgress(progress: number, infoText: string): void;
  onComplete: OnCompleteCallback;
};

export const fixSequenceNumberTransactionBegin = createAction<fixSequenceNumberTransactionBeginType>('fixSequenceNumberTransactionBegin');

export const syncAppState = createAction<string>('syncAppState');

export type DeviceInfoType = {
  platform: string;
  model: string;
  version: string;
  systemVersion?: string;
  firmwareVersion?: string;
  sn?: string;
};
export const setCurrentDevice = createAction<DeviceInfoType>('setCurrentDevice');

export const setTestEnvName = createAction<string>('setTestEnvName');
