import { createAction } from 'redux-actions';
import { PrintAyalaMallReportType } from '../typings';

export type ProcessToPrintAyalaEODType = {
  date: string;
  onPrintSuccess?: any;
  onPrintFailed?: any;
};

export const processToPrintAyalaEOD = createAction<ProcessToPrintAyalaEODType>('processToPrintAyalaEOD');

export type ProcessToPrintSMEODType = {
  date: string;
  onPrintSuccess?: any;
  onPrintFailed?: any;
};

export const processToPrintSMEOD = createAction<ProcessToPrintSMEODType>('processToPrintSMEOD');

export type ProcessToPrintSMXReadingType = {
  date: string;
  shiftId: string;
  shiftIndex: number;
};
export const processToPrintSMXReading = createAction<ProcessToPrintSMXReadingType>('processToPrintSMXReading');

export type ProcessToPrintOrtigasEODType = {
  date: string;
  onPrintSuccess?: any;
  onPrintFailed?: any;
};

export const processToPrintOrtigasEOD = createAction<ProcessToPrintOrtigasEODType>('processToPrintOrtigasEOD');

export const printAyalaMallReport = createAction<PrintAyalaMallReportType>('printAyalaMallReport');

export type PrintSMEODReportType = {
  smReport: any;
};

export const printSMEODReport = createAction<PrintSMEODReportType>('printSMEODReport');

export type ProcessToCallEODType = {
  needEOD?: boolean;
  onEODSuccess?: any;
  onEODFailed?: any;
  isZReadingClosed?: boolean;
};

export const processToCallEOD = createAction<ProcessToCallEODType>('processToCallEOD');

export type PrintOrtigasZreadingType = {
  ortigasZreading: any;
};

export const printOrtigasEODReport = createAction<PrintOrtigasZreadingType>('printOrtigasEODReport');
