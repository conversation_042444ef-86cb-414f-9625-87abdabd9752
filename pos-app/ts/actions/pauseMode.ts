import { createAction } from 'redux-actions';

import { BeepPauseModeSettings, PauseModeEnum } from './http/pauseMode';

export const setPauseMode = createAction<BeepPauseModeSettings>('setPauseMode');

export interface AsycCallbackType<T = any> {
  onSuccess?: (payload: T) => void;
  onFailure?: (message: string) => void;
}

export interface CloseDeliveryType extends AsycCallbackType {
  pauseMode: PauseModeEnum;
}
export const closeDelivery = createAction<CloseDeliveryType>('closeDelivery');

export const openForDelivery = createAction<AsycCallbackType>('openForDelivery');

export const updatePauseMode = createAction('updatePauseMode');
