import { createAction } from 'redux-actions';
import { DailyReportSummaryType } from '../components/shiftReport';
import { PrinterJobBusinessType } from '../constants';
import { PrintTaskResult } from '../models/print/task/AbstractPrintTask';
import { AllPurchasedItemType } from '../sagas/printing/common';
import { PurchasedItemType, TransactionType } from '../typings';
import { LogOptions } from '../utils/logComponent';
import { PrinterConfigType } from './settings';

export type PrintReceiptType = {
  transactionId?: string;
  customer?: any;
  isReprint?: boolean;
  onlineTransaction?: any;
  isNeedLog?: boolean;
  onComplete?: any;
  eventName: string;
};

export const printReceipt = createAction<PrintReceiptType>('printReceipt');
export const newPrintReceiptAction = createAction<PrintReceiptType>('newPrintReceiptAction');

export const testPrintOnAllTagedPrinters = createAction('testPrintOnAllTagedPrinters');

export interface IntervalPrintTestPayload {
  mode: 'single' | 'interval';
  times: number;
  intervals?: number[];
  printerIds?: string[];
  onTaskComplete?: (result: PrintTaskResult) => void;
}

export const intervalPrintTest = createAction<IntervalPrintTestPayload>('intervalPrintTest');

export type MultipleItemsPrintTestPayload = {
  itemCount: number;
};
export const multipleItemsPrintTest = createAction<MultipleItemsPrintTestPayload>('multipleItemsPrintTest');

export type PrintBeepQRTicketType = {
  tableName?: string;
  expirationHours?: number;
  url?: string;
  qrCode?: string;
  onComplete?: any;
};

export const printBeepQRTicket = createAction<PrintBeepQRTicketType>('printBeepQRTicket');

export type PrintEInvoiceQrReceiptType = {
  transaction?: TransactionType;
  transactionId?: string;
  qrData?: string;
};

export const printEInvoiceQrReceipt = createAction<PrintEInvoiceQrReceiptType>('printEInvoiceQrReceipt');

export const printSimpleReceipt = createAction<PrintReceiptType>('printSimpleReceipt');

export const printBIRReceipt = createAction<PrintReceiptType>('printBIRReceipt');

export type ShiftReportType = any;
export const printShiftReport = createAction<ShiftReportType>('printShiftReport');

export type OpenDrawerType = string;
export const openDrawer = createAction<OpenDrawerType>('openDrawer');

export type SearchPrinterType = {
  onComplete?: { callback(payload: { res: any }): void };
};
export const searchPrinters = createAction<SearchPrinterType>('searchPrinters');

export type InitPrinterType = {
  onComplete?: { callback(payload: { res: any }): void };
};

export const initPrinters = createAction<InitPrinterType>('initPrinters');
export interface PrintKitchenTicketType {
  previousTableId?: string;
  order: any;
  purchasedItems: PurchasedItemType[];
  unprintedItemsIds?: string[];
  unprintedDiffItems?: PurchasedItemType[];
  isBeepOrder?: boolean;
  logOptions?: LogOptions;
  receiptDate?: string;
  isDiffOperation?: boolean;
  errorPrinterId?: string;
  needRematchPrinter?: boolean;
  showErrorToast?: boolean;
  isResendToKitchen?: boolean;
  isReprinted?: boolean;
  isDeleteOrder?: boolean;
}

export type PrintOpenOrderReceiptType = Partial<PrintReceiptType>;
export const newPrintOpenOrderReceipt = createAction<PrintOpenOrderReceiptType>('newPrintOpenOrderReceipt');

export type PrintZReadingReportType = any;
export const printZReadingReport = createAction<PrintZReadingReportType>('printZReadingReport');
export const printTodayBeepPreOrderAction = createAction<any>('printTodayBeepPreOrderAction');

// pritner job
export interface RetryAllPrinterJobsType {
  isSubOrder?: boolean;
  schemaId: string;
  isBeepOrder?: boolean;
  isDiffOperation?: boolean;
  order?: any;
  notification?: any;
  needRematchPrinter?: boolean;
  showErrorToast?: boolean;
  logOptions?: LogOptions;
}
export const upsertPrinterErrorJobs = createAction<Record<string, number>>('upsertPrinterErrorJobs');

export const notifyPrinterJob = createAction<boolean>('notifyPrinterJob');

export type DissmissPrinterJobType = {
  submitId?: string;
  orderId?: string;
  jobId: string;
};
export const dismissPrinterJob = createAction<DissmissPrinterJobType>('dismissPrinterJob');
export const dismissAllPrinterJobs = createAction('dismissAllPrinterJobs');

export const lockPrinterJob = createAction<string>('lockPrinterJob');
export const unlockPrinterJob = createAction<string>('unlockPrinterJob');

export interface CreatePrinterJobsType {
  purchasedItems: PurchasedItemType[];
  isDiffOperation: boolean;
  errorPrinterId?: string;
  businessType?: PrinterJobBusinessType;
  isBeepOrder?: boolean;
  logOptions?: LogOptions;
}

export interface DailyReportChannelItemType {
  name: string;
  count: string;
  amount: string;
}

export interface PrintDailyReportType {
  dailyReportSummary: DailyReportSummaryType;
}

export const printDailyReport = createAction<PrintDailyReportType>('printDailyReport');

export type CheckPrinterAssignedResult = {
  isPrinterAssigned: boolean;
  offlinePrinters?: PrinterConfigType[];
  noAssignedTags: string[];
  isAllAssigned?: boolean;
};
export interface CheckAssignedPrinterType {
  extraTags?: string[];
  printerTagMapItems?: Record<string, AllPurchasedItemType[]>;
  printerTagsSettings?: PrinterConfigType[];
  order?: any;
  onComplete?: (result: CheckPrinterAssignedResult) => void;
}
export const checkAssignedPrinter = createAction<CheckAssignedPrinterType>('checkAssignedPrinter');
export const updatePrinterAssignedStatus = createAction<CheckPrinterAssignedResult>('updatePrinterAssignedStatus');
export const updatePrinterSearching = createAction<boolean>('updatePrinterSearching');

export type PingLanPrintersType = {
  onlyAssigned?: boolean;
  showToast?: boolean;
  isSearch?: boolean;
  source?: string;
};
export const pingLanPrinters = createAction<PingLanPrintersType>('pingLanPrinters');
export const forgetPrinter = createAction<string>('forgetPrinter');

export type FindNewPrintersType = {
  printers: PrinterConfigType[];
  udpRestoreId?: string;
};
export const findNewPrinters = createAction<FindNewPrintersType>('findNewPrinters');

export type TryAutoReprintFailedJobsType = {
  submitIds?: string[];
  orderId?: string;
  eventName: string;
};
export const tryAutoReprintFailedJobs = createAction<TryAutoReprintFailedJobsType>('tryAutoReprintFailedJobs');

export const udpSearchFinished = createAction<FindNewPrintersType>('udpSearchFinished');

export type UpdatePrinterTagType = {
  title: string;
  value: boolean;
  printerId: string;
};
export const updatePrinterTag = createAction<UpdatePrinterTagType>('updatePrinterTag');

export const updateAutoReprintByUdpTime = createAction('updateAutoReprintByUdpTime');
