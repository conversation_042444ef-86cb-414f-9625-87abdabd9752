import { createAction } from 'redux-actions';
import { PrinterConnectType } from '../constants';
import { ReceiptFontSize } from '../utils/printer';

export type UpdateGeneralSettingsType = {
  autoSyncAfterOpenShift?: boolean;
  enableOpenOrders?: boolean;
  enableOpenOrdersShortCut?: boolean;
  enableCustomerShortCut?: boolean;
  enableCustomerQR?: boolean;
  disablePushNotification?: boolean;
  disablePolling?: boolean;
  enableCloseLastShiftNotification?: boolean;
  shouldLoadOnlineOpenOrders?: boolean;
  autoSignOutCount?: string;
  pauseBeepDelivery?: string;
  allowOutOfStock?: boolean;
  //         -1: allow for current transaction
  //          0: allow always
  // 1720000000: allow until this timestamp
  allowOutOfStockUntil?: number;
};

export const updateGeneralSettings = createAction<UpdateGeneralSettingsType>('updateGeneralSettings');

export type UpdatePrinterGeneralSettingsType = {
  alwaysPrintReceipt?: boolean;
  alwaysPrintReceiptWithBeepQRPayOnline?: boolean;
  alwaysPrintReceiptWithBeepQRPayAtCounter?: boolean;
  alwaysPrintReceiptWithOnlineDelivery?: boolean;
  merchantHasPrinter?: boolean;
  kitchenDocketVariantIsMultipleLine?: boolean;
  printEmployeeName?: boolean;
  receiptFontSize?: ReceiptFontSize;
  kitchenDocketFontSize?: ReceiptFontSize;
  printEInvoiceOnReceipt?: boolean;
};

export const updatePrinterGeneralSettings = createAction<UpdatePrinterGeneralSettingsType>('updatePrinterGeneralSettings');

export type PrinterConfigType = {
  printerId: string;
  prevPrinterId: string;
  isOnline?: boolean;
  isLabelPrinter?: boolean;
  isBuiltInPrinter?: boolean;
  isReceiptPrinter?: boolean;
  isBeepOrderSummaryPrinter?: boolean;
  printerConnectType: PrinterConnectType;
  printerName?: string;
  printerModelType?: string;
  bluetoothMacAddress?: string;
  lanPort?: number;
  lanIp?: string;
  usbPath?: string;
  usbVendorId?: number;
  usbProductId?: number;
  serialNumber?: string;
  tags?: string[];
  pingStatus?: number;
  pingWorking?: boolean;
  lastPrintedTime?: string;
  macAddress?: string;
  dhcp?: boolean;
  printerPaperWidth?: string;
  model?: string;
  modifiedDate?: string;
  migrationPrinterIdV3?: string;
  errorCode?: number;
  netmask?: string;
  gateway?: string;
};

export interface PrinterWithError extends PrinterConfigType {
  errorCount: number;
}

export type UpdatePrinterTagsSettingsType = PrinterConfigType[];

export const updatePrinterTagsSettings = createAction<UpdatePrinterTagsSettingsType>('updatePrinterTagsSettings');

export type UpdateSinglePrinterType = { printerId: string; data?: Partial<PrinterConfigType> };
export const updateSinglePrinter = createAction<UpdateSinglePrinterType>('updateSinglePrinter');

export const updatePrinterSettings = createAction<UpdateSinglePrinterType[]>('updatePrinterSettings');

export type UpdatePrinterLastPrintedTimeType = {
  printerId: string;
};

export const updatePrinterLastPrintedTime = createAction<UpdatePrinterLastPrintedTimeType>('updatePrinterLastPrintedTime');

export type UpdateCustomerDisplaySettingsType = {
  enableCustomerDisplay: boolean;
};

export const updateCustomerDisplaySettings = createAction<UpdateCustomerDisplaySettingsType>('updateCustomerDisplaySettingsType');

export type UpdateTableLayoutSettingsType = {
  enableTableLayout?: boolean;
  alreadyMigrateTableLayout?: boolean;
  needShowEditTableLayoutToast?: boolean;
  needShowNewTableLayoutTag?: boolean;
  needShowMigrateSuccessToast?: boolean;
};

export const updateTableLayoutSettings = createAction<UpdateTableLayoutSettingsType>('updateTableLayoutSettings');

export interface TableLayoutSectionsType {
  sections: any[];
  appVersion?: number;
}
export const saveTableLayoutSections = createAction<TableLayoutSectionsType>('saveTableLayoutSections');

export interface UpdateAccountSettingsType {
  expired: boolean;
  offline: boolean;
}
export const updateAccountSettings = createAction<UpdateAccountSettingsType>('updateAccountSettings');

export const clearSetting = createAction('clearSetting');

export const autoSetDefaultNetworkAfterSuccessfulPrinting = createAction('autoSetDefaultNetworkAfterSuccessfulPrinting');

export const updateNotificationToken = createAction<string>('updateNotificationToken');
