import { createAction } from 'redux-actions';

export const getCurrentShiftStatus = createAction('getCurrentShiftStatus');

export const openShift = createAction<number>('openShift');

export const closeShift = createAction<number>('closeShift');

export const setShiftStatus = createAction<boolean>('setShiftStatusType');

export type PayInOutType = {
  payType: number;
  type?: string;
  amount?: number;
  comment?: string;
  onComplete?: any;
};

export const payInOut = createAction<PayInOutType>('payInOut');

export type GenerateShiftReportType = {
  onComplete?: any;
};

export const generateShiftReport = createAction<GenerateShiftReportType>('generateShiftReport');

export type SafeCloseZReadingType = {
  onSuccess?: any;
  onFailure?: any;
  closingTime?: string;
  onEODSuccess?: any;
  onEODFailed?: any;
  needEOD?: boolean;
};

export type UpdateSequenceType = {
  receiptNumberStart?: any;
  invoiceNumberStart?: any;
  receiptDateStart?: any;
  isStart: boolean;
  from?: string;
  minimumSequence?: any;
  transaction?: any;
};

export const safeCloseZReading = createAction<SafeCloseZReadingType>('safeCloseZReading');

export const checkAutoOpenDrawer = createAction('checkAutoOpenDrawer');

export const checkCloseLastShiftNotification = createAction('checkCloseLastShiftNotification');

export const checkReportUpdateStatusBegin = createAction('checkReportUpdateStatusBegin');

export const updateSequence = createAction<UpdateSequenceType>('updateSequence');

export const checkReportUpdateStatusEnd = createAction('checkReportUpdateStatusEnd');

export const checkReportUpdateStatus = createAction('checkReportUpdateStatus');
