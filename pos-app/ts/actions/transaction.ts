import { createAction } from 'redux-actions';
import { AthletesAndCoachesInfo, BIRDiscountType, DiplomatsInfo, MedalofValorInfo } from '../constants';
import { PurchasedItemType, TransactionType } from '../typings/schema';

import { ShareCustomerInfoType } from './http';
import { MRSError, OnMRSComplete } from './mrs';

// @ts-ignore
export interface TransactionTypeWithDisplay extends TransactionType {
  display?: {
    subtotal: number;
    discount: number;
    serviceCharge: number;
    tax: number;
    total: number;
  };
  serialNumbers: any;
  previousTableId?: string;
  customer?: CustomerType;
  isFullBillDiscountCanEnable?: boolean;
  isLoyaltyCanEnable?: boolean;
  isLoyaltyEnable?: boolean;
  promotions: PromotionTagType[];
  takeawayCharge?: number;
  contactDetail?: any;
  appliedVoucher?: AppliedVoucherType;
  hasUnsavedItems?: boolean;
  channel?: number;
  subOrders?: any[];
  uniquePromos?: any;
  maximumDiscountInputValue?: number;
  isCollectPreorder?: boolean;
  isPreview?: boolean;
}

export interface KdsTransactionType extends TransactionType {
  mergeFromTransactionList?: TransactionType[];
  splitToTransaction?: TransactionType;
  splitFromId?: string;
}

export type AppliedVoucherType = {
  voucherId?: string;
  voucherCode?: string;
  value?: number;
  cost?: number;
  purchaseChannel?: number | string;
  coveredBySH?: number | string;
};

export type MembershipTierInfo = {
  membershipTierId: string;
  membershipSource: string;
  membershipTierName: string;
  membershipTierLevel: number; // int
  membershipTierTotalSpent: number; // float
  membershipTierStartTime: string;
  membershipTierNextReviewTime: string;
  membershipJoinTime: string;
};

export type CustomerType = {
  customerId: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  street1?: string;
  street2?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  modifiedTime?: Date;
  totalSpend?: number;
  totalTransactions?: number;
  lastPurchasedDate?: Date;
  storeCredit?: number;
  tags?: any[];
  birthday?: Date;
  memberId?: string;
  orderHistory?: any[];
  memberEffectiveDate?: Date;
  taxIdNo?: string;
  storeCreditsSpent?: number;
  loyalty?: number;
  isStoreCredit?: boolean;
  loyaltyEarned?: number;
  loyaltyBalance?: number;
  loyaltySpent?: number;
  cashbackExpirationDate?: Date;
  membershipTierInfo?: MembershipTierInfo;
  availableUniquePromotionCount?: number;
  storeCreditInfo?: {
    storeCreditsBalance?: number;
    storeCreditsSpent?: number;
    cashbackClaimCount?: number;
    lastCashbackClaimDate?: Date;
    cashbackExpirationDate?: Date;
  };
  availablePointsBalance?: number;
};

export interface PromotionTagType {
  discount: number;
  display: {
    discount: number;
  };
  inputValue: number;
  promotionName: string;
  taxCode?: string;
  type: string;
  discountType: string;
  promotionId: string;
}

// export interface PurchasedItemTypeWithDisplay extends PurchasedItemType {
//   promotions?: PromotionTagType[];
//   display?: {
//     subtotal: number;
//     discount: number;
//     total: number;
//   };
// }

export interface PurchansedItemTypeInRefund extends PurchasedItemType {
  originalQuantity: number;
  return?: {
    quantity: number;
    total?: number;
  };
}

export const initTransactionSession = createAction<TransactionTypeWithDisplay>('initTransactionSession');
export const setTransactionSession = createAction<TransactionTypeWithDisplay>('setTransactionSession');

export const setCfdTransaction = createAction<TransactionTypeWithDisplay>('setCfdTransaction');

export const clearTransactionSession = createAction('clearTransactionSession');

export type CheckOutResultType = { custom?: any; errorCode: number; errorMessage: string; loyaltyInfo?: any; mrs?: boolean };

export type CheckoutTransactionType = {
  businessName?: string;
  enableCashback?: boolean;
  customer?: any;
  transaction?: TransactionTypeWithDisplay;
  onComplete?: { callback(payload: CheckOutResultType): void };
};

export const checkoutTransaction = createAction<CheckoutTransactionType>('checkoutTransaction');

export type ItemOptionType = {
  variationId: string;
  optionId: string;
  quantity: number;
  optionValue: string;
  priceDiff: number;
  sn?: string;
};

export type AddPurchasedItemType = {
  productId: string;
  options?: ItemOptionType[];
  quantity: number;
  variablePrice?: number;
  notes?: string;
  sn?: string;
  return?: any;
  originalQuantity?: number;
  tax?: string;
  taxCode?: string;
  title?: string;
  onBIRTaxRateDisableCallBack?(result: any): void;
};
export const addPurchasedItem = createAction<AddPurchasedItemType>('addPurchasedItem');

export type AddBIRDiscountToTransactionType = {
  discountType: {
    type: string;
    enum: BIRDiscountType;
  };
  seniorsCount: number;
  pwdCount: number;
  headCount: number;
  collectedInfo: AthletesAndCoachesInfo | MedalofValorInfo | DiplomatsInfo;
};
export const addBIRDiscountToTransaction = createAction<AddBIRDiscountToTransactionType>('addBIRDiscountToTransaction');

export const removeBIRDiscount = createAction('removeBIRDiscount');

export type AddCustomerToTransactionType = {
  customerId: string;
  customer: CustomerType;
  transaction?: any;
  checkCustomerWithBIR?(result: any): void;
  navigation?: any;
  isInCheckOut?: boolean;
};
export const addCustomerToTransaction = createAction<AddCustomerToTransactionType>('addCustomerToTransaction');
export type UpdateCustomerInTransactionType = {
  customer: CustomerType;
};
export const updateCustomerInTransaction = createAction<UpdateCustomerInTransactionType>('updateCustomerInTransaction');

export type fetchUniquePromoType = {
  selectedUniquePromoId?: string;
};
export const fetchUniquePromo = createAction<fetchUniquePromoType>('fetchUniquePromo');

export type deleteCustomerFromTransactionType = {
  checkCustomerWithBIR?(result: any): void;
};
export const deleteCustomerFromTransaction = createAction<deleteCustomerFromTransactionType>('deleteCustomerFromTransaction');

export type DeletePurchasedItemType = {
  itemIndex: number;
  itemId?: string;
};
export const deletePurchasedItem = createAction<DeletePurchasedItemType>('deletePurchasedItem');

export type UpdatePurchasedItemType = {
  itemIndex: number;
  quantity?: number;
  discountOption?: {
    inputValue: number;
    type: string;
  };
  options?: ItemOptionType[];
  notes?: string;
  isDiscountEnable?: boolean;
  discountValue?: number;
  itemChannel?: number;
  onBIRQuantityDisableCallBack?(result: any): void;
};
export const updatePurchasedItem = createAction<UpdatePurchasedItemType>('updatePurchasedItem');

export type TogglePayLaterItemTakeawayType = {
  itemIndex: number;
  isTakeaway: boolean;
};
export const togglePayLaterItemTakeaway = createAction<TogglePayLaterItemTakeawayType>('togglePayLaterItemTakeaway');

export type UpdatePurchasedItemNotesType = {
  itemIndex: number;
  notes?: string;
};
export const updatePurchasedItemNotes = createAction<UpdatePurchasedItemNotesType>('updatePurchasedItemNotes');

export type updateOrderLevelNotesType = {
  orderLevelNotes?: string;
};
export const updateOrderLevelNotes = createAction<updateOrderLevelNotesType>('updateOrderLevelNotes');

export enum CheckPlaceOrderAvailableLocation {
  Checkout = 'Checkout',
  Shift = 'Shift',
}

export type CheckPlaceOrderAvailableType = {
  callLocation: CheckPlaceOrderAvailableLocation;
  onComplete?: any;
};
export const checkPlaceOrderAvailable = createAction<CheckPlaceOrderAvailableType>('checkPlaceOrderAvailable');

export type PrintKitchenOnCheckOutType = {
  isPayByCash?: boolean;
  isPayLater?: boolean;
  currentRecord?: any;
};
export const printKitchenOnCheckOut = createAction<PrintKitchenOnCheckOutType>('printKitchenOnCheckOut');

export type UpdatePurchasedItemQuantityType = {
  itemIndex: number;
  quantity?: number;
  onBIRQuantityDisableCallBack?(result: any): void;
  onUpdateResult?(result: boolean): void;
};
export const updatePurchasedItemQuantity = createAction<UpdatePurchasedItemQuantityType>('updatePurchasedItemQuantity');

export type UpdatePurchasedItemDiscountType = {
  itemIndex: number;
  isDiscountEnable?: boolean;
  discountValue: number;
  discountOption?: {
    inputValue: number;
    type: string;
  };
};
export const updatePurchasedItemDiscount = createAction<UpdatePurchasedItemDiscountType>('updatePurchasedItemDiscount');

export type UpdateReturnItemType = {
  itemIndex: number;
  quantity?: number;
  total?: number;
  isDelete?: boolean;
};

export const updateReturnItem = createAction<UpdateReturnItemType>('updateReturnItem');

export type RefundTransactionType = {
  type: string; // manual || auto
};

export const refundTransaction = createAction<RefundTransactionType>('refundTransaction');

export type UpdateRefundTransactionReasonType = {
  otherReason?: string;
  returnReason?: string;
};

export const updateRefundTransactionReason = createAction<UpdateRefundTransactionReasonType>('updateRefundTransactionReason');

export type MakeSalePaymentType = {
  paymentId: number;
  tenderedAmount: number;
  tableId?: string;
  ewalletInfo?: any;
  splitedAmount?: number;
  paymentType?: string;
  onComplete?: any;
  paymentExtras?: any;
  isOnlineOrder?: boolean;
};

export const makeSalePayment = createAction<MakeSalePaymentType>('makeSalePayment');

export type MakePaymentType = {
  unRoundedAmount: number;
  paymentId: number;
  dueAmount: number;
  tenderedAmount: number;
  mPOSTransactionId?: string;
  tableId?: string;
  onComplete?: any;
  isDeposit?: boolean;
  beepOrderLoyaltyInfo?: any;
  manualApprovedReason?: string;
  paymentType?: string;
  paymentExtras?: any;
  addOnFields?: any;
  transaction: any;
};

export const makePayment = createAction<MakePaymentType>('makePayment');

export type ClearPaymentType = Record<string, unknown>;

export const clearPayment = createAction<ClearPaymentType>('clearPayment');

export type CheckTrxRefundStatusType = {
  receiptNumber: string;
  onComplete?: any;
  transactionId?: string;
  needCustomerInfo?: boolean;
  needWithInternet?: boolean;
  alreadyRefundErrMsg?: string;
};

export const checkTrxRefundStatus = createAction<CheckTrxRefundStatusType>('checkTrxRefundStatus');
export const checkCancellableWithRefundStatus = createAction<CheckTrxRefundStatusType>('checkCancellableWithRefundStatus');

export interface CancelTransactionType extends OnMRSComplete {
  transactionId: string;
  otherReason?: string;
  returnReason: string;
  onlineTransaction: any;
  transaction?: any;
  isBeepOrder?: boolean;
  onCancelOfflineBeepOrder?: any;
}
export const cancelTransaction = createAction<CancelTransactionType>('cancelTransaction');

export type SearchTransactionsType = {
  keyword: string;
  linkedRefundTrx?: boolean;
  onComplete?: any;
};
export const searchTransactions = createAction<SearchTransactionsType>('searchTransactions');

export type GiveFullBillDiscountType = {
  itemIndex: number;
  inputValue: number;
  type: string; // amount  || percent
  displayFullBillDiscountValue: number;
  isDiscountEnable: boolean;
};

export const giveFullBillDiscount = createAction<GiveFullBillDiscountType>('giveFullBillDiscount');

export type ToggleServiceChargeType = {
  itemIndex: number;
  serviceChargeRate: number;
  serviceChargeTax: string;
};

export const toggleServiceCharge = createAction<ToggleServiceChargeType>('toggleServiceCharge');

export type SetItemSalespersonType = {
  itemIndex?: number;
  employeeId?: string;
  employeeName?: string;
};
export const setItemSalesperson = createAction<SetItemSalespersonType>('setItemSalesperson');

export type SetLastAutoOrderIdType = number;

export const setLastAutoOrderId = createAction<SetLastAutoOrderIdType>('setLastAutoOrderId');

export type SaveOpenOrderType = OnMRSComplete;

export const saveOpenOrder = createAction<SaveOpenOrderType>('saveOpenOrder');

export type UpdateOpenOrderType = OnMRSComplete;

export const updateOpenOrder = createAction<UpdateOpenOrderType>('updateOpenOrder');

export type UpdateTransactionCommentType = string;
export const updateTransactionComment = createAction<UpdateTransactionCommentType>('updateTransactionComment');

export type UpdateTransactionTableIdAndPaxType = {
  tableId?: string;
  pax?: string;
};
export const updateTransactionTableIdAndPax = createAction<UpdateTransactionTableIdAndPaxType>('updateTransactionTableIdAndPax');

export type UpdateTransactionPaxType = {
  pax?: string;
};
export const updateTransactionPax = createAction<UpdateTransactionPaxType>('updateTransactionPax');
export const resetTransactionTableIdAndPax = createAction('resetTransactionTableIdAndPax');

export type SetOpenOrderTransactionSessionType = {
  transactionId: string;
  onComplete?: any;
};

export const setOpenOrderTransactionSession = createAction<SetOpenOrderTransactionSessionType>('setOpenOrderTransactionSessionType');

export type printOnlineOpenOrderReceiptType = {
  onlineTransaction: any;
};

export const printOnlineOpenOrderReceipt = createAction<printOnlineOpenOrderReceiptType>('printOnlineOpenOrderReceipt');

export type setOnlineOpenOrderToTransactionType = {
  onlineTransaction: any;
  isInSalePayment?: boolean;
  onComplete?: any;
};

export const setOnlineOpenOrderToTransaction = createAction<setOnlineOpenOrderToTransactionType>('setOnlineOpenOrderToTransaction');

export type DeleteOpenOrderType = {
  transactionId: string;
  onComplete?: any;
};
export const deleteOpenOrder = createAction<DeleteOpenOrderType>('deleteOpenOrder');

export type DeleteOnlineOpenOrderType = {
  receiptNumber: string;
};
export const deleteOnlineOpenOrder = createAction<DeleteOnlineOpenOrderType>('deleteOnlineOpenOrder');

export type NavigateType = {
  navigation: any;
};

export const navigateToRegister = createAction<NavigateType>('navigateToRegister');
export const navigateToHome = createAction<NavigateType>('navigateToHome');

export type CheckRefundTransactionValidityType = {
  onComplete?: any;
};

export const checkRefundTransactionValidity = createAction<CheckRefundTransactionValidityType>('checkRefundTransactionValidity');

export type UpdateLoyaltyDiscountsType = {
  type: string;
  inputValue: number;
  isRefund?: boolean;
};

export const updateLoyaltyDiscounts = createAction<UpdateLoyaltyDiscountsType>('updateLoyaltyDiscounts');

export type AutoApplyCustomerType = {
  customer: ShareCustomerInfoType;
  navigation?: any;
  isInCheckOut?: boolean;
};
export const autoApplyCustomer = createAction<AutoApplyCustomerType>('autoApplyCustomer');

export const disableLoyaltyDiscounts = createAction('disableLoyaltyDiscounts');
export const cancelNextPayment = createAction('cancelNextPayment');

export const checkCustomerInfoFromQRBegin = createAction('checkCustomerInfoFromQRBegin');

export const createPreOrder = createAction('createPreOrder');

export const setupPreOrder = createAction<SetupPreOrderType>('setupPreOrder');

export type SetupPreOrderType = {
  date: string;
  time: string;
  comment?: string;
  deposit?: number;
  onComplete?: any;
};

export type GetLocalPreOrdersType = {
  onError?: (error: any) => void;
  onComplete?: (result: TransactionType[]) => void;
};

export const getLocalPreOrders = createAction<GetLocalPreOrdersType>('getLocalPreOrders');

export type RestoreLocalPreOrderType = {
  transactionId: string;
};
export const restoreLocalPreOrder = createAction<RestoreLocalPreOrderType>('restoreLocalPreOrder');

export type RestoreSearchedPreOrderType = {
  transaction: TransactionType;
};
export const restoreSearchedPreOrder = createAction<RestoreSearchedPreOrderType>('restoreSearchedPreOrder');

export const setNextId = createAction('setNextId');

export type SetNextIdType = number;
export type SetTakeAwayToTransactionType = {
  salesChannel: number;
  currentTransaction?: any;
};
export const setTakeAwayToTransaction = createAction<SetTakeAwayToTransactionType>('setTakeAwayToTransaction');
export const clearPromotion = createAction('clearPromotion');
export const clearRefundPromotion = createAction('clearRefundPromotion');

export type UnlockPayLaterOrderType = {
  receiptNumber?: string;
  onSuccess?: any;
  onFailed?: any;
};
export const unlockPayLaterOrder = createAction<UnlockPayLaterOrderType>('unlockPayLaterOrder');

export type SaveEditedOnlineOrderType = {
  onSuccess?: any;
  onFailed?: any;
  goCheckOutAfterSave: boolean;
};
export const saveOnlineOrder = createAction<SaveEditedOnlineOrderType>('saveOnlineOrder');

export type GenerateMergeOpenOrderType = {
  selectedOpenOrderIds: string[];
  onResult?: any;
  operation?: 'mergeAndSave' | 'mergeAndPay';
};

export const genarateMergeOpenOrder = createAction<GenerateMergeOpenOrderType>('genarateMergeOpenOrder');

export type MergeOpenOrderType = {
  mergedTransaction: TransactionType;
  onComplete: (error: MRSError) => void;
};
export const mergeToSave = createAction<MergeOpenOrderType>('mergeToSave');

export type MergePayOpenOrderType = {
  mergedTransaction: TransactionType;
};
export const mergeToPay = createAction<MergePayOpenOrderType>('mergeToPay');

export type SelectedOpenOrderType = {
  id: string; // local transactionId, payLater receiptNumber
  isPayLater?: boolean;
  srcTableId?: string;
  modifiedTime?: string;
};

export type MoveOpenOrderType = {
  pendingMoveOrders: SelectedOpenOrderType[]; // source table orders information.
  tableId: string; // Destination table id.
  onComplete: (result: MRSError) => void;
};

export const moveOpenOrder = createAction<MoveOpenOrderType>('moveOpenOrder');

export type ChangeOpenOrderTableType = {
  tableId: string;
};

export const changeOpenOrderTable = createAction<ChangeOpenOrderTableType>('changeOpenOrderTable');

export const refreshMrsOpenOrders = createAction<{ source: string }>('refreshMrsOpenOrders');

export const updateOutOfStockStatus = createAction<number>('updateOutOfStockStatus');
export const refreshBadgeNumber = createAction('refreshBadgeNumber');
export const resetOnlineBadgeNumber = createAction('resetOnlineBadgeNumber');
