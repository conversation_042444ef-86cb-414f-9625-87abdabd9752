import React from 'react';
import { useSelector } from 'react-redux';
import AIChatWidget from './AIChatWidget';

const AIChatContainer: React.FC = () => {
  const aiChatState = useSelector((state: any) => state.get('aiChat'));

  if (!aiChatState) {
    return <AIChatWidget isVisible={false} isMinimized={false} messages={[]} isTyping={false} />;
  }

  const messages =
    aiChatState
      .get('messages')
      ?.toArray()
      .map((msg: any) => msg.toJS()) || [];
  const isVisible = aiChatState.get('isVisible') || false;
  const isMinimized = aiChatState.get('isMinimized') || false;

  return <AIChatWidget isVisible={isVisible} isMinimized={isMinimized} messages={messages} isTyping={false} />;
};

export default AIChatContainer;
