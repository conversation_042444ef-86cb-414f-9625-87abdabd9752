import React from 'react';
import { TouchableOpacity, Text, StyleSheet, Animated } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { setChatVisibility } from '../../actions/aiChat';
import { CommonColors, scaleSizeW, scaleSizeH, setSpText } from '../../constants/themes';
import { selectEnableAiAssistant } from '../../sagas/selector';
import { testProps } from '../../utils';

const AIChatFAB: React.FC = () => {
  const dispatch = useDispatch();
  const isVisible = useSelector((state: any) => {
    const aiChatState = state.get('aiChat');
    return aiChatState ? aiChatState.get('isVisible') : false;
  });
  const enabled = true;

  const handlePress = () => {
    dispatch(setChatVisibility({ isVisible: !isVisible }));
  };

  if (!enabled) return null;
  if (isVisible) return null;

  return (
    <TouchableOpacity {...testProps('al_btn_192')} style={styles.fab} onPress={handlePress}>
      <Text style={styles.fabText}>AI</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  fab: {
    position: 'absolute',
    bottom: scaleSizeH(20),
    right: scaleSizeW(20),
    width: scaleSizeW(100),
    height: scaleSizeH(100),
    borderRadius: scaleSizeW(50),
    backgroundColor: CommonColors.PrincetonOrange,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: CommonColors.BlackCurrant,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
    zIndex: 999,
  },
  fabText: {
    fontSize: setSpText(20),
    fontWeight: '700',
    color: CommonColors.White,
  },
});

export default AIChatFAB;
