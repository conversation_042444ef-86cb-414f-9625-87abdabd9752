import React, { useState, useRef, useEffect } from 'react';
import { View, Text, TextInput, TouchableOpacity, FlatList, StyleSheet, Animated, Dimensions, KeyboardAvoidingView, Platform } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { sendChatMessage, setChatVisibility, toggleChatMinimized, clearChatHistory, ChatMessage, ActionButton } from '../../actions/aiChat';
import { CommonColors, scaleSizeW, scaleSizeH, setSpText, SharedStyles, scaleSize } from '../../constants/themes';
import IconTrash from '../ui/svgIcons/iconTrash';
import IconClose from '../ui/svgIcons/iconClose';
import IconMicrophone from '../ui/svgIcons/iconMicrophone';
import { testProps } from '../../utils';
import { useVoiceInput } from '../../hooks/useVoiceInput';

interface Props {
  isVisible: boolean;
  isMinimized: boolean;
  messages: ChatMessage[];
  isTyping: boolean;
}

const AIChatWidget: React.FC<Props> = ({ isVisible, isMinimized, messages, isTyping }) => {
  const dispatch = useDispatch();
  const [inputText, setInputText] = useState('');
  const [animatedValue] = useState(new Animated.Value(0));
  const flatListRef = useRef<FlatList>(null);
  const inputRef = useRef<TextInput>(null);
  const voiceTimeoutRef = useRef<any>(null);
  const lastSentTranscriptRef = useRef<string>('');

  // Voice input hook
  const [voiceState, voiceActions] = useVoiceInput();

  useEffect(() => {
    if (isVisible) {
      Animated.spring(animatedValue, {
        toValue: 1,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();
    } else {
      Animated.spring(animatedValue, {
        toValue: 0,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();
    }
  }, [isVisible]);

  useEffect(() => {
    // Auto-scroll to bottom when new messages arrive
    if (messages.length > 0 && flatListRef.current) {
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [messages]);

  // Handle voice input transcript with debouncing
  useEffect(() => {
    if (voiceState.transcript && !voiceState.isListening && !voiceState.isRecognizing) {
      // Clear any existing timeout
      if (voiceTimeoutRef.current) {
        clearTimeout(voiceTimeoutRef.current);
      }

      // Set a debounce timeout to prevent multiple sends
      voiceTimeoutRef.current = setTimeout(() => {
        const transcript = voiceState.transcript.trim();
        if (transcript && transcript !== lastSentTranscriptRef.current) {
          lastSentTranscriptRef.current = transcript;
          dispatch(sendChatMessage({ message: transcript }));
          voiceActions.clearTranscript();
        }
      }, 500); // 500ms debounce delay
    }

    // Cleanup timeout on unmount
    return () => {
      if (voiceTimeoutRef.current) {
        clearTimeout(voiceTimeoutRef.current);
      }
    };
  }, [voiceState.transcript, voiceState.isListening, voiceState.isRecognizing, dispatch, voiceActions]);

  // Reset last sent transcript when starting new voice input
  useEffect(() => {
    if (voiceState.isListening) {
      lastSentTranscriptRef.current = '';
    }
  }, [voiceState.isListening]);

  const handleSendMessage = () => {
    if (inputText.trim()) {
      dispatch(sendChatMessage({ message: inputText.trim() }));
      setInputText('');
    }
  };

  const handleActionButtonPress = (actionButton: ActionButton) => {
    dispatch(sendChatMessage({ message: actionButton.message }));
  };

  const toggleMinimize = () => {
    dispatch(toggleChatMinimized());
  };

  const closeChatWidget = () => {
    dispatch(setChatVisibility({ isVisible: false }));
  };

  const clearHistory = () => {
    dispatch(clearChatHistory());
  };

  const handleVoiceInput = async () => {
    if (voiceState.isListening || voiceState.isRecognizing) {
      // Stop listening/recognizing when button is clicked during voice input
      await voiceActions.stopListening();
      voiceActions.clearTranscript(); // Clear any partial transcript
    } else {
      if (!voiceState.isSupported) {
        // Show error message for unsupported devices
        return;
      }

      if (!voiceState.hasPermission) {
        const hasPermission = await voiceActions.requestPermissions();
        if (!hasPermission) {
          // Show permission denied message
          return;
        }
      }

      await voiceActions.startListening();
    }
  };

  const renderMessage = ({ item }: { item: ChatMessage }) => {
    // Special rendering for tool call messages
    if (item.type === 'tool_call') {
      return (
        <View style={[styles.messageContainer, styles.aiMessage]}>
          <View style={styles.toolCallHeader}>
            <View style={styles.toolCallIndicator} />
            <Text style={styles.toolCallText}>{item.text}</Text>
          </View>
          {item.toolCall && <Text style={styles.aiMessageText}>Using tool {item.toolCall.toolName}</Text>}
          <Text style={styles.aiTextTimestamp}>{new Date(item.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</Text>
        </View>
      );
    }

    // Special rendering for tool result messages
    if (item.type === 'tool_result') {
      return (
        <View style={[styles.messageContainer, styles.userMessage]}>
          <View style={styles.toolResultHeader}>
            <View style={styles.toolResultIndicator} />
            <Text style={styles.toolResultText}>{item.text}</Text>
          </View>
          <Text style={styles.userTextTimestamp}>{new Date(item.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</Text>
        </View>
      );
    }

    // Regular message rendering
    return (
      <View style={[styles.messageContainer, item.isUser ? styles.userMessage : styles.aiMessage]}>
        <Text style={[styles.messageText, item.isUser ? styles.userMessageText : styles.aiMessageText]}>{item.text}</Text>

        {/* Action buttons */}
        {!item.isUser && item.actionButtons && item.actionButtons.length > 0 && (
          <View style={styles.actionButtonsContainer}>
            {item.actionButtons.map((actionButton: ActionButton, index: number) => (
              <TouchableOpacity
                {...testProps('al_btn_108')}
                key={index}
                style={[
                  styles.actionButton,
                  actionButton.type === 'primary' && styles.actionButtonPrimary,
                  actionButton.type === 'secondary' && styles.actionButtonSecondary,
                  actionButton.type === 'danger' && styles.actionButtonDanger,
                ]}
                onPress={() => handleActionButtonPress(actionButton)}
              >
                <Text
                  style={[
                    styles.actionButtonText,
                    actionButton.type === 'primary' && styles.actionButtonTextPrimary,
                    actionButton.type === 'secondary' && styles.actionButtonTextSecondary,
                    actionButton.type === 'danger' && styles.actionButtonTextDanger,
                  ]}
                >
                  {actionButton.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        )}

        <Text style={item.isUser ? styles.userTextTimestamp : styles.aiTextTimestamp}>
          {new Date(item.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
        </Text>
      </View>
    );
  };

  const renderTypingIndicator = () => (
    <View style={[styles.messageContainer, styles.aiMessage]}>
      <View style={styles.typingContainer}>
        <View style={styles.typingDot} />
        <View style={styles.typingDot} />
        <View style={styles.typingDot} />
      </View>
    </View>
  );

  if (!isVisible) return null;

  const translateY = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [scaleSizeH(400), 0],
  });

  const opacity = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 1],
  });

  return (
    <Animated.View style={[styles.container, isMinimized && styles.minimizedContainer, { transform: [{ translateY }], opacity }]}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <View style={styles.aiIndicator} />
          <Text style={styles.headerTitle}>StoreHub AI Assistant</Text>
        </View>
        <View style={styles.headerRight}>
          <TouchableOpacity {...testProps('al_btn_959')} onPress={clearHistory} style={styles.headerButton}>
            <IconTrash color={CommonColors.White} width={28} height={28}></IconTrash>
          </TouchableOpacity>
          {/* <TouchableOpacity onPress={toggleMinimize} style={styles.headerButton}> */}
          {/*  <Text style={styles.headerButtonText}>{isMinimized ? '▲' : '▼'}</Text> */}
          {/* </TouchableOpacity> */}
          <TouchableOpacity {...testProps('al_btn_618')} onPress={closeChatWidget} style={styles.headerButton}>
            <IconClose color={CommonColors.White} width={28} height={28} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Chat Content */}
      {!isMinimized && (
        <>
          {/* Messages */}
          <View style={styles.messagesContainer}>
            <FlatList
              ref={flatListRef}
              data={messages}
              renderItem={renderMessage}
              keyExtractor={item => item.id}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.messagesList}
              ListFooterComponent={isTyping ? renderTypingIndicator : null}
            />
          </View>

          {/* Input */}
          <View style={styles.inputContainer}>
            {/* Voice status indicator */}
            {voiceState.isListening && (
              <View style={styles.voiceStatusContainer}>
                <View style={styles.voiceIndicator} />
                <Text style={styles.voiceStatusText}>Listening...</Text>
              </View>
            )}

            {/* Voice error message */}
            {voiceState.error && (
              <View style={styles.voiceErrorContainer}>
                <Text style={styles.voiceErrorText}>{voiceState.error}</Text>
              </View>
            )}

            <View style={styles.inputRow}>
              <TextInput
                {...testProps('al_textinput_106')}
                ref={inputRef}
                style={styles.textInput}
                value={inputText}
                onChangeText={setInputText}
                placeholder={voiceState.isListening ? 'Listening...' : 'Ask me anything about the POS system...'}
                placeholderTextColor={CommonColors.PlaceholderText}
                multiline
                maxLength={500}
                onSubmitEditing={handleSendMessage}
                blurOnSubmit={false}
                editable={!voiceState.isListening}
              />

              {/* Microphone Button */}
              {voiceState.isSupported && (
                <TouchableOpacity
                  {...testProps('al_btn_voice')}
                  style={[
                    styles.voiceButton,
                    (voiceState.isListening || voiceState.isRecognizing) && styles.voiceButtonActive,
                    !voiceState.hasPermission && styles.voiceButtonDisabled,
                  ]}
                  onPress={handleVoiceInput}
                  disabled={!voiceState.hasPermission}
                >
                  <IconMicrophone
                    color={voiceState.isListening || voiceState.isRecognizing ? CommonColors.White : CommonColors.Pumpkin}
                    width={24}
                    height={24}
                  />
                </TouchableOpacity>
              )}

              <TouchableOpacity
                {...testProps('al_btn_224')}
                style={[styles.sendButton, !inputText.trim() && styles.sendButtonDisabled]}
                onPress={handleSendMessage}
                disabled={!inputText.trim() || voiceState.isListening}
              >
                <Text style={styles.sendButtonText}>Send</Text>
              </TouchableOpacity>
            </View>
          </View>
        </>
      )}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: scaleSizeH(20),
    right: scaleSizeW(20),
    width: scaleSizeW(640),
    maxHeight: scaleSizeH(780),
    backgroundColor: CommonColors.White,
    shadowColor: CommonColors.BlackCurrant,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
    zIndex: 1000,
  },
  minimizedContainer: {
    maxHeight: scaleSizeH(100),
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: scaleSizeW(24),
    paddingVertical: scaleSizeH(20),
    backgroundColor: CommonColors.Pumpkin,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  aiIndicator: {
    width: scaleSizeW(14),
    height: scaleSizeH(14),
    borderRadius: scaleSizeW(7),
    backgroundColor: CommonColors.Emerald,
    marginRight: scaleSizeW(12),
  },
  headerTitle: {
    color: CommonColors.White,
    fontSize: setSpText(24),
    fontWeight: '600',
  },
  headerRight: {
    flexDirection: 'row',
  },
  headerButton: {
    marginLeft: scaleSizeW(0),
    paddingHorizontal: scaleSizeW(8),
    paddingVertical: scaleSizeH(8),
  },
  headerButtonText: {
    color: CommonColors.White,
    fontSize: setSpText(20),
    fontWeight: '500',
  },
  messagesContainer: {
    flex: 1,
    maxHeight: scaleSizeH(480),
  },
  messagesList: {
    paddingHorizontal: scaleSizeW(24),
    paddingVertical: scaleSizeH(20),
  },
  messageContainer: {
    marginVertical: scaleSizeH(8),
    maxWidth: '90%',
  },
  userMessage: {
    alignSelf: 'flex-end',
    backgroundColor: CommonColors.Solitude,
    borderRadius: scaleSizeW(32),
    borderBottomRightRadius: scaleSizeW(8),
    paddingHorizontal: scaleSizeW(24),
    paddingVertical: scaleSizeH(18),
  },
  aiMessage: {
    alignSelf: 'flex-start',
    backgroundColor: CommonColors.PrincetonOrange,
    borderRadius: scaleSizeW(32),
    borderBottomLeftRadius: scaleSizeW(8),
    paddingHorizontal: scaleSizeW(24),
    paddingVertical: scaleSizeH(18),
  },
  messageText: {
    fontSize: setSpText(20),
    lineHeight: setSpText(26),
  },
  userMessageText: {
    color: CommonColors.BlackCurrant,
  },
  aiMessageText: {
    color: CommonColors.White,
  },
  userTextTimestamp: {
    fontSize: setSpText(18),
    color: CommonColors.BlackCurrant,
    marginTop: scaleSizeH(8),
    textAlign: 'right',
  },
  aiTextTimestamp: {
    fontSize: setSpText(18),
    color: CommonColors.White,
    marginTop: scaleSizeH(8),
    textAlign: 'right',
  },
  actionButtonsContainer: {
    marginTop: scaleSizeH(16),
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  actionButton: {
    backgroundColor: CommonColors.White,
    borderWidth: 1,
    borderColor: CommonColors.Pumpkin,
    borderRadius: scaleSizeW(8),
    paddingHorizontal: scaleSizeW(20),
    paddingVertical: scaleSizeH(12),
    marginRight: scaleSizeW(12),
    marginBottom: scaleSizeH(8),
  },
  actionButtonPrimary: {
    backgroundColor: CommonColors.Pumpkin,
    borderColor: CommonColors.Pumpkin,
  },
  actionButtonSecondary: {
    backgroundColor: CommonColors.White,
    borderColor: CommonColors.Manatee,
  },
  actionButtonDanger: {
    backgroundColor: CommonColors.Raspberry,
    borderColor: CommonColors.Raspberry,
  },
  actionButtonText: {
    color: CommonColors.Pumpkin,
    fontSize: setSpText(20),
    fontWeight: '500',
  },
  actionButtonTextPrimary: {
    color: CommonColors.White,
  },
  actionButtonTextSecondary: {
    color: CommonColors.Manatee,
  },
  actionButtonTextDanger: {
    color: CommonColors.White,
  },
  typingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  typingDot: {
    width: scaleSizeW(10),
    height: scaleSizeH(10),
    borderRadius: scaleSizeW(5),
    backgroundColor: CommonColors.Manatee,
    marginHorizontal: scaleSizeW(4),
  },
  inputContainer: {
    paddingHorizontal: scaleSizeW(24),
    paddingVertical: scaleSizeH(16),
    borderTopWidth: 1,
    borderTopColor: CommonColors.Pewter,
    minHeight: scaleSizeH(80),
    maxHeight: scaleSizeH(160),
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: CommonColors.Pewter,
    paddingHorizontal: scaleSizeW(24),
    paddingVertical: scaleSizeH(12),
    fontSize: setSpText(20),
    color: CommonColors.MainTextGrey,
    maxHeight: scaleSizeH(80),
    marginRight: scaleSizeW(16),
  },
  sendButton: {
    backgroundColor: CommonColors.Pumpkin,
    borderRadius: scaleSizeW(10),
    paddingHorizontal: scaleSizeW(28),
    paddingVertical: scaleSizeH(18),
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: CommonColors.Disabled,
  },
  sendButtonText: {
    color: CommonColors.White,
    fontSize: setSpText(18),
    fontWeight: '600',
  },
  chatContent: {
    flex: 1,
  },
  messagesContent: {
    paddingHorizontal: scaleSizeW(24),
    paddingVertical: scaleSizeH(20),
  },
  toolResultMessage: {
    backgroundColor: CommonColors.Solitude,
    borderRadius: scaleSizeW(32),
    paddingHorizontal: scaleSizeW(24),
    paddingVertical: scaleSizeH(18),
  },
  toolResultHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  toolResultIndicator: {
    width: scaleSizeW(14),
    height: scaleSizeH(14),
    borderRadius: scaleSizeW(7),
    backgroundColor: CommonColors.Emerald,
    marginRight: scaleSizeW(12),
  },
  toolResultText: {
    color: CommonColors.BlackCurrant,
    fontSize: setSpText(20),
    fontWeight: '600',
  },
  toolResultDetails: {
    color: CommonColors.BlackCurrant,
    fontSize: setSpText(18),
    marginTop: scaleSizeH(8),
  },
  toolCallMessage: {
    backgroundColor: CommonColors.PrincetonOrange,
    borderRadius: scaleSizeW(32),
    paddingHorizontal: scaleSizeW(24),
    paddingVertical: scaleSizeH(18),
  },
  toolCallHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  toolCallIndicator: {
    width: scaleSizeW(14),
    height: scaleSizeH(14),
    borderRadius: scaleSizeW(7),
    backgroundColor: CommonColors.Emerald,
    marginRight: scaleSizeW(12),
  },
  toolCallText: {
    color: CommonColors.White,
    fontSize: setSpText(20),
    fontWeight: '600',
  },
  voiceStatusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: scaleSizeH(8),
    paddingHorizontal: scaleSizeW(12),
    paddingVertical: scaleSizeH(6),
    backgroundColor: CommonColors.Solitude,
    borderRadius: scaleSizeW(8),
  },
  voiceIndicator: {
    width: scaleSizeW(14),
    height: scaleSizeH(14),
    borderRadius: scaleSizeW(7),
    backgroundColor: CommonColors.Emerald,
    marginRight: scaleSizeW(8),
  },
  voiceStatusText: {
    color: CommonColors.BlackCurrant,
    fontSize: setSpText(16),
  },
  voiceErrorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: scaleSizeH(8),
    paddingHorizontal: scaleSizeW(12),
    paddingVertical: scaleSizeH(6),
    backgroundColor: CommonColors.Raspberry,
    borderRadius: scaleSizeW(8),
  },
  voiceErrorText: {
    color: CommonColors.White,
    fontSize: setSpText(16),
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  voiceButton: {
    backgroundColor: CommonColors.White,
    borderWidth: 1,
    borderColor: CommonColors.Pumpkin,
    borderRadius: scaleSizeW(10),
    paddingHorizontal: scaleSizeW(12),
    paddingVertical: scaleSizeH(12),
    marginRight: scaleSizeW(16),
    justifyContent: 'center',
    alignItems: 'center',
  },
  voiceButtonActive: {
    backgroundColor: CommonColors.Pumpkin,
  },
  voiceButtonDisabled: {
    backgroundColor: CommonColors.Disabled,
    borderColor: CommonColors.Disabled,
  },
});

export default AIChatWidget;
