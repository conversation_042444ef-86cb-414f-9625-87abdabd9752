import { isNil } from 'lodash';
import React, { memo } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { useSelector } from 'react-redux';
import { currentThemes, scaleSizeH, scaleSizeW, t } from '../../constants';
import { selectRegisterId, selectStoreName } from '../../sagas/selector';

const StoreInfo = () => {
  const registerId = useSelector(selectRegisterId);
  const storeName = useSelector(selectStoreName);

  return (
    <View style={styles.container}>
      {!isNil(registerId) && <Text style={styles.text}>{`${t('Register')} ${registerId}`}</Text>}
      {!!storeName && <Text style={styles.name}>{storeName}</Text>}
    </View>
  );
};

export default memo(StoreInfo);

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    right: scaleSizeW(60),
    bottom: scaleSizeW(60),
  },
  text: {
    color: '#FFF',
    fontSize: currentThemes.fontSize18,
    marginBottom: scaleSizeH(6),
    textAlign: 'right',
  },
  name: {
    color: '#FFF',
    fontSize: currentThemes.fontSize18,
    maxWidth: scaleSizeW(300),
    textAlign: 'right',
  },
});
