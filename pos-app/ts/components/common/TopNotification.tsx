import { filter, findIndex, forEach } from 'lodash';
import React, { FC, memo, MutableRefObject, PureComponent, useEffect, useRef } from 'react';
import { Animated, Platform, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { useSelector } from 'react-redux';

import { CommonColors, currentThemes, IsIOS, scaleSizeH, scaleSizeW, screenWidth, t } from '../../constants';
import { getCurrentRouteName } from '../../navigation/navigatorService';
import { selectOfflinePrintersCount, selectPrinterErrorCount } from '../../sagas/selector';

import { useMemoizedFn } from 'ahooks';

import { AllSettingTabs } from '../../constants/settingTabs';
import { navigateToSettings } from '../../navigation/commonNavigate';
import { testProps } from '../../utils';
import { LoggingLevel, PrinterAction, trackLogEvent } from '../../utils/logComponent';
import { NcsNotification } from '../ncs/NcsNotification';
import { ExclamationRed, IconSuccess, MaterialCancel } from '../ui';
import TextUnderline from './TextUnderline';

export enum ContentType {
  PrintAssignedToast,
  EditMigrateTableLayoutToast,
  PrinterErrorToast,
  ShiftNotCloseToast,
  SplitErrorToast,
  Error,
  Success,
  KdsLostToast,
  NcsLostToast,
  CfdLostToast,
  PrinterOfflineToast,
}
export interface ContentItemType {
  type: ContentType;
  content?: React.ReactNode;
}

export interface TopNotificationRefType {
  addToast(content: ContentItemType[]): void;
  removeToast(types?: ContentType[]): void;
}
export const topNotificationRef: MutableRefObject<TopNotificationRefType> = React.createRef();

export interface State {
  visibleValue: Animated.Value;
  contents: ContentItemType[];
  left: number;
  place?: 'top' | 'bottom';
}

class TopNotification extends PureComponent<Record<string, unknown>, State> {
  ANIMATION_DURATION = 225;
  contentRef;
  visiable = false;
  constructor(props) {
    super(props);
    this.state = {
      visibleValue: new Animated.Value(0),
      contents: [],
      left: 0,
    };
  }

  componentDidMount(): void {
    topNotificationRef.current = {
      addToast: this.addToast,
      removeToast: this.removeToast,
    };
  }

  addToast = (addContent: ContentItemType[]) => {
    requestAnimationFrame(() => {
      const { contents } = this.state;
      const newContents = filter(contents, content => findIndex(addContent, item => item.type === content.type) < 0).concat(addContent);
      if (newContents.length > 0) {
        this.setState({ contents: newContents }, () => {
          this.openToast();
        });
      } else {
        this.closeToast();
      }
    });
  };

  removeToast = (types?: ContentType[]) => {
    if (!Boolean(types)) {
      this.closeToast();
    } else {
      const { contents } = this.state;
      const newContents = filter(contents, item => findIndex(types, type => type === item.type) < 0);
      if (newContents.length > 0) {
        this.setState({ contents: newContents }, () => {
          this.openToast();
        });
      } else {
        this.closeToast();
      }
    }
  };

  displayTopNotification = left => {
    this.setState({ left }, () => {
      this.state.visibleValue.setValue(0);
      Animated.timing(this.state.visibleValue, {
        toValue: 1,
        duration: this.ANIMATION_DURATION,
        useNativeDriver: true,
      }).start(() => {
        this.visiable = true;
      });
    });
  };

  openToast = () => {
    if (!this.visiable) {
      requestAnimationFrame(() => {
        if (Boolean(this.contentRef)) {
          this.contentRef.measure((x, y, w) => {
            const left = (screenWidth - w) / 2 || scaleSizeW(670);
            this.displayTopNotification(left);
          });
        } else {
          this.displayTopNotification(scaleSizeW(670));
        }
      });
    }
  };

  closeToast = () => {
    if (this.visiable) {
      Animated.timing(this.state.visibleValue, {
        toValue: 0,
        duration: this.ANIMATION_DURATION,
        useNativeDriver: true,
      }).start(() => {
        this.setState({ contents: [] });
        this.visiable = false;
      });
    }
  };

  render(): React.ReactNode {
    const { visibleValue, contents, left } = this.state;
    const animStyle = FlashMessageTransition(visibleValue);
    const contentItems = [];
    forEach(contents, item => {
      contentItems.push(item.content);
    });
    return (
      <Animated.View pointerEvents='box-none' style={[styles.root, animStyle, { left }]}>
        <View
          style={styles.content}
          ref={ref => {
            this.contentRef = ref;
          }}
          pointerEvents='box-none'
          collapsable={false}
        >
          {contentItems}
        </View>
      </Animated.View>
    );
  }
}

export default TopNotification;

const styles = StyleSheet.create({
  root: {
    position: 'absolute',
    zIndex: 9999,
    elevation: 9999,
    top: scaleSizeH(20),
  },
  content: {
    flexDirection: 'column',
    alignItems: 'center',
    minWidth: screenWidth / 2,
  },
  flashMsgContent: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: scaleSizeW(24),
    backgroundColor: '#FFEAEA',
    height: scaleSizeH(64),
    borderRadius: scaleSizeW(8),
    marginTop: scaleSizeH(10),
  },
  flashMsgOrangeContent: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: scaleSizeW(24),
    backgroundColor: '#FFF3E5',
    height: scaleSizeH(64),
    borderRadius: scaleSizeW(8),
    marginTop: scaleSizeH(10),
  },
  successMsgContent: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: scaleSizeW(24),
    backgroundColor: '#D2FFEF',
    height: scaleSizeH(64),
    borderRadius: scaleSizeW(8),
    marginTop: scaleSizeH(10),
  },
  errorText: {
    marginLeft: scaleSizeW(12),
    color: '#EB4646',
    fontSize: currentThemes.fontSize16,
    fontWeight: '400',
  },
  successText: {
    marginLeft: scaleSizeW(12),
    color: '#00A86B',
    fontSize: currentThemes.fontSize16,
    fontWeight: '400',
  },
  closeButton: {
    marginLeft: scaleSizeW(30),
  },
});

export function FlashMessageTransition(animValue, position = 'top') {
  const OFFSET_HEIGHT = Platform.OS !== 'ios' ? 60 : 48;
  const opacity = animValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 1],
  });

  if (position === 'top') {
    const translateY = animValue.interpolate({
      inputRange: [0, 1],
      outputRange: [-OFFSET_HEIGHT, 0],
    });

    return {
      transform: [{ translateY }],
      opacity,
    };
  } else if (position === 'bottom') {
    const translateY = animValue.interpolate({
      inputRange: [0, 1],
      outputRange: [OFFSET_HEIGHT, 0],
    });

    return {
      transform: [{ translateY }],
      opacity,
    };
  }

  return {
    opacity,
  };
}

export const closeTopNotification = (types?: ContentType[]) => {
  if (topNotificationRef.current && topNotificationRef.current.removeToast) {
    topNotificationRef.current.removeToast(types);
  }
};

export const closeTopNotificationDelay = (delay: number, types?: ContentType[]) => {
  let timer = setTimeout(() => {
    closeTopNotification(types);
    timer && clearTimeout(timer);
    timer = null;
  }, delay);
};

const BLACK_ROUTES = ['Settings', 'ModalFailedPrintJobList', 'SignIn', 'LockScreen'];
const errorIconSize = IsIOS ? scaleSizeW(28) : scaleSizeW(24);
const PrintFailedNotification: FC = () => {
  const errorCount = useSelector(selectPrinterErrorCount);

  const goToSetting = useMemoizedFn(() => {
    closeTopNotification([ContentType.PrinterErrorToast]);
    navigateToSettings({ tabName: AllSettingTabs.Printer, timeStamp: Date.now() });
  });

  const onClose = useMemoizedFn(() => {
    closeTopNotification([ContentType.PrinterErrorToast]);
  });

  useEffect(() => {
    if (errorCount <= 0) {
      onClose();
    }
  }, [errorCount]);
  return (
    <View style={styles.flashMsgContent}>
      <ExclamationRed width={errorIconSize} height={errorIconSize} />
      <Text style={styles.errorText}>{`${errorCount} ${t('printer job has failed')}.`}</Text>
      <TextUnderline onPress={goToSetting}>{t('Go to printer settings')}</TextUnderline>
      <TouchableOpacity
        {...testProps('al_btn_615')}
        activeOpacity={1}
        hitSlop={{ left: scaleSizeW(28), right: scaleSizeW(28), top: scaleSizeH(10) }}
        onPress={onClose}
      >
        <MaterialCancel style={styles.closeButton} />
      </TouchableOpacity>
    </View>
  );
};
export const openPrinterErrorToast = () => {
  const currentRoute = getCurrentRouteName();
  const canShow = !BLACK_ROUTES.includes(currentRoute);
  if (topNotificationRef.current && topNotificationRef.current.addToast && canShow) {
    topNotificationRef.current.addToast([{ type: ContentType.PrinterErrorToast, content: <PrintFailedNotification key={'PrinterErrorToast'} /> }]);
  }
};

const PrinterAssignNotification: FC<{ text: string; onClose: () => void }> = ({ text, onClose }) => {
  const goToSetting = () => {
    onClose();
    navigateToSettings({ tabName: AllSettingTabs.Printer, timeStamp: Date.now() });
  };

  return (
    <View style={styles.flashMsgContent}>
      <ExclamationRed width={errorIconSize} height={errorIconSize} />
      <Text style={styles.errorText}>{text}</Text>
      <TextUnderline onPress={goToSetting}>{t('Assign Now')}</TextUnderline>
      <TouchableOpacity
        {...testProps('al_btn_250')}
        activeOpacity={1}
        hitSlop={{ left: scaleSizeW(28), right: scaleSizeW(28), top: scaleSizeH(10) }}
        onPress={onClose}
      >
        <MaterialCancel style={styles.closeButton} />
      </TouchableOpacity>
    </View>
  );
};

const NormalWarningNotification: FC<{ text: string; type: ContentType; showClose?: boolean }> = ({ text, type, showClose = true }) => {
  const onClose = () => closeTopNotification([type]);

  return (
    <View style={styles.flashMsgContent}>
      <ExclamationRed width={errorIconSize} height={errorIconSize} />
      <Text style={styles.errorText}>{text}</Text>
      {showClose && (
        <TouchableOpacity
          {...testProps('al_btn_772')}
          activeOpacity={1}
          hitSlop={{ left: scaleSizeW(28), right: scaleSizeW(28), top: scaleSizeH(10) }}
          onPress={onClose}
        >
          <MaterialCancel style={styles.closeButton} />
        </TouchableOpacity>
      )}
    </View>
  );
};

export const openPrintAssignedToast = (text: string = t('Kitchen docket not printed')) => {
  const currentRoute = getCurrentRouteName();
  const canShow = !BLACK_ROUTES.includes(currentRoute);
  if (canShow && topNotificationRef.current && topNotificationRef.current.addToast) {
    topNotificationRef.current.addToast([
      {
        type: ContentType.PrintAssignedToast,
        content: <PrinterAssignNotification key={'PrintAssignedToast'} text={text} onClose={() => closeTopNotification([ContentType.PrintAssignedToast])} />,
      },
    ]);
  }
};

export type KdsSettingNotificationType = { text: string; subText?: string; onClose: () => void };
const KdsSettingNotification: FC<KdsSettingNotificationType> = ({ text, subText, onClose }) => {
  const goToSetting = () => {
    onClose();
    navigateToSettings({ tabName: AllSettingTabs.KitchenDisplaySystem, timeStamp: Date.now() });
  };

  return (
    <View style={styles.flashMsgContent}>
      <ExclamationRed width={errorIconSize} height={errorIconSize} />
      <Text style={styles.errorText}>{text}</Text>
      {Boolean(subText) && <TextUnderline onPress={goToSetting}>{subText}</TextUnderline>}
      <TouchableOpacity
        {...testProps('al_btn_797')}
        activeOpacity={1}
        hitSlop={{ left: scaleSizeW(28), right: scaleSizeW(28), top: scaleSizeH(10) }}
        onPress={onClose}
      >
        <MaterialCancel style={styles.closeButton} />
      </TouchableOpacity>
    </View>
  );
};

export type CfdSettingNotificationType = { text: string; subText?: string; onClose: () => void };
const CfdSettingNotification: FC<CfdSettingNotificationType> = ({ text, subText, onClose }) => {
  const goToSetting = () => {
    onClose();
    navigateToSettings({ tabName: AllSettingTabs.NewCFD, timeStamp: Date.now() });
  };

  return (
    <View style={styles.flashMsgContent}>
      <ExclamationRed width={errorIconSize} height={errorIconSize} />
      <Text style={styles.errorText}>{text}</Text>
      {Boolean(subText) && <TextUnderline onPress={goToSetting}>{subText}</TextUnderline>}
      <TouchableOpacity
        {...testProps('al_btn_207')}
        activeOpacity={1}
        hitSlop={{ left: scaleSizeW(28), right: scaleSizeW(28), top: scaleSizeH(10) }}
        onPress={onClose}
      >
        <MaterialCancel style={styles.closeButton} />
      </TouchableOpacity>
    </View>
  );
};

export const openKdsErrorToast = (text: string = t('No KDS connected'), subText: string = t('Check Now')) => {
  const currentRoute = getCurrentRouteName();
  const inBlackRoutes = BLACK_ROUTES.includes(currentRoute);
  if (inBlackRoutes) {
    openErrorToast(text);
    return;
  }
  if (topNotificationRef.current && topNotificationRef.current.addToast) {
    topNotificationRef.current.addToast([
      {
        type: ContentType.KdsLostToast,
        content: <KdsSettingNotification key={'KdsLostToast'} text={text} subText={subText} onClose={() => closeTopNotification([ContentType.KdsLostToast])} />,
      },
    ]);
  }
};

export const openCfdErrorToast = (text: string = t('No CFD connected'), subText: string = t('Check Now')) => {
  const currentRoute = getCurrentRouteName();
  const inBlackRoutes = BLACK_ROUTES.includes(currentRoute);
  if (inBlackRoutes) {
    openErrorToast(text);
    return;
  }
  if (topNotificationRef.current && topNotificationRef.current.addToast) {
    topNotificationRef.current.addToast([
      {
        type: ContentType.CfdLostToast,
        content: <CfdSettingNotification key={'CfdLostToast'} text={text} subText={subText} onClose={() => closeTopNotification([ContentType.CfdLostToast])} />,
      },
    ]);
  }
};

export const openNcsErrorToast = (text: string = t('No NCS connected'), subText: string = t('Check Now')) => {
  const currentRoute = getCurrentRouteName();
  const inBlackRoutes = BLACK_ROUTES.includes(currentRoute);
  if (inBlackRoutes) {
    openErrorToast(text);
    return;
  }
  if (topNotificationRef.current && topNotificationRef.current.addToast) {
    topNotificationRef.current.addToast([
      {
        type: ContentType.NcsLostToast,
        content: <NcsNotification key={'NcsLostToast'} text={text} subText={subText} onClose={() => closeTopNotification([ContentType.NcsLostToast])} />,
      },
    ]);
  }
};

export type ConfirmErrorNotificationType = { text: string; subText: string; onPress: () => void; onClose: () => void };

const ConfirmErrorNotification = memo(({ text, subText, onPress, onClose }: ConfirmErrorNotificationType) => {
  return (
    <View style={styles.flashMsgContent}>
      <ExclamationRed width={errorIconSize} height={errorIconSize} />
      <Text style={styles.errorText}>{text}</Text>
      <TextUnderline onPress={onPress}>{subText}</TextUnderline>
      <TouchableOpacity
        {...testProps('al_btn_684')}
        activeOpacity={1}
        hitSlop={{ left: scaleSizeW(28), right: scaleSizeW(28), top: scaleSizeH(10) }}
        onPress={onClose}
      >
        <MaterialCancel style={styles.closeButton} />
      </TouchableOpacity>
    </View>
  );
});

const PrinterOfflineNotification = memo((props: ConfirmErrorNotificationType) => {
  const { text, subText, onClose, onPress } = props;
  const timeStampRef = useRef<number>(0);
  const offlineCount = useSelector(selectOfflinePrintersCount);

  const handleClose = useMemoizedFn(() => {
    timeStampRef.current = 0;
    onClose();
  });

  useEffect(() => {
    if (offlineCount <= 0) {
      onClose();
    }
  }, [offlineCount]);

  useEffect(() => {
    timeStampRef.current = Date.now();
    return () => {
      if (Date.now() - timeStampRef.current < 2 * 60 * 1000) {
        // if the merchant check the toast within 2 minutes, upload the logs to track
        trackLogEvent({ action: PrinterAction.ClickPrinterOffline, level: LoggingLevel.Info, tags: ['rn_pos', 'printer'] });
      }
    };
  }, []);
  return <ConfirmErrorNotification text={text} subText={subText} onClose={handleClose} onPress={onPress} />;
});

export const openPrinterOfflineToast = (text: string, subText = t('Fix Now')) => {
  const currentRoute = getCurrentRouteName();
  const inBlackRoutes = BLACK_ROUTES.includes(currentRoute);
  if (inBlackRoutes) {
    openErrorToast(text);
    return;
  }

  if (topNotificationRef.current && topNotificationRef.current.addToast) {
    const onClose = () => closeTopNotification([ContentType.PrinterOfflineToast]);
    const onPress = () => {
      onClose();
      navigateToSettings({ tabName: AllSettingTabs.Printer, timeStamp: Date.now() });
    };
    topNotificationRef.current.addToast([
      {
        type: ContentType.PrinterOfflineToast,
        content: <PrinterOfflineNotification key={'PrinterOfflineToast'} text={text} subText={subText} onClose={onClose} onPress={onPress} />,
      },
    ]);
  }
};

export const openShiftNotCloseToast = (text: string = t('Last shift not closed')) => {
  if (topNotificationRef.current && topNotificationRef.current.addToast) {
    topNotificationRef.current.addToast([
      {
        type: ContentType.ShiftNotCloseToast,
        content: <NormalWarningNotification key={'ShiftNotCloseToast'} text={text} type={ContentType.ShiftNotCloseToast} />,
      },
    ]);
  }
};

const EditMigrateTableLayoutNotification: FC<{ text: string; onClose: () => void }> = ({ text, onClose }) => {
  const goToSetting = () => {
    onClose();
    navigateToSettings({ tabName: AllSettingTabs.Layouts, timeStamp: Date.now() });
  };

  return (
    <View style={styles.flashMsgOrangeContent}>
      <IconSuccess color={CommonColors.Pumpkin} width={errorIconSize} height={errorIconSize} />
      <Text style={styles.errorText}>{text}</Text>
      <TextUnderline onPress={goToSetting}>{t('Edit Table Layout Now')}</TextUnderline>
      <TouchableOpacity
        {...testProps('al_btn_758')}
        activeOpacity={1}
        hitSlop={{ left: scaleSizeW(28), right: scaleSizeW(28), top: scaleSizeH(10) }}
        onPress={onClose}
      >
        <MaterialCancel style={styles.closeButton} />
      </TouchableOpacity>
    </View>
  );
};

export const editMigrateTableLayoutToast = (text: string = t('Your table layout is now migrated')) => {
  const currentRoute = getCurrentRouteName();
  const canShow = !BLACK_ROUTES.includes(currentRoute);
  if (canShow && topNotificationRef.current && topNotificationRef.current.addToast) {
    topNotificationRef.current.addToast([
      {
        type: ContentType.EditMigrateTableLayoutToast,
        content: (
          <EditMigrateTableLayoutNotification
            key={'editMigrateTableLayoutToast'}
            text={text}
            onClose={() => closeTopNotification([ContentType.EditMigrateTableLayoutToast])}
          />
        ),
      },
    ]);
  }
};

const SuccessNotification: FC<{ text: string }> = ({ text }) => {
  return (
    <View style={styles.successMsgContent}>
      <IconSuccess />
      <Text style={styles.successText}>{text}</Text>
    </View>
  );
};

export const openSuccessToast = (text: string = t('Merged Successfully'), duration = 3 * 1000) => {
  if (topNotificationRef.current && topNotificationRef.current.addToast) {
    topNotificationRef.current.addToast([
      {
        type: ContentType.Success,
        content: <SuccessNotification key={text} text={text} />,
      },
    ]);
    if (duration > 0) {
      let timer = setTimeout(() => {
        closeTopNotification([ContentType.Success]);
        timer && clearTimeout(timer);
        timer = null;
      }, duration);
    }
  }
};

export const openErrorToast = (text: string = t('An error occurred, please try again'), duration = 3 * 1000, type = ContentType.Error) => {
  if (topNotificationRef.current && topNotificationRef.current.addToast) {
    topNotificationRef.current.addToast([
      {
        type,
        content: <NormalWarningNotification key={text} text={text} type={type} showClose={false} />,
      },
    ]);
    if (duration > 0) {
      let timer = setTimeout(() => {
        closeTopNotification([ContentType.Error]);
        timer && clearTimeout(timer);
        timer = null;
      }, duration);
    }
  }
};
