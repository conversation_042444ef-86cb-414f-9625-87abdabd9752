import React, { PureComponent } from 'react';
import { Keyboard, StyleSheet, Text, TextInput, TextInputProps, TouchableOpacity, View } from 'react-native';

import { currentThemes, scaleSizeH, scaleSizeW, SharedStyles } from '../../../constants';
import { testProps } from '../../../utils';
import { FontAwesomeIcons, MaterialIcons } from '../../ui';
import { ValidatorType } from './Validator';

interface GenearlTextInputProps extends TextInputProps {
  groupTitle?: string;
  showLabel: string;
  required?: boolean;
  validator: ValidatorType | ValidatorType[];
  hideError?: boolean;
  hideRequired?: boolean;
  hideCleanButton?: boolean;
  hidden?: boolean;
  inputTextStyle?: any;
  contentStyle?: any; // Set input height in this prop
  createAdditionalButton?(): React.ReactElement<any>;

  txd?: string;
  secureTextEntry?: boolean;
  inputContainerStyle?: any;
  showLabelStyle?: any;

  onClearValue?(): void;

  renderClearIcon?(): React.ReactElement<any>;

  id?: string;
}

interface GenearlTextInputState {
  value: string;
  errorMessage: string;
  secureTextEntry: boolean;
  keyboardShown: boolean;
}

export default class GeneralTextInput extends PureComponent<GenearlTextInputProps, GenearlTextInputState> {
  static defaultProps = {
    editable: true,
    multiline: false,
    autoCorrect: false,
    groupTitle: undefined,
    hideError: true,
    hideCleanButton: false,
    hidden: false,
    autoCapitalize: 'none',
    hideRequired: true,
  };

  private _refTextInput: TextInput;

  constructor(props) {
    super(props);
    this.state = {
      value: props.value || '',
      errorMessage: '',
      secureTextEntry: props.secureTextEntry || false,
      keyboardShown: true,
    };
  }

  UNSAFE_componentWillReceiveProps(nextProps: GenearlTextInputProps) {
    // Here we didn't detech nextPros.value is undefined.So we need to use clear() when clear value.
    if (nextProps.value !== this.props.value && nextProps.value) {
      this.setState({ value: nextProps.value });
    }
  }

  private keyboardDidHideListener;

  componentWillMount() {
    this.keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', this._keyboardDidHide);
  }

  componentWillUnmount() {
    this.keyboardDidHideListener.remove();
  }

  focus = () => {
    this._refTextInput && this._refTextInput.focus();
  };

  isFocused = () => {
    return this._refTextInput && this._refTextInput.isFocused();
  };

  clear = () => {
    this.setState({ value: '', errorMessage: '' });
  };

  blur = () => {
    this._refTextInput && this._refTextInput.blur();
  };

  getValue = () => {
    return this.state.value;
  };

  getError = () => {
    return this.state.errorMessage;
  };

  getPending = () => {
    return false;
  };

  _keyboardDidHide = () => {
    this._refTextInput && this._refTextInput.blur();
  };

  renderClearButton = () => {
    const { renderClearIcon } = this.props;
    const { value } = this.state;
    return (
      <TouchableOpacity
        {...testProps('al_btn_607')}
        activeOpacity={1}
        style={[SharedStyles.columnCenter, styles.clearButtonContainer]}
        onPress={this.clearValue}
      >
        <View>{Boolean(value) && (renderClearIcon ? renderClearIcon() : <MaterialIcons name={'clear'} size={18} color={'#303030'} />)}</View>
      </TouchableOpacity>
    );
  };

  clearValue = () => {
    const { required, onClearValue } = this.props;
    if (!required) {
      this.setState(
        {
          value: '',
          errorMessage: '',
        },
        () => !onClearValue && this.props.onChangeText && this.props.onChangeText('')
      );
    } else {
      this.setState({ value: '' }, () => !onClearValue && this.props.onChangeText && this.props.onChangeText(''));
    }
    onClearValue && onClearValue();
  };

  setValue = value => {
    this.setState({ value });
  };

  onChangeTextHandler = newValue => {
    this.setState({ value: newValue, errorMessage: '' }, () => {
      this.props.onChangeText && this.props.onChangeText(newValue);
    });
  };

  validate = () => {
    const { validator, required } = this.props;
    const value = this.state.value;

    let result;
    // If not required and no value, needn't validate then.
    if (!validator || (!required && !value)) {
      return;
    }
    const validatorArray = Array.isArray(validator) ? validator : [validator];
    // Catch first error message.
    for (const _validator of validatorArray) {
      const { ok, message } = _validator(value);
      if (!ok) {
        this.setState({ errorMessage: message });
        result = message;
        break;
      }
    }
    return result;
  };

  onEndEditingHandler = ({ nativeEvent: { text } }) => {
    const { onEndEditing } = this.props;
    this.validate();
    onEndEditing && onEndEditing({ nativeEvent: { text } } as any);
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  onBlurHandler = ({ nativeEvent: { text } }) => {
    // TODO: This callback behave like be triggered by all TexTInput onBlur, not each one.
    this.validate();
  };

  onSubmitEditingHandler = e => {
    this.validate();
    this.props.onSubmitEditing && this.props.onSubmitEditing(e);
  };

  renderLabel = () => {
    const { errorMessage } = this.state;
    const { showLabel, showLabelStyle, hideError } = this.props;
    const showErrorMessage = Boolean(errorMessage) && !hideError;

    if (!showLabel) {
      return null;
    }

    const errorColor = 'red';
    return (
      <View style={[SharedStyles.row, { marginBottom: scaleSizeW(8) }]}>
        {showErrorMessage && <FontAwesomeIcons name='exclamation-circle' color={errorColor} size={currentThemes.mediumFontSize} />}
        <Text
          style={[
            {
              color: '#959595',
              fontSize: currentThemes.mediumFontSize,
            },
            showLabelStyle,
            showErrorMessage && { color: errorColor, paddingLeft: 3 },
          ]}
        >
          {showLabel}
          {showErrorMessage && <Text style={{ color: errorColor }}>{`: ${errorMessage}`}</Text>}
        </Text>
      </View>
    );
  };

  renderGroupTitle = groupTitle => {
    return (
      <View style={{ backgroundColor: '#999999', width: '100%', paddingVertical: 5, paddingLeft: 5 }}>
        <Text style={{ color: '#1F2230' }}>{groupTitle}</Text>
      </View>
    );
  };

  refHandler = ref => {
    this._refTextInput = ref;
  };

  onKeyPressHandler = e => {
    const { onKeyPress } = this.props;
    onKeyPress && onKeyPress(e);
  };

  renderEye = () => {
    const { secureTextEntry } = this.state;

    return (
      <TouchableOpacity
        {...testProps('al_btn_509')}
        activeOpacity={1}
        style={[{ width: scaleSizeW(50) }, SharedStyles.columnCenter]}
        onPress={() => {
          this.setState({ secureTextEntry: !secureTextEntry });
        }}
      >
        <MaterialIcons name={secureTextEntry ? 'visibility-off' : 'visibility'} color={'#1F2230'} size={scaleSizeW(36)} />
      </TouchableOpacity>
    );
  };

  render() {
    const {
      txd,
      style,
      required,
      groupTitle,
      secureTextEntry,
      inputContainerStyle,
      contentStyle,
      inputTextStyle,
      createAdditionalButton,
      hideCleanButton,
      hideRequired,
      hidden,
      ...others
    } = this.props;
    if (hidden) {
      return <View />;
    }
    return (
      <View style={[{ alignItems: 'flex-start' }, style]}>
        {Boolean(groupTitle) && this.renderGroupTitle(groupTitle)}
        {this.renderLabel()}
        <View style={[styles.content, contentStyle]}>
          {!hideRequired && (
            <View style={[SharedStyles.rowCenter, { width: scaleSizeW(100) }]}>
              <Text style={{ color: 'red', fontSize: currentThemes.smallFontSize }}>{required && '*'}</Text>
            </View>
          )}
          <View style={[SharedStyles.flexOne, inputContainerStyle]}>
            <View style={[SharedStyles.flexOne, SharedStyles.row]}>
              <TextInput
                {...others}
                {...testProps(`al_${txd}`)}
                testID={txd}
                style={[
                  styles.inputText,
                  SharedStyles.flexOne,
                  hideCleanButton && {
                    paddingRight: 0,
                    paddingLeft: 5,
                  },
                  inputTextStyle,
                ]}
                ref={this.refHandler}
                onChangeText={this.onChangeTextHandler}
                onEndEditing={this.onEndEditingHandler}
                onBlur={this.onBlurHandler}
                onSubmitEditing={this.onSubmitEditingHandler}
                value={this.state.value}
                clearButtonMode='never'
                underlineColorAndroid='transparent'
                onKeyPress={this.onKeyPressHandler}
                secureTextEntry={this.state.secureTextEntry}
                placeholderTextColor={'#cccccc'}
                autoCorrect={false}
                spellCheck={false}
                textContentType='none'
              />
            </View>
            {!hideCleanButton && this.renderClearButton()}
          </View>
          {createAdditionalButton && createAdditionalButton()}
          {secureTextEntry && this.renderEye()}
        </View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'stretch',
    minHeight: scaleSizeH(40),
    borderBottomWidth: 1,
    borderBottomColor: currentThemes.borderBottomColor,
  },
  inputText: {
    marginRight: scaleSizeW(50),
    fontSize: currentThemes.smallFontSize,
    color: '#333333',
    letterSpacing: 1,
  },
  clearButtonContainer: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    justifyContent: 'center',
    right: 0,
    width: scaleSizeW(50),
    backgroundColor: 'transparent',
  },
});
