import React, { PureComponent } from 'react';
import { StyleSheet, Text, TouchableOpacity, TouchableWithoutFeedback, View } from 'react-native';
import { connect } from 'react-redux';
import { currentThemes, scaleSizeH, scaleSizeW, t } from '../../constants';
import { SH_TIER_TAGS } from '../../containers/Customer/ModalCustomerDetail';
import { selectMembershipEnabled, selectViewCustomerInfo } from '../../sagas/selector';
import { localeNumber, maskPhoneNumber, testProps } from '../../utils';
import { isEmpty } from '../../utils/validator';
import { MaterialIcons } from '../ui';
import IconArrowsOut from '../ui/svgIcons/iconArrowsOut';
import IconGoldMember from '../ui/svgIcons/IconGoldMember';
import IconPlatinumMember from '../ui/svgIcons/iconMember';
import IconMinus from '../ui/svgIcons/iconMinus';
import IconNormalMember from '../ui/svgIcons/IconNormalMember';
import IconSilverMember from '../ui/svgIcons/IconSilverMember';

interface Props {
  item: any;
  isInAddCustomer?: boolean;
  onRemoveCustomerClick?(): any;
  onItemclick?(item): any;
  position?: number;
  disabled?: boolean;
  showLoyalty?: boolean;
  editable?: boolean;
  membershipEnabled?: boolean;
  viewCustomerInfo?: boolean;
}

export const CUSTOMER_ITEM_HEIGHT = scaleSizeH(103);

const mapStateToProps = state => ({
  membershipEnabled: selectMembershipEnabled(state),
  viewCustomerInfo: selectViewCustomerInfo(state),
});

class CustomerItem extends PureComponent<Props> {
  onItemclickHandler = () => {
    const { item, onItemclick } = this.props;
    onItemclick && onItemclick(item);
  };

  onRemoveCustomerClick = () => {
    const { onRemoveCustomerClick } = this.props;
    onRemoveCustomerClick && onRemoveCustomerClick();
  };

  render() {
    const {
      isInAddCustomer,
      position,
      disabled = false,
      editable = true,
      item: { loyalty, firstName, lastName, phone, isCreatedWithin24hours, tags },
      membershipEnabled,
      viewCustomerInfo,
    } = this.props;
    const loyaltyAmount = loyalty ? Number(loyalty.toFixed(2)) : 0;
    const loyaltyText = loyaltyAmount ? localeNumber(loyaltyAmount) : '0.00';
    const name = `${Boolean(firstName) ? firstName + ' ' : ''}${lastName || ''}`;
    const specialTags = tags && tags.filter(tag => SH_TIER_TAGS.includes(tag));

    return (
      <TouchableWithoutFeedback
        style={{ flex: 1 }}
        disabled={disabled}
        onPress={this.onItemclickHandler}
        {...testProps(`al_custom_item${position === undefined ? '' : '_' + position}`)}
      >
        <View style={styles.container}>
          {isInAddCustomer ? (
            <MaterialIcons name='face' size={scaleSizeW(60)} color='#E0E0E4' />
          ) : (
            <View style={styles.iconContainer}>
              <IconArrowsOut width={scaleSizeW(48)} height={scaleSizeH(48)} />
            </View>
          )}
          <View style={styles.content}>
            <View style={styles.customerInfo}>
              <View style={styles.namePhoneContainer}>
                <View style={styles.nameContainer}>
                  <Text
                    style={isInAddCustomer ? styles.normalNameText : styles.shortNameText}
                    numberOfLines={isInAddCustomer ? 1 : 2}
                    ellipsizeMode='tail'
                    {...testProps('al_custom_name')}
                  >
                    {isEmpty(name) ? (viewCustomerInfo ? phone : maskPhoneNumber(phone)) : name}
                  </Text>
                  {!isInAddCustomer && specialTags && specialTags.length > 0 && membershipEnabled && (
                    <View>
                      {specialTags.map((tag, index) => {
                        switch (tag) {
                          case 'SH_Tier_1':
                            return (
                              <View style={styles.membershipTier}>
                                <IconNormalMember width={scaleSizeW(140)} height={scaleSizeH(40)} style={styles.membershipIcon} />
                              </View>
                            );
                          case 'SH_Tier_2':
                            return (
                              <View style={styles.membershipTier}>
                                <IconSilverMember width={scaleSizeW(140)} height={scaleSizeH(40)} style={styles.membershipIcon} />
                              </View>
                            );
                          case 'SH_Tier_3':
                            return (
                              <View style={styles.membershipTier}>
                                <IconGoldMember width={scaleSizeW(140)} height={scaleSizeH(40)} style={styles.membershipIcon} />
                              </View>
                            );
                          case 'SH_Tier_4':
                            return (
                              <View style={styles.membershipTier}>
                                <IconPlatinumMember width={scaleSizeW(140)} height={scaleSizeH(40)} style={styles.membershipIcon} />
                              </View>
                            );
                          default:
                            return null;
                        }
                      })}
                    </View>
                  )}
                  {isCreatedWithin24hours && isInAddCustomer && (
                    <View style={styles.newMemberContainer}>
                      <Text style={styles.newMemberText}>{t('New Member')}</Text>
                    </View>
                  )}
                </View>
                <Text style={styles.phoneText}>{!isEmpty(firstName) ? (viewCustomerInfo ? phone : maskPhoneNumber(phone)) : ''}</Text>
              </View>
              {isInAddCustomer ? (
                <Text style={styles.loyatyText}>{loyaltyText}</Text>
              ) : (
                !(disabled || !editable) && (
                  <TouchableOpacity style={styles.removeIconContainer} onPress={this.onRemoveCustomerClick} {...testProps('al_delete_customer')}>
                    <View style={styles.minusContainer}>
                      <IconMinus width={scaleSizeW(45)} height={scaleSizeH(45)} />
                    </View>
                  </TouchableOpacity>
                )
              )}
            </View>
          </View>
        </View>
      </TouchableWithoutFeedback>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    minHeight: CUSTOMER_ITEM_HEIGHT,
    alignItems: 'center',
    flexDirection: 'row',
    paddingLeft: scaleSizeW(24),
  },
  content: {
    flexDirection: 'column',
    marginLeft: scaleSizeW(24),
    flexGrow: 1,
  },
  customerInfo: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: scaleSizeW(30),
  },
  shortNameText: {
    fontSize: currentThemes.fontSize26,
    color: '#60636B',
    fontWeight: '500',
    maxWidth: scaleSizeW(240),
  },
  normalNameText: {
    fontSize: currentThemes.fontSize26,
    color: '#60636B',
    fontWeight: '500',
  },
  phoneText: {
    fontSize: currentThemes.fontSize18,
    color: '#757575',
    fontWeight: '400',
    marginTop: scaleSizeH(10),
  },
  loyatyText: {
    fontSize: currentThemes.fontSize26,
    color: '#757575',
    fontWeight: '500',
  },
  removeIconContainer: {
    paddingHorizontal: scaleSizeW(5),
    paddingVertical: scaleSizeH(5),
  },
  iconContainer: {
    marginTop: scaleSizeH(10),
  },
  minusContainer: {
    // paddingTop: scaleSizeH(25),
    // backgroundColor: 'blue',
  },
  namePhoneContainer: {
    flexDirection: 'column',
    justifyContent: 'space-between',
  },
  nameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  newMemberContainer: {
    backgroundColor: '#FFF2E3',
    borderRadius: scaleSizeW(8),
    paddingHorizontal: scaleSizeW(8),
    paddingVertical: scaleSizeH(3),
    marginLeft: scaleSizeW(15),
  },
  newMemberText: {
    color: '#FF9419',
    fontSize: currentThemes.fontSize16,
    fontWeight: '500',
  },
  membershipTier: {
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: scaleSizeW(15),
  },
  membershipIcon: {
    // marginLeft: scaleSizeW(-24),
    // marginTop: scaleSizeH(5),
  },
});

export default connect(mapStateToProps)(CustomerItem);
