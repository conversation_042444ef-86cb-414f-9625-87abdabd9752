import React, { FC } from 'react';
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { currentThemes, scaleSizeH, scaleSizeW, screenHeight, screenWidth, t } from '../../constants';
import { getCurrencySymbol, localeNumber, testProps } from '../../utils';
import { StorehubLogo } from '../ui';
import { icons } from '../ui/icons';

interface QrPhProps {
  amount?: string;
  uri?: string;
  onApproveManually?: () => void;
}

const whiteContainerWidth = screenWidth * 0.7;
const whiteContainerHeight = screenHeight * 0.6;
const imageQRWidth = Math.min(whiteContainerHeight * 0.9, whiteContainerWidth * 0.9);

const QrPh: FC<QrPhProps> = props => {
  const { amount, uri, onApproveManually } = props;
  return (
    <View style={styles.container}>
      <View style={styles.backgroundContainer}>
        <Image source={icons.backdrop} style={styles.backgroundImage} />
      </View>

      <LinearGradient
        colors={['rgba(255, 255, 255, 0)', 'rgba(255, 255, 255, 0.3)']}
        style={styles.reflection}
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
        locations={[0, 1]}
      />

      <Text style={styles.scanToPayText}>{t('Scan to pay')}</Text>

      <View style={styles.whiteContainer}>
        <View style={styles.amountContainer}>
          <Image source={icons.qrPh} style={styles.qrPhLogo} resizeMode='contain' />
          <Text style={styles.amountText}>{`${getCurrencySymbol()} ${localeNumber(amount)}`}</Text>
        </View>
        <View style={styles.dashedLine} />
        <Image source={{ uri: uri }} style={styles.imageQR} fadeDuration={0} resizeMode='contain' />
      </View>

      <View style={styles.storehubLogoContainer}>
        <StorehubLogo />
      </View>

      <TouchableOpacity
        style={styles.approveManuallyContainer}
        onPress={() => {
          onApproveManually ? onApproveManually() : console.log('approveManually');
        }}
        {...testProps('al_btn_692')}
      >
        <Text style={styles.approveManuallyText}>Approve Manually</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
    alignItems: 'center',
  },
  backdrop: {
    flex: 1,
    padding: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  content: {
    flex: 1,
  },
  backgroundContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: screenHeight * 0.2,
    zIndex: 1,
  },
  backgroundImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  whiteContainer: {
    backgroundColor: 'white',
    flexDirection: 'row',
    width: whiteContainerWidth,
    height: whiteContainerHeight,
    borderRadius: scaleSizeW(48),
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 2,
    position: 'absolute',
    top: screenHeight * 0.15,
    left: screenWidth * 0.15,
    shadowColor: 'rgba(0, 0, 0, 0.5)',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
    paddingHorizontal: scaleSizeW(30),
  },
  imageQR: {
    width: imageQRWidth,
    height: imageQRWidth,
    flex: 1,
  },
  reflection: {
    position: 'absolute',
    width: whiteContainerWidth,
    height: whiteContainerHeight,
    borderRadius: scaleSizeW(48),
    left: screenWidth * 0.15,
    top: screenHeight * 0.15 + whiteContainerHeight,
    transform: [{ scaleY: -1 }],
    zIndex: 1.5,
  },
  scanToPayText: {
    zIndex: 2,
    fontSize: currentThemes.fontSize80,
    fontWeight: 'bold',
    color: 'white',
    marginTop: screenHeight * 0.04,
  },
  amountContainer: {
    flex: 0.9,
    alignItems: 'center',
    justifyContent: 'center',
  },
  qrPhLogo: {
    width: screenWidth * 0.25,
  },
  amountText: {
    fontSize: currentThemes.fontSize52,
    fontWeight: '700',
    marginTop: screenHeight * 0.05,
    color: 'black',
  },
  dashedLine: {
    height: screenHeight * 0.4,
    width: 1,
    borderStyle: 'dashed',
    borderWidth: 1,
    borderColor: '#C9C9C9',
    marginRight: screenWidth * 0.05,
  },
  storehubLogoContainer: {
    position: 'absolute',
    bottom: screenHeight * 0.05,
    left: 0,
    right: 0,
    alignItems: 'center',
    zIndex: 10,
  },
  approveManuallyContainer: {
    position: 'absolute',
    right: screenWidth * 0.02,
    top: screenHeight * 0.03,
    paddingHorizontal: scaleSizeW(50),
    paddingVertical: scaleSizeH(15),
    backgroundColor: '#FFFFFF',
    elevation: 3,
    zIndex: 11,
    alignItems: 'center',
    justifyContent: 'center',
  },
  approveManuallyText: {
    fontSize: currentThemes.fontSize16,
    fontWeight: '500',
    color: 'black',
    fontStyle: 'normal',
  },
});

export default QrPh;
