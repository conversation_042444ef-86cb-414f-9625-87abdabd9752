import React, { useEffect, useState } from 'react';
import { StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';
import { currentThemes, scaleSizeH, scaleSizeW, SharedStyles, t } from '../../constants';
import { EmployeeType, ScreenProps } from '../../typings';
import { ModalContainer } from '../common';
import { getParam } from '../../utils/navigation';
import { useNavigation } from '@react-navigation/native';
import { testProps } from '../../utils';
import { useSelector } from 'react-redux';
import { selectEmployeeId } from '../../sagas/selector';
import DAL from '../../dal';

interface Props extends ScreenProps {
  title?: string;
  okText?: string;
  cancelText?: string;

  onCancelHandler(): void;

  onSubmitHandler(): void;
}

export const ModalApproveManually = (props: Props) => {
  const [notes, setNotes] = useState('');

  const employeeId = useSelector(selectEmployeeId);

  const [employee, setEmployee] = useState<EmployeeType>();
  useEffect(() => {
    setEmployee(DAL.getEmployeeById(employeeId));
  }, [employeeId]);

  const renderContent = () => {
    const needTextInput = getParam(props, 'needTextInput');
    const textInputPlaceholder = getParam(props, 'textInputPlaceholder');
    return (
      <View style={styles.cotent}>
        <Text testID='Info' style={styles.txtInfo}>
          {t('ADD NOTE')}
        </Text>
        {needTextInput && (
          <TextInput
            {...testProps('al_textinput_686')}
            style={styles.textInputStyle}
            multiline={true}
            clearButtonMode='never'
            value={notes}
            placeholder={t(textInputPlaceholder)}
            placeholderTextColor={currentThemes.inActiveButtonBackgroundColor}
            onChangeText={setNotes}
            underlineColorAndroid='transparent'
          />
        )}
        <Text testID='Info' style={styles.txtInfo}>
          {employee && `Approved by ${employee.firstName} ${employee.lastName}`}
        </Text>
      </View>
    );
  };

  const navigation = useNavigation();

  const closeModalHandler = () => {
    navigation.goBack();
  };

  const onSubmitHandler = () => {
    const onSubmitHandler = getParam(props, 'onSubmitHandler', undefined);
    requestAnimationFrame(() => {
      onSubmitHandler && onSubmitHandler(notes);
    });
    closeModalHandler();
  };

  const onCancelHandler = () => {
    const onCancelHandler = getParam(props, 'onCancelHandler', undefined);
    requestAnimationFrame(() => {
      onCancelHandler && onCancelHandler();
    });
    closeModalHandler();
  };

  return (
    <ModalContainer
      noScroll
      title={props.title || t('Approve Payment Manually')}
      onCloseHandler={onCancelHandler}
      contentStyle={styles.layer}
      headerRightButton={
        <TouchableOpacity {...testProps('al_btn_293')} style={SharedStyles.modalContainerHeaderRightButton} onPress={onSubmitHandler}>
          <Text style={styles.addText}>{t('CONFIRM')}</Text>
        </TouchableOpacity>
      }
      mainContentStyle={styles.mainContentStyle}
    >
      {renderContent()}
    </ModalContainer>
  );
};

const styles = StyleSheet.create({
  layer: {
    justifyContent: 'center',
    alignItems: 'center',
    width: scaleSizeW(1430),
    backgroundColor: 'white',
    overflow: 'hidden',
  },
  mainContentStyle: {
    flex: 1,
    width: '100%',
    alignItems: 'center',
  },
  addText: {
    color: '#FFF',
    fontSize: currentThemes.fontSize18,
    fontWeight: '500',
  },
  cotent: {
    flex: 1,
    width: '100%',
    paddingVertical: scaleSizeH(58),
    paddingHorizontal: scaleSizeW(32),
  },
  txtInfo: {
    fontSize: currentThemes.fontSize22,
    color: '#959595',
    fontWeight: '500',
    marginTop: scaleSizeH(8),
    marginBottom: scaleSizeH(8),
  },
  textInputStyle: {
    width: '100%',
    height: scaleSizeH(220),
    fontSize: currentThemes.fontSize22,
    lineHeight: scaleSizeH(32),
    color: '#393939',
    textAlignVertical: 'top',
    paddingHorizontal: scaleSizeW(15),
    paddingVertical: scaleSizeH(12),
    borderColor: '#E0E0E4',
    borderWidth: 1,
  },
});
