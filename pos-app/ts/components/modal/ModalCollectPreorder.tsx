import { chain, get, throttle, zipObject } from 'lodash';
import moment from 'moment';
import React, { PureComponent } from 'react';
import { SectionList, SectionListData, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import sectionListGetItemLayout from 'react-native-section-list-get-item-layout';
import { connect, ConnectedProps } from 'react-redux';
import { bindActionCreators } from 'redux';
import {
  getCustomerByIdMulti,
  getLocalPreOrders,
  restoreLocalPreOrder,
  restoreSearchedPreOrder,
  searchPreOrderByCustomer,
  searchRemoteTransactionsWithType,
  toggleToastInfo,
} from '../../actions';
import { EmptyCustomer, MaterialIcons } from '../../components/ui';
import { currentThemes, scaleSizeH, scaleSizeW, t } from '../../constants';
import { RootState, ScreenProps } from '../../typings';
import { createDate } from '../../utils/datetime';
import { ModalContainer, ModalDropdown } from '../common';
import PreOrderItem from '../common/PreOrderItem';
import { CustomerSearch } from '../customer';
import { testProps } from '../../utils';
import { isIminFalcon1 } from '../../utils/deviceInfo';

interface State {
  dataList: any[];
  keyboardHide: boolean;
  searchText: string;
  searching: boolean;
  customerData: any[];
  sectionlistHeight: number;
  searchByReceiptNumber: boolean;
  isLocalOrders: boolean;
}

const mapStateToProps = (state: RootState) => ({
  businessName: state.getIn<string>(['Storage', 'storeInfo', 'name']),
  storeId: state.getIn<string>(['Storage', 'storeInfo', 'store', '_id']),
});

const mapDispatchToProps = dispatch => ({
  actions: bindActionCreators(
    {
      searchPreOrderByCustomer,
      toggleToastInfo,
      restoreLocalPreOrder,
      restoreSearchedPreOrder,
      searchRemoteTransactionsWithType,
      getCustomerByIdMulti,
      getLocalPreOrders,
    },
    dispatch
  ),
});

const connector = connect(mapStateToProps, mapDispatchToProps);
type PropsFromRedux = ConnectedProps<typeof connector>;
interface Props extends PropsFromRedux, ScreenProps {}

class ModalCollectPreorder extends PureComponent<Props, State> {
  private searchString: string;
  private _dropdown: any;
  private waitingCallBack = {};
  private customerCache = {};
  private localOrders: any[];

  constructor(props) {
    super(props);
    this.searchString = '';
    this.state = {
      keyboardHide: true,
      searchText: '',
      searching: false,
      customerData: [],
      sectionlistHeight: 0,
      dataList: [],
      searchByReceiptNumber: true,
      isLocalOrders: true,
    };
  }

  componentDidMount() {
    requestAnimationFrame(() => {
      this.setState({ keyboardHide: false });
      this.getPreorders();
    });
  }

  render() {
    return (
      <ModalContainer
        noScroll
        title={t('Search Pre-Orders')}
        onCloseHandler={this.closeButtonClicked}
        contentStyle={styles.layer}
        mainContentStyle={styles.mainContentStyle}
      >
        {!this.state.keyboardHide && this.renderSearchItem()}
        {this.renderPreOrderList()}
      </ModalContainer>
    );
  }

  getPreorders = () => {
    const { isLocalOrders } = this.state;
    if (!isLocalOrders || !Boolean(this.localOrders)) {
      this.props.actions.getLocalPreOrders({
        onComplete: result => {
          this.localOrders = this.groupList(result);
          this.setState({ dataList: this.localOrders, isLocalOrders: true });
        },
      });
    }
  };

  groupList = result => {
    return chain(result)
      .groupBy(item => {
        const time = moment(item.createdDate || item.createdTime);
        return time.format('YYYY-MM-DD');
      })
      .toPairs()
      .map(currentItem => {
        return zipObject(['title', 'data'], currentItem);
      })
      .sortBy('title')
      .reverse()
      .value();
  };

  _keyExtractor = (item, index) => item.transactionId;

  renderPreOrderList = () => {
    return (
      <SectionList
        testID='sectionList'
        renderItem={this.renderItem}
        renderSectionHeader={this.renderTransactionHeaderItem}
        sections={this.state.dataList}
        ItemSeparatorComponent={this.renderSeparator}
        keyExtractor={this._keyExtractor}
        ListEmptyComponent={this.renderEmpty}
      />
    );
  };

  renderEmpty = () => {
    return (
      <View style={styles.searchEmpty}>
        <EmptyCustomer />
        <Text style={styles.textEmpty}>{t('No Results Found')}</Text>
      </View>
    );
  };

  renderSeparator = () => {
    return <View style={styles.line} />;
  };

  renderTransactionHeaderItem = ({ section: { title } }: { section: SectionListData<any> }) => {
    let showTitle = title;
    if (title == moment().format('YYYY-MM-DD')) {
      showTitle = 'TODAY';
    } else if (title == createDate(new Date(new Date().getTime() - 24 * 60 * 60 * 1000), 'YYYY-MM-DD')) {
      showTitle = 'YESTERDAY';
    }
    return (
      <View>
        <Text
          style={{
            paddingVertical: scaleSizeH(6),
            paddingLeft: scaleSizeW(24),
            backgroundColor: '#E0E0E0',
            fontSize: currentThemes.fontSize24,
            fontWeight: '500',
            color: '#303030',
          }}
        >
          {showTitle}
        </Text>
      </View>
    );
  };

  renderItem = ({ item }) => {
    return <PreOrderItem key={item.transactionId} item={item} onItemClick={this.onItemClick} getCustomer={this.onGetItemCustomer} />;
  };

  onItemClick = item => {
    const jumpToRegister = get(this.props.route.params, 'jumpToRegister', false);
    if (this.state.isLocalOrders) {
      this.props.actions.restoreLocalPreOrder({ transactionId: item.transactionId });
      this.props.navigation.goBack();
    } else {
      this.props.actions.restoreSearchedPreOrder({ transaction: item });
      this.props.navigation.goBack();
    }
    if (Boolean(jumpToRegister)) {
      this.props.navigation.navigate('Register');
    }
  };

  onGetItemCustomer = (onComplete, item) => {
    const { customerId } = item;
    if (Boolean(this.customerCache[customerId])) {
      onComplete(this.customerCache[customerId]);
    } else if (Boolean(this.waitingCallBack[customerId])) {
      this.waitingCallBack[customerId].push(onComplete);
    } else {
      this.waitingCallBack[customerId] = [onComplete];
      const { businessName } = this.props;
      const onSuccess = {
        callback: payload => {
          const { res } = payload;
          this.customerCache[customerId] = res;
          this.waitingCallBack[customerId].forEach(cb => cb(res));
          delete this.waitingCallBack[customerId];
        },
      };
      this.props.actions.getCustomerByIdMulti({
        bn: businessName,
        customerId,
        onSuccess,
      });
    }
  };

  onLayout = e => {
    const height = e.nativeEvent.layout.height;
    if (this.state.sectionlistHeight < height) {
      this.setState({ sectionlistHeight: height });
    }
  };

  getItemLayout = sectionListGetItemLayout({
    // The height of the row with rowData at the given sectionIndex and rowIndex
    getItemHeight: () => ITEM_HEIGHT,
    // These four properties are optional
    getSeparatorHeight: () => 1, // The height of your separators
    getSectionHeaderHeight: () => HEADER_HEIGHT, // The height of your section headers
  });

  onSelectSearchType = (item, index) => {
    if (Number(index) === 1) {
      this.setState({ searchByReceiptNumber: false });
    } else {
      this.setState({ searchByReceiptNumber: true });
    }
  };

  onDropDownRef = ref => (this._dropdown = ref);

  showDropDown = () => this._dropdown && this._dropdown.show();

  renderSearchItem = () => {
    const options = ['By Receipt No.', 'By Customer'];
    return (
      <View style={styles.searchBarContainer}>
        <ModalDropdown
          ref={this.onDropDownRef}
          defaultIndex={0}
          animated={false}
          defaultValue={options[0]}
          dropdownStyle={{ height: scaleSizeH(120), width: scaleSizeW(200), offset: isIminFalcon1() ? -24 : 0 }}
          options={options}
          showsVerticalScrollIndicator={false}
          scrollEnabled={false}
          textStyle={{ fontSize: currentThemes.fontSize18, color: 'white', textAlignVertical: 'center', marginRight: scaleSizeW(8) }}
          buttonStyle={{ paddingHorizontal: scaleSizeW(24), height: '100%', alignItems: 'center', backgroundColor: 'black' }}
          dropdownTextContainerStyle={{ height: scaleSizeH(58) }}
          dropdownTextStyle={{ fontSize: currentThemes.fontSize18, color: 'black' }}
          dropdownTextHighlightStyle={{ color: 'black' }}
          onSelect={this.onSelectSearchType}
          renderRight={this.renderDropDownIcon}
        />
        <View style={styles.searchInputContainer}>
          <CustomerSearch
            placeholder={'Search'}
            searchHandler={this.searchHandler}
            onChangeTextHandler={this.onChangeTextHandler}
            onClearValueHandler={this.onClearValueHandler}
            clearButton={this.renderClearButton}
          />
        </View>
      </View>
    );
  };

  renderDropDownIcon = () => {
    return <MaterialIcons name='arrow-drop-down' size={scaleSizeW(18)} color='white' />;
  };

  onChangeTextHandler = value => {
    const { isLocalOrders } = this.state;
    const newSearchText = typeof value === 'string' ? value.trim() : value;
    this.setState({ searchText: newSearchText });
    if (!Boolean(newSearchText) && !isLocalOrders) {
      this.getPreorders();
    }
  };

  searchHandler = () => {
    if (this.state.searchByReceiptNumber) {
      const { storeId, businessName } = this.props;
      const onSuccess = {
        callback: payload => {
          if (Boolean(payload.res)) {
            const groupList = this.groupList(payload.res);
            this.setState({ dataList: groupList, isLocalOrders: false });
          } else {
            this.setState({ dataList: [], isLocalOrders: false });
          }
        },
      };
      this.props.actions.searchRemoteTransactionsWithType({
        business: businessName,
        storeId,
        receiptNumber: this.state.searchText,
        type: 'PreOrder',
        onSuccess,
      });
    } else {
      const onSuccess = {
        callback: payload => {
          if (Boolean(payload.res.orders) && Boolean(payload.res.orders.length)) {
            const groupList = this.groupList(payload.res.orders);
            this.setState({ dataList: groupList, isLocalOrders: false });
          } else {
            this.setState({ dataList: [], isLocalOrders: false });
          }
        },
      };
      this.props.actions.searchPreOrderByCustomer({
        business: this.props.businessName,
        searchText: this.state.searchText,
        onSuccess,
      });
    }
  };

  renderClearButton = () => {
    return (
      <View style={{ paddingRight: 8 }}>
        <MaterialIcons name='close' size={scaleSizeW(24)} color={currentThemes.inActiveButtonBackgroundColor} />
      </View>
    );
  };

  onClearValueHandler = () => {
    this.searchString = '';
    this.setState({ searchText: '' });
    this.getPreorders();
  };

  triggerSearch = throttle(
    () => {
      this.setState({ searchText: this.searchString });
    },
    100,
    { leading: true, trailing: true }
  );

  renderTitleBar = () => {
    return (
      <View style={styles.titleStyle}>
        <TouchableOpacity {...testProps('al_btn_371')} onPress={this.closeButtonClicked}>
          <MaterialIcons name='close' size={scaleSizeW(40)} color='#9E9E9E' />
        </TouchableOpacity>
        <Text style={{ fontSize: currentThemes.fontSize24, alignSelf: 'center', color: '#303030' }}>{'Pre-Order Pick Up Selection'}</Text>
        <View></View>
      </View>
    );
  };

  closeButtonClicked = () => {
    this.props.navigation.goBack();
  };
}

export default connector(ModalCollectPreorder);
const ITEM_HEIGHT = scaleSizeH(56); // item的高度
const HEADER_HEIGHT = scaleSizeH(25); // 分组头部的高度

const styles = StyleSheet.create({
  mainContentStyle: {
    flex: 1,
    width: '100%',
  },
  layer: {
    justifyContent: 'center',
    alignItems: 'center',
    width: scaleSizeW(1430),
    backgroundColor: 'white',
    overflow: 'hidden',
  },
  titleStyle: {
    flexDirection: 'row',
    padding: scaleSizeW(24),
    justifyContent: 'space-between',
    width: '100%',
    borderBottomColor: currentThemes.borderBottomColor,
    borderBottomWidth: 1,
    alignItems: 'center',
  },
  searchInputContainer: {
    height: scaleSizeH(36),
    flex: 1,
    marginHorizontal: scaleSizeW(18),
    flexDirection: 'row',
    alignItems: 'center',
  },
  line: {
    height: 1,
    backgroundColor: '#C8C7CC',
    flex: 1,
    marginLeft: 20,
  },
  searchBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomColor: '#FF9419',
    borderBottomWidth: 1,
    marginBottom: 1,
    height: scaleSizeH(60),
  },
  searchEmpty: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: scaleSizeH(40),
  },
  textEmpty: {
    color: '#60636B',
    fontSize: currentThemes.fontSize32,
    marginTop: scaleSizeH(11),
  },
});
