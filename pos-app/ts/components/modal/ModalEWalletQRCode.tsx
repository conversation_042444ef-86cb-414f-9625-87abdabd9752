import * as Immutable from 'immutable';
import { get, startsWith } from 'lodash';
import moment from 'moment';
import React, { PureComponent } from 'react';
import { Image, StyleSheet, Text, View } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import ViewShot from 'react-native-view-shot';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { createSelector } from 'reselect';
import RedWarning from '../../../assets/icons/svg/redWarning.svg';
import {
  cancelEWalletPayment,
  CancelEWalletPaymentType,
  clearEWalletPay,
  eWalletPayBegin,
  EWalletPayBeginType,
  HttpAction,
  inquiryEWalletPayment,
  InquiryEWalletPaymentType,
  setCfdConfigurationBegin,
  setCfdQRCode,
  updateTransactionComment,
  UpdateTransactionCommentType,
} from '../../actions';
import { isQRPhProvider, QRCodeProvider } from '../../config/paymentOption';
import { CommonColors, currentThemes, IsAndroid, NETWORK_ERROR_PREFIX, scaleSizeH, scaleSizeW, t } from '../../constants';
import { isCompatCfd } from '../../containers/External/CfdUtils';
import { selectLocalCountryMap, selectStoreName } from '../../sagas/selector';
import { ScreenProps } from '../../typings';
import { localeNumber, newConvertCurrencyToSymbol } from '../../utils';
import { isIminD1, isIminFalcon1 } from '../../utils/deviceInfo';
import { IminLcdManager } from '../../utils/lcd';
import { getParam } from '../../utils/navigation';
import { ScannerSubscription } from '../../utils/scanner';
import { NavigatorHeader } from '../common';
import QrPh from '../eWallet/qrPh';
import { IconEdit, IconLeft, IconMore, IconQRScan, icons } from '../ui';
import ModalMoreAction from './ModalMoreAction';

interface Props extends ScreenProps {
  total?: number;
  eWallet?: any;
  currency?: string;
  paymentType?: string;
  storeName?: string;
  localCountryMap?: any;
  actions?: {
    inquiryEWalletPayment: HttpAction<InquiryEWalletPaymentType>;
    clearEWalletPay;
    updateTransactionComment(payload: UpdateTransactionCommentType): void;
    eWalletPayBegin(payload: EWalletPayBeginType): void;
    setCfdConfigurationBegin;
    cancelEWalletPayment: HttpAction<CancelEWalletPaymentType>;
    setCfdQRCode;
  };
}

interface State {
  scanCustomerCompleted: boolean;
  count: number;
  manuallyApproved: boolean;
}

const fromImmutableEWallet = createSelector(
  (state: Immutable.Map<string, any>) => state.get('EWallet', Immutable.Map()),
  ewallet => ewallet.toJS()
);

const mapStateToProps = state => ({
  eWallet: fromImmutableEWallet(state),
  currency: state.getIn(['Storage', 'storeInfo', 'store', 'currency']),
  total: state.getIn(['TransactionSession', 'total']),
  storeName: selectStoreName(state),
  localCountryMap: selectLocalCountryMap(state),
});

const mapDispatchToProps = dispatch => ({
  actions: bindActionCreators(
    {
      inquiryEWalletPayment,
      clearEWalletPay,
      updateTransactionComment,
      eWalletPayBegin,
      setCfdConfigurationBegin,
      cancelEWalletPayment,
      setCfdQRCode,
    },
    dispatch
  ),
});

export class ModalEwalletQRCode extends PureComponent<Props, State> {
  timer: ReturnType<typeof setTimeout>;
  countDownTimer: ReturnType<typeof setInterval>;
  _menu: ModalMoreAction;
  private _scannerListener;
  private retryCount: number = 0;
  private readonly MAX_RETRY_COUNT = 5;
  private readonly RETRY_DELAY = 3000;

  constructor(props) {
    super(props);
    this.state = {
      scanCustomerCompleted: false,
      count: 600, // 10 minutes countdown
      manuallyApproved: false,
    };
  }

  private viewShotCapture;

  componentDidMount() {
    const paymentOption = getParam(this.props, 'paymentOption');
    const qrCodeType = get(paymentOption, ['additionalInfo', 'qrCodeType']);
    if (qrCodeType === 'dynamic') {
      this.ewalletPayBegin();
    } else if (qrCodeType === 'customer') {
      // current scan qr design is for compat cfd layout only
      if (isCompatCfd()) {
        const qrCodeProvider = get(paymentOption, ['additionalInfo', 'qrCodeProvider']);
        const qrCodeType = get(paymentOption, ['additionalInfo', 'qrCodeType']);
        const amount = getParam(this.props, 'amount');
        const currency = this.props.eWallet.currency;
        this.props.actions.setCfdQRCode({
          qrData: null,
          qrCodeProvider,
          qrCodeType,
          currency,
          amount,
          showPayAmount: true,
        });
      }
      this._scannerListener = ScannerSubscription.addListener('Scanner_Resp', result => {
        const barcode = result.barcode;
        this.handleScanBarcode(barcode);
      });
    }
    if (IsAndroid) {
      this.viewShotCapture = setInterval(() => {
        // code to be executed every 1 second
        if (this.captrueRef) {
          this.captrueRef?.capture();
        }
      }, 1500);
    }
  }

  componentWillUnmount() {
    this.timer && clearTimeout(this.timer);
    this.stopCountDown();
    this._scannerListener && this._scannerListener.remove();
    this.props.actions.clearEWalletPay();
    this.props.actions.setCfdConfigurationBegin();
    IminLcdManager.clear();
    this.viewShotCapture && clearInterval(this.viewShotCapture);
    this.retryCount = 0;
  }

  handleScanBarcode = barcode => {
    if (!Boolean(barcode)) {
      return;
    }
    this.setState({ scanCustomerCompleted: true });
    this.ewalletPayBegin(barcode);
  };

  ewalletPayBegin = (tokenId?) => {
    const onComplete = {
      callback: payload => {
        const { result, err } = payload;
        if (!result) {
          const paymentOption = getParam(this.props, 'paymentOption');
          const isQRPh = isQRPhProvider(paymentOption);
          if (isQRPh) {
            this.props.navigation.navigate('ModalInfo', {
              title: t('Failed Generating QR'),
              isShowTitle: true,
              info: `QR Code cannot be generated. Check the POS connection and generate another QR code.\n\nError: ${err}`,
              titleIcon: <RedWarning width={scaleSizeW(40)} height={scaleSizeW(40)} color={'#303030'} />,
              okText: t('Retry'),
              cancelText: t('Change payment method'),
              onSubmitHandler: () => {
                // retry
                this.ewalletPayBegin(tokenId);
              },
              onCancelHandler: () => {
                this.props.navigation.goBack();
              },
            });
          } else {
            const onSubmitHandler = () => {
              this.props.navigation.goBack();
            };
            if (startsWith(err, NETWORK_ERROR_PREFIX)) {
              this.props.navigation.navigate('ModalInfo', {
                title: t('No Internet Connection'),
                isShowTitle: true,
                info: t('Please check your POS internet connection and try again'),
                titleIcon: <RedWarning width={scaleSizeW(40)} height={scaleSizeW(40)} color={'#303030'} />,
                okText: t('OK'),
              });
            } else {
              this.props.navigation.navigate('ModalInfo', { info: err, onSubmitHandler });
            }
          }
        } else {
          const qrCodeProvider = get(paymentOption, ['additionalInfo', 'qrCodeProvider']);
          if (qrCodeProvider === 'KBank') {
            this.startCountDown();
          }
          this.queryEWalletPay();
        }
      },
    };
    const paymentOption = getParam(this.props, 'paymentOption');
    const amount = getParam(this.props, 'amount');
    if (Boolean(amount) && Boolean(paymentOption)) {
      const paymentType = getParam(this.props, 'paymentType');
      this.props.actions.eWalletPayBegin({ paymentOption, amount, onComplete, tokenId, paymentType });
    }
  };

  startCountDown = () => {
    this.countDownTimer = setInterval(() => {
      const { count } = this.state;
      this.setState({ count: count - 1 });
    }, 1000);
  };

  stopCountDown = () => {
    this.countDownTimer && clearInterval(this.countDownTimer);
  };

  queryEWalletPay = () => {
    if (this.state.manuallyApproved) {
      return;
    }

    const paymentOption = getParam(this.props, 'paymentOption');
    const qrCodeType = get(paymentOption, ['additionalInfo', 'qrCodeType']);
    const paySuccess = getParam(this.props, 'paySuccess');
    const paymentType = getParam(this.props, 'paymentType');
    const paymentMethodId = get(paymentOption, 'paymentId');
    const payFail = getParam(this.props, 'payFail');
    const eWallet = this.props.eWallet;
    if (Boolean(eWallet.qrData) || qrCodeType === 'customer') {
      const eWallet = this.props.eWallet;
      const onSuccess = {
        callback: payload => {
          if (this.state.manuallyApproved) {
            return;
          }

          this.retryCount = 0;

          const status = get(payload, ['res', 'data', 'status'], 'Unknown');
          if (status === 'Success') {
            const paymentId = get(payload, ['res', 'data', 'paymentId']);
            const amount = getParam(this.props, 'amount');
            paySuccess && paySuccess(paymentMethodId, amount, paymentType, eWallet.mPOSTxnId || paymentId);
            this.timer && clearTimeout(this.timer);
            this.stopCountDown();
          } else if (status === 'Cancel') {
            this.props.navigation.navigate('ModalInfo', {
              info: 'This QR Code is cancelled or expired',
            });
          } else {
            if (qrCodeType === 'dynamic') {
              this.timer = setTimeout(this.queryEWalletPay, 3000);
            } else if (qrCodeType === 'customer') {
              this.timer = setTimeout(this.queryEWalletPay, 1000);
            }
          }
        },
      };
      const onFailure = {
        callback: failurePayload => {
          if (this.state.manuallyApproved) {
            return;
          }

          const message = failurePayload?.message || '';
          if (startsWith(message, NETWORK_ERROR_PREFIX)) {
            this.retryCount = this.retryCount + 1;
            if (this.retryCount <= this.MAX_RETRY_COUNT) {
              console.log(`Polling request failed, retrying ${this.retryCount}th time, waiting ${this.RETRY_DELAY}ms`);
              this.timer = setTimeout(this.queryEWalletPay, this.RETRY_DELAY);
            } else {
              this.timer && clearTimeout(this.timer);
              this.stopCountDown();
              this.props.navigation.navigate('ModalInfo', {
                title: t('No Internet Connection'),
                isShowTitle: true,
                info: t('Please check your POS internet connection and try again'),
                titleIcon: <RedWarning width={scaleSizeW(40)} height={scaleSizeW(40)} color={'#303030'} />,
                okText: t('OK'),
              });
              payFail && payFail();
              this.retryCount = 0;
            }
          } else {
            console.log('not network connection error, retry again');
            this.timer = setTimeout(this.queryEWalletPay, this.RETRY_DELAY);
          }
        },
      };
      this.props.actions.inquiryEWalletPayment({
        additionalInfo: eWallet.additionalInfo,
        receiptNumber: eWallet.receiptNumber,
        currency: eWallet.currency,
        onSuccess,
        onFailure,
        paymentId: eWallet.mPOSTxnId,
      });
    }
  };

  goBack = () => {
    const paymentOption = getParam(this.props, 'paymentOption');
    const qrCodeProvider = get(paymentOption, ['additionalInfo', 'qrCodeProvider']);
    if (qrCodeProvider === 'KBank') {
      const eWallet = this.props.eWallet;
      const onSuccess = {
        callback: payload => {
          this.timer && clearTimeout(this.timer);
          this.stopCountDown();
          this.props.navigation.goBack();
        },
      };
      const onFailure = {
        callback: () => {
          this.timer && clearTimeout(this.timer);
          this.stopCountDown();
          this.props.navigation.goBack();
        },
      };
      this.props.actions.cancelEWalletPayment({
        additionalInfo: eWallet.additionalInfo,
        receiptNumber: eWallet.receiptNumber,
        currency: eWallet.currency,
        onSuccess,
        onFailure,
        paymentId: eWallet.mPOSTxnId,
      });
    } else if (qrCodeProvider === 'Modulus Labs - QR Ph') {
      this.timer && clearTimeout(this.timer);
      this.stopCountDown();
      this.props.actions.clearEWalletPay();
      this.props.navigation.goBack();
    } else {
      this.props.navigation.goBack();
    }
  };

  closeModalHandler = () => {
    const onDismissHandler = getParam(this.props, 'onDismissHandler', undefined);
    onDismissHandler && onDismissHandler({ ok: false });
    this.props.navigation.goBack();
  };

  setMenuRef = ref => {
    this._menu = ref;
  };

  hideMenu = () => {
    this._menu && this._menu.dismiss();
  };

  showMenu = () => {
    this._menu && this._menu.show();
  };

  onPressApprove = () => {
    const { eWallet } = this.props;
    const tenderedAmount = getParam(this.props, 'amount');
    this.hideMenu();
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    const onCancelHandler = () => {};
    const onSubmitHandler = notes => {
      this.setState({ manuallyApproved: true });
      this.timer && clearTimeout(this.timer);
      this.stopCountDown();
      this.props.navigation.goBack();
      const paySuccess = getParam(this.props, 'paySuccess');
      const paymentOption = getParam(this.props, 'paymentOption');
      const paymentType = getParam(this.props, 'paymentType');
      const paymentId = get(paymentOption, 'paymentId');
      paySuccess &&
        paySuccess(paymentId, tenderedAmount, paymentType, {
          mPOSTransactionId: eWallet.receiptNumber,
          approveReason: notes || '',
        });
    };

    const paymentOption = getParam(this.props, 'paymentOption');
    const isQRPh = isQRPhProvider(paymentOption);
    if (isQRPh) {
      this.props.navigation.navigate('ModalForceApproveManually', {
        onSubmitHandler,
        onCancelHandler,
      });
    } else {
      this.props.navigation.navigate('ModalInfoManually', {
        onSubmitHandler,
        onCancelHandler,
        needTextInput: true,
        textInputPlaceholder: t('Add Note'),
      });
    }
  };

  renderHeader = () => {
    const headerLeftIcons = [
      {
        icon: <IconLeft width={scaleSizeW(48)} height={scaleSizeH(49)} color={'#303030'} />,
        onClick: this.goBack,
      },
    ];
    const headerRightIcons = [{ icon: <IconMore color={CommonColors.Icon} />, onClick: this.showMenu }];
    return <NavigatorHeader title={t('Scan QR Code')} leftIcons={headerLeftIcons} rightIcons={headerRightIcons} />;
  };

  private captrueRef;

  sendImage = uri => {
    if (IsAndroid && (isIminFalcon1() || isIminD1())) {
      const routes = this.props.navigation.getState().routes;
      if (routes[routes.length - 1].name == 'ModalEWalletQRCode') {
        console.log('lcd captured Checkout.tsx ' + Date.now());
        IminLcdManager.sendImage(uri);
      }
    }
  };

  renderViewShot = () => {
    if (!(IsAndroid && (isIminFalcon1() || isIminD1()))) {
      return null;
    }

    const amount = getParam(this.props, 'amount');
    const { currency, localCountryMap } = this.props;
    const currencySymbol = newConvertCurrencyToSymbol(localCountryMap, currency);
    const paymentOption = getParam(this.props, 'paymentOption');
    const qrCodeProvider = get(paymentOption, ['additionalInfo', 'qrCodeProvider']);
    const qrCodeType = get(paymentOption, ['additionalInfo', 'qrCodeType']);

    const eWallet = this.props.eWallet;
    const { qrData } = eWallet;
    const uri = 'data:image/png;base64,' + qrData;

    const colorMap = {
      MayBank: '#E14B7F', // DuitNow
      TnGPay: '#005BA9',
      Boost: '#EE2E24',
      GrabPay: '#00B14F',
      kiplePay: '#1B9AD7', // scan
      'Razer-RazerPay': '#00B14F',
      'Razer-Alipay': '#00A0E9',
      'Razer-WeChatPay': '#28AA42',
      'Razer-TnGPay': '#005BA9', // scan
      'Razer-Boost': '#EE2E24',
      'Razer-GrabPay': '#00B14F',
      'Razer-MaybankQR': '#FFDD00', // DuitNow
    };

    return (
      <ViewShot
        ref={ref => {
          this.captrueRef = ref;
        }}
        onCapture={this.sendImage}
        style={{ width: 320, height: 245, backgroundColor: '#ffffff', position: 'absolute', top: -300 }}
        options={{ width: 320, height: 240, fileName: 'lcd_capture' }}
      >
        <View>
          <View style={{ height: 56, backgroundColor: '#555555' }}>
            <Text
              style={{
                fontWeight: 'bold',
                fontSize: 26,
                textAlign: 'left',
                color: '#ffffff',
                padding: 10,
              }}
            >{`Total: ${`${currencySymbol} ${localeNumber(amount)}`}`}</Text>
          </View>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              alignContent: 'center',
              width: 320,
              height: 184,
              backgroundColor: '#ffffff',
              borderWidth: 10,
              borderColor: colorMap[qrCodeProvider] ?? '#3f3f3f',
            }}
          >
            <View style={{ padding: 8 }}>
              {qrCodeType === 'dynamic' && qrData && <Image source={{ uri }} style={{ width: 148, height: 148 }} fadeDuration={0} />}
              {qrCodeType === 'customer' && (
                <Text
                  style={{
                    width: 148,
                    textAlign: 'center',
                    alignItems: 'center',
                    fontWeight: 'bold',
                    fontSize: 36,
                  }}
                >
                  {'SHOW \nQR \nTO PAY'}
                </Text>
              )}
            </View>
            {qrCodeProvider === 'MayBank' && (
              <Image
                source={icons.qrDuitPay}
                style={{ width: 120, height: 120, position: 'absolute', right: 20 }}
                width={120}
                height={120}
                resizeMode='center'
                fadeDuration={0}
              />
            )}
            {qrCodeProvider === 'Razer-MaybankQR' && (
              <Image
                source={icons.qrMayBankQr}
                style={{ width: 120, height: 120, position: 'absolute', right: 20 }}
                width={120}
                height={120}
                resizeMode='center'
                fadeDuration={0}
              />
            )}
            {(qrCodeProvider === 'Boost' || qrCodeProvider === 'Razer-Boost') && (
              <Image
                source={icons.qrBoost}
                style={{ width: 120, height: 120, position: 'absolute', right: 20 }}
                width={120}
                height={120}
                resizeMode='center'
                fadeDuration={0}
              />
            )}
            {(qrCodeProvider === 'TnGPay' || qrCodeProvider === 'Razer-TnGPay') && (
              <Image
                source={icons.qrTngPay}
                style={{ width: 120, height: 120, position: 'absolute', right: 20 }}
                width={120}
                height={120}
                resizeMode='center'
                fadeDuration={0}
              />
            )}
            {(qrCodeProvider === 'GrabPay' || qrCodeProvider === 'Razer-GrabPay') && (
              <Image
                source={icons.qrGrabPay}
                style={{ width: 120, height: 120, position: 'absolute', right: 20 }}
                width={120}
                height={120}
                resizeMode='center'
                fadeDuration={0}
              />
            )}
            {qrCodeProvider === 'Razer-Alipay' && (
              <Image
                source={icons.qrAlipay}
                style={{ width: 120, height: 120, position: 'absolute', right: 20 }}
                width={120}
                height={120}
                resizeMode='center'
                fadeDuration={0}
              />
            )}
            {qrCodeProvider === 'Razer-WeChatPay' && (
              <Image
                source={icons.qrWechatPay}
                style={{ width: 120, height: 120, position: 'absolute', right: 20 }}
                width={120}
                height={120}
                resizeMode='center'
                fadeDuration={0}
              />
            )}
            {qrCodeProvider === QRCodeProvider.Modulus_QRPH && (
              <Image
                source={icons.xenditQRPHIcon}
                style={{ width: 120, height: 120, position: 'absolute', right: 20 }}
                width={120}
                height={120}
                resizeMode='center'
                fadeDuration={0}
              />
            )}
          </View>
        </View>
      </ViewShot>
    );
    // }
    // return <View/>
  };

  renderContent = () => {
    const amount = getParam(this.props, 'amount');
    const { storeName, currency, localCountryMap } = this.props;
    const currencySymbol = newConvertCurrencyToSymbol(localCountryMap, currency);
    const paymentOption = getParam(this.props, 'paymentOption');
    const qrCodeType = get(paymentOption, ['additionalInfo', 'qrCodeType']);
    const qrCodeProvider = get(paymentOption, ['additionalInfo', 'qrCodeProvider'], '');
    const isQRPh = isQRPhProvider(paymentOption);
    if (qrCodeType === 'dynamic' && qrCodeProvider != 'KBank') {
      const eWallet = this.props.eWallet;
      const { qrData } = eWallet;
      const uri = 'data:image/png;base64,' + qrData;
      if (isQRPh) {
        return <QrPh amount={amount} uri={uri} onApproveManually={this.onPressApprove} />;
      } else {
        return (
          <View style={styles.qrContainer}>
            <Text style={styles.amountText}>{`${currencySymbol} ${localeNumber(amount)}`}</Text>
            {qrData && <Image source={{ uri }} style={styles.imageQR} fadeDuration={0} />}
          </View>
        );
      }
    } else if (qrCodeType === 'customer') {
      return (
        <View style={[styles.qrContainer, styles.qrScanContainer]}>
          <IconQRScan />
          <Text style={styles.qrScanText}>{this.state.scanCustomerCompleted ? t('Waiting for response') : t('Please scan QR Code')}</Text>
        </View>
      );
    } else if (qrCodeProvider === 'KBank') {
      const eWallet = this.props.eWallet;
      const { qrData } = eWallet;
      const { count } = this.state;
      const time = moment.duration(count, 'seconds');
      if (count === 0) {
        this.stopCountDown();
      }
      const mins = time.minutes();
      const seconds = time.seconds();
      const countDown = moment({ m: mins, s: seconds }).format('mm:ss');
      const uri = 'data:image/png;base64,' + qrData;
      const accountName = get(eWallet, 'accountName', '');

      return (
        <View style={styles.qrContainer}>
          {qrData && <Image source={{ uri }} style={styles.imageQR} fadeDuration={0} />}
          <View style={{ width: 570 }}>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <Text style={styles.amountText}>{storeName}</Text>
              <Text style={styles.amountText}>{`${localeNumber(amount)}`}</Text>
            </View>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <Text style={styles.accountNameText}>{accountName}</Text>
              <Text style={styles.accountNameText}>บาท(BAHT)</Text>
            </View>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <Text style={styles.counstDownText}>{`Receipt no:${eWallet.receiptNumber}`}</Text>
              <Text style={styles.counstDownText}>{countDown}</Text>
            </View>
          </View>
        </View>
      );
    } else {
      return <></>;
    }
  };

  render() {
    const moreActionList = [
      {
        name: t('Approve Manually'),
        icon: <IconEdit width={scaleSizeW(40)} height={scaleSizeH(40)} color={'#303030'} />,
        onClick: this.onPressApprove,
      },
    ];
    return (
      <View style={styles.container}>
        {this.renderHeader()}
        <ModalMoreAction ref={refs => (this._menu = refs)} list={[...moreActionList]} />
        {this.renderContent()}
        {IsAndroid && this.renderViewShot()}
      </View>
    );
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(ModalEwalletQRCode);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F3',
  },
  qrContainer: {
    flex: 1,
    alignItems: 'center',
  },
  qrScanContainer: {
    paddingTop: scaleSizeH(182),
  },
  amountText: {
    color: '#303030',
    fontSize: currentThemes.fontSize72,
    fontWeight: 'bold',
    marginTop: scaleSizeH(63),
  },
  accountNameText: {
    color: '#303030',
    fontSize: currentThemes.fontSize30,
    marginTop: scaleSizeH(5),
  },
  counstDownText: {
    fontSize: currentThemes.fontSize46,
    marginTop: scaleSizeH(20),
  },
  qrScanText: {
    marginTop: scaleSizeH(10),
    color: '#60636B',
    fontSize: currentThemes.fontSize32,
    fontWeight: '500',
  },
  imageQR: {
    width: scaleSizeH(570),
    height: scaleSizeH(570),
  },
});
