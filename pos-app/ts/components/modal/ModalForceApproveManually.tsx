import { useNavigation } from '@react-navigation/native';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import { StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';
import { useSelector } from 'react-redux';
import { currentThemes, scaleSizeH, scaleSizeW, SharedStyles, t } from '../../constants';
import DAL from '../../dal';
import { selectEmployeeId } from '../../sagas/selector';
import { EmployeeType, ScreenProps } from '../../typings';
import { testProps } from '../../utils';
import { getParam } from '../../utils/navigation';
import { ModalContainer } from '../common';

interface Props extends ScreenProps {
  title?: string;
  okText?: string;
  cancelText?: string;

  onCancelHandler(): void;

  onSubmitHandler(): void;
}

export const ModalForceApproveManually = (props: Props) => {
  const [notes, setNotes] = React.useState('');
  const isNotesEmpty = _.isEmpty(notes);

  const employeeId = useSelector(selectEmployeeId);

  const [employee, setEmployee] = useState<EmployeeType>();
  useEffect(() => {
    setEmployee(DAL.getEmployeeById(employeeId));
  }, [employeeId]);

  const renderContent = () => {
    return (
      <View style={styles.content}>
        <View style={styles.yellowBox}>
          <Text style={styles.yellowBoxText}>{t('You should only do this if the payment has succeeded')}</Text>
        </View>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text testID='Info' style={styles.txtInfo}>
            {t('ADD NOTE')}
          </Text>
          <Text style={[styles.txtInfo, { color: 'red' }]}> *</Text>
        </View>
        <TextInput
          {...testProps('al_textinput_686')}
          style={styles.textInputStyle}
          multiline={true}
          clearButtonMode='never'
          value={notes}
          placeholder={t('Provide any transaction or payment ID to assist in verifying your payment records')}
          placeholderTextColor={currentThemes.inActiveButtonBackgroundColor}
          onChangeText={setNotes}
          underlineColorAndroid='transparent'
        />
        <Text testID='Info' style={[styles.txtInfo, { marginTop: scaleSizeH(24) }]}>
          {employee && `${employee.firstName} ${employee.lastName}`}
        </Text>
      </View>
    );
  };

  const navigation = useNavigation();

  const closeModalHandler = () => {
    navigation.goBack();
  };

  const onSubmitHandler = () => {
    if (isNotesEmpty) return;

    const onSubmitHandler = getParam(props, 'onSubmitHandler', undefined);
    requestAnimationFrame(() => {
      onSubmitHandler && onSubmitHandler(notes);
    });
    closeModalHandler();
  };

  const onCancelHandler = () => {
    const onCancelHandler = getParam(props, 'onCancelHandler', undefined);
    requestAnimationFrame(() => {
      onCancelHandler && onCancelHandler();
    });
    closeModalHandler();
  };

  return (
    <ModalContainer
      noScroll
      title={props.title || t('Approve manually')}
      onCloseHandler={onCancelHandler}
      contentStyle={styles.layer}
      headerRightButton={
        <TouchableOpacity
          {...testProps('al_btn_293')}
          style={[SharedStyles.modalContainerHeaderRightButton, isNotesEmpty && styles.disabledButton]}
          onPress={onSubmitHandler}
          disabled={isNotesEmpty}
        >
          <Text style={[styles.addText, isNotesEmpty && styles.disabledText]}>{t('CONFIRM')}</Text>
        </TouchableOpacity>
      }
      mainContentStyle={styles.mainContentStyle}
    >
      {renderContent()}
    </ModalContainer>
  );
};

const styles = StyleSheet.create({
  layer: {
    justifyContent: 'center',
    alignItems: 'center',
    width: scaleSizeW(1430),
    backgroundColor: 'white',
    overflow: 'hidden',
  },
  mainContentStyle: {
    flex: 1,
    width: '100%',
    alignItems: 'center',
  },
  addText: {
    color: '#FFF',
    fontSize: currentThemes.fontSize18,
    fontWeight: '500',
  },
  disabledButton: {
    backgroundColor: '#D8D8D8',
  },
  disabledText: {
    color: '#FFFFFF',
  },
  content: {
    flex: 1,
    width: '100%',
    paddingVertical: scaleSizeH(58),
    paddingHorizontal: scaleSizeW(32),
  },
  txtInfo: {
    fontSize: currentThemes.fontSize22,
    color: '#959595',
    fontWeight: '500',
    marginTop: scaleSizeH(8),
    marginBottom: scaleSizeH(8),
  },
  textInputStyle: {
    width: '100%',
    height: scaleSizeH(220),
    fontSize: currentThemes.fontSize22,
    lineHeight: scaleSizeH(32),
    color: '#393939',
    textAlignVertical: 'top',
    paddingHorizontal: scaleSizeW(15),
    paddingVertical: scaleSizeH(12),
    borderColor: '#E0E0E4',
    borderWidth: 1,
  },
  yellowBox: {
    width: '100%',
    height: scaleSizeH(53),
    backgroundColor: '#FEE8CB',
    borderRadius: scaleSizeW(4),
    marginBottom: scaleSizeH(16),
    justifyContent: 'center',
    alignItems: 'center',
  },
  yellowBoxText: {
    fontSize: currentThemes.fontSize18,
    color: '#FC7118',
    fontWeight: '400',
    fontStyle: 'normal',
  },
});
