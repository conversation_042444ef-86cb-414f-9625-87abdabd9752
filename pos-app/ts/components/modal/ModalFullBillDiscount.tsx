import * as Immutable from 'immutable';
import { find, findIndex, get, throttle } from 'lodash';
import React, { PureComponent } from 'react';
import { StyleSheet, Text, TextInput, TouchableOpacity, TouchableWithoutFeedback, View } from 'react-native';
import { connect, ConnectedProps } from 'react-redux';

import { bindActionCreators } from 'redux';
import { createSelector } from 'reselect';
import {
  disableLoyaltyDiscounts,
  giveFullBillDiscount,
  requestAuthorizedAction,
  toggleServiceCharge,
  TransactionTypeWithDisplay,
  updateLoyaltyDiscounts,
  updateOrderLevelNotes,
} from '../../actions';
import {
  AuthorizationType,
  CART_WIDTH,
  CashierActions,
  currentThemes,
  IsAndroid,
  LoyaltyType,
  SalesChannelType,
  t,
  TransactionFlowType,
} from '../../constants';
import { scaleSizeH, scaleSizeW, setSpText } from '../../constants/themes';
import {
  selectBirAccredited,
  selectCountry,
  selectCurrency,
  selectEmployeeId,
  selectEnableCashback,
  selectEnableLoyalty,
  selectEnableServiceCharge,
  selectLocalCountryMap,
  selectMakeStoreCreditAsPayment,
} from '../../sagas/selector';
import { LeafState, RootState, ScreenProps } from '../../typings';
import {
  checkNumberInput,
  checkPositiveIntInput,
  checkPriceNumberInput,
  convertToNumber,
  isValidNumber,
  localeNumber,
  newConvertCurrencyToSymbol,
  testProps,
} from '../../utils';
import { getParam } from '../../utils/navigation';
import { isBirDiscountAvailable, isPreOrderPickUp } from '../../utils/transaction';
import { CommonNumberPercentSwitch, CommonToggleSwitch } from '../common';
import { DiscountType } from '../common/CommonNumberPercentSwitch';
import { MemoizedPercentPresets } from '../register/DiscountPercentPresets';
import { CurrencyTextInput, PosTextInput } from '../textInput';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { hasProductAmusementTax } from '../../sagas/transaction/common';

const fromImmutableTransactionSession = createSelector<RootState, LeafState, TransactionTypeWithDisplay>(
  (state: Immutable.Map<string, any>) => state.get('TransactionSession', Immutable.Map()),
  transactionSession => transactionSession.toJS()
);

interface Props extends ScreenProps, PropsFromRedux {
  inputValue: number;
  type: string; // amount  || percent
  onClose(): void;
  country: string;
}

interface State {
  discountValue: number;
  useLoyalty: boolean;
  storeCredit?: number;
  needCheckCashierAccess: boolean;
  discountType: boolean; // true is amount, false is percent
}

const mapStateToProps = (state: RootState) => ({
  country: selectCountry(state),
  currency: selectCurrency(state),
  transactionSession: fromImmutableTransactionSession(state),
  enableCashback: selectEnableCashback(state),
  enableLoyalty: selectEnableLoyalty(state),
  enableServiceCharge: selectEnableServiceCharge(state),
  birAccredited: selectBirAccredited(state),
  currentEmployeeId: selectEmployeeId(state),
  localCountryMap: selectLocalCountryMap(state),
  makeStoreCreditAsPayment: selectMakeStoreCreditAsPayment(state),
});

const mapDispatchToProps = dispatch => ({
  actions: bindActionCreators(
    {
      giveFullBillDiscount,
      toggleServiceCharge,
      updateLoyaltyDiscounts,
      disableLoyaltyDiscounts,
      requestAuthorizedAction,
      updateOrderLevelNotes,
    },
    dispatch
  ),
});

const connector = connect(mapStateToProps, mapDispatchToProps);
type PropsFromRedux = ConnectedProps<typeof connector>;

class ModalFullBillDiscount extends PureComponent<Props, State> {
  private _isDiscountEnable?: boolean;
  private _enableServiceCharge: boolean;
  private _useStoreCredit: boolean;
  private _useCashback: boolean;
  private discountInputRef: TextInput;
  private _comment: string;

  constructor(props: Props) {
    super(props);
    const type = getParam(this.props, 'type', undefined);
    const inputValue = getParam(this.props, 'inputValue', undefined);
    const discountType = type === 'amount' || type === undefined;
    const discountValue = convertToNumber(inputValue);
    const { transactionSession, enableServiceCharge } = props;
    this._enableServiceCharge = getParam(this.props, 'enabledServiceCharge', true) && enableServiceCharge;
    this._isDiscountEnable = getParam(this.props, 'isDiscountEnable') && transactionSession.isFullBillDiscountCanEnable;
    const { enableLoyalty, enableCashback } = props;
    const { loyaltyDiscounts, customer, isLoyaltyEnable = true, isFullBillDiscountCanEnable = true } = transactionSession;
    const isLoyaltyDiscountsUseful = Boolean(loyaltyDiscounts) && loyaltyDiscounts.length > 0;
    this._useCashback = enableLoyalty && isLoyaltyEnable && isFullBillDiscountCanEnable;
    this._useStoreCredit = enableLoyalty && !enableCashback && isLoyaltyEnable && isFullBillDiscountCanEnable;
    let storeCredit = get(customer, 'loyalty', 0);
    if (this._useCashback && isLoyaltyDiscountsUseful) {
      const loyaltyDiscount = loyaltyDiscounts[0];
      storeCredit = get(loyaltyDiscount, 'inputValue', 0);
    }
    this._comment = get(transactionSession, 'comment', '');
    this.state = {
      discountValue: discountValue,
      useLoyalty: this._useCashback,
      storeCredit: Number(Number.parseFloat(String(storeCredit)).toFixed(2)),
      needCheckCashierAccess: true,
      discountType,
    };
  }

  onValueChagedHandler = value => {
    this._enableServiceCharge = value;
    this.toggleServiceCharge();
  };

  toggleServiceCharge = throttle(
    () => {
      const {
        transactionSession,
        actions: { toggleServiceCharge },
      } = this.props;
      const { items, serviceChargeRate, serviceChargeTaxId } = transactionSession;
      const searchedIndex = findIndex(items, item => item.itemType === 'ServiceCharge');

      toggleServiceCharge({
        itemIndex: searchedIndex,
        serviceChargeRate: this._enableServiceCharge ? serviceChargeRate : 0,
        serviceChargeTax: serviceChargeTaxId,
      });
    },
    300,
    { leading: true, trailing: true }
  );

  closeModalHandler = () => {
    const onClose = getParam(this.props, 'onClose', undefined);
    onClose && onClose();
    this.props.navigation.goBack();
  };

  checkDiscountValue = (discountType, discountValue) => {
    const { transactionSession } = this.props;
    const { maximumDiscountInputValue } = transactionSession;
    const _discountValue = Number(discountValue);

    if (_discountValue > maximumDiscountInputValue && discountType) {
      return { discountValue: String(maximumDiscountInputValue) };
    } else if (_discountValue > 100 && !discountType) {
      return { discountValue: 100 };
    } else {
      return { discountValue };
    }
  };

  onDiscountValueChangeHandler = preDiscountValue => {
    const { discountType } = this.state;
    const { discountValue } = this.checkDiscountValue(discountType, preDiscountValue);

    this.setState(
      {
        discountValue,
      },
      () => {
        this.updateFillBillDiscount();
      }
    );
  };

  onPercentPresetsClick = discountValue => {
    const onSuccess = {
      callback: () => {
        this.setState(
          {
            discountValue,
            discountType: DiscountType.percent,
          },
          () => {
            this.updateFillBillDiscount();
          }
        );
      },
    };
    this.requestDiscountAccess(onSuccess);
  };

  getDiscountItem = () => {
    const { transactionSession } = this.props;
    const items = get(transactionSession, 'items', []);
    return find(items, item => item.itemType === 'Discount');
  };

  onDiscountTypeChangeHandler = discountType => {
    const equivalentValue = get(this.getDiscountItem(), 'equivalentValue', 0);
    const _discountValue = convertToNumber(equivalentValue);
    const { discountValue } = this.checkDiscountValue(discountType, _discountValue);
    this.setState(
      {
        discountValue,
        discountType,
      },
      () => {
        this.updateFillBillDiscount();
      }
    );
  };

  requestDiscountAccess = onSuccess => {
    const { needCheckCashierAccess } = this.state;
    const { transactionSession } = this.props;
    const isFullBillDiscountCanEnable = get(transactionSession, 'isFullBillDiscountCanEnable', true);
    if (isFullBillDiscountCanEnable === false) {
      this.onFullBillDiscountDisablePressHandler();
      return;
    }
    if (needCheckCashierAccess) {
      const { currentEmployeeId } = this.props;
      this.props.actions.requestAuthorizedAction({
        name: CashierActions.Discount,
        employeeId: currentEmployeeId,
        type: AuthorizationType.Cashier,
        onSuccess,
      });
    } else {
      onSuccess.callback();
    }
  };

  updateFillBillDiscount = throttle(
    () => {
      const { discountValue, discountType } = this.state;
      const {
        transactionSession,
        actions: { giveFullBillDiscount },
      } = this.props;
      const { items } = transactionSession;
      const searchedIndex = findIndex(items, item => item.itemType === 'Discount');
      giveFullBillDiscount({
        itemIndex: searchedIndex,
        inputValue: this._isDiscountEnable ? convertToNumber(discountValue) : 0,
        type: discountType ? 'amount' : 'percent',
        displayFullBillDiscountValue: discountValue,
        isDiscountEnable: this._isDiscountEnable,
      });
    },
    300,
    { leading: true, trailing: true }
  );

  toogleStoreCredit = value => {
    this._useCashback = value;
    this.setState({ useLoyalty: value }, () => {
      if (value) {
        this.updateUseStoreCredit();
      } else {
        this.props.actions.disableLoyaltyDiscounts();
      }
    });
  };

  updateUseStoreCredit = () => {
    const {
      actions: { updateLoyaltyDiscounts },
    } = this.props;
    updateLoyaltyDiscounts({ type: LoyaltyType.STORECREDIT, inputValue: Number(this.state.storeCredit) });
  };

  onStoreCreditValueChangeHandler = storeCredit => {
    this.setState(() => {
      return this.checkStoreCredit(storeCredit);
    }, this.updateUseStoreCredit);
  };

  checkStoreCredit = storeCredit => {
    const { transactionSession } = this.props;
    const { customer } = transactionSession;
    const loyalty = get(customer, 'loyalty', 0);
    const _storeCredit = Number(storeCredit);
    if (_storeCredit > loyalty) {
      return { storeCredit: Number(Number.parseFloat(String(loyalty)).toFixed(2)) };
    } else {
      return { storeCredit };
    }
  };

  toogleCashback = value => {
    this._useCashback = value;
    this.setState({ useLoyalty: value }, () => {
      if (value) {
        const {
          transactionSession,
          actions: { updateLoyaltyDiscounts },
        } = this.props;
        const { customer } = transactionSession;
        if (Boolean(customer)) {
          updateLoyaltyDiscounts({ type: LoyaltyType.CASHBACK, inputValue: customer.loyalty });
        } else {
          updateLoyaltyDiscounts({ type: LoyaltyType.CASHBACK, inputValue: 0 });
        }
      } else {
        this.props.actions.disableLoyaltyDiscounts();
      }
    });
  };

  checkDiscountValueInput = value => {
    const { discountType } = this.state;
    if (discountType === DiscountType.amount) {
      // amount
      checkPriceNumberInput(value, amount => {
        if (!isNaN(amount)) {
          this.onDiscountValueChangeHandler(amount);
        }
      });
    } else if (discountType === DiscountType.percent) {
      // percent
      checkPositiveIntInput(value, amount => {
        if (!isNaN(amount)) {
          this.onDiscountValueChangeHandler(amount);
        }
      });
    }
  };

  checkStoreCreditInput = value => {
    checkNumberInput(value, this.onStoreCreditValueChangeHandler);
  };

  onFullBillDiscountDisablePressHandler = () => {
    this.props.navigation.navigate('ModalInfo', {
      info: t("Full bill discount amount cannot be modified when there're items of different tax codes"),
    });
  };

  onCashbackDisablePressHandler = () => {
    const { transactionSession } = this.props;
    // @ts-ignore
    const hasBirDiscount = isBirDiscountAvailable(transactionSession);
    this.props.navigation.navigate('ModalInfo', {
      info: hasBirDiscount ? 'Cashback cannot be used when there is Special Discount' : t("Cashback cannot be used when there're items of different tax codes"),
    });
  };

  onStoreCreditDisablePressHandler = () => {
    const { transactionSession } = this.props;
    // @ts-ignore
    const hasBirDiscount = isBirDiscountAvailable(transactionSession);
    this.props.navigation.navigate('ModalInfo', {
      info: hasBirDiscount
        ? 'Store credit cannot be used when there is Special Discount'
        : t("Store credit cannot be used when there're items of different tax codes"),
    });
  };

  requestDiscountAccessToEdit = () => {
    const onSuccess = {
      callback: () => {
        this.setState({ needCheckCashierAccess: false });
      },
    };
    this.requestDiscountAccess(onSuccess);
  };

  onEditSpecialDiscount = () => {
    const { transactionSession } = this.props;
    if (hasProductAmusementTax(transactionSession)) {
      this.props.navigation.navigate('ModalInfo', { info: 'Cannot apply special discount while product with Amusement Tax is in the cart.' });
      return;
    }
    const onBIRDiscountConfirmHandler = getParam(this.props, 'onBIRDiscountConfirmHandler');
    this.props.navigation.navigate('AddBIRDiscount', {
      onBIRDiscountConfirmHandler: onBIRDiscountConfirmHandler,
    });
  };

  onRemoveSpecialDiscount = () => {
    const onBIRDiscountRemoveHandler = getParam(this.props, 'onBIRDiscountRemoveHandler');
    onBIRDiscountRemoveHandler && onBIRDiscountRemoveHandler();
  };

  onNotesChangeHandler = notes => {
    const {
      actions: { updateOrderLevelNotes },
    } = this.props;
    updateOrderLevelNotes({ orderLevelNotes: notes });
  };

  discountInputRefHandler = ref => {
    this.discountInputRef = ref;
  };

  focusDiscountInput = () => {
    if (this.discountInputRef && this.discountInputRef.focus) {
      this.discountInputRef.focus();
    }
  };

  render() {
    const { currency, transactionSession, enableCashback, enableServiceCharge, enableLoyalty, country, birAccredited, makeStoreCreditAsPayment } = this.props;
    // disable loyalty discount for 1.7.0 release
    const { serviceChargeRate, transactionType } = transactionSession;
    const birDiscountType = get(transactionSession, ['birInfo', 'discountType']);
    // @ts-ignore
    const hasBirDiscount = isBirDiscountAvailable(transactionSession);
    const { discountValue, useLoyalty, storeCredit, needCheckCashierAccess, discountType } = this.state;
    const title = t('Full Bill Discount');
    const { customer, isLoyaltyCanEnable, isOnlineOrder = false } = transactionSession;
    const isPreOrder = transactionType === TransactionFlowType.PreOrder;
    const isManualReturn = transactionType === TransactionFlowType.Return;
    // @ts-ignore
    const isPickUp = isPreOrderPickUp(transactionSession);
    const showServiceCharge = enableServiceCharge && transactionSession.salesChannel !== SalesChannelType.TAKEAWAY && !transactionSession.isOnlineOrder;
    const showDivlderLine = showServiceCharge || (Boolean(customer) && enableLoyalty);
    const fullbillDiscountDisabled = hasBirDiscount;
    const loyatyDiscountDisabled = isLoyaltyCanEnable === false || hasBirDiscount;
    const isBIREnabled = country === 'PH' && birAccredited && !isOnlineOrder;
    const isPercentDiscount = discountType === DiscountType.percent;

    const discountSuffix = isPercentDiscount ? '%' : '';
    let displayDiscountValue;
    if (isPercentDiscount) {
      displayDiscountValue = isValidNumber(discountValue) ? String(Math.round(discountValue)) : '0';
    } else {
      displayDiscountValue = localeNumber(isValidNumber(discountValue) ? discountValue : '0');
    }

    const currencySymbol = newConvertCurrencyToSymbol(this.props.localCountryMap, currency);

    return (
      <KeyboardAwareScrollView
        style={{ flex: 1 }}
        contentContainerStyle={{ flex: 1 }}
        enableOnAndroid
        keyboardOpeningTime={50}
        keyboardDismissMode='none'
        keyboardShouldPersistTaps='never'
        horizontal={false}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.container}>
          <TouchableWithoutFeedback {...testProps('al_btn_38')} onPress={this.closeModalHandler}>
            <View style={StyleSheet.absoluteFill} />
          </TouchableWithoutFeedback>
          <View style={[styles.card]} {...testProps('al_all_card')}>
            <View style={styles.content}>
              <View style={[styles.discountLabelRow, { opacity: fullbillDiscountDisabled ? 0.4 : 1 }]} {...testProps('al_discountLabelRow')}>
                <View style={[styles.discountRow, { paddingLeft: 0 }]} {...testProps('al_discountRow')}>
                  <Text style={styles.textTitle} {...testProps('al_textTitle')}>
                    {title}
                  </Text>
                </View>
                <View style={[styles.discountValueRow]}>
                  {needCheckCashierAccess ? (
                    <TouchableOpacity
                      {...testProps('al_btn_814')}
                      style={{ flex: 1 }}
                      onPress={this.requestDiscountAccessToEdit}
                      disabled={fullbillDiscountDisabled}
                    >
                      <Text style={{ fontSize: setSpText(64), color: '#303030' }}>{displayDiscountValue + discountSuffix}</Text>
                    </TouchableOpacity>
                  ) : (
                    <TouchableWithoutFeedback {...testProps('al_btn_674')} onPress={this.focusDiscountInput}>
                      <View style={{ flexDirection: 'row', flex: 1, alignItems: 'center' }}>
                        <CurrencyTextInput
                          ref={this.discountInputRefHandler}
                          style={{
                            height: ROW_HEIGHT,
                            fontSize: setSpText(64),
                            padding: 0,
                            marginBottom: scaleSizeH(4),
                            color:
                              this._isDiscountEnable === false || this._isDiscountEnable === undefined
                                ? currentThemes.inActiveButtonBackgroundColor
                                : '#303030',
                          }}
                          keyboardType='decimal-pad'
                          placeholderTextColor='gray'
                          onChangeText={this.checkDiscountValueInput}
                          autoFocus={true}
                          value={displayDiscountValue}
                          underlineColorAndroid='transparent'
                          returnKeyLabel={t('ADD')}
                          editable={this._isDiscountEnable === true && !fullbillDiscountDisabled}
                        />
                        {!!discountSuffix && (
                          <Text
                            style={[
                              {
                                fontSize: currentThemes.fontSize64,
                                color: '#303030',
                              },
                              IsAndroid && { alignSelf: 'stretch', textAlignVertical: 'center' },
                            ]}
                          >
                            {discountSuffix}
                          </Text>
                        )}
                      </View>
                    </TouchableWithoutFeedback>
                  )}
                  <CommonNumberPercentSwitch
                    checkAvailableBeforeHandling={this.requestDiscountAccess}
                    onValueChange={this.onDiscountTypeChangeHandler}
                    value={discountType}
                    disabled={fullbillDiscountDisabled}
                    currencySymbol={currencySymbol}
                  />
                </View>
                <MemoizedPercentPresets disabled={fullbillDiscountDisabled} onPress={this.onPercentPresetsClick} />
              </View>

              {isBIREnabled && <View style={styles.horizontalLine} />}
              {isBIREnabled && (
                <View style={styles.discountRow}>
                  <View style={styles.specialDiscountNameContainer}>
                    <Text testID='specialDiscount' style={styles.buttonText} {...testProps('al_specialDiscount')}>
                      Special Discount
                    </Text>
                    {hasBirDiscount && <Text style={styles.specialDiscountTypeText}>{birDiscountType}</Text>}
                  </View>
                  <View style={styles.specialDiscountButtonsContainer}>
                    <TouchableOpacity {...testProps('al_btn_252')} style={styles.specialDiscountButton} onPress={this.onEditSpecialDiscount}>
                      <Text style={styles.specialDiscountButtonText}>{hasBirDiscount ? 'EDIT' : 'ADD'}</Text>
                    </TouchableOpacity>

                    {hasBirDiscount && (
                      <TouchableOpacity {...testProps('al_btn_92')} style={styles.specialDiscountButton2} onPress={this.onRemoveSpecialDiscount}>
                        <Text style={styles.specialDiscountButtonText}>REMOVE</Text>
                      </TouchableOpacity>
                    )}
                  </View>
                </View>
              )}
              {showDivlderLine && <View style={styles.horizontalLine} />}
              {showServiceCharge && (
                <View style={styles.discountRow}>
                  <Text testID='serviceCharegeRate' style={styles.buttonText} {...testProps('al_serviceCharegeRate')}>
                    {!isNaN(serviceChargeRate) && `${Number((serviceChargeRate * 100).toFixed(2))}%` + t('Service Charge')}
                  </Text>
                  <CommonToggleSwitch
                    isValueStatic
                    defaultValue={this._enableServiceCharge}
                    onValueChange={this.onValueChagedHandler}
                    backgroundActive={currentThemes.buttonBackgroundColor}
                    backgroundInactive={currentThemes.inActiveButtonBackgroundColor}
                    circleActiveColor={'#FFF'}
                    circleBorderWidth={0}
                    circleInActiveColor={'#FFF'}
                    innerCircleStyle={{ alignItems: 'center', justifyContent: 'center' }}
                    renderActiveText={false}
                    renderInActiveText={false}
                    accessibilityLabel={'al_enableServiceCharge'}
                  />
                </View>
              )}
              {!makeStoreCreditAsPayment &&
                Boolean(customer) &&
                enableLoyalty &&
                !enableCashback &&
                !isPreOrder &&
                !isManualReturn &&
                !isPickUp &&
                !transactionSession.isOnlineOrder && (
                  <View style={{ opacity: loyatyDiscountDisabled ? 0.4 : 1 }}>
                    <View style={[styles.discountRow, styles.topBorder]}>
                      <Text testID='useStoreCredit' style={styles.buttonText}>
                        {t('Use Store Credit')}
                      </Text>
                      <CommonToggleSwitch
                        disabled={loyatyDiscountDisabled}
                        defaultValue={this._useStoreCredit}
                        isValueStatic
                        onValueChange={this.toogleStoreCredit}
                        onDisablePressHandler={this.onStoreCreditDisablePressHandler}
                        backgroundActive={currentThemes.buttonBackgroundColor}
                        backgroundInactive={currentThemes.inActiveButtonBackgroundColor}
                        circleActiveColor={'#FFF'}
                        circleBorderWidth={0}
                        circleInActiveColor={'#FFF'}
                        innerCircleStyle={{ alignItems: 'center', justifyContent: 'center' }}
                        renderActiveText={false}
                        renderInActiveText={false}
                      />
                    </View>
                    <TextInput
                      {...testProps('al_textinput_252')}
                      style={{
                        paddingHorizontal: PADDING_LEFT,
                        height: ROW_HEIGHT,
                        fontSize: setSpText(64),
                        padding: 0,
                        marginBottom: scaleSizeH(4),
                        color: useLoyalty ? currentThemes.buttonBackgroundColor : currentThemes.inActiveButtonBackgroundColor,
                      }}
                      onChangeText={this.checkStoreCreditInput}
                      editable={useLoyalty && !loyatyDiscountDisabled}
                      value={String(storeCredit)}
                      placeholder={'0'}
                      placeholderTextColor='gray'
                      keyboardType='decimal-pad'
                      underlineColorAndroid='transparent'
                    />
                  </View>
                )}
              {enableLoyalty && enableCashback && !isPreOrder && !isManualReturn && !isPickUp && !transactionSession.isOnlineOrder && (
                <View style={[styles.discountRow, styles.topBorder, { opacity: loyatyDiscountDisabled ? 0.4 : 1 }, styles.bottomLine]}>
                  <Text testID='useCashback' style={styles.buttonText}>
                    {t('Use Cashback')}
                  </Text>
                  <CommonToggleSwitch
                    disabled={loyatyDiscountDisabled}
                    defaultValue={this._useCashback}
                    isValueStatic
                    onValueChange={this.toogleCashback}
                    onDisablePressHandler={this.onCashbackDisablePressHandler}
                    backgroundActive={currentThemes.buttonBackgroundColor}
                    backgroundInactive={currentThemes.inActiveButtonBackgroundColor}
                    circleActiveColor={'#FFF'}
                    circleBorderWidth={0}
                    circleInActiveColor={'#FFF'}
                    innerCircleStyle={{ alignItems: 'center', justifyContent: 'center' }}
                    renderActiveText={false}
                    renderInActiveText={false}
                  />
                </View>
              )}
              {!isOnlineOrder && (
                <View>
                  <Text style={styles.orderNote}>{t('Order Note')}</Text>
                  <PosTextInput
                    {...testProps('al_textinput_796')}
                    defaultValue={String(this._comment || '')}
                    onChangeText={this.onNotesChangeHandler}
                    style={styles.addNote}
                    placeholder={t('Add Note')}
                    placeholderTextColor='#9F9F9F'
                    multiline={true}
                    autoCorrect={false}
                    maxLength={140}
                    underlineColorAndroid='transparent'
                  />
                </View>
              )}
            </View>
            <View style={styles.triangle} />
          </View>
        </View>
      </KeyboardAwareScrollView>
    );
  }
}

export default connector(ModalFullBillDiscount);

const PADDING_LEFT = scaleSizeW(20);
const ROW_HEIGHT = scaleSizeH(100);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  card: {
    marginRight: CART_WIDTH,
    flexDirection: 'row',
  },
  content: {
    width: scaleSizeW(520),
    overflow: 'hidden',
    backgroundColor: '#FFF',
  },
  triangle: {
    width: 0,
    height: 0,
    borderTopWidth: scaleSizeH(12),
    borderLeftWidth: scaleSizeW(12),
    borderBottomWidth: scaleSizeH(12),
    borderTopColor: 'transparent',
    borderBottomColor: 'transparent',
    borderLeftColor: '#FFF',
    top: scaleSizeH(38),
    marginLeft: -scaleSizeW(2),
  },
  buttonText: {
    fontSize: currentThemes.fontSize26,
    color: '#60636B',
    fontWeight: 'bold',
  },
  discountLabelRow: {
    // paddingTop: 20,
    paddingHorizontal: PADDING_LEFT,
    justifyContent: 'center',
  },
  discountRow: {
    height: ROW_HEIGHT,
    paddingHorizontal: PADDING_LEFT,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  discountValueRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: scaleSizeH(38),
    height: scaleSizeH(74),
  },
  specialDiscountNameContainer: {
    flexDirection: 'column',
    justifyContent: 'center',
  },
  specialDiscountButtonsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  specialDiscountTypeText: {
    fontSize: currentThemes.fontSize18,
    color: '#9F9F9F',
    marginTop: scaleSizeH(10),
  },
  specialDiscountButton: {
    paddingHorizontal: scaleSizeW(12),
    paddingVertical: scaleSizeH(12),
  },
  specialDiscountButton2: {
    paddingHorizontal: scaleSizeW(12),
    paddingVertical: scaleSizeH(12),
    marginLeft: scaleSizeW(12),
  },
  specialDiscountButtonText: {
    fontSize: currentThemes.fontSize18,
    color: '#00B0FF',
    fontWeight: 'bold',
  },
  horizontalLine: {
    height: StyleSheet.hairlineWidth,
    marginHorizontal: 0,
    backgroundColor: currentThemes.borderBottomColor,
  },
  topBorder: {
    borderTopWidth: StyleSheet.hairlineWidth,
    borderTopColor: currentThemes.borderBottomColor,
  },
  textTitle: {
    color: '#60636B',
    fontSize: currentThemes.fontSize26,
    fontWeight: 'bold',
  },
  addNote: {
    height: scaleSizeH(149),
    paddingVertical: scaleSizeH(14),
    paddingHorizontal: scaleSizeW(20),
    fontSize: currentThemes.fontSize18,
    textAlignVertical: 'top',
  },
  bottomLine: {
    borderBottomColor: currentThemes.borderBottomColor,
    borderBottomWidth: StyleSheet.hairlineWidth,
  },
  orderNote: {
    color: '#60636B',
    fontSize: currentThemes.fontSize26,
    fontWeight: 'bold',
    paddingTop: 15,
    paddingLeft: PADDING_LEFT,
    paddingBottom: 10,
  },
});
