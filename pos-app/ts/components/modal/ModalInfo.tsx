import React, { PureComponent } from 'react';
import { StyleSheet, Text, TextInput, View } from 'react-native';
import { ConnectedProps, connect } from 'react-redux';

import Icon from 'react-native-vector-icons/MaterialIcons';
import { bindActionCreators } from 'redux';
import { signOut } from '../../actions';
import { IsIOS, currentThemes, scaleSizeH, scaleSizeW, t } from '../../constants';
import { canGoBack } from '../../navigation/navigatorService';
import { ScreenProps } from '../../typings';
import { testProps } from '../../utils';
import { POSBasicAction, warnPOSBasicEvent } from '../../utils/logComponent';
import { getParam } from '../../utils/navigation';
import { ModalContainer, SubmitButton, SubmitFooter } from '../common';

const mapDispatchToProps = dispatch => ({
  actions: bindActionCreators(
    {
      signOut,
    },
    dispatch
  ),
});
const connector = connect(null, mapDispatchToProps);
type PropsFromRedux = ConnectedProps<typeof connector>;

export type ModalInfoProps = ModalInfoParams & ScreenProps & PropsFromRedux;

type materialIconsType = {
  name: string;
  size: number;
  color: string;
};
export interface ModalInfoParams {
  title?: string;
  info?: string;
  infoFn?: () => React.ReactNode;
  onCancelHandler?(): void;
  okText?: string;
  cancelText?: string;
  onSubmitHandler(): void;
  textAlign?: string;
  isShowTitle?: boolean;
  backParams?: any;
  needGoBackWhenCancel?: boolean;
  needGoBackWhenSubmit?: boolean;
  isCancelButtonRight?: boolean;
  closeable?: boolean;
  infoFontWeight?: string;
  textContentStyle?: any;
  titleIcon?: React.ReactNode;
  materialIcons?: materialIconsType;
}

class ModalInfo extends PureComponent<ModalInfoProps> {
  private _notes;

  handleInputNotes = notes => (this._notes = notes);

  renderContent = () => {
    const title = getParam(this.props, 'title', '');
    const isShowTitle = getParam(this.props, 'isShowTitle');
    const notShowInfo = getParam(this.props, 'notShowInfo');
    const info = getParam(this.props, 'info', '');
    const infoFn = getParam(this.props, 'infoFn');
    const okText = getParam(this.props, 'okText', t('OK'));
    const cancelText = getParam(this.props, 'cancelText', t('CANCEL'));
    const onCancelHandler = getParam(this.props, 'onCancelHandler', undefined);
    const needTextInput = getParam(this.props, 'needTextInput');
    const textAlign = getParam(this.props, 'textAlign', 'left');
    const textInputPlaceholder = getParam(this.props, 'textInputPlaceholder');
    const isCancelButtonRight = getParam(this.props, 'isCancelButtonRight', false);
    const fontWeight = getParam(this.props, 'infoFontWeight', '400');
    const textContentStyle = getParam(this.props, 'textContentStyle', {});
    const materialIcons = getParam(this.props, 'materialIcons');
    const titleIcon = getParam(this.props, 'titleIcon');

    const infoContent = infoFn ? (
      infoFn()
    ) : (
      <Text testID='Info' style={[styles.txtInfo, { textAlign, fontWeight }]}>
        {info}
      </Text>
    );

    return (
      <View style={styles.container}>
        <View style={[styles.textContent, textContentStyle]}>
          {isShowTitle && (
            <View style={styles.titleContainer}>
              {(titleIcon || materialIcons) && (
                <View style={styles.titleIconContainer}>
                  {titleIcon || <Icon name={materialIcons.name} size={materialIcons.size} color={materialIcons.color} />}
                </View>
              )}
              <Text style={[styles.title, notShowInfo && !needTextInput && styles.onlyTitle]}>{title}</Text>
            </View>
          )}
          {!notShowInfo && infoContent}
          {needTextInput && (
            <TextInput
              {...testProps('al_textinput_78')}
              style={styles.textInputStyle}
              multiline={true}
              clearButtonMode='never'
              placeholder={t(textInputPlaceholder)}
              placeholderTextColor={currentThemes.inActiveButtonBackgroundColor}
              onChangeText={this.handleInputNotes}
              underlineColorAndroid='transparent'
            />
          )}
        </View>

        <SubmitFooter style={[styles.footerContent, isCancelButtonRight && styles.footerContentReverse]}>
          {Boolean(onCancelHandler) && (
            <SubmitButton
              accessibilityLabel='al_modalinfo_cancel'
              style={[styles.submitBtnStyle, { borderTopWidth: 1, borderTopColor: '#D6D6D6', backgroundColor: null }]}
              onPress={this.onCancelHandler}
              textStyle={{ color: '#393939', fontSize: currentThemes.fontSize18, fontWeight: 'bold' }}
            >
              {cancelText}
            </SubmitButton>
          )}
          <SubmitButton
            accessibilityLabel='al_modalinfo_ok'
            style={styles.submitBtnStyle}
            onPress={this.onSubmitHandler}
            textStyle={{ fontSize: currentThemes.fontSize18, fontWeight: 'bold' }}
          >
            {okText}
          </SubmitButton>
        </SubmitFooter>
      </View>
    );
  };

  closeModalHandler = () => {
    if (canGoBack()) {
      this.props.navigation.goBack();
    } else {
      warnPOSBasicEvent({ action: POSBasicAction.UnknownError, reason: 'ModalInfo can not go back' });
      this.props.actions.signOut({ event: 'modalInfoCannotGoBack' });
    }
  };
  onCloseClick = () => {
    const onCloseClick = getParam(this.props, 'onCloseClick', undefined);
    onCloseClick && onCloseClick();
    const closeable = getParam(this.props, 'closeable', true);
    if (closeable) {
      this.closeModalHandler();
    }
  };

  onSubmitHandler = () => {
    const backParams = getParam(this.props, 'backParams', undefined);
    const needGoBackWhenSubmit = getParam(this.props, 'needGoBackWhenSubmit', true);
    const onSubmitHandler = getParam(this.props, 'onSubmitHandler', undefined);
    onSubmitHandler && onSubmitHandler(this._notes, this.props.navigation, backParams);
    needGoBackWhenSubmit && this.closeModalHandler();
  };

  onCancelHandler = () => {
    const onCancelHandler = getParam(this.props, 'onCancelHandler', undefined);
    const needGoBackWhenCancel = getParam(this.props, 'needGoBackWhenCancel', true);
    onCancelHandler && onCancelHandler();
    if (needGoBackWhenCancel) this.closeModalHandler();
  };

  render() {
    return (
      <ModalContainer
        onCloseHandler={this.onCloseClick}
        contentStyle={styles.content}
        mainContentContainerStyle={styles.mainContentContainerStyle}
        hiddenHeader={true}
      >
        {this.renderContent()}
      </ModalContainer>
    );
  }
}
export default connector(ModalInfo);

const styles = StyleSheet.create({
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: scaleSizeH(16),
  },
  titleIconContainer: {
    marginRight: scaleSizeW(12),
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    alignItems: 'center',
    justifyContent: 'space-between',
    height: '100%',
  },
  content: {
    width: IsIOS ? scaleSizeW(900) : scaleSizeW(720),
    height: undefined,
    backgroundColor: '#FFF',
    overflow: 'hidden',
  },
  textContent: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'center',
    textAlignVertical: 'center',
    paddingBottom: scaleSizeH(12),
  },
  textInputStyle: {
    flex: 1,
    width: scaleSizeW(240),
    height: scaleSizeH(100),
    textAlignVertical: 'center',
    textAlign: 'center',
    borderColor: currentThemes.borderBottomColor,
    borderWidth: scaleSizeW(1),
    fontSize: currentThemes.smallFontSize,
    color: '#333333',
    letterSpacing: 1,
    marginVertical: scaleSizeH(20),
  },
  title: {
    fontSize: currentThemes.fontSize28,
    color: '#303030',
    fontWeight: '500',
    textAlign: 'center',
    minHeight: scaleSizeH(52),
    textAlignVertical: 'center',
    paddingVertical: scaleSizeH(10),
    paddingHorizontal: scaleSizeW(16),
  },
  onlyTitle: {
    marginTop: scaleSizeH(40),
    marginBottom: scaleSizeH(40),
  },
  txtInfo: {
    fontSize: currentThemes.fontSize24,
    color: '#60636B',
    fontWeight: '400',
    textAlign: 'center',
    marginBottom: scaleSizeH(12),
    minHeight: IsIOS ? scaleSizeH(70) : scaleSizeH(120),
    paddingHorizontal: scaleSizeW(60),
    paddingVertical: scaleSizeH(20),
    textAlignVertical: 'center',
  },
  footerContent: {
    paddingHorizontal: 0,
    paddingVertical: 0,
  },
  footerContentReverse: {
    flexDirection: 'row-reverse',
  },
  submitBtnStyle: {
    marginHorizontal: 0,
    height: IsIOS ? scaleSizeH(65) : scaleSizeH(80),
  },
  mainContentContainerStyle: {
    width: '100%',
    minHeight: scaleSizeH(180),
  },
});
