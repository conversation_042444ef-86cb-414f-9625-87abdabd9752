import React, { PureComponent } from 'react';
import { BackHandler, ImageBackground, StyleSheet, Text, TouchableWithoutFeedback, View } from 'react-native';

import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { navigateToHome, NavigateType, RequestActionAccessResult } from '../../actions';
import { IconClose, icons, LockLogo } from '../../components/ui';
import { CommonColors, currentThemes, IsIOS, scaleSizeH, scaleSizeW, t } from '../../constants';
import DAL from '../../dal';
import { EmployeeType, ScreenProps } from '../../typings';
import { testProps } from '../../utils';
import { getParam } from '../../utils/navigation';
import { PinView } from '../auth';

export interface ManagerPinProps extends ScreenProps {
  // Navigation props
  onDismissHandler(result: RequestActionAccessResult): void;
  title?: string;
  renderIcon?: () => React.ReactNode;
  hint?: string;
  showBackground?: boolean;
  actions: {
    navigateToHome(payload: NavigateType): void;
  };
  authPinCode?: string;
}

const mapDispatchToProps = dispatch => ({
  actions: bindActionCreators(
    {
      navigateToHome,
    },
    dispatch
  ),
});

export class ModalManagerPin extends PureComponent<ManagerPinProps, null> {
  private _backButtonListener: any;

  componentDidMount(): void {
    const onDismissHandler = getParam(this.props, 'onDismissHandler', undefined);
    const goHomeWhenClose = getParam(this.props, 'goHomeWhenClose', false);
    this._backButtonListener = BackHandler.addEventListener('hardwareBackPress', () => {
      onDismissHandler && onDismissHandler({ ok: false });
      this.goBack();
      return !goHomeWhenClose;
    });
  }

  componentWillUnmount(): void {
    this._backButtonListener && this._backButtonListener.remove();
    this._backButtonListener = null;
  }

  renderContent = () => {
    const title = getParam(this.props, 'title', t('Authentication Required'));
    const hint = getParam(this.props, 'hint', t("Enter Manager's PIN"));
    const renderIcon = getParam(this.props, 'renderIcon', undefined);

    return (
      <View style={styles.tab}>
        <View style={styles.header}>
          <TouchableWithoutFeedback {...testProps('al_btn_368')} onPress={this.closeModalHandler}>
            <View style={styles.headerColoseButton}>
              {renderIcon ? renderIcon() : <IconClose width={scaleSizeW(48)} height={scaleSizeW(48)} color={CommonColors.Icon} />}
            </View>
          </TouchableWithoutFeedback>
          <Text style={styles.titleStyle}>{title}</Text>
          <View style={styles.headerColoseButton} />
        </View>

        <LockLogo width={scaleSizeW(360)} height={scaleSizeH(240)} />
        <Text style={{ fontSize: currentThemes.fontSize38, color: '#303030', fontWeight: '500' }}>{hint}</Text>
        <PinView container={{ alignSelf: 'stretch' }} maxPinCodeLength={4} onSubmitPinCodeHandler={this.onSubmitWrapper} />
      </View>
    );
  };

  goBack = () => {
    const { navigation } = this.props;
    if (navigation.canGoBack()) {
      navigation.goBack();
    }
  };

  closeModalHandler = () => {
    const onDismissHandler = getParam(this.props, 'onDismissHandler', undefined);
    const goHomeWhenClose = getParam(this.props, 'goHomeWhenClose', false);
    onDismissHandler && onDismissHandler({ ok: false });
    if (goHomeWhenClose) {
      const {
        actions: { navigateToHome },
        navigation,
      } = this.props;
      navigateToHome({ navigation });
    } else {
      this.goBack();
    }
  };

  checkEmployee = (pinCode: string) => {
    const employee: EmployeeType = DAL.getEmployeeByPinCode(pinCode) as any;
    return new Promise(resolve => {
      if (employee !== undefined) {
        resolve(employee);
      } else {
        resolve(null);
      }
    });
  };
  onSubmitWrapper = async (pinCode, onSuccessUICb, onFailureUICb) => {
    const authPinCode = getParam(this.props, 'authPinCode', undefined);
    if (authPinCode) {
      return this.onSubmitAuthPinCodeHandler(pinCode, authPinCode, onSuccessUICb, onFailureUICb);
    }
    return this.onSubmitClockInOutHandler(pinCode, onSuccessUICb, onFailureUICb);
  };

  onSubmitAuthPinCodeHandler = async (pinCode, authPinCode, onSuccessUICb, onFailureUICb) => {
    const onDismissHandler = getParam(this.props, 'onDismissHandler', undefined);
    try {
      if (pinCode !== authPinCode) {
        onFailureUICb && onFailureUICb(t('WrongPINCode'));
        return;
      }
      onDismissHandler && onDismissHandler({ ok: true });
      onSuccessUICb && onSuccessUICb();
      this.props.navigation.goBack();
    } catch (ex) {
      console.warn('onSubmitClockInOutHandler exception', ex);
    }
  };

  onSubmitClockInOutHandler = async (pinCode, onSuccessUICb, onFailureUICb) => {
    const onDismissHandler = getParam(this.props, 'onDismissHandler', undefined);
    try {
      const employee: EmployeeType = (await this.checkEmployee(pinCode)) as any;
      if (!employee) {
        onFailureUICb && onFailureUICb(t('WrongPINCode'));
        return;
      }

      if (employee.isManager) {
        onDismissHandler && onDismissHandler({ ok: true });
      } else {
        onFailureUICb && onFailureUICb(t('WrongManagerPin'));
        return;
      }
      onSuccessUICb && onSuccessUICb();
      this.props.navigation.goBack();
    } catch (ex) {
      console.warn('onSubmitClockInOutHandler exception', ex);
    }
  };

  render() {
    const showBackground = getParam(this.props, 'showBackground', true);
    return showBackground ? (
      <ImageBackground source={icons.managerbg} style={styles.background}>
        <View style={styles.container}>{this.renderContent()}</View>
      </ImageBackground>
    ) : (
      <View style={styles.container}>{this.renderContent()}</View>
    );
  }
}

export default connect(null, mapDispatchToProps)(ModalManagerPin);

const TAB_WIDTH = IsIOS ? scaleSizeW(900) : scaleSizeW(702);
const styles = StyleSheet.create({
  background: {
    width: '100%',
    height: '100%',
  },
  container: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.50)',
  },
  tab: {
    backgroundColor: 'white',
    marginTop: scaleSizeH(80),
    width: TAB_WIDTH,
    height: scaleSizeH(928),
    overflow: 'hidden',
    alignItems: 'center',
  },
  titleStyle: {
    fontSize: currentThemes.fontSize24,
    textAlign: 'center',
    color: '#303030',
    fontWeight: '500',
  },
  header: {
    width: '100%',
    height: scaleSizeH(108),
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerColoseButton: {
    width: scaleSizeW(180),
    height: scaleSizeH(108),
    justifyContent: 'center',
    alignItems: 'flex-start',
    paddingLeft: scaleSizeW(24),
  },
});
