import * as Immutable from 'immutable';
import { filter, find, findIndex, get, isEmpty } from 'lodash';
import React, { Component } from 'react';
import { StyleSheet, Text, TextInput, TouchableOpacity, TouchableWithoutFeedback, View } from 'react-native';
import RNFS from 'react-native-fs';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { ConnectedProps, connect } from 'react-redux';

import { Shadow } from 'react-native-shadow-2';
import { bindActionCreators } from 'redux';
import { createSelector } from 'reselect';
import {
  RequestActionAccessCallback,
  TransactionTypeWithDisplay,
  deletePurchasedItem,
  requestAuthorizedAction,
  setItemSalesperson,
  togglePayLaterItemTakeaway,
  updatePurchasedItem,
  updatePurchasedItemDiscount,
  updatePurchasedItemNotes,
  updatePurchasedItemQuantity,
} from '../../actions';
import { AuthorizationType, CART_WIDTH, CashierActions, IsAndroid, ItemChannelType, SalesChannelType, currentThemes, t } from '../../constants';
import { CommonColors, SharedStyles, scaleSizeH, scaleSizeW, setSpText } from '../../constants/themes';
import DAL from '../../dal';
import { selectEmployeeId, selectEnableSalespersonAssignment, selectLocalCountryMap } from '../../sagas/selector';
import { ProductType, ScreenProps } from '../../typings';
import {
  checkPositiveIntInput,
  checkPriceNumberInput,
  convertToNumber,
  getUnNullValue,
  localeNumber,
  newConvertCurrencyToSymbol,
  testProps,
} from '../../utils';
import { getParam } from '../../utils/navigation';
import { ScannerSubscription } from '../../utils/scanner';
import { getSimplifiedStr } from '../../utils/string';
import { isBirDiscountAvailable } from '../../utils/transaction';
import { CommonNumberPercentSwitch, CommonToggleSwitch, NumberSelector } from '../common';
import { DiscountType } from '../common/CommonNumberPercentSwitch';
import { MemoizedPercentPresets } from '../register/DiscountPercentPresets';
import { CurrencyTextInput } from '../textInput';
import PosTextInput from '../textInput/PosTextInput';
import { IconCircleMinus } from '../ui';

const fromImmutableTransactionSession = createSelector(
  (state: Immutable.Map<string, any>) => state.get('TransactionSession', Immutable.Map()),
  transactionSession => transactionSession.toJS()
);

interface Props extends ScreenProps, PropsFromRedux {
  // navigation
  itemIndex: number;
  itemId?: string;
  transactionSession: TransactionTypeWithDisplay;
  onClose(): void;
}

interface State {
  discountValue: number;
  needCheckCashierAccess: boolean;
  notes?: string;
  thumbnail: string;
  discountType: boolean; // true is amount, false is percent
  salesPerson: { employeeId?: string; employeeName: string };
}

const mapStateToProps = state => ({
  currency: state.getIn(['Storage', 'storeInfo', 'store', 'currency']),
  transactionSession: fromImmutableTransactionSession(state),
  currentEmployeeId: selectEmployeeId(state),
  enableTakeaway: state.getIn(['Storage', 'storeInfo', 'store', 'enableTakeaway']),
  localCountryMap: selectLocalCountryMap(state),
  enableSalespersonAssignment: selectEnableSalespersonAssignment(state),
});

const mapDispatchToProps = dispatch => ({
  actions: bindActionCreators(
    {
      updatePurchasedItemNotes,
      updatePurchasedItemQuantity,
      updatePurchasedItemDiscount,
      updatePurchasedItem,
      deletePurchasedItem,
      requestAuthorizedAction,
      togglePayLaterItemTakeaway,
      setItemSalesperson,
    },
    dispatch
  ),
});

const connector = connect(mapStateToProps, mapDispatchToProps);
type PropsFromRedux = ConnectedProps<typeof connector>;

class ModalProductCountSelector extends Component<Props, State> {
  private itemIndex: number;
  private itemId: string;
  private product: ProductType;
  private _mounted: boolean;
  private _isDiscountEnable: boolean;
  private _isTakeawayEnable: boolean;
  private _quantity: number;
  private _notes: string;
  private discountInputRef: TextInput;
  private _isOnlineOrder: boolean;
  private _scannerListener: any;
  private notesInputRef: TextInput;
  private _currentNotesText: string = '';
  private _unsubscribeFocusListener: () => void;
  private _unsubscribeBlurListener: () => void;
  private _lastScannedBarcode: string = '';
  private _lastScanTime: number = 0;

  constructor(props: Props) {
    super(props);
    const { transactionSession } = this.props;
    this.itemId = getParam(props, 'itemId', undefined);
    if (!isEmpty(this.itemId)) {
      this.itemIndex = findIndex(transactionSession.items, item => this.itemId === item.id);
    } else {
      this.itemIndex = getParam(props, 'itemIndex', undefined);
    }
    const purchasedItem = this.getCurPurchasedItem();
    const salesPersonId = get(purchasedItem, 'employeeId');
    let salesPerson = { employeeId: '', employeeName: t('Tag sales person') };
    if (salesPersonId) {
      salesPerson = { employeeId: salesPersonId, employeeName: get(purchasedItem, 'employeeName') };
    }
    this._isOnlineOrder = transactionSession && transactionSession.isOnlineOrder;

    const itemLevelDiscount = this._isOnlineOrder ? get(purchasedItem, 'manualDiscount') : get(purchasedItem, 'itemLevelDiscount');
    const discountType = getUnNullValue(itemLevelDiscount, 'type', 'amount') === 'amount';
    const discountValue = this._isOnlineOrder ? getUnNullValue(itemLevelDiscount, 'inputValue', '') : get(purchasedItem, 'discountInputValue', '');

    const productId = get(purchasedItem, 'productId');
    this.product = DAL.getProductById(productId);
    this._quantity = get(purchasedItem, 'quantity', 1);
    this._isDiscountEnable = get(purchasedItem, 'isDiscountEnable', true);
    this._isTakeawayEnable = get(purchasedItem, 'itemChannel') === ItemChannelType.TAKEAWAY || get(purchasedItem, 'isTakeaway');
    this._notes = get(purchasedItem, 'notes');
    this._currentNotesText = this._notes || '';

    this.state = {
      discountValue: discountValue,
      needCheckCashierAccess: true,
      thumbnail: '',
      discountType,
      salesPerson,
    };
  }

  shouldComponentUpdate(nextProps, nextState) {
    const { discountValue, needCheckCashierAccess, thumbnail, discountType, salesPerson } = this.state;
    if (discountValue !== nextState.discountValue) {
      return true;
    }
    if (discountType !== nextState.discountType) {
      return true;
    }
    if (needCheckCashierAccess !== nextState.needCheckCashierAccess) {
      return true;
    }
    if (thumbnail !== nextState.thumbnail) {
      return true;
    }
    if (salesPerson !== nextState.salesPerson) {
      return true;
    }
    return false;
  }

  componentDidMount() {
    this._mounted = true;
    const hasThumbnail = get(this.product, 'hasThumbnail');
    const lastUpdateThumbnail = get(this.product, 'lastUpdateThumbnail');
    if (hasThumbnail) {
      this.loadImage(this.product.productId, lastUpdateThumbnail);
    } else if (get(this.product, 'parentProductId')) {
      this.loadImage(this.product.parentProductId, '');
    }

    this._scannerListener = ScannerSubscription.addListener('Scanner_Resp', result => {
      this.handleScanBarcodeForNotes(result.barcode);
    });
  }

  componentWillUnmount() {
    this._mounted = false;

    this._scannerListener && this._scannerListener.remove();
    this._scannerListener = null;
  }

  getCurPurchasedItem = () => {
    const {
      transactionSession: { items },
    } = this.props;
    if (!isEmpty(this.itemId)) {
      return find(items, item => this.itemId === item.id);
    } else {
      return get(items, this.itemIndex);
    }
  };

  loadImage = (productId, lastUpdateThumbnail) => {
    const imagePath = RNFS.DocumentDirectoryPath + `/${productId}.png`;

    RNFS.exists(imagePath)
      .then(exist => {
        if (exist) {
          const newThumbnail = IsAndroid ? 'file://' + imagePath + '?time=' + lastUpdateThumbnail : imagePath;

          if (this.state.thumbnail !== newThumbnail) {
            requestAnimationFrame(() => {
              this._mounted && this.setState({ thumbnail: newThumbnail });
            });
          }
        } else {
          requestAnimationFrame(() => {
            this._mounted && this.setState({ thumbnail: '' });
          });
        }
      })
      .catch(err => {
        console.log('loadImage error: ' + err);
        requestAnimationFrame(() => {
          this._mounted && this.setState({ thumbnail: '' });
        });
      });
  };

  closeModalHandler = () => {
    const onClose = getParam(this.props, 'onClose', undefined);
    onClose && onClose();
    this.props.navigation.goBack();
  };

  onBIRQuantityDisableCallBack = ({ enable, message }) => {
    if (!enable) {
      this.props.navigation.navigate('ModalInfo', { info: message });
    }
  };

  onQuantityChangeHanlder = (quantity, onUpdateResult) => {
    if (quantity <= 0) {
      onUpdateResult && onUpdateResult(false);
      return;
    }
    const {
      actions: { updatePurchasedItemQuantity },
    } = this.props;
    updatePurchasedItemQuantity({
      itemIndex: this.itemIndex,
      quantity,
      onUpdateResult,
      onBIRQuantityDisableCallBack: this.onBIRQuantityDisableCallBack,
    });
  };

  checkDiscountValue = (discountType, discountValue) => {
    const currentItem = this.getCurPurchasedItem();
    const takeawayCharges = get(currentItem, 'takeawayCharges', 0);
    const currentSubtotal = get(currentItem, ['display', 'subtotal']) || get(currentItem, ['display', 'subTotal']);
    const maximumDiscountInputValue = convertToNumber(currentSubtotal) - convertToNumber(takeawayCharges);
    const _discountValue = convertToNumber(discountValue);

    if (_discountValue > maximumDiscountInputValue && discountType) {
      return { discountValue: String(maximumDiscountInputValue) };
    } else if (_discountValue > 100 && !discountType) {
      return { discountValue: 100 };
    } else {
      return { discountValue };
    }
  };

  _updatePurchasedItemDiscount = (isDiscountEnable, discountType, discountValue) => {
    requestAnimationFrame(() => {
      const {
        actions: { updatePurchasedItemDiscount },
      } = this.props;

      updatePurchasedItemDiscount({
        itemIndex: this.itemIndex,
        isDiscountEnable,
        discountValue,
        discountOption: {
          inputValue: isDiscountEnable ? convertToNumber(discountValue) : 0,
          type: discountType ? 'amount' : 'percent',
        },
      });
    });
  };

  onDiscountValueChangeHandler = preDiscountValue => {
    const { discountType } = this.state;
    const { discountValue } = this.checkDiscountValue(discountType, preDiscountValue);
    this.setState({ discountValue });
    this._updatePurchasedItemDiscount(this._isDiscountEnable, discountType, discountValue);
  };

  onPercentPresetsClick = discountValue => {
    const onSuccess = {
      callback: () => {
        this.setState({
          discountValue,
          discountType: DiscountType.percent,
        });
        this._updatePurchasedItemDiscount(this._isDiscountEnable, DiscountType.percent, discountValue);
      },
    };
    this.requestDiscountAccess(onSuccess);
  };

  onTakeawayEnableChangeHanlder = isTakeawayEnable => {
    requestAnimationFrame(() => {
      this._isTakeawayEnable = isTakeawayEnable;
      if (this._isOnlineOrder) {
        this.props.actions.togglePayLaterItemTakeaway({ itemIndex: this.itemIndex, isTakeaway: isTakeawayEnable });
      } else {
        this.props.actions.updatePurchasedItem({
          itemIndex: this.itemIndex,
          itemChannel: isTakeawayEnable ? ItemChannelType.TAKEAWAY : ItemChannelType.DEFAULT,
        });
      }
    });
  };

  onDiscountTypeChangeHandler = discountType => {
    requestAnimationFrame(() => {
      const equivalentValue = get(this.getCurPurchasedItem(), [this._isOnlineOrder ? 'manualDiscount' : 'itemLevelDiscount', 'equivalentValue'], 0);
      const _discountValue = convertToNumber(equivalentValue);
      const { discountValue } = this.checkDiscountValue(discountType, _discountValue);
      this.setState({
        discountValue,
        discountType,
      });
      this._updatePurchasedItemDiscount(this._isDiscountEnable, discountType, discountValue);
    });
  };

  requestDiscountAccess = onSuccess => {
    const { needCheckCashierAccess } = this.state;
    if (needCheckCashierAccess) {
      const { currentEmployeeId } = this.props;
      this.props.actions.requestAuthorizedAction({
        name: CashierActions.Discount,
        employeeId: currentEmployeeId,
        type: AuthorizationType.Cashier,
        onSuccess,
      });
    } else {
      onSuccess.callback();
    }
  };

  onNotesChangeHandler = notes => {
    this._currentNotesText = notes;

    const {
      actions: { updatePurchasedItemNotes },
    } = this.props;
    updatePurchasedItemNotes({
      itemIndex: this.itemIndex,
      notes,
    });
  };

  handleScanBarcodeForNotes = barcode => {
    if (!Boolean(barcode) || !this.notesInputRef) {
      return;
    }

    const currentTime = Date.now();
    const timeDiff = currentTime - this._lastScanTime;

    if (this._lastScannedBarcode === barcode && timeDiff < 1000) {
      return;
    }

    this._lastScannedBarcode = barcode;
    this._lastScanTime = currentTime;

    const currentText = this._currentNotesText || '';

    const newNotes = currentText ? `${currentText} ${barcode}` : barcode;

    this.notesInputRef.setNativeProps({ text: newNotes });

    this._currentNotesText = newNotes;
    this.onNotesChangeHandler(newNotes);
  };

  requestCashierDeleteActionWrapper = (onSuccess?: RequestActionAccessCallback, onFailure?: RequestActionAccessCallback) => {
    const { currentEmployeeId, transactionSession } = this.props;
    const isOpen = Boolean(transactionSession.isOpen);
    this.props.actions.requestAuthorizedAction({
      employeeId: currentEmployeeId,
      type: AuthorizationType.Cashier,
      name: CashierActions.DeleteOrder,
      content: { isNew: !isOpen, isSaved: isOpen },
      onSuccess,
      onFailure,
    });
  };

  deletePurchasedItem = () => {
    const onSuccess = {
      callback: () => {
        const { transactionSession } = this.props;
        let justDelete = true;
        if (this._isOnlineOrder) {
          const items = filter(transactionSession.items, item => isEmpty(item.itemType));
          justDelete = items && items.length > 1;
        }
        if (justDelete) {
          this.props.actions.deletePurchasedItem({ itemIndex: this.itemIndex, itemId: this.itemId });
          this.closeModalHandler();
        } else {
          const onCancelOrder = getParam(this.props, 'onCancelOrder', undefined);
          onCancelOrder && onCancelOrder(transactionSession.isOpen);
          if (!transactionSession.isOpen) this.closeModalHandler();
        }
      },
    };
    this.requestCashierDeleteActionWrapper(onSuccess);
  };

  onBeforeDecrementHandler = callback => {
    const onSuccess = {
      callback,
    };
    this.requestCashierDeleteActionWrapper(onSuccess);
  };

  onEditPurchasedItem = () => {
    this.closeModalHandler();
    const currentItem = this.getCurPurchasedItem();
    const options = get(currentItem, 'options');

    this.props.navigation.navigate('ModalProductOptionsSelector', {
      selectedItemIndex: this.itemIndex,
      selectedItem: currentItem,
      product: this.product,
      selectedOptionValues: options,
    });
  };

  requestDiscountAccessToEdit = () => {
    const onSuccess = {
      callback: () => {
        this.setState({ needCheckCashierAccess: false });
      },
    };
    this.requestDiscountAccess(onSuccess);
  };

  discountInputRefHandler = ref => {
    this.discountInputRef = ref;
  };

  focusDiscountInput = () => {
    if (this.discountInputRef && this.discountInputRef.focus) {
      this.discountInputRef.focus();
    }
  };

  checkDiscountValueInput = value => {
    const { discountType } = this.state;
    if (discountType === DiscountType.amount) {
      // amount
      checkPriceNumberInput(value, value => {
        if (!isNaN(value)) {
          this.onDiscountValueChangeHandler(value);
        }
      });
    } else if (discountType === DiscountType.percent) {
      // percent
      checkPositiveIntInput(value, value => {
        if (!isNaN(value)) {
          this.onDiscountValueChangeHandler(value);
        }
      });
    }
  };

  getMaxQuantity = purchasedItem => {
    const isSerialized = Boolean(get(purchasedItem, 'sn'));
    if (isSerialized) {
      return 1;
    }
    const limitAddQuantity = this._isOnlineOrder && get(purchasedItem, 'submitId', '');
    const quantity = get(purchasedItem, 'quantity', Number.MAX_SAFE_INTEGER);
    return limitAddQuantity ? quantity : Number.MAX_SAFE_INTEGER;
  };

  onDeleteSalesPerson = () => {
    this.setState({ salesPerson: { employeeId: '', employeeName: t('Tag sales person') } });
    this.props.actions.setItemSalesperson({ itemIndex: this.itemIndex, employeeId: null });
  };

  onOpenSalesPersonSelector = (itemTitle: string) => {
    const onSelect = (employeeId, employeeName) => {
      this.setState({
        salesPerson: { employeeName, employeeId },
      });
    };
    this.props.navigation.navigate('ModalSelectSalesPerson', {
      itemTitle,
      selectedEmployeeId: this.state.salesPerson.employeeId,
      itemIndex: this.itemIndex,
      onSelect,
    });
  };

  notesInputRefHandler = ref => {
    this.notesInputRef = ref;
  };

  renderSalesPerson = (title: string) => {
    if (this._isOnlineOrder || !this.props.enableSalespersonAssignment) {
      return null;
    }
    const { employeeName, employeeId } = this.state.salesPerson;
    return (
      <View style={styles.salesPersonBlock}>
        <TouchableWithoutFeedback
          {...testProps('al_btn_760')}
          onPress={() => {
            this.onOpenSalesPersonSelector(title);
          }}
        >
          <View style={SharedStyles.flexOne}>
            <View style={styles.salesPersonLabelRow}>
              <Text style={styles.textSubTitle}>{t('Sales Person')}</Text>
            </View>
            <View style={styles.salesPersonValueRow}>
              <Text style={[styles.salesPersonName, !employeeId && { color: '#9F9F9F' }]} numberOfLines={1} ellipsizeMode='tail'>
                {employeeName}
              </Text>
            </View>
          </View>
        </TouchableWithoutFeedback>

        {Boolean(this.state.salesPerson.employeeId) && (
          <TouchableOpacity {...testProps('al_btn_838')} onPress={this.onDeleteSalesPerson} hitSlop={{ left: 30, right: 30, top: 30, bottom: 30 }}>
            <IconCircleMinus width={scaleSizeW(34)} height={scaleSizeW(34)} />
          </TouchableOpacity>
        )}
      </View>
    );
  };

  render() {
    const purchasedItem = this.getCurPurchasedItem();
    const { transactionSession, currency, enableTakeaway } = this.props;
    const { discountValue, needCheckCashierAccess, discountType } = this.state;
    const { salesChannel, isPayLater } = transactionSession;
    // @ts-ignore
    const hasBirDiscount = isBirDiscountAvailable(transactionSession);
    const title = getSimplifiedStr(get(purchasedItem, 'title', 'title'), 24, true, true);
    const optionsLength = get(purchasedItem, ['options', 'length'], 0);
    const isUnitPrice = get(purchasedItem, 'priceType') === 'weight';
    const showTakeAway = enableTakeaway && (salesChannel === SalesChannelType.DINE_IN || isPayLater);
    const isDeleteButtonOrange = !Boolean(optionsLength) || this._isOnlineOrder;
    const maxQuantity = this.getMaxQuantity(purchasedItem);
    const itemDiscountDisabled = hasBirDiscount;
    const isPercentDiscount = discountType === DiscountType.percent;
    const discountSuffix = isPercentDiscount ? '%' : '';
    const displayDiscountValue = isPercentDiscount ? String(Math.round(Number(discountValue || 0))) : localeNumber(discountValue || '0');
    const currencySymbol = newConvertCurrencyToSymbol(this.props.localCountryMap, currency);

    return (
      <KeyboardAwareScrollView
        contentContainerStyle={styles.container}
        enableOnAndroid
        keyboardOpeningTime={50}
        keyboardDismissMode='none'
        keyboardShouldPersistTaps='never'
        horizontal={false}
        bounces={false}
      >
        <TouchableWithoutFeedback {...testProps('al_btn_249')} onPress={this.closeModalHandler}>
          <View style={StyleSheet.absoluteFill} />
        </TouchableWithoutFeedback>
        <View style={[styles.card]}>
          {/* @ts-ignore */}
          <Shadow>
            <View style={styles.content}>
              <View style={{ height: scaleSizeH(83), flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
                <Text testID='title' style={styles.textTitle}>
                  {title}
                </Text>
              </View>
              <NumberSelector
                min={0}
                reducibleMin={1}
                textInputStyle={{ flex: 2, fontSize: setSpText(38) }}
                fixedCount={isUnitPrice ? 2 : 0}
                max={maxQuantity}
                buttonHeight={this._isOnlineOrder ? scaleSizeH(80) : 0}
                style={{ width: WIDTH, height: scaleSizeH(130), borderRadius: 0, borderWidth: 0, borderTopWidth: 1, borderBottomWidth: 1 }}
                disabledColor={'#CCCCCC'}
                changeAfterBlur
                direction='Horizontal'
                defaultValue={this._quantity}
                onChangeValue={this.onQuantityChangeHanlder}
                onBeforeDecrementHandler={this.onBeforeDecrementHandler}
              />
              <View style={[styles.discountLabelRow, { opacity: itemDiscountDisabled ? 0.4 : 1 }]}>
                <View style={styles.discountRow}>
                  <Text style={styles.textSubTitle}>{t('Discount')}</Text>
                </View>
                <View style={styles.discountValueRow}>
                  {needCheckCashierAccess ? (
                    <TouchableOpacity
                      {...testProps('al_btn_545')}
                      style={{ flex: 1 }}
                      onPress={this.requestDiscountAccessToEdit}
                      disabled={itemDiscountDisabled}
                    >
                      <Text style={{ fontSize: setSpText(64), color: '#303030' }}>{displayDiscountValue + discountSuffix}</Text>
                    </TouchableOpacity>
                  ) : (
                    <TouchableWithoutFeedback {...testProps('al_btn_829')} onPress={this.focusDiscountInput}>
                      <View style={{ flexDirection: 'row', flex: 1, alignItems: 'center' }}>
                        <CurrencyTextInput
                          ref={this.discountInputRefHandler}
                          style={styles.discountInputStyle}
                          keyboardType='decimal-pad'
                          placeholderTextColor='#303030'
                          onChangeText={this.checkDiscountValueInput}
                          autoFocus={true}
                          value={displayDiscountValue}
                          underlineColorAndroid='transparent'
                          returnKeyLabel={t('ADD')}
                          editable={!itemDiscountDisabled}
                        />
                        {!!discountSuffix && (
                          <Text
                            style={[
                              {
                                fontSize: currentThemes.fontSize64,
                                color: '#303030',
                              },
                              IsAndroid && { alignSelf: 'stretch', textAlignVertical: 'center' },
                            ]}
                          >
                            {discountSuffix}
                          </Text>
                        )}
                      </View>
                    </TouchableWithoutFeedback>
                  )}
                  <CommonNumberPercentSwitch
                    checkAvailableBeforeHandling={this.requestDiscountAccess}
                    onValueChange={this.onDiscountTypeChangeHandler}
                    value={discountType}
                    disabled={itemDiscountDisabled}
                    currencySymbol={currencySymbol}
                  />
                </View>
                <MemoizedPercentPresets disabled={itemDiscountDisabled} onPress={this.onPercentPresetsClick} />
              </View>
              {showTakeAway && (
                <View
                  style={[
                    styles.discountRow,
                    {
                      paddingHorizontal: scaleSizeW(20),
                      paddingBottom: scaleSizeW(20),
                      marginBottom: 0,
                      borderBottomColor: currentThemes.borderBottomColor,
                      borderBottomWidth: 1,
                    },
                  ]}
                >
                  <Text style={styles.textSubTitle}>{t('Takeaway')}</Text>
                  <CommonToggleSwitch
                    isValueStatic
                    defaultValue={this._isTakeawayEnable}
                    onValueChange={this.onTakeawayEnableChangeHanlder}
                    backgroundActive={currentThemes.buttonBackgroundColor}
                    backgroundInactive={currentThemes.inActiveButtonBackgroundColor}
                    circleActiveColor={'#FFF'}
                    circleBorderWidth={0}
                    circleInActiveColor={'#FFF'}
                    innerCircleStyle={{ alignItems: 'center', justifyContent: 'center' }}
                    renderActiveText={false}
                    renderInActiveText={false}
                  />
                </View>
              )}

              {this.renderSalesPerson(title)}

              <View>
                <Text style={styles.orderNote}>{t('Item Note')}</Text>
                <PosTextInput
                  {...testProps('al_textinput_913')}
                  ref={this.notesInputRefHandler}
                  defaultValue={this._notes || ''}
                  onChangeText={this.onNotesChangeHandler}
                  style={styles.addNote}
                  placeholder={t('Add Note')}
                  placeholderTextColor='#9F9F9F'
                  multiline={true}
                  autoCorrect={false}
                  maxLength={140}
                  underlineColorAndroid='transparent'
                />
              </View>
              <View style={{ height: scaleSizeH(81), flexDirection: 'row' }}>
                <TouchableOpacity
                  {...testProps('al_btn_767')}
                  style={[styles.deleteButton, isDeleteButtonOrange && { backgroundColor: currentThemes.buttonBackgroundColor }]}
                  onPress={this.deletePurchasedItem}
                >
                  <Text style={[styles.buttonText, { color: !isDeleteButtonOrange ? '#393939' : 'white' }]}>{t('Delete')}</Text>
                </TouchableOpacity>
                {!isDeleteButtonOrange && (
                  <TouchableOpacity {...testProps('al_btn_457')} style={styles.editButton} onPress={this.onEditPurchasedItem}>
                    <Text style={styles.buttonText}>{t('Edit')}</Text>
                  </TouchableOpacity>
                )}
              </View>
            </View>
          </Shadow>
          <View style={styles.triangle} />
          {/* {Boolean(this.state.thumbnail) && <Image source={{ uri: this.state.thumbnail }} resizeMode='cover' style={styles.productImage} />} */}
        </View>
      </KeyboardAwareScrollView>
    );
  }
}

export default connector(ModalProductCountSelector);

const PADDING_LEFT = scaleSizeW(20);

const WIDTH = scaleSizeW(520);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  card: {
    marginRight: CART_WIDTH,
    flexDirection: 'row',
  },
  content: {
    width: WIDTH,
    overflow: 'hidden',
    backgroundColor: '#FFF',
  },
  triangle: {
    width: 0,
    height: 0,
    borderTopWidth: scaleSizeH(12),
    borderLeftWidth: scaleSizeW(12),
    borderBottomWidth: scaleSizeH(12),
    borderTopColor: 'transparent',
    borderBottomColor: 'transparent',
    borderLeftColor: '#FFF',
    top: scaleSizeH(38),
    marginLeft: -scaleSizeH(2),
  },
  buttonText: {
    fontSize: currentThemes.mediumFontSize,
    color: 'white',
  },
  editButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderTopColor: currentThemes.borderBottomColor,
    borderTopWidth: 1,
    backgroundColor: currentThemes.buttonBackgroundColor,
  },
  deleteButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderTopColor: currentThemes.borderBottomColor,
    borderTopWidth: 1,
  },
  discountLabelRow: {
    paddingHorizontal: PADDING_LEFT,
    justifyContent: 'center',
    borderBottomColor: currentThemes.borderBottomColor,
    borderBottomWidth: 1,
  },
  discountRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: scaleSizeH(17),
    marginBottom: scaleSizeH(39),
    // height: scaleSizeH(57),
  },
  discountValueRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: scaleSizeH(38),
  },
  addNote: {
    height: scaleSizeH(149),
    paddingVertical: scaleSizeH(14),
    paddingHorizontal: scaleSizeW(17),
    fontSize: currentThemes.fontSize18,
    textAlignVertical: 'top',
  },
  textTitle: {
    fontWeight: '500',
    color: '#303030',
    fontSize: currentThemes.fontSize28,
  },
  textSubTitle: {
    color: '#60636B',
    fontSize: currentThemes.fontSize26,
    textAlign: 'center',
    fontWeight: 'bold',
  },
  discountInputStyle: {
    fontSize: setSpText(64),
    // flex: 1,
    padding: 0,
    color: '#303030',
  },
  orderNote: {
    color: '#60636B',
    fontSize: currentThemes.fontSize26,
    fontWeight: 'bold',
    paddingTop: 15,
    paddingLeft: PADDING_LEFT,
    paddingBottom: 10,
  },
  salesPersonBlock: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomColor: currentThemes.borderBottomColor,
    borderBottomWidth: 1,
    paddingHorizontal: PADDING_LEFT,
    paddingVertical: scaleSizeH(24),
  },
  salesPersonLabelRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: scaleSizeH(10),
  },
  salesPersonValueRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  salesPersonName: {
    fontSize: currentThemes.fontSize18,
    color: CommonColors.MainTextGrey,
    lineHeight: scaleSizeH(25),
    maxWidth: scaleSizeW(400),
  },
});
