import { filter, flatten, forEach, get, map, sortBy } from 'lodash';
import React, { PureComponent } from 'react';
import { PanResponderInstance, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { connect, ConnectedProps } from 'react-redux';
import { bindActionCreators } from 'redux';
import { addPurchasedItem, ItemOptionType, updatePurchasedItem } from '../../actions';
import { currentThemes, t } from '../../constants';
import { scaleSizeH, scaleSizeW, setSpText, SharedStyles } from '../../constants/themes';
import { PurchasedItemType, ScreenProps } from '../../typings';
import { testProps } from '../../utils';
import * as JSONUtils from '../../utils/json';
import { getParam } from '../../utils/navigation';
import { getSimplifiedStr } from '../../utils/string';
import { generateResponer, ModalContainer, NumberSelector } from '../common';
import { ProductOptions, ProductOptionsType } from '../register';
import { PosTextInput } from '../textInput';

interface Props extends ScreenProps, PropsFromRedux {
  // navigation
  selectedOptionValues?: any[];
  selectedItem?: PurchasedItemType;
}

const mapDispatchToProps = dispatch => ({
  actions: bindActionCreators(
    {
      addPurchasedItem,
      updatePurchasedItem,
    },
    dispatch
  ),
});

const PADDING_HORIZONTAL = scaleSizeW(30);

const connector = connect(null, mapDispatchToProps);
type PropsFromRedux = ConnectedProps<typeof connector>;

class ModalProductOptionsSelector extends PureComponent<Props> {
  private selectedItemIndex;
  private _handling: boolean;
  private _panResponder: PanResponderInstance;
  private _variations;
  private _optionValues;
  private _notes: string;
  private quantity: number;

  constructor(props: Props) {
    super(props);
    this._handling = false;
    this.selectedItemIndex = getParam(props, 'selectedItemIndex', undefined);
    this._panResponder = generateResponer(this.onSubmitHandler);
  }

  initOptions = () => {
    this.initVariations();
    if (!isNaN(this.selectedItemIndex)) {
      const selectedOptionValues = getParam(this.props, 'selectedOptionValues', undefined);
      const selectedItem = getParam(this.props, 'selectedItem', undefined);
      this._notes = (get(selectedItem, 'notes') || '').trim();
      this.quantity = get(selectedItem, 'quantity', 1);
      this._optionValues = this.generateFromSelectedOptionValues(selectedOptionValues);
    } else {
      this._optionValues = this.generateFromDefaultOptionValues();
      this._notes = '';
      this.quantity = 1;
    }
  };

  generateFromDefaultOptionValues = () => {
    const _optionValues = {} as ItemOptionType;
    forEach(this._variations, variation => {
      const { _id, optionValues } = variation;
      _optionValues[_id] = filter(optionValues, optionValue => optionValue.isDefault).map(optionValue => ({
        variationId: _id,
        optionId: optionValue._id,
        optionValue: optionValue.value,
        priceDiff: optionValue.priceDiff,
        quantity: 1,
      }));
    });

    return _optionValues;
  };

  generateFromSelectedOptionValues = selectedOptionValues => {
    const _optionValues = {} as ItemOptionType;
    forEach(selectedOptionValues, _option => {
      const _variationId = get(_option, 'variationId');
      const _optionId = get(_option, 'optionId');
      const _optionValue = get(_option, 'optionValue');
      const _priceDiff = get(_option, 'priceDiff');
      const _quantity = get(_option, 'quantity');

      if (_optionValues[_variationId]) {
        _optionValues[_variationId].push({
          variationId: _variationId,
          optionId: _optionId,
          optionValue: _optionValue,
          priceDiff: _priceDiff,
          quantity: _quantity,
        });
      } else {
        _optionValues[_variationId] = [
          {
            variationId: _variationId,
            optionId: _optionId,
            optionValue: _optionValue,
            priceDiff: _priceDiff,
            quantity: _quantity,
          },
        ];
      }
    });

    return _optionValues;
  };

  onOptionValuesChangeHanlder = (variationId, optionValues) => {
    this._optionValues = Object.assign({}, this._optionValues, { [variationId]: optionValues });
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  renderProductOptions = (variation, index) => {
    let type;
    if (variation.variationType === 'SingleChoice') {
      type = ProductOptionsType.SingleChoice;
    } else if (variation.variationType === 'MultipleChoice') {
      type = ProductOptionsType.MultipleChoice;
    }
    const title = `${type === ProductOptionsType.SingleChoice ? 'Single choice' : 'Multiple choice'} ${variation.name}  `;
    const allowMultiQty = get(variation, 'allowMultiQty');
    return (
      <ProductOptions
        key={variation._id}
        title={title.toUpperCase()}
        type={type}
        contentWidth={scaleSizeW(1430) - PADDING_HORIZONTAL * 2}
        allowMultiQty={allowMultiQty}
        items={variation.optionValues}
        variationId={variation._id}
        options={this._optionValues[variation._id]}
        onOptionValuesChange={this.onOptionValuesChangeHanlder}
      />
    );
  };

  initVariations = () => {
    const product = getParam(this.props, 'product', undefined);
    const { variationsJson } = product;
    const variations: any[] = JSONUtils.parse(variationsJson, []);
    this._variations = variations.length > 0 ? sortBy(variations, ['order']) : variations;
  };

  renderOptions = () => {
    return (
      <View testID='renderOptions' style={[{ width: '100%', paddingHorizontal: PADDING_HORIZONTAL, flex: 1 }]}>
        {map(this._variations, this.renderProductOptions)}
      </View>
    );
  };

  onNotesChange = notes => (this._notes = notes);

  rnederNotesTextInput = () => {
    return (
      <PosTextInput
        {...testProps('al_modal_option_notes')}
        defaultValue={this._notes}
        style={styles.notesTextInput}
        multiline={true}
        clearButtonMode='never'
        maxLength={140}
        onChangeText={this.onNotesChange}
        underlineColorAndroid='transparent'
      />
    );
  };

  closeModalHandler = () => {
    this.props.navigation.goBack();
  };

  onBIRTaxRateDisableCallBack = ({ enable, message }) => {
    if (enable) {
      this.closeModalHandler();
    } else {
      this.props.navigation.navigate('ModalInfo', { info: message || t('Due to BIR requirements') });
    }
  };

  onSubmitHandler = () => {
    if (this._handling) {
      return;
    }

    this._handling = true;
    const { _notes, props, quantity } = this;
    const {
      actions: { addPurchasedItem, updatePurchasedItem },
    } = props;
    if (quantity <= 0) {
      this._handling = false;
      this.props.navigation.navigate('ModalInfo', {
        info: t('Quantity cannot be 0'),
      });
      return;
    }
    const options = flatten<any>(Object.values(this._optionValues));
    if (isNaN(this.selectedItemIndex)) {
      const product = getParam(this.props, 'product', undefined);
      const { priceType } = product;

      if (priceType === 'variable') {
        this.closeModalHandler();
        this.props.navigation.navigate('ModalProductVariablePrice', { product, quantity, options, notes: _notes && _notes.trim() });
      } else if (product.isSerialized) {
        this.closeModalHandler();
        this.props.navigation.navigate('ModalSerialNumberSelector', { product, quantity, options, notes: _notes && _notes.trim() });
      } else {
        addPurchasedItem({
          productId: product.productId,
          quantity,
          options,
          notes: _notes && _notes.trim(),
          onBIRTaxRateDisableCallBack: this.onBIRTaxRateDisableCallBack,
        });
      }
    } else {
      updatePurchasedItem({
        itemIndex: this.selectedItemIndex,
        notes: _notes,
        quantity,
        options,
        onBIRQuantityDisableCallBack: this.onBIRTaxRateDisableCallBack,
      });
    }
    this._handling = false;
  };

  onCountChangedHandler = quantity => {
    this.quantity = quantity;
  };

  renderHeaderRightBtn = () => {
    const buttonTitle = isNaN(this.selectedItemIndex) ? t('ADD') : t('UPDATE');
    return (
      <TouchableOpacity
        {...testProps('al_modal_option_add_update')}
        onPress={this.onSubmitHandler}
        style={SharedStyles.modalContainerHeaderRightButton}
        {...this._panResponder.panHandlers}
      >
        <Text style={styles.textAdd}>{buttonTitle}</Text>
      </TouchableOpacity>
    );
  };

  render() {
    this.initOptions();
    const product = getParam(this.props, 'product', undefined);
    const title = getSimplifiedStr(get(product, 'title'), 80, true, true);
    const isUnitPrice = get(product, 'priceType') === 'weight';

    return (
      <ModalContainer
        useKeyboard={true}
        title={title}
        onCloseHandler={this.closeModalHandler}
        contentStyle={styles.content}
        headerRightButton={this.renderHeaderRightBtn()}
        mainContentContainerStyle={{ width: '100%', minHeight: '88%' }}
      >
        {this.renderOptions()}
        <Text style={styles.title}>{t('NOTES AND QUANTITY')}</Text>
        <View style={styles.bottomContainer}>
          {this.rnederNotesTextInput()}
          <NumberSelector
            fixedCount={isUnitPrice ? 2 : 0}
            style={styles.numberSelector}
            min={0}
            defaultValue={this.quantity}
            onChangeValue={this.onCountChangedHandler}
          />
        </View>
      </ModalContainer>
    );
  }
}

export default connector(ModalProductOptionsSelector);

const styles = StyleSheet.create({
  content: {
    width: scaleSizeW(1430),
  },
  bottomContainer: {
    width: '100%',
    paddingHorizontal: PADDING_HORIZONTAL,
    paddingVertical: scaleSizeH(20),
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: scaleSizeH(120),
  },
  notesTextInput: {
    width: '66%',
    height: scaleSizeH(176),
    paddingHorizontal: scaleSizeW(20),
    paddingVertical: scaleSizeH(20),
    textAlignVertical: 'top',
    borderColor: currentThemes.borderBottomColor,
    borderWidth: 1,
    fontSize: currentThemes.fontSize18,
    color: '#333333',
    letterSpacing: 1,
  },
  numberSelector: {
    width: '32%',
    height: scaleSizeH(176),
  },
  textAdd: {
    fontSize: setSpText(18),
    color: 'white',
    fontWeight: 'bold',
  },
  title: {
    fontSize: currentThemes.fontSize20,
    color: '#303030',
    fontWeight: 'bold',
    alignSelf: 'flex-start',
    paddingHorizontal: PADDING_HORIZONTAL,
  },
});
