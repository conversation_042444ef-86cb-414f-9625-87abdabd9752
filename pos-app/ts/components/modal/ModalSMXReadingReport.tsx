import { useSafeState } from 'ahooks';
import { isEmpty, orderBy } from 'lodash';
import moment from 'moment';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { FlatList, StatusBar, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { getShiftsByDate, processToPrintSMXReading } from '../../actions';
import { CommonColors, IsIOS, SharedStyles, currentThemes, scaleSizeH, scaleSizeW } from '../../constants';
import { t } from '../../constants/i18n';
import { useAppNavigation } from '../../hooks/common/useAppNavigation';
import { selectRegisterObjectId } from '../../sagas/selector';
import { getUnNullValue, testProps } from '../../utils';
import { createDatePickerData, generateDatePickerData } from '../../utils/datetime';
import Picker from '../../utils/picker';
import { IconClose, IconDown } from '../ui';

const DATE_FORMAT = 'DD MMMM YYYY';
const SHIFT_FORMAT = 'h:mmA';
const SHIFT_DATE_FORMAT = 'MM-DD h:mmA';
const ITEM_HEIGHT = scaleSizeH(88);
const generateShiftTitle = (index: number, shift, date) => {
  const openTimeDate = moment(shift.openTime);
  let openTime;
  if (openTimeDate.isSame(moment(date), 'day')) {
    openTime = openTimeDate.format(SHIFT_FORMAT);
  } else {
    openTime = openTimeDate.format(SHIFT_DATE_FORMAT);
  }
  return `Shift ${index + 1}         ${openTime} - ${moment(shift.closeTime).format(SHIFT_FORMAT)}`;
};
export default function ModalSMXReadingReport() {
  const navigation = useAppNavigation();
  const dispatch = useDispatch();
  const registerObjectId = useSelector(selectRegisterObjectId);

  const pickDataRef = useRef([]);
  const flatListRef = useRef<FlatList>();

  const [shiftState, setShiftState] = useSafeState<{ open: boolean; index: number; list: any[] }>(() => ({ open: false, index: -1, list: [] }));
  const openShift = shiftState.open;
  const shiftIndex = shiftState.index;
  const shiftList = shiftState.list;
  const [date, setDate] = useState(''); // YYYY-MM-DD
  const shiftTitle = useMemo(() => {
    if (shiftIndex < 0) {
      return '';
    }
    const shift = shiftList[shiftIndex];
    if (!shift) {
      return '';
    }
    return generateShiftTitle(shiftIndex, shift, date);
  }, [shiftIndex, shiftList, date]);
  const shiftDisabled = useMemo(() => isEmpty(shiftList), [shiftList]);

  const displayDate = useMemo(() => {
    if (!date) {
      return '';
    }
    return moment(date).format(DATE_FORMAT);
  }, [date]);
  const saveable = useMemo(() => date && shiftIndex >= 0, [date, shiftIndex]);

  const queryShift = async (): Promise<any[]> => {
    return new Promise(resolve => {
      const onSuccess = {
        callback: payload => {
          resolve(orderBy(getUnNullValue(payload, 'res', []), ['closeTime'], ['asc']));
        },
      };

      const onFailure = {
        callback: () => {
          resolve([]);
        },
      };
      dispatch(getShiftsByDate({ date: moment(date).format('MM-DD-YYYY'), registerObjectId, onSuccess, onFailure }));
    });
  };

  const toggleShift = async () => {
    const pickerOpened = await Picker.isPickerShowAsync();
    if (pickerOpened) {
      Picker.hide();
    }
    setShiftState(state => ({ ...state, open: !state.open }));
  };

  const closeButtonClicked = async () => {
    const pickerOpened = await Picker.isPickerShowAsync();
    if (pickerOpened) {
      Picker.hide();
    }
    if (navigation.canGoBack) {
      navigation.goBack();
    }
  };

  const onConfirm = () => {
    const shiftId = shiftList[shiftIndex]?.shiftId;
    if (date && shiftId) {
      dispatch(processToPrintSMXReading({ date, shiftId, shiftIndex: shiftIndex + 1 }));
      closeButtonClicked();
    }
  };

  const renderShiftList = () => {
    const _keyExtractor = (item, index: number) => {
      return item.shiftId;
    };

    const renderShiftItem = ({ item, index }) => {
      const selected = shiftIndex === index;
      return (
        <TouchableOpacity
          {...testProps('al_btn_84')}
          style={{ ...StyleSheet.flatten(styles.shiftItem), backgroundColor: selected ? '#8D90A3' : 'transparent' }}
          onPress={() => {
            setShiftState(state => ({ ...state, open: !state.open, index }));
          }}
        >
          <Text style={{ ...StyleSheet.flatten(styles.shiftItemTitle), color: selected ? 'white' : '#60636B' }}>{generateShiftTitle(index, item, date)}</Text>
        </TouchableOpacity>
      );
    };

    return (
      <FlatList
        style={styles.shiftList}
        contentContainerStyle={styles.shiftListContentContainer}
        keyExtractor={_keyExtractor}
        renderItem={renderShiftItem}
        getItemLayout={(data, index) => ({ length: ITEM_HEIGHT, offset: ITEM_HEIGHT * index, index })}
        data={shiftList}
        ref={flatListRef}
      />
    );
  };

  const showPicker = async () => {
    setShiftState(state => ({ ...state, open: false }));
    const pickerOpened = await Picker.isPickerShowAsync();
    if (pickerOpened) {
      return;
    }
    const curDate = isEmpty(date) ? moment() : moment(date, 'YYYY-MM-DD');
    const datePickerData = createDatePickerData(curDate, pickDataRef.current);
    const selectedValue = datePickerData.selectedValue;
    const pickerData = datePickerData.pickerData;

    Picker.init({
      pickerData,
      selectedValue,
      pickerFontSize: currentThemes.fontSize28,
      pickerToolBarFontSize: currentThemes.fontSize24,
      pickerRowHeight: scaleSizeH(36),
      pickerToolBarHeight: currentThemes.fontSize100,
      pickerTitleText: '',
      pickerTextEllipsisLen: 100,
      wheelFlex: [1],
      pickerCancelBtnText: t('Cancel'),
      pickerConfirmBtnText: t('Confirm'),
      pickerBg: [255, 255, 255, 1],
      pickerToolBarBg: [239, 239, 244, 1],
      pickerFontFamily: IsIOS ? 'Courier' : 'monospace',
      onPickerConfirm: pickedValue => {
        setDate(pickedValue.join('-'));
        Picker.hide();
      },
      onPickerCancel: () => {
        Picker.hide();
      },
    });

    Picker.show();
  };

  useEffect(
    function () {
      let cancelled = false;
      async function executor() {
        setShiftState({
          open: false,
          list: [],
          index: -1,
        });
        const list = await queryShift();
        if (!cancelled) {
          setShiftState(state => ({ ...state, list }));
        }
      }
      if (date) {
        executor();
      }

      return () => {
        cancelled = true;
      };
    },
    [date]
  );

  useEffect(() => {
    pickDataRef.current = generateDatePickerData();
    showPicker();
  }, []);

  useEffect(() => {
    const id = requestAnimationFrame(() => {
      if (openShift && flatListRef.current && shiftIndex >= 0) {
        flatListRef.current.scrollToIndex({ index: shiftIndex, animated: false });
      }
    });
    return () => {
      cancelAnimationFrame(id);
    };
  }, [openShift, shiftIndex]);

  return (
    <View style={styles.container}>
      <StatusBar barStyle='dark-content' />
      <View style={styles.layer}>
        <View style={styles.titleBarContainer}>
          <TouchableOpacity {...testProps('al_btn_144')} style={styles.titleBarTouchableCloseIcon} onPress={closeButtonClicked}>
            <IconClose color={CommonColors.Icon} width={scaleSizeW(48)} height={scaleSizeH(48)} />
          </TouchableOpacity>
          <View style={{ flexGrow: 1, justifyContent: 'center', alignItems: 'center' }}>
            <Text style={styles.titleBarTitle}>Select Shift to Print SM X-Reading</Text>
          </View>
          <View style={{ width: scaleSizeW(240) }}>
            <TouchableOpacity {...testProps('al_btn_956')} disabled={!saveable} onPress={onConfirm}>
              <View style={[styles.saveButton, { backgroundColor: saveable ? currentThemes.buttonBackgroundColor : '#9E9E9E' }]}>
                <Text style={{ color: '#FFFFFF', fontSize: currentThemes.fontSize18, fontWeight: 'bold' }}>CONFIRM</Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>
        <View style={styles.content}>
          <View style={styles.rowSpaceContent}>
            <View style={styles.rowSpaceItem}>
              <Text style={styles.tipText}>
                DATE OF SHIFT <Text style={{ color: 'red' }}>*</Text>
              </Text>
              <TouchableOpacity {...testProps('al_btn_587')} onPress={showPicker}>
                <View style={[styles.inputContent, { flexDirection: 'row' }]}>
                  <TextInput
                    {...testProps('al_textinput_592')}
                    style={[styles.inputText, { flex: 1, textAlignVertical: 'center' }]}
                    placeholder='Select Date'
                    keyboardType='default'
                    editable={false}
                    pointerEvents='none'
                    placeholderTextColor='#9F9F9F'
                    value={displayDate}
                  />
                  <IconDown width={scaleSizeW(30)} height={scaleSizeH(30)} color={'#393939'} />
                </View>
              </TouchableOpacity>
            </View>
            <View style={styles.rowSpaceItem}>
              <Text style={styles.tipText}>
                SHIFT TO PRINT <Text style={{ color: 'red' }}>*</Text>
              </Text>
              <View>
                <TouchableOpacity {...testProps('al_btn_649')} disabled={shiftDisabled} onPress={toggleShift}>
                  <View style={[styles.inputContent, { flexDirection: 'row', backgroundColor: shiftDisabled ? '#D1D1D1' : undefined }]}>
                    <TextInput
                      {...testProps('al_textinput_619')}
                      style={[styles.inputText, { flex: 1, textAlignVertical: 'center' }]}
                      placeholder='Select Shift'
                      keyboardType='default'
                      editable={false}
                      pointerEvents='none'
                      placeholderTextColor='#9F9F9F'
                      value={shiftTitle}
                    />
                    <IconDown width={scaleSizeW(30)} height={scaleSizeH(30)} color={'#393939'} />
                  </View>
                </TouchableOpacity>
                {!!openShift && renderShiftList()}
              </View>
            </View>
          </View>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#858585A0',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  layer: {
    justifyContent: 'flex-end',
    alignItems: 'center',
    width: '74%',
    height: '100%',
    backgroundColor: 'white',
    overflow: 'hidden',
  },
  content: {
    alignItems: 'center',
    paddingTop: scaleSizeH(20),
    flex: 1,
  },
  titleBarContainer: {
    alignSelf: 'stretch',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    height: scaleSizeH(108),
  },
  titleBarTouchableCloseIcon: {
    ...StyleSheet.flatten(SharedStyles.touchableIconContainer),
    margin: scaleSizeW(12),
  },
  titleBarTitle: {
    fontSize: currentThemes.fontSize24,
    color: '#303030',
    fontWeight: '500',
  },
  rowSpaceItem: {
    width: scaleSizeW(671),
  },
  shiftListContentContainer: {
    paddingVertical: scaleSizeH(24),
  },
  shiftItem: {
    height: ITEM_HEIGHT,
    justifyContent: 'flex-start',
    flexDirection: 'row',
    alignItems: 'center',
  },
  shiftItemTitle: {
    marginLeft: scaleSizeW(18),
    fontWeight: '400',
    fontSize: currentThemes.fontSize18,
  },
  shiftList: {
    borderColor: currentThemes.borderBottomColor,
    borderWidth: scaleSizeW(1),
    borderRadius: scaleSizeW(8),
    backgroundColor: 'white',
    width: '100%',
    maxHeight: scaleSizeH(830),
  },
  inputText: {
    fontSize: currentThemes.fontSize18,
    color: '#393939',
    letterSpacing: 1,
    padding: 0,
    width: '100%',
    height: '100%',
  },
  tipText: {
    fontSize: currentThemes.fontSize18,
    color: '#959595',
    fontWeight: '500',
  },
  inputContent: {
    height: scaleSizeH(72),
    paddingHorizontal: scaleSizeW(16),
    alignItems: 'center',
    flexDirection: 'row',
    borderRadius: scaleSizeW(8),
    borderWidth: scaleSizeW(1),
    borderColor: '#E0E0E4',
    marginTop: scaleSizeH(4),
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',

    height: scaleSizeH(108),
  },
  rowSpaceContent: {
    flexDirection: 'row',
    width: scaleSizeW(1430),
    paddingHorizontal: scaleSizeW(32),
    // marginTop: scaleSizeH(52),
    justifyContent: 'space-between',
    flex: 1,
  },
});
