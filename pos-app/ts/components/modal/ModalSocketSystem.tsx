import React, { FC } from 'react';
import { Linking, StyleSheet, Text, TouchableOpacity, View, ViewStyle } from 'react-native';
import { IsIOS, SharedStyles, widthRatio } from '../../constants';
import { SubmitButton, SubmitFooter } from '../common';
import { scaleSize, setSpText } from '../../constants/themes';
import { IconError } from '../ui';
import QRCode from 'react-native-qrcode-svg';
import { testProps } from '../../utils';
import { useAppNavigation } from '../../hooks/common/useAppNavigation';

interface Props {
  title: string;
  content: string;
  message: string;
  submitText?: string;
  cancelText?: string;
  textInput?: boolean;
  icon?: string;
  style?: ViewStyle;
  submitColor?: string;
  cancelColor?: string;
  onCancelHandler?: () => void;
  onSubmitHandler?: () => void;
  link?: string;
  route?: any;
}

export const ModalSocketSystem: FC<Props> = (props: Props) => {
  props = { ...props, ...props.route?.params };

  const navigation = useAppNavigation();

  return (
    <View style={styles.container}>
      <View style={[styles.containerContent, props.style]}>
        {props.icon === 'error' && (
          <View style={{ alignItems: 'center', paddingTop: scaleSize(32) }}>
            <IconError></IconError>
          </View>
        )}
        <Text style={styles.titleStyle}>{props.title}</Text>
        {props.content && <Text style={styles.content}>{props.content}</Text>}
        <Text style={styles.txtInfo}>{''}</Text>
        <View style={[SharedStyles.flexOneColumnCenter, styles.textContent, { height: scaleSize(200), flexDirection: 'row', alignContent: 'center' }]}>
          <View style={{ height: scaleSize(200), width: scaleSize(300), paddingRight: scaleSize(40) }}>
            <QRCode size={scaleSize(240)} logo={require('../../../assets/icons/iconLogoSmall.png')} value={props.link} />
            <Text
              style={{
                paddingTop: scaleSize(8),
                color: '#60636B',
                fontSize: IsIOS ? setSpText(12) : setSpText(18),
                textAlign: IsIOS ? 'left' : 'center',
              }}
            >
              {'Scan the QR code for more info'}
            </Text>
          </View>
          <View
            style={{
              width: scaleSize(1),
              paddingTop: scaleSize(160),
              paddingVertical: scaleSize(100),
              marginHorizontal: scaleSize(16),
            }}
          >
            <View
              style={{
                width: scaleSize(1),
                height: scaleSize(260),
                backgroundColor: '#D6D6D6',
              }}
            />
          </View>
          <View style={{ width: scaleSize(300), height: scaleSize(200), paddingRight: scaleSize(40) }}>
            <Text style={{ paddingTop: scaleSize(68), color: '#60636B', fontSize: setSpText(22) }}>{'Or, visit this link'}</Text>
            <TouchableOpacity {...testProps('al_btn_977')} onPress={() => Linking.openURL(props.link)}>
              <Text style={{ paddingTop: scaleSize(8), fontSize: setSpText(24), color: '#00B0FF' }}>{props.message}</Text>
            </TouchableOpacity>
          </View>
        </View>

        <SubmitFooter style={styles.footerContent}>
          {props.cancelText && (
            <SubmitButton
              accessibilityLabel='CancelButton'
              style={[styles.submitBtnStyle, { backgroundColor: props.cancelColor ?? '#FFFFFF' }]}
              onPress={() => {
                props.onCancelHandler();
                navigation.goBack();
              }}
              textStyle={{ fontSize: setSpText(24), color: '#393939' }}
            >
              {props.cancelText}
            </SubmitButton>
          )}
          {props.submitText && (
            <SubmitButton
              accessibilityLabel='OKButton'
              style={[styles.submitBtnStyle, { backgroundColor: props.submitColor ?? '#FC7118' }]}
              onPress={() => {
                props.onSubmitHandler();
                navigation.goBack();
              }}
              textStyle={{ fontSize: setSpText(24), color: '#FFFFFF' }}
            >
              {props.submitText}
            </SubmitButton>
          )}
        </SubmitFooter>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignSelf: 'baseline',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: undefined,
    backgroundColor: 'rgba(0, 0, 0, 0.50)',
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  containerContent: {
    width: 760 * widthRatio,
    backgroundColor: '#FFF',
    overflow: 'hidden',
  },
  titleStyle: {
    fontSize: IsIOS ? setSpText(26) : setSpText(32),
    color: '#303030',
    textAlign: 'center',
    fontWeight: '500',
    marginTop: 35 * widthRatio,
    marginHorizontal: 48 * widthRatio,
    marginBottom: IsIOS ? scaleSize(20) : scaleSize(0),
  },
  textContent: {
    flex: 1,
    paddingVertical: 10 * widthRatio,
    paddingHorizontal: 10 * widthRatio,
    paddingTop: scaleSize(40),
    paddingBottom: scaleSize(200),
  },
  txtInfo: {
    fontSize: setSpText(26),
    color: '#60636B',
    fontWeight: '400',
    textAlign: 'center',
    marginHorizontal: 24 * widthRatio,
    marginVertical: 24 * widthRatio,
  },
  content: {
    fontSize: setSpText(26),
    color: '#60636B',
    fontWeight: '400',
    textAlign: 'center',
    marginHorizontal: 24 * widthRatio,
    marginVertical: 24 * widthRatio,
  },
  footerContent: {
    paddingHorizontal: 0,
    paddingVertical: 0,
  },
  submitBtnStyle: {
    marginHorizontal: 0,
    height: scaleSize(96),
    color: '#FFFFFF',
    borderTopWidth: 1 * widthRatio,
    borderTopColor: '#D6D6D6',
  },
});
