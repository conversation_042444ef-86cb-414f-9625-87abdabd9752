import React, { FC, useEffect, useRef, useState } from 'react';
import { AppState, Linking, StyleSheet, Text, TouchableOpacity, View, ViewStyle } from 'react-native';
import { IsIOS, SharedStyles, widthRatio } from '../../constants';
import { SubmitButton, SubmitFooter } from '../common';
import { scaleSize, setSpText } from '../../constants/themes';
import { IconContactSupport, IconError } from '../ui';
import QRCode from 'react-native-qrcode-svg';
import { testProps } from '../../utils';
import { useAppNavigation } from '../../hooks/common/useAppNavigation';
import IconWarn from '../../../assets/icons/svg/warn.svg';
import { useSelector } from 'react-redux';
import { selectInsufficientStorageWarning } from '../../sagas/selector';
import RNFS from 'react-native-fs';

interface Props {
  title: string;
  content: string;
  message: string;
  submitText?: string;
  cancelText?: string;
  textInput?: boolean;
  icon?: string;
  style?: ViewStyle;
  submitColor?: string;
  cancelColor?: string;
  onCancelHandler?: () => void;
  onSubmitHandler?: () => void;
  link?: string;
  route?: any;
}

export const ModalStorageCheck: FC<Props> = (props: Props) => {
  props = { ...props, ...props.route?.params };

  const appState = useRef(AppState.currentState);
  const [appStateVisible, setAppStateVisible] = useState(appState.current);
  const navigation = useAppNavigation();
  const warning = useSelector(selectInsufficientStorageWarning);

  useEffect(() => {
    const subscription = AppState.addEventListener('change', nextAppState => {
      if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
        console.log('App has come to the foreground!');

        RNFS.getFSInfo().then(info => {
          if (info && info.freeSpace) {
            if (info.freeSpace / 1024 / 1024 >= warning.criticalThreshold) {
              navigation.goBack();
              props.onCancelHandler();
            }
          }
        });
      }
      appState.current = nextAppState;
      setAppStateVisible(appState.current);
    });

    return () => {
      subscription.remove();
    };
  }, []);

  return (
    <View style={styles.container}>
      <View style={[styles.containerContent, props.style]}>
        <View style={{ flexDirection: 'row', alignItems: 'center', alignSelf: 'center' }}>
          {props.icon === 'error' && (
            <View style={{ alignItems: 'center', paddingTop: scaleSize(32) }}>
              <IconError width={32} height={32}></IconError>
            </View>
          )}
          {props.icon === 'warn' && (
            <View style={{ alignItems: 'center', paddingTop: scaleSize(32) }}>
              <IconWarn width={32} height={32}></IconWarn>
            </View>
          )}
          <Text style={styles.titleStyle}>{props.title}</Text>
        </View>
        {props.content && <Text style={styles.content}>{props.content}</Text>}
        <Text style={styles.txtInfo}>{''}</Text>
        <View
          style={[
            SharedStyles.flexOneColumnCenter,
            styles.textContent,
            {
              height: scaleSize(200),
              flexDirection: 'row',
              alignContent: 'center',
            },
          ]}
        >
          <View style={{ height: scaleSize(200), width: scaleSize(320), paddingRight: scaleSize(40) }}>
            <View style={{ paddingStart: scaleSize(12) }}>
              <QRCode size={scaleSize(240)} logo={require('../../../assets/icons/iconLogoSmall.png')} value={props.link} />
            </View>
            <Text
              style={{
                paddingTop: scaleSize(8),
                color: '#60636B',
                fontSize: setSpText(16),
                textAlign: IsIOS ? 'left' : 'center',
              }}
            >
              {'Scan the QR code for more info'}
            </Text>
          </View>
          <View
            style={{
              width: scaleSize(1),
              paddingTop: scaleSize(160),
              paddingVertical: scaleSize(100),
              marginHorizontal: scaleSize(16),
            }}
          >
            <View
              style={{
                width: scaleSize(1),
                height: scaleSize(260),
                backgroundColor: '#D6D6D6',
              }}
            />
          </View>
          <View style={{ width: scaleSize(300), height: scaleSize(200), paddingRight: scaleSize(40) }}>
            <View style={{ justifyContent: 'space-between' }}>
              <Text
                style={{
                  paddingTop: scaleSize(68),
                  color: '#60636B',
                  fontSize: setSpText(22),
                }}
              >
                {'Or, visit this link'}
              </Text>
              <TouchableOpacity {...testProps('al_btn_977')} onPress={() => Linking.openURL(props.link)}>
                <Text style={{ paddingTop: scaleSize(8), fontSize: setSpText(24), color: '#00B0FF' }}>{props.message}</Text>
              </TouchableOpacity>
            </View>
            <View style={{ paddingTop: scaleSize(80) }}>
              <View style={{ flexDirection: 'row' }}>
                <IconContactSupport></IconContactSupport>
                <Text style={{ paddingStart: 4, fontSize: setSpText(16) }}>{'If issue persists, contact '}</Text>
                <TouchableOpacity
                  {...testProps('al_btn_977')}
                  onPress={() => {
                    navigation.navigate('Support', {
                      liveChat: true,
                    });
                  }}
                >
                  <Text style={{ fontSize: setSpText(16), color: '#00B0FF' }}>{'our support'}</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>

        <SubmitFooter style={styles.footerContent}>
          {props.cancelText && (
            <SubmitButton
              accessibilityLabel='CancelButton'
              style={[styles.submitBtnStyle, { backgroundColor: props.cancelColor ?? '#FFFFFF' }]}
              onPress={() => {
                navigation.goBack();
                props.onCancelHandler();
              }}
              textStyle={{ fontSize: setSpText(24), color: '#393939' }}
            >
              {props.cancelText}
            </SubmitButton>
          )}
          {props.submitText && (
            <SubmitButton
              accessibilityLabel='OKButton'
              style={[styles.submitBtnStyle, { backgroundColor: props.submitColor ?? '#FC7118' }]}
              onPress={() => {
                props.onSubmitHandler();
              }}
              textStyle={{ fontSize: setSpText(24), color: '#FFFFFF' }}
            >
              {props.submitText}
            </SubmitButton>
          )}
        </SubmitFooter>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignSelf: 'baseline',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: undefined,
    backgroundColor: 'rgba(0, 0, 0, 0.50)',
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  containerContent: {
    width: IsIOS ? 840 * widthRatio : 760 * widthRatio,
    backgroundColor: '#FFF',
    overflow: 'hidden',
  },
  titleStyle: {
    fontSize: IsIOS ? setSpText(26) : setSpText(32),
    color: '#303030',
    textAlign: 'center',
    fontWeight: '500',
    marginTop: 35 * widthRatio,
    marginHorizontal: 24 * widthRatio,
    marginBottom: IsIOS ? scaleSize(20) : scaleSize(0),
  },
  textContent: {
    flex: 1,
    paddingVertical: 10 * widthRatio,
    paddingHorizontal: 10 * widthRatio,
    paddingTop: scaleSize(40),
    paddingBottom: scaleSize(200),
  },
  txtInfo: {
    fontSize: setSpText(26),
    color: '#60636B',
    fontWeight: '400',
    textAlign: 'center',
    marginHorizontal: 24 * widthRatio,
    marginVertical: 24 * widthRatio,
  },
  content: {
    fontSize: setSpText(26),
    color: '#60636B',
    fontWeight: '400',
    textAlign: 'left',
    marginHorizontal: 24 * widthRatio,
    marginVertical: 24 * widthRatio,
  },
  footerContent: {
    paddingHorizontal: 0,
    paddingVertical: 0,
  },
  submitBtnStyle: {
    marginHorizontal: 0,
    height: scaleSize(96),
    color: '#FFFFFF',
    borderTopWidth: 1 * widthRatio,
    borderTopColor: '#D6D6D6',
  },
});
