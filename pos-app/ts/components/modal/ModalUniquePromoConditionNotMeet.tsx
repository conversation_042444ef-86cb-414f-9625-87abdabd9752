import { useNavigation, useRoute } from '@react-navigation/native';
import { get } from 'lodash';
import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { clearSelectedUniquePromo } from '../../actions';
import { currentThemes, IsIOS, scaleSizeH, scaleSizeW, t } from '../../constants';
import { selectTransactionSessionTotal, selectUniquePromos, selectUniquePromosError } from '../../sagas/selector';
import { mapErrorCodeToMessage } from '../../utils/errorMapping';
import { ModalContainer, SubmitButton, SubmitFooter } from '../common';
import { IconError } from '../ui';

interface Props {
  onSubmitHandler?: () => void;
  needToChooseAnotherPromo?: boolean;
}

const ModalUniquePromoConditionNotMeet: React.FC<Props> = props => {
  const navigation = useNavigation<any>();
  const route = useRoute<any>();
  const uniquePromoError = useSelector(selectUniquePromosError);
  // const transactionSessionJs = useSelector(selectTransactionSessionJs);
  const transactionSessionTotal = useSelector(selectTransactionSessionTotal);
  const dispatch = useDispatch();
  const uniquePromos = useSelector(selectUniquePromos);

  const routeParams = route.params || {};
  const needToChooseAnotherPromo = routeParams.needToChooseAnotherPromo || props.needToChooseAnotherPromo;

  const closeModalHandler = () => {
    navigation.goBack();
  };

  const okHandler = () => {
    closeModalHandler();
    dispatch(clearSelectedUniquePromo());
  };

  const onCancelHandler = () => {
    closeModalHandler();
    dispatch(clearSelectedUniquePromo());
  };

  const onChooseAnotherPromoHandler = () => {
    closeModalHandler();
    requestAnimationFrame(() => {
      navigation.navigate('ModalSelectUniquePromo', {
        promoData: uniquePromos,
        selectedUniquePromoId: '',
      });
    });
  };

  const renderContent = () => {
    let code = 'undefined code';
    let message = 'undefined message';
    if (Array.isArray(uniquePromoError) && uniquePromoError.length > 0) {
      const firstError = uniquePromoError[0];
      if (firstError) {
        code = get(firstError, 'code', '');
        message = mapErrorCodeToMessage(code) || get(firstError, 'message', '');
      }
    }

    if (transactionSessionTotal === 0) {
      code = '0000';
      message = t('The product price cannot be 0 when using Unique Promotion');
    }

    return (
      <View style={styles.container}>
        <View style={[styles.textContent]}>
          <View style={styles.titleContainer}>
            <View style={styles.titleIconContainer}>
              <IconError width={scaleSizeW(40)} height={scaleSizeW(40)} />
            </View>
            <Text style={[styles.title]} numberOfLines={2} ellipsizeMode='tail'>
              {t('Promo is not applicable')}
            </Text>
          </View>
          <Text testID='Info' style={[styles.txtInfo]}>
            {message} #{code}
          </Text>
        </View>

        <SubmitFooter style={styles.footerContent}>
          {needToChooseAnotherPromo ? (
            <>
              <SubmitButton
                accessibilityLabel='al_modalinfo_cancel'
                style={[styles.submitBtnStyle, { borderTopWidth: 1, borderTopColor: '#D6D6D6', backgroundColor: null }]}
                onPress={onCancelHandler}
                textStyle={{ color: '#393939', fontSize: currentThemes.fontSize18, fontWeight: 'bold' }}
              >
                {t('Cancel')}
              </SubmitButton>
              <SubmitButton
                accessibilityLabel='al_modalinfo_ok'
                style={styles.submitBtnStyle}
                onPress={onChooseAnotherPromoHandler}
                textStyle={{ fontSize: currentThemes.fontSize18, fontWeight: 'bold' }}
              >
                {t('Choose Another Promo')}
              </SubmitButton>
            </>
          ) : (
            <SubmitButton
              accessibilityLabel='al_modalinfo_ok'
              style={styles.submitBtnStyle}
              onPress={okHandler}
              textStyle={{ fontSize: currentThemes.fontSize18, fontWeight: 'bold' }}
            >
              {t('OK')}
            </SubmitButton>
          )}
        </SubmitFooter>
      </View>
    );
  };

  return (
    <ModalContainer
      onCloseHandler={closeModalHandler}
      contentStyle={styles.content}
      mainContentContainerStyle={styles.mainContentContainerStyle}
      hiddenHeader={true}
      clickOutsideClose={false}
    >
      {renderContent()}
    </ModalContainer>
  );
};

const styles = StyleSheet.create({
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: scaleSizeH(16),
  },
  titleIconContainer: {
    marginRight: scaleSizeW(12),
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    alignItems: 'center',
    justifyContent: 'space-between',
    height: '100%',
  },
  content: {
    width: IsIOS ? scaleSizeW(900) : scaleSizeW(720),
    height: undefined,
    backgroundColor: '#FFF',
    overflow: 'hidden',
  },
  textContent: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'center',
    textAlignVertical: 'center',
    paddingBottom: scaleSizeH(12),
  },
  title: {
    fontSize: currentThemes.fontSize28,
    color: '#303030',
    fontWeight: '500',
    textAlign: 'center',
    minHeight: scaleSizeH(52),
    textAlignVertical: 'center',
    paddingVertical: scaleSizeH(10),
  },
  txtInfo: {
    fontSize: currentThemes.fontSize24,
    color: '#60636B',
    fontWeight: '400',
    textAlign: 'center',
    marginBottom: scaleSizeH(12),
    minHeight: IsIOS ? scaleSizeH(70) : scaleSizeH(120),
    paddingHorizontal: scaleSizeW(60),
    paddingVertical: scaleSizeH(20),
    textAlignVertical: 'center',
  },
  footerContent: {
    paddingHorizontal: 0,
    paddingVertical: 0,
  },
  submitBtnStyle: {
    marginHorizontal: 0,
    height: IsIOS ? scaleSizeH(65) : scaleSizeH(80),
  },
  mainContentContainerStyle: {
    width: '100%',
    minHeight: scaleSizeH(180),
  },
});

export default ModalUniquePromoConditionNotMeet;
