import React, { FC } from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { IsIOS, currentThemes, scaleSizeH, scaleSizeW } from '../../constants';
import { AllSettingTabs } from '../../constants/settingTabs';
import { navigateToSettings } from '../../navigation/commonNavigate';
import { testProps } from '../../utils';
import TextUnderline from '../common/TextUnderline';
import { ExclamationRed, MaterialCancel } from '../ui';

const errorIconSize = IsIOS ? scaleSizeW(28) : scaleSizeW(24);

export type NcsNotificationType = { text: string; subText?: string; onClose: () => void };
export const NcsNotification: FC<NcsNotificationType> = ({ text, subText, onClose }) => {
  const goToSetting = () => {
    onClose();
    navigateToSettings({ tabName: AllSettingTabs.NumberCallingSystem, timeStamp: Date.now() });
  };

  return (
    <View style={styles.flashMsgContent}>
      <ExclamationRed width={errorIconSize} height={errorIconSize} />
      <Text style={styles.errorText}>{text}</Text>
      {Boolean(subText) && <TextUnderline onPress={goToSetting}>{subText}</TextUnderline>}
      <TouchableOpacity
        {...testProps('al_btn_576')}
        activeOpacity={1}
        hitSlop={{ left: scaleSizeW(28), right: scaleSizeW(28), top: scaleSizeH(10) }}
        onPress={onClose}
      >
        <MaterialCancel style={styles.closeButton} />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  flashMsgContent: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: scaleSizeW(24),
    backgroundColor: '#FFEAEA',
    height: scaleSizeH(64),
    borderRadius: scaleSizeW(8),
    marginTop: scaleSizeH(10),
  },

  errorText: {
    marginLeft: scaleSizeW(12),
    color: '#EB4646',
    fontSize: currentThemes.fontSize16,
    fontWeight: '400',
  },
  closeButton: {
    marginLeft: scaleSizeW(30),
  },
});
