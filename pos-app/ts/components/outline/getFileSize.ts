import RNFS from 'react-native-fs';

export async function getStorageDistribution(root: string) {
  const rootRecord = new Map<string, string>();
  let total = 0;
  if (!root || !(await RNFS.exists(root))) {
    return [rootRecord, total.toString()];
  }
  const rootFolders = await RNFS.readdir(root);
  if (typeof rootFolders !== 'object') return;
  await Promise.all(
    rootFolders.map(async directoryItem => {
      console.log('getStorageDistribution files ' + directoryItem);
      return getFileSize(root + '/' + directoryItem).then(result => {
        total += result.size / 1024 / 1024;
        rootRecord[directoryItem] = (result.size / 1024 / 1024).toString();
      });
    })
  );
  console.log('getStorageDistribution ' + JSON.stringify(rootRecord));
  return [rootRecord, total.toString()];
}

export async function getFileSize(path: string, options = {}) {
  let folderSize = 0;
  const errors = [];
  if (!path) {
    return {
      size: folderSize,
      errors: errors.length > 0 ? errors : null,
    };
  }
  async function processItem(subPath: string) {
    const stats = await RNFS.stat(subPath).catch(error => errors.push(error));
    if (typeof stats !== 'object') return;
    folderSize += stats.size;
    if (stats.isDirectory()) {
      const directoryItems = await RNFS.readdir(subPath).catch(error => errors.push(error));
      if (typeof directoryItems !== 'object') return;
      await Promise.all(
        directoryItems.map(directoryItem => {
          return processItem(subPath + '/' + directoryItem);
        })
      );
    }
  }

  await processItem(path);
  // console.log('folderSize: ' + folderSize / 1024 / 1024 + ' MB');

  return {
    size: folderSize,
    errors: errors.length > 0 ? errors : null,
  };
}
