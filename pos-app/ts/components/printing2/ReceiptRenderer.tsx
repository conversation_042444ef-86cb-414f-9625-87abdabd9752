import { PixelRatio, StyleSheet, Text, View } from 'react-native';
import React, { memo, useRef } from 'react';
import { KitchenDocketSample } from './template/TestKitchenDocket';
import ViewShot from 'react-native-view-shot';
import { LayoutChangeEvent } from 'react-native/Libraries/Types/CoreEventTypes';
import { useDispatch, useSelector } from 'react-redux';
import { selectRenderingReceipt } from '../../sagas/printing2';
import { finishReceiptRendering } from '../../actions';
import RNFS from 'react-native-fs';
import { OrderedSet } from 'immutable';

export const ReceiptRenderer = () => {
  // @ts-ignore
  const receiptIds: OrderedSet<string> = useSelector(selectRenderingReceipt);
  const receiptId = receiptIds.first();

  console.log('Printing2', 'receiptIds', receiptIds);
  return <ReceiptRendererInternal receiptId={receiptId}></ReceiptRendererInternal>;
};

export const ReceiptRendererInternal = memo((props: { receiptId: string }) => {
  const dispatch = useDispatch();
  const receiptId = props.receiptId;

  const scaledWith = 480 / PixelRatio.get();

  console.log('Printing2', 'receiptId 000', receiptId);
  return (
    <View style={{ flex: 1, position: 'absolute', start: -scaledWith }}>
      <View style={{ flexDirection: 'row', columnGap: 16, flex: 1, width: scaledWith }}>
        {Boolean(receiptId) && (
          <ViewShot
            onCapture={uri => {
              if (!receiptId) return;
              // await RNFS.moveFile(uri, 'saved_' + uri);

              RNFS.stat(uri).then(fileStat => {
                console.log('Printing2 onCapture ', receiptId, fileStat.size);
                // if (fileStat.size != 59824) {
                //   console.error('Printing2 failed');
                // }
              });

              dispatch(
                finishReceiptRendering({
                  receiptId: receiptId,
                  receiptPreview: uri,
                })
              );
            }}
            onCaptureFailure={err => {
              console.error(err);
              dispatch(
                finishReceiptRendering({
                  receiptId: receiptId,
                  receiptPreview: null,
                })
              );
            }}
            style={{ width: scaledWith, backgroundColor: '#ffffff' }}
            options={{ width: scaledWith, fileName: receiptId + '_' }}
            captureMode={receiptId ? 'mount' : null}
            onLayout={(event: LayoutChangeEvent) => {
              console.log('Printing2 onLayout', event.type);
            }}
          >
            <Text style={{ fontSize: 1 }}>{receiptId}</Text>
            {Boolean(receiptId) && <KitchenDocketSample receiptId={receiptId} />}
          </ViewShot>
        )}
      </View>
      <View style={{ flex: 1, backgroundColor: 'grey' }}></View>
      <View style={{ flexDirection: 'row' }}></View>
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    flex: 1,
    flexDirection: 'row',
  },
  leftPanel: {
    flex: 1,
    padding: 16,
  },
  rightPanel: {
    flex: 2,
    padding: 16,
    backgroundColor: '#f8f8f8',
  },
  divider: {
    width: 1,
    backgroundColor: '#E0E0E0',
  },
  buttonGroup: {
    marginBottom: 24,
  },
  groupTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    color: '#333',
  },
  testButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
    padding: 12,
    borderRadius: 10,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  testButtonSelected: {
    backgroundColor: '#e8f0fe',
    borderColor: '#007AFF',
  },
  testButtonTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
    borderRadius: 8,
    marginBottom: 4,
    borderWidth: 1,
    padding: 2,
    borderColor: '#e0e0e0',
  },
  testButtonTagSelected: {
    backgroundColor: '#e8f0fe',
    borderColor: '#007AFF',
  },
  buttonIcon: {
    color: '#666',
    marginRight: 8,
  },
  buttonText: {
    color: '#333',
    fontSize: 15,
    fontWeight: '500',
    paddingRight: 20,
  },
  buttonTextSelected: {
    color: '#007AFF',
  },
  buttonTextSecondary: {
    color: '#969696',
    fontSize: 15,
    fontWeight: '500',
    paddingRight: 20,
  },
  buttonTextSecondarySelected: {
    color: '#007AFF',
  },
  emptyStatus: {
    flex: 2,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f8f8f8',
  },
  emptyText: {
    marginTop: 16,
    fontSize: 16,
    color: '#999',
  },
  statusGroup: {
    marginBottom: 24,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  statusItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  statusLabel: {
    fontSize: 14,
    color: '#666',
  },
  statusValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  contentWrapper: {
    backgroundColor: '#fff',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.2,
    shadowRadius: 20,
    elevation: 2,
    borderWidth: 1, // Added border
    borderColor: '#e0e0e0', // Border colorad
  },
  contentTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  contentBody: {
    flex: 1,
  },
  description: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
  },
  optionGroup: {
    marginBottom: 20,
  },
  optionLabel: {
    fontSize: 14,
    color: '#333',
    marginBottom: 8,
  },
  radioGroup: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  radioButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 20,
  },
  radio: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#007AFF',
    marginRight: 8,
    padding: 2,
  },
  radioSelected: {
    backgroundColor: '#007AFF',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 6,
    padding: 8,
    fontSize: 14,
    width: 200,
  },
  rangeInputs: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rangeInput: {
    width: 100,
  },
  rangeSeparator: {
    marginHorizontal: 10,
  },
  developingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  developingTitle: {
    fontSize: 18,
    color: '#666',
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  developingDesc: {
    fontSize: 14,
    color: '#999',
  },
  groupContainer: {
    flex: 1,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#d1d1d1',
    backgroundColor: '#e8e8e8',
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  groupHeader: { fontSize: 16, color: '#555' },
  groupContent: { fontSize: 28, color: '#111' },
});
