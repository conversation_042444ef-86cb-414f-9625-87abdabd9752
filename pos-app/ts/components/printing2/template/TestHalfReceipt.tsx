import React from 'react';
import { View, Text, StyleSheet, FlatList } from 'react-native';

interface PurchasedItem {
  quantityStr: string;
  title: string;
  price: string;
  total: string;
  discountAmount?: string;
  taxAmount?: string;
  notes?: string;
}

interface SubOrder {
  purchasedItems: PurchasedItem[];
  subtotal: string;
  tax: string;
  discount?: string;
  total: string;
  note?: string;
}

interface ReceiptProps {
  reprintTitle?: string;
  isPrinterPaperWidth58: boolean;
  storeName: string;
  storeAddress?: string;
  storePhone?: string;
  receiptNumber: string;
  orderNumber?: string;
  orderType?: string;
  employeeName?: string;
  customerName?: string;
  receiptDate: string;
  paymentMethod: string;
  subOrders: SubOrder[];
  grandTotal: string;
  footer?: string;
}

const sampleData: ReceiptProps = {
  reprintTitle: 'Reprint',
  isPrinterPaperWidth58: false,
  storeName: 'Sample Store',
  storeAddress: '123 Sample Street',
  storePhone: '************',
  receiptNumber: 'R12345',
  orderNumber: 'O12345',
  orderType: 'Dine-In',
  employeeName: '<PERSON>',
  customerName: '<PERSON>',
  receiptDate: '2023-10-01 14:30',
  paymentMethod: 'Cash',
  subOrders: [
    {
      purchasedItems: [
        {
          quantityStr: '2',
          title: 'Burger',
          price: '10.00',
          total: '20.00',
          discountAmount: '2.00',
          taxAmount: '1.80',
          notes: 'No onions',
        },
      ],
      subtotal: '20.00',
      tax: '1.80',
      discount: '2.00',
      total: '19.80',
      note: 'Special request: Extra cheese',
    },
  ],
  grandTotal: '19.80',
  footer: 'Thank you for your purchase!',
};

export const AlternateReceipt: React.FC<ReceiptProps> = props => {
  return (
    <View style={{ padding: 10, backgroundColor: '#fff' }}>
      <Text style={{ fontSize: 20, fontWeight: 'bold', textAlign: 'center' }}>{props.storeName}</Text>
      <Text style={{ textAlign: 'center' }}>{props.receiptDate}</Text>
      <Text>Receipt #: {props.receiptNumber}</Text>
      {props.orderNumber && <Text>Order #: {props.orderNumber}</Text>}
      <Text>Employee: {props.employeeName}</Text>

      <View style={{ marginTop: 10 }}>
        {props.subOrders.map((suborder, index) => (
          <View key={index}>
            {suborder.purchasedItems.map((item, i) => (
              <View key={i} style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                <Text>
                  {item.quantityStr} x {item.title}
                </Text>
                <Text>{item.total}</Text>
              </View>
            ))}
            <Text>Subtotal: {suborder.subtotal}</Text>
            {suborder.discount && <Text>Discount: -{suborder.discount}</Text>}
            <Text>Tax: {suborder.tax}</Text>
            <Text>Total: {suborder.total}</Text>
          </View>
        ))}
      </View>

      <Text style={{ fontWeight: 'bold', marginTop: 10 }}>Grand Total: {props.grandTotal}</Text>
      <Text>Paid with: {props.paymentMethod}</Text>
      {props.footer && <Text style={{ marginTop: 10, textAlign: 'center' }}>{props.footer}</Text>}
    </View>
  );
};

export const TestReceipt: React.FC<ReceiptProps> = ({
  reprintTitle,
  isPrinterPaperWidth58,
  storeName,
  storeAddress,
  storePhone,
  receiptNumber,
  orderNumber,
  orderType,
  employeeName,
  customerName,
  receiptDate,
  paymentMethod,
  subOrders,
  grandTotal,
  footer,
}) => {
  const styles = StyleSheet.create({
    main: {
      padding: 8,
      fontSize: isPrinterPaperWidth58 ? 30 : 22.3,
    },
    reprintTitle: {
      backgroundColor: '#000',
      color: '#fff',
      padding: 2,
      textAlign: 'center',
      fontSize: isPrinterPaperWidth58 ? 54 : 22.3,
    },
    baseInfoItem: {
      fontSize: isPrinterPaperWidth58 ? 30 : 22.3,
    },
    textCenter: {
      textAlign: 'center',
    },
    textLeft: {
      textAlign: 'left',
    },
    textRight: {
      textAlign: 'right',
    },
    storeName: {
      fontSize: isPrinterPaperWidth58 ? 54 : 44,
      textAlign: 'center',
      fontWeight: 'bold',
      marginBottom: 10,
    },
    storeInfo: {
      fontSize: isPrinterPaperWidth58 ? 30 : 22.3,
      textAlign: 'center',
      marginBottom: 5,
    },
    receiptInfo: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 10,
    },
    purchasedTable: {
      width: '100%',
      marginTop: 20,
    },
    purchasedTableRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 5,
    },
    itemQuantity: {
      width: '10%',
    },
    itemTitle: {
      width: '50%',
    },
    itemPrice: {
      width: '20%',
      textAlign: 'right',
    },
    itemTotal: {
      width: '20%',
      textAlign: 'right',
    },
    totalSection: {
      marginTop: 20,
      borderTopWidth: 1,
      borderTopColor: '#000',
      paddingTop: 10,
    },
    totalRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 5,
    },
    grandTotal: {
      fontWeight: 'bold',
      fontSize: isPrinterPaperWidth58 ? 36 : 26,
    },
    footer: {
      marginTop: 30,
      textAlign: 'center',
      fontSize: isPrinterPaperWidth58 ? 30 : 22.3,
    },
    divider: {
      height: 1,
      backgroundColor: '#000',
      marginVertical: 10,
    },
  });

  return (
    <View style={styles.main}>
      {reprintTitle && (
        <View>
          <Text style={styles.reprintTitle}>{reprintTitle}</Text>
        </View>
      )}

      <Text style={styles.storeName}>{storeName}</Text>
      {storeAddress && <Text style={styles.storeInfo}>{storeAddress}</Text>}
      {storePhone && <Text style={styles.storeInfo}>{storePhone}</Text>}

      <View style={styles.divider} />

      <View style={styles.receiptInfo}>
        <Text style={styles.baseInfoItem}>Receipt #: {receiptNumber}</Text>
        <Text style={styles.baseInfoItem}>{receiptDate}</Text>
      </View>

      {orderNumber && (
        <View style={styles.receiptInfo}>
          <Text style={styles.baseInfoItem}>Order #: {orderNumber}</Text>
          {orderType && <Text style={styles.baseInfoItem}>{orderType}</Text>}
        </View>
      )}

      {employeeName && <Text style={styles.baseInfoItem}>Server: {employeeName}</Text>}
      {customerName && <Text style={styles.baseInfoItem}>Customer: {customerName}</Text>}

      <View style={styles.divider} />

      {subOrders.map((suborder, index) => (
        <View key={index} style={styles.purchasedTable}>
          {suborder.purchasedItems.map((item, itemIndex) => (
            <View key={itemIndex} style={styles.purchasedTableRow}>
              <Text style={[styles.baseInfoItem, styles.itemQuantity]}>{item.quantityStr}</Text>
              <Text style={[styles.baseInfoItem, styles.itemTitle]}>{item.title}</Text>
              <Text style={[styles.baseInfoItem, styles.itemPrice]}>{item.price}</Text>
              <Text style={[styles.baseInfoItem, styles.itemTotal]}>{item.total}</Text>
            </View>
          ))}

          <View style={styles.totalSection}>
            <View style={styles.totalRow}>
              <Text style={styles.baseInfoItem}>Subtotal:</Text>
              <Text style={styles.baseInfoItem}>{suborder.subtotal}</Text>
            </View>
            {suborder.discount && (
              <View style={styles.totalRow}>
                <Text style={styles.baseInfoItem}>Discount:</Text>
                <Text style={styles.baseInfoItem}>-{suborder.discount}</Text>
              </View>
            )}
            <View style={styles.totalRow}>
              <Text style={styles.baseInfoItem}>Tax:</Text>
              <Text style={styles.baseInfoItem}>{suborder.tax}</Text>
            </View>
          </View>
        </View>
      ))}

      <View style={styles.totalSection}>
        <View style={styles.totalRow}>
          <Text style={styles.grandTotal}>TOTAL:</Text>
          <Text style={styles.grandTotal}>{grandTotal}</Text>
        </View>
        <View style={styles.totalRow}>
          <Text style={styles.baseInfoItem}>Payment Method:</Text>
          <Text style={styles.baseInfoItem}>{paymentMethod}</Text>
        </View>
      </View>

      {footer && <Text style={styles.footer}>{footer}</Text>}
    </View>
  );
};

export const ReceiptSample = (props: { receiptId: string }) => {
  return <TestReceipt {...sampleData} />;
};
