import React from 'react';
import { View, Text, StyleSheet, FlatList } from 'react-native';

interface PurchasedItem {
  quantityStr: string;
  title: string;
  isQuantityPositive: boolean;
  kitchenDocketVariantIsMultipleLine: boolean;
  notes: string;
  notesTitle: string;
}

interface SubOrder {
  purchasedItems: PurchasedItem[];
  takeawayItems: any[]; // Replace 'any' with a specific type if needed
  note: string;
  noteTitle: string;
}

interface KitchenDocketProps {
  reprintTitle?: string;
  isPrinterPaperWidth58: boolean;
  docketCountString?: string;
  expirationDateString?: string;
  docketTitle?: string;
  orderNumber?: string;
  orderNumberTitle?: string;
  previousTableId?: string;
  orderTypeName?: string;
  extraOrderNumber?: string;
  employeeName?: string;
  pax?: string;
  receiptDate?: string;
  subOrders?: SubOrder[];
}

const sampleData: KitchenDocketProps = {
  reprintTitle: 'Reprint',
  isPrinterPaperWidth58: false,
  docketCountString: 'Docket Count: 5',
  expirationDateString: 'Expires: 2023-12-31',
  docketTitle: 'Order Details',
  orderNumber: '12345',
  orderNumberTitle: 'Order Number',
  previousTableId: '67890',
  orderTypeName: 'Dine-In',
  extraOrderNumber: 'Extra Order: 98765',
  employeeName: 'John Doe',
  pax: 'Pax: 4',
  receiptDate: '2023-10-01',
  subOrders: [
    {
      purchasedItems: Array.from({ length: 10 }).map((_, index) => {
        return {
          quantityStr: '2',
          title: 'Burger',
          isQuantityPositive: true,
          kitchenDocketVariantIsMultipleLine: false,
          notes: 'No onions',
          notesTitle: 'Notes:',
        };
      }),
      takeawayItems: [],
      note: 'Special request: Extra cheese',
      noteTitle: 'Note:',
    },
  ],
};

export const TestKitchenDocket: React.FC<KitchenDocketProps> = ({
  reprintTitle,
  isPrinterPaperWidth58,
  docketCountString,
  expirationDateString,
  docketTitle,
  orderNumber,
  orderNumberTitle,
  previousTableId,
  orderTypeName,
  extraOrderNumber,
  employeeName,
  pax,
  receiptDate,
  subOrders,
}) => {
  const styles = StyleSheet.create({
    main: {
      padding: 8,
      fontSize: isPrinterPaperWidth58 ? 30 : 22.3,
    },
    reprintTitle: {
      backgroundColor: '#000',
      color: '#fff',
      padding: 2,
      textAlign: 'center',
      fontSize: isPrinterPaperWidth58 ? 54 : 22.3,
    },
    beerdocketsetting: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
    },
    baseInfoItem: {
      fontSize: isPrinterPaperWidth58 ? 30 : 22.3,
    },
    textCenter: {
      textAlign: 'center',
    },
    textLeft: {
      textAlign: 'left',
    },
    textRight: {
      textAlign: 'right',
    },
    storeName: {
      fontSize: isPrinterPaperWidth58 ? 54 : 44,
      textAlign: 'center',
    },
    orderNumberContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'center',
    },
    orderNumber: {
      fontSize: isPrinterPaperWidth58 ? 64 : 44,
      textAlign: 'center',
      margin: 5,
      padding: 10,
    },
    lineThrough: {
      position: 'relative',
    },
    lineThroughAfter: {
      position: 'absolute',
      left: -20,
      right: -20,
      top: 25,
      height: 2,
      backgroundColor: 'black',
    },
    purchasedTable: {
      width: '100%',
      paddingTop: 0,
    },
    purchasedTableRow: {
      flexDirection: 'row',
    },
    purchasedTableItem: {
      width: isPrinterPaperWidth58 ? '25%' : '20%',
      justifyContent: 'flex-start',
    },
    purchasedTableItemText: {
      fontSize: isPrinterPaperWidth58 ? 36 : 22.3,
      fontWeight: 'bold',
    },
    notesInfo: {
      fontSize: isPrinterPaperWidth58 ? 30 : 20,
      fontStyle: 'italic',
      paddingLeft: 20,
    },
    horizontalDivider: {
      height: 2,
      backgroundColor: '#000',
      marginTop: 25,
      marginBottom: 40,
    },
    bottomBlank: {
      height: 100,
    },
    pt15: {
      paddingTop: 15,
    },
    pb20: {
      paddingBottom: 20,
    },
    shortenBottomMargin: {
      marginBottom: 10,
    },
    titleFont: {
      fontWeight: 'bold',
    },
    normalFont: {
      fontWeight: 'normal',
    },
    baseInfoWrapper: {
      marginBottom: 10,
    },
    lineWithArrow: {
      height: 2,
      backgroundColor: 'black',
      marginHorizontal: 10,
    },
    orderNumberTitleFont: {
      fontSize: isPrinterPaperWidth58 ? 54 : 44,
      fontWeight: 'bold',
      textAlign: 'center',
    },
  });

  return (
    <View style={styles.main}>
      {reprintTitle && (
        <View style={styles.pt15}>
          <Text style={styles.reprintTitle}>{reprintTitle}</Text>
        </View>
      )}
      {docketCountString && (
        <View style={styles.beerdocketsetting}>
          <Text style={[styles.baseInfoItem, styles.textLeft]}>{expirationDateString}</Text>
          <Text style={[styles.baseInfoItem, styles.textRight]}>{docketCountString}</Text>
        </View>
      )}
      {docketTitle && (
        <View style={styles.pb20}>
          <Text style={[styles.textCenter, styles.orderNumberTitleFont]}>{docketTitle}</Text>
        </View>
      )}
      {orderNumber && (
        <View style={styles.shortenBottomMargin}>
          <Text style={[styles.storeName, styles.textCenter]}>{orderNumberTitle}</Text>
          <View style={styles.orderNumberContainer}>
            {previousTableId && (
              <>
                <Text style={[styles.storeName, styles.orderNumber, styles.lineThrough]}>{previousTableId}</Text>
                <View style={styles.lineWithArrow} />
              </>
            )}
            <Text style={[styles.storeName, styles.orderNumber]}>{orderNumber}</Text>
          </View>
        </View>
      )}
      {orderTypeName && (
        <Text style={styles.baseInfoItem}>
          <Text style={styles.titleFont}>{orderTypeName}</Text>
        </Text>
      )}
      {extraOrderNumber && <Text style={styles.baseInfoItem}>{extraOrderNumber}</Text>}
      {employeeName && <Text style={styles.baseInfoItem}>{employeeName}</Text>}
      {pax && <Text style={styles.baseInfoItem}>{pax}</Text>}
      {receiptDate && <Text style={styles.baseInfoItem}>{receiptDate}</Text>}
      {subOrders &&
        subOrders.map(suborder => {
          return (
            <View>
              {suborder.purchasedItems &&
                suborder.purchasedItems.map(item => (
                  <View style={styles.purchasedTable}>
                    <View style={styles.purchasedTableRow}>
                      <View style={styles.purchasedTableItem}>
                        <Text style={styles.purchasedTableItemText}>{item.quantityStr}</Text>
                      </View>
                      <View style={styles.purchasedTableItem}>
                        <Text style={styles.purchasedTableItemText}>{item.title}</Text>
                      </View>
                    </View>
                    {item.notes && (
                      <View style={styles.purchasedTableRow}>
                        <View style={styles.purchasedTableItem}>
                          <Text style={styles.purchasedTableItemText}>{item.notesTitle}</Text>
                        </View>
                        <View style={styles.purchasedTableItem}>
                          <Text style={styles.notesInfo}>{item.notes}</Text>
                        </View>
                      </View>
                    )}
                  </View>
                ))}
              {suborder.note && (
                <View style={styles.baseInfoWrapper}>
                  <Text style={styles.purchasedTableItemText}>
                    <Text style={styles.normalFont}>{suborder.noteTitle}</Text>
                  </Text>
                  <Text style={styles.normalFont}>{suborder.note}</Text>
                </View>
              )}
            </View>
          );
        })}
      {isPrinterPaperWidth58 && <View style={styles.bottomBlank} />}
    </View>
  );
};

export const KitchenDocketSample = (props: { receiptId: string }) => {
  return <TestKitchenDocket {...sampleData} reprintTitle={props.receiptId} />;
};
