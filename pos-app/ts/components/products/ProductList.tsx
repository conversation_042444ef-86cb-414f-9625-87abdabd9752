import React, { memo, useCallback, useState } from 'react';
import { FlashList } from '@shopify/flash-list';
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { CommonColors, currentThemes, scaleSizeH, scaleSizeW } from '../../constants';
import { ProductType } from '../../typings';
import RNFS from 'react-native-fs';
import { getSimplifiedStr } from '../../utils/string';
import { localeNumber, testProps } from '../../utils';
import DAL from '../../dal';
import { IconFilledPhoto } from '../ui';
import { useSelector } from 'react-redux';
import { selectSyncInfoLastProductImagesSyncProgress } from '../../sagas/selector';
import { useAsyncEffect } from 'ahooks';

interface ProductListProps {
  dataList: ProductType[];

  onItemPressHandler(item): void;

  currentItem?: ProductType;

  onLoadMoreData?(): void;
}

export const checkItemValid = (item: ProductType) => {
  return item && item.isValid();
};

const ProductList: React.FC<ProductListProps> = ({ dataList, currentItem, onLoadMoreData, onItemPressHandler }) => {
  const handleItemPress = useCallback(
    (productId: string) => {
      const product = DAL.getProductById(productId);
      onItemPressHandler?.(product);
    },
    [onItemPressHandler]
  );

  const renderProductItem = useCallback(
    item => {
      if (!checkItemValid(item)) {
        return null;
      }

      const isSelected = currentItem?.productId === item.productId;
      const { productId, title, barcode, unitPrice } = item;
      const price = String(unitPrice ? localeNumber(unitPrice) : '0.00');

      return (
        <ProductListItem productId={productId} title={title} barcode={barcode} price={price} isSelected={isSelected} onItemPressHandler={handleItemPress} />
      );
    },
    [currentItem, handleItemPress]
  );

  const renderProductHeaderItem = useCallback(({ title }) => {
    return <ProductHeaderItem title={title} />;
  }, []);

  const renderItem = useCallback(
    ({ item }) => {
      return item.isHeader ? renderProductHeaderItem(item) : renderProductItem(item);
    },
    [renderProductHeaderItem, renderProductItem]
  );

  const renderSeparator = useCallback(() => <View style={styles.itemSeparator} />, []);

  const keyExtractor = useCallback((item, index) => {
    if (item.isHeader || !checkItemValid(item)) return index;
    return `${item.title}_${item.productId}_${index}`;
  }, []);

  return (
    <FlashList
      style={{ flex: 1, backgroundColor: 'red' }}
      data={dataList}
      keyExtractor={keyExtractor}
      renderItem={renderItem}
      ItemSeparatorComponent={renderSeparator}
      onEndReached={onLoadMoreData}
      onEndReachedThreshold={0.5}
      extraData={currentItem}
    />
  );
};

interface ProductHeaderItemProps {
  title: string;
}

export const ProductHeaderItem = memo(({ title }: ProductHeaderItemProps) => {
  return (
    <View style={styles.sectionHeader}>
      <Text style={{ fontWeight: '500', fontSize: currentThemes.fontSize24, color: '#303030' }}>{title}</Text>
    </View>
  );
});

interface ProductListItemProps {
  isSelected: boolean;
  onItemPressHandler: (productId: string) => void;
  title: string;
  productId: string;
  barcode: string;
  price: string | number;
}

const ProductListItem: React.FC<ProductListItemProps> = ({ isSelected, onItemPressHandler, title, productId, barcode, price }) => {
  const [thumbnail, setThumbnail] = useState('');

  const lastProductImagesSyncProgress = useSelector(selectSyncInfoLastProductImagesSyncProgress);

  useAsyncEffect(async () => {
    if (!productId) {
      return;
    }

    const product = DAL.getProductById(productId);

    if (!product || !product.hasThumbnail) {
      setThumbnail('');
      return;
    }

    const imagePath = RNFS.DocumentDirectoryPath + `/${productId}.png`;

    try {
      const exists = await RNFS.exists(imagePath);
      if (exists) {
        const hash = await RNFS.stat(imagePath);
        setThumbnail('file://' + imagePath + '?mtime=' + hash.mtime);
      } else {
        setThumbnail('');
      }
    } catch (err) {
      console.error('loadImage error:', err);
      setThumbnail('');
    }
  }, [productId, lastProductImagesSyncProgress]);

  const handlePress = useCallback(() => {
    onItemPressHandler?.(productId);
  }, [onItemPressHandler, productId]);

  return (
    <TouchableOpacity
      testID={'ProductItem'}
      style={{
        ...StyleSheet.flatten(styles.itemContainer),
        backgroundColor: isSelected ? CommonColors.Pumpkin : 'white',
      }}
      onPress={handlePress}
      {...testProps('al_productList_item')}
    >
      {thumbnail ? (
        <Image key={`image_${productId}`} source={{ uri: thumbnail }} style={{ width: scaleSizeH(104), height: scaleSizeH(104) }} resizeMode={'cover'} />
      ) : (
        <View style={styles.itemImage}>
          <IconFilledPhoto color={CommonColors.Icon} width={scaleSizeW(48)} height={scaleSizeH(48)} />
        </View>
      )}
      <View style={styles.itemRightContent}>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text style={{ ...StyleSheet.flatten(styles.itemProductTitle), color: isSelected ? 'white' : '#60636B' }}>
            {getSimplifiedStr(title, scaleSizeW(40), true, true)}
          </Text>
          <Text
            style={{
              ...StyleSheet.flatten(styles.itemProductPrice),
              color: isSelected ? 'white' : '#757575',
            }}
          >
            {price}
          </Text>
        </View>
        <Text
          style={{
            ...StyleSheet.flatten(styles.itemProductSubTitle),
            color: isSelected ? 'white' : '#757575',
          }}
        >
          {barcode}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

const ITEM_HEIGHT = scaleSizeH(104);
const styles = StyleSheet.create({
  sectionHeader: {
    backgroundColor: CommonColors.Solitude,
    height: scaleSizeH(40),
    justifyContent: 'center',
    paddingLeft: scaleSizeW(24),
  },
  itemSeparator: {
    flex: 1,
    backgroundColor: CommonColors.Solitude,
    height: scaleSizeH(1),
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    height: ITEM_HEIGHT,
    flex: 1,
  },
  itemImage: {
    width: scaleSizeH(104),
    height: scaleSizeH(104),
    backgroundColor: CommonColors.Solitude,
    alignItems: 'center',
    justifyContent: 'center',
  },
  itemRightContent: {
    flex: 1,
    height: ITEM_HEIGHT,
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    paddingLeft: scaleSizeW(32),
    paddingRight: scaleSizeW(24),
  },
  itemProductTitle: {
    width: '75%',
    fontWeight: '500',
    fontSize: currentThemes.fontSize26,
    color: '#60636B',
  },
  itemProductSubTitle: {
    width: '100%',
    height: scaleSizeH(24),
    color: '#757575',
    fontSize: currentThemes.fontSize18,
    fontWeight: '400',
  },
  itemProductPrice: {
    flex: 1,
    textAlign: 'right',
    color: '#757575',
    fontSize: currentThemes.fontSize26,
    fontWeight: '500',
  },
});

export default ProductList;
