import { get, head, isEmpty, isEqual, map } from 'lodash';
import React, { memo, PureComponent, useEffect, useRef, useState } from 'react';
import { FlatList, Platform, StyleSheet, Text, TouchableOpacity, TouchableWithoutFeedback, View } from 'react-native';
import { SwipeRow } from 'react-native-swipe-list-view';

import { connect, ConnectedProps, useDispatch, useSelector } from 'react-redux';

import { bindActionCreators } from 'redux';
import { clearSelectedUniquePromo, clearTransactionSession, RequestActionAccessResult, updateGeneralSettings, updateOutOfStockStatus } from '../../actions';
import { AppliedVoucherType, fetchUniquePromo, TransactionTypeWithDisplay } from '../../actions/transaction';
import { NAVIGATOR_HEADER_HEIGHT } from '../../components/common/NavigatorHeader';
import { IsIOS, ItemChannelType, SalesChannelType, SharedStyles, STATUS_BAR_HEIGHT, t, TransactionFlowType } from '../../constants';
import { CART_REAL_WIDTH, CART_WIDTH, CommonColors, currentThemes, scaleSizeH, scaleSizeW, setSpText } from '../../constants/themes';
import DAL from '../../dal';
import {
  selectAllowOutOfStockForSalesFlow,
  selectAllowOutOfStockUntil,
  selectGBAllowOutOfStock,
  selectIsNetConnected,
  selectLocalCountryMap,
  selectRoundAllTransactions,
  selectRountTo,
  selectSelectedUniquePromo,
  selectStore,
  selectUniquePromos,
} from '../../sagas/selector';
import { calculateItemsToKitchenOrder, CalculateItemsType } from '../../sagas/transaction/common';
import { PromotionType, RootState } from '../../typings';
import { getLocaleNumberString, getUnNullValue, localeNumber, newConvertCurrencyToSymbol, numericValue, testProps } from '../../utils';

import { useFocusEffect } from '@react-navigation/native';
import { useEmployee } from '../../hooks/business/employee';
import { selectSmMallLimited } from '../../sagas/pageSelector';
import { INVALID_OOS_STATUS_BANNER, INVALID_OOS_STATUS_PENDING, INVALID_OOS_STATUS_POPUP } from '../../sagas/transaction/saleFlow';
import { generatePromotionTitle } from '../../utils/promotion';
import { getSimplifiedStr } from '../../utils/string';
import {
  generateDescriptionString,
  getDisplayItemsCount,
  getPayedDepositAmount,
  getSCDisplayBirDiscountTitle,
  isPreOrderPickUp,
} from '../../utils/transaction';
import { SubmitButton, SubmitFooter } from '../common/SubmitFooter';
import CustomerItem, { CUSTOMER_ITEM_HEIGHT } from '../customer/CustomerItem';
import { ModalMoreAction } from '../modal';
import { ModalErrorParams } from '../modal/ModalError';
import {
  EmptyCart,
  IconAdd,
  IconClock,
  IconClosed,
  IconCollect,
  IconEditLayout,
  IconFilledSort,
  IconMore,
  IconMoveOrder,
  IconNotes,
  IconPrinter,
  IconPromotion,
  IconRemove,
  IconReturn,
  IconSpecialDiscount,
  IconTrash,
} from '../ui';

import IconSelectPromo from '../ui/svgIcons/iconSelectPromo';
import { ItemSalesPerson } from './ItemSalesPerson';
import { DefaultPaymentOptionType } from '../../config/paymentOption';
import { calculatePayAmount, getConditionalRoundingValue } from '../../utils/checkout';

const SEARCH_BAR_HEIGHT = NAVIGATOR_HEADER_HEIGHT;
const DISABLED_COLOR = '#DBDBDB';

interface CartContentProps {
  alreadyCloseZReading: boolean;
  transactionSession: TransactionTypeWithDisplay;
  shiftOpenStatus: boolean;
  birAccredited: boolean;
  currency?: string;
  country: string;
  enableServiceCharge: boolean;
  serviceChargeRate: number;
  serviceChargeTax: number;
  cartHeight: number;
  selectedItemIndex?: number;
  enableOpenOrdersShortCut?: boolean;
  enableCustomerShortCut: boolean;
  isEmpty: boolean;
  isOpenOrder: boolean;
  isBeepEnabled: boolean;
  isBeepPreOrderEnabled: boolean;
  customer: any;
  haveProduct: boolean;
  editing: boolean;
  enableOpenOrders: boolean;
  enableTakeaway: boolean;
  enableTableLayout: boolean;
  enableCashback: boolean;
  enableLoyalty: boolean;

  onFullBillDiscountPressHandler?(): void;

  onServiceChargePressHandler?(): void;

  onSubmitHandler(): void;

  onDeletePurchasedItemHandler(index: number, itemId?: string): void;

  onSaveClicked?(): void;

  onOrdersClicked?(): void;

  onSelectedPurchasedItem(index: number, itemId?: string): void;

  onAddCustomerClick?(): void;

  onRemoveCustomerClick(): void;

  goToOpenOrderLists?(): void;

  onPressDeleteOrder?(): void;

  onPressGoSaveOrder?(): void;

  onPressTakeAway?(): void;

  onPressNewOrder?(): void;

  onPressPrint?(): void;

  onPrintOpenOrder(): void;

  onPressPrintTodayPreOrder?(): void;

  onMenuCustomerClick?(): void;

  onManualRefundHandler?(): void;

  onMenuPressSaveOrder?(): void;

  onCloseShiftHandler?(): void;

  onSpecialDiscountPressHandler?(): void;

  setMenuRef?(ref): void;

  showMenu?(): void;

  onPressEditDone?(): void;

  onPressPreOrder?(): void;

  onPressMoveOrder?(): void;

  onPressCollectPreOrder?(): void;

  shouldRenderDeposit: boolean;
  customerDisabled?: boolean;
  disableCustomerEdit?: boolean;
  displayLoyalty?: boolean;
  displayDiscount?: boolean;
  isEnabledMRS: boolean;
  navigation?: any;
  localCountryMap: any;
}

export const CartContent = (props: CartContentProps) => {
  const allowOutOfStock = useSelector(selectAllowOutOfStockForSalesFlow);
  const roundingTo = useSelector(selectRountTo);
  const roundAllTransactions = useSelector(selectRoundAllTransactions);

  const totalOutOfStockItemsCount = props.transactionSession.totalOutOfStockItemsCount;

  const getCartTitle = () => {
    const {
      transactionSession: { tableId, items, salesChannel },
      enableTakeaway,
      enableTableLayout,
    } = props;
    const totalCount = getDisplayItemsCount(items);
    let title = tableId ? `${tableId} (${totalCount})` : String(totalCount);

    if (enableTakeaway) {
      if (salesChannel === SalesChannelType.TAKEAWAY) {
        title = t('Takeaway') + ' ' + `(${totalCount})`;
      } else {
        if (!enableTableLayout) title = t('Dine-In') + ' ' + `(${totalCount})`;
      }
    }
    return title;
  };

  const dispatch = useDispatch();
  const allowOutOfStockGB = useSelector(selectGBAllowOutOfStock);
  const allowOutOfStockUntil = useSelector(selectAllowOutOfStockUntil);
  const resetOutOfStock = () => {
    if (allowOutOfStockGB && allowOutOfStock) {
      // current txn
      if (allowOutOfStockUntil === -1) {
        dispatch(updateGeneralSettings({ allowOutOfStock: false, allowOutOfStockUntil: 0 }));
      }
    }
  };

  const resetOutOfStockTempAllow = () => {
    if (allowOutOfStockGB && allowOutOfStock) {
      // current txn
      if (allowOutOfStockUntil !== -1 && allowOutOfStockUntil !== 0 && allowOutOfStockUntil < Date.now()) {
        dispatch(updateGeneralSettings({ allowOutOfStock: false, allowOutOfStockUntil: 0 }));
      }
    }
  };

  const isNetConnected = useSelector(selectIsNetConnected);

  useFocusEffect(() => {
    resetOutOfStockTempAllow();
    console.log('resetOutOfStockTempAllow');
  });

  useEffect(() => {
    if (
      isNetConnected &&
      (totalOutOfStockItemsCount === INVALID_OOS_STATUS_POPUP ||
        totalOutOfStockItemsCount === INVALID_OOS_STATUS_BANNER ||
        totalOutOfStockItemsCount === INVALID_OOS_STATUS_PENDING)
    ) {
      dispatch(updateOutOfStockStatus(0));
    }
  }, [isNetConnected]);

  const renderCartHeader = (enableOpenOrders, enableOpenOrdersShortCut, enableCustomerShortCut, isEmpty, canSaveOrders, trashDisabled, titleDisabled) => {
    const {
      country,
      transactionSession,
      shiftOpenStatus,
      goToOpenOrderLists,
      onPressDeleteOrder,
      onPressGoSaveOrder,
      onPressNewOrder,
      onPressPrint,
      onPrintOpenOrder,
      onPressPrintTodayPreOrder,
      onMenuCustomerClick,
      onManualRefundHandler,
      onMenuPressSaveOrder,
      onCloseShiftHandler,
      setMenuRef,
      showMenu,
      onPressEditDone,
      customer,
      onPressPreOrder,
      onPressCollectPreOrder,
      birAccredited,
      onSpecialDiscountPressHandler,
      onPressMoveOrder,
      isBeepEnabled,
      isBeepPreOrderEnabled,
      enableTakeaway,
      onPressTakeAway,
      enableTableLayout,
      isEnabledMRS,
      alreadyCloseZReading,
    } = props;
    if (!shiftOpenStatus) {
      return <View style={[styles.cartHeader, { borderBottomWidth: 0 }]} />;
    }
    const { isOpen } = transactionSession;
    const optionText = customer ? 'Unlink Customer' : 'Add Customer';
    // @ts-ignore
    const isPickUp = isPreOrderPickUp(transactionSession);
    const cartTitle = getCartTitle();
    return (
      <CartHeader
        alreadyCloseZReading={alreadyCloseZReading}
        isEnabledMRS={isEnabledMRS}
        enableTableLayout={enableTableLayout}
        transactionSession={transactionSession}
        birAccredited={birAccredited}
        trashDisabled={trashDisabled}
        titleDisabled={titleDisabled}
        isOpen={isOpen}
        cartTitle={cartTitle}
        optionText={optionText}
        enableOpenOrders={enableOpenOrders}
        canSaveOrders={canSaveOrders}
        enableOpenOrdersShortCut={enableOpenOrdersShortCut}
        enableCustomerShortCut={enableCustomerShortCut}
        enableTakeaway={enableTakeaway}
        isEmpty={isEmpty}
        isPickUp={isPickUp}
        onPressMoveOrder={onPressMoveOrder}
        goToOpenOrderLists={goToOpenOrderLists}
        onPressDeleteOrder={onPressDeleteOrder}
        onPressGoSaveOrder={onPressGoSaveOrder}
        onPressNewOrder={onPressNewOrder}
        onPressPrint={onPressPrint}
        onPrintOpenOrder={onPrintOpenOrder}
        onPressTakeAway={onPressTakeAway}
        onPressPrintTodayPreOrder={onPressPrintTodayPreOrder}
        onMenuCustomerClick={onMenuCustomerClick}
        onManualRefundHandler={onManualRefundHandler}
        onMenuPressSaveOrder={onMenuPressSaveOrder}
        onCloseShiftHandler={onCloseShiftHandler}
        setMenuRef={setMenuRef}
        showMenu={showMenu}
        onPressEditDone={onPressEditDone}
        onPressPreOrder={onPressPreOrder}
        onPressCollectPreOrder={onPressCollectPreOrder}
        onSpecialDiscountPressHandler={onSpecialDiscountPressHandler}
        country={country}
        isBeepEnabled={isBeepEnabled}
        isBeepPreOrderEnabled={isBeepPreOrderEnabled}
      />
    );
  };

  const renderEditModeCart = () => {
    const { onPressEditDone } = props;
    return (
      <View style={styles.cart}>
        <View style={styles.cartHeader} />
        <View style={[styles.cartContainer]}>
          <View style={styles.editModeContainer}>
            <IconEditLayout width={scaleSizeW(360)} height={scaleSizeH(240)} color={CommonColors.Icon} />
            <Text
              style={{
                marginTop: scaleSizeW(30),
                fontWeight: 'bold',
                fontSize: currentThemes.fontSize32,
                marginRight: scaleSizeW(20),
                letterSpacing: 1.2,
              }}
            >
              {t('Edit Layout')}
            </Text>
            <Text style={styles.editModeDescriptionText}>{t('Drag to arrange your items on the grid')}</Text>
            <Text style={styles.editModeDescriptionText}>{t('Press and hold a tab to rename it')}</Text>
            <SubmitButton
              style={{
                flex: null,
                backgroundColor: '#FC7118',
                height: scaleSizeH(108),
                width: scaleSizeW(514),
                marginTop: scaleSizeH(160),
              }}
              onPress={onPressEditDone}
              textStyle={{ fontSize: currentThemes.mediumFontSize, fontWeight: 'bold' }}
            >
              {t('Done')}
            </SubmitButton>
          </View>
        </View>
      </View>
    );
  };

  const renderEmptyCart = () => {
    return (
      <View
        style={{
          flex: 1,
          flexDirection: 'column',
          alignItems: 'center',
          paddingTop: scaleSizeH(204),
          backgroundColor: 'white',
        }}
        testID='emptyView'
      >
        <EmptyCart />
      </View>
    );
  };

  const renderCart = () => {
    const {
      haveProduct,
      editing,
      enableOpenOrders,
      shiftOpenStatus,
      transactionSession,
      currency,
      cartHeight,
      enableOpenOrdersShortCut,
      isEmpty,
      isOpenOrder,
      enableCustomerShortCut,
      enableTakeaway,
      onAddCustomerClick,
      customer,
      selectedItemIndex,
      enableServiceCharge,
      onFullBillDiscountPressHandler,
      onDeletePurchasedItemHandler,
      onSelectedPurchasedItem,
      onServiceChargePressHandler,
      onRemoveCustomerClick,
      shouldRenderDeposit,
      enableCashback,
      enableLoyalty,
      birAccredited,
      customerDisabled = false,
      disableCustomerEdit = false,
      displayLoyalty,
      displayDiscount,
      alreadyCloseZReading,
      localCountryMap,
    } = props;

    const currencySymbol = newConvertCurrencyToSymbol(localCountryMap, currency);
    const { display, transactionType, items, isOnlineOrder = false } = transactionSession;
    const total = get(display, 'total');
    const count = get(transactionSession, ['items', 'length'], 0);
    // @ts-ignore
    const depositAmount = getPayedDepositAmount(transactionSession);
    let totalAmount = '0.00';
    if (!isNaN(count) && total !== undefined && depositAmount < total) {
      totalAmount = localeNumber(getConditionalRoundingValue(DefaultPaymentOptionType.Cash, total - depositAmount, roundingTo, roundAllTransactions));
    }

    const title = shiftOpenStatus ? `${currencySymbol} ${totalAmount}` : t('Open Shift');
    const showManualReturn = transactionType === TransactionFlowType.Return;
    const showSave = isOpenOrder || !isEmpty;
    const openOrderButtonTitle = showSave ? t('Save') : t('Orders');
    const disableByOutOfStock =
      !allowOutOfStock &&
      (totalOutOfStockItemsCount > 0 ||
        totalOutOfStockItemsCount === INVALID_OOS_STATUS_POPUP ||
        totalOutOfStockItemsCount === INVALID_OOS_STATUS_BANNER ||
        totalOutOfStockItemsCount === INVALID_OOS_STATUS_PENDING);
    const disabled = (shiftOpenStatus === true && isEmpty) || alreadyCloseZReading || disableByOutOfStock;
    const isTakeAway = get(transactionSession, 'salesChannel', SalesChannelType.DEFAULT) === SalesChannelType.TAKEAWAY;
    const isPreOrder = transactionType === TransactionFlowType.PreOrder;
    const isManualReturn = transactionType === TransactionFlowType.Return;
    const totalCount = getDisplayItemsCount(items);
    const trashDisabled = totalCount === 0 && !isManualReturn && !isPreOrder && !alreadyCloseZReading; // If the current transaction type is Return, give users a chance to clear it.
    // @ts-ignore
    const isPickUp = isPreOrderPickUp(transactionSession);
    const titleDisabled = (isPickUp || (isPreOrder && !enableTakeaway) || isOnlineOrder) && !alreadyCloseZReading;
    const canSaveOrders = !isManualReturn && !isPreOrder && !isPickUp && !isTakeAway;

    return (
      <View style={styles.cart}>
        {haveProduct &&
          !editing &&
          renderCartHeader(enableOpenOrders, enableOpenOrdersShortCut, enableCustomerShortCut, isEmpty, canSaveOrders, trashDisabled, titleDisabled)}
        <View testID='CartContainer' style={[styles.cartContainer, { flex: 1 }]}>
          <Cart
            alreadyCloseZReading={alreadyCloseZReading}
            displayLoyalty={displayLoyalty}
            displayDiscount={displayDiscount}
            customerDisabled={customerDisabled}
            disableCustomerEdit={disableCustomerEdit}
            isDiscountHighLight={false}
            onRemoveCustomerClick={onRemoveCustomerClick}
            shiftOpenStatus={shiftOpenStatus}
            transactionSession={transactionSession}
            cartHeight={cartHeight - scaleSizeH(132)}
            enableCustomerShortCut={enableCustomerShortCut}
            onAddCustomerClick={onAddCustomerClick}
            enableTakeaway={enableTakeaway}
            currency={currency}
            customer={customer}
            disableRightSwipe={true}
            selectedItemIndex={selectedItemIndex}
            enableServiceCharge={enableServiceCharge}
            onFullBillDiscountPressHandler={onFullBillDiscountPressHandler}
            onDeletePurchasedItemHandler={onDeletePurchasedItemHandler}
            onSelectedPurchasedItem={onSelectedPurchasedItem}
            onServiceChargePressHandler={onServiceChargePressHandler}
            shouldRenderDeposit={shouldRenderDeposit}
            enableCashback={enableCashback}
            enableLoyalty={enableLoyalty}
            birAccredited={birAccredited}
            navigation={props.navigation}
          />
          <SubmitFooter style={styles.bottonStyle}>
            {shiftOpenStatus && enableOpenOrdersShortCut && canSaveOrders ? (
              <SubmitButton
                {...testProps('al_submit_orders')}
                style={{
                  width: scaleSizeW(100),
                  flex: 0.6,
                  backgroundColor: '#FC7118',
                  marginRight: scaleSizeW(20),
                }}
                disabled={disableByOutOfStock}
                onPress={() => {
                  if (showSave) {
                    props.onSaveClicked();
                  } else {
                    props.onOrdersClicked();
                  }
                  resetOutOfStock();
                }}
                textStyle={{ fontSize: currentThemes.fontSize30, fontWeight: 'bold' }}
              >
                {openOrderButtonTitle}
              </SubmitButton>
            ) : null}
            <SubmitButton
              style={[!shiftOpenStatus && { backgroundColor: '#FC7118' }, showManualReturn && { backgroundColor: '#F44436' }]}
              disabled={disabled}
              accessibilityLabel={'al_checkout'}
              disabledStyle={{ backgroundColor: DISABLED_COLOR }}
              onPress={() => {
                props.onSubmitHandler();
                resetOutOfStock();
              }}
              textStyle={{ fontSize: currentThemes.fontSize30, fontWeight: 'bold' }}
            >
              {title}
            </SubmitButton>
          </SubmitFooter>
        </View>
      </View>
    );
  };

  const { haveProduct, editing } = props;
  return editing ? renderEditModeCart() : haveProduct ? renderCart() : renderEmptyCart();
};

export default CartContent;

interface CartProps {
  shiftOpenStatus: boolean;
  transactionSession: TransactionTypeWithDisplay;
  cartHeight: number;
  enableCustomerShortCut: boolean;
  customer: any;
  currency: string;
  selectedItemIndex?: number;
  enableServiceCharge?: boolean;
  disableLeftSwipe?: boolean;
  disableRightSwipe?: boolean;
  enableTakeaway?: boolean;
  isDiscountHighLight: boolean;

  onAddCustomerClick?(): void;

  onFullBillDiscountPressHandler?(): void;

  onDeletePurchasedItemHandler(index: number): void;

  onSelectedPurchasedItem(index: number, itemId?: string): void;

  onServiceChargePressHandler?(): void;

  onRemoveCustomerClick(): void;

  shouldRenderDeposit?: boolean;
  enableCashback: boolean;
  enableLoyalty: boolean;
  customerDisabled?: boolean;
  disableCustomerEdit?: boolean;
  showLoyalty?: boolean;
  birAccredited?: boolean;
  displayLoyalty?: boolean;
  displayDiscount?: boolean;
  alreadyCloseZReading?: boolean;
  navigation?: any;
  isInCheckout?: boolean;
}

export const Cart = memo((props: CartProps) => {
  const {
    shiftOpenStatus,
    transactionSession,
    cartHeight,
    enableCustomerShortCut,
    onAddCustomerClick,
    customer,
    selectedItemIndex,
    onFullBillDiscountPressHandler,
    onDeletePurchasedItemHandler,
    onSelectedPurchasedItem,
    enableServiceCharge,
    onServiceChargePressHandler,
    disableLeftSwipe,
    disableRightSwipe,
    onRemoveCustomerClick,
    currency,
    isDiscountHighLight,
    shouldRenderDeposit,
    enableCashback,
    enableLoyalty,
    customerDisabled = false,
    disableCustomerEdit = false,
    showLoyalty,
    birAccredited,
    displayLoyalty = true,
    displayDiscount = true,
    navigation,
    isInCheckout = false,
  } = props;
  const showCustomer = enableCustomerShortCut && shiftOpenStatus;
  // @ts-ignore
  const isPickUp = isPreOrderPickUp(transactionSession);
  const showManualRefund = transactionSession.transactionType === TransactionFlowType.Return;
  const showTakeAway = transactionSession.salesChannel === SalesChannelType.TAKEAWAY;
  const showPreOrder = transactionSession.transactionType === TransactionFlowType.PreOrder;
  let title;
  if (showManualRefund) {
    title = t('Refund');
  } else if (showPreOrder) {
    title = t('PreOrder');
  } else if (isPickUp) {
    title = t('Pre-Order Pick Up');
  } else if (showTakeAway) {
    title = t('Takeaway');
  }
  const totalOutOfStockItemsCount = transactionSession.totalOutOfStockItemsCount;

  const allowOutOfStockGB = useSelector(selectGBAllowOutOfStock);
  const allowOutOfStock = useSelector(selectAllowOutOfStockForSalesFlow);
  const store = useSelector(selectStore);
  const employee = useEmployee();
  const pinAuthed = employee?.isManager;

  const dispatch = useDispatch();

  useEffect(() => {
    if (!allowOutOfStock && totalOutOfStockItemsCount === INVALID_OOS_STATUS_POPUP) {
      const params: ModalErrorParams = {
        titleIcon: 'error',
        title: t('OOS_POPUP_TITLE_UNABLE_TO_ACCESS_STOCK_LEVEL'),
        subTitle: t('OOS_POPUP_MSG_RECONNECT_OR_TEMP_ALLOW'),
        okText: t('OOS_POPUP_BUTTON_TEMP_ALLOW_SELLING'),
        cancelText: t('Dismiss'),
        closeable: false,
        style: IsIOS ? { width: scaleSizeW(890) } : undefined,
        onOk: () => {
          if (!pinAuthed) {
            requestAnimationFrame(() => {
              navigation.navigate('ModalManagerPinFC', {
                showBackground: false,
                onDismissHandler: (result: RequestActionAccessResult) => {
                  if (result.ok) {
                    requestAnimationFrame(() => {
                      navigation.navigate('ModalTemporaryAllowOutOfStock', {});
                    });
                  } else {
                    dispatch(updateOutOfStockStatus(INVALID_OOS_STATUS_BANNER));
                  }
                },
              });
            });
          } else {
            requestAnimationFrame(() => {
              navigation.navigate('ModalTemporaryAllowOutOfStock', {});
            });
          }
        },
        onCancel: () => {
          dispatch(clearTransactionSession());
          dispatch(updateOutOfStockStatus(INVALID_OOS_STATUS_BANNER));
        },
      };
      navigation.navigate('ModalError', params);
    }
  }, [allowOutOfStock, totalOutOfStockItemsCount]);

  return (
    <View style={[SharedStyles.flexOne, styles.cartContentConatiner]}>
      {Boolean(title) && (
        <View style={styles.modeConatiner}>
          <Text style={{ color: '#FFF', textAlign: 'center', fontSize: setSpText(24) }}>{title} </Text>
        </View>
      )}
      {showCustomer &&
        (customer ? (
          <CustomerComponent
            disabled={customerDisabled}
            editable={!disableCustomerEdit}
            showLoyalty={showLoyalty}
            onRemoveCustomerClick={onRemoveCustomerClick}
            customer={customer}
            onAddCustomerClick={onAddCustomerClick}
          />
        ) : (
          !customerDisabled && !disableCustomerEdit && <AddCustomer onAddCustomerClick={onAddCustomerClick} />
        ))}
      {totalOutOfStockItemsCount > 0 && (
        <View style={[styles.outOfStockContaner, allowOutOfStock ? {} : { backgroundColor: '#FF2825' }]}>
          <Text style={{ color: '#FFFFFF', textAlign: 'center', fontSize: setSpText(24) }}>
            {t('count item(s) out of stock', { count: totalOutOfStockItemsCount })}
          </Text>
        </View>
      )}
      {!shiftOpenStatus ? (
        <ShiftClosedComponent />
      ) : (
        <ConnectedPurchasedItems
          displayLoyalty={displayLoyalty}
          displayDiscount={displayDiscount}
          isDiscountHighLight={isDiscountHighLight}
          transactionSession={transactionSession}
          showCustomer={showCustomer}
          cartHeight={cartHeight}
          birAccredited={birAccredited}
          currency={currency}
          selectedItemIndex={selectedItemIndex}
          disableLeftSwipe={disableLeftSwipe}
          disableRightSwipe={disableRightSwipe}
          onFullBillDiscountPressHandler={onFullBillDiscountPressHandler}
          onDeletePurchasedItemHandler={onDeletePurchasedItemHandler}
          onSelectedPurchasedItem={onSelectedPurchasedItem}
          enableServiceCharge={enableServiceCharge}
          onServiceChargePressHandler={onServiceChargePressHandler}
          shouldRenderDeposit={shouldRenderDeposit}
          enableCashback={enableCashback}
          enableLoyalty={enableLoyalty}
          navigation={navigation}
          isInCheckout={isInCheckout}
        />
      )}
    </View>
  );
});

interface CartHeaderProps {
  transactionSession: TransactionTypeWithDisplay;
  country: string;
  enableTableLayout: boolean;
  trashDisabled: boolean;
  titleDisabled: boolean;
  birAccredited: boolean;
  isPickUp: boolean;
  isOpen: boolean;
  optionText: string;
  cartTitle: string;
  isBeepEnabled: boolean;
  isBeepPreOrderEnabled: boolean;
  canSaveOrders: boolean;
  enableOpenOrders: boolean;
  enableOpenOrdersShortCut: boolean;
  enableCustomerShortCut: boolean;
  isEmpty: boolean;
  enableTakeaway: boolean;
  isEnabledMRS: boolean;

  onPressTakeAway?(): void;

  goToOpenOrderLists?(): void;

  onPressDeleteOrder?(): void;

  onPressGoSaveOrder?(): void;

  onPressNewOrder?(): void;

  onPressPrint?(): void;

  onPrintOpenOrder(): void;

  onPressPrintTodayPreOrder?(): void;

  onMenuCustomerClick?(): void;

  onManualRefundHandler?(): void;

  onMenuPressSaveOrder?(): void;

  onCloseShiftHandler?(): void;

  setMenuRef?(ref: ModalMoreAction): void;

  showMenu?(): void;

  onSpecialDiscountPressHandler?(): void;

  onPressEditDone?(): void;

  onPressPreOrder?(): void;

  onPressCollectPreOrder?(): void;

  onPressMoveOrder?(): void;

  alreadyCloseZReading: boolean;
}

export const CartHeader = memo((props: CartHeaderProps) => {
  /* eslint-disable  react/prop-types */
  const {
    transactionSession,
    country,
    birAccredited,
    enableTableLayout,
    enableOpenOrders,
    canSaveOrders,
    isPickUp,
    isEmpty,
    onPressDeleteOrder,
    onPressGoSaveOrder,
    onManualRefundHandler,
    onSpecialDiscountPressHandler,
    onPressTakeAway,
    trashDisabled,
    titleDisabled,
    isOpen,
    onPressPrint,
    onPressPrintTodayPreOrder,
    showMenu,
    setMenuRef,
    onPressPreOrder,
    onPrintOpenOrder,
    onPressCollectPreOrder,
    onPressMoveOrder,
    isBeepEnabled,
    isBeepPreOrderEnabled,
    enableTakeaway,
    alreadyCloseZReading,
    cartTitle,
  } = props;
  const { isOnlineOrder = false, createdDate, customer, transactionType, salesChannel } = transactionSession;
  const smMallLimited = useSelector(selectSmMallLimited);
  const originTransaction = useRef<CalculateItemsType>();
  const [itemChanged, setItemChanged] = useState(false);
  useEffect(() => {
    const isSavedOpenOrder =
      transactionSession &&
      transactionSession.transactionId &&
      !transactionSession.isOnlineOrder &&
      transactionSession.createdDate &&
      transactionSession.salesChannel === SalesChannelType.DINE_IN &&
      transactionSession.isOpen;

    if (!isSavedOpenOrder) {
      itemChanged && setItemChanged(false);
      originTransaction.current = undefined;
      return;
    }
    const originalTransactionId = get(originTransaction.current, 'transactionId', '');
    if (originalTransactionId !== transactionSession.transactionId) {
      setItemChanged(false);
      originTransaction.current = {
        transactionId: transactionSession.transactionId,
        // @ts-ignore
        items: map(transactionSession.items, item => {
          return { ...item, options: item.options ? JSON.stringify(item.options) : null };
        }),
        isOpen: transactionSession.isOpen,
      };
      return;
    }
    const current = {
      transactionId: transactionSession.transactionId,
      items: map(transactionSession.items, item => {
        return { ...item, options: item.options ? JSON.stringify(item.options) : null };
      }),
      isOpen: transactionSession.isOpen,
    };
    // @ts-ignore
    const checkResult = calculateItemsToKitchenOrder(current, originTransaction.current);

    setItemChanged(checkResult.itemsDiff.length > 0);
  }, [transactionSession, itemChanged]);

  const buttons = [];
  if (!isPickUp) {
    let hasFirstDivider = false;
    if (enableOpenOrders) {
      if (!isOnlineOrder && enableTableLayout && ((customer && !isEmpty) || createdDate)) {
        buttons.push({
          icon: <IconMoveOrder width={scaleSizeW(41)} height={scaleSizeH(40)} color={CommonColors.Icon} />,
          name: t('Move Order'),
          onClick: onPressMoveOrder,
          disabled: isEmpty || alreadyCloseZReading,
        });
      }
      hasFirstDivider = !enableTableLayout || canSaveOrders;
    }
    if (hasFirstDivider) {
      buttons.push({ isDivider: true });
    }
    if (!enableTableLayout) {
      if (!isOnlineOrder && !createdDate) {
        // order not saved
        buttons.push({
          icon: <IconClock width={scaleSizeW(41)} height={scaleSizeH(40)} color={CommonColors.Icon} />,
          name: t('PreOrder'),
          onClick: onPressPreOrder,
          disabled: alreadyCloseZReading,
        });
      }
      const needRemoveCollect = (!isEmpty && !createdDate && !customer) || createdDate;
      if (!needRemoveCollect) {
        buttons.push({
          icon: <IconCollect width={scaleSizeW(41)} height={scaleSizeH(40)} color={CommonColors.Icon} />,
          name: t('Collect Preorder'),
          onClick: onPressCollectPreOrder,
          disabled: alreadyCloseZReading,
        });
      }
    }
    if (!smMallLimited && !isOnlineOrder && !createdDate && transactionType !== TransactionFlowType.Return) {
      buttons.push({
        icon: <IconReturn width={scaleSizeW(41)} height={scaleSizeH(40)} color={CommonColors.Icon} />,
        name: t('Manual Refund'),
        onClick: onManualRefundHandler,
        disabled: alreadyCloseZReading,
      });
    }
    if (country === 'PH' && birAccredited && !isOnlineOrder) {
      buttons.push({
        icon: <IconSpecialDiscount width={scaleSizeW(41)} height={scaleSizeH(40)} color={CommonColors.Icon} />,
        name: 'Special Discount',
        disabled: !Boolean(transactionSession) || Object.keys(transactionSession).length === 0 || alreadyCloseZReading,
        onClick: onSpecialDiscountPressHandler,
      });
    }

    let hasPrinterDividerAlready = false;
    if (enableOpenOrders && !isEmpty && isOpen) {
      hasPrinterDividerAlready = true;
      buttons.push(
        { isDivider: true },
        {
          icon: <IconPrinter width={scaleSizeW(41)} height={scaleSizeH(40)} color={CommonColors.Icon} />,
          name: t('Print Order'),
          onClick: onPressPrint,
        }
      );
      if (!isOnlineOrder && createdDate && salesChannel === SalesChannelType.DINE_IN) {
        buttons.push({
          icon: <IconReturn width={scaleSizeW(41)} height={scaleSizeH(40)} color={CommonColors.Icon} />,
          name: t('Send To Kitchen'),
          onClick: onPrintOpenOrder,
          disabled: itemChanged,
        });
      }
    }
    if (isBeepEnabled && isBeepPreOrderEnabled) {
      if (!hasPrinterDividerAlready) {
        buttons.push({ isDivider: true });
      }
      buttons.push({
        icon: <IconPrinter width={scaleSizeW(41)} height={scaleSizeH(40)} color={CommonColors.Icon} />,
        name: t("Print Today's Pre Order Beep"),
        onClick: onPressPrintTodayPreOrder,
      });
    }
  }
  return (
    <View style={styles.cartHeader}>
      <View style={styles.subCartHeader}>
        <View style={styles.splitLine}></View>
        <TouchableOpacity
          {...testProps('al_btn_694')}
          activeOpacity={1}
          style={SharedStyles.touchableIconContainertrash}
          disabled={trashDisabled}
          onPress={onPressDeleteOrder}
        >
          <IconTrash width={scaleSizeW(48)} height={scaleSizeH(48)} color={CommonColors.Icon} />
        </TouchableOpacity>
        <TouchableOpacity
          {...testProps('al_btn_354')}
          disabled={titleDisabled}
          onPress={enableTakeaway && !enableTableLayout ? onPressTakeAway : onPressGoSaveOrder}
        >
          <View style={SharedStyles.row}>
            <Text style={styles.cartHeaderText}>{cartTitle}</Text>
            {enableTakeaway && !enableTableLayout && !titleDisabled && <IconFilledSort width={scaleSizeW(48)} height={scaleSizeH(48)} color={'#000'} />}
          </View>
        </TouchableOpacity>
        <TouchableOpacity disabled={isPickUp} activeOpacity={1} style={SharedStyles.touchableIconContainer} onPress={showMenu} {...testProps('al_icon_more')}>
          {!isPickUp && buttons.length > 0 && <IconMore color={CommonColors.Icon} />}
        </TouchableOpacity>
        <ModalMoreAction ref={setMenuRef} list={buttons} />
      </View>
    </View>
  );
});

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const ShiftClosedComponent = memo(props => {
  return (
    <View style={SharedStyles.flexOne} testID='shiftClosedView'>
      <View style={SharedStyles.flexOneColumnCenter}>
        <IconClosed width={scaleSizeW(360)} height={scaleSizeH(240)} color={CommonColors.Icon} />
      </View>
    </View>
  );
});

interface AddCustomerProps {
  onAddCustomerClick(): void;

  disabled?: boolean;
}

export const AddCustomer = memo((props: AddCustomerProps) => {
  const { onAddCustomerClick, disabled = false } = props;
  return (
    <TouchableWithoutFeedback {...testProps('al_add_customer')} disabled={disabled} onPress={onAddCustomerClick}>
      <View style={styles.addCustomerStyle}>
        <View style={styles.addCustomerBorder}>
          <IconAdd width={scaleSizeW(28)} height={scaleSizeW(28)} color={CommonColors.Icon} />
        </View>
        <Text style={styles.addCustomerText}>{t('Add Customer')}</Text>
      </View>
    </TouchableWithoutFeedback>
  );
});

interface CustomerComponentProps extends AddCustomerProps {
  customer: any;

  onRemoveCustomerClick(): void;

  disabled?: boolean;
  showLoyalty?: boolean;
  editable?: boolean;
}

export const CustomerComponent = memo((props: CustomerComponentProps) => {
  const { onAddCustomerClick, customer, onRemoveCustomerClick, disabled, showLoyalty, editable } = props;
  return (
    <View
      style={{
        width: '100%',
        flexDirection: 'row',
        borderBottomColor: currentThemes.borderBottomColor,
        borderBottomWidth: scaleSizeW(1),
      }}
    >
      <CustomerItem
        disabled={disabled}
        editable={editable}
        showLoyalty={showLoyalty}
        onRemoveCustomerClick={onRemoveCustomerClick}
        isInAddCustomer={false}
        item={customer}
        onItemclick={onAddCustomerClick}
      />
    </View>
  );
});

interface RenderCartSummaryProps {
  onFullBillDiscountPressHandler?(): void;

  enableServiceCharge?: boolean;
  showLoyaltyDiscount: boolean;
  loyaltyDiscountType: string;
  isDiscountHighLight: boolean;
  appliedVoucher?: AppliedVoucherType;

  onServiceChargePressHandler?(): void;

  testID?: string;
  subtotal: number;
  discount: number;
  birDiscount: number;
  tax: number;
  serviceCharge: number;
  loyaltyDiscount: string;
  birDiscountTitle: string;
  depositAmount: number;
  shouldRenderDeposit?: boolean;
  enableCashback: boolean;
  enableLoyalty: boolean;
  isOnlineOrder: boolean;
  displayDiscount?: boolean;
}

export class CartSummary extends PureComponent<RenderCartSummaryProps> {
  renderSimple = () => {
    const {
      onFullBillDiscountPressHandler,
      enableServiceCharge,
      onServiceChargePressHandler,
      showLoyaltyDiscount,
      loyaltyDiscountType,
      isDiscountHighLight,
      shouldRenderDeposit,
      displayDiscount,
      subtotal,
      discount,
      birDiscount,
      loyaltyDiscount,
      serviceCharge,
      tax,
      depositAmount,
      birDiscountTitle,
    } = this.props;
    return (
      <View style={styles.cartSummaryConatiner}>
        {/* <View style={styles.summarySpiltLine} /> */}
        <SummaryRow title={t('Subtotal')} value={numericValue(subtotal)} />
        <SummaryRow
          title={isEmpty(birDiscountTitle) ? t('Discount') : 'Full Bill Discount'}
          value={numericValue(discount)}
          dismiss={!displayDiscount}
          titleColor={isDiscountHighLight ? currentThemes.buttonBackgroundColor : '#60636B'}
          onPressHandler={onFullBillDiscountPressHandler}
        />
        <SummaryRow
          title={birDiscountTitle}
          value={numericValue(birDiscount, '-')}
          dismiss={isEmpty(birDiscountTitle)}
          titleColor={isDiscountHighLight ? currentThemes.buttonBackgroundColor : '#60636B'}
          onPressHandler={onFullBillDiscountPressHandler}
        />
        <SummaryRow
          dismiss={!showLoyaltyDiscount}
          value={numericValue(loyaltyDiscount, '-')}
          title={loyaltyDiscountType === 'storeCredit' ? t('Store Credit') : t('Cashback')}
          titleColor={isDiscountHighLight ? currentThemes.buttonBackgroundColor : '#60636B'}
          onPressHandler={onFullBillDiscountPressHandler}
        />
        <SummaryRow
          title={t('Service Charge')}
          value={numericValue(serviceCharge)}
          dismiss={!enableServiceCharge}
          titleColor={isDiscountHighLight ? currentThemes.buttonBackgroundColor : '#60636B'}
          onPressHandler={onServiceChargePressHandler}
        />
        <SummaryRow title={t('Tax')} value={numericValue(tax)} />
        <SummaryRow title={t('Deposit')} value={numericValue(depositAmount)} dismiss={!shouldRenderDeposit} />
      </View>
    );
  };

  render() {
    const { appliedVoucher } = this.props;
    return (
      <>
        {Boolean(appliedVoucher) && (
          <View style={styles.voucherContainer}>
            <Text style={styles.voucherText}>{t('Voucher')}</Text>
            <Text style={styles.voucherCodeText}>{appliedVoucher.voucherCode}</Text>
            <Text style={styles.voucherValueText}>{`- ${localeNumber(appliedVoucher.value)}`}</Text>
          </View>
        )}
        {this.renderSimple()}
      </>
    );
  }
}

interface RenderSummaryRowProps {
  title: string;
  titleColor?: string;
  dismiss?: boolean;
  value?: string;

  onPressHandler?(): void;
}

export const SummaryRow = memo((props: RenderSummaryRowProps) => {
  const { title, titleColor, dismiss, value, onPressHandler } = props;
  return (
    !dismiss && (
      <TouchableOpacity
        activeOpacity={1}
        style={dismiss ? styles.summaryRowDismissContainer : styles.summaryRowContainer}
        onPress={onPressHandler}
        {...testProps(`al_${title}`)}
      >
        <Text style={[styles.summaryText, titleColor && { color: titleColor }, dismiss && { height: 0 }]}>{title}</Text>
        <Text numberOfLines={1} style={styles.summaryAmmount}>
          {value}
        </Text>
      </TouchableOpacity>
    )
  );
});

interface PurchasedItemsProps {
  transactionSession: TransactionTypeWithDisplay;
  cartHeight: number;
  selectedItemIndex?: number;

  onDeletePurchasedItemHandler(index: number, itemId?: string): void;

  onSelectedPurchasedItem(index: number, itemId?: string): void;

  onFullBillDiscountPressHandler(): void;

  enableServiceCharge?: boolean;

  onServiceChargePressHandler?(): void;

  showCustomer: boolean;
  transactionType?: string;
  disableLeftSwipe?: boolean;
  disableRightSwipe?: boolean;
  currency?: string;
  takeawayCharge?: number;
  isDiscountHighLight: boolean;
  shouldRenderDeposit?: boolean;
  enableCashback: boolean;
  enableLoyalty: boolean;
  birAccredited: boolean;
  displayLoyalty?: boolean;
  displayDiscount?: boolean;
  uniquePromos: any;
  selectedUniquePromo: PromotionType;
  navigation?: any;
  isInCheckout?: boolean;
  localCountryMap: any;
}

const mapStateToProps = (state: RootState) => {
  return {
    uniquePromos: selectUniquePromos(state),
    selectedUniquePromo: selectSelectedUniquePromo(state),
    localCountryMap: selectLocalCountryMap(state),
  };
};

const mapDispatchToProps = dispatch => ({
  actions: bindActionCreators(
    {
      clearSelectedUniquePromo,
      fetchUniquePromo,
    },
    dispatch
  ),
});

export class PurchasedItems extends PureComponent<PropsFromRedux & PurchasedItemsProps> {
  private _scrollViewRef: FlatList;
  private _lastDataLength: number;

  renderItem = ({ item, index }) => {
    const {
      selectedItemIndex,
      onDeletePurchasedItemHandler,
      onSelectedPurchasedItem,
      disableLeftSwipe,
      disableRightSwipe,
      currency,
      transactionSession,
      localCountryMap,
    } = this.props;
    const isSelected = selectedItemIndex === index;
    const { itemType, itemChannel } = item;
    if (isEqual(itemType, 'ServiceCharge') || isEqual(itemType, 'Discount')) {
      return null;
    }
    const {
      id,
      title,
      quantity,
      isOutOfStock,
      options,
      promotions,
      display,
      notes,
      sn,
      isGroupFirst,
      isGroupLast,
      submitNotes,
      submittedFrom,
      submittedTime,
      takeawayCharge,
      isTakeaway,
      employeeName,
    } = item;
    const takeawayChargeValue = transactionSession.takeawayCharge || takeawayCharge || 0;
    const total = get(display, 'total');
    const subtotal = get(display, 'subtotal');
    const description = generateDescriptionString(options);
    return (
      <PurchasedItem
        title={title}
        isGroupFirst={isGroupFirst}
        isGroupLast={isGroupLast}
        submitNotes={submitNotes}
        description={description}
        quantity={quantity}
        isOutOfStock={isOutOfStock}
        notes={notes}
        sn={sn}
        salesPerson={employeeName}
        itemId={id}
        total={total}
        subtotal={subtotal}
        index={index}
        currency={currency}
        takeawayCharge={takeawayChargeValue}
        salesChannel={transactionSession.salesChannel}
        promotions={promotions}
        isSelected={isSelected}
        isShowTakeaway={(itemChannel === ItemChannelType.TAKEAWAY && transactionSession.salesChannel === SalesChannelType.DINE_IN) || isTakeaway}
        disableLeftSwipe={disableLeftSwipe}
        disableRightSwipe={disableRightSwipe}
        onDeletePurchasedItemHandler={onDeletePurchasedItemHandler}
        onSelectedPurchasedItem={onSelectedPurchasedItem}
        submittedFrom={submittedFrom}
        submittedTime={submittedTime}
        localCountryMap={localCountryMap}
      />
    );
  };

  keyExtractor = (item, index) => {
    return item.id ? `${item.id}` : item.productId ? `${item.productId}${index}` : String(index);
  };

  refHandler = ref => {
    // this._scrollViewRef = ref && ref.getNode();
    this._scrollViewRef = ref;
  };

  renderEmptyFooter = () => {
    return <View />;
  };

  renderFooter = () => {
    const {
      transactionSession,
      onFullBillDiscountPressHandler,
      enableServiceCharge,
      onServiceChargePressHandler,
      isDiscountHighLight,
      shouldRenderDeposit,
      enableCashback,
      enableLoyalty,
      displayLoyalty,
      displayDiscount,
    } = this.props;
    const { display, loyaltyDiscounts, transactionType, appliedVoucher, isOnlineOrder = false } = transactionSession;
    const isManualReturn = transactionType === TransactionFlowType.Return;
    const isPreOrder = transactionType === TransactionFlowType.PreOrder;
    // @ts-ignore
    const isPickUp = isPreOrderPickUp(transactionSession);
    const loyaltyDiscount = get(loyaltyDiscounts, '0');
    const subtotal = get(display, 'subtotal');
    const discount = get(display, 'discount');
    const birDiscount = get(display, 'birDiscount');
    const depositAmount = get(transactionSession, 'depositAmount', 0);
    const tax = get(display, 'tax');
    const displayLoyaltyDiscount = isOnlineOrder ? get(loyaltyDiscount, 'displayDiscount', 0) : get(loyaltyDiscount, ['display', 'discount'], 0);
    const showLoyaltyDiscount =
      enableLoyalty && !isManualReturn && !isPreOrder && !isPickUp && displayLoyalty && !(isOnlineOrder && displayLoyaltyDiscount === 0);
    const serviceCharge = getUnNullValue(display, 'serviceCharge', 0);
    const loyaltyDiscountType = enableLoyalty ? (enableCashback ? 'cashback' : 'storeCredit') : '';
    const birDiscountTitle = getSCDisplayBirDiscountTitle(transactionSession);
    return (
      <CartSummary
        displayDiscount={displayDiscount}
        isDiscountHighLight={isDiscountHighLight}
        onFullBillDiscountPressHandler={onFullBillDiscountPressHandler}
        onServiceChargePressHandler={onServiceChargePressHandler}
        showLoyaltyDiscount={showLoyaltyDiscount}
        loyaltyDiscountType={loyaltyDiscountType}
        loyaltyDiscount={displayLoyaltyDiscount}
        subtotal={subtotal}
        appliedVoucher={appliedVoucher}
        discount={discount}
        birDiscount={birDiscount}
        serviceCharge={serviceCharge}
        tax={tax}
        enableServiceCharge={enableServiceCharge}
        depositAmount={depositAmount}
        shouldRenderDeposit={shouldRenderDeposit}
        enableCashback={enableCashback}
        enableLoyalty={enableLoyalty}
        isOnlineOrder={isOnlineOrder}
        birDiscountTitle={birDiscountTitle}
      />
    );
  };

  onContentSizeChange = () => {
    const newLength = get(this.props.transactionSession, ['items', 'length']);
    newLength > this._lastDataLength && requestAnimationFrame(() => this._scrollViewRef && this._scrollViewRef.scrollToEnd({ animated: true }));
    this._lastDataLength = newLength;
  };

  renderEmpty = () => {
    return (
      <View
        style={{
          flex: 1,
          flexDirection: 'column',
          alignItems: 'center',
          paddingTop: scaleSizeH(60),
        }}
        testID='emptyView'
      >
        <EmptyCart />
      </View>
    );
  };

  render() {
    const { transactionSession, isInCheckout } = this.props;
    const { items = [] } = transactionSession;
    const data = [...items];

    const uniquePromos = get(transactionSession, ['uniquePromos', 'data'], []);
    const selectedUniquePromo = get(transactionSession, ['uniquePromos', 'selectedUniquePromo'], null);
    const selectedUniquePromoId = get(selectedUniquePromo, ['uniquePromotionCodeInfo', 'id'], '');

    let promoText = t('Select Promo');
    let shouldRenderPromoBanner = false;
    if (uniquePromos && uniquePromos.length > 0) {
      shouldRenderPromoBanner = true;
    }
    if (selectedUniquePromo) {
      const name = selectedUniquePromo.name;
      promoText = t('Promo Applied: ') + name;
    } else if (isInCheckout) {
      shouldRenderPromoBanner = false;
    }

    if (transactionSession.promotions) {
      data.push({
        id: 'Transaction_Level_Promotion',
        // @ts-ignore
        promotions: transactionSession.promotions,
      });
    }

    return (
      <View style={{ flex: 1 }}>
        {shouldRenderPromoBanner && (
          <View style={styles.promoBanner}>
            <TouchableOpacity
              {...testProps('al_btn_759')}
              onPress={() => {
                this.props.actions.fetchUniquePromo({ selectedUniquePromoId });
              }}
              style={styles.promoLeft}
              disabled={isInCheckout}
            >
              <IconSelectPromo width={scaleSizeW(40)} height={scaleSizeW(40)} color={CommonColors.Icon} style={styles.IconSelectPromoStyle} />
              <View style={{ flex: 1, justifyContent: 'center', marginRight: scaleSizeW(20) }}>
                <Text style={styles.promoText} numberOfLines={2} ellipsizeMode='tail'>
                  {promoText}
                </Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              {...testProps('al_btn_338')}
              style={styles.promoRight}
              onPress={() => {
                this.props.actions.clearSelectedUniquePromo();
              }}
            >
              {Boolean(selectedUniquePromoId) && !isInCheckout ? <IconRemove width={scaleSizeW(40)} height={scaleSizeH(40)} color={CommonColors.Icon} /> : null}
            </TouchableOpacity>
          </View>
        )}
        <FlatList
          data={data}
          ref={this.refHandler}
          keyExtractor={this.keyExtractor}
          renderItem={this.renderItem}
          windowSize={10}
          onContentSizeChange={this.onContentSizeChange}
          ListEmptyComponent={this.renderEmpty}
          ListFooterComponent={this.renderFooter}
          ListFooterComponentStyle={{ flex: 1, justifyContent: 'flex-end' }}
          contentContainerStyle={{ flexGrow: 1, paddingTop: scaleSizeH(12) }}
        />
      </View>
    );
  }
}

const connector = connect(mapStateToProps, mapDispatchToProps);
type PropsFromRedux = ConnectedProps<typeof connector>;
export const ConnectedPurchasedItems = connector(PurchasedItems);

export type PromotionTagProps = { promotionId: string; discount: number; title: string; isSelected?: boolean };

export const PromotionTag = memo((props: PromotionTagProps) => {
  const { discount, title, isSelected } = props;
  return (
    <View
      style={[
        styles.item,
        {
          width: '100%',
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        },
      ]}
    >
      <Text
        style={[
          styles.tintText,
          {
            marginLeft: scaleSizeW(68),
            marginRight: scaleSizeW(30),
            flex: 3,
          },
          isSelected && { color: 'white' },
        ]}
        numberOfLines={2}
      >
        {title}
      </Text>
      <Text
        style={[
          styles.tintText,
          {
            marginRight: scaleSizeW(34),
            textAlign: 'right',
            flex: 1,
          },
          isSelected && { color: 'white' },
        ]}
      >
        {localeNumber(-discount)}
      </Text>
    </View>
  );
});

interface PurchasedItemProps {
  total: number;
  title: string;
  notes: string;
  sn: string;
  isGroupFirst?: boolean;
  isGroupLast?: boolean;
  submitNotes?: string;
  currency?: string;
  takeawayCharge?: number;
  salesChannel?: number;
  isShowTakeaway: boolean;
  description: string;
  subtotal: number;
  quantity: number;
  isOutOfStock: number;
  promotions: any[];
  index: number;
  isSelected: boolean;
  disableLeftSwipe: boolean;
  disableRightSwipe?: boolean;
  submittedFrom?: string;
  submittedTime?: string;
  itemId?: string;

  onDeletePurchasedItemHandler(index: number, itemId?: string): void;

  onSelectedPurchasedItem(index: number, itemId?: string): void;

  localCountryMap: any;
  salesPerson?: string;
}

const deleteWidth = 75;

export const PurchasedItem = (props: PurchasedItemProps) => {
  const onDeletePurchasedItemHandler = () => {
    const { onDeletePurchasedItemHandler, index, itemId } = props;
    onDeletePurchasedItemHandler && onDeletePurchasedItemHandler(index, itemId);
  };

  const onSelectedPurchasedItem = () => {
    const { onSelectedPurchasedItem, index, itemId } = props;
    onSelectedPurchasedItem && onSelectedPurchasedItem(index, itemId);
  };

  const allowOutOfStock = useSelector(selectAllowOutOfStockForSalesFlow);

  const {
    index,
    isSelected,
    title,
    quantity,
    isOutOfStock,
    promotions,
    total,
    subtotal,
    notes,
    sn,
    isGroupFirst,
    description,
    disableLeftSwipe,
    disableRightSwipe,
    isShowTakeaway,
    currency,
    takeawayCharge,
    submittedTime,
    submittedFrom,
    itemId,
    localCountryMap,
    salesPerson,
  } = props;
  const currencySymbol = newConvertCurrencyToSymbol(localCountryMap, currency);
  const showPromotionTag = get(promotions, 'length') > 0;
  if (quantity === undefined) {
    return (
      <View style={styles.item} {...testProps(`al_PurchasedItem${index}`)}>
        {map(promotions, promotion => {
          const promotionId = get(promotion, 'promotionId');
          const realmPromotion: PromotionType = DAL.getPromotionById(promotionId);
          const title = generatePromotionTitle(realmPromotion, promotion);
          const discount = get(promotion, ['display', 'discount'], 0);
          return (
            <React.Fragment key={promotionId}>
              <PromotionTag key={`${itemId}_${promotionId}`} promotionId={promotionId} title={title} discount={discount} />
            </React.Fragment>
          );
        })}
      </View>
    );
  }

  const discount = isNaN(subtotal - total) ? 0 : subtotal - total;

  let _notes;
  if (Boolean(notes)) {
    const strArray = notes.split('\n');
    const headStr = head(strArray);
    _notes = getSimplifiedStr(headStr, 140, true, true);
  }

  let textColor;
  if (isOutOfStock) {
    textColor = allowOutOfStock ? '#FF8F00' : '#FF2825';
  }
  if (isSelected) {
    textColor = 'white';
  }

  const textColorStyle = textColor ? { color: textColor } : {};

  return (
    <>
      {isGroupFirst && (
        <Text style={[styles.groupTitle, index != 0 && styles.groupDivideLine]}>
          {submittedTime} | Placed by {submittedFrom}
        </Text>
      )}
      <View style={[styles.item, isSelected && { backgroundColor: '#8D90A4' }]} {...testProps(`al_PurchasedItem${index}`)}>
        {/* @ts-ignore */}
        <SwipeRow
          disableRightSwipe={disableRightSwipe}
          disableLeftSwipe={disableLeftSwipe}
          style={{ overflow: 'hidden' }}
          leftOpenValue={0}
          rightOpenValue={-deleteWidth}
        >
          <TouchableWithoutFeedback onPress={onDeletePurchasedItemHandler} {...testProps('al_purchasedItem_delete')}>
            <View style={{ flex: 1, backgroundColor: 'red', alignItems: 'flex-end', justifyContent: 'center' }}>
              <Text style={styles.delete}>{t('Delete')}</Text>
            </View>
          </TouchableWithoutFeedback>

          <TouchableWithoutFeedback {...testProps('al_btn_364')} onPress={onSelectedPurchasedItem}>
            <View style={{ backgroundColor: isSelected ? '#8D90A4' : 'white' }}>
              <View style={[styles.standaloneRowFront, SharedStyles.row, { justifyContent: 'space-between' }]}>
                <View style={[{ flexDirection: 'row', justifyContent: 'space-between', flex: 1 }]}>
                  <View style={{ width: ITEM_CONTENT_WIDTH * 0.68 }}>
                    <View style={[SharedStyles.row, { width: ITEM_CONTENT_WIDTH * 0.68 }]}>
                      <Text
                        style={[
                          styles.smallText,
                          textColorStyle,
                          {
                            minWidth: scaleSizeW(48),
                            textAlign: 'right',
                            paddingLeft: scaleSizeW(5),
                            fontWeight: 'bold',
                          },
                        ]}
                        {...testProps('al_purchasedItem_quantity')}
                      >
                        {quantity}
                      </Text>
                      <View style={styles.itemTitleContainer}>
                        <Text style={[styles.smallText, textColorStyle, { marginRight: scaleSizeW(10) }]} {...testProps('al_purchasedItem_title')}>
                          {title}
                        </Text>
                        {showPromotionTag && <IconPromotion width={scaleSizeW(28)} height={scaleSizeW(30)} color={CommonColors.Icon} />}
                      </View>
                    </View>
                    {Boolean(description) && (
                      <Text style={[styles.tintText, textColorStyle, { marginLeft: scaleSizeW(63) }]} {...testProps('al_purchasedItem_description')}>
                        {description}
                      </Text>
                    )}
                    {isShowTakeaway && (
                      <Text
                        style={[
                          styles.tintText,
                          isOutOfStock && (allowOutOfStock ? { color: '#FF8F00' } : { color: '#FF2825' }),
                          {
                            paddingLeft: scaleSizeW(63),
                            backgroundColor: isSelected ? '#8D90A4' : 'white',
                            color: isSelected ? 'white' : allowOutOfStock ? '#FC7118' : '#FF2825',
                          },
                        ]}
                      >
                        {`${t('Takeaway')} - ${currencySymbol} ${getLocaleNumberString(takeawayCharge)}`}
                      </Text>
                    )}
                    {Boolean(_notes) && (
                      <View style={styles.notesContainer}>
                        <IconNotes color={isSelected ? CommonColors.White : CommonColors.Icon} width={20} height={20} />
                        <Text style={[styles.noteText, textColorStyle]} {...testProps('al_purchasedItem_notes')}>
                          {_notes}
                        </Text>
                      </View>
                    )}
                    <ItemSalesPerson isSelected={isSelected} salesPerson={salesPerson} color={textColorStyle.color} />
                  </View>
                  <View style={{ width: ITEM_CONTENT_WIDTH * 0.32 }}>
                    <Text
                      style={[
                        styles.smallText,
                        textColorStyle,
                        {
                          textAlign: 'right',
                          fontWeight: '500',
                          marginRight: scaleSizeW(24),
                        },
                      ]}
                      {...testProps('al_purchasedItem_price')}
                    >
                      {localeNumber(total)}
                    </Text>
                    {Boolean(discount) && (
                      <Text
                        style={[
                          styles.tintText,
                          isOutOfStock && (allowOutOfStock ? { color: '#FF8F00' } : { color: '#FF2825' }),
                          {
                            textAlign: 'right',
                            textDecorationLine: 'line-through',
                            marginRight: scaleSizeW(24),
                          },
                        ]}
                      >
                        {localeNumber(subtotal)}
                      </Text>
                    )}
                  </View>
                </View>
              </View>
              {sn && (
                <Text
                  style={[
                    styles.smallText,
                    {
                      paddingHorizontal: scaleSizeW(63),
                      backgroundColor: 'white',
                    },
                    isSelected && { backgroundColor: 'gray' },
                  ]}
                >
                  S/N: {sn}
                </Text>
              )}
              {showPromotionTag &&
                map(promotions, promotion => {
                  const promotionId = get(promotion, 'promotionId');
                  const realmPromotion: PromotionType = DAL.getPromotionById(promotionId);

                  const title = generatePromotionTitle(realmPromotion, promotion);
                  const discount = get(promotion, ['display', 'discount'], 0);
                  return <PromotionTag key={`${itemId}_${promotionId}`} isSelected={isSelected} promotionId={promotionId} title={title} discount={discount} />;
                })}
            </View>
          </TouchableWithoutFeedback>
        </SwipeRow>
      </View>
    </>
  );
};

const ITEM_CONTENT_WIDTH = CART_REAL_WIDTH;

const styles = StyleSheet.create({
  summaryRowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: scaleSizeW(24),
    marginTop: scaleSizeH(16),
    height: scaleSizeH(32),
  },
  summaryRowDismissContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: 0,
  },
  summaryText: {
    fontSize: currentThemes.fontSize24,
    color: '#60636B',
    fontWeight: '400',
    // includeFontPadding: false,
    textAlignVertical: 'center',
  },
  summaryAmmount: {
    fontSize: currentThemes.fontSize20,
    color: '#303030',
    letterSpacing: 1,
    fontWeight: '500',
    padding: 0,
    textAlignVertical: 'center',
  },
  item: {
    paddingVertical: scaleSizeH(6),
    width: '100%',
  },
  standaloneRowFront: {
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: scaleSizeH(60),
  },
  tintText: {
    fontSize: currentThemes.fontSize18,
    color: '#757575',
  },
  noteText: {
    fontSize: currentThemes.fontSize18,
    color: '#757575',
    // marginLeft: scaleSizeW(2),
    // includeFontPadding: false,
  },
  smallText: {
    fontSize: currentThemes.fontSize24,
    color: '#303030',
    fontWeight: '400',
    // includeFontPadding: false,
  },
  delete: {
    color: '#FFF',
    fontWeight: '400',
    fontSize: currentThemes.fontSize24,
    width: deleteWidth,
    // backgroundColor: 'blue',
    textAlign: 'center',
  },
  addCustomerStyle: {
    height: CUSTOMER_ITEM_HEIGHT,
    paddingLeft: scaleSizeW(36),
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomColor: currentThemes.borderBottomColor,
    borderBottomWidth: scaleSizeW(1),
  },
  addCustomerText: {
    marginLeft: scaleSizeW(24),
    fontSize: currentThemes.fontSize26,
    fontWeight: '500',
    color: '#9F9F9F',
  },
  cart: {
    flex: 1,
    width: CART_WIDTH,
  },
  cartHeader: {
    paddingTop: STATUS_BAR_HEIGHT,
    height: SEARCH_BAR_HEIGHT,
    backgroundColor: '#FFF',
    ...Platform.select({
      ios: {
        shadowColor: 'black',
        shadowOffset: { width: 0.75, height: 1 },
        shadowRadius: 0.75,
        shadowOpacity: 0.1,
        zIndex: 50,
      },
      android: {
        elevation: 1,
      },
    }),
  },
  subCartHeader: {
    flex: 1,
    paddingHorizontal: scaleSizeW(20),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  editModeDescriptionText: {
    marginTop: scaleSizeH(10),
    fontSize: currentThemes.fontSize24,
    width: scaleSizeW(474),
    lineHeight: scaleSizeH(36),
    textAlign: 'center',
  },
  editModeContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    backgroundColor: 'white',
    borderRadius: scaleSizeH(8),
    paddingTop: scaleSizeH(200),
  },
  cartSummaryConatiner: {
    alignSelf: 'stretch',
    borderTopColor: currentThemes.borderBottomColor,
    borderTopWidth: scaleSizeW(1),
  },
  bottonStyle: {
    width: '100%',
    justifyContent: 'center',
    paddingTop: scaleSizeH(33),
    paddingBottom: scaleSizeH(15),
    paddingHorizontal: scaleSizeW(30),
  },
  cartContentConatiner: {
    backgroundColor: 'white',
  },
  modeConatiner: {
    width: '100%',
    height: scaleSizeH(64),
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#242644',
  },
  addCustomerBorder: {
    borderColor: '#8D90A3',
    borderWidth: 1,
    borderStyle: 'dashed',
    alignItems: 'center',
    justifyContent: 'center',
    padding: scaleSizeW(6),
    borderRadius: 4,
  },
  itemTitleContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: scaleSizeW(14),
  },
  cartHeaderText: {
    color: '#000',
    fontSize: currentThemes.fontSize24,
    fontWeight: '500',
    padding: 0,
  },
  groupTitle: {
    width: ITEM_CONTENT_WIDTH,
    color: '#9E9E9E',
    fontSize: currentThemes.fontSize20,
    alignSelf: 'center',
    paddingBottom: scaleSizeH(8),
  },
  groupDivideLine: {
    borderTopColor: '#E0E0E4',
    borderTopWidth: StyleSheet.hairlineWidth,
    paddingTop: scaleSizeH(18),
    marginTop: scaleSizeH(8),
  },
  cartContainer: {
    width: '100%',
    backgroundColor: '#E1F6F9',
  },
  voucherContainer: {
    flexDirection: 'row',
    paddingHorizontal: scaleSizeW(24),
    marginVertical: scaleSizeH(15),
    width: '100%',
  },
  voucherText: {
    width: scaleSizeW(116),
    textAlignVertical: 'center',
    color: '#60636B',
    fontSize: currentThemes.fontSize18,
  },
  voucherCodeText: {
    textAlignVertical: 'center',
    color: '#FC7118',
    fontSize: currentThemes.fontSize18,
    flex: 1,
  },
  voucherValueText: {
    textAlignVertical: 'center',
    color: '#757575',
    fontSize: currentThemes.fontSize18,
  },
  splitLine: {
    position: 'absolute',
    height: scaleSizeH(40),
    width: scaleSizeW(1),
    backgroundColor: currentThemes.splitLineColor,
    left: 0,
  },
  notesContainer: {
    marginTop: scaleSizeH(4),
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: scaleSizeW(56),
  },
  outOfStockContaner: {
    width: '100%',
    height: scaleSizeH(64),
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FF8F00',
  },
  promoBanner: {
    height: scaleSizeH(64),
    justifyContent: 'space-around',
    alignItems: 'center',
    backgroundColor: '#FFF0DE',
    borderRadius: scaleSizeW(8),
    flexDirection: 'row',
    margin: scaleSizeW(12),
    paddingHorizontal: scaleSizeW(24),
  },
  promoLeft: {
    flexDirection: 'row',
  },
  promoRight: {},
  IconSelectPromoStyle: {
    marginHorizontal: scaleSizeW(12),
  },
  promoText: {
    fontSize: currentThemes.fontSize20,
    color: '#FC7118',
    fontWeight: '400',
  },
});
