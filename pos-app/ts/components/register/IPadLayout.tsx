import { floor, get, map } from 'lodash';
import React, { FunctionComponent, PureComponent } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import ScrollableTabView from 'react-native-scrollable-tab-view';
import { EmptyLayout, ProductItem, ProductLayout } from '.';
import { UpdateProductQuickLayoutInfoBeginType } from '../../actions';
import CustomScrollTabBar from '../../components/register/CustomScrollTabBar';
import { IsAndroid, width } from '../../constants';
import { AllSettingTabs } from '../../constants/settingTabs';
import { CART_WIDTH, scaleSizeH, scaleSizeW, setSpText } from '../../constants/themes';
import DAL from '../../dal';
import { navigateToSettings } from '../../navigation/commonNavigate';
import { ProductType } from '../../typings';
import { testProps } from '../../utils';
import eventBus, { PRODUCTS_UPDATED } from '../../utils/eventBus';
import { NAVIGATOR_HEADER_HEIGHT } from '../common';
import ProductSelectLayout from './ProductSelectLayout';
import { RegisterLayoutContext } from './RegisterLayoutContext';

const ScrollableTabButton = require('react-native-scrollable-tab-view/Button');

interface QuickLayout {
  name: string;
  order: number;
  categoryId: string;
  items?: ProductItem[];
}

const RowGap = scaleSizeW(24);
const ColumnGap = scaleSizeW(24);

export interface LayoutContextType {
  containerWidth: number;
  containerHeight: number;
  itemWidth: number;
  itemHeight: number;
  rowGap?: any;
  columnGap?: any;
  rows?: any;
  columns?: any;
  onProductLongClick?(productId: string): void;

  onAddProductToCart?(payload: ProductType): void;

  onPressAddProductToTile?(row: number, column: number, categoryIndex: number): void;

  onPressDeleteItem?(categoryIndex: number, productId: string): void;

  onUpdateProductPositionInfo?(payload: UpdateProductQuickLayoutInfoBeginType): void;

  onModifyProductBackgroundColor?(payload: UpdateProductQuickLayoutInfoBeginType): void;
}

/* eslint-disable */
export const layoutContext = React.createContext<LayoutContextType>({
  containerWidth: 0,
  containerHeight: 0,
  itemWidth: 0,
  itemHeight: 0,
  rowGap: 0,
  columnGap: 0,
  rows: 5,
  columns: 5,
  onProductLongClick: () => console.log('onProductLongClick'),
  onAddProductToCart: (payload: ProductType) => console.log('onAddProductToCart'),
  onPressAddProductToTile: (row: number, column: number, categoryIndex: number) => console.log('onPressAddProductToTile'),
  onPressDeleteItem: (categoryIndex: number, productId: string) => console.log('onPressDeleteItem'),
  onUpdateProductPositionInfo: (payload: UpdateProductQuickLayoutInfoBeginType) => console.log('onUpdateProductPositionInfo'),
  onModifyProductBackgroundColor: (payload: UpdateProductQuickLayoutInfoBeginType) => console.log('onModifyProductBackgroundColor'),
});

/* eslint-enable */

interface IPadLayoutProps extends LayoutContextType {
  quickLayout: any[];
  isEditing: boolean;
  isNewProductLayout: boolean;
  onChangeTab?(i, ref): void;
  onTabEdit?: (page: number, id: string) => void;
  rowGap: any;
  columnGap: any;
  rows: any;
  columns: any;
  initialPage?: number;
}

interface IPadLayoutState extends LayoutContextType {
  itemWidth: number;
  itemHeight: number;
  initLayoutConfigs: any;
  prerenderingSiblingsNumber: number;
  updateProducts;
}

export default class IPadLayout extends PureComponent<IPadLayoutProps, IPadLayoutState> {
  private _meragedQuickLayout: any[];
  private _productUpdateSubscribe;
  private _delayUpdateProducts;
  static defaultProps = {
    rowGap: RowGap,
    columnGap: ColumnGap,
    rows: 5,
    columns: 5,
  };

  constructor(props) {
    super(props);
    const {
      containerWidth,
      containerHeight,
      onProductLongClick,
      onAddProductToCart,
      onPressAddProductToTile,
      onPressDeleteItem,
      onUpdateProductPositionInfo,
      onModifyProductBackgroundColor,
      rowGap,
      columnGap,
      rows,
      columns,
    } = props;
    const itemWidth = floor((containerWidth - rowGap * (rows + 1)) / rows, 0);
    const itemHeight = floor((containerHeight - columnGap * (columns + 1)) / columns, 0);

    const initLayoutConfigs = [];
    const iWidth = itemWidth + rowGap;
    const iHeight = itemHeight + columnGap;
    for (let i = 0; i < 5; i += 1) {
      for (let j = 0; j < 5; j += 1) {
        initLayoutConfigs.push({ x: iWidth * j + rowGap, y: iHeight * i });
      }
    }

    this.state = {
      containerWidth,
      containerHeight,
      onProductLongClick,
      onAddProductToCart,
      onPressAddProductToTile,
      onPressDeleteItem,
      onUpdateProductPositionInfo,
      onModifyProductBackgroundColor,
      itemWidth,
      itemHeight,
      initLayoutConfigs,
      prerenderingSiblingsNumber: 0,
      updateProducts: {},
    };
  }

  componentDidMount() {
    requestAnimationFrame(this.initPrerenderingSiblingsNumber);
    this._productUpdateSubscribe = eventBus.addListener(PRODUCTS_UPDATED, () => {
      this._delayUpdateProducts = setTimeout(() => {
        this.setState({ updateProducts: {} });
      }, 1000);
    });
  }

  componentWillUnmount(): void {
    eventBus.remove(this._productUpdateSubscribe);
    this._delayUpdateProducts && clearTimeout(this._delayUpdateProducts);
  }

  initPrerenderingSiblingsNumber = () => this.setState({ prerenderingSiblingsNumber: 1 });

  renderTabBar = () => {
    const { onTabEdit, isEditing } = this.props;
    return renderTabBar({ quickLayout: this._meragedQuickLayout, onTabEdit, isEditing });
  };

  render() {
    const { quickLayout, onChangeTab, isEditing, isNewProductLayout, initialPage = 0 } = this.props;
    this._meragedQuickLayout = map(quickLayout, category => {
      if (Boolean(category) && Boolean(category.items)) {
        category.items = map(category.items, item => {
          const product = DAL.getProductById(item.productId);
          const modifiedTime = product && product.modifiedTime;
          const lastUpdateThumbnail = product && product.lastUpdateThumbnail;
          item.lastUpdateThumbnail = lastUpdateThumbnail;
          item.modifiedTime = modifiedTime;
          return item;
        });
      }
      return category;
    });

    const tabPages = map(this._meragedQuickLayout, (category, index) => {
      const name = get(category, 'name');
      return (
        <TabPage
          testID='TabPage'
          key={index}
          categoryIndex={index}
          category={category}
          tabLabel={name}
          isEditing={isEditing}
          isNewProductLayout={isNewProductLayout}
        />
      );
    });
    const { prerenderingSiblingsNumber } = this.state;
    return (
      <RegisterLayoutContext.Provider value={this.state}>
        <ScrollableTabView
          locked={isEditing}
          scrollWithoutAnimation
          style={carouselTabStyles.scrollableTabView}
          initialPage={initialPage}
          renderTabBar={this.renderTabBar}
          onChangeTab={onChangeTab}
          tabBarUnderlineStyle={carouselTabStyles.tabBarUnderlineStyle}
          tabBarPosition='bottom'
          prerenderingSiblingsNumber={prerenderingSiblingsNumber}
        >
          {tabPages.length > 0 && tabPages}
        </ScrollableTabView>
      </RegisterLayoutContext.Provider>
    );
  }
}

interface TabPageProps {
  category: QuickLayout;
  categoryIndex: number;
  tabLabel?: string;
  testID?: string;
  isEditing: boolean;
  isNewProductLayout: boolean;
}

export const TabPage: FunctionComponent<TabPageProps> = props => {
  const { category, categoryIndex, isEditing, isNewProductLayout } = props;
  const { items } = category || ({} as any);
  const count = get(items, 'length', 0);
  const showEmptyLayout = count === 0 && !isEditing;

  if (showEmptyLayout) {
    const onAddProduct = () => {
      navigateToSettings({ tabName: AllSettingTabs.Layouts, timeStamp: Date.now(), showSyncPopup: true, initialPage: categoryIndex });
    };
    return <EmptyLayout testID={'EmptyLayout'} onAddProduct={onAddProduct} />;
  } else {
    if (!isEditing && isNewProductLayout && IsAndroid) {
      return <ProductSelectLayout items={items} />;
    } else {
      return <ProductLayout rowGap={RowGap} columnGap={ColumnGap} categoryIndex={categoryIndex} items={items} rows={5} columns={5} isEditing={isEditing} />;
    }
  }
};

interface TabBarComponentProps {
  quickLayout: any[];
  onTabEdit: (page?: number, id?: string) => void;
  isEditing: boolean;
}

const renderTabBar = (props: TabBarComponentProps) => {
  const { quickLayout, onTabEdit, isEditing } = props;

  const count = get(quickLayout, 'length', 0);
  const tabsContainerWidth = count * scaleSizeW(180);
  const containerWidth = width - CART_WIDTH - 2 * scaleSizeW(24);

  return (
    <CustomScrollTabBar
      style={carouselTabStyles.scrollableTabBarStyle}
      isEditing={isEditing}
      onNewTabPress={onTabEdit}
      tabsContainerStyle={[
        carouselTabStyles.tabsContainerStyle,
        { width: tabsContainerWidth < containerWidth ? containerWidth : IsAndroid ? tabsContainerWidth : undefined },
      ]}
      onScroll={() => console.log('scroll')}
      renderTab={(id, page, isTabActive, onPressHandler, onLayoutHandler) => {
        const tabStyle = isTabActive ? carouselTabStyles.tabActiveStyle : carouselTabStyles.tabBaseStyle;
        return (
          <ScrollableTabButton
            key={`scrollable_tab_button_${page}`}
            {...testProps(`al_tab_${id}`)}
            onPress={() => onPressHandler(page)}
            onLayout={onLayoutHandler}
            activeOpacity={1}
            underlayColor={'#DDDDDD'}
            onLongPress={() => (isEditing ? onTabEdit(page, id) : onPressHandler(page))}
          >
            <View style={tabStyle}>
              <Text style={carouselTabStyles.textStyle} numberOfLines={1}>
                {id}
              </Text>
            </View>
          </ScrollableTabButton>
        );
      }}
    />
  );
};

const TAB_BAR_HEIGHT = NAVIGATOR_HEADER_HEIGHT;

export const carouselTabStyles = StyleSheet.create({
  scrollableTabView: {
    flex: 1,
    backgroundColor: '#E1F6F9',
  },
  tabBarUnderlineStyle: {
    height: scaleSizeH(8),
    backgroundColor: '#FC7118',
  },
  scrollableTabBarStyle: {
    height: TAB_BAR_HEIGHT,
  },
  tabsContainerStyle: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    backgroundColor: 'white',
  },
  tabBaseStyle: {
    flexShrink: 1,
    minWidth: scaleSizeW(180),
    height: '100%',
    alignSelf: 'stretch',
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabActiveStyle: {
    flexShrink: 1,
    minWidth: scaleSizeW(180),
    height: '100%',
    alignSelf: 'stretch',
    alignItems: 'center',
    justifyContent: 'center',
    borderTopWidth: scaleSizeH(8),
    borderColor: '#FC7118',
  },
  textStyle: {
    paddingHorizontal: scaleSizeH(4),
    fontWeight: 'bold',
    fontSize: setSpText(24),
    color: '#393939',
    borderColor: '#E0E0E4',
    textAlign: 'center',
  },
});
