import React, { memo } from 'react';
import { StyleSheet, Text, View, ViewStyle } from 'react-native';
import { CommonColors, currentThemes, scaleSizeH, scaleSizeW } from '../../constants';
import { IconPeople } from '../ui';

export interface SalesPersonProps {
  salesPerson?: string;
  style?: ViewStyle;
  color?: string;
  fontSize?: number;
  isSelected?: boolean;
}
export const ItemSalesPerson = memo((props: SalesPersonProps) => {
  const { salesPerson, color, style, fontSize, isSelected } = props;
  if (!salesPerson) {
    return null;
  }
  return (
    <View style={[styles.salesPersonContainer, style]}>
      <IconPeople color={isSelected ? CommonColors.White : CommonColors.Icon} width={20} height={20} />
      <Text numberOfLines={1} ellipsizeMode='tail' style={[styles.salesPersonText, color && { color }, fontSize && { fontSize }]}>
        {salesPerson}
      </Text>
    </View>
  );
});

const styles = StyleSheet.create({
  salesPersonContainer: {
    marginTop: scaleSizeH(4),
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: scaleSizeW(55),
  },
  salesPersonText: {
    fontSize: currentThemes.fontSize18,
    color: '#757575',
  },
});
