import { findIndex } from 'lodash';
import React, { FunctionComponent, useContext, useRef, useState } from 'react';
import { Image, StyleSheet, Text, TouchableHighlight, TouchableOpacityProps, View } from 'react-native';

import { currentThemes, IsAndroid, IsIOS, PRODUCT_COLOURS, PRODUCT_COLOURS_EXCESSIVE, PRODUCT_COLOURS_OLD, scaleSizeH, scaleSizeW } from '../../constants';
import DAL from '../../dal';
import { ProductType } from '../../typings';
import { testProps } from '../../utils';
import { getSimplifiedStr } from '../../utils/string';

import RNFS from 'react-native-fs';
import { RegisterLayoutContext } from './RegisterLayoutContext';
import { useSelector } from 'react-redux';
import { selectSyncInfo } from '../../sagas/selector';
import { useAsyncEffect } from 'ahooks';

export interface ProductItem {
  productId: string;
  row: number;
  column: number;
  backgroundColor: string;
}

interface ProductLayoutProps {
  items: ProductItem[];
}

interface ProductSingleSelectCellProps {
  index: number;
  productId?: string;
  productTitle?: string;
  backgroundColor?: string;
  onSelect?: () => void;
  onLongSelect?: () => void;
}

const ProductSingleSelectCell: FunctionComponent<ProductSingleSelectCellProps> = props => {
  const [thumbnail, setThumbnail] = useState('');
  const { productId } = props;

  const syncInfoTypeImmutable = useSelector(selectSyncInfo) as any;
  const syncInfoType = syncInfoTypeImmutable.toJS();

  useAsyncEffect(async () => {
    if (!productId) {
      return;
    }

    const product = DAL.getProductById(productId);

    if (!product || !product.hasThumbnail) {
      setThumbnail('');
      return;
    }

    const imagePath = RNFS.DocumentDirectoryPath + `/${productId}.png`;

    try {
      const exists = await RNFS.exists(imagePath);
      if (exists) {
        const hash = await RNFS.stat(imagePath);
        setThumbnail('file://' + imagePath + '?mtime=' + hash.mtime);
      } else {
        setThumbnail('');
      }
    } catch (err) {
      console.error('loadImage error:', err);
      setThumbnail('');
    }
  }, [productId, syncInfoType.lastProductImagesSyncProgress]);

  const renderContent = () => {
    if (!productId) {
      return (
        <View {...testProps('al_btn_843')} key={`empty_${props.index}`} style={[{ width: '20%', height: '100%' }]}>
          <View style={{ flex: 1, margin: 8, backgroundColor: '#ECEEF4' }}></View>
        </View>
      );
    }

    if (thumbnail) {
      return (
        <View
          style={{
            flex: 1,
            padding: 8,
            alignContent: 'center',
            justifyContent: 'center',
            width: '20%',
            height: '100%',
          }}
        >
          <FixedTouchableHighlight
            {...testProps(`al_product_${props.index}`)}
            activeOpacity={1}
            key={`editableItem_${props.productId}`}
            onPress={props.onSelect}
            onLongPress={props.onLongSelect}
            style={{
              flex: 1,
            }}
          >
            <View
              style={{
                flex: 1,
                alignContent: 'center',
                justifyContent: 'center',
                backgroundColor: props.backgroundColor,
              }}
            >
              <Image
                key={`image_${productId}`}
                source={{
                  uri: thumbnail,
                }}
                style={{ height: '100%', width: '100%' }}
                resizeMode={'cover'}
              />
              <View key={'title_' + props.productTitle} style={styles.titleTextContainer}>
                <Text style={styles.titleWithImage} numberOfLines={1} ellipsizeMode='tail'>
                  {props.productTitle}
                </Text>
              </View>
            </View>
          </FixedTouchableHighlight>
        </View>
      );
    }

    return (
      <View
        style={{
          flex: 1,
          padding: 8,
          alignContent: 'center',
          justifyContent: 'center',
          width: '20%',
          height: '100%',
        }}
      >
        <FixedTouchableHighlight
          activeOpacity={1}
          {...testProps(`al_product_${props.index}`)}
          key={`editableItem_${props.productId}`}
          onPress={props.onSelect}
          onLongPress={props.onLongSelect}
          style={{
            flex: 1,
          }}
        >
          <View
            style={{
              flex: 1,
              alignContent: 'center',
              justifyContent: 'center',
              backgroundColor: props.backgroundColor,
            }}
          >
            <Text style={styles.title}>{getSimplifiedStr(props.productTitle, 56, true, true)}</Text>
          </View>
        </FixedTouchableHighlight>
      </View>
    );
  };

  return renderContent();
};

const ProductSelectLayout: FunctionComponent<ProductLayoutProps> = props => {
  const context = useContext(RegisterLayoutContext);

  const onProductLongClick = productId => {
    const { onProductLongClick } = context;
    onProductLongClick(productId);
  };

  const onAddProductToCart = (payload: ProductType) => {
    const { onAddProductToCart } = context;
    onAddProductToCart(payload);
  };

  const { items } = props;
  const views = new Array(25);

  items.forEach(item => {
    const { row, column, productId } = item as any;
    const index = row * 5 + column;
    let backgroundColor = item.backgroundColor;
    if (!backgroundColor) {
      backgroundColor = PRODUCT_COLOURS[0];
    } else {
      const bgColorIndexInColors = findIndex(PRODUCT_COLOURS, v => v === backgroundColor);
      if (bgColorIndexInColors < 0) {
        const bgColorInOldColorsIndex = findIndex(PRODUCT_COLOURS_OLD, v => v === backgroundColor);
        if (bgColorInOldColorsIndex > -1) {
          backgroundColor = PRODUCT_COLOURS_EXCESSIVE[bgColorInOldColorsIndex];
        }
      }
    }
    const product = DAL.getProductById(productId);
    if (!product) {
      views[index] = null;
      return;
    }
    views[index] = (
      <ProductSingleSelectCell
        index={index}
        key={`cell-${index}`}
        productId={productId}
        backgroundColor={backgroundColor}
        productTitle={product.title}
        onSelect={() => onAddProductToCart(product)}
        onLongSelect={() => onProductLongClick(productId)}
      />
    );
  });

  for (let index = 0; index < 25; index++) {
    if (views[index]) {
      continue;
    }
    views[index] = <ProductSingleSelectCell key={`cell-${index}`} index={index} />;
  }

  return (
    <View style={styles.pan}>
      <View style={styles.container}>{views.slice(0, 5)}</View>
      <View style={styles.container}>{views.slice(5, 10)}</View>
      <View style={styles.container}>{views.slice(10, 15)}</View>
      <View style={styles.container}>{views.slice(15, 20)}</View>
      <View style={styles.container}>{views.slice(20, 25)}</View>
    </View>
  );
};

export default ProductSelectLayout;

export const FixedTouchableHighlight = ({ onPress, onPressIn, onLongPress, ...props }: TouchableOpacityProps) => {
  const touchActivatePositionRef = useRef(null);
  const onPressedIn = e => {
    if (IsIOS) {
      onPressIn?.(e);
      return;
    }
    const { pageX, pageY } = e.nativeEvent;

    touchActivatePositionRef.current = {
      pageX,
      pageY,
    };
    console.log('onPressedIn', touchActivatePositionRef.current?.pageX, touchActivatePositionRef.current?.pageY);
    onPressIn?.(e);
  };
  const onPressed = e => {
    if (IsIOS) {
      onPress?.(e);
      return;
    }
    const { pageX, pageY } = e.nativeEvent;
    const absX = Math.abs(touchActivatePositionRef.current.pageX - pageX);
    const absY = Math.abs(touchActivatePositionRef.current.pageY - pageY);

    const dragged = absX > 20 || absY > 30;
    if (!dragged) {
      onPress?.(e);
    }
    console.log('onPressed', absX, absY);
  };
  const onLongPressed = e => {
    if (IsIOS) {
      onLongPress?.(e);
      return;
    }
    const { pageX, pageY } = e.nativeEvent;
    const absX = Math.abs(touchActivatePositionRef.current.pageX - pageX);
    const absY = Math.abs(touchActivatePositionRef.current.pageY - pageY);

    const dragged = absX > 20 || absY > 30;
    if (!dragged) {
      onLongPress?.(e);
    }
  };
  // eslint-disable-next-line test-props/jsx-test-props
  return <TouchableHighlight underlayColor={'#CCCCCC'} onPressIn={onPressedIn} onPress={onPressed} onLongPress={onLongPressed} {...props}></TouchableHighlight>;
};

const styles = StyleSheet.create({
  pan: {
    flex: 1,
    padding: 8,
  },
  container: {
    flex: 1,
    flexDirection: 'row',
  },
  title: {
    fontSize: currentThemes.fontSize18,
    color: '#FFF',
    fontWeight: '500',
    textAlign: 'center',
    paddingHorizontal: scaleSizeW(8),
  },
  titleWithImage: {
    fontSize: currentThemes.fontSize18,
    color: '#303030',
    textAlign: 'center',
    fontWeight: '500',
    paddingHorizontal: scaleSizeW(8),
  },
  titleTextContainer: {
    position: 'absolute',
    bottom: 0,
    width: '100%',
    backgroundColor: 'white',
    justifyContent: 'center',
    height: IsAndroid ? scaleSizeH(42) : scaleSizeH(40),
  },
});
