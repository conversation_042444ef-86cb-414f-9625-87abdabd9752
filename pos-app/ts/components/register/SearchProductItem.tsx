import { get } from 'lodash';
import React, { PureComponent } from 'react';
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import FastImage from 'react-native-fast-image';
import RNFS from 'react-native-fs';
import { CommonColors, currentThemes, scaleSizeH, scaleSizeW, t } from '../../constants';
import { isValidNumber, localeNumber, testProps } from '../../utils';
import { getIOSStringFromDate } from '../../utils/datetime';
import { IconImageHolder } from '../ui';
import { SearchedProductItemType } from './ProductLayout';

interface Props {
  item: SearchedProductItemType;
  onProductItemClick: (productItem: any) => void;
  onProductItemLongPress?: (productId: string) => void;
}

interface State {
  thumbnail: string;
  hasThumbnail: boolean;
}

export class SearchProductItem extends PureComponent<Props, State> {
  private _mounted: boolean;

  constructor(props) {
    super(props);
    this.state = {
      hasThumbnail: false,
      thumbnail: '',
    };
  }
  componentDidMount() {
    const { item } = this.props;
    this._mounted = true;
    const hasThumbnail = get(item, 'hasThumbnail');
    if (hasThumbnail) {
      this.loadImage();
    }
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (
      get(nextProps.item, 'productId') !== get(this.props.item, 'productId') ||
      getIOSStringFromDate(get(nextProps.item, 'lastUpdateThumbnail')) !== getIOSStringFromDate(get(this.props.item, 'lastUpdateThumbnail'))
    ) {
      this.setState({ hasThumbnail: false });
      this.loadImage(nextProps);
    }
  }

  componentWillUnmount() {
    this._mounted = false;
  }

  _onProductItem = () => {
    const { item, onProductItemClick } = this.props;
    onProductItemClick && onProductItemClick(item);
  };

  onLongPress = () => {
    const { item, onProductItemLongPress } = this.props;
    const { productId } = item;

    onProductItemLongPress && onProductItemLongPress(productId);
  };

  renderRefPrices = () => {
    const { item } = this.props;
    const maxRefUnitPrice = get(item, 'maxRefUnitPrice');
    const minRefUnitPrice = get(item, 'minRefUnitPrice');
    return (
      <>
        <View style={styles.priceItemContainer}>
          <Text style={styles.textPriceName}>{t('Min')}</Text>
          <Text style={styles.textPrice}>{isValidNumber(minRefUnitPrice) ? localeNumber(minRefUnitPrice) : '-'}</Text>
        </View>
        <View style={styles.priceItemContainer}>
          <Text style={styles.textPriceName}>{t('Max')}</Text>
          <Text style={styles.textPrice}>{isValidNumber(maxRefUnitPrice) ? localeNumber(maxRefUnitPrice) : '-'}</Text>
        </View>
      </>
    );
  };

  render() {
    const { item } = this.props;
    const title = get(item, 'title', t('title'));
    const unitPrice = get(item, 'unitPrice');
    const priceType = get(item, 'priceType');
    const skuID = get(item, 'skuId');
    const _unitPrice = isNaN(Number(unitPrice)) ? 0 : Number(unitPrice);
    const { hasThumbnail, thumbnail } = this.state;
    return (
      <TouchableOpacity {...testProps('al_btn_352')} onPress={this._onProductItem} onLongPress={this.onLongPress}>
        <View style={styles.item}>
          {hasThumbnail ? (
            <Image source={{ uri: thumbnail }} style={{ width: ITEM_HEIGHT, height: ITEM_HEIGHT }} resizeMode={'cover'} />
          ) : (
            <View style={[styles.image, { backgroundColor: '#f2f2f2', alignItems: 'center', justifyContent: 'center' }]}>
              <IconImageHolder width={scaleSizeW(40)} height={scaleSizeH(40)} color={CommonColors.Icon} />
            </View>
          )}
          <View style={styles.contentContainer}>
            <View style={styles.columnContainer}>
              <Text style={styles.textName} numberOfLines={2}>
                {title}
              </Text>
              {Boolean(skuID) && <Text style={styles.textSku}>{skuID}</Text>}
            </View>
            <View style={styles.priceContainer}>
              {priceType === 'variable' && this.renderRefPrices()}
              <View style={styles.priceItemContainer}>
                <Text style={styles.textPriceName}>{t('Price')}</Text>
                <Text style={styles.textPrice}>{localeNumber(_unitPrice)}</Text>
              </View>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  }

  loadImage = (props: Props = this.props) => {
    const { item } = props;
    const { productId } = item;
    const imagePath = RNFS.DocumentDirectoryPath + `/${productId}.png`;
    const lastUpdateThumbnail = get(item, 'lastUpdateThumbnail');
    RNFS.exists(imagePath)
      .then(exist => {
        if (exist) {
          const newThumbnail = 'file://' + imagePath + '?time=' + lastUpdateThumbnail;

          if (this.state.thumbnail !== newThumbnail) {
            requestAnimationFrame(() => {
              this._mounted && this.setState({ thumbnail: newThumbnail, hasThumbnail: true });
            });
          }
        } else {
          requestAnimationFrame(() => {
            this._mounted && this.setState({ thumbnail: '', hasThumbnail: false });
          });
        }
      })
      .catch(err => {
        console.log('loadImage error: ' + err);
        requestAnimationFrame(() => {
          this._mounted && this.setState({ thumbnail: '', hasThumbnail: false });
        });
      });
  };
}

const ITEM_HEIGHT = scaleSizeH(102);

const styles = StyleSheet.create({
  item: {
    height: ITEM_HEIGHT,
    paddingHorizontal: scaleSizeW(24),
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF',
  },
  textName: {
    fontSize: currentThemes.fontSize20,
    color: '#60636B',
    fontWeight: 'bold',
    marginLeft: scaleSizeW(32),
    lineHeight: currentThemes.fontSize28,
  },
  textPriceName: {
    fontSize: currentThemes.fontSize16,
    color: CommonColors.PlaceholderText,
    fontWeight: 'bold',
    marginBottom: scaleSizeH(2),
  },
  textPrice: {
    fontSize: currentThemes.fontSize20,
    color: '#757575',
    fontWeight: 'bold',
    lineHeight: currentThemes.fontSize28,
  },
  textSku: {
    fontSize: currentThemes.fontSize14,
    color: '#60636B',
    marginLeft: scaleSizeW(32),
    marginTop: scaleSizeH(4),
    lineHeight: currentThemes.fontSize24,
  },
  image: {
    height: ITEM_HEIGHT,
    width: ITEM_HEIGHT,
    borderRadius: 4,
  },
  columnContainer: {
    flex: 1,
  },
  priceContainer: {
    flexDirection: 'row',
  },
  priceItemContainer: {
    alignItems: 'flex-end',
    minWidth: scaleSizeW(100),
    marginLeft: scaleSizeW(16),
  },
  contentContainer: {
    flex: 1,
    alignItems: 'center',
    flexDirection: 'row',
  },
});
