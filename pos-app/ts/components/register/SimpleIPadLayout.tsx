import { useAsyncEffect } from 'ahooks';
import { findIndex, get, map } from 'lodash';
import React, { FC, FunctionComponent, memo, useContext, useEffect, useRef, useState } from 'react';
import { ImageBackground, StyleSheet, Text, TouchableOpacity, TouchableOpacityProps, View } from 'react-native';
import RNFS from 'react-native-fs';
import ScrollableTabView from 'react-native-scrollable-tab-view';
import { useSelector } from 'react-redux';
import { EmptyLayout, ProductItem } from '.';
import CustomScrollTabBar from '../../components/register/CustomScrollTabBar';
import { IsAndroid, IsIOS, width } from '../../constants';
import { AllSettingTabs } from '../../constants/settingTabs';
import {
  CART_WIDTH,
  currentThemes,
  PRODUCT_COLOURS,
  PRODUCT_COLOURS_EXCESSIVE,
  PRODUCT_COLOURS_OLD,
  scaleSizeH,
  scaleSizeW,
  setSpText,
} from '../../constants/themes';
import DAL from '../../dal';
import { navigateToSettings } from '../../navigation/commonNavigate';
import { selectSyncInfoLastProductImagesSyncProgress } from '../../sagas/selector';
import { ProductType } from '../../typings';
import { testProps } from '../../utils';
import { getSimplifiedStr } from '../../utils/string';
import { NAVIGATOR_HEADER_HEIGHT } from '../common';
import { layoutContext } from './IPadLayout';

const ScrollableTabButton = require('react-native-scrollable-tab-view/Button');

interface QuickLayout {
  name: string;
  order: number;
  categoryId: string;
  items?: ProductItem[];
}

const RowGap = scaleSizeW(24);
const ColumnGap = scaleSizeW(24);

interface Props {
  quickLayout: any[];
  rowGap?: any;
  columnGap?: any;
  rows?: any;
  columns?: any;
  containerWidth: number;
  containerHeight: number;
  onProductLongClick?(productId: string): void;
  onAddProductToCart?(payload: ProductType): void;
}

const SimpleIPadLayout: FC<Props> = props => {
  const {
    quickLayout = [],
    rowGap = RowGap,
    columnGap = ColumnGap,
    rows = 5,
    columns = 5,
    containerWidth,
    containerHeight,
    onProductLongClick,
    onAddProductToCart,
  } = props;

  const [prerenderingSiblingsNumber, setPrerenderingSiblingsNumber] = useState(0);

  const _renderTabBar = () => {
    return renderTabBar({ tabsCount: get(quickLayout, 'length', 0) });
  };

  useEffect(() => {
    setPrerenderingSiblingsNumber(1);
  }, []);

  return (
    <layoutContext.Provider
      /* @ts-ignore */
      value={{ rowGap, columnGap, rows, columns, containerHeight, containerWidth, onProductLongClick, onAddProductToCart }}
    >
      <ScrollableTabView
        scrollWithoutAnimation
        style={carouselTabStyles.scrollableTabView}
        renderTabBar={_renderTabBar}
        tabBarUnderlineStyle={carouselTabStyles.tabBarUnderlineStyle}
        tabBarPosition='bottom'
        prerenderingSiblingsNumber={prerenderingSiblingsNumber}
      >
        {map(quickLayout, (category, index) => {
          const name = get(category, 'name');
          return <TabPage testID='TabPage' key={`${name}_${index}`} categoryIndex={index} category={category} tabLabel={name} />;
        })}
      </ScrollableTabView>
    </layoutContext.Provider>
  );
};

export default memo(SimpleIPadLayout);

interface ProductLayoutProps {
  items: ProductItem[];
}

const ProductSimpleLayout: FunctionComponent<ProductLayoutProps> = memo((props: ProductLayoutProps) => {
  const context = useContext(layoutContext);
  const onProductLongClick = productId => {
    const { onProductLongClick } = context;
    onProductLongClick(productId);
  };

  const onAddProductToCart = (payload: ProductType) => {
    const { onAddProductToCart } = context;
    onAddProductToCart(payload);
  };

  const { items } = props;
  const { rows, columns, columnGap, rowGap } = context;
  const views = new Array(rows * columns);

  items.forEach(item => {
    const { row, column, productId } = item as any;
    const index = row * rows + column;
    let backgroundColor = item.backgroundColor;
    if (!backgroundColor) {
      backgroundColor = PRODUCT_COLOURS[0];
    } else {
      const bgColorIndexInColors = findIndex(PRODUCT_COLOURS, v => v === backgroundColor);
      if (bgColorIndexInColors < 0) {
        const bgColorInOldColorsIndex = findIndex(PRODUCT_COLOURS_OLD, v => v === backgroundColor);
        if (bgColorInOldColorsIndex > -1) {
          backgroundColor = PRODUCT_COLOURS_EXCESSIVE[bgColorInOldColorsIndex];
        }
      }
    }
    const product = DAL.getProductById(productId);
    if (!product) {
      views[index] = null;
      return;
    }
    views[index] = (
      <SimpleProductItem
        index={index}
        key={`cell-${index}`}
        productId={productId}
        backgroundColor={backgroundColor}
        productTitle={product.title}
        modifiedTime={product.modifiedTime}
        onSelect={() => onAddProductToCart(product)}
        onLongSelect={() => onProductLongClick(productId)}
      />
    );
  });

  for (let index = 0; index < views.length; index++) {
    if (views[index]) {
      continue;
    }
    views[index] = <SimpleProductItem key={`cell-${index}`} index={index} />;
  }

  const rowViews = [];

  for (let index = 0; index < columns; index++) {
    rowViews.push(<View style={styles.container}>{views.slice(index * rows, (index + 1) * rows)}</View>);
  }

  return <View style={[styles.pan, { paddingEnd: columnGap, paddingBottom: rowGap }]}>{rowViews}</View>;
});

interface SimpleProductItemProps {
  index: number;
  productId?: string;
  productTitle?: string;
  modifiedTime?: Date;
  backgroundColor?: string;
  onSelect?: () => void;
  onLongSelect?: () => void;
}

const SimpleProductItem: FunctionComponent<SimpleProductItemProps> = memo((props: SimpleProductItemProps) => {
  const [thumbnail, setThumbnail] = useState('');
  const { index, productId, productTitle, backgroundColor, onSelect, onLongSelect } = props;
  const lastProductImagesSyncProgress = useSelector(selectSyncInfoLastProductImagesSyncProgress);
  const context = useContext(layoutContext);
  const { rowGap, columnGap } = context;

  useAsyncEffect(async () => {
    if (!productId) {
      return;
    }

    const product = DAL.getProductById(productId);

    if (!product || !product.hasThumbnail) {
      setThumbnail('');
      return;
    }

    const imagePath = RNFS.DocumentDirectoryPath + `/${productId}.png`;

    try {
      const exists = await RNFS.exists(imagePath);
      if (exists) {
        const hash = await RNFS.stat(imagePath);
        setThumbnail('file://' + imagePath + '?mtime=' + hash.mtime);
      } else {
        setThumbnail('');
      }
    } catch (err) {
      console.error('loadImage error:', err);
      setThumbnail('');
    }
  }, [productId, lastProductImagesSyncProgress]);

  const commonStyle = { flex: 1, marginTop: columnGap, marginLeft: rowGap, backgroundColor: '#ECEEF4' };

  const renderContent = () => {
    if (!productId) {
      return <View {...testProps('al_btn_843')} key={`empty_${index}`} style={commonStyle} />;
    }

    if (thumbnail) {
      return (
        <FixedTouchableOpacity
          {...testProps(`al_product_${index}`)}
          key={`register_editableItem_${productId}`}
          style={[commonStyle, styles.itemContainerWithImage]}
          onPress={onSelect}
          activeOpacity={1}
          onLongPress={onLongSelect}
        >
          <ImageBackground
            key={`image_${productId}`}
            source={{
              uri: thumbnail,
            }}
            style={styles.image}
            resizeMode={'cover'}
          >
            <Text style={styles.titleWithImage} numberOfLines={1} ellipsizeMode='tail'>
              {productTitle}
            </Text>
          </ImageBackground>
        </FixedTouchableOpacity>
      );
    }

    return (
      <FixedTouchableOpacity
        style={[commonStyle, styles.itemContainerWithImage, { backgroundColor: backgroundColor }]}
        {...testProps(`al_product_${index}`)}
        key={`register_editableItem_${productId}`}
        onPress={onSelect}
        activeOpacity={1}
        onLongPress={onLongSelect}
      >
        <Text style={styles.title}>{getSimplifiedStr(productTitle, 56, true, true)}</Text>
      </FixedTouchableOpacity>
    );
  };

  return renderContent();
});

export const FixedTouchableOpacity = ({ onPress, onPressIn, onLongPress, ...props }: TouchableOpacityProps) => {
  const touchActivatePositionRef = useRef(null);
  const onPressedIn = e => {
    if (IsIOS) {
      onPressIn?.(e);
      return;
    }
    const { pageX, pageY } = e.nativeEvent;

    touchActivatePositionRef.current = {
      pageX,
      pageY,
    };
    console.log('onPressedIn', touchActivatePositionRef.current?.pageX, touchActivatePositionRef.current?.pageY);
    onPressIn?.(e);
  };
  const onPressed = e => {
    if (IsIOS) {
      onPress?.(e);
      return;
    }
    const { pageX, pageY } = e.nativeEvent;
    const absX = Math.abs(touchActivatePositionRef.current.pageX - pageX);
    const absY = Math.abs(touchActivatePositionRef.current.pageY - pageY);

    const dragged = absX > 20 || absY > 30;
    if (!dragged) {
      onPress?.(e);
    }
    console.log('onPressed', absX, absY);
  };
  const onLongPressed = e => {
    if (IsIOS) {
      onLongPress?.(e);
      return;
    }
    const { pageX, pageY } = e.nativeEvent;
    const absX = Math.abs(touchActivatePositionRef.current.pageX - pageX);
    const absY = Math.abs(touchActivatePositionRef.current.pageY - pageY);

    const dragged = absX > 20 || absY > 30;
    if (!dragged) {
      onLongPress?.(e);
    }
  };
  // eslint-disable-next-line test-props/jsx-test-props
  return <TouchableOpacity onPressIn={onPressedIn} onPress={onPressed} onLongPress={onLongPressed} {...props}></TouchableOpacity>;
};

interface TabPageProps {
  category: QuickLayout;
  categoryIndex: number;
  tabLabel?: string;
  testID?: string;
}

export const TabPage: FunctionComponent<TabPageProps> = memo((props: TabPageProps) => {
  const { category, categoryIndex } = props;
  const { items } = category || ({} as any);
  const count = get(items, 'length', 0);
  const showEmptyLayout = count === 0;

  if (showEmptyLayout) {
    const onAddProduct = () => {
      navigateToSettings({ tabName: AllSettingTabs.Layouts, timeStamp: Date.now(), showSyncPopup: true, initialPage: categoryIndex });
    };
    return <EmptyLayout testID={'EmptyLayout'} onAddProduct={onAddProduct} />;
  } else {
    return <ProductSimpleLayout items={items} />;
  }
});

interface TabBarComponentProps {
  tabsCount: number;
}

const renderTabBar = (props: TabBarComponentProps) => {
  const { tabsCount = 0 } = props;

  const count = tabsCount;
  const tabsContainerWidth = count * scaleSizeW(180);
  const containerWidth = width - CART_WIDTH - 2 * scaleSizeW(24);

  return (
    <CustomScrollTabBar
      style={carouselTabStyles.scrollableTabBarStyle}
      tabsContainerStyle={[
        carouselTabStyles.tabsContainerStyle,
        { width: tabsContainerWidth < containerWidth ? containerWidth : IsAndroid ? tabsContainerWidth : undefined },
      ]}
      onScroll={() => console.log('scroll')}
      renderTab={(id, page, isTabActive, onPressHandler, onLayoutHandler) => {
        const tabStyle = isTabActive ? carouselTabStyles.tabActiveStyle : carouselTabStyles.tabBaseStyle;
        return (
          <ScrollableTabButton
            key={`scrollable_tab_button_${page}`}
            {...testProps(`al_tab_${id}`)}
            onPress={() => onPressHandler(page)}
            onLayout={onLayoutHandler}
            activeOpacity={1}
            underlayColor={'#DDDDDD'}
            onLongPress={() => onPressHandler(page)}
          >
            <View style={tabStyle}>
              <Text style={carouselTabStyles.textStyle} numberOfLines={1}>
                {id}
              </Text>
            </View>
          </ScrollableTabButton>
        );
      }}
    />
  );
};

const TAB_BAR_HEIGHT = NAVIGATOR_HEADER_HEIGHT;

export const carouselTabStyles = StyleSheet.create({
  scrollableTabView: {
    flex: 1,
    backgroundColor: '#E1F6F9',
  },
  tabBarUnderlineStyle: {
    height: scaleSizeH(8),
    backgroundColor: '#FC7118',
  },
  scrollableTabBarStyle: {
    height: TAB_BAR_HEIGHT,
  },
  tabsContainerStyle: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    backgroundColor: 'white',
  },
  tabBaseStyle: {
    flexShrink: 1,
    minWidth: scaleSizeW(180),
    height: '100%',
    alignSelf: 'stretch',
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabActiveStyle: {
    flexShrink: 1,
    minWidth: scaleSizeW(180),
    height: '100%',
    alignSelf: 'stretch',
    alignItems: 'center',
    justifyContent: 'center',
    borderTopWidth: scaleSizeH(8),
    borderColor: '#FC7118',
  },
  textStyle: {
    paddingHorizontal: scaleSizeH(4),
    fontWeight: 'bold',
    fontSize: setSpText(24),
    color: '#393939',
    borderColor: '#E0E0E4',
    textAlign: 'center',
  },
});

const styles = StyleSheet.create({
  pan: {
    flex: 1,
    justifyContent: 'space-evenly',
  },
  container: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-evenly',
  },
  title: {
    fontSize: currentThemes.fontSize18,
    color: '#FFF',
    fontWeight: '500',
    textAlign: 'center',
    paddingHorizontal: scaleSizeW(8),
  },
  titleWithImage: {
    fontSize: currentThemes.fontSize18,
    color: '#303030',
    textAlign: 'center',
    fontWeight: '500',
    paddingHorizontal: scaleSizeW(8),
    backgroundColor: 'white',
    paddingVertical: scaleSizeH(7),
  },
  itemContainerWithImage: {
    alignContent: 'center',
    justifyContent: 'center',
  },
  image: {
    flex: 1,
    justifyContent: 'flex-end',
  },
});
