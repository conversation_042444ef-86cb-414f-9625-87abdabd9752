import { useMemoizedFn } from 'ahooks';
import React, { FC, memo, useContext, useEffect } from 'react';
import { ScrollView, StyleSheet, Text } from 'react-native';
import { connect, ConnectedProps, useDispatch, useSelector } from 'react-redux';
import { bindActionCreators } from 'redux';

import { View } from 'react-native-animatable';
import { FlatList } from 'react-native-gesture-handler';
import {
  checkCloseLastShiftNotification,
  clearTransactionSession,
  employeeOOSDisable,
  employeeOOSEnable,
  RequestActionAccessResult,
  updateGeneralSettings,
} from '../../actions';

import { closeTopNotification, ContentType } from '../../components/common/TopNotification';
import { ENVIRONMENT, EnvironmentType } from '../../config';
import { AuthorizationAccessType, currentThemes, IsIOS, scaleSizeH, scaleSizeW, SharedStyles, t } from '../../constants';
import useCustomerQR from '../../hooks/settings/useCustomerQR';
import {
  selectAllowOutOfStockForSalesFlow,
  selectAllowOutOfStockUntil,
  selectAutoSignOutCount,
  selectAutoSyncAfterOpenShift,
  selectCloseLastShiftNotification,
  selectCountry,
  selectDisablePolling,
  selectDisablePushNotification,
  selectEmployeeId,
  selectEnableCustomerQR,
  selectEnableCustomerShortCut,
  selectEnableOpenOrders,
  selectEnableOpenOrdersShortCut,
  selectGBAllowOutOfStock,
  selectIsNetConnected,
  selectStore,
} from '../../sagas/selector';
import { RootState } from '../../typings';
import { SwitchSettingItem } from './SettingsList';
import { SettingContext } from './context';
import { NormalRowItem } from './printer/NormalRowItem';
import { settingGrayBorder } from './styles';

import { getCashierAccessConfig } from '../../sagas/authorization';

import { useEmployee } from '../../hooks/business/employee';
import { useAppNavigation } from '../../hooks/common/useAppNavigation';
import { useTimestamp } from '../../hooks/common/useTimestamp';
import { ModalErrorParams } from '../modal/ModalError';

interface Props extends PropsFromRedux {
  rightBackShow?: boolean;
}

const autoSignOutMap = {
  30: '30 seconds',
  60: '1 minute',
  120: '2 minutes',
  300: '5 minutes',
  600: '10 minutes',
  Never: 'Never',
};
const General: FC<Props> = props => {
  const {
    autoSyncAfterOpenShift,
    enableCloseLastShiftNotification,
    enableOpenOrders,
    disablePolling,
    disablePushNotification,
    enableOpenOrdersShortCut,
    enableCustomerShortCut,
    enableCustomerQR,
    country,
    rightBackShow,
    autoSignOutCount,
  } = props;

  useCustomerQR();
  const onToggleOpenLastShiftNotification = value => {
    const {
      actions: { updateGeneralSettings, checkCloseLastShiftNotification },
    } = props;
    updateGeneralSettings({ enableCloseLastShiftNotification: value });
    if (value) {
      checkCloseLastShiftNotification();
    } else {
      closeTopNotification([ContentType.ShiftNotCloseToast]);
    }
  };

  const onToggleCustomerShortcut = useMemoizedFn((value: boolean) => {
    if (value) {
      props.actions.updateGeneralSettings({ enableCustomerShortCut: true });
    } else {
      props.actions.updateGeneralSettings({ enableCustomerShortCut: false, enableCustomerQR: false });
    }
  });
  const { setRightBackShow, setRightTitle } = useContext(SettingContext);

  const allowOutOfStockGB = useSelector(selectGBAllowOutOfStock);
  const allowOutOfStockUntil = useSelector(selectAllowOutOfStockUntil);
  const allowOutOfStock = useSelector(selectAllowOutOfStockForSalesFlow);
  const isNetConnected = useSelector(selectIsNetConnected);

  const navigation = useAppNavigation();
  const store = useSelector(selectStore);
  const employeeId = useSelector(selectEmployeeId);
  const employee = useEmployee();
  const pinAuthed = getCashierAccessConfig(store, 'posSetting') === AuthorizationAccessType.NeedManagerGranted || employee?.isManager;
  const dispatch = useDispatch();

  const onToggleCustomerQR = useMemoizedFn((value: boolean) => {
    if (value) {
      dispatch(updateGeneralSettings({ enableCustomerQR: true, enableCustomerShortCut: true }));
    } else {
      dispatch(updateGeneralSettings({ enableCustomerQR: false }));
    }
  });

  const timestamp = useTimestamp(1_000);

  useEffect(() => {
    if (allowOutOfStockGB && allowOutOfStock) {
      // current txn
      if (allowOutOfStockUntil !== -1 && allowOutOfStockUntil !== 0 && allowOutOfStockUntil < timestamp) {
        dispatch(updateGeneralSettings({ allowOutOfStock: false, allowOutOfStockUntil: 0 }));
      }
    }
  }, [allowOutOfStock, allowOutOfStockGB, allowOutOfStockUntil, dispatch, timestamp]);

  const renderItem = ({ item, index }) => {
    return (
      <NormalRowItem
        title={item.title}
        value={''}
        callback={() => {
          props.actions.updateGeneralSettings({ autoSignOutCount: item.value });
          setRightBackShow(false);
        }}
        style={[settingGrayBorder, { marginTop: 10 }]}
      />
    );
  };

  const onToggleAllowOutOfStock = useMemoizedFn((value: boolean) => {
    if (value) {
      const params: ModalErrorParams = {
        title: t('OOS_POPUP_TITLE_WARNING'),
        subTitle: [t('OOS_POPUP_TITLE_DISABLE_WILL_NOT_ALLOW_ANY_SALES_IF_POS_OFFLINE'), t('OOS_POPUP_MSG_ARE_YOU_SURE_CHANGE_SETTINGS')],
        okText: t('OOS_POPUP_BUTTON_YES_DISABLE_IT'),
        cancelText: t('Try Something Else'),
        style: { width: IsIOS ? scaleSizeW(860) : scaleSizeW(730) },
        onOk: () => {
          requestAnimationFrame(() => {
            if (pinAuthed) {
              dispatch(updateGeneralSettings({ allowOutOfStock: false, allowOutOfStockUntil: 0 }));
              dispatch(employeeOOSDisable());
            } else {
              navigation.navigate('ModalManagerPinFC', {
                showBackground: false,
                onDismissHandler: (result: RequestActionAccessResult) => {
                  if (result.ok) {
                    dispatch(updateGeneralSettings({ allowOutOfStock: false, allowOutOfStockUntil: 0 }));
                    dispatch(employeeOOSDisable());
                    dispatch(clearTransactionSession());
                  }
                },
              });
            }
          });
        },
      };
      navigation.navigate('ModalError', params);
    } else {
      if (pinAuthed) {
        dispatch(updateGeneralSettings({ allowOutOfStock: true, allowOutOfStockUntil: 0 }));
        dispatch(employeeOOSEnable());
      } else {
        // @ts-ignore
        navigation.navigate('ModalManagerPinFC', {
          showBackground: false,
          onDismissHandler: (result: RequestActionAccessResult) => {
            if (result.ok) {
              dispatch(updateGeneralSettings({ allowOutOfStock: true, allowOutOfStockUntil: 0 }));
              dispatch(employeeOOSEnable());
            }
          },
        });
      }
    }
  });

  return (
    <>
      <View style={SharedStyles.flexOne}>
        {rightBackShow ? (
          <FlatList
            style={[{ backgroundColor: currentThemes.bgMainColor }]}
            data={[
              { title: 'Never', value: 'Never' },
              { title: '30 seconds', value: '30' },
              { title: '1 minute', value: '60' },
              { title: '2 minutes', value: '120' },
              { title: '5 minutes', value: '300' },
              { title: '10 minutes', value: '600' },
            ]}
            renderItem={renderItem}
            // contentContainerStyle={[styles.contentContainerStyle, settingGrayBorder]}
          />
        ) : (
          <ScrollView style={SharedStyles.flexOne}>
            <Text style={styles.switchTitle}>{t('SHIFT OPTIONS')}</Text>
            <SwitchSettingItem
              title={t('Auto-Sync After Open Shift')}
              accessibilityLabel={'al_auto_sync'}
              style={settingGrayBorder}
              value={autoSyncAfterOpenShift}
              callback={value => {
                props.actions.updateGeneralSettings({ autoSyncAfterOpenShift: value });
              }}
            />
            <SwitchSettingItem
              title={t('Close Last Shift Notification')}
              accessibilityLabel={'al_close_last_shift_notification'}
              style={[{ marginTop: scaleSizeH(24) }, settingGrayBorder]}
              value={enableCloseLastShiftNotification}
              callback={onToggleOpenLastShiftNotification}
            />
            <Text style={styles.switchTitle}>{t('OPEN ORDER')}</Text>
            <SwitchSettingItem
              style={[{ marginTop: 0 }, settingGrayBorder]}
              title={t('Enable Open Order')}
              accessibilityLabel={'al_enable_open_order'}
              value={enableOpenOrders}
              callback={value => {
                props.actions.updateGeneralSettings({ enableOpenOrders: value, enableOpenOrdersShortCut: value });
              }}
            />
            {enableOpenOrders ? (
              <SwitchSettingItem
                testID='openOrderShortCutSwitch'
                title={t('Add Shortcut Button To Checkout')}
                accessibilityLabel={'al_add_open_order_shortcut'}
                style={[{ marginTop: scaleSizeH(24) }, settingGrayBorder]}
                value={enableOpenOrdersShortCut}
                callback={value => {
                  props.actions.updateGeneralSettings({ enableOpenOrdersShortCut: value });
                }}
              />
            ) : null}
            <Text style={styles.switchTitle}>{t('CUSTOMER MANAGEMENT')}</Text>
            <SwitchSettingItem
              style={[{ marginTop: 0 }, settingGrayBorder]}
              title={t('Add Customer Shortcut To Orders')}
              accessibilityLabel={'al_add_customer_shortcut'}
              value={enableCustomerShortCut}
              callback={onToggleCustomerShortcut}
            />
            {(country === 'MY' || country === 'PH' || country === 'TH') && (
              <SwitchSettingItem
                style={[{ marginTop: scaleSizeH(24) }, settingGrayBorder]}
                title={t('QR Display for Loyalty Redemption')}
                accessibilityLabel={'al_add_customer_qr'}
                value={enableCustomerQR}
                callback={onToggleCustomerQR}
              />
            )}
            <Text style={styles.switchTitle}>{t('SIGN OUT')}</Text>
            <NormalRowItem
              title={'Auto sign-out'}
              value={autoSignOutCount ? autoSignOutMap[autoSignOutCount] : 'Never'}
              callback={() => {
                setRightBackShow(true);
                setRightTitle('Auto Signout');
              }}
              style={[settingGrayBorder, { marginTop: 0 }]}
            />
            {allowOutOfStockGB && (
              <>
                <Text style={styles.switchTitle}>{t('OOS_SETTINGS_TITLE_SELLS_OUT_OF_STOCK_ITEMS')}</Text>
                <SwitchSettingItem
                  style={[settingGrayBorder]}
                  title={t('OOS_SETTINGS_TITLE_ALLOW_WHEN_PRODUCT_QUANTITY_ZERO_OR_LESS')}
                  subtitle={
                    allowOutOfStock
                      ? allowOutOfStockUntil === 0
                        ? null
                        : allowOutOfStockUntil === -1
                        ? t('OOS_SETTINGS_SUBTITLE_TEMP_ALLOW_FOR_THIS_TXN')
                        : t('OOS_SETTINGS_SUBTITLE_TEMP_ALLOW_UNTIL', {
                            until: `${new Date(allowOutOfStockUntil).toLocaleString('en-US', {
                              hour: 'numeric',
                              minute: 'numeric',
                              hour12: true,
                            })}`,
                          })
                      : !isNetConnected
                      ? t('OOS_SETTINGS_SUBTITLE_DEVICE_OFFLINE')
                      : null
                  }
                  accessibilityLabel={'al_allow_out_of_stock'}
                  value={allowOutOfStock}
                  onInterceptor={onToggleAllowOutOfStock}
                />
              </>
            )}
            {ENVIRONMENT !== EnvironmentType.Pro && (
              <>
                <Text style={styles.switchTitle}>Kitchen To Print of Beep Order</Text>
                <SwitchSettingItem
                  style={[{ marginTop: 0 }, settingGrayBorder]}
                  title={'Disable Push Notification'}
                  accessibilityLabel={'al_disable_push_shortcut'}
                  value={disablePushNotification}
                  callback={value => {
                    props.actions.updateGeneralSettings({ disablePushNotification: value });
                  }}
                />
                <SwitchSettingItem
                  style={[{ marginTop: scaleSizeH(24) }, settingGrayBorder]}
                  title={'Disable Polling'}
                  accessibilityLabel={'al_disable_polling'}
                  value={disablePolling}
                  callback={value => {
                    props.actions.updateGeneralSettings({ disablePolling: value });
                  }}
                />
              </>
            )}
          </ScrollView>
        )}
      </View>
    </>
  );
};

const mapStateToProps = (state: RootState) => ({
  enableOpenOrders: selectEnableOpenOrders(state),
  autoSyncAfterOpenShift: selectAutoSyncAfterOpenShift(state),
  disablePolling: selectDisablePolling(state),
  disablePushNotification: selectDisablePushNotification(state),
  enableCustomerShortCut: selectEnableCustomerShortCut(state),
  enableCustomerQR: selectEnableCustomerQR(state),
  enableCloseLastShiftNotification: selectCloseLastShiftNotification(state),
  enableOpenOrdersShortCut: selectEnableOpenOrdersShortCut(state),
  country: selectCountry(state),
  autoSignOutCount: selectAutoSignOutCount(state),
});
const mapDispatchToProps = dispatch => ({
  actions: bindActionCreators(
    {
      updateGeneralSettings,
      checkCloseLastShiftNotification,
    },
    dispatch
  ),
});

type PropsFromRedux = ConnectedProps<typeof connector>;
const connector = connect(mapStateToProps, mapDispatchToProps);

export default memo(connector(General));

const styles = StyleSheet.create({
  switchTitle: {
    marginTop: scaleSizeH(20),
    marginBottom: scaleSizeH(15),
    color: '#959595',
    fontSize: currentThemes.fontSize18,
    fontWeight: '500',
  },
});
