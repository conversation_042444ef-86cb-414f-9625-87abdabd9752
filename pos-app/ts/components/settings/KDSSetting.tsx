import { useMemoizedFn, useSafeState } from 'ahooks';

import React, { FC, memo, PropsWithChildren, useEffect, useRef } from 'react';
import { ActivityIndicator, Linking, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

import { map } from 'lodash';
import { currentThemes, scaleSizeH, scaleSizeW, SharedStyles, t } from '../../constants';
import { KdsSettingType } from '../../reducers/kds';

import WebView from 'react-native-webview';
import { useSelector } from 'react-redux';
import { createSelector } from 'reselect';
import { searchKds } from '../../actions/kds';
import { scaleSize } from '../../constants/themes';
import { useAppNavigation } from '../../hooks/common/useAppNavigation';
import useAsyncDispatch from '../../hooks/redux/useAsyncDispatch';
import {
  selectBusinessName,
  selectCountry,
  selectIsKDSPurchased,
  selectIsKDSPurchasedButNotAssigned,
  selectKds,
  selectKdsLandingPageUrls,
} from '../../sagas/selector';
import { testProps } from '../../utils';
import { MixpanelManagerInstance } from '../../utils/Analytics';
import { to } from '../../utils/promise';
import { Collapse } from '../mrs/Collapse';
import KDSSettingItem from './kds/KDSServerItem';
import { settingGrayBorder } from './styles';

const TipsContent = [
  t("Download 'StoreHub KDS App’ on Play Store in the kitchen display device"),
  t('Launch the app, set a default name for the Kitchen Display System App'),
  t('Click ‘Pair’ on the KDS device'),
];

type TipItemProps = PropsWithChildren<{
  index: number;
  withMarginBottom?: boolean;
}>;
const TipItem = memo((props: TipItemProps) => {
  const { index, children, withMarginBottom = true } = props;
  return (
    <View
      style={[
        {
          flexDirection: 'row',
          alignItems: 'flex-start',
        },
        withMarginBottom && { marginBottom: scaleSizeH(12) },
      ]}
    >
      <Text style={styles.tipsText}>{`${index}. `}</Text>
      {children}
    </View>
  );
});

const fromImmutableKdsLandingPageUrls = createSelector(selectKdsLandingPageUrls, kdsLandingPageUrls => kdsLandingPageUrls.toJS());
const KDSSetting: FC = () => {
  const printerScrollRef = useRef<ScrollView>();
  const [searching, setSearching] = useSafeState(false);
  const kds = useSelector(selectKds);
  const asyncDispatch = useAsyncDispatch();
  const navigation = useAppNavigation();
  const country = useSelector(selectCountry);
  const business = useSelector(selectBusinessName);
  const kdsPurchased = useSelector(selectIsKDSPurchased);
  const kdsPurchasedButNotAssigned = useSelector(selectIsKDSPurchasedButNotAssigned);
  const kdsLandingPageUrls = useSelector(fromImmutableKdsLandingPageUrls);

  useEffect(() => {
    MixpanelManagerInstance.throttledTrack('View KDS Settings', {
      'KDS Purchased': kdsPurchased,
      'Store License': kdsPurchasedButNotAssigned ? 'Inactive' : 'Active',
    });
  }, []);

  useEffect(() => {
    if (searching && printerScrollRef.current) {
      printerScrollRef.current.scrollToEnd({ animated: false });
    }
  });

  const onSearchKds = useMemoizedFn(async () => {
    setSearching(true);
    await to(asyncDispatch(searchKds()));
    setSearching(false);
  });

  useEffect(() => {
    onSearchKds();
  }, [onSearchKds]);

  const renderKdsList = useMemoizedFn((list: KdsSettingType[]) => {
    return map(list, (kds, index) => <KDSSettingItem id={kds.id} key={kds.id} style={{ marginTop: index === 0 ? 0 : scaleSizeH(20) }} />);
  });

  const renderInUsed = useMemoizedFn(() => {
    const used = kds.filter(it => it.isPaired);

    const onPress = () => {
      navigation.navigate('ModalSocketSystem', {
        title: "Can't find your Kitchen Display System device?",
        message: 'How to connect your Kitchen Display System',
        link: 'https://care.storehub.com/en/articles/8385457',
        submitText: 'Close',
      });
      MixpanelManagerInstance.throttledTrack('Click help text - Cant find KDS');
    };

    return (
      <View style={SharedStyles.flexOne}>
        <View style={{ flex: 1, flexDirection: 'row', justifyContent: 'space-between' }}>
          <Text style={[styles.sectionTitle]}>{t('IN-USE x', { count: used.length })}</Text>
          <TouchableOpacity {...testProps('al_btn_977')} onPress={onPress}>
            <Text style={[styles.sectionTitle, { color: '#00B0FF' }]}>{"Can't find your Kitchen Display System device?"}</Text>
          </TouchableOpacity>
        </View>
        {used.length === 0 && (
          <View style={[styles.emptyRow, settingGrayBorder]}>
            <Text style={styles.emptyText}>{t('No kitchen display system connected')}</Text>
          </View>
        )}
        {renderKdsList(used)}
      </View>
    );
  });

  const renderUnUsed = useMemoizedFn(() => {
    const unUsed = kds.filter(it => !it.isPaired);
    if (searching) {
      return null;
    }
    return (
      <>
        <Text style={[styles.sectionTitle]}>{t('Not IN-USE x', { count: unUsed.length })}</Text>
        {renderKdsList(unUsed)}
        {unUsed.length === 0 && !searching && (
          <View style={[styles.emptyRow, settingGrayBorder, { height: 96 }]}>
            <View style={{ flexDirection: 'column' }}>
              <Text style={styles.emptyText}>{t('KDS_NOT_FOUND_TIP_1')}</Text>
              <View style={{ height: 12 }}></View>
              <Text style={styles.emptyText}>{t('KDS_NOT_FOUND_TIP_2')}</Text>
            </View>
          </View>
        )}
      </>
    );
  });

  const KdsNormalView = useMemoizedFn(() => {
    return (
      <View style={[SharedStyles.flexOne]} pointerEvents={searching ? 'box-only' : 'box-none'}>
        <Collapse title={t('How To Set Up Kitchen Display System')} renderContent={renderTips} />
        <ScrollView showsVerticalScrollIndicator={false} ref={printerScrollRef}>
          {renderInUsed()}
          {renderUnUsed()}
        </ScrollView>
        {renderSearch()}
      </View>
    );
  });

  const KdsUnbounceView = useMemoizedFn(() => {
    let url = '';

    switch (country) {
      case 'MY':
        url = kdsLandingPageUrls.my;
        break;
      case 'PH':
        url = kdsLandingPageUrls.ph;
        break;
      case 'TH':
        url = kdsLandingPageUrls.th;
        break;
      default:
        url = kdsLandingPageUrls.default;
    }

    console.log('url:', url);

    return (
      <WebView
        source={{ uri: url }}
        startInLoadingState={true}
        javaScriptEnabled={true}
        mediaPlaybackRequiresUserAction={false}
        onError={syntheticEvent => {
          const { nativeEvent } = syntheticEvent;
          console.warn('WebView error: ', nativeEvent);
        }}
        onLoadEnd={() => {
          console.log('WebView loaded');
        }}
      />
    );
  });

  const KDSNotAssignedView = useMemoizedFn(() => {
    const BOLink = `https://${business}.storehubhq.com`;
    const onPress = () => {
      Linking.openURL(BOLink);
      MixpanelManagerInstance.throttledTrack('Activate KDS');
    };

    return (
      <View style={styles.kdsNotAssignedContainer}>
        <Text style={styles.kdsNotAssignedText}>{t('No Kitchen Display System license activated for this store')}</Text>
        <TouchableOpacity {...testProps('al_btn_640')} style={styles.kdsNotAssignedLinkContainer} onPress={onPress}>
          <Text style={styles.kdsNotAssignedGrayText}>{t('To activate, please go to your')}</Text>
          <Text> </Text>
          <Text style={styles.kdsNotAssignedLinkText}>{t('BackOffice > Settings > Kitchen Display System')}</Text>
        </TouchableOpacity>
      </View>
    );
  });

  // const renderSearch = useMemoizedFn(() => {
  //   return (
  //     <View style={styles.bottomAction}>
  //       <TouchableOpacity {...testProps('al_btn_433')} style={styles.action} onPress={onSearchKds}>
  //         <Text style={styles.actionText}>{t('SEARCH KITCHEN DISPLAY SYSTEM')}</Text>
  //       </TouchableOpacity>
  //     </View>
  //   );
  // });

  const renderSearch = useMemoizedFn(() => {
    return (
      <View style={styles.bottomAction}>
        <TouchableOpacity
          {...testProps('al_btn_864')}
          style={[
            styles.action,
            {
              backgroundColor: searching ? '#F2F2F3' : '#FFF',
              borderColor: '#D6D6D6',
              borderWidth: 1,
              borderRadius: 8,
            },
          ]}
          onPress={onSearchKds}
        >
          {searching && <Text style={styles.actionText}>{t('Searching for kitchen display system').toUpperCase()}</Text>}
          {!searching && <Text style={styles.actionText}>{t('SEARCH KITCHEN DISPLAY SYSTEM').toUpperCase()}</Text>}
          {searching && <View style={{ width: 12 }} />}
          {searching && <ActivityIndicator size={currentThemes.fontSize24} color='#757575' />}
        </TouchableOpacity>
      </View>
    );
  });

  const renderTips = useMemoizedFn(() => {
    return (
      <View>
        {map(TipsContent, (it, index) => {
          return (
            <TipItem index={index + 1} key={index}>
              <Text style={styles.tipsText}>{it}</Text>
            </TipItem>
          );
        })}
      </View>
    );
  });

  return !kdsPurchased ? (
    <KdsUnbounceView />
  ) : kdsPurchasedButNotAssigned ? (
    <KDSNotAssignedView />
  ) : (
    <KdsNormalView /> // purchased and assigned
  );
};

export default memo(KDSSetting);

const styles = StyleSheet.create({
  sectionTitle: {
    marginTop: scaleSizeH(20),
    marginBottom: scaleSizeH(15),
    color: '#959595',
    fontSize: currentThemes.fontSize18,
    fontWeight: '600',
  },
  bottomAction: {
    paddingTop: scaleSizeH(24),
    paddingBottom: scaleSizeH(4),
    flexDirection: 'row',
    backgroundColor: currentThemes.bgMainColor,
  },
  action: {
    flex: 1,
    flexDirection: 'row',
    height: scaleSizeH(88),
    backgroundColor: '#FFF',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
  },
  actionText: {
    fontWeight: 'bold',
    color: '#60636B',
    fontSize: currentThemes.fontSize24,
  },
  emptyRow: {
    flexDirection: 'row',
    backgroundColor: '#FFF',
    borderRadius: scaleSizeW(8),
    alignItems: 'center',
    height: scaleSizeH(88),
    paddingHorizontal: scaleSizeW(32),
    marginBottom: scaleSizeH(20),
  },
  emptyText: {
    color: '#9F9F9F',
    fontSize: currentThemes.fontSize24,
    fontWeight: '400',
  },
  tipsText: {
    color: '#60636B',
    fontSize: currentThemes.fontSize24,
    fontWeight: '400',
    lineHeight: scaleSize(35),
  },
  kdsNotAssignedContainer: {
    backgroundColor: 'transparent',
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  kdsNotAssignedText: {
    fontSize: currentThemes.fontSize24,
    fontStyle: 'normal',
    fontWeight: '400',
    color: '#1A1A1A',
  },
  kdsNotAssignedLinkContainer: {
    flexDirection: 'row',
    marginTop: scaleSizeH(15),
  },
  kdsNotAssignedGrayText: {
    fontSize: currentThemes.fontSize24,
    fontStyle: 'normal',
    fontWeight: '400',
    color: '#808080',
  },
  kdsNotAssignedLinkText: {
    fontSize: currentThemes.fontSize24,
    fontStyle: 'normal',
    fontWeight: '400',
    color: '#1890FF',
  },
});
