import { NetInfoState } from '@react-native-community/netinfo';
import { get, throttle } from 'lodash';
import React, { memo, useEffect, useMemo, useRef, useState } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { useSelector } from 'react-redux';

import { TouchableOpacity } from 'react-native-gesture-handler';
import { createSelector } from 'reselect';
import { currentThemes, scaleSizeH, scaleSizeW, t } from '../../constants';
import { AllSettingTabs } from '../../constants/settingTabs';
import { useAppNavigation } from '../../hooks/common/useAppNavigation';
import { getCurrentRouteName, goBack } from '../../navigation/navigatorService';
import { selectCurrentNetworkInfo, selectDefaultNetwork, selectEnableTableLayout } from '../../sagas/selector';
import { getUnNullValue, testProps } from '../../utils';
import { MixpanelManagerInstance } from '../../utils/Analytics';
import { areInSameSubnet, isOnSameNetwork } from '../../utils/network';
import { openSuccessToast } from '../common/TopNotification';
import { ModalErrorParams } from '../modal/ModalError';
import IconWifiWarn from '../ui/svgIcons/iconWifiWarn';

/* eslint-disable react/prop-types */
export const NetInfoSelector = createSelector(selectDefaultNetwork, selectCurrentNetworkInfo, (defaultNetwork, currentNetInfo) => ({
  defaultNetwork: defaultNetwork.toJS(),
  current: currentNetInfo.toJS(),
}));

let errorDescription = '';
let errorGroupDescription = '';

/* eslint-disable react/prop-types */
export interface NewWifiBannerProps {
  onFixPress: () => void;
  inTableLayout?: boolean;
}
const NewWifiBanner = memo<NewWifiBannerProps>(props => {
  const { defaultNetwork, current } = useSelector(NetInfoSelector);
  const { onFixPress } = props;
  const navigation = useAppNavigation();
  const enableTableLayout = useSelector(selectEnableTableLayout);
  const isFirstEffect = useRef(true);

  const currentWifiName = getUnNullValue(current, 'details.ssid', '');
  const defaultWifiName = getUnNullValue(defaultNetwork, 'details.ssid', '');
  const currentEthernetIp = getUnNullValue(current, 'details.ipAddress', '');

  const hasDefaultWifi = !!defaultWifiName;
  const hasDefaultEthernet = !!getUnNullValue(defaultNetwork, 'details.ipAddress', '') && defaultNetwork.type === 'ethernet';
  const [isFirstRender, setIsFirstRender] = useState(true);
  const hasDefaultNetwork = hasDefaultWifi || hasDefaultEthernet;
  const preDefaultNetwork = useRef<string | undefined>();
  const [debouncedCurrent, setDebouncedCurrent] = useState<NetInfoState>(current);

  const lastChangeTimeRef = useRef(Date.now());
  useEffect(() => {
    lastChangeTimeRef.current = Date.now();
  }, [enableTableLayout]);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedCurrent(current);
    }, 8000);

    return () => {
      clearTimeout(timer);
    };
  }, [JSON.stringify(current)]);

  useEffect(() => {
    const timer = setTimeout(() => {
      if (hasDefaultNetwork) {
        if (preDefaultNetwork.current && preDefaultNetwork.current !== JSON.stringify(defaultNetwork)) {
          const preObj = JSON.parse(preDefaultNetwork.current);
          const preType = get(preObj, 'type', '');
          if (preType === 'wifi') {
            const preName = get(preObj, 'details.ssid', '');
            const currentType = get(defaultNetwork, 'type', '');
            let currentName = '';
            if (currentType === 'wifi') {
              currentName = get(defaultNetwork, 'details.ssid', '');
              if (preName !== currentName) {
                MixpanelManagerInstance.throttledTrack('Change default network', {
                  'Previous default network': preName,
                  'Current default network': currentName,
                });
              }
            } else if (currentType === 'ethernet') {
              currentName = get(defaultNetwork, 'details.ipAddress', '');
              MixpanelManagerInstance.throttledTrack('Change default network', {
                'Previous default network': preName,
                'Current default network': currentName,
              });
            }
          } else if (preType === 'ethernet') {
            const preIp = get(preObj, 'details.ipAddress', '');
            const currentType = get(defaultNetwork, 'type', '');
            let currentName = '';
            if (currentType === 'wifi') {
              currentName = get(defaultNetwork, 'details.ssid', '');
              MixpanelManagerInstance.throttledTrack('Change default network', {
                'Previous default network': preIp,
                'Current default network': currentName,
              });
            } else if (currentType === 'ethernet') {
              currentName = get(defaultNetwork, 'details.ipAddress', '');
              if (preIp !== currentName) {
                MixpanelManagerInstance.throttledTrack('Change default network', {
                  'Previous default network': preIp,
                  'Current default network': currentName,
                });
              }
            }
          }
        }
        preDefaultNetwork.current = JSON.stringify(defaultNetwork);
      }
    }, 5000);

    return () => {
      clearTimeout(timer);
    };
  }, [JSON.stringify(defaultNetwork)]);

  const isSame = useMemo(() => {
    const { isSameNetwork, errorDes, errorGroupDes } = isOnSameNetwork(debouncedCurrent, defaultNetwork);
    errorDescription = errorDes;
    errorGroupDescription = errorGroupDes;
    return isSameNetwork;
  }, [defaultNetwork, debouncedCurrent]);

  useEffect(() => {
    if (isFirstRender) {
      setIsFirstRender(false);
      return;
    }

    const defaultIp = getUnNullValue(defaultNetwork, 'details.ipAddress', '');
    const defaultSubnet = getUnNullValue(defaultNetwork, 'details.subnet', '');
    const currentIp = getUnNullValue(current, 'details.ipAddress', '');
    const currentSubnet = getUnNullValue(current, 'details.subnet', '');
    const currentNetworkType = get(current, 'type', '');
    if (hasDefaultWifi && currentNetworkType === 'wifi' && areInSameSubnet(defaultIp, defaultSubnet, currentIp, currentSubnet)) {
      requestAnimationFrame(() => {
        openSuccessToast('Default network restored');

        const currentRoute = getCurrentRouteName();
        if (currentRoute === 'ModalError') {
          goBack();
        }

        MixpanelManagerInstance.throttledTrack('Default Network Restored', {
          'Current Network': currentWifiName,
        });
      });
    } else if (hasDefaultEthernet && currentNetworkType === 'ethernet' && areInSameSubnet(defaultIp, defaultSubnet, currentIp, currentSubnet)) {
      requestAnimationFrame(() => {
        openSuccessToast('Default network restored');

        const currentRoute = getCurrentRouteName();
        if (currentRoute === 'ModalError') {
          goBack();
        }

        MixpanelManagerInstance.throttledTrack('Default Network Restored', {
          'Current Network': currentEthernetIp,
        });
      });
    }
  }, [isSame]);

  useEffect(() => {
    if (isFirstEffect.current && enableTableLayout && !props.inTableLayout) {
      isFirstEffect.current = false;
      return;
    }
    const throttledNavigate = throttle(() => {
      const params: ModalErrorParams = {
        titleIcon: 'error',
        title: t('POS has disconnected from Default Network'),
        subTitle: t('Reconnect immediately to avoid printing issues'),
        okText: t('Fix it now'),
        cancelText: t('Dismiss'),
        onOk: () => {
          navigation.navigate('Settings', { tabName: AllSettingTabs.DefaultNetwork, timeStamp: Date.now() });
          MixpanelManagerInstance.throttledTrack('Default Network Error - Fix now', {
            Location: 'Pop Up',
          });
        },
        onCancel: () => {
          MixpanelManagerInstance.throttledTrack('Default Network Error - Dismiss (pop up)');
        },
      };
      navigation.navigate('ModalError', params);
    }, 1000);

    const throttledTrackError = throttle((errorDescription, errorGroupDescription) => {
      MixpanelManagerInstance.throttledTrack('Default Network Error', {
        'Error type': errorDescription,
        'Error Group': errorGroupDescription,
      });
    }, 1000);

    const timeSinceLastChange = Date.now() - lastChangeTimeRef.current;

    if (timeSinceLastChange >= 1000 && !isSame) {
      throttledNavigate();
      throttledTrackError(errorDescription, errorGroupDescription);
    }

    return () => {
      throttledNavigate.cancel();
      throttledTrackError.cancel();
    };
  }, [isSame]);

  if (isSame) {
    return null;
  }
  return (
    <View style={styles.row}>
      <View style={{ flexDirection: 'row', justifyContent: 'center', alignItems: 'center', marginLeft: scaleSizeW(24) }}>
        <IconWifiWarn width={20} height={20} />
        <Text style={styles.text} testID='text'>
          {t('Reconnect to Default Network')}
        </Text>
      </View>
      <TouchableOpacity
        {...testProps('al_btn_856')}
        style={{ justifyContent: 'center', alignItems: 'center', height: scaleSizeH(56) }}
        onPress={() => {
          onFixPress();
          MixpanelManagerInstance.throttledTrack('Default Network Error - Fix now', {
            Location: 'Red Banner',
          });
        }}
      >
        <Text style={styles.fixText}>{t('Fix it now')}</Text>
      </TouchableOpacity>
    </View>
  );
});
/* eslint-enable react/prop-types */

export default NewWifiBanner;
const styles = StyleSheet.create({
  row: {
    height: scaleSizeH(56),
    backgroundColor: '#FF2825',
    justifyContent: 'space-between',
    flexDirection: 'row',
  },
  text: {
    fontWeight: '400',
    fontSize: currentThemes.fontSize20,
    color: 'white',
    marginLeft: scaleSizeW(8),
  },
  fixText: {
    color: 'white',
    fontSize: currentThemes.fontSize20,
    fontWeight: '700',
    textDecorationLine: 'underline',
    textAlignVertical: 'center',
    marginRight: scaleSizeW(24),
  },
});
