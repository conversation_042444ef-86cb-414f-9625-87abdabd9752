import { NetInfoState } from '@react-native-community/netinfo';
import { get, isEmpty } from 'lodash';
import React, { FC, memo, useMemo } from 'react';
import { FlatList, ListRenderItemInfo, StyleSheet, Text, TouchableOpacity, View, ViewProps } from 'react-native';
import { useSelector } from 'react-redux';
import { createSelector, createStructuredSelector } from 'reselect';
import { t } from '../../constants';
import { AllSettingTabs } from '../../constants/settingTabs';
import { CommonColors, currentThemes, scaleSizeH, scaleSizeW, SharedStyles } from '../../constants/themes';
import { selectCurrentNetworkInfo, selectDefaultNetwork, selectErrorPrinters, selectGBSettingTag, selectReceiptType } from '../../sagas/selector';
import { getUnNullValue, testProps } from '../../utils';
import { infoPOSBasicEvent, POSBasicAction } from '../../utils/logComponent';
import { areInSameSubnet } from '../../utils/network';
import { ExclamationRed } from '../ui';
import Version from './Version';

type Props = {
  pageIndex: number;
  tabNameList: string[];
  printerAssignStatus: boolean;
  onPress(tab: string, index: number): void;
};

const fromImmutableDefaultNetwork = createSelector(selectDefaultNetwork, defaultNetwork => defaultNetwork.toJS());
const fromImmutableNetInfo = createSelector(selectCurrentNetworkInfo, netInfo => netInfo.toJS());

const fromImmutableErrorPrinters = createSelector(selectErrorPrinters, errorPrinters => errorPrinters.toJS());
const fromImmutableGBSettingTag = createSelector(selectGBSettingTag, settingTag => settingTag.toJS());
const selectorPrinter = createStructuredSelector({
  receiptType: selectReceiptType,
  errorPrinters: fromImmutableErrorPrinters,
});

const selectorNet = createStructuredSelector({
  netInfo: fromImmutableNetInfo,
  defaultNetwork: fromImmutableDefaultNetwork,
});

const getDefaultNetwork = (current?: NetInfoState, defaultNetwork?: NetInfoState) => {
  const defaultNetworkType = get(defaultNetwork, 'type');
  const currentNetworkType = get(current, 'type');
  const defaultEthernetIp = getUnNullValue(defaultNetwork, 'details.ipAddress', '');

  let isNetChanged = false;
  const defaultWifiName = get(defaultNetwork, 'details.ssid', '');
  const isEnabledNetwork = (defaultNetworkType === 'ethernet' && defaultEthernetIp) || (defaultNetworkType === 'wifi' && defaultWifiName);

  if (defaultNetworkType === 'wifi' && defaultWifiName) {
    const currentName = get(current, 'details.ssid', '');
    if (currentNetworkType === 'ethernet') {
      isNetChanged = true;
    } else if (defaultWifiName !== currentName) {
      isNetChanged = true;
    }
  } else if (defaultNetworkType === 'ethernet' && currentNetworkType === 'wifi') {
    isNetChanged = true;
  }

  return { isNetChanged, isEnabledNetwork };
};

export const OrangeNewTag = (props: ViewProps) => {
  return (
    <View
      style={[
        props.style,
        {
          paddingVertical: 6,
          paddingHorizontal: 16,
          borderRadius: 8,
          backgroundColor: '#FC7118',
        },
      ]}
    >
      <Text
        style={{
          color: '#fff',
          fontSize: 16,
        }}
      >
        {'NEW'}
      </Text>
    </View>
  );
};

const isOnSameNetwork = (current?: NetInfoState, defaultNetwork?: NetInfoState) => {
  const defaultNetworkType = get(defaultNetwork, 'type');
  const currentNetworkType = get(current, 'type');
  const defaultWifiName = getUnNullValue(defaultNetwork, 'details.ssid', '');
  const defaultEthernetIp = getUnNullValue(defaultNetwork, 'details.ipAddress', '');
  const defaultEthernetSubnet = getUnNullValue(defaultNetwork, 'details.subnet', '');
  const currentEthernetIp = getUnNullValue(current, 'details.ipAddress', '');
  const currentEthernetSubnet = getUnNullValue(current, 'details.subnet', '');
  const hasDefaultEthernet = !!getUnNullValue(defaultNetwork, 'details.ipAddress', '') && defaultNetwork.type === 'ethernet';
  const defaultIp = getUnNullValue(defaultNetwork, 'details.ipAddress', '');
  const defaultSubnet = getUnNullValue(defaultNetwork, 'details.subnet', '');
  const currentIp = getUnNullValue(current, 'details.ipAddress', '');
  const currentSubnet = getUnNullValue(current, 'details.subnet', '');

  if (defaultNetworkType === 'wifi' && defaultWifiName) {
    if (currentNetworkType === 'ethernet' && !areInSameSubnet(defaultEthernetIp, defaultEthernetSubnet, currentEthernetIp, currentEthernetSubnet)) {
      return false;
    } else if (!areInSameSubnet(defaultIp, defaultSubnet, currentIp, currentSubnet)) {
      return false;
    }
  } else if (hasDefaultEthernet) {
    if (currentNetworkType === 'wifi' && !areInSameSubnet(defaultEthernetIp, defaultEthernetSubnet, currentEthernetIp, currentEthernetSubnet)) {
      return false;
    } else if (currentNetworkType === 'ethernet' && !areInSameSubnet(defaultEthernetIp, defaultEthernetSubnet, currentEthernetIp, currentEthernetSubnet)) {
      return false;
    } else if (!current.isConnected) {
      return false;
    } else {
      return true;
    }
  }
  return true;
};

const SettingTabBar: FC<Props> = props => {
  const { onPress, tabNameList, pageIndex = 0, printerAssignStatus } = props;
  const onPressHandler = (index: number) => {
    onPress(tabNameList[index], index);
  };

  const { receiptType, errorPrinters } = useSelector(selectorPrinter);
  const { netInfo, defaultNetwork } = useSelector(selectorNet);
  const settingTags = useSelector(fromImmutableGBSettingTag);

  const shouldDisplayExclamation = useMemo(() => {
    return receiptType !== 'a4' && (!isEmpty(errorPrinters) || !printerAssignStatus);
  }, [printerAssignStatus, errorPrinters, receiptType]);

  const { isEnabledNetwork } = useMemo(() => {
    const result = getDefaultNetwork(netInfo, defaultNetwork);
    if (result.isNetChanged) {
      infoPOSBasicEvent({
        action: POSBasicAction.DefaultWiFiChanged,
        reason: 'Default WIFI changed',
        privateDataPayload: { defaultNetwork, netInfo },
      });
    }
    return result;
  }, [netInfo, defaultNetwork]);

  const isSame = useMemo(() => {
    return isOnSameNetwork(netInfo, defaultNetwork);
  }, [netInfo, defaultNetwork]);

  // Find the corresponding tag settings for a tab
  const findSettingTag = (tabName: string) => {
    for (const key of Object.keys(AllSettingTabs)) {
      if (AllSettingTabs[key] === tabName) {
        if (settingTags[key]?.show) {
          return settingTags[key];
        } else {
          return null;
        }
      }
    }
    return null;
  };

  const renderItem = (info: ListRenderItemInfo<string>) => {
    const { item, index } = info;
    const isActive = pageIndex === index;
    const tagTextStyle = isActive ? styles.tagTextActive : styles.tagText;

    const renderLabel = () => {
      if (item === AllSettingTabs.Printer && shouldDisplayExclamation) {
        return <ExclamationRed width={20} height={20} style={styles.ml20} />;
      }
      if (item === AllSettingTabs.DefaultNetwork && (!isSame || !isEnabledNetwork)) {
        return <ExclamationRed width={20} height={20} style={styles.ml20} />;
      }
      const settingTag = findSettingTag(item);
      if (settingTag) {
        return (
          <View style={[styles.tagBlock, isActive ? styles.tagBlockActive : styles.tagBlockInActive]}>
            <Text style={tagTextStyle}>{settingTag.label || t('NEW')}</Text>
          </View>
        );
      }

      return null;
    };

    return (
      <TouchableOpacity activeOpacity={1} {...testProps(`al_leftTab_${item}`)} key={`${index}${item}`} onPress={() => onPressHandler(index)}>
        <View style={[styles.tab, isActive && { backgroundColor: '#008EC4' }]}>
          <Text style={[styles.tabText, isActive && { color: '#FFF' }]}>{item}</Text>
          {renderLabel()}
        </View>
      </TouchableOpacity>
    );
  };
  return (
    <View style={SharedStyles.flexOne}>
      <FlatList
        style={SharedStyles.flexOne}
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        directionalLockEnabled={true}
        bounces={false}
        scrollsToTop={false}
        data={tabNameList}
        renderItem={renderItem}
      />
      <Version />
    </View>
  );
};

export default memo(SettingTabBar);

const styles = StyleSheet.create({
  tab: { flexDirection: 'row', height: scaleSizeH(88), paddingLeft: scaleSizeW(34), alignItems: 'center' },
  tabText: {
    height: scaleSizeH(88),
    lineHeight: scaleSizeH(88), // ios
    fontSize: currentThemes.fontSize24,
    fontWeight: '500',
    color: '#60636B',
    textAlignVertical: 'center', // android
  },
  tagBlock: {
    flexDirection: 'row',
    paddingHorizontal: scaleSizeW(16),
    paddingVertical: scaleSizeH(6),
    borderRadius: scaleSizeW(8),
    marginLeft: scaleSizeW(24),
    alignItems: 'center',
    justifyContent: 'center',
  },
  tagBlockInActive: {
    backgroundColor: '#FFF0E7',
  },
  tagBlockActive: {
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: 'white',
    backgroundColor: 'transparent',
  },
  tagText: {
    fontSize: currentThemes.fontSize16,
    fontWeight: '400',
    color: CommonColors.Pumpkin,
  },
  tagTextActive: {
    fontSize: currentThemes.fontSize16,
    fontWeight: '400',
    color: 'white',
  },
  ml20: {
    marginLeft: 20,
  },
});
