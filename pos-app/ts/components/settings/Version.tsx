import moment from 'moment';
import React, { FC, memo, useRef } from 'react';
import { Alert, TouchableWithoutFeedback, View } from 'react-native';
import { AWS_S3_DB_ACL, AWS_S3_DB_BUCKET, AWS_S3_REGION } from '../../config';
import { IsAndroid, scaleSizeH, scaleSizeW, t } from '../../constants';

import RNFS from 'react-native-fs';

import { useMemoizedFn } from 'ahooks';
import { useSelector } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { RequestActionAccessResult, uploadFileToS3WithCheckInvalid } from '../../actions';
import { useActions } from '../../hooks';
import { AppVersionLabel, RegisterId, Timestamp, UniqueTimestamp } from '../../hooks/common/codePush';
import { useAppNavigation } from '../../hooks/common/useAppNavigation';
import {
  selectBusinessName,
  selectCountry,
  selectLocalLoggingMailAddress,
  selectLocalLoggingMailLocalLogPin,
  selectRegisterId,
  selectShiftOpenStatus,
} from '../../sagas/selector';
import { testProps } from '../../utils';
import LocalLogger from '../../utils/logComponent/local-logging/LocalLogger';

const selector = createStructuredSelector({
  shiftOpenStatus: selectShiftOpenStatus,
  businessName: selectBusinessName,
  registerId: selectRegisterId,
  country: selectCountry,
  mailPin: selectLocalLoggingMailLocalLogPin,
  mailAddress: selectLocalLoggingMailAddress,
});
const Version: FC = () => {
  const clickCount = useRef(0);
  const navigation = useAppNavigation();
  const { shiftOpenStatus, businessName, registerId, country, mailPin, mailAddress } = useSelector(selector);
  const dispatch = useActions({ uploadFileToS3WithCheckInvalid });

  const onUploadComplete = response => {
    if (response.status === 201) {
      navigation.navigate('ModalInfo', {
        info: 'Upload Successful',
        okText: t('OK'),
      });
    } else {
      navigation.navigate('ModalInfo', {
        info: 'Upload Failed',
        okText: t('OK'),
      });
    }
  };

  const handleVersionClick = useMemoizedFn(() => {
    clickCount.current++;

    if (clickCount.current === 3) {
      clickCount.current = 0;
      if (shiftOpenStatus) {
        navigation.navigate('ModalInfo', {
          title: t('Upload data file to StoreHub'),
          isShowTitle: true,
          info: t('Please close your shift before uploading your data'),
          okText: t('OKAY'),
        });
      } else {
        const appDirectory = RNFS.DocumentDirectoryPath;
        const realmFilePath = `${appDirectory}/default.realm`;

        RNFS.exists(realmFilePath)
          .then(exist => {
            if (exist) {
              const time = moment().format('yyyy-MM-DD_HH:mm:ss');
              navigation.navigate('ModalInfo', {
                title: t('Upload data file to StoreHub'),
                isShowTitle: true,
                info: t('This will allow us to assess your data issues such as duplicated transactions'),
                okText: t('UPLOAD'),
                cancelText: t('CANCEL'),
                onSubmitHandler: () => {
                  const file = {
                    uri: IsAndroid ? `file://${realmFilePath}` : realmFilePath,
                    name: 'default.realm',
                    type: 'application/plain',
                  };

                  const options = {
                    acl: AWS_S3_DB_ACL,
                    keyPrefix: `${businessName}/${registerId}/${time}/`,
                    bucket: AWS_S3_DB_BUCKET,
                    region: AWS_S3_REGION,
                    // accessKey: AWS_ACESS_KEY_ID,
                    // secretKey: AWS_SECRET_ACCESS_KEY,
                    successActionStatus: 201,
                  };
                  dispatch.uploadFileToS3WithCheckInvalid({ file, options, onComplete: onUploadComplete });
                  // RNS3.put(file, options).then(response => {
                  //   console.log('RNS3.put response = ', response);
                  //   if (response.status === 201) {
                  //     navigation.navigate('ModalInfo', {
                  //       info: 'Upload Successful',
                  //       okText: t('OK'),
                  //     });
                  //   } else {
                  //     navigation.navigate('ModalInfo', {
                  //       info: 'Upload Failed',
                  //       okText: t('OK'),
                  //     });
                  //   }
                  // });
                },
                onCancelHandler: () => {
                  console.log('cancel clicked');
                },
              });
            }
          })
          .catch(err => {
            console.log('realm error: ' + err);
          });
      }
    }
  });

  const handleVersionLongPress = async () => {
    if (!mailPin || !mailAddress) {
      return;
    }
    try {
      const logFiles = await LocalLogger.getLogFilePaths();
      if (logFiles.length === 0) {
        Alert.alert('No logs to upload');
        return;
      }
    } catch (ex) {
      Alert.alert('Error', `Failed to get logs: ${ex}`);
      console.error('error', ex);
    }
    navigation.navigate('ModalManagerPin', {
      authPinCode: mailPin,
      showBackground: false,
      hint: 'Enter Auth PIN to upload logs',
      onDismissHandler: async (result: RequestActionAccessResult) => {
        try {
          if (result.ok) {
            await LocalLogger.sendLogsByEmail({
              to: mailAddress,
              subject: `${businessName}_${registerId}_LocalLogs`,
              body: 'Please investigate the attached logs for any issues.',
            });
            Alert.alert('Success', 'We have got your logs. Thank you for your help!');
          }
        } catch (error) {
          Alert.alert('Error', `Failed to send logs: ${error}`);
          console.error('error', error);
        }
      },
    });
  };
  return (
    <TouchableWithoutFeedback {...testProps('al_btn_63')} onPress={handleVersionClick} onLongPress={handleVersionLongPress}>
      <View style={[{ paddingBottom: scaleSizeH(16), left: scaleSizeW(34) }]}>
        <AppVersionLabel country={country} />
        <Timestamp />
        <UniqueTimestamp />
        <RegisterId />
      </View>
    </TouchableWithoutFeedback>
  );
};

export default memo(Version);
