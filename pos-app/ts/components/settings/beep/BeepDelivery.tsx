import { useMemoizedFn } from 'ahooks';

import { map, noop } from 'lodash';
import React, { memo } from 'react';

import { StyleSheet, Text, View } from 'react-native';

import { PauseModeEnum } from '../../../actions';
import { currentThemes, scaleSizeH, scaleSizeW, t } from '../../../constants';
import { usePauseMode } from '../../../hooks';

import { useSelector } from 'react-redux';
import { useAppNavigation } from '../../../hooks/common/useAppNavigation';
import { selectPauseBeepDelivery } from '../../../sagas/selector';
import RadioRow from '../RadioRow';
import { settingGrayBorder } from '../styles';
import PauseBeepNotification from './PauseBeepNotification';

const pauseModeSetting = [
  { label: t('Close for 30 minutes'), value: PauseModeEnum.MINS_30 },
  { label: t('Close for 1 hour'), value: PauseModeEnum.MINS_60 },
  { label: t('Close for rest of the day'), value: PauseModeEnum.EOD },
];
const OPEN_DELIVERY = 'OPEN_DELIVERY';

const renderPopupInfo = () => (
  <Text
    style={{
      fontSize: currentThemes.fontSize24,
      color: '#60636B',
      fontWeight: '400',
      textAlign: 'center',
      marginBottom: scaleSizeH(12),
      paddingHorizontal: scaleSizeW(60),
      paddingVertical: scaleSizeH(20),
      textAlignVertical: 'center',
    }}
  >
    {t('Are you ready to reopen for delivery now?')}
  </Text>
);

const BeepDelivery = () => {
  const navigator = useAppNavigation();
  const { openForDelivery, closeDelivery, pauseModeInfo } = usePauseMode();
  const paused = pauseModeInfo.paused;
  const pauseValue = useSelector(selectPauseBeepDelivery);

  const handleOpenPress = useMemoizedFn(value => {
    if (value === OPEN_DELIVERY && paused) {
      const onProceed = () => openForDelivery({});
      navigator.navigate('ModalInfo', {
        isShowTitle: true,
        title: t('Open for delivery'),
        infoFn: renderPopupInfo,
        okText: t('CANCEL'),
        cancelText: t('PROCEED'),
        textAlign: 'center',
        onCancelHandler: onProceed,
        onSubmitHandler: noop,
      });
    }
  });

  const handlePauseItemPress = async (pauseMode: PauseModeEnum) => {
    closeDelivery({
      pauseMode,
    });
  };
  const renderPauseBeepDelivery = () => {
    return map(pauseModeSetting, it => (
      <RadioRow
        key={it.value}
        rowStyle={[styles.mb24, settingGrayBorder]}
        label={it.label}
        value={it.value}
        isSelected={paused && pauseValue === it.value}
        onPress={handlePauseItemPress}
      />
    ));
  };
  const rowOne = StyleSheet.compose(styles.mb46, paused ? { marginTop: scaleSizeH(70) } : { marginTop: scaleSizeH(24) });
  return (
    <View>
      <PauseBeepNotification containerStyle={styles.pauseNotification} showReopen={false} />
      <View style={styles.container}>
        <View style={rowOne}>
          <Text style={styles.title}>{t('Open for Beep Delivery')}</Text>
          <Text style={styles.subTitle}>{t("This will be applied during your store's Beep Delivery opening hours")}</Text>
        </View>
        <RadioRow rowStyle={settingGrayBorder} label={t('Open for delivery')} value={OPEN_DELIVERY} isSelected={!paused} onPress={handleOpenPress} />

        <View style={styles.mv46}>
          <Text style={styles.title}>{t('Pause Beep Delivery')}</Text>
          <Text style={styles.subTitle}>{t("Enabling this will temporarily close your store's Beep Delivery in the time duration of your choice")}</Text>
        </View>
        {renderPauseBeepDelivery()}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  pauseNotification: {
    position: 'absolute',
    width: '100%',
    paddingLeft: scaleSizeW(30),
  },
  container: {
    paddingHorizontal: scaleSizeW(30),
  },
  mb46: {
    marginBottom: scaleSizeH(46),
  },
  mv46: {
    marginVertical: scaleSizeH(46),
  },
  title: {
    color: '#303030',
    fontSize: currentThemes.fontSize28,
    fontWeight: '500',
    lineHeight: scaleSizeH(48),
  },
  subTitle: {
    color: '#959595',
    fontSize: currentThemes.fontSize18,
    fontWeight: '500',
  },
  mb24: {
    marginBottom: scaleSizeH(24),
  },
});

export default memo(BeepDelivery);
