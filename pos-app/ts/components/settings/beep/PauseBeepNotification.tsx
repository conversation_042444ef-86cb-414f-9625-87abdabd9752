import React, { FC, useEffect } from 'react';
import { StyleSheet, Text, View, ViewStyle } from 'react-native';

import { CommonColors, currentThemes, scaleSizeH, scaleSizeW, t } from '../../../constants';
import { AllSettingTabs } from '../../../constants/settingTabs';
import { usePauseMode } from '../../../hooks';
import { navigateToSettings } from '../../../navigation/commonNavigate';
import { IconNotification } from '../../ui';

export type Props = {
  showReopen?: boolean;
  containerStyle?: ViewStyle;
  maxWidth?: number;
};

const PauseBeepNotification: FC<Props> = props => {
  const { showReopen = true, containerStyle, maxWidth = 380 } = props;
  const {
    pauseModeInfo: { paused, reopenTime },
    updatePauseMode,
  } = usePauseMode();

  useEffect(() => {
    updatePauseMode();
  }, []);

  if (!paused) {
    return null;
  }
  return (
    <View style={StyleSheet.flatten([styles.row, containerStyle])}>
      <View style={styles.rowCenter}>
        <IconNotification width={scaleSizeW(20)} height={scaleSizeW(20)} color='#121738' />
        <Text style={styles.time}>{t('Store closed for delivery until x', { time: reopenTime })}</Text>
        {maxWidth >= 380 && showReopen && (
          <Text
            style={styles.reopen}
            onPress={() => {
              navigateToSettings({ tabName: AllSettingTabs.BeepDelivery, timeStamp: Date.now() });
            }}
          >
            {t('Reopen now')}
          </Text>
        )}
      </View>
      {maxWidth < 380 && showReopen && (
        <Text
          style={reopenRow}
          onPress={() => {
            navigateToSettings({ tabName: AllSettingTabs.BeepDelivery, timeStamp: Date.now() });
          }}
        >
          {t('Reopen now')}
        </Text>
      )}
    </View>
  );
};

export default PauseBeepNotification;

const styles = StyleSheet.create({
  row: {
    backgroundColor: '#FEE8CB',
    minHeight: scaleSizeH(56),
    paddingVertical: scaleSizeH(12),
    justifyContent: 'center',
  },
  rowCenter: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: scaleSizeW(10),
    paddingRight: scaleSizeW(10),
  },
  time: {
    color: '#303030',
    fontSize: currentThemes.fontSize20,
    fontWeight: '400',
    marginLeft: scaleSizeW(12),
    marginRight: scaleSizeW(24),
  },
  reopen: {
    fontSize: currentThemes.fontSize20,
    fontWeight: '700',
    color: CommonColors.Pumpkin,
  },
});

const reopenRow = StyleSheet.flatten([styles.reopen, { marginLeft: scaleSizeW(50) }]);
