import React, { memo } from 'react';
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { DetailTitle, SalesSummaryItem } from '.';
import { CommonColors, currentThemes, scaleSizeH, scaleSizeW, t } from '../../constants';
import { CashDrawerSummary, PaymentsSummary, SaleSummary, TopOverviewFields } from '../../sagas/shift/common';
import { testProps } from '../../utils';
import { IconInfo, IconQuestion } from '../ui';

interface SaleSummaryProps {
  saleSummary?: SaleSummary;
  enableLoyalty?: boolean;
  enableCashback?: boolean;
  makeStoreCreditAsPayment?: boolean;
}

export const SalesSummary = memo((props: SaleSummaryProps) => {
  const { saleSummary = {}, enableLoyalty, enableCashback, makeStoreCreditAsPayment } = props;
  const {
    grossSales = '0.00',
    grossReturn = '0.00',
    discount = '0.00',
    loyaltyDiscount = '0.00',
    netSales = '0.00',
    deposit = '0.00',
    rounded = '0.00',
    totalTax = '0.00',
    total = '0.00',
    serviceCharge = '0.00',
  } = saleSummary;
  return (
    <View style={styles.reportItem}>
      <ScrollView>
        <SalesSummaryItem title={t('Gross Sales')} content={grossSales} isShowBackgroundColor={false} isBold={false} />
        <SalesSummaryItem title={t('Refunds')} content={grossReturn} isShowBackgroundColor={true} isBold={false} />
        <SalesSummaryItem title={t('Discount')} content={discount} isShowBackgroundColor={false} isBold={false} />
        {(enableCashback || (enableLoyalty && !makeStoreCreditAsPayment)) && (
          <SalesSummaryItem
            title={enableCashback ? t('Cashback Discount') : t('Store Credit Discount')}
            content={`- ${loyaltyDiscount}`}
            isShowBackgroundColor={true}
            isBold={false}
          />
        )}
        <SalesSummaryItem title={t('Net Sales')} content={netSales} isShowBackgroundColor={!enableCashback} isBold={false} />
        <SalesSummaryItem title={t('Deposit')} content={deposit} isShowBackgroundColor={enableCashback} isBold={false} />
        <SalesSummaryItem title={t('Service Charge')} content={serviceCharge} isShowBackgroundColor={enableCashback} isBold={false} />
        <SalesSummaryItem title={t('Rounding')} content={rounded} isShowBackgroundColor={!enableCashback} isBold={false} />
        <SalesSummaryItem title={t('Tax')} content={totalTax} isShowBackgroundColor={enableCashback} isBold={false} />
      </ScrollView>
      <DetailTitle title={t('Total Tendered')} content={total} />
    </View>
  );
});

interface CashDrawerSummaryProps {
  cashDrawerSummary?: CashDrawerSummary;
  shiftOpenStatus?: boolean;
  onOpenPayList(): void;
}

export const CashSummary = memo((props: CashDrawerSummaryProps) => {
  const { cashDrawerSummary = {}, shiftOpenStatus, onOpenPayList } = props;
  const {
    openingAmount = '0.00',
    totalCashSales = '0.00',
    cashDeposit = '0.00',
    cashReturn = '0.00',
    cashRounded = '0.00',
    payinoutSum = '0.00/-0.00',
    actualDrawer = '0.00',
    expectedDrawer = '0.00',
    overShort = '0.00',
    payListEnable = '0.00',
  } = cashDrawerSummary;
  return (
    <View style={styles.reportItem}>
      <ScrollView>
        <SalesSummaryItem title={t('Opening Amount')} content={openingAmount} isShowBackgroundColor={false} isBold={false} />
        <SalesSummaryItem title={t('Cash Sales')} content={totalCashSales} isShowBackgroundColor={true} isBold={false} />
        <SalesSummaryItem title={t('Cash Deposit')} content={cashDeposit} isShowBackgroundColor={false} isBold={false} />
        <SalesSummaryItem title={t('Cash Refunds')} content={cashReturn} isShowBackgroundColor={true} isBold={false} />
        <SalesSummaryItem title={t('Cash Rounding')} content={cashRounded} isShowBackgroundColor={false} isBold={false} />
        {!shiftOpenStatus && <SalesSummaryItem title={t('Expected Drawer')} content={expectedDrawer} isShowBackgroundColor={true} isBold={false} />}
        <View style={[styles.summaryItem, { backgroundColor: !shiftOpenStatus ? '#FFF' : '#F9F9F9' }]}>
          <Text {...testProps('al_text_827')} style={[styles.contentFont, { flex: 1 }]}>
            {t('Pay In/Out')}
          </Text>
          {payListEnable && (
            <TouchableOpacity {...testProps('al_btn_455')} style={styles.iconBtnContainer} onPress={onOpenPayList}>
              <IconQuestion width={scaleSizeW(40)} height={scaleSizeH(40)} color={CommonColors.Icon} />
            </TouchableOpacity>
          )}
          <Text {...testProps('al_text_171')} style={styles.payCountText}>
            {payinoutSum}
          </Text>
        </View>
        {!shiftOpenStatus && <SalesSummaryItem title={t('Over/Short')} content={overShort} isShowBackgroundColor={true} isBold={false} />}
      </ScrollView>
      <DetailTitle title={shiftOpenStatus ? t('Expected Drawer') : t('Actual Drawer')} content={shiftOpenStatus ? expectedDrawer : actualDrawer} />
    </View>
  );
});

interface PaymentsSummaryProps {
  paymentsSummary?: PaymentsSummary;
}

export const PaymentSummary = memo((props: PaymentsSummaryProps) => {
  const { paymentsSummary = {} } = props;
  const { paymentsList = [], total = '0.00' } = paymentsSummary;
  return (
    <View style={styles.reportItem}>
      <ScrollView>
        {paymentsList.map((paymentItem, index) => {
          const { paymentName, paymentAmount, paymentId, _id } = paymentItem;
          return (
            <View style={[styles.summaryItem, { backgroundColor: index % 2 === 0 ? '#FFF' : '#F9F9F9' }]} key={paymentId}>
              <Text {...testProps('al_text_958')} style={{ ...StyleSheet.flatten(styles.contentFont), width: '30%' }}>
                {paymentName}
              </Text>
              <Text {...testProps('al_text_793')} style={[styles.contentFont, styles.summaryValueFont]}>
                {paymentAmount}
              </Text>
            </View>
          );
        })}
      </ScrollView>
      <DetailTitle title={t('Total')} content={total} />
    </View>
  );
});

export interface DailyReportItem {
  name: string;
  count: string;
  amount: string;
}
export interface DailyReportSummaryType {
  dailyReportChannels?: DailyReportItem[];
  totalAmount?: string;
  totalQty?: string;
  averageOrderValue?: string;
  reportDate?: string;
}
interface DailyReportSummaryProps {
  dailyReportSummary?: DailyReportSummaryType;
  onInfoClick?: (infoTitle, infoContent) => void;
}

export const DailyReportSummary = memo((props: DailyReportSummaryProps) => {
  const { dailyReportSummary, onInfoClick } = props;
  const { dailyReportChannels = [], totalAmount = '0.00' } = dailyReportSummary;
  return (
    <View style={styles.reportItem}>
      <ScrollView>
        {dailyReportChannels.map((dailyReportChannel, index) => {
          const { name, amount } = dailyReportChannel;
          let infoTitle;
          let infoContent;
          if ('Beep QR (Online Payment)' === name) {
            infoTitle = t('Online Payments');
            infoContent = t('Beep QR Online Payments desc');
          }
          if ('Beep QR (Offline Payment)' === name) {
            infoTitle = t('Offline Payments');
            infoContent = t('Beep QR Offline Payments desc');
          }
          return (
            <View style={[styles.summaryItem, { backgroundColor: index % 2 === 0 ? '#FFF' : '#F9F9F9' }]} key={name}>
              <Text {...testProps('al_text_710')} style={{ ...StyleSheet.flatten(styles.contentFont) }}>
                {name}
              </Text>
              {Boolean(infoTitle) && (
                <TouchableOpacity
                  {...testProps('al_btn_494')}
                  style={styles.infoContainer}
                  onPress={() => {
                    onInfoClick && onInfoClick(infoTitle, infoContent);
                  }}
                >
                  <IconInfo width={scaleSizeW(30)} height={scaleSizeH(30)} color={'#303030'} />
                </TouchableOpacity>
              )}
              <Text {...testProps('al_text_564')} style={[styles.contentFont, styles.summaryValueFont]}>
                {amount}
              </Text>
            </View>
          );
        })}
      </ScrollView>
      <DetailTitle title={t('Total Tendered')} content={totalAmount} />
    </View>
  );
});

interface TopOverviewProps {
  topOverviewFields?: TopOverviewFields;
}

export const TopOverview = memo((props: TopOverviewProps) => {
  const { topOverviewFields = {} } = props;
  const { netSales = '0.00', count = '0', avgAmount = '0.00' } = topOverviewFields;
  return (
    <View style={styles.rightTopTransactionContent}>
      <View style={styles.rightTopTransactionItem}>
        <Text {...testProps('al_text_785')} style={styles.transactionItemTip}>
          {t('TRANSACTIONS')}
        </Text>
        <Text {...testProps('al_text_262')} style={styles.transactionItemAmount}>
          {count}
        </Text>
      </View>
      <View style={styles.rightTopTransactionItem}>
        <Text {...testProps('al_text_662')} style={styles.transactionItemTip}>
          {t('AVERAGE ORDER VALUE')}
        </Text>
        <Text {...testProps('al_text_663')} style={styles.transactionItemAmount}>
          {avgAmount}
        </Text>
      </View>
      <View style={styles.rightTopTransactionItem}>
        <Text {...testProps('al_text_149')} style={[styles.transactionItemTip, { color: '#FC7118', textAlign: 'right' }]}>
          {t('TOTAL NET SALES')}
        </Text>
        <Text {...testProps('al_text_548')} style={[styles.transactionItemAmount, { textAlign: 'right' }]}>
          {netSales}
        </Text>
      </View>
    </View>
  );
});

const styles = StyleSheet.create({
  reportItem: {
    paddingTop: scaleSizeH(32),
    flex: 1,
    borderRadius: scaleSizeW(8),
    overflow: 'hidden',
  },
  summaryItem: {
    backgroundColor: '#F9F9F9',
    alignItems: 'center',
    flexDirection: 'row',
    paddingHorizontal: scaleSizeW(32),
    height: scaleSizeH(64),
  },
  contentFont: {
    color: '#60636B',
    fontSize: currentThemes.fontSize26,
    fontWeight: '400',
    textAlignVertical: 'center',
  },
  iconBtnContainer: {
    height: '100%',
    paddingHorizontal: scaleSizeW(12),
    alignItems: 'center',
    justifyContent: 'center',
  },
  payCountText: {
    fontWeight: '500',
    fontSize: currentThemes.fontSize24,
    textAlign: 'right',
    color: '#303030',
    marginLeft: scaleSizeW(6),
  },
  summaryValueFont: {
    fontWeight: '500',
    fontSize: currentThemes.fontSize24,
    flex: 1,
    textAlign: 'right',
    color: '#303030',
  },
  rightTopTransactionContent: {
    width: '100%',
    paddingHorizontal: scaleSizeW(8),
    flexDirection: 'row',
  },
  rightTopTransactionItem: {
    flex: 1,
    flexDirection: 'column',
    marginTop: scaleSizeH(42),
    marginHorizontal: scaleSizeW(16),
  },
  transactionItemTip: {
    color: '#959595',
    fontSize: currentThemes.fontSize18,
    fontWeight: '500',
  },
  transactionItemAmount: {
    color: '#303030',
    fontSize: currentThemes.fontSize64,
    fontWeight: 'bold',
  },
  infoContainer: {
    paddingHorizontal: scaleSizeW(8),
    marginLeft: scaleSizeW(8),
    height: '80%',
    alignItems: 'center',
    justifyContent: 'center',
  },
});
