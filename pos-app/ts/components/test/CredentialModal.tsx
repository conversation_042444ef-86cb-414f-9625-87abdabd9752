import React, { useEffect, useState } from 'react';
import { ActivityIndicator, FlatList, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { ConnectedProps, connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { setDevPanelData } from '../../actions';
import { currentThemes, scaleSizeH, scaleSizeW } from '../../constants';
import { ScreenProps } from '../../typings';
import { DevPanelAction, logDevPanelEvent } from '../../utils/logComponent/buz/devPanel';
import { testProps } from '../../utils';

interface CredentialModalProps {
  externalProp: string;
}

type PropsFromRedux = ConnectedProps<typeof connector>;
interface Props extends CredentialModalProps, ScreenProps, PropsFromRedux {}

const mapStateToProps = null;

const mapDispatchToProps = dispatch => ({
  actions: bindActionCreators(
    {
      setDevPanelData,
    },
    dispatch
  ),
});
const connector = connect(mapStateToProps, mapDispatchToProps);

const CredentialModal: React.FC<Props> = props => {
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [selectedItem, setSelectedItem] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // 5s timeout
        const response = await fetchWithTimeout('https://fat-rnpos-ipa.s3.ap-southeast-1.amazonaws.com/devPanelData.json', 5000);
        const jsonData = await response.json();
        setDataSource(
          JSON.parse(
            '[{"bn":"stephen666","eml":"<EMAIL>","pwd":"111111","pin":"2222"},{"bn":"kamdar","eml":"<EMAIL>","pwd":"123456","pin":"1111"},{"bn":"justcoffee","eml":"<EMAIL>","pwd":"123456","pin":"2222"},{"bn":"feida","eml":"<EMAIL>","pwd":"123456","pin":"1111"},{"bn":"ancafe","eml":"<EMAIL>","pwd":"123456","pin":"1234"},{"bn":"kafe123","eml":"<EMAIL>","pwd":"111111","pin":"1111"},{"bn":"cathy6","eml":"<EMAIL>","pwd":"123456","pin":"6721"},{"bn":"leonphtestfood","eml":"<EMAIL>","pwd":"111111","pin":"1111","comment":"BIR, updated by Grace"},{"bn":"thteststore","eml":"<EMAIL>","pwd":"Test123","pin":"3333","comment":"TH"},{"bn":"myteststore","eml":"<EMAIL>","pwd":"Test123","pin":"1111"},{"bn":"birph","eml":"<EMAIL>","pwd":"test123123","pin":"1111","comment":"FDI"},{"bn":"chunyuanth","eml":"<EMAIL>","pwd":"Ccy@123456","pin":""},{"bn":"onlytestaccount","eml":"<EMAIL>","pwd":"test123123","pin":""},{"bn":"kdstest","eml":"<EMAIL>","pwd":"123456","pin":"1111"},{"bn":"cafe95425676","eml":"<EMAIL>","pwd":"Divya123","pin":"2998"},{"bn":"productstore","eml":"<EMAIL>","pwd":"123456","pin":"1282"},{"bn": "mytest","eml": "<EMAIL>","pwd": "123456","pin": ""}]'
          )
        );
      } catch (error) {
        setDataSource(
          JSON.parse(
            '[{"bn":"stephen666","eml":"<EMAIL>","pwd":"111111","pin":"2222"},{"bn":"kamdar","eml":"<EMAIL>","pwd":"123456","pin":"1111"},{"bn":"justcoffee","eml":"<EMAIL>","pwd":"123456","pin":"2222"},{"bn":"feida","eml":"<EMAIL>","pwd":"123456","pin":"1111"},{"bn":"ancafe","eml":"<EMAIL>","pwd":"123456","pin":"1234"},{"bn":"kafe123","eml":"<EMAIL>","pwd":"111111","pin":"1111"},{"bn":"cathy6","eml":"<EMAIL>","pwd":"123456","pin":"6721"},{"bn":"leonphtestfood","eml":"<EMAIL>","pwd":"111111","pin":"1111","comment":"BIR, updated by Grace"},{"bn":"thteststore","eml":"<EMAIL>","pwd":"Test123","pin":"3333","comment":"TH"},{"bn":"myteststore","eml":"<EMAIL>","pwd":"Test123","pin":"1111"},{"bn":"birph","eml":"<EMAIL>","pwd":"test123123","pin":"1111","comment":"FDI"},{"bn":"chunyuanth","eml":"<EMAIL>","pwd":"Ccy@123456","pin":""},{"bn":"onlytestaccount","eml":"<EMAIL>","pwd":"test123123","pin":""},{"bn":"kdstest","eml":"<EMAIL>","pwd":"123456","pin":"1111"},{"bn":"cafe95425676","eml":"<EMAIL>","pwd":"Divya123","pin":"2998"},{"bn":"productstore","eml":"<EMAIL>","pwd":"123456","pin":"1282"}, {"bn": "mytest","eml": "<EMAIL>","pwd": "123456","pin": ""}]'
          )
        );
        console.error('request error:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  const fetchWithTimeout = (urlStr, timeout): Promise<Response> => {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error('request timeout'));
      }, timeout);

      fetch(urlStr)
        .then(resolve, reject)
        .finally(() => clearTimeout(timeoutId));
    });
  };

  const renderItem = ({ item, index }) => {
    const isSelected = selectedItem === index;
    return (
      <TouchableOpacity
        {...testProps('al_btn_249')}
        style={[styles.itemContainer, isSelected ? styles.selectedItemContainer : {}]}
        onPress={() => setSelectedItem(index)}
      >
        <View style={[{ flex: 1 }, styles.fieldContainer]}>
          <Text>{item.bn}</Text>
        </View>
        <View style={[{ flex: 2 }, styles.fieldContainer]}>
          <Text>{item.eml}</Text>
        </View>
        <View style={[{ flex: 1 }, styles.fieldContainer]}>
          <Text>{item.pwd}</Text>
        </View>
        <View style={[{ flex: 1 }, styles.fieldContainer]}>
          <Text>{item.pin}</Text>
        </View>
        <View style={[{ flex: 1 }, styles.fieldContainer]}>
          <Text>{item.comment}</Text>
        </View>
      </TouchableOpacity>
    );
  };

  const renderContent = () => {
    return (
      <>
        <View style={styles.header}>
          <View style={[{ flex: 1 }, styles.headerContainer]}>
            <Text style={styles.headerText}>Business Name</Text>
          </View>
          <View style={[{ flex: 2 }, styles.headerContainer]}>
            <Text style={styles.headerText}>Email</Text>
          </View>
          <View style={[{ flex: 1 }, styles.headerContainer]}>
            <Text style={styles.headerText}>Password</Text>
          </View>
          <View style={[{ flex: 1 }, styles.headerContainer]}>
            <Text style={styles.headerText}>PIN</Text>
          </View>
          <View style={[{ flex: 1 }, styles.headerContainer]}>
            <Text style={styles.headerText}>COMMENT</Text>
          </View>
        </View>
        <FlatList data={dataSource} keyExtractor={(item, index) => item + index} renderItem={renderItem} style={styles.flatListStyle} />
      </>
    );
  };

  const renderNoResult = () => {
    return (
      <View style={styles.noResultContainer}>
        <Text style={styles.noResultText}>No credential found</Text>
      </View>
    );
  };

  if (isLoading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size='large' />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {dataSource?.length === 0 ? renderNoResult() : renderContent()}

      <View style={styles.bottomButtons}>
        <TouchableOpacity
          {...testProps('al_btn_169')}
          onPress={() => {
            props.navigation.goBack();

            logDevPanelEvent({
              action: DevPanelAction.DevPanelClose,
            });
          }}
          style={styles.closeButton}
        >
          <Text style={styles.closeButtonText}>Close</Text>
        </TouchableOpacity>
        <TouchableOpacity
          {...testProps('al_btn_686')}
          onPress={() => {
            props.navigation.goBack();
            props.actions.setDevPanelData(dataSource[selectedItem]);

            logDevPanelEvent({
              action: DevPanelAction.DevPanelConfirm,
            });
          }}
          style={selectedItem === null ? styles.confirmButtonDisabled : styles.confirmButton}
          disabled={selectedItem === null}
        >
          <Text style={styles.confirmButtonText}>Confirm</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'rgba(38, 33, 28, 1.0)',
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 20,
    // width: '90%',
    // height: '80%',
  },
  itemContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
    backgroundColor: 'white',
    flexDirection: 'row',
    height: 80,
  },
  flatListStyle: {
    backgroundColor: 'white',
    width: '80%',
  },
  fieldContainer: {
    marginHorizontal: scaleSizeW(10),
    // backgroundColor: 'white',
    flexDirection: 'column',
    // flex: 1,
    justifyContent: 'center',
  },
  header: {
    flexDirection: 'row',
    backgroundColor: 'lightgray',
    width: '80%',
    height: scaleSizeH(70),
    padding: 16,
  },
  headerContainer: {
    marginHorizontal: scaleSizeW(10),
    flexDirection: 'column',
    justifyContent: 'center',
  },
  headerText: {
    fontSize: currentThemes.fontSize26,
    fontWeight: 'bold',
  },
  selectedItemContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
    backgroundColor: 'rgba(0, 132, 186, 0.2)',
    // backgroundColor: 'rgba(255, 228, 198, 1.0)',
    flexDirection: 'row',
    height: 80,
  },
  bottomButtons: {
    height: scaleSizeH(80),
    width: '80%',
    flexDirection: 'row',
    marginBottom: scaleSizeH(100),
  },
  closeButton: {
    flex: 1,
    backgroundColor: 'rgba(255, 228, 198, 1.0)',
    justifyContent: 'center',
    alignItems: 'center',
    borderBottomLeftRadius: scaleSizeW(8),
  },
  closeButtonText: {
    fontSize: currentThemes.fontSize26,
    fontWeight: 'bold',
    color: 'rgba(255, 112, 52, 1.0)',
  },
  confirmButton: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 102, 36, 1.0)',
    borderBottomRightRadius: scaleSizeW(8),
  },
  confirmButtonText: {
    fontSize: currentThemes.fontSize26,
    fontWeight: 'bold',
    color: 'white',
  },
  confirmButtonDisabled: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(214, 214, 214, 1.0)',
    borderBottomRightRadius: scaleSizeW(8),
  },
  noResultContainer: {
    flex: 1,
    backgroundColor: 'white',
    width: '80%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  noResultText: {
    fontSize: currentThemes.fontSize30,
  },
});

export default connector(CredentialModal);
