import React, { useState } from 'react';
import { ScrollView, StyleSheet, Text } from 'react-native';
import ActionButton from 'react-native-action-button';
import Icon from 'react-native-vector-icons/Ionicons';
import { useDispatch, useSelector } from 'react-redux';

import { deactivateAllRegisters, deactivateRegister } from '../../actions';
import { scaleSizeH, scaleSizeW, t } from '../../constants';
import LocalReceiptTestPage from '../../containers/Settings/LocalReceiptTestPage';
import * as NavigationService from '../../navigation/navigatorService';
import { selectCfdConfigurations } from '../../sagas/selector';
import { FloatingWindow } from './FloatingWindow';
import NetworkQualityTestPage from './NetworkQualityTestPage';
import NewPrinterTestPage from './printer/NewPrinterTestPage';
import TestTools from './TestTools';

const DevShortcut = () => {
  const dataTest = useSelector(selectCfdConfigurations);
  const [showCons, setShowCons] = useState(false);
  const [showPrinterTest, setShowPrinterTest] = useState(false);
  const [showNetworkTest, setShowNetworkTest] = useState(false);
  const [showReceiptTest, setShowReceiptTest] = useState(false);
  const [showProductTest, setShowProductTest] = useState(false);
  const dispatch = useDispatch();
  return (
    <>
      {showCons && (
        <ScrollView style={{ height: 20, backgroundColor: 'black' }}>
          <Text style={{ fontSize: 12, color: 'white' }}>{JSON.stringify(dataTest, null, 2)}</Text>
        </ScrollView>
      )}

      <FloatingWindow
        isVisible={showPrinterTest}
        title='Printer Testing Page'
        onClose={() => setShowPrinterTest(false)}
        position='left'
        offsetX={scaleSizeW(70)}
        offsetY={scaleSizeH(200)}
      >
        <NewPrinterTestPage onClose={() => setShowPrinterTest(false)} />
      </FloatingWindow>

      <FloatingWindow
        isVisible={showNetworkTest}
        title='Network Testing Page'
        onClose={() => setShowNetworkTest(false)}
        position='left'
        offsetX={scaleSizeW(70)}
        offsetY={scaleSizeH(200)}
      >
        <NetworkQualityTestPage onClose={() => setShowNetworkTest(false)} />
      </FloatingWindow>

      <FloatingWindow
        isVisible={showReceiptTest}
        title='Receipt Testing Page'
        onClose={() => setShowReceiptTest(false)}
        position='left'
        offsetX={scaleSizeW(70)}
        offsetY={scaleSizeH(200)}
      >
        <LocalReceiptTestPage onClose={() => setShowReceiptTest(false)} />
      </FloatingWindow>

      <FloatingWindow
        isVisible={showProductTest}
        title='Test Tools'
        onClose={() => setShowProductTest(false)}
        position='left'
        offsetX={scaleSizeW(70)}
        offsetY={scaleSizeH(200)}
      >
        <TestTools onClose={() => setShowProductTest(false)} />
      </FloatingWindow>

      <ActionButton buttonColor='rgba(231,76,60,1)' position='left' offsetY={scaleSizeH(100)} size={36}>
        <Text>{t('actions')}</Text>
        <ActionButton.Item
          buttonColor='red'
          title='Insufficient Registers? Deactivate All Your Registers!'
          textContainerStyle={styles.textContainerStyle}
          textStyle={styles.textStyle}
          onPress={() => {
            dispatch(deactivateAllRegisters());
          }}
        >
          <Icon name='diamond-outline' style={styles.actionButtonIcon} />
        </ActionButton.Item>
        <ActionButton.Item
          buttonColor='blue'
          title='Deactivate Current Register'
          textContainerStyle={styles.textContainerStyle}
          textStyle={styles.textStyle}
          onPress={() => {
            dispatch(deactivateRegister());
          }}
        >
          <Icon name='brush-outline' style={styles.actionButtonIcon} />
        </ActionButton.Item>
        <ActionButton.Item
          buttonColor='#9b59b6'
          title='Switch Environment'
          textContainerStyle={styles.textContainerStyle}
          textStyle={styles.textStyle}
          onPress={() => {
            NavigationService.navigate({ routeName: 'ModalEnvironmentSwitch' });
          }}
        >
          <Icon name='build-outline' style={styles.actionButtonIcon} />
        </ActionButton.Item>
        <ActionButton.Item
          buttonColor='#40f029'
          title='XPrinter Settings'
          textContainerStyle={styles.textContainerStyle}
          textStyle={styles.textStyle}
          onPress={() => {
            NavigationService.navigate({ routeName: 'ModalLanXPrinterUdp' });
          }}
        >
          <Icon name='pizza-outline' style={styles.actionButtonIcon} />
        </ActionButton.Item>
        <ActionButton.Item
          buttonColor='#1abc9c'
          title='Printer Testing'
          textContainerStyle={styles.textContainerStyle}
          textStyle={styles.textStyle}
          onPress={() => setShowPrinterTest(true)}
        >
          <Icon name='print-outline' style={styles.actionButtonIcon} />
        </ActionButton.Item>
        <ActionButton.Item
          buttonColor='gold'
          title='Password Manager'
          textContainerStyle={styles.textContainerStyle}
          textStyle={styles.textStyle}
          onPress={() => {
            NavigationService.navigate({ routeName: 'CredentialModal' });
          }}
        >
          <Icon name='key-outline' style={styles.actionButtonIcon} />
        </ActionButton.Item>
        <ActionButton.Item
          buttonColor='orange'
          title='Preview Local Receipt'
          textContainerStyle={styles.textContainerStyle}
          textStyle={styles.textStyle}
          onPress={() => setShowReceiptTest(true)}
        >
          <Icon name='analytics-outline' style={styles.actionButtonIcon} />
        </ActionButton.Item>
        <ActionButton.Item
          buttonColor='orange'
          title='Test Tools'
          textContainerStyle={styles.textContainerStyle}
          textStyle={styles.textStyle}
          onPress={() => setShowProductTest(true)}
        >
          <Icon name='analytics-outline' style={styles.actionButtonIcon} />
        </ActionButton.Item>
      </ActionButton>
    </>
  );
};

export default DevShortcut;

const styles = StyleSheet.create({
  textContainerStyle: {
    height: 35,
  },
  textStyle: {
    fontSize: 24,
  },
  actionButtonIcon: {
    fontSize: 30,
    color: 'white',
  },
});
