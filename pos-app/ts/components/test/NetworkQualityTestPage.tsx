import React, { useEffect, useState } from 'react';
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import { useDispatch, useSelector } from 'react-redux';
import { testProps } from '../../utils';
import SpeedTestEngine from '@cloudflare/speedtest';
import { mapSpeedTestResult, SpeedTestResult } from '../../actions';
import { PrimaryTestButton } from './printer/PrimaryTestButton';
import { selectNetworkQualityMonitoring } from '../../sagas/selector';

interface TestItem {
  id: string;
  title: string;
  icon: string;
}

interface TestGroup {
  id: string;
  title: string;
  items: TestItem[];
}

interface TestRenderer {
  render: (dispatch: any) => React.ReactNode;
}

interface TestRenderers {
  [key: string]: TestRenderer;
}

const TestButton: React.FC<{
  item: TestItem;
  isSelected: boolean;
  onPress: () => void;
}> = ({ item, isSelected, onPress }) => (
  <TouchableOpacity {...testProps('al_btn_26')} style={[styles.testButton, isSelected && styles.testButtonSelected]} onPress={onPress}>
    <Icon name={item.icon} size={20} style={styles.buttonIcon} />
    <Text style={[styles.buttonText, isSelected && styles.buttonTextSelected]}>{item.title}</Text>
  </TouchableOpacity>
);

const EmptyStatus: React.FC = () => (
  <View style={styles.emptyStatus}>
    <Icon name='document-text-outline' size={48} color='#ccc' />
    <Text style={styles.emptyText}></Text>
  </View>
);

export const NetworkQualityTestPage: React.FC<{ onClose?: () => void }> = ({ onClose }) => {
  const [selectedTest, setSelectedTest] = useState<TestItem>(null);
  const networkQualityMonitoring = useSelector(selectNetworkQualityMonitoring) as any;

  const dispatch = useDispatch();
  const [engine, setEngine] = useState<SpeedTestEngine>(
    new SpeedTestEngine({
      autoStart: false,
    })
  );
  const [testResult, setTestResult] = useState<SpeedTestResult>(null);
  const [testRunning, setTestRunning] = useState<boolean>(false);

  useEffect(() => {
    setTestResult(mapSpeedTestResult(engine.results));
    setTestRunning(engine.isRunning);
    engine.onRunningChange = setTestRunning;
    engine.onResultsChange = ({ type }) => {
      setTestResult(mapSpeedTestResult(engine.results));
    };
    return () => {
      engine.pause();
      engine.onResultsChange = undefined;
      engine.onRunningChange = undefined;
    };
  }, [engine]);

  const speedTest = () => {
    setTestResult(null);
    engine.restart();
  };

  const pauseSpeedTest = () => {
    engine.pause();
  };

  const TEST_GROUPS: TestGroup[] = [
    {
      id: 'basic',
      title: 'General',
      items: [
        {
          id: 'config',
          title: 'GrowthBook Config',
          icon: 'options-outline',
        },
        {
          id: 'connectivity_test',
          title: 'Quality Test',
          icon: 'options-outline',
        },
      ],
    },
  ];

  const TEST_RENDERERS: TestRenderers = {
    config: {
      render: () => (
        <View style={{ flex: 1 }}>
          <View style={{ flexDirection: 'row' }}>
            <View style={styles.groupContainer}>
              <Text style={styles.groupHeader}>{'Enabled'}</Text>
              <Text style={styles.groupContent}>{`${networkQualityMonitoring.enabled}`}</Text>
            </View>
            <View style={styles.groupContainer}>
              <Text style={styles.groupHeader}>{'Type'}</Text>
              <Text style={styles.groupContent}>{`${networkQualityMonitoring.type}`}</Text>
            </View>
            <View style={styles.groupContainer}>
              <Text style={styles.groupHeader}>{'Continuous'}</Text>
              <Text style={styles.groupContent}>{`${networkQualityMonitoring.continuous}`}</Text>
            </View>
            <View style={styles.groupContainer}>
              <Text style={styles.groupHeader}>{'Start Delay'}</Text>
              <Text style={styles.groupContent}>{`${networkQualityMonitoring.delay}`}</Text>
            </View>
            <View style={styles.groupContainer}>
              <Text style={styles.groupHeader}>{'Interval'}</Text>
              <Text style={styles.groupContent}>{`${networkQualityMonitoring.interval}`}</Text>
            </View>
          </View>
        </View>
      ),
    },
    connectivity_test: {
      render: () => (
        <View style={{ flex: 1 }}>
          <ScrollView style={{ flex: 1 }}>
            <View style={{ flexDirection: 'row' }}>
              <View style={styles.groupContainer}>
                <Text style={styles.groupHeader}>{'Download'}</Text>
                <Text style={styles.groupContent}>{!testResult?.download ? '-' : ((testResult?.download ?? 0) / 1_000_000).toFixed(0) + ' Mbps'}</Text>
              </View>
              <View style={styles.groupContainer}>
                <Text style={styles.groupHeader}>{'Upload'}</Text>
                <Text style={styles.groupContent}>{!testResult?.upload ? '-' : ((testResult?.upload ?? 0) / 1_000_000).toFixed(0) + ' Mbps'}</Text>
              </View>
            </View>
            <View style={{ flexDirection: 'row' }}>
              <View style={styles.groupContainer}>
                <Text style={styles.groupHeader}>{'Latency'}</Text>
                <Text style={styles.groupContent}>{!testResult?.latency ? '-' : (testResult?.latency ?? 0).toFixed(0) + ' ms'}</Text>
              </View>
              <View style={styles.groupContainer}>
                <Text style={styles.groupHeader}>{'Jitter'}</Text>
                <Text style={styles.groupContent}>{!testResult?.jitter ? '-' : (testResult?.jitter ?? 0).toFixed(0) + ' ms'}</Text>
              </View>
              <View style={styles.groupContainer}>
                <Text style={styles.groupHeader}>{'Packet Loss'}</Text>
                <Text style={styles.groupContent}>
                  {!testResult?.packetLoss && testResult?.packetLoss !== 0 ? '-' : ((testResult?.packetLoss ?? 0) * 1).toFixed(3) + ' '}
                </Text>
              </View>
            </View>
            <View style={{ flexDirection: 'row' }}></View>
            {/* {testResult && <Text style={styles.description}>{JSON.stringify(testResult, null, 2)}</Text>} */}
          </ScrollView>
          <PrimaryTestButton
            style={testRunning ? { backgroundColor: '#d23100' } : null}
            title={testRunning ? 'Pause' : 'Start'}
            icon={null}
            onPress={testRunning ? pauseSpeedTest : speedTest}
          />
        </View>
      ),
    },
  };

  const renderContent = () => {
    if (!selectedTest) {
      return <EmptyStatus />;
    }

    const renderer = TEST_RENDERERS[selectedTest.id];
    if (!renderer) {
      return <EmptyStatus />;
    }

    return (
      <View style={styles.contentWrapper}>
        <Text style={styles.contentTitle}>{selectedTest.title}</Text>
        <View style={styles.contentBody}>{renderer.render(dispatch)}</View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <ScrollView style={styles.leftPanel} showsVerticalScrollIndicator={false}>
          {TEST_GROUPS.map(group => (
            <View key={group.id} style={styles.buttonGroup}>
              <Text style={styles.groupTitle}>{group.title}</Text>
              {group.items.map(item => (
                <TestButton key={item.id} item={item} isSelected={selectedTest?.id === item.id} onPress={() => setSelectedTest(item)} />
              ))}
            </View>
          ))}
        </ScrollView>
        <View style={styles.divider} />
        <View style={styles.rightPanel}>{renderContent()}</View>
      </View>
    </View>
  );
};

/* eslint-disable react-native/no-unused-styles */
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    flex: 1,
    flexDirection: 'row',
  },
  leftPanel: {
    flex: 1,
    padding: 16,
  },
  rightPanel: {
    flex: 2,
    padding: 16,
    backgroundColor: '#f8f8f8',
  },
  divider: {
    width: 1,
    backgroundColor: '#E0E0E0',
  },
  buttonGroup: {
    marginBottom: 24,
  },
  groupTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    color: '#333',
  },
  testButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
    padding: 12,
    borderRadius: 10,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  testButtonSelected: {
    backgroundColor: '#e8f0fe',
    borderColor: '#007AFF',
  },
  buttonIcon: {
    color: '#666',
    marginRight: 8,
  },
  buttonText: {
    color: '#333',
    fontSize: 15,
    fontWeight: '500',
    paddingRight: 20,
  },
  buttonTextSelected: {
    color: '#007AFF',
  },
  emptyStatus: {
    flex: 2,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f8f8f8',
  },
  emptyText: {
    marginTop: 16,
    fontSize: 16,
    color: '#999',
  },
  statusGroup: {
    marginBottom: 24,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  statusItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  statusLabel: {
    fontSize: 14,
    color: '#666',
  },
  statusValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  contentWrapper: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  contentTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  contentBody: {
    flex: 1,
  },
  description: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
  },
  optionGroup: {
    marginBottom: 20,
  },
  optionLabel: {
    fontSize: 14,
    color: '#333',
    marginBottom: 8,
  },
  radioGroup: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  radioButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 20,
  },
  radio: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#007AFF',
    marginRight: 8,
    padding: 2,
  },
  radioSelected: {
    backgroundColor: '#007AFF',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 6,
    padding: 8,
    fontSize: 14,
    width: 200,
  },
  rangeInputs: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rangeInput: {
    width: 100,
  },
  rangeSeparator: {
    marginHorizontal: 10,
  },
  developingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  developingTitle: {
    fontSize: 18,
    color: '#666',
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  developingDesc: {
    fontSize: 14,
    color: '#999',
  },
  groupContainer: { flex: 1, borderRadius: 8, borderWidth: 1, borderColor: '#d1d1d1', backgroundColor: '#e8e8e8', padding: 16, margin: 8 },
  groupHeader: { fontSize: 16, color: '#555' },
  groupContent: { fontSize: 32, color: '#111' },
});

export default NetworkQualityTestPage;
