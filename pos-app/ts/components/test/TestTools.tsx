import React, { useState } from 'react';
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import { useDispatch, useSelector } from 'react-redux';
import { testProps } from '../../utils';
import { PrimaryTestButton } from './printer/PrimaryTestButton';
import { deleteAllProductThumbnails, syncLargeProductThumbnails, voidAllProductThumbnails } from '../../actions';
import { selectNewProductSyncFlow, selectSyncInfo } from '../../sagas/selector';
import { AutoDeployList } from './tools/AutoDeployList';
import { AutoDeployLocal } from './tools/AutoDeployLocal';
import { Printing2StressTest } from './tools/Printing2StressTest';
import { PopulateTransactionTest } from './tools/PopulateTransactionTest';

interface TestItem {
  id: string;
  title: string;
  icon: string;
}

interface TestGroup {
  id: string;
  title: string;
  items: TestItem[];
}

interface TestRenderer {
  render: (dispatch: any) => React.ReactNode;
}

interface TestRenderers {
  [key: string]: TestRenderer;
}

const TestButton: React.FC<{
  item: TestItem;
  isSelected: boolean;
  onPress: () => void;
}> = ({ item, isSelected, onPress }) => (
  <TouchableOpacity {...testProps('al_btn_26')} style={[styles.testButton, isSelected && styles.testButtonSelected]} onPress={onPress}>
    <Icon name={item.icon} size={20} style={styles.buttonIcon} />
    <Text style={[styles.buttonText, isSelected && styles.buttonTextSelected]}>{item.title}</Text>
  </TouchableOpacity>
);

const EmptyStatus: React.FC = () => (
  <View style={styles.emptyStatus}>
    <Icon name='document-text-outline' size={48} color='#ccc' />
    <Text style={styles.emptyText}></Text>
  </View>
);

export const TestTools: React.FC<{ onClose?: () => void }> = ({ onClose }) => {
  const [selectedTest, setSelectedTest] = useState<TestItem | null>(null);

  const dispatch = useDispatch();
  const syncInfoImmutable = useSelector(selectSyncInfo) as any;
  const syncInfo = syncInfoImmutable.toJS();

  const syncFlowType = useSelector(selectNewProductSyncFlow);

  // const thumbnails = useFolderState(RNFS.DocumentDirectoryPath).files.filter(item => item.name.endsWith('.png'));

  const TEST_GROUPS: TestGroup[] = [
    {
      id: 'deployments',
      title: 'Deploy',
      items: [
        // {
        //   id: 'auto_deploy_status',
        //   title: 'Local Deployment',
        //   icon: 'options-outline',
        // },
        // {
        //   id: 'auto_deploy_list',
        //   title: 'Remote Deployments',
        //   icon: 'options-outline',
        // },
        {
          id: 'auto_deploy',
          title: 'Deployments',
          icon: 'options-outline',
        },
      ],
    },
    {
      id: 'product_thumbnails',
      title: 'Product Thumbnails',
      items: [
        {
          id: 'config',
          title: 'GrowthBook Config',
          icon: 'options-outline',
        },
        {
          id: 'thumbnails_test',
          title: 'Sync',
          icon: 'options-outline',
        },
      ],
    },
    {
      id: 'receipt_generation',
      title: 'Printing II',
      items: [
        {
          id: 'printing_2_stress_test',
          title: 'Stress Test',
          icon: 'options-outline',
        },
      ],
    },
    {
      id: 'transaction_generation',
      title: 'Transaction',
      items: [
        {
          id: 'populate_transactions',
          title: 'Populate Transactions',
          icon: 'options-outline',
        },
      ],
    },
  ];

  const renderContent = () => {
    if (!selectedTest) {
      return <EmptyStatus />;
    }

    if (selectedTest.id === 'auto_deploy') {
      return (
        <>
          <View style={{ flex: 1, flexDirection: 'row', columnGap: 16 }}>
            <View style={styles.contentWrapper}>
              <Text style={styles.contentTitle}>{'Local Deployment'}</Text>
              <View style={styles.contentBody}>
                <AutoDeployLocal />
              </View>
            </View>
            <View style={styles.contentWrapper}>
              <Text style={styles.contentTitle}>{'Remote Deployments'}</Text>
              <View style={styles.contentBody}>
                <AutoDeployList />
              </View>
            </View>
          </View>
        </>
      );
    }

    return (
      <View style={styles.contentWrapper}>
        <Text style={styles.contentTitle}>{selectedTest.title}</Text>
        <View style={styles.contentBody}>
          {selectedTest.id === 'config' && (
            <View style={{ flex: 1 }}>
              <View style={{ flexDirection: 'row' }}>
                <View style={styles.groupContainer}>
                  <Text style={styles.groupHeader}>{'Enabled'}</Text>
                  <Text style={styles.groupContent}>{`${syncFlowType.enabled}`}</Text>
                </View>
                <View style={styles.groupContainer}>
                  <Text style={styles.groupHeader}>{'Async Fetch'}</Text>
                  <Text style={styles.groupContent}>{`${syncFlowType.asyncThumbnail}`}</Text>
                </View>
                <View style={styles.groupContainer}>
                  <Text style={styles.groupHeader}>{'Show Indicator'}</Text>
                  <Text style={styles.groupContent}>{`${syncFlowType.showIndicator}`}</Text>
                </View>
                <View style={styles.groupContainer}>
                  <Text style={styles.groupHeader}>{'Show Warning'}</Text>
                  <Text style={styles.groupContent}>{`${syncFlowType.warnSyncFailed}`}</Text>
                </View>
                <View style={styles.groupContainer}>
                  <Text style={styles.groupHeader}>{'Page Size'}</Text>
                  <Text style={styles.groupContent}>{`${syncFlowType.pageSize}`}</Text>
                </View>
              </View>
            </View>
          )}
          {selectedTest.id === 'thumbnails_test' && (
            <View style={{ flex: 1 }}>
              <View style={{ flexDirection: 'row' }}>
                <View style={styles.groupContainer}>
                  <Text style={styles.groupHeader}>{'Download Progress'}</Text>
                  <Text style={styles.groupContent}>{`${syncInfo.lastProductImagesSyncProgress} / ${syncInfo.lastProductImagesSyncCount}`}</Text>
                </View>
                <View style={styles.groupContainer}>
                  <Text style={styles.groupHeader}>{'Download Failed'}</Text>
                  <Text style={styles.groupContent}>{`${syncInfo.lastProductImagesSyncFailedProgress}`}</Text>
                </View>
              </View>
              <View style={{ flexDirection: 'row' }}>
                <View style={styles.groupContainer}>
                  {/* <Text style={styles.groupHeader}>{'Local Thumbnails'}</Text> */}
                  {/* <Text style={styles.groupContent}>{`${thumbnails.length}`}</Text> */}
                </View>
              </View>
              <View style={{ flexDirection: 'row' }}>
                <PrimaryTestButton
                  style={{ backgroundColor: '#d23100' }}
                  title={'Clean Product Thumbnails'}
                  icon={null}
                  onPress={() => {
                    dispatch(deleteAllProductThumbnails());
                  }}
                />
                <PrimaryTestButton
                  style={{ backgroundColor: '#d27000' }}
                  title={'Void Product Thumbnails'}
                  icon={null}
                  onPress={() => {
                    dispatch(voidAllProductThumbnails());
                  }}
                />
              </View>
              <PrimaryTestButton
                style={{ backgroundColor: '#0057b9' }}
                title={'Sync Product Thumbnails'}
                icon={null}
                onPress={() => {
                  dispatch(syncLargeProductThumbnails());
                }}
              />
            </View>
          )}
          {selectedTest.id === 'auto_deploy_status' && <AutoDeployLocal />}
          {selectedTest.id === 'auto_deploy_list' && <AutoDeployList />}
          {selectedTest.id === 'printing_2_stress_test' && <Printing2StressTest />}
          {selectedTest.id === 'populate_transactions' && <PopulateTransactionTest />}
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <ScrollView style={styles.leftPanel} showsVerticalScrollIndicator={false}>
          {TEST_GROUPS.map(group => (
            <View key={group.id} style={styles.buttonGroup}>
              <Text style={styles.groupTitle}>{group.title}</Text>
              {group.items.map(item => (
                <TestButton key={item.id} item={item} isSelected={selectedTest?.id === item.id} onPress={() => setSelectedTest(item)} />
              ))}
            </View>
          ))}
        </ScrollView>
        <View style={styles.divider} />
        <View style={styles.rightPanel}>{renderContent()}</View>
      </View>
    </View>
  );
};

/* eslint-disable react-native/no-unused-styles */
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    flex: 1,
    flexDirection: 'row',
  },
  leftPanel: {
    flex: 1,
    padding: 16,
  },
  rightPanel: {
    flex: 3,
    padding: 16,
    backgroundColor: '#f8f8f8',
  },
  divider: {
    width: 1,
    backgroundColor: '#E0E0E0',
  },
  buttonGroup: {
    marginBottom: 24,
  },
  groupTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    color: '#333',
  },
  testButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
    padding: 12,
    borderRadius: 10,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  testButtonSelected: {
    backgroundColor: '#e8f0fe',
    borderColor: '#007AFF',
  },
  buttonIcon: {
    color: '#666',
    marginRight: 8,
  },
  buttonText: {
    color: '#333',
    fontSize: 15,
    fontWeight: '500',
    paddingRight: 20,
  },
  buttonTextSelected: {
    color: '#007AFF',
  },
  emptyStatus: {
    flex: 2,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f8f8f8',
  },
  emptyText: {
    marginTop: 16,
    fontSize: 16,
    color: '#999',
  },
  statusGroup: {
    marginBottom: 24,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  statusItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  statusLabel: {
    fontSize: 14,
    color: '#666',
  },
  statusValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  contentWrapper: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  contentTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  contentBody: {
    flex: 1,
  },
  description: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
  },
  optionGroup: {
    marginBottom: 20,
  },
  optionLabel: {
    fontSize: 14,
    color: '#333',
    marginBottom: 8,
  },
  radioGroup: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  radioButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 20,
  },
  radio: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#007AFF',
    marginRight: 8,
    padding: 2,
  },
  radioSelected: {
    backgroundColor: '#007AFF',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 6,
    padding: 8,
    fontSize: 14,
    width: 200,
  },
  rangeInputs: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rangeInput: {
    width: 100,
  },
  rangeSeparator: {
    marginHorizontal: 10,
  },
  developingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  developingTitle: {
    fontSize: 18,
    color: '#666',
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  developingDesc: {
    fontSize: 14,
    color: '#999',
  },
  groupContainer: {
    flex: 1,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#d1d1d1',
    backgroundColor: '#e8e8e8',
    padding: 16,
    margin: 8,
  },
  groupHeader: { fontSize: 16, color: '#555' },
  groupContent: { fontSize: 32, color: '#111' },
});

export default TestTools;
