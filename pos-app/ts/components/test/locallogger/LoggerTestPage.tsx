// @ts-nocheck
import AsyncStorage from '@react-native-async-storage/async-storage';
import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, <PERSON>ton, ScrollView, StyleSheet, Text, TextInput, View } from 'react-native';
import RNFS from 'react-native-fs';
import { useDispatch, useSelector } from 'react-redux';
import { setGrowthBookFeatures } from '../../../actions';
import { selectLocalLoggingConfig } from '../../../sagas/selector';
import { RootState } from '../../../typings';
import { testProps } from '../../../utils';
import { GBFeature } from '../../../utils/growthbook';
import { LoggingLevel } from '../../../utils/logComponent';
import { LogSettingOptions } from '../../../utils/logComponent/local-logging/libs/NativeFileLogger';
import LocalLogger, { LocalLoggerStorageKey } from '../../../utils/logComponent/local-logging/LocalLogger';

const LoggerTestPage: React.FC = props => {
  const [logFiles, setLogFiles] = useState<string[]>([]);
  const [email, setEmail] = useState<string>('<EMAIL>');
  const [customMessage, setCustomMessage] = useState<string>('');
  const [isInitialized, setIsInitialized] = useState(() => LocalLogger.getEnabled());
  const [contents, setContents] = useState<string[]>([]);

  const localLoggingConfig = useSelector(selectLocalLoggingConfig);

  const gb = useSelector((state: RootState) => state.get('GrowthBookFeatures'));

  const dispatch = useDispatch();

  const onUpdateGrowthbook = config => {
    // console.log(updateGrowthbook());
    const value = gb.toJS();
    value[GBFeature.CM_9450] = config;
    dispatch(setGrowthBookFeatures(value));
  };

  const onStart = () => {
    onUpdateGrowthbook({
      enabled: true,
      logLevel: LoggingLevel.Info,
      maximumFileSize: 1024 * 5, // 5KB
      maximumNumberOfFiles: 10,
    });
    setIsInitialized(true);
  };

  const clearContents = () => {
    setContents([]);
  };

  const onStop = () => {
    onUpdateGrowthbook({
      enabled: false,
    });
    setIsInitialized(false);
  };

  function onReconfigure() {
    try {
      const options: LogSettingOptions = {
        enabled: true,
        logLevel: 1,
        maximumFileSize: 5242881,
        maximumNumberOfFiles: 16,
      };
      onUpdateGrowthbook(options);
      // LocalLogger.updateLogSetting(options);
      LocalLogger.log({ level: LoggingLevel.Info, message: '日志重新配置', payload: options, action: 'LocalTestPage' });
      refreshLogFiles();
    } catch (error) {
      Alert.alert('初始化失败', `无法初始化日志系统: ${error}`);
    }
  }

  useEffect(() => {
    return () => {
      LocalLogger.log({ level: LoggingLevel.Info, message: '日志测试页面已卸载', action: 'LocalTestPage' });
    };
  }, []);

  const refreshLogFiles = async () => {
    try {
      const files = await LocalLogger.getLogFilePaths();
      setLogFiles(files);
    } catch (error) {
      Alert.alert('错误', `获取日志文件失败: ${error}`);
    }
  };

  const logDebug = () => {
    LocalLogger.log({ action: 'TestDebug', level: LoggingLevel.Debug, message: customMessage || '这是一条调试日志' });
  };

  const logInfo = () => {
    LocalLogger.log({ action: 'TestInfo', level: LoggingLevel.Info, message: customMessage || '这是一条信息日志' });
  };

  const logWarn = () => {
    LocalLogger.log({ action: 'TestWarning', level: LoggingLevel.Warning, message: customMessage || '这是一条警告日志' });
  };

  const logError = () => {
    const testError = new Error('测试错误');
    LocalLogger.log({ action: 'TestError', level: LoggingLevel.Error, error: testError, message: customMessage || '这是一条错误日志' });
  };

  const logNativeInfoLog = () => {
    LocalLogger.testNativeLogs(LoggingLevel.Info, '这是一条原生信息日志');
  };

  const logNativeErrorLog = () => {
    LocalLogger.testNativeLogs(LoggingLevel.Error, '这是一条原生错误日志');
  };

  const clearAllLogs = async () => {
    try {
      const value = gb.toJS();
      value[GBFeature.CM_9450] = { ...value[GBFeature.CM_9450], deleteKey: `${Date.now()}` };
      onUpdateGrowthbook(value[GBFeature.CM_9450]);
      Alert.alert('成功', '已清除所有日志');
      refreshLogFiles();
    } catch (error) {
      Alert.alert('错误', `清除日志失败: ${error}`);
    }
  };

  const readLogs = async () => {
    // const logs = await LocalLogger.readLogs();
    // console.log(logs);
    try {
      const files = await LocalLogger.getLogFilePaths();
      setLogFiles(files);
      let latestFile;
      for (const file of files) {
        console.log('file', file);
        const stats = await RNFS.stat(file);
        if (!latestFile || latestFile.mtime < stats.mtime) {
          latestFile = stats;
        }
      }

      RNFS.readFile(latestFile.originalFilepath, 'utf8').then(content => {
        // setContents(prev => [...prev, content]);
        const logs = content.split('\n').slice(-10, content.length).reverse();
        // .sort((a, b) => {
        //   return JSON.parse(b).ts - JSON.parse(a).ts;
        // });
        console.log(logs);

        setContents(logs);
      });
    } catch (error) {
      Alert.alert('错误', `获取日志文件失败: ${error}`);
    }
  };

  const uploadLogs = async () => {
    try {
      const value = gb.toJS();
      value[GBFeature.CM_9450] = { ...value[GBFeature.CM_9450], uploadKey: '1747370918570' /* `${Date.now()}` */ };
      onUpdateGrowthbook(value[GBFeature.CM_9450]);
      Alert.alert('成功', '日志已上传');
      refreshLogFiles();
    } catch (error) {
      Alert.alert('错误', `上传日志失败: ${error}`);
    }
  };

  const updateMailConfig = () => {
    const value = gb.toJS();
    value[GBFeature.CM_9450] = { ...value[GBFeature.CM_9450], mailPin: '1111', mailAddress: '<EMAIL>' };
    onUpdateGrowthbook(value[GBFeature.CM_9450]);
  };

  const compressLogs = async () => {
    const compressedLog = await LocalLogger.compressLogs();
    if (compressedLog) {
      console.log('compressedLog', compressedLog);
      Alert.alert('成功', `日志已压缩为 ${compressedLog}`);
    } else {
      Alert.alert('错误', '压缩日志失败');
    }
  };

  const sendLogsByEmail = async () => {
    if (!email) {
      Alert.alert('错误', '请输入邮箱地址');
      return;
    }

    try {
      await LocalLogger.sendLogsByEmail({
        to: email,
        subject: '日志测试文件',
        body: '这是从日志测试页面发送的日志文件',
      });
      Alert.alert('成功', `日志文件已发送至 ${email}`);
    } catch (error) {
      Alert.alert('错误', `发送日志失败: ${error}`);
    }
  };

  const onGetStorageSetting = async () => {
    // console.log(getRNSetting());
    const keys = await AsyncStorage.getItem(LocalLoggerStorageKey);
    console.log(JSON.parse(keys));
  };

  return (
    <ScrollView style={styles.container}>
      <Button title='返回' onPress={() => props.navigation.goBack()} />
      <Text style={styles.title}>日志测试页面</Text>
      <Text style={styles.sectionTitle}>日志状态</Text>
      <View style={styles.buttonRow}>
        <Button title='获取Storage设置' color='#FC7118' onPress={onGetStorageSetting} />
        <Button title='重新配置' color='#FC7118' onPress={onReconfigure} />
      </View>
      <Text>状态: {isInitialized ? '开始' : '停止'}</Text>
      <Text>邮件地址: {localLoggingConfig.mailAddress}</Text>
      <Text>邮件Pin: {localLoggingConfig.mailPin}</Text>
      <View style={styles.buttonRow}>
        <Button title='开始' color='#FC7118' onPress={onStart} />
        <Button title='停止' color='#FC7118' onPress={onStop} />
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.label}>自定义日志消息:</Text>
        <TextInput
          {...testProps('log_message_input')}
          style={styles.input}
          value={customMessage}
          onChangeText={setCustomMessage}
          placeholder='输入自定义日志消息'
        />
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>记录不同级别的日志</Text>
        <View style={styles.buttonRow}>
          <Button title='调试日志' onPress={logDebug} />
          <Button title='信息日志' onPress={logInfo} />
        </View>
        <View style={styles.buttonRow}>
          <Button title='警告日志' onPress={logWarn} />
          <Button title='错误日志' onPress={logError} />
        </View>
        <View style={styles.buttonRow}>
          <Button title='原生信息日志' onPress={logNativeInfoLog} />
          <Button title='原生错误日志' onPress={logNativeErrorLog} />
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>发送日志</Text>
        <TextInput
          {...testProps('email_input')}
          style={styles.input}
          value={email}
          onChangeText={setEmail}
          placeholder='输入接收者邮箱'
          keyboardType='email-address'
        />
        <View style={styles.buttonRow}>
          <Button title='修改邮件配置' onPress={updateMailConfig} />
          <Button title='通过邮件发送日志' onPress={sendLogsByEmail} />
        </View>
      </View>
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>日志操作</Text>
        <View style={styles.buttonRow}>
          <Button title='压缩日志' onPress={compressLogs} />
          <Button title='上传日志' onPress={uploadLogs} color='#FF0000' />
          <Button title='清除所有日志文件' onPress={clearAllLogs} color='#FF0000' />
        </View>
      </View>
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>日志读取</Text>
        <View style={styles.buttonRow}>
          <Button title='刷新文件列表' onPress={refreshLogFiles} />
          <Button title='读取日志' onPress={readLogs} color='#007AFF' />
          <Button title='隐藏日志' onPress={clearContents} color='#FF0000' />
        </View>

        <View style={styles.fileList}>
          <Text style={styles.label}>日志文件 ({logFiles.length}):</Text>
          {logFiles.length > 0 ? (
            logFiles.map((file, index) => (
              <Text key={index} style={styles.filePath}>
                {file}
              </Text>
            ))
          ) : (
            <Text>没有找到日志文件</Text>
          )}
        </View>
        <View style={styles.fileList}>
          {contents.map((content, index) => (
            <Text key={index} style={styles.filePath}>
              {content}
            </Text>
          ))}
        </View>
      </View>
      <View style={styles.spacer} />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  section: {
    marginVertical: 12,
    padding: 12,
    backgroundColor: '#fff',
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  inputContainer: {
    backgroundColor: '#fff',
    padding: 12,
    borderRadius: 8,
    marginVertical: 8,
  },
  label: {
    fontSize: 16,
    marginBottom: 4,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
    padding: 8,
    marginBottom: 8,
  },
  fileList: {
    marginTop: 8,
  },
  filePath: {
    fontSize: 12,
    marginBottom: 4,
    color: '#555',
  },
  spacer: {
    height: 40,
  },
});

export default LoggerTestPage;
