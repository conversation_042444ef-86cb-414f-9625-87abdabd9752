import { ActivityIndicator, Modal, ScrollView, StyleSheet, Text, TouchableOpacity, View, Linking } from 'react-native';
import React, { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { testProps } from '../../../utils';
import CodePush from 'react-native-code-push';
import { TestButton } from './TestButton';
import { useAsyncEffect } from 'ahooks';
import { FloatingWindow } from '../FloatingWindow';
import { TestPopup } from './TestPopup';
import RNFS from 'react-native-fs';
import RNApkInstaller from '@dominicvonk/react-native-apk-installer';
import { getAppsAsync, getDeploymentsAsync } from '../../../actions';

export const AutoDeployList = () => {
  const dispatch = useDispatch();
  const [errMsg, setErrMsg] = useState(null);
  const [popupErrMsg, setPopupErrMsg] = useState(null);

  const [apps, setApps] = useState([]);
  const [selectedAppId, setSelectedAppId] = useState(null);

  const [deployments, setDeployments] = useState([]);
  const [loadingDeployments, setLoadingDeployments] = useState(false);
  const [selectedDeployment, setSelectedDeployment] = useState(null);

  const [isSyncing, setSyncing] = useState(false);
  const [syncStatus, setSyncStatus] = useState(null);
  const [downloadProgress, setDownloadProgress] = useState(null);

  const [isInDeployment, setInDeployment] = useState(false);

  const [isInNativeDeploy, setIsInNativeDeploy] = useState(false);

  // New state for APK download
  const [isDownloading, setIsDownloading] = useState(false);
  const [downloadProgressPercent, setDownloadProgressPercent] = useState(0);
  const [downloadJobId, setDownloadJobId] = useState(null);

  useAsyncEffect(async () => {
    await onRefreshApps();
  }, []);

  // Cleanup effect
  useEffect(() => {
    return () => {
      // Cancel any ongoing download when component unmounts
      if (downloadJobId && isDownloading) {
        RNFS.stopDownload(downloadJobId);
      }
    };
  }, [downloadJobId, isDownloading]);

  const accountId = 'id_0';

  const formatTimePassed = (timestamp: number): string => {
    const now = Date.now();
    const diff = now - timestamp;

    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days} days ago`;
    } else if (hours > 0) {
      return `${hours} hours ago`;
    } else if (minutes > 0) {
      return `${minutes} mins ago`;
    } else {
      return 'Just now';
    }
  };

  // Configuration for APK download - replace with your actual APK URL
  // To use with a real APK:
  // 1. Replace the URL with your actual APK download URL
  // 2. Update the fileName to match your APK file name
  // 3. Ensure the APK URL is accessible and returns proper content-length headers for progress tracking
  const APK_DOWNLOAD_CONFIG = {
    // Using a working test URL for demonstration - replace with your actual APK URL
    url: 'https://fat-rnpos-apk.s3.ap-southeast-1.amazonaws.com/StoreHub_POS_fat_CM-9953_1.91.0_202506061129.apk',
    fileName: 'pos-app-update.apk',
  };

  const onSelectApp = async app => {
    setErrMsg(null);
    setLoadingDeployments(true);
    try {
      setSelectedAppId(app.id);
      const deployments: any[] = await getDeploymentsAsync(accountId, app.id);
      setDeployments(deployments.reverse());
    } catch (error) {
      setApps([]);
      setErrMsg(JSON.stringify(error));
    }
    setLoadingDeployments(false);
  };

  const onSelectDeployment = deployment => {
    setSelectedDeployment(deployment);
  };

  const onDeployNative = () => {
    setPopupErrMsg(null);
    setIsInNativeDeploy(true);
  };

  const onRefreshApps = async () => {
    try {
      setApps([]);
      setDeployments([]);
      setSelectedAppId(null);
      setSelectedDeployment(null);

      const apps = await getAppsAsync(accountId);
      setApps(apps);
    } catch (error) {
      setErrMsg(JSON.stringify(error));
    }
  };

  // APK Installation function
  const installAPK = async (filePath: string) => {
    try {
      // For Android, we need to open the APK file
      const fileUri = `file://${filePath}`;
      await RNApkInstaller.install(filePath);
    } catch (error) {
      console.error('APK installation error:', error);
      setErrMsg(`Installation failed: ${error.message}`);
    }
  };

  // APK Download function using RNFS
  const downloadAPK = async () => {
    try {
      setIsDownloading(true);
      setDownloadProgressPercent(0);
      setErrMsg(null); // Clear any previous errors

      const downloadDest = `${RNFS.DocumentDirectoryPath}/${APK_DOWNLOAD_CONFIG.fileName}`;

      // Check if file already exists and remove it
      if (await RNFS.exists(downloadDest)) {
        await RNFS.unlink(downloadDest);
      }

      console.log('Starting APK download from:', APK_DOWNLOAD_CONFIG.url);
      console.log('Download destination:', downloadDest);

      // Start download with progress tracking
      const downloadOptions = {
        fromUrl: APK_DOWNLOAD_CONFIG.url,
        toFile: downloadDest,
        progress: res => {
          const progress = (res.bytesWritten / res.contentLength) * 100;
          const progressPercent = Math.floor(progress);
          setDownloadProgressPercent(progressPercent);
          console.log(`Download progress: ${progressPercent}%`);
        },
      };

      const result = await RNFS.downloadFile(downloadOptions);
      setDownloadJobId(result.jobId);

      // Wait for download to complete
      const downloadResult = await result.promise;

      if (downloadResult.statusCode === 200) {
        console.log('APK downloaded successfully');
        setIsDownloading(false);
        setDownloadProgressPercent(100);

        // Small delay to show 100% completion
        setTimeout(async () => {
          setIsInNativeDeploy(false);
          // Install APK
          await installAPK(downloadDest);
        }, 500);
      } else {
        throw new Error(`Download failed with status: ${downloadResult.statusCode}`);
      }
    } catch (error) {
      console.error('APK download error:', error);
      setIsDownloading(false);
      setDownloadProgressPercent(0);
      setDownloadJobId(null);
      setPopupErrMsg(`Download failed: ${error.message}`);
    }
  };

  const onDeploy = async () => {
    // if (true) {
    //   onDeployNative();
    //   return;
    // }
    setSyncStatus(null);
    setDownloadProgress(null);
    try {
      setSyncing(true);
      const syncResult = await CodePush.sync(
        { deploymentKey: selectedDeployment.key, installMode: CodePush.InstallMode.IMMEDIATE },
        syncStatus => {
          setSyncStatus(syncStatus);
        },
        downloadProgress => {
          setDownloadProgress(downloadProgress);
        },
        update => {
          console.log('[CodePush]', 'Version Mismatch!', update.appVersion);
        }
      );
      console.log('[CodePush]', 'syncResult', syncResult);
    } catch (error) {
      console.error(error);
    }
    setSyncing(false);
  };

  if (isInDeployment) {
    if (!selectedDeployment) {
      return null;
    }
    const lastPackage = selectedDeployment.packageHistory.sort((a, b) => b.uploadTime - a.uploadTime)[0];
    return (
      <>
        <Modal transparent={true} visible={isInNativeDeploy}>
          <TestPopup
            title={'Native Deploy Required'}
            message={popupErrMsg ? `Error: ${popupErrMsg}` : 'You need to install native package for this deploy'}
            cancelText={isDownloading ? 'Cancel Download' : 'Cancel'}
            submitText={isDownloading ? `Downloading... (${downloadProgressPercent}%)` : 'Install'}
            submitDisabled={isDownloading}
            onSubmitHandler={() => {
              if (!isDownloading) {
                downloadAPK();
              }
            }}
            onCancelHandler={() => {
              if (downloadJobId && isDownloading) {
                console.log('Cancelling download...');
                RNFS.stopDownload(downloadJobId);
                setIsDownloading(false);
                setDownloadProgressPercent(0);
                setDownloadJobId(null);
              }
              setErrMsg(null); // Clear any errors
              setIsInNativeDeploy(false);
            }}
          />
        </Modal>
        <View style={{ flex: 1 }}>
          <View style={{ flexDirection: 'column', rowGap: 16 }}>
            <View style={{ flexDirection: 'row', columnGap: 16 }}>
              <View style={styles.groupContainer}>
                <Text style={styles.groupHeader}>{'Deployment'}</Text>
                <Text style={styles.groupContent}>{selectedDeployment.name}</Text>
              </View>
              <View style={styles.groupContainer}>
                <Text style={styles.groupHeader}>{'Version'}</Text>
                <Text style={styles.groupContent}>{lastPackage?.appVersion ?? 'Unknown'}</Text>
              </View>
            </View>
            <View style={{ flexDirection: 'row', columnGap: 16 }}>
              <View style={styles.groupContainer}>
                <Text style={styles.groupHeader}>{'Package Label'}</Text>
                <Text style={styles.groupContent}>{lastPackage.label}</Text>
              </View>
              <View style={styles.groupContainer}>
                <Text style={styles.groupHeader}>{'Package Size'}</Text>
                <Text style={styles.groupContent}>{(lastPackage.size / 1024 / 1024).toFixed(0) + ' MB'}</Text>
              </View>
            </View>
            <View style={{ flexDirection: 'row', columnGap: 16 }}>
              {/*<View style={styles.groupContainer}>*/}
              {/*  <Text style={styles.groupHeader}>{'Description'}</Text>*/}
              {/*  <Text style={styles.groupContent}>{selectedDeployment.description}</Text>*/}
              {/*</View>*/}
              <View style={styles.groupContainer}>
                <Text style={styles.groupHeader}>{'Last Updated'}</Text>
                <Text style={styles.groupContent}>{formatTimePassed(lastPackage.uploadTime)}</Text>
              </View>
            </View>
            <View style={{ flexDirection: 'row', columnGap: 16 }}>
              <View style={styles.groupContainer}>
                <Text style={styles.groupHeader}>{'Status'}</Text>
                <>
                  <View>
                    {syncStatus == null && <Text style={styles.groupContent}>{'Pending'}</Text>}
                    {syncStatus === 0 && <Text style={styles.groupContent}>{'Version Mismatch'}</Text>}
                    {syncStatus === 1 && <Text style={styles.groupContent}>{'Installed'}</Text>}
                    {syncStatus === 2 && <Text style={styles.groupContent}>{'Ignored'}</Text>}
                    {syncStatus === 3 && <Text style={styles.groupContent}>{'Unknown Error'}</Text>}
                    {syncStatus === 4 && <Text style={styles.groupContent}>{'Blocked'}</Text>}
                    {syncStatus === 5 && <Text style={styles.groupContent}>{'Deploying...'}</Text>}
                  </View>
                </>
              </View>
            </View>
          </View>
          <View style={{ flex: 1 }}></View>

          <View style={{ flexDirection: 'row', columnGap: 16 }}>
            <TestButton
              style={{ flex: 1, backgroundColor: '#d98800' }}
              title={'Go Back'}
              icon={null}
              onPress={() => {
                setInDeployment(false);
              }}
            />
            <TestButton
              style={{ flex: 1, backgroundColor: '#0057b9' }}
              title={isSyncing ? 'Deploying' : 'Deploy'}
              icon={null}
              enabled={!isSyncing}
              onPress={() => {
                onDeploy();
              }}
            />
          </View>
          <View style={{ flexDirection: 'row' }}></View>
        </View>
      </>
    );
  }

  return (
    <>
      <View style={{ flex: 1 }}>
        {Boolean(errMsg) && (
          <View>
            <Text>{errMsg}</Text>
          </View>
        )}
        <View style={{ flex: 1, flexDirection: 'row', columnGap: 10 }}>
          <View style={{ flex: 1 }}>
            {apps.map(app => {
              const isSelected = app.id === selectedAppId;
              return (
                <TouchableOpacity
                  key={app.id}
                  {...testProps('al_btn_26')}
                  style={[styles.testButton, isSelected && styles.testButtonSelected]}
                  onPress={() => onSelectApp(app)}
                >
                  <Text style={[styles.buttonText, isSelected && styles.buttonTextSelected]}>{app.name}</Text>
                </TouchableOpacity>
              );
            })}
          </View>
          <View style={{ flex: 2 }}>
            {loadingDeployments && <ActivityIndicator size={32} animating={true} color={'gray'} />}
            <ScrollView>
              {deployments.map(deployment => {
                const isSelected = selectedDeployment && deployment.key === selectedDeployment.key;
                return (
                  <TouchableOpacity
                    key={deployment.key}
                    {...testProps('al_btn_26')}
                    style={[styles.testButton, isSelected && styles.testButtonSelected]}
                    onPress={() => onSelectDeployment(deployment)}
                  >
                    <View style={{ flexDirection: 'column', columnGap: 4 }}>
                      <Text style={[styles.buttonText, isSelected && styles.buttonTextSelected]}>{deployment.name}</Text>
                      <Text style={[styles.buttonTextSecondary, isSelected && styles.buttonTextSecondarySelected]}>
                        {!deployment.package?.uploadTime ? 'No Package' : `${formatTimePassed(deployment.package?.uploadTime)}`}
                      </Text>
                    </View>
                  </TouchableOpacity>
                );
              })}
            </ScrollView>
          </View>

          <View style={{ flex: 0 }} />
        </View>

        <View style={{ flexDirection: 'row', columnGap: 16 }}>
          <TestButton style={{ flex: 1, backgroundColor: '#d98800' }} title={'Refresh'} icon={null} onPress={() => onRefreshApps()} />
          <TestButton
            style={{ flex: 1, backgroundColor: '#0057b9' }}
            title={'Confirm'}
            icon={null}
            enabled={selectedDeployment}
            onPress={() => {
              setInDeployment(true);
            }}
          />
        </View>
        <View style={{ flexDirection: 'row' }}></View>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  testButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
    padding: 12,
    borderRadius: 10,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  testButtonSelected: {
    backgroundColor: '#e8f0fe',
    borderColor: '#007AFF',
  },
  buttonText: {
    color: '#333',
    fontSize: 15,
    fontWeight: '500',
    paddingRight: 20,
  },
  buttonTextSelected: {
    color: '#007AFF',
  },
  buttonTextSecondary: {
    color: '#969696',
    fontSize: 15,
    fontWeight: '500',
    paddingRight: 20,
  },
  buttonTextSecondarySelected: {
    color: '#007AFF',
  },
  groupContainer: {
    flex: 1,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#d1d1d1',
    backgroundColor: '#e8e8e8',
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  groupHeader: { fontSize: 16, color: '#555' },
  groupContent: { fontSize: 28, color: '#111' },
});
