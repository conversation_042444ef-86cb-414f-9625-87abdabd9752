import { StyleSheet, Text, View } from 'react-native';
import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import CodePush, { LocalPackage } from 'react-native-code-push';
import { TestButton } from './TestButton';
import { useAsyncEffect } from 'ahooks';
import UpdateState = CodePush.UpdateState;
import { getDeploymentAsync } from '../../../actions';

export const AutoDeployLocal = () => {
  const dispatch = useDispatch();
  const [localPackage, setLocalPackage] = useState<LocalPackage>(null);
  const [selectedDeployment, setSelectedDeployment] = useState(null);

  const [isSyncing, setSyncing] = useState(false);
  const [syncStatus, setSyncStatus] = useState(null);
  const [downloadProgress, setDownloadProgress] = useState(null);

  const onDeploy = async () => {
    setSyncStatus(null);
    setDownloadProgress(null);
    try {
      setSyncing(true);
      const syncResult = await CodePush.sync(
        { deploymentKey: selectedDeployment.key, installMode: CodePush.InstallMode.IMMEDIATE },
        syncStatus => {
          setSyncStatus(syncStatus);
        },
        downloadProgress => {
          setDownloadProgress(downloadProgress);
        },
        update => {
          console.log('[CodePush]', 'Version Mismatch!', update.appVersion);
        }
      );
      console.log(syncResult);
    } catch (error) {
      console.error('[CodePush] Error', JSON.stringify(error));
    }
    setSyncing(false);
  };

  const onSync = async () => {
    setSelectedDeployment(null);
    const localPackage = await CodePush.getUpdateMetadata(UpdateState.RUNNING);
    setLocalPackage(localPackage);
    try {
      const deployment = await getDeploymentAsync(localPackage.deploymentKey);
      setSelectedDeployment(deployment);
    } catch (e) {
      setSelectedDeployment(e);
    }
  };

  useAsyncEffect(async () => {
    await onSync();
  }, []);

  if (!localPackage) {
    return (
      <View style={{ flex: 1 }}>
        <Text>{'No deployment found'}</Text>
      </View>
    );
  }

  return (
    <View style={{ flex: 1 }}>
      <View style={{ flexDirection: 'column', rowGap: 16 }}>
        <View style={{ flexDirection: 'row', columnGap: 16 }}>
          <View style={styles.groupContainer}>
            <Text style={styles.groupHeader}>{'Deployment'}</Text>
            <Text style={styles.groupContent}>{selectedDeployment?.name}</Text>
          </View>
          <View style={styles.groupContainer}>
            <Text style={styles.groupHeader}>{'Version'}</Text>
            <Text style={styles.groupContent}>{localPackage.appVersion}</Text>
          </View>
        </View>
        <View style={{ flexDirection: 'row', columnGap: 16 }}>
          <View style={styles.groupContainer}>
            <Text style={styles.groupHeader}>{'Local Package'}</Text>
            <Text style={styles.groupContent}>{localPackage.label}</Text>
          </View>
          <View style={styles.groupContainer}>
            <Text style={styles.groupHeader}>{'Package Size'}</Text>
            <Text style={styles.groupContent}>{(localPackage.packageSize / 1024 / 1024).toFixed(0) + ' MB'}</Text>
          </View>
        </View>
        <View style={{ flexDirection: 'row', columnGap: 16 }}>
          {/*<View style={styles.groupContainer}>*/}
          {/*  <Text style={styles.groupHeader}>{'Description'}</Text>*/}
          {/*  <Text style={styles.groupContent}>{localPackage.description}</Text>*/}
          {/*</View>*/}
          <View style={styles.groupContainer}>
            <Text style={styles.groupHeader}>{'Last Updated'}</Text>
            <Text style={styles.groupContent}>{''}</Text>
          </View>
        </View>
        <View style={{ flexDirection: 'row', columnGap: 16 }}>
          <View style={styles.groupContainer}>
            <Text style={styles.groupHeader}>{'Status'}</Text>
            {/* <Text style={styles.groupContent}>{selectedDeployment.description}</Text> */}
            <>
              <View>
                {syncStatus == null && <Text style={styles.groupContent}>{'Pending'}</Text>}
                {syncStatus === 0 && <Text style={styles.groupContent}>{'Version Mismatch'}</Text>}
                {syncStatus === 1 && <Text style={styles.groupContent}>{'Installed'}</Text>}
                {syncStatus === 2 && <Text style={styles.groupContent}>{'Ignored'}</Text>}
                {syncStatus === 3 && <Text style={styles.groupContent}>{'Unknown Error'}</Text>}
                {syncStatus === 4 && <Text style={styles.groupContent}>{'Blocked'}</Text>}
                {syncStatus === 5 && <Text style={styles.groupContent}>{'Deploying...'}</Text>}
              </View>
            </>
          </View>
        </View>
      </View>
      {/* <ScrollView> */}
      {/*  <Text style={styles.buttonText}>{JSON.stringify(selectedDeployment, null, 2)}</Text> */}
      {/* </ScrollView> */}
      <View style={{ flex: 1 }}></View>

      <View style={{ flexDirection: 'row', columnGap: 16 }}>
        <TestButton style={{ flex: 1, backgroundColor: '#d98800' }} title={'Check For Updates'} icon={null} onPress={onSync} />
      </View>
      <View style={{ flexDirection: 'row' }}></View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    flex: 1,
    flexDirection: 'row',
  },
  leftPanel: {
    flex: 1,
    padding: 16,
  },
  rightPanel: {
    flex: 2,
    padding: 16,
    backgroundColor: '#f8f8f8',
  },
  divider: {
    width: 1,
    backgroundColor: '#E0E0E0',
  },
  buttonGroup: {
    marginBottom: 24,
  },
  groupTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    color: '#333',
  },
  testButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
    padding: 12,
    borderRadius: 10,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  testButtonSelected: {
    backgroundColor: '#e8f0fe',
    borderColor: '#007AFF',
  },
  testButtonTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
    borderRadius: 8,
    marginBottom: 4,
    borderWidth: 1,
    padding: 2,
    borderColor: '#e0e0e0',
  },
  testButtonTagSelected: {
    backgroundColor: '#e8f0fe',
    borderColor: '#007AFF',
  },
  buttonIcon: {
    color: '#666',
    marginRight: 8,
  },
  buttonText: {
    color: '#333',
    fontSize: 15,
    fontWeight: '500',
    paddingRight: 20,
  },
  buttonTextSelected: {
    color: '#007AFF',
  },
  buttonTextSecondary: {
    color: '#969696',
    fontSize: 15,
    fontWeight: '500',
    paddingRight: 20,
  },
  buttonTextSecondarySelected: {
    color: '#007AFF',
  },
  emptyStatus: {
    flex: 2,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f8f8f8',
  },
  emptyText: {
    marginTop: 16,
    fontSize: 16,
    color: '#999',
  },
  statusGroup: {
    marginBottom: 24,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  statusItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  statusLabel: {
    fontSize: 14,
    color: '#666',
  },
  statusValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  contentWrapper: {
    backgroundColor: '#fff',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.2,
    shadowRadius: 20,
    elevation: 2,
    borderWidth: 1, // Added border
    borderColor: '#e0e0e0', // Border colorad
  },
  contentTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  contentBody: {
    flex: 1,
  },
  description: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
  },
  optionGroup: {
    marginBottom: 20,
  },
  optionLabel: {
    fontSize: 14,
    color: '#333',
    marginBottom: 8,
  },
  radioGroup: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  radioButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 20,
  },
  radio: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#007AFF',
    marginRight: 8,
    padding: 2,
  },
  radioSelected: {
    backgroundColor: '#007AFF',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 6,
    padding: 8,
    fontSize: 14,
    width: 200,
  },
  rangeInputs: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rangeInput: {
    width: 100,
  },
  rangeSeparator: {
    marginHorizontal: 10,
  },
  developingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  developingTitle: {
    fontSize: 18,
    color: '#666',
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  developingDesc: {
    fontSize: 14,
    color: '#999',
  },
  groupContainer: {
    flex: 1,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#d1d1d1',
    backgroundColor: '#e8e8e8',
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  groupHeader: { fontSize: 16, color: '#555' },
  groupContent: { fontSize: 28, color: '#111' },
});
