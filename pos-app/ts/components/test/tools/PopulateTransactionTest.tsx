import { StyleSheet, Text, View } from 'react-native';
import React, { useState, useEffect } from 'react';
import { TestButton } from './TestButton';
import { useDispatch, useSelector } from 'react-redux';
import { clearReceiptRendering, populateTransactions, startReceiptRendering, toggleToastInfo } from '../../../actions';
import { useAsyncEffect, useMemoizedFn, useTimeout } from 'ahooks';
import RNFS from 'react-native-fs';
import { useTimestamp } from '../../../hooks/common/useTimestamp';

const FreeSpaceInfo = () => {
  const [freeSpace, setFreeSpace] = useState(null);

  const timestamp = useTimestamp(1000);
  const fetchFreeSpace = async () => {
    try {
      const freeDiskStorage = await RNFS.getFSInfo();
      setFreeSpace(freeDiskStorage.freeSpace);
    } catch (error) {
      console.error('Failed to get free space:', error);
    }
  };
  useAsyncEffect(fetchFreeSpace, [timestamp]);

  return (
    <View style={{ flexDirection: 'column', rowGap: 16 }}>
      <View style={{ flexDirection: 'row', columnGap: 16 }}>
        <View style={styles.groupContainer}>
          <Text style={styles.groupHeader}>{'Free Space'}</Text>
          <Text style={styles.groupContent}>{freeSpace !== null ? `${(freeSpace / (1024 * 1024)).toFixed(2)} MB` : 'Loading...'}</Text>
        </View>
      </View>
    </View>
  );
};

export const PopulateTransactionTest = () => {
  const dispatch = useDispatch();

  const saveTransaction = useMemoizedFn((count: number) => {
    dispatch(toggleToastInfo({ visible: true, text: 'save start' }));
    dispatch(populateTransactions(count));
    dispatch(toggleToastInfo({ visible: true, text: `${count} Inserted` }));
  });

  return (
    <View style={{ flex: 1 }}>
      <FreeSpaceInfo />
      <View style={{ flex: 1 }} />
      <View style={{ flexDirection: 'row', columnGap: 16 }}>
        <TestButton
          style={{ flex: 1, backgroundColor: '#d98800' }}
          title={'Insert 1'}
          icon={null}
          onPress={async () => {
            saveTransaction(1);
          }}
        />
        <TestButton
          style={{ flex: 1, backgroundColor: '#d98800' }}
          title={'Insert 10'}
          icon={null}
          onPress={async () => {
            saveTransaction(10);
          }}
        />
        <TestButton
          style={{ flex: 1, backgroundColor: '#d98800' }}
          title={'Insert 100'}
          icon={null}
          onPress={async () => {
            saveTransaction(100);
          }}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    flex: 1,
    flexDirection: 'row',
  },
  leftPanel: {
    flex: 1,
    padding: 16,
  },
  rightPanel: {
    flex: 2,
    padding: 16,
    backgroundColor: '#f8f8f8',
  },
  divider: {
    width: 1,
    backgroundColor: '#E0E0E0',
  },
  buttonGroup: {
    marginBottom: 24,
  },
  groupTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    color: '#333',
  },
  testButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
    padding: 12,
    borderRadius: 10,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  testButtonSelected: {
    backgroundColor: '#e8f0fe',
    borderColor: '#007AFF',
  },
  testButtonTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
    borderRadius: 8,
    marginBottom: 4,
    borderWidth: 1,
    padding: 2,
    borderColor: '#e0e0e0',
  },
  testButtonTagSelected: {
    backgroundColor: '#e8f0fe',
    borderColor: '#007AFF',
  },
  buttonIcon: {
    color: '#666',
    marginRight: 8,
  },
  buttonText: {
    color: '#333',
    fontSize: 15,
    fontWeight: '500',
    paddingRight: 20,
  },
  buttonTextSelected: {
    color: '#007AFF',
  },
  buttonTextSecondary: {
    color: '#969696',
    fontSize: 15,
    fontWeight: '500',
    paddingRight: 20,
  },
  buttonTextSecondarySelected: {
    color: '#007AFF',
  },
  emptyStatus: {
    flex: 2,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f8f8f8',
  },
  emptyText: {
    marginTop: 16,
    fontSize: 16,
    color: '#999',
  },
  statusGroup: {
    marginBottom: 24,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  statusItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  statusLabel: {
    fontSize: 14,
    color: '#666',
  },
  statusValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  contentWrapper: {
    backgroundColor: '#fff',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.2,
    shadowRadius: 20,
    elevation: 2,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  contentTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  contentBody: {
    flex: 1,
  },
  description: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
  },
  optionGroup: {
    marginBottom: 20,
  },
  optionLabel: {
    fontSize: 14,
    color: '#333',
    marginBottom: 8,
  },
  radioGroup: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  radioButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 20,
  },
  radio: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#007AFF',
    marginRight: 8,
    padding: 2,
  },
  radioSelected: {
    backgroundColor: '#007AFF',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 6,
    padding: 8,
    fontSize: 14,
    width: 200,
  },
  rangeInputs: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rangeInput: {
    width: 100,
  },
  rangeSeparator: {
    marginHorizontal: 10,
  },
  developingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  developingTitle: {
    fontSize: 18,
    color: '#666',
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  developingDesc: {
    fontSize: 14,
    color: '#999',
  },
  groupContainer: {
    flex: 1,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#d1d1d1',
    backgroundColor: '#e8e8e8',
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  groupHeader: { fontSize: 16, color: '#555' },
  groupContent: { fontSize: 28, color: '#111' },
  freeSpaceContainer: {
    padding: 10,
  },
  freeSpaceText: {
    fontSize: 16,
  },
});
