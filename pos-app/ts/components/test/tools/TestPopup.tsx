import React, { FC, useEffect, useRef } from 'react';
import { Platform, StyleSheet, Text, View, ViewStyle, Dimensions, Animated, Easing } from 'react-native';
import { SharedStyles } from '../../../constants';
import { TestButton } from './TestButton';

interface Props {
  title: string;
  message: string;
  submitText?: string;
  cancelText?: string;
  textInput?: boolean;
  style?: ViewStyle;
  submitColor?: string;
  cancelColor?: string;
  submitDisabled?: boolean;
  onCancelHandler?: () => void;
  onSubmitHandler?: () => void;
  route?: any;
  children?: () => React.ReactNode;
}

const { width: SCREEN_WIDTH } = Dimensions.get('window');

export const TestPopup: FC<Props> = (props: Props) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    // Entrance animation
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 400,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  }, [fadeAnim, scaleAnim]);

  return (
    <Animated.View style={[styles.container, { opacity: fadeAnim }]}>
      <View style={styles.backdrop} />
      <View style={styles.micaLayer} />
      <Animated.View
        style={[
          styles.containerContent,
          props.style,
          {
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        <View style={styles.glassOverlay} />
        <View style={styles.header}>
          <Animated.Text style={[styles.titleStyle]}>{props.title}</Animated.Text>
          {props.message && (
            <Animated.Text
              style={[
                styles.txtInfo,
                {
                  opacity: fadeAnim,
                },
              ]}
            >
              {props.message}
            </Animated.Text>
          )}
        </View>

        {props.children && <View style={[SharedStyles.flexOneColumnCenter, styles.textContent]}>{props.children()}</View>}

        <Animated.View
          style={[
            styles.buttonContainer,
            {
              opacity: fadeAnim,
            },
          ]}
        >
          {props.cancelText && (
            <TestButton style={[styles.button, styles.cancelButton]} title={props.cancelText || 'Cancel'} icon={null} onPress={props.onCancelHandler} />
          )}
          {props.submitText && (
            <TestButton
              style={[styles.button, styles.submitButton]}
              title={props.submitText || 'OK'}
              icon={null}
              enabled={!props.submitDisabled}
              onPress={props.onSubmitHandler}
            />
          )}
        </Animated.View>
      </Animated.View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  backdrop: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    backgroundColor: 'rgba(0, 0, 0, 0.25)',
  },
  micaLayer: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    backgroundColor: 'rgba(255, 255, 255, 0.02)',
    // Mica texture simulation with multiple layers
  },
  containerContent: {
    width: Math.min(640, SCREEN_WIDTH * 0.85),
    maxWidth: 640,
    borderRadius: 12,
    shadowRadius: 60,
    elevation: 36,
    overflow: 'hidden',
    backgroundColor: '#fff',
  },
  glassOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '50%',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  header: {
    paddingTop: 18,
    paddingHorizontal: 24,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.2)',
    backgroundColor: '#e6e6e6',
  },
  titleStyle: {
    fontSize: 24,
    color: '#1D1D1F',
    textAlign: 'left',
    fontWeight: '600',
    marginBottom: 4,
  },
  txtInfo: {
    fontSize: 18,
    fontWeight: '500',
    textAlign: 'left',
    lineHeight: 26,
  },
  textContent: {
    paddingVertical: 24,
    paddingHorizontal: 32,
    minHeight: 60,
    backgroundColor: 'rgba(255, 255, 255, 0.02)',
  },
  buttonContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 16,
    gap: 16,
    backgroundColor: '#e9e9e9',
  },
  button: {
    flex: 1,
    height: 54,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
  },
  cancelButton: {
    backgroundColor: '#d98800',
    borderColor: 'rgba(0, 122, 255, 0)',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  submitButton: {
    backgroundColor: '#0057b9',
    borderColor: 'rgba(0, 122, 255, 0)',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  // Remove unused styles
  footerContent: {
    paddingHorizontal: 0,
    paddingVertical: 0,
  },
  submitBtnStyle: {
    marginHorizontal: 0,
    height: 96,
    color: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#D6D6D6',
    ...Platform.select({
      android: {
        borderRadius: 0,
      },
    }),
  },
});
