import React, { Component, PureComponent } from 'react';
import { Dimensions, Keyboard, StyleSheet, Text, TextInput, TextInputProps, TouchableHighlight, View, ViewProps } from 'react-native';
import { CommonColors, IsAndroid, IsIOS } from '../../constants';
import { testProps } from '../../utils';
import IconDelete from '../ui/svgIcons/iconDelete';
import IconHide from '../ui/svgIcons/iconHide';
import IconKeyboardSwitch from '../ui/svgIcons/iconKeyboardSwitch';
import IconNormalKeyboard from '../ui/svgIcons/iconNormalKeyboard';

const keyBoardLibs = require('../../../libs/react-native-custom-keyboard-kit');
const { CustomTextInput, register } = keyBoardLibs;
const { backSpace, hideKeyboard, insertText, enter, switchSystemKeyboard } = keyBoardLibs.default;

interface PosTextInputProps extends TextInputProps {
  delayMillisecond: number;
}

interface State {
  x: number;
  y: number;
  width: number;
  height: number;
}

interface MyKeyboardProps extends ViewProps {
  tag: string;
}

class MyKeyboard extends Component<MyKeyboardProps> {
  constructor(props) {
    super(props);
  }

  onPress1 = () => {
    insertText(this.props.tag, '1');
  };

  onPress2 = () => {
    insertText(this.props.tag, '2');
  };

  onPress3 = () => {
    insertText(this.props.tag, '3');
  };

  onPress4 = () => {
    insertText(this.props.tag, '4');
  };

  onPress5 = () => {
    insertText(this.props.tag, '5');
  };

  onPress6 = () => {
    insertText(this.props.tag, '6');
  };

  onPress7 = () => {
    insertText(this.props.tag, '7');
  };

  onPress8 = () => {
    insertText(this.props.tag, '8');
  };

  onPress9 = () => {
    insertText(this.props.tag, '9');
  };

  onPress00 = () => {
    insertText(this.props.tag, '00');
  };

  onPressBackSpace = () => {
    backSpace(this.props.tag);
  };

  onPress0 = () => {
    insertText(this.props.tag, '0');
  };

  onPressHideKeyboard = () => {
    hideKeyboard(this.props.tag);
  };

  onPressSwitchSystemKeyboard = () => {
    switchSystemKeyboard(this.props.tag);
  };

  onPressEnter = () => {
    enter(this.props.tag);
  };

  onPressDecimal = () => {
    insertText(this.props.tag, '.');
  };

  render() {
    return (
      <View style={styles.keyboardContainer}>
        <View style={styles.keyboard}>
          <View style={styles.singleColomn}>
            <TouchableHighlight style={styles.singleNumberHighlight} onPress={this.onPress1} underlayColor={'gray'} activeOpacity={0.7}>
              <View style={styles.highlightButton}>
                <Text style={styles.numberLabel}>1</Text>
              </View>
            </TouchableHighlight>
            <TouchableHighlight style={styles.singleNumberHighlight} onPress={this.onPress4} underlayColor={'gray'} activeOpacity={0.7}>
              <View style={styles.highlightButton}>
                <Text style={styles.numberLabel}>4</Text>
              </View>
            </TouchableHighlight>
            <TouchableHighlight style={styles.singleNumberHighlight} onPress={this.onPress7} underlayColor={'gray'} activeOpacity={0.7}>
              <View style={styles.highlightButton}>
                <Text style={styles.numberLabel}>7</Text>
              </View>
            </TouchableHighlight>
            <TouchableHighlight style={styles.singleNumberHighlight} onPress={this.onPress0} underlayColor={'gray'} activeOpacity={0.7}>
              <View style={styles.highlightButton}>
                <Text style={styles.numberLabel}>0</Text>
              </View>
            </TouchableHighlight>
          </View>
          <View style={styles.doubleColumn}>
            <View style={styles.doubleColumnRow}>
              <TouchableHighlight style={styles.singleNumberHighlight} onPress={this.onPress2} underlayColor={'gray'} activeOpacity={0.7}>
                <View style={styles.highlightButton}>
                  <Text style={styles.numberLabel}>2</Text>
                </View>
              </TouchableHighlight>
              <TouchableHighlight style={styles.singleNumberHighlight} onPress={this.onPress3} underlayColor={'gray'} activeOpacity={0.7}>
                <View style={styles.highlightButton}>
                  <Text style={styles.numberLabel}>3</Text>
                </View>
              </TouchableHighlight>
            </View>
            <View style={styles.doubleColumnRow}>
              <TouchableHighlight style={styles.singleNumberHighlight} onPress={this.onPress5} underlayColor={'gray'} activeOpacity={0.7}>
                <View style={styles.highlightButton}>
                  <Text style={styles.numberLabel}>5</Text>
                </View>
              </TouchableHighlight>
              <TouchableHighlight style={styles.singleNumberHighlight} onPress={this.onPress6} underlayColor={'gray'} activeOpacity={0.7}>
                <View style={styles.highlightButton}>
                  <Text style={styles.numberLabel}>6</Text>
                </View>
              </TouchableHighlight>
            </View>
            <View style={styles.doubleColumnRow}>
              <TouchableHighlight style={styles.singleNumberHighlight} onPress={this.onPress8} underlayColor={'gray'} activeOpacity={0.7}>
                <View style={styles.highlightButton}>
                  <Text style={styles.numberLabel}>8</Text>
                </View>
              </TouchableHighlight>
              <TouchableHighlight style={styles.singleNumberHighlight} onPress={this.onPress9} underlayColor={'gray'} activeOpacity={0.7}>
                <View style={styles.highlightButton}>
                  <Text style={styles.numberLabel}>9</Text>
                </View>
              </TouchableHighlight>
            </View>
            <View style={styles.doubleColumnRow}>
              <TouchableHighlight style={styles.doubleNumberHighlight} onPress={this.onPress00} underlayColor={'gray'} activeOpacity={0.7}>
                <View style={styles.highlightButton}>
                  <Text style={styles.numberLabel}>00</Text>
                </View>
              </TouchableHighlight>
            </View>
          </View>
          <View style={styles.rightSingleColomn}>
            <TouchableHighlight style={styles.singleNumberHighlight} onPress={this.onPressBackSpace} underlayColor={'gray'} activeOpacity={0.7}>
              <View style={styles.highlightButton}>
                <IconDelete width={DELETE_WIDTH} height={DELETE_HEIGHT} color={CommonColors.Icon}></IconDelete>
              </View>
            </TouchableHighlight>
            <TouchableHighlight style={styles.singleNumberHighlight} onPress={this.onPressDecimal} underlayColor={'gray'} activeOpacity={0.7}>
              <View style={styles.highlightButton}>
                <Text style={styles.numberLabel}>.</Text>
              </View>
            </TouchableHighlight>
            <TouchableHighlight style={styles.bigEnter} onPress={this.onPressEnter} underlayColor={'#FF7D1F'} activeOpacity={0.7}>
              <View style={styles.enterHighlight}>
                <Text style={styles.enterLabel}>Enter</Text>
              </View>
            </TouchableHighlight>
          </View>
        </View>
        <View style={styles.hideKeyboard}>
          <TouchableHighlight style={styles.singleNumberHighlight} onPress={this.onPressSwitchSystemKeyboard} underlayColor={'gray'} activeOpacity={0.7}>
            <View style={styles.rowHighlightButton}>
              <IconNormalKeyboard width={HIDE_WIDTH} height={HIDE_HEIGHT} color={CommonColors.Icon}></IconNormalKeyboard>
              <IconKeyboardSwitch width={HIDE_WIDTH * 0.65} height={HIDE_HEIGHT * 0.65} color={CommonColors.Icon}></IconKeyboardSwitch>
            </View>
          </TouchableHighlight>
          <TouchableHighlight style={styles.hideSingleNumberHighlight} onPress={this.onPressHideKeyboard} underlayColor={'gray'} activeOpacity={0.7}>
            <View style={styles.highlightButton}>
              <IconHide width={HIDE_WIDTH} height={HIDE_HEIGHT} color={CommonColors.Icon}></IconHide>
            </View>
          </TouchableHighlight>
        </View>
      </View>
    );
  }
}

if (IsIOS) {
  register('price', () => MyKeyboard);
}

export default class CurrencyTextInput extends PureComponent<PosTextInputProps, State> {
  private inputRef;
  private inputFocusTimeout;
  private keyboardDidHideListener;

  static defaultProps = {
    delayMillisecond: 100,
  };

  constructor(props) {
    super(props);
    this.state = {
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    };
  }

  componentDidMount() {
    const { autoFocus = false } = this.props;
    if (autoFocus) {
      this.makeInputFocus();
    }
    if (IsAndroid) {
      // fix SUNMI T2 keyboard hide issue
      this.keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', this.keyboardDidHide);
    }
  }

  componentWillUnmount() {
    this.inputFocusTimeout && clearTimeout(this.inputFocusTimeout);
    this.keyboardDidHideListener && this.keyboardDidHideListener.remove();
  }

  makeInputFocus = () => {
    const { delayMillisecond } = this.props;
    this.inputFocusTimeout = setTimeout(this.focus, delayMillisecond);
  };

  focus = () => {
    this.inputRef && this.inputRef.focus && this.inputRef.focus();
  };

  blur = () => {
    this.inputRef && this.inputRef.blur();
  };

  onLayout = ({ nativeEvent }) => {
    const {
      layout: { x, y, width, height },
    } = nativeEvent;
    this.setState({
      x,
      y,
      width,
      height,
    });
  };

  onInputRef = ref => (this.inputRef = ref);

  keyboardDidHide = () => {
    this.inputRef && this.inputRef.blur();
  };

  render() {
    const { style = {} } = this.props;
    return (
      <>
        <TextInput
          {...testProps('al_textinput_557')}
          editable={false}
          onLayout={this.onLayout}
          {...this.props}
          style={[style]}
          autoCorrect={false}
          spellCheck={false}
          textContentType='none'
        />
        {IsIOS ? this.renderCustomInput() : this.renderPosInput()}
      </>
    );
  }

  renderPosInput() {
    const { style = {} } = this.props;
    const { x, y, width, height } = this.state;
    return (
      <TextInput
        {...testProps('al_textinput_426')}
        ref={this.onInputRef}
        {...this.props}
        placeholderTextColor={'transparent'}
        placeholder=''
        style={[style, { left: x, top: y, width, height }, styles.bottomLayer]}
        autoFocus={false}
        autoCorrect={false}
        spellCheck={false}
        textContentType='none'
      />
    );
  }

  renderCustomInput() {
    const { style = {} } = this.props;
    const { x, y, width, height } = this.state;
    return (
      <View style={[{ left: x, top: y, width, height }, styles.bottomLayer]}>
        <CustomTextInput
          ref={this.onInputRef}
          customKeyboardType='price'
          {...this.props}
          caretHidden={false}
          style={[style, styles.bottomLayer, { width, height }]}
          placeholderTextColor={'transparent'}
          returnKeyType=''
          autoCorrect={false}
          spellCheck={false}
          textContentType='none'
        />
      </View>
    );
  }
}

const { height } = Dimensions.get('window');
const SCREEN_HEIGHT = height;
const KEYBOARD_HEIGHT = SCREEN_HEIGHT * 0.474; // same with the keyboard height defined in the iOS native code
const KEY_HEIGHT = KEYBOARD_HEIGHT * 0.217;
const KEY_WIDTH = KEY_HEIGHT * 1.747;
const KEY_SPACE = KEYBOARD_HEIGHT * 0.022;
const SINGLE_COLOMN_HEIGHT = KEY_HEIGHT * 4 + KEY_SPACE * 3;
const BOTTM_MARGIN = KEYBOARD_HEIGHT * 0.033;
const NUMBER_FONT = KEYBOARD_HEIGHT * 0.089;
const ENTER_FONT = KEYBOARD_HEIGHT * 0.055;
const DELETE_HEIGHT = KEYBOARD_HEIGHT * 0.04;
const DELETE_WIDTH = DELETE_HEIGHT * 1.76;
const HIDE_HEIGHT = KEYBOARD_HEIGHT * 0.078;
const HIDE_WIDTH = HIDE_HEIGHT * 1.2;
const RADIUS = 10;

const styles = StyleSheet.create({
  bottomLayer: {
    position: 'absolute',
    zIndex: 1,
    color: 'transparent',
    backgroundColor: 'transparent',
    marginVertical: 0,
  },
  keyboardContainer: {
    backgroundColor: '#ECEEF4',
    height: KEYBOARD_HEIGHT,
  },
  keyboard: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  singleColomn: {
    flexDirection: 'column',
    justifyContent: 'space-between',
    width: KEY_WIDTH,
    height: SINGLE_COLOMN_HEIGHT,
  },
  rightSingleColomn: {
    flexDirection: 'column',
    width: KEY_WIDTH,
    height: SINGLE_COLOMN_HEIGHT,
    justifyContent: 'space-between',
  },
  doubleColumn: {
    flexDirection: 'column',
    width: 2 * KEY_WIDTH + KEY_SPACE,
    height: SINGLE_COLOMN_HEIGHT,
    justifyContent: 'space-between',
    marginLeft: KEY_SPACE,
    marginRight: KEY_SPACE,
  },
  doubleColumnRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  numberLabel: {
    textAlign: 'center',
    justifyContent: 'center',
    alignItems: 'center',
    fontSize: NUMBER_FONT,
    fontWeight: '400',
    color: '#000',
  },
  enterLabel: {
    textAlign: 'center',
    justifyContent: 'center',
    alignItems: 'center',
    fontSize: ENTER_FONT,
    fontWeight: '600',
    color: 'white',
  },
  hideKeyboard: {
    width: KEY_WIDTH,
    justifyContent: 'center',
    position: 'absolute',
    right: 20,
    bottom: BOTTM_MARGIN,
  },
  singleNumberHighlight: {
    borderRadius: RADIUS,
    width: KEY_WIDTH,
    overflow: 'hidden',
  },
  hideSingleNumberHighlight: {
    borderRadius: RADIUS,
    width: KEY_WIDTH,
    overflow: 'hidden',
    marginTop: 15,
  },
  bigEnter: {
    borderRadius: RADIUS,
    height: 2 * KEY_HEIGHT + 1 * KEY_SPACE,
    backgroundColor: '#FF8F00',
  },
  doubleNumberHighlight: {
    borderRadius: RADIUS,
    width: 2 * KEY_WIDTH + KEY_SPACE,
  },
  highlightButton: {
    backgroundColor: 'white',
    height: KEY_HEIGHT,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: RADIUS,
  },
  rowHighlightButton: {
    flexDirection: 'row',
    backgroundColor: 'white',
    height: KEY_HEIGHT,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: RADIUS,
  },
  enterHighlight: {
    height: 2 * KEY_HEIGHT + 1 * KEY_SPACE,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
