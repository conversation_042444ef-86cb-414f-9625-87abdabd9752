import React, { PureComponent } from 'react';
import { Keyboard, TextInput, TextInputProps } from 'react-native';
import { testProps } from '../../utils';

interface PosTextInputProps extends TextInputProps {
  delayMillisecond: number;
}

export default class PosTextInput extends PureComponent<PosTextInputProps> {
  private inputRef;
  private inputFocusTimeout;
  private keyboardDidHideListener;

  static defaultProps = {
    delayMillisecond: 100,
  };

  constructor(props) {
    super(props);
  }

  componentDidMount() {
    const { autoFocus = false } = this.props;
    if (autoFocus) {
      this.makeInputFocus();
    }
    this.keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', this.keyboardDidHide);
  }

  componentWillUnmount() {
    this.inputFocusTimeout && clearTimeout(this.inputFocusTimeout);
    this.keyboardDidHideListener && this.keyboardDidHideListener.remove();
  }

  makeInputFocus = () => {
    const { delayMillisecond } = this.props;
    this.inputFocusTimeout = setTimeout(this.focus, delayMillisecond);
  };

  focus = () => {
    this.inputRef && this.inputRef.focus();
  };

  blur = () => {
    this.inputRef && this.inputRef.blur();
  };

  setNativeProps = params => {
    this.inputRef.setNativeProps(params);
  };

  onInputRef = ref => {
    this.inputRef = ref;
  };

  keyboardDidHide = () => {
    this.inputRef && this.inputRef.blur();
  };

  clear = () => {
    this.inputRef && this.inputRef.clear();
  };

  isFocused = () => {
    return this.inputRef && this.inputRef.isFocused();
  };

  render() {
    return (
      <TextInput
        {...testProps('al_textinput_78')}
        {...this.props}
        ref={this.onInputRef}
        autoCorrect={false}
        spellCheck={false}
        autoCapitalize='none'
        smartInsertDelete={false}
        textContentType='none'
      />
    );
  }
}
