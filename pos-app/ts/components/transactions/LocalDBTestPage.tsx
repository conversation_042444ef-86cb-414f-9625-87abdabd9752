import React, { FC, memo, useEffect, useState } from 'react';
import { StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';

import { DrawerActions } from '@react-navigation/native';

import { CommonColors, currentThemes, scaleSizeH, scaleSizeW } from '../../constants';
import { useAppNavigation } from '../../hooks/common/useAppNavigation';

import { noop } from 'lodash';
import moment from 'moment';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ConnectedProps, connect, useDispatch } from 'react-redux';
import { UpdateMode } from 'realm';
import { bindActionCreators } from 'redux';
import { setShiftStatus, syncShiftsAction } from '../../actions';
import PaymentOptions from '../../config/paymentOption';
import DAL from '../../dal';
import { PromotionHelper } from '../../dal/helper';
import RealmManager from '../../dal/realm';
import { selectEmployeeId, selectRegisterObjectId, selectShiftOpenStatus } from '../../sagas/selector';
import { RootState, ScreenProps } from '../../typings';
import { isValidNumber, testProps } from '../../utils';
import { getIOSStringFromDate } from '../../utils/datetime';
import { isEmpty } from '../../utils/validator';
import TopMainBar from '../common/TopMainBar';
import { IconMenu } from '../ui';

const uuidv4 = require('uuid/v4');

const WorkingStatus = {
  Idle: 'Mock Data',
  Working: 'Mocking...',
};

const ShiftStatus = {
  Open: 'Open',
  Close: 'Close',
};

type Props = PropsFromRedux & ScreenProps;

const createBeepNotiWithoutJobs = orderId => ({
  orderId,
  receiptNumber: '0022206270042291',
  messageId: null,
  status: null,
  uploaded: null,
  receiptPrinted: null,
  isKitchenPrinted: false,
  isOrderSummaryPrinted: false,
  jobTitle: 'Table ppp',
  isBeepOrder: false,
});
const createSubNotiWithoutJobs = submitId => ({
  submitId,
  orderId: submitId,
  messageId: null,
  uploaded: null,
  isHalfReceiptPrinted: null,
  isKitchenPrinted: false,
  isOrderSummaryPrinted: false,
  jobTitle: 'Table ppp',
  isBeepOrder: true,
});

const LocalDBTest: FC<Props> = props => {
  const baseTransaction = TransactionHelper.serializeTransaction(DAL.getBaseTransaction());
  const {
    registerId,
    employeeId,
    shiftOpenStatus,
    actions: { setShiftStatus },
  } = props;

  const dispatch = useDispatch();

  const navigation = useAppNavigation();

  const [localTrxCount, setLocalTrxCount] = useState(0);

  const [curStatus, setCurStatus] = useState(WorkingStatus.Idle);

  const [mockCount, setMockCount] = useState(10000);

  const [trxCountOnLastestShift, setTrxCountOnLastestShift] = useState(0);

  const createBeepNotis = () => {
    if (RealmManager.getRealmInstance().isInTransaction) {
      console.log('isInTransaction');
      return;
    }
    RealmManager.getRealmInstance().beginTransaction();
    const transactionDate = moment().subtract(2, 'w').toDate();
    for (let index = 0; index < 1000; index++) {
      RealmManager.getRealmInstance().create('BeepNotification', { ...createBeepNotiWithoutJobs(index.toString()), transactionDate }, UpdateMode.All);
    }
    const now = new Date();
    for (let index = 0; index < 200; index++) {
      RealmManager.getRealmInstance().create('BeepNotification', { ...createBeepNotiWithoutJobs(`1_${index}`), transactionDate: now }, UpdateMode.All);
    }
    RealmManager.getRealmInstance().commitTransaction();
    console.log('created createBeepNotis');
  };

  const createSubOrderNotis = () => {
    if (RealmManager.getRealmInstance().isInTransaction) {
      console.log('isInTransaction');
      return;
    }
    RealmManager.getRealmInstance().beginTransaction();
    const transactionDate = moment().subtract(2, 'w').toDate();
    for (let index = 0; index < 1000; index++) {
      RealmManager.getRealmInstance().create('SubOrderNotification', { ...createSubNotiWithoutJobs(index.toString()), transactionDate }, UpdateMode.All);
    }
    const now = new Date();
    for (let index = 0; index < 200; index++) {
      RealmManager.getRealmInstance().create('SubOrderNotification', { ...createSubNotiWithoutJobs(`1_${index}`), transactionDate: now }, UpdateMode.All);
    }
    RealmManager.getRealmInstance().commitTransaction();
    console.log('created createSubOrderNotis');
  };

  const deleteAllRecords = () => {
    dispatch(syncShiftsAction({}));
  };

  const refreshLocalTrxCount = () => {
    const curLocalTrxCount = DAL.getLocalTransactionCount();
    setLocalTrxCount(curLocalTrxCount);
  };

  const refreshShiftStatus = () => {
    const currentShift = DAL.getLastShift();
    let curShiftStatus = false;
    if (currentShift !== undefined) {
      const { openTime, closeTime } = currentShift;
      curShiftStatus = currentShift.closeTime === undefined || currentShift.closeTime === null;

      const count = DAL.getTrxCountonLastestShift(openTime, closeTime, registerId);
      setTrxCountOnLastestShift(count);
    }
    setShiftStatus(curShiftStatus);
  };

  const clearAllLocalTrx = () => {
    DAL.clearAllLocalTransaction();
    refreshLocalTrxCount();
  };

  const onDidMount = () => {
    refreshLocalTrxCount();
    refreshShiftStatus();
  };

  useEffect(onDidMount, []);

  const openDrawer = () => navigation.dispatch(DrawerActions.openDrawer());

  const mockTransaction = () => {
    if (!baseTransaction) {
      navigation.navigate('ModalInfo', {
        info: 'plz place a transaction manually first',
        textAligh: 'center',
        onSubmitHandler: noop,
      });
      return;
    }
    setCurStatus(WorkingStatus.Working);
    const trxs = [];
    for (let index = 0; index < mockCount; index++) {
      const now = moment();
      const transactionId = `${now.unix()}_${index + 1}`;
      const createdDate = now.toISOString();
      const modifiedDate = now.subtract(-1, 'minute').toISOString();
      const uploadedDate = now.subtract(-1, 'minute').toISOString();
      const trx = { ...baseTransaction, createdDate, modifiedDate, uploadedDate, transactionId, receiptNumber: transactionId, registerId };
      trxs.push(trx);
    }
    DAL.saveTransactions(trxs);
    setCurStatus(WorkingStatus.Idle);
    refreshLocalTrxCount();
    refreshShiftStatus();
  };

  const openShift = () => {
    const newShift: any = {};
    newShift.shiftId = uuidv4();
    newShift.openBy = employeeId;
    const now = new Date();
    newShift.openTime = now.toISOString();
    newShift.openingAmount = 0;
    newShift.version = 1;
    const { success, reason } = DAL.saveShift(newShift);
    if (success) {
      setShiftStatus(true);
    }
  };

  const closeShift = () => {
    const currentShift = DAL.getLastShift();
    const now = new Date();
    const newShift = {
      shiftId: currentShift.shiftId,
      closeBy: employeeId,
      closeTime: now.toISOString(),
      closingAmount: 0,
    };
    const { success, reason } = DAL.saveShift(newShift);
    if (success) {
      setShiftStatus(false);
    }
  };

  const onInputMockCount = value => {
    if (isValidNumber(value)) {
      setMockCount(Math.max(Number(value), 0));
    } else if (isEmpty(value)) {
      setMockCount(0);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <TopMainBar
        isLeftContainerTitle={false}
        leftIcon={<IconMenu color={CommonColors.Icon} />}
        leftText={'LocalDB Test'}
        rightIsIcon={true}
        rightButtonText={''}
        isRightButtonShow={true}
        onLeftClick={openDrawer}
        rightBtnDisabled
      />
      <View style={styles.rowConatiner}>
        <View style={styles.txtContainer}>
          <Text style={styles.commonText}>Transaction count</Text>
        </View>
        <View style={styles.txtContainer}>
          <Text style={styles.commonText}>{localTrxCount}</Text>
        </View>
        <TouchableOpacity {...testProps('al_btn_916')} style={styles.buttonContainer} onPress={refreshLocalTrxCount}>
          <Text style={styles.buttonText}>Refresh</Text>
        </TouchableOpacity>
      </View>
      <View style={styles.rowConatiner}>
        <View style={styles.inputContainer}>
          <TextInput
            {...testProps('al_textinput_216')}
            style={styles.inputStyle}
            placeholder={'mock count'}
            keyboardType='numeric'
            underlineColorAndroid='transparent'
            autoCapitalize={'none'}
            value={String(mockCount)}
            onChangeText={onInputMockCount}
          />
        </View>
        <TouchableOpacity {...testProps('al_btn_47')} style={styles.buttonContainer} onPress={clearAllLocalTrx}>
          <Text style={styles.buttonText}>Clear</Text>
        </TouchableOpacity>
        <TouchableOpacity
          {...testProps('al_btn_728')}
          disabled={curStatus === WorkingStatus.Working}
          style={[styles.buttonContainer, curStatus === WorkingStatus.Working && styles.disableButtonContainer]}
          onPress={mockTransaction}
        >
          <Text style={styles.buttonText}>{curStatus}</Text>
        </TouchableOpacity>
      </View>
      <View style={styles.rowConatiner}>
        <View style={styles.txtContainer}>
          <Text style={[styles.commonText, { paddingBottom: 0 }]}>
            Shift Open Status: <Text style={{ color: shiftOpenStatus ? '#00FF00' : '#FF0000' }}>{shiftOpenStatus ? ShiftStatus.Open : ShiftStatus.Close}</Text>
          </Text>
          <Text style={[styles.commonText, { paddingTop: 6 }]}>
            Trx count on lastest Shift:
            <Text style={{ color: shiftOpenStatus ? '#00FF00' : '#FF0000' }}>{trxCountOnLastestShift}</Text>
          </Text>
        </View>
        <TouchableOpacity
          {...testProps('al_btn_978')}
          disabled={shiftOpenStatus}
          style={[styles.buttonContainer, shiftOpenStatus && styles.disableButtonContainer]}
          onPress={openShift}
        >
          <Text style={styles.buttonText}>Open Shift</Text>
        </TouchableOpacity>

        <TouchableOpacity
          {...testProps('al_btn_141')}
          disabled={!shiftOpenStatus}
          style={[styles.buttonContainer, !shiftOpenStatus && styles.disableButtonContainer]}
          onPress={closeShift}
        >
          <Text style={styles.buttonText}>Close Shift</Text>
        </TouchableOpacity>
      </View>
      <View style={styles.rowConatiner}>
        <View style={styles.txtContainer}>
          <TouchableOpacity {...testProps('al_btn_117')} style={[styles.buttonContainer]} onPress={createBeepNotis}>
            <Text style={styles.buttonText}>Create beep</Text>
          </TouchableOpacity>

          <TouchableOpacity {...testProps('al_btn_19')} style={[styles.buttonContainer]} onPress={createSubOrderNotis}>
            <Text style={styles.buttonText}>Create subOrder</Text>
          </TouchableOpacity>
        </View>
        <TouchableOpacity {...testProps('al_btn_897')} style={[styles.buttonContainer]} onPress={deleteAllRecords}>
          <Text style={styles.buttonText}>Delete all records</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const mapStateToProps = (state: RootState) => ({
  registerId: selectRegisterObjectId(state),
  employeeId: selectEmployeeId(state),
  shiftOpenStatus: selectShiftOpenStatus(state),
});

const mapDispatchToProps = dispatch => ({
  actions: bindActionCreators(
    {
      setShiftStatus,
    },
    dispatch
  ),
});

const connector = connect(mapStateToProps, mapDispatchToProps);
type PropsFromRedux = ConnectedProps<typeof connector>;
export default memo(connector(LocalDBTest));

class TransactionHelper {
  static serializeTransaction(realmRecord) {
    if (!realmRecord) {
      return null;
    }

    const jsonObj: any = {
      isOriginalOnline: realmRecord.isOriginalOnline,
      total: realmRecord.total || 0,
      subtotal: realmRecord.subtotal || 0,
      discount: realmRecord.discount || 0,
      tax: realmRecord.tax || 0,
      modifiedTime: getIOSStringFromDate(realmRecord.modifiedDate), // Only for uploading transaction
      createdTime: getIOSStringFromDate(realmRecord.createdDate), // Only for uploading transaction
      createdDate: realmRecord.createdDate,
      modifiedDate: realmRecord.modifiedDate,
      employeeNumber: realmRecord.employeeId,
      employeeId: realmRecord.employeeId,
      transactionId: realmRecord.transactionId,
      transactionType: realmRecord.transactionType,
      receiptNumber: realmRecord.receiptNumber,
      isCancelled: realmRecord.isCancelled,
      roundedAmount: realmRecord.roundedAmount,
      appVersion: realmRecord.appVersion,
      serviceCharge: realmRecord.serviceCharge,
      serviceChargeTax: realmRecord.serviceChargeTax,
      serviceChargeRate: realmRecord.serviceChargeRate,
      isOpen: realmRecord.isOpen,
      registerId: realmRecord.registerId,
      registerNumber: realmRecord.registerNumber,
      tableId: realmRecord.tableId,
      takeawayId: realmRecord.takeawayId,
      pickUpId: realmRecord.pickUpId,
      headcount: realmRecord.headcount,
      pwdCount: realmRecord.pwdCount,
      seniorsCount: realmRecord.seniorsCount,
      isOnlineOrder: realmRecord.isOnlineOrder,
      mrs: realmRecord.mrs,
      shippingType: realmRecord.shippingType,
      channel: realmRecord.channel,
      amusementTax: realmRecord.amusementTax || 0,
    };
    // Selective Fileds
    if (realmRecord.takeawayCharges != null) {
      jsonObj.takeawayCharges = realmRecord.takeawayCharges;
    }
    if (realmRecord.takeawayCharge != null) {
      jsonObj.takeawayCharge = realmRecord.takeawayCharge;
    }
    if (realmRecord.salesChannel != null) {
      jsonObj.salesChannel = realmRecord.salesChannel;
    }
    if (realmRecord.enableCashback != null) {
      jsonObj.enableCashback = realmRecord.enableCashback;
    }
    if (realmRecord.originalReceiptNumber != null) {
      jsonObj.originalReceiptNumber = realmRecord.originalReceiptNumber;
    }
    if (realmRecord.comment != null) {
      jsonObj.comment = realmRecord.comment;
    }
    if (realmRecord.returnStatus != null) {
      jsonObj.returnStatus = realmRecord.returnStatus;
    }
    if (realmRecord.returnReason != null) {
      jsonObj.returnReason = realmRecord.returnReason;
    }
    if (realmRecord.customerId != null) {
      jsonObj.customerId = realmRecord.customerId;
    }
    if (realmRecord.pax != null) {
      jsonObj.pax = realmRecord.pax;
    }
    if (realmRecord.seniorDiscount != null) {
      jsonObj.seniorDiscount = realmRecord.seniorDiscount;
    }
    if (realmRecord.pwdDiscount != null) {
      jsonObj.pwdDiscount = realmRecord.pwdDiscount;
    }
    if (realmRecord.taxableSales != null) {
      jsonObj.taxableSales = realmRecord.taxableSales;
    }
    if (realmRecord.taxExemptedSales != null) {
      jsonObj.taxExemptedSales = realmRecord.taxExemptedSales;
    }
    if (realmRecord.zeroRatedSales != null) {
      jsonObj.zeroRatedSales = realmRecord.zeroRatedSales;
    }
    if (realmRecord.totalDeductedTax != null) {
      jsonObj.totalDeductedTax = realmRecord.totalDeductedTax;
    }
    if (realmRecord.shippingFee != null) {
      jsonObj.shippingFee = realmRecord.shippingFee;
    }
    if (realmRecord.shippingFeeDiscount != null) {
      jsonObj.shippingFeeDiscount = realmRecord.shippingFeeDiscount;
    }

    if (realmRecord.sequenceNumber != null) {
      jsonObj.sequenceNumber = realmRecord.sequenceNumber;
    }
    if (realmRecord.invoiceSeqNumber != null) {
      jsonObj.invoiceSeqNumber = realmRecord.invoiceSeqNumber;
    }
    if (realmRecord.pickUpDate != null) {
      jsonObj.pickUpDate = getIOSStringFromDate(realmRecord.pickUpDate);
    }
    if (realmRecord.pickUpId != null) {
      jsonObj.pickUpId = realmRecord.pickUpId;
    }
    if (realmRecord.otherReason != null) {
      jsonObj.otherReason = realmRecord.otherReason;
    }
    if (realmRecord.isCancelled) {
      jsonObj.cancelledAt = getIOSStringFromDate(realmRecord.cancelledAt);
      jsonObj.cancelledBy = realmRecord.cancelledBy;
    }
    if (realmRecord.preOrderId != null) {
      jsonObj.preOrderId = realmRecord.preOrderId;
    }
    if (realmRecord.preOrderBy != null) {
      jsonObj.preOrderBy = realmRecord.preOrderBy;
    }
    if (realmRecord.preOrderDate != null) {
      jsonObj.preOrderDate = realmRecord.preOrderDate;
    }
    if (realmRecord.depositAmount != null) {
      jsonObj.depositAmount = realmRecord.depositAmount;
    }
    if (realmRecord.addonBirCompliance && realmRecord.addonBirCompliance.discountType !== 'SC/PWD') {
      jsonObj.addonBirCompliance = TransactionHelper.serializeAddonBirCompliance(realmRecord.addonBirCompliance);
    }

    if (Boolean(realmRecord.promotions) && realmRecord.promotions.length > 0) {
      jsonObj.promotions = PromotionHelper.serializePromotions(realmRecord.promotions);
    }
    const loyaltyDiscounts = TransactionHelper.serializeLoyaltyDiscounts(realmRecord.loyaltyDiscounts);
    if (loyaltyDiscounts.length > 0) {
      jsonObj.loyaltyDiscounts = loyaltyDiscounts;
    }
    // Purchased Item parse
    jsonObj.items = TransactionHelper.serializeItems(realmRecord.items, realmRecord.takeawayCharge);

    // Payments parse
    jsonObj.payments = TransactionHelper.serializePayments(realmRecord.payments);

    // calcualtion
    if (realmRecord.calculation) {
      jsonObj.calculation = TransactionHelper.serializeCalculation(realmRecord.calculation);
    }

    return jsonObj;
  }

  static serializeItems(items, takeawayCharge?) {
    const result = [];
    if (Boolean(items) && items.length > 0) {
      for (const itemObj of items) {
        const item: any = itemObj;
        const itemJson: any = {
          productId: item.productId,
          quantity: item.quantity || 0,
          subTotal: item.subTotal || 0,
          total: item.total || 0,
          discount: item.discount || 0,
          adhocDiscount: item.adhocDiscount || 0,
          unitPrice: item.unitPrice || 0,
          tax: item.tax || 0,
          isServiceChargeNotApplicable: item.isServiceChargeNotApplicable,
          taxRate: item.taxRate || 0,
          orderingValue: item.orderingValue,
          selectedOptions: item.selectedOptions,
          options: item.options,
          title: item.title,
          taxCode: item.taxCode || '',
          taxableAmount: item.taxableAmount,
          taxExemptAmount: item.taxExemptAmount,
          zeroRatedSales: item.zeroRatedSales,
          totalDeductedTax: item.totalDeductedTax,
          seniorDiscount: item.seniorDiscount,
          pwdDiscount: item.pwdDiscount,
          discountType: item.discountType,
          discountValue: item.discountInputValue, // The server recognizes discountValue rather than discountInputValue.
          athleteAndCoachDiscount: item.athleteAndCoachDiscount,
          soloParentDiscount: item.soloParentDiscount,
          medalOfValorDiscount: item.medalOfValorDiscount,
          isTakeaway: item.isTakeaway,
          loyaltyDiscount: item.loyaltyDiscount || 0,
        };

        if (item.itemChannel) {
          itemJson.itemChannel = item.itemChannel;
        }

        if (item.takeawayCharges) {
          itemJson.takeawayCharges = item.takeawayCharges;
        }

        if (item.takeawayCharge) {
          itemJson.takeawayCharge = item.takeawayCharge;
        } else if (takeawayCharge) {
          itemJson.takeawayCharge = takeawayCharge;
        }

        if (item.itemType != null && item.itemType.length > 0) {
          itemJson.itemType = item.itemType;
        }
        if (item.notes != null && item.notes.length > 0) {
          itemJson.notes = item.notes;
        }
        if (item.comments != null && item.comments.length > 0) {
          itemJson.comments = item.comments;
        }
        if (item.sn != null && item.sn.length > 0) {
          itemJson.sn = item.sn;
        }
        if (item.promotions != null && item.promotions.length > 0) {
          itemJson.promotions = PromotionHelper.serializePromotions(item.promotions);
        }
        if (item.calculation) {
          itemJson.calculation = TransactionHelper.serializeCalculation(item.calculation);
        }
        result.push(itemJson);
      }
    }
    return result;
  }

  static serializePayments(payments) {
    const result = [];

    if (Boolean(payments) && payments.length > 0) {
      for (const itemObj of payments) {
        const item: any = itemObj;
        const { type, paymentMethod } = item;
        const finalPaymentMethod = isValidNumber(item.paymentMethodId)
          ? PaymentOptions.getUploadPaymentMethod(item.paymentMethodId)
          : Boolean(type)
            ? type
            : paymentMethod;
        result.push({
          amount: item.amount,
          paymentMethodId: item.paymentMethodId,
          paymentMethod: finalPaymentMethod, // Only for uploading transaction.
          cashTendered: item.cashTendered,
          roundedAmount: item.roundedAmount,
          isDeposit: item.isDeposit,
          subType: item.subType,
          type: finalPaymentMethod,
          mPOSTxnId: item.mPOSTxnId,
          isOnline: item.isOnline,
          manualApproveInfo: item.manualApproveInfo,
        });
      }
    }
    return result;
  }

  static serializeLoyaltyDiscounts(loyaltyDiscounts) {
    const result = [];
    if (Boolean(loyaltyDiscounts) && loyaltyDiscounts.length > 0) {
      for (const loyaltyDiscount of loyaltyDiscounts) {
        const loyaltyItem: any = loyaltyDiscount;
        const loyaltyJson: any = {
          displayDiscount: loyaltyItem.displayDiscount,
          type: loyaltyItem.type,
          loyaltyType: loyaltyItem.type,
          spentValue: loyaltyItem.spentValue,
        };
        if (loyaltyItem.taxRate != null) {
          loyaltyJson.taxRate = loyaltyItem.taxRate;
        }
        result.push(loyaltyJson);
      }
    }
    return result;
  }

  static serializeAddonBirCompliance(addonBirCompliance) {
    const result: any = {};
    if (addonBirCompliance.discountType !== null) {
      result.discountType = addonBirCompliance.discountType;
    }
    result.athleteAndCoachDiscount = addonBirCompliance.athleteAndCoachDiscount;
    result.medalOfValorDiscount = addonBirCompliance.medalOfValorDiscount;
    result.soloParentDiscount = addonBirCompliance.soloParentDiscount;
    result.collectedInfo = addonBirCompliance.collectedInfo;
    return result;
  }

  static serializeCalculation(calculation) {
    const result: any = {};

    const { fullPrice, discounts, taxes, original } = calculation;

    if (fullPrice) {
      result.fullPrice = fullPrice;
    }
    if (discounts) {
      result.discounts = TransactionHelper.serializeCalculationDiscounts(discounts);
    }
    if (taxes) {
      result.taxes = TransactionHelper.serializeCalculationTaxs(taxes);
    }

    if (original) {
      result.original = {
        tax: original.tax,
        subtotal: original.subtotal,
        total: original.total,
      };
    }

    return result;
  }

  static serializeCalculationDiscounts(discounts) {
    const result = [];

    if (Boolean(discounts) && discounts.length > 0) {
      for (const itemObj of discounts) {
        const item: any = itemObj;
        const { type, discount, deductedTax, subType, promotionId } = item;
        const discountItem: any = {
          type,
          discount: discount || 0,
          deductedTax: deductedTax || 0,
        };
        if (subType) {
          discountItem.subType = subType;
        }
        if (promotionId) {
          discountItem.promotionId = promotionId;
        }
        result.push(discountItem);
      }
    }
    return result;
  }

  static serializeCalculationTaxs(taxes) {
    const result = [];

    if (Boolean(taxes) && taxes.length > 0) {
      for (const itemObj of taxes) {
        const item: any = itemObj;
        const { taxCode, taxRate, tax, isVatExempted, isAmusementTax } = item;
        const taxItem: any = {
          taxCode,
          taxRate,
          tax,
          isVatExempted,
          isAmusementTax,
        };
        result.push(taxItem);
      }
    }
    return result;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: currentThemes.bgMainColor,
  },
  rowConatiner: {
    flexDirection: 'row',
    width: '100%',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: scaleSizeW(100),
  },
  inputStyle: {
    color: '#303030',
    fontSize: currentThemes.fontSize28,
    padding: 0,
    flex: 1,
  },
  inputContainer: {
    marginVertical: scaleSizeH(36),
    marginHorizontal: scaleSizeW(50),
    width: scaleSizeW(300),
    height: scaleSizeH(80),
    borderRadius: scaleSizeH(16),
    borderColor: 'black',
    borderWidth: 1,
    paddingHorizontal: scaleSizeW(24),
    backgroundColor: 'white',
  },
  txtContainer: {
    marginVertical: scaleSizeH(36),
    marginHorizontal: scaleSizeW(50),
  },
  buttonContainer: {
    marginVertical: scaleSizeH(36),
    marginHorizontal: scaleSizeW(50),
    backgroundColor: CommonColors.PrincetonOrange,
    borderRadius: 8,
    overflow: 'hidden',
  },
  commonText: {
    paddingHorizontal: scaleSizeW(24),
    paddingVertical: scaleSizeH(20),
    fontSize: currentThemes.fontSize26,
  },
  buttonText: {
    paddingHorizontal: scaleSizeW(24),
    paddingVertical: scaleSizeH(24),
    color: 'white',
    minWidth: scaleSizeW(260),
    fontSize: currentThemes.fontSize24,
    textAlign: 'center',
  },
  disableButtonContainer: {
    backgroundColor: 'grey',
  },
});
