import React, { useState } from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { connect } from 'react-redux';
import { CustomerType } from '../../actions';
import { currentThemes, SalesChannelType, scaleSizeH, scaleSizeW, t, TransactionFlowType } from '../../constants';
import { selectViewCustomerInfo } from '../../sagas/selector';
import { RootState } from '../../typings';
import { PaymentType } from '../../typings/schema';
import { localeNumber, maskPhoneNumber, testProps } from '../../utils';
import { isEmpty } from '../../utils/validator';
import IconHideCustomerInfoEye from '../ui/svgIcons/iconHideCustomerInfoEye';
import IconViewCustomerInfoEye from '../ui/svgIcons/iconViewCustomerInfoEye';

interface Props {
  customer: CustomerType;
  payments: [PaymentType];
  receiptNumber: string;
  transactionType: string;
  total: number;
  statusText?: string;
  salesChannel?: number;
  isOnlineTransaction: boolean;
  viewCustomerInfo?: boolean;
  navigation?: any;
}

const TransactionBasicInfo = React.memo((props: Props) => {
  const { transactionType, receiptNumber, total, customer, statusText, salesChannel, viewCustomerInfo, navigation } = props;
  const [tempShowInfo, setTempShowInfo] = useState(false);

  let customerName = '';
  let showEyeButton = false;

  const isReturn = transactionType === TransactionFlowType.Return;
  if (Boolean(customer)) {
    const { firstName, lastName, phone } = customer;
    if (salesChannel === SalesChannelType.TAKEAWAY) {
      customerName = t('Takeaway');
    } else if (!isEmpty(firstName) || !isEmpty(lastName)) {
      const shouldShowUnmasked = tempShowInfo || viewCustomerInfo;
      const displayedPhone = shouldShowUnmasked ? phone : maskPhoneNumber(phone);
      customerName = [firstName, lastName].filter(i => !isEmpty(i)).join(' ') + (phone ? ` (${displayedPhone})` : '');
      showEyeButton = !viewCustomerInfo && !isEmpty(phone);
    } else if (!isEmpty(phone)) {
      const shouldShowUnmasked = tempShowInfo || viewCustomerInfo;
      customerName = shouldShowUnmasked ? phone : maskPhoneNumber(phone);
      showEyeButton = !viewCustomerInfo;
    } else {
      customerName = t('Walk-in Customer');
    }
  }

  const toggleTempShowInfo = () => {
    if (!tempShowInfo) {
      if (navigation) {
        navigation.navigate('ModalManagerPinFC', {
          onDismissHandler: result => {
            if (result.ok) {
              setTempShowInfo(true);
            }
          },
        });
      } else {
        setTempShowInfo(true);
      }
    } else {
      setTempShowInfo(false);
    }
  };

  return (
    <View style={styles.container}>
      {Boolean(statusText) && (
        <View style={styles.statusContainer}>
          <Text style={styles.textStatus}>{statusText}</Text>
        </View>
      )}
      <View style={styles.subContainer}>
        <View style={styles.leftPart}>
          <View style={styles.customerNameContainer}>
            <Text style={styles.customerName}>{customerName}</Text>
            {showEyeButton && (
              <TouchableOpacity style={styles.eyeButton} onPress={toggleTempShowInfo} {...testProps('al_btn_eye_transaction')}>
                {tempShowInfo ? (
                  <IconHideCustomerInfoEye color={'#757575'} width={scaleSizeW(40)} height={scaleSizeH(40)} />
                ) : (
                  <IconViewCustomerInfoEye color={'#4FDB87'} width={scaleSizeW(40)} height={scaleSizeH(40)} />
                )}
              </TouchableOpacity>
            )}
          </View>
          <Text testID='receipt id' style={styles.receiptNumber}>
            {receiptNumber}
          </Text>
        </View>
        <View style={styles.rightPart}>
          <Text style={styles.paidTypeText} testID='paidType'>
            {t('TOTAL')}
          </Text>
          <Text style={[styles.textlocalNumber, { color: '#303030' }]} {...testProps('paidTotal')}>
            {(isReturn ? '- ' : '') + localeNumber(total)}
          </Text>
        </View>
      </View>
    </View>
  );
});

const mapStateToProps = (state: RootState) => ({
  viewCustomerInfo: selectViewCustomerInfo(state),
});

export default connect(mapStateToProps)(TransactionBasicInfo);

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    paddingHorizontal: scaleSizeW(24),
    paddingBottom: scaleSizeH(20),
  },
  statusContainer: {
    height: scaleSizeH(56),
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  textStatus: {
    fontSize: currentThemes.fontSize24,
    color: 'white',
  },
  subContainer: {
    width: '100%',
    flexDirection: 'row',
    height: scaleSizeH(170),
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  leftPart: {
    justifyContent: 'center',
    flex: 1,
  },
  customerNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  customerName: {
    fontSize: currentThemes.fontSize28,
    fontWeight: '500',
    color: '#303030',
    lineHeight: currentThemes.fontSize32,
  },
  eyeButton: {
    marginLeft: scaleSizeW(10),
    padding: scaleSizeW(5),
  },
  receiptNumber: {
    fontSize: currentThemes.fontSize18,
    color: '#959595',
    marginTop: scaleSizeH(10),
    lineHeight: currentThemes.fontSize24,
  },
  rightPart: {
    justifyContent: 'center',
    alignItems: 'flex-end',
  },
  paidTypeText: {
    fontSize: currentThemes.fontSize18,
    color: '#959595',
    marginBottom: scaleSizeH(10),
    marginRight: scaleSizeW(6),
    lineHeight: currentThemes.fontSize24,
    textAlign: 'right',
  },
  textlocalNumber: {
    fontSize: currentThemes.fontSize64,
    color: '#303030',
    lineHeight: currentThemes.fontSize72,
    fontWeight: 'bold',
  },
});
