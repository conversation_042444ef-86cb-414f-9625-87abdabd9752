import { get } from 'lodash';
import React, { PureComponent } from 'react';
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import RNFS from 'react-native-fs';
import { CommonColors, currentThemes, IsAndroid, ItemChannelType, scaleSizeH, scaleSizeW, t } from '../../constants';
import {
  checkPriceNumberInput,
  checkQuantityNumberInput,
  getLocaleNumberString,
  getReceiptQtyString,
  localeNumber,
  newConvertCurrencyToSymbol,
  testProps,
} from '../../utils/index';
import * as JSONUtils from '../../utils/json';
import { generateDescriptionString } from '../../utils/transaction';
import { CurrencyTextInput } from '../textInput';
import { IconImageHolder, IconNotes, IconPromotion, IconRemove } from '../ui';

import DAL from '../../dal';
import { PromotionHelper } from '../../dal/helper';
import { getImagePath } from '../../utils/product';
import { ItemSalesPerson } from '../register/ItemSalesPerson';

interface TrxDetailProductItemProps {
  isRefunding: boolean;
  purchasedItem: any;
  itemIndex: number;
  onPressDeleteItem(itemIndex: number): void;
  onChangeItemQuantity;
  onChangeItemTotal;
  isPromotion?: boolean;
  takeawayCharge?: number;
  currency?: string;
  isOnlineTransaction?: boolean;
  localCountryMap: any;
}

interface State {
  thumbnail: string;
  hasThumbnail: boolean;
  returnQuantity?: string;
}

export class TrxDetailProductItem extends PureComponent<TrxDetailProductItemProps, State> {
  private _mounted = false;
  private _timeout: any;

  constructor(props) {
    super(props);
    this.state = {
      hasThumbnail: false,
      thumbnail: '',
      returnQuantity: undefined,
    };
  }

  componentDidMount() {
    this._mounted = true;
    this.loadImage();
  }

  componentWillUnmount(): void {
    this._mounted = false;
  }

  loadImage = () => {
    const { purchasedItem } = this.props;
    const { productId } = purchasedItem;
    if (!Boolean(productId)) return;
    const { imagePath, lastUpdateThumbnail } = getImagePath(productId);
    if (!Boolean(imagePath)) return;
    RNFS.exists(imagePath)
      .then(exist => {
        if (exist) {
          const newThumbnail = IsAndroid ? 'file://' + imagePath + '?time=' + lastUpdateThumbnail : imagePath;
          if (this.state.thumbnail !== newThumbnail) {
            requestAnimationFrame(() => {
              this._mounted && this.setState({ thumbnail: newThumbnail, hasThumbnail: true });
            });
          }
        } else {
          requestAnimationFrame(() => {
            this._mounted && this.setState({ thumbnail: '', hasThumbnail: false });
          });
        }
      })
      .catch(err => {
        console.log('loadImage error: ' + err);
        requestAnimationFrame(() => {
          this._mounted && this.setState({ thumbnail: '', hasThumbnail: false });
        });
      });
  };

  onPressDeleteItem = () => {
    const { onPressDeleteItem, itemIndex } = this.props;
    onPressDeleteItem(itemIndex);
  };

  onChangeQuantity = value => {
    const { onChangeItemQuantity, itemIndex, purchasedItem } = this.props;
    const { priceType } = purchasedItem;

    checkQuantityNumberInput(value, newValue => {
      let returnQuantity;
      if (priceType === 'weight') {
        if (Number(newValue) > this.props.purchasedItem.originalQuantity) {
          returnQuantity = String(this.props.purchasedItem.originalQuantity);
        } else {
          returnQuantity = String(newValue);
        }
        this.setState({ returnQuantity: String(returnQuantity) });
        onChangeItemQuantity && onChangeItemQuantity(returnQuantity, itemIndex);
      } else {
        if (Number.isInteger(Number(newValue))) {
          returnQuantity = newValue;
          onChangeItemQuantity && onChangeItemQuantity(returnQuantity, itemIndex);
        }
      }
    });
  };

  onChangeItemTotal = value => {
    const { onChangeItemTotal, itemIndex } = this.props;
    checkPriceNumberInput(value, value => onChangeItemTotal && onChangeItemTotal(value, itemIndex));
  };

  onAmountInputKeyPress = ({ nativeEvent: { key } }) => {
    if (key === 'Backspace') {
      this._timeout && clearTimeout(this._timeout);
      this._timeout = setTimeout(() => {
        this.onChangeItemTotal('0');
      }, 1);
    }
  };

  render() {
    const { purchasedItem, isRefunding, isPromotion, takeawayCharge, currency, isOnlineTransaction, localCountryMap, itemIndex } = this.props;
    const currencySymbol = newConvertCurrencyToSymbol(localCountryMap, currency);
    const { itemType, productId, selectedOptions, subTotal, priceType, isTakeaway } = purchasedItem;
    const { hasThumbnail, thumbnail } = this.state;
    if ((itemType === 'ServiceCharge' && (!isRefunding || subTotal === 0)) || itemType === 'Discount') return <View />;
    let options = [];
    if (Boolean(purchasedItem.options)) {
      if (isRefunding) {
        options = purchasedItem.options;
      } else if (typeof purchasedItem.options === 'string') {
        options = purchasedItem.options ? JSONUtils.parse(purchasedItem.options, []) : [];
      } else {
        options = purchasedItem.options;
      }
    }
    const itemPromotions = PromotionHelper.serializePromotions(get(purchasedItem, ['promotions']));

    if (Boolean(selectedOptions) && !isRefunding) {
      options = get(purchasedItem, 'selectedOptions', []);
    }
    if (isPromotion) {
      purchasedItem.inputValue = purchasedItem.discount || 0;
      if (!Boolean(purchasedItem.promotionName)) {
        // TODO: 这里不应该再依赖BO设置的Promotion
        const promotion = DAL.getPromotionById(purchasedItem.promotionId);
        if (Boolean(promotion)) {
          purchasedItem.promotionName = promotion.name;
        } else {
          purchasedItem.promotionName = 'Promotion';
        }
      }
    }
    const optionsStr = generateDescriptionString(options);
    let takeawayStr;
    if ((purchasedItem.itemChannel && purchasedItem.itemChannel === ItemChannelType.TAKEAWAY) || isTakeaway) {
      takeawayStr = `${t('Takeaway')} - ${currencySymbol} ${getLocaleNumberString(takeawayCharge)}`;
    }
    const itemNotes = purchasedItem.comments || purchasedItem.notes;
    return (
      <View style={styles.purchasedItem}>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          {hasThumbnail ? (
            <Image source={{ uri: thumbnail }} style={styles.image} resizeMode='cover' />
          ) : (
            <View style={[styles.image, { backgroundColor: '#f2f2f2', alignItems: 'center', justifyContent: 'center' }]}>
              <IconImageHolder width={scaleSizeW(40)} height={scaleSizeH(40)} color={CommonColors.Icon} />
            </View>
          )}
          {isRefunding && (
            <TouchableOpacity
              {...testProps(`al_btn_remove_item_${itemIndex}`)}
              style={{ marginLeft: scaleSizeW(32), marginTop: -scaleSizeH(24) }}
              onPress={this.onPressDeleteItem}
            >
              <IconRemove color='#303030' width={scaleSizeH(48)} height={scaleSizeH(48)} />
            </TouchableOpacity>
          )}
          <View>
            <View style={styles.rowItem}>
              <Text
                style={{
                  ...StyleSheet.flatten(styles.contentFont),
                  marginLeft: scaleSizeW(10),
                  marginRight: scaleSizeW(16),
                  textAlign: 'right',
                  alignSelf: 'flex-start',
                }}
              >
                {isPromotion || Boolean(itemType) ? '' : getReceiptQtyString(purchasedItem.quantity)}
              </Text>
              <View style={{ flexDirection: 'column' }}>
                <Text style={{ ...StyleSheet.flatten(styles.contentFont), marginRight: scaleSizeW(5), maxWidth: scaleSizeW(380) }}>
                  {isPromotion ? purchasedItem.promotionName : purchasedItem.itemType ? t(purchasedItem.itemType) : purchasedItem.title}
                </Text>
              </View>
              {(isPromotion || get(itemPromotions, 'length', 0) > 0) && (
                <IconPromotion width={scaleSizeW(28)} height={scaleSizeW(30)} color={CommonColors.Icon} />
              )}
            </View>
            {Boolean(takeawayStr) && <Text style={[styles.descriptionText, { color: '#FC7118', marginLeft: scaleSizeW(81) }]}>{takeawayStr}</Text>}
            <View style={{ marginLeft: scaleSizeW(81) }}>
              {Boolean(optionsStr) && optionsStr.length > 0 && (
                <Text style={styles.descriptionText} testID={productId}>
                  {optionsStr}
                </Text>
              )}
              {!!purchasedItem.sn && <Text style={styles.descriptionText}>S/N: {purchasedItem.sn}</Text>}
              {!!itemNotes && (
                <View style={styles.notesContainer}>
                  <IconNotes />
                  <Text style={styles.notesText}>{itemNotes}</Text>
                </View>
              )}
              <ItemSalesPerson
                style={styles.salesPersonContainer}
                salesPerson={purchasedItem.employeeName}
                color='#9E9E9E'
                fontSize={currentThemes.fontSize16}
              />
            </View>
          </View>
        </View>
        {isRefunding ? (
          <View style={{ flexDirection: 'row', marginRight: scaleSizeW(20), flex: 1, justifyContent: 'flex-end' }}>
            {!purchasedItem.itemType && !isPromotion && (
              <CurrencyTextInput
                style={styles.quantityInput}
                keyboardType='decimal-pad'
                editable
                underlineColorAndroid='transparent'
                onChangeText={this.onChangeQuantity}
                value={
                  priceType === 'weight'
                    ? this.state.returnQuantity || String(purchasedItem.return.quantity.toFixed(2))
                    : String(purchasedItem.return ? purchasedItem.return.quantity : purchasedItem.quantity)
                }
              />
            )}
            <CurrencyTextInput
              editable={itemType !== 'ServiceCharge' && !isPromotion}
              style={styles.totalInput}
              value={
                isPromotion
                  ? `-${localeNumber(purchasedItem.inputValue)}`
                  : String(localeNumber(purchasedItem.return && purchasedItem.return.total ? purchasedItem.return.total : purchasedItem.total))
              }
              onChangeText={this.onChangeItemTotal}
              onKeyPress={this.onAmountInputKeyPress}
              keyboardType='decimal-pad'
              underlineColorAndroid='transparent'
            />
          </View>
        ) : (
          <View style={{ flexDirection: 'row', justifyContent: 'flex-end', marginTop: -scaleSizeH(24) }}>
            {isPromotion ? null : (
              <Text style={{ fontSize: currentThemes.fontSize24, fontWeight: '500', color: '#757575', textAlign: 'right', marginRight: scaleSizeW(60) }}>
                {localeNumber(purchasedItem.unitPrice)}
              </Text>
            )}
            <Text style={{ ...StyleSheet.flatten(styles.contentFont), width: scaleSizeW(140), textAlign: 'right', marginRight: scaleSizeW(30) }}>
              {isPromotion
                ? isOnlineTransaction
                  ? localeNumber(purchasedItem.discount)
                  : localeNumber(purchasedItem.inputValue)
                : localeNumber(purchasedItem.total)}
            </Text>
          </View>
        )}
      </View>
    );
  }
}

export default TrxDetailProductItem;

const styles = StyleSheet.create({
  contentFont: {
    fontWeight: '500',
    fontSize: currentThemes.fontSize24,
    color: '#303030',
  },
  purchasedItem: {
    minHeight: scaleSizeH(120),
    paddingVertical: scaleSizeH(30),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  descriptionText: {
    color: '#60636B',
    fontSize: currentThemes.fontSize18,
    width: scaleSizeW(600),
    marginTop: scaleSizeH(4),
  },
  notesContainer: {
    flexDirection: 'row',
    marginTop: scaleSizeH(4),
  },
  notesText: {
    color: '#9E9E9E',
    fontSize: currentThemes.fontSize16,
    width: scaleSizeW(600),
    // marginLeft: scaleSizeW(2),
    includeFontPadding: false,
  },
  quantityInput: {
    fontSize: currentThemes.fontSize24,
    textAlign: 'right',
    borderWidth: 1,
    borderColor: '#E0E0E4',
    marginRight: scaleSizeW(32),
    width: scaleSizeW(112),
    height: scaleSizeH(72),
    paddingHorizontal: scaleSizeW(16),
    paddingVertical: scaleSizeW(12),
    borderRadius: 8,
  },
  totalInput: {
    fontSize: currentThemes.fontSize24,
    textAlign: 'right',
    borderWidth: 1,
    borderColor: '#E0E0E4',
    padding: 8,
    width: scaleSizeW(160),
    marginRight: scaleSizeW(160),
    paddingHorizontal: scaleSizeW(16),
    paddingVertical: scaleSizeW(12),
    height: scaleSizeH(72),
    borderRadius: 8,
  },
  rowItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  image: {
    width: scaleSizeH(104),
    height: scaleSizeH(90),
    borderRadius: 4,
    marginLeft: scaleSizeW(24),
  },
  salesPersonContainer: {
    marginLeft: 0,
    width: scaleSizeW(600),
  },
});
