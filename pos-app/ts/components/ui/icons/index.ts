export const icons = {
  menu: require('../../../../assets/icons/menu.png'),
  backdrop: require('../../../../assets/icons/backdrop.png'),
  coffee: require('../../../../assets/icons/coffee.png'),
  'manual-return': require('../../../../assets/icons/manual-return.png'),
  add: require('../../../../assets/icons/add.png'),
  iconLeft: require('../../../../assets/icons/iconLeft.png'),
  managerbg: require('../../../../assets/icons/managerbg.png'),
  beep: require('../../../../assets/icons/Beep.png'),
  emptyTransaction: require('../../../../assets/icons/Null_search.png'),
  printerPNG: require('../../../../assets/icons/printer.png'),
  arrow: require('../../../../assets/icons/arrow.png'),
  triangleRight: require('../../../../assets/icons/triangle-right.png'),
  BeepIcon: require('../../../../assets/icons/iconBeep.png'),
  ECIcon: require('../../../../assets/icons/iconEC.png'),
  FoodPandaIcon: require('../../../../assets/icons/iconFoodpanda.png'),
  GrabIcon: require('../../../../assets/icons/iconGrab.png'),
  ShopeeFoodIcon: require('../../../../assets/icons/iconShopeefood.png'),
  noAssignedPrinterImage: require('../../../../assets/icons/noAssignedPrinter.png'),
  posRegisterImage: require('../../../../assets/icons/pos-register.png'),
  cfdLogo: require('../../../../assets/icons/storehubcfdlogo.png'),
  LazadaIcon: require('../../../../assets/icons/iconLazada.png'),
  ShopifyIcon: require('../../../../assets/icons/iconShopify.png'),
  ShopeeEcommerceIcon: require('../../../../assets/icons/iconShopeeEcommerce.png'),
  WooCommerceIcon: require('../../../../assets/icons/iconWooCommerce.png'),
  TikTokShopIcon: require('../../../../assets/icons/iconTikTokShop.png'),
  ZaloraIcon: require('../../../../assets/icons/iconZalora.png'),
  wave: require('../../../../assets/icons/wave.png'),
  samsungPay: require('../../../../assets/icons/samsung-pay.png'),
  scanMe: require('../../../../assets/icons/scanMe.png'),
  scanMeIOS: require('../../../../assets/icons/scanMeIOS.png'),
  beepRound: require('../../../../assets/icons/beepRound.png'),
  tng: require('../../../../assets/icons/tng.png'),
  tngScanner: require('../../../../assets/icons/tngScanner.png'),
  beepScanner: require('../../../../assets/icons/beepScanner.png'),
  tap: require('../../../../assets/icons/tap.png'),
  person: require('../../../../assets/icons/person.png'),
  qrDuitPay: require('../../../../assets/icons/iconQrDuit.png'),
  qrTngPay: require('../../../../assets/icons/iconQrTng.png'),
  qrGrabPay: require('../../../../assets/icons/iconQrGrabPay.png'),
  qrBoost: require('../../../../assets/icons/iconQrBoost.png'),
  qrAlipay: require('../../../../assets/icons/iconQrAlipay.png'),
  qrWechatPay: require('../../../../assets/icons/iconQrWechatPay.png'),
  qrMayBankQr: require('../../../../assets/icons/iconQrMayBankQr.png'),
  qrPh: require('../../../../assets/icons/Xendit_QRPH.png'),
  MagentoIcon: require('../../../../assets/icons/iconMagento.png'),
  xenditQRPHIcon: require('../../../../assets/icons/Xendit_QRPH.png'),
  intersect: require('../../../../assets/icons/intersect.png'),

  lcdNfcInitial: require('../../../../assets/lcd/lcd_nfc_initial.png'),
  lcdNfcLoading: require('../../../../assets/lcd/lcd_nfc_loading.png'),
  lcdNfcPaymentFailed: require('../../../../assets/lcd/lcd_nfc_payment_failed.png'),
  lcdNfcPaymentSuccess: require('../../../../assets/lcd/lcd_nfc_payment_success.png'),
  lcdNfcPaymentPin: require('../../../../assets/lcd/lcd_nfc_pin.png'),
  lcdNfcPaymentSignature: require('../../../../assets/lcd/lcd_nfc_signature.png'),
  lcdNfcPaymentTapCard: require('../../../../assets/lcd/lcd_nfc_tap_card.png'),

  camera: require('../../../../assets/icons/camera.png'),
  tngNonMy: require('../../../../assets/icons/tngNonMy.png'),
};
