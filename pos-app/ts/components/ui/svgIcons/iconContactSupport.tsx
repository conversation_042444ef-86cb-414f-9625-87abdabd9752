import * as React from 'react';
import Svg, { <PERSON>, <PERSON>, Defs, ClipPath } from 'react-native-svg';
const SvgComponent = props => (
  <Svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' width={12} height={12} fill='none' {...props}>
    <G fill='#000' fillRule='evenodd' clipPath='url(#a)' clipRule='evenodd'>
      <Path d='M8.544 1.926a6.374 6.374 0 0 1 6.425 6.375v3.5a1.375 1.375 0 0 1-1.375 1.375h-1a1.375 1.375 0 0 1-1.375-1.375v-2.5a1.375 1.375 0 0 1 1.375-1.375h1.612a5.625 5.625 0 0 0-5.665-5.25 5.625 5.625 0 0 0-5.654 5.25H4.5a1.375 1.375 0 0 1 1.375 1.375v2.5A1.375 1.375 0 0 1 4.5 13.176h-1a1.375 1.375 0 0 1-1.375-1.375v-3.5a6.375 6.375 0 0 1 6.419-6.375Zm-5.669 6.75v3.125a.625.625 0 0 0 .625.625h1a.625.625 0 0 0 .625-.625v-2.5a.625.625 0 0 0-.625-.625H2.875Zm11.344 0h-1.625a.625.625 0 0 0-.625.625v2.5a.625.625 0 0 0 .625.625h1a.625.625 0 0 0 .625-.625V8.676Z' />
      <Path d='M14.594 11.426c.207 0 .375.168.375.375v1.5a2.375 2.375 0 0 1-2.375 2.375H9a.375.375 0 0 1 0-.75h3.594a1.625 1.625 0 0 0 1.625-1.625v-1.5c0-.207.168-.375.375-.375Z' />
    </G>
    <Defs>
      <ClipPath id='a'>
        <Path fill='#fff' d='M.5.3h16v16H.5z' />
      </ClipPath>
    </Defs>
  </Svg>
);
export default SvgComponent;
