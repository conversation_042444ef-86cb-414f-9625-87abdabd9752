import React from 'react';
import { SvgProps, SvgXml } from 'react-native-svg';

const iconHideCustomerInfoEye = ({ color = '#000000', ...props }: SvgProps & { color?: string }) => {
  const xml = `
    <svg width="36" height="23" viewBox="0 0 36 23" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g id="Icon">
        <g id="Group">
          <path id="Path" d="M0.102087 11.5153C1.4676 13.967 3.31416 16.1666 5.52924 17.8929C7.77924 19.6502 10.3629 20.9614 13.1404 21.6519C16.0073 22.3657 19.0137 22.4317 21.9116 21.8614C24.662 21.3222 27.3116 20.17 29.5965 18.5446C31.9241 16.8881 33.9064 14.7778 35.4077 12.3455C35.4969 12.2019 35.5861 12.0545 35.6715 11.9071C35.7956 11.6937 35.9547 11.4687 35.9896 11.2205C36.0478 10.786 35.656 10.3011 35.431 9.94029C35.0586 9.34676 34.6551 8.76874 34.2284 8.214C33.3555 7.08124 32.3896 6.03383 31.3111 5.09503C29.1426 3.20581 26.6094 1.75107 23.8629 0.885983C21.0348 -0.00237948 18.04 -0.207983 15.1073 0.203224C12.3142 0.595034 9.6064 1.61917 7.23226 3.1321C4.82321 4.66831 2.75941 6.69331 1.14174 9.04805C0.769328 9.59116 0.42019 10.1498 0.102087 10.7239C-0.0957578 11.0808 0.00510424 11.5851 0.381397 11.7868C0.746053 11.9808 1.23485 11.8877 1.44433 11.5075C1.76243 10.9334 2.11157 10.3709 2.48398 9.83167C2.67795 9.55236 2.87579 9.27693 3.0814 9.00538C3.12407 8.94719 3.1551 8.96659 3.062 9.02866C3.08528 9.01314 3.11243 8.96271 3.13183 8.93943C3.18226 8.87348 3.23657 8.80753 3.29088 8.74159C3.40338 8.60193 3.51588 8.46616 3.63226 8.3265C4.49735 7.30624 5.46329 6.36745 6.51071 5.53728C6.54174 5.51012 6.76286 5.33167 6.59605 5.46745C6.65424 5.4209 6.71631 5.37822 6.7745 5.33167C6.91028 5.23081 7.04605 5.12995 7.18183 5.03297C7.45726 4.839 7.73269 4.64891 8.01588 4.47047C8.58226 4.10969 9.16804 3.77607 9.76933 3.47348C10.0719 3.32219 10.3745 3.17865 10.6848 3.04288C10.7547 3.01184 10.8206 2.98081 10.8904 2.95366C10.6887 3.03512 10.9525 2.93038 10.9913 2.91486C11.1581 2.84891 11.3249 2.78297 11.4917 2.7209C12.1279 2.48426 12.7797 2.27866 13.4392 2.10797C14.0986 1.94116 14.762 1.80538 15.437 1.7084C15.4758 1.70452 15.5146 1.69676 15.5534 1.69288C15.6736 1.67348 15.3555 1.71616 15.5495 1.69288C15.6271 1.68512 15.7047 1.67348 15.7784 1.66572C15.9607 1.64633 16.1469 1.62693 16.3292 1.61141C16.6706 1.58038 17.012 1.56098 17.3534 1.54934C18.0594 1.52607 18.7693 1.54159 19.4754 1.5959C19.6499 1.61141 19.8245 1.62693 19.9991 1.64633C20.0767 1.65409 20.1542 1.66184 20.2318 1.67348C20.2706 1.67736 20.3094 1.68124 20.3482 1.689C20.4064 1.69676 20.5499 1.74719 20.3715 1.69288C20.5228 1.73943 20.7012 1.73943 20.8603 1.76659C21.0426 1.79762 21.2211 1.82866 21.3995 1.86357C22.0745 1.99547 22.7417 2.16616 23.4012 2.36788C23.7193 2.46486 24.0336 2.57348 24.3439 2.68986C24.4952 2.74417 24.6465 2.80236 24.7939 2.86443C24.8715 2.89547 24.9529 2.9265 25.0305 2.96141C25.0538 2.97305 25.1702 3.0196 25.0693 2.97693C24.9607 2.93038 25.1392 3.00797 25.1586 3.01572C26.3961 3.55107 27.5792 4.21831 28.681 4.99417C28.8206 5.09116 28.9564 5.19202 29.0922 5.29288C29.1542 5.33943 29.2124 5.3821 29.2745 5.42866C29.3055 5.45193 29.3366 5.47133 29.3637 5.49848C29.2124 5.37047 29.3598 5.4946 29.387 5.514C29.6547 5.72348 29.9146 5.94072 30.1745 6.16184C30.6866 6.60797 31.1792 7.07736 31.6486 7.57003C32.1103 8.05883 32.5448 8.5709 32.9637 9.0946C32.8512 8.94719 33.0374 9.18771 32.9637 9.0946C32.987 9.12564 33.0103 9.15667 33.0336 9.18383C33.0801 9.2459 33.1228 9.30409 33.1693 9.36615C33.2779 9.50969 33.3827 9.6571 33.4836 9.80452C33.6736 10.0761 33.8598 10.3515 34.0344 10.6347C34.2167 10.9256 34.3913 11.2166 34.562 11.5153C34.562 11.2554 34.562 10.9916 34.562 10.7317C34.2284 11.3291 33.8715 11.9071 33.4797 12.4696C33.3827 12.6093 33.2857 12.7489 33.1848 12.8847C33.1422 12.9468 33.0956 13.0049 33.0491 13.067C33.018 13.1097 32.8784 13.2455 32.8784 13.292C32.8784 13.2493 32.9637 13.1873 32.8978 13.2687C32.8667 13.3075 32.8396 13.3424 32.8124 13.3812C32.7581 13.4472 32.7077 13.5131 32.6534 13.5791C32.5486 13.711 32.44 13.839 32.3275 13.967C31.4547 14.9912 30.4771 15.9299 29.4219 16.7601C29.3948 16.7834 29.356 16.8028 29.3327 16.8299C29.4374 16.7174 29.3986 16.7795 29.356 16.8106C29.29 16.861 29.2241 16.9114 29.1542 16.9618C29.0107 17.0666 28.8672 17.1713 28.7236 17.2722C28.4482 17.4662 28.1689 17.6524 27.8857 17.8308C27.3232 18.1838 26.7452 18.5097 26.1517 18.8045C25.8491 18.9558 25.5426 19.0955 25.2361 19.2312C25.1935 19.2506 25.1508 19.27 25.1081 19.2856C25.1275 19.2778 25.2672 19.2196 25.1392 19.2739C25.0693 19.3011 24.9995 19.3321 24.9297 19.3593C24.7629 19.4252 24.5961 19.4912 24.4292 19.5493C23.7892 19.786 23.1413 19.9838 22.4818 20.1506C21.8223 20.3174 21.1551 20.4416 20.484 20.5424C20.2939 20.5696 20.612 20.5269 20.4879 20.5424C20.4413 20.5502 20.3909 20.5541 20.3443 20.5618C20.2667 20.5696 20.1892 20.5812 20.1154 20.589C19.9409 20.6084 19.7663 20.6239 19.5956 20.6394C19.2465 20.6666 18.8935 20.686 18.5404 20.6976C17.8344 20.717 17.1245 20.6976 16.4223 20.6394C16.2478 20.6239 16.0732 20.6084 15.9025 20.589C15.8249 20.5812 15.7473 20.5735 15.6736 20.5618C15.6348 20.558 15.5961 20.5502 15.5573 20.5463C15.3788 20.5231 15.6969 20.5657 15.534 20.5424C15.1926 20.492 14.8512 20.4377 14.5137 20.3718C13.8426 20.2399 13.1792 20.0731 12.5275 19.8713C12.2017 19.7705 11.8836 19.6618 11.5654 19.5455C11.4142 19.4912 11.2667 19.433 11.1193 19.3748C11.0417 19.3437 10.9642 19.3127 10.8827 19.2778C10.8439 19.2623 10.7547 19.2118 10.9137 19.2894C10.8633 19.2662 10.809 19.2468 10.7586 19.2235C9.52493 18.6881 8.3495 18.0248 7.25166 17.2489C7.1314 17.1636 7.00726 17.0743 6.89088 16.9851C6.82493 16.9347 6.7551 16.8843 6.68916 16.8338C6.65424 16.8067 6.54174 16.7291 6.68916 16.8377C6.65036 16.8106 6.61545 16.7795 6.58054 16.7524C6.31674 16.5429 6.05683 16.3295 5.80467 16.1084C5.29648 15.6662 4.81157 15.1968 4.34993 14.708C4.11717 14.4636 3.89217 14.2114 3.67493 13.9554C3.57019 13.8351 3.46933 13.711 3.36847 13.5868C3.32191 13.5287 3.27536 13.4705 3.22881 13.4123C3.19778 13.3774 3.17062 13.3386 3.13959 13.2998C3.12407 13.2765 3.10467 13.2571 3.08916 13.2338C3.14735 13.3075 3.15898 13.3231 3.12407 13.2804C2.72062 12.745 2.33657 12.1981 1.97967 11.6278C1.79735 11.3368 1.62666 11.042 1.45985 10.7433C1.262 10.3903 0.746053 10.2429 0.396915 10.464C0.0206215 10.6851 -0.111275 11.1351 0.102087 11.5153Z" fill="#393939"/>
          <path id="Path_2" d="M22.7337 11.1235C22.7337 11.2593 22.7299 11.3951 22.7182 11.5308C22.7143 11.6007 22.7066 11.6744 22.6988 11.7442C22.6949 11.7752 22.6717 11.8877 22.6949 11.7752C22.726 11.6472 22.6872 11.814 22.6833 11.8295C22.6406 12.0972 22.5747 12.361 22.4932 12.617C22.4544 12.7412 22.4079 12.8614 22.3613 12.9817C22.307 13.1175 22.3807 12.9429 22.3807 12.939C22.3846 12.9468 22.338 13.0321 22.338 13.0321C22.3109 13.0942 22.2799 13.1563 22.2488 13.2183C22.1247 13.4627 21.985 13.6955 21.8299 13.9166C21.7988 13.9632 21.7561 14.0485 21.7096 14.0795C21.7057 14.0834 21.8105 13.9554 21.7523 14.0214C21.729 14.0485 21.7096 14.0757 21.6863 14.0989C21.601 14.2037 21.5079 14.3045 21.4148 14.4015C21.3255 14.4946 21.2363 14.58 21.1432 14.6653C21.0928 14.7119 21.0424 14.7545 20.9919 14.7972C20.9648 14.8205 20.9376 14.8399 20.9143 14.8632C20.8057 14.9563 21.0113 14.7972 20.9299 14.8515C20.7126 15.0028 20.4993 15.1541 20.2704 15.2821C20.1579 15.3442 20.0454 15.4024 19.929 15.4567C19.898 15.4722 19.8669 15.4839 19.8359 15.4994C19.7428 15.5459 19.9794 15.4451 19.8786 15.48C19.8049 15.5071 19.735 15.5382 19.6613 15.5653C19.4092 15.6584 19.1493 15.7282 18.8816 15.7826C18.8195 15.7942 18.7613 15.8058 18.6993 15.8175C18.6954 15.8175 18.6061 15.8407 18.5945 15.833C18.6255 15.8524 18.7536 15.8136 18.6411 15.8252C18.4975 15.8407 18.354 15.8524 18.2105 15.8601C17.9195 15.8718 17.6324 15.8563 17.3454 15.8252C17.2018 15.8097 17.4967 15.8485 17.3143 15.8214C17.2445 15.8097 17.1747 15.7981 17.1049 15.7826C16.9691 15.7554 16.8333 15.7205 16.6975 15.6856C16.5734 15.6507 16.4493 15.6119 16.3251 15.5653C16.2592 15.542 16.1971 15.5149 16.1311 15.4916C16.0303 15.4528 16.1389 15.5188 16.1738 15.511C16.1505 15.5188 16.0768 15.4683 16.0574 15.4567C15.8169 15.3442 15.588 15.2162 15.3669 15.0726C15.2622 15.0028 15.1574 14.9291 15.0566 14.8515C14.9712 14.7895 15.1691 14.9485 15.0954 14.8787C15.0682 14.8554 15.0411 14.836 15.0178 14.8127C14.9596 14.7623 14.9014 14.7157 14.8471 14.6614C14.6531 14.4869 14.4708 14.2968 14.304 14.0951C14.2807 14.0679 14.2613 14.0407 14.238 14.0175C14.1799 13.9515 14.2846 14.0757 14.2807 14.0757C14.2342 14.0446 14.1915 13.9554 14.1605 13.9127C14.0829 13.8002 14.0092 13.6877 13.9393 13.5714C13.8695 13.455 13.8036 13.3347 13.7415 13.2145C13.7143 13.1601 13.6872 13.1058 13.6639 13.0515C13.6484 13.0205 13.6368 12.9895 13.6212 12.9584C13.5747 12.8692 13.6911 13.1407 13.629 12.9778C13.532 12.7257 13.4467 12.4735 13.3846 12.2136C13.3536 12.0856 13.3264 11.9576 13.307 11.8257C13.307 11.8257 13.2837 11.7287 13.2915 11.7209C13.2876 11.7248 13.3148 11.9188 13.2993 11.7675C13.2915 11.6976 13.2837 11.6239 13.2799 11.5541C13.2566 11.267 13.2566 10.9761 13.2799 10.689C13.2837 10.6192 13.2915 10.5455 13.2993 10.4757C13.3148 10.3321 13.2721 10.6425 13.3031 10.4446C13.3264 10.2972 13.3574 10.1498 13.3924 10.0063C13.4583 9.74247 13.5475 9.4942 13.6406 9.24204C13.6717 9.16057 13.6135 9.31187 13.6135 9.30799C13.6096 9.30023 13.6484 9.2304 13.6561 9.21488C13.6833 9.15282 13.7143 9.09075 13.7454 9.02868C13.8074 8.90842 13.8734 8.78816 13.9432 8.67178C14.0092 8.56316 14.079 8.45454 14.1527 8.35368C14.1915 8.29937 14.2342 8.24506 14.273 8.19075C14.3234 8.12092 14.2303 8.24894 14.2303 8.24894C14.2303 8.23342 14.3156 8.1442 14.3273 8.13256C14.4941 7.93083 14.6803 7.74463 14.8743 7.57006C14.9169 7.53126 14.9635 7.49247 15.01 7.45368C15.0372 7.4304 15.0643 7.41101 15.0876 7.38773C15.1924 7.29851 15.0566 7.40325 15.0488 7.41489C15.1109 7.32178 15.2855 7.24032 15.3824 7.17825C15.6113 7.03083 15.848 6.89506 16.0962 6.78256C16.1118 6.7748 16.1816 6.73601 16.1893 6.73988C16.1855 6.73988 16.0109 6.80971 16.1467 6.75928C16.201 6.73601 16.2592 6.71661 16.3135 6.69333C16.4454 6.64678 16.5734 6.60411 16.7092 6.56532C16.8333 6.5304 16.9613 6.49937 17.0893 6.47221C17.1592 6.4567 17.229 6.44506 17.2988 6.43342C17.3337 6.42954 17.3725 6.42566 17.4036 6.4179C17.326 6.44118 17.2639 6.43342 17.3842 6.42178C17.6635 6.39075 17.9428 6.37911 18.2221 6.39075C18.3579 6.39463 18.4898 6.40626 18.6255 6.42178C18.8273 6.44506 18.4859 6.39851 18.6837 6.42954C18.7613 6.44118 18.8389 6.4567 18.9165 6.47221C19.1803 6.52652 19.4402 6.60023 19.6924 6.69333C19.7505 6.71273 19.8049 6.73601 19.8592 6.75928C19.9949 6.81359 19.8204 6.73988 19.8165 6.73988C19.8243 6.73601 19.8941 6.7748 19.9096 6.78256C20.0337 6.84075 20.154 6.90282 20.2743 6.96876C20.3945 7.03471 20.5109 7.10454 20.6234 7.17825C20.7165 7.24032 20.8911 7.3179 20.957 7.41489C20.9493 7.40325 20.8135 7.29851 20.9182 7.38773C20.9376 7.40325 20.957 7.41876 20.9764 7.43428C21.0268 7.47695 21.0811 7.52351 21.1316 7.56618C21.2324 7.6554 21.3294 7.74851 21.4225 7.84937C21.5118 7.94247 21.5971 8.03557 21.6786 8.13256C21.7018 8.15971 21.7212 8.18687 21.7445 8.21014C21.7911 8.26445 21.8143 8.24506 21.7174 8.17135C21.7639 8.20626 21.8027 8.28773 21.8376 8.33428C21.9967 8.5554 22.1363 8.78816 22.2566 9.03256C22.2876 9.09463 22.3187 9.1567 22.3458 9.21876C22.3574 9.24204 22.3652 9.26532 22.3768 9.28859C22.4273 9.3817 22.3225 9.14894 22.3574 9.24592C22.4079 9.3817 22.4622 9.51747 22.5087 9.65713C22.5902 9.91704 22.6523 10.1808 22.6949 10.4485C22.726 10.6308 22.6833 10.336 22.6988 10.4795C22.7066 10.5494 22.7143 10.6231 22.7182 10.6929C22.7299 10.8326 22.7337 10.98 22.7337 11.1235C22.7376 11.5308 23.0906 11.9188 23.5096 11.8994C23.9286 11.88 24.2893 11.558 24.2855 11.1235C24.2777 9.84333 23.8937 8.55152 23.1411 7.50799C22.3768 6.44894 21.3488 5.65756 20.123 5.20368C17.71 4.30756 14.8277 5.08342 13.1906 7.06963C12.3333 8.10928 11.829 9.34678 11.7243 10.689C11.6234 11.9576 11.9454 13.2688 12.601 14.355C13.2411 15.4218 14.2148 16.3179 15.3553 16.8261C16.5928 17.3808 17.9389 17.5438 19.2734 17.28C21.7523 16.7951 23.8083 14.6847 24.1962 12.1864C24.2505 11.8334 24.2893 11.4804 24.2893 11.1235C24.2932 10.7162 23.9324 10.3282 23.5135 10.3476C23.0906 10.367 22.7376 10.689 22.7337 11.1235Z" fill="#393939"/>
        </g>
      </g>
    </svg>
  `;
  return <SvgXml xml={xml} {...props} />;
};

export default iconHideCustomerInfoEye;
