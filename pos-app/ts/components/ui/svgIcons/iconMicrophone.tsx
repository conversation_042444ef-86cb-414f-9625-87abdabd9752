import React from 'react';
import Svg, { Path } from 'react-native-svg';

interface Props {
  width?: number;
  height?: number;
  color?: string;
}

const IconMicrophone: React.FC<Props> = ({ width = 24, height = 24, color = '#000' }) => {
  return (
    <Svg width={width} height={height} viewBox='0 0 24 24' fill='none'>
      <Path d='M12 14C13.66 14 15 12.66 15 11V5C15 3.34 13.66 2 12 2C10.34 2 9 3.34 9 5V11C9 12.66 10.34 14 12 14Z' fill={color} />
      <Path
        d='M17 11C17 14.53 14.39 17.44 11 17.93V21H13C13.55 21 14 21.45 14 22C14 22.55 13.55 23 13 23H11C10.45 23 10 22.55 10 22C10 21.45 10.45 21 11 21H13V17.93C9.61 17.44 7 14.53 7 11'
        stroke={color}
        strokeWidth='2'
        fill='none'
      />
    </Svg>
  );
};

export default IconMicrophone;
