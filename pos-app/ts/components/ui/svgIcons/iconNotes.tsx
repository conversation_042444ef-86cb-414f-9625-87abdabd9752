import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

interface Props {
  color?: string;
  width?: number;
  height?: number;
  testID?: string;
}
const SVGComponent = (props: Props) => (
  <Svg testID={props.testID} width={props.width || 20} height={props.height || 20} fill='none' viewBox='0 0 20 20' {...props}>
    <Path
      d='m15.256 15.482 -1.718 0.893 -0.244 0.126h0.318l-1.731 -0.9q-0.113 -0.061 -0.229 -0.119c-0.179 -0.088 -0.308 -0.006 -0.46 0.073q-0.502 0.262 -1.007 0.522l-0.813 0.424h0.318l-1.732 -0.9q-0.113 -0.061 -0.228 -0.119c-0.18 -0.088 -0.309 -0.006 -0.46 0.073q-0.502 0.262 -1.007 0.522l-0.814 0.424h0.318l-1.717 -0.893 -0.244 -0.126 0.155 0.272V3.338c0 -0.168 0.005 -0.337 0 -0.505V2.81l-0.473 0.272 1.731 0.9q0.113 0.06 0.228 0.118c0.18 0.089 0.309 0.007 0.46 -0.072q0.502 -0.263 1.007 -0.523 0.407 -0.21 0.814 -0.423H7.41l1.732 0.9q0.112 0.06 0.228 0.118c0.18 0.089 0.309 0.007 0.46 -0.072q0.502 -0.263 1.007 -0.523l0.814 -0.423h-0.318l1.731 0.9q0.114 0.06 0.229 0.118c0.18 0.089 0.308 0.007 0.46 -0.072q0.502 -0.263 1.007 -0.523 0.406 -0.21 0.813 -0.423L15.1 2.81v12.416c0 0.168 -0.003 0.337 0 0.505v0.022c0 0.166 0.145 0.323 0.315 0.315a0.32 0.32 0 0 0 0.314 -0.315V3.338c0 -0.168 0.004 -0.337 0 -0.505V2.81c0 -0.248 -0.264 -0.381 -0.473 -0.273l-1.718 0.893 -0.244 0.126h0.318l-1.731 -0.9q-0.113 -0.06 -0.229 -0.119c-0.179 -0.088 -0.308 -0.006 -0.46 0.073q-0.502 0.262 -1.007 0.522l-0.813 0.424h0.318l-1.732 -0.9 -0.228 -0.119c-0.18 -0.088 -0.309 -0.006 -0.46 0.073q-0.502 0.262 -1.007 0.522l-0.814 0.424h0.318l-1.714 -0.893 -0.244 -0.126c-0.21 -0.108 -0.474 0.024 -0.474 0.273v12.416c0 0.168 -0.003 0.337 0 0.505v0.022c0 0.107 0.058 0.222 0.156 0.273l1.731 0.9q0.113 0.06 0.228 0.118c0.18 0.088 0.309 0.006 0.46 -0.072q0.502 -0.263 1.008 -0.523l0.813 -0.423h-0.318l1.732 0.9q0.113 0.06 0.228 0.118c0.18 0.088 0.309 0.006 0.46 -0.072q0.502 -0.263 1.007 -0.523l0.814 -0.423h-0.318l1.732 0.9q0.112 0.06 0.228 0.118c0.18 0.088 0.308 0.006 0.46 -0.072q0.502 -0.263 1.007 -0.523l0.814 -0.423c0.146 -0.076 0.201 -0.293 0.113 -0.432a0.327 0.327 0 0 0 -0.434 -0.113'
      fill={props.color || '#757575'}
    />
    <Path
      d='M5.66 7.084h6.79c0.313 0 0.627 0.008 0.94 0h0.014c0.165 0 0.323 -0.144 0.315 -0.314a0.32 0.32 0 0 0 -0.315 -0.315H6.613c-0.313 0 -0.626 -0.008 -0.94 0H5.66c-0.165 0 -0.322 0.145 -0.314 0.315a0.32 0.32 0 0 0 0.314 0.314m7.744 2.393H6.613c-0.313 0 -0.626 -0.008 -0.94 0H5.66c-0.165 0 -0.322 0.145 -0.314 0.315a0.32 0.32 0 0 0 0.314 0.315h6.791c0.313 0 0.627 0.007 0.94 0h0.014c0.165 0 0.323 -0.145 0.315 -0.315a0.32 0.32 0 0 0 -0.315 -0.315M10.466 12.5H5.658c-0.165 0 -0.322 0.144 -0.314 0.314a0.32 0.32 0 0 0 0.314 0.315h4.808c0.165 0 0.322 -0.145 0.315 -0.315a0.32 0.32 0 0 0 -0.315 -0.315'
      fill={props.color || '#757575'}
    />
  </Svg>
);
export default SVGComponent;
