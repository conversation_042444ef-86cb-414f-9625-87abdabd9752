import * as React from 'react';
import Svg, { <PERSON>, <PERSON>, De<PERSON>, ClipPath } from 'react-native-svg';
const SvgComponent = props => (
  <Svg xmlns='http://www.w3.org/2000/svg' viewBox='6 6 40 40' fill='none' {...props}>
    <G clipPath='url(#a)'>
      <Path
        fill='#000'
        d='M36.273 27.91c-.434.24-.929.492-1.486.743-2.432 1.094-6.03 2.165-10.787 2.165-4.756 0-8.355-1.071-10.787-2.165a20.173 20.173 0 0 1-1.486-.743v5.08c.***************.137.145.292.294.819.736 1.693 1.197 1.747.922 4.926 1.94 10.443 1.94 5.517 0 8.696-1.018 10.443-1.94.874-.46 1.401-.903 1.693-1.197a3.72 3.72 0 0 0 .137-.145v-5.08Zm1.363 5.53.693.376c.506.275.506.276.505.276l-.001.003-.003.006-.007.012a3.904 3.904 0 0 1-.062.103 3.667 3.667 0 0 1-.146.212 5.64 5.64 0 0 1-.544.629c-.49.493-1.24 1.1-2.355 1.688C33.486 37.92 29.846 39 24 39c-5.846 0-9.486-1.08-11.716-2.255-1.115-.588-1.866-1.195-2.355-1.688a5.64 5.64 0 0 1-.544-.629 3.686 3.686 0 0 1-.208-.315L9.17 34.1l-.003-.006-.001-.002c0-.002-.001-.003 1.198-.652l-1.2.649L9 33.786V14.214l.165-.304 1.199.65a64.178 64.178 0 0 1-1.198-.652l.001-.003.003-.006.007-.012a2.944 2.944 0 0 1 .208-.316c.123-.167.3-.382.544-.628.49-.493 1.24-1.1 2.355-1.688C14.514 10.08 18.154 9 24 9c5.846 0 9.486 1.08 11.716 2.255 1.115.588 1.866 1.195 2.355 1.688.244.246.421.461.544.629a3.667 3.667 0 0 1 .191.287l.017.028.007.012.003.006.001.003s0 .003-1.198.651l1.2-.65.164.305v19.572l-.165.304-1.199-.65ZM36.273 19.73c-.434.24-.929.491-1.486.742-2.432 1.094-6.03 2.165-10.787 2.165-4.756 0-8.355-1.071-10.787-2.165a20.132 20.132 0 0 1-1.486-.742v4.969c.075.053.159.112.252.175.51.344 1.294.816 2.353 1.293 2.114.95 5.333 1.925 9.668 1.925 4.334 0 7.554-.974 9.668-1.925a15.858 15.858 0 0 0 2.353-1.293c.093-.063.177-.122.252-.175v-4.97ZM24 11.726c-5.517 0-8.696 1.019-10.443 1.94-.874.462-1.401.904-1.693 1.198a3.653 3.653 0 0 0-.137.145v1.506c.075.053.159.112.252.175.51.344 1.294.816 2.353 1.293 2.114.951 5.333 1.925 9.668 1.925 4.334 0 7.554-.974 9.668-1.925a15.85 15.85 0 0 0 2.353-1.293c.093-.063.177-.122.252-.175V15.01a3.72 3.72 0 0 0-.137-.145c-.292-.294-.819-.736-1.693-1.197-1.747-.922-4.926-1.94-10.443-1.94Z'
      />
      <Path fill='#fff' d='M30 29h5v14h-5z' />
      <Path
        fill='#FF9419'
        d='M45.556 41.69 35.063 23.468a2.972 2.972 0 0 0-5.124 0L19.446 41.69a2.821 2.821 0 0 0 0 2.846A2.922 2.922 0 0 0 22.008 46h20.987a2.92 2.92 0 0 0 2.56-1.463 2.822 2.822 0 0 0 .002-2.846ZM31.541 31.6a.96.96 0 1 1 1.92 0v4.8a.96.96 0 1 1-1.92 0v-4.8Zm.96 10.56a1.44 1.44 0 1 1 0-2.88 1.44 1.44 0 0 1 0 2.88Z'
      />
    </G>
    <Defs>
      <ClipPath id='a'>
        <Path fill='#fff' d='M0 0h48v48H0z' />
      </ClipPath>
    </Defs>
  </Svg>
);
export default SvgComponent;
