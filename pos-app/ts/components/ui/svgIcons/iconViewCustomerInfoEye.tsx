import React from 'react';
import { SvgProps, SvgXml } from 'react-native-svg';

const IconViewCustomerInfoEye = ({ color = '#000000', ...props }: SvgProps & { color?: string }) => {
  const xml = `
    <svg width="33" height="30" viewBox="0 0 33 30" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g id="Icon">
        <g id="Group">
          <path id="Path" d="M9.16409 21.0299C7.902 20.4546 6.70301 19.7456 5.60053 18.9066C5.57084 18.8844 5.53743 18.8584 5.50773 18.8361C5.63394 18.9326 5.52629 18.851 5.49289 18.825C5.42978 18.7767 5.37039 18.7285 5.30728 18.6765C5.16251 18.5577 5.01775 18.4389 4.87669 18.3164C4.62427 18.0974 4.37556 17.8747 4.13057 17.6446C3.63316 17.1694 3.16173 16.6683 2.71628 16.1412C2.59007 15.989 2.46386 15.8368 2.34508 15.6846C2.31909 15.6549 2.29682 15.6215 2.27084 15.5918C2.3822 15.7329 2.25599 15.5732 2.24114 15.5547C2.18175 15.4767 2.12236 15.3988 2.06296 15.3171C1.82911 14.9979 1.60638 14.6712 1.3948 14.3408C1.3948 14.5896 1.3948 14.842 1.3948 15.0907C1.56926 14.816 1.75486 14.5487 1.94418 14.2815C2.02956 14.1627 2.11493 14.0476 2.20031 13.9325C2.22258 13.9028 2.24485 13.8768 2.26713 13.8471C2.34879 13.7395 2.26341 13.8509 2.24857 13.8694C2.30425 13.7989 2.35993 13.7284 2.41561 13.6578C2.80908 13.1641 3.22854 12.6927 3.67028 12.2398C4.11201 11.7907 4.57601 11.3601 5.05858 10.9555C5.17736 10.859 5.29615 10.7587 5.41493 10.6659C5.45205 10.6362 5.64879 10.4766 5.50031 10.5991C5.57084 10.5434 5.64508 10.4878 5.71561 10.4358C5.9606 10.2539 6.21302 10.0757 6.46915 9.90496C6.99626 9.55232 7.54193 9.22566 8.09873 8.92498C8.38085 8.7765 8.66668 8.63173 8.9525 8.4981C9.09356 8.43128 9.23462 8.36818 9.37568 8.30507C9.44249 8.27538 9.50931 8.24568 9.57613 8.2197C9.42022 8.2828 9.51673 8.24568 9.55014 8.23083C9.60211 8.20856 9.65037 8.19 9.70233 8.16773C10.905 7.68145 12.1597 7.3251 13.4366 7.09866C13.6222 7.06525 13.8078 7.03556 13.9934 7.00957C14.0083 7.00586 14.1011 6.99844 14.1048 6.99472C14.012 7.00586 13.9934 7.00957 14.0491 7.00215C14.0788 6.99844 14.1048 6.99472 14.1345 6.99101C14.2273 6.97988 14.3238 6.96874 14.4166 6.9576C14.7767 6.91677 15.1405 6.88708 15.5005 6.86851C16.3654 6.82397 17.2341 6.83882 18.099 6.91306C18.4887 6.94647 18.8599 6.54928 18.8414 6.17065C18.8228 5.74006 18.5147 5.46165 18.099 5.42824C15.2704 5.19067 12.3713 5.57673 9.71347 6.57526C7.18557 7.52555 4.87298 8.98438 2.93529 10.8664C1.86251 11.9095 0.912233 13.0862 0.103009 14.3446C-0.0343363 14.5599 -0.0343363 14.8791 0.103009 15.0944C1.68062 17.5518 3.79277 19.6453 6.27612 21.1821C6.95914 21.6053 7.66814 21.9839 8.39941 22.318C8.75205 22.4776 9.21606 22.4294 9.41651 22.0507C9.60582 21.7204 9.54272 21.2007 9.16409 21.0299Z" fill="#393939"/>
          <path id="Path_2" d="M23.6853 8.33104C24.9771 8.90641 26.2021 9.62283 27.3342 10.4729C27.3602 10.4952 27.5866 10.6659 27.4419 10.5546C27.505 10.6028 27.5644 10.6511 27.6275 10.6993C27.7648 10.8107 27.9022 10.922 28.0358 11.0371C28.3068 11.2673 28.5703 11.5011 28.8265 11.7461C29.3313 12.225 29.8102 12.7335 30.2593 13.2681C30.3855 13.4165 30.5117 13.5724 30.6305 13.7246C30.6602 13.7618 30.6899 13.8026 30.7233 13.8397C30.6602 13.7618 30.6862 13.7914 30.7344 13.8546C30.7938 13.9325 30.8532 14.0105 30.9126 14.0921C31.1539 14.4188 31.3803 14.7492 31.5994 15.0907C31.5994 14.842 31.5994 14.5895 31.5994 14.3408C31.4286 14.6081 31.2504 14.8679 31.0685 15.1278C30.9794 15.254 30.8866 15.3765 30.7976 15.499C30.7753 15.5287 30.753 15.5584 30.7307 15.5844C30.6416 15.7031 30.7382 15.5658 30.7493 15.5621C30.7048 15.5695 30.612 15.7366 30.5823 15.7737C30.1888 16.2674 29.7693 16.7388 29.3276 17.1917C28.8933 17.6371 28.4367 18.0603 27.9578 18.4575C27.8391 18.5577 27.7203 18.6542 27.6015 18.747C27.5458 18.7916 27.4901 18.8398 27.4307 18.8807C27.5644 18.7841 27.4827 18.8398 27.453 18.8621C27.4085 18.8955 27.3676 18.9289 27.3231 18.9586C27.0707 19.1479 26.8108 19.3298 26.551 19.508C26.0313 19.8569 25.4968 20.1799 24.9437 20.4768C24.6616 20.629 24.3794 20.7701 24.0899 20.9074C23.9488 20.9742 23.8078 21.0373 23.6667 21.1004C23.5999 21.1301 23.5331 21.1598 23.4663 21.1895C23.3252 21.2489 23.4886 21.1784 23.4923 21.1784C23.4886 21.1747 23.3475 21.2378 23.3401 21.2415C22.1485 21.7278 20.905 22.0879 19.6355 22.3143C19.461 22.3477 19.2828 22.3737 19.1046 22.4034C19.0564 22.4108 19.0118 22.4182 18.9636 22.4256C18.8077 22.4479 18.945 22.4034 18.9636 22.4256C18.945 22.4034 18.7149 22.4553 18.6815 22.4591C18.3103 22.5036 17.9391 22.5333 17.5679 22.5556C16.7141 22.6038 15.8566 22.5927 15.0028 22.5259C14.6131 22.4962 14.2419 22.8897 14.2604 23.2683C14.279 23.6952 14.5871 23.9773 15.0028 24.0107C17.8203 24.2297 20.7008 23.8325 23.3438 22.8228C25.8494 21.8688 28.1434 20.4174 30.0626 18.5429C31.1279 17.5035 32.0708 16.3342 32.8763 15.0832C33.0136 14.8679 33.0136 14.5487 32.8763 14.3334C31.2764 11.8426 29.1234 9.72306 26.5918 8.17885C25.8977 7.75568 25.1775 7.37705 24.4351 7.04668C24.0825 6.89077 23.6222 6.93161 23.418 7.31395C23.251 7.63689 23.3067 8.16029 23.6853 8.33104Z" fill="#393939"/>
          <path id="Path_3" d="M12.4011 16.0966C12.3491 15.9407 12.3046 15.7811 12.2712 15.6215C12.2526 15.5472 12.2378 15.4693 12.2266 15.3913C12.2192 15.3468 12.2118 15.3022 12.2044 15.2577C12.2155 15.3319 12.2118 15.3245 12.2044 15.254C12.1747 14.9496 12.1672 14.6415 12.1895 14.3371C12.1932 14.2666 12.2007 14.196 12.2081 14.1292C12.2229 13.9919 12.1821 14.2851 12.2155 14.0958C12.2378 13.9585 12.2638 13.8248 12.2972 13.6912C12.3269 13.5761 12.3566 13.4611 12.3937 13.346C12.4122 13.2903 12.4308 13.2346 12.4531 13.179C12.4642 13.1455 12.4753 13.1158 12.4902 13.0824C12.4976 13.0602 12.5533 12.9228 12.5088 13.0305C12.4679 13.1307 12.5384 12.9674 12.5533 12.934C12.5793 12.882 12.6053 12.83 12.6312 12.7743C12.6906 12.6556 12.7575 12.5405 12.828 12.4254C12.8948 12.3178 12.9616 12.2138 13.0359 12.1136C13.0507 12.095 13.0656 12.0728 13.0804 12.0542C13.1398 11.9725 12.9913 12.1619 13.0544 12.0876C13.0953 12.0356 13.1361 11.9837 13.1806 11.9354C13.344 11.7461 13.5184 11.5716 13.7077 11.4083C13.7523 11.3712 13.7968 11.3341 13.8414 11.2969C13.9416 11.2116 13.8191 11.3081 13.808 11.3192C13.8265 11.2969 13.8674 11.2747 13.8859 11.2598C13.9936 11.1819 14.1049 11.1076 14.2163 11.0371C14.3314 10.9666 14.4464 10.8998 14.5652 10.8404C14.632 10.807 14.6989 10.7736 14.7694 10.7401C14.7917 10.729 14.8139 10.7179 14.8399 10.7104C14.9958 10.6399 14.7323 10.7513 14.8251 10.7179C15.1072 10.6139 15.3856 10.5248 15.6788 10.4617C16.0612 10.3801 16.3173 9.91236 16.1985 9.54858C16.0686 9.14768 15.6937 8.94352 15.2854 9.02889C14.1346 9.27018 13.047 9.88637 12.2489 10.755C11.3989 11.683 10.8866 12.83 10.7344 14.0773C10.6379 14.8828 10.727 15.718 10.9757 16.4901C11.0945 16.8576 11.4954 17.136 11.8888 17.0098C12.2526 16.891 12.5273 16.4938 12.4011 16.0966Z" fill="#393939"/>
          <path id="Path_4" d="M20.5045 13.0676C20.6418 13.4202 20.7383 13.784 20.794 14.1589C20.768 14.003 20.8052 14.2629 20.8052 14.3C20.8126 14.3928 20.8163 14.4819 20.82 14.5747C20.8237 14.7343 20.82 14.8939 20.8089 15.0535C20.8052 15.1241 20.7977 15.1909 20.7903 15.2614C20.768 15.4544 20.8126 15.1278 20.7829 15.3208C20.7606 15.473 20.7272 15.6215 20.6901 15.7699C20.6567 15.8999 20.6196 16.0298 20.575 16.156C20.5527 16.2191 20.5305 16.2822 20.5082 16.3453C20.4971 16.375 20.4822 16.4084 20.4711 16.4381C20.4377 16.5235 20.5342 16.2971 20.4785 16.4196C20.3597 16.6757 20.2261 16.9207 20.0702 17.1545C20.0331 17.2102 19.9959 17.2622 19.9588 17.3142C19.9477 17.3327 19.9143 17.3847 19.8994 17.3921C19.9069 17.3884 20.0108 17.2548 19.9254 17.3587C19.8363 17.4663 19.7472 17.5703 19.6507 17.6742C19.5616 17.7707 19.4688 17.8598 19.3686 17.9489C19.3203 17.9935 19.2721 18.0343 19.2201 18.0788C19.1941 18.1011 19.1682 18.1197 19.1459 18.1419C19.0419 18.2273 19.1459 18.1753 19.1607 18.1308C19.1496 18.1679 19.0345 18.2236 19.0011 18.2459C18.9454 18.283 18.8935 18.3201 18.8378 18.3535C18.7116 18.4315 18.5854 18.5057 18.4517 18.5762C18.3775 18.6134 18.3033 18.6505 18.229 18.6839C18.1993 18.6987 18.1659 18.7099 18.1399 18.7247C18.203 18.6839 18.2587 18.6765 18.1548 18.7173C18.0063 18.7767 17.8578 18.825 17.7056 18.8695C17.5608 18.9103 17.4124 18.9474 17.2602 18.9771C17.1748 18.992 17.0894 19.0031 17.0078 19.018C17.0263 19.0143 17.1859 18.9994 17.0523 19.0106C17.0003 19.0143 16.9484 19.0217 16.8964 19.0254C16.5103 19.0588 16.1354 19.3446 16.154 19.7678C16.1725 20.1427 16.4806 20.5436 16.8964 20.5102C18.2587 20.3989 19.5356 19.8495 20.5305 18.8992C21.5179 17.9526 22.1081 16.6905 22.2714 15.3394C22.3791 14.4448 22.2603 13.513 21.9373 12.6741C21.8 12.314 21.4325 12.0245 21.0242 12.1544C20.6715 12.2658 20.356 12.6778 20.5045 13.0676Z" fill="#393939"/>
          <path id="Path_5" d="M24.1608 0.361305C24.0123 0.610011 23.8638 0.86243 23.7154 1.11114C23.3145 1.78673 22.9136 2.46603 22.5089 3.14162C21.9113 4.14758 21.3174 5.14983 20.7197 6.15579C19.9959 7.37705 19.2721 8.59831 18.5445 9.81957C17.7464 11.1633 16.952 12.5034 16.1539 13.8471C15.3484 15.202 14.5466 16.5569 13.7411 17.9118C12.995 19.1702 12.2489 20.4286 11.499 21.687C10.868 22.7523 10.237 23.8139 9.60591 24.8793C9.14933 25.6477 8.69275 26.4161 8.23617 27.1845C8.02087 27.5483 7.79815 27.9083 7.59027 28.2758C7.58285 28.2907 7.57171 28.3055 7.56429 28.3204C7.36755 28.6544 7.47891 29.1481 7.83156 29.3375C8.18791 29.5268 8.63707 29.4265 8.84865 29.0702C8.99714 28.8215 9.14562 28.5691 9.2941 28.3204C9.695 27.6448 10.0959 26.9655 10.5005 26.2899C11.0981 25.2839 11.6921 24.2817 12.2897 23.2757C13.0136 22.0544 13.7374 20.8332 14.465 19.6119C15.2631 18.2682 16.0574 16.9281 16.8555 15.5844C17.661 14.2295 18.4628 12.8746 19.2683 11.5197C20.0145 10.2613 20.7606 9.00292 21.5104 7.74454C22.1415 6.67919 22.7725 5.61755 23.4035 4.55219C23.8601 3.7838 24.3167 3.01541 24.7733 2.24702C24.9886 1.88324 25.2113 1.52317 25.4192 1.15568C25.4266 1.14083 25.4377 1.12598 25.4452 1.11114C25.6419 0.777053 25.5305 0.283352 25.1779 0.0940382C24.8178 -0.0952756 24.3724 0.00494939 24.1608 0.361305Z" fill="#393939"/>
        </g>
      </g>
    </svg>
  `;
  return <SvgXml xml={xml} {...props} />;
};

export default IconViewCustomerInfoEye;
