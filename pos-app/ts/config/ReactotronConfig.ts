import AsyncStorage from '@react-native-async-storage/async-storage';
import { NativeModules } from 'react-native';
import { ReactotronReactNative } from 'reactotron-react-native';

// iOS can't connect to Reactotron https://github.com/infinitered/reactotron/issues/272
let reactotron: ReactotronReactNative | null = null;
if (__DEV__) {
  const Reactotron = require('reactotron-react-native').default;
  const reactotronRedux = require('reactotron-redux').reactotronRedux;
  const sagaPlugin = require('reactotron-redux-saga');
  const scriptURL = NativeModules.SourceCode.scriptURL;
  const scriptHostname = scriptURL.split('://')[1].split(':')[0];
  reactotron = Reactotron.setAsyncStorageHandler(AsyncStorage)
    .configure({ name: 'RN-POS', host: scriptHostname })
    .useReactNative({
      asyncStorage: true,
      editor: true,
      overlay: false,
    })
    .use(reactotronRedux({ onBackup: state => state }))
    .use(sagaPlugin({}))
    .connect();
}

export default reactotron;
