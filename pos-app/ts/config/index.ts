import { Map } from 'immutable';
import Config from 'react-native-config';
import { getTimeZone } from 'react-native-localize';

import { Base64 } from 'js-base64';
import { Store } from 'redux';
import { generateApiUrl } from '../utils/url';
import { encodeBase64 } from '../utils/websocket/utils';

export enum EnvironmentType {
  Debug = 'DEBUG',
  Fat = 'FAT',
  Uat = 'UAT',
  Pro = 'PRO',
}

let store;
export const setStore = reduxStore => {
  store = reduxStore;
};

export const getPreservedStore = () => store as Store;

export const getStoreState = () => (store ? store.getState() : Map());

const TIMEZONE = getTimeZone();

const API_URL = (businessName?: string) => {
  const apiUrl = getTestEnvURL('API_URL', 'https://www.backoffice.${testEnv}.shub.us');
  return generateApiUrl(apiUrl, businessName);
};

const CASHBACK_URL = () => {
  return getTestEnvURL('CASH_BACK_URL', 'https://www.beep.${testEnv}.shub.us');
};

const CORE_API_URL = () => {
  if (Boolean(store)) {
    return getTestEnvURL('CORE_API_URL', 'https://mobile-api.${testEnv}.shub.us/graphql');
  } else {
    return Config.CORE_API_URL;
  }
};

const POS_BFF_API_URL = () => {
  if (Boolean(store)) {
    return getTestEnvURL('POS_BFF_API_URL', 'https://pos-bff.${testEnv}.shub.us');
  } else {
    return Config.POS_BFF_API_URL;
  }
};

export const PAYMENT_API_URL_SWITCHABLE = () => {
  if (Boolean(store)) {
    return getTestEnvURL('PAYMENT_API_URL', 'https://payment-api.${testEnv}.shub.us');
  } else {
    return Config.PAYMENT_API_URL;
  }
};

const JOIN_MEMBERSHIP_URL = (business: string, storeId: string, source: string) => {
  const apiUrl = getTestEnvURL('JOIN_MEMBERSHIP_URL', 'https://scan.beep.${testEnv}.shub.us/rewards/business/membership/sign-up');
  return `${apiUrl}?business=${business}&source=${source}&storeId=${storeId}`;
};

const NEW_JOIN_MEMBERSHIP_URL = (business: string, storeId: string, channel: number, receiptNumber: string, pointsOnly: boolean) => {
  const base64ReceiptNumber = Base64.encode(receiptNumber || '', true);
  let apiUrl = null;
  if ((Config.ENVIRONMENT === EnvironmentType.Fat || Config.ENVIRONMENT === EnvironmentType.Debug) && Boolean(store)) {
    apiUrl = getTestEnvURL('NEW_JOIN_MEMBERSHIP_URL', 'https://scan.beep.${testEnv}.shub.us/rewards/business/join-membership');
  } else if (Config.ENVIRONMENT === EnvironmentType.Uat || Config.ENVIRONMENT === EnvironmentType.Pro) {
    apiUrl = 'https://beepit.com/rewards/business/join-membership';
  }
  return `${apiUrl}?business=${business}&source=${
    pointsOnly ? 'Receipt_PointsQR' : 'Receipt_PointsCashbackQR'
  }&storeId=${storeId}&channel=${channel}&receiptNumber=${base64ReceiptNumber}`;
};

// https://scan.beep.test35.shub.us/rw/jm?bn=jw&s=1&c=3&rn=ODUyMDY4MjExMjQ5OTc1&si=60ed669916c14b0006720d09
const SHORT_NEW_JOIN_MEMBERSHIP_URL = (business: string, storeId: string, channel: number, receiptNumber: string, pointsOnly: boolean) => {
  const base64ReceiptNumber = Base64.encode(receiptNumber || '', true);
  let apiUrl = null;
  if ((Config.ENVIRONMENT === EnvironmentType.Fat || Config.ENVIRONMENT === EnvironmentType.Debug) && Boolean(store)) {
    apiUrl = getTestEnvURL('SHORT_NEW_JOIN_MEMBERSHIP_URL', 'https://scan.beep.${testEnv}.shub.us/rw/jm');
  } else if (Config.ENVIRONMENT === EnvironmentType.Uat || Config.ENVIRONMENT === EnvironmentType.Pro) {
    apiUrl = 'https://beepit.com/rw/jm';
  }
  return `${apiUrl}?bn=${business}&s=${pointsOnly ? '3' : '4'}&si=${storeId}&c=${channel}&rn=${base64ReceiptNumber}`;
};

const MEMBERSHIP_DETAIL_URL = (business: string, storeId: string, channel: number, receiptNumber: string) => {
  const base64ReceiptNumber = Base64.encode(receiptNumber || '', true);
  let apiUrl = null;
  if ((Config.ENVIRONMENT === EnvironmentType.Fat || Config.ENVIRONMENT === EnvironmentType.Debug) && Boolean(store)) {
    apiUrl = getTestEnvURL('MEMBERSHIP_DETAIL_URL', 'https://scan.beep.${testEnv}.shub.us/rewards/business/membership/membership-detail');
  } else if (Config.ENVIRONMENT === EnvironmentType.Uat || Config.ENVIRONMENT === EnvironmentType.Pro) {
    apiUrl = 'https://beepit.com/rewards/business/membership/membership-detail';
  }
  return `${apiUrl}?business=${business}&source=Receipt_MembershipDetailsQR&storeId=${storeId}&channel=${channel}&receiptNumber=${base64ReceiptNumber}`;
};

const SHORT_MEMBERSHIP_DETAIL_URL = (business: string, storeId: string, channel: number, receiptNumber: string) => {
  const base64ReceiptNumber = Base64.encode(receiptNumber || '', true);
  let apiUrl = null;
  if ((Config.ENVIRONMENT === EnvironmentType.Fat || Config.ENVIRONMENT === EnvironmentType.Debug) && Boolean(store)) {
    apiUrl = getTestEnvURL('SHORT_MEMBERSHIP_DETAIL_URL', 'https://scan.beep.${testEnv}.shub.us/rw/md');
  } else if (Config.ENVIRONMENT === EnvironmentType.Uat || Config.ENVIRONMENT === EnvironmentType.Pro) {
    apiUrl = 'https://beepit.com/rw/md';
  }
  return `${apiUrl}?bn=${business}&s=2&si=${storeId}&c=${channel}&rn=${base64ReceiptNumber}`;
};

export const getMembershipDetailPageUrl = (business: string, storeId: string, channel: number, receiptNumber: string) => {
  const detailsUrl = `/rewards/business/membership/membership-detail?business=${business}&source=Receipt_MembershipDetailsQR&storeId=${storeId}&channel=${channel}&receiptNumber=${encodeBase64(
    receiptNumber
  )}`;
  let apiUrl = 'https://beepit.com'; // Config.JOIN_MEMBERSHIP_URL;
  if (Config.ENVIRONMENT === EnvironmentType.Fat || Config.ENVIRONMENT === EnvironmentType.Debug) {
    const testEnv = store.getState().getIn(['EnvSetting', 'testEnvName'], 'Default');
    if (testEnv === 'Default') {
      apiUrl = 'https://beepit.com'; // Config.JOIN_MEMBERSHIP_URL;
    } else if (testEnv === 'Pro') {
      // TODO use the prod membership URL
      apiUrl = 'https://beepit.com';
    } else if (testEnv === 'Staging') {
      // TODO use the prod membership URL
      apiUrl = 'https://beepit.com';
    } else {
      apiUrl = `https://scan.beep.${testEnv}.shub.us`;
    }
  }
  return `${apiUrl}${detailsUrl}`;
};

export const getPosEInvoiceDomain = () => {
  if (Boolean(store)) {
    const testEnv = store.getState().getIn(['EnvSetting', 'testEnvName'], 'Default');
    if (testEnv === 'Default') {
      // fixme: move to .env
      return 'https://e-invoice.storehub.com';
    } else if (testEnv === 'Pro') {
      return 'https://e-invoice.storehub.com';
    } else if (testEnv === 'Staging') {
      return 'https://e-invoice.storehub.com';
    } else {
      return `https://e-invoice.beep.${testEnv}.shub.us`;
    }
  } else {
    return 'https://e-invoice.storehub.com';
  }
};

const SENTRY_RN_POS_URL = Config.SENTRY_RN_POS_URL;
const SH_CONNECTOR_URL = Config.SH_CONNECTOR_URL;
// const AWS_ACESS_KEY_ID = Config.AWS_ACESS_KEY_ID;
// const AWS_SECRET_ACCESS_KEY = Config.AWS_SECRET_ACCESS_KEY;
const AWS_S3_REGION = Config.AWS_S3_REGION;
const AWS_S3_BUCKET = Config.AWS_S3_BUCKET;
const AWS_S3_DB_BUCKET = Config.AWS_S3_DB_BUCKET;
const AWS_S3_REQUEST_ACCESS_URL = Config.AWS_S3_REQUEST_ACCESS_URL;
const AWS_S3_DIAGNOSE_LOG_BUCKET = Config.AWS_S3_DIAGNOSE_LOG_BUCKET;
const AWS_S3_DB_ACL = Config.AWS_S3_DB_ACL;
const STOREHUB_APP_VERSION = Config.Storehub_App_Version;
const LOGGLY_TOKEN = Config.LOGGLY_TOKEN;
const ENVIRONMENT = Config.ENVIRONMENT;

const PAYMENT_API_URL = Config.PAYMENT_API_URL;
const LOG_SERVICE_URL = Config.LOG_SERVICE_URL;
const LOG_SERVICE_TOKEN = Config.LOG_SERVICE_TOKEN;

const MIXPANEL_TOKEN = Config.MIXPANEL_TOKEN;

// #region growthBook
const GB_ENDPOINT = Config.GB_ENDPOINT;
const GB_HOST = Config.GB_HOST;
const GB_KEY = Config.GB_KEY;

const ENABLE_DUMP_PRINTING_IMAGES = Config.ENABLE_DUMP_PRINTING_IMAGES === 'TRUE';
const AWS_S3_PRINTING_IMAGE = Config.AWS_S3_PRINTING_IMAGE;

const IS_DEV_FAT = ENVIRONMENT === EnvironmentType.Fat || ENVIRONMENT === EnvironmentType.Debug;

const getTestEnvURL = (domain: string, testURL: string) => {
  if (Config.ENVIRONMENT === EnvironmentType.Fat || Config.ENVIRONMENT === EnvironmentType.Debug) {
    const testEnv = store.getState().getIn(['EnvSetting', 'testEnvName'], 'Default');
    let url = BE_URL_CONFIG[testEnv]?.[domain];
    if (!url) {
      url = testURL.replace('${testEnv}', testEnv);
    }
    return url;
  } else {
    return Config[domain];
  }
};

const BE_URL_CONFIG = {
  Default: {
    API_URL: Config.API_URL,
    CASH_BACK_URL: Config.CASH_BACK_URL,
    CORE_API_URL: Config.CORE_API_URL,
    POS_BFF_API_URL: Config.POS_BFF_API_URL,
    JOIN_MEMBERSHIP_URL: Config.JOIN_MEMBERSHIP_URL,
    NEW_JOIN_MEMBERSHIP_URL: 'https://scan.beep.test17.shub.us/rewards/business/join-membership',
    SHORT_NEW_JOIN_MEMBERSHIP_URL: 'https://scan.beep.test17.shub.us/rw/jm',
    MEMBERSHIP_DETAIL_URL: 'https://scan.beep.test17.shub.us/rewards/business/membership/membership-detail',
    SHORT_MEMBERSHIP_DETAIL_URL: 'https://scan.beep.test17.shub.us/rw/md',
  },
  Pro: {
    API_URL: 'https://www.storehubhq.com',
    CASH_BACK_URL: 'https://www.beepit.co',
    CORE_API_URL: 'https://mobile-api.storehubhq.com/graphql',
    POS_BFF_API_URL: 'https://mobile.storehubhq.com',
    JOIN_MEMBERSHIP_URL: 'https://beepit.com/rewards/business/membership/sign-up',
    NEW_JOIN_MEMBERSHIP_URL: 'https://beepit.com/rewards/business/join-membership',
    SHORT_NEW_JOIN_MEMBERSHIP_URL: 'https://beepit.com/rw/jm',
    MEMBERSHIP_DETAIL_URL: 'https://beepit.com/rewards/business/membership/membership-detail',
    SHORT_MEMBERSHIP_DETAIL_URL: 'https://beepit.com/rw/md',
  },
  Staging: {
    API_URL: 'https://www.backoffice.staging.mymyhub.com',
    CASH_BACK_URL: 'https://www.beep.staging.mymyhub.com',
    CORE_API_URL: 'https://mobile-api.staging.mymyhub.com',
    POS_BFF_API_URL: 'https://pos-bff.staging.mymyhub.com',
    JOIN_MEMBERSHIP_URL: 'https://scan.beep.staging.mymyhub.com/rewards/business/membership/sign-up',
    NEW_JOIN_MEMBERSHIP_URL: 'https://beepit.com/rewards/business/join-membership',
    SHORT_NEW_JOIN_MEMBERSHIP_URL: 'https://beepit.com/rw/jm',
    MEMBERSHIP_DETAIL_URL: 'https://beepit.com/rewards/business/membership/membership-detail',
    SHORT_MEMBERSHIP_DETAIL_URL: 'https://beepit.com/rw/md',
  },
};

// #endregion
export {
  API_URL,
  // AWS_ACESS_KEY_ID,
  AWS_S3_BUCKET,
  AWS_S3_DB_ACL,
  AWS_S3_DB_BUCKET,
  AWS_S3_DIAGNOSE_LOG_BUCKET,
  AWS_S3_PRINTING_IMAGE,
  AWS_S3_REGION,
  AWS_S3_REQUEST_ACCESS_URL,
  // AWS_SECRET_ACCESS_KEY,
  CASHBACK_URL,
  CORE_API_URL,
  ENABLE_DUMP_PRINTING_IMAGES,
  ENVIRONMENT,
  GB_ENDPOINT,
  GB_HOST,
  GB_KEY,
  IS_DEV_FAT,
  JOIN_MEMBERSHIP_URL,
  LOG_SERVICE_TOKEN,
  LOG_SERVICE_URL,
  LOGGLY_TOKEN,
  MEMBERSHIP_DETAIL_URL,
  MIXPANEL_TOKEN,
  NEW_JOIN_MEMBERSHIP_URL,
  PAYMENT_API_URL,
  POS_BFF_API_URL,
  SENTRY_RN_POS_URL,
  SH_CONNECTOR_URL,
  SHORT_MEMBERSHIP_DETAIL_URL,
  SHORT_NEW_JOIN_MEMBERSHIP_URL,
  STOREHUB_APP_VERSION,
  TIMEZONE,
};
