import { filter, find, get, map } from 'lodash';
import { t } from '../constants';

export enum DefaultPaymentOptionType {
  Cash = 0,
  CreditCard = 1,
  Loyalty = 2,
  DebitCard = 3,
}

export enum PaymentType {
  QRCODE = 'qrCode',
  NFC = 'NFC',
  TERMINAL = 'paymentTerminal',
  CUSTOM = 'custom',
}

export const defaultPaymentOptions: PaymentOptionType[] = [
  { paymentId: DefaultPaymentOptionType.Cash, name: 'Cash' },
  { paymentId: DefaultPaymentOptionType.CreditCard, name: 'CreditCard' },
  { paymentId: DefaultPaymentOptionType.Loyalty, name: 'Loyalty' },
  { paymentId: DefaultPaymentOptionType.DebitCard, name: 'DebitCard' },
];

export enum QRCodeProvider {
  Modulus_QRPH = 'Modulus Labs - QR Ph', // QR Ph
}

const PaymentMethodMap = {
  paymentOptions: [...defaultPaymentOptions],
};

export enum OnlineOrderPaymentMethod {
  ONLINE = 'Online',
  OFFLINE = 'Offline',
}

export interface PaymentOptionType {
  paymentId: number;
  name: string;
  type?: string;
  ordering?: number;
  _id?: string;
  stores?: string[];
  isDeleted?: boolean;
  isDisabled?: boolean;
  additionalInfo?: PaymentOptionAdditionalInfoType;
  isExcludedFromEInvoice?: boolean;
}

export interface PaymentOptionAdditionalInfoType {
  clientId?: string;
  clientSecret?: string;
  clientQrId?: string;
  clientTerminalId?: string;
  qrCodeType?: string;
  qrCodeProvider?: string;
  /**
   * The id is generated by Razer Merchant Services for merchant,
   * which is prohibited to use at multiple POS devices.
   */
  terminalUID?: string;
}

export interface GhlPaymentOptionAdditionalInfoType extends PaymentOptionAdditionalInfoType {
  merchantId: string;
  terminalId: string;
  paymentTerminalType: string;
  paymentTerminalProvider: string;
}

export interface GhlPaymentOptionType extends PaymentOptionType {
  paymentId: number;
  name: string;
  type?: string;
  ordering?: number;
  _id?: string;
  stores?: [];
  isDeleted?: boolean;
  isDisabled?: boolean;
  additionalInfo?: GhlPaymentOptionAdditionalInfoType;
}

export default class PaymentOptions {
  static updatePaymentOptions = (paymentOptions: PaymentOptionType[] = [], storeId) => {
    const paymentOptionList = JSON.parse(JSON.stringify(paymentOptions));
    const insertPaymentOptions = [];

    if (Boolean(paymentOptionList) && paymentOptionList.length > 0) {
      const appliedPaymentOptions = filter(paymentOptionList, paymentOption => {
        const stores = paymentOption.stores;
        return find(stores, store => store === 'All' || store === storeId);
      });
      PaymentMethodMap.paymentOptions = [...insertPaymentOptions, ...defaultPaymentOptions, ...appliedPaymentOptions];
    } else {
      PaymentMethodMap.paymentOptions = [...insertPaymentOptions, ...defaultPaymentOptions];
    }
  };

  static addPaymentOption = (paymentOption: PaymentOptionType) => {
    PaymentMethodMap.paymentOptions.push(paymentOption);
  };

  static insertPaymentOption = (paymentOption: PaymentOptionType) => {
    PaymentMethodMap.paymentOptions.unshift(paymentOption);
  };

  static getPaymentNameForReceipt = (paymentId: number, enableCashback: boolean) => {
    return paymentId === DefaultPaymentOptionType.Loyalty ? (enableCashback ? t('Cashback') : t('Store Credit')) : PaymentOptions.getPaymentNameById(paymentId);
  };

  static getPaymentNameById = (paymentId: number): string => {
    const paymentIdNumber = Number(paymentId);
    const paymentOption: PaymentOptionType = find(PaymentMethodMap.paymentOptions, paymentOption => paymentOption.paymentId === paymentIdNumber);
    return Boolean(paymentOption) ? paymentOption.name : '';
  };

  static getSupportedPaymentOptions = (paymentOptions = PaymentMethodMap.paymentOptions) => {
    return filter(paymentOptions, (paymentOption: PaymentOptionType) => !(paymentOption.isDeleted || paymentOption.isDisabled));
  };

  // static getNfcUid = () => {
  //   const paymentions: PaymentOptionType[] = PaymentOptions.getSupportedPaymentOptions();
  //   const nfcPaymentOption = paymentions.find(paymentOption => {
  //     return paymentOption.type == 'NFC';
  //   });
  //   if (Boolean(nfcPaymentOption)) {
  //     const uid = nfcPaymentOption.additionalInfo.terminalUID;
  //     return uid;
  //   }
  //   return undefined;
  // };

  static getSupportedPaymentIds = () => {
    return map(PaymentOptions.getSupportedPaymentOptions(PaymentMethodMap.paymentOptions), v => v.paymentId);
  };

  static getUploadPaymentMethod = (paymentMethodId: number) => {
    return paymentMethodId >= 4 ? String(paymentMethodId) : PaymentOptions.getPaymentNameById(paymentMethodId);
  };

  static getDownloadPaymentMethodId = (paymentMethod: string) => {
    return parseInt(paymentMethod) >= 4 ? parseInt(paymentMethod) : DefaultPaymentOptionType[paymentMethod];
  };

  static getDownloadPaymentMethod = (paymentMethodId: number) => {
    return PaymentOptions.getPaymentNameById(paymentMethodId);
  };

  static getPaymentTypeById = (paymentId: number): string => {
    const paymentOption: PaymentOptionType = find(PaymentMethodMap.paymentOptions, paymentOption => paymentOption.paymentId === paymentId);
    return Boolean(paymentOption) ? paymentOption.type : '';
  };

  static getPaymentOptionById = (paymentId: number): PaymentOptionType => {
    const paymentOption: PaymentOptionType = find(PaymentMethodMap.paymentOptions, paymentOption => paymentOption.paymentId === paymentId);
    return Boolean(paymentOption) ? paymentOption : undefined;
  };

  static getPaymentOptionByName = (name: string): PaymentOptionType => {
    const paymentOption: PaymentOptionType = find(PaymentMethodMap.paymentOptions, paymentOption => paymentOption.name === name);
    return Boolean(paymentOption) ? paymentOption : undefined;
  };
}

export function isQRPhProvider(paymentOption: any): boolean {
  const qrCodeProvider = get(paymentOption, ['additionalInfo', 'qrCodeProvider'], '');
  return qrCodeProvider === 'Modulus Labs - QR Ph';
}
