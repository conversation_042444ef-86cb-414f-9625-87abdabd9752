export enum AuthorizationType {
  Cashier = 'Cashier',
  EditMode = 'EditMode',
}

export enum AuthorizationAccessType {
  AnyCashier = '0',
  NeedManagerGranted = '1',
  Disable = '2',
  Always = '10',
}

export enum CashierActions {
  DeleteOrder = 'DeleteOrder',
  Refund = 'Refund',
  Cancel = 'Cancel',
  Discount = 'Discount',
  RePrint = 'Reprint',
  ReSend = 'ReSend',
  OpenCloseShift = 'OpenCloseShift',
  ViewEditTransaction = 'ViewEditTransaction',
  POSSetting = 'posSetting',
  CloseZReading = 'CloseZReading',
  AyalaMallCloseZReading = 'AyalaMallCloseZReading',
  TapResetMRSButton = 'TapResetMRSButton',
  SwitchMaster = 'SwitchMaster',
}

export enum AuthorizationAccessStatus {
  Granted = 'granted',
  Denied = 'denied',
  Requesting = 'requesting',
}

export enum BackOfficeDetailAccesses {
  Dashboard = 'dashboard',
  Transactions = 'transactions',
  Promotions = 'promotions',
  Customers = 'customers',
  Reports = 'reports',
  Employees = 'employees',
  Products = 'products',
  ProductsPriceBooks = 'products/pricebooks',
  OnlinestoreGettingStarted = 'onlinestore/gettingstarted',
  OnlinestoreSetup = 'onlinestore/setup',
  OnlinestoreAppearance = 'onlinestore/appearance',
  OnlinestoreOrders = 'onlinestore/orders',
  StocksSupplynees = 'stocks/supplyneeds',
  StocksPurchaseOrdres = 'stocks/purchaseorders',
  StocksStockReturn = 'stocks/stockreturn',
  StocksStockTransfers = 'stocks/stocktransfers',
  StocksStocktTakes = 'stocks/stocktakes',
  StocksAuditTrail = 'stocks/audittrail',
  SettingsAccount = 'settings/account',
  SettingsTax = 'settings/tax',
  SettingsReceipts = 'settings/receipts',
  SettingsAddons = 'settings/addons',
  SettingsPaymentOptions = 'settings/paymentoptions',
  SetttingsBilling = 'settings/billing',
  SettingsStore = 'settings/stores',
  SettingsRegistes = 'settings/registers',
  SettingsIpadlayouts = 'settings/ipadlayouts',
}

export enum SubscriptionStatus {
  Trial = 'Trial',
  Expired = 'Expired',
  Active = 'Active',
}
