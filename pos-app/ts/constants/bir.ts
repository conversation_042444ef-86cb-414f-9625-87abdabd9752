export enum BIRDiscountType {
  SCAndPWD = 'SC/PWD',
  AthletesAndCoaches = 'ATHLETE_AND_COACH',
  MedalofValor = 'MEDAL_OF_VALOR',
  Diplomats = 'DIPLOMAT',
  SoloParent = 'SOLO_PARENT',
}

export const DisplayBirDiscountTitle = Object.freeze({
  'SC/PWD': 'SC/PWD',
  SC: 'SENIOR CITIZEN',
  PWD: 'PERSON WITH DISABILITY',
  ATHLETE_AND_COACH: 'NATIONAL ATHLETE/COACH',
  MEDAL_OF_VALOR: 'MEDAL OF VALOR',
  DIPLOMAT: 'DIPLOMAT',
  SOLO_PARENT: 'SOLO PARENT',
});

export const FAB_SCPWD_DISCOUNT_RATE = 0.2;

export const Retail_SCPWD_DISCOUNT_RATE = 0.05;

export enum BIRType {
  FAB = 'f&b',
  Retail = 'retail',
}

export enum BIRStoreType {
  RetailStore = 1,
  FABStore = 0,
}

export interface SCAndPWDInfo {
  seniorsCount: number;
  pwdCount: number;
  headCount: number;
}

export interface SoloParentInfo {
  name: string;
  spicId: string;
  nameOfChild: string;
}

export interface AthletesAndCoachesInfo {
  name: string;
  pnstmId: string;
}

export interface MedalofValorInfo {
  name: string;
  movId: string;
}

export interface DiplomatsInfo {
  name: string;
  dfaOrVicId: string;
  address: string;
}

export interface BirInfo {
  type: string;
  discountType?: {
    type: string;
    enum: BIRDiscountType;
  };
  discountRate: number;
  seniorsCount: number;
  pwdCount: number;
  headCount: number;
  collectedInfo?: AthletesAndCoachesInfo | MedalofValorInfo | DiplomatsInfo;
}
