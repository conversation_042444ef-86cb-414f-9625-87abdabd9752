export enum MallIntegrationChannel {
  RO<PERSON>NSON_MALL = 'RO<PERSON><PERSON><PERSON>',
  MEGAWORLD = 'MEGAWORLD', // ROBINSON_MALL
  ROCKWELL = 'ROCKWELL', // ROBINSON_MALL
  FBDC = 'FBDC', // RO<PERSON><PERSON><PERSON>_MALL
  FILINVEST = 'FILINVEST', // ROBINSON_MALL
  SFDC = 'SFDC', // ROBINSON_MALL
  EVER_GOTESCO = 'EVER_GOTESCO', // ROBINSON_MALL
  AYALA_MALL = 'AYALA',
  ORTIGAS_MALL = 'ORTIGAS',
  SM_MALL = 'SM', // SM MALL
  SM_Hypermarket = 'SM_HYPER', // SM MALL
  SM_Supermarket = 'SM_SUPER', // SM MALL
  SM_Savemore_Market = 'SM_SAVEMORE', // SM MALL
}

export const RobinsonMallChannelsList = [
  MallIntegrationChannel.ROBINSON_MALL,
  MallIntegrationChannel.MEGAWORLD,
  MallIntegrationChannel.ROCKWELL,
  MallIntegrationChannel.FBDC,
  MallIntegrationChannel.FILINVEST,
  MallIntegrationChannel.SFDC,
  MallIntegrationChannel.EVER_GOTESCO,
];

export const SMMallChannelList: string[] = [
  MallIntegrationChannel.SM_MALL,
  MallIntegrationChannel.SM_Hypermarket,
  MallIntegrationChannel.SM_Supermarket,
  MallIntegrationChannel.SM_Savemore_Market,
];

export const belongToRobinsonMall = channel => {
  return RobinsonMallChannelsList.includes(channel);
};

export enum MallIntegrationMSG {
  ROBINSON_MALL_ERR_MSG = 'Sales file is not sent to RLC server. Please contact your POS vendor',
}
