import { PrinterConfigType } from '../actions';
import { PurchasedItemType } from '../typings';

// receipt printer used for order summary
export const ORDER_SUMMARY_PRINTER_TAG = '__receipt__';
export const BEEP_ORDER_SUMMARY_PRINTER_TAG = '__BeepQR__';

export enum PrinterConnectType {
  USB = 'USB',
  LAN = 'LAN',
  Bluetooth = 'Bluetooth',
}

export enum PrinterModelType {
  BIXOLON = 'BIXOLON',
  SUNMIPRINTER = 'SUNMIPRINTER',
  XPRINTER = 'XPRINTER',
  LANXPRINTER = 'LANXPRINTER', // iOS
  IMINPRINTER = 'IMINPRINTER',
  AIRPRINTER = 'AIRPRINTER',
}

export const isLanXPrinter = (printer: PrinterConfigType) => {
  return (
    printer &&
    (printer.printerModelType === PrinterModelType.XPRINTER || printer.printerModelType === PrinterModelType.LANXPRINTER) &&
    printer.printerConnectType === PrinterConnectType.LAN
  );
};

export enum PrintingBusinessType {
  TRANSACTION = 'TRANSACTION',
  SHIFT_REPORT = 'SHIFT_REPORT',
  KITCHEN_TICKET = 'KITCHEN_TICKET',
  Z_READING_REPORT = 'Z_READING_REPORT',
  DAILY_REPORT = 'DAILY_REPORT',
  DYNAMIC_BEEP_QR = 'DYNAMIC_BEEP_QR',
  AYALA_MALL_REPORT = 'AYALA_MALL_REPORT',
  SM_EOD_REPORT = 'SM_EOD_REPORT',
  SM_XReading_REPORT = 'SM_XReading_REPORT',
  ORTIGAS_EOD_REPORT = 'ORTIGAS_EOD_REPORT',
  E_INVOICE_QR = 'E_INVOICE_QR',
}
export enum PrinterJobBusinessType {
  TRANSACTION = 'TRANSACTION',
  KITCHEN_TICKET = 'KITCHEN_TICKET',
  ORDER_SUMMARY = 'ORDER_SUMMARY',
  BEER_DOCKET = 'BEER_DOCKET',
  SHIFT_REPORT = 'SHIFT_REPORT',
  Z_READING_REPORT = 'Z_READING_REPORT',
}

export interface RequestPrintingModelType {
  printerId: string;
  businessType: PrintingBusinessType;
  data: PrinterKitchenModel | any;
}

export interface PrinterKitchenModel {
  receiptDate: string;
  bixReceiptDate: string;
  isOpenOrder: boolean;
  isBeepOrder: boolean;
  orderNumberTitle: string;
  orderTypeName?: string;
  extraOrderNumber?: string;
  orderNumber: string;
  purchasedItems?: PurchasedItemType[];
  takeawayItems?: PurchasedItemType[];
  takeawayTitle?: string;
  isTakeaway: boolean;
  total: number;
  noteTitle: string;
  note?: string;
  pickUpId?: string;
  tableId: string;
  bixOrderNumber: string;
}
