import { invert } from 'lodash';
import { AllPurchasedItemType } from '../sagas/printing/common';
import { LogOptions } from '../utils/logComponent';
import { t } from './i18n';

export enum TransactionFlowType {
  Sale = 'Sale',
  Return = 'Return',
  PreOrder = 'PreOrder',
}

export enum NotificationType {
  StatusChange = 'statusChange',
  SubOrderCreated = 'subOrderCreated',
}

/**
 * both, beep pay first
 * kitchen, FDI(Grab, Shopee, FoodPanda) + subOrders that not printed
 * full, paid others order(pay-later, not beep pay first, not FDI)
 */
export enum PollingPrintType {
  BOTH = 'both',
  KITCHEN = 'kitchen',
  FULL = 'full',
  NONE = 'none', // no print
}

export enum CancelledSource {
  BO = 'BO',
  POS = 'POS',
  PUBLIC_API = 'PUBLIC_API',
}

export enum OrderChannel {
  OrderChannelNone = 0,
  OrderChannelOnline = 1, // EC
  OrderChannelOffline = 2, // POS
  OrderChannelQRCode = 3, // beep
  OrderChannelGrab = 10,
  OrderChannelShopee = 11,
  OrderChannelFoodPanda = 12,
  OrderChannelLazada = 100,
  OrderChannelShopeeEC = 101,
  OrderChannelZalora = 102,
  OrderChannelWooCommerce = 103,
  OrderChannelShopify = 104,
  OrderChannelTikTokShop = 105,
  OrerChannelMagento = 110,
  OrerChannelCustom = 111,
}

export const OfflineChannels = [OrderChannel.OrderChannelOffline];

export enum OnlineOrderStatus {
  PickedUp = 'pickedUp',
  PendingPayment = 'pendingPayment',
  PendingVerification = 'pendingVerification',
  ReadyForDelivery = 'readyForDelivery',
  Shipped = 'shipped',
  Paid = 'paid',
  ReadyForPickup = 'readyForPickup',
  Confirmed = 'confirmed',
  PendingConfirmed = 'pendingConfirmed',
  PaymentCancelled = 'paymentCancelled',
  Delivered = 'delivered',
  Cancelled = 'cancelled',
  LogisticsConfirmed = 'logisticsConfirmed',
  Accepted = 'accepted',
  SentToKitchen = 'sentToKitchen',
  Created = 'created',
}

export const OnlineOrderStatusMapTitle = Object.freeze({
  delivery: {
    paid: t('PendingAcceptance'),
    accepted: t('FindingRider'),
    logisticsConfirmed: t('RiderAssigned'),
    pendingPayment: t('PendingPayment'),
    confirmed: t('PendingPickUp'),
    pickedUp: t('PickedUp'),
    cancelled: t('Cancelled'),
    delivered: t('Delivered'),
  },
  selfPickUp: {
    paid: t('PendingAcceptance'),
    accepted: t('PendingPickUp'),
    pendingPayment: t('PendingPayment'),
    confirmed: t('PendingPickUp'),
    pickedUp: t('PickedUp'),
    cancelled: t('Cancelled'),
  },
  dineIn: {
    paid: t('NewOrder'),
    pendingPayment: t('PendingPayment'),
    confirmed: t('Printed To Kitchen'),
    cancelled: t('Cancelled'),
  },
});

export const OnlineOrderTitleMapStatus = Object.freeze({
  delivery: invert(OnlineOrderStatusMapTitle.delivery),
  selfPickUp: invert(OnlineOrderStatusMapTitle.selfPickUp),
  dineIn: invert(OnlineOrderStatusMapTitle.dineIn),
});

export const monthFullNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];

export enum SalesChannelType {
  DEFAULT = 1,
  // eslint-disable-next-line @typescript-eslint/no-duplicate-enum-values
  DINE_IN = 1,
  TAKEAWAY = 2,
}

export enum ItemChannelType {
  DEFAULT = 1,
  // eslint-disable-next-line @typescript-eslint/no-duplicate-enum-values
  DINE_IN = 1,
  TAKEAWAY = 2,
}

export enum LoyaltyType {
  CASHBACK = 'cashback',
  STORECREDIT = 'storeCredit',
}

export enum ShippingType {
  DELIVERY = 'delivery',
  PICKUP = 'pickup',
  DIGITAL = 'digital',
  TAKEAWAY = 'takeaway',
  DINE_IN = 'dineIn',
}

export type SubOrderInfoType = {
  comments?: string;
  submitId: string;
  submittedTime: string;
  isPrinted: boolean;
  printedTime?: string;
  submittedBy: string;
  submittedFrom: string; // consumer
};
export interface OnlinePayLaterOrderType {
  orderId?: string;
  receiptNumber?: string;
  submitId?: string;
  isSubOrder?: boolean;
  subOrderInfo?: SubOrderInfoType;
  status?: OnlineOrderStatus;
  items: AllPurchasedItemType[];
  isPayLater?: string;
  comment?: string;
  tableId: string;
  pickUpId?: string;
  shippingType?: ShippingType;
  printType?: PollingPrintType;
  logOptions?: LogOptions;
}

export enum MakePaymentErrorType {
  LessAmount = 1,
  MarkpaidFailed = 2,
  InvalidTransactionTime = 3,
  GetOrderDetailError = 4,
}
