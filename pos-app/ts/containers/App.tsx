import NetInfo, { NetInfoSubscription } from '@react-native-community/netinfo';
import * as Sentry from '@sentry/react-native';

import PushNotificationIOS from '@react-native-community/push-notification-ios';
import { filter, find, get } from 'lodash';
import React, { useEffect } from 'react';
import { AppState, BackHandler, DeviceEventEmitter, LogBox, NativeEventSubscription, StatusBar, StyleSheet, ToastAndroid, View } from 'react-native';

import FlashMessage from 'react-native-flash-message';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import 'react-native-get-random-values';
import RNLanguages from 'react-native-languages';
import { PushyProvider } from 'react-native-update';
import { connect, ConnectedProps, Provider } from 'react-redux';
import { bindActionCreators } from 'redux';
import {
  checkUpdate,
  clearGrowthBook,
  EventType,
  findNewPrinters,
  handleBeepOrderNotification,
  rebootMRSAction,
  receivedMessageAction,
  setCfdConfigurationBegin,
  setGrowthBookFeatures,
  setNetInfo,
  setNetInfoAction,
  signOut,
  syncAppState,
  updateMasterStateAction,
  uploadIncompletePrintingImage,
  uploadLocalDataToS3,
  uploadMemoryInformation,
} from '../actions';
import CFD from '../components/cfd/CFD';
import { LoadingMask, ToastInfo } from '../components/common';
import TopNotification from '../components/common/TopNotification';
import KDS from '../components/kds/KDS';
import NCS from '../components/ncs/NCS';
import { ExternalDisplayProvider } from '../components/outline';
import { ReceiptRenderer } from '../components/printing2/ReceiptRenderer';
import TopOverflowBanner from '../components/settings/TopOverflowBanner';
import { ENVIRONMENT, EnvironmentType, IS_DEV_FAT, setStore } from '../config';
import { currentThemes, i18n, IsAndroid, IsIOS, MallIntegrationChannel, scaleSizeH, scaleSizeW, SharedStyles, t } from '../constants';
import useLocalLogger from '../hooks/local-logger/useLocalLogger';
import * as NavigationService from '../navigation/navigatorService';
import { RootStack } from '../navigation/routers';
import configureStore from '../reduxStore/configureStore';
import {
  selectAccountExpired,
  selectAccountOffline,
  selectAuthToken,
  selectAutoSignOutCount,
  selectDisablePushNotification,
  selectEmployeeId,
  selectEnableTableLayout,
  selectEnableViewLocalSettingsOnIST,
  selectGBProductLayoutRevampEnabled,
  selectIsMallIntegrationEnabled,
  selectIsReduxLoaded,
  selectMallIntegrationChannel,
  selectNetworkReachability,
  selectOperationHours,
  selectShiftOpenStatus,
  selectTableLayoutEnabled,
} from '../sagas/selector';
import { RootState } from '../typings';
import { isValidNumber } from '../utils';
import { isLastShiftNotClosed } from '../utils/datetime';
import eventBus, { APP_ACTIVE, APP_BACKGROUND, UPDATE_GLOBAL_GROWTHBOOK } from '../utils/eventBus';
import globalConfig from '../utils/globalConfig';
import { infoPOSBasicEvent, logMRSEvent, MRSAction, POSBasicAction, SetUpFlow } from '../utils/logComponent';
import WsManager, { WsManagerSubscription } from '../utils/mrs';
import PrinterManager, { PrinterSubscription } from '../utils/printer';
import { pushyClient } from '../utils/pushy';
import SplashManager from '../utils/splashScreen';
import { isEmpty } from '../utils/validator';
import AIChatContainer from '../components/aiChat/AIChatContainer';
import AIChatFAB from '../components/aiChat/AIChatFAB';

const { store } = configureStore();
setStore(store);

if (IsIOS) {
  LogBox.ignoreAllLogs(true);
}

// if (__DEV__) {
//   const whyDidYouRender = require('@welldone-software/why-did-you-render');
//   whyDidYouRender(React, {
//     trackAllPureComponents: true,
//   });
// }
let DevShortcut;
if (IS_DEV_FAT) {
  DevShortcut = require('../components/test/DevShortcut').default;
}
const App = () => {
  useEffect(() => {
    SplashManager.hide();
  }, []);

  useLocalLogger();
  return (
    <Provider store={store}>
      <GestureHandlerRootView style={SharedStyles.flexOne}>
        <ExternalDisplayProvider />
        <ReceiptRenderer />
        <StatusBar backgroundColor={'transparent'} barStyle='dark-content' translucent={true} />
        <Navigator />
        {IS_DEV_FAT && Boolean(DevShortcut) && <DevShortcut />}
        <TopNotification />
        <FlashMessage duration={3000} position='bottom' style={styles.flashMsgContainer} titleStyle={styles.flashMsgText} />
        <LoadingMask style={styles.loading} />
        <ToastInfo />
      </GestureHandlerRootView>
    </Provider>
  );
};

const mapStateToProps = (state: RootState) => ({
  authToken: selectAuthToken(state),
  disablePushNotification: selectDisablePushNotification(state),
  currentEmployeeId: selectEmployeeId(state),
  isReduxLoaded: selectIsReduxLoaded(state),
  enableTableLayout: selectEnableTableLayout(state),
  tableLayoutEnabled: selectTableLayoutEnabled(state),
  accountExpired: selectAccountExpired(state),
  accountOffline: selectAccountOffline(state),
  ShiftOpenStatus: selectShiftOpenStatus(state),
  operationHours: selectOperationHours(state),
  isMallIntegrationEnabled: selectIsMallIntegrationEnabled(state),
  mallIntegrationChannel: selectMallIntegrationChannel(state),
  autoSignOutCount: selectAutoSignOutCount(state),
  enableViewLocalSettingsOnIST: selectEnableViewLocalSettingsOnIST(state),
  isNewLayout: selectGBProductLayoutRevampEnabled(state),
  networkReachability: selectNetworkReachability(state),
});

const mapDispatchToProps = dispatch => ({
  actions: bindActionCreators(
    {
      checkUpdate,
      setNetInfo,
      syncAppState,
      handleBeepOrderNotification,
      receivedMessageAction,
      setCfdConfigurationBegin,
      updateMasterStateAction,
      rebootMRSAction,
      signOut,
      clearGrowthBook,
      setGrowthBookFeatures,
      findNewPrinters,
      uploadIncompletePrintingImage,
      uploadMemoryInformation,
      uploadLocalDataToS3,
      setNetInfoAction,
    },
    dispatch
  ),
});

const connector = connect(mapStateToProps, mapDispatchToProps);
type PropsFromRedux = ConnectedProps<typeof connector>;
type NavigatorProps = PropsFromRedux;

class NavigatorInner extends React.Component<NavigatorProps> {
  private _printerListener;
  private _incompleteImageListener;
  private _mrsMessageListener;
  private backButtonListener;
  private lastBackButtonPress;
  private canNotExit;
  private _timer;
  private unsubscribeNetInfo: NetInfoSubscription;
  private unsubscribeAppStateInfo: NativeEventSubscription;
  private _scannerListener;
  private inactivityThreshold;
  private inactivityTimer;
  private _notificationEventListener;
  private lastInteractionTime;
  private _growthBookSubscribe;
  private _autoSignOutCount = 'Never';

  constructor(props) {
    super(props);
    this.backButtonListener = null;
    this.lastBackButtonPress = null;
    this.canNotExit = false;
    this.lastInteractionTime = new Date().getTime();
    this.inactivityThreshold = 30000; // 30秒
  }

  async componentDidMount() {
    const {
      authToken,
      actions: { checkUpdate },
    } = this.props;

    // For emulator will catch a .so file missing error.
    PrinterManager.turnOnPower();
    globalConfig.startTime();
    if (isEmpty(authToken)) checkUpdate({ checkInstall: true });
    this._printerListener = PrinterSubscription.addListener('update_printer_list', res => {
      if (res.printers) {
        this.props.actions.findNewPrinters(res);
      }
    });
    this._incompleteImageListener = PrinterSubscription.addListener('upload_incomplete_printing_image', fileName => {
      this.props.actions.uploadMemoryInformation();
      this.props.actions.uploadIncompletePrintingImage(fileName);
    });

    this._mrsMessageListener = WsManagerSubscription.addListener('mrs_message_received', message => {
      if (message.event !== EventType.SERVER_PONG) {
        this.props.actions.receivedMessageAction(message);
      }
    });

    PrinterManager.bindService();
    if (IsAndroid) {
      this.backButtonListener = BackHandler.addEventListener('hardwareBackPress', () => {
        if (this.canNotExit) {
          return false;
        }

        // Press back button within 2 seconds
        if (this.lastBackButtonPress + 2000 >= new Date().getTime()) {
          BackHandler.exitApp();
          return true;
        }

        ToastAndroid.show(t('Press again within 2 seconds will Exit app'), ToastAndroid.SHORT);
        this.lastBackButtonPress = new Date().getTime();

        return true;
      });
    }
    RNLanguages.addEventListener('change', this._onLanguagesChange);

    const code = get(this.props.networkReachability, 'reachabilityCode');
    if (get(this.props.networkReachability, 'useNativeReachability')) {
      NetInfo.configure({
        shouldFetchWiFiSSID: true,
        useNativeReachability: true,
      });
    } else {
      NetInfo.configure({
        reachabilityUrl: get(this.props.networkReachability, 'reachabilityUrl'),
        reachabilityTest: async response => response.status === code,
        reachabilityShouldRun: () => true,
        // The number of milliseconds between internet reachability checks when the internet was previously detected.
        reachabilityLongTimeout: 60 * 1000,
        // The number of milliseconds between internet reachability checks when the internet was not previously detected.
        reachabilityShortTimeout: 5 * 1000,
        // The number of milliseconds that a reachability check is allowed to take before failing.
        reachabilityRequestTimeout: 5 * 1000,
        shouldFetchWiFiSSID: true,
        useNativeReachability: false,
      });
    }

    this.unsubscribeNetInfo = NetInfo.addEventListener(this.netInfoListener);

    this._notificationEventListener = DeviceEventEmitter.addListener('UpdateNotificationEvent', message => {
      this._handleOpenNotification(message);
    });

    PushNotificationIOS.addEventListener('notification', this.handleIOSPushNotification);

    this.unsubscribeAppStateInfo = AppState.addEventListener('change', this._handleAppStateChange);
    this._handleAppStateChange('init');
    this._growthBookSubscribe = eventBus.addListener(UPDATE_GLOBAL_GROWTHBOOK, this._updateGlobalGrowthBook);
  }

  handleIOSPushNotification = notification => {
    const data = notification.getData();
    const alert = notification.getAlert();
    const title = alert['title'];
    const messageID = data['notificationId'];
    const extras = data['data'];
    if (extras) {
      const payload = extras['payload'];
      if (payload) {
        extras['payload'] = JSON.stringify(payload);
        const beepMessage = {
          messageID,
          title,
          extras,
        };
        this._handleOpenNotification(beepMessage);
      }
    }
  };

  _updateGlobalGrowthBook = growthbook => {
    this.props.actions.setGrowthBookFeatures(growthbook);
  };

  componentWillUnmount() {
    this.props.actions.syncAppState('background');
    if (IsAndroid) {
      this.backButtonListener.remove();
    }
    RNLanguages.removeEventListener('change', this._onLanguagesChange);
    this._printerListener.remove();
    this._incompleteImageListener.remove();
    this._mrsMessageListener.remove();
    this._timer && clearTimeout(this._timer);
    logMRSEvent(MRSAction.Set_Up, SetUpFlow.EXIT_APP);
    WsManager.onExitApp();
    Boolean(this.unsubscribeNetInfo) && this.unsubscribeNetInfo();
    this.unsubscribeAppStateInfo.remove();
    this.props.actions.clearGrowthBook();

    this._scannerListener && this._scannerListener.remove();
    this._scannerListener = null;
    this._notificationEventListener && this._notificationEventListener.remove();
    // DeviceEventEmitter.removeAllListeners();
    PrinterSubscription.removeAllListeners('update_printer_list');
    this._growthBookSubscribe && eventBus.remove(this._growthBookSubscribe);
  }

  handleAutoSignOut = () => {
    const { autoSignOutCount } = this.props;
    if (autoSignOutCount === 'Never' || isEmpty(autoSignOutCount)) {
      this._autoSignOutCount = autoSignOutCount;
      this.clearInactivityTimer();
    } else if (autoSignOutCount !== this._autoSignOutCount) {
      this._autoSignOutCount = autoSignOutCount;
      this.clearInactivityTimer();
      const countNumber = Number(autoSignOutCount);
      this.inactivityThreshold = countNumber;
      if (!isValidNumber(countNumber)) {
        return;
      }
      this.inactivityTimer = setInterval(() => {
        const currentTime = new Date().getTime();
        const currentRoute = NavigationService.getCurrentRouteName();

        if (currentRoute === 'SignIn' || currentRoute === 'LockScreen' || currentRoute === 'LoginedRoute') {
          this.resetleInteraction();
        } else if (currentTime - this.lastInteractionTime > this.inactivityThreshold * 1000) {
          this.handleInactivity();
        }
      }, 1000);
    }
  };

  clearInactivityTimer = () => {
    this.inactivityTimer && clearInterval(this.inactivityTimer);
    this.inactivityTimer = null;
  };

  handleInactivity = () => {
    requestAnimationFrame(() => {
      this.props.actions.signOut({ event: 'autoSignOut' });
    });
    this.resetleInteraction();
  };

  resetleInteraction = () => {
    // 用户进行操作时，更新最后交互时间为当前时间
    this.lastInteractionTime = new Date().getTime();
  };

  _handleOpenNotification = message => {
    console.log(IsAndroid, '_handleOpenNotification', message);
    const { disablePushNotification, enableViewLocalSettingsOnIST } = this.props;
    if (disablePushNotification && ENVIRONMENT !== EnvironmentType.Pro) return;
    const payload = JSON.parse(message.extras.payload);
    const eventType = get(payload, 'eventType', '');
    if (eventType === 'POSRemoteDiagnosis') {
      console.log('POSRemoteDiagnosis');
      if (enableViewLocalSettingsOnIST) {
        this.props.actions.uploadLocalDataToS3();
      }
    } else {
      this.props.actions.handleBeepOrderNotification(message);
    }
  };

  _handleAppStateChange = async appState => {
    this.props.actions.syncAppState(appState);
    if (appState === 'active') {
      this.performEOD(appState);
      this.props.actions.setCfdConfigurationBegin();
      NetInfo.refresh();
      eventBus.emit(APP_ACTIVE);
    } else if (appState === 'background') {
      eventBus.emit(APP_BACKGROUND);
    }
  };

  performEOD = appState => {
    const { currentEmployeeId, ShiftOpenStatus, isMallIntegrationEnabled, operationHours, mallIntegrationChannel } = this.props;
    if (
      appState === 'active' &&
      currentEmployeeId &&
      ShiftOpenStatus &&
      isMallIntegrationEnabled &&
      mallIntegrationChannel !== MallIntegrationChannel.AYALA_MALL
    ) {
      const lastShiftNotClosed = isLastShiftNotClosed(operationHours);
      if (lastShiftNotClosed) {
        NavigationService.navigate({
          routeName: 'ModalInfo',
          params: {
            needGoBackWhenSubmit: false,
            info: t('PerformEOD'),
            okText: 'PROCEED',
            textAlign: 'center',
            infoFontWeight: 'bold',
            closeable: false,
            onSubmitHandler: () => {
              NavigationService.goBack();
              NavigationService.navigate({ routeName: 'ModalShiftChange', params: { needEOD: true } });
            },
          },
        });
      }
    }
  };

  netInfoListener = data => {
    this.props.actions.setNetInfoAction(data);
    this.props.actions.rebootMRSAction();
    infoPOSBasicEvent({ action: POSBasicAction.WiFiChanged, privateDataPayload: { ...data }, destination: 'both' });
  };

  _onLanguagesChange = ({ language }) => {
    i18n.locale = language;
  };

  appStackCanNotExit = routes => {
    const appStack = find(routes, route => get(route, 'key') === 'App');

    const combineStack = get(appStack, ['routes', 0]);
    const combineStackIndex = get(combineStack, 'index');
    const currentTabStack = get(combineStack, ['routes', combineStackIndex]);
    const currentTabStackIndex = get(currentTabStack, 'index', 0);
    const mainStackRoutes = get(currentTabStack, 'routes');
    // For pages resident in memory, a { isResident: true } field is appended
    const residentCount = filter(mainStackRoutes, route => Boolean(get(route, 'isResident'))).length;
    let modalScreen;
    if (combineStackIndex > 0) {
      modalScreen = get(combineStack, ['routes', combineStackIndex, 'routeName']);
    }
    this.canNotExit = Boolean(modalScreen) === true || currentTabStackIndex - residentCount > 0;
  };

  authStackCanNotExit = routes => {
    const authStack = find(routes, route => get(route, 'key') === 'Auth');
    const currentStackIndex = get(authStack, 'index');
    const currentStack = get(authStack, ['routes', currentStackIndex]);
    const currentIndex: number = get(currentStack, 'index');
    this.canNotExit = currentStackIndex > 0 || currentIndex >= 0;
  };

  render() {
    const { currentEmployeeId, authToken, isReduxLoaded, enableTableLayout, tableLayoutEnabled, accountExpired, accountOffline, isNewLayout } = this.props;
    this.handleAutoSignOut();
    return (
      <View style={styles.container} onTouchEnd={this.resetleInteraction}>
        <TopOverflowBanner />
        <RootStack
          currentEmployeeId={currentEmployeeId}
          isReduxLoaded={isReduxLoaded}
          authToken={authToken}
          enableTableLayout={enableTableLayout && tableLayoutEnabled}
          accountExpired={accountExpired}
          accountOffline={accountOffline}
          isNewLayout={isNewLayout}
        />
        {Boolean(isReduxLoaded) && (
          <>
            <KDS />
            <NCS />
            <CFD />
          </>
        )}
      </View>
    );
  }
}

const Navigator = connector(NavigatorInner);

const Root = () => {
  return (
    <PushyProvider client={pushyClient}>
      <App />
    </PushyProvider>
  );
};

export default Sentry.wrap(Root);
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loading: {
    ...StyleSheet.absoluteFillObject,
    flexDirection: 'column',
    justifyContent: 'center',
  },
  flashMsgContainer: {
    alignSelf: 'center',
    backgroundColor: 'transparent',
    alignItems: 'center',
  },
  flashMsgText: {
    color: 'white',
    alignSelf: 'center',
    textAlign: 'center',
    textAlignVertical: 'center',
    paddingHorizontal: scaleSizeW(24),
    fontSize: currentThemes.fontSize18,
    fontWeight: '400',
    backgroundColor: '#121838',
    height: scaleSizeH(60),
    minWidth: scaleSizeW(480),
  },
});
