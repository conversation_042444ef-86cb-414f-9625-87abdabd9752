import { find, get, isEmpty, map, pick, throttle } from 'lodash';
import React, { PureComponent } from 'react';
import { AppState, findNodeHandle, ImageBackground, NativeModules, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import Config from 'react-native-config';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import * as Progress from 'react-native-progress';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import ActivateLogo from '../../../assets/icons/svg/activation.svg';
import {
  activate,
  ActivateType,
  HttpAction,
  migrateToNewPOS,
  MigrateToNewPOSType,
  PrinterConfigType,
  registerEmailCheck,
  RegisterEmailCheckType,
  setFreeTrial,
  SetFreeTrialType,
  setSyncInfo,
  SyncInfoType,
  setSequence,
  SetSequenceType,
  setStoreInfo,
  StoreInfoType,
  toggleToastInfo,
  ToggleToastInfoType,
  updateCustomerDisplaySettings,
  UpdateCustomerDisplaySettingsType,
  updateGeneralSettings,
  UpdateGeneralSettingsType,
  updatePrinterGeneralSettings,
  updatePrinterTagsSettings,
  UpdatePrinterTagsSettingsType,
  updateTableLayoutSettings,
} from '../../actions';
import { basicNonEmptyValidator, emailValidator, FormTypes, GeneralTextInput, SubmitButton, SubmitFooter } from '../../components/common';
import TabContainer from '../../components/common/TabContainer';
import { icons } from '../../components/ui';
import IconMigrationError from '../../components/ui/svgIcons/iconMigrationError';
import { EnvironmentType } from '../../config';
import { currentThemes, screenHeight, screenWidth, SharedStyles, SubscriptionStatus, t } from '../../constants';
import { scaleSizeH, scaleSizeW } from '../../constants/themes';
import { selectDevPanelData } from '../../sagas/selector';
import { ScreenProps } from '../../typings';
import { testProps } from '../../utils';
import { stringify } from '../../utils/json';
import { infoPOSBasicEvent, POSBasicAction } from '../../utils/logComponent';
import { logiOSMigrationEvent, MigrationAction } from '../../utils/logComponent/buz/iOSMigration';
import { getParam } from '../../utils/navigation';
import { MixpanelInstance } from '../../utils/Analytics';

const { SHKeyChainManager } = NativeModules;

interface Props extends ScreenProps {
  devPanelData: any;
  actions?: {
    activate: HttpAction<ActivateType>;
    setStoreInfo(payload: StoreInfoType): void;
    setSequence(payload: SetSequenceType): void;
    toggleToastInfo(payload: ToggleToastInfoType): void;
    registerEmailCheck: HttpAction<RegisterEmailCheckType>;
    setFreeTrial(payload: SetFreeTrialType): void;
    setSyncInfo(payload: SyncInfoType): void;
    updateGeneralSettings(payload: UpdateGeneralSettingsType): void;
    migrateToNewPOS(payload: MigrateToNewPOSType): void;
    updateCustomerDisplaySettings(payload: UpdateCustomerDisplaySettingsType): void;
    updatePrinterTagsSettings(payload: UpdatePrinterTagsSettingsType): void;
    updatePrinterGeneralSettings(payload: any): void;
    updateTableLayoutSettings(payload: any): void;
  };
}

interface State {
  business: string;
  email: string;
  pwd: string;
  formValues: object;
  isSuccess: boolean;
  isInMigration?: boolean;
  migrationProgress?: number;
  migrationMeetError?: boolean;
}

const mapStateToProps = state => ({
  devPanelData: selectDevPanelData(state),
});

const mapDispatchToProps = dispatch => ({
  actions: bindActionCreators(
    {
      activate,
      setStoreInfo,
      setSequence,
      toggleToastInfo,
      registerEmailCheck,
      setFreeTrial,
      setSyncInfo,
      updateGeneralSettings,
      migrateToNewPOS,
      updateCustomerDisplaySettings,
      updatePrinterTagsSettings,
      updatePrinterGeneralSettings,
      updateTableLayoutSettings,
    },
    dispatch
  ),
});

export class Activation extends PureComponent<Props, State> {
  static navigationOptions = {
    headerShown: false,
  };

  private activationScrollRef: KeyboardAwareScrollView;
  private _activation_textInputRefs = [];

  constructor(props) {
    super(props);
    this.state = {
      business: '',
      email: '',
      pwd: '',
      isSuccess: false,
      formValues: {},
      migrationProgress: 0.5,
    };
  }

  getKeychainData = async key => {
    try {
      const jsonString = await SHKeyChainManager.getFromKeychain(key);
      const data = JSON.parse(jsonString);
      logiOSMigrationEvent({
        action: MigrationAction.getKeychainData,
        result: 'success',
      });
      return data;
    } catch (error) {
      logiOSMigrationEvent({
        action: MigrationAction.getKeychainData,
        result: 'failed',
        reason: String(error),
      });
      console.log('Error getting keychain data:', error);
      return {};
    }
  };

  removeKeyChainData = async key => {
    try {
      await SHKeyChainManager.deleteFromKeychain(key);
      logiOSMigrationEvent({
        action: MigrationAction.removeKeyChainData,
        result: 'success',
      });
      return true;
    } catch (error) {
      console.log('Error removing keychain data:', error);
      logiOSMigrationEvent({
        action: MigrationAction.removeKeyChainData,
        result: 'failed',
        reason: String(error),
      });
      return false;
    }
  };

  handleAppStateChangeListener = null;

  componentDidMount(): void {
    this.handleAppStateChangeListener = AppState.addEventListener('change', this.handleAppStateChange);
  }

  handleAppStateChange = nextAppState => {
    if (nextAppState === 'active') {
      this.tryToMigrateFromOldPOS();
    }
  };

  componentWillUnmount() {
    this.handleAppStateChangeListener && this.handleAppStateChangeListener.remove();
  }

  tryToMigrateFromOldPOS = throttle(
    () => {
      this.getKeychainData('SHKeyChain').then(data => {
        if (!isEmpty(data)) {
          console.log('get local data success:', data);
          this.setState({ isInMigration: true });

          const token = get(data, 'token', '');
          const business = get(data, 'business', '');
          const registerId = get(data, 'registerId', '');

          const autoSyncAfterOpenShift = get(data, 'autoSyncAfterOpenShift', true);
          const enableOpenOrder = Boolean(get(data, 'enableOpenOrder', false));
          const addShortcutButtonToCheckout = get(data, 'addShortcutButtonToCheckout', false);
          const isCustomerDisplayOn = get(data, 'isCustomerDisplayOn', false);
          const autoSignOutIndex = get(data, 'autoSignOutIndex', 5); // default is 'Never'
          const printers = get(data, 'printers', []) as PrinterConfigType[];
          const alwaysPrintReceiptFromIOS = get(data, 'alwaysPrintReceipt', false);
          const enableTableLayout = get(data, 'enableTableLayout', false);

          this.props.actions.updatePrinterTagsSettings(printers);

          const onSuccess = data => {
            logiOSMigrationEvent({
              action: MigrationAction.callMigrateAPI,
              result: 'success',
              reason: '',
              privateDataPayload: { business, registerId },
            });
            MixpanelInstance.track('iOS Migration', {
              result: 'in_progress',
              reason: 'callMigrateAPI',
              business: business,
              registerId: registerId,
            });
            this.removeKeyChainData('SHKeyChain').then(res => {
              if (res) {
                console.log('delete local data success');
                this.migrationSuccess();
                logiOSMigrationEvent({
                  action: MigrationAction.migrationSuccess,
                  result: 'success',
                  reason: '',
                  privateDataPayload: { business, registerId },
                });
                MixpanelInstance.track('iOS Migration', {
                  result: 'success',
                  reason: '',
                  business: business,
                  registerId: registerId,
                });
                // Save store , pad register info for offline usage
                this.props.actions.setStoreInfo(data);
                const { receiptNumberStart, receiptDateStart, invoiceNumberStart, subscriptionStatus } = data;
                this.props.actions.setSequence({ receiptNumberStart, receiptDateStart, invoiceNumberStart, sourceForLog: 'activation' });
                this.props.actions.setFreeTrial(subscriptionStatus === SubscriptionStatus.Trial);
                // Jump to Employee Sign in

                const autoSignOutValue = this.mapAutoSignOutIndexToValue(autoSignOutIndex);

                this.props.actions.updateGeneralSettings({
                  autoSyncAfterOpenShift: autoSyncAfterOpenShift,
                  enableOpenOrders: enableOpenOrder,
                  autoSignOutCount: autoSignOutValue,
                });
                this.props.actions.updateGeneralSettings({
                  enableOpenOrdersShortCut: addShortcutButtonToCheckout,
                });
                this.props.actions.updateCustomerDisplaySettings({
                  enableCustomerDisplay: isCustomerDisplayOn,
                });
                this.props.actions.updatePrinterGeneralSettings({
                  alwaysPrintReceipt: alwaysPrintReceiptFromIOS,
                  alwaysPrintReceiptWithBeepQRPayOnline: alwaysPrintReceiptFromIOS,
                  alwaysPrintReceiptWithBeepQRPayAtCounter: alwaysPrintReceiptFromIOS,
                  alwaysPrintReceiptWithOnlineDelivery: alwaysPrintReceiptFromIOS,
                });
                this.props.actions.updateTableLayoutSettings({ enableTableLayout: enableTableLayout });
              } else {
                this.migrationFailure();
                logiOSMigrationEvent({
                  action: MigrationAction.migrationFailure,
                  result: 'failed',
                  reason: 'delete local data failed',
                  privateDataPayload: { business, registerId },
                });
                MixpanelInstance.track('iOS Migration', {
                  result: 'failed',
                  reason: 'delete local data failed',
                  business: business,
                  registerId: registerId,
                });
              }
            });
          };

          const onFailure = data => {
            this.migrationFailure();
            logiOSMigrationEvent({
              action: MigrationAction.migrationFailure,
              result: 'failed',
              reason: 'callMigrateAPI',
              privateDataPayload: {
                errorMessage: JSON.stringify(data),
                business,
                registerId,
              },
            });
            MixpanelInstance.track('iOS Migration', {
              result: 'failed',
              reason: 'callMigrateAPI',
              business: business,
              registerId: registerId,
            });
            this.props.actions.updatePrinterTagsSettings([]);
          };

          this.props.actions.migrateToNewPOS({ onSuccess, onFailure, token, business, registerId });
        } else {
          console.log('no keychain data');
          this.setState({ isInMigration: false });
        }
      });
    },
    1000,
    { leading: true, trailing: false }
  );

  mapAutoSignOutIndexToValue = index => {
    switch (index) {
      case 0:
        return '30';
      case 1:
        return '60';
      case 2:
        return '120';
      case 3:
        return '300';
      case 4:
        return '600';
      case 5:
        return 'Never';
      default:
        return 'Never';
    }
  };

  migrationSuccess = () => {
    this.setState({ migrationProgress: 1 });
  };

  migrationFailure = () => {
    this.setState({ migrationMeetError: true });
  };

  UNSAFE_componentWillReceiveProps(newProps) {
    const isSuccessNew = getParam(newProps, 'isSuccess', false);
    const { isSuccess } = this.state;
    isSuccessNew !== isSuccess && this.setState({ isSuccess: isSuccessNew });

    const isFAT = Config.ENVIRONMENT === EnvironmentType.Fat || Config.ENVIRONMENT === EnvironmentType.Debug;
    if (isFAT) {
      const { devPanelData } = newProps;
      if (!isEmpty(devPanelData)) {
        const { bn, eml, pwd } = devPanelData;
        this.setState({
          formValues: {
            activation_store_name: bn,
            activation_email: eml,
            activation_password: pwd,
          },
        });
      }
    }
  }

  userClickedLogin = () => {
    const { formValues } = this.state;
    const errors = map(this._activation_textInputRefs, ref => {
      return ref ? ref.validate() : '';
    });

    const error = find(errors, error => {
      return Boolean(error);
    });

    if (error) {
      this.props.actions.toggleToastInfo({ text: `Error: ${error}`, visible: true });
      return;
    }

    const business = get(formValues, 'activation_store_name');
    const email = get(formValues, 'activation_email');
    const password = get(formValues, 'activation_password');

    const onSuccess = {
      callback: payload => {
        // Save store , pad register info for offline usage
        const now = new Date().toISOString();
        payload.res.activatedTime = now;
        this.props.actions.setStoreInfo(payload.res);
        const { receiptNumberStart, receiptDateStart, invoiceNumberStart, subscriptionStatus } = payload.res;

        // remove this from CM-4646
        // Auto toglle on the settings below for new on-board users.
        // const effectiveDate = moment('2023-08-31T00:00:00.000Z'); // 31 Aug 2023
        // if (Boolean(createdTime) && moment(createdTime) > effectiveDate) {
        //   this.props.actions.updateGeneralSettings({ enableCustomerQR: true, enableCustomerShortCut: true });
        // }
        this.props.actions.setSequence({ receiptNumberStart, receiptDateStart, invoiceNumberStart, sourceForLog: 'activation' });
        this.props.actions.setFreeTrial(subscriptionStatus === SubscriptionStatus.Trial);
        this.props.actions.setSyncInfo({
          lastTrxCancelledFromBOSyncTime: now,
        });
        infoPOSBasicEvent({
          action: POSBasicAction.ACTIVATE,
          privateDataPayload: { message: stringify(pick(payload.res, ['registerId', 'registerObjectId', 'name'])) },
        });
        // Jump to Employee Sign in
      },
    };

    const onFailure = {
      callback: payload => {
        if (payload.status == 300) {
          const selectCallback = index => {
            if (index === undefined || index === null) return;
            const register = payload.error[index];

            this.props.actions.activate({
              business,
              email,
              password,
              code: register.code,
              onSuccess,
              onFailure,
            });
          };
          this.props.navigation.navigate('RegisterSelectModal', { data: payload.error, selectCallback });
        } else if (payload && payload.message) {
          this.props.navigation.navigate('ModalInfo', {
            title: payload.message,
            isShowTitle: true,
            notShowInfo: true,
          });
        }
      },
    };
    this.props.actions.activate({
      business,
      email,
      password,
      onSuccess,
      onFailure,
    });
  };

  onSubmitEditing = index => {
    const inputRef = get(this._activation_textInputRefs, index + 1);

    if (inputRef && inputRef.focus) {
      inputRef.focus();
    }
    inputRef && this.activationScrollRef && this.activationScrollRef.scrollToFocusedInput(findNodeHandle(inputRef));
  };

  refHandler = (ref, index) => {
    this._activation_textInputRefs[index] = ref;
  };

  onChangeHandler = formValues => {
    this.setState(prevState => {
      return {
        formValues: Object.assign({}, prevState.formValues, formValues),
      };
    });
  };

  onChangeText = (fieldId: string, newText: string) => {
    const newFormValues = { [fieldId]: newText };
    this.onChangeHandler(newFormValues);
  };

  renderFormInput = (formValues, inputConfig, index) => {
    return (
      <GeneralTextInput
        key={index}
        txd={inputConfig.id}
        ref={ref => this.refHandler(ref, index)}
        style={inputConfig.style}
        inputTextStyle={inputConfig.inputTextStyle}
        contentStyle={styles.inputContent}
        showLabel={inputConfig.label}
        showLabelStyle={{ fontSize: currentThemes.fontSize18, fontWeight: '400' }}
        required={inputConfig.required}
        validator={inputConfig.validator}
        keyboardType={inputConfig.keyboardType || 'default'}
        placeholder={inputConfig.placeholder}
        placeholderTextColor={'#303030'}
        returnKeyType={inputConfig.returnKeyType || 'done'}
        onChangeText={newText => this.onChangeText(inputConfig.id, newText)}
        defaultValue={get(formValues, inputConfig.id)}
        value={get(formValues, inputConfig.id)}
        onSubmitEditing={() => this.onSubmitEditing(index)}
        secureTextEntry={inputConfig.secureTextEntry || false}
        createAdditionalButton={inputConfig.createAdditionalButton}
      />
    );
  };

  renderContinueButton = (isSuccess, onClickHanlder, disabled = false) => {
    return (
      <SubmitFooter style={[styles.submitButton, isSuccess && { marginTop: scaleSizeH(130) }]}>
        <SubmitButton
          disabled={disabled}
          accessibilityLabel='al_continue'
          style={{ marginHorizontal: 0, borderRadius: 4 }}
          textStyle={{ fontSize: currentThemes.fontSize30, fontWeight: 'bold' }}
          onPress={onClickHanlder}
        >
          {!isSuccess ? t('Continue') : t('Let Go')}
        </SubmitButton>
      </SubmitFooter>
    );
  };

  renderActivateRegister = () => {
    const { formValues } = this.state;
    const btnDisabled =
      isEmpty(get(formValues, ACTIVATION_FORM[0].id)) || isEmpty(get(formValues, ACTIVATION_FORM[1].id)) || isEmpty(get(formValues, ACTIVATION_FORM[2].id));

    return (
      <View style={styles.tab}>
        <TabContainer style={SharedStyles.column} tabLabel={t('ACTIVATE REGISTER')}>
          <KeyboardAwareScrollView
            ref={ref => (this.activationScrollRef = ref)}
            style={styles.keyboardAvoidView}
            contentContainerStyle={{
              alignItems: 'center',
              paddingTop: scaleSizeH(48),
            }}
            enableOnAndroid
            extraHeight={scaleSizeH(380)}
            keyboardOpeningTime={50}
            keyboardDismissMode='none'
            keyboardShouldPersistTaps='never'
            horizontal={false}
          >
            <ActivateLogo width={scaleSizeW(360)} height={scaleSizeH(240)} />
            <Text style={styles.formTitle}> {t('Activate your register')} </Text>

            {this.renderFormInput(formValues, ACTIVATION_FORM[0], 0)}
            <View style={styles.inputRowContainer}>
              {this.renderFormInput(formValues, ACTIVATION_FORM[1], 1)}
              {this.renderFormInput(formValues, ACTIVATION_FORM[2], 2)}
            </View>
            {this.renderContinueButton(false, this.userClickedLogin, btnDisabled)}
          </KeyboardAwareScrollView>
        </TabContainer>
      </View>
    );
  };

  renderMigrationProcess = () => {
    const { migrationProgress } = this.state;
    const progress = migrationProgress * 100;
    return (
      <View>
        <Text style={styles.progressLabel}>{`Migrating: ${progress}%`}</Text>
        <Progress.Bar progress={migrationProgress} width={350} color={'#FC7118'} unfilledColor={'#E0E0E4'} borderColor={'#E0E0E4'} />
      </View>
    );
  };

  throttledSetState = throttle(
    () => {
      this.setState({ migrationMeetError: false });
    },
    1000,
    { leading: true, trailing: false }
  );

  tryAgain = () => {
    this.throttledSetState();
    this.tryToMigrateFromOldPOS();
  };

  activateManually = () => {
    this.setState({ migrationMeetError: false, isInMigration: false });
    this.removeKeyChainData('SHKeyChain');
  };

  renderErrorPage = () => {
    return (
      <View style={styles.errorContainer}>
        <View style={styles.errorView}>
          <IconMigrationError width={scaleSizeW(101)} height={scaleSizeH(101)} style={styles.icon} />
          <Text style={styles.errorTitle}>There is an error while migrating to the new app</Text>
          <Text style={styles.errorDes}>Please try again or activate it manually</Text>
          <TouchableOpacity {...testProps('al_btn_77')} style={styles.errorBtn} onPress={this.tryAgain}>
            <Text style={styles.errorBtnTitle}>TRY AGAIN</Text>
          </TouchableOpacity>
          <TouchableOpacity {...testProps('al_btn_335')} onPress={this.activateManually}>
            <Text style={styles.manuallyBtnTitle}>ACTIVATE MANUALLY</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function
  goDeviceHome = () => NativeModules.RNHomeModule.goBackHome();

  render() {
    const { isInMigration, migrationMeetError } = this.state;
    return (
      <ImageBackground source={icons.backdrop} style={styles.container}>
        {migrationMeetError ? this.renderErrorPage() : isInMigration ? this.renderMigrationProcess() : this.renderActivateRegister()}
      </ImageBackground>
    );
  }
}

const PADDING_HORIZONTAL = scaleSizeW(90);

export default connect(mapStateToProps, mapDispatchToProps)(Activation);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#262626',
    alignItems: 'center',
    justifyContent: 'center',
  },
  tab: {
    marginTop: scaleSizeH(80),
    marginBottom: scaleSizeH(37),
    marginHorizontal: scaleSizeW(420),
    // width: scaleSizeW(1080),
    height: scaleSizeH(928),
    overflow: 'hidden',
    borderRadius: scaleSizeW(12),
  },
  keyboardAvoidView: {
    flex: 1,
    width: '100%',
    backgroundColor: 'white',
  },
  formTitle: {
    color: '#303030',
    fontSize: currentThemes.fontSize38,
    fontWeight: '500',
  },
  inputStyle: {
    width: '100%',
    marginTop: scaleSizeH(52),
    paddingHorizontal: PADDING_HORIZONTAL,
  },
  inLineStyle: {
    width: scaleSizeW(436),
    marginTop: scaleSizeH(52),
  },
  inputContent: {
    paddingHorizontal: scaleSizeW(16),
    width: '100%',
    // height: scaleSizeH(72),
    // paddingHorizontal: scaleSizeW(16),
    borderRadius: scaleSizeW(8),
    borderWidth: scaleSizeH(1),
    borderColor: '#E0E0E4',
    alignItems: 'center',
  },
  inputTextStyle: {
    fontSize: currentThemes.fontSize18,
    color: '#393939',
    paddingVertical: 0,
    alignItems: 'center',
    height: scaleSizeH(72),
  },
  inputRowContainer: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: PADDING_HORIZONTAL,
  },
  submitButton: {
    width: '100%',
    height: scaleSizeH(112),
    paddingHorizontal: scaleSizeW(90),
    paddingVertical: 0,
    marginTop: scaleSizeH(52),
    borderRadius: scaleSizeW(8),
  },
  normalText: {
    fontSize: currentThemes.fontSize18,
    color: '#303030',
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorView: {
    width: screenWidth * 0.8,
    height: screenHeight * 0.5,
    backgroundColor: 'white',
    borderRadius: scaleSizeW(8),
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorTitle: {
    color: 'black',
    fontSize: currentThemes.fontSize32,
    fontWeight: '500',
    marginTop: scaleSizeH(10),
  },
  errorDes: {
    color: '#60636B',
    fontSize: currentThemes.fontSize28,
    marginTop: scaleSizeH(20),
  },
  errorBtn: {
    width: scaleSizeW(807),
    height: scaleSizeH(90),
    backgroundColor: '#FC7118',
    borderRadius: scaleSizeW(8),
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: scaleSizeH(120),
    marginBottom: scaleSizeH(40),
  },
  errorBtnTitle: {
    color: 'white',
    fontSize: currentThemes.fontSize28,
    fontWeight: '500',
  },
  manuallyBtnTitle: {
    color: '#00B0FF',
    fontSize: currentThemes.fontSize28,
    fontWeight: '500',
  },
  icon: {
    marginTop: scaleSizeH(-50),
  },
  progressLabel: {
    color: 'white',
    fontSize: currentThemes.fontSize24,
    textAlign: 'center',
    marginBottom: scaleSizeH(20),
  },
});

const ACTIVATION_FORM = [
  {
    id: 'activation_store_name',
    required: true,
    label: t('STORE NAME'),
    placeholder: t('yourstorename'),
    validator: basicNonEmptyValidator,
    secureTextEntry: false,
    type: FormTypes.TEXTINPUT,
    style: styles.inputStyle,
    inputTextStyle: styles.inputTextStyle,
    createAdditionalButton: () => <Text style={styles.normalText}>{'.storehubhq.com'}</Text>,
  },
  {
    id: 'activation_email',
    required: true,
    label: t('EMAIL'),
    placeholder: t('natalie email'),
    validator: [basicNonEmptyValidator, emailValidator],
    secureTextEntry: false,
    style: styles.inLineStyle,
    type: FormTypes.TEXTINPUT,
    inputTextStyle: styles.inputTextStyle,
  },
  {
    id: 'activation_password',
    required: true,
    label: t('Password').toUpperCase(),
    placeholder: '',
    validator: basicNonEmptyValidator,
    secureTextEntry: true,
    style: styles.inLineStyle,
    type: FormTypes.TEXTINPUT,
    inputTextStyle: styles.inputTextStyle,
  },
];
