import { find, get, isEmpty, map, pick, throttle } from 'lodash';
import React, { PureComponent } from 'react';
import { ImageBackground, NativeModules, Platform, StatusBar, StyleSheet, Text, TouchableOpacity, View, findNodeHandle } from 'react-native';
import Config from 'react-native-config';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import ScrollableTabView, { ScrollableTabBar } from 'react-native-scrollable-tab-view';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import ActivateLogo from '../../../assets/icons/svg/activation.svg';
import {
  ActivateType,
  HttpAction,
  RegisterEmailCheckType,
  SetFreeTrialType,
  SetSequenceType,
  StoreInfoType,
  SyncInfoType,
  ToggleToastInfoType,
  UpdateGeneralSettingsType,
  activate,
  registerEmailCheck,
  setFreeTrial,
  setSequence,
  setStoreInfo,
  setSyncInfo,
  toggleToastInfo,
  updateGeneralSettings,
} from '../../actions';
import { FormTypes, GeneralTextInput, SubmitButton, SubmitFooter, basicNonEmptyValidator, emailValidator } from '../../components/common';
import TabContainer from '../../components/common/TabContainer';
import { IconExitApp, Rocket, icons } from '../../components/ui';
import { EnvironmentType } from '../../config';
import { SharedStyles, SubscriptionStatus, currentThemes, height, isBaPingApp, t } from '../../constants';
import { scaleSizeH, scaleSizeW } from '../../constants/themes';
import { selectDevPanelData } from '../../sagas/selector';
import { ScreenProps } from '../../typings';
import { testProps } from '../../utils';
import { stringify } from '../../utils/json';
import { POSBasicAction, infoPOSBasicEvent } from '../../utils/logComponent';
import { getParam } from '../../utils/navigation';
interface Props extends ScreenProps {
  devPanelData: any;
  actions?: {
    activate: HttpAction<ActivateType>;
    setStoreInfo(payload: StoreInfoType): void;
    setSequence(payload: SetSequenceType): void;
    toggleToastInfo(payload: ToggleToastInfoType): void;
    registerEmailCheck: HttpAction<RegisterEmailCheckType>;
    setFreeTrial(payload: SetFreeTrialType): void;
    setSyncInfo(payload: SyncInfoType): void;
    updateGeneralSettings(payload: UpdateGeneralSettingsType): void;
  };
}

interface State {
  business: string;
  email: string;
  pwd: string;
  formValues: object;
  isSuccess: boolean;
}

const mapStateToProps = state => ({
  devPanelData: selectDevPanelData(state),
});

const mapDispatchToProps = dispatch => ({
  actions: bindActionCreators(
    {
      activate,
      setStoreInfo,
      setSequence,
      toggleToastInfo,
      registerEmailCheck,
      setFreeTrial,
      setSyncInfo,
      updateGeneralSettings,
    },
    dispatch
  ),
});

export class Activation extends PureComponent<Props, State> {
  static navigationOptions = {
    headerShown: false,
  };

  private _register_scroll_ref: KeyboardAwareScrollView;
  private _register_textInputRefs = [];

  private _activation_scroll_ref: KeyboardAwareScrollView;
  private _activation_textInputRefs = [];

  constructor(props) {
    super(props);
    this.state = {
      business: '',
      email: '',
      pwd: '',
      isSuccess: false,
      formValues: {},
    };
  }

  UNSAFE_componentWillReceiveProps(newProps) {
    const isSuccessNew = getParam(newProps, 'isSuccess', false);
    const { isSuccess } = this.state;
    isSuccessNew !== isSuccess && this.setState({ isSuccess: isSuccessNew });

    const isFAT = Config.ENVIRONMENT === EnvironmentType.Fat || Config.ENVIRONMENT === EnvironmentType.Debug;
    if (isFAT) {
      const { devPanelData } = newProps;
      if (!isEmpty(devPanelData)) {
        const { bn, eml, pwd } = devPanelData;
        this.setState({
          formValues: {
            activation_store_name: bn,
            activation_email: eml,
            activation_password: pwd,
          },
        });
      }
    }
  }

  userClickedLogin = () => {
    console.log('  userClickedLogin ');
    const { formValues } = this.state;
    const errors = map(this._activation_textInputRefs, ref => {
      return ref ? ref.validate() : '';
    });

    const error = find(errors, error => {
      return Boolean(error);
    });

    if (error) {
      this.props.actions.toggleToastInfo({ text: `Error: ${error}`, visible: true });
      return;
    }

    const business = get(formValues, 'activation_store_name');
    const email = get(formValues, 'activation_email');
    const password = get(formValues, 'activation_password');

    const onSuccess = {
      callback: payload => {
        // Save store , pad register info for offline usage
        const now = new Date().toISOString();
        payload.res.activatedTime = now;
        this.props.actions.setStoreInfo(payload.res);
        const { receiptNumberStart, receiptDateStart, invoiceNumberStart, subscriptionStatus } = payload.res;

        // remove this from CM-4646
        // Auto toglle on the settings below for new on-board users.
        // const effectiveDate = moment('2023-08-31T00:00:00.000Z'); // 31 Aug 2023
        // if (Boolean(createdTime) && moment(createdTime) > effectiveDate) {
        //   this.props.actions.updateGeneralSettings({ enableCustomerQR: true, enableCustomerShortCut: true });
        // }
        this.props.actions.setSequence({ receiptNumberStart, receiptDateStart, invoiceNumberStart, sourceForLog: 'activation' });
        this.props.actions.setFreeTrial(subscriptionStatus === SubscriptionStatus.Trial);
        this.props.actions.setSyncInfo({
          lastTrxCancelledFromBOSyncTime: now,
        });
        infoPOSBasicEvent({
          action: POSBasicAction.ACTIVATE,
          privateDataPayload: { message: stringify(pick(payload.res, ['registerId', 'registerObjectId', 'name'])) },
        });
        // Jump to Employee Sign in
      },
    };

    const onFailure = {
      callback: payload => {
        if (payload.status == 300) {
          const selectCallback = index => {
            if (index === undefined || index === null) return;
            const register = payload.error[index];

            this.props.actions.activate({
              business,
              email,
              password,
              code: register.code,
              onSuccess,
              onFailure,
            });
          };
          this.props.navigation.navigate('RegisterSelectModal', { data: payload.error, selectCallback });
        } else if (payload && payload.message) {
          this.props.navigation.navigate('ModalInfo', {
            title: payload.message,
            isShowTitle: true,
            notShowInfo: true,
          });
        }
      },
    };
    this.props.actions.activate({
      business,
      email,
      password,
      onSuccess,
      onFailure,
    });
  };

  onSubmitEditing = (index, field, pageNo) => {
    let inputRef;
    if (pageNo === 0) {
      inputRef = get(this._register_textInputRefs, index + 1);
    } else {
      inputRef = get(this._activation_textInputRefs, index + 1);
    }

    if (inputRef && inputRef.focus) {
      inputRef.focus();
    }
    if (pageNo === 0) {
      inputRef && this._register_scroll_ref && this._register_scroll_ref.scrollToFocusedInput(findNodeHandle(inputRef));
    } else {
      inputRef && this._activation_scroll_ref && this._activation_scroll_ref.scrollToFocusedInput(findNodeHandle(inputRef));
    }
  };

  refHandler = (ref, index, key, pageNo) => {
    if (pageNo === 0) {
      this._register_textInputRefs[index] = ref;
    } else {
      this._activation_textInputRefs[index] = ref;
    }
  };

  onChangeHandler = formValues => {
    this.setState(prevState => {
      return {
        formValues: Object.assign({}, prevState.formValues, formValues),
      };
    });
  };

  onChangeText = (fieldId: string, newText: string) => {
    const newFormValues = { [fieldId]: newText };
    this.onChangeHandler(newFormValues);
  };

  renderFormInput = (formValues, inputConfig, index, pageNo) => {
    return (
      <GeneralTextInput
        key={index}
        txd={inputConfig.id}
        ref={ref => this.refHandler(ref, index, inputConfig.placeholder, pageNo)}
        style={inputConfig.style}
        inputTextStyle={inputConfig.inputTextStyle}
        contentStyle={styles.inputContent}
        showLabel={inputConfig.label}
        showLabelStyle={{ fontSize: currentThemes.fontSize18, fontWeight: '400' }}
        required={inputConfig.required}
        validator={inputConfig.validator}
        keyboardType={inputConfig.keyboardType || 'default'}
        placeholder={inputConfig.placeholder}
        placeholderTextColor={'#303030'}
        returnKeyType={inputConfig.returnKeyType || 'done'}
        onChangeText={newText => this.onChangeText(inputConfig.id, newText)}
        defaultValue={get(formValues, inputConfig.id)}
        value={get(formValues, inputConfig.id)}
        onSubmitEditing={() => this.onSubmitEditing(index, inputConfig, pageNo)}
        secureTextEntry={inputConfig.secureTextEntry || false}
        createAdditionalButton={inputConfig.createAdditionalButton}
      />
    );
  };

  onRegisterContinueHandler = throttle(
    () => {
      const { formValues } = this.state;
      const errors = map(this._register_textInputRefs, ref => {
        return ref ? ref.validate() : '';
      });

      const error = find(errors, error => {
        return Boolean(error);
      });

      if (error) {
        this.props.actions.toggleToastInfo({ text: `Error: ${error}`, visible: true });
        return;
      }

      const registerFirstName = get(formValues, 'register_first_name');
      const registerLastName = get(formValues, 'register_last_name');
      const registerEmail = get(formValues, 'register_email');

      const onSuccess = {
        callback: payload => {
          const result = get(payload, ['res', 'result']);
          if (result) {
            this.props.navigation.navigate('FreeTrialAccount', { registerFirstName, registerLastName, registerEmail });
          }
        },
      };

      this.props.actions.registerEmailCheck({ email: registerEmail, onSuccess });
    },
    1000,
    { leading: true, trailing: true }
  );

  renderWelcome = () => {
    return (
      <TabContainer style={SharedStyles.column} tabLabel={t('WELCOME')}>
        <KeyboardAwareScrollView
          style={styles.keyboardAvoidView}
          contentContainerStyle={{
            alignItems: 'center',
          }}
          enableOnAndroid
          keyboardOpeningTime={50}
          keyboardDismissMode='none'
          keyboardShouldPersistTaps='never'
          horizontal={false}
        >
          <Rocket width={scaleSizeW(360)} height={scaleSizeH(240)} />
          <Text style={[styles.formTitle, { textAlign: 'center', marginHorizontal: scaleSizeW(150) }]}>
            {t('Welcome aboard the rocket to awesome business')}
          </Text>
          {this.renderContinueButton(true, this.onSuccessHandler)}
        </KeyboardAwareScrollView>
      </TabContainer>
    );
  };

  onSuccessHandler = () => null;

  renderFreeTrial = () => {
    const { formValues } = this.state;
    const btnDisabled = isEmpty(get(formValues, REGISTER_FORM[0].id)) || isEmpty(get(formValues, REGISTER_FORM[2].id));
    return (
      <TabContainer style={SharedStyles.column} tabLabel={t('14-DAY FREE TRIAL')}>
        <KeyboardAwareScrollView
          style={styles.keyboardAvoidView}
          contentContainerStyle={{
            alignItems: 'center',
          }}
          enableOnAndroid
          keyboardOpeningTime={50}
          keyboardDismissMode='none'
          keyboardShouldPersistTaps='never'
          horizontal={false}
        >
          <Rocket width={scaleSizeW(360)} height={scaleSizeH(240)} />
          <Text style={styles.formTitle}>{t('Get unlimited access to all features')}</Text>
          {/* <View style={styles.inputRowContainer}>
            {this.renderFormInput(formValues, REGISTER_FORM[1], 1, 0)}
          </View> */}
          {this.renderFormInput(formValues, REGISTER_FORM[0], 0, 0)}
          {this.renderFormInput(formValues, REGISTER_FORM[2], 2, 0)}

          {this.renderContinueButton(false, this.onRegisterContinueHandler, btnDisabled)}
          {/* {this.renderTryDemo()} */}
        </KeyboardAwareScrollView>
      </TabContainer>
    );
  };

  renderTryDemo = () => {
    return (
      <View style={[SharedStyles.rowCenter, { marginVertical: scaleSizeH(15) }]}>
        <Text style={styles.signupText}>{t('Sign up later')}</Text>
        <Text style={[styles.signupText, { color: '#00B0FF' }]}>{t('Try demo first')}</Text>
      </View>
    );
  };

  renderContinueButton = (isSuccess, onClickHanlder, disabled = false) => {
    return (
      <SubmitFooter style={[styles.submitButton, isSuccess && { marginTop: scaleSizeH(130) }]}>
        <SubmitButton
          disabled={disabled}
          accessibilityLabel='al_continue'
          style={{ marginHorizontal: 0, borderRadius: 4 }}
          textStyle={{ fontSize: currentThemes.fontSize30, fontWeight: 'bold' }}
          onPress={onClickHanlder}
        >
          {!isSuccess ? t('Continue') : t('Let Go')}
        </SubmitButton>
      </SubmitFooter>
    );
  };

  renderActivateRegister = () => {
    const { formValues } = this.state;
    const btnDisabled =
      isEmpty(get(formValues, ACTIVATION_FORM[0].id)) || isEmpty(get(formValues, ACTIVATION_FORM[1].id)) || isEmpty(get(formValues, ACTIVATION_FORM[2].id));

    return (
      <TabContainer style={SharedStyles.column} tabLabel={t('ACTIVATE REGISTER')}>
        <KeyboardAwareScrollView
          style={styles.keyboardAvoidView}
          contentContainerStyle={{
            alignItems: 'center',
            paddingTop: 20,
          }}
          enableOnAndroid
          extraHeight={Platform.OS === 'ios' ? scaleSizeH(300) : scaleSizeH(75)}
          keyboardOpeningTime={50}
          keyboardDismissMode='none'
          keyboardShouldPersistTaps='never'
          horizontal={false}
        >
          <ActivateLogo width={scaleSizeW(360)} height={scaleSizeH(240)} />
          <Text style={styles.formTitle}> {t('Activate your register')} </Text>

          {this.renderFormInput(formValues, ACTIVATION_FORM[0], 0, 1)}
          <View style={styles.inputRowContainer}>
            {this.renderFormInput(formValues, ACTIVATION_FORM[1], 1, 1)}
            {this.renderFormInput(formValues, ACTIVATION_FORM[2], 2, 1)}
          </View>
          {this.renderContinueButton(false, this.userClickedLogin, btnDisabled)}
        </KeyboardAwareScrollView>
      </TabContainer>
    );
  };

  renderTabBar = () => {
    return (
      <ScrollableTabBar
        style={carouselTabStyles.scrollableTabBarStyle}
        tabsContainerStyle={carouselTabStyles.tabsContainerStyle}
        renderTab={(id, page, isTabActive, onPressHandler, onLayoutHandler) => {
          const textStyle = isTabActive ? carouselTabStyles.textActiveStyle : carouselTabStyles.textInactiveStyle;
          return (
            <TouchableOpacity
              {...testProps(`al_tab_${id}`)}
              style={carouselTabStyles.tabBaseStyle}
              key={`scrollable_tab_button_${page}`}
              onPress={() => onPressHandler(page)}
              onLayout={onLayoutHandler}
              activeOpacity={1}
            >
              <Text style={textStyle}>{id}</Text>
            </TouchableOpacity>
          );
        }}
      />
    );
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function
  onChangeTabHandler = ({ i }) => {};

  goDeviceHome = () => NativeModules.RNHomeModule.goBackHome();

  render() {
    const { isSuccess } = this.state;
    const exitText = t('Exit App');
    return (
      <ImageBackground source={icons.backdrop} style={styles.container}>
        <StatusBar barStyle='dark-content' />
        <View style={styles.tab}>
          <ScrollableTabView
            style={carouselTabStyles.tabView}
            initialPage={0}
            renderTabBar={this.renderTabBar}
            tabBarUnderlineStyle={carouselTabStyles.tabBarUnderlineStyle}
            tabBarPosition='top'
            onChangeTab={this.onChangeTabHandler}
          >
            {isSuccess ? this.renderWelcome() : this.renderFreeTrial()}
            {this.renderActivateRegister()}
          </ScrollableTabView>
        </View>
        {isBaPingApp && (
          <TouchableOpacity {...testProps('al_btn_771')} onPress={this.goDeviceHome} style={styles.exitContainer}>
            <View style={styles.exitItemContainer}>
              <IconExitApp width={scaleSizeW(40)} height={scaleSizeH(40)} color={'#FFFFFF'} />
              <Text style={styles.exitText}>{exitText}</Text>
            </View>
          </TouchableOpacity>
        )}
      </ImageBackground>
    );
  }

  componentWillUnmount() {
    this.onRegisterContinueHandler.cancel();
  }
}

const TAB_BAR_HEIGHT = scaleSizeH(100);
const PADDING_HORIZONTAL = scaleSizeW(90);

export default connect(mapStateToProps, mapDispatchToProps)(Activation);

const tabWidth = scaleSizeW(1080);
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#262626',
    alignItems: 'center',
    justifyContent: 'center',
  },
  tab: {
    marginTop: height - scaleSizeH(928),
    backgroundColor: 'white',
    width: tabWidth,
    height: scaleSizeH(928),
    overflow: 'hidden',
  },
  keyboardAvoidView: {
    flex: 1,
    width: '100%',
    backgroundColor: 'white',
  },
  formTitle: {
    color: '#303030',
    fontSize: currentThemes.fontSize38,
    fontWeight: '500',
  },
  inputStyle: {
    width: '100%',
    marginTop: scaleSizeH(52),
    paddingHorizontal: PADDING_HORIZONTAL,
  },
  inLineStyle: {
    width: scaleSizeW(436),
    marginTop: scaleSizeH(52),
  },
  inputContent: {
    width: '100%',
    paddingHorizontal: scaleSizeW(16),
    borderRadius: scaleSizeW(8),
    borderWidth: scaleSizeH(1),
    borderColor: '#E0E0E4',
    alignItems: 'center',
  },
  inputTextStyle: {
    height: scaleSizeH(72),
    fontSize: currentThemes.fontSize18,
    color: '#393939',
    paddingVertical: 0,
    alignItems: 'center',
  },
  inputRowContainer: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: PADDING_HORIZONTAL,
  },
  submitButton: {
    width: '100%',
    height: scaleSizeH(112),
    paddingHorizontal: scaleSizeW(90),
    paddingVertical: 0,
    marginTop: scaleSizeH(52),
    borderRadius: scaleSizeW(8),
  },
  normalText: {
    fontSize: currentThemes.fontSize18,
    color: '#303030',
  },
  signupText: {
    fontSize: currentThemes.fontSize24,
    color: '#757575',
  },
  exitContainer: {
    position: 'absolute',
    left: scaleSizeW(42),
    bottom: scaleSizeH(44),
    height: scaleSizeH(40),
    width: scaleSizeW(153),
  },
  exitItemContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  exitText: {
    color: '#FFFFFF',
    fontSize: currentThemes.fontSize26,
    fontWeight: '500',
    marginLeft: scaleSizeW(16),
    width: scaleSizeW(97),
  },
});

const carouselTabStyles = StyleSheet.create({
  tabView: {
    flex: 1,
  },
  tabBarUnderlineStyle: {
    height: scaleSizeH(8),
    backgroundColor: currentThemes.buttonBackgroundColor,
  },
  scrollableTabBarStyle: {
    height: TAB_BAR_HEIGHT,
    borderWidth: 0,
  },
  tabsContainerStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    width: tabWidth,
    alignSelf: 'stretch',
  },
  tabBaseStyle: {
    flex: 1,
    height: TAB_BAR_HEIGHT,
    alignSelf: 'stretch',
    alignItems: 'center',
    justifyContent: 'center',
    borderBottomColor: '#D6D6D6',
    borderBottomWidth: 1,
  },
  textActiveStyle: {
    fontSize: currentThemes.fontSize24,
    color: '#303030',
    fontWeight: 'bold',
  },
  textInactiveStyle: {
    fontSize: currentThemes.fontSize24,
    color: '#757575',
    fontWeight: '500',
  },
});

const REGISTER_FORM = [
  {
    id: 'register_first_name',
    required: true,
    label: t('FIRST NAME'),
    placeholder: t('What should we call you'),
    validator: basicNonEmptyValidator,
    secureTextEntry: false,
    type: FormTypes.TEXTINPUT,
    style: styles.inputStyle,
    inputTextStyle: styles.inputTextStyle,
  },
  {
    id: 'register_last_name',
    required: true,
    label: t('LAST NAME'),
    placeholder: '',
    validator: basicNonEmptyValidator,
    secureTextEntry: false,
    type: FormTypes.TEXTINPUT,
    style: styles.inLineStyle,
    inputTextStyle: styles.inputTextStyle,
  },
  {
    id: 'register_email',
    required: true,
    label: t('EMAIL') + ' ' + t('ADDRESS'),
    placeholder: t('natalie email'),
    validator: [basicNonEmptyValidator, emailValidator],
    secureTextEntry: false,
    style: styles.inputStyle,
    type: FormTypes.TEXTINPUT,
    inputTextStyle: styles.inputTextStyle,
  },
];

const ACTIVATION_FORM = [
  {
    id: 'activation_store_name',
    required: true,
    label: t('STORE NAME'),
    placeholder: t('yourstorename'),
    validator: basicNonEmptyValidator,
    secureTextEntry: false,
    type: FormTypes.TEXTINPUT,
    style: styles.inputStyle,
    inputTextStyle: styles.inputTextStyle,
    createAdditionalButton: () => <Text style={styles.normalText}>{'.storehubhq.com'}</Text>,
  },
  {
    id: 'activation_email',
    required: true,
    label: t('EMAIL'),
    placeholder: t('natalie email'),
    validator: [basicNonEmptyValidator, emailValidator],
    secureTextEntry: false,
    style: styles.inLineStyle,
    type: FormTypes.TEXTINPUT,
    inputTextStyle: styles.inputTextStyle,
  },
  {
    id: 'activation_password',
    required: true,
    label: t('Password').toUpperCase(),
    placeholder: '',
    validator: basicNonEmptyValidator,
    secureTextEntry: true,
    style: styles.inLineStyle,
    type: FormTypes.TEXTINPUT,
    inputTextStyle: styles.inputTextStyle,
  },
];
