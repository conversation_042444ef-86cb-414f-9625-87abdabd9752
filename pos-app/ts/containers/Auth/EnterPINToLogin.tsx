import React, { useEffect, useImperativeHandle, useRef, useState } from 'react';
import { Alert, AppState, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import ScrollableTabView from 'react-native-scrollable-tab-view';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { requestCameraPermissionAction, toggleLoadingMask } from '../../actions';
import { PinView } from '../../components/auth';
import TabContainer from '../../components/common/TabContainer';
import { Clock, LockLogo } from '../../components/ui';
import { IsAndroid, IsIOS, SharedStyles, currentThemes, scaleSizeH, scaleSizeW, t } from '../../constants';
import { testProps } from '../../utils';
// @ts-ignore
import { get } from 'lodash';
import moment from 'moment';
import { UvcCamera } from '../../../libs/react-native-uvc-camera';
import { CameraManager } from '../../../libs/react-native-uvc-camera/UvcCamera';
import { requestPermissions } from '../../../libs/react-native-uvc-camera/handlePermissions';
import DAL from '../../dal';
import { EmployeeType } from '../../typings';
import { isIminD1, isIminFalcon1 } from '../../utils/deviceInfo';

const mapDispatchToProps = dispatch => ({
  actions: bindActionCreators(
    {
      requestCameraAuthorizedAction: requestCameraPermissionAction,
      toggleLoadingMask,
    },
    dispatch
  ),
});

interface Props {
  shiftOpenStatus: boolean;
  onSubmitSignInHandler: any;
  onSubmitClockInOutHandler: any;
  onCheckAndroidFaceCapture: (clockInfo) => boolean;
  onChangeTabHandler: any;
  renderTabBar: any;
  enableFaceCapture: boolean;
  actions: {
    requestCameraAuthorizedAction: any;
    toggleLoadingMask: any;
  };
  initialPage: number;
}

export const EnterPINToLogin = React.forwardRef<any, Props>((props, ref) => {
  const {
    actions,
    shiftOpenStatus,
    onSubmitSignInHandler,
    onSubmitClockInOutHandler,
    onChangeTabHandler,
    renderTabBar,
    enableFaceCapture,
    initialPage = 0,
  } = props;
  const dateTime = moment().format('hh:mm A');

  const [isUvcCameraInCapture, setIsUvcCameraInCapture] = useState(false);
  const [uvcCameraStatus, setUvcCameraStatus] = useState('disabled');

  const cameraRef = useRef<UvcCamera>();
  const pinViewRef = useRef<PinView>();

  const onChangeTab = info => {
    const { i, from } = info;
    if (from === 1 && i !== from) {
      setIsUvcCameraInCapture(false);
      pinViewRef.current && pinViewRef.current.clearBtnClicked();
    }
    return onChangeTabHandler({ i, from });
  };

  const takePicture = async () => {
    if (cameraRef.current) {
      const options = {
        width: 640,
        quality: 0.5,
      };
      setUvcCameraStatus('uploading');
      actions.toggleLoadingMask({ visible: true });
      await cameraRef.current
        .takePictureAsync(options)
        .then(data => {
          actions.toggleLoadingMask({ visible: false });
          setIsUvcCameraInCapture(false);
          setUvcCameraStatus('disabled');
          onSubmitFaceCapture(data.uri);
        })
        .catch(err => {
          actions.toggleLoadingMask({ visible: false });
          onUvcCameraError();
        });
    }
  };

  const startFaceCapture = async () => {
    await requestCameraPermission()
      .then(data => {
        actions.requestCameraAuthorizedAction();
        setIsUvcCameraInCapture(true);
      })
      .catch(async err => {
        await new Promise((resolve, reject) => {
          Alert.alert(
            (err.code || err) === 'E_DEVICE_NOT_FOUND' ? 'UVC Camera Not Found' : 'UVC Camera Permission Denied',
            get(errorMsgMap, err.code || err, JSON.stringify(err)),
            [
              {
                text: t('Skip Face Capture'),
                onPress: () => {
                  onSubmitFaceCapture();
                },
              },
              {
                text: t('Try Again'),
                onPress: () => {
                  startFaceCapture().then(resolve).catch(reject);
                },
              },
              {
                text: t('Cancel'),
                onPress: () => {
                  pinViewRef.current && pinViewRef.current.clearBtnClicked();
                },
              },
            ],
            { cancelable: false }
          );
        });
      });
    setIsUvcCameraInCapture(true);
  };

  const requestCameraPermission = async () => {
    if (await requestPermissions(false, CameraManager)) {
      return await CameraManager.requestUvcPermission();
    } else {
      return Promise.reject('UVC_CAM_PERMISSION_DENIED');
    }
  };

  const onUvcCameraDisconnected = () => {
    // todo: check

    // Alert.alert(
    //   'UVC Camera Disconnected',
    //   'Please check your camera device and restart the app',
    //   [
    //     { text: t('Try Again'), onPress: () => {
    //         pinViewRef.current.clearBtnClicked();
    //       }},
    //     { text: t('Skip Face Capture'), onPress: () => {
    //         onSubmitFaceCapture()
    //       }}
    //   ],
    //   { cancelable: false }
    // )
    setIsUvcCameraInCapture(false);
    setUvcCameraStatus('disabled');
  };

  const onUvcCameraError = () => {
    Alert.alert(
      'UVC Camera Connection Error',
      'Please check your camera device and restart the app',
      [
        {
          text: t('Skip Face Capture'),
          onPress: () => {
            onSubmitFaceCapture();
          },
        },
        {
          text: t('Try Again'),
          onPress: () => {
            setIsUvcCameraInCapture(true);
          },
        },
        {
          text: t('Cancel'),
          onPress: () => {
            pinViewRef.current && pinViewRef.current.clearBtnClicked();
          },
        },
      ],
      { cancelable: false }
    );
    setIsUvcCameraInCapture(false);
    setUvcCameraStatus('disabled');
  };

  const onUvcCameraReady = () => {
    setUvcCameraStatus('ready');
  };

  const onSubmitFaceCapture = (uri?: string) => {
    if (pinViewRef.current) {
      onSubmitClockInOutHandler(
        pinViewRef.current.state.pinCode,
        () => {
          pinViewRef.current && pinViewRef.current.onSuccess();
        },
        err => {
          pinViewRef.current && pinViewRef.current.onFailure(err);
        },
        uri
      );
      pinViewRef.current && pinViewRef.current.clearBtnClicked();
    }
  };

  const errorMsgMap = {
    UVC_CAM_PERMISSION_DENIED: 'Please allow the app to access your camera in settings',
    E_DEVICE_NOT_FOUND: 'No UVC camera device found. Please check your camera device and restart the app',
  };

  const onSubmitClockInOut = (pinCode, onSuccess, onFailure) => {
    if (enableFaceCapture && IsAndroid) {
      const employee: EmployeeType = DAL.getEmployeeByPinCode(pinCode) as any;
      if (!employee) {
        pinViewRef.current && pinViewRef.current.onFailure(t('WrongPINCode'));
        return;
      }
      if (props.onCheckAndroidFaceCapture({ employee, onSuccess, onFailure, pinCode })) {
        startFaceCapture();
      }
    } else {
      return onSubmitClockInOutHandler(pinCode, onSuccess, onFailure);
    }
  };

  const onReSubmitClockInOut = (pinCode, onSuccess, onFailure) => {
    pinViewRef.current.setState({ pinCode }, () => {
      onSubmitClockInOut(pinCode, onSuccess, onFailure);
    });
  };

  useImperativeHandle(ref, () => {
    return {
      onReSubmitClockInOut,
    };
  }, []);

  useEffect(() => {
    const handleAppStateChange = (nextAppState: string) => {
      if (nextAppState === 'background') {
        setIsUvcCameraInCapture(false);
        setUvcCameraStatus('disabled');
        if (uvcCameraStatus === 'ready') {
          pinViewRef.current && pinViewRef.current.setState({ pinCode: '' });
        }
      }
    };
    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => {
      subscription.remove();
    };
  }, [uvcCameraStatus]);

  return (
    <View style={styles.tab}>
      <ScrollableTabView
        style={SharedStyles.flexOne}
        initialPage={initialPage}
        renderTabBar={renderTabBar}
        tabBarUnderlineStyle={carouselTabStyles.tabBarUnderlineStyle}
        tabBarPosition='top'
        onChangeTab={onChangeTab}
      >
        <TabContainer style={SharedStyles.column} tabLabel={t('LOG IN')}>
          <LockLogo width={scaleSizeW(360)} height={scaleSizeH(240)} />
          <Text style={styles.tipText} {...testProps('al_shift_status')}>
            {t(shiftOpenStatus ? 'Shift Open' : 'Shift Closed')}
          </Text>
          <PinView container={{ alignSelf: 'stretch' }} maxPinCodeLength={4} onSubmitPinCodeHandler={onSubmitSignInHandler} />
        </TabContainer>
        <TabContainer style={SharedStyles.column} tabLabel={t('CLOCK IN/OUT')}>
          {isUvcCameraInCapture ? (
            <View
              style={{
                flex: 1,
                flexDirection: 'column',
                alignSelf: 'stretch',
                alignItems: 'center',
              }}
            >
              <UvcCamera
                ref={cameraRef}
                rotation={isIminFalcon1() || isIminD1() ? 270 : 0}
                type={UvcCamera.Constants.Type.back}
                onCameraReady={onUvcCameraReady}
                onDisconnected={onUvcCameraDisconnected}
                onMountError={onUvcCameraError}
                style={{
                  flex: 1,
                  height: 0,
                  width: '100%',
                  alignSelf: 'stretch',
                  justifyContent: 'flex-end',
                  alignItems: 'center',
                }}
              />
              {uvcCameraStatus === 'ready' ? (
                <TouchableOpacity
                  {...testProps('al_btn_441')}
                  onPress={takePicture}
                  style={{
                    backgroundColor: '#FC7118',
                    position: 'absolute',
                    bottom: scaleSizeH(0),
                    width: '100%',
                    height: scaleSizeW(100),
                    alignSelf: 'center',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  <Text style={{ color: 'white', fontSize: scaleSizeW(22), fontWeight: 'bold' }}>{t('TAKE_SNAPSHOT_AND_CLOCK_IN_OUT').toUpperCase()}</Text>
                </TouchableOpacity>
              ) : (
                <View
                  style={{
                    flex: 1,
                    position: 'absolute',
                    left: 0,
                    right: 0,
                    top: 0,
                    bottom: 0,
                    backgroundColor: 'white',
                    alignSelf: 'stretch',
                    alignItems: 'center',
                  }}
                >
                  <Clock width={scaleSizeW(360)} height={scaleSizeH(240)} />
                  <Text style={styles.tipText}>{uvcCameraStatus !== 'loading' ? dateTime : 'Loading Camera'}</Text>
                  <PinView container={{ alignSelf: 'stretch' }} maxPinCodeLength={0} onSubmitPinCodeHandler={() => undefined} />
                </View>
              )}
            </View>
          ) : (
            <View />
          )}
          <View
            style={{
              height: !isUvcCameraInCapture ? '100%' : 0,
              flexDirection: 'column',
              alignSelf: 'stretch',
              alignItems: 'center',
            }}
          >
            <Clock width={scaleSizeW(360)} height={scaleSizeH(240)} />
            <Text style={styles.tipText}>{dateTime}</Text>
            <PinView ref={pinViewRef} container={{ alignSelf: 'stretch' }} maxPinCodeLength={4} onSubmitPinCodeHandler={onSubmitClockInOut} />
          </View>
        </TabContainer>
      </ScrollableTabView>
    </View>
  );
});

const connector = connect(null, mapDispatchToProps, null, { forwardRef: true });

export default connector(EnterPINToLogin);

const TAB_WIDTH = IsIOS ? scaleSizeW(900) : scaleSizeW(702);
const TAB_BAR_HEIGHT = scaleSizeH(100);

const styles = StyleSheet.create({
  tab: {
    backgroundColor: 'white',
    alignSelf: 'flex-end',
    width: TAB_WIDTH,
    height: scaleSizeH(928),
    overflow: 'hidden',
  },
  tipText: {
    color: '#303030',
    fontSize: currentThemes.fontSize38,
    fontWeight: '500',
  },
});

const carouselTabStyles = StyleSheet.create({
  tabBarUnderlineStyle: {
    height: scaleSizeH(8),
    backgroundColor: currentThemes.buttonBackgroundColor,
  },
  scrollableTabBarStyle: {
    height: TAB_BAR_HEIGHT,
  },
  tabsContainerStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    width: TAB_WIDTH,
    alignSelf: 'stretch',
  },
  tabBaseStyle: {
    flex: 1,
    alignSelf: 'stretch',
    alignItems: 'center',
    justifyContent: 'center',
  },
  textActiveStyle: {
    fontSize: currentThemes.fontSize24,
    color: '#303030',
    fontWeight: 'bold',
  },
  textInactiveStyle: {
    fontSize: currentThemes.fontSize24,
    color: '#757575',
    fontWeight: '500',
  },
});
