import { get, map, throttle } from 'lodash';
import React, { PureComponent } from 'react';
import { Image, ImageBackground, Keyboard, StatusBar, StyleSheet, Text, TextInput, TouchableNativeFeedback, TouchableOpacity, View } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { ConnectedProps, connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { activate, register, registerStoreNameCheck, setFreeTrial, setSyncInfo, setSequence, setStoreInfo } from '../../actions';
import { IconDown, IconSorting, MaterialIcons, countryIcons, icons } from '../../components/ui';
import { SubscriptionStatus, countryAndCurrency, currentThemes, height, t } from '../../constants';
import { CommonColors, scaleSizeH, scaleSizeW } from '../../constants/themes';
import { ScreenProps } from '../../typings';
import { testProps } from '../../utils';

import { getParam } from '../../utils/navigation';
import Picker from '../../utils/picker';
import { checkMobileNumber, isEmpty } from '../../utils/validator';

interface Props extends ScreenProps, PropsFromRedux {}

export enum SelectType {
  bussinessType = 0,
  storesNum = 1,
  country = 2,
}

interface State {
  keyboardHide: boolean;
  storeName: string;
  password: string;
  bussinessTypeSel: string;
  storesNumSel: string;
  storesNumValue: string;
  country: string;
  countryCode: string;
  timezone: string;
  phone: string;
  zoneNum: string;
  currency: string;
  bussinessType: any;
  storesNum: any;
  isVisiablePwd: boolean;
  enableSaveBtn: boolean;
}

const mapDispatchToProps = dispatch => ({
  actions: bindActionCreators(
    {
      registerStoreNameCheck,
      register,
      setStoreInfo,
      setSequence,
      setFreeTrial,
      setSyncInfo,
      activate,
    },
    dispatch
  ),
});

const connector = connect(null, mapDispatchToProps);
type PropsFromRedux = ConnectedProps<typeof connector>;

class FreeTrialAccount extends PureComponent<Props, State> {
  static navigationOptions = () => ({
    headerShown: false,
  });

  private keyboardDidShowListener;
  private keyboardDidHideListener;
  private mFirstName = getParam(this.props, 'registerFirstName');
  private mLastName = getParam(this.props, 'registerLastName');
  private mEmail = getParam(this.props, 'registerEmail');

  constructor(props) {
    super(props);
    this.state = {
      keyboardHide: false,
      storeName: '',
      password: '',
      bussinessTypeSel: '',
      storesNumSel: '',
      storesNumValue: '',
      country: 'Malaysia',
      countryCode: 'MY',
      zoneNum: '+60',
      currency: 'MYR',
      timezone: 'Asia/Kuala_Lumpur',
      phone: '',
      bussinessType: ['Retail', 'Cafe', 'Restaurant', 'E-Commerce', 'Service'],
      storesNum: [
        { key: 'Don’t own one', value: '0' },
        { key: 'One store', value: '1' },
        { key: 'More than one store', value: '2' },
      ],
      isVisiablePwd: false,
      enableSaveBtn: true,
    };
  }

  componentDidMount() {
    this.keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
      this.onKeyboardShow();
    });
    this.keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => this.setState({ keyboardHide: true }));
  }

  onKeyboardShow = () => {
    this.setState({ keyboardHide: false }), () => Picker.hide();
  };

  componentWillUnmount() {
    this.keyboardDidShowListener.remove();
    this.keyboardDidHideListener.remove();
    Picker.hide();
  }

  render() {
    return (
      <ImageBackground source={icons.backdrop} style={styles.container}>
        <StatusBar barStyle='dark-content' />
        <KeyboardAwareScrollView
          horizontal={false}
          enableOnAndroid
          keyboardOpeningTime={50}
          keyboardDismissMode='none'
          keyboardShouldPersistTaps='never'
          style={styles.scrollViewStyle}
          contentContainerStyle={styles.contentContainer}
        >
          <Text testID='button' style={styles.titleStyle}>
            {t('Complete your sign up')}
          </Text>
          {this.renderContent()}
        </KeyboardAwareScrollView>
      </ImageBackground>
    );
  }

  renderContent = () => {
    return (
      <View style={styles.content}>
        {this.renderAccount()}
        {this.renderPassWord()}
        {this.renderBussinessAndStores()}
        {this.renderCountryAndMobile()}
        {this.renderSaveButton()}
        {this.renderBottomTip()}
      </View>
    );
  };

  renderAccount = () => {
    return (
      <View style={styles.rowStyle}>
        <Text style={styles.tipText}>{t('Claim Your Account')}</Text>
        <View style={[styles.inputContent, { justifyContent: 'space-between', flexDirection: 'row', alignItems: 'center' }]}>
          <TextInput
            {...testProps('al_textinput_923')}
            style={styles.inputText}
            placeholder={t('yourstorename')}
            keyboardType='default'
            placeholderTextColor='#9F9F9F'
            value={this.state.storeName}
            onChangeText={value => {
              this.setState({ storeName: value });
            }}
            onBlur={() => this.registerStoreNameCheck()}
            underlineColorAndroid='transparent'
          />
          <Text style={styles.normalText}>{'.storehubhq.com'}</Text>
        </View>
      </View>
    );
  };

  renderPassWord = () => {
    const { isVisiablePwd, password } = this.state;
    return (
      <View style={styles.rowStyle}>
        <Text style={styles.tipText}>{t('PASSWORD')}</Text>
        <View style={[styles.inputContent, { justifyContent: 'space-between', flexDirection: 'row' }]}>
          <TextInput
            {...testProps('al_textinput_382')}
            style={styles.inputText}
            placeholder={t('Combine letters, numbers, and symbols')}
            keyboardType='default'
            secureTextEntry={!isVisiablePwd}
            autoCapitalize='none'
            placeholderTextColor='#9F9F9F'
            value={password}
            onChangeText={value => {
              this.setState({ password: value });
            }}
            underlineColorAndroid='transparent'
          />
          <TouchableNativeFeedback {...testProps('al_btn_357')} onPress={this.passWordVisiable}>
            <MaterialIcons name={isVisiablePwd ? 'visibility' : 'visibility-off'} color={'#959595'} size={scaleSizeW(30)} />
          </TouchableNativeFeedback>
        </View>
      </View>
    );
  };

  passWordVisiable = () => {
    const isVisiable = this.state.isVisiablePwd;
    this.setState({
      isVisiablePwd: !isVisiable,
    });
  };

  renderBussinessAndStores = () => {
    return (
      <View style={[styles.rowStyle, { flexDirection: 'row', justifyContent: 'space-between' }]}>
        <View style={{ flexDirection: 'column', width: '49%' }}>
          <Text style={styles.tipText}>{t('BUSINESS TYPE')}</Text>
          <TouchableNativeFeedback {...testProps('al_btn_173')} onPress={() => this.showPicker(SelectType.bussinessType)}>
            <View style={styles.selectStyle}>
              <TextInput
                {...testProps('al_textinput_689')}
                style={styles.inputText}
                placeholder={t('Select')}
                keyboardType='default'
                editable={false}
                placeholderTextColor='#9F9F9F'
                value={this.state.bussinessTypeSel}
                onChangeText={value => {
                  this.setState({ bussinessTypeSel: value });
                }}
              />
              <IconDown width={scaleSizeW(24)} height={scaleSizeH(14)} color={'#959595'} />
            </View>
          </TouchableNativeFeedback>
        </View>
        <View style={{ flexDirection: 'column', width: '49%' }}>
          <Text style={styles.tipText}>{t('NUMBER OF STORES')}</Text>
          <TouchableNativeFeedback {...testProps('al_btn_241')} onPress={() => this.showPicker(SelectType.storesNum)}>
            <View style={styles.selectStyle}>
              <TextInput
                {...testProps('al_textinput_889')}
                style={styles.inputText}
                placeholder={t('Select')}
                keyboardType='default'
                editable={false}
                placeholderTextColor='#9F9F9F'
                value={this.state.storesNumValue}
                onChangeText={value => {
                  this.setState({ storesNumValue: value });
                }}
              />
              <IconDown width={scaleSizeW(24)} height={scaleSizeH(14)} color={'#959595'} />
            </View>
          </TouchableNativeFeedback>
        </View>
      </View>
    );
  };

  renderCountryAndMobile = () => {
    const { phone, countryCode, zoneNum } = this.state;
    return (
      <View style={[styles.rowStyle, { marginTop: scaleSizeH(44) }]}>
        <Text style={styles.tipText}>{t('MOBILE NUMBER')}</Text>
        <View style={[styles.inputContent, { flexDirection: 'row', alignItems: 'center' }]}>
          <TouchableOpacity
            {...testProps('al_btn_453')}
            style={{ flexDirection: 'row', alignItems: 'center' }}
            onPress={() => this.showPicker(SelectType.country)}
          >
            <Image style={styles.countryImage} resizeMode={'stretch'} source={countryIcons[countryCode.toLowerCase()]} />
            <IconSorting color={CommonColors.Icon} />
          </TouchableOpacity>
          <Text style={styles.zoneNumText}>{zoneNum}</Text>
          <TextInput
            {...testProps('al_textinput_327')}
            style={[styles.inputText, { width: undefined, flex: 1, textAlignVertical: 'center' }]}
            placeholder={t('Phone Number')}
            keyboardType='default'
            autoCapitalize='none'
            placeholderTextColor='#9F9F9F'
            value={phone}
            onChangeText={value => {
              this.setState({ phone: value });
            }}
            underlineColorAndroid='transparent'
          />
        </View>
      </View>
    );
  };

  _renderPhoneInput = () => {
    const { zoneNum, phone } = this.state;
    return (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
        }}
      >
        <IconSorting color={CommonColors.Icon} />
        <Text style={[styles.inputText, { marginLeft: scaleSizeW(5), width: '10%' }]}>{zoneNum}</Text>
        <TextInput
          {...testProps('al_textinput_609')}
          style={styles.inputText}
          keyboardType={'numeric'}
          value={phone}
          underlineColorAndroid='transparent'
          onChangeText={value => {
            this.setState({
              phone: value,
            });
          }}
        />
      </View>
    );
  };

  showPicker = type => {
    this.setState({
      keyboardHide: true,
    });
    let data;
    if (type === SelectType.bussinessType) {
      data = this.state.bussinessType;
    } else if (type === SelectType.storesNum) {
      const storesKey = [];
      map(this.state.storesNum, item => {
        storesKey.push(item.key);
      });
      data = storesKey;
    }

    if (type === SelectType.bussinessType) {
      data = this.state.bussinessType;
    } else if (type === SelectType.storesNum) {
      const storesKey = [];
      map(this.state.storesNum, item => {
        storesKey.push(item.key);
      });
      data = storesKey;
    } else if (type === SelectType.country) {
      const cuntryAndzoneNum = [];
      map(countryAndCurrency, item => {
        cuntryAndzoneNum.push(`${item.country} ${item.zoneNum}`);
      });
      data = cuntryAndzoneNum;
    }

    Picker.init({
      pickerData: data,
      pickerFontSize: currentThemes.fontSize28,
      pickerToolBarFontSize: currentThemes.fontSize24,
      pickerRowHeight: scaleSizeH(36),
      pickerToolBarHeight: scaleSizeH(80),
      pickerTitleText: t('Please Select'),
      pickerCancelBtnText: t('Cancel'),
      pickerConfirmBtnText: t('Confirm'),
      pickerBg: [255, 255, 255, 1],
      pickerToolBarBg: [239, 239, 244, 1],
      pickerTextEllipsisLen: 20,
      onPickerConfirm: pickedValue => {
        if (type === SelectType.bussinessType) {
          this.setState({ bussinessTypeSel: pickedValue.toString() });
        } else if (type === SelectType.storesNum) {
          map(this.state.storesNum, item => {
            if (item.key === pickedValue.toString()) {
              this.setState({ storesNumSel: item.value, storesNumValue: item.key });
            }
          });
        } else if (type === SelectType.country) {
          map(countryAndCurrency, item => {
            if (`${item.country} ${item.zoneNum}` === pickedValue.toString()) {
              this.setState({
                country: item.country,
                countryCode: item.countryCode,
                zoneNum: item.zoneNum,
                currency: item.currency,
                timezone: item.timezone,
              });
            }
          });
        }
        Picker.hide();
      },
      onPickerCancel: () => {
        Picker.hide();
      },
    });
    Picker.show();
  };

  showMsgModal = msg => {
    const onSubmitHandler = (_, navigation) => navigation.goBack();
    this.props.navigation.navigate('ModalInfo', { info: msg, onSubmitHandler });
  };

  renderSaveButton = () => {
    return (
      <TouchableNativeFeedback {...testProps('al_btn_884')} onPress={this.onSavePress}>
        <View style={styles.saveButton}>
          <Text style={styles.saveText}>{t('Start FREE Trial')}</Text>
        </View>
      </TouchableNativeFeedback>
    );
  };

  renderBottomTip = () => {
    return (
      <View style={{ flexDirection: 'row', justifyContent: 'center', marginTop: scaleSizeH(30) }}>
        <Text style={styles.bottomTipText}>{t('By signing up')}</Text>
        <TouchableNativeFeedback
          {...testProps('al_btn_206')}
          onPress={() => this.gotoWebViewPage(t('Terms of Service'), 'https://www.storehub.com/terms-conditions')}
        >
          <Text style={[styles.bottomTipText, { color: '#1C7BFF' }]}>{t('Terms of Service')}</Text>
        </TouchableNativeFeedback>
        <Text style={styles.bottomTipText}>{t('and')}</Text>
        <TouchableNativeFeedback {...testProps('al_btn_57')} onPress={() => this.gotoWebViewPage(t('Privacy Policy'), 'https://www.storehub.com/privacy')}>
          <Text style={[styles.bottomTipText, { color: '#1C7BFF' }]}>{t('Privacy Policy')}</Text>
        </TouchableNativeFeedback>
      </View>
    );
  };

  gotoWebViewPage = (title, url) => {
    this.props.navigation.navigate('WebViewPage', { title: title, url: url });
  };

  registerStoreNameCheck = throttle(
    () => {
      const onSuccess = {
        callback: payload => {
          const res = get(payload, 'res');
          if (res.result === 'false') {
            this.showMsgModal(t('This domain is already taken'));
          }
        },
      };

      this.props.actions.registerStoreNameCheck({
        name: this.state.storeName,
        onSuccess,
      });
    },
    1000,
    { leading: true, trailing: true }
  );

  validateEntryData = () => {
    const { storeName, password, bussinessTypeSel, storesNumSel, country, phone } = this.state;

    if (isEmpty(storeName)) {
      this.showMsgModal(t('Your Store URL is required'));
      return false;
    }

    if (isEmpty(password)) {
      this.showMsgModal(t('Password is required'));
      return false;
    }
    if (isEmpty(bussinessTypeSel)) {
      this.showMsgModal(t('Business Type is required'));
      return false;
    }

    if (isEmpty(storesNumSel)) {
      this.showMsgModal(t('Number of Stores is required'));
      return false;
    }

    if (isEmpty(country)) {
      this.showMsgModal(t('Country is required'));
      return false;
    }

    if (isEmpty(phone)) {
      this.showMsgModal(t('Phone is required'));
      return false;
    }

    const isMobileNumber = checkMobileNumber(phone);
    if (!isMobileNumber) {
      this.showMsgModal(t('Phone Pattern'));
      return false;
    }

    return true;
  };

  onSavePress = throttle(
    () => {
      this.setState({ enableSaveBtn: false });
      if (!this.validateEntryData()) {
        this.setState({ enableSaveBtn: true });
        return;
      }

      const onSuccess = {
        callback: () => {
          this.props.actions.activate({
            business: this.state.storeName,
            email: this.mEmail,
            password: this.state.password,
            onSuccess: {
              callback: payload => {
                // Save store , pad register info for offline usage
                const now = new Date().toISOString();
                payload.res.activatedTime = now;
                this.props.actions.setStoreInfo(payload.res);
                const { receiptNumberStart, receiptDateStart, invoiceNumberStart, subscriptionStatus } = payload.res;
                this.props.actions.setSequence({ receiptNumberStart, receiptDateStart, invoiceNumberStart, sourceForLog: 'free activation' });
                this.props.actions.setFreeTrial(subscriptionStatus === SubscriptionStatus.Trial);
                this.props.actions.setSyncInfo({
                  lastTrxCancelledFromBOSyncTime: now,
                });
              },
            },
          });
        },
      };

      const onFailure = {
        callback: res => {
          this.setState({ enableSaveBtn: true });
          this.showMsgModal(res.message);
        },
      };
      this.props.actions.register({
        name: this.state.storeName,
        firstName: this.mFirstName,
        lastName: this.mLastName,
        email: this.mEmail,
        password: this.state.password,
        country: this.state.countryCode,
        phone: this.state.zoneNum + this.state.phone,
        numberOfStores: this.state.storesNumSel,
        businessType: this.state.bussinessTypeSel,
        currency: this.state.currency,
        timezone: this.state.timezone,
        onSuccess,
        onFailure,
      });
    },
    1000,
    { leading: true, trailing: true }
  );
}

export default connector(FreeTrialAccount);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#262626',
    alignItems: 'center',
    justifyContent: 'center',
  },
  scrollViewStyle: {
    backgroundColor: 'white',
    marginTop: height - scaleSizeH(928),
    width: scaleSizeW(900),
    height: scaleSizeH(928),
    overflow: 'hidden',
  },
  contentContainer: {
    flex: 1,
  },
  titleStyle: {
    fontSize: currentThemes.fontSize24,
    textAlign: 'center',
    color: '#303030',
    fontWeight: '500',
    marginTop: scaleSizeH(30),
  },
  content: {
    flex: 1,
    flexDirection: 'column',
    backgroundColor: '#FFFFFFFF',
    marginHorizontal: scaleSizeW(90),
  },
  inputText: {
    width: '70%',
    fontSize: currentThemes.fontSize18,
    fontWeight: '400',
    color: '#303030',
    letterSpacing: 1,
    padding: 0,
  },
  tipText: {
    fontSize: currentThemes.fontSize18,
    color: '#959595',
    marginBottom: scaleSizeH(4),
  },
  inputContent: {
    width: '100%',
    height: scaleSizeH(72),
    paddingHorizontal: scaleSizeW(16),
    borderWidth: scaleSizeW(1),
    borderColor: '#E0E0E4',
    alignItems: 'center',
  },
  saveButton: {
    width: '100%',
    height: scaleSizeH(112),
    backgroundColor: '#FC7118',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: scaleSizeH(52),
  },
  normalText: {
    fontSize: currentThemes.fontSize18,
    letterSpacing: 1,
    color: '#303030',
  },
  rowStyle: {
    flexDirection: 'column',
    width: '100%',
    marginTop: scaleSizeH(52),
  },
  selectStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: scaleSizeH(1),
    borderColor: '#E0E0E4',
    paddingHorizontal: scaleSizeW(16),
    paddingVertical: scaleSizeH(24),
    width: '100%',
    height: scaleSizeH(72),
  },
  saveText: {
    color: '#FFFFFF',
    fontSize: currentThemes.fontSize30,
    fontWeight: 'bold',
  },
  bottomTipText: {
    fontSize: currentThemes.fontSize24,
    fontWeight: '400',
    color: '#757575',
  },
  countryImage: {
    width: scaleSizeW(40),
    height: scaleSizeW(24),
  },
  zoneNumText: {
    color: '#9F9F9F',
    fontSize: currentThemes.fontSize18,
    marginRight: scaleSizeW(15),
    marginLeft: scaleSizeW(10),
  },
});
