import NetInfo from '@react-native-community/netinfo';
import * as Immutable from 'immutable';
import { findIndex, get, isUndefined, map } from 'lodash';
import moment from 'moment';
import React, { PureComponent } from 'react';
import { Alert, ImageBackground, NativeModules, ScrollView, StatusBar, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { ScrollableTabBar } from 'react-native-scrollable-tab-view';
import { connect, ConnectedProps } from 'react-redux';
import { bindActionCreators } from 'redux';
import { createSelector } from 'reselect';

import PushNotificationIOS from '@react-native-community/push-notification-ios';
import * as Sentry from '@sentry/react-native';

import {
  bindPushToken,
  checkCloseLastShiftNotification,
  checkUpdate,
  employeeClockIn,
  employeeClockOut,
  getClockedInEmployeeList,
  getCurrentLocation,
  getCurrentShiftStatus,
  initialLogNormalFields,
  initLocalCountryMap,
  jumpToSettingsAction,
  logForDefaultNetwork,
  migrateTableLayoutAction,
  mockAWSAccessInvalid,
  readFaceCaptureIntro,
  requestCameraPermissionAction,
  setCfdConfigurationBegin,
  setCurrentEmployee,
  syncActivationStatus,
  syncAllStoreInfo,
  SyncInfoType,
  syncLargeProductsAction,
  syncTableLayoutAction,
  testDownload,
  testUpload,
  updateNotificationToken,
  validateSignInPinCode,
} from '../../actions';
import { PinView } from '../../components/auth';
import FaceCaptureIntro from '../../components/auth/FaceCaptureIntro';
import StoreInfo from '../../components/auth/StoreInfo';
import TabContainer from '../../components/common/TabContainer';

import Icon from 'react-native-vector-icons/MaterialIcons';
import { appReadyForTrackingNetInfo } from '../../actions/app';
import FaceCaptureClock from '../../components/auth/FaceCaptureClock';
import { IconExitApp, icons, LockLogo } from '../../components/ui';
import { currentThemes, IsAndroid, isBaPingApp, IsIOS, scaleSizeH, scaleSizeW, screenWidth, SharedStyles, SyncType, t } from '../../constants';
import { scaleSize, setSpText } from '../../constants/themes';
import DAL from '../../dal';
import RealmManager from '../../dal/realm';
import {
  selectCameraPermissionStatus,
  selectCountry,
  selectEnableFaceCapture,
  selectEnableFaceSnapshotOnAndroid,
  selectGBEnableLargeProductQuantitySync,
  selectHaveUsedFaceCapture,
  selectNotificationToken,
  selectOperationHours,
  selectReceiptType,
  selectShiftOpenStatus,
  selectStoreId,
  selectSubscriptionPlan,
  selectSupportPhoneNumberMessages,
} from '../../sagas/selector';
import { EmployeeType, LeafState, RootState, ScreenProps } from '../../typings';
import { getUnNullValue, testProps } from '../../utils';
import { MixpanelInstance } from '../../utils/Analytics';
import { createDate } from '../../utils/datetime';
import { IminLcdManager } from '../../utils/lcd';
import { insertMobileData } from '../../utils/logComponent';
import { isEmpty } from '../../utils/validator';
import EnterPINToLogin from './EnterPINToLogin';
import ProgressBar from './ProgressBar';
import ServiceStarter from './ServiceStarter';

import { PERMISSIONS, request } from 'react-native-permissions';
import { ModalInfoPopup } from '../../components/modal/ModalInfoPopup';
import { IS_DEV_FAT } from '../../config';

const FcmPushModule = NativeModules.FcmPushModule;

interface Props extends ScreenProps, PropsFromRedux {}

interface State {
  currentProgress: number;
  haveUsedFaceCapture: boolean;
  showFaceClockedIn: boolean | null;
  isRenderingClockIn: boolean;
  initialTabPage: number;
  downloadedBytes: number;
  downloadedSize: number;
  syncType: number;
  syncError: any;
  clockedInEmployees?: {
    list: any[];
    status: 'success' | 'failure';
  };
}

const fromImmutableSyncInfo = createSelector<RootState, LeafState, SyncInfoType>(
  (state: Immutable.Map<string, any>) => state.getIn(['Storage', 'syncInfo'], Immutable.Map()),
  syncInfo => syncInfo.toJS()
);

const mapStateToProps = (state: RootState) => ({
  registerObjectId: state.getIn<string>(['Storage', 'storeInfo', 'registerObjectId']),
  business: state.getIn<string>(['Storage', 'storeInfo', 'name']),
  registerId: state.getIn<string>(['Storage', 'storeInfo', 'registerId']),
  storeId: selectStoreId(state),
  syncInfo: fromImmutableSyncInfo(state),
  operationHours: selectOperationHours(state),
  shiftOpenStatus: selectShiftOpenStatus(state),
  lastClockInTime: state.getIn<string>(['Shift', 'lastClockInTime']),
  freeTrial: state.getIn<boolean>(['Storage', 'freeTrial']),
  tableLayoutEnabled: state.getIn<boolean>(['Storage', 'storeInfo', 'store', 'tableLayoutEnabled']),
  enableTableLayout: state.getIn<boolean>(['Settings', 'tableLayoutSettings', 'enableTableLayout']),
  geo: state.getIn(['Storage', 'geo']),
  device: state.getIn(['Storage', 'device']),
  receiptType: selectReceiptType(state),
  enableFaceCapture: selectEnableFaceCapture(state) && (IsIOS || selectEnableFaceSnapshotOnAndroid(state)),
  haveUsedFaceCapture: selectHaveUsedFaceCapture(state),
  cameraPermissionStatus: selectCameraPermissionStatus(state),
  supportPhoneNumberMessages: selectSupportPhoneNumberMessages(state),
  country: selectCountry(state),
  subscriptionPlan: selectSubscriptionPlan(state),
  largeProductQuantitySync: selectGBEnableLargeProductQuantitySync(state),
  notificationToken: selectNotificationToken(state),
});

const mapDispatchToProps = dispatch => ({
  actions: bindActionCreators(
    {
      syncAllStoreInfo,
      syncLargeProductsAction,
      validateSignInPinCode,
      getCurrentShiftStatus,
      setCurrentEmployee,
      getClockedInEmployeeList,
      getCurrentLocation,
      setCfdConfigurationBegin,
      initialLogNormalFields,
      checkUpdate,
      bindPushToken,
      syncActivationStatus,
      checkCloseLastShiftNotification,
      migrateTableLayoutAction,
      syncTableLayoutAction,
      readFaceCaptureIntro,
      jumpToSettingsAction,
      employeeClockIn,
      employeeClockOut,
      requestCameraPermissionAction,
      initLocalCountryMap,
      logForDefaultNetwork,
      testDownload,
      testUpload,
      mockAWSAccessInvalid,
      appReadyForTrackingNetInfo,
      updateNotificationToken,
    },
    dispatch
  ),
});

const LASTWEEK_WEEKS_AT = [
  'Last Monday at',
  'Last Tuesday at',
  'Last Wednesday at',
  'Last Thursday at',
  'Last Friday at',
  'Last Saturday at',
  'Last Sunday at',
];

const connector = connect(mapStateToProps, mapDispatchToProps);
type PropsFromRedux = ConnectedProps<typeof connector>;

class SignIn extends PureComponent<Props, State> {
  static navigationOptions = {
    headerShown: false,
  };
  private _clockInInfo;
  private _signInRef;

  constructor(props) {
    super(props);
    this.state = {
      currentProgress: this.checkPopupVisibility() ? 0 : 1,
      haveUsedFaceCapture: this.props.haveUsedFaceCapture,
      isRenderingClockIn: false,
      showFaceClockedIn: false,
      initialTabPage: 0,
      syncType: null,
      downloadedBytes: 0,
      downloadedSize: 0,
      syncError: null,
    };
  }

  componentDidMount() {
    this.fetchClockedEmployeeList();
    this.props.actions.checkUpdate({ checkInstall: true });
    this.props.actions.migrateTableLayoutAction({});
    this.props.actions.syncAllStoreInfo({ onSyncProgress: this.onSyncProgress });
    this.props.actions.getCurrentLocation();
    this.props.actions.setCfdConfigurationBegin();
    this.props.actions.initialLogNormalFields();
    this.syncActivationStatus();
    this.props.actions.initLocalCountryMap();
    if (this.props.freeTrial) {
      setTimeout(() => this.props.navigation.navigate('ModalTemporaryPin'));
    }

    if (IsAndroid) {
      // This is a hacky way to fix the lifecycle issue on react-native-navigation
      // https://storehub-sdn-bhd.sentry.io/issues/5538531660/events/ddfd0a0442744df6b32b2fac73d1dc94/
      //
      // solution is described here https://github.com/facebook/react-native/issues/10009
      setTimeout(() => {
        request(PERMISSIONS.ANDROID.POST_NOTIFICATIONS).then(() => {
          if (this.props.enableFaceCapture) {
            this.props.actions.requestCameraPermissionAction();
          }
        });
      }, 0);
    }

    IminLcdManager.clear();

    MixpanelInstance.registerSuperProperties({
      'Business name': this.props.business,
      'Store ID': this.props.storeId,
      'Register ID': this.props.registerId,
      'Subscription plan': this.props.subscriptionPlan,
    });
    this.props.actions.appReadyForTrackingNetInfo(true);
  }

  componentDidUpdate(prevProps) {
    if (Boolean(RealmManager.getRealmInstance() && isUndefined(this.props.shiftOpenStatus))) {
      this.props.actions.getCurrentShiftStatus();
    }
    if (prevProps.viewedFaceCapture !== this.props.haveUsedFaceCapture) {
      this.setState({ haveUsedFaceCapture: this.props.haveUsedFaceCapture });
    }
  }

  syncActivationStatus = () => {
    const onSuccess = {
      callback: this.bindPushService,
    };
    this.props.actions.syncActivationStatus({
      onSuccess,
    });
  };

  onSyncProgress_Legacy = (type, page) => {
    const { currentProgress } = this.state;
    const { syncInfo } = this.props;
    if (currentProgress >= 1) return;
    let newProgress;
    switch (type) {
      case SyncType.SyncProduct:
        if (page >= 0) {
          newProgress = Math.min(0.99, currentProgress + 0.01);
        } else if (syncInfo !== undefined && !Boolean(syncInfo.needFixsequential) && Object.keys(syncInfo).length !== 0) {
          newProgress = 1;
        } else {
          newProgress = 0.99;
        }
        this.setState({ currentProgress: newProgress });
        break;
      case SyncType.SyncEmployee:
      case SyncType.SyncPriceBook:
      case SyncType.SyncQuickSelectLayout:
      case SyncType.SyncStoreInfo:
        newProgress = Math.min(1, currentProgress + 0.17);
        this.setState({ currentProgress: newProgress });
        break;
      default:
        break;
    }
  };

  onSyncProgress = (type, value) => {
    const { currentProgress } = this.state;
    const { syncInfo, largeProductQuantitySync } = this.props;
    if (currentProgress >= 1) return;
    if (!largeProductQuantitySync) {
      this.onSyncProgress_Legacy(type, value);
      return;
    }
    let newProgress;
    switch (type) {
      case SyncType.SyncEmployee:
      case SyncType.SyncPriceBook:
      case SyncType.SyncQuickSelectLayout:
      case SyncType.SyncStoreInfo:
        newProgress = Math.min(1, currentProgress + 0.1);
        this.setState({ currentProgress: newProgress, syncType: type });
        break;
      case SyncType.SyncProduct:
        if (value >= 0) {
          newProgress = Math.min(0.99, currentProgress + 0.01);
        } else if (syncInfo !== undefined && !Boolean(syncInfo.needFixsequential) && Object.keys(syncInfo).length !== 0) {
          newProgress = 1;
        } else {
          newProgress = 0.99;
        }
        this.setState({ currentProgress: newProgress, syncType: type });
        break;
      case SyncType.SyncProductDownloadStream:
        this.setState({ currentProgress: 0.5, syncType: type, downloadedBytes: value });
        break;
      case SyncType.SyncProductParsing:
        this.setState({ currentProgress: Math.min(1, 0.5 + value / 2), syncType: type });
        break;
      case SyncType.SyncProductPages:
        this.setState({ currentProgress: 0.75, syncType: type, downloadedSize: value });
        break;
      case SyncType.SyncProductImages:
        this.setState({ currentProgress: 0.85, syncType: type, downloadedSize: value });
        break;
      case SyncType.SyncProductFinished:
        this.setState({ currentProgress: 1, syncType: type, downloadedSize: 0 });
        break;
      case SyncType.SyncProductFailed:
        this.setState({ currentProgress: 0.75, syncType: type, syncError: value });
        break;
      default:
        break;
    }
  };

  bindPushService = () => {
    if (IsAndroid) {
      this.initFcmPush();
    } else {
      PushNotificationIOS.requestPermissions({
        alert: true,
        badge: true,
        sound: true,
      });
      PushNotificationIOS.addEventListener('register', deviceToken => {
        this.bindPushToken(deviceToken);
      });
    }
  };

  initFcmPush = () => {
    FcmPushModule.getFcmTokenFrmNative()
      .then(deviceToken => {
        this.bindPushToken(deviceToken);
      })
      .catch(error => {
        if (!IS_DEV_FAT) {
          Alert.alert('error', `push notification register error \n Code: ${error.code || ''} \n Message: ${error.message || ''}`);
        }
      });
  };

  bindPushToken = deviceToken => {
    const { storeId, notificationToken } = this.props;
    // Only bind push token if it's different from the stored token
    if (deviceToken && deviceToken !== notificationToken) {
      // Call the API to bind the token
      this.props.actions.bindPushToken({
        deviceToken: deviceToken,
        storeId,
        registerId: this.props.registerObjectId,
        merchantName: this.props.business,
        onSuccess: {
          callback: payload => {
            if (get(payload, 'res.register.success')) {
              this.props.actions.updateNotificationToken(deviceToken);
            }
          },
        },
      });
    }
  };

  setSentryUser = () => {
    const { business, registerId } = this.props;

    Sentry.setUser({
      id: business,
      username: registerId,
    });
  };

  checkEmployee = (pinCode: string) => {
    const { syncInfo } = this.props;
    const employee: EmployeeType = DAL.getEmployeeByPinCode(pinCode) as any;
    return new Promise(resolve => {
      if (employee !== undefined) {
        resolve(employee);
      } else if (!get(syncInfo, 'employeeInfoSync')) {
        // TODO: Save new employee
        const onSuccess = {
          callback: () => {
            resolve(null);
          },
        };
        const onFailure = {
          callback: () => {
            resolve(null);
          },
        };
        this.props.actions.validateSignInPinCode({
          business: this.props.business,
          pin: pinCode,
          onSuccess,
          onFailure,
        });
      } else {
        resolve(null);
      }
    });
  };

  signInAction = async (pinCode, onSuccessUICb, onFailureUICb) => {
    const { freeTrial, navigation } = this.props;
    try {
      let employee: EmployeeType;
      if (freeTrial && pinCode === '1111') {
        employee = DAL.getFirstEmployee();
      } else {
        employee = (await this.checkEmployee(pinCode)) as any;
      }
      // Prevents the employeeId from being empty
      if (!employee || isEmpty(employee.employeeId)) {
        onFailureUICb && onFailureUICb(t('WrongPINCode'));
      } else {
        if (employee.isCashier !== false) {
          this.props.actions.setCurrentEmployee({
            employeeId: employee.employeeId,
          });
          insertMobileData({ employeeId: employee.employeeId });
          this.checkShiftNotification();
          this.setSentryUser();
          onSuccessUICb && onSuccessUICb();
          this.props.actions.logForDefaultNetwork();
        } else {
          onSuccessUICb && onSuccessUICb();
          requestAnimationFrame(() => navigation.navigate('ModalInfo', { info: t('Not enable to Sign In') }));
        }
      }
    } catch (ex) {
      console.warn('signInAction exception', ex);
    }
  };

  checkShiftNotification = () => {
    this.props.actions.checkCloseLastShiftNotification();
  };

  fetchClockedEmployeeList = () => {
    const { storeId } = this.props;
    const onSuccess = {
      callback: payload => {
        this.setState({
          clockedInEmployees: { list: getUnNullValue(payload, 'res.timesheets', []), status: 'success' },
        });
      },
    };
    const onFailure = {
      callback: payload => {
        this.setState({
          clockedInEmployees: { list: getUnNullValue(payload, 'res.timesheets', []), status: 'failure' },
        });
      },
    };
    this.props.actions.getClockedInEmployeeList({ storeId, onSuccess, onFailure });
  };

  onConfirmFaceClockIn = () => {
    if (this._clockInInfo) {
      this.setState({ showFaceClockedIn: false, initialTabPage: 1 }, () => {
        const { pinCode, onSuccess, onFailure } = this._clockInInfo;
        const clockedInWithFaceCapture = new Date().toISOString();
        DAL.saveEmployee({ employeeId: this._clockInInfo.employee.employeeId, clockedInWithFaceCapture });
        this._clockInInfo.employee = DAL.getEmployeeById(this._clockInInfo.employee.employeeId);
        // android confirm and start camera
        if (pinCode) {
          // android
          this._signInRef?.onReSubmitClockInOut(pinCode, onSuccess, onFailure);
        } else {
          // ios
          this.onStartClockIn(this._clockInInfo);
        }
      });
    }
  };

  onStartClockIn = clockInfo => {
    const { employee, onSuccessUICb, faceCaptureUri } = clockInfo;
    const { storeId, enableFaceCapture } = this.props;
    if (enableFaceCapture && !employee.clockedInWithFaceCapture) {
      this._clockInInfo = clockInfo;
      this.setState({ showFaceClockedIn: true });
      return;
    }
    try {
      const employeeId = employee.employeeId;

      const employeeName = employee.firstName + ' ' + employee.lastName;

      // Clock in
      const clockInTime = new Date().toISOString();
      this.props.actions.employeeClockIn({
        employeeId,
        storeId,
        clockInTime,
        onSuccess: {
          callback: payload => {
            const _clockInTime = get(payload, ['employeeClockIn', 'clockInTime']);
            const onSubmitHandler = onSuccessUICb;
            const info = t('clocked in at', { name: employeeName, time: createDate(_clockInTime, 'h:mm A') });
            requestAnimationFrame(() =>
              this.props.navigation.navigate('ModalInfo', {
                info,
                onSubmitHandler,
                onCloseClick: onSubmitHandler,
              })
            );
            this.fetchClockedEmployeeList();
          },
        },
        onError: {
          callback: payload => {
            const onSubmitHandler = onSuccessUICb;
            const info = t('Clock in unsuccessful, please try again');
            requestAnimationFrame(() =>
              this.props.navigation.navigate('ModalInfo', {
                info,
                onSubmitHandler,
                onCloseClick: onSubmitHandler,
              })
            );
          },
        },
        faceCaptureUri,
      });
    } catch (ex) {
      console.warn('onStartClockIn exception', ex);
    }
  };

  onCheckAndroidFaceCapture = clockInfo => {
    const { employee } = clockInfo;
    const { enableFaceCapture } = this.props;
    const { clockedInEmployees } = this.state;
    const hasClockedIn = findIndex(clockedInEmployees?.list, it => get(it, ['employeeInfo', 'id']) === employee.employeeId) !== -1;
    if (!hasClockedIn && enableFaceCapture && !employee.clockedInWithFaceCapture) {
      this._clockInInfo = clockInfo;
      this.setState({ showFaceClockedIn: true });
      return false;
    }
    return true;
  };

  onSubmitClockInOutHandler = async (pinCode, onSuccessUICb, onFailureUICb, faceCaptureUri?: string) => {
    const { storeId, business } = this.props;
    const { clockedInEmployees } = this.state;

    try {
      const employee: EmployeeType = DAL.getEmployeeByPinCode(pinCode) as any;

      if (!employee) {
        onFailureUICb && onFailureUICb(t('WrongPINCode'));
        return;
      }

      if (clockedInEmployees?.status !== 'success') {
        return;
      }

      const employeeId = employee.employeeId;
      const hasClockedIn = findIndex(clockedInEmployees?.list, employee => get(employee, ['employeeInfo', 'id']) === employeeId) !== -1;

      const employeeName = employee.firstName + ' ' + employee.lastName;
      if (hasClockedIn) {
        // Clock out
        const clockOutTime = new Date().toISOString();
        this.props.actions.employeeClockOut({
          employeeId,
          storeId,
          business,
          clockOutTime,
          onSuccess: {
            callback: payload => {
              const _clockOutTime = get(payload, ['employeeClockOut', 'clockOutTime']);
              const onSubmitHandler = onSuccessUICb;
              const info = t('clocked out at', { name: employeeName, time: createDate(_clockOutTime, 'h:mm A') });
              requestAnimationFrame(() =>
                this.props.navigation.navigate('ModalInfo', {
                  info,
                  onSubmitHandler,
                  onCloseClick: onSubmitHandler,
                })
              );
              this.fetchClockedEmployeeList();
            },
          },
          onError: {
            callback: payload => {
              const onSubmitHandler = onSuccessUICb;
              const info = t('Clock out unsuccessful, please try again');
              requestAnimationFrame(() =>
                this.props.navigation.navigate('ModalInfo', {
                  info,
                  onSubmitHandler,
                  onCloseClick: onSubmitHandler,
                })
              );
            },
          },
          faceCaptureUri,
        });
      } else {
        this.onStartClockIn({ employee, onSuccessUICb, faceCaptureUri });
      }
    } catch (ex) {
      console.warn('onSubmitClockInOutHandler exception', ex);
    }
  };

  renderNoticePopup = () => {
    const { currentProgress, syncType, downloadedBytes, downloadedSize } = this.state;
    return (
      <View style={styles.noticeContent}>
        {this.props.largeProductQuantitySync && (
          <>
            {!syncType && <Text style={styles.prepareText}>{'Preparing Data...'}</Text>}
            {syncType === SyncType.SyncEmployee && <Text style={styles.prepareText}>{'Syncing Employees...'}</Text>}
            {syncType === SyncType.SyncPriceBook && <Text style={styles.prepareText}>{'Syncing Price Books...'}</Text>}
            {syncType === SyncType.SyncQuickSelectLayout && <Text style={styles.prepareText}>{'Syncing Quick Layout...'}</Text>}
            {syncType === SyncType.SyncStoreInfo && <Text style={styles.prepareText}>{'Syncing Store Info...'}</Text>}
            {syncType === SyncType.SyncProduct && <Text style={styles.prepareText}>{'Syncing Products...'}</Text>}
            {syncType === SyncType.SyncProductDownloadStream && (
              <Text style={styles.prepareText}>{`Syncing Products... ${(downloadedBytes / 1024 / 1024).toFixed(2)}MB`}</Text>
            )}
            {syncType === SyncType.SyncProductParsing && (
              <Text style={styles.prepareText}>{`Preparing Products... ${(currentProgress * 100).toFixed(0)}%`}</Text>
            )}
            {syncType === SyncType.SyncProductPages && <Text style={styles.prepareText}>{`Syncing Products... (${downloadedSize})`}</Text>}
            {syncType === SyncType.SyncProductImages && <Text style={styles.prepareText}>{`Syncing Product Images... (${downloadedSize})`}</Text>}
            {syncType === SyncType.SyncProductFailed && <Text style={styles.prepareText}>{'Syncing Products Failed'}</Text>}
          </>
        )}
        {!this.props.largeProductQuantitySync && <Text style={styles.prepareText}>{t('Preparing data')}</Text>}
        <ProgressBar
          progress={currentProgress}
          width={scaleSizeW(480)}
          borderWidth={0}
          unfilledColor={'white'}
          color={'#FC7118'}
          height={scaleSizeH(8)}
          borderRadius={scaleSizeH(4)}
        />
        {syncType === SyncType.SyncProductFailed && (
          <>
            <ModalInfoPopup
              {...testProps('al_btn_273')}
              title={'Product Sync Failed'}
              message={'Failed to sync products. Please try again.'}
              submitText={'Retry'}
              onSubmitHandler={() => {
                // this.props.actions.syncLargeProductsAction({ onSyncProgress: this.onSyncProgress });
                this.setState({
                  currentProgress: 0.75,
                  syncType: SyncType.SyncProductPages,
                  downloadedSize: 0,
                  syncError: null,
                });
                this.props.actions.syncLargeProductsAction({ onSyncProgress: this.onSyncProgress, isInitial: true });
              }}
            />
          </>
        )}
      </View>
    );
  };

  checkPopupVisibility = () => {
    const { syncInfo } = this.props;
    if (syncInfo === undefined || !Boolean(syncInfo.productInfoSync) || Boolean(syncInfo.needFixsequential) || Object.keys(syncInfo).length === 0) {
      return true;
    }
    return false;
  };

  onSubmitSignInHandler = (pinCode, onSuccess, onFailure) => {
    requestAnimationFrame(() => this.signInAction(pinCode, onSuccess, onFailure));
  };

  internationalizationWithLastWeek = (time: string) => {
    for (const week of LASTWEEK_WEEKS_AT) {
      if (time.includes(week)) {
        return time.replace(week, t(week));
      }
    }
    return time;
  };

  renderItem = item => {
    if (!item) {
      return null;
    }
    const {
      id,
      clockInTime,
      employeeInfo: { firstName, lastName },
    } = item;

    const name = `${firstName} ${lastName}`;
    let time = moment(clockInTime).calendar(null, {
      sameDay: t('Today at'),
      nextDay: t('Tomorrow at'),
      lastDay: t('Yesterday at'),
      sameElse: 'DD/MM/YYYY hh:mm A',
    });

    time = this.internationalizationWithLastWeek(time);
    return (
      <View style={styles.item} key={String(id)}>
        <Text {...testProps(`al_employee_${name}`)} style={styles.name}>
          {name}
        </Text>
        <Text style={styles.time}>{time}</Text>
      </View>
    );
  };

  renderClockedInList = () => {
    const { clockedInEmployees } = this.state;

    return (
      <View style={SharedStyles.flexOne}>
        <ScrollView
          contentContainerStyle={[
            SharedStyles.column,
            {
              paddingHorizontal: scaleSizeW(32),
              paddingTop: scaleSizeH(120),
            },
          ]}
        >
          <Text style={styles.title}>{t('CLOCKED IN')}</Text>
          <View style={styles.employeeStyle} />
          {Boolean(clockedInEmployees?.list) &&
            map(clockedInEmployees.list, item => {
              return this.renderItem(item);
            })}
        </ScrollView>
      </View>
    );
  };

  onChangeTabHandler = ({ i, from }: { i: number; from: number }) => {
    if (i !== from && i === 1) {
      this.fetchClockedEmployeeList();
      NetInfo.fetch().then(data => {
        if (data.type === 'none') {
          requestAnimationFrame(() =>
            this.props.navigation.navigate('ModalInfo', {
              title: t('No Internet Connection'),
              isShowTitle: true,
              info: t('Please connect to the internet to use this function'),
              titleIcon: <Icon name='warning' size={24} color='#FFA500' />,
              okText: t('Close'),
            })
          );
        }
      });
      this.setState({ isRenderingClockIn: true });
    } else if (i !== from && i === 0) {
      this.setState({ isRenderingClockIn: false });
    }
  };

  onPressProceedToSettings = () => {
    this.props.actions.jumpToSettingsAction();
  };

  goDeviceHome = () => NativeModules.RNHomeModule.goBackHome();

  render() {
    const { freeTrial } = this.props;
    const { currentProgress } = this.state;
    const exitText = t('Exit App');

    const enableFaceCapture = this.props.enableFaceCapture;
    const { haveUsedFaceCapture, showFaceClockedIn } = this.state;
    let needToShowFaceCaptureIntro = !haveUsedFaceCapture || showFaceClockedIn;
    if (!enableFaceCapture) {
      needToShowFaceCaptureIntro = false;
    }

    const { cameraPermissionStatus } = this.props;
    let needToShowProceedView = false;
    const isRenderingClockIn = this.state.isRenderingClockIn;
    if (isRenderingClockIn && enableFaceCapture && !cameraPermissionStatus) {
      needToShowProceedView = true;
    }

    const rendereSupportContacts = () => {
      const message = JSON.parse(JSON.stringify(this.props.supportPhoneNumberMessages));
      if (!message || !message.messages) {
        return null;
      }
      const localMessage = message.messages.find(msg => msg.country === this.props.country);
      if (!message.visible || !localMessage) {
        return null;
      }

      const localMessages = localMessage.messages;

      return (
        <View style={{ position: 'absolute', start: scaleSize(18), bottom: scaleSize(32) }}>
          {localMessages.map((msg, index) => {
            return (
              <Text
                key={msg.body + String(index)}
                style={{
                  maxWidth: scaleSize(400),
                  fontWeight: msg.bold ? '600' : '400',
                  color: '#fff',
                  fontSize: setSpText(24),
                }}
              >
                {msg.body}
              </Text>
            );
          })}
        </View>
      );
    };

    return (
      <ImageBackground testID='bk' source={icons.backdrop} style={styles.background}>
        <StatusBar hidden />
        {/* <View style={{ width: 500, flexDirection: 'row', justifyContent: 'space-between', paddingTop: 20 }}>
          <TouchableOpacity onPress={() => this.props.actions.mockAWSAccessInvalid()}>
            <Text style={{ width: 100, height: 50, backgroundColor: 'white' }}>Test Reset</Text>
          </TouchableOpacity>
          <TouchableOpacity onPress={() => this.props.actions.testUpload()}>
            <Text style={{ width: 100, height: 50, backgroundColor: 'white' }}>Test Upload</Text>
          </TouchableOpacity>
          <TouchableOpacity onPress={() => this.props.actions.testDownload()}>
            <Text style={{ width: 100, height: 50, backgroundColor: 'white' }}>Test Download</Text>
          </TouchableOpacity>
        </View> */}
        {currentProgress === 1 && (
          <>
            <View style={[SharedStyles.flexOne, SharedStyles.row]}>
              {!needToShowFaceCaptureIntro && <View style={SharedStyles.flexOne} />}
              {freeTrial ? (
                this.renderFreeTrialPin()
              ) : needToShowFaceCaptureIntro ? (
                this.rendereFaceCaptureIntro()
              ) : (
                <>
                  {rendereSupportContacts()}
                  {this.renderSignIn()}
                  {!needToShowFaceCaptureIntro && this.renderClockedInList()}
                </>
              )}
              {freeTrial ? <View style={SharedStyles.flexOne} /> : null}
            </View>
            {needToShowProceedView && this.renderProceedView()}
          </>
        )}
        {currentProgress !== 1 && this.renderNoticePopup()}
        {isBaPingApp && (
          <TouchableOpacity {...testProps('al_btn_839')} onPress={this.goDeviceHome} style={styles.exitContainer}>
            <View style={styles.exitItemContainer}>
              <IconExitApp width={scaleSizeW(40)} height={scaleSizeH(40)} color={'#FFFFFF'} />
              <Text style={styles.exitText}>{exitText}</Text>
            </View>
          </TouchableOpacity>
        )}
        <StoreInfo />
        <ServiceStarter currentProgress={currentProgress} />
      </ImageBackground>
    );
  }

  rendereFaceCaptureIntro = () => {
    const { showFaceClockedIn } = this.state;
    return (
      <View style={styles.faceCaptureIntro}>
        {showFaceClockedIn ? (
          <FaceCaptureClock onPress={this.onConfirmFaceClockIn} />
        ) : (
          <FaceCaptureIntro
            onPress={() => {
              requestAnimationFrame(this.props.actions.requestCameraPermissionAction);
              this.props.actions.readFaceCaptureIntro();
            }}
          />
        )}
      </View>
    );
  };

  getPinRef = ref => {
    this._signInRef = ref;
  };

  renderSignIn = () => {
    return (
      <EnterPINToLogin
        ref={this.getPinRef}
        shiftOpenStatus={this.props.shiftOpenStatus}
        onSubmitSignInHandler={this.onSubmitSignInHandler}
        onCheckAndroidFaceCapture={this.onCheckAndroidFaceCapture}
        onSubmitClockInOutHandler={this.onSubmitClockInOutHandler}
        onChangeTabHandler={this.onChangeTabHandler}
        renderTabBar={renderTabBar}
        initialPage={this.state.initialTabPage}
        enableFaceCapture={this.props.enableFaceCapture}
      />
    );
  };

  renderFreeTrialPin = () => {
    return (
      <View style={styles.tab}>
        <TabContainer style={SharedStyles.column} tabLabel={''}>
          <Text
            style={[
              styles.tipText,
              {
                fontSize: currentThemes.fontSize24,
                marginTop: scaleSizeH(30),
              },
            ]}
          >
            {t('Login')}
          </Text>
          <LockLogo width={scaleSizeW(360)} height={scaleSizeH(240)} />
          <Text style={styles.tipText}>{t('Enter Your Pin')}</Text>
          <PinView container={{ alignSelf: 'stretch' }} maxPinCodeLength={4} onSubmitPinCodeHandler={this.onSubmitSignInHandler} />
        </TabContainer>
      </View>
    );
  };

  renderProceedView = () => {
    return (
      <View style={styles.proceedView}>
        <Text style={styles.proceedTitle}>{t('You are clocking in/out without Face Capture')}</Text>
        <View style={styles.proceedTextContainer}>
          <Text style={styles.proceedText}>{t('To proceed with Face Capture please enable camera settings')}</Text>
          <TouchableOpacity {...testProps('al_btn_25')} onPress={this.onPressProceedToSettings} style={styles.enableText}>
            <Text style={styles.proceedButton}>{t('Enable Camera Now')}</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };
}

export default connector(SignIn);

const renderTabBar = () => {
  return (
    <ScrollableTabBar
      style={carouselTabStyles.scrollableTabBarStyle}
      tabsContainerStyle={[carouselTabStyles.tabsContainerStyle, {}]}
      renderTab={(id, page, isTabActive, onPressHandler, onLayoutHandler) => {
        const textStyle = isTabActive ? carouselTabStyles.textActiveStyle : carouselTabStyles.textInactiveStyle;
        return (
          <TouchableOpacity
            style={carouselTabStyles.tabBaseStyle}
            {...testProps(`al_tab_${id}`)}
            key={`scrollable_tab_button_${page}`}
            onPress={() => onPressHandler(page)}
            onLayout={onLayoutHandler}
            activeOpacity={1}
          >
            <Text style={textStyle}>{id}</Text>
          </TouchableOpacity>
        );
      }}
    />
  );
};

const TAB_BAR_HEIGHT = scaleSizeH(100);
const ITEM_HEIGHT = scaleSizeH(107);
const TAB_WIDTH = IsIOS ? scaleSizeW(900) : scaleSizeW(702);
const FACE_CAPTURE_INTRO_WIDTH = screenWidth * 0.7;
const PROCEED_VIEW_WIDTH = (screenWidth - TAB_WIDTH) / 2 - 2 * scaleSizeW(49);

const styles = StyleSheet.create({
  tab: {
    backgroundColor: 'white',
    alignSelf: 'flex-end',
    width: TAB_WIDTH,
    height: scaleSizeH(928),
    overflow: 'hidden',
  },
  title: {
    color: '#FFF',
    fontSize: currentThemes.fontSize24,
    marginVertical: scaleSizeH(24),
  },
  name: {
    color: currentThemes.buttonBackgroundColor,
    fontWeight: '500',
    fontSize: currentThemes.fontSize24,
  },
  time: {
    color: '#FFF',
    fontSize: currentThemes.fontSize18,
  },
  employeeStyle: {
    borderBottomColor: '#FFFFFF',
    borderBottomWidth: scaleSizeH(1),
    width: '100%',
  },
  item: {
    width: '100%',
    height: ITEM_HEIGHT,
    alignItems: 'flex-start',
    paddingTop: scaleSizeH(38),
    borderBottomColor: '#FFFFFF',
    borderBottomWidth: scaleSizeW(1),
  },
  background: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  tipText: {
    color: '#303030',
    fontSize: currentThemes.fontSize38,
    fontWeight: '500',
  },
  noticeContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    ...StyleSheet.absoluteFillObject,
    backgroundColor: '#858585A0',
  },
  prepareText: {
    fontSize: currentThemes.fontSize32,
    color: 'white',
    marginBottom: scaleSizeH(24),
  },
  exitContainer: {
    position: 'absolute',
    left: scaleSizeW(42),
    bottom: scaleSizeH(44),
    height: scaleSizeH(40),
    width: scaleSizeW(153),
  },
  exitItemContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  exitText: {
    color: '#FFFFFF',
    fontSize: currentThemes.fontSize26,
    fontWeight: '500',
    marginLeft: scaleSizeW(16),
    width: scaleSizeW(97),
  },
  faceCaptureIntro: {
    alignItems: 'center',
    justifyContent: 'center',
    width: FACE_CAPTURE_INTRO_WIDTH,
  },
  proceedView: {
    backgroundColor: 'white',
    position: 'absolute',
    width: PROCEED_VIEW_WIDTH,
    left: scaleSizeW(49),
    bottom: scaleSizeH(38),
  },
  proceedTitle: {
    fontSize: currentThemes.fontSize26,
    fontWeight: '500',
    color: 'black',
    marginTop: scaleSizeH(30),
    marginHorizontal: scaleSizeW(40),
    marginBottom: scaleSizeH(12),
    lineHeight: scaleSizeH(35),
  },
  proceedText: {
    fontSize: currentThemes.fontSize22,
    color: '#60636B',
    marginHorizontal: scaleSizeW(40),
    marginBottom: scaleSizeH(8),
    lineHeight: scaleSizeH(30),
  },
  enableText: {
    fontSize: currentThemes.fontSize22,
    color: '#60636B',
    marginHorizontal: scaleSizeW(40),
    marginBottom: scaleSizeH(8),
    lineHeight: scaleSizeH(30),
    paddingBottom: scaleSizeH(10),
    paddingTop: scaleSizeH(10),
  },
  proceedButton: {
    fontSize: currentThemes.fontSize22,
    color: '#00B0FF',
    textDecorationLine: 'underline',
    marginBottom: -2,
  },
  proceedTextContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: scaleSizeH(20),
  },
});

const carouselTabStyles = StyleSheet.create({
  scrollableTabBarStyle: {
    height: TAB_BAR_HEIGHT,
  },
  tabsContainerStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    width: TAB_WIDTH,
    alignSelf: 'stretch',
  },
  tabBaseStyle: {
    flex: 1,
    alignSelf: 'stretch',
    alignItems: 'center',
    justifyContent: 'center',
  },
  textActiveStyle: {
    fontSize: currentThemes.fontSize24,
    color: '#303030',
    fontWeight: 'bold',
  },
  textInactiveStyle: {
    fontSize: currentThemes.fontSize24,
    color: '#757575',
    fontWeight: '500',
  },
});
