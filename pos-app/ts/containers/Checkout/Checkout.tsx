import * as Immutable from 'immutable';
import { cloneDeep, filter, findIndex, forEach, get, isEmpty, map, noop, throttle } from 'lodash';
import React, { PureComponent } from 'react';
import { <PERSON><PERSON>, BackHandler, FlatList, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import Swiper from 'react-native-swiper';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { createSelector } from 'reselect';
import {
  addCustomerToTransaction,
  AddCustomerToTransactionType,
  cancelNextPayment,
  CheckOutResultType,
  checkoutTransaction,
  CheckoutTransactionType,
  checkPlaceOrderAvailable,
  CheckPlaceOrderAvailableLocation,
  CheckPlaceOrderAvailableType,
  clearTransactionSession,
  clearUniquePromos,
  CustomerType,
  deleteCustomerFromTransaction,
  deleteCustomerFromTransactionType,
  GhlRequestType,
  lockUniquePromo,
  makeSalePayment,
  MakeSalePaymentType,
  navigateToRegister,
  NavigateType,
  openDrawer,
  queryEInvoice,
  QueryEInvoiceType,
  returnUniquePromo,
  setTransactionSession,
  stopCheckCustomerInfoFromQR,
  StopCheckCustomerInfoFromQRType,
  TransactionTypeWithDisplay,
} from '../../actions';
import { Loading, TopMainBar } from '../../components/common';
import { Cart } from '../../components/register/CartContent';
import { IconCash, IconCreditCard, IconDebitCard, IconLeft, IconQRCode, IconStoreCredit, IconWallet } from '../../components/ui';
import PaymentOptions, { DefaultPaymentOptionType, GhlPaymentOptionType, PaymentOptionType, PaymentType } from '../../config/paymentOption';
import {
  CommonColors,
  currentThemes,
  IsAndroid,
  MakePaymentErrorType,
  SalesChannelType,
  scaleSizeH,
  scaleSizeW,
  t,
  TransactionFlowType,
  width,
} from '../../constants';
import { onMRSInterceptor } from '../../sagas/mrs/checkSync';
import {
  selectEInvoiceEnabled,
  selectFreeTrial,
  selectIsNetConnected,
  selectLocalCountryMap,
  selectMakeStoreCreditAsPayment,
  selectRegisterObjectId,
  selectSelectedUniquePromo,
  selectSlavePid,
  selectTrialOrderLimit,
  selectUniquePromos,
} from '../../sagas/selector';

import moment from 'moment';

import ViewShot from 'react-native-view-shot';
import { getCustomerQRSetting, GetCustomerQRSettingType } from '../../actions/customer';
import { navigateModalWarningLocalTime } from '../../components/modal/ModalError';
import { CurrencyTextInput } from '../../components/textInput';
import IconWave from '../../components/ui/svgIcons/iconWave';
import { scaleSize } from '../../constants/themes';
import { EInvoiceStatus } from '../../sagas/transaction/eInvoice';
import { lockUniquePromoType } from '../../sagas/transaction/promotion';
import { checTrialOrderLimit } from '../../sagas/transaction/saleFlow';
import { PromotionType, ScreenProps } from '../../typings';
import { checkPriceNumberInput, convertToNumber, getNumberValue, localeNumber, newConvertCurrencyToSymbol, testProps } from '../../utils';
import { calculateCashShortcut, calculatePayAmount, calculateUnpaidTotal } from '../../utils/checkout';
import { isIminD1, isIminFalcon1 } from '../../utils/deviceInfo';
import { stringify } from '../../utils/json';
import { IminLcdManager } from '../../utils/lcd';
import { infoTransactionEvent, SaleFlowAction } from '../../utils/logComponent';
import { MRSConfig } from '../../utils/mrs/MRSConfig';
import { getParam } from '../../utils/navigation';
import { getDisplayItemsCount, isPreOrderPickUp } from '../../utils/transaction';
import PaymentOptionItem, { CashShortcutItem, CustomPaymentItem } from './PaymentOptionItem';
import RedWarning from '../../../assets/icons/svg/redWarning.svg';

const ReactNativeNfc = require('../../../libs/react-native-nfc').default;
const { isSupportNfcPayment } = ReactNativeNfc;

interface Props extends ScreenProps {
  // Navigation Props
  shiftOpenStatus?: boolean;
  transactionSession?: TransactionTypeWithDisplay;
  enableServiceCharge?: boolean;
  splitedAmount?: number;
  currency?: string;
  localCountryMap?: any;
  roundingTo?: number;
  roundAllTransactions?: boolean;
  disabledDefaultPayments?: string[];
  enableCustomerShortCut?: boolean;
  enableLoyalty?: boolean;
  businessName?: string;
  enableCashback?: boolean;
  enableTableLayout?: boolean;
  tableLayoutEnabled?: boolean;
  transactionId?: string;
  isOpenOrder?: boolean;
  pid?: number;
  uniquePromos: any;
  customer?: CustomerType;
  eInvoiceEnabled?: boolean;
  freeTrial?: boolean;
  registerObjectId: string;
  trialOrderLimitEnabled?: boolean;
  selectedUniquePromo: PromotionType;
  isConnected: boolean;
  makeStoreCreditAsPayment: boolean;
  actions?: {
    clearTransactionSession(): void;
    checkoutTransaction(payload: CheckoutTransactionType): void;
    makeSalePayment(payload: MakeSalePaymentType): void;
    openDrawer;
    addCustomerToTransaction(payload: AddCustomerToTransactionType): void;
    cancelNextPayment;
    deleteCustomerFromTransaction(payload: deleteCustomerFromTransactionType): void;
    navigateToRegister(payload: NavigateType): void;
    getCustomerQRSetting(payload: GetCustomerQRSettingType): void;
    setTransactionSession(payload: TransactionTypeWithDisplay): void;
    clearUniquePromos(): void;
    lockUniquePromo(payload: lockUniquePromoType): void;
    returnUniquePromo(): void;
    stopCheckCustomerInfoFromQR(payload: StopCheckCustomerInfoFromQRType): void;
    checkPlaceOrderAvailable(payload: CheckPlaceOrderAvailableType): void;
    queryEInvoice(payload: QueryEInvoiceType): void;
  };
}

interface State {
  payInAmount: number;
  displayAmount: number;
  loading: boolean;
  isShowOtherPay: boolean;
  priorPaymentOptions: any[];
  otherPaymentOptions: any[];
}

const mapStateToProps = state => ({
  shiftOpenStatus: state.getIn(['Shift', 'ShiftOpenStatus']),
  transactionSession: fromImmutableTransactionSession(state),
  enableServiceCharge: state.getIn(['Storage', 'storeInfo', 'store', 'enableServiceCharge']),
  currency: state.getIn(['Storage', 'storeInfo', 'store', 'currency']),
  roundingTo: state.getIn(['Storage', 'storeInfo', 'store', 'roundingTo']),
  roundAllTransactions: state.getIn(['Storage', 'storeInfo', 'store', 'roundAllTransactions']),
  enableCustomerShortCut: state.getIn(['Settings', 'generalSettings', 'enableCustomerShortCut']),
  enableLoyalty: state.getIn(['Storage', 'storeInfo', 'store', 'enableLoyalty']),
  businessName: state.getIn(['Storage', 'storeInfo', 'name']),
  enableCashback: state.getIn(['Storage', 'storeInfo', 'store', 'enableCashback']),
  disabledDefaultPayments: fromImmutableDisabledDefaultPayments(state),
  enableTableLayout: state.getIn(['Settings', 'tableLayoutSettings', 'enableTableLayout']),
  tableLayoutEnabled: state.getIn(['Storage', 'storeInfo', 'store', 'tableLayoutEnabled']),
  transactionId: state.getIn(['TransactionSession', 'transactionId']),
  isOpenOrder: state.getIn(['TransactionSession', 'isOpen']),
  pid: selectSlavePid(state),
  uniquePromos: selectUniquePromos(state),
  customer: state.getIn(['TransactionSession', 'customer']),
  localCountryMap: selectLocalCountryMap(state),
  eInvoiceEnabled: selectEInvoiceEnabled(state),
  freeTrial: selectFreeTrial(state),
  registerObjectId: selectRegisterObjectId(state),
  trialOrderLimitEnabled: selectTrialOrderLimit(state),
  selectedUniquePromo: selectSelectedUniquePromo(state),
  isConnected: selectIsNetConnected(state),
  makeStoreCreditAsPayment: selectMakeStoreCreditAsPayment(state),
});

const fromImmutableTransactionSession = createSelector(
  (state: Immutable.Map<string, any>) => state.get('TransactionSession', Immutable.Map()),
  transactionSession => transactionSession.toJS()
);

const fromImmutableDisabledDefaultPayments = createSelector(
  (state: Immutable.Map<string, any>) => state.getIn(['Storage', 'storeInfo', 'store', 'disabledDefaultPayments'], Immutable.List()),
  disabledDefaultPayments => disabledDefaultPayments.toJS()
);

const mapDispatchToProps = dispatch => ({
  actions: bindActionCreators(
    {
      checkoutTransaction,
      makeSalePayment,
      clearTransactionSession,
      openDrawer,
      addCustomerToTransaction,
      cancelNextPayment,
      deleteCustomerFromTransaction,
      navigateToRegister,
      getCustomerQRSetting,
      setTransactionSession,
      clearUniquePromos,
      lockUniquePromo,
      returnUniquePromo,
      stopCheckCustomerInfoFromQR,
      checkPlaceOrderAvailable,
      queryEInvoice,
    },
    dispatch
  ),
});

const CASH = 'Cash';

const CASHOPTION = { paymentId: DefaultPaymentOptionType.Cash, type: CASH, name: CASH };

class Checkout extends PureComponent<Props, State> {
  private _splitedAmount?: number;
  private _splitPaySuccessed = false;
  private _backButtonListener;
  private _timeout: any;
  private transactionSessionCopy: TransactionTypeWithDisplay;
  private _transactionId: string;
  private viewShotRef;
  private viewShotCapture;
  private _inPayment = false;
  static navigationOptions = {
    headerShown: false,
  };

  constructor(props) {
    super(props);
    this._splitedAmount = getParam(this.props, 'splitedAmount');
    // payInAmount For the Cash Input
    const payInAmount = this._getCurrentDueAmount(DefaultPaymentOptionType.Cash);
    this.state = {
      payInAmount,
      displayAmount: undefined,
      loading: false,
      isShowOtherPay: false,
      priorPaymentOptions: [],
      otherPaymentOptions: [],
    };
  }

  setTransactionSessionCopy = () => {
    try {
      this.transactionSessionCopy = JSON.parse(JSON.stringify(this.props.transactionSession));
    } catch (e) {
      this.transactionSessionCopy = cloneDeep(this.props.transactionSession);
    }
  };
  restoreTransactionSession = payload => {
    if (this.transactionSessionCopy) {
      infoTransactionEvent({
        action: SaleFlowAction.RestoreCheckoutTransaction,
        // @ts-ignore
        transaction: this.transactionSessionCopy,
        privateDataPayload: {
          payload: stringify(payload),
        },
      });
      const { selectedUniquePromo } = this.props;
      if (Boolean(selectedUniquePromo)) {
        this.props.actions.returnUniquePromo();
      }
    }
  };

  componentWillReceiveProps(nextProps: Readonly<Props>, nextContext: any): void {
    if (isEmpty(this.props.transactionSession) && !isEmpty(nextProps.transactionSession)) {
      const { transactionSession, roundingTo, roundAllTransactions } = nextProps;
      const { dueAmount } = calculatePayAmount(transactionSession, DefaultPaymentOptionType.Cash, this._splitedAmount, roundingTo, roundAllTransactions);
      this.setState({
        payInAmount: dueAmount,
      });
    }
  }

  generatePaymentOptions = (isSupportNfc, filterEInvoice?: boolean) => {
    const { transactionSession, makeStoreCreditAsPayment, enableCashback, enableLoyalty } = this.props;
    const { transactionType, isOnlineOrder = false } = transactionSession;
    const isRefund = transactionType === TransactionFlowType.Return;
    const hasCreditCard = findIndex(this.props.disabledDefaultPayments, o => o === 'CreditCard') === -1;
    const hasDebitCard = findIndex(this.props.disabledDefaultPayments, o => o === 'DebitCard') === -1;
    const priorPaymentOptions = [];
    const otherPaymentOptions = [];
    const supportedPaymentOptions = PaymentOptions.getSupportedPaymentOptions();

    if (hasCreditCard) {
      priorPaymentOptions.push({
        paymentId: DefaultPaymentOptionType.CreditCard,
        type: 'Credit Card',
        name: t('Credit Card'),
        testId: 'CreditCard',
      });
    }
    if (makeStoreCreditAsPayment && enableLoyalty && !enableCashback && !isOnlineOrder) {
      const customer = get(transactionSession, 'customer');
      if (!isEmpty(customer)) {
        priorPaymentOptions.push({
          paymentId: DefaultPaymentOptionType.Loyalty,
          type: 'Store Credit',
          name: t('Store Credit'),
          testId: 'StoreCredit',
        });
      }
    }

    if (hasDebitCard) {
      priorPaymentOptions.push({
        paymentId: DefaultPaymentOptionType.DebitCard,
        type: 'Debit Card',
        name: t('Debit Card'),
        testId: 'DebitCard',
      });
    }
    const customPaymentOptions = filter(
      supportedPaymentOptions,
      paymentOption =>
        !(
          paymentOption.isDeleted ||
          paymentOption.isDisabled ||
          paymentOption.paymentId < 4 || // internal payments
          ((paymentOption.type === PaymentType.NFC || paymentOption.type === PaymentType.QRCODE || paymentOption.type === PaymentType.TERMINAL) && isRefund) ||
          (paymentOption.type === PaymentType.NFC && !isSupportNfc)
        )
    );
    const totalPaymentOptionsCount = priorPaymentOptions.length + customPaymentOptions.length;
    const showOther = totalPaymentOptionsCount > 6;
    const priorPaymentOptionsExpectedCount = showOther ? 5 : 6;
    forEach(customPaymentOptions, paymentOption => {
      if (priorPaymentOptions.length < priorPaymentOptionsExpectedCount) {
        priorPaymentOptions.push({ ...paymentOption, testId: paymentOption.name });
      } else {
        otherPaymentOptions.push({ ...paymentOption, testId: paymentOption.name });
      }
    });
    if (showOther) {
      priorPaymentOptions.push({
        paymentId: -1,
        type: null,
        name: t('Others'),
        testId: 'Others',
        label: t('Third-party ewallet'),
      });
    }
    this.setState({
      priorPaymentOptions,
      otherPaymentOptions,
    });
  };

  componentDidMount() {
    IsAndroid ? isSupportNfcPayment(this.generatePaymentOptions) : this.generatePaymentOptions(false);
    const { transactionId, isOpenOrder } = this.props;
    if (Boolean(isOpenOrder)) {
      this._transactionId = transactionId;
      MRSConfig.addTransactionToBlackList(transactionId);
    }
    if (IsAndroid) {
      this._backButtonListener = BackHandler.addEventListener('hardwareBackPress', () => {
        if (this.state.loading) {
          return false;
        }
        this.closeButtonClicked();
        return true;
      });
      this.viewShotCapture = setInterval(() => {
        // code to be executed every 1 second
        if (this.viewShotRef) {
          this.viewShotRef?.capture();
        }
      }, 1500);
    }
    this.checkEInvoice();
  }

  checkEInvoice() {
    const { transactionSession } = this.props;
    const transactionType = get(transactionSession, 'transactionType');
    const isRefund = transactionType === TransactionFlowType.Return;

    if (this.props.eInvoiceEnabled && isRefund && this.props.transactionId) {
      if (!transactionSession || !transactionSession.receiptNumber) {
        return;
      }
      this.props.actions.queryEInvoice({
        // @ts-ignore
        transaction: { receiptNumber: transactionSession.receiptNumber },
        onResponse: (eInvoiceStatus, documentType) => {
          console.log(eInvoiceStatus, documentType);
          if (!eInvoiceStatus) {
            return;
          }
          if (eInvoiceStatus === EInvoiceStatus.SUBMITTED || eInvoiceStatus === EInvoiceStatus.VALID) {
            IsAndroid ? isSupportNfcPayment(nfc => this.generatePaymentOptions(nfc, true)) : this.generatePaymentOptions(false, true);
          }
        },
      });
    }
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    this._splitedAmount = getParam(nextProps, 'splitedAmount');
  }

  componentWillUnmount() {
    this._backButtonListener && this._backButtonListener.remove();
    this._timeout && clearTimeout(this._timeout);
    this.makeSalePay.cancel();
    this.makeRefundPay.cancel();
    if (this._transactionId) {
      MRSConfig.removeTransactionFromBlackList(this._transactionId);
    }
    IminLcdManager.clear();
    this.viewShotCapture && clearInterval(this.viewShotCapture);
  }

  closeButtonClicked = () => {
    if (this.state.loading) return;
    const {
      transactionSession,
      navigation,
      actions: { cancelNextPayment, clearTransactionSession },
    } = this.props;
    const { totalPaid = 0 } = transactionSession;
    const { navigate, goBack } = navigation;
    const isReturn = getParam(this.props, 'isReturn');
    const needClearTransactionWhenBack = getParam(this.props, 'needClearTransactionWhenBack', false);
    if (needClearTransactionWhenBack) {
      clearTransactionSession();
    }
    // @ts-ignore
    const isPickUp = isPreOrderPickUp(transactionSession);
    if (isReturn || isPickUp) {
      goBack();
    } else if (totalPaid > 0) {
      navigate('ModalInfo', {
        info: t('Abandon split payment and refund previous transactions?'),
        onCancelHandler: () => {
          // NOP
        },
        onSubmitHandler: () => {
          cancelNextPayment();
          goBack();
        },
      });
    } else {
      const onGoBackToRefresh = getParam(this.props, 'onGoBackToRefresh');
      onGoBackToRefresh && onGoBackToRefresh();
      goBack();
    }
  };

  // rounding the unpaid value
  _calculateUnpaidTotal = (type: number): number => {
    const { transactionSession, roundingTo, roundAllTransactions } = this.props;
    return calculateUnpaidTotal(transactionSession, type, roundingTo, roundAllTransactions);
  };

  // get rounded unpaid value or rounded splited value
  _getCurrentDueAmount = (type: number = DefaultPaymentOptionType.Cash): number => {
    const { transactionSession, roundingTo, roundAllTransactions } = this.props;
    const { dueAmount } = calculatePayAmount(transactionSession, type, this._splitedAmount, roundingTo, roundAllTransactions);
    return dueAmount;
  };

  makeSalePay = throttle(
    (paymentId: number, tenderedAmount: number, paymentType: string, ewalletInfo?, paymentExtras?) => {
      // Make Payment
      // commit Transaction
      this.setState({ loading: true });
      const { businessName, enableCashback, transactionSession } = this.props;
      const { customer, tableId, isOnlineOrder } = transactionSession;
      this.setTransactionSessionCopy();
      const onComplete = {
        callback: payload => {
          const {
            result,
            errorCode,
            err,
            dueAmount,
            changeAmount,
            total,
            transactionId,
            editableTimeAfterForPayLaterOrder,
            beepOrderLoyaltyInfo,
            transaction,
          } = payload;
          if (result) {
            if (paymentId === DefaultPaymentOptionType.Cash) {
              this.props.actions.openDrawer();
            }
            if (dueAmount === 0) {
              const onCheckOutComplete = {
                callback: (payload: CheckOutResultType) => {
                  const isSuccess = !payload.mrs || onMRSInterceptor(payload);
                  if (isSuccess) {
                    requestAnimationFrame(() => {
                      const loyaltyInfo = get(payload, 'loyaltyInfo') || beepOrderLoyaltyInfo;
                      const curCustomer = customer;
                      if (loyaltyInfo && customer) {
                        curCustomer.isStoreCredit = true;
                        curCustomer.loyaltyEarned = getNumberValue(loyaltyInfo, 'loyaltyEarned', 0);
                        curCustomer.loyaltyBalance = getNumberValue(loyaltyInfo, 'loyaltyBalance', 0);
                        curCustomer.loyaltySpent = getNumberValue(loyaltyInfo, 'loyaltySpent', 0);
                        const cashbackExpirationDate = get(loyaltyInfo, 'cashbackExpirationDate');
                        if (!isEmpty(cashbackExpirationDate)) {
                          curCustomer.cashbackExpirationDate = cashbackExpirationDate;
                        }
                      }

                      // pay split payLater order will update the transactionSession.receiptNumber
                      // so need to re-get
                      const receiptNumber = transaction.receiptNumber;
                      this.props.navigation.replace('PaymentConfirm', {
                        remainingAmount: dueAmount,
                        totalPaid: tenderedAmount,
                        total,
                        changeAmount,
                        transactionId,
                        isOnlineOrder,
                        needClearTransaction: true,
                        orderId: receiptNumber,
                        customer: curCustomer,
                        showEmailReceipt: !isOnlineOrder,
                      });
                    });
                  } else {
                    this.restoreTransactionSession(payload);
                    this.setState({ loading: false });
                    this._inPayment = false;
                  }
                },
              };
              this.props.actions.checkoutTransaction({
                transaction,
                businessName,
                enableCashback,
                customer,
                onComplete: onCheckOutComplete,
              });
            } else {
              // pay split payLater order will update the transactionSession.receiptNumber
              // so need to re-get
              const receiptNumber = transaction.receiptNumber;
              // Later Split Payment
              this._splitPaySuccessed = true;
              requestAnimationFrame(() =>
                this.props.navigation.replace('PaymentConfirm', {
                  remainingAmount: dueAmount,
                  totalPaid: tenderedAmount,
                  total,
                  changeAmount,
                  transactionId,
                  isOnlineOrder,
                  orderId: receiptNumber,
                  customer,
                  dueAmount,
                  showEmailReceipt: !isOnlineOrder,
                })
              );
            }
          } else {
            this.restoreTransactionSession(payload);
            this.setState({ loading: false });
            const onRefreshOpenOrderList = getParam(this.props, 'onRefreshOpenOrderList', null);
            if (isOnlineOrder) onRefreshOpenOrderList && onRefreshOpenOrderList();
            // editablTime Modal already showed when gprahql returned error
            if (isEmpty(editableTimeAfterForPayLaterOrder)) {
              Alert.alert(err, '', [
                {
                  text: t('OK'),
                  onPress: () => {
                    if (errorCode === MakePaymentErrorType.LessAmount) {
                      const payInAmount = this._getCurrentDueAmount(DefaultPaymentOptionType.Cash);
                      this.setState({ payInAmount, displayAmount: undefined });
                    }
                    if (isOnlineOrder && err == 'The order has paid') {
                      this.props.actions.clearTransactionSession();
                      this.props.navigation.goBack();
                    }
                  },
                },
              ]);
            } else {
              const displayTime = moment(editableTimeAfterForPayLaterOrder).format('hh:mmA');
              this.props.navigation.navigate('ModalInfo', {
                title: t('This order is being checked out by customer'),
                isShowTitle: true,
                textAlign: 'center',
                info: t('Order can be edited at', { editableTimeAfterForPayLaterOrder: displayTime }),
              });
            }
            this._inPayment = false;
          }
        },
      };

      this.props.actions.makeSalePayment({
        paymentId,
        tenderedAmount,
        tableId,
        ewalletInfo,
        paymentType,
        splitedAmount: this._splitedAmount,
        onComplete,
        paymentExtras,
        isOnlineOrder,
      });
    },
    1000,
    { leading: true, trailing: false }
  );

  makeRefundPay = throttle(
    (paymentId: number, tenderedAmount: number, paymentType: string, ewalletInfo?, paymentExtras?: any) => {
      // Make Payment
      // commit Transaction
      this.setState({ loading: true });
      const refreshData = getParam(this.props, 'refreshData', null);
      const { transactionSession } = this.props;
      const { tableId } = transactionSession;
      this.setTransactionSessionCopy();
      const onComplete = {
        callback: payload => {
          const { result, dueAmount, transactionType, transaction } = payload;
          if (result) {
            if (paymentId === DefaultPaymentOptionType.Cash) {
              this.props.actions.openDrawer();
            }
            if (transactionType === TransactionFlowType.Return) {
              this.setState({ loading: false });
              this.props.navigation.navigate('RefundReason', {
                remainingAmount: dueAmount,
                transaction,
                totalPaid: tenderedAmount,
                isFullScreen: true,
                refreshData,
                eInvoiceEnabled: this.props.eInvoiceEnabled,
                onManualGoBack: this.onRefundReasonManualGoBack,
              });
              return;
            }
          } else {
            this.restoreTransactionSession(payload);
            this.setState({ loading: false });
            this._inPayment = false;
          }
        },
      };

      this.props.actions.makeSalePayment({
        paymentId,
        tenderedAmount,
        tableId,
        ewalletInfo,
        paymentType,
        splitedAmount: this._splitedAmount,
        onComplete,
        paymentExtras,
      });
    },
    1000,
    { leading: true, trailing: false }
  );

  onRefundReasonManualGoBack = () => {
    this._inPayment = false;
  };

  // 0 cash, 1 credit card, 2 store credit, 3 debit card
  makePay = (paymentId: number, tenderedAmount: number, paymentType: string, ewalletInfo?, paymentExtras?: any) => {
    if (this._inPayment) return;
    this._inPayment = true;
    const { transactionSession } = this.props;
    const transactionType = get(transactionSession, 'transactionType');
    const isRefund = transactionType === TransactionFlowType.Return;
    isRefund
      ? this.makeRefundPay(paymentId, tenderedAmount, paymentType, ewalletInfo, paymentExtras)
      : this.makeSalePay(paymentId, tenderedAmount, paymentType, ewalletInfo, paymentExtras);
  };

  onSplitPaymentPressed = () => {
    this.props.navigation.navigate('ModalSplitPayment', { unpaidAmount: this._calculateUnpaidTotal(1) });
  };

  cashPayWithCustomAmount = () => {
    const { payInAmount } = this.state;
    this._clickPay(CASHOPTION, payInAmount);
  };

  cashPayWithDueAmount = () => {
    this._clickPay(CASHOPTION);
  };

  _renderCash = () => {
    return (
      <View style={styles.paymentRow}>
        <TouchableOpacity
          {...testProps('Cash')}
          style={[styles.solidButtonCash, { backgroundColor: currentThemes.buttonBackgroundColor }]}
          onPress={this.cashPayWithDueAmount}
        >
          <Text style={styles.borderButtonText}>{t(CASH)}</Text>
        </TouchableOpacity>
      </View>
    );
  };

  onChangeExactText = value => {
    checkPriceNumberInput(value, value => {
      if (!isNaN(value)) {
        this.setState({ payInAmount: value, displayAmount: value });
      }
    });
  };

  onInputPayAmountKeyPress = ({ nativeEvent: { key } }) => {
    if (key === 'Backspace') {
      this._timeout && clearTimeout(this._timeout);
      this._timeout = setTimeout(() => {
        this.onChangeExactText('0');
      }, 1);
    }
  };

  _renderExact = () => {
    const {
      transactionSession: { transactionType },
    } = this.props;
    const { displayAmount, payInAmount } = this.state;

    const _displayAmount = Boolean(displayAmount) ? localeNumber(displayAmount) : undefined;
    const isPreOrder = transactionType === TransactionFlowType.PreOrder;
    const cashDueAmount = this._getCurrentDueAmount(DefaultPaymentOptionType.Cash);
    return (
      <View>
        <View style={styles.paymentRow}>
          <View style={styles.inputContent}>
            <View style={styles.iconStyle}>
              <IconCash color={'#303030'} />
            </View>
            <Text style={[styles.borderButtonText, { color: '#303030' }]}>{isPreOrder ? 'Deposit to pay' : t('Custom')}</Text>
            <CurrencyTextInput
              style={[styles.borderButtonText, { flex: 1, textAlign: 'right', color: 'black', letterSpacing: 1 }]}
              keyboardType='number-pad'
              autoCapitalize='none'
              returnKeyType='done'
              value={_displayAmount}
              onSubmitEditing={this.cashPayWithCustomAmount}
              placeholderTextColor={'#9E9E9E'}
              onKeyPress={this.onInputPayAmountKeyPress}
              onChangeText={this.onChangeExactText}
              placeholder={localeNumber(payInAmount)}
              underlineColorAndroid='transparent'
            />
          </View>
          <TouchableOpacity
            sentry-label='al_exact'
            {...testProps('al_exact')}
            style={[styles.solidButton, { backgroundColor: '#00A86B' }]}
            onPress={this.cashPayWithDueAmount}
          >
            <Text style={styles.borderButtonText}>{t('EXACT')}</Text>
          </TouchableOpacity>
        </View>
        <View style={[styles.paymentRow, { marginTop: scaleSizeH(32) }]}>
          {map(calculateCashShortcut(cashDueAmount), (amount: number, index: number) => {
            return <CashShortcutItem key={`${index}-${amount}`} amount={amount} paymentItem={CASHOPTION} onClick={this._clickPay} />;
          })}
        </View>
      </View>
    );
  };

  getPayOptionIcon = (paymentId, paymentType, color) => {
    if (paymentId === DefaultPaymentOptionType.CreditCard) {
      return <IconCreditCard color={color} />;
    } else if (paymentId === DefaultPaymentOptionType.DebitCard) {
      return <IconDebitCard color={color} />;
    } else if (paymentId === -1) {
      return <IconWallet color={color} />;
    } else if (paymentId === DefaultPaymentOptionType.Loyalty) {
      return <IconStoreCredit color={color} />;
    } else if (paymentType === PaymentType.NFC) {
      return <IconWave color={color} />;
    } else if (paymentType === PaymentType.QRCODE) {
      return <IconQRCode color={color} />;
    } else if (paymentType === PaymentType.TERMINAL) {
      return (
        <View style={{ flexDirection: 'row' }}>
          <IconCreditCard color={color} />
          <IconWave color={color} />
          <IconQRCode color={color} />
          <View style={{ width: scaleSize(80) }} />
        </View>
      );
    }
    return <IconWallet color={color} />;
  };

  _renderPriorItem = ({ item }) => {
    return <PaymentOptionItem payment={item} getIcon={this.getPayOptionIcon} onClick={this._clickPay} style={styles.paymentOptionItem} />;
  };

  _renderPay = () => {
    const { priorPaymentOptions } = this.state;

    return (
      <FlatList
        key={'prior_items'}
        style={styles.priorFlatStyle}
        renderItem={this._renderPriorItem}
        data={priorPaymentOptions}
        keyExtractor={this._keyExtractor}
        numColumns={3}
        horizontal={false}
      />
    );
  };

  sendImage = uri => {
    if (IsAndroid && (isIminFalcon1() || isIminD1())) {
      const routes = this.props.navigation.getState().routes;
      if (routes[routes.length - 1].name == 'Checkout') {
        console.log('lcd captured Checkout.tsx ' + Date.now());
        IminLcdManager.sendImage(uri);
      }
    }
  };

  // @ts-ignore
  renderViewShot = () => {
    if (!(IsAndroid && (isIminFalcon1() || isIminD1()))) {
      return null;
    }

    const { transactionSession, currency, localCountryMap } = this.props;
    const currencySymbol = newConvertCurrencyToSymbol(localCountryMap, currency);
    const { transactionType, totalPaid = 0 } = transactionSession;
    const isPreorder = transactionType === TransactionFlowType.PreOrder;

    // const qrData = null;
    // const uri = 'data:image/png;base64,' + qrData;
    //
    // const colorMap = {
    //   'MayBank': '#E14B7F', // DuitNow
    //   'TnGPay': '#005BA9',
    //   'Boost': '#EE2E24',
    //   'GrabPay': '#00B14F',
    //   'kiplePay': '#1B9AD7',  // scan
    //   'Razer-RazerPay': '#00B14F',
    //   'Razer-Alipay': '#00A0E9',
    //   'Razer-WeChatPay': '#28AA42',
    //   'Razer-TnGPay': '#005BA9', // scan
    //   'Razer-Boost': '#EE2E24',
    //   'Razer-GrabPay': '#00B14F',
    //   'Razer-MaybankQR': '#FFDD00', // DuitNow
    // }

    const typeText =
      isNaN(this._splitedAmount) && totalPaid > 0 && !isPreorder ? t('Remaining') + ':' : (Boolean(this._splitedAmount) ? t('Split') : 'Total') + ':';
    const amountText = localeNumber(this._getCurrentDueAmount(DefaultPaymentOptionType.Loyalty));

    return (
      <ViewShot
        ref={ref => {
          this.viewShotRef = ref;
        }}
        captureMode={'mount'}
        onCapture={this.sendImage}
        style={{ width: 320, height: 245, backgroundColor: '#ffffff', position: 'absolute', top: -300 }}
        options={{ width: 320, height: 240 }}
      >
        <View>
          <Text
            style={{
              fontWeight: '900',
              fontSize: 26,
              textAlign: 'left',
              color: '#000000',
              paddingLeft: 10,
              paddingBottom: -40,
              paddingTop: 70,
              position: 'absolute',
            }}
          >
            {typeText}
          </Text>
          <View style={{ alignItems: 'flex-end', justifyContent: 'flex-end', flexDirection: 'row', paddingTop: 80 }}>
            <Text
              style={{
                fontWeight: '900',
                fontSize: 28,
                textAlign: 'left',
                color: '#000000',
                paddingBottom: 16,
              }}
            >
              {currencySymbol}
            </Text>
            <Text
              style={{
                fontWeight: 'bold',
                fontSize: 56,
                textAlign: 'left',
                color: '#000000',
                padding: 10,
              }}
            >
              {amountText}
            </Text>
          </View>
        </View>
      </ViewShot>
    );
    // }
    // return <View/>
  };

  render() {
    const {
      enableCustomerShortCut,
      shiftOpenStatus,
      transactionSession,
      enableServiceCharge,
      currency,
      enableCashback,
      enableLoyalty,
      localCountryMap,
      makeStoreCreditAsPayment,
    } = this.props;
    const currencySymbol = newConvertCurrencyToSymbol(localCountryMap, currency);
    const { transactionType, customer, totalPaid = 0, total = 0, depositAmount, customerId } = transactionSession;
    const { isShowOtherPay, loading } = this.state;
    const isRefund = transactionType === TransactionFlowType.Return;
    const isPreorder = transactionType === TransactionFlowType.PreOrder;
    const items = get(transactionSession, 'items', []);
    const isOnlineOrder = get(transactionSession, 'isOnlineOrder', false);
    const count = getDisplayItemsCount(items);
    const remaining = (
      total -
      (totalPaid || 0) -
      (isPreorder ? depositAmount : 0) -
      (this._splitPaySuccessed ? 0 : this._getCurrentDueAmount(DefaultPaymentOptionType.Loyalty))
    ).toFixed(2);
    const displayItemsCountText = count + (count === 1 ? t('Item') : t('Items'));
    const customerDisabled = isOnlineOrder && !Boolean(customerId);
    const shouldRenderDeposit = isPreOrderPickUp(transactionSession);

    return (
      <View style={styles.container}>
        {this.renderViewShot()}
        <TopMainBar
          isLeftContainerTitle={false}
          leftIcon={<IconLeft width={scaleSizeW(48)} height={scaleSizeH(49)} color={'#303030'} />}
          leftText={displayItemsCountText}
          rightIsIcon={false}
          leftButtonDisabled={loading}
          rightButtonText={t('SPLIT BILL')}
          onRightClick={this.onSplitPaymentPressed}
          // online order no split bill
          isRightButtonShow={!isRefund && !isPreorder && !isOnlineOrder}
          onLeftClick={this.closeButtonClicked}
        />
        <View style={styles.content}>
          <View style={styles.leftContent}>
            <View style={styles.transactionContent}>
              <Cart
                disableCustomerEdit={isOnlineOrder}
                showLoyalty={!isOnlineOrder}
                isDiscountHighLight={false}
                customerDisabled={customerDisabled}
                onRemoveCustomerClick={this.onUnlinkCustomer}
                shiftOpenStatus={shiftOpenStatus}
                transactionSession={transactionSession}
                cartHeight={scaleSizeH(856)}
                enableCustomerShortCut={enableCustomerShortCut}
                onAddCustomerClick={this.onCustomerClick}
                customer={customer}
                currency={currency}
                shouldRenderDeposit={shouldRenderDeposit}
                enableServiceCharge={transactionSession.salesChannel === SalesChannelType.TAKEAWAY ? false : enableServiceCharge}
                disableLeftSwipe={true}
                disableRightSwipe={true}
                // eslint-disable-next-line @typescript-eslint/no-empty-function
                onFullBillDiscountPressHandler={() => {}}
                // eslint-disable-next-line @typescript-eslint/no-empty-function
                onDeletePurchasedItemHandler={() => {}}
                // eslint-disable-next-line @typescript-eslint/no-empty-function
                onSelectedPurchasedItem={() => {}}
                // eslint-disable-next-line @typescript-eslint/no-empty-function
                onServiceChargePressHandler={() => {}}
                enableLoyalty={enableLoyalty}
                enableCashback={enableCashback}
                isInCheckout={true}
                makeStoreCreditAsPayment={makeStoreCreditAsPayment}
              />
            </View>
          </View>
          <View style={styles.rightContent}>
            <Loading style={styles.loading} loadingMaskIsVisible={loading} disableInteractions={true} />
            <Text style={styles.amountText} {...testProps('amountText')}>
              {(Boolean(this._splitedAmount) ? t('Split') + ' ' : '') +
                currencySymbol +
                ' ' +
                localeNumber(this._getCurrentDueAmount(DefaultPaymentOptionType.Loyalty)) +
                (isNaN(this._splitedAmount) && totalPaid > 0 && !isPreorder ? ' ' + t('Remaining') : '')}
            </Text>
            <Text style={styles.remainingText} {...testProps('remainingText')}>
              {Boolean(this._splitedAmount) && Number(remaining) > 0 ? `${t('Remaining')} ${currencySymbol} ${remaining}` : ''}
            </Text>
            {isShowOtherPay && (
              <TouchableOpacity {...testProps('al_btn_729')} style={styles.backPayment} onPress={this.showDefaultPayment}>
                <Text style={styles.backButtonText}>{t('BACK TO PAYMENT')}</Text>
              </TouchableOpacity>
            )}
            {isShowOtherPay ? (
              this._renderOtherPays()
            ) : (
              <View style={styles.paymentOptions}>
                {isRefund ? this._renderCash() : this._renderExact()}
                {this._renderPay()}
              </View>
            )}
          </View>
        </View>
      </View>
    );
  }

  _keyExtractor = (item, index) => item.paymentId + item.name + index;

  _renderRow = ({ item }) => {
    const { _id } = item;
    return <CustomPaymentItem key={_id} customPayment={item} onClick={this._clickPay} />;
  };

  _renderOtherPays = () => {
    const newArr = [];
    const { otherPaymentOptions } = this.state;
    map(otherPaymentOptions, (item, index) => {
      if (index % 12 === 0) {
        newArr.push(otherPaymentOptions.slice(index, index + 12));
      }
    });
    return (
      <View style={styles.paymentLayout}>
        <Swiper
          showsButtons={false}
          removeClippedSubviews={false}
          autoplay={false}
          horizontal={true}
          dot={<View style={styles.dotStyle} />}
          activeDot={<View style={styles.activeDotStyle} />}
          paginationStyle={{ bottom: scaleSizeH(70), alignSelf: 'center' }}
        >
          {map(newArr, (item, index) => {
            return (
              <FlatList
                key={index}
                style={styles.flatStyle}
                renderItem={this._renderRow}
                data={item}
                keyExtractor={this._keyExtractor}
                numColumns={4}
                horizontal={false}
              />
            );
          })}
        </Swiper>
      </View>
    );
  };

  onGetEnableCustomerQR = () => {
    return new Promise(resolve => {
      const onComplete = (enabled: boolean) => {
        resolve(enabled);
      };
      this.props.actions.getCustomerQRSetting({ onComplete });
    });
  };

  onAddCustomerClick = async () => {
    const { transactionSession } = this.props;
    const transactionType = get(transactionSession, 'transactionType');
    const enableCustomerQR = await this.onGetEnableCustomerQR();

    if (enableCustomerQR && transactionType !== TransactionFlowType.Return) {
      this.props.navigation.navigate('ModalCustomerQR', { isInCheckOut: true });
    } else {
      this.props.navigation.navigate('AddCustomer', {
        onCustomerInfoHandler: this.onCustomerInfoHandler,
        isInCheckOut: true,
      });
    }
  };

  onCustomerClick = () => {
    const { customer } = this.props;
    customer
      ? this.props.navigation.navigate('CustomerDetail', {
          customer: customer,
          isInCheckOut: true,
        })
      : this.onAddCustomerClick();
  };

  onUnlinkCustomer = () => {
    const { selectedUniquePromo, navigation } = this.props;
    if (Boolean(selectedUniquePromo)) {
      navigation.navigate('ModalInfo', {
        title: t('Applied Promo Will Be Removed'),
        isShowTitle: true,
        info: t('This customer has a promo applied, proceed to remove customer information?'),
        okText: t('Proceed to Remove'),
        cancelText: t('Cancel'),
        onSubmitHandler: () => {
          this.props.actions.deleteCustomerFromTransaction({
            checkCustomerWithBIR: this.checkCustomerWithBIR,
          });
        },
        onCancelHandler: noop,
      });
    } else {
      this.props.actions.deleteCustomerFromTransaction({
        checkCustomerWithBIR: this.checkCustomerWithBIR,
      });
    }
  };

  checkCustomerWithBIR = ({ enable, message }) => {
    if (!enable) {
      this.props.navigation.navigate('ModalInfo', { info: message, textAlign: 'left' });
    }
  };

  onCustomerInfoHandler = customer => {
    this.props.actions.addCustomerToTransaction({ customerId: customer.customerId, customer: customer });
  };

  _clickPay = (paymentOption: PaymentOptionType, amount?) => {
    const { paymentId } = paymentOption;
    if (paymentId === -1) {
      this.showOtherPayment();
      return;
    }
    const {
      selectedUniquePromo,
      navigation,
      actions: { stopCheckCustomerInfoFromQR, clearUniquePromos, lockUniquePromo, checkPlaceOrderAvailable },
      transactionId,
      freeTrial,
      registerObjectId,
      trialOrderLimitEnabled,
    } = this.props;
    if (!checTrialOrderLimit(trialOrderLimitEnabled, freeTrial, registerObjectId, transactionId)) return;
    const onComplete = {
      callback: payload => {
        const { isPlaceOrderAvailable, title, message } = payload;
        if (isPlaceOrderAvailable) {
          stopCheckCustomerInfoFromQR({});
          if (Boolean(selectedUniquePromo)) {
            const onSuccess = () => {
              this._handlePaymentLogic(paymentOption, amount);
            };
            const onFailure = () => {
              navigation.navigate('ModalInfo', {
                title: t('Customer has already redeemed this promo'),
                isShowTitle: true,
                info: t('Promo is not applicable and will be removed'),
                okText: t('Proceed to Remove'),
                onSubmitHandler: () => {
                  clearUniquePromos();
                },
              });
            };
            lockUniquePromo({
              onSuccess,
              onFailure,
            });
          } else {
            this._handlePaymentLogic(paymentOption, amount);
          }
        } else {
          navigateModalWarningLocalTime(title, message, navigation);
        }
      },
    };
    checkPlaceOrderAvailable({ onComplete, callLocation: CheckPlaceOrderAvailableLocation.Checkout });
  };

  _handlePaymentLogic = (paymentOption: PaymentOptionType, amount?) => {
    const { paymentId, type } = paymentOption;
    // toggle show/dismiss other payment
    if (paymentId === DefaultPaymentOptionType.Cash && amount !== undefined) {
      // Cash with custom amount
      this.makePay(paymentId, amount, type);
    } else if (paymentId === DefaultPaymentOptionType.Loyalty) {
      const { transactionSession, navigation } = this.props;
      const dueAmount = this._getCurrentDueAmount(paymentId);
      const loyaltyBalance = get(transactionSession, ['customer', 'loyalty'], 0);
      const transactionType = get(transactionSession, 'transactionType');
      const isRefund = transactionType === TransactionFlowType.Return;
      if (convertToNumber(loyaltyBalance) < dueAmount && !isRefund) {
        navigation.navigate('ModalInfo', {
          title: t('Error'),
          isShowTitle: true,
          info: t('Store credit of this customer is less than amount due'),
        });
      } else {
        this.makePay(paymentId, dueAmount, type);
      }
    } else {
      // Cash CreditCard Loyalty DebitCard and other payment
      const dueAmount = this._getCurrentDueAmount(paymentId);
      switch (type) {
        case PaymentType.QRCODE:
          this.clickEWalletPay(paymentOption, dueAmount);
          break;
        case PaymentType.NFC:
          this.clickNfcPay(paymentOption, dueAmount);
          break;
        case PaymentType.TERMINAL:
          this.clickGHLPay(paymentOption, dueAmount);
          break;
        default:
          this.makePay(paymentId, dueAmount, type);
          break;
      }
    }
  };

  onlinePayFail = () => {
    const { selectedUniquePromo } = this.props;
    if (Boolean(selectedUniquePromo)) {
      this.props.actions.returnUniquePromo();
    }
  };

  clickNfcPay = (paymentOption: PaymentOptionType, tenderedAmount: number) => {
    const { transactionSession } = this.props;
    const receiptNumber = get(transactionSession, 'receiptNumber');
    const transactionId = get(transactionSession, 'transactionId');
    if (paymentOption && paymentOption.type === PaymentType.NFC) {
      this.props.navigation.navigate('NfcPay', {
        paymentOption: paymentOption,
        paySuccess: this.makePay,
        payFail: this.onlinePayFail,
        orderId: receiptNumber,
        transactionId,
        amount: tenderedAmount,
        paymentType: paymentOption.type,
      });
    }
  };

  clickEWalletPay = (paymentOption: PaymentOptionType, tenderedAmount: number) => {
    const { isConnected } = this.props;
    if (!isConnected) {
      this.props.navigation.navigate('ModalInfo', {
        title: t('No Internet Connection'),
        isShowTitle: true,
        info: t('Please check your POS internet connection and try again'),
        titleIcon: <RedWarning width={scaleSizeW(40)} height={scaleSizeW(40)} color={'#303030'} />,
      });
      return;
    }

    if (paymentOption && paymentOption.type === 'qrCode') {
      this.props.navigation.navigate('ModalEWalletQRCode', {
        paymentOption: paymentOption,
        paySuccess: this.makePay,
        payFail: this.onlinePayFail,
        amount: tenderedAmount,
        paymentType: paymentOption.type,
      });
    }
  };

  clickGHLPay = (paymentOption: PaymentOptionType, tenderedAmount: number) => {
    const { transactionSession } = this.props;
    const transactionType = get(transactionSession, 'transactionType');
    const receiptNumber = get(transactionSession, 'receiptNumber');
    const transactionId = get(transactionSession, 'transactionId');
    if (
      paymentOption &&
      paymentOption.type === PaymentType.TERMINAL &&
      (paymentOption as GhlPaymentOptionType).additionalInfo.paymentTerminalProvider === 'GHL'
    ) {
      this.props.navigation.navigate('ModalGhlPay', {
        paymentOption: paymentOption,
        orderId: receiptNumber,
        transactionId: transactionId,
        amount: tenderedAmount,
        requestType: transactionType === TransactionFlowType.Return ? GhlRequestType.Refund : GhlRequestType.Sale,
        paySuccess: this.makePay,
        payFail: this.onlinePayFail,
      });
    }
  };

  showOtherPayment = () => {
    this.setState({
      isShowOtherPay: true,
    });
  };

  showDefaultPayment = () => {
    this.setState({
      isShowOtherPay: false,
    });
  };
}

// @ts-ignore
export default connect(mapStateToProps, mapDispatchToProps)(Checkout);

const RIGHT_CONTENT_WIDTH = width * 0.69;
const PRIOR_PAYMENT_WIDTH = (RIGHT_CONTENT_WIDTH - 2 * scaleSizeW(50) - scaleSizeW(6 * 17)) / 3;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F6FA',
  },
  borderButtonText: {
    fontWeight: '500',
    fontSize: currentThemes.fontSize24,
    color: 'white',
  },
  backButtonText: {
    fontWeight: 'bold',
    fontSize: currentThemes.fontSize18,
    color: '#393939',
  },
  paymentRow: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    height: scaleSizeH(80),
  },
  solidButton: {
    flex: 1,
    marginLeft: -1,
    marginRight: scaleSizeW(16),
    backgroundColor: '#FFF',
    height: scaleSizeH(80),
    justifyContent: 'center',
    alignItems: 'center',
    borderBottomRightRadius: scaleSizeW(8),
    borderTopRightRadius: scaleSizeW(8),
  },
  paymentOptions: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'space-between',
  },
  priorFlatStyle: {
    width: '100%',
    marginTop: scaleSizeH(60),
  },
  paymentOptionItem: {
    backgroundColor: '#FFFFFF',
    borderColor: '#7B90A6',
    borderWidth: 0.6,
    marginTop: 20,
    width: PRIOR_PAYMENT_WIDTH,
  },
  solidButtonCash: {
    flex: 1,
    marginHorizontal: scaleSizeW(16),
    backgroundColor: '#FFF',
    height: scaleSizeH(80),
    justifyContent: 'center',
    alignItems: 'center',
  },
  loading: {
    ...StyleSheet.absoluteFillObject,
    flexDirection: 'column',
    justifyContent: 'center',
  },
  inputContent: {
    width: scaleSizeW(780),
    paddingLeft: scaleSizeW(16),
    paddingRight: scaleSizeW(22),
    marginLeft: scaleSizeW(16),
    height: scaleSizeH(80),
    backgroundColor: '#FFF',
    flexDirection: 'row',
    alignItems: 'center',
    borderTopLeftRadius: scaleSizeH(8),
    borderBottomLeftRadius: scaleSizeH(8),
    borderWidth: 0.6,
    borderColor: '#00A86B',
  },
  iconStyle: {
    width: scaleSizeW(48),
    height: scaleSizeH(48),
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    flexDirection: 'row',
  },
  leftContent: {
    width: width * 0.31,
  },
  transactionContent: {
    flex: 1,
    backgroundColor: '#FFF',
    paddingBottom: scaleSizeH(24),
  },
  rightContent: {
    width: RIGHT_CONTENT_WIDTH,
    paddingHorizontal: scaleSizeW(50),
  },
  amountText: {
    fontSize: currentThemes.fontSize100,
    fontWeight: 'bold',
    color: '#303030',
    alignSelf: 'center',
    marginTop: scaleSizeH(62),
    marginBottom: scaleSizeH(2),
    textAlign: 'center',
    textAlignVertical: 'center',
    // includeFontPadding: false,
  },
  remainingText: {
    fontSize: currentThemes.fontSize24,
    color: '#60636B',
    fontWeight: '500',
    alignSelf: 'center',
    lineHeight: scaleSizeH(32),
    marginBottom: scaleSizeH(75),
  },
  backPayment: {
    width: scaleSizeW(200),
    height: scaleSizeH(56),
    position: 'absolute',
    top: scaleSizeH(48),
    right: scaleSizeW(64),
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: scaleSizeW(8),
    borderColor: '#D6D6D6',
    borderWidth: 1,
  },
  dotStyle: {
    backgroundColor: '#DBDBDB',
    width: scaleSizeW(12),
    height: scaleSizeH(12),
    borderRadius: scaleSizeW(10),
    marginHorizontal: scaleSizeH(10),
    marginTop: scaleSizeH(65),
  },
  activeDotStyle: {
    backgroundColor: CommonColors.Manatee,
    width: scaleSizeW(12),
    height: scaleSizeH(12),
    borderRadius: scaleSizeW(10),
    marginHorizontal: scaleSizeH(10),
    marginTop: scaleSizeH(65),
  },
  paymentLayout: {
    width: '100%',
    height: scaleSizeH(615),
    overflow: 'hidden',
  },
  flatStyle: {
    width: '100%',
    height: scaleSizeH(492),
  },
});
