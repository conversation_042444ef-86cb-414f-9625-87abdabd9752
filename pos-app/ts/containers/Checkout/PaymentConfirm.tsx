import * as EmailValidator from 'email-validator';
import { get, throttle } from 'lodash';
import React, { PureComponent } from 'react';
import { BackHandler, KeyboardAvoidingView, ScrollView, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';
import { showMessage } from 'react-native-flash-message';
import { connect, ConnectedProps } from 'react-redux';

import { bindActionCreators } from 'redux';
import {
  clearTransactionSession,
  employeePrintEInvoiceQrReceipt,
  navigateToHome,
  newPrintReceiptAction,
  printEInvoiceQrReceipt,
  sendReceiptToEmail,
  toggleToastInfo,
} from '../../actions';
import { CfdPaymentConfirmModel, resetCfdMembership, updateCfdPaymentConfirm } from '../../actions/cfd';
import { PosTextInput } from '../../components/textInput';
import { IconFilledCheck, MaterialIcons } from '../../components/ui';
import IconQrThin from '../../components/ui/svgIcons/iconQrThin';
import IconReceiptThin from '../../components/ui/svgIcons/iconReceiptThin';
import PaymentOptions from '../../config/paymentOption';
import { currentThemes, IsAndroid, scaleSizeH, scaleSizeW, t, TransactionFlowType } from '../../constants';
import { scaleSize } from '../../constants/themes';
import DAL from '../../dal';
import { copyRealmTransaction } from '../../sagas/printing/common';
import {
  selectAlwaysPrintReceipt,
  selectAlwaysPrintReceiptWithBeepQRPayAtCounter,
  selectBusinessName,
  selectCurrency,
  selectEInvoiceEnabled,
  selectGBEnableEInvoiceBeepWebstore,
  selectInSplitOpenOrder,
  selectInSplitOpenRemainingAmount,
  selectLocalCountryMap,
  selectPrinterTagsSettings,
} from '../../sagas/selector';
import { RootState, ScreenProps, TransactionType } from '../../typings';
import { getUnNullValue, localeNumber, newConvertCurrencyToSymbol, testProps } from '../../utils';
import { IminLcdManager } from '../../utils/lcd';
import { infoTransactionEvent, SaleFlowAction } from '../../utils/logComponent';
import { getParam } from '../../utils/navigation';
import { hasNoReceiptPrinter } from '../../utils/printer';

const ReactNativeNfc = require('../../../libs/react-native-nfc').default;
const { finishTransaction } = ReactNativeNfc;

interface Props extends ScreenProps, PropsFromRedux {}

interface State {
  validate: boolean;
  emailLoading: boolean;
}

const mapStateToProps = (state: RootState) => ({
  currency: selectCurrency(state),
  business: selectBusinessName(state),
  alwaysPrintReceipt: selectAlwaysPrintReceipt(state),
  alwaysPrintReceiptWithBeepQRPayAtCounter: selectAlwaysPrintReceiptWithBeepQRPayAtCounter(state),
  hasNoReceiptPrinter: hasNoReceiptPrinter(selectPrinterTagsSettings(state)),
  inSplit: selectInSplitOpenOrder(state),
  splitOrderRemainingAmount: selectInSplitOpenRemainingAmount(state),
  localCountryMap: selectLocalCountryMap(state),
  eInvoiceEnabled: selectEInvoiceEnabled(state),
  eInvoiceBeepWebstoreEnabled: selectGBEnableEInvoiceBeepWebstore(state),
});

const mapDispatchToProps = dispatch => ({
  actions: bindActionCreators(
    {
      sendReceiptToEmail,
      toggleToastInfo,
      navigateToHome,
      clearTransactionSession,
      updateCfdPaymentConfirm,
      newPrintReceiptAction,
      printEInvoiceQrReceipt,
      employeePrintEInvoiceQrReceipt,
      resetCfdMembership,
    },
    dispatch
  ),
});

const connector = connect(mapStateToProps, mapDispatchToProps);
type PropsFromRedux = ConnectedProps<typeof connector>;

class PaymentConfirm extends PureComponent<Props, State> {
  private _emailInputRef: TextInput;
  private email: string;
  private onThrottlePrintReceiptHandler: any;
  private onThrottlePrintEInvoiceQrHandler: any;
  private _backButtonListener: any;
  private isOnlineOrder: boolean;

  static navigationOptions = {
    headerShown: false,
  };

  state = {
    validate: false,
    emailLoading: false,
  };

  constructor(props) {
    super(props);
    this.isOnlineOrder = getParam(props, 'isOnlineOrder', false);
    this.onThrottlePrintReceiptHandler = throttle(this.onPrintReceiptHandler, 1000, { leading: true, trailing: false });
    this.onThrottlePrintEInvoiceQrHandler = throttle(this.onPrintEInvoiceQR, 1000, { leading: true, trailing: false });
    this.showCFDPaymentConfirm();
  }

  showCFDPaymentConfirm = () => {
    const { currency } = this.props;
    const changeAmount = this.formatCfdAmount(getParam(this.props, 'changeAmount', 0));
    const isRuturn = getParam(this.props, 'transactionType') === 'Return';
    const remainingAmount = getParam(this.props, 'remainingAmount', 0);
    const refundPaymentType = getParam(this.props, 'refundPaymentType', '');
    const total = getParam(this.props, 'total', 0);
    const paidAmount = this.formatCfdAmount(getParam(this.props, 'totalPaid', 0));
    const isSplitPayment = remainingAmount > 0;
    const totalAmount = this.formatCfdAmount(isSplitPayment ? remainingAmount : total);

    const model = {
      isSplitPayment,
      isRuturn,
      totalAmount,
      paidAmount,
      changeAmount,
      refundPaymentType,
      currency,
    };
    // if (IsAndroid) {
    //   CfdManager.showPaymentConfirm(model);
    // }
    this.props.actions.updateCfdPaymentConfirm(model as CfdPaymentConfirmModel);
  };

  formatCfdAmount = (value: number) => {
    const { currency, localCountryMap } = this.props;
    const currencySymbol = newConvertCurrencyToSymbol(localCountryMap, currency);
    return value > 0 ? `${currencySymbol} ${localeNumber(value)}` : '0.00';
  };

  componentDidMount() {
    this.perposeAfterDidMount();
    IminLcdManager.clear();
  }

  perposeAfterDidMount = () => {
    const needClearTransaction = getParam(this.props, 'needClearTransaction', false);
    if (needClearTransaction) {
      this.props.actions.clearTransactionSession();
      const orderId = getParam(this.props, 'orderId');
      const transactionId = getParam(this.props, 'transactionId');
      infoTransactionEvent({ action: SaleFlowAction.ClearTransaction, orderId, transactionId });
    }
    this.printReceiptChecking();
    if (IsAndroid) {
      this._backButtonListener = BackHandler.addEventListener('hardwareBackPress', () => {
        this.handleNewSalesClicked();
        return true;
      });
    }
    this.initEmail();
    const customer = getParam(this.props, 'customer', {});
    this.props.actions.resetCfdMembership({ transactionId: getParam(this.props, 'transactionId', ''), loyaltyEarned: customer.loyaltyEarned, timeout: 10000 });
  };

  initEmail = () => {
    const showEmailReceipt = getParam(this.props, 'showEmailReceipt', true);
    if (this._emailInputRef && showEmailReceipt) {
      const customer = getParam(this.props, 'customer', undefined);
      const customerEmail = getUnNullValue(customer, 'email', '');
      this.email = customerEmail;
      this._emailInputRef.setNativeProps({ text: this.email });
      this.setState({
        validate: EmailValidator.validate(this.email),
      });
    }
  };

  componentWillUnmount() {
    this.onThrottlePrintReceiptHandler.cancel();
    this.onThrottlePrintEInvoiceQrHandler.cancel();
    this._backButtonListener && this._backButtonListener.remove();
    // if (IsAndroid) {
    //   CfdManager.dismissPaymentConfirm();
    // }
    this.props.actions.updateCfdPaymentConfirm(null);
  }

  printReceiptChecking = () => {
    const { alwaysPrintReceipt, alwaysPrintReceiptWithBeepQRPayAtCounter } = this.props;
    const remaining = this.getRemaining();
    const shouldAutoPrintReceipt =
      ((!this.isOnlineOrder && alwaysPrintReceipt) || (this.isOnlineOrder && alwaysPrintReceiptWithBeepQRPayAtCounter)) && remaining < 0.001;
    if (shouldAutoPrintReceipt) {
      this.onThrottlePrintReceiptHandler();
    }
  };

  getRemaining = () => {
    return getParam(this.props, 'remainingAmount', 0);
  };

  newSalesClicked = () => requestAnimationFrame(this.handleNewSalesClicked);

  handleNewSalesClicked = () => {
    const {
      navigation,
      actions: { navigateToHome, resetCfdMembership },
      inSplit,
    } = this.props;
    const remaining = this.getRemaining();
    this.finishTransaction();
    if (remaining > 0) {
      navigation.replace('Checkout');
    } else if (inSplit) {
      navigation.goBack();
    } else {
      requestAnimationFrame(() => navigateToHome({ navigation }));
    }
    const customer = getParam(this.props, 'customer', {});
    this.props.actions.resetCfdMembership({ transactionId: getParam(this.props, 'transactionId', ''), loyaltyEarned: customer.loyaltyEarned, timeout: 3000 });
  };

  finishTransaction = async () => {
    // notify native NFC module that the transaction has been finish. Native can reset customer display UI.
    await finishTransaction();
  };

  okClicked = () => {
    const refreshData = getParam(this.props, 'refreshData', null);
    const transactionId = getParam(this.props, 'transactionId');
    this.props.navigation.navigate('Transactions');
    refreshData && refreshData(transactionId);
  };

  onPrintReceiptHandler = () => {
    const transactionId = getParam(this.props, 'transactionId', undefined);
    const customer = getParam(this.props, 'customer', undefined);
    if (transactionId) {
      this.props.actions.newPrintReceiptAction({
        transactionId,
        customer,
        isReprint: false,
        isNeedLog: false,
        eventName: 'checkOut',
      });
    }
  };

  onPrintEInvoiceQR = () => {
    const { hasNoReceiptPrinter } = this.props;
    // if (hasNoReceiptPrinter) {
    //   this.props.navigation.navigate('ModalInfo', { info: t('NoPrinterError') });
    //   return;
    // }

    const transactionId = getParam(this.props, 'transactionId', undefined);
    const customer = getParam(this.props, 'customer', undefined);
    const localTransaction = copyRealmTransaction(DAL.getTrancationById(transactionId) as any);

    if (transactionId) {
      this.props.actions.printEInvoiceQrReceipt({
        transaction: localTransaction,
      });
      this.props.actions.employeePrintEInvoiceQrReceipt({
        transactionId: localTransaction.transactionId,
      });
    }
  };

  onEmailReceiptHandler = () => {
    const { validate } = this.state;

    if (!validate) {
      this.props.actions.toggleToastInfo({
        visible: true,
        text: this.email + ' ' + t('NotValidEmail'),
      });
      return;
    }
    const transactionId = getParam(this.props, 'transactionId', undefined);
    if (transactionId) {
      const onSuccess = {
        callback: () => {
          showMessage({ message: `${t('Receipt sent to')} ${this.email}` });
          this.clearEmail();
        },
      };

      const onFailure = {
        callback: () => {
          this.setState({ emailLoading: false });
          this.props.actions.toggleToastInfo({
            visible: true,
            text: t('SendEmailFail'),
          });
        },
      };

      this.setState({ emailLoading: true }, () => {
        this.props.actions.sendReceiptToEmail({
          business: this.props.business,
          transactionId,
          email: this.email,
          onSuccess,
          onFailure,
        });
      });
    }
  };

  clearEmail = () => {
    this.email = '';
    this._emailInputRef && this._emailInputRef.clear();
    this.setState({ validate: false, emailLoading: false });
  };

  inputRefhandler = ref => {
    this._emailInputRef = ref;
  };

  onEmailInput = email => {
    const { validate } = this.state;
    this.email = email;
    const newValidate = EmailValidator.validate(email);
    if (validate !== newValidate) {
      this.setState({ validate: newValidate });
    }
  };

  render() {
    const { currency, inSplit, splitOrderRemainingAmount, localCountryMap } = this.props;
    const currencySymbol = newConvertCurrencyToSymbol(localCountryMap, currency);
    const { validate, emailLoading } = this.state;
    const totalPaid = getParam(this.props, 'totalPaid', 0);
    const isRefund = getParam(this.props, 'transactionType', 'Sale') === TransactionFlowType.Return;
    const change = String(getParam(this.props, 'changeAmount', 0.0));
    const refundTotal = String(getParam(this.props, 'totalAmount', 0.0));
    const showEmailReceipt = getParam(this.props, 'showEmailReceipt', true);
    const paymentMethod = getParam(this.props, 'paymentMethod', '');
    const remaining = this.getRemaining();
    const goBackToTransactionContainer = getParam(this.props, 'goBackToTransactionContainer', false);
    let showEInvoice = this.props.eInvoiceEnabled;
    let totalPaidText;
    if (totalPaid > 0 && remaining > 0) {
      totalPaidText = `${t('Total Paid')} ${currencySymbol} ${localeNumber(totalPaid)}`;
    } else {
      const transactionId = getParam(this.props, 'transactionId', undefined);
      const localTransaction = DAL.getTrancationById(transactionId) as any;
      if (localTransaction) {
        if (localTransaction.transactionType === TransactionFlowType.PreOrder) {
          totalPaidText = `${t('Total Paid')} ${currencySymbol} ${localeNumber(localTransaction.depositAmount)}`;
        } else {
          totalPaidText = `${t('Total Paid')} ${currencySymbol} ${localeNumber(Math.max(localTransaction.total - get(localTransaction, 'depositAmount', 0)))}`;
        }
        if (localTransaction.payments) {
          (localTransaction as TransactionType).payments.forEach(payment => {
            const paymentOption = PaymentOptions.getPaymentOptionById(payment.paymentMethodId);
            if (paymentOption && paymentOption.isExcludedFromEInvoice) {
              showEInvoice = false;
            }
          });
        }
      }
    }

    let title = '';
    if (isRefund) {
      title = currencySymbol + ' ' + localeNumber(refundTotal) + ' ' + t('Refunded');
      totalPaidText = ' ';
    } else {
      if (change && Number(change) > 0) {
        title = currencySymbol + ' ' + localeNumber(change) + ' ' + t('Change');
      } else {
        title = t('No Change');
      }
    }

    let salesTypeBtnText = t('New Sale');
    if (remaining > 0) {
      salesTypeBtnText = `${currencySymbol} ${localeNumber(remaining)} ${t('Remaining')}`;
    } else if (inSplit) {
      salesTypeBtnText = t('Back to Remaining currency amount', {
        currency: currencySymbol,
        amount: localeNumber(splitOrderRemainingAmount),
      });
    }

    return (
      <ScrollView contentContainerStyle={styles.container} scrollEnabled={false}>
        {isRefund && goBackToTransactionContainer ? (
          <MaterialIcons name='monetization-on' size={scaleSizeW(136)} color='#4495EC' />
        ) : (
          <IconFilledCheck width={scaleSizeW(136)} height={scaleSizeH(136)} color={'#40CD7D'} />
        )}
        <Text testID='title' style={styles.changeText}>
          {title}
        </Text>
        {totalPaidText && (
          <Text testID='TotalPaid' style={styles.totalPaidText}>
            {totalPaidText}
          </Text>
        )}
        {remaining < 0.001 && (
          <>
            {showEmailReceipt && (
              <KeyboardAvoidingView behavior='position' keyboardVerticalOffset={10}>
                <View style={styles.itemView}>
                  <PosTextInput
                    {...testProps('al_textinput_103')}
                    ref={this.inputRefhandler}
                    placeholder={t('Email Receipt') + '?'}
                    placeholderTextColor={'#9F9F9F'}
                    onChangeText={this.onEmailInput}
                    style={styles.emailInput}
                    underlineColorAndroid='transparent'
                  />
                  <TouchableOpacity
                    {...testProps('al_btn_397')}
                    style={{
                      marginLeft: scaleSizeW(24),
                      borderRadius: scaleSizeW(8),
                      backgroundColor: validate && !emailLoading ? '#242644' : '#DBDBDB',
                      width: scaleSizeW(192),
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                    disabled={emailLoading}
                    onPress={this.onEmailReceiptHandler}
                  >
                    <Text style={styles.sendText}>{t('Send')}</Text>
                  </TouchableOpacity>
                </View>
              </KeyboardAvoidingView>
            )}
            <View style={[styles.itemView, { marginTop: showEmailReceipt ? scaleSizeH(32) : scaleSizeH(184) }]}>
              <TouchableOpacity
                {...testProps('al_btn_822')}
                style={{ ...StyleSheet.flatten(styles.button), backgroundColor: 'white' }}
                onPress={this.onThrottlePrintReceiptHandler}
              >
                <IconReceiptThin width={scaleSizeW(32)} height={scaleSizeH(32)} />
                <View style={{ width: scaleSize(8) }} />
                <Text style={styles.printText}>{t('PRINT RECEIPT')}</Text>
              </TouchableOpacity>
            </View>
            {showEInvoice && (
              <View style={[styles.itemView, { marginTop: scaleSize(32) }]}>
                <TouchableOpacity
                  {...testProps('al_btn_822')}
                  style={{ ...StyleSheet.flatten(styles.button), backgroundColor: 'white' }}
                  onPress={this.onThrottlePrintEInvoiceQrHandler}
                >
                  <IconQrThin width={scaleSizeW(32)} height={scaleSizeH(32)} />
                  <View style={{ width: scaleSize(8) }} />
                  {!isRefund && <Text style={styles.printText}>{'PRINT E-INVOICE QR CODE'}</Text>}
                  {isRefund && <Text style={styles.printText}>{'PRINT CREDIT NOTE QR CODE'}</Text>}
                </TouchableOpacity>
              </View>
            )}
          </>
        )}
        <View style={[styles.itemView, { marginTop: scaleSizeH(showEInvoice ? 102 : 206), height: scaleSizeH(112) }]}>
          <TouchableOpacity
            {...testProps('al_new_sale')}
            style={styles.button}
            onPress={isRefund && goBackToTransactionContainer ? this.okClicked : this.newSalesClicked}
          >
            <Text testID='salesTypeBtnText' style={{ fontSize: currentThemes.fontSize30, color: 'white', fontWeight: 'bold' }}>
              {isRefund && goBackToTransactionContainer ? t('OK') : salesTypeBtnText}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    );
  }
}

export default connector(PaymentConfirm);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    paddingTop: scaleSizeH(80),
    backgroundColor: currentThemes.bgMainColor,
  },
  changeText: {
    marginTop: scaleSizeH(24),
    fontSize: currentThemes.fontSize80,
    color: '#303030',
    fontWeight: 'bold',
  },
  totalPaidText: {
    marginTop: scaleSizeH(8),
    fontSize: currentThemes.fontSize28,
    color: '#303030',
    fontWeight: '500',
  },
  itemView: {
    height: scaleSizeH(72),
    width: scaleSizeW(800),
    marginTop: scaleSizeH(80),
    flexDirection: 'row',
  },
  button: {
    flexDirection: 'row',
    flex: 1,
    backgroundColor: '#FC7118',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: scaleSizeW(8),
  },
  emailInput: {
    paddingHorizontal: scaleSizeW(16),
    textAlignVertical: 'center',
    padding: 0,
    fontSize: currentThemes.fontSize24,
    borderRadius: scaleSizeH(8),
    flex: 1,
    backgroundColor: 'white',
    borderColor: '#E0E0E4',
    borderWidth: scaleSizeW(1),
  },
  sendText: {
    fontSize: currentThemes.fontSize24,
    color: 'white',
    fontWeight: 'bold',
  },
  printText: {
    fontSize: currentThemes.fontSize24,
    color: '#393939',
    fontWeight: 'bold',
  },
});
