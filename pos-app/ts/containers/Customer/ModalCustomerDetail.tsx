import { StackActions } from '@react-navigation/compat';
import { filter, get, isEqual, isNumber } from 'lodash';
import moment from 'moment';
import React, { PureComponent } from 'react';
import { FlatList, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import ScrollableTabView from 'react-native-scrollable-tab-view';
import { connect, ConnectedProps } from 'react-redux';
import { bindActionCreators } from 'redux';
import { CustomerFrequentItem } from '.';
import { addCustomerToTransaction, getCustomer, getOrderLogList, mostSoldProducts, toggleToastInfo, updateCustomerInTransaction } from '../../actions';
import { NavigatorHeader, TabBarWithTextAndIcon } from '../../components/common';
import TabContainer from '../../components/common/TabContainer';
import { ModalMoreAction } from '../../components/modal';
import { IconEdit, IconEmail, IconLeft, IconMobile, IconMore, MaterialIcons } from '../../components/ui';
import IconGoldMember from '../../components/ui/svgIcons/IconGoldMember';
import IconHideCustomerInfoEye from '../../components/ui/svgIcons/iconHideCustomerInfoEye';
import IconPlatinumMember from '../../components/ui/svgIcons/iconMember';
import IconNormalMember from '../../components/ui/svgIcons/IconNormalMember';
import IconSilverMember from '../../components/ui/svgIcons/IconSilverMember';
import IconViewCustomerInfoEye from '../../components/ui/svgIcons/iconViewCustomerInfoEye';
import { CommonColors, currentThemes, scaleSizeH, scaleSizeW, width } from '../../constants';
import { t } from '../../constants/i18n';
import { selectEnableCashback, selectEnablePoints, selectEnableStoreCredit, selectMembershipEnabled, selectViewCustomerInfo } from '../../sagas/selector';
import { RootState, ScreenProps } from '../../typings';
import { localeNumber, maskCompleteText, maskEmail, maskPhoneNumber, roundingToAmount, testProps } from '../../utils';
import { formatCustomerDisplayName } from '../../utils/customer';
import { createDate } from '../../utils/datetime';
import { getParam } from '../../utils/navigation';
import { isEmpty } from '../../utils/validator';

interface Props extends ScreenProps, PropsFromRedux {
  enableCashback: boolean;
  enableStoreCredit: boolean;
  pointsEnabled: boolean;
  membershipEnabled: boolean;
  viewCustomerInfo: boolean;
}

interface State {
  customer?: any;
  mostSoldProducts?: any;
  orderLogList?: any;
  tempShowInfo: boolean;
}

export const SH_TIER_TAGS = ['SH_Tier_1', 'SH_Tier_2', 'SH_Tier_3', 'SH_Tier_4'];

const mapStateToProps = (state: RootState) => ({
  businessName: state.getIn<string>(['Storage', 'storeInfo', 'name']),
  enableCashback: selectEnableCashback(state),
  enableStoreCredit: selectEnableStoreCredit(state),
  pointsEnabled: selectEnablePoints(state),
  membershipEnabled: selectMembershipEnabled(state),
  viewCustomerInfo: selectViewCustomerInfo(state),
});

const mapDispatchToProps = dispatch => ({
  actions: bindActionCreators(
    {
      mostSoldProducts,
      getOrderLogList,
      toggleToastInfo,
      addCustomerToTransaction,
      getCustomer,
      updateCustomerInTransaction,
    },
    dispatch
  ),
});

export enum CustomerUpdateStatus {
  NoUpdate = 0,
  UpdatedWithoutTags = 1,
  UpdatedWithTags = 2,
}

const connector = connect(mapStateToProps, mapDispatchToProps);
type PropsFromRedux = ConnectedProps<typeof connector>;

class CustomerDetail extends PureComponent<Props, State> {
  private _moreDialog;
  private _customerStatus = CustomerUpdateStatus.NoUpdate;

  constructor(props) {
    super(props);
    const _customer = getParam(props, 'customer');
    this.state = {
      customer: _customer,
      mostSoldProducts: [],
      orderLogList: [],
      tempShowInfo: false,
    };
    this.getmostSoldProducts(this.state.customer.customerId);
    this.getOrderLogList(this.state.customer.customerId);
  }

  componentDidMount() {
    const { customerId } = this.state.customer;
    const { businessName, pointsEnabled } = this.props;
    if (pointsEnabled) {
      const onSuccess = {
        callback: payload => {
          if (payload.res) {
            const availablePointsBalance = get(payload.res, ['customer', 'availablePointsBalance'], 0);
            const pointsTotalSpent = get(payload.res, ['customer', 'pointsTotalSpent'], 0);
            this.setState({
              customer: {
                ...this.state.customer,
                availablePointsBalance,
                pointsTotalSpent,
              },
            });
          }
        },
      };
      const onFailure = {
        callback: payload => {
          console.error('getCustomer error', payload);
        },
      };
      this.props.actions.getCustomer({
        business: businessName,
        customerId,
        needPointsBalance: true,
        needPointsTotalSpend: true,
        needMembershipDetail: false,
        onSuccess,
        onFailure,
      });
    }
  }

  getmostSoldProducts = customerId => {
    const { businessName } = this.props;
    const onSuccess = {
      callback: payload => {
        if (payload.res) {
          this.setState({
            mostSoldProducts: payload.res.mostSoldProducts,
          });
        }
      },
    };

    this.props.actions.mostSoldProducts({
      businessName,
      customerId,
      onSuccess,
    });
  };

  getOrderLogList = customerId => {
    const { businessName } = this.props;
    const onSuccess = {
      callback: payload => {
        if (payload.res) {
          this.setState({
            orderLogList: payload.res.transactions,
          });
        }
      },
    };

    this.props.actions.getOrderLogList({
      businessName: businessName,
      customerId: customerId,
      onSuccess,
    });
  };

  _onMoreClick = () => {
    this._moreDialog.show();
  };

  render() {
    const headerLeftIcons = [
      { icon: <IconLeft width={scaleSizeW(48)} height={scaleSizeH(49)} color={currentThemes.buttonBackgroundColor} />, onClick: this.closeButtonClicked },
    ];
    const headerRightIcons = [{ icon: <IconMore color={CommonColors.Icon} />, onClick: this._onMoreClick }];
    const moreActionList = [
      { name: t('Update Profile'), icon: <IconEdit width={scaleSizeW(48)} height={scaleSizeH(48)} color={'#8D90A3'} />, onClick: this.onUpdateCustomerClick },
    ];
    return (
      <View style={styles.container}>
        <NavigatorHeader title={t('Customer Profile')} style={{ backgroundColor: '#F5F5F7' }} leftIcons={headerLeftIcons} rightIcons={headerRightIcons} />
        <ModalMoreAction ref={refs => (this._moreDialog = refs)} list={[...moreActionList]} />
        <View style={styles.content}>
          <View style={styles.leftContainer}>
            <View style={styles.tabContent}>{this.renderLeftTab()}</View>
          </View>
          <View style={styles.rightContainer}>{this.renderRightContent()}</View>
        </View>
      </View>
    );
  }

  renderLeftTab = () => {
    const { customer, tempShowInfo } = this.state;
    const { phone, email } = customer;
    const { viewCustomerInfo } = this.props;
    const customerName = formatCustomerDisplayName(customer, false);

    const shouldShowUnmasked = tempShowInfo || viewCustomerInfo;
    const displayedPhone = shouldShowUnmasked ? phone : maskPhoneNumber(phone);
    const displayedEmail = shouldShowUnmasked ? email : maskEmail(email);

    return (
      <View style={styles.rightContent}>
        <View style={styles.leftTopContent}>
          <MaterialIcons name='face' size={scaleSizeW(128)} color='#E0E0E4' />
          <View style={styles.infoStyle}>
            <Text testID='customerName' style={styles.nameText} numberOfLines={2} ellipsizeMode='tail'>
              {customerName}
            </Text>
            <View style={styles.mobilerow}>
              <IconMobile width={scaleSizeW(24)} height={scaleSizeH(24)} color={CommonColors.Icon} />
              <Text style={styles.mobileText}>{displayedPhone}</Text>
            </View>
            <View style={[styles.mobilerow, { marginTop: scaleSizeH(19) }]}>
              <IconEmail width={scaleSizeW(24)} height={scaleSizeH(24)} color={CommonColors.Icon} />
              <Text style={styles.mobileText}>{!isEmpty(displayedEmail) ? displayedEmail : '-'}</Text>
            </View>
          </View>
          {!viewCustomerInfo && (
            <View style={styles.eyeContainer}>
              <TouchableOpacity {...testProps('al_btn_842')} onPress={this.toggleTempShowInfo}>
                {tempShowInfo ? (
                  <IconHideCustomerInfoEye color={'#757575'} width={scaleSizeW(40)} height={scaleSizeH(40)} />
                ) : (
                  <IconViewCustomerInfoEye color={'#4FDB87'} width={scaleSizeW(40)} height={scaleSizeH(40)} />
                )}
              </TouchableOpacity>
            </View>
          )}
        </View>
        <ScrollableTabView
          locked
          scrollWithoutAnimation
          style={{ width: '100%', height: '100%', overflow: 'hidden' }}
          renderTabBar={() => <TabBarWithTextAndIcon />}
          initialPage={0}
          tabBarActiveTextColor={'#393939'}
          tabBarInactiveTextColor={'#60636B'}
          tabBarTextStyle={{ fontSize: currentThemes.fontSize24, fontWeight: '500' }}
          tabBarUnderlineStyle={{ backgroundColor: '#FC7118', height: scaleSizeH(8), bottom: -1 }}
          prerenderingSiblingsNumber={1}
        >
          {this.renderCustomerProfile()}
          {this.renderCustomerAddress()}
        </ScrollableTabView>
      </View>
    );
  };

  renderCustomerAddress = () => {
    const { customer, tempShowInfo } = this.state;
    const { street1, street2, postalCode, city, state } = customer;
    const { viewCustomerInfo } = this.props;

    const shouldShowUnmasked = tempShowInfo || viewCustomerInfo;

    const displayStreet1 = shouldShowUnmasked ? street1 : maskCompleteText(street1);
    const displayStreet2 = shouldShowUnmasked ? street2 : maskCompleteText(street2);
    const displayPostalCode = shouldShowUnmasked ? postalCode : maskCompleteText(postalCode);
    const displayCity = shouldShowUnmasked ? city : maskCompleteText(city);
    const displayState = shouldShowUnmasked ? state : maskCompleteText(state);

    return (
      <TabContainer tabLabel={{ isVisiable: false, label: t('Address') }}>
        <View style={styles.profileContent}>
          <Text style={styles.tipText}>{t('ADDRESS LINE 1')}</Text>
          <Text style={styles.contentText}>{!isEmpty(street1) ? displayStreet1 : '-'}</Text>
          <Text style={styles.tipText}>{t('ADDRESS LINE 2')}</Text>
          <Text style={styles.contentText}>{!isEmpty(street2) ? displayStreet2 : '-'}</Text>
          <View style={styles.row}>
            <View style={[styles.rowItemContainer, { marginRight: scaleSizeW(24) }]}>
              <Text style={styles.tipText}>{t('PASSCODE')}</Text>
              <Text style={styles.contentText}>{!isEmpty(postalCode) ? displayPostalCode : '-'}</Text>
            </View>
            <View style={[styles.rowItemContainer, { marginRight: scaleSizeW(24) }]}>
              <Text style={styles.tipText}>{t('CITY')}</Text>
              <Text style={styles.contentText}>{!isEmpty(city) ? displayCity : '-'}</Text>
            </View>
          </View>
          <Text style={styles.tipText}>{t('STATE')}</Text>
          <Text style={styles.contentText}>{displayState || ''}</Text>
        </View>
      </TabContainer>
    );
  };

  renderCustomerProfile = () => {
    const { customer } = this.state;
    const specialTags = customer.tags && customer.tags.filter(tag => SH_TIER_TAGS.includes(tag));
    const { membershipEnabled } = this.props;
    const nonSpecialTags = customer.tags && customer.tags.filter(tag => !SH_TIER_TAGS.includes(tag));

    return (
      <TabContainer tabLabel={{ isVisiable: false, label: t('Profile') }}>
        <View style={styles.profileContent}>
          {specialTags && specialTags.length > 0 && membershipEnabled && (
            <View>
              <Text style={styles.tipText}>{t('MEMBERSHIP TIER')}</Text>
              {specialTags.map((tag, index) => {
                switch (tag) {
                  case 'SH_Tier_1':
                    return (
                      <View style={styles.membershipTier}>
                        <IconNormalMember width={scaleSizeW(140)} height={scaleSizeH(40)} style={styles.membershipIcon} />
                      </View>
                    );
                  case 'SH_Tier_2':
                    return (
                      <View style={styles.membershipTier}>
                        <IconSilverMember width={scaleSizeW(140)} height={scaleSizeH(40)} style={styles.membershipIcon} />
                      </View>
                    );
                  case 'SH_Tier_3':
                    return (
                      <View style={styles.membershipTier}>
                        <IconGoldMember width={scaleSizeW(140)} height={scaleSizeH(40)} style={styles.membershipIcon} />
                      </View>
                    );
                  case 'SH_Tier_4':
                    return (
                      <View style={styles.membershipTier}>
                        <IconPlatinumMember width={scaleSizeW(140)} height={scaleSizeH(40)} style={styles.membershipIcon} />
                      </View>
                    );
                  default:
                    return null;
                }
              })}
            </View>
          )}
          <Text style={styles.tipText}>{t('DATE OF BIRTH')}</Text>
          <Text style={styles.contentText}>{!isEmpty(customer.birthday) ? createDate(customer.birthday, 'D MMM YYYY') : '-'}</Text>
          <Text style={styles.tipText}>{t('ID')}</Text>
          <Text style={styles.contentText}>{!isEmpty(customer.memberId) ? customer.memberId : '-'}</Text>
          <Text style={styles.tipText}>{t('TAG')}</Text>
          {(!nonSpecialTags || nonSpecialTags.length == 0) && <Text style={styles.contentText}>{'-'}</Text>}
          <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
            {nonSpecialTags &&
              nonSpecialTags.map((tag, index) => {
                return (
                  <View style={[styles.tagItem, { backgroundColor: index % 2 === 0 ? '#008EC4' : '#121838' }]} key={tag}>
                    <Text style={styles.tagText}>{tag}</Text>
                  </View>
                );
              })}
          </View>
          <Text style={styles.tipText}>{t('TAX ID')}</Text>
          <Text style={styles.contentText}>{!isEmpty(customer.taxIdNo) ? customer.taxIdNo : '-'}</Text>
        </View>
      </TabContainer>
    );
  };

  _keyExtractorFrequentItem = (item, index: number) => {
    return `${item.id}`;
  };

  _keyExtractorOrderHistory = (item, index: number) => {
    return `${item.receiptNumber}`;
  };

  _getItemLayout = (data, index: number) => {
    const itemHeight = FREQUENT_ITEM_HEIGHT;
    return { length: itemHeight, offset: itemHeight * index + FREQUENT_SEPARATOR_HEIGHT * (index - 1), index };
  };

  renderSeperator = () => {
    return <View style={styles.seperator} />;
  };

  renderFrequentItem = ({ item, index }) => {
    return <CustomerFrequentItem item={item} key={item.id} />;
  };

  renderFrequentItems = () => {
    const { mostSoldProducts } = this.state;
    return (
      <FlatList
        showsVerticalScrollIndicator={false}
        renderToHardwareTextureAndroid
        contentContainerStyle={styles.frequentItemscontentContainerStyle}
        style={styles.frequentItemsContainer}
        renderItem={this.renderFrequentItem}
        keyExtractor={this._keyExtractorFrequentItem}
        getItemLayout={this._getItemLayout}
        keyboardDismissMode='none'
        keyboardShouldPersistTaps='handled'
        ItemSeparatorComponent={this.renderSeperator}
        data={mostSoldProducts}
      />
    );
  };

  renderRightContent = () => {
    const { customer } = this.state;
    const { enableCashback, enableStoreCredit, pointsEnabled } = this.props;
    const { availablePointsBalance = 0, pointsTotalSpent = 0 } = customer;
    return (
      <View style={styles.rightContent}>
        <View style={styles.rightTopContent}>
          <View style={{ flexDirection: 'column' }}>
            <Text style={styles.transactionItemTip}>{t('TOTAL TRANSACTIONS')}</Text>
            <Text style={styles.transactionItemAmount} {...testProps('totalTransactions')}>
              {!isEmpty(customer.totalTransactions) ? customer.totalTransactions : '-'}
            </Text>
          </View>
          <View style={{ flexDirection: 'column' }}>
            <Text style={styles.transactionItemTip}>{t('AVERAGE SALE VALUE(RM)')}</Text>
            <Text style={styles.transactionItemAmount} {...testProps('averageSale')}>
              {localeNumber(this.averageSpend(customer.totalSpent, customer.totalTransactions))}
            </Text>
          </View>
          <View style={{ flexDirection: 'column' }}>
            <Text style={[styles.transactionItemTip, { color: '#FC7118', textAlign: 'right' }]}>{t('TOTAL SALES(RM)')}</Text>
            <Text style={[styles.transactionItemAmount, { textAlign: 'right' }]} {...testProps('totalSales')}>
              {localeNumber(get(customer, 'totalSpent', 0))}
            </Text>
          </View>
        </View>
        <ScrollableTabView
          locked
          style={{ width: '100%', height: '100%', overflow: 'hidden' }}
          renderTabBar={() => <TabBarWithTextAndIcon tabStyle={styles.tabStyle} />}
          initialPage={0}
          tabBarActiveTextColor={'#393939'}
          tabBarInactiveTextColor={'#60636B'}
          tabBarTextStyle={{ fontSize: currentThemes.fontSize24, fontWeight: '500' }}
          tabBarUnderlineStyle={{ backgroundColor: '#FC7118', height: scaleSizeH(8), bottom: -1 }}
          prerenderingSiblingsNumber={1}
          scrollWithoutAnimation
        >
          <TabContainer tabLabel={{ isVisiable: false, label: t('Overview') }}>
            <View style={{ justifyContent: 'space-between', flexDirection: 'row' }}>
              <View style={styles.overViewContainer}>
                <Text style={styles.overViewTip}>{t('Last Purchase Day')}</Text>
                <Text style={styles.overViewContent} {...testProps('lastPurchaseDay')}>
                  {!isEmpty(customer.lastPurchaseDate) ? createDate(customer.lastPurchaseDate, 'MMM DD, YYYY') : '-'}
                </Text>
              </View>
              <View style={styles.overViewContainer}>
                <Text style={styles.overViewTip}>{t('Joined Date')}</Text>
                <Text style={styles.overViewContent} {...testProps('joinedDate')}>
                  {!isEmpty(customer.createdTime) ? createDate(customer.createdTime, 'MMM DD, YYYY') : '-'}
                </Text>
              </View>
              <View style={styles.overViewContainer}>
                <Text style={styles.overViewTip}>{t('As Customer')}</Text>
                <Text style={styles.overViewContent} {...testProps('asCustomer')}>
                  {this.frequencyDay()}
                </Text>
              </View>
            </View>
            {pointsEnabled && (
              <View style={{ marginTop: scaleSizeH(0), justifyContent: 'space-between', flexDirection: 'row' }}>
                <View style={styles.overViewContainer}>
                  <Text style={styles.overViewTip}>{t('Points Balance')}</Text>
                  <Text style={styles.overViewContent} {...testProps('pointsBalance')}>
                    {roundingToAmount(availablePointsBalance, 0.0)}
                  </Text>
                </View>
                <View style={styles.overViewContainer}>
                  <Text style={styles.overViewTip}>{t('Total Points Spent')}</Text>
                  <Text style={styles.overViewContent} {...testProps('totalPointsSpent')}>
                    {roundingToAmount(pointsTotalSpent, 0.0)}
                  </Text>
                </View>
                <View style={[styles.overViewContainer, { backgroundColor: currentThemes.bgMainColor }]}></View>
              </View>
            )}
            {(enableCashback || enableStoreCredit) && (
              <View style={{ marginTop: scaleSizeH(0), justifyContent: 'space-between', flexDirection: 'row' }}>
                <View style={styles.overViewContainer}>
                  <Text style={styles.overViewTip}>{enableCashback ? t('Cashback Balance') : t('Store Credit Balance')}</Text>
                  <Text style={styles.overViewContent} {...testProps('cashbackBalance')}>
                    {localeNumber(get(customer, 'loyalty', 0))}
                  </Text>
                </View>
                <View style={styles.overViewContainer}>
                  <Text style={styles.overViewTip}>{enableCashback ? t('Total Cashback Spent') : t('Total Store Credit Spent')}</Text>
                  <Text style={styles.overViewContent} {...testProps('totalCashback')}>
                    {localeNumber(get(customer, 'storeCreditsSpent', 0))}
                  </Text>
                </View>
                <View style={[styles.overViewContainer, { backgroundColor: currentThemes.bgMainColor }]}></View>
              </View>
            )}
          </TabContainer>
          <TabContainer tabLabel={{ isVisiable: false, label: t('Frequent Items') }}>{this.renderFrequentItems()}</TabContainer>
          <TabContainer tabLabel={{ isVisiable: false, label: t('Order History') }}>{this.renderOrderHistory()}</TabContainer>
        </ScrollableTabView>
      </View>
    );
  };

  isProductItem = item => {
    // Ignore intangible item such as service charge.
    return isEmpty(item.itemType);
  };

  renderOrderHistoryItem = ({ item, index }) => {
    const { createdTime } = item;
    const items = get(item, 'items', []);

    return (
      <View style={[styles.orderHistoryItem, { backgroundColor: index % 2 === 0 ? '#FFFFFF' : '#F9F9F9' }]} key={item.receiptNumber}>
        {filter(items, item => this.isProductItem(item)).map((product, index) => {
          const { title, quantity, total, id } = product;
          return (
            <View key={`${id}_${title}_${index}`} style={styles.orderHistoryItemChild}>
              <View style={{ flexDirection: 'column' }}>
                <Text style={styles.itemData}>{index == 0 ? createDate(createdTime, 'DD/MM/YYYY') : ''}</Text>
                <Text style={styles.itemTime}>{index == 0 ? createDate(createdTime, 'h:mm A') : ''}</Text>
              </View>

              <Text style={[styles.itemData, { textAlign: 'center' }]}>{quantity}</Text>
              <Text style={[styles.itemTitle]}>{title}</Text>
              <Text style={[styles.itemPrice, { textAlign: 'right' }]}>{total.toFixed(2)}</Text>
            </View>
          );
        })}
      </View>
    );
  };

  renderOrderHistoryHeader = () => {
    return (
      <View style={styles.orderHistoryTopItem}>
        <Text style={[styles.orderHistoryItemTitle, { width: scaleSizeW(200), textAlign: 'justify' }]}>{t('Date')}</Text>
        <Text style={[styles.orderHistoryItemTitle, { width: scaleSizeW(200), textAlign: 'center' }]}>{t('Qty')}</Text>
        <Text style={[styles.orderHistoryItemTitle, { flexGrow: 1 }]}>{t('Purchase')}</Text>
        <Text style={[styles.orderHistoryItemTitle, { width: scaleSizeW(200), textAlign: 'right' }]}>{t('Price')}</Text>
      </View>
    );
  };

  renderOrderHistory = () => {
    const { orderLogList } = this.state;
    return (
      <View style={[styles.fieldStyle]}>
        {orderLogList && orderLogList.length > 0 && (
          <FlatList
            showsVerticalScrollIndicator={false}
            renderToHardwareTextureAndroid
            contentContainerStyle={styles.frequentItemscontentContainerStyle}
            style={styles.frequentItemsContainer}
            data={orderLogList}
            renderItem={this.renderOrderHistoryItem}
            keyExtractor={this._keyExtractorOrderHistory}
            getItemLayout={this._getItemLayout}
            ListHeaderComponent={this.renderOrderHistoryHeader}
            stickyHeaderIndices={[0]}
            keyboardDismissMode='none'
            keyboardShouldPersistTaps='handled'
            ItemSeparatorComponent={this.renderSeperator}
          />
        )}
      </View>
    );
  };

  frequencyDay = () => {
    const { customer } = this.state;
    if (isEmpty(customer.createdTime) || isEmpty(customer.lastPurchaseDate)) return '-';

    const days = moment(customer.lastPurchaseDate).diff(moment(customer.createdTime), 'day', true);
    return `${days.toFixed(0)} ${t('Days')}`;
  };

  averageSpend = (totalSpent, totalTransactions) => {
    if (!isNumber(totalSpent) || totalSpent === 0 || !isNumber(totalTransactions) || totalTransactions === 0) return 0;
    return totalSpent / totalTransactions;
  };

  onUpdateCustomerClick = () => {
    this.props.navigation.navigate('NewCustomer', { customer: this.state.customer, onCustomerInfoHandler: this.onCustomerInfoHandler });
    this._moreDialog.dismiss();
  };

  onCustomerInfoHandler = customer => {
    this.setState(prevState => {
      const { phone, firstName, lastName, email, birthday, memberId, tags, street1, street2, city, state, postalCode, taxIdNo } = customer;
      this._customerStatus = isEqual(tags, get(prevState, tags)) ? CustomerUpdateStatus.UpdatedWithoutTags : CustomerUpdateStatus.UpdatedWithTags;
      const newCustomer = Object.assign({}, prevState.customer, {
        phone,
        firstName,
        lastName,
        email,
        birthday,
        memberId,
        tags,
        street1,
        street2,
        city,
        state,
        postalCode,
        taxIdNo,
      });
      return {
        customer: newCustomer,
      };
    });
  };

  checkCustomerWithBIR = ({ enable, message }) => {
    if (enable) {
      this.props.navigation.goBack();
    } else {
      const replaceAction = StackActions.replace({
        routeName: 'ModalInfo',
        params: {
          info: message,
          textAlign: 'left',
        },
      });
      this.props.navigation.dispatch(replaceAction);
    }
  };

  closeButtonClicked = () => {
    const { customer } = this.state;
    const { isInCheckOut } = getParam(this.props, 'isInCheckOut', false);
    // if the customer updated, then need update it on Trnasaction also
    // only the Tags update will effect the logic
    // and only need update the customer info only for the other fields updating
    if (this._customerStatus === CustomerUpdateStatus.UpdatedWithoutTags) {
      this.props.actions.updateCustomerInTransaction({ customer });
    } else if (this._customerStatus === CustomerUpdateStatus.UpdatedWithTags) {
      this.props.actions.addCustomerToTransaction({ customer, customerId: customer.customerId, isInCheckOut, checkCustomerWithBIR: this.checkCustomerWithBIR });
    } else {
      this.props.navigation.goBack();
    }
  };

  toggleTempShowInfo = () => {
    const { tempShowInfo } = this.state;

    if (!tempShowInfo) {
      this.props.navigation.navigate('ModalManagerPinFC', {
        onDismissHandler: result => {
          if (result.ok) {
            this.setState({ tempShowInfo: true });
          }
        },
      });
    } else {
      this.setState({ tempShowInfo: false });
    }
  };
}

export default connector(CustomerDetail);

const FREQUENT_ITEM_HEIGHT = scaleSizeH(104);

const FREQUENT_SEPARATOR_HEIGHT = StyleSheet.hairlineWidth;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: currentThemes.bgMainColor,
  },
  content: {
    flex: 1,
    flexDirection: 'row',
  },
  leftContainer: {
    width: width * 0.31,
    paddingLeft: scaleSizeW(32),
    paddingTop: scaleSizeH(24),
    paddingBottom: scaleSizeH(20),
  },
  rightContainer: {
    width: width * 0.69,
    paddingHorizontal: scaleSizeW(30),
    paddingTop: scaleSizeH(24),
    paddingBottom: scaleSizeH(20),
  },
  rightContent: {
    width: '100%',
    height: '100%',
    flexDirection: 'column',
  },
  rightTopContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    backgroundColor: '#FFF',
    overflow: 'hidden',
    paddingHorizontal: scaleSizeW(24),
    paddingTop: scaleSizeH(50),
    height: scaleSizeH(209),
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E4',
  },
  tabContent: {
    flex: 1,
    backgroundColor: '#FFF',
    borderRadius: scaleSizeW(8),
    overflow: 'hidden',
  },
  leftTopContent: {
    display: 'flex',
    width: '100%',
    paddingTop: scaleSizeH(24),
    paddingHorizontal: scaleSizeW(24),
    height: scaleSizeH(209),
    flexDirection: 'row',
    borderBottomColor: currentThemes.splitLineColor,
    borderBottomWidth: scaleSizeH(1),
  },
  infoStyle: {
    flex: 1,
    paddingLeft: scaleSizeW(24),
    flexDirection: 'column',
    overflow: 'hidden',
  },
  nameText: {
    color: '#303030',
    fontSize: currentThemes.fontSize38,
    fontWeight: '500',
    width: '100%',
  },
  mobileText: {
    color: '#393939',
    fontSize: currentThemes.fontSize18,
    fontWeight: '400',
    marginLeft: scaleSizeW(24),
  },
  mobilerow: {
    flexDirection: 'row',
    marginTop: scaleSizeH(15),
  },
  profileContent: {
    width: '100%',
    height: '100%',
    paddingHorizontal: scaleSizeW(32),
    paddingTop: scaleSizeH(40),
  },
  tagItem: {
    flexDirection: 'row',
    borderRadius: scaleSizeW(4),
    alignItems: 'center',
    paddingHorizontal: scaleSizeW(10),
    paddingVertical: scaleSizeH(4),
    backgroundColor: '#121838',
    marginHorizontal: scaleSizeW(8),
    marginTop: scaleSizeH(12),
  },
  tagText: {
    color: '#FFFFFF',
    fontSize: currentThemes.fontSize14,
    fontWeight: '500',
  },
  transactionItemTip: {
    color: '#959595',
    fontSize: currentThemes.fontSize18,
    fontWeight: '500',
  },
  transactionItemAmount: {
    color: '#303030',
    fontSize: currentThemes.fontSize64,
    fontWeight: 'bold',
  },
  overViewContainer: {
    width: scaleSizeW(400),
    marginTop: scaleSizeH(40),
    paddingTop: scaleSizeH(26),
    paddingBottom: scaleSizeH(40),
    paddingLeft: scaleSizeW(20),
    borderRadius: scaleSizeW(8),
    backgroundColor: '#FFF',
  },
  overViewTip: {
    color: '#60636B',
    fontSize: currentThemes.fontSize18,
    fontWeight: '400',
  },
  overViewContent: {
    color: '#303030',
    fontSize: currentThemes.fontSize28,
    fontWeight: '500',
  },
  fieldStyle: {
    flex: 1,
    flexDirection: 'column',
    borderRadius: scaleSizeW(8),
    overflow: 'hidden',
  },
  tipText: {
    fontSize: currentThemes.fontSize18,
    color: '#959595',
    fontWeight: '500',
  },
  contentText: {
    fontSize: currentThemes.fontSize24,
    color: '#303030',
    marginTop: scaleSizeH(10),
    marginBottom: scaleSizeH(52),
    fontWeight: '400',
  },
  orderHistoryTopItem: {
    width: '100%',
    height: scaleSizeH(48),
    flexDirection: 'row',
    backgroundColor: '#8D90A3',
    paddingHorizontal: scaleSizeW(24),
    paddingVertical: scaleSizeH(12),
  },
  orderHistoryItemTitle: {
    fontSize: currentThemes.fontSize18,
    fontWeight: '400',
    color: '#FFF',
  },
  orderHistoryItem: {
    width: '100%',
    flexDirection: 'column',
    paddingHorizontal: scaleSizeW(24),
    paddingVertical: scaleSizeH(12),
  },
  orderHistoryItemChild: {
    width: '100%',
    flexDirection: 'row',
    paddingVertical: scaleSizeH(12),
  },
  itemData: {
    width: scaleSizeW(200),
    fontSize: currentThemes.fontSize24,
    color: '#60636B',
    fontWeight: '400',
  },
  itemTitle: {
    flexGrow: 1,
    fontSize: currentThemes.fontSize24,
    color: CommonColors.BlackCurrant,
    fontWeight: '400',
  },
  itemTime: {
    fontSize: currentThemes.fontSize14,
    color: '#60636B',
    fontWeight: '400',
  },
  itemPrice: {
    width: scaleSizeW(200),
    fontSize: currentThemes.fontSize24,
    color: '#303030',
    fontWeight: '500',
  },
  row: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  rowItemContainer: {
    flex: 1,
  },
  frequentItemsContainer: {
    marginTop: scaleSizeH(43),
  },
  frequentItemscontentContainerStyle: {
    overflow: 'hidden',
    borderRadius: scaleSizeH(8),
    backgroundColor: 'white',
  },
  seperator: {
    backgroundColor: '#E0E0E4',
    height: FREQUENT_SEPARATOR_HEIGHT,
    width: '100%',
  },
  tabStyle: {
    backgroundColor: 'white',
  },
  membershipTier: {
    alignItems: 'flex-start',
    justifyContent: 'center',
    marginBottom: scaleSizeH(40),
    // backgroundColor: 'red',
    marginTop: scaleSizeH(10),
  },
  membershipIcon: {
    // marginLeft: scaleSizeW(-24),
    // backgroundColor: 'blue',
  },
  eyeContainer: {
    width: scaleSizeW(44),
  },
});
