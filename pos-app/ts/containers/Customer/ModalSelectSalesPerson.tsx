import { FlashList, ListRenderItem } from '@shopify/flash-list';
import { debounce, noop } from 'lodash';
import React, { memo, useEffect, useRef, useState } from 'react';
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { ConnectedProps, connect, useDispatch } from 'react-redux';
import { bindActionCreators } from 'redux';
import { addCustomerToTransaction, getAllCustomersList, searchCustomer, setItemSalesperson, toggleToastInfo } from '../../actions';
import { ModalContainer } from '../../components/common';
import { CustomerSearch } from '../../components/customer';
import { EmptyCustomer, IconClose, icons } from '../../components/ui';
import { CommonColors, IsIOS, SharedStyles, currentThemes, scaleSizeH, scaleSizeW } from '../../constants';
import { t } from '../../constants/i18n';
import DAL from '../../dal';
import { selectStoreName } from '../../sagas/selector';
import { EmployeeType, RootState, ScreenProps } from '../../typings';
import { testProps } from '../../utils';
import { formatNameOfEmployee } from '../../utils/employee';
import { getParam } from '../../utils/navigation';

interface Props extends ScreenProps, PropsFromRedux {}

const mapStateToProps = (state: RootState) => ({
  storeName: selectStoreName(state),
});

const mapDispatchToProps = dispatch => ({
  actions: bindActionCreators(
    {
      getAllCustomersList,
      searchCustomer,
      toggleToastInfo,
      addCustomerToTransaction,
    },
    dispatch
  ),
});

const connector = connect(mapStateToProps, mapDispatchToProps);
type PropsFromRedux = ConnectedProps<typeof connector>;

function ModalSelectSalesPerson(props: Props) {
  const selectedEmployeeId = getParam(props, 'selectedEmployeeId', '');
  const itemIndex = getParam(props, 'itemIndex', undefined);
  const itemTitle = getParam(props, 'itemTitle', 'Item');
  const onSelectSalesPerson = getParam(props, 'onSelect');

  const dispatch = useDispatch();

  const [search, setSearch] = useState('');
  const closeButtonClicked = () => {
    props.navigation.goBack();
  };

  const onChangeText = debounce(
    newValue => {
      setSearch(newValue.trim());
    },
    200,
    { leading: false, trailing: true }
  );

  const onClearText = () => {
    setSearch('');
  };

  const renderClearButton = () => {
    return (
      <View style={{ paddingRight: scaleSizeW(8) }}>
        <IconClose width={scaleSizeW(48)} height={scaleSizeH(48)} color={CommonColors.Icon} />
      </View>
    );
  };

  const onSelect = (employeeId: string, employeeName: string) => {
    onSelectSalesPerson && onSelectSalesPerson(employeeId, employeeName);
    dispatch(
      setItemSalesperson({
        employeeId,
        employeeName,
        itemIndex,
      })
    );
    closeButtonClicked();
  };

  return (
    <ModalContainer
      title={t('Tag Sales Person for Title', { Title: itemTitle })}
      onCloseHandler={closeButtonClicked}
      contentStyle={styles.layer}
      mainContentStyle={SharedStyles.flexOne}
      noScroll={true}
    >
      <View style={styles.searchStyle}>
        <CustomerSearch
          placeholder={t('Search by sales person code or name')}
          searchHandler={noop}
          onChangeTextHandler={onChangeText}
          onClearValueHandler={onClearText}
          clearButton={renderClearButton}
        />
      </View>
      <SalesPersonList storeName={props.storeName} search={search} selectedId={selectedEmployeeId} onSelect={onSelect} />
    </ModalContainer>
  );
}

interface SalesPersonListProps {
  selectedId: string;
  storeName: string;
  search?: string;
  onSelect: (employeeId: string, employeeName: string) => void;
}

const SalesPersonList = memo((props: SalesPersonListProps) => {
  const { storeName, search, selectedId, onSelect } = props;

  const [dataList, setDataList] = useState([]);

  const pageInfo = useRef({ hasMore: true, pageIndex: 0, pageSize: 15 });

  const loadMoreData = () => {
    if (pageInfo.current.hasMore) {
      const data = DAL.getEmployeeListByPage(pageInfo.current.pageIndex++, pageInfo.current.pageSize, search);
      if (data.length < pageInfo.current.pageSize) {
        pageInfo.current.hasMore = false;
      }
      setDataList(list => {
        return [...list, ...data];
      });
    }
  };

  useEffect(() => {
    pageInfo.current.hasMore = true;
    pageInfo.current.pageIndex = 0;
    setDataList([]);
    loadMoreData();
  }, [search]);

  const keyExtractor = item => {
    return item.employeeId;
  };

  const renderItem: ListRenderItem<EmployeeType> = ({ item }) => {
    const name = formatNameOfEmployee(item);
    return (
      <TouchableOpacity {...testProps('al_btn_677')} onPress={() => onSelect(item.employeeId, name)}>
        <View style={styles.itemBlock}>
          <Image source={icons.person} style={styles.itemImage} resizeMode='contain' />
          <View style={SharedStyles.flexOne}>
            <View style={styles.titleRow}>
              <Text style={styles.itemTitle} numberOfLines={1} ellipsizeMode='tail'>
                {name}
              </Text>

              {item.employeeId === selectedId && <Text style={styles.tagged}>{t('Tagged')}</Text>}
            </View>
            <Text style={styles.storeName}>{storeName}</Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderSeparator = () => {
    return <View style={styles.itemSeparator} />;
  };

  return (
    <FlashList
      data={dataList}
      keyExtractor={keyExtractor}
      renderItem={renderItem}
      ItemSeparatorComponent={renderSeparator}
      onEndReached={loadMoreData}
      onEndReachedThreshold={0.5}
      ListEmptyComponent={EmptyList}
    />
  );
});

export default connector(ModalSelectSalesPerson);

const styles = StyleSheet.create({
  layer: {
    justifyContent: 'center',
    alignItems: 'center',
    width: scaleSizeW(1430),
    backgroundColor: 'white',
    overflow: 'hidden',
  },
  searchStyle: {
    width: '100%',
    paddingHorizontal: scaleSizeW(22),
    height: scaleSizeH(72),
    alignItems: 'center',
    justifyContent: 'center',
    borderBottomColor: currentThemes.splitLineColor,
    borderBottomWidth: scaleSizeH(1),
  },
  searchEmpty: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: scaleSizeH(40),
  },
  textEmpty: {
    color: '#60636B',
    fontSize: currentThemes.fontSize32,
    marginTop: scaleSizeH(11),
  },
  itemBlock: {
    paddingHorizontal: scaleSizeW(15),
    paddingVertical: scaleSizeH(10),
    flexDirection: 'row',
    columnGap: scaleSizeW(20),
    alignItems: 'center',
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    columnGap: scaleSizeW(16),
  },
  itemTitle: {
    color: CommonColors.SlateGray,
    fontSize: currentThemes.fontSize24,
    fontWeight: '500',
    lineHeight: scaleSizeH(33),
    maxWidth: '90%',
  },
  itemImage: {
    width: scaleSizeW(80),
    height: scaleSizeW(80),
  },
  storeName: {
    color: CommonColors.Placeholder,
    fontSize: currentThemes.fontSize18,
    fontWeight: '400',
    lineHeight: scaleSizeH(25),
  },
  itemSeparator: {
    flex: 1,
    backgroundColor: CommonColors.Solitude,
    height: scaleSizeH(1),
  },
  tagged: {
    backgroundColor: CommonColors.Pumpkin,
    color: CommonColors.White,
    fontSize: currentThemes.fontSize16,
    paddingHorizontal: scaleSizeW(20),
    paddingVertical: scaleSizeH(10),
    borderRadius: IsIOS ? scaleSizeH(20) : scaleSizeH(43),
    fontWeight: '400',
    overflow: 'hidden',
  },
});
const EmptyList = (
  <View style={styles.searchEmpty}>
    <EmptyCustomer />
    <Text style={styles.textEmpty}>{t('No Results Found')}</Text>
  </View>
);
