import { DrawerNavigationProp } from '@react-navigation/drawer';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import * as Immutable from 'immutable';
import { get } from 'lodash';
import React, { PureComponent } from 'react';
import { Linking, NativeModules, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { ConnectedProps, connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { requestAuthorizedAction, signOut } from '../actions';
import IconError from '../components/printer/IconError';
import {
  IconChartLine,
  IconClock,
  IconDiamond,
  IconExchange,
  IconExitApp,
  IconItemRefresh,
  IconRegister,
  IconSettings,
  IconShopBasket,
  IconSupport,
} from '../components/ui';

import { AuthorizationType, CashierActions, CommonColors, SharedStyles, currentThemes, isBaPingApp, scaleSizeH, scaleSizeW, t } from '../constants';
import DAL from '../dal';
import {
  createDeepEqualSelector,
  selectDealClubFeature,
  selectFreeTrial,
  selectIntersectLink,
  selectSupportPhoneNumberMessages,
  selectTrialOrderLimit,
} from '../sagas/selector';
import { EmployeeType, RootState, ScreenProps } from '../typings';
import { testProps } from '../utils';
import AccessControl from '../utils/accessControl';

type NavigationProp = NativeStackNavigationProp<any, string> & DrawerNavigationProp<any, string>;

export interface DrawerContentProps extends ScreenProps, PropsFromRedux {
  // Navigation props
  itmes;
  navigation: NavigationProp;
}

const fromImmutableUserInfo = createDeepEqualSelector(
  (state: Immutable.Map<string, any>) => state.getIn(['UserInfo'], Immutable.Map()).toJS(),
  userInfo => userInfo
);

const fromImmutableShift = createDeepEqualSelector(
  (state: Immutable.Map<string, any>) => state.getIn(['Shift'], Immutable.Map()).toJS(),
  Shift => Shift
);

const mapStateToProps = (state: RootState) => ({
  userInfo: fromImmutableUserInfo(state),
  shift: fromImmutableShift(state),
  dealClubFeature: selectDealClubFeature(state),
  currentEmployeeId: state.getIn<string | undefined>(['CurrentEmployee', 'employeeId']),
  supportPhoneNumberMessages: selectSupportPhoneNumberMessages(state),
  freeTrial: selectFreeTrial(state),
  trialOrderLimitEnabled: selectTrialOrderLimit(state),
  intersectLink: selectIntersectLink(state),
});

const mapDispatchToProps = dispatch => ({
  actions: bindActionCreators(
    {
      signOut,
      requestAuthorizedAction,
    },
    dispatch
  ),
});

const connector = connect(mapStateToProps, mapDispatchToProps);
type PropsFromRedux = ConnectedProps<typeof connector>;

class DrawerContent extends PureComponent<DrawerContentProps, null> {
  private _employee: EmployeeType;

  constructor(props) {
    super(props);
    this._employee = DAL.getEmployeeById(props.currentEmployeeId);
  }

  renderDrawerItmes = (icon: any, text, onPressHandler, style?: any, alLabel?: string, textStyle?: any, suffix = null) => {
    return (
      <TouchableOpacity
        {...testProps(alLabel)}
        onPress={() => {
          // NOTE: closeDrawer must be called before navigate
          requestAnimationFrame(() => this.props.navigation.closeDrawer());
          onPressHandler && onPressHandler();
        }}
      >
        <View style={[styles.drawerItemContainer, style]}>
          {icon}
          <Text style={[styles.itemText, textStyle]}>{text}</Text>
          {suffix}
        </View>
      </TouchableOpacity>
    );
  };

  goSignIn = () => {
    this.props.actions.signOut({ event: 'manually' });
  };

  goHome = () => requestAnimationFrame(() => this.props.navigation.navigate('TableLayoutScreens'));

  goTransactions = () => {
    requestAnimationFrame(() => this.props.navigation.navigate('Transactions'));
  };

  goProducts = () => requestAnimationFrame(() => this.props.navigation.navigate('Products'));

  goOpenCloseShift = () => requestAnimationFrame(() => this.props.navigation.navigate('Shift'));

  goShiftReport = () => requestAnimationFrame(() => this.props.navigation.navigate('ShiftReport'));

  goSync = () => requestAnimationFrame(() => this.props.navigation.navigate('Sync'));

  goSetting = () => requestAnimationFrame(() => this.props.navigation.navigate('Settings'));

  goSupport = () => requestAnimationFrame(() => this.props.navigation.navigate('Support'));

  goLocalDBTest = () => requestAnimationFrame(() => this.props.navigation.navigate('LocalDBTest'));

  goDealClub = () => {
    const { label, href } = this.props.dealClubFeature;
    requestAnimationFrame(() => this.props.navigation.navigate('MainWebViewPage', { title: label, url: href, isDrawerPage: true }));
  };

  goDeviceHome = () => NativeModules.RNHomeModule.goBackHome();

  openNewPrinterTestPage = () => {
    requestAnimationFrame(() => {
      this.props.navigation.closeDrawer();
      this.props.navigation.navigate('NewPrinterTestPage');
    });
  };

  goLCD = () => requestAnimationFrame(() => this.props.navigation.navigate('IminLcdTestPage'));

  onTransactionsCallBack = () => {
    const { currentEmployeeId } = this.props;
    this.props.actions.requestAuthorizedAction({
      name: CashierActions.ViewEditTransaction,
      type: AuthorizationType.Cashier,
      employeeId: currentEmployeeId,
      onSuccess: {
        callback: this.goTransactions,
      },
    });
  };

  openIntersectLink = () => {
    const { intersectLink } = this.props;
    Linking.openURL(intersectLink).catch(err => console.error('open link error', err));
  };

  render() {
    const { dealClubFeature, freeTrial, trialOrderLimitEnabled } = this.props;
    const bottomBorderStyle = { borderBottomColor: currentThemes.splitLineColor, borderBottomWidth: scaleSizeH(1) };
    const haveManageProductAccess = AccessControl.checkManageProductAccess(this._employee);
    let employeeFullName = '';
    if (Boolean(this._employee)) {
      const firstName = get(this._employee, 'firstName', '');
      const lastName = get(this._employee, 'lastName', '');
      employeeFullName = `${firstName}  ${lastName}`;
    }
    const { visible = false, label } = dealClubFeature;
    return (
      <View style={styles.container}>
        <View style={styles.signContent}>
          <TouchableOpacity {...testProps('al_sign_out')} onPress={this.goSignIn}>
            <Text style={[styles.signText, { color: '#FC7118' }]}>{t('Sign Out')}</Text>
          </TouchableOpacity>
          <Text style={styles.signText}>{employeeFullName}</Text>
        </View>
        {trialOrderLimitEnabled && freeTrial && (
          <View style={styles.trialIntersectContent}>
            <Text style={styles.trialIntersectText}>{t('Trial Intersect Warning')}</Text>
            <Text style={styles.linkText} onPress={this.openIntersectLink}>
              {t('How to activate your account')}
            </Text>
          </View>
        )}
        <ScrollView style={SharedStyles.flexOne}>
          {this.renderDrawerItmes(
            <IconRegister width={scaleSizeW(40)} height={scaleSizeH(40)} color={CommonColors.Icon} />,
            t('Register'),
            this.goHome,
            null,
            'al_register'
          )}
          {this.renderDrawerItmes(
            <IconExchange width={scaleSizeW(40)} height={scaleSizeH(40)} color={CommonColors.Icon} />,
            t('Transactions'),
            this.onTransactionsCallBack,
            null,
            'al_transactions'
          )}
          {haveManageProductAccess
            ? this.renderDrawerItmes(
                <IconShopBasket testID={'widgets'} width={scaleSizeW(40)} height={scaleSizeH(48)} color={CommonColors.Icon} />,
                t('Manage Products'),
                this.goProducts,
                bottomBorderStyle,
                'al_products'
              )
            : null}
          {DAL.haveProduct()
            ? this.renderDrawerItmes(
                <IconClock testID={'history'} width={scaleSizeW(40)} height={scaleSizeH(40)} color={CommonColors.Icon} />,
                t(this.props.shift.ShiftOpenStatus ? 'Close Shift' : 'Open Shift'),
                this.goOpenCloseShift,
                null,
                'al_open_close_shift'
              )
            : null}
          {get(this._employee, 'isManager', false) &&
            this.renderDrawerItmes(
              <IconChartLine width={scaleSizeW(40)} height={scaleSizeH(40)} color={CommonColors.Icon} />,
              t('Sales Report'),
              this.goShiftReport,
              bottomBorderStyle,
              'al_shiftreport'
            )}
          {this.renderDrawerItmes(
            <IconItemRefresh width={scaleSizeW(40)} height={scaleSizeH(40)} color={CommonColors.Icon} />,
            t('Sync'),
            this.goSync,
            null,
            'al_sync'
          )}
          {this.renderDrawerItmes(
            <IconSettings width={scaleSizeW(40)} height={scaleSizeH(40)} color={CommonColors.Icon} />,
            t('Settings'),
            this.goSetting,
            null,
            'al_settings',
            null,
            <IconError style={{ marginLeft: scaleSizeW(20), width: 22, height: 22 }} />
          )}
          {this.renderDrawerItmes(
            <IconSupport width={scaleSizeW(40)} height={scaleSizeH(40)} color={CommonColors.Icon} />,
            JSON.parse(JSON.stringify(this.props.supportPhoneNumberMessages)).visible ? t('DRAWER_TAB_SUPPORT_STOREHUB_CARE') : t('DRAWER_TAB_SUPPORT'),
            this.goSupport,
            null,
            'al_support'
          )}
          {visible &&
            this.renderDrawerItmes(
              <IconDiamond width={scaleSizeW(40)} height={scaleSizeH(40)} color={CommonColors.Icon} />,
              label,
              this.goDealClub,
              null,
              'al_dealclub'
            )}
          {__DEV__ &&
            this.renderDrawerItmes(
              <IconSettings width={scaleSizeW(40)} height={scaleSizeH(40)} color={'#8D90A3'} />,
              'New Printer Test Page',
              this.openNewPrinterTestPage,
              null,
              'al_new_printer'
            )}
          {__DEV__ &&
            this.renderDrawerItmes(
              <IconSettings width={scaleSizeW(40)} height={scaleSizeH(40)} color={'#8D90A3'} />,
              'LocalDB Test Page',
              this.goLocalDBTest,
              null,
              'al_local_db'
            )}
          {__DEV__ &&
            this.renderDrawerItmes(
              <IconSettings width={scaleSizeW(40)} height={scaleSizeH(40)} color={'#8D90A3'} />,
              'Imin LCD Test Page',
              this.goLCD,
              null,
              'al_imin_lcd'
            )}
        </ScrollView>
        {isBaPingApp &&
          this.renderDrawerItmes(
            <IconExitApp width={scaleSizeW(40)} height={scaleSizeH(40)} color={'#FF2825'} />,
            t('Exit App'),
            this.goDeviceHome,
            null,
            'al_exit',
            { color: '#FF2825' }
          )}
      </View>
    );
  }
}

export default connector(DrawerContent);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: scaleSizeH(40),
  },
  drawerItemContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    height: scaleSizeH(88),
    paddingLeft: scaleSizeW(24),
  },
  itemText: {
    color: '#60636B',
    fontSize: currentThemes.fontSize26,
    fontWeight: '500',
    marginLeft: scaleSizeW(20),
  },
  signText: {
    color: '#60636B',
    fontSize: currentThemes.fontSize24,
    fontWeight: '400',
  },
  signContent: {
    height: scaleSizeH(88),
    flexDirection: 'row',
    paddingHorizontal: scaleSizeW(24),
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  trialIntersectContent: {
    width: '100%',
    backgroundColor: '#121838',
    paddingHorizontal: scaleSizeW(24),
    paddingVertical: scaleSizeH(20),
  },
  trialIntersectText: {
    fontWeight: '400',
    fontSize: currentThemes.fontSize18,
    color: 'white',
    lineHeight: currentThemes.fontSize26,
  },
  linkText: {
    fontWeight: '500',
    fontSize: currentThemes.fontSize18,
    color: '#00B0FF',
    lineHeight: currentThemes.fontSize26,
    marginTop: scaleSizeH(10),
    textDecorationLine: 'underline',
  },
});
