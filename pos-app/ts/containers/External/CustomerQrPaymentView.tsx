import React from 'react';
import { Image, Text, View } from 'react-native';
import { useSelector } from 'react-redux';
import { selectCfdConfigurations } from '../../sagas/selector';
import { scaleLayoutSizeCfd, scaleSizeCfd } from './CfdUtils';
import { icons } from '../../components/ui/icons';

export const QrPaymentView = () => {
  const cfdState = useSelector(selectCfdConfigurations);

  const qrData = cfdState.configurations.qrData;
  const qrCodeProvider = cfdState.configurations.qrCodeProvider;
  const isQRPh = qrCodeProvider === 'Modulus Labs - QR Ph';

  return !isQRPh ? (
    <View style={{ flex: 1, backgroundColor: '#fff' }}>
      <Image
        style={{ height: '100%', width: '100%', position: 'absolute', bottom: 0 }}
        source={require('../../../assets/icons/cfdContainerNoPower.png')}
        resizeMode={'stretch'}
      />
      <View style={{ flex: 1 }}>
        <Text
          style={{
            textAlign: 'center',
            paddingTop: scaleLayoutSizeCfd(40),
            fontSize: scaleSizeCfd(24),
            fontWeight: '700',
            color: 'black',
          }}
        >
          {'Scan to Pay'}
        </Text>
        <View style={{ flex: 1 }} />

        <View
          style={{
            position: 'absolute',
            width: '100%',
            height: '100%',
            justifyContent: 'center',
            alignItems: 'center',
            padding: scaleLayoutSizeCfd(64),
          }}
        >
          {qrData && (
            <Image
              style={{
                alignSelf: 'center',
                width: '70%',
                height: '50%',
                resizeMode: 'contain',
              }}
              // source={require('../../../assets/icons/cfdBg.png')}
              source={{ uri: 'data:image/png;base64,' + qrData }}
            />
          )}
        </View>

        <View style={{ width: '100%', height: scaleLayoutSizeCfd(22), justifyContent: 'center', alignItems: 'center' }}>
          {/* <IconStoreHub width={147} height={22} ></IconStoreHub> */}
        </View>
        <Text
          style={{
            textAlign: 'center',
            marginHorizontal: scaleLayoutSizeCfd(88),
            fontSize: scaleLayoutSizeCfd(32),
            color: 'white',
          }}
        />
      </View>
    </View>
  ) : (
    <View style={{ flex: 1, backgroundColor: '#fff' }}>
      <Image
        style={{ height: '100%', width: '100%', position: 'absolute', bottom: 0 }}
        source={require('../../../assets/icons/backgroundWithLogo.png')}
        resizeMode={'stretch'}
      />
      <View style={{ flex: 1 }}>
        <Text
          style={{
            textAlign: 'center',
            marginTop: scaleLayoutSizeCfd(20),
            fontSize: scaleSizeCfd(48),
            fontWeight: '700',
            color: 'white',
          }}
        >
          {'Scan to Pay'}
        </Text>
        <View style={{ flex: 1 }} />

        <View
          style={{
            position: 'absolute',
            width: '100%',
            height: '100%',
            justifyContent: 'center',
            alignItems: 'center',
            padding: scaleLayoutSizeCfd(64),
          }}
        >
          {qrData && (
            <View
              style={{
                width: '90%',
                height: '85%',
                backgroundColor: 'white',
                borderRadius: scaleLayoutSizeCfd(40),
                justifyContent: 'flex-end',
                alignItems: 'center',
                paddingBottom: scaleLayoutSizeCfd(20),
                paddingTop: scaleLayoutSizeCfd(10),
              }}
            >
              <Image
                source={icons.qrPh}
                style={{
                  alignSelf: 'center',
                  width: '75%',
                  resizeMode: 'contain',
                  flex: 1,
                }}
                resizeMode='contain'
              />
              <Image
                style={{
                  alignSelf: 'center',
                  width: '80%',
                  resizeMode: 'contain',
                  flex: 2,
                }}
                source={{ uri: 'data:image/png;base64,' + qrData }}
              />
            </View>
          )}
        </View>

        <View style={{ width: '100%', height: scaleLayoutSizeCfd(22), justifyContent: 'center', alignItems: 'center' }}>
          {/* <IconStoreHub width={147} height={22} ></IconStoreHub> */}
        </View>
        <Text
          style={{
            textAlign: 'center',
            marginHorizontal: scaleLayoutSizeCfd(88),
            fontSize: scaleLayoutSizeCfd(32),
            color: 'white',
          }}
        />
      </View>
    </View>
  );
};
