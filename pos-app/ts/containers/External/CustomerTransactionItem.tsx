import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { CfdPurchasedItem } from '../../actions/cfd';
import { localeNumber } from '../../utils';
import { scaleLayoutSizeCfd, scaleSizeCfd } from './CfdUtils';
import { IconPromotion } from '../../components/ui';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'column',
    padding: scaleLayoutSizeCfd(8),
  },
  quantity: {
    width: scaleLayoutSizeCfd(40),
    textAlign: 'right',
    color: '#000000',
    fontSize: scaleSizeCfd(20),
  },
  name: {
    flex: 1,
    marginHorizontal: scaleLayoutSizeCfd(10),
    color: '#000000',
    fontSize: scaleSizeCfd(20),
  },
  total: {
    textAlign: 'right',
    marginRight: scaleLayoutSizeCfd(10),
    color: '#000000',
    fontSize: scaleSizeCfd(20),
  },
  options: {
    color: '#888888',
    fontSize: scaleSizeCfd(16),
    paddingStart: scaleLayoutSizeCfd(50),
  },
  notes: {
    color: '#888888',
    fontSize: scaleSizeCfd(16),
    paddingStart: scaleLayoutSizeCfd(50),
  },
  productContent: {
    flexDirection: 'row',
  },
  promotionList: {},
  promotionContent: {
    paddingVertical: scaleSizeCfd(8),
    paddingStart: scaleLayoutSizeCfd(8),
    backgroundColor: '#ffe9d1',
    flexDirection: 'row',
  },
  promotionTag: {
    width: scaleLayoutSizeCfd(40),
    color: '#000000',
    fontSize: scaleLayoutSizeCfd(8),
  },
  beforeDiscount: {
    textAlign: 'right',
    fontStyle: 'italic',
    textDecorationLine: 'line-through',
    marginRight: 10,
    color: '#777777',
    fontSize: scaleLayoutSizeCfd(20),
  },
});

interface Props {
  item: CfdPurchasedItem;
}

const CustomerTransactionItem = (props: Props) => {
  return (
    <View style={styles.container}>
      {props.item.title && (
        <>
          <View style={styles.productContent}>
            <Text style={styles.quantity}>{props.item.quantity}</Text>
            <Text style={styles.name}>{props.item.title}</Text>
            <Text style={styles.total}>{localeNumber(props.item.total)}</Text>
          </View>
          <View style={styles.productContent}>
            <View style={styles.container}>
              {Boolean(props.item.options) && <Text style={styles.options}>{props.item.options}</Text>}
              {Boolean(props.item.notes) && <Text style={styles.notes}>{props.item.notes}</Text>}
            </View>
            {props.item.total !== props.item.subTotal && <Text style={styles.beforeDiscount}>{localeNumber(props.item.subTotal)}</Text>}
          </View>
          {/* <Text style={styles.subtotal}>{localeNumber(props.item.subtotal)}</Text> */}
          <View style={styles.promotionList}>{/* Render the promotion list */}</View>
        </>
      )}
      {props.item.promotions &&
        props.item.promotions.map(promotion => {
          return (
            <View style={styles.promotionContent} key={promotion.promotionName}>
              <View style={styles.promotionTag}>
                <IconPromotion height={scaleSizeCfd(32)} width={scaleSizeCfd(32)} />
              </View>
              <Text style={styles.name}>{promotion.promotionName}</Text>
              <Text style={styles.total}>{localeNumber(promotion.discount)}</Text>
            </View>
          );
        })}
    </View>
  );
};

export default CustomerTransactionItem;
