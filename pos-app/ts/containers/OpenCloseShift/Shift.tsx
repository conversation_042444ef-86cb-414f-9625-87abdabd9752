import { DrawerActions } from '@react-navigation/compat';
import moment from 'moment';
import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { connect, ConnectedProps } from 'react-redux';
import { bindActionCreators } from 'redux';
import { requestAuthorizedAction, checkFreeStorage } from '../../actions';
import { NAVIGATOR_PADDING_HORIZONTAL } from '../../components/common';
import MenuWithNotification from '../../components/common/MenuWithNotification';
import { AuthorizationType, CashierActions, CommonColors, currentThemes, scaleSizeH, scaleSizeW, SharedStyles, t } from '../../constants';
import DAL from '../../dal';
import { testProps } from '../../utils';
import {
  selectEmployeeId,
  selectInsufficientStorageWarning,
  selectLastZReadingCloseTime,
  selectOperationHours,
  selectShiftOpenStatus,
} from '../../sagas/selector';

import { RootState, ScreenProps } from '../../typings';
import { getAlreadyCloseZReading } from '../../utils/datetime';

interface Props extends ScreenProps, PropsFromRedux {}

const mapStateToProps = (state: RootState) => ({
  currentEmployeeId: selectEmployeeId(state),
  shiftOpenStatus: selectShiftOpenStatus(state),
  lastCloseZReadingTime: selectLastZReadingCloseTime(state),
  operationHours: selectOperationHours(state),
  insufficientStorageWarning: selectInsufficientStorageWarning(state),
});

const mapDispatchToProps = dispatch => ({
  actions: bindActionCreators(
    {
      requestAuthorizedAction,
      checkFreeStorage,
    },
    dispatch
  ),
});

const connector = connect(mapStateToProps, mapDispatchToProps);
type PropsFromRedux = ConnectedProps<typeof connector>;

class Shift extends React.Component<Props> {
  static navigationOptions = () => ({
    headerShown: false,
  });

  constructor(props) {
    super(props);
  }

  onOpenOrCloseShiftHandler = () => {
    this.requestOpenOrCloseShift();
  };

  requestOpenOrCloseShift = () => {
    const {
      currentEmployeeId,
      navigation,
      lastCloseZReadingTime,
      operationHours,
      shiftOpenStatus,
      actions: { requestAuthorizedAction },
    } = this.props;
    const alreadyCloseZReading = getAlreadyCloseZReading(lastCloseZReadingTime, operationHours);
    if (alreadyCloseZReading && !shiftOpenStatus) {
      this.props.navigation.navigate('ModalInfo', {
        infoFontWeight: 'bold',
        textAlign: 'center',
        info: t('OpenShiftForbidden'),
        okText: t('OK'),
      });
    } else {
      if (shiftOpenStatus || !this.props.insufficientStorageWarning.enabled) {
        requestAuthorizedAction({
          name: CashierActions.OpenCloseShift,
          type: AuthorizationType.Cashier,
          employeeId: currentEmployeeId,
          onSuccess: {
            callback: () => {
              navigation.navigate('ModalShiftChange');
            },
          },
        });
      } else {
        this.props.actions.checkFreeStorage({
          navigation: navigation,
          onContinue: () => {
            requestAuthorizedAction({
              name: CashierActions.OpenCloseShift,
              type: AuthorizationType.Cashier,
              employeeId: currentEmployeeId,
              onSuccess: {
                callback: () => {
                  navigation.navigate('ModalShiftChange');
                },
              },
            });
          },
        });
      }
    }
  };

  openMainDrawer = () => {
    this.props.navigation.dispatch(DrawerActions.openDrawer());
  };

  render() {
    const { shiftOpenStatus } = this.props;
    let openTime;
    if (shiftOpenStatus) {
      const currentShift = DAL.getLastShift();
      if (currentShift) {
        openTime = moment(currentShift.openTime);
        openTime = openTime.format('h:mm A, dddd MMM D YYYY');
      }
    }
    return (
      <View style={styles.container}>
        <View style={styles.headerContainer}>
          <TouchableOpacity style={SharedStyles.touchableIconContainer} onPress={this.openMainDrawer} {...testProps('al_shift_menu')}>
            <MenuWithNotification color={CommonColors.Icon} />
          </TouchableOpacity>
        </View>
        <Text style={styles.topTip}>{shiftOpenStatus ? t('Yes,we are') : t('Sorry,we are')}</Text>
        <Text style={styles.bottomTip}>{shiftOpenStatus ? t('OPEN') : t('CLOSED')}</Text>
        {shiftOpenStatus && openTime && (
          <>
            <Text style={styles.openTimeLabel}>{t('OPENED AT')}</Text>
            <View style={styles.openTimeLine} />
            <Text style={styles.openTime}>{openTime}</Text>
          </>
        )}
        <TouchableOpacity
          onPress={this.onOpenOrCloseShiftHandler}
          style={styles.buttonStyle}
          {...testProps(`al_${shiftOpenStatus ? 'close_register' : 'open_register'}`)}
        >
          <Text style={styles.buttonText}>{shiftOpenStatus ? t('Close Register') : t('Open Register')}</Text>
        </TouchableOpacity>
      </View>
    );
  }
}

export default connector(Shift);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: currentThemes.bgMainColor,
    flexDirection: 'column',
    alignItems: 'center',
  },
  headerContainer: {
    width: '100%',
    paddingVertical: scaleSizeH(30),
    paddingHorizontal: NAVIGATOR_PADDING_HORIZONTAL,
  },
  topTip: {
    fontFamily: 'Noteworthy-Bold',
    fontSize: currentThemes.fontSize72,
    color: '#60636B',
    width: '100%',
    textAlign: 'center',
    marginTop: scaleSizeH(100),
  },
  bottomTip: {
    fontFamily: 'Chalkboard-Bold',
    fontSize: currentThemes.fontSize144,
    color: '#60636B',
    width: '100%',
    textAlign: 'center',
    includeFontPadding: false,
    lineHeight: currentThemes.fontSize144,
  },
  buttonStyle: {
    position: 'absolute',
    justifyContent: 'center',
    height: scaleSizeH(112),
    width: scaleSizeH(640),
    backgroundColor: currentThemes.buttonBackgroundColor,
    bottom: scaleSizeH(32),
    borderRadius: scaleSizeW(8),
  },
  buttonText: {
    fontSize: currentThemes.fontSize30,
    fontWeight: 'bold',
    color: '#FFF',
    textAlign: 'center',
    borderRadius: scaleSizeW(8),
  },
  openTimeLabel: {
    color: '#60636B',
    fontSize: currentThemes.fontSize32,
    fontWeight: '500',
    marginTop: scaleSizeH(150),
  },
  openTimeLine: {
    marginVertical: scaleSizeH(12),
    width: scaleSizeW(380),
    height: 1,
    backgroundColor: '#D6D6D6',
  },
  openTime: {
    color: '#60636B',
    fontSize: currentThemes.fontSize28,
    fontWeight: '400',
  },
});
