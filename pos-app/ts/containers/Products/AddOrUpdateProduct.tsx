import * as Immutable from 'immutable';
import { floor, get, isEmpty } from 'lodash';
import React from 'react';
import { <PERSON><PERSON>, FlatList, StatusBar, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { connect, ConnectedProps } from 'react-redux';
import { bindActionCreators } from 'redux';
import { createSelector } from 'reselect';
import { addProductAction, updateProductAction } from '../../actions';
import { PosTextInput } from '../../components/textInput';
import { IconClose, IconDown } from '../../components/ui';
import { CommonColors, currentThemes, scaleSizeH, scaleSizeW, SharedStyles, t, width } from '../../constants';
import DAL from '../../dal';
import { LeafState, RootState, ScreenProps } from '../../typings';
import {
  checkNumberInput,
  checkNumberInputWithoutDefault,
  getTaxCodeList,
  getTaxCodeObject,
  isValidNumber,
  roundingToAmount,
  roundTo2DecimalPlaces,
  testProps,
} from '../../utils';
import { getParam } from '../../utils/navigation';

interface Props extends ScreenProps, PropsFromRedux {}

interface State {
  name: string;
  sku: string;
  priceIncludeTax: number;
  priceExcludeTax: number;
  priceIncludeTaxDisplay: number;
  priceExcludeTaxDisplay: number;
  quantity: number;
  barcode: string;
  showTaxCode: boolean;
  taxcode: any;
  enableSaveBtn: boolean;
}

const mapStateToProps = (state: RootState) => ({
  currency: state.getIn<string>(['Storage', 'storeInfo', 'store', 'currency']),
  storeId: state.getIn<string>(['Storage', 'storeInfo', 'store', '_id']),
  defaultTax: state.getIn<number>(['Storage', 'storeInfo', 'store', 'tax']),
  taxCodes: fromImmutableTaxcodes(state),
  currentEmployeeId: state.getIn<string>(['CurrentEmployee', 'employeeId']),
});

const fromImmutableTaxcodes = createSelector<RootState, LeafState, any[]>(
  (state: Immutable.Map<string, any>) => state.getIn(['Storage', 'storeInfo', 'store', 'taxCodes'], Immutable.Map()),
  taxCodes => taxCodes.toJS()
);

const mapDispatchToProps = dispatch => ({
  actions: bindActionCreators(
    {
      addProductAction,
      updateProductAction,
    },
    dispatch
  ),
});

const connector = connect(mapStateToProps, mapDispatchToProps);
type PropsFromRedux = ConnectedProps<typeof connector>;

class AddOrUpdateProduct extends React.PureComponent<Props, State> {
  private isEditing = false;
  private _taxInPriceInputRef?: TextInput;

  constructor(props: Props) {
    super(props);
    const product = getParam(props, 'product', null);
    this.isEditing = product != null;
    const quantity = getParam(props, 'quantity', null);
    let barcode = getParam(props, 'barcode', '');
    let name = getParam(props, 'name', '');
    if (product != null) {
      barcode = product.barcode;
      name = product.title;
    }
    const taxcode = getTaxCodeObject(product != null ? product.taxCode : '', this.props.taxCodes, this.props.defaultTax);
    this.state = {
      name,
      sku: product != null ? product.skuId : '',
      priceExcludeTax: product != null ? product.unitPrice : null,
      priceIncludeTax: null,
      priceIncludeTaxDisplay: null,
      priceExcludeTaxDisplay: null,
      quantity,
      barcode,
      taxcode,
      showTaxCode: false,
      enableSaveBtn: true,
    };
  }

  componentDidMount() {
    if (this.state.taxcode != null) this.calculaTaxIncludePrice();
  }

  closeButtonClicked = () => {
    requestAnimationFrame(() => this.props.navigation.goBack());
  };

  validateEntryData = () => {
    if (this.state.name.length === 0) {
      Alert.alert(t('PRODUCT NAME') + ' ' + t('Required'), '', [{ text: t('OK'), onPress: () => console.log('OK Pressed') }]);
      return false;
    }
    return true;
  };

  onUpdateProduct = () => {
    const product = getParam(this.props, 'product', null);
    if (!product) {
      return;
    }

    this.setState({ enableSaveBtn: false });
    if (!this.validateEntryData()) {
      this.setState({ enableSaveBtn: true });
      return;
    }
    const onSuccess = {
      callback: payload => {
        DAL.saveProduct(payload.res);
        const { _id } = payload.res;
        const refreshCurrentItem = getParam(this.props, 'refreshCurrentItem', null);
        refreshCurrentItem && refreshCurrentItem(_id);
        this.closeButtonClicked();
      },
    };

    const onFailure = {
      callback: payload => {
        let error = t('Saving Product fail');
        if (Boolean(payload.message)) {
          error = payload.message;
        }
        Alert.alert(error, '', [{ text: t('OK'), onPress: () => console.log('OK Pressed') }]);
      },
    };

    const { productId, category, tags, inventoryType, unit, kitchenPrinter, isSerialized, trackInventory, priceType } = product;
    let _tags;
    if (Boolean(tags) && tags.length > 0) {
      const _tArray = [];
      for (const _t of tags) {
        _tArray.push(_t);
      }
      _tags = _tArray.join(',');
    }
    const { name, barcode, priceExcludeTax, taxcode, sku, quantity } = this.state;
    const { storeId, currentEmployeeId } = this.props;
    this.props.actions.updateProductAction({
      productId,
      category,
      tags: _tags,
      inventoryType,
      unit,
      kitchenPrinter,
      isSerialized,
      trackInventory,
      title: name,
      barcode: barcode,
      pricingType: priceType || 'fixed',
      unitPrice: priceExcludeTax || 0,
      taxCode: taxcode != null ? taxcode._id : '',
      multiStoreQuantity: isValidNumber(quantity)
        ? [
            {
              storeId: storeId,
              quantity: quantity || 0,
            },
          ]
        : null,
      sku: sku,
      employeeId: currentEmployeeId,
      onSuccess,
      onFailure,
    });
  };

  onAddProduct = () => {
    const { name, barcode, priceExcludeTax, taxcode, sku, quantity } = this.state;
    this.setState({ enableSaveBtn: false });
    if (!this.validateEntryData()) {
      this.setState({ enableSaveBtn: true });
      return;
    }
    const onSuccess = {
      callback: payload => {
        DAL.saveProduct(payload.res);
        const { _id } = payload.res;
        const reloadData = getParam(this.props, 'reloadData', null);
        reloadData && reloadData({ id: _id, barcode, name });
        this.closeButtonClicked();
      },
    };

    const onFailure = {
      callback: payload => {
        this.setState({ enableSaveBtn: true });
        let error = t('Saving Product fail');
        if (payload.message) {
          error = payload.message;
        }
        Alert.alert(error, '', [{ text: t('OK'), onPress: () => console.log('OK Pressed') }]);
      },
    };
    const { storeId, currentEmployeeId } = this.props;
    this.props.actions.addProductAction({
      title: name,
      barcode: barcode,
      pricingType: 'fixed',
      unitPrice: priceExcludeTax || 0,
      inventoryType: '',
      taxCode: taxcode != null ? this.state.taxcode._id : '',
      trackInventory: true,
      multiStoreQuantity: isValidNumber(quantity)
        ? [
            {
              storeId: storeId,
              quantity: quantity || 0,
            },
          ]
        : null,
      sku: sku,
      employeeId: currentEmployeeId,
      onSuccess,
      onFailure,
    });
  };

  onChangeTaxExcludePrice = () => {
    this.calculaTaxIncludePrice();
  };

  calculaTaxIncludePrice = () => {
    const rate = this.state.taxcode === null ? 0 : this.state.taxcode.rate;
    const priceExcludeTax = this.state.priceExcludeTax || 0;
    const priceIncludeTax = priceExcludeTax * (1 + roundingToAmount(rate, 0.00001));
    this.setState({
      priceExcludeTax,
      priceIncludeTax,
      priceExcludeTaxDisplay: roundTo2DecimalPlaces(priceExcludeTax),
      priceIncludeTaxDisplay: roundTo2DecimalPlaces(priceIncludeTax),
    });
  };

  onChangeTaxIncludePrice = () => {
    this.calculaTaxExcludePrice();
  };

  calculaTaxExcludePrice = (callback?: () => void) => {
    const rate = this.state.taxcode === null ? 0 : this.state.taxcode.rate;
    const priceIncludeTax = this.state.priceIncludeTax || 0;
    const priceExcludeTax = priceIncludeTax / (1 + roundingToAmount(rate, 0.00001));
    // Round calculated price
    this.setState(
      {
        priceIncludeTax: priceIncludeTax,
        priceExcludeTax,
        priceExcludeTaxDisplay: roundTo2DecimalPlaces(priceExcludeTax),
        priceIncludeTaxDisplay: roundTo2DecimalPlaces(priceIncludeTax),
      },
      callback
    );
  };

  formatedTaxRate = rate => {
    return Math.round(rate * 10000) / 100;
  };

  onSelectTaxCode = taxcode => {
    this.setState({ showTaxCode: false, taxcode }, this.calculaTaxIncludePrice);
  };

  renderTitleBar = () => {
    return (
      <View style={styles.titleBarContainer}>
        <TouchableOpacity {...testProps('al_btn_462')} style={styles.titleBarTouchableCloseIcon} onPress={this.closeButtonClicked}>
          <IconClose color={CommonColors.Icon} width={scaleSizeW(48)} height={scaleSizeH(48)} />
        </TouchableOpacity>
        <View style={{ flexGrow: 1, justifyContent: 'center', alignItems: 'center' }}>
          <Text style={styles.titleBarTitle}>{this.isEditing ? t('Edit Product') : t('Add Product')}</Text>
        </View>
        {this.renderSaveButton()}
      </View>
    );
  };

  renderTaxCodeItem = ({ item }) => {
    const rateString = String(this.formatedTaxRate(item.rate)) + '%';
    const selected = this.state.taxcode._id === item._id;
    return (
      <TouchableOpacity
        {...testProps('al_btn_84')}
        style={{ ...StyleSheet.flatten(styles.taxCodeItem), backgroundColor: selected ? '#8D90A3' : 'transparent' }}
        onPress={() => this.onSelectTaxCode(item)}
      >
        <Text style={{ ...StyleSheet.flatten(styles.taxCodeItemTitle), color: selected ? 'white' : '#60636B' }}>{item.name + ': ' + rateString}</Text>
      </TouchableOpacity>
    );
  };

  _keyExtractor = (item, index: number) => {
    const id = get(item, '_id');
    return `${id}_${index}`;
  };

  renderTaxCodeList = () => {
    const dataSource = getTaxCodeList(this.props.taxCodes, this.props.defaultTax);
    return (
      <FlatList
        style={styles.taxCodeList}
        contentContainerStyle={styles.taxCodeListContentContainer}
        keyExtractor={this._keyExtractor}
        renderItem={this.renderTaxCodeItem}
        data={dataSource}
      />
    );
  };

  taxInPriceInputRef = (ref: TextInput) => {
    this._taxInPriceInputRef = ref;
  };

  toogleTaxCodeMenu = () => this.setState({ showTaxCode: !this.state.showTaxCode });

  render() {
    const { priceExcludeTaxDisplay, priceIncludeTaxDisplay, quantity, taxcode, name } = this.state;
    let taxcodeString = t('Default');
    if (taxcode != null) {
      const rateString = String(this.formatedTaxRate(taxcode.rate)) + '%';
      taxcodeString = taxcode.name + ' ' + rateString;
    } else {
      const rateString = String(this.formatedTaxRate(this.props.defaultTax)) + '%';
      taxcodeString = t('Default') + ' ' + rateString;
    }
    return (
      <View style={styles.container}>
        <StatusBar barStyle='dark-content' />
        <View style={styles.layer}>
          {this.renderTitleBar()}
          <KeyboardAwareScrollView
            enableOnAndroid
            keyboardOpeningTime={50}
            horizontal={false}
            contentContainerStyle={styles.keyboradScrollContentContainer}
            keyboardDismissMode='none'
            keyboardShouldPersistTaps='never'
            style={styles.keyboradScrollContainer}
          >
            <View style={styles.rowContainner}>
              <View style={styles.inputItemContainerFlex2}>
                <Text style={styles.titleFont}>{t('PRODUCT NAME')}</Text>
                <View style={styles.textInputContainner}>
                  <PosTextInput
                    {...testProps('al_product_name')}
                    style={styles.contentFont}
                    placeholder={t('Required *')}
                    placeholderTextColor={'#9F9F9F'}
                    value={name}
                    onChangeText={name => this.setState({ name })}
                    underlineColorAndroid='transparent'
                    autoFocus={true}
                  />
                </View>
              </View>
              <View style={styles.inputItemContainer}>
                <Text style={styles.titleFont}>{t('SKU')}</Text>
                <View style={styles.textInputContainner}>
                  <TextInput
                    {...testProps('al_product_sku')}
                    style={styles.contentFont}
                    placeholder={t('Optional - Unique')}
                    placeholderTextColor={'#9F9F9F'}
                    value={this.state.sku}
                    onChangeText={sku => this.setState({ sku })}
                    underlineColorAndroid='transparent'
                  />
                </View>
              </View>
            </View>
            <View style={styles.rowContainner}>
              <View style={{ ...StyleSheet.flatten(styles.inputItemContainerFlex2), flexDirection: 'row' }}>
                <View style={[styles.inputItemContainer, styles.inputItemContainerMarginRight]}>
                  <Text style={styles.titleFont}>{t('PRICE EX TAX')}</Text>
                  <View style={styles.textInputContainner}>
                    <TextInput
                      {...testProps('al_product_exclude_tax_price')}
                      style={styles.contentFont}
                      placeholder={'0.00'}
                      placeholderTextColor={'#9F9F9F'}
                      keyboardType='decimal-pad'
                      value={Boolean(priceExcludeTaxDisplay) ? String(priceExcludeTaxDisplay) : null}
                      onSubmitEditing={this.onChangeTaxExcludePrice}
                      onEndEditing={this.onChangeTaxExcludePrice}
                      onChangeText={value => {
                        checkNumberInput(value, value => this.setState({ priceExcludeTax: value, priceExcludeTaxDisplay: value }));
                      }}
                      underlineColorAndroid='transparent'
                    />
                  </View>
                </View>
                <View style={styles.inputItemContainer}>
                  <Text style={styles.titleFont}>{t('TAX CODE')}</Text>
                  <View style={styles.textInputContainner} {...testProps('al_product_taxCode')}>
                    <TouchableOpacity {...testProps('al_btn_850')} style={styles.taxCodeTouchableContainer} onPress={this.toogleTaxCodeMenu}>
                      <Text style={[styles.contentFont, { height: scaleSizeH(30), flex: 1 }, isEmpty(name) && { color: '#9F9F9F' }]}>{taxcodeString}</Text>
                      <IconDown color={CommonColors.Icon} width={scaleSizeW(36)} height={scaleSizeH(36)} />
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
              <View style={styles.inputItemContainer}>
                <Text style={styles.titleFont}>{t('TAX - IN PRICE')}</Text>
                <View style={styles.textInputContainner}>
                  <TextInput
                    ref={this.taxInPriceInputRef}
                    {...testProps('al_product_taxInPrice')}
                    style={styles.contentFont}
                    placeholder={'0.00'}
                    placeholderTextColor={'#9F9F9F'}
                    keyboardType='decimal-pad'
                    value={Boolean(priceIncludeTaxDisplay) ? String(priceIncludeTaxDisplay) : null}
                    onSubmitEditing={this.onChangeTaxIncludePrice}
                    onEndEditing={this.onChangeTaxIncludePrice}
                    onChangeText={value => {
                      checkNumberInput(value, value => this.setState({ priceIncludeTax: value, priceIncludeTaxDisplay: value }));
                    }}
                    underlineColorAndroid='transparent'
                  />
                </View>
              </View>
            </View>
            <View style={styles.rowContainner}>
              <View style={styles.inputItemContainerFlex2}>
                <Text style={styles.titleFont}>{t('BARCODE')}</Text>
                <View style={styles.textInputContainner}>
                  <TextInput
                    {...testProps('al_product_barCode')}
                    style={styles.contentFont}
                    placeholder={t('Optional')}
                    placeholderTextColor={'#9F9F9F'}
                    value={this.state.barcode}
                    onChangeText={barcode => this.setState({ barcode })}
                  />
                </View>
              </View>
              <View style={styles.inputItemContainer}>
                <Text style={styles.titleFont}>{t('QUANTITY')}</Text>
                <View style={styles.textInputContainner}>
                  <TextInput
                    {...testProps('al_product_quantity')}
                    style={styles.contentFont}
                    placeholder={t('Optional')}
                    placeholderTextColor={'#9F9F9F'}
                    keyboardType='decimal-pad'
                    value={Boolean(quantity) ? String(quantity) : null}
                    onChangeText={value => {
                      checkNumberInputWithoutDefault(value, value => this.setState({ quantity: value }));
                    }}
                    underlineColorAndroid='transparent'
                  />
                </View>
              </View>
            </View>
            {this.state.showTaxCode && this.renderTaxCodeList()}
          </KeyboardAwareScrollView>
        </View>
      </View>
    );
  }

  editProduct = () => {
    let performFunc;
    if (this.isEditing) {
      performFunc = this.onUpdateProduct;
    } else {
      performFunc = this.onAddProduct;
    }
    if (this._taxInPriceInputRef && this._taxInPriceInputRef.isFocused()) {
      this.calculaTaxExcludePrice(performFunc);
    } else {
      performFunc();
    }
  };

  renderSaveButton = () => {
    const { name, enableSaveBtn } = this.state;
    const saveBtnDisabled = isEmpty(name);
    return (
      <View style={{ width: scaleSizeW(240) }}>
        <TouchableOpacity {...testProps('al_btn_298')} disabled={saveBtnDisabled} onPress={!enableSaveBtn ? null : this.editProduct}>
          <View
            style={[styles.saveButton, { backgroundColor: saveBtnDisabled ? '#DBDBDB' : currentThemes.buttonBackgroundColor }]}
            {...testProps('al_product_saveBtn')}
          >
            <Text style={{ color: '#FFFFFF', fontSize: currentThemes.fontSize18, fontWeight: 'bold' }}>{this.isEditing ? t('SAVE') : t('ADD')}</Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };
}

export default connector(AddOrUpdateProduct);

const TITLE_CONTAINER_HEIGHT = scaleSizeH(108);
const MODAL_CONTENT_WIDTH = floor(width * 0.74);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#858585A0',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  layer: {
    justifyContent: 'flex-end',
    alignItems: 'center',
    width: '74%',
    height: '100%',
    backgroundColor: 'white',
    overflow: 'hidden',
  },
  titleBarContainer: {
    alignSelf: 'stretch',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    height: TITLE_CONTAINER_HEIGHT,
  },
  titleBarTouchableCloseIcon: {
    ...StyleSheet.flatten(SharedStyles.touchableIconContainer),
    margin: scaleSizeW(12),
  },
  titleBarTitle: {
    fontSize: currentThemes.fontSize24,
    color: '#303030',
    fontWeight: '500',
  },
  keyboradScrollContainer: {
    paddingHorizontal: scaleSizeW(32),
    width: '100%',
    flex: 1,
  },
  keyboradScrollContentContainer: {
    alignItems: 'center',
    paddingTop: scaleSizeH(20),
    flex: 1,
  },
  titleFont: {
    fontSize: currentThemes.fontSize18,
    color: '#959595',
    marginBottom: scaleSizeH(4),
  },
  contentFont: {
    fontSize: currentThemes.fontSize18,
    height: scaleSizeH(72),
    color: '#393939',
  },
  rowContainner: {
    flexDirection: 'row',
    width: '100%',
    marginTop: scaleSizeH(52),
  },
  textInputContainner: {
    height: scaleSizeH(72),
    borderWidth: scaleSizeW(1),
    borderRadius: scaleSizeW(8),
    borderColor: '#E0E0E4',
    paddingLeft: scaleSizeW(16),
  },
  inputItemContainer: {
    justifyContent: 'center',
    flex: 1,
  },
  inputItemContainerMarginRight: {
    marginRight: scaleSizeW(24),
  },
  inputItemContainerFlex2: {
    justifyContent: 'center',
    flex: 2,
    marginRight: scaleSizeW(24),
  },
  taxCodeTouchableContainer: {
    paddingRight: scaleSizeW(18),
    height: scaleSizeH(72),
    flexDirection: 'row',
    alignItems: 'center',
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',

    height: scaleSizeH(108),
  },
  taxCodeList: {
    position: 'absolute',
    left: scaleSizeW(455),
    marginTop: scaleSizeH(332),
    borderColor: currentThemes.borderBottomColor,
    borderWidth: scaleSizeW(1),
    borderRadius: scaleSizeW(8),
    backgroundColor: 'white',
    width: (MODAL_CONTENT_WIDTH - scaleSizeW(120)) / 3.0,
    height: scaleSizeH(224),
  },
  taxCodeListContentContainer: {
    paddingVertical: scaleSizeH(24),
  },
  taxCodeItem: {
    height: scaleSizeH(88),
    justifyContent: 'center',
  },
  taxCodeItemTitle: {
    marginLeft: scaleSizeW(18),
    fontWeight: '500',
    fontSize: currentThemes.fontSize26,
  },
});
