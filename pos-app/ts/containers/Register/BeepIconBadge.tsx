import React, { useEffect } from 'react';
import { bindActionCreators } from 'redux';
import { ConnectedProps, connect, useSelector } from 'react-redux';
import { TouchableOpacity, Text, StyleSheet, View } from 'react-native';
import { IconBadge } from '../../components/common';
import { testProps } from '../../utils';
import { currentThemes, IsIOS, scaleSizeW, SharedStyles } from '../../constants';
import { NetInfoSelector } from '../../components/settings/NewWifiBanner';
import { get } from 'lodash';
import IconBeepOnline from '../../components/ui/svgIcons/iconBeepOnline';
import IconBeepOffline from '../../components/ui/svgIcons/iconBeepOffline';
import { selectBeepBadgeNumber } from '../../sagas/selector';
import { refreshBadgeNumber, resetOnlineBadgeNumber } from '../../actions';
import { useNavigation } from '@react-navigation/native';

const mapStateToProps = state => ({
  beepBadgeNumber: selectBeepBadgeNumber(state),
});

const mapDispatchToProps = dispatch => ({
  actions: bindActionCreators(
    {
      refreshBadgeNumber,
      resetOnlineBadgeNumber,
    },
    dispatch
  ),
});

const BeepIconBadge: React.FC<PropsFromRedux> = props => {
  const { beepBadgeNumber = 0, actions } = props;
  const { current } = useSelector(NetInfoSelector);
  const isInternetReachable = get(current, 'isInternetReachable', false);
  const displayNumber = beepBadgeNumber > 99 ? '99' : (beepBadgeNumber?.toString() ?? '0');
  const navigation = useNavigation<any>();

  useEffect(() => {
    actions.refreshBadgeNumber();
  });

  const onResetBadgeNumber = () => {
    actions.resetOnlineBadgeNumber();
    navigation.navigate('Transactions', { showBeepNewOrder: true });
  };

  return (
    <IconBadge
      MainElement={
        <TouchableOpacity {...testProps('al_btn_567')} onPress={onResetBadgeNumber}>
          {isInternetReachable ? (
            <IconBeepOnline width={scaleSizeW(53)} height={scaleSizeW(53)} color={isInternetReachable ? '#000000' : '#8D90A3'} />
          ) : (
            <IconBeepOffline width={scaleSizeW(53)} height={scaleSizeW(53)} color={isInternetReachable ? '#000000' : '#8D90A3'} />
          )}
        </TouchableOpacity>
      }
      MainViewStyle={SharedStyles.touchableIconContainerregister}
      BadgeElement={
        <View style={styles.badgeContainer}>
          <Text style={styles.badgeTextStyle} testID='badge-number'>
            {displayNumber}
          </Text>
        </View>
      }
      IconBadgeStyle={styles.badgeIconStyle}
      Displayed={Boolean(beepBadgeNumber) && beepBadgeNumber > 0 && isInternetReachable}
    />
  );
};

const styles = StyleSheet.create({
  badgeTextStyle: {
    fontSize: IsIOS ? currentThemes.fontSize10 : currentThemes.fontSize14,
    color: 'white',
    padding: 0,
    includeFontPadding: false,
    textAlign: 'center',
    fontWeight: '700',
  },
  badgeIconStyle: {
    width: scaleSizeW(30),
    height: scaleSizeW(30),
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: currentThemes.buttonBackgroundColor,
    marginTop: scaleSizeW(5),
  },
  badgeContainer: {
    backgroundColor: '#FF9419',
    borderRadius: 10,
    padding: 0,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'white',
  },
});

const connector = connect(mapStateToProps, mapDispatchToProps);
type PropsFromRedux = ConnectedProps<typeof connector>;
export default connector(BeepIconBadge);
