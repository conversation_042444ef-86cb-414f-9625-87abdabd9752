import React, { useEffect } from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import IconGlobal from '../../components/ui/svgIcons/iconGlobal';
import { CommonColors, scaleSizeH, scaleSizeW } from '../../constants/themes';
import { useDispatch, useSelector } from 'react-redux';
import { NetInfoSelector } from '../../components/settings/NewWifiBanner';
import { get } from 'lodash';
import IconBadGlobal from '../../components/ui/svgIcons/iconBadGlobal';
import { testProps } from '../../utils';
import { selectNewProductSyncFlow, selectSyncInfo } from '../../sagas/selector';
import Animated, { Easing, useAnimatedStyle, useSharedValue, withRepeat, withTiming } from 'react-native-reanimated';
import IconItemRefresh from '../../components/ui/svgIcons/iconItemRefresh';
import { useAppNavigation } from '../../hooks/common/useAppNavigation';
import ProgressBar from '../Auth/ProgressBar';
import { syncLargeProductThumbnails } from '../../actions';

interface InternetButtonProps {
  onPress: () => void;
}

const SpinningIconItemRefresh = () => {
  const rotation = useSharedValue(0);

  useEffect(() => {
    rotation.value = withRepeat(withTiming(360, { duration: 4000, easing: Easing.linear }), -1);
  }, [rotation]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${rotation.value}deg` }],
  }));

  return (
    <Animated.View style={animatedStyle}>
      <IconItemRefresh color={CommonColors.Icon} width={36} height={36} />
    </Animated.View>
  );
};

const ProductSyncProgress: React.FC = () => {
  const syncInfoImmutable = useSelector(selectSyncInfo) as any;
  const { lastProductImagesSyncCount, lastProductImagesSyncProgress, lastProductImagesSyncFailedProgress } = syncInfoImmutable.toJS();
  const progress = lastProductImagesSyncCount == 0 ? 1 : lastProductImagesSyncProgress / lastProductImagesSyncCount;
  const navigation = useAppNavigation();

  useEffect(() => {
    if (lastProductImagesSyncCount == 0) {
      navigation.goBack();
    }
  }, [lastProductImagesSyncCount, navigation]);

  return (
    <View style={{ width: '100%', paddingTop: 8, paddingBottom: 4, alignItems: 'center' }}>
      <ProgressBar progress={progress} width={480} borderWidth={0} unfilledColor={'#E1E1E1'} color={'#FC7118'} height={8} borderRadius={4} />
      <Text
        style={{ fontSize: 20, fontWeight: '500', paddingTop: 12, paddingBottom: 24 }}
      >{`${lastProductImagesSyncProgress} / ${lastProductImagesSyncCount}`}</Text>
    </View>
  );
};

const InternetStateButton: React.FC<InternetButtonProps> = props => {
  const { onPress } = props;
  const { current } = useSelector(NetInfoSelector);

  const navigation = useAppNavigation();
  const dispatch = useDispatch();

  const syncInfoImmutable = useSelector(selectSyncInfo) as any;
  const { lastProductImagesSyncCount, lastProductImagesSyncProgress, lastProductImagesSyncFailedProgress } = syncInfoImmutable.toJS();
  const newProductSyncFlow = useSelector(selectNewProductSyncFlow);

  const isSyncingProducts = lastProductImagesSyncCount > 0;
  const isSyncingFailed = lastProductImagesSyncFailedProgress > 0;

  const isInternetReachable = get(current, 'isInternetReachable', false);

  const handlePressInternet = () => {
    if (!isInternetReachable) {
      onPress();
    }
  };

  const handlePressSync = () => {
    navigation.navigate('ModalInfoPopup', {
      title: 'Syncing Product Images',
      message: '',
      cancelText: 'CLOSE',
      // cancelText: 'Retry',
      style: {
        width: 700,
      },
      onCancelHandler: () => {
        navigation.goBack();
      },
      children: () => {
        return <ProductSyncProgress />;
      },
    });
  };

  const handlePressSyncFailed = () => {
    navigation.navigate('ModalInfoPopup', {
      title: 'Some Product Images Not Synced',
      message: `There were a total of ${lastProductImagesSyncFailedProgress} product images that failed to sync. Please click "Retry" to sync again.`,
      cancelText: 'DISMISS',
      submitText: 'RETRY',
      style: {
        width: 700,
      },
      onCancelHandler: () => {
        navigation.goBack();
      },
      onSubmitHandler: () => {
        navigation.goBack();
        dispatch(syncLargeProductThumbnails());
      },
      children: () => {
        return null;
      },
    });
  };

  if (!isInternetReachable) {
    return (
      <TouchableOpacity {...testProps('al_btn_514')} testID='internet-state-button' style={styles.innerTouchableIconContainer} onPress={handlePressInternet}>
        <IconBadGlobal testID='IconBadGlobal' color={'#FF2825'} width={25} height={25} />
      </TouchableOpacity>
    );
  }

  if (newProductSyncFlow.showIndicator && isSyncingProducts) {
    return (
      <>
        <TouchableOpacity {...testProps('al_btn_514')} testID='internet-state-button' style={styles.innerTouchableIconContainer} onPress={handlePressSync}>
          {/* <SpinningIconItemRefresh /> */}
          <IconItemRefresh color={CommonColors.Icon} width={36} height={36} />
        </TouchableOpacity>
      </>
    );
  }
  if (newProductSyncFlow.showIndicator && newProductSyncFlow.warnSyncFailed && isSyncingFailed) {
    return (
      <>
        <TouchableOpacity
          {...testProps('al_btn_514')}
          testID='internet-state-button'
          style={styles.innerTouchableIconContainer}
          onPress={handlePressSyncFailed}
        >
          <IconItemRefresh color={'#d23100'} width={36} height={36} />
        </TouchableOpacity>
      </>
    );
  }

  return (
    <TouchableOpacity {...testProps('al_btn_514')} testID='internet-state-button' style={styles.innerTouchableIconContainer} onPress={handlePressInternet}>
      <IconGlobal testID='IconGlobal' color={'#4FDB87'} width={25} height={25} />
    </TouchableOpacity>
  );
};

export default InternetStateButton;

const styles = StyleSheet.create({
  innerTouchableIconContainer: {
    width: scaleSizeW(60),
    height: scaleSizeH(60),
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    marginLeft: scaleSizeW(20),
  },
});
