import { differenceWith, filter, find, findIndex, get, isArray, isEmpty, map, noop, throttle } from 'lodash';
import moment from 'moment';
import React, { lazy, PureComponent } from 'react';
import { DeviceEventEmitter, FlatList, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { connect, ConnectedProps } from 'react-redux';

import { bindActionCreators } from 'redux';
import {
  checkLimitBeforeExecute,
  completeMergeSplitOpenOrder,
  genarateMergeOpenOrder,
  getOnlineOpenOrders,
  getOnlineOrderDetail,
  mergeToPay,
  mergeToSave,
  MRSError,
  navigateToHome,
  navigateToRegister,
  newPrintOpenOrderReceipt,
  OpenOrderUpdatedEvent,
  printOnlineOpenOrderReceipt,
  refreshMrsOpenOrders,
  SelectedOpenOrderType,
  setOnlineOpenOrderToTransaction,
  setOnlineOrderToSplitMaster,
  setOrderToSplitMaster,
  startMergeOpenOrder,
  startSplitOpenOrder,
  toggleToastInfo,
  unlockPayLaterOrder,
  updateGeneralSettings,
  updateTransactionTableIdAndPax,
} from '../../actions';
import { GeneralTextInput, NAVIGATOR_HEADER_HEIGHT, NAVIGATOR_PADDING_HORIZONTAL, NavigatorHeader, SearchHeader } from '../../components/common';
import { ModalMoreAction } from '../../components/modal';
import { ModalErrorParams } from '../../components/modal/ModalError';
import MRSOrderTip from '../../components/mrs/MRSOrderTip';
import { IconAdd, IconLeft, IconMerge, IconMore, IconSearchLabel, IconSplit, MaterialIcons } from '../../components/ui';
import { CommonColors, currentThemes, ForbiddenCheckOutError, IsIOS, scaleSizeH, scaleSizeW, STATUS_BAR_HEIGHT, t, width } from '../../constants';
import DAL from '../../dal';
import { onMRSInterceptor } from '../../sagas/mrs/checkSync';
import {
  selectBusinessName,
  selectClientConnected,
  selectEnablePayByCash,
  selectEnablePayLater,
  selectEnableTableLayout,
  selectInMergeOpenOrder,
  selectInMoveOpenOrder,
  selectInSplitOpenOrder,
  selectIPAddress,
  selectIsEnabledMRS,
  selectIsMaster,
  selectLastZReadingCloseTime,
  selectOperationHours,
  selectShouldLoadOnlineOpenOrders,
  selectStoreId,
  selectTableLayoutEnabled,
} from '../../sagas/selector';
import { ScreenProps, TransactionType } from '../../typings';
import { getUnNullValue, isValidNumber, localeNumber, testProps } from '../../utils';
import { getAlreadyCloseZReading, getCurOperationDayTime, getHoursAndMinutesTimeSpage } from '../../utils/datetime';
import eventBus from '../../utils/eventBus';
import { OrderOperationEnum } from '../../utils/logComponent';
import { getParam } from '../../utils/navigation';
import { TableSelectedOrdersMap } from '../TableLayout/TableLayout';
import { DisplayOpenOrderType, OpenOrderEvent, OrderItemHolder } from './OpenOrderItem';

const OrderItem = lazy(() => import('./OpenOrderItem'));

type SearchResultType = {
  table: DisplayOpenOrderType[];
  orders: DisplayOpenOrderType[];
  notes: DisplayOpenOrderType[];
};

interface Props extends ScreenProps, PropsFromRedux {}

interface State {
  onlineOpenOrderList?: DisplayOpenOrderType[];
  offlineOpenOrderList?: DisplayOpenOrderType[];
  searching: boolean;
  keyboardShows: boolean;
  searchResult?: SearchResultType;
  hintText?: string;
}

const mapStateToProps = state => ({
  businessName: selectBusinessName(state),
  storeId: selectStoreId(state),
  enablePayByCash: selectEnablePayByCash(state),
  enablePayLater: selectEnablePayLater(state),
  shouldLoadOnlineOpenOrders: selectShouldLoadOnlineOpenOrders(state),
  enableTableLayout: selectEnableTableLayout(state),
  tableLayoutEnabled: selectTableLayoutEnabled(state),
  inMerge: selectInMergeOpenOrder(state),
  inSplit: selectInSplitOpenOrder(state),
  inMove: selectInMoveOpenOrder(state),
  isEnabledMRS: selectIsEnabledMRS(state),
  isMaster: selectIsMaster(state),
  ipAddress: selectIPAddress(state),
  isClientConnected: selectClientConnected(state),
  lastZReadingCloseTime: selectLastZReadingCloseTime(state),
  operationHours: selectOperationHours(state),
});

const mapDispatchToProps = dispatch => ({
  actions: bindActionCreators(
    {
      newPrintOpenOrderReceipt,
      printOnlineOpenOrderReceipt,
      toggleToastInfo,
      getOnlineOrderDetail,
      setOnlineOpenOrderToTransaction,
      getOnlineOpenOrders,
      navigateToRegister,
      updateTransactionTableIdAndPax,
      unlockPayLaterOrder,
      updateGeneralSettings,
      mergeToSave,
      mergeToPay,
      genarateMergeOpenOrder,
      startMergeOpenOrder,
      startSplitOpenOrder,
      completeMergeSplitOpenOrder,
      setOrderToSplitMaster,
      setOnlineOrderToSplitMaster,
      navigateToHome,
      checkLimitBeforeExecute,
      refreshMrsOpenOrders,
    },
    dispatch
  ),
});

const connector = connect(mapStateToProps, mapDispatchToProps);
type PropsFromRedux = ConnectedProps<typeof connector>;

export const shouldUpdateOnlineOpenOrders = (fetched: DisplayOpenOrderType[], cached: DisplayOpenOrderType[]) => {
  const fLength = get(fetched, 'length', 0);
  const cLength = get(cached, 'length', 0);

  return (
    fLength !== cLength ||
    differenceWith(fetched, cached, (next, prev) => {
      return next.transactionId === prev.transactionId && next.modifiedTime === prev.modifiedTime;
    }).length > 0
  );
};

class OpenOrdersList extends PureComponent<Props, State> {
  static navigationOptions = () => ({
    headerShown: false,
  });

  private _searchTextInputRef: GeneralTextInput;
  private searchString = '';
  private _tableId: string;
  private _autoRefreshTimer: ReturnType<typeof setInterval>;
  private subscribeToRefresh: any;
  private viewDidAppear: any;
  private viewDidDisAppear: any;
  private selectedOpenOrderIds = [];
  private _selectedOpenOrdersMap: TableSelectedOrdersMap;
  private _mergedTransaction;
  private _headerRef: NavigatorHeader;
  private _menu: ModalMoreAction = null;
  private updateOpenOrderListListener;

  constructor(props: Props) {
    super(props);
    this._tableId = getParam(props, 'tableId');
    this._selectedOpenOrdersMap = getParam(props, 'selectedOpenOrdersMap', {});
    if (Object.keys(this._selectedOpenOrdersMap).length > 0) {
      for (const selectedOpenOrders of Object.values(this._selectedOpenOrdersMap)) {
        this.selectedOpenOrderIds = this.selectedOpenOrderIds.concat(map(selectedOpenOrders, v => v.id));
      }
    }
    const { inMerge, inMove, inSplit } = this.props;
    let hintText;
    if (inMerge) {
      hintText = t('Select minimum 2 tables');
    } else if (inSplit) {
      hintText = t('Select to split');
    } else if (inMove) {
      hintText = t('Select minimum 1 order to move');
    }
    this.state = {
      onlineOpenOrderList: [],
      offlineOpenOrderList: [],
      searching: false,
      keyboardShows: false,
      hintText,
    };
    this.viewDidAppear = this.props.navigation.addListener('focus', this.onFocus);
    this.viewDidDisAppear = this.props.navigation.addListener('blur', this.onBlur);
  }

  componentDidMount() {
    const { inMerge, inMove } = this.props;
    if ((inMerge || inMove) && Boolean(this._tableId)) {
      this.updateEditOpenOrderUI();
    }
  }

  registerUpdateLocalListListener = () => {
    this.updateOpenOrderListListener = DeviceEventEmitter.addListener(OpenOrderUpdatedEvent, this.onLearnLocalOpenOrder);
  };

  unRegisterUpdateLocalListListener = () => {
    this.updateOpenOrderListListener && this.updateOpenOrderListListener.remove();
  };

  onLearnLocalOpenOrder = async () => {
    const { inMerge, inSplit, inMove } = this.props;
    const isEditingOpenOrder = inMerge || inSplit || inMove;
    if (isEmpty(this.searchString) && !isEditingOpenOrder) {
      await this.getLocalOpenOrderList();
    }
  };

  onFocus = () => {
    this.triggerSearch();
    const { shouldLoadOnlineOpenOrders } = this.props;
    if (shouldLoadOnlineOpenOrders) {
      // The page will be refreshed every 30 seconds
      this._autoRefreshTimer = setInterval(this.triggerRefreshOnlineOpenOrderList, 60 * 1000);
      // listen to refresh online open order notification
      this.subscribeToRefresh = eventBus.addListener('RefreshOnlineOpenOrder', this.triggerRefreshOnlineOpenOrderList);
    }
    this.registerUpdateLocalListListener();
  };

  removeBeepListener = () => {
    this._autoRefreshTimer && clearInterval(this._autoRefreshTimer);
    this._autoRefreshTimer = null;
    this.subscribeToRefresh && eventBus.remove(this.subscribeToRefresh);
    this.subscribeToRefresh = null;
    this.triggerRefreshOnlineOpenOrderList && this.triggerRefreshOnlineOpenOrderList.cancel();
  };

  onBlur = () => {
    this.removeBeepListener();
    this.unRegisterUpdateLocalListListener();
  };

  componentWillUnmount() {
    this.onBlur();
    this.viewDidAppear && this.viewDidAppear();
    this.viewDidDisAppear && this.viewDidDisAppear();
    this.triggerSearch && this.triggerSearch.cancel();
  }

  generateLocalOpenOrderItem = openOrder => {
    const now = moment();
    const tableId = get(openOrder, 'tableId');
    const total = localeNumber(get(openOrder, 'total'));
    const createTime = get(openOrder, 'createdDate');
    const timeSpan = getHoursAndMinutesTimeSpage(createTime, now);
    const transactionId = get(openOrder, 'transactionId');
    return { tableId, total, timeSpan, transactionId, isOnlineTransaction: false, isPayLater: false };
  };

  getOnlineOpenOrdersAsync = (): Promise<{ needUpdate: boolean; state?: any }> => {
    return new Promise(resolve => {
      const {
        businessName,
        storeId,
        shouldLoadOnlineOpenOrders,
        enablePayByCash,
        enablePayLater,
        actions: { updateGeneralSettings },
      } = this.props;
      if (!shouldLoadOnlineOpenOrders) return resolve({ needUpdate: false });
      const onSuccess = {
        callback: payload => {
          const onlineOpenOrders = get(payload, ['res', 'onlineOpenOrders'], []);
          // If the merchant has not opened enablePayByCash and enablePayLater and there is currently no unpaid onlineOpenOrder, then there is no need to keep requesting
          if (!enablePayByCash && !enablePayLater && isEmpty(this._tableId) && isEmpty(this.searchString) && onlineOpenOrders.length === 0) {
            updateGeneralSettings({ shouldLoadOnlineOpenOrders: false });
            this.removeBeepListener();
          }
          const isRealSearching = !isEmpty(this.searchString);
          const onlineOpenOrderList = [];
          const table = [];
          const orders = [];
          const now = moment();
          onlineOpenOrders.map(item => {
            const { tableId, pickUpId, total, createdTime, transactionId, receiptNumber, payments, isPayLater = false, modifiedTime } = item;
            const timeSpan = getHoursAndMinutesTimeSpage(createdTime, now);
            let totalAmount = isValidNumber(total) ? Number(total) : 0;
            let voucherAmount = 0;
            if (Boolean(payments) && isArray(payments) && payments.length > 0) {
              const voucherPayment = find(payments, item => item.paymentMethod == 'Voucher');
              if (Boolean(voucherPayment)) {
                voucherAmount = isValidNumber(voucherPayment.amount) ? Number(voucherPayment.amount) : 0;
                totalAmount = totalAmount - voucherAmount;
              }
            }
            const onlineOpenOrderItem = {
              tableId,
              pickUpId,
              total: localeNumber(totalAmount),
              timeSpan,
              modifiedTime,
              transactionId,
              receiptNumber,
              isPayLater,
              isOnlineTransaction: true,
            };
            if (isRealSearching) {
              if (findIndex(tableId, this.searchString) < 0) {
                orders.push(onlineOpenOrderItem);
              } else {
                table.push(onlineOpenOrderItem);
              }
            } else {
              if (Boolean(this._tableId)) {
                tableId === this._tableId && onlineOpenOrderList.push(onlineOpenOrderItem);
              } else {
                onlineOpenOrderList.push(onlineOpenOrderItem);
              }
            }
          });
          if (isRealSearching) {
            return resolve({
              state: {
                searchResult: {
                  table,
                  orders,
                  notes: getUnNullValue(this.state, 'searchResult.notes', []),
                },
              },
              needUpdate: true,
            });
          } else {
            const hasDiff = shouldUpdateOnlineOpenOrders(onlineOpenOrderList, this.state.onlineOpenOrderList);
            if (hasDiff) {
              return resolve({
                state: {
                  onlineOpenOrderList: onlineOpenOrderList,
                  searchResult: null,
                },
                needUpdate: true,
              });
            } else {
              return resolve({
                state: {
                  searchResult: null,
                },
                needUpdate: true,
              });
            }
          }
        },
      };

      const onFailure = {
        callback: () => {
          if (!enablePayByCash && !enablePayLater && isEmpty(this._tableId) && isEmpty(this.searchString)) {
            updateGeneralSettings({ shouldLoadOnlineOpenOrders: false });
            this.removeBeepListener();
          }
          this.props.actions.toggleToastInfo({
            visible: true,
            text: t('Search QR Order fail'),
          });
          resolve({
            needUpdate: false,
          });
        },
      };
      this.props.actions.getOnlineOpenOrders({
        businessName,
        storeId,
        includePayLater: true,
        includePayByCash: true,
        filter: this._tableId || this.searchString,
        onSuccess,
        onFailure,
      });
    });
  };

  getOnlineOpenOrderList = async () => {
    const result = await this.getOnlineOpenOrdersAsync();
    if (result.needUpdate && result.state) {
      if (isEmpty(this.searchString)) {
        this.setState(result.state);
      } else {
        this.setState(prevState => {
          const latestOnlineOrders: SearchResultType = result.state.searchResult || { table: [], orders: [] };
          const notes = getUnNullValue(prevState, 'searchResult.notes', []);
          return {
            searchResult: {
              ...latestOnlineOrders,
              notes,
            },
          };
        });
      }
    }
  };

  getLocalOpenOrdersAsync = (): Promise<DisplayOpenOrderType[]> => {
    return new Promise(res => {
      let openOrders;
      if (Boolean(this._tableId)) {
        openOrders = DAL.getOpenOrderByTableId(this._tableId);
      } else {
        openOrders = DAL.searchOpenOrderByTableIdKeyword(this.searchString, true);
      }
      const localOpenOrders: DisplayOpenOrderType[] = this.localOpenOrder2DisplayType(openOrders);
      return res(localOpenOrders);
    });
  };

  getLocalOpenOrderList = async () => {
    const localOpenOrders = await this.getLocalOpenOrdersAsync();
    if (isEmpty(this.searchString)) {
      this.setState({ offlineOpenOrderList: localOpenOrders, searchResult: null });
    } else {
      this.setState(prev => {
        const table = get(prev, 'searchResult.table', []);
        const orders = get(prev, 'searchResult.orders', []);
        return { searchResult: { table, orders, notes: localOpenOrders } };
      });
    }
  };

  localOpenOrder2DisplayType = (openOrders: TransactionType[]) => {
    if (isEmpty(openOrders)) {
      return [];
    }
    const localOpenOrders: DisplayOpenOrderType[] = [];
    const now = moment();
    openOrders.forEach(item => {
      const tableId = get(item, 'tableId');
      const total = localeNumber(get(item, 'total'));
      const createTime = get(item, 'createdDate');
      const timeSpan = getHoursAndMinutesTimeSpage(createTime, now);
      const transactionId = get(item, 'transactionId');
      const mrs = Boolean(get(item, 'mrs'));
      localOpenOrders.push({ tableId, total, timeSpan, transactionId, isOnlineTransaction: false, isPayLater: false, mrs });
    });
    return localOpenOrders;
  };

  _keyExtractor = (item, index) => get(item, 'transactionId') + index;

  updateEditOpenOrderUI = () => {
    const { inMerge } = this.props;
    const selectedOpenOrderCount = this.selectedOpenOrderIds.length;
    if (inMerge) {
      if (selectedOpenOrderCount > 1) {
        this._headerRef && this._headerRef.showRightButtons();
      } else {
        this._headerRef && this._headerRef.hideRightButtons();
      }
      if (selectedOpenOrderCount === 0) {
        this.setState({
          hintText: t('Select minimum 2 tables'),
        });
      } else if (selectedOpenOrderCount === 1) {
        this.setState({
          hintText: t('1 order selected'),
        });
      } else {
        this.setState({
          hintText: t('multiple orders selected', { count: selectedOpenOrderCount }),
        });
      }
    } else {
      if (selectedOpenOrderCount > 0) {
        this._headerRef && this._headerRef.showRightButtons();
      } else {
        this._headerRef && this._headerRef.hideRightButtons();
      }
    }
  };

  onToggleSelect = (orderId: string, isSelected: boolean, isPayLater: boolean, modifiedTime?: string) => {
    // local is transactionId, payLater is receiptNumber
    if (isSelected) {
      this.selectedOpenOrderIds.push(orderId);
      if (Boolean(this._tableId)) {
        const newItem: SelectedOpenOrderType = { id: orderId, isPayLater, modifiedTime, srcTableId: this._tableId };
        if (Boolean(this._selectedOpenOrdersMap[this._tableId])) {
          // save selected open order, then we can get the data in tableLayout and select table.
          // screens connected through navigation params
          this._selectedOpenOrdersMap[this._tableId].push(newItem);
        } else {
          this._selectedOpenOrdersMap[this._tableId] = [newItem];
        }
      }
    } else {
      this.selectedOpenOrderIds = filter(this.selectedOpenOrderIds, id => id !== orderId);
      if (Boolean(this._tableId)) {
        this._selectedOpenOrdersMap[this._tableId] = filter(this._selectedOpenOrdersMap[this._tableId], order => order.id !== orderId);
      }
    }
    this.updateEditOpenOrderUI();
  };

  renderItems = ({ item, index }) => {
    const { inMerge, inSplit, inMove, isEnabledMRS } = this.props;
    const { searchResult } = this.state;
    let selected = false;
    if (inMerge && Boolean(this._tableId)) {
      selected = findIndex(this.selectedOpenOrderIds, id => id === item.transactionId) > -1;
    }
    const style = index < 4 && { marginTop: Boolean(searchResult) || inMerge || inSplit || inMove ? scaleSizeH(8) : scaleSizeH(32) };
    return (
      <React.Suspense fallback={<OrderItemHolder style={style} />}>
        <OrderItem
          style={style}
          orderContent={item}
          onOpenOrderPress={this.onOpenOrderPress}
          printOpenOrderReceipt={this.printOpenOrderReceipt}
          position={index}
          inMerge={inMerge}
          inSplit={inSplit}
          inMove={inMove}
          selected={selected}
          onToggleSelect={this.onToggleSelect}
          isEnabledMRS={isEnabledMRS}
        />
      </React.Suspense>
    );
  };

  renderSearchResult = () => {
    const {
      searchResult: { table, orders, notes },
    } = this.state;
    return (
      <ScrollView>
        {table.length > 0 && <Text style={styles.sectionText}>Table</Text>}
        <FlatList numColumns={4} style={styles.flatStyle} keyExtractor={this._keyExtractor} data={table} renderItem={this.renderItems} extraData={this.state} />
        {orders.length > 0 && <Text style={styles.sectionText}>Orders</Text>}
        <FlatList
          numColumns={4}
          style={styles.flatStyle}
          keyExtractor={this._keyExtractor}
          data={orders}
          renderItem={this.renderItems}
          extraData={this.state}
        />
        {notes.length > 0 && <Text style={styles.sectionText}>Notes</Text>}
        <FlatList numColumns={4} style={styles.flatStyle} keyExtractor={this._keyExtractor} data={notes} renderItem={this.renderItems} extraData={this.state} />
      </ScrollView>
    );
  };

  renderOpenOrders = () => {
    const { inMerge, inSplit, inMove } = this.props;
    const { onlineOpenOrderList = [], offlineOpenOrderList = [] } = this.state;
    const openOrderList = inMerge || inSplit || inMove ? offlineOpenOrderList.concat(onlineOpenOrderList) : onlineOpenOrderList.concat(offlineOpenOrderList);
    return (
      <FlatList
        numColumns={4}
        style={styles.flatStyle}
        keyExtractor={this._keyExtractor}
        data={openOrderList}
        renderItem={this.renderItems}
        extraData={this.state}
      />
    );
  };

  setMenuRef = refs => {
    this._menu = refs;
  };

  hideMenu = () => {
    this._menu && this._menu.dismiss();
  };

  showMenu = () => {
    this._menu && this._menu.show();
  };

  onStartMergeOpenOrder = () => {
    this.props.actions.startMergeOpenOrder();
    this.selectedOpenOrderIds = [];
    this.hideMenu();
    this._headerRef && this._headerRef.hideRightButtons();
    this.setState({ hintText: t('Select minimum 2 tables') });
  };

  onStartSplitOpenOrder = () => {
    this.props.actions.startSplitOpenOrder();
    this.setState({ hintText: t('Select to split') });
    this.hideMenu();
  };
  renderMRSOrderTip = () => {
    const { isEnabledMRS } = this.props;
    if (!isEnabledMRS) {
      return null;
    }
    const { offlineOpenOrderList = [] } = this.state;
    const hasPreviousOrders = find(offlineOpenOrderList, v => !v.isPayLater && !v.isOnlineTransaction && !v.mrs);
    if (!hasPreviousOrders) {
      return null;
    }
    return <MRSOrderTip />;
  };

  render() {
    const { enableTableLayout, tableLayoutEnabled, inMerge, inSplit, inMove } = this.props;
    const { searchResult, hintText } = this.state;
    const buttons: any = [];
    if (!(enableTableLayout && tableLayoutEnabled)) {
      buttons.push({
        icon: <IconMerge width={scaleSizeW(41)} height={scaleSizeH(40)} color={CommonColors.Icon} />,
        name: t('Merge'),
        onClick: this.onStartMergeOpenOrder,
      });
      buttons.push({
        icon: <IconSplit width={scaleSizeW(41)} height={scaleSizeH(40)} color={CommonColors.Icon} />,
        name: t('Split'),
        onClick: this.onStartSplitOpenOrder,
      });
    }
    return (
      <View style={{ backgroundColor: '#E1F6F9', flex: 1 }}>
        {this.renderHeader()}
        {this.renderMRSOrderTip()}

        {buttons.length > 0 && <ModalMoreAction ref={this.setMenuRef} list={buttons} />}
        {(inMerge || inSplit || inMove) && <Text style={styles.mergeSplitHint}>{hintText}</Text>}
        {Boolean(searchResult) ? this.renderSearchResult() : this.renderOpenOrders()}
      </View>
    );
  }

  renderHeader = () => {
    return this.state.searching ? this.renderSearchHeader() : this.renderCommonHeader();
  };

  onSearchClick = () => {
    this.setState({ searching: true }, () => {
      // remove listener
      this.onBlur();
    });
    this._searchTextInputRef && this._searchTextInputRef.focus();
  };

  goToNewOpenOrder = () => {
    if (isEmpty(this._tableId)) {
      // TODO: need save the current transaction?
      this.goBack();
    } else {
      this.props.navigation.navigate('ModalPax', {
        shouldGoBackWhenConfirm: false,
        onSubmitHandler: pax => {
          this.props.actions.updateTransactionTableIdAndPax({ tableId: this._tableId, pax: Boolean(pax) ? pax : 0 });
        },
        title: t('Assign Seat'),
      });
    }
  };

  onMergeToPay = ({ mergedTransaction, warning }) => {
    const {
      navigation,
      actions: { mergeToPay },
    } = this.props;
    if (Boolean(warning)) {
      const { title, message } = warning;
      navigation.navigate('ModalInfo', {
        title,
        isShowTitle: true,
        isCancelButtonRight: true,
        needGoBackWhenSubmit: false,
        textAlign: 'center',
        info: message,
        okText: t('OK'),
        onCancelHandler: () => null,
        onSubmitHandler: () => {
          mergeToPay({
            mergedTransaction,
          });
          navigation.replace('Checkout', { needClearTransactionWhenBack: true });
        },
      });
    } else {
      mergeToPay({
        mergedTransaction,
      });
      navigation.navigate('Checkout', { needClearTransactionWhenBack: true });
    }
  };
  onCompleteMergeSplitOpenOrder = () => {
    const {
      actions: { completeMergeSplitOpenOrder },
    } = this.props;
    this.selectedOpenOrderIds = [];
    this._selectedOpenOrdersMap = {};
    this._mergedTransaction = undefined;
    completeMergeSplitOpenOrder();
  };

  // mergeToPay: merge the order and setTransaction to redux to display, and go to CheckOut page
  // once paid success, save the new order and delete the original Open orders
  onClickMergeToPay = () => {
    const {
      actions: { genarateMergeOpenOrder },
    } = this.props;

    const onResult = async result => {
      const { mergedTransaction, mergeConflict } = result;
      if (mergeConflict) {
        const params: ModalErrorParams = {
          titleIcon: 'warn',
          title: t('Cannot MERGE & PAY'),
          subTitle: t('Detected different salesperson'),
          okText: t('OK'),
          onOk: noop,
          style: { width: IsIOS ? scaleSizeW(860) : scaleSizeW(730) },
          closeable: true,
        };
        this.props.navigation.navigate('ModalError', params);
        return;
      }
      const checkResult = await this.checkCanProposer(OrderOperationEnum.MergeAndCheckOut, mergedTransaction);
      if (checkResult) {
        this.onMergeToPay(result);
      } else {
        this.getLocalOpenOrderList();
        this.goBack();
      }
    };
    genarateMergeOpenOrder({ selectedOpenOrderIds: this.selectedOpenOrderIds, operation: 'mergeAndPay', onResult });
  };

  seveMergedOpenOrder = mergedTransaction => {
    const {
      actions: { mergeToSave },
    } = this.props;
    mergeToSave({
      mergedTransaction,
      onComplete: () => {
        this.getLocalOpenOrderList();
      },
    });
  };

  onInputSubmitId = (mergedTransaction, withSameTableId, needReplacePage = false) => {
    const {
      navigation,
      actions: { navigateToHome },
    } = this.props;
    const { replace, navigate } = navigation;
    if (this._tableId) {
      this._mergedTransaction = mergedTransaction;
      if (withSameTableId) {
        const onMergeSuccess = () => {
          navigateToHome({ navigation });
        };
        this.onSelectTargetTable(mergedTransaction.tableId, onMergeSuccess);
      } else {
        this.goToSelectTargetTable();
      }
    } else {
      const { tableId } = mergedTransaction;
      if (withSameTableId) {
        this.seveMergedOpenOrder({ ...mergedTransaction });
      } else {
        const navigateFunc = needReplacePage ? replace : navigate;
        navigateFunc('ModalPureTableId', {
          tableId,
          onSubmitHandler: newTableId => {
            this.seveMergedOpenOrder({ ...mergedTransaction, tableId: newTableId });
          },
        });
      }
    }
  };

  onMergeToSave = ({ mergedTransaction, warning, withSameTableId }) => {
    const { navigation } = this.props;
    if (Boolean(warning)) {
      const { title, message } = warning;
      navigation.navigate('ModalInfo', {
        title,
        isShowTitle: true,
        isCancelButtonRight: true,
        needGoBackWhenSubmit: false,
        textAlign: 'center',
        info: message,
        onCancelHandler: () => null,
        okText: t('YES PROCEED'),
        onSubmitHandler: () => {
          this.onInputSubmitId(mergedTransaction, withSameTableId, true);
        },
      });
    } else {
      this.onInputSubmitId(mergedTransaction, withSameTableId, false);
    }
  };

  // mergeToSave: merge the order and save the merged OpenOrder to local DB， and delete the original Open orders
  onClickMergeToSave = () => {
    const {
      actions: { genarateMergeOpenOrder },
    } = this.props;
    const onResult = async result => {
      const { mergedTransaction } = result;
      const checkResult = await this.checkCanProposer(OrderOperationEnum.MergeAndSave, mergedTransaction);

      if (checkResult) {
        this.onMergeToSave(result);
      } else {
        this.getLocalOpenOrderList();
        this.goBack();
      }
    };
    genarateMergeOpenOrder({ selectedOpenOrderIds: this.selectedOpenOrderIds, operation: 'mergeAndSave', onResult });
  };

  checkCanProposer = (orderOperation: OrderOperationEnum, transaction): Promise<boolean> => {
    return new Promise(res => {
      const onComplete = (error: MRSError) => {
        return res(onMRSInterceptor(error));
      };

      this.props.actions.checkLimitBeforeExecute({
        transaction,
        orderOperation,
        onComplete,
      });
    });
  };

  onSelectTargetTable = (tableId, onMergeSuccess) => {
    const onSelectTargetTable = getParam(this.props, 'onSelectTargetTable');
    onSelectTargetTable(this._selectedOpenOrdersMap, tableId, this._mergedTransaction, onMergeSuccess);
  };

  goToSelectTargetTable = () => {
    this.props.navigation.navigate('SelectTable', { onSelectTargetTable: this.onSelectTargetTable, selectedOpenOrdersMap: this._selectedOpenOrdersMap });
  };

  onManualRefresh = () => {
    this.triggerSearch(true);
  };

  renderCommonHeader = () => {
    const { enableTableLayout, tableLayoutEnabled, inMerge, inSplit, inMove, isEnabledMRS, isMaster, isClientConnected, ipAddress } = this.props;
    const isClient = isEnabledMRS && !isMaster;
    const isMRSOffline = isEnabledMRS && (!isClientConnected || !ipAddress);
    const leftIcons = [
      { icon: <IconLeft color={'#FC7118'} width={scaleSizeW(48)} height={scaleSizeH(49)} {...testProps('al_backIcon')} />, onClick: this.goBack },
    ];
    const rightIcons: any = [];
    const rightButtons: any = [];
    const isEditingOpenOrder = inMerge || inSplit || inMove;
    if (inMerge) {
      rightButtons.push(
        <TouchableOpacity
          {...testProps('al_btn_493')}
          key={'MERGE&SAVE'}
          style={[styles.rightButtonContainer, isMRSOffline && { backgroundColor: '#DBDBDB' }]}
          onPress={this.onClickMergeToSave}
        >
          <Text style={styles.rightButtonText}>MERGE & SAVE</Text>
        </TouchableOpacity>
      );
      rightButtons.push(
        <TouchableOpacity
          {...testProps('al_btn_458')}
          key={'MERGE&PAY'}
          style={[styles.rightButtonContainer, (isClient || isMRSOffline) && { backgroundColor: '#DBDBDB' }]}
          onPress={this.onClickMergeToPay}
        >
          <Text style={styles.rightButtonText}>MERGE & PAY</Text>
        </TouchableOpacity>
      );
    } else if (inMove) {
      rightButtons.push(
        <TouchableOpacity {...testProps('al_btn_632')} key={'MOVE ORDER'} style={styles.moveOrderButtonContainer} onPress={this.goToSelectTargetTable}>
          <Text style={styles.moveOrderButtonText}>{t('MOVE ORDER')}</Text>
        </TouchableOpacity>
      );
    }
    if (!isEditingOpenOrder) {
      if (isEmpty(this._tableId)) {
        rightIcons.push({
          icon: <IconSearchLabel width={scaleSizeW(38)} height={scaleSizeW(38)} {...testProps('al_searchIcon')} />,
          onClick: this.onSearchClick,
          containerStyle: { marginRight: scaleSizeW(40) },
        });
      }
      rightIcons.push({
        icon: <IconAdd width={scaleSizeW(42)} height={scaleSizeW(42)} {...testProps('al_addIcon')} />,
        onClick: this.goToNewOpenOrder,
      });
      if (!(enableTableLayout && tableLayoutEnabled) && this._menu) {
        rightIcons.push({
          icon: <IconMore color={CommonColors.Icon} />,
          onClick: this.showMenu,
          containerStyle: { marginLeft: scaleSizeW(40) },
        });
      }
    }
    let title = t('Open Order');
    if (Boolean(this._tableId) && isEditingOpenOrder) {
      title = t('Select Order');
    } else if (inMerge) {
      title = t('Merge Order');
    } else if (inSplit) {
      title = t('Split Order');
    }
    return (
      <NavigatorHeader
        ref={this.headerRefHandler}
        title={title}
        leftIcons={leftIcons}
        rightIcons={rightIcons}
        onRefreshClick={isEditingOpenOrder ? null : this.onManualRefresh}
        rightButtons={rightButtons}
      />
    );
  };

  onCloseSearch = () => {
    this.setState({ searching: false }, () => {
      // re-register listener
      this.onFocus();
    });
    this.onClearValueHandler();
  };

  renderSearchHeader = () => {
    return (
      <SearchHeader
        headerStyle={styles.header}
        onPressBack={this.onCloseSearch}
        searchRefHandler={this.searchRefHandler}
        placeholder={t('Search open orders')}
        searchHandler={this.searchHandler}
        onChangeTextHandler={this.onChangeTextHandler}
        onClearValueHandler={this.onClearValueHandler}
        clearButton={this.renderClearButton}
      />
    );
  };

  onOpenOrderSplit(detail: OpenOrderEvent) {
    const {
      navigation,
      actions: { setOnlineOrderToSplitMaster, setOrderToSplitMaster },
      inSplit,
    } = this.props;
    const { receiptNumber, transactionId, isPayLater, tableId } = detail;

    if (inSplit) {
      if (isPayLater) {
        requestAnimationFrame(() => {
          this.props.navigation.navigate('ModalInfo', {
            title: t('Proceed to split order?'),
            isShowTitle: true,
            needGoBackWhenCancel: true,
            needGoBackWhenSubmit: false,
            textAlign: 'center',
            info: (
              <>
                {t('split pay later order1')}
                <Text style={styles.editOrderWarn2}>{t('removed')}</Text>
                {t('split pay later order2')}
              </>
            ),
            okText: t('SPLIT ORDER'),
            cancelText: t('CANCEL'),
            onSubmitHandler: () => setOnlineOrderToSplitMaster({ receiptNumber, transactionId, tableId: this._tableId || tableId }),
            onCancelHandler: noop,
          });
        });
      } else {
        const onComplete = result => {
          if (result) {
            navigation.navigate('SplitOrder');
          } else {
            this.getLocalOpenOrderList();
            this.goBack();
          }
        };
        setOrderToSplitMaster({ transactionId, onComplete });
      }
    }
  }

  onOpenOrderPress = (detail: OpenOrderEvent) => {
    const {
      navigation,
      actions: { navigateToRegister },
      inSplit,
    } = this.props;
    const { receiptNumber, transactionId, isPayLater } = detail;
    if (inSplit) {
      this.onOpenOrderSplit(detail);
    } else if (!Boolean(receiptNumber)) {
      const onOpenOrderPressFunc = getParam(this.props, 'onOpenOrderPress', () => {
        // NOP
      });
      const shouldGoBackWhenPrssItem = getParam(this.props, 'shouldGoBackWhenPrssItem', true);
      shouldGoBackWhenPrssItem ? navigation.goBack() : navigateToRegister({ navigation });
      onOpenOrderPressFunc(transactionId);
    } else {
      if (isPayLater) {
        requestAnimationFrame(() => {
          this.props.navigation.navigate('ModalInfo', {
            title: t('Edit Order or Proceed to Checkout'),
            isShowTitle: true,
            needGoBackWhenCancel: false,
            textAlign: 'center',
            needGoBackWhenSubmit: false,
            info: (
              <>
                {t('unlock pay later order1')}
                <Text style={styles.editOrderWarn1}>{t('removed')}</Text>
                {t('unlock pay later order2')}
                <Text style={styles.editOrderWarn2}>{t('cannot be undone')}</Text>
              </>
            ),
            okText: t('Checkout'),
            cancelText: t('Edit Order'),
            onSubmitHandler: () => this.getOrderDetailAndGoCheckout(receiptNumber, true),
            // eslint-disable-next-line @typescript-eslint/no-empty-function
            onCancelHandler: () => this.onEditOrder(receiptNumber),
          });
        });
      } else {
        this.getOrderDetailAndGoCheckout(receiptNumber, false);
      }
    }
  };

  getOrderDetailAndGoCheckout = (receiptNumber, isPayLater) => {
    const {
      navigation: { replace, navigate },
      isEnabledMRS,
      isMaster,
      lastZReadingCloseTime,
      operationHours,
      actions: { setOnlineOpenOrderToTransaction },
    } = this.props;
    const alreadyCloseZReading = getAlreadyCloseZReading(lastZReadingCloseTime, operationHours);
    if (isEnabledMRS && !isMaster) {
      return new Promise(res => {
        res(true);
      }).then(() => {
        onMRSInterceptor(ForbiddenCheckOutError);
      });
    }
    const jumpFunc = isPayLater ? replace : navigate;
    if (alreadyCloseZReading) {
      const { curOperationEndTime } = getCurOperationDayTime(operationHours);
      const displayTime = curOperationEndTime.format('DD/M/YYYY hh:mm:ss A');
      jumpFunc('ModalInfo', {
        textAlign: 'center',
        info: `Z-Reading already closed. This register will not be able to create any transaction prior to ${displayTime}`,
        okText: t('YES PROCEED'),
        onCancelHandler: () => null,
        onSubmitHandler: noop,
      });
    } else {
      this.getOnlineOrderDetail(receiptNumber).then((onlineTransaction: any) => {
        setOnlineOpenOrderToTransaction({ onlineTransaction });
        jumpFunc('Checkout', { onRefreshOpenOrderList: this.triggerSearch, needClearTransactionWhenBack: true });
      });
    }
  };

  onEditOrder = receiptNumber => {
    const {
      navigation,
      actions: { navigateToRegister, unlockPayLaterOrder },
    } = this.props;
    const onSuccess = {
      callback: () => {
        // setOnlineOpenOrderToTransaction();
        navigateToRegister({ navigation });
      },
    };

    unlockPayLaterOrder({ receiptNumber, onSuccess });
  };

  printOpenOrderReceipt = (transactionId, receiptNumber) => {
    if (!Boolean(receiptNumber)) {
      this.props.actions.newPrintOpenOrderReceipt({ transactionId });
    } else {
      this.getOnlineOrderDetail(receiptNumber).then(onlineTransaction => {
        this.props.actions.printOnlineOpenOrderReceipt({ onlineTransaction });
      });
    }
  };

  getOnlineOrderDetail = orderId => {
    return new Promise((resolve, reject) => {
      const getOrderSuccess = {
        callback: payload => {
          const { onlineOrder } = payload.res;
          if (Boolean(onlineOrder)) {
            resolve(onlineOrder);
          } else {
            reject();
          }
        },
      };
      this.props.actions.getOnlineOrderDetail({ orderId, onSuccess: getOrderSuccess });
    });
  };

  renderClearButton = () => {
    return (
      <View style={{ paddingRight: 8 }}>
        <MaterialIcons name='close' size={scaleSizeW(24)} color={currentThemes.inActiveButtonBackgroundColor} />
      </View>
    );
  };

  searchRefHandler = ref => {
    this._searchTextInputRef = ref;
  };

  headerRefHandler = ref => {
    this._headerRef = ref;
  };

  triggerRefreshOnlineOpenOrderList = throttle(this.getOnlineOpenOrderList, 1000, { leading: false, trailing: true });

  triggerSearch = throttle(
    (isManual = false) => {
      if (isManual) {
        this.props.actions.refreshMrsOpenOrders({ source: 'openOrderListRefresh' });
      }
      this.getLocalOpenOrderList();
      requestAnimationFrame(this.getOnlineOpenOrderList);
    },
    100,
    { leading: false, trailing: true }
  );

  onChangeTextHandler = newValue => {
    this.searchString = newValue.trim();
    this.triggerSearch();
  };

  searchHandler = () => {
    this.triggerSearch();
  };

  onClearValueHandler = () => {
    this.searchString = '';
    this.triggerSearch();
  };

  goBack = () => {
    const { inMerge, inSplit, inMove } = this.props;
    const onMultipleChooseBack = getParam(this.props, 'onMultipleChooseBack');
    if ((inMerge || inSplit) && !Boolean(this._tableId)) {
      this.onCompleteMergeSplitOpenOrder();
    } else {
      if (inMerge && Boolean(this._tableId)) {
        onMultipleChooseBack && onMultipleChooseBack(this._selectedOpenOrdersMap);
      } else if (inMove) {
        onMultipleChooseBack && onMultipleChooseBack({});
      }
      this.props.navigation.goBack();
    }
  };
}

export default connector(OpenOrdersList);

const styles = StyleSheet.create({
  header: {
    paddingTop: STATUS_BAR_HEIGHT,
    height: NAVIGATOR_HEADER_HEIGHT,
    paddingHorizontal: NAVIGATOR_PADDING_HORIZONTAL,
    backgroundColor: 'white',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    elevation: 1,
    shadowColor: 'black',
    shadowOffset: { height: 1, width: 0 },
    shadowOpacity: 0.1,
  },
  flatStyle: {
    paddingHorizontal: scaleSizeW(16),
  },
  sectionText: {
    width: width,
    paddingHorizontal: scaleSizeW(34),
    fontSize: currentThemes.fontSize18,
    color: '#393939',
  },
  editOrderWarn1: {
    color: '#EB4646',
  },
  editOrderWarn2: {
    fontWeight: 'bold',
  },
  rightButtonContainer: {
    marginRight: scaleSizeW(40),
    minWidth: scaleSizeW(160),
    paddingHorizontal: scaleSizeW(21),
    height: scaleSizeH(56),
    borderRadius: scaleSizeW(8),
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FC7118',
    borderWidth: 0,
  },
  rightButtonText: {
    fontSize: currentThemes.fontSize18,
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
    textAlignVertical: 'center',
  },
  moveOrderButtonContainer: {
    marginRight: scaleSizeW(40),
    minWidth: scaleSizeW(160),
    paddingHorizontal: scaleSizeW(21),
    height: scaleSizeH(56),
    borderRadius: scaleSizeW(8),
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: '#D6D6D6',
  },
  moveOrderButtonText: {
    fontSize: currentThemes.fontSize18,
    color: '#393939',
    fontWeight: 'bold',
    textAlign: 'center',
    textAlignVertical: 'center',
  },
  mergeSplitHint: {
    alignSelf: 'center',
    paddingHorizontal: scaleSizeW(46),
    marginVertical: scaleSizeH(11),
    paddingVertical: scaleSizeH(12),
    fontSize: currentThemes.fontSize24,
    backgroundColor: '#FFC84A',
    textAlign: 'center',
    overflow: 'hidden',
    color: '#000',
    borderWidth: 0,
    borderRadius: scaleSizeH(24),
  },
});
