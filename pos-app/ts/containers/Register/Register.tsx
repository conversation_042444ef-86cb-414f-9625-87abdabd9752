import * as Immutable from 'immutable';
import { compact, filter, findIndex, get, isEmpty, throttle } from 'lodash';
import React, { PureComponent } from 'react';
import { Alert, BackHandler, Linking, Platform, StatusBar, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import ScrollableTabView from 'react-native-scrollable-tab-view';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { createSelector } from 'reselect';
import {
  addBIRDiscountToTransaction,
  AddBIRDiscountToTransactionType,
  addPurchasedItem,
  AddPurchasedItemType,
  changeOpenOrderTable,
  ChangeOpenOrderTableType,
  checkCustomerInfoFromQR,
  CheckCustomerInfoFromQRType,
  checkLimitBeforeExecute,
  CheckLimitBeforeExecuteType,
  checkTimezone,
  clearSelectedUniquePromo,
  clearTransactionSession,
  createPreOrder,
  CustomerType,
  deleteCustomerFromTransaction,
  deleteCustomerFromTransactionType,
  deleteOnlineOpenOrder,
  DeleteOnlineOpenOrderType,
  deleteOpenOrder,
  DeleteOpenOrderType,
  deletePurchasedItem,
  DeletePurchasedItemType,
  employeeDeleteTransaction,
  EmployeeDeleteTransactionType,
  MRSError,
  navigateToHome,
  NavigateType,
  newPrintOpenOrderReceipt,
  printKitchenDocket,
  PrintKitchenDocketType,
  PrintOpenOrderReceiptType,
  printTodayBeepPreOrderAction,
  refundTransaction,
  RefundTransactionType,
  removeBIRDiscount,
  RequestActionAccessCallback,
  requestAuthorizedAction,
  RequestAuthorizedActionType,
  SaveEditedOnlineOrderType,
  saveOnlineOrder,
  saveOpenOrder,
  SaveOpenOrderType,
  setOpenOrderTransactionSession,
  SetOpenOrderTransactionSessionType,
  setTakeAwayToTransaction,
  SetTakeAwayToTransactionType,
  toggleToastInfo,
  ToggleToastInfoType,
  TransactionTypeWithDisplay,
  unlockPayLaterOrder,
  UnlockPayLaterOrderType,
  updateOpenOrder,
  UpdateOpenOrderType,
  updateTableLayoutSettings,
  UpdateTableLayoutSettingsType,
  updateTransactionPax,
  UpdateTransactionPaxType,
  updateTransactionTableIdAndPax,
  UpdateTransactionTableIdAndPaxType,
  checkFreeStorage,
  CheckFreeStorageType,
} from '../../actions';
import { GeneralTextInput, SearchHeader } from '../../components/common';
import { NAVIGATOR_HEADER_HEIGHT, NAVIGATOR_PADDING_HORIZONTAL } from '../../components/common/NavigatorHeader';
import { ModalMoreAction } from '../../components/modal';
import { CartContent } from '../../components/register/';
import { carouselTabStyles } from '../../components/register/IPadLayout';
import { IconClose, IconLeft, IconOpenOrder, IconPeople, IconSearch } from '../../components/ui';
import {
  AuthorizationType,
  CART_WIDTH,
  CashierActions,
  currentThemes,
  height,
  IsAndroid,
  MallIntegrationChannel,
  SalesChannelType,
  SharedStyles,
  STATUS_BAR_HEIGHT,
  t,
  TransactionFlowType,
  width,
} from '../../constants';
import { CommonColors, scaleSizeH, scaleSizeW } from '../../constants/themes';
import DAL from '../../dal';
import { EmployeeType, ProductType, PromotionType } from '../../typings';
import { testProps } from '../../utils';
import AccessControl from '../../utils/accessControl';
import { createDate, getAlreadyCloseZReading, isLastZReadingNotClose, isPreviousZReadingIncludedUncountedTrx } from '../../utils/datetime';
import * as JSONUtils from '../../utils/json';
import { ScannerSubscription } from '../../utils/scanner';
import TimezoneManager from '../../utils/timezone';
import { canSaveOrders, getDisplayItemsCount, isPreOrderPickUp } from '../../utils/transaction';

import { DrawerActions } from '@react-navigation/native';
import MenuWithNotification from '../../components/common/MenuWithNotification';

import MRSInfo from '../../components/register/MRSInfo';
import PauseBeepNotification from '../../components/settings/beep/PauseBeepNotification';

import RNFS from 'react-native-fs';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { getCustomerQRSetting, GetCustomerQRSettingType } from '../../actions/customer';
import { editMigrateTableLayoutToast } from '../../components/common/TopNotification';
import InitLearnInterceptor from '../../components/mrs/InitLearnInterceptor';
import NewSearchProductResult from '../../components/register/NewSearchProductResult';
import SimpleIPadLayout from '../../components/register/SimpleIPadLayout';
import NewWifiBanner from '../../components/settings/NewWifiBanner';
import StockLevelBanner from '../../components/settings/StockLevelBanner';
import IconBadGlobal from '../../components/ui/svgIcons/iconBadGlobal';
import { AllSettingTabs } from '../../constants/settingTabs';
import { KitchenEvent } from '../../models/print/manager/KitchenManager';
import { navigateToSettings } from '../../navigation/commonNavigate';
import { onMRSInterceptor } from '../../sagas/mrs/checkSync';
import {
  selectAssignTableID,
  selectAutoOrderId,
  selectBirAccredited,
  selectCountry,
  selectCurrency,
  selectCurrentNetworkType,
  selectEmployeeId,
  selectEnableCashback,
  selectEnableCustomerShortCut,
  selectEnableLoyalty,
  selectEnableOpenOrders,
  selectEnableOpenOrdersShortCut,
  selectEnablePax,
  selectEnableServiceCharge,
  selectEnableTableLayout,
  selectEnableTakeaway,
  selectFreeTrial,
  selectGBEnableIminScannerFix,
  selectGBEnableNewScannerFlow,
  selectGBProductLayoutRevampEnabled,
  selectInsufficientStorageWarning,
  selectIsBeepPreOrderEnabled,
  selectIsBeepQREnabled,
  selectIsEnabledMRS,
  selectIsMallIntegrationEnabled,
  selectIsOneZReadingPerDayEnabled,
  selectLastZReadingCloseTime,
  selectLocalCountryMap,
  selectMakeStoreCreditAsPayment,
  selectMallIntegrationChannel,
  selectNeedShowEditTableLayoutToast,
  selectOperationHours,
  selectRegisterObjectId,
  selectSelectedUniquePromo,
  selectServiceChargeRate,
  selectServiceChargeTax,
  selectShiftOpenStatus,
  selectTableLayoutEnabled,
  selectTrialOrderLimit,
  selectUniquePromos,
  selectUniquePromosError,
  selectViewEditTransaction,
} from '../../sagas/selector';
import { hasProductAmusementTax } from '../../sagas/transaction/common';
import { checTrialOrderLimit, INVALID_OOS_STATUS_BANNER } from '../../sagas/transaction/saleFlow';
import { MixpanelManagerInstance } from '../../utils/Analytics';
import { isIminD1, isIminD4, isIminFalcon1, isIminSwan1 } from '../../utils/deviceInfo';
import globalConfig from '../../utils/globalConfig';
import { ScannerFix } from '../../utils/growthbook';
import { IminLcdManager } from '../../utils/lcd';
import { infoZReadingEvent, OrderOperationEnum, ZReadingAction } from '../../utils/logComponent';
import { TableSelectedOrdersMap } from '../TableLayout/TableLayout';
import BarcodeScanner from './BarcodeScanner';
import BeepIconBadge from './BeepIconBadge';
import InternetStateButton from './InternetStateButton';
import StorageButton from './StorageButton';

interface Props {
  // Redux
  country?: string;
  navigation: any;
  shiftOpenStatus?: boolean;
  birAccredited?: boolean;
  transactionSession?: TransactionTypeWithDisplay;
  quickLayout?: any[];
  syncInfo?: any;
  currency?: string;
  enableServiceCharge?: boolean;
  serviceChargeRate?: number;
  serviceChargeTax?: number;
  assignTableID?: boolean;
  autoOrderId?: boolean;
  currentEmployeeId?: string;
  enableOpenOrders?: boolean;
  enableTableLayout?: boolean;
  tableLayoutEnabled?: boolean;
  enableOpenOrdersShortCut?: boolean;
  enableCustomerShortCut?: boolean;
  netInfoType?: string;
  shareQuickLayoutStores?: any[];
  customer?: CustomerType;
  isBeepEnabled?: boolean;
  isBeepPreOrderEnabled?: boolean;
  registerObjectId: string;
  freeTrial?: boolean;
  enableTakeaway: boolean;
  tableId?: string;
  viewEditTransactionNeedManagerGranted: boolean;
  enableCashback: boolean;
  enableLoyalty: boolean;
  isEnabledMRS: boolean;
  operationHours?: string;
  needShowEditTableLayoutToast?: boolean;
  lastZReadingCloseTime?: string;
  enablePax?: boolean;
  uniquePromos?: any;
  uniquePromosError?: any;
  mallIntegrationChannel?: string;
  isMallIntegrationEnabled?: boolean;
  localCountryMap?: any;
  isOneZReadingPerDayEnabled?: boolean;
  isNewProductLayout?: boolean;
  trialOrderLimitEnabled?: boolean;
  enableScannerFix?: ScannerFix;
  enableNewScannerFlow?: boolean;
  selectedUniquePromo: PromotionType;
  insufficientStorageWarning: any;
  makeStoreCreditAsPayment: boolean;
  actions: {
    clearTransactionSession(): void;
    addBIRDiscountToTransaction(payload: AddBIRDiscountToTransactionType): void;
    addPurchasedItem(payload: AddPurchasedItemType): void;
    deletePurchasedItem(payload: DeletePurchasedItemType): void;
    refundTransaction(payload: RefundTransactionType): void;
    saveOpenOrder(payload: SaveOpenOrderType): void;
    updateOpenOrder(payload: UpdateOpenOrderType): void;
    setOpenOrderTransactionSession(payload: SetOpenOrderTransactionSessionType): void;
    checkTimezone;
    requestAuthorizedAction(payload: RequestAuthorizedActionType): void;
    printTodayBeepPreOrderAction(payload: any): void;
    newPrintOpenOrderReceipt(payload: PrintOpenOrderReceiptType): void;
    printKitchenDocket(payload: PrintKitchenDocketType): void;
    deleteOpenOrder(payload: DeleteOpenOrderType): void;
    deleteOnlineOpenOrder(payload: DeleteOnlineOpenOrderType): void;
    employeeDeleteTransaction(payload: EmployeeDeleteTransactionType): void;
    deleteCustomerFromTransaction(payload: deleteCustomerFromTransactionType): void;
    createPreOrder;
    updateTransactionTableIdAndPax(payload: UpdateTransactionTableIdAndPaxType): void;
    updateTransactionPax(payload: UpdateTransactionPaxType): void;
    toggleToastInfo(payload: ToggleToastInfoType): void;
    setTakeAwayToTransaction(payload: SetTakeAwayToTransactionType): void;
    navigateToHome(payload: NavigateType): void;
    saveOnlineOrder(payload: SaveEditedOnlineOrderType): void;
    unlockPayLaterOrder(payload: UnlockPayLaterOrderType): void;
    changeOpenOrderTable(payload: ChangeOpenOrderTableType): void;
    checkLimitBeforeExecute(payload: CheckLimitBeforeExecuteType): void;
    updateTableLayoutSettings(payload: UpdateTableLayoutSettingsType): void;
    getCustomerQRSetting(payload: GetCustomerQRSettingType): void;
    removeBIRDiscount(): void;
    checkCustomerInfoFromQR(payload: CheckCustomerInfoFromQRType): void;
    clearSelectedUniquePromo(): void;
    checkFreeStorage(payload: CheckFreeStorageType): void;
  };
}

interface State {
  selectedItemIndex: number;
  search: string;
  searching: boolean;
}

const TAB_BAR_HEIGHT = NAVIGATOR_HEADER_HEIGHT;
const SEARCH_BAR_HEIGHT = NAVIGATOR_HEADER_HEIGHT;

const fromImmutableTransactionSession = createSelector(
  (state: Immutable.Map<string, any>) => state.get('TransactionSession', Immutable.Map()),
  transactionSession => transactionSession.toJS()
);

const fromImmutableQuickLayout = createSelector(
  (state: Immutable.Map<string, any>) => {
    return state.getIn(['Storage', 'quickLayout'], Immutable.List());
  },
  immutableQuickLayout => {
    return immutableQuickLayout.toJS();
  }
);

const fromImmutableShareQuickLayoutStores = createSelector(
  (state: Immutable.Map<string, any>) => state.getIn(['Storage', 'shareQuickLayoutStores'], Immutable.List()),
  shareQuickLayoutStores => shareQuickLayoutStores.toJS()
);

const fromImmutableSyncInfo = createSelector(
  (state: Immutable.Map<string, any>) => state.getIn(['Storage', 'syncInfo'], Immutable.Map()),
  syncInfo => syncInfo.toJS()
);

const mapStateToProps = state => ({
  country: selectCountry(state),
  shiftOpenStatus: selectShiftOpenStatus(state),
  transactionSession: fromImmutableTransactionSession(state),
  quickLayout: fromImmutableQuickLayout(state),
  viewEditTransactionNeedManagerGranted: selectViewEditTransaction(state) === '1',
  currency: selectCurrency(state),
  birAccredited: selectBirAccredited(state),
  enableServiceCharge: selectEnableServiceCharge(state),
  serviceChargeRate: selectServiceChargeRate(state),
  serviceChargeTax: selectServiceChargeTax(state),
  assignTableID: selectAssignTableID(state),
  autoOrderId: selectAutoOrderId(state),
  currentEmployeeId: selectEmployeeId(state),
  enableOpenOrders: selectEnableOpenOrders(state),
  enableTableLayout: selectEnableTableLayout(state),
  tableLayoutEnabled: selectTableLayoutEnabled(state),
  needShowEditTableLayoutToast: selectNeedShowEditTableLayoutToast(state),
  enableOpenOrdersShortCut: selectEnableOpenOrdersShortCut(state),
  enableCustomerShortCut: selectEnableCustomerShortCut(state),
  netInfoType: selectCurrentNetworkType(state),
  shareQuickLayoutStores: fromImmutableShareQuickLayoutStores(state),
  customer: state.getIn(['TransactionSession', 'customer']),
  syncInfo: fromImmutableSyncInfo(state),
  isBeepEnabled: selectIsBeepQREnabled(state),
  isBeepPreOrderEnabled: selectIsBeepPreOrderEnabled(state),
  registerObjectId: selectRegisterObjectId(state),
  freeTrial: selectFreeTrial(state),
  enableTakeaway: selectEnableTakeaway(state),
  enableCashback: selectEnableCashback(state),
  enableLoyalty: selectEnableLoyalty(state),
  isEnabledMRS: selectIsEnabledMRS(state),
  operationHours: selectOperationHours(state),
  lastZReadingCloseTime: selectLastZReadingCloseTime(state),
  enablePax: selectEnablePax(state),
  uniquePromos: selectUniquePromos(state),
  uniquePromosError: selectUniquePromosError(state),
  mallIntegrationChannel: selectMallIntegrationChannel(state),
  isMallIntegrationEnabled: selectIsMallIntegrationEnabled(state),
  localCountryMap: selectLocalCountryMap(state),
  isOneZReadingPerDayEnabled: selectIsOneZReadingPerDayEnabled(state),
  isNewProductLayout: selectGBProductLayoutRevampEnabled(state),
  trialOrderLimitEnabled: selectTrialOrderLimit(state),
  enableScannerFix: selectGBEnableIminScannerFix(state),
  enableNewScannerFlow: selectGBEnableNewScannerFlow(state),
  selectedUniquePromo: selectSelectedUniquePromo(state),
  insufficientStorageWarning: selectInsufficientStorageWarning(state),
  makeStoreCreditAsPayment: selectMakeStoreCreditAsPayment(state),
});

const mapDispatchToProps = dispatch => ({
  actions: bindActionCreators(
    {
      addBIRDiscountToTransaction,
      clearTransactionSession,
      addPurchasedItem,
      deletePurchasedItem,
      refundTransaction,
      checkTimezone,
      requestAuthorizedAction,
      printTodayBeepPreOrderAction,
      saveOpenOrder,
      setOpenOrderTransactionSession,
      updateOpenOrder,
      newPrintOpenOrderReceipt,
      deleteOpenOrder,
      deleteOnlineOpenOrder,
      employeeDeleteTransaction,
      deleteCustomerFromTransaction,
      createPreOrder,
      updateTransactionTableIdAndPax,
      toggleToastInfo,
      setTakeAwayToTransaction,
      navigateToHome,
      saveOnlineOrder,
      unlockPayLaterOrder,
      checkLimitBeforeExecute,
      changeOpenOrderTable,
      updateTableLayoutSettings,
      removeBIRDiscount,
      getCustomerQRSetting,
      updateTransactionPax,
      checkCustomerInfoFromQR,
      printKitchenDocket,
      clearSelectedUniquePromo,
      checkFreeStorage,
    },
    dispatch
  ),
});

export class Register extends PureComponent<Props, State> {
  static navigationOptions = () => ({
    headerShown: false,
  });

  private _moreDialog;
  private _unsubscribeFocusListener;
  private _unsubscribeBlurListener;
  private _scannerListener;
  private searchString: string;
  private _searchTextInputRef: GeneralTextInput;
  private _employee: EmployeeType;
  private _containerWidth: number;
  private _containerHeight: number;
  private _cartHeight: number;
  private _backButtonListener: any;
  private _searchFocusTimer: any;

  constructor(props) {
    super(props);
    this.state = {
      selectedItemIndex: -1,
      search: '',
      searching: false,
    };
    this._containerWidth = width - CART_WIDTH;
    this._containerHeight = height - SEARCH_BAR_HEIGHT - TAB_BAR_HEIGHT;
    this._cartHeight = height - SEARCH_BAR_HEIGHT;

    this._employee = DAL.getEmployeeById(props.currentEmployeeId);
  }

  triggerSearch = throttle(
    () => {
      this.setState({ search: this.searchString });
    },
    100,
    { leading: true, trailing: true }
  );

  onChangeTextHandler = newValue => {
    this.searchString = newValue.trim();
    this.triggerSearch();
  };

  initSearch = () => {
    this.searchString = '';
    this.setState({ search: '' });
  };

  onClearValueHandler = () => {
    this.initSearch();
  };

  searchHandler = () => {
    this.setState({ search: this.searchString });
  };

  searchRefHandler = ref => {
    this._searchTextInputRef = ref;
  };

  UNSAFE_componentWillReceiveProps(nextProps) {
    const { enableTakeaway, transactionSession, enablePax } = this.props;
    if (enableTakeaway !== nextProps.enableTakeaway) {
      this.setSalesChannel(nextProps.enableTakeaway, transactionSession);
    }
    if (enablePax !== nextProps.enablePax) {
      this.props.actions.updateTransactionPax({ pax: null });
    }
  }

  setSalesChannel = (enableTakeaway, transactionSession) => {
    if (Boolean(transactionSession) && Object.keys(transactionSession).length > 0) {
      if (enableTakeaway) {
        if (transactionSession.salesChannel && transactionSession.salesChannel === SalesChannelType.TAKEAWAY) {
          this.props.actions.setTakeAwayToTransaction({ salesChannel: SalesChannelType.TAKEAWAY });
        } else {
          this.props.actions.setTakeAwayToTransaction({ salesChannel: SalesChannelType.DINE_IN });
        }
      } else {
        this.props.actions.setTakeAwayToTransaction({ salesChannel: SalesChannelType.DEFAULT });
      }
    }
  };

  componentWillUnmount() {
    Boolean(this._unsubscribeFocusListener) && this._unsubscribeFocusListener();
    Boolean(this._unsubscribeBlurListener) && this._unsubscribeBlurListener();
    this.onBlur();
    this.clearTimer();
  }

  componentDidMount() {
    const { freeTrial, enableTakeaway, transactionSession, needShowEditTableLayoutToast, trialOrderLimitEnabled } = this.props;
    const onComplete = {
      callback: ({ storeTimezoneGMT, localTimezoneGMT, needSetTimezone }) => {
        this.checkTimezone({ storeTimezoneGMT, localTimezoneGMT, needSetTimezone });
      },
    };

    this.props.actions.checkTimezone({
      onComplete,
    });
    this._unsubscribeFocusListener = this.props.navigation.addListener('focus', this.onFocus);
    this._unsubscribeBlurListener = this.props.navigation.addListener('blur', this.onBlur);

    if (freeTrial) {
      this.props.navigation.navigate(trialOrderLimitEnabled ? 'ModalWelcomeFreeTrial' : 'ModalWelcomeFreeTrialOld');
    }

    if (needShowEditTableLayoutToast) {
      editMigrateTableLayoutToast();
      this.props.actions.updateTableLayoutSettings({
        needShowEditTableLayoutToast: false,
      });
    }
    this.setSalesChannel(enableTakeaway, transactionSession);

    IminLcdManager.clear();

    this.checkZReadingAndMallIntegration();

    if (globalConfig.customerQRFlag.needListening) {
      checkCustomerInfoFromQR({ currentRecord: transactionSession });
    }
  }

  checkZReadingAndMallIntegration = () => {
    const {
      operationHours,
      lastZReadingCloseTime,
      birAccredited,
      registerObjectId,
      isMallIntegrationEnabled,
      mallIntegrationChannel,
      tableLayoutEnabled,
      enableTableLayout,
      isOneZReadingPerDayEnabled,
    } = this.props;
    if (tableLayoutEnabled && enableTableLayout) return;
    if (birAccredited) {
      const { lastZReadingNotClose, needCloseFrom, needCloseTo } = isLastZReadingNotClose(lastZReadingCloseTime, operationHours, registerObjectId);
      const previousZReadingIncludedUncountedTrx =
        isOneZReadingPerDayEnabled && isPreviousZReadingIncludedUncountedTrx(lastZReadingCloseTime, operationHours, registerObjectId);
      const needPrompt = lastZReadingNotClose || previousZReadingIncludedUncountedTrx;
      if (needPrompt) {
        switch (mallIntegrationChannel) {
          // AYALA_MALL
          case MallIntegrationChannel.AYALA_MALL:
            // If POS detected there are transactions created after Zreading last close time, and before the previous operation close time (yesterday)
            infoZReadingEvent({
              action: ZReadingAction.PromptToCloseZReading,
              privateDataPayload: {
                channel: 'ModalAyalaMallPrompt',
                operationHours,
                lastZReadingCloseTime,
                needCloseFrom,
                needCloseTo,
              },
            });
            this.props.navigation.navigate('ModalAyalaMallPrompt');
            break;
          case MallIntegrationChannel.ROBINSON_MALL:
          case MallIntegrationChannel.MEGAWORLD:
          case MallIntegrationChannel.ROCKWELL:
          case MallIntegrationChannel.FBDC:
          case MallIntegrationChannel.FILINVEST:
          case MallIntegrationChannel.SFDC:
          case MallIntegrationChannel.EVER_GOTESCO:
            // ROBINSON_MALL
            // If POS detected there are transactions created after Zreading last close time, and before the previous operation close time (yesterday)
            infoZReadingEvent({
              action: ZReadingAction.PromptToCloseZReading,
              privateDataPayload: {
                channel: 'ModalRobinsonMallPrompt',
                operationHours,
                lastZReadingCloseTime,
                needCloseFrom,
                needCloseTo,
              },
            });
            this.props.navigation.navigate('ModalRobinsonMallPrompt', { needCloseFrom, needCloseTo });
            break;
          default:
            // If not close z-reading yesterday, then prompt and force close
            // If the z-reading was closed yesterday, then the close time should be greater than the operation time of the day before yesterday and less than or equal to yesterday's operation time
            // So if the last close time was earlier than the operation time the day before, it means that there was no close yesterday
            infoZReadingEvent({
              action: ZReadingAction.PromptToCloseZReading,
              privateDataPayload: {
                channel: 'ModalCloseLastZReading',
                operationHours,
                lastZReadingCloseTime,
                needCloseFrom,
                needCloseTo,
              },
            });
            this.props.navigation.navigate('ModalCloseLastZReading', { needCloseFrom, needCloseTo });
            break;
        }
      }
    }
  };

  onFocus = () => {
    if (IsAndroid) {
      const { enableTableLayout, tableLayoutEnabled } = this.props;
      this._backButtonListener = BackHandler.addEventListener('hardwareBackPress', () => {
        if (tableLayoutEnabled && enableTableLayout) {
          this.backToHome();
          return true;
        } else {
          return false;
        }
      });
    }
    this._scannerListener = ScannerSubscription.addListener('Scanner_Resp', result => {
      const barcode = result.barcode;
      this.handleScanBarcode(barcode);
    });
  };

  onBlur = () => {
    this._backButtonListener && this._backButtonListener.remove();
    this._backButtonListener = null;
    this._scannerListener && this._scannerListener.remove();
    this._scannerListener = null;
  };

  checkTimezone = ({ storeTimezoneGMT, localTimezoneGMT, needSetTimezone }) => {
    if (needSetTimezone) {
      Alert.alert(
        `The system timezone is currently set as GMT ${storeTimezoneGMT}, however your account setting timezone is set as GMT ${localTimezoneGMT}. To proceed please change the system timezone to GMT ${storeTimezoneGMT}`,
        '',
        [{ text: 'CHANGE', onPress: () => TimezoneManager.setTimezone() }],
        { cancelable: false }
      );
    }
  };

  handleScanBarcode = barcode => {
    if (!Boolean(barcode)) {
      return;
    }
    if (this.state.searching) {
      this._searchTextInputRef && this._searchTextInputRef.setValue(barcode);
      this.searchString = barcode;
      this.searchHandler();
    } else {
      const product = DAL.getProductByBarcode(barcode);
      if (product === null || product === undefined) {
        if (!this.props.enableNewScannerFlow) {
          if (this.props.enableScannerFix === ScannerFix.Default && (isIminSwan1() || isIminD1() || isIminFalcon1() || isIminD4())) {
            this.handleScanBarcode_fixImin(barcode.toUpperCase());
            return;
          }
          if (this.props.enableScannerFix === ScannerFix.Enabled) {
            this.handleScanBarcode_fixImin(barcode.toUpperCase());
            return;
          }
        }
        const haveManageProductAccess = AccessControl.checkManageProductAccess(this._employee);
        if (haveManageProductAccess) {
          this.props.navigation.navigate('ModalInfo', {
            title: `${t('Cant find')} ${barcode}`,
            isShowTitle: true,
            info: t('Would you like to add this product to your store'),
            okText: t('ADD PRODUCT'),
            backParams: barcode,
            needGoBackWhenSubmit: false,
            onCancelHandler: () => null,
            onSubmitHandler: this.onNewProductWithBarCode,
          });
        } else {
          this.props.navigation.navigate('ModalInfo', { info: t('was not found', { barcode }) });
        }
        return;
      }
      this.onAddProductToCart(product);
    }
  };

  handleScanBarcode_fixImin = barcode => {
    if (!Boolean(barcode)) {
      return;
    }
    if (this.state.searching) {
      this._searchTextInputRef && this._searchTextInputRef.setValue(barcode);
      this.searchString = barcode;
      this.searchHandler();
    } else {
      const product = DAL.getProductByBarcode(barcode);
      if (product === null || product === undefined) {
        const haveManageProductAccess = AccessControl.checkManageProductAccess(this._employee);
        if (haveManageProductAccess) {
          this.props.navigation.navigate('ModalInfo', {
            title: `${t('Cant find')} ${barcode}`,
            isShowTitle: true,
            info: t('Would you like to add this product to your store'),
            okText: t('ADD PRODUCT'),
            backParams: barcode,
            needGoBackWhenSubmit: false,
            onCancelHandler: () => null,
            onSubmitHandler: this.onNewProductWithBarCode,
          });
        } else {
          this.props.navigation.navigate('ModalInfo', { info: t('was not found', { barcode }) });
        }
        return;
      }
      this.onAddProductToCart(product);
    }
  };

  handleScanBarcode_newFlow = barcode => {
    if (!Boolean(barcode)) {
      return;
    }
    if (this.state.searching) {
      this._searchTextInputRef && this._searchTextInputRef.setValue(barcode);
      this.searchString = barcode;
      this.searchHandler();
    } else {
      const product = DAL.getProductByBarcode(barcode);
      if (product === null || product === undefined) {
        const haveManageProductAccess = AccessControl.checkManageProductAccess(this._employee);
        if (haveManageProductAccess) {
          this.props.navigation.navigate('ModalInfo', {
            title: `${t('Cant find')} ${barcode}`,
            isShowTitle: true,
            info: t('Would you like to add this product to your store'),
            okText: t('ADD PRODUCT'),
            backParams: barcode,
            needGoBackWhenSubmit: false,
            onCancelHandler: () => null,
            onSubmitHandler: this.onNewProductWithBarCode,
          });
        } else {
          this.props.navigation.navigate('ModalInfo', { info: t('was not found', { barcode }) });
        }
        return;
      }
      this.onAddProductToCart(product);
    }
  };

  onNewProductWithBarCode = (_, navigation, barcode) => {
    this.props.navigation.navigate('Products', { barcode });
  };

  clearTimer = () => {
    this._searchFocusTimer && clearTimeout(this._searchFocusTimer);
    this._searchFocusTimer = null;
  };

  makeSearchFocusDelay = () => {
    const { searching } = this.state;
    if (searching) {
      this._searchFocusTimer = setTimeout(() => this._searchTextInputRef && this._searchTextInputRef.focus());
    }
  };

  toggleSearchInputHandler = () => {
    const { searching, search } = this.state;
    if (searching) {
      this.clearTimer();
      requestAnimationFrame(() => this._searchTextInputRef && this._searchTextInputRef.clear());
    }
    this.setState(
      {
        searching: !searching,
        search: searching ? search : '',
      },
      this.makeSearchFocusDelay
    );
    this.searchString = '';
  };

  checkOpenOrderOnPressSave = () => {
    const { transactionSession } = this.props;
    const { transactionId, modifiedDate, isOpen } = transactionSession;
    if (!isOpen) return false;

    const realmTransaction = DAL.getTrancationById(transactionId);
    if (!Boolean(realmTransaction)) return true;

    if (realmTransaction.isCompleted) {
      console.log('transactionSession data is expired, this order has already been checked out.');
      Alert.alert(
        '',
        'This order has already been checked out. ',
        [
          {
            text: 'OK',
            onPress: () => this.props.actions.clearTransactionSession(),
          },
        ],
        {
          cancelable: false,
        }
      );
      return false;
    }

    if (realmTransaction.isOpen && realmTransaction.modifiedDate && realmTransaction.modifiedDate.getTime && modifiedDate && modifiedDate.getTime) {
      const diffTime = realmTransaction.modifiedDate.getTime() - modifiedDate.getTime();
      if (diffTime > 0) {
        console.log('transactionSession data is expired, please refresh it');
        Alert.alert(
          '',
          'This order data is expired, please refresh it.',
          [{ text: 'OK', onPress: () => this.props.actions.setOpenOrderTransactionSession({ transactionId }) }],
          { cancelable: false }
        );
        return false;
      } else if (diffTime === 0) {
        console.log('transactionSession data changed', `diff time: ${diffTime}`);
        this.props.actions.clearTransactionSession();
        return false;
      } else {
        console.log('transactionSession data changed', `diff time: ${diffTime}`);
        // Alert.alert('You changed this order, do you want to save changes?');
        return true;
      }
    }

    return true;
  };

  onPressSaveOrder = throttle(
    (onComplete?, needGoHome = true) => {
      // const { items } = transactionSession;
      // const totalCount = getDisplayItemsCount(items);
      // const isEmpty = totalCount <= 0;
      const {
        transactionSession: { isOpen, items, transactionId, tableId, isOnlineOrder, isPreview = false },
        navigation,
        actions: { deleteOpenOrder, updateOpenOrder, saveOpenOrder, saveOnlineOrder, navigateToHome, checkLimitBeforeExecute, clearTransactionSession },
        freeTrial,
        registerObjectId,
        trialOrderLimitEnabled,
      } = this.props;
      if (isOnlineOrder) {
        if (isPreview) {
          const onSuccess = {
            callback: () => {
              navigateToHome({ navigation });
            },
          };
          const onFailed = {
            callback: this.warningToReSave,
          };
          saveOnlineOrder({ goCheckOutAfterSave: false, onSuccess, onFailed });
        } else {
          clearTransactionSession();
          navigateToHome({ navigation });
        }
      } else if (isOpen) {
        const totalCount = getDisplayItemsCount(items);
        const isEmpty = totalCount <= 0;
        // local open order MRS
        const onComplete = (error: MRSError) => {
          if (onMRSInterceptor(error)) {
            if (needGoHome) this.backToHome();
          }
        };
        if (isEmpty) {
          deleteOpenOrder({ transactionId, onComplete });
        } else {
          updateOpenOrder({ onComplete });
        }
      } else {
        if (!checTrialOrderLimit(trialOrderLimitEnabled, freeTrial, registerObjectId)) return;
        if (!Boolean(tableId)) {
          const onLimitComplete = (result: MRSError) => {
            if (onMRSInterceptor(result)) {
              const { enablePax } = this.props;
              if (enablePax) {
                navigation.navigate('ModalPaxAndTable', {
                  onSubmitHandler: data => {
                    const { tableId, pax } = data;
                    this.saveOrder(needGoHome, tableId, onComplete, pax);
                  },
                  transactionId,
                  title: t('Save Order'),
                  ctaTitle: t('SAVE'),
                });
              } else {
                navigation.navigate('ModalTableId', {
                  transactionId,
                  onSubmitHandler: tableId => {
                    // open order save
                    this.saveOrder(needGoHome, tableId, onComplete);
                  },
                  title: t('Save Order'),
                });
              }
            }
          };
          checkLimitBeforeExecute({
            transaction: this.props.transactionSession,
            orderOperation: OrderOperationEnum.Save,
            onComplete: onLimitComplete,
          });
        } else {
          const onCompleteInterceptor = (error: MRSError) => {
            if (onMRSInterceptor(error)) {
              onComplete && onComplete();
              if (needGoHome) this.backToHome();
            }
          };
          saveOpenOrder({ onComplete: onCompleteInterceptor });
        }
      }
    },
    1000,
    { leading: true, trailing: false }
  );

  saveOrder = (needGoHome, tableId, onComplete, pax?) => {
    this.props.actions.updateTransactionTableIdAndPax({ tableId, pax });
    const onCompleteInterceptor = (error: MRSError) => {
      if (onMRSInterceptor(error)) {
        onComplete && onComplete();
        if (needGoHome) this.backToHome();
      }
    };
    this.props.actions.saveOpenOrder({ onComplete: onCompleteInterceptor });
  };

  saveOpenOrderShortCut = () => {
    this.onPressSaveOrder();
  };

  openDrawer = () => {
    requestAnimationFrame(() => {
      this.props.navigation.dispatch(DrawerActions.openDrawer());
    });
  };

  backToHome = () => {
    const {
      actions: { clearTransactionSession, navigateToHome },
      navigation,
    } = this.props;
    requestAnimationFrame(() => {
      clearTransactionSession();
      navigateToHome({ navigation });
    });
  };

  renderClearButton = () => {
    return (
      <View style={{ paddingRight: 8 }}>
        <IconClose width={scaleSizeW(48)} height={scaleSizeW(46)} color={'#A2A3B1'} />
      </View>
    );
  };

  renderSearchBar = (haveProduct, isOnlineOrder, alreadyCloseZReading) => {
    const { isBeepEnabled, enableOpenOrdersShortCut, transactionSession, viewEditTransactionNeedManagerGranted, shiftOpenStatus } = this.props;
    const { searching } = this.state;
    const canViewEditTransaction = AccessControl.checkViewEditTransactionAccess(this._employee, viewEditTransactionNeedManagerGranted);
    if (searching) {
      return (
        <SearchHeader
          headerStyle={styles.searchBarContainerSearching}
          onPressBack={this.toggleSearchInputHandler}
          searchRefHandler={this.searchRefHandler}
          placeholder={t('Search products')}
          searchHandler={this.searchHandler}
          onChangeTextHandler={this.onChangeTextHandler}
          onClearValueHandler={this.onClearValueHandler}
          clearButton={this.renderClearButton}
        />
      );
    } else {
      let openOrderCreateTime;
      const { enableTableLayout, tableLayoutEnabled } = this.props;
      if (enableOpenOrdersShortCut && transactionSession.isOpen) {
        const { createdDate } = transactionSession;
        if (createdDate) {
          openOrderCreateTime = createDate(createdDate, 'h:mm A');
        }
      }

      const { isOnlineOrder, pax } = transactionSession;
      const shouldSaveOrders = canSaveOrders(transactionSession);
      const needToShowIconPeople = (tableLayoutEnabled && enableTableLayout) || (this.props.enablePax && shouldSaveOrders && !isOnlineOrder);

      return (
        <View style={styles.searchBarContainer}>
          <View style={styles.leftBtnsContainer}>
            {/* this 1x1 view is reserved for barcode scanner focus point, avoid opening the drawer when key events dispatched to ui */}
            {IsAndroid && this.props.enableNewScannerFlow && (
              <>
                {/* <TouchableOpacity style={{ width: 1, height: 1 }} onPress={() => {}} {...testProps('al_drawer')}> */}
                {/*  <IconSearch color={'rgba(255,255,255,0)'} /> */}
                {/* </TouchableOpacity> */}
                <BarcodeScanner onBarcodeScanned={this.handleScanBarcode_newFlow} />
              </>
            )}
            <TouchableOpacity
              style={SharedStyles.touchableIconContainer}
              onPress={tableLayoutEnabled && enableTableLayout ? this.backToHome : this.openDrawer}
              {...testProps('al_drawer')}
            >
              {tableLayoutEnabled && enableTableLayout ? <IconLeft color={'#FC7118'} /> : <MenuWithNotification color={CommonColors.Icon} />}
            </TouchableOpacity>
            <InternetStateButton onPress={this.onTapInternetButton} />
            <StorageButton />
            {needToShowIconPeople && (
              <TouchableOpacity
                {...testProps('al_btn_433')}
                style={[SharedStyles.touchableIconContainer, { marginLeft: scaleSizeW(60), width: scaleSizeW(100) }]}
                onPress={this.onChangePax}
              >
                <IconPeople color={CommonColors.Icon} />
                <Text
                  style={{
                    color: '#303030',
                    marginLeft: scaleSizeW(5),
                    fontSize: currentThemes.fontSize24,
                  }}
                >
                  {String(pax || 0)}
                </Text>
              </TouchableOpacity>
            )}
            {enableOpenOrdersShortCut && shiftOpenStatus && (
              <TouchableOpacity
                activeOpacity={1}
                style={[SharedStyles.touchableIconContainer, { marginLeft: scaleSizeW(58), width: scaleSizeW(180) }]}
                onPress={this.goToOpenOrderLists}
                {...testProps('al_icon_openOrder')}
              >
                <IconOpenOrder color={CommonColors.Icon} />
                {openOrderCreateTime && <Text style={styles.openOrderCreateTimeText}>{openOrderCreateTime}</Text>}
              </TouchableOpacity>
            )}
            <MRSInfo style={{ marginLeft: scaleSizeW(24) }} />
          </View>
          <View style={SharedStyles.row}>
            {haveProduct ? (
              <TouchableOpacity
                {...testProps('al_btn_2')}
                activeOpacity={1}
                style={SharedStyles.touchableIconContainerregister}
                onPress={this.toggleSearchInputHandler}
                testID='searchButton'
                disabled={alreadyCloseZReading}
              >
                <IconSearch color={alreadyCloseZReading ? CommonColors.Disabled : CommonColors.Icon} />
              </TouchableOpacity>
            ) : null}
            {isBeepEnabled && canViewEditTransaction && <BeepIconBadge />}
          </View>
        </View>
      );
    }
  };

  onTapInternetButton = () => {
    this.props.navigation.navigate('ModalInfo', {
      title: t('Your network is unavailable'),
      isShowTitle: true,
      info: t('Check your network settings'),
      titleIcon: <IconBadGlobal color={'#D3D8EB'} width={25} height={25} />,
      okText: t('Go to Settings'),
      onSubmitHandler: () => {
        Linking.openSettings();
      },
    });

    MixpanelManagerInstance.throttledTrack('No Internet Connection Pop up');
  };

  onChangePax = () => {
    const { transactionSession } = this.props;
    const currentPax = get(transactionSession, 'pax', '');
    const currentTableId = get(transactionSession, 'tableId', '');
    const { enableTableLayout, tableLayoutEnabled } = this.props;
    if (tableLayoutEnabled && enableTableLayout) {
      this.props.navigation.navigate('ModalPax', {
        currentPax,
        onSubmitHandler: pax => {
          this.props.actions.updateTransactionTableIdAndPax({ pax: Boolean(pax) ? pax : 0 });
        },
        title: t('Assign Seat'),
      });
    } else if (this.props.enablePax) {
      // for non-table layout
      this.props.navigation.navigate('ModalPaxAndTable', {
        currentPax,
        currentTableId,
        shouldGoBackWhenConfirm: true,
        transactionId: transactionSession.transactionId,
        onSubmitHandler: data => {
          const { pax, tableId } = data;
          this.props.actions.updateTransactionTableIdAndPax({ tableId, pax });
        },
        title: t('Order Details'),
        ctaTitle: t('SAVE'),
      });
    }
  };

  _menu = null;

  setMenuRef = ref => {
    this._menu = ref;
  };

  hideMenu = () => {
    this._menu && this._menu.dismiss();
  };

  showMenu = () => {
    this._menu && this._menu.show();
  };

  navigateToOpenOrderLists = () => {
    this.props.navigation.navigate('OpenOrderLists', {
      onOpenOrderPress: transactionId => {
        const onComplete = {
          callback: () => {
            // NOP
          },
        };
        this.props.actions.setOpenOrderTransactionSession({ transactionId, onComplete });
      },
    });
  };

  toggleHomeAndGoOpenOrderList = () => {
    const {
      tableLayoutEnabled,
      enableTableLayout,
      actions: { clearTransactionSession },
    } = this.props;
    clearTransactionSession();
    if (tableLayoutEnabled && enableTableLayout) {
      // if tableLayout enabled, the transaction will always isOpen
    } else {
      // if tableLayout disabled, then need to close ModalTableId and navigate to OpenOrderLists
      this.props.navigation.navigate('OpenOrderLists', {
        onOpenOrderPress: transactionId => {
          const onComplete = {
            callback: () => {
              // NOP
            },
          };
          this.props.actions.setOpenOrderTransactionSession({ transactionId, onComplete });
        },
      });
    }
  };

  goToOpenOrderLists = () => {
    const { transactionSession, enableOpenOrdersShortCut } = this.props;
    const { items } = transactionSession;
    const totalCount = getDisplayItemsCount(items);
    const isEmpty = totalCount <= 0;
    const { transactionType, isPayLater } = transactionSession;
    const isManualReturn = transactionType === TransactionFlowType.Return;
    const isPreOrder = transactionType === TransactionFlowType.PreOrder;
    // @ts-ignore
    const isPickUp = isPreOrderPickUp(transactionSession);
    const isTakeAway = get(transactionSession, 'salesChannel', SalesChannelType.DEFAULT) === SalesChannelType.TAKEAWAY;
    const canSaveOrders = !isManualReturn && !isPreOrder && !isPickUp && !isTakeAway && enableOpenOrdersShortCut && !isPayLater;
    if (isEmpty || transactionSession.isOpen || !canSaveOrders) {
      this.navigateToOpenOrderLists();
    } else {
      this.onPressSaveOrder(this.toggleHomeAndGoOpenOrderList, false);
    }

    this.hideMenu();
  };

  onPressCollectPreOrder = () => {
    this.props.navigation.navigate('ModalCollectPreorder');
    this.hideMenu();
  };

  onPressPreOrder = () => {
    const { customer, navigation, actions } = this.props;
    if (Boolean(customer)) {
      actions.createPreOrder({});
    } else {
      navigation.navigate('ModalInfo', {
        info: t('To process a pre-order transaction, a customer must be selected'),
        needGoBackWhenSubmit: false,
        onSubmitHandler: (_, nav) => {
          actions.createPreOrder({});
          requestAnimationFrame(() => {
            this.onReplaceToAddCustomer(nav);
          });
        },
      });
    }
    this.hideMenu();
  };

  onManualRefundHandler = () => {
    const { currentEmployeeId } = this.props;
    this.props.actions.requestAuthorizedAction({
      employeeId: currentEmployeeId,
      name: CashierActions.Refund,
      type: AuthorizationType.Cashier,
      onSuccess: {
        callback: () => {
          this.props.actions.refundTransaction({ type: 'manual' });
          this.hideMenu();
        },
      },
      onFailure: {
        callback: () => {
          this.hideMenu();
        },
      },
    });
  };

  onPressPrint = () => {
    this.props.actions.newPrintOpenOrderReceipt({});
    this.hideMenu();
  };

  onPrintOpenOrder = () => {
    const transaction = this.props.transactionSession;
    if (transaction.transactionId && transaction.isOpen && !transaction.isOnlineOrder) {
      // @ts-ignore
      this.props.actions.printKitchenDocket({ eventName: KitchenEvent.resendOpenOrder, transaction });
      this.hideMenu();
    }
  };

  onPressPrintTodayBeepPreOrder = () => {
    const onComplete = {
      callback: ({ printFailedOrderIds }) => {
        this.hideMenu();
        if (printFailedOrderIds && printFailedOrderIds.length > 0) {
          let showOrderIds = '';
          for (const orderId of printFailedOrderIds) {
            showOrderIds += '\n' + orderId;
          }
          this.props.navigation.navigate('ModalInfo', { info: t('Print Failed Below:') + showOrderIds });
        }
      },
    };
    const deliveryDate = new Date().toISOString();
    this.props.actions.printTodayBeepPreOrderAction({ onComplete, deliveryDate });
  };

  onMenuCustomerClick = () => {
    const { customer } = this.props;
    customer ? this.onUnlinkCustomer() : this.onPressAddCustomer();
    this.hideMenu();
  };

  checkCustomerWithBIR = ({ enable, message }) => {
    if (!enable) {
      this.props.navigation.navigate('ModalInfo', { info: message, textAlign: 'left' });
    }
  };

  onUnlinkCustomer = () => {
    this.props.actions.deleteCustomerFromTransaction({ checkCustomerWithBIR: this.checkCustomerWithBIR });
  };

  onReplaceToAddCustomer = (nav?) => {
    const { netInfoType, navigation } = this.props;
    if (netInfoType === 'none' || netInfoType === 'unknown') {
      (nav || navigation).replace('ModalInfo', {
        title: t('No Internet Connection'),
        isShowTitle: true,
        info: t('Please connect to the internet to use this function'),
        titleIcon: <Icon name='warning' size={24} color='#FFA500' />,
        okText: t('Close'),
      });
    } else {
      (nav || navigation).replace('AddCustomer');
    }
  };

  onGetEnableCustomerQR = () => {
    return new Promise(resolve => {
      const onComplete = (enabled: boolean) => {
        resolve(enabled);
      };
      this.props.actions.getCustomerQRSetting({ onComplete });
    });
  };

  onPressAddCustomer = async () => {
    const { netInfoType, transactionSession } = this.props;
    if (netInfoType === 'none' || netInfoType === 'unknown') {
      this.props.navigation.navigate('ModalInfo', {
        title: t('No Internet Connection'),
        isShowTitle: true,
        info: t('Please connect to the internet to use this function'),
        titleIcon: <Icon name='warning' size={24} color='#FFA500' />,
        okText: t('Close'),
      });
    } else {
      const transactionType = get(transactionSession, 'transactionType');
      const enableCustomerQR = await this.onGetEnableCustomerQR();
      if (enableCustomerQR && transactionType !== TransactionFlowType.Return) {
        this.props.navigation.navigate('ModalCustomerQR');
      } else {
        this.props.navigation.navigate('AddCustomer');
      }
    }
  };

  onCustomerClick = () => {
    const { customer } = this.props;
    customer
      ? this.props.navigation.navigate('CustomerDetail', {
          customer: customer,
          isInCheckOut: false,
        })
      : this.onPressAddCustomer();
    this.hideMenu();
  };

  onPressNewOrder = () => {
    const {
      transactionSession,
      actions: { clearTransactionSession },
    } = this.props;
    // 如果是refund或者takeaway或者preorder的transaction，不能save，需要直接clear
    const { transactionType, isPayLater, hasUnsavedItems } = transactionSession;
    const isPreOrder = transactionType === TransactionFlowType.PreOrder;
    const isManualReturn = transactionType === TransactionFlowType.Return;
    // @ts-ignore
    const isPickUp = isPreOrderPickUp(transactionSession);
    const isTakeAway = get(transactionSession, 'salesChannel', SalesChannelType.DEFAULT) === SalesChannelType.TAKEAWAY;
    if (isPayLater) {
      if (hasUnsavedItems) {
        this.warningToLeaveChange();
      } else {
        clearTransactionSession();
      }
    } else if (!transactionSession.isOpen && !isManualReturn && !isPreOrder && !isPickUp && !isTakeAway) {
      this.onPressSaveOrder();
    } else {
      clearTransactionSession();
    }
    this.hideMenu();
  };

  alertErrorMsg = (title, msg) => {
    requestAnimationFrame(() => {
      this.props.navigation.navigate('ModalInfo', {
        title,
        isShowTitle: true,
        textAlign: 'center',
        info: msg,
        okText: t('OK'),
        onSubmitHandler: () => null,
        // eslint-disable-next-line @typescript-eslint/no-empty-function
      });
    });
  };

  warningToLeaveChange = () => {
    const {
      actions: { clearTransactionSession },
    } = this.props;
    requestAnimationFrame(() => {
      this.props.navigation.navigate('ModalInfo', {
        title: t('Leave Changes'),
        isShowTitle: true,
        textAlign: 'center',
        info: t('Changes will not be saved Do you want to proceed'),
        okText: t('CANCEL'),
        cancelText: t('PROCEED'),
        onSubmitHandler: () => null,
        // eslint-disable-next-line @typescript-eslint/no-empty-function
        onCancelHandler: clearTransactionSession,
      });
    });
  };

  onPressDeleteOrder = (needReplaceTo?) => {
    const {
      transactionSession,
      navigation: { navigate, replace },
    } = this.props;
    const navigateFunc = needReplaceTo == true ? replace : navigate;
    const { transactionId, total, isOnlineOrder = false, receiptNumber } = transactionSession;
    const { deleteOpenOrder, deleteOnlineOpenOrder, clearTransactionSession, employeeDeleteTransaction } = this.props.actions;
    this.hideMenu();
    if (transactionSession.isOpen) {
      navigateFunc('ModalInfo', {
        title: t('Are you sure you want to delete this order'),
        isShowTitle: true,
        info: 'You may not undo this action',
        okText: t('CONFIRM'),
        cancelText: t('CANCEL'),
        onSubmitHandler: () => {
          this.hideMenu();
          const onSuccess = {
            callback: () => {
              // cancel online order
              if (isOnlineOrder) {
                deleteOnlineOpenOrder({ receiptNumber });
              } else {
                const onComplete = (error: MRSError) => {
                  if (onMRSInterceptor(error)) {
                    employeeDeleteTransaction({ transactionId, amount: total, isOpenOrder: true });
                  }
                };
                deleteOpenOrder({ transactionId: transactionSession.transactionId, onComplete });
              }
            },
          };
          // need next frame
          requestAnimationFrame(() => {
            this.requestCashierDeleteActionWrapper(onSuccess);
          });
        },
        onCancelHandler: () => {
          this.hideMenu();
        },
      });
    } else {
      this.hideMenu();
      const onSuccess = {
        callback: () => {
          clearTransactionSession();
          employeeDeleteTransaction({ transactionId, amount: total, isOpenOrder: false });
        },
      };
      this.requestCashierDeleteActionWrapper(onSuccess);
    }
  };

  checkUniquePmotionsError = () => {
    const { navigation, uniquePromosError } = this.props;
    // apply UP failed
    if (compact(uniquePromosError).length > 0) {
      navigation.navigate('ModalUniquePromoConditionNotMeet', { needToChooseAnotherPromo: true });

      return false;
    }

    return true;
  };

  onCheckOutHandler = () => {
    const {
      assignTableID,
      autoOrderId,
      transactionSession,
      navigation,
      actions: { updateTransactionTableIdAndPax, saveOnlineOrder, unlockPayLaterOrder },
      enablePax,
    } = this.props;
    const { customer } = this.props;
    const { display, loyaltyDiscounts, isOnlineOrder, tableId, isPreview = false } = transactionSession;
    const total = get(display, 'total');
    let loyaltyDiscount = 0;
    if (Boolean(loyaltyDiscounts) && loyaltyDiscounts.length > 0) {
      loyaltyDiscount = get(loyaltyDiscounts[0], 'spentValue');
    }
    if (total < 0 || loyaltyDiscount < 0) {
      Alert.alert(
        t('Your current order transaction cannot be checked out as it has a negative value'),
        '',
        [
          {
            text: t('OK'),
            onPress: () => null,
          },
        ],
        {
          cancelable: false,
        }
      );
      return;
    }

    const pass = this.checkUniquePmotionsError();
    if (!pass) return;

    if (isOnlineOrder && !isEmpty(tableId)) {
      // if edited then need save the beep order first
      // else go checkout directly
      if (isPreview) {
        const onSuccess = {
          callback: () => {
            navigation.navigate('Checkout', { onGoBackToRefresh: unlockPayLaterOrder });
          },
        };
        const onFailed = {
          callback: this.warningToReSave,
        };
        saveOnlineOrder({ goCheckOutAfterSave: true, onSuccess, onFailed });
      } else {
        navigation.navigate('Checkout');
      }
    } else if (transactionSession.transactionType === TransactionFlowType.PreOrder) {
      if (Boolean(customer)) {
        navigation.navigate('ModalNewPreOrder');
      } else {
        navigation.navigate('ModalInfo', {
          info: t('To process a pre-order transaction, a customer must be selected'),
          needGoBackWhenSubmit: false,
          onSubmitHandler: (_, nav) => {
            this.onReplaceToAddCustomer(nav);
          },
        });
      }
    } else if (assignTableID && !autoOrderId && transactionSession.salesChannel !== SalesChannelType.TAKEAWAY) {
      const currentPax = get(transactionSession, 'pax', '');
      const currentTableId = get(transactionSession, 'tableId', '');
      if (currentTableId) {
        navigation.navigate('Checkout', { tableId: currentTableId, pax: currentPax });
      } else {
        if (enablePax) {
          navigation.navigate('ModalPaxAndTable', {
            shouldGoBackWhenConfirm: false,
            transactionId: transactionSession.tableId,
            onSubmitHandler: data => {
              const { pax, tableId } = data;
              updateTransactionTableIdAndPax({ tableId, pax });
              navigation.navigate('Checkout', { tableId, pax });
            },
          });
        } else {
          navigation.navigate('ModalTableId', {
            onSubmitHandler: tableId => {
              updateTransactionTableIdAndPax({ tableId });
              navigation.navigate('Checkout', { tableId });
            },
            transactionId: transactionSession.transactionId,
          });
        }
      }
    } else {
      navigation.navigate('Checkout');
    }
  };

  onSubmitHandler = () => {
    const {
      shiftOpenStatus,
      transactionSession,
      actions: { checkLimitBeforeExecute },
    } = this.props;

    if (!shiftOpenStatus) {
      this.requestOpenOrCloseShift();
    } else {
      const onComplete = (result: MRSError) => {
        if (onMRSInterceptor(result)) {
          this.onCheckOutHandler();
        }
      };
      checkLimitBeforeExecute({
        transaction: transactionSession,
        orderOperation: OrderOperationEnum.CheckOut,
        onComplete,
      });
    }
  };

  warningToReSave = ({ title, message }) => {
    const {
      actions: { unlockPayLaterOrder },
    } = this.props;
    requestAnimationFrame(() => {
      this.props.navigation.navigate('ModalInfo', {
        title: title || t('Pay Later Save order error'),
        isShowTitle: true,
        textAlign: 'center',
        info: message || t('Please try again'),
        okText: t('OK'),
        onSubmitHandler: unlockPayLaterOrder,
      });
    });
  };

  requestOpenOrCloseShift = () => {
    const { shiftOpenStatus, currentEmployeeId } = this.props;

    if (shiftOpenStatus || !this.props.insufficientStorageWarning.enabled) {
      this.props.actions.requestAuthorizedAction({
        name: CashierActions.OpenCloseShift,
        type: AuthorizationType.Cashier,
        employeeId: currentEmployeeId,
        onSuccess: {
          callback: () => {
            this.props.navigation.navigate('ModalShiftChange');
          },
        },
      });
    } else {
      this.props.actions.checkFreeStorage({
        navigation: this.props.navigation,
        onContinue: () => {
          this.props.actions.requestAuthorizedAction({
            name: CashierActions.OpenCloseShift,
            type: AuthorizationType.Cashier,
            employeeId: currentEmployeeId,
            onSuccess: {
              callback: () => {
                this.props.navigation.navigate('ModalShiftChange');
              },
            },
          });
        },
      });
    }
  };

  checkFullBillDiscountEnabled = items => {
    const itemsLength = get(items, 'length', 0);
    let enabled = true;
    let _taxRate;
    if (itemsLength === 0) {
      return false;
    }

    for (let i = 0; i < itemsLength; i++) {
      const item = items[i];
      const itemType = get(item, 'itemType');
      const taxCode = get(items, 'taxCode', 0);
      if (Boolean(itemType)) {
        break;
      }
      if (_taxRate === undefined) {
        _taxRate = taxCode;
      } else {
        if (_taxRate !== taxCode) {
          enabled = false;
          break;
        }
      }
    }

    return enabled;
  };

  onFullBillDiscountAndServiceChargeHandler = () => {
    const { transactionSession, serviceChargeRate, serviceChargeTax } = this.props;
    const { items, isOnlineOrder } = transactionSession;
    const discountIndex = findIndex(items, item => item.itemType === 'Discount');
    const inputValue = isOnlineOrder ? get(items, [discountIndex, 'discountValue']) : get(items, [discountIndex, 'displayFullBillDiscountValue']);
    const type = isOnlineOrder ? get(items, [discountIndex, 'discountType']) : get(items, [discountIndex, 'type']);
    const serviceChargeIndex = findIndex(items, item => item.itemType === 'ServiceCharge');
    const rate = get(items, [serviceChargeIndex, 'rate'], undefined);
    const enabledServiceCharge = rate !== 0;
    const isDiscountEnable = get(items, [discountIndex, 'isDiscountEnable'], true);
    if (this.checkFullBillDiscountEnabled(items)) {
      this.props.navigation.navigate('ModalFullBillDiscount', {
        inputValue,
        type,
        enabledServiceCharge,
        serviceChargeRate,
        serviceChargeTax,
        isDiscountEnable,
        onBIRDiscountConfirmHandler: this.onBIRDiscountConfirmHandler,
        onBIRDiscountRemoveHandler: this.onBIRDiscountRemoveHandler,
      });
    }
  };

  requestCashierDeleteActionWrapper = (onSuccess?: RequestActionAccessCallback, onFailure?: RequestActionAccessCallback) => {
    const {
      currentEmployeeId,
      transactionSession,
      actions: { requestAuthorizedAction },
    } = this.props;
    const isOpen = Boolean(transactionSession.isOpen);
    requestAuthorizedAction({
      employeeId: currentEmployeeId,
      type: AuthorizationType.Cashier,
      name: CashierActions.DeleteOrder,
      content: { isNew: !isOpen, isSaved: isOpen },
      onSuccess,
      onFailure,
    });
  };

  onDeletePurchasedItemHandler = (index, itemId) => {
    const onSuccess = {
      callback: () => {
        const { transactionSession } = this.props;
        let justDelete = true;
        if (transactionSession && transactionSession.isOnlineOrder) {
          const items = filter(transactionSession.items, item => isEmpty(item.itemType));
          justDelete = items && items.length > 1;
        }
        if (justDelete) {
          this.props.actions.deletePurchasedItem({ itemIndex: index, itemId });
        } else {
          this.onPressDeleteOrder();
        }
      },
    };
    this.requestCashierDeleteActionWrapper(onSuccess);
  };

  onModalCloseHandler = () => {
    this.setState({ selectedItemIndex: -1 });
  };

  onSelectedPurchasedItem = (index: number, itemId?: string) => {
    this.props.navigation.navigate('ModalProductCountSelector', {
      itemId,
      itemIndex: index,
      onClose: this.onModalCloseHandler,
      onCancelOrder: this.onPressDeleteOrder,
    });
    this.setState({ selectedItemIndex: index });
  };

  onBIRTaxRateDisableCallBack = ({ enable, message }) => {
    if (!enable) {
      this.props.navigation.navigate('ModalInfo', { info: message || t('Due to BIR requirements') });
    }
  };

  onAddProductToCart = (product: ProductType) => {
    const {
      shiftOpenStatus,
      transactionSession: { isOnlineOrder },
    } = this.props;

    if (!shiftOpenStatus || !Boolean(product)) {
      return;
    }
    if (isOnlineOrder) {
      this.onAddProductToOnlineOrder(product);
    } else {
      this.onAddProductToOfflineOrder(product);
    }
  };

  onAddProductToOfflineOrder = (product: ProductType) => {
    const {
      actions: { addPurchasedItem },
    } = this.props;
    const { priceType, variationsJson, isSerialized } = product;
    const variations = JSONUtils.parse(variationsJson, []);
    if (get(variations, 'length', 0) > 0) {
      this.props.navigation.navigate('ModalProductOptionsSelector', { product });
    } else if (priceType === 'weight') {
      // Unit Price
      this.props.navigation.navigate('ModalProductVariableWeight', { product });
    } else if (priceType === 'variable') {
      this.props.navigation.navigate('ModalProductVariablePrice', { product });
    } else if (isSerialized) {
      this.props.navigation.navigate('ModalSerialNumberSelector', { product });
    } else {
      addPurchasedItem({
        productId: product.productId,
        quantity: 1,
        onBIRTaxRateDisableCallBack: this.onBIRTaxRateDisableCallBack,
      });
    }
  };

  onAddProductToOnlineOrder = (product: ProductType) => {
    const {
      actions: { addPurchasedItem },
    } = this.props;
    const { priceType, variationsJson, isSerialized } = product;
    const variations = JSONUtils.parse(variationsJson, []);
    if (['weight', 'variable'].includes(priceType) || isSerialized) {
      this.warningToCantAdded();
    } else if (get(variations, 'length', 0) > 0) {
      this.props.navigation.navigate('ModalProductOptionsSelector', { product });
    } else {
      addPurchasedItem({
        productId: product.productId,
        quantity: 1,
        onBIRTaxRateDisableCallBack: this.onBIRTaxRateDisableCallBack,
      });
    }
  };

  warningToCantAdded = () => {
    requestAnimationFrame(() => {
      this.props.navigation.navigate('ModalInfo', {
        title: t('Item Cannot be Added'),
        isShowTitle: true,
        textAlign: 'center',
        info: t('Please try adding another item'),
        okText: t('OK'),
        onSubmitHandler: () => null,
      });
    });
  };

  onProductLongClick = productId => {
    const { currentEmployeeId } = this.props;
    // Align with iOS, where doesn't check employee permissions.
    this.props.navigation.navigate('ModalProductDetail', {
      productId,
      currentEmployeeId,
      onAddProductToCart: this.onAddProductToCart,
    });
  };

  renderIPadLayout = (containerWidth, containerHeight) => {
    const { quickLayout } = this.props;
    return (
      <SimpleIPadLayout
        quickLayout={quickLayout}
        containerHeight={containerHeight}
        containerWidth={containerWidth}
        onProductLongClick={this.onProductLongClick}
        onAddProductToCart={this.onAddProductToCart}
      />
    );
  };

  onSearchResultItemHandler = item => {
    requestAnimationFrame(() => this.onAddProductToCart(item));
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  renderSearchContent = () => {
    const { search } = this.state;
    return <NewSearchProductResult search={search} onProductItemHandler={this.onSearchResultItemHandler} onProductItemLongPress={this.onProductLongClick} />;
  };

  onPressGoSaveOrder = () => {
    const {
      enableTableLayout,
      tableLayoutEnabled,
      transactionSession: { transactionId },
    } = this.props;
    if (!(tableLayoutEnabled && enableTableLayout)) {
      this.props.navigation.navigate('ModalTableId', {
        onSubmitHandler: tableId => {
          this.props.actions.updateTransactionTableIdAndPax({ tableId });
        },
        value: this.props.transactionSession.tableId,
        transactionId,
        needGoBack: true,
        title: t('Save Order'),
      });
    }
  };

  onPressTakeAway = () => {
    this.showDialog();
  };

  renderMoreAction = () => {
    const { transactionSession } = this.props;
    const moreActionList = [
      {
        name: t('Dine-In'),
        icon: null,
        onClick: this.onDineInClick,
        isShowBackGroundClolr: transactionSession.salesChannel !== SalesChannelType.TAKEAWAY ? true : false,
        isSelectedText: transactionSession.salesChannel !== SalesChannelType.TAKEAWAY ? true : false,
      },
      {
        name: t('Takeaway'),
        icon: null,
        onClick: this.onTakeAwayClick,
        isShowBackGroundClolr: transactionSession.salesChannel === SalesChannelType.TAKEAWAY ? true : false,
        isSelectedText: transactionSession.salesChannel === SalesChannelType.TAKEAWAY ? true : false,
      },
    ];
    return <ModalMoreAction ref={this.setDialogRef} list={moreActionList} width={CART_WIDTH - scaleSizeW(30)} />;
  };

  setDialogRef = ref => {
    this._moreDialog = ref;
  };

  hideDialog = () => {
    this._moreDialog.dismiss();
  };

  showDialog = () => {
    this._moreDialog.show();
  };

  onDineInClick = () => {
    this.props.actions.setTakeAwayToTransaction({ salesChannel: SalesChannelType.DINE_IN });
    this.hideDialog();
  };

  onTakeAwayClick = () => {
    this.props.actions.setTakeAwayToTransaction({ salesChannel: SalesChannelType.TAKEAWAY });
    this.hideDialog();
  };

  onCloseShiftHandler = () => {
    this.requestOpenOrCloseShift();
    this.hideMenu();
  };

  onMenuPressSaveOrder = () => {
    this.hideMenu();
    this.onPressSaveOrder();
  };

  onSpecialDiscountPress = () => {
    const { transactionSession } = this.props;
    if (hasProductAmusementTax(transactionSession)) {
      this.hideMenu();
      this.props.navigation.navigate('ModalInfo', { info: 'Cannot apply special discount while product with Amusement Tax is in the cart.' });
      return;
    }
    this.hideMenu();
    this.props.navigation.navigate('AddBIRDiscount', {
      onBIRDiscountConfirmHandler: this.onBIRDiscountConfirmHandler,
    });
  };

  onSelectTargetTable = newTableId => {
    this.props.actions.changeOpenOrderTable({ tableId: newTableId });
  };

  onPressMoveOrder = () => {
    const {
      transactionSession: { tableId, transactionId },
    } = this.props;
    const selectedOpenOrdersMap: TableSelectedOrdersMap = {};
    selectedOpenOrdersMap[tableId] = [{ id: transactionId }];
    this.props.navigation.navigate('SelectTable', {
      onSelectTargetTable: this.onSelectTargetTable,
      selectedOpenOrdersMap,
    });
  };

  onBIRDiscountConfirmHandler = birInfo => {
    this.props.actions.addBIRDiscountToTransaction(birInfo);
  };

  onBIRDiscountRemoveHandler = () => {
    this.props.actions.removeBIRDiscount();
  };

  renderTabBar = () => <View />;

  renderLayoutContent = () => {
    const { searching } = this.state;

    return (
      <View
        style={{ flex: 1 }}
        onLayout={event => {
          if (IsAndroid && event) {
            const { height } = event.nativeEvent.layout;
            this._containerHeight = height - TAB_BAR_HEIGHT;
          }
        }}
      >
        <ScrollableTabView
          locked
          scrollWithoutAnimation
          contentProps={{ keyboardShouldPersistTaps: 'always', keyboardDismissMode: 'none' }}
          style={carouselTabStyles.scrollableTabView}
          initialPage={0}
          page={searching ? 1 : 0}
          renderTabBar={this.renderTabBar}
        >
          {this.renderIPadLayout(this._containerWidth, this._containerHeight)}
          {this.renderSearchContent()}
        </ScrollableTabView>
      </View>
    );
  };

  renderCoverView = () => {
    return <View style={styles.coverView} />;
  };

  onFixPress = () => {
    navigateToSettings({ tabName: AllSettingTabs.DefaultNetwork, timeStamp: Date.now() });
  };

  render() {
    const {
      transactionSession,
      shiftOpenStatus,
      currency,
      enableServiceCharge,
      serviceChargeRate,
      serviceChargeTax,
      enableOpenOrders,
      enableOpenOrdersShortCut,
      enableCustomerShortCut,
      customer,
      birAccredited,
      country,
      isBeepEnabled,
      isBeepPreOrderEnabled,
      enableTakeaway,
      enableTableLayout,
      tableLayoutEnabled,
      enableCashback,
      enableLoyalty,
      isEnabledMRS,
      lastZReadingCloseTime,
      operationHours,
      navigation,
      localCountryMap,
      makeStoreCreditAsPayment,
    } = this.props;
    const { selectedItemIndex } = this.state;
    const { items, isOnlineOrder = false, isOpen, isPreview, customerId } = transactionSession;
    const totalCount = getDisplayItemsCount(items);
    const isEmpty = totalCount <= 0;
    const alreadyCloseZReading = getAlreadyCloseZReading(lastZReadingCloseTime, operationHours);
    let haveProduct = !isEmpty;
    if (!haveProduct) {
      haveProduct = DAL.haveProduct();
    }
    const shouldRenderDeposit = isPreOrderPickUp(transactionSession);
    const customerDisabled = isOnlineOrder && !Boolean(customerId);
    const isCustomerEditAble = !isOnlineOrder;
    return (
      <View style={styles.container}>
        <StatusBar backgroundColor='transparent' barStyle='dark-content' />
        <View style={{ width: this._containerWidth }}>
          {this.renderSearchBar(haveProduct, isOnlineOrder, alreadyCloseZReading)}
          {transactionSession.totalOutOfStockItemsCount === INVALID_OOS_STATUS_BANNER && <StockLevelBanner></StockLevelBanner>}
          {transactionSession.totalOutOfStockItemsCount !== INVALID_OOS_STATUS_BANNER && <NewWifiBanner onFixPress={this.onFixPress} />}
          <PauseBeepNotification containerStyle={{ paddingLeft: scaleSizeW(14) }} />
          {this.renderLayoutContent()}
        </View>
        {enableTakeaway && this.renderMoreAction()}
        <CartContent
          disableCustomerEdit={!isCustomerEditAble}
          alreadyCloseZReading={alreadyCloseZReading}
          isEnabledMRS={isEnabledMRS}
          displayDiscount={true}
          onPressMoveOrder={this.onPressMoveOrder}
          displayLoyalty={!isPreview}
          customerDisabled={customerDisabled}
          onRemoveCustomerClick={this.onUnlinkCustomer}
          country={country}
          birAccredited={birAccredited}
          enableTableLayout={tableLayoutEnabled && enableTableLayout}
          shiftOpenStatus={shiftOpenStatus}
          transactionSession={transactionSession}
          currency={currency}
          enableServiceCharge={transactionSession.salesChannel === SalesChannelType.TAKEAWAY ? false : enableServiceCharge}
          serviceChargeRate={serviceChargeRate}
          serviceChargeTax={serviceChargeTax}
          cartHeight={this._cartHeight}
          selectedItemIndex={selectedItemIndex}
          onFullBillDiscountPressHandler={this.onFullBillDiscountAndServiceChargeHandler}
          onDeletePurchasedItemHandler={this.onDeletePurchasedItemHandler}
          onServiceChargePressHandler={this.onFullBillDiscountAndServiceChargeHandler}
          onSelectedPurchasedItem={this.onSelectedPurchasedItem}
          onSubmitHandler={this.onSubmitHandler}
          enableOpenOrdersShortCut={enableOpenOrdersShortCut}
          enableCustomerShortCut={enableCustomerShortCut}
          enableTakeaway={enableTakeaway}
          onSaveClicked={this.saveOpenOrderShortCut}
          onOrdersClicked={this.goToOpenOrderLists}
          onAddCustomerClick={this.onCustomerClick}
          isEmpty={isEmpty}
          isOpenOrder={isOpen || isOnlineOrder}
          isBeepEnabled={isBeepEnabled}
          isBeepPreOrderEnabled={isBeepPreOrderEnabled}
          customer={customer}
          goToOpenOrderLists={this.goToOpenOrderLists}
          onPressDeleteOrder={this.onPressDeleteOrder}
          onPressGoSaveOrder={this.onPressGoSaveOrder}
          onPressTakeAway={this.onPressTakeAway}
          onPressNewOrder={this.onPressNewOrder}
          onPressPrint={this.onPressPrint}
          onPrintOpenOrder={this.onPrintOpenOrder}
          onPressPrintTodayPreOrder={this.onPressPrintTodayBeepPreOrder}
          onMenuCustomerClick={this.onMenuCustomerClick}
          onManualRefundHandler={this.onManualRefundHandler}
          onMenuPressSaveOrder={this.onMenuPressSaveOrder}
          onCloseShiftHandler={this.onCloseShiftHandler}
          haveProduct={haveProduct}
          editing={false}
          enableOpenOrders={enableOpenOrders}
          setMenuRef={this.setMenuRef}
          showMenu={this.showMenu}
          onPressPreOrder={this.onPressPreOrder}
          onPressCollectPreOrder={this.onPressCollectPreOrder}
          onSpecialDiscountPressHandler={this.onSpecialDiscountPress}
          shouldRenderDeposit={shouldRenderDeposit}
          enableCashback={enableCashback}
          enableLoyalty={enableLoyalty}
          navigation={navigation}
          localCountryMap={localCountryMap}
          makeStoreCreditAsPayment={makeStoreCreditAsPayment}
        />
        <InitLearnInterceptor />
      </View>
    );
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(Register);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: '#F5F6FA',
  },
  searchBarContainerSearching: {
    paddingTop: STATUS_BAR_HEIGHT,
    height: SEARCH_BAR_HEIGHT,
    paddingHorizontal: NAVIGATOR_PADDING_HORIZONTAL,
    backgroundColor: 'white',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    ...Platform.select({
      ios: {
        shadowColor: '#000000',
        shadowOffset: { width: 0, height: 1 },
        shadowRadius: 1,
        shadowOpacity: 0.1,
        zIndex: 100,
      },
      android: {
        elevation: 1,
      },
    }),
  },
  searchBarContainer: {
    paddingTop: STATUS_BAR_HEIGHT,
    height: SEARCH_BAR_HEIGHT,
    paddingLeft: NAVIGATOR_PADDING_HORIZONTAL,
    paddingRight: scaleSizeW(12),
    backgroundColor: 'white',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    elevation: 1,
    shadowColor: 'black',
    shadowOffset: { height: 3, width: 4 },
    shadowOpacity: 0.1,
  },
  leftBtnsContainer: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  openOrderCreateTimeText: {
    color: '#303030',
    fontSize: currentThemes.fontSize24,
    includeFontPadding: false,
    lineHeight: scaleSizeH(38),
    marginLeft: scaleSizeW(12),
  },
  coverView: {
    position: 'absolute',
    backgroundColor: 'rgba(255,255,255,0.65)',
    top: SEARCH_BAR_HEIGHT + scaleSizeW(23),
    left: scaleSizeW(24),
    right: scaleSizeW(28),
    bottom: TAB_BAR_HEIGHT + scaleSizeW(31),
    borderWidth: 0,
  },
});
