import * as Immutable from 'immutable';
import React, { PureComponent } from 'react';
import { <PERSON><PERSON>, BackHandler, Platform, StatusBar, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import ScrollableTabView from 'react-native-scrollable-tab-view';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { createSelector } from 'reselect';
import {
  DeleteProductFromQuickLayoutBeginType,
  UpdateProductQuickLayoutInfoBeginType,
  deleteProductFromQuickLayoutBegin,
  updateProductQuickLayoutInfoBegin,
} from '../../actions';
import { Loading, SubmitButton } from '../../components/common';
import { NAVIGATOR_HEADER_HEIGHT, NAVIGATOR_PADDING_HORIZONTAL } from '../../components/common/NavigatorHeader';
import { ModalLoading } from '../../components/modal/ModalLoading';

import IPadLayout, { carouselTabStyles } from '../../components/register/IPadLayout';

import InitLearnInterceptor from '../../components/mrs/InitLearnInterceptor';
import PauseBeepNotification from '../../components/settings/beep/PauseBeepNotification';
import { IconEditLayout, MaterialIcons } from '../../components/ui';
import { CART_WIDTH, IsAndroid, STATUS_BAR_HEIGHT, SharedStyles, currentThemes, t, width } from '../../constants';
import { CommonColors, scaleSizeH, scaleSizeW } from '../../constants/themes';
import { ProductType } from '../../typings';
import { testProps } from '../../utils';
import { getParam } from '../../utils/navigation';
interface Props {
  // Redux
  navigation: any;
  quickLayout?: any[];
  syncInfo?: any;
  actions: {
    deleteProductFromQuickLayoutBegin(payload: DeleteProductFromQuickLayoutBeginType): void;
    updateProductQuickLayoutInfoBegin(payload: UpdateProductQuickLayoutInfoBeginType): void;
  };
}

interface State {
  selectedItemIndex: number;
  search: string;
  searching: boolean;
  editing: boolean;
  didMount: boolean;
  modalVisible: boolean;
  loadingMaskIsVisible: boolean;
  loadingText: string;
  containerHeight: number;
}

const TAB_BAR_HEIGHT = NAVIGATOR_HEADER_HEIGHT;
const SEARCH_BAR_HEIGHT = NAVIGATOR_HEADER_HEIGHT;

const fromImmutableQuickLayout = createSelector(
  (state: Immutable.Map<string, any>) => {
    return state.getIn(['Storage', 'quickLayout'], Immutable.List());
  },
  immutableQuickLayout => {
    return immutableQuickLayout.toJS();
  }
);

const fromImmutableSyncInfo = createSelector(
  (state: Immutable.Map<string, any>) => state.getIn(['Storage', 'syncInfo'], Immutable.Map()),
  syncInfo => syncInfo.toJS()
);

const mapStateToProps = state => ({
  quickLayout: fromImmutableQuickLayout(state),
  syncInfo: fromImmutableSyncInfo(state),
});

const mapDispatchToProps = dispatch => ({
  actions: bindActionCreators(
    {
      deleteProductFromQuickLayoutBegin,
      updateProductQuickLayoutInfoBegin,
    },
    dispatch
  ),
});

export class RegisterLayoutEdit extends PureComponent<Props, State> {
  private initialPage = 0;

  private _unsubscribeFocusListener;
  private _unsubscribeBlurListener;
  private _containerWidth: number;
  private _backButtonListener: any;

  constructor(props) {
    super(props);
    this.state = {
      selectedItemIndex: -1,
      search: '',
      searching: false,
      editing: true,
      didMount: false,
      modalVisible: false,
      loadingMaskIsVisible: true,
      loadingText: 'loading...',
      containerHeight: null,
    };
    this._containerWidth = width - CART_WIDTH;
    this.initialPage = getParam(props, 'initialPage', 0);
  }

  componentWillUnmount() {
    Boolean(this._unsubscribeFocusListener) && this._unsubscribeFocusListener();
    Boolean(this._unsubscribeBlurListener) && this._unsubscribeBlurListener();
    this.onBlur();
  }

  componentDidMount() {
    this._unsubscribeFocusListener = this.props.navigation.addListener('focus', this.onFocus);
    this._unsubscribeBlurListener = this.props.navigation.addListener('blur', this.onBlur);
    requestAnimationFrame(() => this.setState({ didMount: true }));
  }

  onFocus = () => {
    if (IsAndroid) {
      this._backButtonListener = BackHandler.addEventListener('hardwareBackPress', () => {
        this.onPressEditDone();
        return true;
      });
    }
    return false;
  };

  onBlur = () => {
    this._backButtonListener && this._backButtonListener.remove();
    this._backButtonListener = null;
  };

  renderHeader = () => {
    return (
      <View style={styles.searchBarContainer}>
        <TouchableOpacity {...testProps('al_btn_151')} style={SharedStyles.touchableIconContainer} onPress={this.onPressEditDone}>
          <MaterialIcons name='arrow-back' size={scaleSizeW(36)} color='#9E9E9E' />
        </TouchableOpacity>
      </View>
    );
  };

  onAddProductToCart = (product: ProductType) => {
    // NOP.
  };

  onTabEditHandler = (page: number, name: string) => {
    this.props.navigation.navigate('AddOrEditTab', { categoryIndex: page, name });
  };

  onProductLongClick = productId => {
    // NOP
  };

  renderIPadLayout = (containerWidth, containerHeight) => {
    const { quickLayout } = this.props;
    return (
      <IPadLayout
        initialPage={this.initialPage}
        quickLayout={quickLayout}
        isEditing={this.state.editing}
        isNewProductLayout={false}
        onTabEdit={this.onTabEditHandler}
        containerHeight={containerHeight}
        containerWidth={containerWidth}
        onProductLongClick={this.onProductLongClick}
        onAddProductToCart={this.onAddProductToCart}
        onPressAddProductToTile={this.onPressAddProductToTile}
        onPressDeleteItem={this.onPressDeleteItem}
        onUpdateProductPositionInfo={this.onUpdateProductPositionInfoHandler}
        onModifyProductBackgroundColor={this.onModifyProductBackgroundColor}
        itemWidth={0}
        itemHeight={0}
      />
    );
  };

  onUpdateProductPositionInfoHandler = ({
    categoryIndex,
    row,
    column,
    productId,
    backgroundColor,
    onFailure,
    onSuccess,
  }: UpdateProductQuickLayoutInfoBeginType) => {
    this.props.actions.updateProductQuickLayoutInfoBegin({ productId, categoryIndex, column, row, backgroundColor, onFailure, onSuccess });
  };

  onModifyProductBackgroundColor = ({ categoryIndex, row, column, productId, backgroundColor }: UpdateProductQuickLayoutInfoBeginType) => {
    const onSubmitHandler = backgroundColor => this.props.actions.updateProductQuickLayoutInfoBegin({ productId, categoryIndex, column, row, backgroundColor });
    this.props.navigation.navigate('ModalColourPicker', { onSubmitHandler, backgroundColor });
  };

  onPressDeleteItem = (categoryIndex, productId) => {
    Alert.alert(
      t('Are you sure you want to delete the product from the layout'),
      '',
      [
        {
          text: t('OK'),
          onPress: () => {
            this.props.actions.deleteProductFromQuickLayoutBegin({ categoryIndex, productId });
          },
        },
        {
          text: t('Cancel'),
          onPress: () => {
            // NOP
          },
        },
      ],
      {
        cancelable: false,
      }
    );
  };

  onPressAddProductToTile = (row, column, categoryIndex) => {
    this.props.navigation.navigate('AddProductToTile', {
      row,
      column,
      categoryIndex,
    });
  };

  onPressEditDone = () => {
    this.props.navigation.goBack();
  };

  renderTabBar = () => <View />;

  renderLayoutContent = () => {
    const { didMount, searching, containerHeight, loadingMaskIsVisible } = this.state;
    return (
      <View
        style={{ flex: 1, alignContent: 'center', justifyContent: 'center' }}
        onLayout={event => {
          if (event) {
            const { height } = event.nativeEvent.layout;
            this.setState({ containerHeight: height - TAB_BAR_HEIGHT, loadingMaskIsVisible: false });
          }
        }}
      >
        {containerHeight && (
          <ScrollableTabView
            locked
            scrollWithoutAnimation
            contentProps={{ keyboardShouldPersistTaps: 'always', keyboardDismissMode: 'none' }}
            style={carouselTabStyles.scrollableTabView}
            initialPage={0}
            page={searching ? 1 : 0}
            renderTabBar={this.renderTabBar}
            prerenderingSiblingsNumber={didMount ? 2 : 1}
          >
            {this.renderIPadLayout(this._containerWidth, containerHeight)}
          </ScrollableTabView>
        )}
        {loadingMaskIsVisible && <Loading loadingMaskIsVisible={true}></Loading>}
      </View>
    );
  };

  renderCoverView = () => {
    return <View style={styles.coverView} />;
  };

  renderEditModeCart = () => {
    return (
      <View style={styles.cart}>
        <View style={styles.cartHeader} />
        <View style={[styles.cartContainer, { flex: 1 }]}>
          <View style={styles.editModeContainer}>
            <IconEditLayout width={scaleSizeW(360)} height={scaleSizeH(240)} color={CommonColors.Icon} />
            <Text
              style={{ marginTop: scaleSizeW(30), fontWeight: 'bold', fontSize: currentThemes.fontSize32, marginRight: scaleSizeW(20), letterSpacing: 1.2 }}
            >
              {t('Edit Layout')}
            </Text>
            <Text style={styles.editModeDescriptionText}>{t('Drag to arrange your items on the grid')}</Text>
            <Text style={styles.editModeDescriptionText}>{t('Press and hold a tab to rename it')}</Text>
            <SubmitButton
              style={{
                flex: null,
                backgroundColor: '#FC7118',
                height: scaleSizeH(108),
                width: scaleSizeW(514),
                marginTop: scaleSizeH(160),
              }}
              onPress={this.onPressEditDone}
              textStyle={{ fontSize: currentThemes.mediumFontSize, fontWeight: 'bold' }}
            >
              {t('Done')}
            </SubmitButton>
          </View>
        </View>
      </View>
    );
  };

  render() {
    const { modalVisible, loadingText } = this.state;
    return (
      <View style={styles.container}>
        <StatusBar backgroundColor='transparent' barStyle='dark-content' />
        <View style={{ width: this._containerWidth }}>
          {this.renderHeader()}
          <PauseBeepNotification containerStyle={{ paddingLeft: scaleSizeW(14) }} />
          {this.renderLayoutContent()}
        </View>
        <ModalLoading visible={modalVisible} text={loadingText}></ModalLoading>
        <InitLearnInterceptor />
        {this.renderEditModeCart()}
      </View>
    );
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(RegisterLayoutEdit);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: '#F5F6FA',
  },
  searchBarContainer: {
    paddingTop: STATUS_BAR_HEIGHT,
    height: SEARCH_BAR_HEIGHT,
    paddingLeft: NAVIGATOR_PADDING_HORIZONTAL,
    paddingRight: scaleSizeW(12),
    backgroundColor: 'white',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    elevation: 1,
    shadowColor: 'black',
    shadowOffset: { height: 3, width: 4 },
    shadowOpacity: 0.1,
  },
  coverView: {
    position: 'absolute',
    backgroundColor: 'rgba(255,255,255,0.65)',
    top: SEARCH_BAR_HEIGHT + scaleSizeW(23),
    left: scaleSizeW(24),
    right: scaleSizeW(28),
    bottom: TAB_BAR_HEIGHT + scaleSizeW(31),
    borderRadius: 8,
    borderWidth: 0,
  },
  cart: {
    flex: 1,
    width: CART_WIDTH,
  },
  cartContainer: {
    width: '100%',
    backgroundColor: '#E1F6F9',
  },
  cartHeader: {
    paddingTop: STATUS_BAR_HEIGHT,
    height: SEARCH_BAR_HEIGHT,
    backgroundColor: '#FFF',
    ...Platform.select({
      ios: {
        shadowColor: 'black',
        shadowOffset: { width: 0.75, height: 1 },
        shadowRadius: 0.75,
        shadowOpacity: 0.1,
        zIndex: 50,
      },
      android: {
        elevation: 1,
      },
    }),
  },
  editModeDescriptionText: {
    marginTop: scaleSizeH(10),
    fontSize: currentThemes.fontSize24,
    width: scaleSizeW(474),
    lineHeight: scaleSizeH(36),
    textAlign: 'center',
  },
  editModeContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    backgroundColor: 'white',
    borderRadius: scaleSizeH(8),
    paddingTop: scaleSizeH(200),
  },
});
