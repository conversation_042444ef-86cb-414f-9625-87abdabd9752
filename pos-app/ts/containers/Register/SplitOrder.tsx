import { filter, get, head, isEmpty, isEqual, map, noop } from 'lodash';
import React, { memo, PureComponent } from 'react';
import { FlatList, StyleSheet, Text, TouchableOpacity, TouchableWithoutFeedback, View } from 'react-native';
import { connect, ConnectedProps } from 'react-redux';
import { bindActionCreators } from 'redux';
import {
  checkLimitBeforeExecute,
  clearSplitOrder,
  completeMergeSplitOpenOrder,
  editSplitOnlineOrder,
  editSplitOrder,
  MRSError,
  navigateToRegister,
  paySplitOnlineOrder,
  paySplitOrder,
  saveSplitOnlineOrder,
  saveSplitOrder,
  SaveSplitOrderResult,
  setOnlineOrderToSplitMaster,
  setOrderToSplitMaster,
  updateMasterOrderPax,
  updateSplitedOrderPax,
  updateSplitedOrderTableId,
} from '../../actions';
import { NavigatorHeader } from '../../components/common';
import { IconLeft, IconNotes, IconPen, IconPeople, IconPromotion, IconTrash } from '../../components/ui';
import {
  currentThemes,
  EditOnlineOrderError,
  EditOnlineOrderErrorMap,
  ForbiddenCheckOutError,
  IsIOS,
  ItemChannelType,
  scaleSizeH,
  scaleSizeW,
  SuccessCode,
  t,
  width,
} from '../../constants';
import {
  selectBusinessName,
  selectClientConnected,
  selectCurrency,
  selectEnableTableLayout,
  selectIPAddress,
  selectIsEnabledMRS,
  selectIsMaster,
  selectLocalCountryMap,
  selectSplitedOrder,
  selectSplitTargetOrder,
  selectStoreId,
  selectTableLayoutEnabled,
} from '../../sagas/selector';

import { createSelector } from 'reselect';
import { CommonColors, SharedStyles } from '../../constants/themes';
import DAL from '../../dal';
import { PromotionType, ScreenProps } from '../../typings';
import { localeNumber, newConvertCurrencyToSymbol, testProps } from '../../utils';
import { generatePromotionTitle } from '../../utils/promotion';
import { getSimplifiedStr } from '../../utils/string';
import { generateDescriptionString, getDisplayItemsCount } from '../../utils/transaction';

import moment from 'moment';
import { closeTopNotification, ContentType, openErrorToast, openSuccessToast } from '../../components/common/TopNotification';
import { ItemSalesPerson } from '../../components/register/ItemSalesPerson';
import { onMRSInterceptor } from '../../sagas/mrs/checkSync';
import { OrderOperationEnum } from '../../utils/logComponent';
import { TableSelectedOrdersMap } from '../TableLayout/TableLayout';
interface Props extends ScreenProps, PropsFromRedux {
  tableId?: string;
  localCountryMap: any;
}

interface State {
  isPaxWarning: boolean;
}

const selectIsClient = createSelector(selectIsEnabledMRS, selectIsMaster, (isEnabledMRS, isMaster) => {
  return isEnabledMRS && !isMaster;
});

const selectIsMRSOffline = createSelector(selectIsEnabledMRS, selectIPAddress, selectClientConnected, (isEnabledMRS, ip, isConnected) => {
  return isEnabledMRS && (!isConnected || !ip);
});

const mapStateToProps = state => ({
  businessName: selectBusinessName(state),
  storeId: selectStoreId(state),
  enableTableLayout: selectEnableTableLayout(state),
  tableLayoutEnabled: selectTableLayoutEnabled(state),
  splitMasterOrder: selectSplitTargetOrder(state),
  splitedOrder: selectSplitedOrder(state),
  currency: selectCurrency(state),
  isClient: selectIsClient(state),
  isMRSOffline: selectIsMRSOffline(state),
  localCountryMap: selectLocalCountryMap(state),
});

const mapDispatchToProps = dispatch => ({
  actions: bindActionCreators(
    {
      navigateToRegister,
      setOrderToSplitMaster,
      editSplitOrder,
      editSplitOnlineOrder,
      clearSplitOrder,
      saveSplitOrder,
      saveSplitOnlineOrder,
      updateSplitedOrderTableId,
      updateSplitedOrderPax,
      updateMasterOrderPax,
      paySplitOrder,
      paySplitOnlineOrder,
      completeMergeSplitOpenOrder,
      checkLimitBeforeExecute,
      setOnlineOrderToSplitMaster,
    },
    dispatch
  ),
});

const connector = connect(mapStateToProps, mapDispatchToProps);
type PropsFromRedux = ConnectedProps<typeof connector>;
export class SplitOrder extends PureComponent<Props, State> {
  static navigationOptions = () => ({
    headerShown: false,
  });

  constructor(props) {
    super(props);
    this.state = {
      isPaxWarning: false,
    };
  }

  goBack = () => {
    const {
      splitedOrder,
      navigation: { navigate },
    } = this.props;
    const isSplitedOrderEmpty = getDisplayItemsCount(get(splitedOrder, 'items', [])) === 0;
    if (isSplitedOrderEmpty) {
      this.onLeaveChange();
    } else {
      // pop up to warning: if leave change
      navigate('ModalInfo', {
        title: t('Leave Page'),
        isShowTitle: true,
        isCancelButtonRight: true,
        textAlign: 'center',
        info: t('Are you sure to leave without saving'),
        okText: t('YES LEAVE PAGE'),
        cancelText: t('CANCEL'),
        onCancelHandler: () => null,
        onSubmitHandler: this.onLeaveChange,
      });
    }
  };

  onLeaveChange = () => {
    const {
      actions: { clearSplitOrder },
      navigation: { goBack },
    } = this.props;
    clearSplitOrder();
    goBack();
  };

  onClickCancelSplitedOrder = () => {
    const {
      navigation: { navigate },
    } = this.props;
    navigate('ModalInfo', {
      title: t('Clear all items on this order'),
      isShowTitle: true,
      textAlign: 'center',
      info: t('Are you sure you want to revert the changes'),
      okText: t('CANCEL'),
      cancelText: t('CLEAR ITEMS'),
      onCancelHandler: this.onClearSplitedOrder,
      onSubmitHandler: () => null,
    });
  };

  onClearSplitedOrder = () => {
    const {
      splitMasterOrder: { isPayLater, receiptNumber, transactionId, tableId },
      actions: { setOrderToSplitMaster, setOnlineOrderToSplitMaster },
    } = this.props;
    if (isPayLater) {
      setOnlineOrderToSplitMaster({ receiptNumber, transactionId, tableId });
    } else {
      setOrderToSplitMaster({});
    }
  };

  onUpdateSplitedOrderTableId = tableId => {
    const {
      actions: { updateSplitedOrderTableId },
    } = this.props;
    updateSplitedOrderTableId(tableId);
  };

  onUpdateSplitedOrderPax = pax => {
    if (pax === '') {
      const { splitedOrder } = this.props;
      const pax = get(splitedOrder, 'pax', '');
      const isSplitedOrderHasPax = pax !== '';
      if (!isSplitedOrderHasPax) {
        this.setState({
          isPaxWarning: true,
        });
      }
    } else {
      const {
        actions: { updateSplitedOrderPax },
      } = this.props;
      updateSplitedOrderPax(pax);
    }
  };

  onClickSplitedOrderTableId = () => {
    const {
      splitedOrder: { tableId },
      splitMasterOrder: { transactionId, isPayLater },
      navigation: { navigate },
      enableTableLayout,
      tableLayoutEnabled,
    } = this.props;
    if (enableTableLayout && tableLayoutEnabled) {
      const selectedOpenOrdersMap: TableSelectedOrdersMap = {};
      selectedOpenOrdersMap[tableId] = [{ id: transactionId, isPayLater }];
      navigate('SelectTable', { onSelectTargetTable: this.onUpdateSplitedOrderTableId, selectedOpenOrdersMap });
    } else {
      navigate('ModalPureTableId', {
        title: isPayLater ? t('Fill in Table ID') : '',
        tableId,
        beepTable: true,
        onSubmitHandler: this.onUpdateSplitedOrderTableId,
      });
    }
  };

  onClickSplitedOrderPax = () => {
    const {
      splitedOrder,
      navigation: { navigate },
    } = this.props;
    const currentPax = get(splitedOrder, 'pax', '');
    navigate('ModalPax', {
      title: t('Number of Pax'),
      desc: t('Enter number of pax to proceed'),
      currentPax,
      shouldGoBackWhenConfirm: true,
      needCallBackWhenBack: true,
      onSubmitHandler: this.onUpdateSplitedOrderPax,
    });
  };

  onClickMasterOrderPax = () => {
    const {
      splitMasterOrder,
      navigation: { navigate },
      actions: { updateMasterOrderPax },
    } = this.props;
    const currentPax = get(splitMasterOrder, 'pax', '');
    navigate('ModalPax', {
      title: t('Number of Pax'),
      desc: t('Enter number of pax to proceed'),
      currentPax,
      shouldGoBackWhenConfirm: true,
      needCallBackWhenBack: false,
      onSubmitHandler: updateMasterOrderPax,
    });
  };

  render() {
    return (
      <View style={{ backgroundColor: '#E1F6F9', flex: 1 }}>
        {this.renderCommonHeader()}
        {this.renderSplitOrders()}
      </View>
    );
  }

  renderCommonHeader = () => {
    const leftIcons = [{ icon: <IconLeft color={'#FC7118'} width={scaleSizeW(48)} height={scaleSizeH(49)} />, onClick: this.goBack }];
    const rightButtons = [
      <TouchableOpacity {...testProps('al_btn_231')} key={'Save Order'} style={styles.moveOrderButtonContainer} onPress={this.onSaveClick}>
        <Text style={styles.moveOrderButtonText}>{t('Save Order')}</Text>
      </TouchableOpacity>,
    ];
    return <NavigatorHeader title={t('Split Order')} leftIcons={leftIcons} rightButtons={rightButtons} />;
  };

  masterKeyExtractor = (item, index) => {
    return item.id ? `master-${item.id}${index}` : item.productId ? `master-${item.productId}${index}` : `master-${index}`;
  };

  keyExtractor = (item, index) => {
    return item.id ? `${item.id}${index}` : item.productId ? `${item.productId}${index}` : String(index);
  };

  onMasterOrderItemClick = itemIndex => {
    const {
      actions: { editSplitOrder, editSplitOnlineOrder },
      splitMasterOrder,
      navigation,
    } = this.props;
    const isPayLater = get(splitMasterOrder, 'isPayLater', false);
    const items = filter(splitMasterOrder.items, v => v.itemType !== 'ServiceCharge');
    const isLastItem = items.length === 1 && items[0].quantity === 1;
    if (isLastItem) {
      navigation.navigate('ModalInfo', {
        isShowTitle: false,
        infoFn: () => (
          <View style={styles.noticeContent}>
            <Text style={styles.noticeText}>{t('Original order cannot be left empty')}</Text>
          </View>
        ),
        okText: t('OK'),
        textAlign: 'center',
        onSubmitHandler: noop,
        closeable: true,
      });
      return;
    }
    if (isPayLater) {
      editSplitOnlineOrder({ itemIndex, isPutBack: false });
    } else {
      editSplitOrder({ itemIndex, isPutBack: false });
    }
  };

  onSplitedOrderItemClick = itemIndex => {
    const {
      actions: { editSplitOrder, editSplitOnlineOrder },
      splitMasterOrder,
    } = this.props;
    const isPayLater = get(splitMasterOrder, 'isPayLater', false);

    if (isPayLater) {
      editSplitOnlineOrder({ itemIndex, isPutBack: true });
    } else {
      editSplitOrder({ itemIndex, isPutBack: true });
    }
  };

  renderMasterItem = ({ item, index }) => {
    const { currency, splitMasterOrder, localCountryMap } = this.props;
    const { itemType, itemChannel } = item;
    if (isEqual(itemType, 'ServiceCharge') || isEqual(itemType, 'Discount')) {
      return null;
    }
    const { takeawayCharge } = splitMasterOrder;
    const {
      title,
      quantity,
      options,
      promotions,
      display,
      notes,
      sn,
      isGroupFirst,
      isGroupLast,
      submitNotes,
      submittedFrom,
      submittedTime,
      isTakeaway,
      employeeName,
    } = item;
    const total = get(display, 'total');
    const subtotal = get(display, 'subtotal');
    const description = generateDescriptionString(options);
    return (
      <PurchasedItem
        title={title}
        isGroupFirst={isGroupFirst}
        isGroupLast={isGroupLast}
        submitNotes={submitNotes}
        description={description}
        quantity={quantity}
        notes={notes}
        salesPerson={employeeName}
        sn={sn}
        total={total}
        subtotal={subtotal}
        index={index}
        currency={currency}
        takeawayCharge={takeawayCharge || item.takeawayCharge || 0}
        promotions={promotions}
        isShowTakeaway={itemChannel === ItemChannelType.TAKEAWAY || Boolean(isTakeaway)}
        onItemClick={this.onMasterOrderItemClick}
        submittedFrom={submittedFrom}
        submittedTime={submittedTime}
        localCountryMap={localCountryMap}
      />
    );
  };

  renderItem = ({ item, index }) => {
    const { currency, splitMasterOrder, localCountryMap } = this.props;
    const { itemType, itemChannel } = item;

    if (isEqual(itemType, 'ServiceCharge') || isEqual(itemType, 'Discount')) {
      return null;
    }
    const { takeawayCharge } = splitMasterOrder;
    const {
      title,
      quantity,
      options,
      promotions,
      display,
      notes,
      sn,
      isGroupFirst,
      isGroupLast,
      submitNotes,
      submittedFrom,
      submittedTime,
      isTakeaway,
      employeeName,
    } = item;
    const total = get(display, 'total');
    const subtotal = get(display, 'subtotal');
    const description = generateDescriptionString(options);
    return (
      <PurchasedItem
        title={title}
        isGroupFirst={isGroupFirst}
        isGroupLast={isGroupLast}
        submitNotes={submitNotes}
        description={description}
        quantity={quantity}
        notes={notes}
        salesPerson={employeeName}
        sn={sn}
        total={total}
        subtotal={subtotal}
        index={index}
        currency={currency}
        takeawayCharge={takeawayCharge || item.takeawayCharge || 0}
        promotions={promotions}
        isShowTakeaway={itemChannel === ItemChannelType.TAKEAWAY || Boolean(isTakeaway)}
        onItemClick={this.onSplitedOrderItemClick}
        submittedFrom={submittedFrom}
        submittedTime={submittedTime}
        localCountryMap={localCountryMap}
      />
    );
  };

  goCheckoutPage = (withReplaceNavigation?: boolean) => {
    withReplaceNavigation
      ? this.props.navigation.replace('Checkout', { needClearTransactionWhenBack: true })
      : this.props.navigation.navigate('Checkout', { needClearTransactionWhenBack: true });
  };

  tiggerAction = (actionFunc, isPaySplitOrder) => {
    const {
      splitedOrder,
      enableTableLayout,
      tableLayoutEnabled,
      navigation: { navigate },
    } = this.props;
    const items = get(splitedOrder, 'items', []);
    const isSplitOrderEmpty = getDisplayItemsCount(items) <= 0;
    if (enableTableLayout && tableLayoutEnabled) {
      const pax = get(splitedOrder, 'pax', '');
      const isSplitedOrderHasPax = pax !== '';
      if (!isSplitedOrderHasPax && !isSplitOrderEmpty) {
        navigate('ModalPax', {
          title: t('Number of Pax'),
          desc: isPaySplitOrder ? t('Enter number of pax to proceed') : t('Enter number of pax for splitted order to proceed'),
          shouldGoBackWhenConfirm: true,
          needCallBackWhenBack: true,
          withNavigationWhenConfirm: !isPaySplitOrder,
          onSubmitHandler: pax => {
            this.onUpdateSplitedOrderPax(pax);
            if (pax !== '') {
              actionFunc(true);
            }
          },
        });
      } else {
        actionFunc();
      }
    } else {
      actionFunc();
    }
  };

  onPayMasterOrder = (withReplaceNavigation?: boolean) => {
    const {
      splitMasterOrder: { isPayLater },
      actions: { paySplitOrder, paySplitOnlineOrder },
    } = this.props;
    if (isPayLater) {
      paySplitOnlineOrder({
        isSplitMaster: true,
        onComplete: (success: boolean) => {
          if (success) {
            this.goCheckoutPage();
          } else {
            openErrorToast(t('Failed to split order'));
          }
        },
      });
    } else {
      paySplitOrder({ isSplitMaster: true });
      this.goCheckoutPage(withReplaceNavigation);
    }
  };

  replacePayOnlineSplittedOrder = () => {
    const {
      actions: { paySplitOnlineOrder },
      navigation,
    } = this.props;
    paySplitOnlineOrder({
      isSplitMaster: false,
      onComplete: (success: boolean) => {
        if (success) {
          navigation.replace('Checkout', { needClearTransactionWhenBack: true });
        } else {
          openErrorToast(t('Failed to split order'));
        }
      },
    });
  };

  onClickPayMasterOrder = async () => {
    const { splitMasterOrder, splitedOrder, navigation, isClient } = this.props;
    const isPayLater = splitMasterOrder.isPayLater;
    if (isPayLater) {
      if (isClient) {
        return onMRSInterceptor(ForbiddenCheckOutError);
      }
      const hasSplitItems = get(splitedOrder, 'items.length', 0) > 0;
      if (hasSplitItems) {
        navigation.navigate('ModalInfo', {
          title: t('Please pay for your split order first'),
          isShowTitle: true,
          needGoBackWhenCancel: true,
          needGoBackWhenSubmit: false,
          textAlign: 'center',
          info: t('In order to complete payment, you must pay for the split order first'),
          okText: t('Pay Split Order'),
          cancelText: t('CANCEL'),
          onSubmitHandler: this.replacePayOnlineSplittedOrder,
          onCancelHandler: noop,
        });
      } else {
        this.onPayMasterOrder();
      }
    } else {
      const checkResult = await this.checkCanProposer(OrderOperationEnum.SplitAndCheckOut, splitMasterOrder);
      if (checkResult) {
        this.tiggerAction(this.onPayMasterOrder, true);
      }
    }
  };

  onPaySplitedOrder = (withReplaceNavigation?: boolean) => {
    const {
      splitedOrder: { isPayLater },
      actions: { paySplitOrder, paySplitOnlineOrder },
    } = this.props;
    if (isPayLater) {
      paySplitOnlineOrder({
        isSplitMaster: false,
        onComplete: (success: boolean) => {
          if (success) {
            this.goCheckoutPage();
          } else {
            openErrorToast(t('Failed to split order'));
          }
        },
      });
    } else {
      paySplitOrder({ isSplitMaster: false });
      this.goCheckoutPage(withReplaceNavigation);
    }
  };

  onClickPaySplitedOrder = async () => {
    const { splitedOrder, isClient } = this.props;
    if (splitedOrder.isPayLater) {
      if (isClient) {
        onMRSInterceptor(ForbiddenCheckOutError);
      } else {
        this.onPaySplitedOrder();
      }
    } else {
      const checkResult = await this.checkCanProposer(OrderOperationEnum.SplitAndCheckOut, splitedOrder);
      if (checkResult) {
        this.tiggerAction(this.onPaySplitedOrder, true);
      }
    }
  };

  onSaveSplitOrder = () => {
    const {
      navigation: { goBack },
      actions: { saveSplitOrder },
    } = this.props;
    saveSplitOrder({
      onComplete: error => {
        if (error.errorCode === SuccessCode) {
          goBack();
        }
      },
    });
  };

  onSaveClick = async () => {
    const {
      splitedOrder,
      splitMasterOrder,
      navigation: { goBack },
      actions: { completeMergeSplitOpenOrder },
    } = this.props;
    const items = get(splitedOrder, 'items', []);
    const isSplitOrderEmpty = getDisplayItemsCount(items) <= 0;
    if (isSplitOrderEmpty) {
      completeMergeSplitOpenOrder();
      goBack();
    } else {
      const isPayLater = get(splitMasterOrder, 'isPayLater', false);
      if (isPayLater) {
        this.onSaveSplitOnlineOrder();
      } else {
        const checkResult = await this.checkCanProposer(OrderOperationEnum.SplitAndSave, splitMasterOrder);
        if (checkResult) {
          this.tiggerAction(this.onSaveSplitOrder, false);
        }
      }
    }
  };

  onOnlineSaveComplete = (result: SaveSplitOrderResult) => {
    const {
      enableTableLayout,
      tableLayoutEnabled,
      navigation: { navigate, goBack, replace },
      splitMasterOrder: { receiptNumber, modifiedDate, isPayLater },
      splitedOrder,
    } = this.props;
    const { success, needGoBack, tableCode, isCancelled, editableTimeAfterForPayLaterOrder } = result;
    if (!isEmpty(editableTimeAfterForPayLaterOrder)) {
      const displayTime = moment(editableTimeAfterForPayLaterOrder).format('hh:mmA');
      navigate('ModalInfo', {
        title: t('This order is being checked out by customer'),
        isShowTitle: true,
        textAlign: 'center',
        info: t('Order can be edited at', { editableTimeAfterForPayLaterOrder: displayTime }),
      });
    }
    if (needGoBack) {
      goBack();
    }

    if (!tableCode) {
      if (success) {
        openSuccessToast(t('Splited Successfully'));
      }
      closeTopNotification([ContentType.SplitErrorToast]);
    }
    if (isCancelled) {
      return;
    }
    if (tableCode) {
      const isTableLayout = enableTableLayout && tableLayoutEnabled;
      if (isTableLayout) {
        openErrorToast(EditOnlineOrderErrorMap.get(tableCode) || t('Failed to split order'), 0, ContentType.SplitErrorToast);
        const tableId = get(splitedOrder, 'tableId', '');
        const selectedOpenOrdersMap: TableSelectedOrdersMap = {};
        selectedOpenOrdersMap[tableId] = [
          {
            id: receiptNumber, // local transactionId, payLater receiptNumber
            isPayLater,
            modifiedTime: modifiedDate as unknown as string,
          },
        ];
        navigate('SelectTable', { onSelectTargetTable: this.onSubmitOnlineTableId, selectedOpenOrdersMap });
        return;
      }
      if (tableCode === EditOnlineOrderError.HAS_ORDER) {
        navigate('ModalInfo', {
          isShowTitle: true,
          title: t('Cannot be saved to this table'),
          info: t('1 table can only contain 1 QR Order'),
          okText: t('TRY ANOTHER TABLE ID'),
          textAlign: 'center',
          needGoBackWhenSubmit: false,
          onSubmitHandler: () => {
            replace('ModalPureTableId', {
              title: t('Fill in Table ID'),
              tableId: '',
              beepTable: true,
              onSubmitHandler: this.onSubmitOnlineTableId,
            });
          },
          closeable: true,
        });
      } else {
        openErrorToast(t('Failed to split order'));

        // openErrorToast(EditOnlineOrderErrorMap.get(tableCode));
      }
    } else if (!success) {
      openErrorToast(t('Failed to split order'));
    }
  };

  onSubmitOnlineTableId = (tableId?: string) => {
    const {
      actions: { saveSplitOnlineOrder },
    } = this.props;
    this.onUpdateSplitedOrderTableId(tableId);
    if (tableId) {
      saveSplitOnlineOrder({ onComplete: this.onOnlineSaveComplete });
    }
  };

  onSaveSplitOnlineOrder = () => {
    const {
      splitedOrder,
      enableTableLayout,
      tableLayoutEnabled,
      splitMasterOrder: { receiptNumber, modifiedDate, isPayLater },
      navigation: { navigate },
      actions: { saveSplitOnlineOrder },
    } = this.props;

    const tableId = get(splitedOrder, 'tableId', '');
    if (!tableId) {
      if (enableTableLayout && tableLayoutEnabled) {
        const selectedOpenOrdersMap: TableSelectedOrdersMap = {};
        selectedOpenOrdersMap[tableId] = [
          {
            id: receiptNumber, // local transactionId, payLater receiptNumber
            isPayLater,
            modifiedTime: modifiedDate as unknown as string,
          },
        ];
        navigate('SelectTable', { onSelectTargetTable: this.onSubmitOnlineTableId, selectedOpenOrdersMap });
      } else {
        navigate('ModalPureTableId', {
          title: t('Fill in Table ID'),
          tableId,
          beepTable: true,
          onSubmitHandler: this.onSubmitOnlineTableId,
        });
      }
    } else {
      saveSplitOnlineOrder({ onComplete: this.onOnlineSaveComplete });
    }
  };

  checkCanProposer = (orderOperation: OrderOperationEnum, transaction): Promise<boolean> => {
    return new Promise(res => {
      const onComplete = (error: MRSError) => {
        return res(onMRSInterceptor(error));
      };

      this.props.actions.checkLimitBeforeExecute({
        transaction,
        orderOperation,
        onComplete,
      });
    });
  };

  renderSplitMaster = () => {
    const { splitMasterOrder, splitedOrder, currency, enableTableLayout, tableLayoutEnabled, isClient, isMRSOffline, localCountryMap } = this.props;
    const currencySymbol = newConvertCurrencyToSymbol(localCountryMap, currency);
    const items = get(splitMasterOrder, 'items', []);
    const splitItems = get(splitedOrder, 'items', []);
    const tableId = get(splitMasterOrder, 'tableId', '');
    const isPayLater = get(splitMasterOrder, 'isPayLater', false);
    const pax = get(splitMasterOrder, 'pax', '');
    const payButtonDisabled = getDisplayItemsCount(items) <= 0;

    const totalAmount = payButtonDisabled ? 0 : get(splitMasterOrder, ['display', 'total'], 0);
    const payDisableColor = payButtonDisabled || isClient || (!isPayLater && isMRSOffline) || (isPayLater && splitItems.length > 0);
    const showPax = enableTableLayout && tableLayoutEnabled && !isPayLater;
    return (
      <View style={styles.orderContainer}>
        <Text style={styles.descText}>{t('Tap on items to split')}</Text>
        <View style={styles.tableAndPaxContainer}>
          <TouchableOpacity {...testProps('al_btn_335')} disabled={!showPax} style={styles.paxContainer} activeOpacity={1} onPress={this.onClickMasterOrderPax}>
            <View style={styles.paxContainer}>
              {showPax && (
                <>
                  <IconPeople width={scaleSizeW(36)} height={scaleSizeH(36)} color={'#60636B'} />
                  <Text testID='pax' style={styles.paxText}>
                    {String(pax)}
                  </Text>
                </>
              )}
            </View>
          </TouchableOpacity>
          <Text style={styles.tableIdText}>{tableId}</Text>
          <View style={styles.paxContainer} />
        </View>
        <FlatList data={items} keyExtractor={this.masterKeyExtractor} renderItem={this.renderMasterItem} windowSize={12} />
        <TouchableOpacity
          {...testProps('al_btn_682')}
          disabled={payButtonDisabled}
          activeOpacity={1}
          style={[styles.payButton, payDisableColor && { backgroundColor: '#DBDBDB' }]}
          onPress={this.onClickPayMasterOrder}
        >
          <Text style={styles.payText}>{`${t('PAY')} ${currencySymbol} ${localeNumber(totalAmount)}`}</Text>
        </TouchableOpacity>
      </View>
    );
  };

  renderSplitedOrder = () => {
    const { splitedOrder, splitMasterOrder, currency, localCountryMap, enableTableLayout, tableLayoutEnabled, isClient, isMRSOffline } = this.props;
    const currencySymbol = newConvertCurrencyToSymbol(localCountryMap, currency);
    const { isPaxWarning } = this.state;
    const items = get(splitedOrder, 'items', []);
    const tableId = get(splitedOrder, 'tableId', '');
    const pax = get(splitedOrder, 'pax', '');
    const totalAmount = get(splitedOrder, ['display', 'total'], 0);
    const payButtonDisabled = getDisplayItemsCount(items) <= 0;
    const desc = payButtonDisabled ? '' : t('Tap on items to put back');
    const isPayLater = get(splitMasterOrder, 'isPayLater', false);

    const title = payButtonDisabled
      ? t('No items')
      : tableId || (isPayLater ? (enableTableLayout && tableLayoutEnabled ? t('Select table') : t('Fill in Table ID')) : tableId);
    const canChangeTableId = !payButtonDisabled;
    const showPax = enableTableLayout && tableLayoutEnabled && !payButtonDisabled && !isPayLater;
    const isPaxEmpty = pax === '';
    const payDisableColor = payButtonDisabled || isClient || (!isPayLater && isMRSOffline);
    return (
      <View style={styles.orderContainer}>
        <View style={styles.descContainer}>
          <Text style={[styles.descText, payButtonDisabled && { backgroundColor: 'transparent' }]}>{desc}</Text>
          {!payButtonDisabled && (
            <TouchableOpacity {...testProps('al_btn_508')} style={styles.trashButton} activeOpacity={1} onPress={this.onClickCancelSplitedOrder}>
              <IconTrash width={scaleSizeW(41)} height={scaleSizeH(40)} color={'#60636B'} />
            </TouchableOpacity>
          )}
        </View>

        <View style={styles.tableAndPaxContainer}>
          <TouchableOpacity
            {...testProps('al_btn_257')}
            disabled={!showPax}
            style={styles.paxContainer}
            activeOpacity={1}
            onPress={this.onClickSplitedOrderPax}
          >
            <View style={styles.paxContainer}>
              {showPax && (
                <>
                  <IconPeople width={scaleSizeW(36)} height={scaleSizeH(36)} color={isPaxWarning ? '#EB4646' : '#60636B'} />
                  <Text style={[styles.paxText, isPaxEmpty && { color: '#9F9F9F' }, isPaxWarning && { color: '#EB4646' }]}>
                    {isPaxEmpty ? t('Pax') : String(pax)}
                  </Text>
                </>
              )}
            </View>
          </TouchableOpacity>
          {canChangeTableId ? (
            <TouchableOpacity {...testProps('al_btn_43')} activeOpacity={1} onPress={this.onClickSplitedOrderTableId}>
              <View style={styles.changeTableIdhButton}>
                <Text style={[styles.tableIdText, { marginRight: scaleSizeW(16) }, !tableId && isPayLater && { color: '#FC7118' }]}>{title}</Text>
                <IconPen />
              </View>
            </TouchableOpacity>
          ) : (
            <Text style={[styles.tableIdText, payButtonDisabled && { color: '#9F9F9F' }, isPayLater && !title && { color: '#FC7118' }]}>{title}</Text>
          )}
          <View style={styles.paxContainer} />
        </View>

        <FlatList data={items} keyExtractor={this.keyExtractor} renderItem={this.renderItem} windowSize={12} />
        <TouchableOpacity
          {...testProps('al_btn_994')}
          disabled={payButtonDisabled}
          activeOpacity={1}
          style={[styles.payButton, payDisableColor && { backgroundColor: '#DBDBDB' }]}
          onPress={this.onClickPaySplitedOrder}
        >
          <Text style={styles.payText}>{`${t('PAY')} ${currencySymbol} ${localeNumber(totalAmount)}`}</Text>
        </TouchableOpacity>
      </View>
    );
  };

  renderSplitOrders = () => {
    return (
      <View style={styles.container}>
        {this.renderSplitMaster()}
        {this.renderSplitedOrder()}
      </View>
    );
  };
}

export default connector(SplitOrder);

interface PurchasedItemProps {
  total: number;
  title: string;
  notes: string;
  sn: string;
  isGroupFirst?: boolean;
  isGroupLast?: boolean;
  submitNotes?: string;
  currency?: string;
  takeawayCharge?: number;
  isShowTakeaway: boolean;
  description: string;
  subtotal: number;
  quantity: number;
  promotions: any[];
  index: number;
  submittedFrom?: string;
  submittedTime?: string;
  onItemClick(index: number): void;
  localCountryMap: any;
  salesPerson?: string;
}

export class PurchasedItem extends PureComponent<PurchasedItemProps> {
  onItemClick = () => {
    const { onItemClick, index } = this.props;
    requestAnimationFrame(() => onItemClick(index));
  };

  render() {
    const {
      index,
      title,
      quantity,
      promotions,
      total,
      subtotal,
      notes,
      sn,
      isGroupFirst,
      isGroupLast,
      submitNotes,
      description,
      isShowTakeaway,
      currency,
      takeawayCharge,
      submittedTime,
      submittedFrom,
      localCountryMap,
      salesPerson,
    } = this.props;
    const showPromotionTag = get(promotions, 'length') > 0;
    const currencySymbol = newConvertCurrencyToSymbol(localCountryMap, currency);

    if (quantity === undefined) {
      return (
        <View style={styles.item}>
          {map(promotions, promotion => {
            const promotionId = get(promotion, 'promotionId');
            const realmPromotion: PromotionType = DAL.getPromotionById(promotionId);
            const title = generatePromotionTitle(realmPromotion, promotion);
            const discount = get(promotion, ['display', 'discount'], 0);
            return (
              <React.Fragment key={promotion.promotionId}>
                <PromotionTag key={`${promotionId}${index}`} promotionId={promotionId} title={title} discount={discount} />
              </React.Fragment>
            );
          })}
        </View>
      );
    }

    const discount = isNaN(subtotal - total) ? 0 : subtotal - total;

    let _notes;
    if (Boolean(notes)) {
      const strArray = notes.split('\n');
      const headStr = head(strArray);
      _notes = getSimplifiedStr(headStr, 140, true, true);
    }

    return (
      <>
        {isGroupFirst && (
          <Text style={[styles.groupTitle, index != 0 && styles.groupDivideLine]}>
            {submittedTime} | Placed by {submittedFrom}
          </Text>
        )}
        <View style={styles.item}>
          <TouchableWithoutFeedback {...testProps('al_btn_871')} onPress={this.onItemClick}>
            <View style={{ backgroundColor: 'white' }}>
              <View style={[styles.standaloneRowFront, SharedStyles.row, { justifyContent: 'space-between' }]}>
                <View style={[{ flexDirection: 'row', justifyContent: 'space-between', flex: 1 }]}>
                  <View style={{ width: ITEM_CONTENT_WIDTH * 0.68 }}>
                    <View style={[SharedStyles.row, { width: ITEM_CONTENT_WIDTH * 0.68 }]}>
                      <Text style={[styles.smallText, { minWidth: scaleSizeW(48), textAlign: 'right', paddingLeft: scaleSizeW(5), fontWeight: 'bold' }]}>
                        {quantity}
                      </Text>
                      <View style={styles.itemTitleContainer}>
                        <Text style={[styles.smallText, { marginRight: scaleSizeW(10) }]}>{title}</Text>
                        {showPromotionTag && <IconPromotion width={scaleSizeW(28)} height={scaleSizeW(30)} color={CommonColors.Icon} />}
                      </View>
                    </View>
                    {Boolean(description) && <Text style={[styles.tintText, { marginLeft: scaleSizeW(63) }]}>{description}</Text>}
                    {isShowTakeaway && (
                      <Text style={[styles.tintText, { paddingLeft: scaleSizeW(63), backgroundColor: 'white', color: '#FC7118' }]}>
                        {t('Takeaway') + ' - ' + currencySymbol + ' ' + takeawayCharge}
                      </Text>
                    )}
                    {Boolean(_notes) && (
                      <View style={styles.notesContainer}>
                        <IconNotes />
                        <Text style={[styles.noteText]}>{_notes}</Text>
                      </View>
                    )}
                    <ItemSalesPerson style={{ marginLeft: scaleSizeW(62) }} salesPerson={salesPerson} color='#757575' />
                  </View>
                  <View style={{ width: ITEM_CONTENT_WIDTH * 0.32 }}>
                    <Text style={[styles.smallText, { textAlign: 'right', fontWeight: '500', marginRight: scaleSizeW(24) }]}>{localeNumber(total)}</Text>
                    {Boolean(discount) && (
                      <Text style={[styles.tintText, { textAlign: 'right', textDecorationLine: 'line-through', marginRight: scaleSizeW(24) }]}>
                        {localeNumber(subtotal)}
                      </Text>
                    )}
                  </View>
                </View>
              </View>
              {sn && <Text style={[styles.smallText, { paddingHorizontal: scaleSizeW(63), backgroundColor: 'white' }]}>S/N: {sn}</Text>}
              {showPromotionTag &&
                map(promotions, (promotion, index) => {
                  const promotionId = get(promotion, 'promotionId');
                  const promotionName = get(promotion, 'promotionName');
                  const realmPromotion: PromotionType = DAL.getPromotionById(promotionId);
                  let title = generatePromotionTitle(realmPromotion, promotion);
                  if (isEmpty(title) && !isEmpty(promotionName)) title = promotionName;
                  const discount = get(promotion, ['display', 'discount'], 0);
                  return <PromotionTag key={`${promotionId}${index}`} promotionId={promotionId} title={title} discount={discount} />;
                })}
            </View>
          </TouchableWithoutFeedback>
        </View>
      </>
    );
  }
}

export type PromotionTagProps = { promotionId: string; discount: number; title: string };

export const PromotionTag = memo((props: PromotionTagProps) => {
  const { discount, title } = props;
  return (
    <View style={[styles.item, { width: '100%', flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }]}>
      <Text style={[styles.tintText, { marginLeft: scaleSizeW(68), marginRight: scaleSizeW(30) }]}>{title}</Text>
      <Text style={[styles.tintText, { marginRight: scaleSizeW(34), textAlign: 'right' }]}>{localeNumber(-discount)}</Text>
    </View>
  );
});

const ITEM_CONTENT_WIDTH = (width - scaleSizeW(120)) / 3;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    paddingTop: scaleSizeH(28),
    paddingBottom: scaleSizeH(32),
    paddingHorizontal: scaleSizeW(18),
  },
  orderContainer: {
    width: ITEM_CONTENT_WIDTH,
    height: '100%',
    backgroundColor: 'white',
    marginHorizontal: scaleSizeW(14),
    borderRadius: scaleSizeH(8),
    borderWidth: 0,
    overflow: 'hidden',
  },
  moveOrderButtonContainer: {
    marginRight: scaleSizeW(40),
    width: scaleSizeW(160),
    height: scaleSizeH(56),
    borderRadius: scaleSizeW(8),
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: '#D6D6D6',
  },
  moveOrderButtonText: {
    fontSize: currentThemes.fontSize18,
    color: '#393939',
    fontWeight: 'bold',
    textAlign: 'center',
    textAlignVertical: 'center',
  },
  item: {
    paddingVertical: scaleSizeH(6),
    width: '100%',
  },
  tintText: {
    fontSize: currentThemes.fontSize18,
    color: '#757575',
  },
  smallText: {
    fontSize: currentThemes.fontSize24,
    color: '#303030',
    fontWeight: '400',
    // includeFontPadding: false,
  },
  noteText: {
    fontSize: currentThemes.fontSize18,
    color: '#757575',
    // marginLeft: scaleSizeW(8),
    // includeFontPadding: false,
  },
  noticeContent: { flex: 1, justifyContent: 'center', minHeight: IsIOS ? scaleSizeH(70) : scaleSizeH(120) },
  noticeText: {
    fontSize: currentThemes.fontSize24,
    color: '#303030',
    fontWeight: '500',
  },
  groupTitle: {
    width: ITEM_CONTENT_WIDTH,
    color: '#9E9E9E',
    fontSize: currentThemes.fontSize20,
    alignSelf: 'center',
    paddingBottom: scaleSizeH(8),
  },
  groupDivideLine: {
    borderTopColor: '#E0E0E4',
    borderTopWidth: StyleSheet.hairlineWidth,
    paddingTop: scaleSizeH(18),
    marginTop: scaleSizeH(8),
  },
  standaloneRowFront: {
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: scaleSizeH(60),
  },
  notesContainer: {
    marginTop: scaleSizeH(4),
    flexDirection: 'row',
    marginLeft: scaleSizeW(63),
  },
  itemTitleContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: scaleSizeW(14),
  },
  descText: {
    alignSelf: 'flex-start',
    fontSize: currentThemes.fontSize18,
    color: '#303030',
    backgroundColor: '#FEE8CB',
    marginTop: scaleSizeH(14),
    marginLeft: scaleSizeW(14),
    paddingVertical: scaleSizeH(8),
    paddingHorizontal: scaleSizeW(8),
    borderWidth: 0,
    borderRadius: scaleSizeH(8),
    // includeFontPadding: false,
    textAlignVertical: 'center',
    overflow: 'hidden',
  },
  tableAndPaxContainer: {
    marginTop: scaleSizeH(26),
    marginBottom: scaleSizeH(20),
    flexDirection: 'row',
    alignItems: 'center',
  },
  paxContainer: {
    flex: 1,
    marginLeft: scaleSizeW(6),
    flexDirection: 'row',
    alignItems: 'center',
  },
  tableIdText: {
    alignSelf: 'center',
    fontSize: currentThemes.fontSize24,
    color: '#303030',
    // includeFontPadding: false,
    textAlignVertical: 'center',
  },
  paxText: {
    fontSize: currentThemes.fontSize24,
    color: '#303030',
    // includeFontPadding: false,
    textAlignVertical: 'center',
    marginLeft: scaleSizeW(4),
  },
  payButton: {
    width: '100%',
    height: scaleSizeH(100),
    backgroundColor: '#FC7118',
    alignItems: 'center',
    justifyContent: 'center',
  },
  payText: {
    fontSize: currentThemes.fontSize28,
    color: 'white',
    // includeFontPadding: false,
    textAlignVertical: 'center',
    fontWeight: '700',
  },
  descContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  trashButton: {
    paddingHorizontal: scaleSizeW(14),
    marginRight: scaleSizeW(8),
    marginTop: scaleSizeH(14),
  },
  changeTableIdhButton: {
    alignSelf: 'center',
    flexDirection: 'row',
    alignItems: 'center',
  },
});
