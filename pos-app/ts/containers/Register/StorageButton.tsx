import React, { useEffect, useRef, useState } from 'react';
import { AppState, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import IconGlobal from '../../components/ui/svgIcons/iconGlobal';
import { CommonColors, scaleSizeH, scaleSizeW } from '../../constants/themes';
import { useDispatch, useSelector } from 'react-redux';
import { NetInfoSelector } from '../../components/settings/NewWifiBanner';
import { get } from 'lodash';
import IconBadGlobal from '../../components/ui/svgIcons/iconBadGlobal';
import { testProps } from '../../utils';
import { selectInsufficientStorageWarning, selectNewProductSyncFlow, selectSyncInfo } from '../../sagas/selector';
import Animated, { Easing, useAnimatedStyle, useSharedValue, withRepeat, withTiming } from 'react-native-reanimated';
import IconItemRefresh from '../../components/ui/svgIcons/iconItemRefresh';
import { useAppNavigation } from '../../hooks/common/useAppNavigation';
import ProgressBar from '../Auth/ProgressBar';
import { checkFreeStorage, syncLargeProductThumbnails } from '../../actions';
import IconStorage from '../../components/ui/svgIcons/iconStorage';
import RNFS from 'react-native-fs';

interface StorageButtonProps {}

const StorageButton: React.FC<StorageButtonProps> = props => {
  const warning = useSelector(selectInsufficientStorageWarning);

  const appState = useRef(AppState.currentState);
  const dispatch = useDispatch();
  const navigation = useAppNavigation();
  const [appStateVisible, setAppStateVisible] = useState(appState.current);
  const [visible, setVisible] = useState(false);

  const onPress = () => {
    dispatch(
      checkFreeStorage({
        navigation: navigation,
        onContinue: () => void 0,
      })
    );
  };

  useEffect(() => {
    const subscription = AppState.addEventListener('change', nextAppState => {
      if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
        console.log('App has come to the foreground!');
      }
      appState.current = nextAppState;
      setAppStateVisible(appState.current);
    });

    return () => {
      subscription.remove();
    };
  }, []);

  useEffect(() => {
    RNFS.getFSInfo().then(info => {
      if (info && info.freeSpace) {
        setVisible(warning.enabled && info.freeSpace / 1024 / 1024 < warning.warningThreshold);
      }
    });
  }, [appStateVisible, warning]);

  if (!visible) {
    return null;
  }

  return (
    <TouchableOpacity {...testProps('al_btn_514')} testID='internet-state-button' style={styles.innerTouchableIconContainer} onPress={onPress}>
      <IconStorage testID='IconGlobal' color={'#4FDB87'} width={25} height={25} />
    </TouchableOpacity>
  );
};

export default StorageButton;

const styles = StyleSheet.create({
  innerTouchableIconContainer: {
    width: scaleSizeW(60),
    height: scaleSizeH(60),
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    marginLeft: scaleSizeW(20),
  },
});
