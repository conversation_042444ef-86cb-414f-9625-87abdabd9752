import { useFocusEffect } from '@react-navigation/native';
import { useMemoizedFn } from 'ahooks';
import { filter, findIndex, map } from 'lodash';
import React, { FC, memo, ReactElement, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { StyleSheet, View } from 'react-native';
import ScrollableTabView from 'react-native-scrollable-tab-view';
import { connect, ConnectedProps, useSelector } from 'react-redux';
import { bindActionCreators } from 'redux';
import { createSelector } from 'reselect';
import { checkAssignedPrinter, checkUpdate, pingLanPrinters, requestAuthorizedAction } from '../../actions';
import { closeTopNotification, ContentType } from '../../components/common/TopNotification';
import CustomDisplay from '../../components/settings/CustomDisplay';
import General from '../../components/settings/General';
import KDSSetting from '../../components/settings/KDSSetting';
import Layouts, { openSyncPopup } from '../../components/settings/Layouts';
import MultipleRegisterSync from '../../components/settings/MultipleRegisterSync';
import NcsSetting from '../../components/settings/NcsSetting';
import PageFocus from '../../components/settings/PageFocus';
import SettingTabBar from '../../components/settings/SettingTabBar';
import SettingTopBar from '../../components/settings/SettingTopBar';
import BeepDelivery from '../../components/settings/beep/BeepDelivery';
import BeepMenu, { BeepMenuComponent } from '../../components/settings/beep/BeepMenu';
import CfdSetting from '../../components/settings/cfd/CfdSetting';
import { PrinterSettingContext, PrinterSettingContextType, SettingContext, SettingContextType } from '../../components/settings/context';
import NetWork from '../../components/settings/network/Network';
import PrinterSetting from '../../components/settings/printer/PrinterSetting';
import { AuthorizationAccessStatus, AuthorizationType, CashierActions, currentThemes, scaleSizeH, scaleSizeW, SharedStyles, t, width } from '../../constants';
import { AllSettingTabs } from '../../constants/settingTabs';
import { getCurrentRouteName } from '../../navigation/navigatorService';
import {
  selectEmployeeId,
  selectIsBeepQREnabled,
  selectIsBOEnabledMRS,
  selectIsEnabledMRS,
  selectIsFAB,
  selectIsNCSPurchased,
  selectNewCFDAllowed,
  selectPrinterAssignedStatus,
  selectPrinterTagsSettings,
  selectSequentialReceiptNumber,
} from '../../sagas/selector';
import { RootState, ScreenProps } from '../../typings';
import eventBus, { APP_ACTIVE, APP_BACKGROUND } from '../../utils/eventBus';
import { KDS_NAME } from '../../utils/kds';
import { NCS_NAME } from '../../utils/ncs';

const SettingInPages = ['TableLayoutEdit', 'ModalManagerPin', 'ModalInfo', 'ModalFailedPrintJobList', 'ModalSwitchKitchenDocketLayout'];
type Props = PropsFromRedux & ScreenProps;
const Settings: FC<Props> = props => {
  const { tabNameList, currentEmployeeId } = props;

  const [pageIndex, setPageIndex] = useState(0);

  let POSSettingAccessedStatus = AuthorizationAccessStatus.Denied;

  const onPOSSettingAccessSuccess = {
    callback: () => {
      POSSettingAccessedStatus === AuthorizationAccessStatus.Granted;
    },
  };

  const onPOSSettingAccessFailure = {
    callback: () => {
      POSSettingAccessedStatus === AuthorizationAccessStatus.Denied;
    },
  };
  // no need check access

  const checkPOSSettingAccess = () => {
    if (POSSettingAccessedStatus === AuthorizationAccessStatus.Denied) {
      POSSettingAccessedStatus = AuthorizationAccessStatus.Requesting;
      props.actions.requestAuthorizedAction({
        name: CashierActions.POSSetting,
        employeeId: currentEmployeeId,
        type: AuthorizationType.Cashier,
        goHomeWhenClose: true,
        onSuccess: onPOSSettingAccessSuccess,
        onFailure: onPOSSettingAccessFailure,
      });
    }
  };

  const onAppActive = () => {
    checkPOSSettingAccess();
  };

  const onAppBackground = () => {
    POSSettingAccessedStatus = AuthorizationAccessStatus.Denied;
  };

  let onAppActiveListener;
  let onAppBackgroundListener;

  useFocusEffect(
    useCallback(() => {
      checkPOSSettingAccess();
      onAppActiveListener = eventBus.addListener(APP_ACTIVE, onAppActive, onAppActiveListener);
      onAppBackgroundListener = eventBus.addListener(APP_BACKGROUND, onAppBackground, onAppBackgroundListener);
      return () => {
        if (!SettingInPages.includes(getCurrentRouteName())) {
          onAppActiveListener && eventBus.remove(onAppActiveListener);
          onAppBackgroundListener && eventBus.remove(onAppBackgroundListener);
          onAppActiveListener = null;
          onAppBackgroundListener = null;
          // POSSettingAccessedStatus = AuthorizationAccessStatus.Denied;
        }
      };
    }, [])
  );

  const tabName = useMemo(() => {
    return tabNameList[pageIndex];
  }, [tabNameList, pageIndex]);

  const [rightBackShow, setRightBackShow] = useState(false);
  const [rightTitle, setRightTitle] = useState('');
  const printerAssignStatus = useSelector(selectPrinterAssignedStatus);

  const [selectPrinterId, setSelectPrinterId] = useState('');
  const [inSetReceiptAutoPrint, setOnSetReceiptAutoPrint] = useState(false);
  const beepMenuRef = useRef<BeepMenuComponent>();
  const [isInKitchenDocketSetting, setIsInKitchenDocketSetting] = useState(false);
  const [isInReceiptSetting, setIsInReceiptSetting] = useState(false);

  const contextValue = useRef<SettingContextType>({
    setRightBackShow,
    setRightTitle,
  });

  const printerContextValue = useMemo((): PrinterSettingContextType => {
    return {
      selectPrinterId,
      setSelectPrinterId,
      inSetReceiptAutoPrint,
      setOnSetReceiptAutoPrint,
      isInKitchenDocketSetting,
      setIsInKitchenDocketSetting,
      isInReceiptSetting,
      setIsInReceiptSetting,
    };
  }, [
    printerAssignStatus,
    selectPrinterId,
    inSetReceiptAutoPrint,
    isInKitchenDocketSetting,
    setIsInKitchenDocketSetting,
    isInReceiptSetting,
    setIsInReceiptSetting,
  ]);

  const renderTabs = useMemoizedFn(() =>
    map(tabNameList, tabName => {
      if (tabName === AllSettingTabs.BeepMenu) {
        return (
          <View key='BeepMenu' style={SharedStyles.flexOne}>
            <BeepMenu ref={beepMenuRef} />
          </View>
        );
      } else if (tabName === AllSettingTabs.Printer) {
        return (
          <View key='PrinterSetting' style={SharedStyles.flexOne}>
            <PrinterSettingContext.Provider value={printerContextValue}>
              <PrinterSetting rightBackShow={rightBackShow} />
            </PrinterSettingContext.Provider>
          </View>
        );
      } else if (tabName === AllSettingTabs.General) {
        return (
          <View key='general' style={SharedStyles.flexOne}>
            <General rightBackShow={rightBackShow} />
          </View>
        );
      } else {
        return SettingTabPage.get(tabName) || <EmptyPage />;
      }
    })
  );

  const hidePrinterError = useMemoizedFn(() => {
    closeTopNotification([ContentType.PrinterErrorToast, ContentType.PrintAssignedToast, ContentType.PrinterOfflineToast]);
  });

  const onPressTabBar = useMemoizedFn((tabName: string, index: number) => {
    if (tabName === AllSettingTabs.Repair) {
      props.navigation.navigate('ModalFixingData');
    } else if (tabName === AllSettingTabs.Printer) {
      props.actions.pingLanPrinters({ onlyAssigned: false, source: 'pressTabBar' });
      hidePrinterError();
    } else if (tabName === AllSettingTabs.KitchenDisplaySystem) {
      closeTopNotification([ContentType.KdsLostToast]);
    } else if (tabName === AllSettingTabs.NumberCallingSystem) {
      closeTopNotification([ContentType.NcsLostToast]);
    } else if (tabName === AllSettingTabs.NewCFD) {
      closeTopNotification([ContentType.CfdLostToast]);
    }
    setPageIndex(index);
    onRightBackClick(index);
  });

  const onRightBackClick = useMemoizedFn((index?: number) => {
    const selectedTabName = tabNameList[index ?? pageIndex];

    if (selectedTabName === AllSettingTabs.Printer) {
      if (rightTitle === t('Print Receipt Automatically')) {
        setOnSetReceiptAutoPrint(false);
        setIsInKitchenDocketSetting(true);
        setRightBackShow(true);
        setRightTitle(t('Kitchen Docket Settings'));
        return;
      }
      if (rightTitle === t('RECEIPT_AND_HALF_RECEIPT_FONT_SIZE_TITLE')) {
        setIsInReceiptSetting(true);
        setRightBackShow(true);
        setRightTitle(t('Kitchen Docket Settings'));
        return;
      }
      if (rightTitle === t('KITCHEN_DOCKET_FONT_SIZE_TITLE')) {
        setIsInKitchenDocketSetting(true);
        setRightBackShow(true);
        setRightTitle(t('Kitchen Docket Settings'));
        return;
      }
      setSelectPrinterId('');
      setOnSetReceiptAutoPrint(false);
      setRightBackShow(false);
      setRightTitle('');
      setIsInKitchenDocketSetting(false);
      setIsInReceiptSetting(false);
    } else if (selectedTabName === AllSettingTabs.General) {
      setRightBackShow(false);
      setRightTitle('');
    } else {
      if (selectedTabName === AllSettingTabs.BeepMenu) {
        beepMenuRef.current && beepMenuRef.current.goBack();
      }
      setSelectPrinterId('');
      setOnSetReceiptAutoPrint(false);
      setRightBackShow(false);
      setRightTitle('');
      setIsInKitchenDocketSetting(false);
      setIsInReceiptSetting(false);
    }
  });

  const onFocusCallback = useMemoizedFn((tabName, params) => {
    const currentTabName = tabNameList[pageIndex] || tabNameList[0];
    if (tabName) {
      const matchedIndex = findIndex(tabNameList, v => v === tabName);
      if (matchedIndex >= 0) {
        setPageIndex(matchedIndex);
      }
      if (params?.showSyncPopup && tabName === AllSettingTabs.Layouts) {
        requestAnimationFrame(() => {
          console.log('openSyncPopup', params.initialPage);
          openSyncPopup(params.initialPage);
        });
      }
    }
    if (tabName === AllSettingTabs.Printer || currentTabName === AllSettingTabs.Printer) {
      hidePrinterError();
    }
    props.actions.checkAssignedPrinter({});
  });

  useEffect(() => {
    props.actions.checkUpdate({ checkInstall: true });
    props.actions.pingLanPrinters({ onlyAssigned: false, source: 'openSettingPage' });
    return () => {
      onAppActiveListener && eventBus.remove(onAppActiveListener);
      onAppBackgroundListener && eventBus.remove(onAppBackgroundListener);
    };
  }, []);

  // @ts-ignore
  return (
    <View style={styles.container}>
      <SettingTopBar onRightBackClick={onRightBackClick} rightBackShow={rightBackShow} rightTitle={rightTitle} />
      <View style={styles.row}>
        <SettingContext.Provider value={contextValue.current}>
          <View style={[{ width: width * 0.31 }]}>
            <SettingTabBar
              tabNameList={tabNameList}
              pageIndex={pageIndex}
              onPress={onPressTabBar}
              printerAssignStatus={printerAssignStatus.isPrinterAssigned}
            />
          </View>
          <View
            style={[
              styles.tabContainer,
              (tabName === AllSettingTabs.BeepDelivery || tabName === AllSettingTabs.BeepMenu) && {
                paddingHorizontal: 0,
                paddingTop: 0,
              },
            ]}
            testID='tabContainer'
          >
            <ScrollableTabView
              scrollWithoutAnimation
              page={pageIndex}
              style={SharedStyles.flexOne}
              locked={true}
              tabBarPosition={'bottom'}
              renderTabBar={false}
            >
              {renderTabs()}
            </ScrollableTabView>
          </View>
        </SettingContext.Provider>
      </View>
      <PageFocus onFocus={onFocusCallback} />
    </View>
  );
};

const SettingTabPage = new Map<string, ReactElement>([
  [AllSettingTabs.BeepDelivery, <BeepDelivery key='BeepDelivery' />],
  [AllSettingTabs.DefaultNetwork, <NetWork key='Default Network' />],
  [AllSettingTabs.MultipleRegisterSync, <MultipleRegisterSync key='MultipleRegisterSync' />],
  [AllSettingTabs.KitchenDisplaySystem, <KDSSetting key={KDS_NAME} />],
  [AllSettingTabs.NumberCallingSystem, <NcsSetting key={NCS_NAME} />],
  [AllSettingTabs.CustomerDisplay, <CustomDisplay key='CustomDisplay' />],
  [AllSettingTabs.NewCFD, <CfdSetting key='CfdSetting' />],
  [AllSettingTabs.Layouts, <Layouts key='Layouts' />],
  [AllSettingTabs.General, <General key='General' />],
]);

const EmptyPage = () => null;

const selectTabs = createSelector(
  selectIsBeepQREnabled,
  selectIsEnabledMRS,
  selectIsBOEnabledMRS,
  selectSequentialReceiptNumber,
  selectIsNCSPurchased,
  selectNewCFDAllowed,
  selectIsFAB,
  (isBeepEnabled, isEnabledMRS, isBOEnabledMRS, sequentialReceiptNumber, isNcsEnabled, isNewCFDAllowed, isFAB) => {
    let initSettingsTabs: string[] = [AllSettingTabs.General, AllSettingTabs.Printer];
    // if (sequentialReceiptNumber) {
    //   initSettingsTabs.push(AllSettingTabs.Repair);
    // }
    if (isNewCFDAllowed) {
      initSettingsTabs.push(AllSettingTabs.NewCFD);
    } else {
      initSettingsTabs.push(AllSettingTabs.CustomerDisplay);
    }
    if (isFAB) {
      initSettingsTabs.push(AllSettingTabs.KitchenDisplaySystem);
    }
    if (isNcsEnabled) {
      initSettingsTabs.push(AllSettingTabs.NumberCallingSystem);
    }
    initSettingsTabs.push(AllSettingTabs.Layouts);
    if (isBeepEnabled) {
      initSettingsTabs = initSettingsTabs.concat([AllSettingTabs.BeepMenu, AllSettingTabs.BeepDelivery]);
    }
    if (isEnabledMRS || isBOEnabledMRS) {
      initSettingsTabs.push(AllSettingTabs.MultipleRegisterSync);
    }

    initSettingsTabs.push(AllSettingTabs.DefaultNetwork);
    return initSettingsTabs;
  }
);

const fromImmutablePrinterTagsSettings = createSelector(selectPrinterTagsSettings, settings => filter(settings.toJS(), Boolean));

const mapStateToProps = (state: RootState) => ({
  tabNameList: selectTabs(state),
  printerTagsSettings: fromImmutablePrinterTagsSettings(state),
  currentEmployeeId: selectEmployeeId(state),
});

const mapDispatchToProps = dispatch => ({
  actions: bindActionCreators(
    {
      checkUpdate,
      checkAssignedPrinter,
      pingLanPrinters,
      requestAuthorizedAction,
    },
    dispatch
  ),
});

const connector = connect(mapStateToProps, mapDispatchToProps);
type PropsFromRedux = ConnectedProps<typeof connector>;
export default memo(connector(Settings));

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  row: {
    flexDirection: 'row',
    flex: 1,
  },
  tabContainer: {
    width: width * 0.69,
    backgroundColor: currentThemes.bgMainColor,
    paddingHorizontal: scaleSizeW(30),
    paddingTop: scaleSizeH(24),
    paddingBottom: scaleSizeH(20),
  },
});
