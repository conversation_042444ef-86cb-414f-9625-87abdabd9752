import * as Immutable from 'immutable';
import { get, map } from 'lodash';
import moment from 'moment';
import React, { PureComponent } from 'react';
import { ScrollView, StyleSheet, Text, View } from 'react-native';
import { Row, Rows, Table } from 'react-native-table-component';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { createSelector } from 'reselect';
import { getPOSBookDailyReport, getPOSBookMonthlyReport } from '../../actions';
import { ModalContainer } from '../../components/common';
import { currentThemes, scaleSizeH, t } from '../../constants';
import { selecIsVATRegistered } from '../../sagas/selector';
import { ScreenProps } from '../../typings';
import { testProps } from '../../utils';
import { getParam } from '../../utils/navigation';

interface Props extends ScreenProps {
  store: any;
  registerId: string;
  currentEmployeeId: string;
  isVATRegistered: boolean;
  actions?: {
    getPOSBookDailyReport;
    getPOSBookMonthlyReport;
  };
}

interface State {
  report: any;
}

const fromImmutableStore = createSelector(
  (state: Immutable.Map<string, any>) => state.getIn(['Storage', 'storeInfo', 'store'], Immutable.Map()),
  storeInfo => storeInfo.toJS()
);

const mapStateToProps = state => ({
  store: fromImmutableStore(state),
  registerId: state.getIn(['Storage', 'storeInfo', 'registerId']),
  currentEmployeeId: state.getIn(['CurrentEmployee', 'employeeId']),
  isVATRegistered: selecIsVATRegistered(state),
});

const mapDispatchToProps = dispatch => ({
  actions: bindActionCreators(
    {
      getPOSBookDailyReport,
      getPOSBookMonthlyReport,
    },
    dispatch
  ),
});

class BirReport extends PureComponent<Props, State> {
  constructor(props) {
    super(props);
    this.state = {
      report: [],
    };
  }
  componentDidMount() {
    const requestDate = getParam(this.props, 'date', '');
    const isMonthlyReport = getParam(this.props, 'isMonthlyReport');

    if (isMonthlyReport) {
      const onSuccess = {
        callback: payload => {
          const response = get(payload, 'res');
          const report = [];
          map(response, data => {
            const _id = data._id;
            const date = `${get(_id, 'month', '')}-${get(_id, 'day', '')}-${get(_id, 'year', '')}`;
            const beginOR = get(data, 'startORNumber');
            const endOR = get(data, 'endORNumber');
            const beginSales = get(data, 'oldNet');
            const endSales = get(data, 'newNet');
            const netAmount = get(data, 'netSales');
            const scDiscount = get(data, 'scDiscount');
            const pwdDiscount = get(data, 'pwdDiscount');
            const otherDiscount = get(data, 'regularDiscount');
            const refund = get(data, 'refundAmount');
            report.push([date, beginOR, endOR, beginSales, endSales, netAmount, scDiscount, pwdDiscount, otherDiscount, refund]);
          });
          this.setState({ report });
        },
      };
      const isoDateString = moment(requestDate).add(1, 'days').toISOString();
      this.props.actions.getPOSBookMonthlyReport({ date: isoDateString, onSuccess });
    } else {
      const onSuccess = {
        callback: payload => {
          const response = get(payload, 'res');
          const report = [];
          map(response, data => {
            const orNumber = get(data, 'orNumber', 'N/A');
            const trxNumber = get(data, 'trxNumber');
            const total = get(data, 'total', 0).toFixed(2);
            const taxableSales = get(data, 'taxableSales', 0).toFixed(2);
            const taxExemptedSales = get(data, 'taxExemptedSales', 0).toFixed(2);
            const zeroRatedSales = get(data, 'zeroRatedSales', 0).toFixed(2);

            report.push([orNumber, trxNumber, '', '', '', '', total, taxableSales, taxExemptedSales, zeroRatedSales]);
          });
          this.setState({ report });
        },
      };
      const isoDateString = moment(requestDate).toISOString();
      this.props.actions.getPOSBookDailyReport({ date: isoDateString, onSuccess });
    }
  }

  renderFooter = () => (
    <Text {...testProps('al_text_577')} style={styles.footer}>
      {t('This report only includes orders that have been issued with a receipt or closed in a Z report')}
    </Text>
  );

  render() {
    const isMonthlyReport = getParam(this.props, 'isMonthlyReport');

    const title = isMonthlyReport ? t('Monthly Report') : t('Daily Report');
    return (
      <ModalContainer title={title} onCloseHandler={this.closeModalHandler} contentStyle={{ flex: 1, width: '100%' }} renderExtraFooter={this.renderFooter}>
        {this.renderContent()}
      </ModalContainer>
    );
  }

  renderContent = () => {
    const { store, registerId, currentEmployeeId, isVATRegistered } = this.props;
    const isMonthlyReport = getParam(this.props, 'isMonthlyReport');
    const dailyReportHeader = [
      'SI No.',
      'Trx No.',
      "Buyer's ID No.",
      "Buyer's Name",
      "Buyer's Address",
      "Buyer's TIN",
      'Total Sales',
      'VATable Sales',
      'VAT-Exempt Sales',
      'Zero-Rated Sales',
    ];
    const monthlyReportHeader = ['Day', 'Beg SI', 'End SI', 'Beg Sales', 'End Sales', 'Net Amount', 'SC Discount', 'PWD Discount', 'Other Discount', 'Refund'];
    const state = {
      tableHead: isMonthlyReport ? monthlyReportHeader : dailyReportHeader,
      tableData: this.state.report,
    };
    const requestDate = getParam(this.props, 'date', '');
    const reportDate = moment(requestDate).format('MMMM DD, YYYY');
    const reportTime = moment(requestDate).format('HH:mm');
    const vatTitle = isVATRegistered ? 'VAT REG TIN:' : 'NON-VAT REG TIN: ';
    return (
      <ScrollView contentContainerStyle={{ paddingHorizontal: 15 }}>
        <View style={{ marginTop: 10 }}>
          <Text {...testProps('al_text_907')} style={styles.topText}>{`Report Date: ${reportDate}`}</Text>
          <Text {...testProps('al_text_438')} style={styles.topText}>{`Report TIme: ${reportTime}`}</Text>
        </View>
        <View style={{ alignItems: 'center', marginTop: 10 }}>
          <Text {...testProps('al_text_611')} style={styles.boldText}>
            {get(store, 'name')}
          </Text>
          <Text {...testProps('al_text_668')} style={styles.boldText}>{`${get(store, 'street1')}${get(store, 'street2')}`}</Text>
          <Text {...testProps('al_text_353')} style={styles.boldText}>
            {vatTitle + get(store, 'gstIdNo')}
          </Text>
          <Text {...testProps('al_text_307')} style={styles.boldText}>
            {'ACCRED NO:' + get(store, 'birAccrNo')}
          </Text>
          <Text {...testProps('al_text_796')} style={styles.boldText}>
            {reportDate}
          </Text>
          <Text {...testProps('al_text_21')} style={styles.boldText}>
            {'System Version: 2.7'}
          </Text>
          <Text {...testProps('al_text_719')} style={styles.boldText}>
            {'UserID:' + currentEmployeeId}
          </Text>
        </View>
        <View style={{ marginVertical: 10 }}>
          <Text {...testProps('al_text_443')} style={styles.boldText}>
            {'Branch:' + registerId + ' ' + get(store, 'name')}
          </Text>
          <Text {...testProps('al_text_509')} style={styles.boldText}>{`Terminal:${registerId} Terminal${registerId}  SERIAL: ${get(
            store,
            'serialNo'
          )} MIN: ${get(store, 'minNo')}  PTU: ${get(store, 'ptu')}`}</Text>
          <Text {...testProps('al_text_154')} style={styles.boldText}>{`Date: ${reportDate}`}</Text>
        </View>
        <Table borderStyle={{ borderWidth: 1 }}>
          <Row data={state.tableHead} style={styles.rowHeader} textStyle={styles.rowText} />
          <Rows data={state.tableData} style={styles.rowHeader} textStyle={styles.rowText} />
        </Table>
      </ScrollView>
    );
  };

  closeModalHandler = () => {
    this.props.navigation.goBack();
  };
}

const styles = StyleSheet.create({
  rowHeader: {
    height: 40,
  },
  rowText: {
    fontSize: 16,
    textAlign: 'center',
  },
  topText: {
    fontSize: 20,
    marginVertical: 5,
  },
  boldText: {
    fontSize: 20,
    fontWeight: 'bold',
    marginVertical: 2,
  },
  footer: {
    marginVertical: scaleSizeH(40),
    fontSize: currentThemes.fontSize24,
    textAlign: 'center',
    color: '#000',
  },
});

export default connect(mapStateToProps, mapDispatchToProps)(BirReport);
