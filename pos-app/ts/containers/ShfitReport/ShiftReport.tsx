import { DrawerActions } from '@react-navigation/compat';
import * as Immutable from 'immutable';
import { get, indexOf, noop } from 'lodash';
import moment from 'moment';
import React, { Component } from 'react';
import { Linking, SectionList, SectionListData, StatusBar, StyleSheet, Text, TouchableOpacity, TouchableWithoutFeedback, View } from 'react-native';
import sectionListGetItemLayout from 'react-native-section-list-get-item-layout';
import { ConnectedProps, connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { createSelector } from 'reselect';
import { AyalaMallReport, RobinsonMallReport } from '.';
import {
  RequestActionAccessResult,
  checkCloseZReadingAccess,
  checkReportUpdateStatus,
  endDay,
  generateShiftReport,
  getDailyClosing,
  getOnlineTransactionDailyReport,
  getZReadingReport,
  navigateToHome,
  openDrawer,
  printDailyReport,
  printShiftReport,
  printZReadingReport,
  processToPrintAyalaEOD,
  processToPrintOrtigasEOD,
  processToPrintSMEOD,
  safeCloseZReading,
} from '../../actions';
import { NAVIGATOR_HEADER_HEIGHT, SubmitButton, TopMainBar } from '../../components/common';
import MenuWithNotification from '../../components/common/MenuWithNotification';
import { ModalMoreAction } from '../../components/modal';
import { CashSummary, DailyReportSummary, DailyReportSummaryType, PaymentSummary, SalesSummary, TopOverview } from '../../components/shiftReport';
import { IconCactus, IconChartLine, IconClock, IconMore, IconPrinter } from '../../components/ui';
import { DefaultPaymentOptionType } from '../../config/paymentOption';
import {
  CommonColors,
  MallIntegrationChannel,
  SMMallChannelList,
  STATUS_BAR_HEIGHT,
  SharedStyles,
  belongToRobinsonMall,
  currentThemes,
  monthFullNames,
  scaleSizeH,
  scaleSizeW,
  t,
  width,
} from '../../constants';
import DAL from '../../dal';
import {
  selectBirAccredited,
  selectBusinessName,
  selectCountry,
  selectEnableCashback,
  selectEnableLoyalty,
  selectIsMallIntegrationEnabled,
  selectLastZReadingCloseTime,
  selectMakeStoreCreditAsPayment,
  selectMallIntegrationChannel,
  selectOperationHours,
  selectRegisterId,
  selectShiftOpenStatus,
  selectStoreId,
} from '../../sagas/selector';
import { ScreenProps } from '../../typings';
import { getCountNumberString, getLocaleNumberString, testProps } from '../../utils';
import { createDatePickerData } from '../../utils/datetime';
import Picker from '../../utils/picker';

interface State {
  report: any;
  selectedTab: string;
  didUpdate: boolean;
  dailyReportSummary: DailyReportSummaryType;
}

const mapStateToProps = state => ({
  birAccredited: selectBirAccredited(state),
  shiftOpenStatus: selectShiftOpenStatus(state),
  paymentOptions: fromImmutablePaymentOptions(state),
  enableCashback: selectEnableCashback(state),
  enableLoyalty: selectEnableLoyalty(state),
  country: selectCountry(state),
  businessName: selectBusinessName(state),
  storeId: selectStoreId(state),
  registerId: selectRegisterId(state),
  lastZReadingCloseTime: selectLastZReadingCloseTime(state),
  isMallIntegrationEnabled: selectIsMallIntegrationEnabled(state),
  operationHours: selectOperationHours(state),
  mallIntegrationChannel: selectMallIntegrationChannel(state),
  makeStoreCreditAsPayment: selectMakeStoreCreditAsPayment(state),
});

const fromImmutablePaymentOptions = createSelector(
  (state: Immutable.Map<string, any>) => state.getIn(['Storage', 'storeInfo', 'store', 'paymentOptions'], Immutable.List()),
  paymentOptions => paymentOptions.toJS()
);

const mapDispatchToProps = dispatch => ({
  actions: bindActionCreators(
    {
      generateShiftReport,
      printShiftReport,
      printZReadingReport,
      openDrawer,
      safeCloseZReading,
      getZReadingReport,
      printDailyReport,
      navigateToHome,
      getOnlineTransactionDailyReport,
      endDay,
      getDailyClosing,
      checkReportUpdateStatus,
      checkCloseZReadingAccess,
      processToPrintAyalaEOD,
      processToPrintSMEOD,
      processToPrintOrtigasEOD,
    },
    dispatch
  ),
});

const LEFT_TAB_ITEM_HEIGHT = scaleSizeH(88);

enum DataPickerActionType {
  IDLE = 'IDLE',
  PRINT_ZREADING = 'PRINT_ZREADING',
  PRINT_AYALA_EOD = 'PRINT_AYALA_EOD',
  PRINT_SM_EOD = 'PRINT_SM_EOD',
  PRINT_SM_XReading = 'PRINT_SM_XReading',
  PRINT_ORTIGAS_EOD = 'PRINT_ORTIGAS_EOD',
  SHOW_BIRREPORT = 'SHOW_BIRREPORT',
}

const tabList = [
  { title: 'By Shift', data: [t('Summary'), t('Cash Drawer'), t('Payments')] },
  { title: t('By Day'), data: [t('Daily Report')] },
];

const PaymentCommonOptions = [
  { name: t('Cash'), paymentId: DefaultPaymentOptionType.Cash },
  { name: t('Credit Card'), paymentId: DefaultPaymentOptionType.CreditCard },
  { name: t('Store Credit'), paymentId: DefaultPaymentOptionType.Loyalty },
  { name: t('Debit Card'), paymentId: DefaultPaymentOptionType.DebitCard },
];

class ShiftReport extends Component<Props, State> {
  private _moreDialog;
  private _unsubscribeFocusListener;
  private dataPickerActionType: DataPickerActionType = DataPickerActionType.IDLE;
  static navigationOptions = () => ({
    headerShown: false,
  });

  generateTabTag = (section, index) => {
    const { title, data } = section;
    return `${title}-${data[index]}`;
  };

  constructor(props) {
    super(props);
    const { isMallIntegrationEnabled } = props;
    if (isMallIntegrationEnabled && indexOf(tabList[1]['data'], t('Mall Report')) === -1) {
      tabList[1]['data'].push(t('Mall Report'));
    }
    this.state = {
      report: null,
      selectedTab: this.generateTabTag(tabList[0], 0),
      didUpdate: false,
      dailyReportSummary: {
        totalAmount: '0.00',
        averageOrderValue: '0.00',
        totalQty: '0',
      },
    };
  }

  componentDidMount() {
    this._unsubscribeFocusListener = this.props.navigation.addListener('focus', this.onFocus);
    this.initDailyReport();
  }

  initDailyReport = () => {
    const {
      actions: { getOnlineTransactionDailyReport },
      businessName,
      storeId,
    } = this.props;

    const onSuccess = {
      callback: payload => {
        const { dailySalesReport } = payload.res;
        if (Boolean(dailySalesReport)) {
          const { salesByTransactionChannel, reportDate } = dailySalesReport;
          let totalAmount = 0;
          let totalQty = 0;
          const dailyReportChannels = [];
          if (salesByTransactionChannel.length > 0) {
            for (let index = 0; index < salesByTransactionChannel.length; index++) {
              const channelItem = salesByTransactionChannel[index];
              const { channel, amount, qty } = channelItem;
              totalAmount += Number(amount);
              totalQty += Number(qty);
              dailyReportChannels.push({ name: channel, count: getCountNumberString(qty), amount: getLocaleNumberString(amount) });
            }
          }
          this.setState({
            dailyReportSummary: {
              dailyReportChannels: dailyReportChannels,
              totalAmount: getLocaleNumberString(totalAmount),
              averageOrderValue: getLocaleNumberString(totalQty == 0 ? totalAmount : totalAmount / totalQty),
              totalQty: getCountNumberString(totalQty),
              reportDate: reportDate,
            },
          });
        }
      },
    };
    getOnlineTransactionDailyReport({ businessName, storeId, onSuccess });
  };

  onFocus = () => {
    this.refreshData();
  };

  UNSAFE_componentWillReceiveProps(nextProps) {
    this.refreshData();
  }

  componentWillUnmount() {
    Picker.hide();
    Boolean(this._unsubscribeFocusListener) && this._unsubscribeFocusListener();
  }

  refreshData = () => {
    const onComplete = {
      callback: shiftReport => {
        this.parseReport(shiftReport);
        if (!this.state.didUpdate) {
          this.setState({ didUpdate: true });
        }
      },
    };
    this.props.actions.generateShiftReport({ onComplete });
  };

  parseReport = report => {
    this.setState({ report });
  };

  onOpenPayList = () => {
    requestAnimationFrame(() => this.props.navigation.navigate('PayInOutList'));
  };

  onPayInPress = () => {
    requestAnimationFrame(() => this.props.navigation.navigate('CashPayInOut', { type: 'Payin', successCallBack: this.refreshData }));
  };

  onPayOutPress = () => {
    requestAnimationFrame(() => this.props.navigation.navigate('CashPayInOut', { type: 'Payout', successCallBack: this.refreshData }));
  };

  onOpenCashDrawerPress = () => {
    requestAnimationFrame(() => this.props.actions.openDrawer('open_cash_drawer'));
  };

  onPrintShiftReportPress = () => {
    const { report } = this.state;
    this.props.actions.printShiftReport(report);
  };

  renderSalesSummary = () => {
    const { report } = this.state;
    const { enableCashback, enableLoyalty, makeStoreCreditAsPayment } = this.props;
    let saleSummary;
    if (report) {
      saleSummary = report.getSaleSummary(enableLoyalty && enableCashback);
    }
    return (
      <SalesSummary
        enableLoyalty={enableLoyalty}
        enableCashback={enableCashback}
        saleSummary={saleSummary}
        makeStoreCreditAsPayment={makeStoreCreditAsPayment}
      />
    );
  };

  renderCashDrawerSummary = () => {
    const { report } = this.state;
    const { shiftOpenStatus } = this.props;
    let cashDrawerSummary;
    if (report) {
      cashDrawerSummary = report.getCashDrawerSummary();
    }
    return <CashSummary shiftOpenStatus={shiftOpenStatus} onOpenPayList={this.onOpenPayList} cashDrawerSummary={cashDrawerSummary} />;
  };

  renderPaymentsSummary = paymentOptions => {
    const { report } = this.state;
    let paymentsSummary;
    if (report) {
      const paymentOptionsAll = PaymentCommonOptions.concat(Array.isArray(paymentOptions) ? paymentOptions : []);
      paymentsSummary = report.getPaymentsSummary(paymentOptionsAll);
    }
    return <PaymentSummary paymentsSummary={paymentsSummary} />;
  };

  showInfoPopup = (infoTitle, infoContent) => {
    this.props.navigation.navigate('ModalInfo', { isShowTitle: true, title: infoTitle, info: infoContent });
  };

  renderDailyReportSummary = () => {
    const { dailyReportSummary } = this.state;
    return <DailyReportSummary dailyReportSummary={dailyReportSummary} onInfoClick={this.showInfoPopup} />;
  };

  renderMallReport = () => {
    const { mallIntegrationChannel } = this.props;
    switch (mallIntegrationChannel) {
      case MallIntegrationChannel.AYALA_MALL:
        return <AyalaMallReport />;
      case MallIntegrationChannel.ROBINSON_MALL:
      case MallIntegrationChannel.MEGAWORLD:
      case MallIntegrationChannel.ROCKWELL:
      case MallIntegrationChannel.FBDC:
      case MallIntegrationChannel.FILINVEST:
      case MallIntegrationChannel.SFDC:
      case MallIntegrationChannel.EVER_GOTESCO:
        return <RobinsonMallReport />;
      default:
        return null;
    }
  };

  onPressPrintXReading = () => {
    this._moreDialog && this._moreDialog.dismiss();
    this.onPrintShiftReportPress();
  };

  onPressPrintZReading = () => {
    this._moreDialog && this._moreDialog.dismiss();
    this.dataPickerActionType = DataPickerActionType.PRINT_ZREADING;
    this.showDatePicker();
  };

  onPressPrintAyalaEOD = () => {
    this._moreDialog && this._moreDialog.dismiss();
    this.dataPickerActionType = DataPickerActionType.PRINT_AYALA_EOD;
    this.showDatePicker();
  };

  onPressPrintOrtigasEOD = () => {
    this._moreDialog && this._moreDialog.dismiss();
    this.dataPickerActionType = DataPickerActionType.PRINT_ORTIGAS_EOD;
    this.showDatePicker();
  };

  onPressPrintSMEOD = () => {
    this._moreDialog && this._moreDialog.dismiss();
    this.dataPickerActionType = DataPickerActionType.PRINT_SM_EOD;
    this.showDatePicker();
  };

  onPressPrintSMXReading = () => {
    this._moreDialog && this._moreDialog.dismiss();
    this.props.navigation.navigate('ModalSMXReadingReport');
  };

  _isMonthlyReport = false;

  onPressMonthlyReport = () => {
    this._moreDialog && this._moreDialog.dismiss();
    this._isMonthlyReport = true;
    this.dataPickerActionType = DataPickerActionType.SHOW_BIRREPORT;
    this.showDatePicker();
  };

  onPressDailyReport = () => {
    this._moreDialog && this._moreDialog.dismiss();
    this._isMonthlyReport = false;
    this.dataPickerActionType = DataPickerActionType.SHOW_BIRREPORT;
    this.showDatePicker();
  };

  openCareLink = () => {
    Linking.openURL('https://care.storehub.com/en/articles/8897739').catch(err => console.error('open link error', err));
  };

  closeZReading = ({ needDoubleConfirm }: RequestActionAccessResult) => {
    // NOP
    const onCancelHandler = () => null;
    const onSubmitHandler = () => {
      const onSuccess = {
        callback: payload => {
          const isLastZReadingUpdated = get(payload, 'isLastZReadingUpdated', false);
          this.props.navigation.navigate('ModalInfo', {
            title: t('Z-Reading Closed Successfully'),
            isShowTitle: true,
            infoFn: () => (
              <View>
                {isLastZReadingUpdated && (
                  <>
                    <Text {...testProps('al_text_67')} style={styles.txtInfo}>
                      There’s update in previous Z-Reading because there’re new transactions on the same day after Z-reading is generated.
                    </Text>
                    <TouchableOpacity {...testProps('al_btn_801')} style={styles.txtInfoContainer} onPress={this.openCareLink}>
                      <Text {...testProps('al_text_938')} numberOfLines={1} style={[styles.txtInfoLink]}>
                        What is this?
                      </Text>
                    </TouchableOpacity>
                  </>
                )}
                <Text {...testProps('al_text_44')} style={[styles.txtInfo, { marginBottom: scaleSizeH(20) }]}>
                  Do you want to print Z-Reading for today?
                </Text>
              </View>
            ),
            okText: 'Print Z-Reading',
            cancelText: 'Skip',
            onSubmitHandler: () => {
              const onSuccess = {
                callback: payload => {
                  const ZReadings = get(payload, 'res');
                  this.props.actions.printZReadingReport(ZReadings);
                },
              };
              const closeTime = get(payload, 'closeTime');
              this.props.actions.getZReadingReport({ onSuccess, date: closeTime });
            },
            onCancelHandler,
          });
        },
      };
      const onFailure = {
        callback: payload => {
          // multiple close attemption will return status 409
          if (payload && payload.status === 409) {
            this.props.navigation.navigate('ModalInfo', {
              info: t('EOD has been performed for the day'),
              textAligh: 'center',
              onSubmitHandler: noop,
            });
          } else {
            this.props.navigation.navigate('ModalInfo', {
              title: t('Failed to Close Z-Reading'),
              isShowTitle: true,
              textAligh: 'center',
              okText: 'Close Z-Reading',
              onSubmitHandler,
              onCancelHandler,
              // info: `The system will attempt to close Z-reading up until ${displayTime}.\nThis register will not be able to create any transaction prior to the time above.`,
            });
          }
        },
      };
      const onEODSuccess = {
        callback: payload => {
          if (get(payload, ['res', 'message'], '')) {
            this.props.navigation.push('ModalInfo', {
              info: payload.res.message,
              textAligh: 'center',
            });
          }
        },
      };
      const onEODFailed = {
        callback: payload => {
          if (get(payload, ['res', 'message'], '')) {
            this.props.navigation.push('ModalInfo', {
              info: payload.res.message,
              textAligh: 'center',
            });
          }
        },
      };

      this.props.actions.safeCloseZReading({ onSuccess, onFailure, onEODSuccess, onEODFailed });
    };

    if (needDoubleConfirm) {
      this.props.navigation.navigate('ModalInfo', {
        // title: 'Do you want to close Z-Reading for today?',
        info: t('Are you sure you want to close Z-reading'),
        okText: 'Close Z-Reading',
        isShowTitle: false,
        textAligh: 'center',
        // info: `The system will attempt to close Z-reading up until ${displayTime}.\nThis register will not be able to create any transaction prior to the time above.`,
        onSubmitHandler,
        onCancelHandler,
      });
    } else {
      onSubmitHandler();
    }
  };

  onCloseZReadingAccessSuccess = {
    callback: this.closeZReading,
  };

  onPressCloseZReading = () => {
    this._moreDialog && this._moreDialog.dismiss();

    this.props.actions.checkCloseZReadingAccess({
      onSuccess: this.onCloseZReadingAccessSuccess,
    });
  };

  showDatePicker = () => {
    const { pickerData, selectedValue } = createDatePickerData();
    Picker.init({
      pickerData,
      selectedValue,
      pickerFontSize: currentThemes.fontSize28,
      pickerToolBarFontSize: currentThemes.fontSize24,
      pickerRowHeight: scaleSizeH(36),
      pickerToolBarHeight: scaleSizeH(80),
      pickerTitleText: '',
      pickerCancelBtnText: 'Cancel',
      pickerConfirmBtnText: 'Confirm',
      pickerBg: [255, 255, 255, 1],
      pickerToolBarBg: [239, 239, 244, 1],
      onPickerConfirm: pickedValue => {
        const date = pickedValue.join('-');
        let onSuccess;
        switch (this.dataPickerActionType) {
          case DataPickerActionType.PRINT_ZREADING:
            onSuccess = {
              callback: payload => {
                const ZReadings = get(payload, 'res');
                this.props.actions.printZReadingReport(ZReadings);
              },
            };
            this.props.actions.getZReadingReport({ onSuccess, date });
            break;
          case DataPickerActionType.PRINT_AYALA_EOD:
            this.props.actions.processToPrintAyalaEOD({ date: moment(date).format('YYYY-MM-DD') });
            break;
          case DataPickerActionType.PRINT_SM_EOD:
            this.props.actions.processToPrintSMEOD({ date: moment(date).format('YYYY-MM-DD') });
            break;
          case DataPickerActionType.PRINT_ORTIGAS_EOD:
            this.props.actions.processToPrintOrtigasEOD({ date: moment(date).format('YYYY-MM-DD') });
            break;
          case DataPickerActionType.SHOW_BIRREPORT:
            this.props.navigation.navigate('BirReport', { date, isMonthlyReport: this._isMonthlyReport });
            Picker.hide();
            break;

          default:
            break;
        }
      },
      onPickerCancel: () => {
        Picker.hide();
      },
    });
    Picker.show();
  };

  registerItemKeyExtractor = (item, index) => {
    return `${item}-${index}`;
  };

  getItemLayout = sectionListGetItemLayout({
    // The height of the row with rowData at the given sectionIndex and rowIndex
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    getItemHeight: (rowData, sectionIndex, rowIndex) => LEFT_TAB_ITEM_HEIGHT,
    // These four properties are optional
    getSeparatorHeight: () => 0, // The height of your separators
    getSectionHeaderHeight: () => scaleSizeH(40), // The height of your section headers
  });

  renderLeftTabItemHeader = ({ section: { title } }: { section: SectionListData<any> }) => {
    return (
      <View style={styles.sectionHeader}>
        <Text {...testProps('al_text_595')} style={{ width: '100%', fontWeight: '500', fontSize: currentThemes.fontSize20, color: '#303030' }}>
          {title}
        </Text>
      </View>
    );
  };

  onPrintDailyReport = () => {
    const { dailyReportSummary } = this.state;
    this.props.actions.printDailyReport({ dailyReportSummary });
  };

  iconPrinter = (<IconPrinter width={scaleSizeW(40)} height={scaleSizeH(40)} color={CommonColors.Icon} />);
  iconChart = (<IconChartLine width={scaleSizeW(40)} height={scaleSizeH(40)} color={CommonColors.Icon} />);

  renderShiftReport = () => {
    const { shiftOpenStatus, country, birAccredited, mallIntegrationChannel } = this.props;
    const { report, selectedTab } = this.state;
    let statusText = t(shiftOpenStatus ? 'Opened by' : 'Close by');
    let dateStr = '';
    let topOverviewFields;
    if (report) {
      topOverviewFields = report.getTotalNetSalesAndAvg(shiftOpenStatus);
      const { timeDate, employeeId } = topOverviewFields;
      const time = moment(timeDate);
      if (!Boolean(employeeId)) {
        return null;
      }
      const employee = DAL.getEmployeeById(employeeId);
      if (Boolean(employee)) {
        statusText += ' ' + employee.firstName + ' ' + employee.lastName;
      }

      dateStr = `${time.format('dddd, D')} ${monthFullNames[time.month()]} ${time.format('YYYY - h:mm A')}`;
    }

    const isInPH = country === 'PH';

    const moreActionList = [];

    if (isInPH) {
      if (birAccredited) {
        moreActionList.push({
          name: 'Close Z-Reading',
          icon: <IconClock width={scaleSizeW(40)} height={scaleSizeH(40)} color={CommonColors.Icon} />,
          onClick: this.onPressCloseZReading,
        });
        moreActionList.push({ isDivider: true });
        moreActionList.push({ name: 'Print Z-Reading', icon: this.iconPrinter, onClick: this.onPressPrintZReading });
        moreActionList.push({ name: 'Print X-Reading', icon: this.iconPrinter, onClick: this.onPressPrintXReading });
        moreActionList.push({ isDivider: true });
        if (mallIntegrationChannel === MallIntegrationChannel.AYALA_MALL) {
          moreActionList.push({ name: 'Print Ayala EOD', icon: this.iconPrinter, onClick: this.onPressPrintAyalaEOD });
          moreActionList.push({ isDivider: true });
        } else if (mallIntegrationChannel === MallIntegrationChannel.ORTIGAS_MALL) {
          moreActionList.push({ name: 'Print Ortigas EOD', icon: this.iconPrinter, onClick: this.onPressPrintOrtigasEOD });
          moreActionList.push({ isDivider: true });
        } else if (SMMallChannelList.includes(mallIntegrationChannel)) {
          moreActionList.push({ name: 'Print SM EOD', icon: this.iconPrinter, onClick: this.onPressPrintSMEOD });
          moreActionList.push({ name: 'Print SM X-Reading', icon: this.iconPrinter, onClick: this.onPressPrintSMXReading });
          moreActionList.push({ isDivider: true });
        }
        moreActionList.push({ name: 'POS Monthly Report', icon: this.iconChart, onClick: this.onPressMonthlyReport });
      }
      moreActionList.push({ name: 'POS Daily Report', icon: this.iconChart, onClick: this.onPressDailyReport });
    }

    const inDailyReport = selectedTab == this.generateTabTag(tabList[1], 0);
    const isMallReport = selectedTab == this.generateTabTag(tabList[1], 1);
    const shouldShowRightMenu = isInPH && (!isMallReport || belongToRobinsonMall(mallIntegrationChannel));
    return (
      <View style={styles.container}>
        <TopMainBar
          isLeftContainerTitle={true}
          leftIcon={<MenuWithNotification color={CommonColors.Icon} />}
          leftText={t('Sales Report')}
          rightIsIcon={true}
          onRightClick={shouldShowRightMenu ? this.onRightIconClick : noop}
          isRightButtonShow={false}
          rightIcon={shouldShowRightMenu ? <IconMore color={CommonColors.Icon} /> : null}
          onLeftClick={this.openMainDrawer}
          rightText={inDailyReport || isMallReport ? '' : statusText}
          rightTitle={isMallReport ? t('Mall Report') : dateStr}
        />
        {isInPH && <ModalMoreAction ref={refs => (this._moreDialog = refs)} list={[...moreActionList]} />}
        <View style={styles.content}>
          <View style={styles.leftContainer}>
            <SectionList
              renderItem={this.renderLeftTabItem}
              renderSectionHeader={this.renderLeftTabItemHeader}
              sections={tabList}
              keyExtractor={this.registerItemKeyExtractor}
              extraData={selectedTab}
              getItemLayout={this.getItemLayout}
            />
          </View>
          {this.renderContent(topOverviewFields)}
        </View>
      </View>
    );
  };

  openMainDrawer = () => this.props.navigation.dispatch(DrawerActions.openDrawer());

  renderContent = topOverviewFields => {
    const { shiftOpenStatus, paymentOptions } = this.props;
    const {
      selectedTab,
      dailyReportSummary: { totalAmount, totalQty, averageOrderValue },
    } = this.state;
    let showContent: any;
    let isDailyReport = false;
    let topOverviewValues = topOverviewFields;
    let isMallReport = false;
    switch (selectedTab) {
      case this.generateTabTag(tabList[0], 0):
        showContent = this.renderSalesSummary();
        break;
      case this.generateTabTag(tabList[0], 1):
        showContent = this.renderCashDrawerSummary();
        break;
      case this.generateTabTag(tabList[0], 2):
        showContent = this.renderPaymentsSummary(paymentOptions);
        break;
      case this.generateTabTag(tabList[1], 0):
        isDailyReport = true;
        showContent = this.renderDailyReportSummary();
        topOverviewValues = { netSales: totalAmount, count: totalQty, avgAmount: averageOrderValue };
        break;
      case this.generateTabTag(tabList[1], 1):
        isMallReport = true;
        showContent = this.renderMallReport();
        break;
    }
    return (
      <View style={styles.rightContainer}>
        {!isMallReport && (
          <View style={styles.rightTopContent}>
            {!isDailyReport && (
              <Text {...testProps('al_text_194')} style={styles.tipText}>
                {t(shiftOpenStatus ? 'Shift Opened' : 'Shift Closed')}
              </Text>
            )}
            <View style={styles.rightTopTransaction}>
              <TopOverview topOverviewFields={topOverviewValues} />
              {isDailyReport ? (
                <View style={styles.btnsContainer}>
                  <SubmitButton disabled={false} style={styles.printDailtReportBtn} onPress={this.onPrintDailyReport} textStyle={[styles.textStyle]}>
                    {t('PRINT DAILY REPORT')}
                  </SubmitButton>
                </View>
              ) : shiftOpenStatus ? (
                <View style={styles.btnsContainer}>
                  <SubmitButton
                    accessibilityLabel='al_payIn_btn'
                    disabled={false}
                    style={styles.submitBtnStyle}
                    onPress={this.onPayInPress}
                    textStyle={styles.textStyle}
                  >
                    {t('PAY IN')}
                  </SubmitButton>
                  <SubmitButton
                    accessibilityLabel='al_payOut_btn'
                    disabled={false}
                    style={styles.submitBtnStyle}
                    onPress={this.onPayOutPress}
                    textStyle={styles.textStyle}
                  >
                    {t('PAY OUT')}
                  </SubmitButton>
                  <SubmitButton
                    accessibilityLabel='al_openCashDrawer_btn'
                    disabled={false}
                    style={[styles.submitBtnStyle, { backgroundColor: '#FC7118', borderWidth: 0 }]}
                    onPress={this.onOpenCashDrawerPress}
                    textStyle={[styles.textStyle, { color: '#FFF' }]}
                  >
                    {t('OPEN CASH DRAWER')}
                  </SubmitButton>
                </View>
              ) : (
                <View style={styles.btnsContainer}>
                  <SubmitButton
                    accessibilityLabel='al_btn_print'
                    disabled={false}
                    style={styles.printShiftReportBtn}
                    onPress={this.onPrintShiftReportPress}
                    textStyle={[styles.textStyle, { color: '#FFF' }]}
                  >
                    {t('PRINT SHIFT REPORT')}
                  </SubmitButton>
                </View>
              )}
            </View>
          </View>
        )}
        {showContent}
      </View>
    );
  };

  renderLeftTabItem = ({ item: tab, index, section }) => {
    const { selectedTab } = this.state;
    const isActived = selectedTab === this.generateTabTag(section, index);
    return (
      <TouchableWithoutFeedback {...testProps('al_btn_952')} onPress={() => this.onLeftTabPressHandler(section, index)}>
        <View key={index} style={[styles.tab, isActived && { backgroundColor: '#008EC4' }]} {...testProps(`al_leftTab_${tab}`)}>
          <Text {...testProps('al_text_271')} style={[styles.tabText, isActived && { color: '#FFF' }]}>
            {tab}
          </Text>
        </View>
      </TouchableWithoutFeedback>
    );
  };

  onLeftTabPressHandler = (section, index) => {
    this.setState({
      selectedTab: this.generateTabTag(section, index),
    });
  };

  onRightIconClick = () => {
    this._moreDialog && this._moreDialog.show();
  };

  goHome = () => {
    const {
      navigation,
      actions: { navigateToHome },
    } = this.props;
    requestAnimationFrame(() => navigateToHome({ navigation }));
  };

  renderEmpty = () => {
    const { didUpdate } = this.state;
    return (
      <View style={{ flex: 1 }}>
        <StatusBar barStyle='dark-content' />
        <View style={[styles.emptyTopBar, SharedStyles.titleShadow]}>
          <View style={styles.emptyTopBarSubContainer}>
            <Text {...testProps('al_text_840')} style={styles.emptyTopBarTitle}>
              {t('Sales Report')}
            </Text>
            <TouchableOpacity
              {...testProps('al_btn_626')}
              activeOpacity={1}
              style={[SharedStyles.touchableIconContainer, styles.emptyTopBarLeftIcon]}
              onPress={this.openMainDrawer}
            >
              <MenuWithNotification color={CommonColors.Icon} />
            </TouchableOpacity>
          </View>
        </View>
        {didUpdate && (
          <View style={styles.emptyContentConatiner}>
            <IconCactus />
            <Text {...testProps('al_text_677')} style={styles.textWarn}>
              {t('Sales Report Unavailable')}
            </Text>
            <Text {...testProps('al_text_368')} style={styles.textDesc}>
              {t('Open your shift to see your sales and cash drawer reports here')}
            </Text>
            <TouchableOpacity {...testProps('al_btn_596')} style={styles.backToRegisterBtnContainer} onPress={this.goHome}>
              <Text {...testProps('al_text_824')} style={styles.backToRegisterBtnText}>
                {t('Back To Register')}
              </Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    );
  };

  render() {
    const { report } = this.state;
    const { shiftOpenStatus } = this.props;
    return Boolean(report) || shiftOpenStatus ? this.renderShiftReport() : this.renderEmpty();
  }
}

// @ts-ignore
const connector = connect(mapStateToProps, mapDispatchToProps);
export default connector(ShiftReport);
type PropsFromRedux = ConnectedProps<typeof connector>;
interface Props extends PropsFromRedux, ScreenProps {}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: currentThemes.bgMainColor,
  },
  content: {
    flex: 1,
    flexDirection: 'row',
  },
  leftContainer: {
    width: width * 0.31,
    flex: 1,
    backgroundColor: '#FFF',
    overflow: 'hidden',
  },
  rightContainer: {
    width: width * 0.69,
    paddingHorizontal: scaleSizeW(30),
    paddingTop: scaleSizeH(24),
    paddingBottom: scaleSizeH(20),
  },
  tab: {
    width: '100%',
    height: scaleSizeH(88),
    paddingLeft: scaleSizeW(34),
    justifyContent: 'center',
    alignItems: 'flex-start',
  },
  tabText: {
    width: '100%',
    fontSize: currentThemes.fontSize24,
    fontWeight: '500',
    color: '#60636B',
    textAlignVertical: 'center',
  },
  rightTopContent: {
    width: '100%',
    borderRadius: scaleSizeW(8),
    backgroundColor: '#FFF',
    overflow: 'hidden',
  },
  rightTopTransaction: {
    width: '100%',
    height: scaleSizeH(328),
    paddingHorizontal: scaleSizeW(8),
    paddingTop: scaleSizeH(24),
    flexDirection: 'column',
    justifyContent: 'space-between',
  },
  tipText: {
    height: scaleSizeH(56),
    lineHeight: scaleSizeH(56), // ios
    color: '#FFF',
    backgroundColor: '#242644',
    fontSize: currentThemes.fontSize24,
    fontWeight: '400',
    textAlign: 'center',
    textAlignVertical: 'center', // andro
  },
  submitBtnStyle: {
    flex: null,
    borderRadius: scaleSizeW(8),
    backgroundColor: '#FFF',
    borderColor: '#D6D6D6',
    borderWidth: scaleSizeW(1),
    height: scaleSizeH(80),
    width: (width * 0.69 - scaleSizeW(30) * 2 - scaleSizeW(24) * 2 - scaleSizeW(32) * 2) / 3,
  },
  textStyle: {
    fontSize: currentThemes.fontSize18,
    color: '#393939',
    fontWeight: 'bold',
  },
  btnsContainer: {
    width: '100%',
    paddingHorizontal: scaleSizeW(24),
    paddingBottom: scaleSizeH(27),
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  printDailtReportBtn: {
    borderRadius: scaleSizeW(8),
    backgroundColor: '#FFF',
    borderWidth: 1,
    height: scaleSizeH(80),
    borderColor: '#D6D6D6',
  },
  printShiftReportBtn: {
    borderRadius: scaleSizeW(8),
    backgroundColor: '#FC7118',
    borderWidth: 0,
    height: scaleSizeH(80),
  },
  emptyTopBar: {
    width: width,
    height: NAVIGATOR_HEADER_HEIGHT,
    paddingTop: STATUS_BAR_HEIGHT,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFF',
  },
  emptyTopBarLeftIcon: {
    position: 'absolute',
    left: scaleSizeW(30),
  },
  emptyTopBarTitle: {
    color: '#303030',
    fontSize: currentThemes.fontSize24,
    fontWeight: '500',
  },
  emptyTopBarSubContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyContentConatiner: {
    flex: 1,
    alignItems: 'center',
    paddingTop: scaleSizeH(187),
  },
  textWarn: {
    fontSize: currentThemes.fontSize32,
    marginTop: scaleSizeH(35),
    color: '#60636B',
    fontWeight: '500',
    lineHeight: scaleSizeH(52),
  },
  textDesc: {
    fontSize: currentThemes.fontSize24,
    marginTop: scaleSizeH(15),
    color: '#757575',
    fontWeight: '400',
    lineHeight: scaleSizeH(38),
  },
  backToRegisterBtnContainer: {
    backgroundColor: '#FC7118',
    width: scaleSizeW(840),
    height: scaleSizeH(112),
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: scaleSizeH(8),
    borderWidth: 0,
    marginTop: scaleSizeH(189),
  },
  backToRegisterBtnText: {
    fontSize: currentThemes.fontSize30,
    color: 'white',
    fontWeight: 'bold',
  },
  sectionHeader: {
    backgroundColor: '#ECEEF4',
    height: scaleSizeH(40),
    justifyContent: 'center',
    paddingLeft: scaleSizeW(24),
  },
  txtInfoContainer: {
    width: scaleSizeW(200),
    marginLeft: scaleSizeW(60),
    paddingVertical: scaleSizeH(5),
  },
  txtInfo: {
    fontSize: currentThemes.fontSize24,
    color: '#60636B',
    fontWeight: '400',
    paddingHorizontal: scaleSizeW(60),
    textAlignVertical: 'center',
    marginTop: scaleSizeH(20),
  },
  txtInfoLink: {
    fontSize: currentThemes.fontSize24,
    color: 'blue',
    fontWeight: '400',
    textAlignVertical: 'center',
  },
});
