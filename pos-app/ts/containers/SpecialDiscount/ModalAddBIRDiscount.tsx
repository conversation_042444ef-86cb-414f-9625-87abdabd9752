import { findIndex, get } from 'lodash';
import React, { PureComponent } from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import Accordion from 'react-native-collapsible/Accordion';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { connect } from 'react-redux';
import { IconRadio, ModalContainer, QuantityInput } from '../../components/common';
import { PosTextInput } from '../../components/textInput';
import {
  AthletesAndCoachesInfo,
  AuthorizationType,
  BIRDiscountType,
  BirInfo,
  BIRType,
  CashierActions,
  currentThemes,
  DiplomatsInfo,
  MedalofValorInfo,
  scaleSizeH,
  scaleSizeW,
  SCAndPWDInfo,
  SoloParentInfo,
  t,
} from '../../constants';
import { selectBirInfo, selectEmployeeId, selectIndustry } from '../../sagas/selector';
import { RootState, ScreenProps } from '../../typings';
import { testProps } from '../../utils';
import { getParam } from '../../utils/navigation';
import { bindActionCreators } from 'redux';
import { requestAuthorizedAction, RequestAuthorizedActionType } from '../../actions';

const CONTENT_FAB = [
  { type: BIRDiscountType.SCAndPWD, name: 'Senior Citizens/Persons with Disability' },
  { type: BIRDiscountType.AthletesAndCoaches, name: 'National Athletes/Coaches' },
  { type: BIRDiscountType.MedalofValor, name: 'Medal of Valor' },
  { type: BIRDiscountType.Diplomats, name: 'Diplomats' },
];

const CONTENT_RETAIL = [
  { type: BIRDiscountType.SCAndPWD, name: 'Senior Citizens/Persons with Disability' },
  { type: BIRDiscountType.SoloParent, name: 'Solo Parent' },
  { type: BIRDiscountType.AthletesAndCoaches, name: 'National Athletes/Coaches' },
  { type: BIRDiscountType.MedalofValor, name: 'Medal of Valor' },
  { type: BIRDiscountType.Diplomats, name: 'Diplomats' },
];

interface Props extends ScreenProps {
  birInfo: BirInfo;
  birType: BIRType;
  children?: any;
  currentEmployeeId: string;
  actions: {
    requestAuthorizedAction(payload: RequestAuthorizedActionType): void;
  };
}

interface State {
  discountType: BIRDiscountType;
  activeSections: any[];
  SCAndPWDInfo: SCAndPWDInfo;
  soloParentInfo: SoloParentInfo;
  athletesAndCoachesInfo: AthletesAndCoachesInfo;
  medalofValorInfo: MedalofValorInfo;
  diplomatsInfo: DiplomatsInfo;
}

const mapStateToProps = (state: RootState) => ({
  birInfo: selectBirInfo(state),
  birType: selectIndustry(state) == 1 ? BIRType.Retail : BIRType.FAB,
  currentEmployeeId: selectEmployeeId(state),
});

const mapDispatchToProps = dispatch => ({
  actions: bindActionCreators(
    {
      requestAuthorizedAction,
    },
    dispatch
  ),
});

class ModalAddBIRDiscount extends PureComponent<Props, State> {
  private contentList: any[];
  constructor(props) {
    super(props);
    const { birInfo, birType } = props;
    this.contentList = birType == BIRType.Retail ? CONTENT_RETAIL : CONTENT_FAB;
    const birDiscountType = get(birInfo, 'discountType', null);
    let collectedInfo = get(birInfo, 'collectedInfo');
    if (Boolean(collectedInfo) && typeof collectedInfo === 'string') collectedInfo = JSON.parse(collectedInfo);
    const { seniorsCount, pwdCount, headCount } = birInfo;
    const SCAndPWDInfo = birDiscountType === BIRDiscountType.SCAndPWD ? { seniorsCount, pwdCount, headCount } : { seniorsCount: 0, pwdCount: 0, headCount: 0 };
    const soloParentInfo = birDiscountType === BIRDiscountType.SoloParent ? collectedInfo : { name: '', spicId: '', nameOfChild: '' };
    const athletesAndCoachesInfo = birDiscountType === BIRDiscountType.AthletesAndCoaches ? collectedInfo : { name: '', pnstmId: '' };
    const medalofValorInfo = birDiscountType === BIRDiscountType.MedalofValor ? collectedInfo : { name: '', movId: '' };
    const diplomatsInfo = birDiscountType === BIRDiscountType.Diplomats ? collectedInfo : { name: '', dfaOrVicId: '', address: '' };
    const activeSections = Boolean(birDiscountType) ? [findIndex(this.contentList, item => item.type === birDiscountType)] : [];

    this.state = {
      discountType: birDiscountType,
      activeSections,
      SCAndPWDInfo,
      athletesAndCoachesInfo,
      medalofValorInfo,
      diplomatsInfo,
      soloParentInfo,
    };
  }

  _renderHeader = header => {
    const { type, name } = header;
    const { discountType } = this.state;
    const isActive = discountType === type;
    return (
      <View style={[styles.header, isActive && styles.headerActive]}>
        <Text style={styles.headerText}>{name}</Text>
        <IconRadio isActive={isActive} />
      </View>
    );
  };

  onInputAthletesAndCoachesName = value => {
    this.setState(prevState => {
      const athletesAndCoachesInfo = prevState.athletesAndCoachesInfo;
      return {
        athletesAndCoachesInfo: { ...athletesAndCoachesInfo, name: value },
      };
    });
  };

  onInputAthletesAndCoachesId = value => {
    this.setState(prevState => {
      const athletesAndCoachesInfo = prevState.athletesAndCoachesInfo;
      return {
        athletesAndCoachesInfo: { ...athletesAndCoachesInfo, pnstmId: value },
      };
    });
  };

  onInputMedalofValorName = value => {
    this.setState(prevState => {
      const medalofValorInfo = prevState.medalofValorInfo;
      return {
        medalofValorInfo: { ...medalofValorInfo, name: value },
      };
    });
  };

  onInputMedalofValorId = value => {
    this.setState(prevState => {
      const medalofValorInfo = prevState.medalofValorInfo;
      return {
        medalofValorInfo: { ...medalofValorInfo, movId: value },
      };
    });
  };

  onInputDiplomatsName = value => {
    this.setState(prevState => {
      const diplomatsInfo = prevState.diplomatsInfo;
      return {
        diplomatsInfo: { ...diplomatsInfo, name: value },
      };
    });
  };

  onInputDiplomatsId = value => {
    this.setState(prevState => {
      const diplomatsInfo = prevState.diplomatsInfo;
      return {
        diplomatsInfo: { ...diplomatsInfo, dfaOrVicId: value },
      };
    });
  };

  onInputDiplomatsAddress = value => {
    this.setState(prevState => {
      const diplomatsInfo = prevState.diplomatsInfo;
      return {
        diplomatsInfo: { ...diplomatsInfo, address: value },
      };
    });
  };

  onInputSoloParentName = value => {
    this.setState(prevState => {
      const soloParentInfo = prevState.soloParentInfo;
      return {
        soloParentInfo: { ...soloParentInfo, name: value },
      };
    });
  };

  onInputSoloParentSpicId = value => {
    this.setState(prevState => {
      const soloParentInfo = prevState.soloParentInfo;
      return {
        soloParentInfo: { ...soloParentInfo, spicId: value },
      };
    });
  };

  onInputSoloParentNameOfChild = value => {
    this.setState(prevState => {
      const soloParentInfo = prevState.soloParentInfo;
      return {
        soloParentInfo: { ...soloParentInfo, nameOfChild: value },
      };
    });
  };

  onInputSeniorCount = value => {
    const { birType } = this.props;
    this.setState(prevState => {
      const {
        SCAndPWDInfo: { seniorsCount, pwdCount, headCount },
      } = prevState;
      // Retail: seniorsCount and pwdCount can be 0 or 1, but not both.
      // When seniorsCount and pwdCount change, headCount also needs to change accordingly
      const newSeniorCount = birType == BIRType.Retail && value > 1 ? 1 : value;
      const newPwdCount = birType == BIRType.Retail && value == 1 ? 0 : pwdCount;
      const diff = newSeniorCount - seniorsCount;
      const newHeadCount = birType == BIRType.Retail ? Math.min(1, headCount + diff) : headCount + diff;
      return {
        SCAndPWDInfo: { seniorsCount: newSeniorCount, pwdCount: newPwdCount, headCount: newHeadCount },
      };
    });
  };

  onInputPwdCount = value => {
    const { birType } = this.props;
    this.setState(prevState => {
      const {
        SCAndPWDInfo: { seniorsCount, pwdCount, headCount },
      } = prevState;
      // Retail: seniorsCount and pwdCount can be 0 or 1, but not both.
      // When seniorsCount and pwdCount change, headCount also needs to change accordingly
      const newPwdCount = birType == BIRType.Retail && value > 1 ? 1 : value;
      const newSeniorCount = birType == BIRType.Retail && value == 1 ? 0 : seniorsCount;
      const diff = newPwdCount - pwdCount;
      // const minHeadCount = newSeniorCount + pwdCount;
      const newHeadCount = birType == BIRType.Retail ? Math.min(1, headCount + diff) : headCount + diff;
      return {
        SCAndPWDInfo: { seniorsCount: newSeniorCount, pwdCount: newPwdCount, headCount: newHeadCount },
      };
    });
  };

  onInputHeadCount = value => {
    const { birType } = this.props;
    if (birType == BIRType.Retail && value > 1) return;
    this.setState(prevState => {
      const SCAndPWDInfo = prevState.SCAndPWDInfo;
      return {
        SCAndPWDInfo: { ...SCAndPWDInfo, headCount: value },
      };
    });
  };

  _renderContent = section => {
    const {
      SCAndPWDInfo: { seniorsCount, pwdCount, headCount },
      soloParentInfo,
      athletesAndCoachesInfo,
      medalofValorInfo,
      diplomatsInfo,
    } = this.state;
    const { type } = section;
    switch (type) {
      case BIRDiscountType.SCAndPWD: {
        return (
          <View style={styles.content}>
            <View style={styles.contentLineContainer}>
              <Text style={styles.contentMainText}>Senior Citizens</Text>
              <QuantityInput value={seniorsCount} onChangeText={this.onInputSeniorCount} />
            </View>
            <View style={styles.contentLineContainer}>
              <Text style={styles.contentMainText}>Persons with Disability</Text>
              <QuantityInput value={pwdCount} onChangeText={this.onInputPwdCount} />
            </View>
            <View style={styles.divider} />
            <View style={styles.contentLineContainer}>
              <Text style={styles.contentMainText}>Total Customers</Text>
              <QuantityInput value={headCount} minValue={seniorsCount + pwdCount} onChangeText={this.onInputHeadCount} />
            </View>
          </View>
        );
      }
      case BIRDiscountType.SoloParent: {
        const { name, spicId, nameOfChild } = soloParentInfo;
        return (
          <View style={styles.content2}>
            <View style={styles.contentLineSubContainer}>
              <Text style={styles.contentNameText}>CUSTOMER NAME</Text>
              <View style={styles.inputConatiner}>
                <PosTextInput
                  {...testProps('al_textinput_9')}
                  style={styles.inputStyle}
                  placeholder={'Name'}
                  keyboardType='default'
                  returnKeyType='next'
                  underlineColorAndroid='transparent'
                  autoCapitalize={'none'}
                  value={name}
                  onChangeText={this.onInputSoloParentName}
                />
              </View>
            </View>
            <View style={styles.contentLineSubContainer}>
              <Text style={styles.contentNameText}>SPIC NUMBER</Text>
              <View style={styles.inputConatiner}>
                <PosTextInput
                  {...testProps('al_textinput_555')}
                  style={styles.inputStyle}
                  placeholder={'ID'}
                  keyboardType='default'
                  returnKeyType='next'
                  underlineColorAndroid='transparent'
                  autoCapitalize={'none'}
                  value={spicId}
                  onChangeText={this.onInputSoloParentSpicId}
                />
              </View>
            </View>
            <View style={styles.contentLineSubContainer2}>
              <Text style={styles.contentNameText}>{'NAME OF CHILD/CHILDREN AGED SIX (6) YEARS OR UNDER'}</Text>
              <View style={styles.inputConatiner}>
                <PosTextInput
                  {...testProps('al_textinput_664')}
                  style={styles.inputStyle}
                  placeholder={'Name of Child/Children, separated by commas'}
                  keyboardType='default'
                  returnKeyType='next'
                  underlineColorAndroid='transparent'
                  autoCapitalize={'none'}
                  value={nameOfChild}
                  onChangeText={this.onInputSoloParentNameOfChild}
                />
              </View>
            </View>
          </View>
        );
      }
      case BIRDiscountType.AthletesAndCoaches: {
        const { name, pnstmId } = athletesAndCoachesInfo;
        return (
          <View style={styles.content2}>
            <View style={styles.contentLineSubContainer}>
              <Text style={styles.contentNameText}>CUSTOMER NAME</Text>
              <View style={styles.inputConatiner}>
                <PosTextInput
                  {...testProps('al_textinput_545')}
                  style={styles.inputStyle}
                  placeholder={'Name'}
                  keyboardType='default'
                  returnKeyType='next'
                  underlineColorAndroid='transparent'
                  autoCapitalize={'none'}
                  value={name}
                  onChangeText={this.onInputAthletesAndCoachesName}
                />
              </View>
            </View>
            <View style={styles.contentLineSubContainer}>
              <Text style={styles.contentNameText}>PNSTM ID</Text>
              <View style={styles.inputConatiner}>
                <PosTextInput
                  {...testProps('al_textinput_453')}
                  style={styles.inputStyle}
                  placeholder={'ID'}
                  keyboardType='default'
                  returnKeyType='next'
                  underlineColorAndroid='transparent'
                  autoCapitalize={'none'}
                  value={pnstmId}
                  onChangeText={this.onInputAthletesAndCoachesId}
                />
              </View>
            </View>
          </View>
        );
      }
      case BIRDiscountType.MedalofValor: {
        const { name, movId } = medalofValorInfo;
        return (
          <View style={styles.content2}>
            <View style={styles.contentLineSubContainer}>
              <Text style={styles.contentNameText}>CUSTOMER NAME</Text>
              <View style={styles.inputConatiner}>
                <PosTextInput
                  {...testProps('al_textinput_572')}
                  style={styles.inputStyle}
                  placeholder={'Name'}
                  keyboardType='default'
                  returnKeyType='next'
                  underlineColorAndroid='transparent'
                  autoCapitalize={'none'}
                  value={name}
                  onChangeText={this.onInputMedalofValorName}
                />
              </View>
            </View>
            <View style={styles.contentLineSubContainer}>
              <Text style={styles.contentNameText}>MEDAL OF VALOR ID</Text>
              <View style={styles.inputConatiner}>
                <PosTextInput
                  {...testProps('al_textinput_579')}
                  style={styles.inputStyle}
                  placeholder={'ID'}
                  keyboardType='default'
                  returnKeyType='next'
                  underlineColorAndroid='transparent'
                  autoCapitalize={'none'}
                  value={movId}
                  onChangeText={this.onInputMedalofValorId}
                />
              </View>
            </View>
          </View>
        );
      }
      case BIRDiscountType.Diplomats: {
        const { name, dfaOrVicId, address } = diplomatsInfo;
        return (
          <View style={styles.content2}>
            <View style={styles.contentLineSubContainer}>
              <Text style={styles.contentNameText}>CUSTOMER NAME</Text>
              <View style={styles.inputConatiner}>
                <PosTextInput
                  {...testProps('al_textinput_445')}
                  style={styles.inputStyle}
                  placeholder={'Name'}
                  keyboardType='default'
                  returnKeyType='next'
                  underlineColorAndroid='transparent'
                  autoCapitalize={'none'}
                  value={name}
                  onChangeText={this.onInputDiplomatsName}
                />
              </View>
            </View>
            <View style={styles.contentLineSubContainer}>
              <Text style={styles.contentNameText}>DFA ID / VIC</Text>
              <View style={styles.inputConatiner}>
                <PosTextInput
                  {...testProps('al_textinput_123')}
                  style={styles.inputStyle}
                  placeholder={'ID'}
                  keyboardType='default'
                  returnKeyType='next'
                  underlineColorAndroid='transparent'
                  autoCapitalize={'none'}
                  value={dfaOrVicId}
                  onChangeText={this.onInputDiplomatsId}
                />
              </View>
            </View>
            <View style={styles.contentLineSubContainer2}>
              <Text style={styles.contentNameText}>Address</Text>
              <View style={styles.inputConatiner}>
                <PosTextInput
                  {...testProps('al_textinput_550')}
                  style={styles.inputStyle}
                  placeholder={'Address'}
                  keyboardType='default'
                  returnKeyType='next'
                  underlineColorAndroid='transparent'
                  autoCapitalize={'none'}
                  value={address}
                  onChangeText={this.onInputDiplomatsAddress}
                />
              </View>
            </View>
          </View>
        );
      }
      default:
        return null;
    }
  };

  _updateSections = activeSections => {
    if (Boolean(activeSections) && activeSections.length > 0) {
      this.setState({ activeSections, discountType: get(this.contentList, activeSections[0]).type });
    } else {
      this.setState({ activeSections, discountType: null });
    }
  };

  closeButtonClicked = () => {
    requestAnimationFrame(() => this.props.navigation.goBack());
  };

  renderContent = () => {
    const { activeSections } = this.state;
    return (
      <KeyboardAwareScrollView
        enableOnAndroid
        enableAutomaticScroll
        keyboardShouldPersistTaps='handled'
        extraScrollHeight={120}
        style={{ width: '100%' }}
        contentContainerStyle={{ alignItems: 'center' }}
      >
        <Accordion
          containerStyle={styles.accordionStyle}
          activeSections={activeSections}
          sections={this.contentList}
          touchableComponent={TouchableOpacity}
          expandMultiple={false}
          renderHeader={this._renderHeader}
          renderContent={this._renderContent}
          duration={100}
          onChange={this._updateSections}
          renderAsFlatList={true}
          sectionContainerStyle={styles.sectionContainerStyle}
        />
      </KeyboardAwareScrollView>
    );
  };

  onConfirmHandler = () => {
    const birInfo = this.generateBirInfo();
    requestAnimationFrame(() => {
      const onBIRDiscountConfirmHandler = getParam(this.props, 'onBIRDiscountConfirmHandler');
      onBIRDiscountConfirmHandler && onBIRDiscountConfirmHandler(birInfo);
      this.props.navigation.goBack();
    });
  };

  requestDiscountAccess = onSuccess => {
    const { currentEmployeeId } = this.props;
    this.props.actions.requestAuthorizedAction({
      name: CashierActions.Discount,
      employeeId: currentEmployeeId,
      type: AuthorizationType.Cashier,
      onSuccess,
    });
  };

  onConfirmClicked = () => {
    this.requestDiscountAccess(this.onConfirmHandler);
  };

  generateBirInfo = () => {
    const { discountType, SCAndPWDInfo, soloParentInfo, athletesAndCoachesInfo, medalofValorInfo, diplomatsInfo } = this.state;
    let birInfo = null;
    switch (discountType) {
      case BIRDiscountType.SCAndPWD:
        {
          const { seniorsCount, pwdCount } = SCAndPWDInfo;
          if (seniorsCount > 0 || pwdCount > 0) {
            birInfo = { ...SCAndPWDInfo, discountType };
          }
        }
        break;
      case BIRDiscountType.SoloParent:
        {
          const { name, spicId, nameOfChild } = soloParentInfo;
          if (Boolean(name) && Boolean(spicId) && Boolean(nameOfChild)) {
            birInfo = { discountType, collectedInfo: soloParentInfo };
          }
        }
        break;
      case BIRDiscountType.AthletesAndCoaches:
        {
          const { name, pnstmId } = athletesAndCoachesInfo;
          if (Boolean(name) && Boolean(pnstmId)) {
            birInfo = { discountType, collectedInfo: athletesAndCoachesInfo };
          }
        }
        break;
      case BIRDiscountType.MedalofValor:
        {
          const { name, movId } = medalofValorInfo;
          if (Boolean(name) && Boolean(movId)) {
            birInfo = { discountType, collectedInfo: medalofValorInfo };
          }
        }
        break;
      case BIRDiscountType.Diplomats:
        {
          const { name, dfaOrVicId, address } = diplomatsInfo;
          if (Boolean(name) && Boolean(dfaOrVicId) && Boolean(address)) {
            birInfo = { discountType, collectedInfo: diplomatsInfo };
          }
        }
        break;
      default:
        birInfo = null;
        break;
    }
    return birInfo;
  };

  render() {
    const birInfo = this.generateBirInfo();
    const isConfirmEnabled = Boolean(birInfo);
    return (
      <ModalContainer
        title={'Special Discount'}
        onCloseHandler={this.closeButtonClicked}
        contentStyle={styles.layer}
        mainContentStyle={styles.mainContentStyle}
        noScroll
        clickOutsideClose={false}
        headerRightButton={
          <TouchableOpacity
            {...testProps('CONFIRM')}
            disabled={!isConfirmEnabled}
            style={[styles.button, { backgroundColor: isConfirmEnabled ? '#FC7118' : '#DADADA' }]}
            activeOpacity={1}
            onPress={this.onConfirmClicked}
          >
            <Text style={styles.addText}>{t('CONFIRM')}</Text>
          </TouchableOpacity>
        }
      >
        {this.renderContent()}
      </ModalContainer>
    );
  }
}

// @ts-ignore
export default connect(mapStateToProps, mapDispatchToProps)(ModalAddBIRDiscount);

const styles = StyleSheet.create({
  mainContentStyle: {
    flex: 1,
    alignItems: 'center',
    paddingTop: scaleSizeH(4),
    width: scaleSizeW(1430),
  },
  layer: {
    marginTop: 0,
    justifyContent: 'center',
    alignItems: 'center',
    width: scaleSizeW(1430),
    height: '100%',
    backgroundColor: 'white',
    overflow: 'hidden',
  },
  accordionStyle: {
    width: scaleSizeW(1270),
  },
  header: {
    height: scaleSizeH(88),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderColor: '#E0E0E4',
    borderWidth: 1,
    paddingHorizontal: scaleSizeW(32),
  },
  headerActive: {
    backgroundColor: 'rgba(255, 143, 0, 0.1)',
    borderColor: '#FC7118',
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
  },
  headerText: {
    fontSize: currentThemes.fontSize24,
    fontWeight: '400',
    color: '#303030',
  },
  content: {
    borderColor: '#FC7118A1',
    borderWidth: 1.5,
    borderTopWidth: 0,
    borderBottomLeftRadius: scaleSizeH(8),
    borderBottomRightRadius: scaleSizeH(8),
    paddingHorizontal: scaleSizeW(36),
    paddingVertical: scaleSizeH(8),
  },
  content2: {
    borderColor: '#FC7118A1',
    borderWidth: 1.5,
    borderTopWidth: 0,
    borderBottomLeftRadius: scaleSizeH(8),
    borderBottomRightRadius: scaleSizeH(8),
    paddingHorizontal: scaleSizeW(32),
    paddingVertical: scaleSizeH(40),
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
    flexDirection: 'row',
  },
  contentLineContainer: {
    height: scaleSizeH(104),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  contentMainText: {
    color: '#60636B',
    fontSize: currentThemes.fontSize24,
  },
  contentLineSubContainer: {
    width: scaleSizeW(560),
  },
  contentLineSubContainer2: {
    marginTop: scaleSizeH(36),
    width: scaleSizeW(1198),
  },
  contentNameText: {
    color: '#959595',
    fontSize: currentThemes.fontSize18,
    marginBottom: scaleSizeH(6),
  },
  inputConatiner: {
    height: scaleSizeH(72),
    justifyContent: 'center',
    borderColor: '#E0E0E4',
    borderWidth: StyleSheet.hairlineWidth,
    borderRadius: scaleSizeH(8),
    paddingHorizontal: scaleSizeW(16),
  },
  sectionContainerStyle: {
    marginTop: scaleSizeH(36),
    borderRadius: scaleSizeH(8),
    borderWidth: 0,
  },
  button: {
    width: scaleSizeW(180),
    height: scaleSizeH(108),
    justifyContent: 'center',
    alignItems: 'center',
  },
  addText: {
    color: '#FFF',
    fontSize: currentThemes.fontSize18,
    fontWeight: '500',
  },
  inputStyle: {
    color: '#303030',
    fontSize: currentThemes.fontSize18,
    padding: 0,
    flex: 1,
  },
  divider: {
    height: StyleSheet.hairlineWidth,
    width: scaleSizeW(1198),
    backgroundColor: '#717171',
  },
});
