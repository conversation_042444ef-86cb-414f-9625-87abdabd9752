import { DrawerActions } from '@react-navigation/compat';
import { get, map } from 'lodash';
import React, { PureComponent } from 'react';
import { ActivityIndicator, StyleSheet, Text, TouchableWithoutFeedback, View } from 'react-native';
import * as Animatable from 'react-native-animatable';
import WebView from 'react-native-webview';
import { ConnectedProps, connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { TopMainBar } from '../../components/common';
import MenuWithNotification from '../../components/common/MenuWithNotification';
import { LiveChat } from '../../components/support';
import { CommonColors, IsAndroid, currentThemes, scaleSizeH, scaleSizeW, t, width } from '../../constants';
import { scaleSize, setSpText } from '../../constants/themes';
import { selectCountry, selectSupportPhoneNumberMessages } from '../../sagas/selector';
import { RootState, ScreenProps } from '../../typings';
import { testProps } from '../../utils';
import DeviceInfoData from '../../utils/deviceInfo';
import { getParam } from '../../utils/navigation';

const SupportTabs = [t('Help'), t('Live Chat')];

interface State {
  selectedTab: number;
  webViewDidMount: boolean;
}

interface Props extends ScreenProps, PropsFromRedux {}

const mapStateToProps = (state: RootState) => ({
  supportPhoneNumberMessages: selectSupportPhoneNumberMessages(state),
  country: selectCountry(state),
});

const mapDispatchToProps = dispatch => ({
  actions: bindActionCreators({}, dispatch),
});

const connector = connect(mapStateToProps, mapDispatchToProps);
type PropsFromRedux = ConnectedProps<typeof connector>;
class Support extends PureComponent<Props, State> {
  private didMount = false;
  static navigationOptions = () => ({
    header: null,
  });
  constructor(props) {
    super(props);
    this.state = {
      selectedTab: 0,
      webViewDidMount: false,
    };
  }

  componentDidMount() {
    this.didMount = true;
    if (getParam(this.props, 'liveChat', '')) {
      this.onLeftTabPressHandler(1, '');
    }
  }

  componentWillUnmount(): void {
    this.didMount = false;
  }

  onLeftTabPressHandler = (index, tab: string) => {
    if (index === 0) {
      this.setState({ webViewDidMount: false });
    }
    this.setState({ selectedTab: index });
  };

  renderLeftTabItem = (index, isActived, tab) => {
    return (
      <TouchableWithoutFeedback {...testProps('al_btn_79')} key={index} onPress={() => this.onLeftTabPressHandler(index, tab)}>
        <View key={index} style={[styles.tab, isActived && { backgroundColor: '#008EC4' }]} {...testProps(`al_leftTab_${tab}`)}>
          <Text style={[styles.tabText, isActived && { color: '#FFF' }]}>{tab}</Text>
        </View>
      </TouchableWithoutFeedback>
    );
  };

  renderLeftTab = () => {
    const { selectedTab } = this.state;
    return (
      <View style={{ flex: 1 }}>
        {map(SupportTabs, (tab, index) => {
          const isActived = Number(index) === selectedTab;
          return this.renderLeftTabItem(index, isActived, tab);
        })}
        <View style={styles.versionNumberContainer}>
          <Text style={styles.versionText}>{`Version: ${DeviceInfoData.version}`}</Text>
        </View>
      </View>
    );
  };

  onLoadEnd = () => {
    if (this.didMount) {
      this.setState({ webViewDidMount: true });
    }
  };

  renderSupportContent = () => {
    const { selectedTab } = this.state;
    if (SupportTabs[selectedTab] === t('Help')) {
      return <WebView source={{ uri: 'https://care.storehub.com/en/' }} allowFileAccess={true} onLoadEnd={this.onLoadEnd} />;
    } else if (SupportTabs[selectedTab] === t('Live Chat')) {
      return <LiveChat />;
    }
    return null;
  };

  rendereSupportContacts = () => {
    const message = JSON.parse(JSON.stringify(this.props.supportPhoneNumberMessages)); // convert immutable to json
    if (!message || !message.messages) {
      return null;
    }
    const localMessage = message.messages.find(msg => msg.country === this.props.country);
    if (!message.visible || !localMessage) {
      return null;
    }

    const localMessages = localMessage.messages;

    return (
      <View style={{ position: 'absolute', start: scaleSize(24), bottom: scaleSize(64) }}>
        {localMessages.map((msg, index) => {
          return (
            <Text
              key={msg.body + String(index)}
              style={{ maxWidth: scaleSize(450), fontWeight: msg.bold ? '600' : '400', color: '#333333', fontSize: setSpText(24) }}
            >
              {msg.body}
            </Text>
          );
        })}
      </View>
    );
  };

  openDrawer = () => requestAnimationFrame(() => this.props.navigation.dispatch(DrawerActions.openDrawer()));

  render() {
    const { selectedTab } = this.state;
    const rightHeaderTitle = get(SupportTabs, selectedTab);
    const animateStyle = {
      opacity: this.state.webViewDidMount === true || selectedTab !== 0 ? 0 : 1,
      zIndex: this.state.webViewDidMount === true || selectedTab !== 0 ? 0 : 999,
    };
    return (
      <View style={styles.container}>
        <TopMainBar
          isLeftContainerTitle={true}
          leftIcon={<MenuWithNotification color={CommonColors.Icon} />}
          leftText={t('Support')}
          rightIsIcon={false}
          rightButtonText={''}
          isRightButtonShow={false}
          onLeftClick={this.openDrawer}
          rightText={''}
          rightTitle={rightHeaderTitle}
          rightBackIconShow={false}
        />
        <View style={styles.content}>
          <View style={styles.leftContainer}>
            <View style={styles.tabContent}>{this.renderLeftTab()}</View>
          </View>
          <View style={styles.rightContainer}>
            <Animatable.View pointerEvents={'none'} duration={100} transition={'opacity'} style={[styles.loading, animateStyle]}>
              <ActivityIndicator size={IsAndroid ? 48 : 'small'} animating={true} />
            </Animatable.View>
            {this.renderSupportContent()}
          </View>
        </View>
        {this.rendereSupportContacts()}
      </View>
    );
  }
}
export default connector(Support);

const RIGHT_ROW_MARGIN = scaleSizeW(20);
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: currentThemes.bgMainColor,
  },
  loading: {
    ...StyleSheet.absoluteFillObject,
    flexDirection: 'column',
    justifyContent: 'center',
  },
  tab: {
    width: '100%',
    height: scaleSizeH(88),
    paddingLeft: scaleSizeW(34),
    justifyContent: 'center',
    alignItems: 'flex-start',
  },
  tabText: {
    width: '100%',
    fontSize: currentThemes.fontSize24,
    fontWeight: '500',
    color: '#60636B',
    textAlignVertical: 'center',
  },
  versionText: {
    color: '#60636B',
    fontSize: currentThemes.fontSize16,
    fontWeight: '400',
  },
  versionNumberContainer: {
    position: 'absolute',
    bottom: scaleSizeH(16),
    left: scaleSizeW(34),
  },
  content: {
    flex: 1,
    flexDirection: 'row',
  },
  leftContainer: {
    width: width * 0.31,
  },
  rightContainer: {
    width: width * 0.69,
    paddingHorizontal: scaleSizeW(30),
    paddingTop: scaleSizeH(24),
    paddingBottom: scaleSizeH(20),
  },
  tabContent: {
    flex: 1,
    backgroundColor: '#FFF',
    overflow: 'hidden',
  },
});
