import { DrawerActions } from '@react-navigation/compat';
import { get } from 'lodash';
import React, { PureComponent } from 'react';
import { Alert, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { connect, ConnectedProps } from 'react-redux';

import { bindActionCreators } from 'redux';
import {
  migrateTableLayoutAction,
  printZReadingReport,
  safeCloseZReading,
  setSyncInfo,
  signOut,
  syncActivationStatus,
  syncEmployeeActivityBegin,
  syncEmployeesAction,
  syncLargeProductsAction,
  syncPriceBookAction,
  syncProductsAction,
  syncPromotionsBegin,
  syncQuickSelectLayoutAction,
  syncShiftsAction,
  syncStoreInfoAction,
  syncTableLayoutAction,
  syncTransactionsAction,
  trackLocalSettings,
  trackLocalStorage,
  updateGrowthBook,
  updatePauseMode,
  updateTableLayoutSettings,
  uploadLocalDataToS3,
  checkFreeStorage,
} from '../../actions';
import { NavigatorHeader } from '../../components/common';
import MenuWithNotification from '../../components/common/MenuWithNotification';
import { editMigrateTableLayoutToast } from '../../components/common/TopNotification';
import { GaugeProgress } from '../../components/common/gauge';
import { IconClose, IconFiiledIndicator, IconInfo, IconTick } from '../../components/ui';
import { CommonColors, currentThemes, scaleSizeH, scaleSizeW, t } from '../../constants';
import {
  selectAlreadyMigrateTableLayout,
  selectBirAccredited,
  selectEnableViewLocalSettingsOnIST,
  selectGBEnableLargeProductQuantitySync,
  selectLastZReadingCloseTime,
  selectNeedShowEditTableLayoutToast,
  selectOperationHours,
  selectTableLayoutEnabled,
} from '../../sagas/selector';
import { ScreenProps } from '../../typings';
import { testProps } from '../../utils';
import { isCurZReadingClosed } from '../../utils/datetime';

import { infoZReadingEvent, ZReadingAction } from '../../utils/logComponent';
import { getParam } from '../../utils/navigation';

interface Props extends ScreenProps, PropsFromRedux {}

interface State {
  syncResult: any;
  transaction?: any; // null or undefined as not triggered, empty object as pending, and then success or fail
  shift?: any;
  product?: any;
  employee?: any;
  quickLayout?: any;
  tableLayout?: any;
  storeInfo?: any;
  pricebook?: any;
  fill: number;
}

const InitiaState = { result: {} };

const InitiaEmptyState = {};

const mapStateToProps = state => ({
  tableLayoutEnabled: selectTableLayoutEnabled(state),
  alreadyMigrateTableLayout: selectAlreadyMigrateTableLayout(state),
  needShowEditTableLayoutToast: selectNeedShowEditTableLayoutToast(state),
  operationHours: selectOperationHours(state),
  birAccredited: selectBirAccredited(state),
  lastZReadingCloseTime: selectLastZReadingCloseTime(state),
  enableViewLocalSettingsOnIST: selectEnableViewLocalSettingsOnIST(state),
  enableLargeProductQuantitySync: selectGBEnableLargeProductQuantitySync(state),
});

const mapDispatchToProps = dispatch => ({
  actions: bindActionCreators(
    {
      syncTransactionsAction,
      syncShiftsAction,
      syncProductsAction,
      syncLargeProductsAction,
      syncEmployeesAction,
      syncQuickSelectLayoutAction,
      syncTableLayoutAction,
      syncStoreInfoAction,
      syncPriceBookAction,
      syncEmployeeActivityBegin,
      syncPromotionsBegin,
      syncActivationStatus,
      signOut,
      migrateTableLayoutAction,
      updateTableLayoutSettings,
      printZReadingReport,
      safeCloseZReading,
      updateGrowthBook,
      trackLocalSettings,
      uploadLocalDataToS3,
      trackLocalStorage,
      setSyncInfo,
      updatePauseMode,
      checkFreeStorage,
    },
    dispatch
  ),
});

const SYNC_COUNT = 8;
const SYNC_COUNT_WITHOUT_TABLELAYOUT = 7;

const connector = connect(mapStateToProps, mapDispatchToProps);
type PropsFromRedux = ConnectedProps<typeof connector>;

class Sync extends PureComponent<Props, State> {
  static navigationOptions = () => ({
    headerShown: false,
  });

  private _syncAll = true;
  private syncCount = SYNC_COUNT_WITHOUT_TABLELAYOUT;
  private _needSyncTableLayout = false;
  private needToCloseZReading = false;

  constructor(props) {
    super(props);
    this._syncAll = getParam(props, 'syncAll', undefined);
    const { tableLayoutEnabled, alreadyMigrateTableLayout } = props;
    this._needSyncTableLayout = tableLayoutEnabled || !alreadyMigrateTableLayout;
    if (this._syncAll === undefined || this._syncAll) {
      this.state = {
        syncResult: InitiaEmptyState,
        transaction: InitiaEmptyState,
        shift: InitiaEmptyState,
        product: InitiaEmptyState,
        employee: InitiaEmptyState,
        quickLayout: InitiaEmptyState,
        storeInfo: InitiaEmptyState,
        pricebook: InitiaEmptyState,
        tableLayout: InitiaEmptyState,
        fill: 0,
      };
    } else {
      this.state = {
        syncResult: InitiaEmptyState,
        transaction: InitiaEmptyState,
        shift: InitiaEmptyState,
        product: InitiaState,
        employee: InitiaState,
        quickLayout: InitiaState,
        storeInfo: InitiaState,
        pricebook: InitiaState,
        tableLayout: InitiaState,
        fill: 0,
      };
    }
    this.checkSyncAmount();
  }

  componentDidMount() {
    const syncAll = getParam(this.props, 'syncAll', undefined);
    if (syncAll !== undefined) {
      this._syncAll = syncAll;
      this.sync();
    } else {
      this._syncAll = true;
      this.syncAllInfo();
    }
  }

  componentDidUpdate(prevProps, prevState) {
    if (Math.round(prevState.fill) !== 100 && Math.round(this.state.fill) == 100) {
      this.perposeAfterSyncComplete();
    }
  }

  perposeAfterSyncComplete = () => {
    this.props.actions.updateGrowthBook();
    this.checkSyncAmount();
    if (this.needToCloseZReading) {
      this.promptToCloseZReading();
    }
  };

  checkSyncAmount = () => {
    if (this._needSyncTableLayout) {
      this.syncCount = SYNC_COUNT;
    } else {
      this.syncCount = SYNC_COUNT_WITHOUT_TABLELAYOUT;
    }
  };

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (!Boolean(this.props.tableLayoutEnabled) && Boolean(nextProps.tableLayoutEnabled)) {
      this.syncTableLayout(false);
    }
  }

  sync = () => {
    requestAnimationFrame(() => {
      if (this._syncAll) {
        this.handleSyncAll();
      } else {
        this.handleSyncShiftAndTransactionOnly();
      }
    });
  };

  handleSyncShiftAndTransactionOnly = () => {
    this.setState(
      {
        transaction: InitiaEmptyState,
        shift: InitiaEmptyState,
        fill: 0,
      },
      this.syncShiftAndTransactionOnly
    );
  };

  syncShiftAndTransactionOnly = () => {
    this.recordLastSyncTime();
    this.syncTransaction();
    this.syncShift();
  };

  handleSyncAll = () => {
    this.setState(
      {
        transaction: InitiaEmptyState,
        shift: InitiaEmptyState,
        product: InitiaEmptyState,
        employee: InitiaEmptyState,
        quickLayout: InitiaEmptyState,
        storeInfo: InitiaEmptyState,
        pricebook: InitiaEmptyState,
        tableLayout: InitiaEmptyState,
        fill: 0,
      },
      this.syncAllInfo
    );
  };
  recordLastSyncTime = () => {
    this.props.actions.setSyncInfo({
      lastSyncTime: new Date().toISOString(),
    });
  };

  syncAllInfo = () => {
    const { enableViewLocalSettingsOnIST } = this.props;
    this.recordLastSyncTime();
    this.syncTransaction();
    this.syncShift();
    this.syncProduct();
    this.syncEmployee();
    this.syncStoreInfo();
    this.syncQuikLayout();
    this._needSyncTableLayout && this.syncTableLayout(true);
    this.syncPriceBook();
    this.props.actions.syncActivationStatus({});
    this.props.actions.updatePauseMode();
    this.props.actions.trackLocalSettings();
    if (enableViewLocalSettingsOnIST) {
      this.props.actions.uploadLocalDataToS3();
    }
    this.props.actions.trackLocalStorage();
  };

  syncTransaction = () => {
    this.needToCloseZReading = false;
    const onComplete = {
      callback: payload => {
        requestAnimationFrame(() => {
          const { fill } = this.state;
          if (this._syncAll) {
            this.setState({ transaction: payload, fill: fill + (1 / this.syncCount) * 100 });
          } else {
            this.setState({ transaction: payload, fill: fill + (1 / 2) * 100 });
            this.needToCloseZReading = true;
          }
        });
      },
    };
    this.props.actions.syncTransactionsAction({
      onComplete,
    });
  };

  promptToCloseZReading = () => {
    const { birAccredited, lastZReadingCloseTime, operationHours } = this.props;
    if (!birAccredited || isCurZReadingClosed(lastZReadingCloseTime, operationHours)) return;
    const needEOD = getParam(this.props, 'needEOD', false);
    infoZReadingEvent({
      action: ZReadingAction.PromptToCloseZReading,
      privateDataPayload: { channel: 'Sync', operationHours, lastZReadingCloseTime },
    });
    this.props.navigation.navigate('ModalCloseCurZReading', { needEOD });
  };

  syncShift = () => {
    const onComplete = {
      callback: payload => {
        requestAnimationFrame(() => {
          const { fill } = this.state;
          if (this._syncAll) {
            this.setState({ shift: payload, fill: fill + (1 / this.syncCount) * 100 });
          } else {
            this.setState({ shift: payload, fill: fill + (1 / 2) * 100 });
          }
        });
      },
    };
    this.props.actions.syncShiftsAction({
      onComplete,
    });
  };

  syncProduct = () => {
    const onComplete = {
      callback: payload => {
        const { fill } = this.state;
        this.setState({ product: payload, fill: fill + (1 / this.syncCount) * 100 });
      },
    };
    if (this.props.enableLargeProductQuantitySync) {
      this.props.actions.syncLargeProductsAction({
        onComplete,
      });
    } else {
      this.props.actions.syncProductsAction({
        onComplete,
      });
    }
  };

  employeeDeleteAlert = employeeName => {
    const {
      actions: { signOut },
    } = this.props;
    Alert.alert(
      null,
      t('employeeName is deleted', { name: employeeName }),
      [
        {
          text: t('OK'),
          onPress: () => {
            signOut({ event: 'employeeDeleteAlert' });
          },
        },
      ],
      {
        cancelable: false,
      }
    );
  };

  syncEmployee = () => {
    const onComplete = {
      callback: payload => {
        const { fill } = this.state;
        this.setState({ employee: payload, fill: fill + (1 / this.syncCount) * 100 });
        if (get(payload, 'currentEmployeeDeleted', false) === true) {
          this.employeeDeleteAlert(get(payload, 'currentEmployeeName', ''));
        }
      },
    };
    this.props.actions.syncEmployeesAction({
      onComplete,
    });
  };

  syncQuikLayout = () => {
    const onComplete = {
      callback: payload => {
        const { fill } = this.state;
        this.setState({ quickLayout: payload, fill: fill + (1 / this.syncCount) * 100 });
      },
    };
    this.props.actions.syncQuickSelectLayoutAction({
      onComplete,
    });
  };

  syncTableLayout = (needAddFill: boolean) => {
    const onComplete = {
      callback: payload => {
        const { fill } = this.state;
        const { needShowEditTableLayoutToast } = this.props;
        if (needAddFill) {
          this.setState({ tableLayout: payload, fill: fill + (1 / this.syncCount) * 100 });
        } else {
          this.setState({ tableLayout: payload });
        }
        if (needShowEditTableLayoutToast) {
          editMigrateTableLayoutToast();
          this.props.actions.updateTableLayoutSettings({
            needShowEditTableLayoutToast: false,
          });
        }
      },
    };
    const { alreadyMigrateTableLayout, tableLayoutEnabled } = this.props;
    if (!alreadyMigrateTableLayout) {
      this.props.actions.migrateTableLayoutAction({ onComplete });
    } else if (tableLayoutEnabled) {
      this.props.actions.syncTableLayoutAction({
        onComplete,
      });
    }
  };

  syncStoreInfo = () => {
    const onComplete = {
      callback: payload => {
        const { fill } = this.state;
        this.setState({ storeInfo: payload, fill: fill + (1 / this.syncCount) * 100 });
      },
    };
    this.props.actions.syncStoreInfoAction({
      onComplete,
    });
  };

  syncPriceBook = () => {
    const onComplete = {
      callback: payload => {
        const { fill } = this.state;
        this.setState({ pricebook: payload, fill: fill + (1 / this.syncCount) * 100 });
      },
    };
    this.props.actions.syncPriceBookAction({
      onComplete,
    });
    this.props.actions.syncPromotionsBegin({});
  };

  renderIndicatorIcon = resp => {
    if (resp === undefined || resp === null) return <View />;
    if (!Boolean(Object.keys(resp).length)) return <IconFiiledIndicator width={scaleSizeW(35)} height={scaleSizeH(35)} color={CommonColors.Icon} />;
    if (resp.result) return <IconTick width={scaleSizeW(35)} height={scaleSizeH(35)} color={'#393939'} />;
    return <IconClose width={scaleSizeW(35)} height={scaleSizeH(35)} color={'#393939'} />;
  };

  renderSyncError = resp => {
    if (resp !== undefined && resp !== null && Boolean(Object.keys(resp).length) && !resp.result) {
      return (
        <TouchableOpacity {...testProps('al_btn_333')} style={{ position: 'absolute', right: scaleSizeW(0), top: scaleSizeH(1) }} onPress={this.showErrorPopup}>
          <IconInfo width={scaleSizeW(40)} height={scaleSizeH(40)} color={'#303030'} />
        </TouchableOpacity>
      );
    }
    return <View />;
  };

  showErrorPopup = () => {
    this.props.navigation.navigate('ModalInfo', {
      isShowTitle: true,
      title: t('Error'),
      info: t('The internet connection appears to be offline'),
    });
  };

  renderSyncItem = (name, state) => {
    return (
      <View style={styles.listItem}>
        <View style={styles.indicator}>{this.renderIndicatorIcon(state)}</View>
        <Text style={styles.textStyle}>{name}</Text>
        {this.renderSyncError(state)}
      </View>
    );
  };

  openDrawer = () => requestAnimationFrame(() => this.props.navigation.dispatch(DrawerActions.openDrawer()));

  render() {
    const { fill } = this.state;
    const inProgress = Math.round(fill) !== 100;
    const headerLeftIcons = [{ icon: <MenuWithNotification color={CommonColors.Icon} />, onClick: this.openDrawer }];
    if (!this._syncAll && Math.round(fill) === 100) {
      {
        this.props.actions.checkFreeStorage({
          navigation: this.props.navigation,
          onContinue: () => void 0,
        });
      }
    }
    return (
      <View style={{ flex: 1 }}>
        <NavigatorHeader title={null} style={{ backgroundColor: '#F5F5F7' }} leftIcons={headerLeftIcons} />
        <ScrollView contentContainerStyle={styles.container}>
          <View style={styles.progressStyle}>
            <GaugeProgress
              size={scaleSizeW(320)}
              width={scaleSizeW(20)}
              fill={fill}
              rotation={90}
              cropDegree={130}
              tintColor={currentThemes.buttonBackgroundColor}
              backgroundColor='#E4E2EC'
              strokeCap='circle'
            >
              {Math.round(fill) !== 100 ? (
                this.renderSyncStatus()
              ) : (
                <View style={styles.progressContentSynced}>
                  <Text style={[styles.progressText, { fontSize: currentThemes.fontSize32 }]} {...testProps('al_Synced')}>
                    {t('Synced')}
                  </Text>
                </View>
              )}
            </GaugeProgress>
          </View>
          <View style={{ alignItems: 'center' }}>
            <View>{this.renderSyncAll()}</View>
            <TouchableOpacity
              disabled={inProgress}
              style={[styles.syncButton, inProgress && { backgroundColor: 'rgba(252, 113, 24, 0.35)' }]}
              onPress={this.sync}
              {...testProps('al_sync_again')}
            >
              <Text style={styles.buttonTitle}>{inProgress ? t('SYNCING') : t('SYNC AGAIN')}</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </View>
    );
  }

  renderSyncAll = () => {
    return (
      <View>
        {this.renderSyncItem(t('Sales Information'), this.state.transaction)}
        {this.renderSyncItem(t('Shift Information'), this.state.shift)}
        {this.renderSyncItem(t('Product Information'), this.state.product)}
        {this.renderSyncItem(t('Employee Information'), this.state.employee)}
        {this.renderSyncItem(t('Quick Select Layout'), this.state.quickLayout)}
        {this._needSyncTableLayout && this.renderSyncItem(t('Table Layout'), this.state.tableLayout)}
        {this.renderSyncItem(t('Store Information'), this.state.storeInfo)}
        {this.renderSyncItem(t('Price Book & Promotion'), this.state.pricebook)}
      </View>
    );
  };

  renderSyncPart = () => {
    return (
      <View>
        {this.renderSyncItem(t('Sales Information'), this.state.transaction)}
        {this.renderSyncItem(t('Shift Information'), this.state.shift)}
      </View>
    );
  };

  renderSyncStatus = () => {
    return (
      <View style={[styles.progressContentStatus]}>
        <View style={{ flex: 1, flexDirection: 'row', justifyContent: 'center', alignItems: 'center' }}>
          <Text style={styles.progressText}>{Math.round(this.state.fill)}</Text>
          <Text style={[styles.progressText, { fontSize: currentThemes.fontSize32 }]}>%</Text>
        </View>
        {/* <Text style={styles.syncStatus}>{t('Sync Status')}</Text> */}
      </View>
    );
  };
}

export default connector(Sync);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    paddingTop: scaleSizeH(16),
    paddingBottom: scaleSizeH(16),
  },
  progressStyle: {
    width: scaleSizeW(320),
    height: scaleSizeH(272),
    // overflow: 'hidden',
  },
  listItem: {
    alignItems: 'center',
    flexDirection: 'row',
    width: scaleSizeW(350),
    marginTop: scaleSizeH(22),
  },
  textStyle: {
    color: '#303030',
    fontSize: currentThemes.fontSize24,
  },
  indicator: {
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: scaleSizeW(10),
    width: scaleSizeW(35),
    height: scaleSizeH(35),
  },
  syncButton: {
    backgroundColor: '#FC7118',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: scaleSizeH(32),
    width: scaleSizeW(320),
    height: scaleSizeH(80),
    borderRadius: scaleSizeW(8),
  },
  buttonTitle: {
    fontSize: currentThemes.fontSize18,
    fontWeight: 'bold',
    color: CommonColors.White,
  },
  progressContentStatus: {
    position: 'absolute',
    top: scaleSizeH(120),
    width: scaleSizeW(320),
    height: scaleSizeH(100),
  },
  progressContentSynced: {
    position: 'absolute',
    top: scaleSizeH(180),
    width: scaleSizeW(320),
    height: scaleSizeH(45),
  },
  progressText: {
    textAlign: 'center',
    color: '#60636B',
    fontSize: currentThemes.fontSize80,
    fontWeight: 'bold',
  },
});
