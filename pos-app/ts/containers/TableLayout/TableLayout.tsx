import React, { PureComponent } from 'react';
import { Linking, StatusBar, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import ScrollableTabView, { ScrollableTabBar } from 'react-native-scrollable-tab-view';
import { connect, ConnectedProps } from 'react-redux';
import { IconClock, IconCollect, IconLeft, IconMerge, IconMore, IconMoveOrder, IconOpenOrder, IconReturn, IconSplit } from '../../components/ui';
import {
  AuthorizationType,
  CashierActions,
  currentThemes,
  IsIOS,
  MallIntegrationChannel,
  SalesChannelType,
  SharedStyles,
  STATUS_BAR_HEIGHT,
  SuccessCode,
  t,
  width,
} from '../../constants';
import { LeafState, RootState, ScreenProps } from '../../typings';

import { DrawerActions } from '@react-navigation/compat';
import * as Immutable from 'immutable';
import { chain, find, get, map, noop, throttle } from 'lodash';

import Icon from 'react-native-vector-icons/MaterialIcons';
import { bindActionCreators } from 'redux';
import { createSelector } from 'reselect';
import { TableLayoutDefault } from '.';
import {
  cancelChangingTable,
  checkFreeStorage,
  checkLimitBeforeExecute,
  completeMergeSplitOpenOrder,
  completeMoveOpenOrder,
  createPreOrder,
  genarateMergeOpenOrder,
  getOnlineOpenOrdersWithoutLoading,
  mergeToPay,
  mergeToSave,
  moveOpenOrder,
  MRSError,
  OnlineOpenOrdersWithoutLoadingType,
  refreshMrsOpenOrders,
  refundTransaction,
  requestAuthorizedAction,
  SelectedOpenOrderType,
  setOnlineOrderToSplitMaster,
  setOpenOrderTransactionSession,
  setOrderToSplitMaster,
  setTakeAwayToTransaction,
  startChangingTable,
  startMergeOpenOrder,
  startMoveOpenOrder,
  startSplitOpenOrder,
  updateGeneralSettings,
  updateTransactionTableIdAndPax,
} from '../../actions';
import { NAVIGATOR_PADDING_HORIZONTAL, NavigatorHeader } from '../../components/common';
import MenuWithNotification from '../../components/common/MenuWithNotification';
import { IconBtnItem } from '../../components/common/NavigatorHeader';
import { closeTopNotificationDelay, ContentType } from '../../components/common/TopNotification';
import { ModalMoreAction } from '../../components/modal';
import { ModalErrorParams } from '../../components/modal/ModalError';
import InitLearnInterceptor from '../../components/mrs/InitLearnInterceptor';
import MRSListener from '../../components/mrs/MRSListener';
import MRSInfo from '../../components/register/MRSInfo';
import NewWifiBanner from '../../components/settings/NewWifiBanner';
import IconBadGlobal from '../../components/ui/svgIcons/iconBadGlobal';
import { AllSettingTabs } from '../../constants/settingTabs';
import { CommonColors, scaleSizeH, scaleSizeW } from '../../constants/themes';
import DAL from '../../dal';
import { navigateToSettings } from '../../navigation/commonNavigate';
import { replace } from '../../navigation/navigatorService';
import { onMRSInterceptor } from '../../sagas/mrs/checkSync';
import { selectSmMallLimited } from '../../sagas/pageSelector';
import {
  selectBirAccredited,
  selectBusinessName,
  selectClientConnected,
  selectCurrentNetworkType,
  selectEnablePayByCash,
  selectEnablePayLater,
  selectEnableTableLayout,
  selectInMergeOpenOrder,
  selectInMoveOpenOrder,
  selectInSplitOpenOrder,
  selectInsufficientStorageWarning,
  selectIPAddress,
  selectIsCanPrintDynamicQRCode,
  selectIsDynamicBeepQREnabled,
  selectIsEnabledMRS,
  selectIsMaster,
  selectIsOneZReadingPerDayEnabled,
  selectLastZReadingCloseTime,
  selectLocalCountryMap,
  selectMallIntegrationChannel,
  selectOnChangingTable,
  selectOperationHours,
  selectRegisterObjectId,
  selectShouldLoadOnlineOpenOrders,
  selectTableLayoutEnabled,
} from '../../sagas/selector';
import { testProps } from '../../utils';
import { MixpanelManagerInstance } from '../../utils/Analytics';
import { isLastZReadingNotClose, isPreviousZReadingIncludedUncountedTrx } from '../../utils/datetime';
import eventBus from '../../utils/eventBus';
import { infoZReadingEvent, OrderOperationEnum, ZReadingAction } from '../../utils/logComponent';
import BeepIconBadge from '../Register/BeepIconBadge';
import InternetStateButton from '../Register/InternetStateButton';
import TableLayoutTableItem, { TableItemClickDetail } from './TableLayoutTableItem';

const NAVIGATOR_HEADER_HEIGHT = scaleSizeH(108);
const TAB_BAR_HEIGHT = scaleSizeW(96);

interface Props extends ScreenProps, PropsFromRedux {
  onInit: any;
}

export type OnlineOpenOrderTableType = {
  count: number;
  orders: OnlineOpenOrdersWithoutLoadingType[];
};

export type OnlineOpenOrdersType = {
  sectionId: string;
  sectionName: string;
  tables: Record<string, OnlineOpenOrderTableType>;
};

interface State {
  beingChangTableTransactionId: string;
  onlineOpenOrders: OnlineOpenOrdersType[];
  hintText?: string;
}

const fromImmutableTableLayout = createSelector<RootState, LeafState, any[]>(
  (state: Immutable.Map<string, any>) => {
    return state.getIn(['Storage', 'tableLayout'], Immutable.List());
  },
  immutableTableLayout => {
    return immutableTableLayout.toJS();
  }
);

const selectIsClient = createSelector(selectIsEnabledMRS, selectIsMaster, (isEnabledMRS, isMaster) => {
  return isEnabledMRS && !isMaster;
});
const selectIsMRSOffline = createSelector(selectIsEnabledMRS, selectIPAddress, selectClientConnected, (isEnabledMRS, ip, isConnected) => {
  return isEnabledMRS && (!isConnected || !ip);
});

const mapStateToProps = (state: RootState) => ({
  shiftOpenStatus: state.getIn<boolean>(['Shift', 'ShiftOpenStatus']),
  sections: fromImmutableTableLayout(state),
  enableTableLayout: selectEnableTableLayout(state),
  tableLayoutEnabled: selectTableLayoutEnabled(state),
  enablePayByCash: selectEnablePayByCash(state),
  enablePayLater: selectEnablePayLater(state),
  enableOpenOrders: state.getIn<boolean>(['Settings', 'generalSettings', 'enableOpenOrders']),
  currentEmployeeId: state.getIn<string>(['CurrentEmployee', 'employeeId']),
  storeId: state.getIn<string>(['Storage', 'storeInfo', 'store', '_id']),
  currency: state.getIn<string>(['Storage', 'storeInfo', 'store', 'currency']),
  enableTakeaway: state.getIn<boolean>(['Storage', 'storeInfo', 'store', 'enableTakeaway']),
  shouldLoadOnlineOpenOrders: selectShouldLoadOnlineOpenOrders(state),
  businessName: selectBusinessName(state),
  inMerge: selectInMergeOpenOrder(state),
  inSplit: selectInSplitOpenOrder(state),
  inMove: selectInMoveOpenOrder(state),
  onChangingTable: selectOnChangingTable(state),
  isClient: selectIsClient(state),
  isMRSOffline: selectIsMRSOffline(state),
  isCanPrintDynamicQRCode: selectIsCanPrintDynamicQRCode(state),
  isDynamicBeepQREnabled: selectIsDynamicBeepQREnabled(state),
  operationHours: selectOperationHours(state),
  lastZReadingCloseTime: selectLastZReadingCloseTime(state),
  birAccredited: selectBirAccredited(state),
  registerObjectId: selectRegisterObjectId(state),
  mallIntegrationChannel: selectMallIntegrationChannel(state),
  localCountryMap: selectLocalCountryMap(state),
  netInfoType: selectCurrentNetworkType(state),
  isOneZReadingPerDayEnabled: selectIsOneZReadingPerDayEnabled(state),
  smMallLimited: selectSmMallLimited(state),
  insufficientStorageWarning: selectInsufficientStorageWarning(state),
});
const mapDispatchToProps = dispatch => ({
  actions: bindActionCreators(
    {
      refundTransaction,
      requestAuthorizedAction,
      updateTransactionTableIdAndPax,
      setOpenOrderTransactionSession,
      setTakeAwayToTransaction,
      getOnlineOpenOrdersWithoutLoading,
      updateGeneralSettings,
      startMergeOpenOrder,
      startSplitOpenOrder,
      completeMergeSplitOpenOrder,
      startMoveOpenOrder,
      completeMoveOpenOrder,
      genarateMergeOpenOrder,
      mergeToSave,
      mergeToPay,
      startChangingTable,
      cancelChangingTable,
      moveOpenOrder,
      setOrderToSplitMaster,
      setOnlineOrderToSplitMaster,
      checkLimitBeforeExecute,
      refreshMrsOpenOrders,
      createPreOrder,
      checkFreeStorage,
    },
    dispatch
  ),
});
export type TableSelectedOrdersMap = Record<string, SelectedOpenOrderType[]>;

const connector = connect(mapStateToProps, mapDispatchToProps);
type PropsFromRedux = ConnectedProps<typeof connector>;

export class TableLayoutInner extends PureComponent<Props, State> {
  private viewDidAppear: any;
  private viewDidDisAppear: any;
  private _pagesTablesRef = [];
  private _autoRefreshTimer: ReturnType<typeof setInterval>;
  private subscribeToRefresh: any;
  // {[tableId]: SelectedOpenOrderType[]}
  private selectedOpenOrdersMap: TableSelectedOrdersMap = {};
  private _headerRef: NavigatorHeader;
  private _mergedTransaction;

  constructor(props: Props) {
    super(props);
    this.state = {
      beingChangTableTransactionId: undefined,
      onlineOpenOrders: [],
    };
    this.viewDidAppear = this.props.navigation.addListener('focus', this.onFocus);
    this.viewDidDisAppear = this.props.navigation.addListener('blur', this.onBlur);
  }

  componentDidMount() {
    this.checkZReadingAndMallIntegration();
  }

  refreshData = (isManual = false) => {
    const { enablePayByCash, enablePayLater } = this.props;
    if (enablePayByCash || enablePayLater) {
      this.triggerRefreshOnlineOpenOrderList();
    }
    if (isManual) {
      this.props.actions.refreshMrsOpenOrders({ source: 'tableLayoutRefresh' });
    }
    this.refreshLocalData();
  };

  refreshLocalData = () => {
    const { inMerge, inSplit, inMove } = this.props;
    const initinal = !(inMerge || inSplit || inMove);
    this.updateComponentStatus(initinal);
  };

  onFocus = () => {
    const { shouldLoadOnlineOpenOrders } = this.props;
    this.triggerRefresh();
    if (shouldLoadOnlineOpenOrders) {
      // The page will be refreshed every 30 seconds
      this._autoRefreshTimer = setInterval(this.triggerRefreshOnlineOpenOrderList, 60 * 1000);
      // listen to refresh online open order notification
      this.subscribeToRefresh = eventBus.addListener('RefreshOnlineOpenOrder', this.triggerRefreshOnlineOpenOrderList);
    }
  };

  onBlur = () => {
    this._autoRefreshTimer && clearInterval(this._autoRefreshTimer);
    this._autoRefreshTimer = null;
    this.subscribeToRefresh && eventBus.remove(this.subscribeToRefresh);
    this.subscribeToRefresh = null;
  };

  triggerRefresh = throttle(
    (isManual = false) => {
      this.refreshData(isManual);
    },
    1000,
    { trailing: false }
  );

  componentWillUnmount() {
    this.onBlur();
    this.triggerRefreshOnlineOpenOrderList && this.triggerRefreshOnlineOpenOrderList.cancel();
    this.viewDidAppear && this.viewDidAppear();
    this.viewDidDisAppear && this.viewDidDisAppear();
    this.viewDidAppear = null;
  }

  static navigationOptions = () => ({
    headerShown: false,
  });

  checkZReadingAndMallIntegration = () => {
    const { operationHours, lastZReadingCloseTime, birAccredited, registerObjectId, mallIntegrationChannel, isOneZReadingPerDayEnabled } = this.props;
    if (birAccredited) {
      const { lastZReadingNotClose, needCloseFrom, needCloseTo } = isLastZReadingNotClose(lastZReadingCloseTime, operationHours, registerObjectId);
      const previousZReadingIncludedUncountedTrx =
        isOneZReadingPerDayEnabled && isPreviousZReadingIncludedUncountedTrx(lastZReadingCloseTime, operationHours, registerObjectId);
      const needPrompt = lastZReadingNotClose || previousZReadingIncludedUncountedTrx;
      if (needPrompt) {
        switch (mallIntegrationChannel) {
          // AYALA_MALL
          case MallIntegrationChannel.AYALA_MALL:
            // If POS detected there are transactions created after Zreading last close time, and before the previous operation close time (yesterday)
            infoZReadingEvent({
              action: ZReadingAction.PromptToCloseZReading,
              privateDataPayload: { channel: 'ModalAyalaMallPrompt', operationHours, lastZReadingCloseTime, needCloseFrom, needCloseTo },
            });
            this.props.navigation.navigate('ModalAyalaMallPrompt');
            break;
          case MallIntegrationChannel.ROBINSON_MALL:
          case MallIntegrationChannel.MEGAWORLD:
          case MallIntegrationChannel.ROCKWELL:
          case MallIntegrationChannel.FBDC:
          case MallIntegrationChannel.FILINVEST:
          case MallIntegrationChannel.SFDC:
          case MallIntegrationChannel.EVER_GOTESCO:
            // ROBINSON_MALL
            // If POS detected there are transactions created after Zreading last close time, and before the previous operation close time (yesterday)
            infoZReadingEvent({
              action: ZReadingAction.PromptToCloseZReading,
              privateDataPayload: { channel: 'ModalRobinsonMallPrompt', operationHours, lastZReadingCloseTime, needCloseFrom, needCloseTo },
            });
            this.props.navigation.navigate('ModalRobinsonMallPrompt', { needCloseFrom, needCloseTo });
            break;
          default:
            // If not close z-reading yesterday, then prompt and force close
            // If the z-reading was closed yesterday, then the close time should be greater than the operation time of the day before yesterday and less than or equal to yesterday's operation time
            // So if the last close time was earlier than the operation time the day before, it means that there was no close yesterday
            infoZReadingEvent({
              action: ZReadingAction.PromptToCloseZReading,
              privateDataPayload: { channel: 'ModalCloseLastZReading', operationHours, lastZReadingCloseTime, needCloseFrom, needCloseTo },
            });
            this.props.navigation.navigate('ModalCloseLastZReading', { needCloseFrom, needCloseTo });
            break;
        }
      }
    }
  };

  getOnlineOpenOrderList = (initial = false) => {
    const {
      businessName,
      storeId,
      sections,
      enablePayByCash,
      enablePayLater,
      actions: { getOnlineOpenOrdersWithoutLoading, updateGeneralSettings },
      shouldLoadOnlineOpenOrders,
    } = this.props;
    if (!shouldLoadOnlineOpenOrders) return;
    const onSuccess = {
      callback: payload => {
        const { onlineOpenOrders = [] } = payload.res;
        if (!enablePayByCash && !enablePayLater && onlineOpenOrders.length === 0) {
          updateGeneralSettings({ shouldLoadOnlineOpenOrders: false });
          this.onBlur();
        }
        const groupList = {};
        chain(onlineOpenOrders)
          .groupBy(item => {
            return item.tableId;
          })
          .forEach((value, key) => {
            groupList[key] = {
              count: value.length,
              orders: value || [],
            };
          })
          .value();
        const onlineOpenOrderList = [];
        map(sections, section => {
          const { sectionId, sectionName, tables } = section;
          const onlineOpenOrderItem = { sectionId, sectionName, tables: {} };
          map(tables, table => {
            const { tableName } = table;
            onlineOpenOrderItem.tables[tableName] = groupList[tableName] || { count: 0, orders: [] };
          });
          onlineOpenOrderList.push(onlineOpenOrderItem);
        });
        this.setState({ onlineOpenOrders: onlineOpenOrderList }, () => {
          this.updateComponentStatus(initial);
        });
      },
    };

    const onFailure = {
      callback: () => {
        if (!enablePayByCash && !enablePayLater) {
          updateGeneralSettings({ shouldLoadOnlineOpenOrders: false });
          this.onBlur();
        }
        this.updateComponentStatus();
      },
    };
    getOnlineOpenOrdersWithoutLoading({
      businessName,
      storeId,
      includePayLater: true,
      includePayByCash: true,
      filter: '',
      onSuccess,
      onFailure,
    });
  };

  triggerRefreshOnlineOpenOrderList = throttle(this.getOnlineOpenOrderList, 1000, { trailing: false });

  openDrawer = () => requestAnimationFrame(() => this.props.navigation.dispatch(DrawerActions.openDrawer()));

  toTakeaway = () => {
    requestAnimationFrame(() => {
      const {
        actions: { setTakeAwayToTransaction },
        navigation,
      } = this.props;
      setTakeAwayToTransaction({ salesChannel: SalesChannelType.TAKEAWAY });
      navigation.navigate('Register');
    });
  };

  onTapInternetButton = () => {
    this.props.navigation.navigate('ModalInfo', {
      title: t('Your network is unavailable'),
      isShowTitle: true,
      info: t('Check your network settings'),
      titleIcon: <IconBadGlobal color={'#D3D8EB'} width={25} height={25} />,
      okText: t('Go to Settings'),
      onSubmitHandler: () => {
        Linking.openSettings();
      },
    });

    MixpanelManagerInstance.throttledTrack('No Internet Connection Pop up');
  };

  render() {
    const { enableTableLayout, tableLayoutEnabled, shiftOpenStatus } = this.props;
    return (
      <>
        {!shiftOpenStatus && enableTableLayout ? this.renderOpenShift() : this.renderTableLayout(tableLayoutEnabled && enableTableLayout)}
        <InitLearnInterceptor />
      </>
    );
  }

  renderOpenShift = () => {
    return (
      <View style={styles.container}>
        <StatusBar backgroundColor='transparent' barStyle='dark-content' />
        <View style={styles.titleBar}>
          <TouchableOpacity
            hitSlop={{ left: 100, right: 100, top: 100, bottom: 100 }}
            style={SharedStyles.touchableIconContainer}
            onPress={this.openDrawer}
            {...testProps('al_drawer')}
          >
            <MenuWithNotification color={CommonColors.Icon} />
          </TouchableOpacity>
        </View>
        <View style={styles.container}>
          <View style={{ flex: 5, justifyContent: 'center' }}>
            <Text style={{ fontFamily: 'Noteworthy-Bold', fontSize: currentThemes.fontSize72, color: '#60636B', width: '100%', textAlign: 'center' }}>
              {"Sorry,We're"}
            </Text>
            <Text style={{ fontFamily: 'Chalkboard-Bold', fontSize: currentThemes.fontSize144, color: '#60636B', width: '100%', textAlign: 'center' }}>
              {'CLOSED'}
            </Text>
          </View>
          <View style={{ flex: 2, justifyContent: 'center', alignItems: 'center' }}>
            <TouchableOpacity
              {...testProps('al_btn_846')}
              onPress={this.onCloseShiftHandler}
              style={{ justifyContent: 'center', height: scaleSizeH(112), width: scaleSizeW(640), backgroundColor: '#FC7118', marginBottom: scaleSizeH(32) }}
            >
              <Text style={{ fontSize: currentThemes.fontSize30, fontWeight: 'bold', color: '#FFF', textAlign: 'center' }}>{'Open Register'}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  };

  onStartMergeOpenOrder = () => {
    this.selectedOpenOrdersMap = {};
    this.hideMenu();
    this.props.actions.startMergeOpenOrder();
    this._headerRef && this._headerRef.hideRightButtons();
    this._mergedTransaction = null;
    this.setState({ hintText: t('Select minimum 2 tables') });
  };

  onStartMoveOpenOrder = () => {
    this.selectedOpenOrdersMap = {};
    this.hideMenu();
    this.props.actions.startMoveOpenOrder();
    this.setState({ hintText: t('Select a table') });
  };

  onStartSplitOpenOrder = () => {
    this.props.actions.startSplitOpenOrder();
    this.hideMenu();
    this.setState({ hintText: t('Select to split') });
  };

  headerRefHandler = ref => {
    this._headerRef = ref;
  };

  onCancelChangingTable = () => {
    const {
      actions: { cancelChangingTable },
    } = this.props;
    cancelChangingTable();
    this.updateHintTextAndMenu(false);
  };

  goBack = () => {
    const {
      onChangingTable,
      inMerge,
      inSplit,
      actions: { completeMoveOpenOrder, completeMergeSplitOpenOrder },
    } = this.props;
    if (inMerge) {
      if (onChangingTable) {
        this.onCancelChangingTable();
      } else {
        this._pagesTablesRef &&
          this._pagesTablesRef.map(tableRef => {
            tableRef && tableRef.initinalSelectedMergeOrderCount();
          });
        completeMergeSplitOpenOrder();
        this._mergedTransaction = null;
        this.selectedOpenOrdersMap = {};
      }
    } else if (inSplit) {
      completeMergeSplitOpenOrder();
    } else {
      this.selectedOpenOrdersMap = {};
      if (onChangingTable) {
        this._pagesTablesRef &&
          this._pagesTablesRef.map(tableRef => {
            tableRef && tableRef.initinalSelectedMergeOrderCount();
          });
        this.onCancelChangingTable();
      } else {
        completeMoveOpenOrder();
      }
    }
  };

  onStartChangingTable = () => {
    const {
      actions: { startChangingTable },
    } = this.props;
    startChangingTable();
    this.updateHintTextAndMenu(true);
  };

  onMergeToSave = ({ mergedTransaction, warning, withSameTableId }) => {
    const { navigation } = this.props;
    this._mergedTransaction = mergedTransaction;
    if (Boolean(warning)) {
      const { title, message } = warning;
      navigation.navigate('ModalInfo', {
        title,
        isShowTitle: true,
        isCancelButtonRight: true,
        textAlign: 'center',
        info: message,
        onCancelHandler: () => null,
        okText: t('YES PROCEED'),
        onSubmitHandler: () => {
          if (withSameTableId) {
            this.onSaveMergedOpenOrder(mergedTransaction.tableId);
          } else {
            this.onStartChangingTable();
          }
        },
      });
    } else {
      if (withSameTableId) {
        this.onSaveMergedOpenOrder(mergedTransaction.tableId);
      } else {
        this.onStartChangingTable();
      }
    }
  };

  // mergeToSave: merge the order and save the merged OpenOrder to local DB， and delete the original Open orders
  onClickMergeToSave = () => {
    const {
      actions: { genarateMergeOpenOrder },
    } = this.props;
    const onResult = async result => {
      const { mergedTransaction } = result;
      const checkResult = await this.checkCanProposer(OrderOperationEnum.MergeAndSave, mergedTransaction);
      if (checkResult) {
        this.onMergeToSave(result);
      } else {
        this.goBack();
      }
    };
    genarateMergeOpenOrder({ selectedOpenOrderIds: this.getSelectedOpenOrderIds(), operation: 'mergeAndSave', onResult });
  };

  checkCanProposer = (orderOperation: OrderOperationEnum, transaction): Promise<boolean> => {
    return new Promise(res => {
      const onComplete = (error: MRSError) => {
        return res(onMRSInterceptor(error));
      };

      this.props.actions.checkLimitBeforeExecute({
        transaction,
        orderOperation,
        onComplete,
      });
    });
  };

  onMergeToPay = ({ mergedTransaction, warning }) => {
    const {
      navigation,
      actions: { mergeToPay },
    } = this.props;
    this._mergedTransaction = mergedTransaction;
    if (Boolean(warning)) {
      const { title, message } = warning;
      navigation.navigate('ModalInfo', {
        title,
        isShowTitle: true,
        needGoBackWhenSubmit: false,
        isCancelButtonRight: true,
        textAlign: 'center',
        info: message,
        okText: t('YES PROCEED'),
        onCancelHandler: () => null,
        onSubmitHandler: () => {
          mergeToPay({ mergedTransaction });
          navigation.replace('Checkout', { needClearTransactionWhenBack: true });
        },
      });
    } else {
      mergeToPay({ mergedTransaction });
      navigation.navigate('Checkout', { needClearTransactionWhenBack: true });
    }
  };

  getSelectedOpenOrderIds = () => {
    let selectedOpenOrderIds = [];
    if (Object.keys(this.selectedOpenOrdersMap).length > 0) {
      for (const selectedOpenOrders of Object.values(this.selectedOpenOrdersMap)) {
        selectedOpenOrderIds = selectedOpenOrderIds.concat(map(selectedOpenOrders, it => it.id));
      }
    }
    return selectedOpenOrderIds;
  };

  // mergeToPay: merge the order and setTransaction to redux to display, and go to CheckOut page
  // once paid success, save the new order and delete the original Open orders
  onClickMergeToPay = () => {
    const {
      actions: { genarateMergeOpenOrder },
    } = this.props;

    const onResult = async result => {
      const { mergedTransaction, mergeConflict } = result;
      if (mergeConflict) {
        const params: ModalErrorParams = {
          titleIcon: 'warn',
          title: t('Cannot MERGE & PAY'),
          subTitle: t('Detected different salesperson'),
          okText: t('OK'),
          onOk: noop,
          style: { width: IsIOS ? scaleSizeW(860) : scaleSizeW(730) },
          closeable: true,
        };
        this.props.navigation.navigate('ModalError', params);
        return;
      }
      const checkResult = await this.checkCanProposer(OrderOperationEnum.MergeAndCheckOut, mergedTransaction);
      if (checkResult) {
        this.onMergeToPay(result);
      } else {
        this.goBack();
      }
    };
    genarateMergeOpenOrder({ selectedOpenOrderIds: this.getSelectedOpenOrderIds(), operation: 'mergeAndPay', onResult });
  };
  onManualRefresh = () => {
    this.triggerRefresh(true);
  };

  renderTableLayout = tableLayoutEnabled => {
    const { enableOpenOrders, sections, enableTakeaway, enablePayByCash, inMerge, inSplit, inMove, onChangingTable, isClient, isMRSOffline, smMallLimited } =
      this.props;
    const buttons = [];
    const inEditingOPenOrder = inMerge || inSplit || inMove;
    if (enableOpenOrders || enablePayByCash) {
      buttons.push({
        icon: <IconOpenOrder width={scaleSizeW(41)} height={scaleSizeH(40)} color={CommonColors.Icon} />,
        name: t('Open Order'),
        onClick: this.goToOpenOrderLists,
      });

      buttons.push({
        isDivider: true,
      });
    }

    buttons.push({
      icon: <IconClock width={scaleSizeW(41)} height={scaleSizeH(40)} color={CommonColors.Icon} />,
      name: t('PreOrder'),
      onClick: this.onPressPreOrder,
    });

    buttons.push({
      icon: <IconCollect width={scaleSizeW(41)} height={scaleSizeH(40)} color={CommonColors.Icon} />,
      name: t('Collect Preorder'),
      onClick: this.onPressCollectPreOrder,
    });

    buttons.push({
      isDivider: true,
    });

    buttons.push({
      icon: <IconMoveOrder width={scaleSizeW(41)} height={scaleSizeH(40)} color={CommonColors.Icon} />,
      name: t('Move Order'),
      onClick: this.onStartMoveOpenOrder,
    });
    buttons.push({
      icon: <IconMerge width={scaleSizeW(41)} height={scaleSizeH(40)} color={CommonColors.Icon} />,
      name: t('Merge'),
      onClick: this.onStartMergeOpenOrder,
    });
    buttons.push({
      icon: <IconSplit width={scaleSizeW(41)} height={scaleSizeH(40)} color={CommonColors.Icon} />,
      name: t('Split'),
      onClick: this.onStartSplitOpenOrder,
    });

    if (!smMallLimited) {
      buttons.push({
        isDivider: true,
      });
      buttons.push({
        icon: <IconReturn width={scaleSizeW(41)} height={scaleSizeH(40)} color={CommonColors.Icon} />,
        name: t('Manual Refund'),
        onClick: this.onManualRefundHandler,
      });
    }

    const leftIcons: IconBtnItem[] = inEditingOPenOrder
      ? [
          {
            icon: <IconLeft color={'#FC7118'} width={scaleSizeW(48)} height={scaleSizeH(49)} />,
            onClick: this.goBack,
          },
        ]
      : [
          {
            icon: <MenuWithNotification color={CommonColors.Icon} />,
            onClick: this.openDrawer,
          },
        ];

    const rightIcons: IconBtnItem[] = inEditingOPenOrder
      ? []
      : [
          {
            icon: <BeepIconBadge />,
          },
          {
            icon: <IconMore color={CommonColors.Icon} />,
            onClick: this.showMenu,
          },
        ];
    const leftButtons = [];
    if (!inEditingOPenOrder) {
      leftButtons.push(<InternetStateButton onPress={this.onTapInternetButton} />);
    }

    if (!inEditingOPenOrder) {
      leftButtons.push(
        <TouchableOpacity {...testProps('al_btn_682')} key={'TAKEAWAY'} style={styles.takeAwayContainer} onPress={this.toTakeaway}>
          <Text style={styles.takeAwayText}>{t('TAKE AWAY')}</Text>
        </TouchableOpacity>
      );
    }
    const rightButtons = [];
    if (inMerge && !onChangingTable) {
      rightButtons.push(
        <TouchableOpacity
          {...testProps('al_btn_581')}
          key={'MERGE&SAVE'}
          style={[styles.rightButtonContainer, isMRSOffline && { backgroundColor: '#DBDBDB' }]}
          onPress={this.onClickMergeToSave}
        >
          <Text style={styles.rightButtonText}>MERGE & SAVE</Text>
        </TouchableOpacity>
      );
      rightButtons.push(
        <TouchableOpacity
          {...testProps('al_btn_499')}
          key={'MERGE&PAY'}
          style={[styles.rightButtonContainer, (isClient || isMRSOffline) && { backgroundColor: '#DBDBDB' }]}
          onPress={this.onClickMergeToPay}
        >
          <Text style={styles.rightButtonText}>MERGE & PAY</Text>
        </TouchableOpacity>
      );
    }
    const { hintText } = this.state;
    let title = t('Table Layout');
    if (inMerge) {
      if (onChangingTable) {
        title = 'Merge & Save';
      } else {
        title = t('Merge Order');
      }
    } else if (inSplit) {
      title = t('Split Order');
    } else if (inMove) {
      title = t('Move Order');
    }
    return (
      <View style={styles.container}>
        <StatusBar backgroundColor='transparent' barStyle='dark-content' />
        <NavigatorHeader
          ref={this.headerRefHandler}
          withHitSlop
          title={title}
          leftIcons={leftIcons}
          onRefreshClick={inEditingOPenOrder ? null : this.onManualRefresh}
          rightIcons={rightIcons}
          showLeftButton={enableTakeaway}
          leftButtons={leftButtons}
          rightButtons={rightButtons}
          renderLeft={() => <MRSInfo style={{ marginLeft: scaleSizeW(40) }} />}
        />
        <NewWifiBanner onFixPress={this.onFixPress} inTableLayout={true} />
        <ModalMoreAction ref={this.setMenuRef} list={buttons} />
        {inEditingOPenOrder && <Text style={[styles.mergeSplitHint, onChangingTable && { backgroundColor: '#8BBBFF' }]}>{hintText}</Text>}
        {tableLayoutEnabled && (sections.length > 0 ? this.renderTableLayoutContent() : <TableLayoutDefault isEditLayout={false} />)}
      </View>
    );
  };

  onFixPress = () => {
    navigateToSettings({ tabName: AllSettingTabs.DefaultNetwork, timeStamp: Date.now() });
  };

  renderTableLayoutContent = () => {
    const { sections } = this.props;
    const count = get(sections, 'length', 0);
    const tabPages = this.renderTabPages();

    return (
      <ScrollableTabView
        scrollWithoutAnimation
        style={SharedStyles.flexOne}
        initialPage={0}
        renderTabBar={this.renderTabBar}
        onChangeTab={this.onChangeTabHandler}
        locked={true}
        tabBarUnderlineStyle={styles.tabBarUnderlineStyle}
        tabBarPosition='bottom'
        prerenderingSiblingsNumber={count}
      >
        {tabPages.length > 0 && tabPages}
        <MRSListener callback={this.refreshLocalData} />
      </ScrollableTabView>
    );
  };

  renderTabPages = () => {
    const { sections, currency, navigation, inMerge, inSplit, inMove, onChangingTable, isCanPrintDynamicQRCode, isDynamicBeepQREnabled, localCountryMap } =
      this.props;
    const { onlineOpenOrders } = this.state;
    const hasSelectedOnlineOrders = !!chain(this.selectedOpenOrdersMap)
      .values()
      .flatten()
      .find(v => v.isPayLater)
      .value();
    return map(sections, (section, index) => {
      const { sectionName, tables } = section;
      const tabLabel = { tabLabel: sectionName };
      const tabPayByCashOrders = Boolean(onlineOpenOrders[index]) ? onlineOpenOrders[index].tables : {};
      return (
        <View key={`${index}/${sectionName}`} testID='tabPages' style={SharedStyles.flexOne} {...tabLabel}>
          {map(tables, table => {
            const { tableName } = table;
            const onlineOrders = Boolean(tabPayByCashOrders[tableName]) ? tabPayByCashOrders[tableName].orders || [] : [];
            const disablePut = hasSelectedOnlineOrders && onlineOrders.length > 0 && onChangingTable;

            return (
              <TableLayoutTableItem
                ref={this._tableRef}
                inMerge={inMerge}
                inSplit={inSplit}
                inMove={inMove}
                isCanPrintDynamicQRCode={isCanPrintDynamicQRCode}
                onChangingTable={onChangingTable}
                navigation={navigation}
                key={`${index}/${sectionName}/${tableName}`}
                table={table}
                currency={currency}
                onClickTable={this.onClickTable}
                onLongPressTable={isDynamicBeepQREnabled ? this.onLongPressTable : null}
                onlineOrders={onlineOrders}
                disablePut={disablePut}
                localCountryMap={localCountryMap}
              />
            );
          })}
        </View>
      );
    });
  };

  renderTabBar = () => {
    const { sections } = this.props;
    const count = get(sections, 'length', 0);
    const tabsContainerWidth = scaleSizeW(count * 323);
    const containerWidth = width - scaleSizeW(64);
    return (
      <ScrollableTabBar
        style={[styles.scrollableTabBarStyle, { width: containerWidth }]}
        tabStyle={styles.tabBaseStyle}
        tabsContainerStyle={[styles.tabsContainerStyle, { width: tabsContainerWidth < containerWidth ? containerWidth : tabsContainerWidth }]}
        renderTab={(id, page, isTabActive, onPressHandler, onLayoutHandler) => {
          const textStyle = isTabActive ? styles.textActiveStyle : styles.textInactiveStyle;
          const tabStyle = id ? (isTabActive ? styles.tabActiveStyle : styles.tabBaseStyle) : styles.tabBaseStyle;
          return (
            <TouchableOpacity
              {...testProps('al_btn_142')}
              style={tabStyle}
              key={`scrollable_tab_button_${page}`}
              onPress={() => {
                onPressHandler(page);
              }}
              onLayout={onLayoutHandler}
              activeOpacity={1}
            >
              <View style={styles.tabBaseStyle}>
                <Text style={textStyle}>{id}</Text>
              </View>
            </TouchableOpacity>
          );
        }}
      />
    );
  };

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  onChangeTabHandler = () => {};

  _menu: ModalMoreAction = null;

  setMenuRef = ref => {
    this._menu = ref;
  };

  hideMenu = () => {
    this._menu && this._menu.dismiss();
  };

  showMenu = () => {
    this._menu && this._menu.show();
  };

  navigateToOpenOrderLists = () => {
    this.props.navigation.navigate('OpenOrderLists', {
      shouldGoBackWhenPrssItem: false,
      onOpenOrderPress: this.editOpenOrder,
    });
  };

  editOpenOrder = transactionId => {
    const onComplete = {
      callback: () => {
        // NOP
      },
    };
    this.props.actions.setOpenOrderTransactionSession({ transactionId, onComplete });
  };

  goToOpenOrderLists = () => {
    this.navigateToOpenOrderLists();
    this.hideMenu();
  };

  onMultipleChooseBack = (tableLayoutItemRef, tableName: string, newSelectedOpenOrdersMap: TableSelectedOrdersMap) => {
    this.selectedOpenOrdersMap = newSelectedOpenOrdersMap;
    const curTableSelectedOrders = get(this.selectedOpenOrdersMap, tableName, []);
    tableLayoutItemRef.updateSelectedMergeOrderCount(curTableSelectedOrders.length);
    this.updateHintTextAndMenu();
  };

  updateHintTextAndMenu = (onChangingTable = false) => {
    const { inMerge, inSplit, inMove } = this.props;
    if (inMerge) {
      if (!onChangingTable) {
        const selectedOpenOrderCount = this.getSelectedOpenOrderIds().length;
        if (selectedOpenOrderCount > 1) {
          this._headerRef && this._headerRef.showRightButtons();
        } else {
          this._headerRef && this._headerRef.hideRightButtons();
        }
        if (selectedOpenOrderCount === 0) {
          this.setState({ hintText: t('Select minimum 2 tables') });
        } else if (selectedOpenOrderCount === 1) {
          this.setState({ hintText: t('1 order selected') });
        } else {
          this.setState({ hintText: t('multiple orders selected', { count: selectedOpenOrderCount }) });
        }
      } else {
        this.setState({ hintText: t('Select table to merge orders to') });
      }
    } else if (inSplit) {
      this.setState({ hintText: t('Select to split') });
    } else if (inMove) {
      if (!onChangingTable) {
        this.setState({ hintText: t('Select a table') });
      } else {
        this.setState({ hintText: t('Select 1 table to move order to') });
      }
    }
  };

  onSaveMergedOpenOrder = (tableName, onMergeSuccess = noop) => {
    const {
      actions: { mergeToSave },
    } = this.props;
    this._mergedTransaction.tableId = tableName;
    mergeToSave({
      mergedTransaction: this._mergedTransaction,
      onComplete: error => {
        if (error.errorCode === SuccessCode) {
          this._pagesTablesRef &&
            this._pagesTablesRef.map(tableRef => {
              tableRef && tableRef.initinalSelectedMergeOrderCount();
            });
          this.updateComponentStatus(true);
          this._mergedTransaction = null;
          this.selectedOpenOrdersMap = {};
          onMergeSuccess();
        } else {
          this._pagesTablesRef &&
            this._pagesTablesRef.map(tableRef => {
              if (tableRef) {
                const tableName = get(tableRef, 'props.table.tableName');
                this.onMultipleChooseBack(tableRef, tableName, this.selectedOpenOrdersMap);
              }
            });
        }
      },
    });
  };

  onSelectTargetTable = (newSelectedOpenOrdersMap: TableSelectedOrdersMap, targetTableId: string, mergedTransaction, onMergeSuccess) => {
    const { inMerge } = this.props;
    if (inMerge) {
      this._mergedTransaction = mergedTransaction;
      this.selectedOpenOrdersMap = newSelectedOpenOrdersMap;
      this.onSaveMergedOpenOrder(targetTableId, onMergeSuccess);
    } else {
      this.changeOrderTable(targetTableId);
    }
  };

  changeOrderTable = tableName => {
    const {
      actions: { moveOpenOrder },
    } = this.props;
    const pendingMoveOrders = Object.values(this.selectedOpenOrdersMap)[0] as SelectedOpenOrderType[];
    const hasSelectedOnlineOrders = find(pendingMoveOrders, v => v.isPayLater);
    moveOpenOrder({
      pendingMoveOrders,
      tableId: tableName,
      onComplete: (error: MRSError) => {
        if (error.errorCode === SuccessCode) {
          this.selectedOpenOrdersMap = {};
          if (hasSelectedOnlineOrders) {
            this.triggerRefreshOnlineOpenOrderList(true);
            this.showRescanModal();
          } else {
            this.updateComponentStatus(true);
          }
        } else {
          this.goBack();
        }
      },
    });
  };

  showRescanModal = () => {
    this.props.navigation.navigate('ModalInfo', {
      title: t('Please request customer to rescan new table QR Code'),
      isShowTitle: true,
      notShowInfo: true,
      okText: t('OK'),
      onSubmitHandler: this.onPressOkAfterMoveOrder,
    });
  };

  onPressOkAfterMoveOrder = () => {
    closeTopNotificationDelay(3 * 1000, [ContentType.Success]);
  };

  onClickTable = (detail: TableItemClickDetail) => {
    const { refs: tableLayoutItemRef, table, hasOpenOrder, orderCount, localOrderCount, transactionId, onlineOrders } = detail;
    const {
      inMerge,
      inSplit,
      inMove,
      onChangingTable,
      actions: { setOrderToSplitMaster, setOnlineOrderToSplitMaster },
      navigation: { navigate },
    } = this.props;
    const { tableName } = table;
    if (inMove) {
      if (onChangingTable) {
        this.changeOrderTable(tableName);
      } else {
        if (orderCount === 1 && localOrderCount === 1) {
          tableLayoutItemRef && tableLayoutItemRef.updateSelectedMergeOrderCount(1);
          this.selectedOpenOrdersMap[tableName] = [{ id: transactionId, isPayLater: false, srcTableId: tableName }];
          this.onStartChangingTable();
        } else if (orderCount === 1 && get(onlineOrders, '0.isPayLater', false)) {
          const { receiptNumber, isPayLater, modifiedTime } = onlineOrders[0];
          tableLayoutItemRef && tableLayoutItemRef.updateSelectedMergeOrderCount(1);
          // online pay later order
          this.selectedOpenOrdersMap[tableName] = [{ id: receiptNumber, isPayLater, srcTableId: tableName, modifiedTime }];
          this.onStartChangingTable();
        } else {
          navigate('OpenOrderLists', {
            tableId: table.tableName,
            shouldGoBackWhenPrssItem: false,
            selectedOpenOrdersMap: this.selectedOpenOrdersMap,
            onSelectTargetTable: this.onSelectTargetTable,
            onMultipleChooseBack: newSelectedOpenOrderIdMap => {
              this.onMultipleChooseBack(tableLayoutItemRef, tableName, newSelectedOpenOrderIdMap);
            },
          });
        }
      }
    } else if (inMerge) {
      if (onChangingTable) {
        if (Boolean(this._mergedTransaction)) {
          this.onSaveMergedOpenOrder(tableName);
        }
      } else {
        const curTableSelectedOrderIds = get(this.selectedOpenOrdersMap, tableName, []);
        if (localOrderCount === 1 && orderCount === 1) {
          if (curTableSelectedOrderIds.length > 0) {
            delete this.selectedOpenOrdersMap[tableName];
            tableLayoutItemRef && tableLayoutItemRef.updateSelectedMergeOrderCount(0);
          } else {
            this.selectedOpenOrdersMap[tableName] = [{ id: transactionId }];
            tableLayoutItemRef && tableLayoutItemRef.updateSelectedMergeOrderCount(1);
          }
          this.updateHintTextAndMenu();
        } else {
          navigate('OpenOrderLists', {
            tableId: table.tableName,
            shouldGoBackWhenPrssItem: false,
            selectedOpenOrdersMap: this.selectedOpenOrdersMap,
            onSelectTargetTable: this.onSelectTargetTable,
            onMultipleChooseBack: (newSelectedOpenOrderIdMap, mergedTransaction?) => {
              this.onMultipleChooseBack(tableLayoutItemRef, tableName, newSelectedOpenOrderIdMap);
            },
          });
        }
      }
    } else if (inSplit) {
      if (localOrderCount === 1 && orderCount === 1) {
        const onComplete = (result: boolean) => {
          if (result) {
            navigate('SplitOrder');
          } else {
            this.goBack();
          }
        };
        setOrderToSplitMaster({ transactionId, onComplete });
      } else if (orderCount === 1 && get(onlineOrders, '0.isPayLater', false)) {
        const { receiptNumber, tableId, transactionId } = onlineOrders[0];
        navigate('ModalInfo', {
          title: t('Proceed to split order?'),
          isShowTitle: true,
          needGoBackWhenCancel: true,
          needGoBackWhenSubmit: false,
          textAlign: 'center',
          info: (
            <>
              {t('split pay later order1')}
              <Text style={{ fontWeight: 'bold' }}>{t('removed')}</Text>
              {t('split pay later order2')}
            </>
          ),
          okText: t('SPLIT ORDER'),
          cancelText: t('CANCEL'),
          onSubmitHandler: () => setOnlineOrderToSplitMaster({ receiptNumber, transactionId, tableId }),
          onCancelHandler: noop,
        });
      } else {
        navigate('OpenOrderLists', {
          tableId: table.tableName,
          shouldGoBackWhenPrssItem: false,
          onOpenOrderPress: this.editOpenOrder,
        });
      }
    } else {
      if (hasOpenOrder) {
        navigate('OpenOrderLists', {
          tableId: tableName,
          shouldGoBackWhenPrssItem: false,
          onOpenOrderPress: this.editOpenOrder,
        });
      } else {
        navigate('ModalPax', {
          shouldGoBackWhenConfirm: false,
          onSubmitHandler: pax => {
            this.props.actions.updateTransactionTableIdAndPax({ tableId: tableName, pax: Boolean(pax) ? pax : 0 });
          },
          title: t('Assign Seat'),
        });
      }
    }
  };

  onLongPressTable = table => {
    if (!Boolean(table)) return;
    const {
      frame: { xCoordinate, yCoordinate, width, height },
      tableName,
    } = table;
    this.props.navigation.navigate('ModalDynamicBeepQrCode', {
      tableName: tableName,
      tableWidth: width,
      tableHeight: height,
      tableX: xCoordinate,
      tableY: yCoordinate,
    });
  };

  _tableRef = ref => this._pagesTablesRef.push(ref);

  updateComponentStatus = (initinal = false) => {
    this._pagesTablesRef &&
      this._pagesTablesRef.map(tableRef => {
        tableRef && tableRef.updateComponentStatus(initinal);
      });
  };

  requestOpenOrCloseShift = () => {
    const { shiftOpenStatus, currentEmployeeId } = this.props;

    if (shiftOpenStatus || !this.props.insufficientStorageWarning.enabled) {
      this.props.actions.requestAuthorizedAction({
        name: CashierActions.OpenCloseShift,
        type: AuthorizationType.Cashier,
        employeeId: currentEmployeeId,
        onSuccess: {
          callback: () => {
            this.props.navigation.navigate('ModalShiftChange');
          },
        },
      });
    } else {
      this.props.actions.checkFreeStorage({
        navigation: this.props.navigation,
        onContinue: () => {
          this.props.actions.requestAuthorizedAction({
            name: CashierActions.OpenCloseShift,
            type: AuthorizationType.Cashier,
            employeeId: currentEmployeeId,
            onSuccess: {
              callback: () => {
                this.props.navigation.navigate('ModalShiftChange');
              },
            },
          });
        },
      });
    }
  };

  onCloseShiftHandler = () => {
    this.requestOpenOrCloseShift();
    this.hideMenu();
  };

  onManualRefundHandler = () => {
    const { currentEmployeeId } = this.props;
    this.props.actions.requestAuthorizedAction({
      employeeId: currentEmployeeId,
      name: CashierActions.Refund,
      type: AuthorizationType.Cashier,
      onSuccess: {
        callback: () => {
          this.props.navigation.navigate('Register');
          this.props.actions.refundTransaction({ type: 'manual' });
          this.hideMenu();
        },
      },
      onFailure: {
        callback: () => {
          this.hideMenu();
        },
      },
    });
  };

  onPressPreOrder = () => {
    const { navigation, actions } = this.props;

    const handlePreOrderSubmit = (_, nav) => {
      navigation.navigate('Register');
      actions.createPreOrder({});
      setTimeout(() => {
        requestAnimationFrame(() => {
          this.onReplaceToAddCustomer(nav);
        });
      }, 500);
    };

    navigation.navigate('ModalInfo', {
      info: t('To process a pre-order transaction, a customer must be selected'),
      needGoBackWhenSubmit: false,
      onSubmitHandler: handlePreOrderSubmit,
    });

    this.hideMenu();
  };

  onPressCollectPreOrder = () => {
    this.props.navigation.navigate('ModalCollectPreorder', {
      jumpToRegister: true,
    });
    this.hideMenu();
  };

  onReplaceToAddCustomer = (nav?) => {
    const { netInfoType, navigation } = this.props;
    if (netInfoType === 'none' || netInfoType === 'unknown') {
      (nav || navigation).navigate('ModalInfo', {
        title: t('No Internet Connection'),
        isShowTitle: true,
        info: t('Please connect to the internet to use this function'),
        titleIcon: <Icon name='warning' size={24} color='#FFA500' />,
        okText: t('Close'),
      });
    } else {
      (nav || navigation).navigate('AddCustomer');
    }
  };

  onPressPax = transactionId => {
    if (!transactionId) {
      return;
    }
    replace('ModalPax', {
      shouldGoBackWhenConfirm: true,
      onSubmitHandler: pax => {
        DAL.updateTransaction(transactionId, { pax: Boolean(pax) ? pax : 0 });
      },
      title: t('Assign Seat'),
    });
  };

  onPressViewOrder = transactionId => {
    if (transactionId) {
      const {
        actions: { setOpenOrderTransactionSession },
        navigation,
      } = this.props;
      setOpenOrderTransactionSession({ transactionId });
      navigation.navigate('Register');
    }
  };

  onPressCheckout = transactionId => {
    this.props.actions.setOpenOrderTransactionSession({ transactionId });
    replace('Checkout', { needClearTransactionWhenBack: true });
  };
}

export const TableLayout = connector(TableLayoutInner);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#E1F6F9',
    flexDirection: 'column',
  },
  titleBar: {
    paddingTop: STATUS_BAR_HEIGHT,
    height: NAVIGATOR_HEADER_HEIGHT,
    paddingHorizontal: NAVIGATOR_PADDING_HORIZONTAL,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'white',
    borderBottomColor: currentThemes.borderBottomColor,
    borderBottomWidth: 1,
  },
  takeAwayContainer: {
    marginLeft: scaleSizeW(40),
    width: scaleSizeW(160),
    height: scaleSizeH(56),
    borderWidth: scaleSizeW(1),
    borderColor: currentThemes.borderBottomColor,
    borderRadius: scaleSizeW(8),
    alignItems: 'center',
    justifyContent: 'center',
  },
  takeAwayText: {
    fontSize: currentThemes.fontSize18,
    color: '#393939',
    fontWeight: 'bold',
    textAlign: 'center',
    textAlignVertical: 'center',
  },
  tabBarUnderlineStyle: {
    width: scaleSizeW(323),
    height: scaleSizeW(8),
    backgroundColor: '#FC7118',
    top: 0,
  },
  scrollableTabBarStyle: {
    position: 'absolute',
    bottom: scaleSizeW(22),
    marginHorizontal: scaleSizeW(32),
    marginTop: scaleSizeW(14),
    height: TAB_BAR_HEIGHT,
    borderBottomLeftRadius: scaleSizeW(8),
    borderTopLeftRadius: scaleSizeW(8),
    borderWidth: 0,
    borderColor: '#00000000',
  },
  tabsContainerStyle: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  tabBaseStyle: {
    width: scaleSizeW(323),
    height: TAB_BAR_HEIGHT,
    alignSelf: 'stretch',
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabActiveStyle: {
    width: scaleSizeW(323),
    height: TAB_BAR_HEIGHT,
    alignSelf: 'stretch',
    alignItems: 'center',
    justifyContent: 'center',
    borderTopWidth: scaleSizeW(8),
    borderTopColor: '#FC7118',
  },
  textActiveStyle: {
    fontWeight: 'bold',
    fontSize: currentThemes.fontSize24,
    color: '#393939',
  },
  textInactiveStyle: {
    fontSize: currentThemes.fontSize24,
    color: '#000',
  },
  rightButtonContainer: {
    marginRight: scaleSizeW(40),
    minWidth: scaleSizeW(160),
    paddingHorizontal: scaleSizeW(21),
    height: scaleSizeH(56),
    borderRadius: scaleSizeW(8),
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FC7118',
  },
  rightButtonText: {
    fontSize: currentThemes.fontSize18,
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
    textAlignVertical: 'center',
  },
  mergeSplitHint: {
    position: 'absolute',
    alignSelf: 'center',
    paddingHorizontal: scaleSizeW(46),
    paddingVertical: scaleSizeH(12),
    fontSize: currentThemes.fontSize24,
    backgroundColor: '#FFC84A',
    color: '#000',
    borderWidth: 0,
    borderRadius: scaleSizeH(24),
    zIndex: 9999,
    overflow: 'hidden',
    top: NAVIGATOR_HEADER_HEIGHT + scaleSizeH(16),
  },
});
