import _, { get, map } from 'lodash';
import React, { PureComponent } from 'react';
import { BackHandler, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { connect, ConnectedProps } from 'react-redux';
import { bindActionCreators } from 'redux';
import {
  cancelEInvoice,
  cancelTransaction,
  EInvoiceErrorType,
  employeeCancelEInvoice,
  employeeCancelWithoutEInvoice,
  GhlRequestType,
  MRSError,
  queryEInvoice,
  returnUniquePromo,
  voidEWalPaymentBegin,
} from '../../actions';
import { ModalHeader } from '../../components/common';
import { MaterialIcons } from '../../components/ui';
import { currentThemes, scaleSizeH, scaleSizeW, t } from '../../constants';
import { onMRSInterceptor } from '../../sagas/mrs/checkSync';
import { ScreenProps, TransactionType } from '../../typings';
import { getParam } from '../../utils/navigation';
import { WrapReasonPicker } from '../Transactions';
import { testProps } from '../../utils';
import PaymentOptions from '../../config/paymentOption';
import { isEmpty } from '../../utils/validator';

interface Props extends ScreenProps, PropsFromRedux {}

interface State {
  reasonIndex: number;
  dataList: string[];
}

const mapDispatchToProps = dispatch => ({
  actions: bindActionCreators(
    {
      cancelTransaction,
      returnUniquePromo,
      voidEWalPaymentBegin,
      cancelEInvoice,
      queryEInvoice,
      employeeCancelEInvoice,
      employeeCancelWithoutEInvoice,
    },
    dispatch
  ),
});

const connector = connect(null, mapDispatchToProps);
type PropsFromRedux = ConnectedProps<typeof connector>;

class CancelReason extends PureComponent<Props, State> {
  private otherReason = '';

  static navigationOptions = {
    headerShown: false,
  };

  constructor(props) {
    super(props);
    this.state = {
      reasonIndex: null,
      dataList: [t('Incorrect item'), t('Incorrect variants'), t('Incorrect payment type'), t('Incorrect quantity'), t('Other')],
    };
  }

  componentDidMount(): void {
    BackHandler.addEventListener('hardwareBackPress', this.onPressGoBack);
  }

  componentWillUnmount(): void {
    BackHandler.removeEventListener('hardwareBackPress', this.onPressGoBack);
  }

  onChangeReason = (reasonIndex: number, otherReason: string) => {
    this.setState({ reasonIndex });
    this.otherReason = otherReason;
  };

  onCancelOfflineBeepOrder = result => {
    const { success } = result;
    const refundAmount = get(result, 'refundAmount', -1);
    const onCancelOfflineBeepOrder = getParam(this.props, 'onCancelOfflineBeepOrder');
    if (success && refundAmount === 0) {
      this.props.navigation.goBack();
    }
    onCancelOfflineBeepOrder && onCancelOfflineBeepOrder(result);
  };

  tryToReturnUniquePromo = () => {
    const transaction = getParam(this.props, 'transaction', null);
    const transactionId = getParam(this.props, 'transactionId', null);
    const customerId = get(transaction, 'customerId', null);

    const promotions = get(transaction, 'promotions', []);

    let promotionId = null;
    let uniquePromotionCodeId = null;

    const validOrderLevelUniquePromotion = promotions.find(promo => get(promo, 'promotionId', null) && get(promo, 'uniquePromotionCodeId', null));

    if (validOrderLevelUniquePromotion) {
      // check UP under order's promotion
      promotionId = get(validOrderLevelUniquePromotion, 'promotionId', null);
      uniquePromotionCodeId = get(validOrderLevelUniquePromotion, 'uniquePromotionCodeId', null);
    } else {
      // check UP under item's promotion
      const items = _.get(transaction, 'items', []);
      for (const item of items) {
        const itemPromotions = _.get(item, 'promotions', []);
        const validItemPromotion = itemPromotions.find(promotion => _.get(promotion, 'promotionId', null) && _.get(promotion, 'uniquePromotionCodeId', null));

        if (validItemPromotion) {
          promotionId = _.get(validItemPromotion, 'promotionId', '');
          uniquePromotionCodeId = _.get(validItemPromotion, 'uniquePromotionCodeId', '');
          break;
        }
      }
    }

    if (promotionId && uniquePromotionCodeId && customerId && transactionId) {
      this.props.actions.returnUniquePromo({
        promotionId: promotionId,
        promotionCodeId: uniquePromotionCodeId,
        transactionId: transactionId,
        customerId: customerId,
      });
    }
  };

  // temp solution for e-invoice to suit CancelReason
  onConfirm_Deprecated = () => {
    const transaction = getParam(this.props, 'transaction', null) as TransactionType;
    if (!transaction) {
      console.log('e-invoice: transaction is null!');
      this.onConfirm();
      return;
    }

    const eInvoiceEnabled = getParam(this.props, 'eInvoiceEnabled', false);
    if (!eInvoiceEnabled) {
      console.log('e-invoice: not enabled!');
      this.onConfirm();
      return;
    }
    const isBeepOrder = getParam(this.props, 'isBeepOrder', false);
    const eInvoicePhase2Enabled = getParam(this.props, 'eInvoicePhase2Enabled', false);
    const eInvoiceBeepWebstoreEnabled = getParam(this.props, 'eInvoiceBeepWebstoreEnabled', false);

    if (isBeepOrder && (!eInvoicePhase2Enabled || !eInvoiceBeepWebstoreEnabled)) {
      this.onConfirm();
      return;
    }

    console.log('e-invoice: cancelEInvoice!');
    this.props.actions.cancelEInvoice({
      transaction,
      onResponse: (success, errorCode) => {
        if (success) {
          console.log('e-invoice: cancel success!');
          this.onConfirm();
        } else {
          console.log('e-invoice: cancel failed!');
          this.props.navigation.goBack();
          if (errorCode === EInvoiceErrorType.CANNOT_CANCEL_CONSOLIDATED_E_INVOICE) {
            this.props.navigation.navigate('ModalInfoPopup', {
              title: 'Transaction Cannot Be Canceled',
              message:
                'This transaction cannot be canceled or refunded since it has already been issued in a consolidated e-Invoice.' + ` (Code: ${errorCode})`,
              icon: 'error',
              submitText: 'Close',
              needGoBackWhenSubmit: true,
              onSubmitHandler: () => {
                this.props.navigation.goBack();
              },
            });
            return;
          }
          if (errorCode === EInvoiceErrorType.CANNOT_CANCEL_E_INVOICE_BEYOND_DEADLINE) {
            this.props.navigation.navigate('ModalInfoPopup', {
              title: 'Transaction Cannot Be Canceled',
              message:
                'This e-Invoice was validated over 72 hours ago and cannot be canceled due to IRBM rules. Go MyInvois portal to cancel this transaction.' +
                ` (Code: ${errorCode})`,
              icon: 'error',
              submitText: 'Got it',
              needGoBackWhenSubmit: true,
              onSubmitHandler: () => {
                this.props.navigation.goBack();
              },
            });
            return;
          }
          if (errorCode >= 0) {
            this.props.navigation.navigate('ModalInfoPopup', {
              title: 'Transaction Cannot Be Canceled',
              message: 'Cancellation request failed due to unexpected error at MyInvois portal. \nPlease retry later.' + ` (Code: ${errorCode})`,
              icon: 'error',
              submitText: 'Got it',
              needGoBackWhenSubmit: true,
              onSubmitHandler: () => {
                this.props.navigation.goBack();
              },
            });
            return;
          }
          this.props.navigation.navigate('ModalInfoPopup', {
            title: t('E_INVOICE_CANCEL_FAILED_ERROR_AT_MY_INVOICE'),
            message: t('E_INVOICE_CANCEL_FAILED_PLEASE_RETRY_LATER'),
            icon: 'error',
            submitText: t('E_INVOICE_RETRY_LATER'),
            cancelText: t('E_INVOICE_CANCEL_ON_STOREHUB_ONLY'),
            needGoBackWhenSubmit: true,
            onCancelHandler: () => {
              this.props.navigation.navigate('ModalInfoPopup', {
                title: t('E_INVOICE_CANCEL_FAILED_ARE_YOU_SURE_TO_PROCEED'),
                message: t('E_INVOICE_CANCEL_FAILED_DISCREPANCY_CREATE_WARING'),
                submitText: t('E_INVOICE_CANCEL_ANYWAY'),
                cancelText: t('E_INVOICE_CLOSE'),
                submitColor: '#FF2825',
                needGoBackWhenSubmit: true,
                onSubmitHandler: () => {
                  this.props.actions.employeeCancelWithoutEInvoice({ transactionId: transaction.transactionId });
                  this.onConfirm();
                },
                onCancelHandler: () => {
                  this.props.navigation.goBack();
                },
              });
            },
            onSubmitHandler: () => {
              this.props.navigation.goBack();
            },
          });
        }
      },
    });
  };

  onConfirm = () => {
    const { state, otherReason } = this;
    const { reasonIndex } = state;
    if (reasonIndex === null && otherReason.length === 0) return;
    const onCancelSuccess = getParam(this.props, 'onCancelSuccess', null);
    const needAutoGoBack = getParam(this.props, 'needAutoGoBack', true);
    const transaction = getParam(this.props, 'transaction', null);
    const transactionId = getParam(this.props, 'transactionId', null);
    const isBeepOrder = getParam(this.props, 'isBeepOrder', false);
    let returnReason = null;
    if (get(this.state.dataList, reasonIndex)) {
      returnReason = get(this.state.dataList, reasonIndex);
    }
    let onlineTransaction: TransactionType;
    if (transaction && transaction.isRemote) {
      onlineTransaction = transaction;
    }

    const onComplete = (error: MRSError) => {
      if (onMRSInterceptor(error)) {
        onCancelSuccess && onCancelSuccess();
        if (needAutoGoBack) {
          this.props.navigation.goBack();
        }

        if (!isBeepOrder) {
          this.tryToReturnUniquePromo();
        }
      } else {
        this.props.navigation.replace('ModalInfo', {
          info: `${!isEmpty(get(error, 'errorMessage')) ? error.errorMessage + '. ' : ''}Cancel transaction failed`,
          okText: t('OK'),
          needGoBackWhenSubmit: true,
          onSubmitHandler: () => null,
        });
      }
    };

    const onProceedCancel = (additionalReason?: string) => {
      const transaction = getParam(this.props, 'transaction', null);

      const handleCancelTransaction = (reason: string) => {
        this.props.actions.cancelTransaction({
          transactionId,
          transaction,
          onlineTransaction,
          returnReason,
          otherReason: reason,
          isBeepOrder,
          onCancelOfflineBeepOrder: this.onCancelOfflineBeepOrder,
          onComplete,
        });
      };

      const finalReason = additionalReason ? (returnReason === 'Other' ? otherReason + ' - ' + additionalReason : additionalReason) : otherReason;
      handleCancelTransaction(finalReason);
    };

    if (isBeepOrder) {
      onProceedCancel();
      return;
    }

    const receiptNumber = transaction.receiptNumber;
    const payments = transaction.payments;
    const paymentsJson = map(payments, payment => {
      return {
        amount: payment.amount,
        cashTendered: payment.cashTendered,
        isDeposit: payment.isDeposit,
        isVoided: payment.isVoided,
        mPOSTxnId: payment.mPOSTxnId,
        onlinePaymentMethod: payment.onlinePaymentMethod,
        paymentMethodId: payment.paymentMethodId,
        roundedAmount: payment.roundedAmount,
        subType: payment.subType,
        type: payment.type,
      };
    });

    for (const payment of payments) {
      const paymentOption = PaymentOptions.getPaymentOptionById(payment.paymentMethodId);
      const paymentType = get(paymentOption, 'type');
      if (paymentType === 'paymentTerminal') {
        this.props.navigation.replace('ModalGhlPay', {
          paymentOption: paymentOption,
          orderId: receiptNumber,
          transactionId: transactionId,
          requestType: GhlRequestType.Void,
          amount: payment.amount,
          ghlTxnId: payment.mPOSTxnId,
          voidSucceed: notes => {
            if (notes) {
              onProceedCancel(notes);
            } else {
              onProceedCancel('GHL Void Payment Successful');
            }
          },
        });
        return;
      }
    }

    this.props.actions.voidEWalPaymentBegin({
      receiptNumber,
      transactionId,
      // @ts-ignore
      payments: paymentsJson,
      onComplete: {
        callback: ({ voidSucceed, paymentName }) => {
          if (voidSucceed) {
            onProceedCancel();
          } else {
            const info = t('void not supported', { paymentName });
            this.props.navigation.navigate('ModalInfo', {
              info,
              okText: t('OK'),
              needGoBackWhenSubmit: true,
              onSubmitHandler: () => {
                // cancel tx with cash
                onProceedCancel();
              },
              onCancelHandler: () => {
                // do not cancel tx
                this.props.navigation.goBack();
              },
            });
          }
        },
      },
    });
  };

  onPressGoBack = () => {
    const { dataList, reasonIndex } = this.state;
    const inEditOtherReason = reasonIndex === dataList.length - 1;
    if (inEditOtherReason) {
      this.clearReason();
    } else {
      this.props.navigation.goBack();
    }
    return true;
  };

  clearReason = () => this.setState({ reasonIndex: -1 });

  renderHeaderLeft = inEditOtherReason => {
    return (
      inEditOtherReason && (
        <TouchableOpacity {...testProps('al_btn_190')} style={styles.iconBack} onPress={this.clearReason}>
          <MaterialIcons name='arrow-back' size={scaleSizeW(36)} color='#8D90A3' />
        </TouchableOpacity>
      )
    );
  };

  renderNavi = inEditOtherReason => {
    return (
      <ModalHeader
        showDivider={!inEditOtherReason}
        showRightBtn={!inEditOtherReason}
        title={inEditOtherReason ? '' : t('Reason For Cancel Transaction')}
        rightBtn={t('CONFIRM')}
        onRightBtnClick={this.onConfirm_Deprecated}
        leftBtn={this.renderHeaderLeft(inEditOtherReason)}
        onCloseClick={this.onPressGoBack}
      />
    );
  };

  render() {
    const { dataList, reasonIndex } = this.state;
    const inEditOtherReason = reasonIndex === dataList.length - 1;
    return (
      <View style={styles.container}>
        <View style={styles.layer}>
          {this.renderNavi(inEditOtherReason)}
          <Text testID='text' style={styles.textWarn}>
            {!inEditOtherReason ? '' : t('Reason For Cancel Transaction')}
          </Text>
          <WrapReasonPicker
            reasonIndex={reasonIndex}
            reasonsContainerStyle={styles.reasonsContainer}
            onValueChangeHandler={this.onChangeReason}
            reasons={dataList}
            onSubmitOtherReason={this.onConfirm_Deprecated}
          />
        </View>
      </View>
    );
  }
}

export default connector(CancelReason);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#858585A0',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  layer: {
    alignSelf: 'flex-end',
    alignItems: 'center',
    flexDirection: 'column',
    width: scaleSizeW(1430),
    height: scaleSizeH(930),
    backgroundColor: 'white',
  },
  reasonsContainer: {
    width: scaleSizeW(1080),
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  iconBack: {
    width: scaleSizeW(96),
    height: '100%',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: scaleSizeW(84),
  },
  textWarn: {
    color: '#60636B',
    fontSize: currentThemes.fontSize32,
    fontWeight: 'bold',
    marginVertical: scaleSizeH(50),
  },
});
