import { get } from 'lodash';
import moment from 'moment';
import React, { PureComponent } from 'react';
import { Image, StyleSheet, Text, TouchableOpacity, View, ViewStyle } from 'react-native';
import { icons } from '../../components/ui';
import { DefaultPaymentOptionType } from '../../config/paymentOption';
import { currentThemes, OnlineOrderStatus, OnlineOrderStatusMapTitle, OrderChannel, scaleSizeH, scaleSizeW, t, TransactionFlowType } from '../../constants';
import { localeNumber, testProps } from '../../utils';
import {
  isDelivery,
  isDineIn,
  isECDelivery,
  isECOrder,
  isECPickUp,
  isPickUpOrTakeAway,
  isPreOrder,
  isPreOrderWithAcceptedOrPaid,
  isZeptyOrder,
} from '../../utils/beep';
interface TransactionItemProps {
  item: any;
  itemClicked?(item, index): void;
  itemType: string;
  isSelected?: boolean;
  index?: any;
}

export default class TransactionItem extends PureComponent<TransactionItemProps> {
  itemClicked = () => {
    const { item, index, itemClicked } = this.props;
    itemClicked && itemClicked(item, index);
  };

  render() {
    const { item, isSelected, itemType, index } = this.props;
    let time;
    let amount;
    let receiptNumber;
    let status = '';
    let label = null;
    let tableId = '';
    let pickUpId = '';
    let title = '';
    let tagLabel = '';
    let needShowTagLabel = false;
    let icon;
    const isOnlineItem = itemType === 'online';
    if (isOnlineItem) {
      time = moment(item.createdTime).format('h:mm A');
      amount = String(item.total !== null ? localeNumber(item.total) : '0.00');
      receiptNumber = item.orderId;
      if (item !== null) {
        if (isDineIn(item)) {
          status = get(OnlineOrderStatusMapTitle, ['dineIn', item.status]);
        }
        if (isPickUpOrTakeAway(item) || isECPickUp(item)) {
          status = get(OnlineOrderStatusMapTitle, ['selfPickUp', item.status]);
          tagLabel = t('PICKUP');
          needShowTagLabel = true;
        }
        if (isDelivery(item) || isECDelivery(item)) {
          status = get(OnlineOrderStatusMapTitle, ['delivery', item.status]);
          tagLabel = t('DELIVERY');
          needShowTagLabel = true;
        }

        tableId = item.tableId;
        pickUpId = item.pickUpId;

        if (pickUpId && tableId) {
          title = `${status} (${tableId}, #${pickUpId})`;
        } else if (tableId) {
          title = `${status} (${tableId})`;
        } else if (pickUpId) {
          title = `${status} (#${pickUpId})`;
        } else if (isZeptyOrder(item) || isECOrder(item)) {
          title = t('New Order');
        } else {
          title = `${status}`;
        }
        if (isPreOrder(item)) {
          const { expectDeliveryDateFrom, expectDeliveryDateTo } = item;
          if (isPickUpOrTakeAway(item) || isECPickUp(item)) {
            if (Boolean(expectDeliveryDateFrom) && Boolean(expectDeliveryDateTo)) {
              time = moment(expectDeliveryDateFrom).format('h:mm A');
            }
          }
          if (isDelivery(item) || isECDelivery(item)) {
            if (Boolean(expectDeliveryDateFrom) && Boolean(expectDeliveryDateTo)) {
              time = `${moment(expectDeliveryDateFrom).format('h:mm A')} - ${moment(expectDeliveryDateTo).format('h:mm A')}`;
            }
          }
        }

        if (isPreOrderWithAcceptedOrPaid(item)) {
          if (item.status === OnlineOrderStatus.Paid || item.status === OnlineOrderStatus.Accepted) {
            title = `${t('Pre Order')} (#${pickUpId})`;
          }
        }

        if (item.channel) {
          switch (item.channel) {
            case OrderChannel.OrderChannelQRCode:
              icon = icons.BeepIcon;
              break;
            case OrderChannel.OrderChannelOnline:
              icon = icons.ECIcon;
              break;
            case OrderChannel.OrderChannelGrab:
              icon = icons.GrabIcon;
              break;
            case OrderChannel.OrderChannelShopee:
              icon = icons.ShopeeFoodIcon;
              break;
            case OrderChannel.OrderChannelFoodPanda:
              icon = icons.FoodPandaIcon;
              break;
            case OrderChannel.OrderChannelLazada:
              icon = icons.LazadaIcon;
              break;
            case OrderChannel.OrderChannelShopify:
              icon = icons.ShopifyIcon;
              break;
            case OrderChannel.OrderChannelShopeeEC:
              icon = icons.ShopeeEcommerceIcon;
              break;
            case OrderChannel.OrderChannelWooCommerce:
              icon = icons.WooCommerceIcon;
              break;
            case OrderChannel.OrderChannelTikTokShop:
              icon = icons.TikTokShopIcon;
              break;
            case OrderChannel.OrderChannelZalora:
              icon = icons.ZaloraIcon;
              break;
            case OrderChannel.OrerChannelMagento:
              icon = icons.MagentoIcon;
              break;
            default:
              break;
          }
        }
      }
    } else if (itemType === 'register') {
      time = moment(item.createdDate ? item.createdDate : item.createdTime).format('h:mm A');
      amount = String(item.total !== null ? localeNumber(item.total) : '0.00');
      receiptNumber = item.receiptNumber;
      status = '';
      if (item !== null && item.isCancelled) {
        status = t('CANCELLED');
        label = (
          <View style={isSelected ? styles.labelSelected : styles.labelCancel}>
            <Text numberOfLines={1} style={styles.testLabel}>
              {t('CANCELLED')}
            </Text>
          </View>
        );
      } else if (item !== null && item.transactionType === TransactionFlowType.Return) {
        status = t('REFUNDED');
        amount = `－ ${amount}`;
        label = (
          <View style={isSelected ? styles.labelSelected : styles.labelRefund}>
            <Text numberOfLines={1} style={styles.testLabel}>
              {t('REFUNDED')}
            </Text>
          </View>
        );
      } else if (item !== null && item.transactionType === TransactionFlowType.PreOrder) {
        status = t('PRE-ORDER');
        amount = `${amount}`;
        label = (
          <View style={isSelected ? styles.labelSelected : styles.labelCash}>
            <Text numberOfLines={1} style={styles.testLabel}>
              {t('PRE-ORDER')}
            </Text>
          </View>
        );
      } else if (item.payments && item.payments[0]) {
        const paymentMethodId = get(item, ['payments', 0, 'paymentMethodId']);
        if (paymentMethodId === DefaultPaymentOptionType.Cash) {
          label = t('CASH');
        } else if (paymentMethodId === DefaultPaymentOptionType.CreditCard) {
          label = t('CREDIT');
        } else if (paymentMethodId === DefaultPaymentOptionType.Loyalty) {
          label = t('LOYALTY');
        } else if (paymentMethodId === DefaultPaymentOptionType.DebitCard) {
          label = t('DEBIT');
        } else if (paymentMethodId > DefaultPaymentOptionType.DebitCard) {
          const paymentType = get(item, ['payments', 0, 'type']);
          if (Boolean(paymentType)) {
            label = paymentType.toUpperCase();
          }
        }
        if (Boolean(label)) {
          label = (
            <View style={isSelected ? styles.labelSelected : styles.labelCash}>
              <Text allowFontScaling={false} style={[styles.testLabel]} testID='transactionLabel'>
                {label}
              </Text>
            </View>
          );
        }
      }
    }
    return isOnlineItem ? (
      <TouchableOpacity
        style={[
          styles.button,
          {
            backgroundColor: isSelected ? '#008EC4' : 'transparent',
            paddingRight: scaleSizeW(18),
            paddingLeft: scaleSizeW(26),
            paddingBottom: scaleSizeH(17),
          },
        ]}
        onPress={this.itemClicked}
        {...testProps('al_online_item')}
      >
        <View style={{ width: '100%', marginTop: scaleSizeH(10), flex: 1, flexDirection: 'row', justifyContent: 'space-between' }}>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            {icon ? <Image testID='itemIcon' source={icon} style={{ width: scaleSizeW(40), height: scaleSizeW(40), marginRight: scaleSizeW(10) }} /> : null}
            <Text style={{ fontSize: currentThemes.fontSize18, color: isSelected ? 'white' : '#60636b' }}>{receiptNumber}</Text>
          </View>
          {needShowTagLabel ? (
            <View
              style={[
                tagCommonStyle,
                { backgroundColor: currentThemes.buttonBackgroundColor },
                isSelected && {
                  backgroundColor: 'transparent',
                  borderColor: 'white',
                  borderWidth: 1,
                },
              ]}
            >
              <Text allowFontScaling={false} style={{ color: 'white', fontSize: currentThemes.fontSize14 }}>
                {tagLabel}
              </Text>
            </View>
          ) : null}
        </View>
        <View style={{ width: '100%', marginTop: scaleSizeH(4), flex: 1, flexDirection: 'row', justifyContent: 'space-between' }}>
          <Text style={[styles.status, { color: isSelected ? 'white' : '#60636b' }]}>{title}</Text>
          <Text style={[styles.status, { color: isSelected ? 'white' : '#757575' }]}>{amount}</Text>
        </View>
        <View style={{ width: '100%' }}>
          <Text style={{ fontSize: currentThemes.fontSize24, color: isSelected ? 'white' : '#60636b', fontWeight: '400' }}>{time}</Text>
        </View>
      </TouchableOpacity>
    ) : (
      <TouchableOpacity
        style={[
          styles.button,
          {
            backgroundColor: isSelected ? '#008EC4' : 'white',
          },
        ]}
        onPress={this.itemClicked}
        {...testProps('al_register_item')}
      >
        <View
          style={{
            width: '100%',
            minHeight: scaleSizeH(104),
            paddingHorizontal: scaleSizeW(24),
            paddingVertical: scaleSizeH(20),
            flexDirection: 'column',
            alignItems: 'center',
          }}
          testID='transactionItemContainer'
        >
          <View style={{ flexDirection: 'row', alignItems: 'center', width: '100%' }}>
            <Text
              style={{
                flex: 1,
                fontWeight: 'normal',
                fontSize: currentThemes.fontSize24,
                color: isSelected ? 'white' : '#60636B',
                lineHeight: scaleSizeH(28),
              }}
              testID={'receipt id_' + String(index)}
            >
              {receiptNumber}
            </Text>
            {label}
          </View>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: scaleSizeH(12) }}>
            <Text style={{ ...StyleSheet.flatten(styles.contentFont), color: isSelected ? 'white' : '#60636B' }}>{time}</Text>
            <Text
              style={{
                ...StyleSheet.flatten(styles.contentFont),
                fontWeight: 'bold',
                flex: 1,
                textAlign: 'right',
                color: isSelected ? 'white' : '#757575',
                textDecorationLine: item.isCancelled ? 'line-through' : 'none',
              }}
            >
              {amount}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  }
}
const tagCommonStyle: ViewStyle = {
  height: scaleSizeH(32),
  minWidth: scaleSizeW(104),
  // paddingVertical: scaleSizeH(4),
  paddingHorizontal: scaleSizeW(6),
  justifyContent: 'center',
  alignItems: 'center',
  borderRadius: 4,
};
const styles = StyleSheet.create({
  contentFont: {
    fontSize: currentThemes.fontSize24,
    fontWeight: '500',
  },
  button: {
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: scaleSizeH(104),
    flex: 1,
  },
  status: {
    maxWidth: scaleSizeW(500),
    color: '#303030',
    fontSize: currentThemes.fontSize26,
    fontWeight: '500',
  },
  labelRefund: {
    ...tagCommonStyle,
    backgroundColor: '#E53A3A',
  },
  labelCancel: {
    ...tagCommonStyle,
    backgroundColor: '#DBDBDB',
  },
  labelCash: {
    ...tagCommonStyle,
    overflow: 'hidden',
    backgroundColor: '#00A86B',
  },
  labelSelected: {
    ...tagCommonStyle,
    borderColor: 'white',
    borderWidth: 1,
  },
  testLabel: {
    fontSize: currentThemes.fontSize14,
    color: 'white',
    // textAlign: 'center',
    // lineHeight: scaleSizeH(32),
    // textAlignVertical: 'center',
    fontWeight: '500',
  },
});
