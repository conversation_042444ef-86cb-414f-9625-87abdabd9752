import * as Immutable from 'immutable';
import { chain, compact, concat, filter, findIndex, forEach, get, isEqual, last, map, reduce, throttle, uniq, zipObject } from 'lodash';
import moment from 'moment';
import React, { PureComponent } from 'react';
import { Al<PERSON>, Linking, ScrollView, SectionList, SectionListData, StatusBar, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';
import { showMessage } from 'react-native-flash-message';
import ScrollableTabView, { DefaultTabBar } from 'react-native-scrollable-tab-view';
import sectionListGetItemLayout from 'react-native-section-list-get-item-layout';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { connect, ConnectedProps } from 'react-redux';
import { bindActionCreators } from 'redux';
import { createSelector } from 'reselect';
import {
  cancelEInvoice,
  checkCancellableWithRefundStatus,
  checkLimitBeforeExecute,
  checkRefundTransactionValidity,
  checkTrxRefundStatus,
  clearRefundPromotion,
  clearTransactionSession,
  employeeCancelWithoutEInvoice,
  employeePrintEInvoiceQrReceipt,
  getCustomerById,
  getOnlineOrderDetails,
  getOnlineOrderStatusList,
  HttpActionArgs,
  HttpActionCallback,
  HttpActionFailurePayload,
  HttpActionSuccessPayload,
  MRSError,
  navigateToHome,
  newPrintReceiptAction,
  printEInvoiceQrReceipt,
  printKitchenDocket,
  requestAuthorizedAction,
  requestOnlineOrderStatusWithCount,
  searchOnlineOrders,
  SearchOnlineOrdersType,
  searchTransactions,
  toggleToastInfo,
  TransactionTypeWithDisplay,
  updateOnlineOrderStatus,
  updateReturnItem,
} from '../../actions';
import { NAVIGATOR_HEADER_HEIGHT, TabBarWithTextAndIcon, TopMainBar } from '../../components/common';
import TabContainer from '../../components/common/TabContainer';
import { ModalMoreAction, ModalOnlineFilter } from '../../components/modal';
import TransactionBasicInfo from '../../components/transactions/TransactionBasicInfo';
import TrxDetailInfo from '../../components/transactions/TrxDetailInfo';
import { FontAwesomeIcons, IconCactus, IconCancel, IconEmail, IconFilter, IconMore, IconSearchLabel } from '../../components/ui';
import {
  AuthorizationType,
  CashierActions,
  CommonColors,
  currentThemes,
  monthFullNames,
  OnlineOrderStatus,
  OnlineOrderStatusMapTitle,
  OnlineOrderTitleMapStatus,
  scaleSizeH,
  scaleSizeW,
  screenWidth,
  SharedStyles,
  ShippingType,
  STATUS_BAR_HEIGHT,
  t,
  TransactionFlowType,
  width,
} from '../../constants';
import DAL from '../../dal';
import { LeafState, LoyaltyDiscountType, RootState, ScreenProps, TransactionType } from '../../typings';
import { getNumberValue, getUnNullValue, newConvertCurrencyToSymbol, testProps } from '../../utils';
import {
  getBeepOrderShippingTag,
  getDisplayedStatus,
  getOrderCountStatistics,
  getPayLaterOrPayFirstTag,
  getPreOrderTag,
  isDelivery,
  isDineIn,
  isECOrder,
  isFDIOrder,
  isPickUpOrTakeAway,
  isPreOrderWithAcceptedOrPaid,
  isTakeAway,
  isZeptyOrder,
} from '../../utils/beep';
import {
  BeepFlowAction,
  BeepFlowTag,
  logBeepEvent,
  logFailedServerRequest,
  logSucceedServerRequest,
  OrderOperationEnum,
  WorkflowStatus,
} from '../../utils/logComponent';

import { MenuItemType } from '../../components/modal/ModalMoreAction';
import { ScannerSubscription } from '../../utils/scanner';
import { isEmpty } from '../../utils/validator';
import OrderCount from './OrderCount';
import TransactionItem from './TransactionItem';

import { DrawerActions } from '@react-navigation/compat';
import MenuWithNotification from '../../components/common/MenuWithNotification';
import NotificationButton from '../../components/transactions/NotificationButton';
import {
  selectBusinessName,
  selectCurrency,
  selectCurrentNetworkInfo,
  selectEInvoiceEnabled,
  selectEmployeeId,
  selectEnableCashback,
  selectEnableSelfPickupAlert,
  selectGBEnableEInvoiceBeepWebstore,
  selectGBEnableEInvoicePhase2,
  selectIsBeepQREnabled,
  selectIsOnline,
  selectLastZReadingCloseTime,
  selectLocalCountryMap,
  selectOperationHours,
  selectPrinterTagsSettings,
  selectStoreId,
} from '../../sagas/selector';
import { getParam } from '../../utils/navigation';

import MRSListener from '../../components/mrs/MRSListener';
import PauseBeepNotification from '../../components/settings/beep/PauseBeepNotification';
import * as NavigationService from '../../navigation/navigatorService';

import { FlashList } from '@shopify/flash-list';
import { PosTextInput } from '../../components/textInput';
import IconBadGlobal from '../../components/ui/svgIcons/iconBadGlobal';
import IconChefHeadThin from '../../components/ui/svgIcons/iconChefHeadThin';
import IconQrThin from '../../components/ui/svgIcons/iconQrThin';
import IconReceiptThin from '../../components/ui/svgIcons/iconReceiptThin';
import PaymentOptions, { DefaultPaymentOptionType, PaymentType } from '../../config/paymentOption';
import { KitchenEvent } from '../../models/print/manager/KitchenManager';
import { PrintReturnResult } from '../../models/print/task/AbstractPrintTask';
import { onMRSInterceptor } from '../../sagas/mrs/checkSync';
import { selectSmMallLimited } from '../../sagas/pageSelector';
import { getAlreadyCloseZReading, getCurOperationDayTime } from '../../utils/datetime';
import { hasNoReceiptPrinter } from '../../utils/printer';
import TransactionHistoryLogs from './TransactionHistoryLogs';

interface Props extends ScreenProps, PropsFromRedux {}

interface State {
  currentTransaction: any;
  curOfflineTrxIndex: number;
  currentOnlineTransaction: any;
  dataList: any[];
  searchText: string;
  onlineSearchResult: any;
  registerSearchResult: any;
  registerCustomer: any;
  onlineCustomer: any;
  isRefunding: boolean;
  onlineOrdersList: any;
  onLineOrderLogsList: any;
  onlineOrdersRefreshing: boolean;
  onlineTypeList: any;
  statusListDone: boolean;
  nextStatusList: any;
  currentOnlineFilter: any;
  currentStatus: any;
  searching: boolean;
  onlineOrderTabSelected: boolean;
  countDataSource: any;
  showingOrderCount: boolean;
  currentCountItem: any;
  prevIsInternetReachable: boolean;
}

const mapStateToProps = state => ({
  businessName: selectBusinessName(state),
  storeId: selectStoreId(state),
  currentEmployeeId: selectEmployeeId(state),
  transactionSession: fromImmutableTransactionSession(state),
  printerTagsSettings: selectPrinterTagsSettings(state),
  enableCashback: selectEnableCashback(state),
  isQROrderingEnabled: selectIsBeepQREnabled(state),
  isOnline: selectIsOnline(state),
  currency: selectCurrency(state),
  enableSelfPickupAlert: selectEnableSelfPickupAlert(state),
  operationHours: selectOperationHours(state),
  lastCloseZReadingTime: selectLastZReadingCloseTime(state),
  localCountryMap: selectLocalCountryMap(state),
  eInvoiceEnabled: selectEInvoiceEnabled(state),
  currentNetworkInfo: selectCurrentNetworkInfo(state),
  eInvoicePhase2Enabled: selectGBEnableEInvoicePhase2(state),
  eInvoiceBeepWebstoreEnabled: selectGBEnableEInvoiceBeepWebstore(state),
  smMallLimited: selectSmMallLimited(state),
});

const fromImmutableTransactionSession = createSelector<RootState, LeafState, TransactionTypeWithDisplay>(
  (state: Immutable.Map<string, any>) => state.get('TransactionSession', Immutable.Map()),
  transactionSession => transactionSession.toJS()
);

const mapDispatchToProps = dispatch => ({
  actions: bindActionCreators(
    {
      checkTrxRefundStatus,
      checkCancellableWithRefundStatus,
      searchTransactions,
      printEInvoiceQrReceipt,
      requestAuthorizedAction,
      getCustomerById,
      clearTransactionSession,
      updateReturnItem,
      checkRefundTransactionValidity,
      searchOnlineOrders,
      getOnlineOrderDetails,
      updateOnlineOrderStatus,
      toggleToastInfo,
      getOnlineOrderStatusList,
      requestOnlineOrderStatusWithCount,
      clearRefundPromotion,
      navigateToHome,
      checkLimitBeforeExecute,
      printKitchenDocket,
      employeePrintEInvoiceQrReceipt,
      newPrintReceiptAction,
      cancelEInvoice,
      employeeCancelWithoutEInvoice,
    },
    dispatch
  ),
});

export enum OnlineTransactionsType {
  // All = t('All'),
  // OnlineStore = t('Online Store'),
  BeepOrders = t('Beep Orders'),
}

export enum OnlineTransactionsStatusType {
  AllStatus = t('All Status'),
  PickedUp = t('PickedUp'),
  PendingPayment = t('PendingPayment'),
  PendingVerification = t('PendingVerification'),
  ReadyForDelivery = t('ReadyForDelivery'),
  Shipped = t('Shipped'),
  Paid = t('PendingAcceptance'),
  Accepted = t('FindingRider'),
  ReadyForPickUp = t('ReadyForPickup'),
  Confirmed = t('Send To Kitchen'),
  PaymentCancelled = t('PaymentCancelled'),
  Delivered = t('Delivered'),
  Cancelled = t('Cancelled'),
  LogisticsConfirmed = t('RiderAssigned'),
  AcceptOrder = t('AcceptOrder'),
  CancelOrder = t('CancelOrder'),
  PrintedToKitchen = t('Send To Kitchen'),
}

export enum TrxSearchType {
  Normally = 'Normally',
  LinkedRefundTrx = 'LinkedRefundTrx',
}

const DEFAULT_SEARCH_TEXT = '';
const TRANSACTIONLIST_WIDTH = screenWidth * 0.31;
const DATE_TITLE_FORMAT = 'YYYY-MM-DD';

const connector = connect(mapStateToProps, mapDispatchToProps);
type PropsFromRedux = ConnectedProps<typeof connector>;

const getLoyaltySpent = (transaction: TransactionFlowType) => {
  let loyaltySpent = 0;
  const loyaltyDiscounts = getUnNullValue(transaction, 'loyaltyDiscounts', []);
  loyaltySpent = reduce(loyaltyDiscounts, (acc, cur) => acc + (Number(cur.spentValue) || 0), 0);
  if (loyaltySpent === 0) {
    // search all paymentswhich paymentMethodId ==2, not only one
    const storeCreditPayment = filter(
      get(transaction, 'payments', []),
      item => item.paymentMethodId === DefaultPaymentOptionType.Loyalty || item.paymentMethod === 'Loyalty'
    );
    loyaltySpent = reduce(storeCreditPayment, (acc, cur) => acc + (Number(cur.amount) || 0), 0);
  }

  return loyaltySpent;
};

const OFFLINE_PAGESIZE = 20;

class Transactions extends PureComponent<Props, State> {
  static navigationOptions = () => ({
    headerShown: false,
  });

  private _moreDialog;
  private _pageIndex;
  private _onLineDialog;
  private _currentFromdate;
  private _isPreOrder;
  private _nextToDate;
  private _statusList;
  private _onlineSearchText;
  private _registerSearchText;
  private _scannerListener;
  private _searchInput: TextInput;
  private _lastRegisterSearchText: string;
  private _unsubscribeFocusListener;
  private _unsubscribeBlurListener;
  private _offlinePageNo;

  constructor(props: Props) {
    super(props);
    const showBeepNewOrder = getParam(props, 'showBeepNewOrder');
    this.state = {
      currentTransaction: null,
      currentOnlineTransaction: null,
      dataList: [],
      onlineSearchResult: null,
      registerSearchResult: null,
      searchText: DEFAULT_SEARCH_TEXT,
      registerCustomer: {},
      onlineCustomer: {},
      isRefunding: false,
      onlineOrdersList: [],
      onlineOrdersRefreshing: false,
      curOfflineTrxIndex: 0,
      onLineOrderLogsList: null,
      onlineTypeList: [
        // { name: OnlineTransactionsType.All, isSelect: true, channel: null },
        // { name: OnlineTransactionsType.OnlineStore, isSelect: false, channel: 1 },
        { name: OnlineTransactionsType.BeepOrders, isSelect: false, channel: 3 },
      ],
      statusListDone: false,
      nextStatusList: [
        { name: OnlineTransactionsStatusType.AllStatus, isSelect: true, status: null },
        { name: OnlineTransactionsStatusType.PickedUp, isSelect: false, status: OnlineOrderStatus.PickedUp },
        {
          name: OnlineTransactionsStatusType.PendingPayment,
          isSelect: false,
          status: OnlineOrderStatus.PendingPayment,
        },
        {
          name: OnlineTransactionsStatusType.PendingVerification,
          isSelect: false,
          status: OnlineOrderStatus.PendingVerification,
        },
        {
          name: OnlineTransactionsStatusType.ReadyForDelivery,
          isSelect: false,
          status: OnlineOrderStatus.ReadyForDelivery,
        },
        { name: OnlineTransactionsStatusType.Shipped, isSelect: false, status: OnlineOrderStatus.Shipped },
        { name: OnlineTransactionsStatusType.Paid, isSelect: false, status: OnlineOrderStatus.Paid },
        {
          name: OnlineTransactionsStatusType.ReadyForPickUp,
          isSelect: false,
          status: OnlineOrderStatus.ReadyForPickup,
        },
        {
          name: OnlineTransactionsStatusType.LogisticsConfirmed,
          isSelect: false,
          status: OnlineOrderStatus.LogisticsConfirmed,
        },
        { name: OnlineTransactionsStatusType.Confirmed, isSelect: false, status: OnlineOrderStatus.Confirmed },
        {
          name: OnlineTransactionsStatusType.PaymentCancelled,
          isSelect: false,
          status: OnlineOrderStatus.PaymentCancelled,
        },
        { name: OnlineTransactionsStatusType.Delivered, isSelect: false, status: OnlineOrderStatus.Delivered },
        { name: OnlineTransactionsStatusType.Cancelled, isSelect: false, status: OnlineOrderStatus.Cancelled },
        { name: OnlineTransactionsStatusType.Accepted, isSelect: false, status: OnlineOrderStatus.Accepted },
        {
          name: OnlineTransactionsStatusType.PrintedToKitchen,
          isSelect: false,
          status: OnlineOrderStatus.SentToKitchen,
        },
      ],
      currentOnlineFilter: { name: OnlineTransactionsType.BeepOrders, isSelect: true, channel: 3 },
      currentStatus: {
        name: OnlineTransactionsStatusType.AllStatus,
        isSelect: true,
        status: null,
      },
      searching: false,
      onlineOrderTabSelected: showBeepNewOrder,
      countDataSource: [],
      showingOrderCount: true,
      currentCountItem: {},
      prevIsInternetReachable: false,
    };
    this._onlineSearchText = DEFAULT_SEARCH_TEXT;
    this._registerSearchText = DEFAULT_SEARCH_TEXT;
  }

  componentDidMount() {
    requestAnimationFrame(() => {
      this.getOnlineOrderStatusList();
      if (this.state.onlineOrderTabSelected) {
        this.refreshOnlineOrderCounts();
      } else {
        this.refreshData();
      }
    });
    this._unsubscribeFocusListener = this.props.navigation.addListener('focus', this.onFocus);
    this._unsubscribeBlurListener = this.props.navigation.addListener('blur', this.onBlur);
  }

  componentDidUpdate(prevProps) {
    const prevIsInternetReachable = get(prevProps.currentNetworkInfo.toJS(), 'isInternetReachable', false);
    const currentIsInternetReachable = get(this.props.currentNetworkInfo.toJS(), 'isInternetReachable', false);

    if (!prevIsInternetReachable && currentIsInternetReachable) {
      if (this.state.onlineOrderTabSelected) {
        if (this.state.showingOrderCount) {
          this.refreshOnlineOrderCounts();
        } else {
          this.refreshOnlineOrders();
        }
      } else if (!isEmpty(this._registerSearchText)) {
        this.refreshData();
      }
    }
  }

  onFocus = () => {
    this._scannerListener = ScannerSubscription.addListener('Scanner_Resp', result => {
      const barcode = result.barcode;
      if (barcode) {
        this.onBarcodeSearch(barcode);
      }
    });
  };

  onBlur = () => {
    this._scannerListener && this._scannerListener.remove();
    this._scannerListener = null;
  };

  onBarcodeSearch = receiptNumber => {
    if (this._searchInput && this._searchInput.isFocused()) {
      // search OnlineTransaction with OrderId
      // or search RegisterTransaction with ReceiptNumber
      this.setState({ searchText: receiptNumber }, this.onSubmitSearch);
      this._searchInput.blur();
    }
  };

  componentWillUnmount() {
    this.props.actions.clearTransactionSession();
    Boolean(this._unsubscribeFocusListener) && this._unsubscribeFocusListener();
    Boolean(this._unsubscribeBlurListener) && this._unsubscribeBlurListener();
    this.onBlur();
  }

  refreshData = () => {
    this._offlinePageNo = 0;
    if (this.state.isRefunding) {
      this.setState({ isRefunding: false });
    }
    this.loadMoreOffline();
  };

  loadMoreOffline = () => {
    if (this._offlinePageNo < 0) return;
    const result = DAL.getTransactionListByPage(this._offlinePageNo, OFFLINE_PAGESIZE);
    const newDataList = this.generateOfflineDataList(result);
    const newSate: any = { dataList: newDataList };
    let currentTransaction;
    // const isSelectedTransactionAbsent = this.isSelectedTransactionAbsent(dataList);
    if (this._offlinePageNo === 0) {
      currentTransaction = result && result.length > 0 ? get(newDataList, 1) : null;
      newSate.currentTransaction = currentTransaction;
      if (currentTransaction) {
        newSate.curOfflineTrxIndex = 1;
      }
    }
    this.setState(newSate);

    if (currentTransaction && currentTransaction.customerId) {
      this.getCustomerById(currentTransaction.customerId, false);
    }
    // no more date
    if (result.length < OFFLINE_PAGESIZE) {
      this._offlinePageNo = -1;
    } else {
      this._offlinePageNo++;
    }
  };

  generateOfflineDataList = (result: TransactionType[]) => {
    const newDataList = [];
    const { dataList } = this.state;
    let lastTitle;
    if (this._offlinePageNo > 0) {
      const lastestItem = last(dataList);
      lastTitle = get(lastestItem, 'isTitle') ? get(lastestItem, 'sectionTitle') : moment(get(lastestItem, 'createdDate')).format(DATE_TITLE_FORMAT);
    }
    forEach(result, item => {
      const curTitle = moment(item.createdDate).format(DATE_TITLE_FORMAT);
      if (!isEqual(curTitle, lastTitle)) {
        lastTitle = curTitle;
        newDataList.push({ sectionTitle: lastTitle, isTitle: true });
      }
      newDataList.push(item);
    });
    return this._offlinePageNo > 0 ? dataList.concat(newDataList) : newDataList;
  };

  generateOfflineSearchedResult = (result: TransactionType[]) => {
    const newSearchResult: any[] = [];
    let lastTitle;
    forEach(result, item => {
      const curTitle = moment(item.createdDate).format(DATE_TITLE_FORMAT);
      if (!isEqual(curTitle, lastTitle)) {
        lastTitle = curTitle;
        newSearchResult.push({ sectionTitle: lastTitle, isTitle: true });
      }
      newSearchResult.push(item);
    });
    return newSearchResult;
  };

  /**
   * Check if there is a transaction is selected and contained in the transaction list.
   * The currentTransaction will be reset during cancelling the selected transaction
   * because the refreshData method will be called everytime the page focus again. thus,
   * the unexpected transaction is cancelled.
   * @param localTransactions
   * @returns
   */
  isSelectedTransactionAbsent = localTransactions => {
    const { currentTransaction } = this.state;
    if (!Boolean(currentTransaction)) {
      return true;
    }
    const index = findIndex(localTransactions, (v: any) => v.transactionId == currentTransaction.transactionId);
    return index < 0;
  };

  refreshOnlineOrderCounts = () => {
    const toDate = new Date().toISOString();
    const fromDate = moment().subtract(10, 'day').toISOString();
    const { currentStatus } = this.state;
    const onSuccess = {
      callback: payload => {
        this._nextToDate = moment(fromDate).subtract(1, 'day').toISOString();
        const countDataSource = [];
        const res = get(payload, 'res');
        const filterText = currentStatus.name !== t('All Status') && currentStatus.name;
        map(res, response => {
          const data = get(response, 'result');
          const statistics = getOrderCountStatistics(data, filterText);
          const date = moment(get(response, 'date')).format(DATE_TITLE_FORMAT);
          const title = date;
          const orderCount = { title, data: statistics };
          if (statistics.length > 0) {
            countDataSource.push(orderCount);
          }
        });
        this.setState({ countDataSource, currentOnlineTransaction: null, onlineOrdersRefreshing: false });
      },
    };

    const onFailure = {
      callback: () => {
        this.setState({ onlineOrdersRefreshing: false });
      },
    };
    this.setState({ onlineOrdersRefreshing: true });
    const status = currentStatus.status ? currentStatus.status : [];
    this.requestOnlineOrderStatusWithCount(fromDate, toDate, status, onSuccess, onFailure);
  };

  requestOnlineOrderStatusWithCount = (fromDate, toDate, status, onSuccess, onFailure?) => {
    const { businessName, storeId } = this.props;

    this.props.actions.requestOnlineOrderStatusWithCount({
      storeId,
      businessName,
      fromDate,
      toDate,
      status,
      onSuccess,
      onFailure,
    });
  };

  loadMoreOnlineOrderCounts = () => {
    if (this.state.onlineOrdersRefreshing) {
      return;
    }

    const fromDate = moment(this._nextToDate).subtract(5, 'day').toISOString();
    const { currentStatus } = this.state;
    const onSuccess = {
      callback: payload => {
        this._nextToDate = moment(fromDate).subtract(1, 'day').toISOString();
        const array = [];
        const res = get(payload, 'res');
        map(res, response => {
          const data = get(response, 'result');
          const filterText = currentStatus.name !== t('All Status') && currentStatus.name;
          const statistics = getOrderCountStatistics(data, filterText);
          const date = moment(get(response, 'date')).format(DATE_TITLE_FORMAT);
          const title = date;
          const orderCount = { title, data: statistics };
          if (statistics.length > 0) {
            array.push(orderCount);
          }
        });
        const countDataSource = this.state.countDataSource;
        const newCountDataSource = countDataSource.concat(array);
        this.setState({ countDataSource: newCountDataSource });
      },
    };
    const status = currentStatus.status ? currentStatus.status : [];
    const toDate = this._nextToDate;
    this.requestOnlineOrderStatusWithCount(fromDate, toDate, status, onSuccess);
  };

  getOnlineOrdersFilteredResult = results => {
    const { currentCountItem } = this.state;
    const filteredResults = map(results, order => {
      let title;
      if (isPreOrderWithAcceptedOrPaid(order)) {
        return order;
      }
      if (isDineIn(order)) {
        title = get(OnlineOrderStatusMapTitle, ['dineIn', order.status]);
      }
      if (isPickUpOrTakeAway(order)) {
        title = get(OnlineOrderStatusMapTitle, ['selfPickUp', order.status]);
      }
      if (isDelivery(order)) {
        title = get(OnlineOrderStatusMapTitle, ['delivery', order.status]);
      }
      // status of takeaway is same as pick up
      if (isTakeAway(order)) {
        title = get(OnlineOrderStatusMapTitle, ['selfPickUp', order.status]);
      }
      if (isZeptyOrder(order) || isECOrder(order)) {
        title = t('New Order');
      }

      if (!Boolean(title)) {
        title = order.status;
      }
      if (title === currentCountItem.name) {
        return order;
      }
    });
    return this.groupList(compact(filteredResults));
  };

  requestOnlineOrders = (onSuccess, fromDate, toDate, status, isPreOrder = null) => {
    const { businessName, storeId } = this.props;
    const params: SearchOnlineOrdersType & HttpActionArgs = {
      pageIndex: this._pageIndex,
      businessName,
      storeId,
      onSuccess,
      orderId: this._onlineSearchText,
      pageSize: 15,
      fromDate,
      toDate,
      status,
    };
    if (isPreOrder !== null) {
      params.isPreOrder = isPreOrder;
    }
    this.props.actions.searchOnlineOrders(params);
  };

  refreshOnlineOrders = () => {
    this._pageIndex = 1;
    if (!isEmpty(this._onlineSearchText)) {
      this.refreshSearchOnlineOrders();
      return;
    }
    this.setState({ onlineOrdersRefreshing: true });
    const { currentCountItem, currentOnlineFilter, currentStatus } = this.state;
    const onSuccess = {
      callback: payload => {
        const results = payload.res.results;
        const groupList = this.getOnlineOrdersFilteredResult(results);
        this._pageIndex += 1;
        const orderCount = get(groupList, ['0', 'data', 'length'], 0);
        if (orderCount > 0) {
          this.setState({
            onlineOrdersList: groupList,
            onlineOrdersRefreshing: false,
          });
          if (get(results, 'length', 0) > 0) {
            const orderId = get(groupList, ['0', 'data', '0', 'orderId']);
            Boolean(orderId) && this.getOnlineOrderDetails(orderId);
          }
        } else {
          this.refreshOnlineOrderCounts();
          this.setState({ showingOrderCount: true, onlineOrdersRefreshing: true, currentOnlineTransaction: null });
        }
      },
    };
    let status = currentCountItem.status || get(currentStatus, 'status', []);
    if (!currentOnlineFilter.channel) {
      status = currentCountItem.status || [];
    }
    this.requestOnlineOrders(onSuccess, this._currentFromdate, this._currentFromdate, status, this._isPreOrder);
  };

  loadMoreOnlineOrders = () => {
    const { onlineOrderTabSelected, currentCountItem, onlineOrdersRefreshing } = this.state;
    if (onlineOrdersRefreshing) {
      return;
    }
    if (onlineOrderTabSelected && !isEmpty(this._onlineSearchText)) {
      this.loadMoreSearchOnlineOrders();
      return;
    }
    const onSuccess = {
      callback: payload => {
        let newList = reduce(
          this.state.onlineOrdersList,
          (newList, item) => {
            return newList.concat(item.data);
          },
          []
        );
        newList = newList.concat(payload.res.results);
        const groupList = this.getOnlineOrdersFilteredResult(newList);
        this._pageIndex += 1;
        requestAnimationFrame(() => {
          this.setState({
            onlineOrdersList: groupList,
            onlineOrdersRefreshing: false,
          });
        });
      },
    };
    const { currentOnlineFilter, currentStatus } = this.state;
    // Online Store && !All Status

    let status = currentCountItem.status || get(currentStatus, 'status', []);
    if (!currentOnlineFilter.channel) {
      status = currentCountItem.status || [];
    }
    this.requestOnlineOrders(onSuccess, this._currentFromdate, this._currentFromdate, status, this._isPreOrder);
  };

  refreshSearchOnlineOrders = () => {
    const onSuccess = {
      callback: payload => {
        const groupList = this.groupList(payload.res.results);
        this._pageIndex += 1;
        this.setState({
          onlineSearchResult: groupList,
          onlineOrdersRefreshing: false,
          showingOrderCount: false,
        });
      },
    };
    this.requestOnlineOrders(onSuccess, '', '', []);
  };

  loadMoreSearchOnlineOrders = () => {
    const onSuccess = {
      callback: payload => {
        let newList = reduce(
          this.state.onlineSearchResult,
          (newList, item) => {
            return newList.concat(item.data);
          },
          []
        );
        newList = newList.concat(payload.res.results);
        const groupList = this.groupList(newList);
        this._pageIndex += 1;
        this.setState({
          onlineSearchResult: groupList,
          onlineOrdersRefreshing: false,
          showingOrderCount: false,
        });
      },
    };
    this.requestOnlineOrders(onSuccess, '', '', []);
  };

  groupList = result => {
    return chain(result)
      .groupBy(item => {
        let time;
        if (!isEmpty(item.expectDeliveryDateFrom)) {
          time = moment(item.expectDeliveryDateFrom);
        } else {
          time = moment(!isEmpty(item.createdDate) ? item.createdDate : item.createdTime);
        }
        return time.format(DATE_TITLE_FORMAT);
      })
      .toPairs()
      .map(currentItem => {
        return zipObject(['title', 'data'], currentItem);
      })
      .value();
  };

  onlineItemClicked = item => {
    if (item) {
      const { orderId } = item;
      this.getOnlineOrderDetails(orderId);
    }
  };

  getOnlineOrderDetails = orderId => {
    const getOrderSuccess = {
      callback: payload => {
        const order = payload.res;
        const contactName = get(order, ['contactDetail', 'name'], undefined);
        const originalShippingType = get(order, 'originalShippingType', undefined);
        if (Boolean(order)) {
          order.isRemote = true; // set tag for print receipt
          if (order.customerId || order.userId) {
            this.getCustomerById(order.customerId || order.userId, true);
          } else if (contactName) {
            this.setState({ onlineCustomer: { firstName: contactName } });
          } else {
            this.setState({ onlineCustomer: {} });
          }
          if (Boolean(originalShippingType)) {
            order.shippingType = originalShippingType;
          }
          this.setState({ currentOnlineTransaction: order });
        }
      },
    };
    this.props.actions.getOnlineOrderDetails({ orderId, onSuccess: getOrderSuccess });
  };

  registerItemClicked = (item, index) => {
    this.setState({ currentTransaction: item, curOfflineTrxIndex: index, isRefunding: false, registerCustomer: {} });
    this.props.actions.clearTransactionSession();
    if (item.customerId) {
      this.getCustomerById(item.customerId, false);
    }
  };

  getCustomerById = (customerId, isOnline) => {
    const { businessName } = this.props;
    const onSuccess = {
      callback: payload => {
        if (isOnline) {
          this.setState({
            onlineCustomer: payload.res,
          });
        } else {
          this.setState({
            registerCustomer: payload.res,
          });
        }
      },
    };

    this.props.actions.getCustomerById({
      bn: businessName,
      customerId: customerId,
      onSuccess,
    });
  };

  checkCanProposer = (orderOperation: OrderOperationEnum): Promise<boolean> => {
    return new Promise(res => {
      const { currentTransaction, currentOnlineTransaction, onlineOrderTabSelected } = this.state;

      const onComplete = (error: MRSError) => {
        return res(onMRSInterceptor(error));
      };

      this.props.actions.checkLimitBeforeExecute({
        transaction: onlineOrderTabSelected ? { ...currentOnlineTransaction, isOnlineOrder: true } : currentTransaction,
        orderOperation,
        onComplete,
      });
    });
  };

  cancelTrxBtnClicked = async () => {
    this.hideMenu();
    const { currentEmployeeId } = this.props;
    const checkResult = await this.checkCanProposer(OrderOperationEnum.Cancel);
    if (!checkResult) {
      return;
    }

    this.props.actions.requestAuthorizedAction({
      employeeId: currentEmployeeId,
      type: AuthorizationType.Cashier,
      name: CashierActions.Cancel,
      onSuccess: {
        callback: () => {
          if (!this.isTransactionCancelable()) return;
          if (this.isTransactionPaidByGhl()) {
            this.props.navigation.navigate('ModalInfo', {
              title: t('Warning'),
              isShowTitle: true,
              needGoBackWhenSubmit: false,
              info: t('CancelTransactionWarning') + '\n\n' + '*Payment will be refunded in full by GHL.',
              onCancelHandler: () => null,
              onSubmitHandler: this.startToCancelTrx,
            });
          } else {
            this.props.navigation.navigate('ModalInfo', {
              title: t('Warning'),
              isShowTitle: true,
              needGoBackWhenSubmit: false,
              info: t('CancelTransactionWarning'),
              onCancelHandler: () => null,
              onSubmitHandler: this.startToCancelTrx,
            });
          }
        },
      },
      onFailure: {
        callback: () => {
          // NOP
        },
      },
    });
  };

  // An order can be cancelled if the following conditions are met at the same time:
  // 1. In the order creation shift
  // 2. On the order creation business day
  isTransactionCancelable = () => {
    let cancelable = true;
    const { currentTransaction, onlineOrderTabSelected, currentOnlineTransaction } = this.state;
    let orderPlaceDate;
    if (onlineOrderTabSelected) {
      if (currentOnlineTransaction !== null) {
        const { isRNPOSCancellable, paidDate, createdTime } = currentOnlineTransaction;
        cancelable = isRNPOSCancellable;
        orderPlaceDate = Boolean(paidDate) ? paidDate : createdTime;
      }
    } else {
      if (Boolean(currentTransaction)) {
        const { createdDate, createdTime } = currentTransaction;
        orderPlaceDate = Boolean(createdDate) ? createdDate : createdTime;
        if (
          !Boolean(currentTransaction.transactionType) ||
          currentTransaction.isCancelled ||
          currentTransaction.transactionType === TransactionFlowType.Return
        ) {
          cancelable = false;
        }
      }
    }
    if (cancelable) {
      let shiftOpenStatus = false;
      const currentShift = DAL.getLastShift();
      if (currentShift !== undefined && (currentShift.closeTime === undefined || currentShift.closeTime === null)) {
        shiftOpenStatus = true;
      }
      if (!shiftOpenStatus) {
        return false;
      }
      if (Boolean(orderPlaceDate)) {
        const { operationHours } = this.props;
        const isOrderPlacedInThisShift = moment(currentShift.openTime).isBefore(moment(orderPlaceDate));
        const { curOperationStartTime, curOperationEndTime } = getCurOperationDayTime(operationHours);
        const isOrderPlacedInThisBusinessDay = moment(orderPlaceDate).isAfter(curOperationStartTime) && moment(orderPlaceDate).isBefore(curOperationEndTime);
        cancelable = isOrderPlacedInThisShift && isOrderPlacedInThisBusinessDay;
      }
    }
    return cancelable;
  };

  isTransactionEInvoicePrintable = () => {
    const { currentTransaction, onlineOrderTabSelected, currentOnlineTransaction } = this.state;
    if (onlineOrderTabSelected) {
      if (Boolean(currentOnlineTransaction)) {
        if (!this.props.eInvoicePhase2Enabled) {
          return false;
        }
        if (!this.props.eInvoiceBeepWebstoreEnabled && currentOnlineTransaction.paymentMethod !== 'Offline') {
          return false;
        }
        const { payments } = currentOnlineTransaction as TransactionType;
        if (payments) {
          for (const payment of payments) {
            const paymentOption = PaymentOptions.getPaymentOptionByName(payment.paymentMethod);
            if (paymentOption && paymentOption.isExcludedFromEInvoice) {
              return false;
            }
          }
        }
      }
    } else {
      if (Boolean(currentTransaction)) {
        if (!this.props.eInvoicePhase2Enabled) {
          return true;
        }
        const { payments } = currentTransaction as TransactionType;
        if (payments) {
          for (const payment of payments) {
            const paymentOption = PaymentOptions.getPaymentOptionById(payment.paymentMethodId);
            if (paymentOption && paymentOption.isExcludedFromEInvoice) {
              return false;
            }
          }
        }
      }
    }
    return true;
  };

  isTransactionPaidByGhl = () => {
    for (const payment of this.getTheTransaction().payments) {
      if (payment?.type === PaymentType.TERMINAL) {
        return true;
      }
    }
    return false;
  };

  isTransactionRefundable = () => {
    let refundable = true;
    const { currentTransaction, onlineOrderTabSelected } = this.state;
    const baseLimit =
      !onlineOrderTabSelected &&
      currentTransaction !== null &&
      (!Boolean(currentTransaction.transactionType) || currentTransaction.isCancelled || currentTransaction.transactionType === TransactionFlowType.Return);

    const smDayRefundable = () => {
      if (!this.props.smMallLimited) {
        return true;
      }
      const transaction = this.getTheTransaction(false);
      if (!transaction) {
        return false;
      }
      const { createdDate, createdTime, paidDate } = transaction;
      const orderPlaceDate = paidDate || createdDate || createdTime;

      if (orderPlaceDate) {
        const { operationHours } = this.props;
        const { curOperationStartTime, curOperationEndTime } = getCurOperationDayTime(operationHours);
        return moment(orderPlaceDate).isAfter(curOperationStartTime) && moment(orderPlaceDate).isBefore(curOperationEndTime);
      }
      return false;
    };
    if (baseLimit) {
      refundable = false;
    }
    if (refundable) {
      refundable = smDayRefundable();
    }
    return refundable;
  };

  onCancelOfflineBeepOrder = ({ success, isOfflinePayment, refundAmount, paymentMethod, paymentProviders }) => {
    if (success) {
      if (refundAmount === 0) {
        this.refreshOnlineOrders();
      } else {
        const { currency, localCountryMap } = this.props;
        const currencySymbol = newConvertCurrencyToSymbol(localCountryMap, currency);
        const title = t('Transaction Successfully Cancelled');
        let info: string;
        if (isOfflinePayment) {
          info = `${t('Please refund')} ${currencySymbol} ${refundAmount} ${t('to the customer')}`;
        } else {
          const isMAEPayemnt = paymentProviders.includes('Maybank2uMAE');
          if (isMAEPayemnt) {
            info = `${refundAmount} will be refunded automatically to customer through MAE by Maybank2u by tomorrow.`;
          } else {
            info = `${t('Payment will be refunded to user via')} ${paymentMethod} ${t('within')} 7 ${t('working day')}`;
          }
        }
        this.props.navigation.replace('ModalInfo', {
          isShowTitle: true,
          title,
          info,
          onSubmitHandler: this.refreshOnlineOrders,
        });
      }
    } else {
      this.props.navigation.navigate('ModalInfo', {
        isShowTitle: true,
        title: t('Failed to Cancel Order'),
        info: t('Network error occured, please try again'),
        onSubmitHandler: () => null,
      });
    }
  };

  onCancelOfflineSuccess = () => {
    // update the target transaction only
    const { currentTransaction, curOfflineTrxIndex, registerSearchResult, dataList } = this.state;
    const dataSource = Boolean(registerSearchResult) ? registerSearchResult : dataList;
    if (currentTransaction) {
      const updatedTrx = DAL.getTransactionById(currentTransaction.transactionId);
      if (updatedTrx && updatedTrx.isValid() && dataSource.length > curOfflineTrxIndex) {
        dataSource[curOfflineTrxIndex] = updatedTrx;
        const newState: any = { currentTransaction: updatedTrx };
        if (Boolean(registerSearchResult)) {
          newState.registerSearchResult = dataSource;
        } else {
          newState.dataList = dataSource;
        }
        this.setState(newState);
      }
    }
  };

  startToCancelTrx = () => {
    const { currentTransaction, onlineOrderTabSelected, currentOnlineTransaction } = this.state;
    // only online
    if (onlineOrderTabSelected && Boolean(currentOnlineTransaction)) {
      this.props.navigation.replace('CancelReason', {
        transactionId: currentOnlineTransaction.id,
        transaction: currentOnlineTransaction,
        isBeepOrder: true,
        needAutoGoBack: false,
        eInvoiceEnabled: this.props.eInvoiceEnabled,
        eInvoicePhase2Enabled: this.props.eInvoicePhase2Enabled,
        eInvoiceBeepWebstoreEnabled: this.props.eInvoiceBeepWebstoreEnabled,
        onCancelOfflineBeepOrder: this.onCancelOfflineBeepOrder,
      });
      return;
    }
    // offline
    if (currentTransaction === null) return;
    const receiptNumber = currentTransaction.receiptNumber;
    // Checking status
    const onComplete = {
      callback: ({ errorMsg, errorTitle }) => {
        if (!isEmpty(errorMsg)) {
          NavigationService.goBack();
          this.showErrorModel(errorMsg, errorTitle);
        } else {
          this.props.navigation.replace('CancelReason', {
            transactionId: currentTransaction.transactionId,
            transaction: currentTransaction,
            eInvoiceEnabled: this.props.eInvoiceEnabled,
            eInvoicePhase2Enabled: this.props.eInvoicePhase2Enabled,
            eInvoiceBeepWebstoreEnabled: this.props.eInvoiceBeepWebstoreEnabled,
            onCancelSuccess: this.onCancelOfflineSuccess,
          });
        }
      },
    };
    this.props.actions.checkCancellableWithRefundStatus({
      receiptNumber,
      onComplete,
      transactionId: get(currentTransaction, 'transactionId'),
      alreadyRefundErrMsg: t('AlreadyRefundError'),
    });
  };

  showErrorMsg = (msg, title = t('Warning')) => {
    Alert.alert(title, msg, [{ text: t('OK') }]);
  };

  showErrorModel = (msg, title = t('Warning')) => {
    this.props.navigation.navigate('ModalInfo', {
      title,
      isShowTitle: true,
      info: msg,
      titleIcon: <Icon name='warning' size={24} color='#FFA500' />,
      okText: t('Close'),
    });
  };

  refundTrxBtnClicked = () => {
    this.requestAuthorization(this.startToRefundTrx);
  };

  requestAuthorization = onAction => {
    const { currentEmployeeId } = this.props;
    this.props.actions.requestAuthorizedAction({
      employeeId: currentEmployeeId,
      type: AuthorizationType.Cashier,
      name: CashierActions.Refund,
      onSuccess: {
        callback: () => {
          onAction();
        },
      },
      onFailure: {
        callback: () => {
          // NOP
        },
      },
    });
  };

  startToRefundTrx = async () => {
    const { currentTransaction } = this.state;
    if (currentTransaction === null) return;
    const checkResult = await this.checkCanProposer(OrderOperationEnum.Refund);
    if (!checkResult) {
      return;
    }
    const receiptNumber = currentTransaction.receiptNumber;
    const transactionId = currentTransaction.transactionId;
    // Checking status
    const onComplete = {
      callback: ({ errorMsg, errorTitle }) => {
        if (!isEmpty(errorMsg)) {
          this.showErrorModel(errorMsg, errorTitle);
        } else {
          this.setState({ isRefunding: true });
        }
      },
    };
    this.props.actions.checkTrxRefundStatus({
      receiptNumber,
      onComplete,
      transactionId,
      needCustomerInfo: true,
      needWithInternet: true,
    });
  };

  cancelRefundClicked = () => {
    this.setState({ isRefunding: false });
    this.props.actions.clearTransactionSession();
  };

  onSubmitSearchWithExactTrx = () => {
    this.onSubmitSearch(TrxSearchType.LinkedRefundTrx);
  };

  onSubmitSearch = (trxSearchType?: any) => {
    const { onlineOrderTabSelected, searchText } = this.state;
    if (onlineOrderTabSelected) {
      if (this._onlineSearchText !== searchText) this._onlineSearchText = searchText;
    } else {
      if (this._registerSearchText !== searchText) this._registerSearchText = searchText;
    }

    if (onlineOrderTabSelected) {
      if (!isEmpty(this._onlineSearchText)) {
        this.refreshOnlineOrders();
      } else {
        this.setState({ onlineSearchResult: null });
        this.refreshOnlineOrders();
      }
    } else {
      if (!isEmpty(this._registerSearchText)) {
        // If the searchText is the same, add throttling. Otherwise, no throttling
        if (this._lastRegisterSearchText == this._registerSearchText) {
          this.searchRegisterTransactionThrottling(trxSearchType);
        } else {
          this._lastRegisterSearchText = this._registerSearchText;
          this.searchRegisterTransaction(trxSearchType);
        }
      } else {
        this.setState({ registerSearchResult: null });
        this.refreshData();
      }
    }
  };

  searchRegisterTransaction = (trxSearchType?: any) => {
    const linkedRefundTrx = trxSearchType === TrxSearchType.LinkedRefundTrx;
    const onComplete = {
      callback: (result?: any[]) => {
        const groupList = this.generateOfflineSearchedResult(result);
        let currentTransaction;
        let curOfflineTrxIndex;
        if (linkedRefundTrx) {
          const findCurTrxIndex = findIndex(result, trx => isEqual(trx.receiptNumber, this._registerSearchText));
          if (findCurTrxIndex > -1) {
            currentTransaction = result[findCurTrxIndex];
            curOfflineTrxIndex = findCurTrxIndex;
          }
        }
        if (!currentTransaction) {
          currentTransaction = groupList !== null && groupList.length > 0 ? get(groupList, 1) : null;
          if (currentTransaction) {
            curOfflineTrxIndex = 1;
          }
        }
        this.setState({ currentTransaction, curOfflineTrxIndex, registerSearchResult: groupList, isRefunding: false });
        if (currentTransaction && currentTransaction.customerId) {
          this.getCustomerById(currentTransaction.customerId, false);
        } else {
          this.setState({
            registerCustomer: {},
          });
        }
      },
    };
    this.props.actions.searchTransactions({
      keyword: this._registerSearchText,
      linkedRefundTrx,
      onComplete,
    });
  };

  searchRegisterTransactionThrottling = throttle(this.searchRegisterTransaction, 2000, {
    leading: true,
    trailing: false,
  });

  changeSearchKeyWord = newValue => {
    this.setState({ searchText: newValue.trim() });
  };

  clearSearchResult = () => {
    const { registerSearchResult, onlineSearchResult } = this.state;
    this._onlineSearchText = DEFAULT_SEARCH_TEXT;
    this._registerSearchText = DEFAULT_SEARCH_TEXT;
    this.setState({ searchText: DEFAULT_SEARCH_TEXT });
    if (Boolean(registerSearchResult)) {
      this.setState({ registerSearchResult: null });
      this.refreshData();
    }
    if (Boolean(onlineSearchResult)) {
      this.setState({ onlineSearchResult: null, showingOrderCount: true, currentOnlineTransaction: null });
      this.refreshOnlineOrderCounts();
    }
  };

  toggleSearchInputHandler = () => {
    requestAnimationFrame(() => {
      this.setState(prevState => {
        return {
          searching: !prevState.searching,
        };
      });
    });
  };

  showFilter = () => {
    if (this.state.showingOrderCount) {
      this._onLineDialog && this._onLineDialog.toggle();
    }
  };

  searchInputRef = refs => {
    this._searchInput = refs;
  };

  renderSearchBar = () => {
    const { searchText, onlineOrderTabSelected, showingOrderCount, statusListDone } = this.state;
    const filterDisable = !showingOrderCount || !(statusListDone && this._statusList.length > 0);
    return (
      <View style={styles.searchBarContainer}>
        <IconSearchLabel width={scaleSizeW(48)} height={scaleSizeW(48)} color={CommonColors.Icon} />
        <View style={styles.searchInputContainer}>
          <PosTextInput
            {...testProps('al_transactions_search')}
            ref={this.searchInputRef}
            style={[SharedStyles.flexOne, styles.inputText]}
            placeholder={t('Search')}
            autoCapitalize={'none'}
            clearButtonMode='never'
            returnKeyType='search'
            value={searchText}
            underlineColorAndroid='transparent'
            onSubmitEditing={this.onSubmitSearch}
            onChangeText={this.changeSearchKeyWord}
            placeholderTextColor={'#60636B'}
          />
          <TouchableOpacity {...testProps('al_btn_90')} onPress={this.clearSearchResult} style={styles.clearButton}>
            {Boolean(searchText) && <FontAwesomeIcons name={'times-circle'} color={'#cccccc'} size={scaleSizeW(36)} />}
          </TouchableOpacity>
        </View>
        {onlineOrderTabSelected && (
          <View style={styles.filterConatiner}>
            <View style={styles.filterDividerLine} />
            <TouchableOpacity disabled={filterDisable} onPress={this.showFilter} style={styles.filterButton} {...testProps('al_showFilter_btn')}>
              <IconFilter width={scaleSizeW(48)} height={scaleSizeW(48)} color={filterDisable ? '#DBDBDB' : '#FC7118'} />
            </TouchableOpacity>
          </View>
        )}
      </View>
    );
  };

  renderRegisterTransactionItem = ({ item, index }: any) => {
    const { currentTransaction } = this.state;
    const isSelected = currentTransaction ? item.transactionId === currentTransaction.transactionId : false;
    return item.isTitle ? (
      <View style={styles.sectionHeader}>
        <Text
          style={{
            width: '100%',
            fontWeight: '500',
            fontSize: currentThemes.fontSize20,
            color: '#303030',
          }}
        >
          {item.sectionTitle}
        </Text>
      </View>
    ) : (
      <TransactionItem index={index} item={item} itemType={'register'} itemClicked={this.registerItemClicked} isSelected={isSelected} />
    );
  };

  renderOnlineTransactionItem = ({ item, index }) => {
    const { currentOnlineTransaction } = this.state;
    const isSelected = currentOnlineTransaction ? item.orderId === currentOnlineTransaction.receiptNumber : false;
    return <TransactionItem index={index} item={item} itemType={'online'} itemClicked={this.onlineItemClicked} isSelected={isSelected} />;
  };

  renderOrderNumber = ({ item, section: { title } }: any) => {
    const { showingOrderCount } = this.state;
    return (
      <OrderCount
        item={item}
        itemClicked={() => {
          this._currentFromdate = moment(title).toISOString();
          this._isPreOrder = Boolean(item.isPreOrder);
          this.setState({ showingOrderCount: false, currentCountItem: item, onlineOrdersList: [] }, () => {
            this.refreshOnlineOrders();
          });
        }}
        selected={!showingOrderCount}
      />
    );
  };

  renderOnLineTransactionHeaderItem = ({ section: { title } }: { section: SectionListData<any> }) => {
    const { onlineOrderTabSelected, showingOrderCount, currentCountItem } = this.state;
    let showToday = false;
    if (moment().format(DATE_TITLE_FORMAT) === title) {
      showToday = true;
    }
    if (onlineOrderTabSelected && !showingOrderCount && isEmpty(this._onlineSearchText)) {
      return (
        <View>
          <View style={styles.sectionHeader}>
            <Text
              style={{
                width: '100%',
                fontWeight: '500',
                fontSize: currentThemes.fontSize20,
                color: '#303030',
              }}
            >
              {showToday ? t('TODAY') : title}
            </Text>
          </View>
          <OrderCount
            item={currentCountItem}
            itemClicked={() => {
              this._currentFromdate = moment(title).toISOString();
              this.refreshOnlineOrderCounts();
              this.setState({ showingOrderCount: true });
            }}
            selected={!showingOrderCount}
          />
        </View>
      );
    } else {
      return (
        <View style={styles.sectionHeader}>
          <Text
            style={{
              width: '100%',
              fontWeight: '500',
              fontSize: currentThemes.fontSize20,
              color: '#303030',
            }}
          >
            {showToday ? t('TODAY') : title}
          </Text>
        </View>
      );
    }
  };

  renderSeparator = () => {
    return <View style={{ height: 1, backgroundColor: '#C8C7CC', flex: 1 }} />;
  };

  registerItemKeyExtractor = item => {
    return item.isTitle ? `title_${item.sectionTitle}` : `register_${item.transactionId}`;
  };

  onlineItemKeyExtractor = (item, index) => {
    const { showingOrderCount } = this.state;
    return showingOrderCount ? item.status + index : `online_${index}_${item.orderId}`;
  };

  onChangeTab = ({ i }) => {
    if (i === 1) {
      this.setState({
        onlineOrderTabSelected: true,
      });
      if (this.state.showingOrderCount && get(this.state.countDataSource, 'length', 0) === 0) {
        this.refreshOnlineOrderCounts();
      }
    } else {
      this.setState({
        onlineOrderTabSelected: false,
      });
      const localTransactionCount = get(this.state.dataList, 'length', 0);
      if (localTransactionCount === 0) {
        this.refreshData();
      }
    }
  };

  onStatusItemSelected = selectedIndex => {
    requestAnimationFrame(() => {
      this.setState(
        {
          currentStatus: this._statusList[selectedIndex],
        },
        () => {
          if (this.state.showingOrderCount) {
            this.refreshOnlineOrderCounts();
          } else {
            this.refreshOnlineOrders();
          }
        }
      );
    });
  };

  setFilterRef = refs => (this._onLineDialog = refs);

  renderTransactionList = () => {
    const showBeepNewOrder = getParam(this.props, 'showBeepNewOrder');
    const {
      onlineOrderTabSelected,
      countDataSource,
      showingOrderCount,
      statusListDone,
      registerSearchResult,
      onlineSearchResult,
      dataList,
      onlineOrdersList,
      currentTransaction,
    } = this.state;
    const { isOnline, isQROrderingEnabled } = this.props;
    const dataSource = Boolean(registerSearchResult) && !onlineOrderTabSelected ? registerSearchResult : dataList;
    const onlineOrders = Boolean(onlineSearchResult) && onlineOrderTabSelected ? onlineSearchResult : onlineOrdersList;
    return (
      <View
        style={{
          width: TRANSACTIONLIST_WIDTH,
          backgroundColor: 'white',
        }}
      >
        <PauseBeepNotification maxWidth={TRANSACTIONLIST_WIDTH} containerStyle={{ paddingLeft: scaleSizeW(20) }} />
        {this.renderSearchBar()}
        <ScrollableTabView
          locked
          scrollWithoutAnimation
          onChangeTab={this.onChangeTab}
          style={{ width: TRANSACTIONLIST_WIDTH, height: '100%', overflow: 'hidden' }}
          renderTabBar={() => <TabBarWithTextAndIcon />}
          initialPage={showBeepNewOrder ? 1 : 0}
          tabBarActiveTextColor={'#393939'}
          tabBarInactiveTextColor={'#60636B'}
          tabBarTextStyle={{ fontSize: currentThemes.fontSize24, fontWeight: '500' }}
          tabBarUnderlineStyle={{ backgroundColor: '#FC7118', height: scaleSizeH(8), bottom: -1 }}
          prerenderingSiblingsNumber={1}
        >
          <TabContainer tabLabel={{ isVisiable: false, label: t('Register') }}>
            <FlashList
              style={{ flex: 1, backgroundColor: 'red' }}
              data={dataSource}
              keyExtractor={this.registerItemKeyExtractor}
              renderItem={this.renderRegisterTransactionItem}
              ItemSeparatorComponent={this.renderSeparator}
              onEndReached={this.loadMoreOffline}
              onEndReachedThreshold={0.5}
              extraData={currentTransaction}
            />
          </TabContainer>
          {(isOnline || isQROrderingEnabled) && (
            <TabContainer tabLabel={{ isVisiable: false, label: t('Online') }}>
              {statusListDone && this._statusList.length > 0 && (
                <ModalOnlineFilter ref={this.setFilterRef} list={this._statusList} isNeedVisiable={false} onItemSelect={this.onStatusItemSelected} />
              )}
              {this.renderSectionList(
                showingOrderCount,
                countDataSource,
                this.refreshOnlineOrderCounts,
                this.renderOrderNumber,
                this.loadMoreOnlineOrderCounts
              )}
              {this.renderSectionList(!showingOrderCount, onlineOrders, this.refreshOnlineOrders, this.renderOnlineTransactionItem, this.loadMoreOnlineOrders)}
            </TabContainer>
          )}
        </ScrollableTabView>
      </View>
    );
  };

  renderSectionList = (show, dataSource, onRefresh, renderItem, onEndReached) => {
    return (
      <SectionList
        style={{
          display: show ? 'flex' : 'none',
          position: show ? 'relative' : 'absolute',
        }}
        onRefresh={onRefresh}
        refreshing={this.state.onlineOrdersRefreshing}
        renderItem={renderItem}
        renderSectionHeader={this.renderOnLineTransactionHeaderItem}
        sections={dataSource}
        ItemSeparatorComponent={this.renderSeparator}
        keyExtractor={this.onlineItemKeyExtractor}
        onEndReached={onEndReached}
        onEndReachedThreshold={0.5}
        extraData={this.state.currentOnlineTransaction}
        getItemLayout={this.getItemLayout}
      />
    );
  };

  getItemLayout = sectionListGetItemLayout({
    // The height of the row with rowData at the given sectionIndex and rowIndex
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    getItemHeight: (rowData, sectionIndex, rowIndex) => ITEM_HEIGHT,
    // These four properties are optional
    getSeparatorHeight: () => 1, // The height of your separators
    getSectionHeaderHeight: () => scaleSizeH(40), // The height of your section headers
  });

  goHome = () => {
    const {
      actions: { navigateToHome },
      navigation,
    } = this.props;
    requestAnimationFrame(() => navigateToHome({ navigation }));
  };

  renderEmpty = () => {
    return (
      <View style={{ flex: 1 }}>
        <StatusBar barStyle='dark-content' />
        <View style={[styles.emptyTopBar, SharedStyles.titleShadow]}>
          <View style={styles.emptyTopBarSubContainer}>
            <Text style={styles.emptyTopBarTitle}>{t('No Transaction')}</Text>
            <TouchableOpacity
              {...testProps('al_btn_442')}
              activeOpacity={1}
              style={[SharedStyles.touchableIconContainer, styles.emptyTopBarLeftIcon]}
              onPress={this.openDrawer}
            >
              <MenuWithNotification color={CommonColors.Icon} />
            </TouchableOpacity>
          </View>
        </View>
        <View style={styles.emptyContentConatiner}>
          <IconCactus />
          <Text style={styles.textWarn}>{t('No transaction to see here')}</Text>
          <Text style={styles.textDesc}>{t('Sell something to see your first transaction')}</Text>
          <TouchableOpacity {...testProps('al_btn_944')} style={styles.backToRegisterBtnContainer} onPress={this.goHome}>
            <Text style={styles.backToRegisterBtnText}>{t('Back To Register')}</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  renderContentEmpty = () => {
    const { currentNetworkInfo } = this.props;
    const isInternetReachable = get(currentNetworkInfo.toJS(), 'isInternetReachable', false);

    return isInternetReachable ? this.renderContentEmptyView() : this.renderNoInternet();
  };

  renderContentEmptyView = () => {
    return (
      <View style={styles.emptyContentConatiner}>
        <IconCactus />
        <Text style={styles.textWarn}>{t('No transaction to see here')}</Text>
        <Text style={styles.textDesc}>{t('Click on order tab to see your transaction details')}</Text>
      </View>
    );
  };

  clickToRefresh = () => {
    if (this.state.onlineOrderTabSelected) {
      if (this.state.showingOrderCount) {
        this.refreshOnlineOrderCounts();
      } else {
        this.refreshOnlineOrders();
      }
    } else {
      this.refreshData();
    }
  };

  renderNoInternet = () => {
    return (
      <View style={styles.emptyContentConatiner}>
        <IconBadGlobal color='#D3D8EB' width={scaleSizeW(300)} height={scaleSizeH(180)} />
        <Text style={styles.textWarn}>{t('It seems like you’re offline now')}</Text>
        <Text style={styles.textDesc2}>{t('During this period, the POS Register device is unable to receive and sync incoming online orders')}</Text>
        <TouchableOpacity {...testProps('al_btn_641')} style={styles.button} onPress={this.clickToRefresh}>
          <Text style={styles.buttonText}>{t('Click to Refresh')}</Text>
        </TouchableOpacity>
      </View>
    );
  };

  renderNotificationButton = transaction => {
    const { receiptNumber } = transaction || {};
    const { currentEmployeeId } = this.props;
    // beep dine-in && login
    const shouldRenderTakeAwayNotificationButton = this.getShouldRenderTakeAwayNotificationButton(transaction);
    if (shouldRenderTakeAwayNotificationButton) {
      return <NotificationButton receiptNumber={receiptNumber} employeeId={currentEmployeeId} />;
    }
    return null;
  };

  onClickRefundForOriginal = () => {
    const { currentTransaction } = this.state;
    if (currentTransaction && currentTransaction.originalReceiptNumber) {
      this.setState({ searchText: currentTransaction.originalReceiptNumber }, this.onSubmitSearchWithExactTrx);
    }
  };

  renderTransactionDetail = transaction => {
    const { isRefunding, onlineOrderTabSelected, onlineCustomer, registerCustomer } = this.state;
    let detailsName = '';
    let isShipping = true;
    if (transaction && transaction.shippingType && transaction.shippingType === ShippingType.DELIVERY) {
      detailsName = t('Shipping Details');
      isShipping = true;
    } else if (transaction && isPickUpOrTakeAway(transaction)) {
      detailsName = t('Pickup Details');
      isShipping = true;
    } else {
      detailsName = t('Details');
      isShipping = false;
    }

    const { enableCashback, currency, localCountryMap } = this.props;
    return (
      <View style={{ flex: 1, paddingVertical: scaleSizeH(24), paddingRight: scaleSizeW(32), marginLeft: scaleSizeW(32) }}>
        <View style={{ flex: 1 }}>
          <View
            style={[
              { backgroundColor: 'white' },
              onlineOrderTabSelected
                ? {
                    borderTopLeftRadius: 8,
                    borderTopRightRadius: 8,
                  }
                : { borderRadius: 8 },
            ]}
          >
            {isRefunding && (
              <View
                style={{
                  backgroundColor: '#242644',
                  marginHorizontal: 0,
                  height: scaleSizeH(56),
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Text
                  style={{
                    color: 'white',
                    fontSize: currentThemes.fontSize24,
                    textAlign: 'center',
                  }}
                >
                  {t('Issue Refund')}
                </Text>
              </View>
            )}
            {!isRefunding && !onlineOrderTabSelected && Boolean(transaction.originalReceiptNumber) && (
              <TouchableOpacity
                {...testProps('al_btn_298')}
                style={{
                  backgroundColor: '#242644',
                  marginHorizontal: 0,
                  height: scaleSizeH(56),
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
                onPress={this.onClickRefundForOriginal}
              >
                <Text style={{ color: 'white', fontSize: currentThemes.fontSize24, textAlign: 'center' }}>
                  {t('Refund for') + `# ${transaction.originalReceiptNumber}`}
                </Text>
              </TouchableOpacity>
            )}
            <TransactionBasicInfo
              customer={onlineOrderTabSelected ? onlineCustomer : registerCustomer}
              transactionType={get(transaction, 'transactionType', '')}
              payments={get(transaction, 'payments', [])}
              receiptNumber={get(transaction, 'receiptNumber', '')}
              total={get(transaction, 'total', '')}
              salesChannel={get(transaction, 'salesChannel', '')}
              isOnlineTransaction={onlineOrderTabSelected}
              navigation={this.props.navigation}
            />
            {this.renderNotificationButton(transaction)}
            {this.renderTrxActionButtons()}
          </View>
          {transaction && transaction.channel && (transaction.transactionType !== TransactionFlowType.Return || this.props.eInvoiceEnabled) ? (
            <ScrollableTabView
              style={{ width: '100%', height: '100%', overflow: 'hidden', marginTop: 1 }}
              renderTabBar={() => <DefaultTabBar tabBarUnderlineStyle={styles.tabBarUnderLineStyle} style={{ borderWidth: 0, height: scaleSizeH(96) }} />}
              initialPage={0}
              tabBarBackgroundColor='white'
              tabBarActiveTextColor='#393939'
              tabBarInactiveTextColor={'#757575'}
              tabBarTextStyle={styles.tabBarTextStyle}
              prerenderingSiblingsNumber={1}
              scrollWithoutAnimation
            >
              <TabContainer tabLabel={t('Order')}>
                <TrxDetailInfo
                  transaction={transaction}
                  onChangeItemQuantity={this.onChangeItemQuantity}
                  onChangeItemTotal={this.onChangeItemTotal}
                  onPressDeleteItem={this.onPressDeleteItem}
                  onPressClearPromotions={this.onPressClearPromotions}
                  isRefunding={isRefunding}
                  enableCashback={enableCashback}
                  currency={currency}
                  shippingFee={
                    onlineOrderTabSelected &&
                    transaction.deliveryInformation &&
                    transaction.deliveryInformation.length > 0 &&
                    transaction.deliveryInformation[0].shippingFee
                  }
                  isOnlineTransaction={onlineOrderTabSelected}
                  localCountryMap={localCountryMap}
                />
              </TabContainer>
              <TabContainer tabLabel={detailsName}>{this.renderShippingDetails(isShipping, transaction)}</TabContainer>
              <TabContainer tabLabel={t('History')}>{this.renderOnlineOrderLogs(transaction)}</TabContainer>
            </ScrollableTabView>
          ) : (
            <TrxDetailInfo
              transaction={transaction}
              onChangeItemQuantity={this.onChangeItemQuantity}
              onChangeItemTotal={this.onChangeItemTotal}
              onPressDeleteItem={this.onPressDeleteItem}
              onPressClearPromotions={this.onPressClearPromotions}
              isRefunding={isRefunding}
              enableCashback={enableCashback}
              currency={currency}
              localCountryMap={localCountryMap}
            />
          )}
        </View>
      </View>
    );
  };

  onPressClearPromotions = () => {
    this.props.actions.clearRefundPromotion();
  };

  getOnlineOrderStatusList = () => {
    const onSuccess = {
      callback: payload => {
        const statusList = [];
        const nameArray = [];
        if (payload.res.results) {
          statusList.push({ name: OnlineTransactionsStatusType.AllStatus, isSelect: true, status: null });
          payload.res.results.map(status => {
            const deliveryName = get(OnlineOrderStatusMapTitle, ['delivery', status]);
            const selfPickName = get(OnlineOrderStatusMapTitle, ['selfPickUp', status]);
            const dineInName = get(OnlineOrderStatusMapTitle, ['dineIn', status]);
            nameArray.push(deliveryName, selfPickName, dineInName);
          });
          const uniqNameArray = compact(uniq(nameArray));
          map(uniqNameArray, name => {
            const deliveryStatus = get(OnlineOrderTitleMapStatus, ['delivery', name]);
            const selfPickStatus = get(OnlineOrderTitleMapStatus, ['selfPickUp', name]);
            const dineInStatus = get(OnlineOrderTitleMapStatus, ['dineIn', name]);
            const statusArray = [deliveryStatus, selfPickStatus, dineInStatus];
            if (name === t('PendingPickUp')) {
              statusArray.push(OnlineOrderStatus.Paid, OnlineOrderStatus.Accepted);
            }
            const uniqStatusArray = compact(uniq(statusArray));
            statusList.push({ name: name, isSelect: false, status: uniqStatusArray });
          });
          this._statusList = statusList;
          this.setState({
            statusListDone: true,
          });
        }
      },
    };

    this.props.actions.getOnlineOrderStatusList({
      onSuccess,
    });
  };

  renderOnlineOrderLogs = transaction => {
    const { receiptNumber } = transaction;
    const { onlineOrderTabSelected } = this.state;
    return <TransactionHistoryLogs orderId={receiptNumber} isOnlineOrder={onlineOrderTabSelected} />;
  };

  onChangeItemTotal = (text, itemIndex) => {
    const total = parseFloat(text) || 0;
    if (total) {
      this.props.actions.updateReturnItem({ itemIndex, total });
    }
  };

  onPressDeleteItem = itemIndex => {
    if (!isNaN(itemIndex)) {
      this.props.actions.updateReturnItem({ itemIndex, isDelete: true });
    }
  };

  onChangeItemQuantity = (text, itemIndex) => {
    const quantity = Number(text) || 0;
    this.props.actions.updateReturnItem({ quantity, itemIndex });
  };

  renderShippingDetails = (isShipping, transaction) => {
    const address = transaction && transaction.deliveryInformation && transaction.deliveryInformation.length > 0 && transaction.deliveryInformation[0].address;
    const useStorehubLogistics = get(transaction, ['deliveryInformation', '0', 'useStorehubLogistics']);
    const contact = transaction && transaction.contactDetail;
    let addressDetail = '';
    if (address) {
      if (!isEmpty(address.address)) {
        addressDetail = addressDetail + address.address + ',';
      }
      if (!isEmpty(address.city)) {
        addressDetail = addressDetail + address.city + ',';
      }
      if (!isEmpty(address.postCode)) {
        addressDetail = addressDetail + address.postCode + ',';
      }
      if (!isEmpty(address.state)) {
        addressDetail = addressDetail + address.state + ',';
      }
      if (!isEmpty(address.country)) {
        addressDetail = addressDetail + address.country;
      }
    }
    const deliveryInfo = get(transaction, ['deliveryInformation', '0']);
    const trackingUrl = get(deliveryInfo, 'trackingUrl');
    const deliveryTime =
      Boolean(transaction.expectDeliveryDateTo) && transaction.expectDeliveryDateTo !== transaction.expectDeliveryDateFrom
        ? ` - ${moment(transaction.expectDeliveryDateTo).format('hh:mm A')}`
        : '';
    const momentExpectDeliveryDateFrom = moment(transaction.expectDeliveryDateFrom);
    return (
      <ScrollView>
        <View style={styles.onlineContent}>
          {!isEmpty(transaction.comment) && (
            <View>
              <Text style={styles.detailsTip}>{t('NOTES')}</Text>
              <View style={styles.detailContent}>
                <Text style={styles.detailText}>{transaction.comment.trim()}</Text>
              </View>
            </View>
          )}
          {Boolean(transaction.expectDeliveryDateFrom) && (
            <View>
              <Text style={styles.detailsTip}>{t('PRE-ORDER PICK UP DATE')}</Text>
              <View style={styles.detailContent}>
                <Text style={styles.detailText}>
                  {`${t('This is a Pre-Order to be fulfilled on')} ${momentExpectDeliveryDateFrom.format('Do')} ${
                    monthFullNames[momentExpectDeliveryDateFrom.month()]
                  }, ${momentExpectDeliveryDateFrom.format('hh:mm A')}${deliveryTime}`}
                </Text>
              </View>
            </View>
          )}
          {useStorehubLogistics &&
            (Boolean(get(deliveryInfo, 'courier')) ||
              Boolean(get(deliveryInfo, 'driverPhone')) ||
              Boolean(get(deliveryInfo, 'trackingUrl')) ||
              Boolean(get(deliveryInfo, 'trackingId'))) && (
              <View>
                <Text style={styles.detailsTip}>{t('LOGISTIC DETAILS')}</Text>
                <View style={styles.detailContent}>
                  <View style={{ flexDirection: 'row' }}>
                    <View style={{ width: '50%' }}>
                      <Text style={styles.detailName}>{t('RIDER')}</Text>
                      <Text style={styles.detailText}>{get(deliveryInfo, 'courier')}</Text>
                    </View>
                    <View style={{ width: '50%' }}>
                      <Text style={styles.detailName}>{t('PHONE NUMBER')}</Text>
                      <Text style={styles.detailText}>{get(deliveryInfo, 'driverPhone')}</Text>
                    </View>
                  </View>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      width: '100%',
                      marginTop: scaleSizeH(50),
                    }}
                  >
                    <View style={{ width: '50%' }}>
                      <Text style={styles.detailName}>{t('TRACKING URL')}</Text>
                      {Boolean(trackingUrl) ? (
                        <TouchableOpacity
                          {...testProps('al_btn_805')}
                          onPress={() => {
                            Linking.openURL(trackingUrl).catch(err => console.error('An error occurred', err));
                          }}
                        >
                          <Text
                            style={[
                              styles.detailText,
                              {
                                width: scaleSizeW(400),
                                textDecorationLine: 'underline',
                                color: 'blue',
                                marginRight: scaleSizeW(20),
                              },
                            ]}
                            ellipsizeMode='middle'
                            numberOfLines={1}
                          >
                            {get(deliveryInfo, 'trackingUrl')}
                          </Text>
                        </TouchableOpacity>
                      ) : (
                        <Text
                          style={[
                            styles.detailText,
                            {
                              color: '#9E9E9E',
                              marginRight: scaleSizeW(20),
                            },
                          ]}
                        >
                          {get(deliveryInfo, 'trackingId')}
                        </Text>
                      )}
                    </View>
                  </View>
                </View>
              </View>
            )}
          {isShipping && (isDelivery(transaction) || isPickUpOrTakeAway(transaction)) && !isFDIOrder(transaction.channel) && (
            <View>
              <Text style={styles.detailsTip}>{isDelivery(transaction) ? t('SHIPPING ADDRESS') : t('CONTACT DETAILS')}</Text>
              <View style={styles.detailContent}>
                <View style={{ flexDirection: 'row' }}>
                  <View style={{ width: '50%' }}>
                    <Text style={styles.detailName}>{t('MOBILE NUMBER')}</Text>
                    <Text style={styles.detailText}>{get(address, 'phone') || get(contact, 'phone')}</Text>
                  </View>
                  <View style={{ width: '50%' }}>
                    <Text style={styles.detailName}>{t('EMAIL')}</Text>
                    <Text style={styles.detailText}>{get(contact, 'email')}</Text>
                  </View>
                </View>
                <View style={{ flexDirection: 'row', width: '100%', marginTop: scaleSizeH(50) }}>
                  <View style={{ width: '100%' }}>
                    <Text style={styles.detailName}>{Boolean(addressDetail) ? t('ADDRESS') : t('CUSTOMER')}</Text>
                    <Text style={styles.detailText}>{addressDetail || get(contact, 'name')}</Text>
                  </View>
                </View>
              </View>
            </View>
          )}
          {transaction && transaction.deliveryInformation && transaction.deliveryInformation[0] && transaction.deliveryInformation[0].comments && (
            <View>
              <Text style={styles.detailsTip}>{t('RIDER NOTE')}</Text>
              <View style={styles.detailContent}>
                <Text style={styles.detailText}>{transaction.deliveryInformation[0].comments}</Text>
              </View>
            </View>
          )}
        </View>
      </ScrollView>
    );
  };

  UpdateOnlineOrderStatus = (nextStatus, courier, trackingId) => {
    const { businessName, currentEmployeeId } = this.props;
    const { currentOnlineTransaction } = this.state;
    // get loggly tag by currentTransaction
    const tags = [
      BeepFlowTag.Beep,
      getBeepOrderShippingTag(currentOnlineTransaction),
      getPreOrderTag(currentOnlineTransaction),
      getPayLaterOrPayFirstTag(currentOnlineTransaction),
    ];
    // the orderInfo be tracked in loggly
    const orderInfo = {
      orderId: currentOnlineTransaction.receiptNumber,
      status: currentOnlineTransaction.status,
      displayedStatus: getDisplayedStatus(currentOnlineTransaction),
    };

    logBeepEvent({
      action: BeepFlowAction.MerchantChangeStatus,
      tags,
      workflowStatus: WorkflowStatus.Start,
      orderInfo,
    });

    // update logOptions to currentTransaction,
    currentOnlineTransaction.logOptions = {
      tags,
      orderInfo,
      isManual: true,
    };
    this.setState({ currentOnlineTransaction });

    const onSuccess = {
      callback: () => {
        const needToPrint =
          (isDelivery(currentOnlineTransaction) && nextStatus === OnlineOrderStatus.Confirmed) ||
          (isPickUpOrTakeAway(currentOnlineTransaction) &&
            currentOnlineTransaction.status === OnlineOrderStatus.Accepted &&
            nextStatus === OnlineOrderStatus.Confirmed);

        logSucceedServerRequest(BeepFlowAction.UpdateOrderStatusToServerManually, tags, needToPrint ? null : WorkflowStatus.End, {
          toStatus: nextStatus,
          ...orderInfo,
        });
        if (needToPrint) {
          this.onPrintReceipt(true);
        }
        this.refreshOnlineOrders();
      },
    };

    const onFailure = {
      callback: payload => {
        this.props.actions.toggleToastInfo({
          visible: true,
          text: t('Change Status Failure'),
        });
        logFailedServerRequest(BeepFlowAction.UpdateOrderStatusToServerManually, tags, payload, WorkflowStatus.End, { toStatus: nextStatus, ...orderInfo });
      },
    };
    this.props.actions.updateOnlineOrderStatus({
      orderId: currentOnlineTransaction.receiptNumber,
      status: nextStatus,
      businessName,
      employeeId: currentEmployeeId,
      courier,
      trackingId,
      onSuccess,
      onFailure,
    });
  };

  getTheTransaction = (withRefund = true) => {
    const { currentTransaction, onlineOrderTabSelected, isRefunding, currentOnlineTransaction } = this.state;
    let transaction;
    if (isRefunding && withRefund) {
      transaction = this.props.transactionSession;
    } else if (!onlineOrderTabSelected && currentTransaction) {
      transaction = currentTransaction;
    } else if (onlineOrderTabSelected && currentOnlineTransaction) {
      transaction = currentOnlineTransaction;
    }
    return transaction;
  };

  onPrintReceipt = (isChangeStatus = false) => {
    const { currentEmployeeId } = this.props;
    const { onlineCustomer, registerCustomer, onlineOrderTabSelected } = this.state;
    const customer = onlineOrderTabSelected ? onlineCustomer : registerCustomer;
    const _customer = Object.assign({}, customer);
    const transaction = this.getTheTransaction();
    this.props.actions.requestAuthorizedAction({
      employeeId: currentEmployeeId,
      name: CashierActions.RePrint,
      type: AuthorizationType.Cashier,
      onSuccess: {
        callback: () => {
          this.hideMenu();
          const transactionId = get(transaction, 'transactionId');
          const receiptNumber = get(transaction, 'receiptNumber');
          if (transactionId || receiptNumber) {
            if (Object.keys(_customer).length > 0) {
              _customer.isStoreCredit = true;
              _customer.loyaltyEarned = getNumberValue(transaction, 'loyaltyEarned', 0.0);
              _customer.loyaltyBalance = getNumberValue(_customer, 'loyalty', 0.0);
              if (!onlineOrderTabSelected) {
                // fix CM-4602 for offline orders
                _customer.loyaltySpent = getLoyaltySpent(transaction);
              }
            }

            let onlineTransaction = null;
            if (transaction.isRemote) {
              if (Boolean(transaction.logOptions)) {
                transaction.logOptions.isNeedLog = isChangeStatus;
                transaction.logOptions.isManual = true;
                transaction.logOptions.tags = concat(transaction.logOptions.tags || [], 'reprintFromTransactions');
                this.setState({ currentOnlineTransaction: transaction });
              }
              onlineTransaction = transaction;
              if (isChangeStatus === true) {
                this.printKitchenOnUpdateStatus().then(() => {
                  this.props.actions.newPrintReceiptAction({
                    transactionId: transactionId || receiptNumber,
                    onlineTransaction,
                    customer: _customer,
                    isReprint: true,
                    eventName: 'printReceiptOnUpdateStatus',
                  });
                });
                return;
              }
            }

            this.props.actions.newPrintReceiptAction({
              transactionId: transactionId || receiptNumber,
              onlineTransaction,
              customer: _customer,
              isReprint: true,
              eventName: 'printOnTransactionPage',
            });
          }
        },
      },
      onFailure: {
        callback: () => {
          this.hideMenu();
        },
      },
    });
  };

  onPrintEInvoiceQr = () => {
    const { currentEmployeeId } = this.props;
    const { onlineCustomer, registerCustomer, onlineOrderTabSelected } = this.state;
    const customer = onlineOrderTabSelected ? onlineCustomer : registerCustomer;
    const _customer = Object.assign({}, customer);
    const transaction = this.getTheTransaction();
    this.props.actions.requestAuthorizedAction({
      employeeId: currentEmployeeId,
      name: CashierActions.RePrint,
      type: AuthorizationType.Cashier,
      onSuccess: {
        callback: () => {
          this.hideMenu();
          this.props.actions.printEInvoiceQrReceipt({
            transaction,
          });
          this.props.actions.employeePrintEInvoiceQrReceipt({
            transactionId: transaction.transactionId,
          });
        },
      },
      onFailure: {
        callback: () => {
          this.hideMenu();
        },
      },
    });
  };

  clickOnResendToKitchen = () => {
    this.hideMenu();
    const { printerTagsSettings } = this.props;
    if (hasNoReceiptPrinter(printerTagsSettings)) {
      this.props.navigation.navigate('ModalInfo', { info: t('NoPrinterError') });
      return;
    }
    const transaction = this.getTheTransaction(false);
    const { onlineOrderTabSelected } = this.state;

    if (isEmpty(transaction)) return;
    const receiptNumber = transaction.receiptNumber;
    const transactionId = transaction.transactionId;
    // Checking status
    const onComplete = {
      callback: ({ errorMsg, errorTitle }) => {
        if (!isEmpty(errorMsg)) {
          this.showErrorModel(errorMsg, errorTitle);
        } else {
          if (onlineOrderTabSelected) {
            transaction.isOnlineOrder = true;
          }
          this.props.actions.printKitchenDocket({ transaction, eventName: KitchenEvent.manualResendToKitchen });
        }
      },
    };
    this.props.actions.checkTrxRefundStatus({
      receiptNumber,
      onComplete,
      transactionId,
    });
  };

  printKitchenOnUpdateStatus = () => {
    return new Promise(resolve => {
      this.hideMenu();
      const { printerTagsSettings } = this.props;
      const transaction = this.getTheTransaction(false);
      if (hasNoReceiptPrinter(printerTagsSettings)) {
        this.hideMenu();
        this.props.navigation.navigate('ModalInfo', { info: t('NoPrinterError') });
        resolve(false);
      }

      const { onlineOrderTabSelected } = this.state;

      if (transaction === null || transaction === undefined) resolve(false);
      const receiptNumber = transaction.receiptNumber;
      const transactionId = transaction.transactionId;
      // Checking status
      const onComplete = {
        callback: ({ errorMsg, errorTitle }) => {
          if (!isEmpty(errorMsg)) {
            this.showErrorModel(errorMsg, errorTitle);
            resolve(false);
          } else {
            // sendToKitchen
            if (onlineOrderTabSelected) {
              transaction.isOnlineOrder = true;
            }
            const onKitchenPrintComplete = (result: PrintReturnResult) => {
              resolve(result.status);
            };
            this.props.actions.printKitchenDocket({
              transaction,
              eventName: KitchenEvent.printOnUpdateOnlineStatus,
              onComplete: onKitchenPrintComplete,
            });
          }
        },
      };
      this.props.actions.checkTrxRefundStatus({
        receiptNumber,
        onComplete,
        transactionId,
      });
    });
  };

  onSendEmail = email => {
    showMessage({ message: `${t('Receipt sent to')} ${email}` });
  };

  onStartSendEmail = () => {
    this.hideMenu();
    const transaction = this.getTheTransaction(false);
    const transactionId = transaction != null ? transaction.transactionId : null;
    if (transactionId) {
      const customerId = get(transaction, 'customerId');
      if (customerId) {
        const onSuccess: HttpActionCallback<HttpActionSuccessPayload> = {
          callback: payload => {
            const customerEmail = getUnNullValue(payload, 'res.email', '');
            this.props.navigation.navigate('EmailReceiptModal', {
              transactionId,
              customerEmail,
              onSendEmail: this.onSendEmail,
            });
          },
        };
        const onFailure: HttpActionCallback<HttpActionFailurePayload> = {
          callback: () => {
            this.props.navigation.navigate('EmailReceiptModal', { transactionId, onSendEmail: this.onSendEmail });
          },
        };
        this.props.actions.getCustomerById({ customerId, bn: this.props.businessName, onSuccess, onFailure });
      } else {
        this.props.navigation.navigate('EmailReceiptModal', { transactionId, onSendEmail: this.onSendEmail });
      }
    }
  };

  onEmailReceipt = () => {
    const { currentEmployeeId } = this.props;
    this.props.actions.requestAuthorizedAction({
      employeeId: currentEmployeeId,
      name: CashierActions.ReSend,
      type: AuthorizationType.Cashier,
      onSuccess: {
        callback: () => {
          this.onStartSendEmail();
        },
      },
      onFailure: {
        callback: () => {
          this.hideMenu();
        },
      },
    });
  };

  getShouldRenderTakeAwayNotificationButton = transaction => {
    const { enableSelfPickupAlert } = this.props;
    const { userId, status } = transaction || {};
    const today = moment();
    const createdTime = moment(transaction.createdTime);
    const isToday = today.isSame(createdTime, 'day');
    return Boolean(userId) && (isDineIn(transaction) || isTakeAway(transaction)) && enableSelfPickupAlert && isToday && status !== OnlineOrderStatus.Cancelled;
  };

  renderTrxActionButtons = () => {
    const { isRefunding, onlineOrderTabSelected, nextStatusList, currentOnlineTransaction, currentTransaction } = this.state;
    const { lastCloseZReadingTime, operationHours, eInvoiceEnabled, eInvoicePhase2Enabled, eInvoiceBeepWebstoreEnabled } = this.props;
    if (onlineOrderTabSelected && currentOnlineTransaction) {
      const { receiptNumber } = currentOnlineTransaction;
      const { currentEmployeeId } = this.props;
      // beep dine-in && login
      const shouldRenderTakeAwayNotificationButton = this.getShouldRenderTakeAwayNotificationButton(currentOnlineTransaction);
      const _nextstatusList = [];
      if (Boolean(nextStatusList) && currentOnlineTransaction.nextStatusTransitions !== null) {
        map(nextStatusList, item => {
          map(currentOnlineTransaction.nextStatusTransitions, status => {
            if (status === item.status) {
              _nextstatusList.push(item);
            }
          });
        });
      }
      if (Boolean(_nextstatusList) || shouldRenderTakeAwayNotificationButton) {
        return (
          <View style={styles.actionButtonContainer}>
            {shouldRenderTakeAwayNotificationButton && <NotificationButton receiptNumber={receiptNumber} employeeId={currentEmployeeId} />}
            {_nextstatusList.length > 0 &&
              map(_nextstatusList, (item, index) => {
                let name = '';
                switch (item.status) {
                  case OnlineOrderStatus.Accepted:
                    name = t('ACCEPT ORDER');
                    break;
                  case OnlineOrderStatus.Confirmed:
                    name = t('SEND TO KITCHEN');
                    break;
                  case OnlineOrderStatus.Cancelled:
                    break;
                  default:
                    name = item.name.toUpperCase();
                    break;
                }
                const shouldBackgroundBeOrange = index === 1 || _nextstatusList.length === 1;
                return (
                  !isEmpty(name) && (
                    <TouchableOpacity
                      {...testProps('al_btn_919')}
                      key={item.status}
                      style={{
                        ...StyleSheet.flatten(styles.solidButton),
                        marginHorizontal: scaleSizeW(10),
                        backgroundColor: shouldBackgroundBeOrange ? currentThemes.buttonBackgroundColor : 'white',
                        borderWidth: shouldBackgroundBeOrange ? 0 : 1,
                        borderColor: '#E0E0E4',
                      }}
                      onPress={() => {
                        if (OnlineTransactionsStatusType.Shipped === item.name) {
                          // currentStatus = item.status;
                        } else {
                          this.UpdateOnlineOrderStatus(item.status, null, null);
                        }
                      }}
                    >
                      <Text
                        style={{
                          fontSize: currentThemes.fontSize24,
                          fontWeight: 'bold',
                          color: shouldBackgroundBeOrange ? 'white' : '#393939',
                        }}
                      >
                        {name}
                      </Text>
                    </TouchableOpacity>
                  )
                );
              })}
          </View>
        );
      } else {
        return null;
      }
    } else {
      let isPreOrder = false;
      if (Boolean(currentTransaction)) {
        isPreOrder = currentTransaction.transactionType === TransactionFlowType.PreOrder;
      }
      const cancelable = this.isTransactionCancelable();
      const refundable = this.isTransactionRefundable();
      const alreadyCloseZReading = getAlreadyCloseZReading(lastCloseZReadingTime, operationHours);
      return isPreOrder ? null : (
        <View style={styles.actionButtonContainer}>
          <TouchableOpacity
            {...testProps('al_btn_140')}
            style={{
              ...StyleSheet.flatten(styles.solidButton),
              backgroundColor: refundable || isRefunding ? 'white' : '#E0E0E4',
              borderWidth: 1,
              borderColor: '#E0E0E4',
            }}
            disabled={(!refundable && !isRefunding) || alreadyCloseZReading}
            onPress={isRefunding ? this.cancelRefundClicked : this.refundTrxBtnClicked}
          >
            <Text
              style={{
                fontSize: currentThemes.fontSize24,
                fontWeight: 'bold',
                color: refundable || isRefunding ? '#303030' : 'white',
              }}
            >
              {isRefunding ? t('CANCEL') : t('ISSUE REFUND')}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={{
              ...StyleSheet.flatten(styles.solidButton),
              backgroundColor: cancelable || isRefunding ? '#FC7118' : '#E0E0E4',
              borderWidth: cancelable || isRefunding ? 0 : 1,
              borderColor: cancelable || isRefunding ? '' : '#E0E0E4',
            }}
            disabled={(!cancelable && !isRefunding) || alreadyCloseZReading}
            onPress={isRefunding ? this.refundNextPressed : this.cancelTrxBtnClicked}
            {...testProps('al_cancel_transition')}
          >
            <Text
              style={{
                fontSize: currentThemes.fontSize24,
                fontWeight: 'bold',
                color: 'white',
              }}
            >
              {isRefunding ? t('NEXT') : t('CANCEL TRANSACTION')}
            </Text>
          </TouchableOpacity>
        </View>
      );
    }
  };

  onRefundComplete = transactionId => {
    // after refund success, just load the new refund transaction and insert to the list
    const { onlineOrderTabSelected } = this.state;
    if (isEmpty(transactionId)) {
      this.setState({ isRefunding: false }, this.onSubmitSearch);
      return;
    }
    const refundtrx = DAL.getTransactionById(transactionId);
    if (refundtrx && !onlineOrderTabSelected) {
      if (!isEmpty(this._registerSearchText)) {
        const { registerSearchResult } = this.state;
        let newRegisterSearchResult = registerSearchResult;
        if (newRegisterSearchResult && newRegisterSearchResult.length > 0) {
          const refundtrxTitle = moment(refundtrx.createdDate).format(DATE_TITLE_FORMAT);
          if (isEqual(refundtrxTitle, newRegisterSearchResult[0].sectionTitle)) {
            newRegisterSearchResult = concat(newRegisterSearchResult[0], newRegisterSearchResult);
            newRegisterSearchResult[1] = refundtrx;
          } else {
            newRegisterSearchResult = concat(this.generateOfflineSearchedResult([refundtrx]), newRegisterSearchResult);
          }
        } else {
          newRegisterSearchResult = this.generateOfflineSearchedResult([refundtrx]);
        }
        const newSate: any = { registerSearchResult: newRegisterSearchResult, isRefunding: false };
        newSate.currentTransaction = refundtrx;
        newSate.curOfflineTrxIndex = 1;
        newSate.isRefunding = false;
        this.setState(newSate);
      } else {
        this.refreshData();
      }
    } else {
      this.setState({ isRefunding: false }, this.onSubmitSearch);
    }
  };

  refundNextPressed = () => {
    const onComplete = {
      callBack: ({ isValidate, errorMsg }) => {
        if (isValidate) {
          this.props.navigation.navigate('Checkout', { refreshData: this.onRefundComplete, isReturn: true });
        } else {
          this.showErrorMsg(errorMsg);
        }
      },
    };
    this.props.actions.checkRefundTransactionValidity({ onComplete });
  };

  openDrawer = () => this.props.navigation.dispatch(DrawerActions.openDrawer());

  renderTopMainBar = transaction => {
    let employeeName = '';
    let time = '';

    if (this.getTheTransaction(false)) {
      const employee = DAL.getEmployeeById(transaction.employeeId);
      if (employee) {
        employeeName = employee.firstName + ' ' + employee.lastName;
      }
      const createTimeMoment = moment(transaction.createdDate || transaction.createdTime);

      time = `${createTimeMoment.format('dddd, D')} ${monthFullNames[createTimeMoment.month()]} ${createTimeMoment.format('YYYY - h:mm A')}`;
    }
    return (
      <TopMainBar
        isLeftContainerTitle
        leftIcon={<MenuWithNotification color={CommonColors.Icon} />}
        leftText={t('Transactions')}
        rightIsIcon={true}
        rightButtonText={''}
        isRightButtonShow={true}
        onLeftClick={this.openDrawer}
        rightText={employeeName}
        rightTitle={time || t('No Transaction')}
        rightIcon={Boolean(transaction) && <IconMore color={CommonColors.Icon} />}
        onRightClick={this.showMenu}
        rightBtnDisabled={!Boolean(transaction)}
      />
    );
  };

  setMenuRef = ref => {
    this._moreDialog = ref;
  };

  hideMenu = () => {
    this._moreDialog.dismiss();
  };

  showMenu = () => {
    this._moreDialog.show();
  };

  renderMoreAction = () => {
    const { isRefunding, onlineOrderTabSelected } = this.state;
    const transaction = this.getTheTransaction();
    const transactionType = get(transaction, 'transactionType', '');
    const isPreOrder = transactionType === TransactionFlowType.PreOrder;
    const canCancelTransaction = !isPreOrder && !isRefunding && this.isTransactionCancelable();
    const enableEInvoice = this.props.eInvoiceEnabled;
    let moreActionList: MenuItemType[] = [
      {
        name: t('Print Receipt'),
        icon: (
          <View style={styles.moreItemIconMargin}>
            <IconReceiptThin width={scaleSizeW(40)} height={scaleSizeH(40)} color={CommonColors.Icon} />
          </View>
        ),
        onClick: this.onPrintReceipt,
      },
    ];
    if (enableEInvoice && this.isTransactionEInvoicePrintable()) {
      moreActionList.push({
        name: !isRefunding && Boolean(transaction?.originalReceiptNumber) ? 'Print Credit Note QR Code' : 'Print e-Invoice QR Code',
        icon: (
          <View style={styles.moreItemIconMargin}>
            <IconQrThin width={scaleSizeW(40)} height={scaleSizeH(40)} color={CommonColors.Icon} />
          </View>
        ),
        onClick: this.onPrintEInvoiceQr,
      });
    }
    if (!onlineOrderTabSelected) {
      moreActionList.push({
        name: t('Email Receipt'),
        icon: (
          <View style={styles.moreItemIconMargin}>
            <IconEmail width={scaleSizeW(40)} height={scaleSizeH(40)} color={CommonColors.Icon} />
          </View>
        ),
        onClick: this.onEmailReceipt,
      });
    }

    if (transactionType !== TransactionFlowType.Return) {
      const resendToKitchenButton: MenuItemType[] = [
        {
          name: t('Send To Kitchen'),
          icon: (
            <View style={styles.moreItemIconMargin}>
              <IconChefHeadThin width={scaleSizeW(40)} height={scaleSizeH(40)} color={CommonColors.Icon} />
            </View>
          ),
          onClick: this.clickOnResendToKitchen,
        },
      ];
      moreActionList = resendToKitchenButton.concat(moreActionList);
    }
    if (canCancelTransaction) {
      moreActionList.push({ isDivider: true });
      moreActionList.push({
        name: t('Cancel Transaction'),
        icon: (
          <View style={styles.moreItemIconMargin}>
            <IconCancel />
          </View>
        ),
        onClick: this.cancelTrxBtnClicked,
      });
    }
    return <ModalMoreAction ref={this.setMenuRef} list={moreActionList} />;
  };

  renderContent = () => {
    const transaction = this.getTheTransaction();
    return (
      <View style={styles.container}>
        {this.renderTopMainBar(transaction)}
        {this.renderMoreAction()}
        <View style={styles.subContainer}>
          {this.renderTransactionList()}
          {Boolean(transaction) ? this.renderTransactionDetail(transaction) : this.renderContentEmpty()}
        </View>
      </View>
    );
  };

  render() {
    const { dataList } = this.state;
    const { isOnline, isQROrderingEnabled } = this.props;
    const showOnline = isOnline || isQROrderingEnabled;
    return (
      <>
        {dataList.length > 0 || showOnline ? this.renderContent() : this.renderEmpty()}
        <MRSListener callback={this.refreshData} />
      </>
    );
  }
}

export default connector(Transactions);

const ITEM_HEIGHT = scaleSizeH(104);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: currentThemes.bgMainColor,
  },
  subContainer: {
    flex: 1,
    flexDirection: 'row',
  },
  searchInputContainer: {
    flex: 1,
    height: scaleSizeH(60),
    paddingHorizontal: scaleSizeW(20),
    flexDirection: 'row',
    alignItems: 'center',
  },
  inputText: {
    paddingVertical: 0,
    fontSize: currentThemes.fontSize26,
    color: '#303030',
    letterSpacing: 1,
  },
  tabBarTextStyle: {
    fontSize: currentThemes.fontSize24,
    fontWeight: '500',
  },
  sectionHeader: {
    backgroundColor: '#ECEEF4',
    height: scaleSizeH(40),
    justifyContent: 'center',
    paddingLeft: scaleSizeW(24),
  },
  actionButtonContainer: {
    flexDirection: 'row',
    paddingHorizontal: scaleSizeW(40),
    paddingBottom: scaleSizeH(26),
  },
  clearButton: {
    width: scaleSizeW(40),
    height: scaleSizeH(40),
    backgroundColor: 'transparent',
    padding: scaleSizeW(2),
  },
  solidButton: {
    flex: 1,
    marginHorizontal: scaleSizeW(16),
    backgroundColor: '#FC7118',
    height: scaleSizeH(80),
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchBarContainer: {
    width: '100%',
    flexDirection: 'row',
    paddingHorizontal: scaleSizeW(16),
    height: scaleSizeH(100),
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomColor: '#E0E0E4',
    borderBottomWidth: 1,
  },
  detailName: {
    fontSize: currentThemes.fontSize18,
    fontWeight: '500',
    color: '#959595',
  },
  detailsTip: {
    color: '#303030',
    fontSize: currentThemes.fontSize18,
    textAlign: 'left',
    marginLeft: scaleSizeW(8),
    marginTop: scaleSizeH(36),
    fontWeight: '500',
  },
  detailContent: {
    paddingHorizontal: scaleSizeW(32),
    paddingVertical: scaleSizeH(20),
    width: '100%',
    backgroundColor: 'white',
    marginTop: scaleSizeH(15),
  },
  detailText: {
    color: '#303030',
    fontSize: currentThemes.fontSize24,
    marginTop: scaleSizeH(8),
  },
  tabBarUnderLineStyle: {
    backgroundColor: '#FC7118',
    height: scaleSizeH(8),
  },
  onlineContent: {
    flex: 1,
    flexDirection: 'column',
    paddingBottom: scaleSizeH(24),
    paddingTop: scaleSizeH(8),
  },
  textWarn: {
    fontSize: currentThemes.fontSize32,
    marginTop: scaleSizeH(35),
    color: '#60636B',
    fontWeight: '500',
    lineHeight: scaleSizeH(52),
  },
  textDesc: {
    fontSize: currentThemes.fontSize24,
    marginTop: scaleSizeH(15),
    color: '#757575',
    fontWeight: '400',
    lineHeight: scaleSizeH(38),
  },
  textDesc2: {
    fontSize: currentThemes.fontSize24,
    marginTop: scaleSizeH(15),
    color: '#757575',
    fontWeight: '400',
    lineHeight: scaleSizeH(38),
    maxWidth: scaleSizeW(900),
    textAlign: 'center',
  },
  backToRegisterBtnContainer: {
    backgroundColor: '#FC7118',
    width: scaleSizeW(840),
    height: scaleSizeH(112),
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 0,
    marginTop: scaleSizeH(189),
  },
  backToRegisterBtnText: {
    fontSize: currentThemes.fontSize30,
    color: 'white',
    fontWeight: 'bold',
  },
  moreItemIconMargin: {
    marginRight: scaleSizeW(16),
  },
  filterConatiner: {
    height: scaleSizeW(48),
    width: scaleSizeW(70),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  filterDividerLine: {
    backgroundColor: '#E0E0E4',
    width: 1,
    height: scaleSizeW(40),
  },
  filterButton: {
    height: scaleSizeW(48),
    width: scaleSizeW(48),
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyTopBar: {
    width: width,
    height: NAVIGATOR_HEADER_HEIGHT,
    paddingTop: STATUS_BAR_HEIGHT,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFF',
  },
  emptyTopBarLeftIcon: {
    position: 'absolute',
    left: scaleSizeW(30),
  },
  emptyTopBarTitle: {
    color: '#303030',
    fontSize: currentThemes.fontSize24,
    fontWeight: '500',
  },
  emptyTopBarSubContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyContentConatiner: {
    flex: 1,
    alignItems: 'center',
    paddingTop: scaleSizeH(187),
  },
  button: {
    backgroundColor: '#FC7118',
    padding: 10,
    marginTop: 20,
  },
  buttonText: {
    color: 'white',
    fontSize: currentThemes.fontSize24,
    fontWeight: '500',
  },
});
