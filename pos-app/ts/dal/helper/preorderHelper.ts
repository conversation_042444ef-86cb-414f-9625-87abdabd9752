import { STOREHUB_APP_VERSION } from '../../config';
import PaymentOptions, { DefaultPaymentOptionType } from '../../config/paymentOption';
import { OrderChannel, SalesChannelType, TransactionFlowType } from '../../constants';
import { isValidNumber } from '../../utils';

import { getIOSStringFromDate } from '../../utils/datetime';
import * as JSONUtils from '../../utils/json';
import { isEmpty } from '../../utils/validator';

export default class PreorderHelper {
  static serializeTransaction(realmRecord, registerId) {
    if (!realmRecord) {
      return null;
    }
    const now = new Date();
    const jsonObj: any = {
      transactionId: realmRecord.transactionId,
      isCollectPreorder: true,
      appVersion: STOREHUB_APP_VERSION,
      transactionType: TransactionFlowType.Sale,
      channel: OrderChannel.OrderChannelOffline,
      registerId: registerId,
      isOriginalOnline: realmRecord.isOriginalOnline,
      total: realmRecord.total,
      subtotal: realmRecord.subtotal,
      discount: realmRecord.discount,
      tax: realmRecord.tax,
      lastRegisterId: realmRecord.registerId,
      createdDate: realmRecord.createdDate,
      isDeleted: realmRecord.isDeleted || false,
      isCompleted: realmRecord.isCompleted || false,
      modifiedTime: now.toISOString(), // Only for uploading transaction
      modifiedDate: now.toISOString(),
      isCancelled: realmRecord.isCancelled,
      roundedAmount: realmRecord.roundedAmount,
      serviceChargeTaxId: realmRecord.serviceChargeTaxId,
      serviceCharge: realmRecord.serviceCharge,
      serviceChargeTax: realmRecord.serviceChargeTax,
      serviceChargeRate: realmRecord.serviceChargeRate,
      shippingType: realmRecord.shippingType,
      isOpen: realmRecord.isOpen,
      tableId: realmRecord.tableId,
      takeawayId: realmRecord.takeawayId,
      pickUpId: realmRecord.pickUpId,
      totalPaid: realmRecord.depositAmount,
      headcount: realmRecord.headcount,
      pwdCount: realmRecord.pwdCount,
      seniorsCount: realmRecord.seniorsCount,
      isOnlineOrder: realmRecord.isOnlineOrder,
      isPayByCash: realmRecord.isPayByCash,
      isPayLater: realmRecord.isPayLater,
      mrs: realmRecord.mrs,
      depositAmount: realmRecord.depositAmount,
      pwdDiscount: realmRecord.pwdDiscount || 0,
      seniorDiscount: realmRecord.seniorDiscount || 0,
      taxableSales: realmRecord.taxableSales || 0, // ？？？
      taxExemptedSales: realmRecord.taxExemptedSales || 0, // ？？？
      zeroRatedSales: realmRecord.zeroRatedSales || 0, // ？？？
      totalDeductedTax: realmRecord.totalDeductedTax || 0, // ？？？
      takeawayCharges: realmRecord.takeawayCharges || 0, // ？？？
      takeawayCharge: realmRecord.takeawayCharge || 0, // ？？？
      loyaltyEarned: realmRecord.loyaltyEarned, // ？？？
      salesChannel: realmRecord.salesChannel || SalesChannelType.DEFAULT,
      preOrderId: realmRecord.preOrderId,
      amusementTax: realmRecord.amusementTax || 0,
      display: {
        subtotal: realmRecord.subtotal,
        discount: realmRecord.discount,
        serviceCharge: realmRecord.serviceCharge,
        tax: realmRecord.tax,
        total: realmRecord.total,
      },
    };

    // employeeId
    // lastSyncTime
    // manuallyRemovedServiceCharge
    // shiftId
    // shiftIdOfPreOrder
    // shiftIdOfCancel
    // receiptNumber
    // registerId
    // returnReason
    // returnStatus
    // sequenceNumber
    // invoiceSeqNumber
    // uploadedDate
    // payments
    // mrs
    // otherReason

    // Selective Fileds
    if (realmRecord.salesChannel != null) {
      jsonObj.salesChannel = realmRecord.salesChannel;
    }
    if (realmRecord.enableCashback != null) {
      jsonObj.enableCashback = realmRecord.enableCashback;
    }
    if (realmRecord.comment != null) {
      jsonObj.comment = realmRecord.comment;
    }
    if (realmRecord.pax != null) {
      jsonObj.pax = realmRecord.pax;
    }
    if (realmRecord.customerId != null) {
      jsonObj.customerId = realmRecord.customerId;
    }
    if (realmRecord.shippingFee != null) {
      jsonObj.shippingFee = realmRecord.shippingFee;
    }
    if (realmRecord.shippingFeeDiscount != null) {
      jsonObj.shippingFeeDiscount = realmRecord.shippingFeeDiscount;
    }
    if (realmRecord.pickUpDate != null) {
      jsonObj.pickUpDate = getIOSStringFromDate(realmRecord.pickUpDate);
    }
    if (realmRecord.pickUpId != null) {
      jsonObj.pickUpId = realmRecord.pickUpId;
    }
    if (realmRecord.isCancelled) {
      jsonObj.cancelledAt = getIOSStringFromDate(realmRecord.cancelledAt);
      jsonObj.cancelledBy = realmRecord.cancelledBy;
    }
    if (realmRecord.preOrderBy != null) {
      jsonObj.preOrderBy = realmRecord.preOrderBy;
    }
    if (realmRecord.preOrderDate != null) {
      jsonObj.preOrderDate = realmRecord.preOrderDate;
    }
    if (realmRecord.addonBirCompliance != null && realmRecord.addonBirCompliance.discountType !== 'SC/PWD') {
      jsonObj.addonBirCompliance = PreorderHelper.serializeAddonBirCompliance(realmRecord.addonBirCompliance);
    }

    if (Boolean(realmRecord.promotions) && realmRecord.promotions.length > 0) {
      jsonObj.promotions = PreorderHelper.serializePromotions(realmRecord.promotions);
    }
    const loyaltyDiscounts = PreorderHelper.serializeLoyaltyDiscounts(realmRecord.loyaltyDiscounts);
    if (loyaltyDiscounts.length > 0) {
      jsonObj.loyaltyDiscounts = loyaltyDiscounts;
    }
    // Purchased Item parse
    jsonObj.items = PreorderHelper.serializeItems(realmRecord.items);
    jsonObj.payments = PreorderHelper.serializePayments(realmRecord.payments);

    // calcualtion
    if (realmRecord.calculation) {
      jsonObj.calculation = PreorderHelper.serializeCalculation(realmRecord.calculation);
    }

    return jsonObj;
  }

  static serializeItems(items) {
    const result = [];
    if (Boolean(items) && items.length > 0) {
      for (const itemObj of items) {
        const item: any = itemObj;
        const itemJson: any = {
          id: item.id,
          _id: item._id,
          productId: item.productId,
          quantity: item.quantity,
          subTotal: item.subTotal,
          total: item.total,
          discount: item.discount,
          adhocDiscount: item.adhocDiscount,
          unitPrice: item.unitPrice,
          tax: item.tax,
          taxRate: item.taxRate,
          originalQuantity: item.quantity,
          isTakeaway: item.isTakeaway,
          isVatExempted: item.isVatExempted || false,
          isBasicNecessitiesPH: item.isBasicNecessitiesPH || false,
          isSoloParentDiscountApplicable: item.isSoloParentDiscountApplicable || false,
          loyaltyDiscount: item.loyaltyDiscount || 0,
          // discountInputValue: item.discountInputValue || 0,
          rate: item.rate || 0,
          orderingValue: item.orderingValue,
          options: item.selectedOptions || (item.options ? JSONUtils.parse(item.options, []) : []),
          title: item.title,
          taxCode: item.taxCode || '',
          taxableAmount: item.taxableAmount || 0,
          taxExemptAmount: item.taxExemptAmount || 0,
          zeroRatedSales: item.zeroRatedSales || 0,
          totalDeductedTax: item.totalDeductedTax || 0,
          seniorDiscount: item.seniorDiscount || 0,
          pwdDiscount: item.pwdDiscount || 0,
          athleteAndCoachDiscount: item.athleteAndCoachDiscount || 0,
          soloParentDiscount: item.soloParentDiscount || 0,
          medalOfValorDiscount: item.medalOfValorDiscount || 0,
          isServiceChargeNotApplicable: item.isServiceChargeNotApplicable || false,
          // TODO: display
          display: {
            total: item.total,
            subtotal: item.subTotal,
            tax: item.tax,
          },
        };

        if (item.originalItemId) {
          itemJson.originalItemId = item.originalItemId;
        }

        if (item.itemChannel) {
          itemJson.itemChannel = item.itemChannel;
        }

        if (item.takeawayCharges) {
          itemJson.takeawayCharges = item.takeawayCharges;
        }

        if (item.takeawayCharge) {
          itemJson.takeawayCharge = item.takeawayCharge;
        }

        if (item.itemType != null && item.itemType.length > 0) {
          itemJson.itemType = item.itemType;
        }
        if (item.notes != null && item.notes.length > 0) {
          itemJson.notes = item.notes;
        }
        if (item.comments != null && item.comments.length > 0) {
          itemJson.comments = item.comments;
        }
        if (item.sn != null && item.sn.length > 0) {
          itemJson.sn = item.sn;
        }
        if (item.promotions != null && item.promotions.length > 0) {
          itemJson.promotions = PreorderHelper.serializePromotions(item.promotions);
        }
        if (item.calculation) {
          itemJson.calculation = PreorderHelper.serializeCalculation(item.calculation);
        }

        result.push(itemJson);
      }
    }
    return result;
  }

  static serializePayments(payments) {
    const result = [];

    if (Boolean(payments) && payments.length > 0) {
      for (const itemObj of payments) {
        const item: any = itemObj;
        const { type, paymentMethod } = item;

        let paymentMethodId = item.paymentMethodId;
        // 兼容IOS Native的preorder/ remote preorder
        if (isEmpty(paymentMethodId) && !isEmpty(paymentMethod)) {
          if (isValidNumber(paymentMethod)) {
            paymentMethodId = paymentMethod;
          } else {
            switch (paymentMethod) {
              case 'Cash':
                paymentMethodId = DefaultPaymentOptionType.Cash;
                break;
              case 'CreditCard':
                paymentMethodId = DefaultPaymentOptionType.CreditCard;
                break;
              case 'DebitCard':
                paymentMethodId = DefaultPaymentOptionType.DebitCard;
                break;

              default:
                break;
            }
          }
        }
        const finalPaymentMethod = isValidNumber(paymentMethodId)
          ? PaymentOptions.getDownloadPaymentMethod(Number(paymentMethodId))
          : Boolean(type)
            ? type
            : paymentMethod;
        const finalPaymentMethodType = Boolean(type)
          ? type
          : isValidNumber(paymentMethodId)
            ? PaymentOptions.getPaymentTypeById(Number(paymentMethodId))
            : paymentMethod;
        result.push({
          amount: item.amount,
          paymentMethodId: Number(paymentMethodId),
          paymentMethod: finalPaymentMethod, // Only for uploading transaction.
          cashTendered: item.cashTendered,
          roundedAmount: item.roundedAmount,
          isDeposit: item.isDeposit,
          subType: item.subType,
          type: finalPaymentMethodType,
          mPOSTxnId: item.mPOSTxnId,
          isOnline: item.isOnline,
          manualApproveInfo: Boolean(item.manualApproveInfo) ? JSON.parse(item.manualApproveInfo) : undefined,
        });
      }
    }
    return result;
  }

  static serializeLoyaltyDiscounts(loyaltyDiscounts) {
    const result = [];
    if (Boolean(loyaltyDiscounts) && loyaltyDiscounts.length > 0) {
      for (const loyaltyDiscount of loyaltyDiscounts) {
        const loyaltyItem: any = loyaltyDiscount;
        const loyaltyJson: any = {
          displayDiscount: loyaltyItem.displayDiscount,
          type: loyaltyItem.type,
          loyaltyType: loyaltyItem.type,
          inputValue: loyaltyItem.inputValue || 0,
          spentValue: loyaltyItem.spentValue,
        };
        loyaltyJson.display = {
          discount: loyaltyItem.displayDiscount,
          discountedTax: loyaltyItem.spentValue - loyaltyItem.displayDiscount,
        };
        result.push(loyaltyJson);
      }
    }
    return result;
  }

  static serializeAddonBirCompliance(addonBirCompliance) {
    const result: any = {};
    if (addonBirCompliance.discountType !== null) {
      result.discountType = addonBirCompliance.discountType;
    }
    result.athleteAndCoachDiscount = addonBirCompliance.athleteAndCoachDiscount;
    result.medalOfValorDiscount = addonBirCompliance.medalOfValorDiscount;
    result.soloParentDiscount = addonBirCompliance.soloParentDiscount;
    if (addonBirCompliance.collectedInfo !== null) {
      result.collectedInfo = addonBirCompliance.collectedInfo; // string
    }
    return result;
  }

  static serializePromotions(realmPromotions) {
    if (!realmPromotions || realmPromotions.length === 0) {
      return [];
    }

    const result = [];
    for (const promotionObj of realmPromotions) {
      const jsonObj: any = { promotionId: promotionObj.promotionId };
      if (promotionObj.discount != null) {
        jsonObj.discount = promotionObj.discount;
      } else {
        jsonObj.discount = 0; // TODO: for combo promotion, the calculator-lib misses the discount.
      }

      jsonObj.display = { discount: jsonObj.discount };

      if (promotionObj.pwdDiscount != null) {
        jsonObj.pwdDiscount = promotionObj.pwdDiscount;
      }

      if (promotionObj.quantity != null) {
        jsonObj.quantity = promotionObj.quantity;
      }

      if (promotionObj.seniorDiscount != null) {
        jsonObj.seniorDiscount = promotionObj.seniorDiscount;
      }

      if (promotionObj.tax != null) {
        jsonObj.tax = promotionObj.tax;
      }

      if (promotionObj.taxableAmount != null) {
        jsonObj.taxableAmount = promotionObj.taxableAmount;
      }

      if (promotionObj.taxCode != null) {
        jsonObj.taxCode = promotionObj.taxCode;
      }
      if (promotionObj.taxExemptAmount != null) {
        jsonObj.taxExemptAmount = promotionObj.taxExemptAmount;
      }

      result.push(jsonObj);
    }
    return result;
  }

  static serializeCalculation(calculation) {
    const result: any = {};

    const { fullPrice, discounts, taxes, original } = calculation;

    if (fullPrice) {
      result.fullPrice = fullPrice;
    }
    if (discounts) {
      result.discounts = PreorderHelper.serializeCalculationDiscounts(discounts);
    }

    if (taxes) {
      result.taxes = PreorderHelper.serializeCalculationTaxs(taxes);
    }

    if (original) {
      result.original = {
        tax: original.tax,
        subtotal: original.subtotal,
        total: original.total,
      };
    }

    return result;
  }

  static serializeCalculationDiscounts(discounts) {
    const result = [];

    if (Boolean(discounts) && discounts.length > 0) {
      for (const itemObj of discounts) {
        const item: any = itemObj;
        const { type, discount, deductedTax, subType, promotionId } = item;
        const discountItem: any = {
          type,
          discount,
          deductedTax,
        };
        if (subType) {
          discountItem.subType = subType;
        }
        if (promotionId) {
          discountItem.promotionId = promotionId;
        }
        result.push(discountItem);
      }
    }
    return result;
  }

  static serializeCalculationTaxs(taxes) {
    const result = [];

    if (Boolean(taxes) && taxes.length > 0) {
      for (const itemObj of taxes) {
        const item: any = itemObj;
        const { taxCode, taxRate, tax, isVatExempted, isAmusementTax } = item;
        const taxItem: any = {
          taxCode,
          taxRate,
          tax,
          isVatExempted,
          isAmusementTax,
        };
        result.push(taxItem);
      }
    }
    return result;
  }
}
