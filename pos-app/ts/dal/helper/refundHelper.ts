import { STOREHUB_APP_VERSION } from '../../config';
import { OrderChannel, TransactionFlowType } from '../../constants';

import { getIOSStringFromDate } from '../../utils/datetime';
import * as JSONUtils from '../../utils/json';
const uuidv4 = require('uuid/v4');

export default class RefundHelper {
  static serializeTransaction(realmRecord) {
    if (!realmRecord) {
      return null;
    }
    const now = new Date();
    const jsonObj: any = {
      transactionId: uuidv4(),
      appVersion: STOREHUB_APP_VERSION,
      transactionType: TransactionFlowType.Return,
      channel: OrderChannel.OrderChannelOffline,
      isOriginalOnline: realmRecord.isOriginalOnline,
      total: realmRecord.total,
      // total: realmRecord.total - (realmRecord.roundedAmount || 0),
      subtotal: realmRecord.subtotal,
      discount: realmRecord.discount,
      tax: realmRecord.tax,
      isDeleted: realmRecord.isDeleted || false,
      isCompleted: realmRecord.isCompleted || false,
      modifiedTime: now.toISOString(), // Only for uploading transaction
      modifiedDate: now.toISOString(),
      originalReceiptNumber: realmRecord.receiptNumber,
      isCancelled: realmRecord.isCancelled,
      // roundedAmount: realmRecord.roundedAmount, // CM-8158 this will cause the rounding amount of partial refund is wrong
      serviceChargeTaxId: realmRecord.serviceChargeTaxId,
      serviceCharge: realmRecord.serviceCharge,
      serviceChargeTax: realmRecord.serviceChargeTax,
      serviceChargeRate: realmRecord.serviceChargeRate,
      shippingType: realmRecord.shippingType,
      isOpen: realmRecord.isOpen,
      tableId: realmRecord.tableId,
      // depositAmount: realmRecord.depositAmount, // preorder can be refund, so the sale transaction no need restore the deposit which will cause issues
      takeawayId: realmRecord.takeawayId,
      pickUpId: realmRecord.pickUpId,
      headcount: realmRecord.headcount,
      pwdCount: realmRecord.pwdCount,
      seniorsCount: realmRecord.seniorsCount,
      isOnlineOrder: realmRecord.isOnlineOrder,
      isPayByCash: realmRecord.isPayByCash,
      isPayLater: realmRecord.isPayLater,
      pax: realmRecord.pax || 0,
      mrs: realmRecord.mrs,
      pwdDiscount: realmRecord.pwdDiscount || 0,
      seniorDiscount: realmRecord.seniorDiscount || 0,
      taxableSales: realmRecord.taxableSales || 0, // ？？？
      taxExemptedSales: realmRecord.taxExemptedSales || 0, // ？？？
      zeroRatedSales: realmRecord.zeroRatedSales || 0, // ？？？
      totalDeductedTax: realmRecord.totalDeductedTax || 0, // ？？？
      takeawayCharges: realmRecord.takeawayCharges || 0, // ？？？
      takeawayCharge: realmRecord.takeawayCharge || 0, // ？？？
      amusementTax: realmRecord.amusementTax || 0,
      // TODO: display
      display: {
        subtotal: realmRecord.subtotal,
        discount: realmRecord.discount,
        serviceCharge: realmRecord.serviceCharge,
        tax: realmRecord.tax,
        total: realmRecord.total,
      },
    };

    // loyaltyEarned
    // createdDate
    // employeeNumber
    // employeeId
    // lastSyncTime
    // manuallyRemovedServiceCharge
    // shiftId
    // shiftIdOfPreOrder
    // shiftIdOfCancel
    // receiptNumber
    // registerId
    // returnReason
    // returnStatus
    // sequenceNumber
    // invoiceSeqNumber
    // uploadedDate
    // payments
    // mrs
    // otherReason

    // Selective Fileds
    if (realmRecord.salesChannel != null) {
      jsonObj.salesChannel = realmRecord.salesChannel;
    }
    if (realmRecord.enableCashback != null) {
      jsonObj.enableCashback = realmRecord.enableCashback;
    }
    if (realmRecord.comment != null) {
      jsonObj.comment = realmRecord.comment;
    }
    if (realmRecord.customerId != null) {
      jsonObj.customerId = realmRecord.customerId;
    }
    if (realmRecord.shippingFee != null) {
      jsonObj.shippingFee = realmRecord.shippingFee;
    }
    if (realmRecord.shippingFeeDiscount != null) {
      jsonObj.shippingFeeDiscount = realmRecord.shippingFeeDiscount;
    }
    if (realmRecord.pickUpDate != null) {
      jsonObj.pickUpDate = getIOSStringFromDate(realmRecord.pickUpDate);
    }
    if (realmRecord.pickUpId != null) {
      jsonObj.pickUpId = realmRecord.pickUpId;
    }
    if (realmRecord.isCancelled) {
      jsonObj.cancelledAt = getIOSStringFromDate(realmRecord.cancelledAt);
      jsonObj.cancelledBy = realmRecord.cancelledBy;
    }
    if (realmRecord.addonBirCompliance != null && realmRecord.addonBirCompliance.discountType !== 'SC/PWD') {
      jsonObj.addonBirCompliance = RefundHelper.serializeAddonBirCompliance(realmRecord.addonBirCompliance);
    }

    if (Boolean(realmRecord.promotions) && realmRecord.promotions.length > 0) {
      jsonObj.promotions = RefundHelper.serializePromotions(realmRecord.promotions);
    }
    const loyaltyDiscounts = RefundHelper.serializeLoyaltyDiscounts(realmRecord.loyaltyDiscounts);
    if (loyaltyDiscounts.length > 0) {
      jsonObj.loyaltyDiscounts = loyaltyDiscounts;
    }
    // Purchased Item parse
    jsonObj.items = RefundHelper.serializeItems(realmRecord);

    // calcualtion
    if (realmRecord.calculation) {
      jsonObj.calculation = RefundHelper.serializeCalculation(realmRecord.calculation);
    }

    return jsonObj;
  }

  static serializeItems(transaction) {
    const items = transaction.items;
    const result = [];
    if (Boolean(items) && items.length > 0) {
      for (const itemObj of items) {
        const item: any = itemObj;
        const total = item.total || 0;
        const itemJson: any = {
          id: item.id || item._id,
          _id: item._id,
          productId: item.productId,
          quantity: item.quantity,
          return: { quantity: item.quantity, total: total, unitPrice: total / (item.quantity || 1) },
          subTotal: item.subTotal || 0,
          total,
          discount: item.discount || 0,
          adhocDiscount: item.adhocDiscount,
          unitPrice: item.unitPrice || 0,
          tax: item.tax || 0,
          taxRate: item.taxRate || 0,
          originalQuantity: item.quantity,
          isTakeaway: item.isTakeaway,
          isVatExempted: item.isVatExempted || false,
          isBasicNecessitiesPH: item.isBasicNecessitiesPH || false,
          isSoloParentDiscountApplicable: item.isSoloParentDiscountApplicable || false,
          loyaltyDiscount: item.loyaltyDiscount || 0,
          // discountInputValue: item.discountInputValue || 0,
          orderingValue: item.orderingValue,
          options: item.selectedOptions || (item.options ? JSONUtils.parse(item.options, []) : []),
          title: item.title,
          taxCode: item.taxCode || '',
          taxableAmount: item.taxableAmount || 0,
          taxExemptAmount: item.taxExemptAmount || 0,
          zeroRatedSales: item.zeroRatedSales || 0,
          totalDeductedTax: item.totalDeductedTax || 0,
          seniorDiscount: item.seniorDiscount || 0,
          pwdDiscount: item.pwdDiscount || 0,
          athleteAndCoachDiscount: item.athleteAndCoachDiscount || 0,
          soloParentDiscount: item.soloParentDiscount || 0,
          medalOfValorDiscount: item.medalOfValorDiscount || 0,
          isServiceChargeNotApplicable: item.isServiceChargeNotApplicable || false,
          // TODO: display
          display: {
            total,
            subtotal: item.subTotal || 0,
            tax: item.tax || 0,
          },
        };

        if (item.itemType === 'ServiceCharge') {
          if (total > 0) {
            itemJson.rate = item.rate || transaction.serviceChargeRate;
          } else {
            itemJson.rate = 0;
          }
        } else {
          itemJson.rate = item.rate || 0;
        }

        if (item.originalItemId) {
          itemJson.originalItemId = item.originalItemId;
        }

        if (item.itemChannel) {
          itemJson.itemChannel = item.itemChannel;
        }

        if (item.takeawayCharges) {
          itemJson.takeawayCharges = item.takeawayCharges;
        }

        if (item.takeawayCharge) {
          itemJson.takeawayCharge = item.takeawayCharge;
        }

        if (item.itemType != null && item.itemType.length > 0) {
          itemJson.itemType = item.itemType;
        }
        if (item.notes != null && item.notes.length > 0) {
          itemJson.notes = item.notes;
        }
        if (item.comments != null && item.comments.length > 0) {
          itemJson.comments = item.comments;
        }
        if (item.sn != null && item.sn.length > 0) {
          itemJson.sn = item.sn;
        }
        if (item.promotions != null && item.promotions.length > 0) {
          itemJson.promotions = RefundHelper.serializePromotions(item.promotions);
        }
        if (item.calculation) {
          itemJson.calculation = RefundHelper.serializeCalculation(item.calculation);
        }

        if (item.employeeId) {
          itemJson.employeeId = item.employeeId;
        }

        if (item.employeeName) {
          itemJson.employeeName = item.employeeName;
        }

        result.push(itemJson);
      }
    }
    return result;
  }

  static serializeLoyaltyDiscounts(loyaltyDiscounts) {
    const result = [];
    if (Boolean(loyaltyDiscounts) && loyaltyDiscounts.length > 0) {
      for (const loyaltyDiscount of loyaltyDiscounts) {
        const loyaltyItem: any = loyaltyDiscount;
        const loyaltyJson: any = {
          displayDiscount: loyaltyItem.displayDiscount,
          type: loyaltyItem.type || loyaltyItem.loyaltyType,
          loyaltyType: loyaltyItem.type || loyaltyItem.loyaltyType,
          inputValue: loyaltyItem.inputValue || 0,
          spentValue: loyaltyItem.spentValue,
        };
        if (loyaltyItem.taxRate != null) {
          loyaltyJson.taxRate = loyaltyItem.taxRate;
        }
        loyaltyJson.display = {
          discount: loyaltyItem.displayDiscount,
          discountedTax: loyaltyItem.spentValue - loyaltyItem.displayDiscount,
        };
        result.push(loyaltyJson);
      }
    }
    return result;
  }

  static serializeAddonBirCompliance(addonBirCompliance) {
    const result: any = {};
    if (addonBirCompliance.discountType !== null) {
      result.discountType = addonBirCompliance.discountType;
    }
    result.athleteAndCoachDiscount = addonBirCompliance.athleteAndCoachDiscount;
    result.medalOfValorDiscount = addonBirCompliance.medalOfValorDiscount;
    result.soloParentDiscount = addonBirCompliance.soloParentDiscount;
    if (addonBirCompliance.collectedInfo !== null) {
      result.collectedInfo = addonBirCompliance.collectedInfo; // string
    }
    return result;
  }

  static serializePromotions(realmPromotions) {
    if (!realmPromotions || realmPromotions.length === 0) {
      return [];
    }

    const result = [];
    for (const promotionObj of realmPromotions) {
      const jsonObj: any = { promotionId: promotionObj.promotionId };
      if (promotionObj.discount != null) {
        jsonObj.discount = promotionObj.discount;
      } else {
        jsonObj.discount = 0; // TODO: for combo promotion, the calculator-lib misses the discount.
      }

      jsonObj.display = { discount: jsonObj.discount };

      if (promotionObj.uniquePromotionCodeId != null) {
        jsonObj.uniquePromotionCodeId = promotionObj.uniquePromotionCodeId;
      }

      if (promotionObj.uniquePromotionCode != null) {
        jsonObj.uniquePromotionCode = promotionObj.uniquePromotionCode;
      }

      if (promotionObj.pwdDiscount != null) {
        jsonObj.pwdDiscount = promotionObj.pwdDiscount;
      }

      if (promotionObj.quantity != null) {
        jsonObj.quantity = promotionObj.quantity;
      }

      if (promotionObj.seniorDiscount != null) {
        jsonObj.seniorDiscount = promotionObj.seniorDiscount;
      }

      if (promotionObj.tax != null) {
        jsonObj.tax = promotionObj.tax;
      }

      if (promotionObj.taxableAmount != null) {
        jsonObj.taxableAmount = promotionObj.taxableAmount;
      }

      if (promotionObj.taxCode != null) {
        jsonObj.taxCode = promotionObj.taxCode;
      }
      if (promotionObj.taxExemptAmount != null) {
        jsonObj.taxExemptAmount = promotionObj.taxExemptAmount;
      }

      result.push(jsonObj);
    }
    return result;
  }

  static serializeCalculation(calculation) {
    const result: any = {};

    const { fullPrice, discounts, taxes, original } = calculation;

    if (fullPrice) {
      result.fullPrice = fullPrice;
    }
    if (discounts) {
      result.discounts = RefundHelper.serializeCalculationDiscounts(discounts);
    }
    if (taxes) {
      result.taxes = RefundHelper.serializeCalculationTaxs(taxes);
    }

    if (original) {
      result.original = {
        tax: original.tax,
        subtotal: original.subtotal,
        total: original.total,
      };
    }

    return result;
  }

  static serializeCalculationDiscounts(discounts) {
    const result = [];

    if (Boolean(discounts) && discounts.length > 0) {
      for (const itemObj of discounts) {
        const item: any = itemObj;
        const { type, discount, deductedTax, subType, promotionId } = item;
        const discountItem: any = {
          type,
          discount,
          deductedTax,
        };
        if (subType) {
          discountItem.subType = subType;
        }
        if (promotionId) {
          discountItem.promotionId = promotionId;
        }
        result.push(discountItem);
      }
    }
    return result;
  }

  static serializeCalculationTaxs(taxes) {
    const result = [];

    if (Boolean(taxes) && taxes.length > 0) {
      for (const itemObj of taxes) {
        const item: any = itemObj;
        const { taxCode, taxRate, tax, isVatExempted, isAmusementTax } = item;
        const taxItem: any = {
          taxCode,
          taxRate,
          tax,
          isVatExempted,
          isAmusementTax,
        };
        result.push(taxItem);
      }
    }
    return result;
  }
}
