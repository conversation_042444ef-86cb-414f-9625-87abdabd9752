import { floor, get, head, isArray, isEmpty, map } from 'lodash';

import { STOREHUB_APP_VERSION } from '../../config';
import PaymentOptions from '../../config/paymentOption';
import { ItemChannelType, PromotionDiscountType, TransactionFlowType } from '../../constants';

import { stringifyItemOptions } from '../../sagas/transaction/common';
import { PurchasedItemType, TransactionType } from '../../typings';
import { isValidNumber } from '../../utils';
import { getIOSStringFromDate } from '../../utils/datetime';
import * as JSONUtils from '../../utils/json';
import DAL from '../index';
import PromotionHelper from './promotionHelper';

export default class TransactionHelper {
  static serializeTransaction(realmRecord) {
    if (!realmRecord) {
      return null;
    }

    const jsonObj: any = {
      isOriginalOnline: realmRecord.isOriginalOnline,
      total: realmRecord.total || 0,
      subtotal: realmRecord.subtotal || 0,
      discount: realmRecord.discount || 0,
      tax: realmRecord.tax || 0,
      modifiedTime: getIOSStringFromDate(realmRecord.modifiedDate), // Only for uploading transaction
      createdTime: getIOSStringFromDate(realmRecord.createdDate), // Only for uploading transaction
      createdDate: realmRecord.createdDate,
      modifiedDate: realmRecord.modifiedDate,
      employeeNumber: realmRecord.employeeId,
      employeeId: realmRecord.employeeId,
      transactionId: realmRecord.transactionId,
      transactionType: realmRecord.transactionType,
      receiptNumber: realmRecord.receiptNumber,
      isCancelled: realmRecord.isCancelled,
      roundedAmount: realmRecord.roundedAmount,
      appVersion: realmRecord.appVersion,
      serviceCharge: realmRecord.serviceCharge,
      serviceChargeTax: realmRecord.serviceChargeTax,
      serviceChargeRate: realmRecord.serviceChargeRate,
      isOpen: realmRecord.isOpen,
      registerId: realmRecord.registerId,
      registerNumber: realmRecord.registerNumber,
      tableId: realmRecord.tableId,
      takeawayId: realmRecord.takeawayId,
      pickUpId: realmRecord.pickUpId,
      headcount: realmRecord.headcount,
      pwdCount: realmRecord.pwdCount,
      seniorsCount: realmRecord.seniorsCount,
      isOnlineOrder: realmRecord.isOnlineOrder,
      mrs: realmRecord.mrs,
      shippingType: realmRecord.shippingType,
      channel: realmRecord.channel,
      amusementTax: realmRecord.amusementTax || 0,
    };
    // Selective Fileds
    if (realmRecord.takeawayCharges != null) {
      jsonObj.takeawayCharges = realmRecord.takeawayCharges;
    }
    if (realmRecord.takeawayCharge != null) {
      jsonObj.takeawayCharge = realmRecord.takeawayCharge;
    }
    if (realmRecord.salesChannel != null) {
      jsonObj.salesChannel = realmRecord.salesChannel;
    }
    if (realmRecord.enableCashback != null) {
      jsonObj.enableCashback = realmRecord.enableCashback;
    }
    if (realmRecord.originalReceiptNumber != null) {
      jsonObj.originalReceiptNumber = realmRecord.originalReceiptNumber;
    }
    if (realmRecord.comment != null) {
      jsonObj.comment = realmRecord.comment;
    }
    if (realmRecord.returnStatus != null) {
      jsonObj.returnStatus = realmRecord.returnStatus;
    }
    if (realmRecord.returnReason != null) {
      jsonObj.returnReason = realmRecord.returnReason;
    }
    if (realmRecord.customerId != null) {
      jsonObj.customerId = realmRecord.customerId;
    }
    if (realmRecord.pax != null) {
      jsonObj.pax = realmRecord.pax;
    }
    if (realmRecord.seniorDiscount != null) {
      jsonObj.seniorDiscount = realmRecord.seniorDiscount;
    }
    if (realmRecord.pwdDiscount != null) {
      jsonObj.pwdDiscount = realmRecord.pwdDiscount;
    }
    if (realmRecord.taxableSales != null) {
      jsonObj.taxableSales = realmRecord.taxableSales;
    }
    if (realmRecord.taxExemptedSales != null) {
      jsonObj.taxExemptedSales = realmRecord.taxExemptedSales;
    }
    if (realmRecord.zeroRatedSales != null) {
      jsonObj.zeroRatedSales = realmRecord.zeroRatedSales;
    }
    if (realmRecord.totalDeductedTax != null) {
      jsonObj.totalDeductedTax = realmRecord.totalDeductedTax;
    }
    if (realmRecord.shippingFee != null) {
      jsonObj.shippingFee = realmRecord.shippingFee;
    }
    if (realmRecord.shippingFeeDiscount != null) {
      jsonObj.shippingFeeDiscount = realmRecord.shippingFeeDiscount;
    }

    if (realmRecord.sequenceNumber != null) {
      jsonObj.sequenceNumber = realmRecord.sequenceNumber;
    }
    if (realmRecord.invoiceSeqNumber != null) {
      jsonObj.invoiceSeqNumber = realmRecord.invoiceSeqNumber;
    }
    if (realmRecord.voidNumber != null) {
      jsonObj.voidNumber = realmRecord.voidNumber;
    }
    if (realmRecord.pickUpDate != null) {
      jsonObj.pickUpDate = getIOSStringFromDate(realmRecord.pickUpDate);
    }
    if (realmRecord.pickUpId != null) {
      jsonObj.pickUpId = realmRecord.pickUpId;
    }
    if (realmRecord.otherReason != null) {
      jsonObj.otherReason = realmRecord.otherReason;
    }
    if (realmRecord.isCancelled) {
      jsonObj.cancelledAt = getIOSStringFromDate(realmRecord.cancelledAt);
      jsonObj.cancelledBy = realmRecord.cancelledBy;
    }
    if (realmRecord.preOrderId != null) {
      jsonObj.preOrderId = realmRecord.preOrderId;
    }
    if (realmRecord.preOrderBy != null) {
      jsonObj.preOrderBy = realmRecord.preOrderBy;
    }
    if (realmRecord.preOrderDate != null) {
      jsonObj.preOrderDate = realmRecord.preOrderDate;
    }
    if (realmRecord.depositAmount != null) {
      jsonObj.depositAmount = realmRecord.depositAmount;
    }
    if (realmRecord.addonBirCompliance && realmRecord.addonBirCompliance.discountType !== 'SC/PWD') {
      jsonObj.addonBirCompliance = TransactionHelper.serializeAddonBirCompliance(realmRecord.addonBirCompliance);
    }

    if (Boolean(realmRecord.promotions) && realmRecord.promotions.length > 0) {
      jsonObj.promotions = PromotionHelper.serializePromotions(realmRecord.promotions);
    }
    const loyaltyDiscounts = TransactionHelper.serializeLoyaltyDiscounts(realmRecord.loyaltyDiscounts);
    if (loyaltyDiscounts.length > 0) {
      jsonObj.loyaltyDiscounts = loyaltyDiscounts;
    }
    // Purchased Item parse
    jsonObj.items = TransactionHelper.serializeItems(realmRecord.items, realmRecord.takeawayCharge);

    // Payments parse
    jsonObj.payments = TransactionHelper.serializePayments(realmRecord.payments);

    // calcualtion
    if (realmRecord.calculation) {
      jsonObj.calculation = TransactionHelper.serializeCalculation(realmRecord.calculation);
    }

    return jsonObj;
  }

  static serializeItems(items, takeawayCharge?) {
    const result = [];
    if (Boolean(items) && items.length > 0) {
      for (const itemObj of items) {
        const item: any = itemObj;
        const itemJson: any = {
          productId: item.productId,
          quantity: item.quantity || 0,
          subTotal: item.subTotal || 0,
          total: item.total || 0,
          discount: item.discount || 0,
          adhocDiscount: item.adhocDiscount || 0,
          unitPrice: item.unitPrice || 0,
          tax: item.tax || 0,
          isServiceChargeNotApplicable: item.isServiceChargeNotApplicable,
          taxRate: item.taxRate || 0,
          orderingValue: item.orderingValue,
          selectedOptions: item.selectedOptions || (item.options ? JSONUtils.parse(item.options, []) : []),
          title: item.title,
          taxCode: item.taxCode || '',
          taxableAmount: item.taxableAmount,
          taxExemptAmount: item.taxExemptAmount,
          zeroRatedSales: item.zeroRatedSales,
          totalDeductedTax: item.totalDeductedTax,
          seniorDiscount: item.seniorDiscount,
          pwdDiscount: item.pwdDiscount,
          discountType: item.discountType,
          discountValue: item.discountInputValue, // The server recognizes discountValue rather than discountInputValue.
          athleteAndCoachDiscount: item.athleteAndCoachDiscount,
          soloParentDiscount: item.soloParentDiscount,
          medalOfValorDiscount: item.medalOfValorDiscount,
          isTakeaway: item.isTakeaway,
          loyaltyDiscount: item.loyaltyDiscount || 0,
        };

        if (item.itemChannel) {
          itemJson.itemChannel = item.itemChannel;
        }

        if (item.takeawayCharges) {
          itemJson.takeawayCharges = item.takeawayCharges;
        }

        if (item.takeawayCharge) {
          itemJson.takeawayCharge = item.takeawayCharge;
        } else if (takeawayCharge) {
          itemJson.takeawayCharge = takeawayCharge;
        }

        if (item.itemType != null && item.itemType.length > 0) {
          itemJson.itemType = item.itemType;
        }
        if (item.notes != null && item.notes.length > 0) {
          itemJson.notes = item.notes;
        }
        if (item.comments != null && item.comments.length > 0) {
          itemJson.comments = item.comments;
        }
        if (item.sn != null && item.sn.length > 0) {
          itemJson.sn = item.sn;
        }
        if (item.promotions != null && item.promotions.length > 0) {
          itemJson.promotions = PromotionHelper.serializePromotions(item.promotions);
        }
        if (item.calculation) {
          itemJson.calculation = TransactionHelper.serializeCalculation(item.calculation);
        }

        if (item.employeeId) {
          itemJson.employeeId = item.employeeId;
        }

        if (item.employeeName) {
          itemJson.employeeName = item.employeeName;
        }
        result.push(itemJson);
      }
    }
    return result;
  }

  static serializePayments(payments) {
    const result = [];

    if (Boolean(payments) && payments.length > 0) {
      for (const itemObj of payments) {
        const item: any = itemObj;
        const { type, paymentMethod } = item;
        const finalPaymentMethod = isValidNumber(item.paymentMethodId)
          ? PaymentOptions.getUploadPaymentMethod(item.paymentMethodId)
          : Boolean(type)
            ? type
            : paymentMethod;
        result.push({
          amount: item.amount,
          paymentMethodId: item.paymentMethodId,
          paymentMethod: finalPaymentMethod, // Only for uploading transaction.
          cashTendered: item.cashTendered,
          roundedAmount: item.roundedAmount,
          isDeposit: item.isDeposit,
          subType: item.subType,
          type: finalPaymentMethod,
          mPOSTxnId: item.mPOSTxnId,
          isOnline: item.isOnline,
          manualApproveInfo: Boolean(item.manualApproveInfo) ? JSON.parse(item.manualApproveInfo) : undefined,
        });
      }
    }
    return result;
  }

  static serializeLoyaltyDiscounts(loyaltyDiscounts) {
    const result = [];
    if (Boolean(loyaltyDiscounts) && loyaltyDiscounts.length > 0) {
      for (const loyaltyDiscount of loyaltyDiscounts) {
        const loyaltyItem: any = loyaltyDiscount;
        const loyaltyJson: any = {
          displayDiscount: loyaltyItem.displayDiscount,
          type: loyaltyItem.type,
          loyaltyType: loyaltyItem.type,
          spentValue: loyaltyItem.spentValue,
        };
        result.push(loyaltyJson);
      }
    }
    return result;
  }

  static serializeAddonBirCompliance(addonBirCompliance) {
    const result: any = {};
    if (addonBirCompliance.discountType !== null) {
      result.discountType = addonBirCompliance.discountType;
    }
    result.athleteAndCoachDiscount = addonBirCompliance.athleteAndCoachDiscount;
    result.medalOfValorDiscount = addonBirCompliance.medalOfValorDiscount;
    result.soloParentDiscount = addonBirCompliance.soloParentDiscount;
    const collectedInfo = get(addonBirCompliance, 'collectedInfo', '');
    if (!isEmpty(collectedInfo)) {
      if (typeof collectedInfo === 'string') {
        result.collectedInfo = JSONUtils.parse(collectedInfo, {});
      } else if (typeof collectedInfo === 'object') {
        result.collectedInfo = collectedInfo;
      }
    }
    return result;
  }

  static mapOnlineOrderItems(onlineTransaction) {
    const { items } = onlineTransaction;
    const newItems = [];
    let newItem: PurchasedItemType;
    if (Boolean(items)) {
      for (let index = 0; index < items.length; index++) {
        const item = items[index];
        // @ts-ignore
        newItem = {};
        const { taxRate, selectedOptions } = item;
        const realTaxRate = isValidNumber(taxRate) ? Number(taxRate) : 0;
        // No need to save 'discount' type item, follow getCurrentRecord
        if (item.itemType === 'Discount') {
          if (onlineTransaction.transactionType != TransactionFlowType.PreOrder) {
            continue;
          }
          // @ts-ignore
          newItem = {
            itemType: item.itemType,
            discountInputValue: item.discountValue,
            discountType: item.discountType,
            taxRate: realTaxRate,
          };
        } else if (item.itemType === 'ServiceCharge') {
          // @ts-ignore
          newItem = {
            itemType: item.itemType,
            subTotal: item.subTotal,
            discount: item.discount,
            tax: item.tax,
            taxRate: realTaxRate,
            taxCode: item.taxCode,
            rate: item.rate,
            total: item.total,
            quantity: 1,
            unitPrice: item.unitPrice,
            pwdDiscount: item.pwdDiscount,
            seniorDiscount: item.seniorDiscount,
            taxableAmount: item.taxableAmount,
            taxExemptAmount: item.taxExemptAmount,
            adhocDiscount: item.adhocDiscount,
            zeroRatedSales: item.zeroRatedSales,
            totalDeductedTax: item.totalDeductedTax,
          };
        } else {
          const options = [];
          if (Boolean(selectedOptions) && selectedOptions.length > 0) {
            for (const option of selectedOptions) {
              // remove priceDiff when save the transaction
              const { priceDiff, ...remain } = option as any;
              options.push(remain);
            }
          }

          // Calculate promotion quantity
          const promotions = map(item.promotions, promotion => {
            let quantity = Boolean(item.promotionAppliedQuantityMap) ? item.promotionAppliedQuantityMap[promotion.promotionId] || 1 : 1;
            if (promotion.discountType === PromotionDiscountType.BuyXFreeY) {
              const realmPromotion = DAL.getPromotionById(promotion.promotionId);
              const condition = head(realmPromotion.conditions);
              const minQuantity = get(condition, 'minQuantity', 1);
              const discountValue = get(condition, 'discountValue', 1) === 0 ? 1 : get(condition, 'discountValue', 1);
              quantity = floor((quantity / minQuantity) * discountValue);
            }
            if (promotion.discountType === PromotionDiscountType.Percentage || promotion.discountType === PromotionDiscountType.Absolute) {
              quantity = 1;
            }
            promotion.quantity = quantity;
            return promotion;
          });
          // @ts-ignore
          newItem = {
            id: item.id,
            submitId: item.submitId,
            productId: item.productId,
            unitPrice: item.unitPrice,
            options: options.length === 0 ? '' : JSON.stringify(options),
            quantity: item.quantity,
            subTotal: item.subTotal,
            total: item.total,
            title: item.title,
            notes: item.notes,
            discount: item.discount,
            tax: item.tax,
            isVatExempted: item.isVatExempted,
            isBasicNecessitiesPH: item.isBasicNecessitiesPH,
            isSoloParentDiscountApplicable: item.isSoloParentDiscountApplicable,
            loyaltyDiscount: item.loyaltyDiscount || 0,
            taxRate: realTaxRate,
            itemType: item.itemType,
            taxCode: item.taxCode || '',
            adhocDiscount: item.adhocDiscount,
            sn: item.sn,
            promotions,
            trackInventory: item.trackInventory,
            discountType: item.discountType,
            discountInputValue: item.discountInputValue,
            seniorDiscount: item.seniorDiscount,
            pwdDiscount: item.pwdDiscount,
            taxableAmount: item.taxableAmount,
            taxExemptAmount: item.taxExemptAmount,
            zeroRatedSales: item.zeroRatedSales,
            totalDeductedTax: item.totalDeductedTax,
            athleteAndCoachDiscount: item.athleteAndCoachDiscount,
            soloParentDiscount: item.soloParentDiscount,
            medalOfValorDiscount: item.medalOfValorDiscount,
            takeawayCharges: item.taxExemptAmount,
            itemChannel: item.itemChannel || ItemChannelType.DEFAULT,
            isTakeaway: item.isTakeaway,
            takeawayCharge: item.takeawayCharge,
            calculation: item.calculation,
          };
          if (newItem.calculation && !newItem.calculation.taxes) {
            newItem.calculation.taxes = [];
          }
        }
        newItem.orderingValue = index;
        newItems.push(newItem);
      }
    }
    return newItems;
  }

  static saveKdsOnlineOrder(onlineTransaction, dbTransaction?: TransactionType) {
    // restore createdDate for ShiftReport
    const createdDate = dbTransaction?.createdDate || onlineTransaction.createdTime;
    // Do not update preOrderDate! Just in case, make sure the data is accurate, because PreOrderDate will affect the ShiftReport statistics
    return {
      transactionId: onlineTransaction.transactionId || onlineTransaction.id,
      receiptNumber: onlineTransaction.receiptNumber,
      tableId: onlineTransaction.tableId,
      pickUpId: onlineTransaction.pickUpId,
      isOnlineOrder: true,
      transactionType: onlineTransaction.transactionType || TransactionFlowType.Sale,
      createdDate,
      modifiedDate: onlineTransaction.modifiedTime || dbTransaction?.modifiedDate || createdDate,
      isCancelled: onlineTransaction.isCancelled || dbTransaction?.isCancelled,
      cancelledAt: onlineTransaction.cancelledDate || dbTransaction?.cancelledAt || null,
      appVersion: STOREHUB_APP_VERSION,
      comment: onlineTransaction.comment,
      subOrders: onlineTransaction.subOrders ?? [],
      items: this.mapOnlineOrderItems(onlineTransaction),
      isSplittedFromReceiptNumber: onlineTransaction.isSplittedFromReceiptNumber,
      status: onlineTransaction.status,
      channel: onlineTransaction.channel,
      shippingType: onlineTransaction.shippingType,
      isPayLater: onlineTransaction.isPayLater,
      pax: onlineTransaction.pax,
      expectDeliveryDateFrom: onlineTransaction.expectDeliveryDateFrom,
      expectDeliveryDateTo: onlineTransaction.expectDeliveryDateTo,
    } as TransactionType;
  }

  static serializeCalculation(calculation) {
    const result: any = {};

    const { fullPrice, discounts, taxes, original } = calculation;

    if (fullPrice) {
      result.fullPrice = fullPrice;
    }
    if (discounts) {
      result.discounts = TransactionHelper.serializeCalculationDiscounts(discounts);
    }
    if (taxes) {
      result.taxes = TransactionHelper.serializeCalculationTaxs(taxes);
    }

    if (original) {
      result.original = {
        tax: original.tax,
        subtotal: original.subtotal,
        total: original.total,
      };
    }

    return result;
  }

  static serializeCalculationDiscounts(discounts) {
    const result = [];

    if (Boolean(discounts) && discounts.length > 0) {
      for (const itemObj of discounts) {
        const item: any = itemObj;
        const { type, discount, deductedTax, subType, promotionId } = item;
        const discountItem: any = {
          type,
          discount: discount || 0,
          deductedTax: deductedTax || 0,
        };
        if (subType) {
          discountItem.subType = subType;
        }
        if (promotionId) {
          discountItem.promotionId = promotionId;
        }
        result.push(discountItem);
      }
    }
    return result;
  }

  static serializeCalculationTaxs(taxes) {
    const result = [];

    if (Boolean(taxes) && taxes.length > 0) {
      for (const itemObj of taxes) {
        const item: any = itemObj;
        const { taxCode, taxRate, tax, isVatExempted, isAmusementTax } = item;
        const taxItem: any = {
          taxCode,
          taxRate,
          tax,
          isVatExempted,
          isAmusementTax,
        };
        result.push(taxItem);
      }
    }
    return result;
  }

  static mappingRemoteToLocal(record) {
    const jsonObj: any = record;

    jsonObj.createdDate = record.createdTime || record.createdDate;
    jsonObj.modifiedDate = record.modifiedTime || record.modifiedDate;
    jsonObj.employeeId = record.employeeNumber || record.employeeId;
    jsonObj.transactionType = record.transactionType || TransactionFlowType.Sale;
    if (isArray(record.items)) {
      map(record.items, item => {
        return { ...item, options: stringifyItemOptions(item.selectedOptions) };
      });
    }

    if (record.addonBirCompliance) {
      const collectedInfo = get(record.addonBirCompliance, 'collectedInfo');
      if (!isEmpty(collectedInfo)) {
        if (typeof collectedInfo === 'string') {
          jsonObj.addonBirCompliance.collectedInfo = collectedInfo;
        } else if (typeof collectedInfo === 'object') {
          jsonObj.addonBirCompliance.collectedInfo = JSONUtils.stringify(collectedInfo);
        }
      }
    }

    return jsonObj;
  }
}
