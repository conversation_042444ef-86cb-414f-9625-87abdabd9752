import { chain, findIndex, forEach, get, head, indexOf, isArray, isEmpty, last, map, orderBy, reduce, sumBy } from 'lodash';
import moment from 'moment';
import RNFS from 'react-native-fs';
import Realm, { Results, UpdateMode } from 'realm';
import { OperationType, ProposeDataType } from '../actions';
import { DefaultPaymentOptionType } from '../config/paymentOption';
import { ERROR_PID, INIT_PID, INIT_SNAPSHOT_VERSION, SalesChannelType, TransactionFlowType } from '../constants';
import { SearchProductsCondition } from '../containers/Products/Products';
import { confirmNcsDate } from '../sagas/ncs/transaction/helper';
import {
  BeepNotificationPlainType,
  BeepNotificationType,
  EmployeeActivityType,
  EmployeeType,
  ExcludedFirstTimeActionType,
  LogType,
  PlainTransactionsLogType,
  PriceBookType,
  ProductType,
  ShiftType,
  SubOrderNotificationPlainType,
  SubOrderNotificationType,
  TransactionType,
  TransactionsLogType,
} from '../typings';
import { PromotionType } from '../typings/schema/PromotionType';
import * as JSONUtils from '../utils/json';
import { CookingStatus } from '../utils/kds/status';
import { DBAction, SaleFlowAction, errorStorageEvent, errorTransactionEvent } from '../utils/logComponent';
import { TransactionHelper } from './helper';
import RealmManager from './realm';
import { checkException } from './errors';

export type SalesItem = { totalAmount: number; totalCashRounded?: number; count?: number; paymentMethodId?: any; totalAmountWithoutTax?: number };

export type SalesGroup = {
  [key: string]: SalesItem;
};

class DAL {
  public static clearDB() {
    RealmManager.getRealmInstance().write(() => {
      RealmManager.getRealmInstance().deleteAll();
    });
  }

  // Employee
  public static saveEmployee(jsonEntity: Record<string, any>) {
    try {
      RealmManager.getRealmInstance().write(() => {
        RealmManager.getRealmInstance().create('Employee', jsonEntity, UpdateMode.All);
      });
      return true;
    } catch (exception) {
      console.log('saveEmployee Save Error');
      errorStorageEvent({
        action: DBAction.Save,
        reason: 'saveEmployee error',
        exception,
        privateDataPayload: { employee: JSONUtils.stringify(jsonEntity) },
      });
      checkException(exception);
      return false;
    }
  }

  public static saveSyncEmployees(jsonEntity: any[]) {
    if (isEmpty(jsonEntity) || !isArray(jsonEntity)) {
      return;
    }

    const failedEmployees: any[] = [];
    const validEmployees: any[] = [];

    for (const item of jsonEntity) {
      if (!item.employeeId || !item.pin || !item.modifiedTime) {
        failedEmployees.push(item);
        continue;
      }

      if (isEmpty(item.backOfficeDetailAccesses)) {
        item.backOfficeDetailAccesses = [];
      }

      validEmployees.push(item);
    }

    try {
      RealmManager.getRealmInstance().write(() => {
        for (const item of validEmployees) {
          RealmManager.getRealmInstance().create('Employee', item, UpdateMode.All);
        }
      });
    } catch (exception) {
      console.log('saveSyncEmployees error', exception.message);
      errorStorageEvent({
        action: DBAction.Save,
        reason: 'saveSyncEmployees exception',
        exception,
        privateDataPayload: { employees: JSONUtils.stringify(validEmployees) },
      });
      checkException(exception);
    }

    if (failedEmployees.length > 0) {
      errorStorageEvent({
        action: DBAction.Save,
        reason: 'saveSyncEmployees meet invalid data',
        privateDataPayload: {
          failedEmployees: JSONUtils.stringify(failedEmployees),
          originalEmployees: JSONUtils.stringify(jsonEntity),
        },
      });
    }
  }

  public static deleteEmployeeById(id: string) {
    const item = DAL.getEmployeeById(id);
    if (item !== undefined) {
      try {
        RealmManager.getRealmInstance().write(() => {
          RealmManager.getRealmInstance().delete(item);
        });
      } catch (exception) {
        console.log('delete employee error', exception);
        errorStorageEvent({
          action: DBAction.Delete,
          reason: 'delete employee error',
          exception,
          privateDataPayload: { employeeId: id },
        });
        checkException(exception);
      }
    }
  }

  public static getLocalTransactionCount(): number {
    try {
      return RealmManager.getRealmInstance().objects<TransactionType>('Transaction').length;
    } catch (exception) {
      console.log('getLocalTransactionCount error', exception);
      errorStorageEvent({ action: DBAction.Read, reason: 'getTransactionList error', exception });
      checkException(exception);
    }
  }

  public static getBaseTransaction(): TransactionType {
    try {
      return head(RealmManager.getRealmInstance().objects<TransactionType>('Transaction').sorted('createdDate', false));
    } catch (exception) {
      console.log('getLocalTransactionCount error', exception);
      errorStorageEvent({ action: DBAction.Read, reason: 'getTransactionList error', exception });
      checkException(exception);
    }
  }

  public static getTrxCountonLastestShift(start: Date, end: Date, registerObjectId: string): number {
    try {
      if (!Boolean(end)) {
        // when the preOrderDate do not match the date, it means that the deposit was not paid in the current date period
        return RealmManager.getRealmInstance()
          .objects('Transaction')
          .filtered('createdDate >= $0 AND isCancelled == false AND registerId == $1', start, registerObjectId).length;
      } else {
        return RealmManager.getRealmInstance()
          .objects('Transaction')
          .filtered('createdDate >= $0 AND createdDate <= $1 AND isCancelled == false  AND owner.registerId == $2', start, end, registerObjectId).length;
      }
    } catch (exception) {
      console.log('getTrxCountonLastestShift', exception);
      checkException(exception);
    }
  }

  public static clearAllLocalTransaction() {
    try {
      const transactions = RealmManager.getRealmInstance().objects<TransactionType>('Transaction');
      RealmManager.getRealmInstance().write(() => {
        RealmManager.getRealmInstance().delete(transactions);
      });
    } catch (exception) {
      console.log('clearAllLocalTransaction error', exception, exception.message);
      checkException(exception);
    }
  }

  public static saveTransactions(trxs: any[]) {
    try {
      RealmManager.getRealmInstance().write(() => {
        for (let index = 0; index < trxs.length; index++) {
          const trx = trxs[index];
          RealmManager.getRealmInstance().create('Transaction', trx, UpdateMode.All);
        }
      });
      return true;
    } catch (exception) {
      console.log('saveTransactions error = ', exception, exception.message);
      checkException(exception);
      return false;
    }
  }

  public static getEmployeeById(id: string): EmployeeType {
    try {
      return RealmManager.getRealmInstance().objectForPrimaryKey<EmployeeType>('Employee', String(id));
    } catch (exception) {
      console.log('get employeeById error', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'get employee error',
        exception,
        privateDataPayload: { employeeId: id },
      });
      checkException(exception);
    }
  }

  public static getEmployeeListByPage(pageIndex: number, pageSize: number, search?: string) {
    try {
      let query = 'isDeleted != true';
      if (search) {
        query += ' AND ((firstName CONTAINS[c] $0) OR (lastName CONTAINS[c] $0))';
      }
      query += ' SORT(firstName ASC, lastName ASC, modifiedTime DESC, employeeId ASC)';
      const sorted = RealmManager.getRealmInstance().objects<EmployeeType>('Employee').filtered(query, search);

      const startIndex = pageIndex * pageSize;
      const endIndex = startIndex + pageSize;
      return sorted.slice(startIndex, endIndex);
    } catch (exception) {
      console.log('get employeeList error', exception);
      errorStorageEvent({ action: DBAction.Read, reason: 'get employeeList error', exception });
      return [];
    }
  }

  public static getEmployeeByPinCode(pinCode: string): EmployeeType {
    try {
      return head(RealmManager.getRealmInstance().objects<EmployeeType>('Employee').filtered('pin == $0 AND isDeleted != true', pinCode));
    } catch (exception) {
      console.log('get employeeByPinCode error', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getEmployeeByPinCode error',
        exception,
        privateDataPayload: { pinCode },
      });
      checkException(exception);
    }
  }

  public static getFirstEmployee(): EmployeeType {
    try {
      return head(RealmManager.getRealmInstance().objects<EmployeeType>('Employee').filtered('isDeleted != true'));
    } catch (exception) {
      console.log('get employeeByPinCode error', exception);
      errorStorageEvent({ action: DBAction.Read, reason: 'getFirstEmployee error', exception });
    }
  }

  public static getTransactionCount() {
    try {
      return RealmManager.getRealmInstance().objects('Transaction').length;
    } catch (exception) {
      console.log('getTransactionCount error', exception);
      checkException(exception);
      return -1;
    }
  }

  public static saveEmployeeActivity(jsonEntity: Record<string, any>) {
    if (!get(jsonEntity, 'user')) {
      return;
    }
    try {
      RealmManager.getRealmInstance().write(() => {
        RealmManager.getRealmInstance().create('EmployeeActivity', jsonEntity, UpdateMode.All);
      });
    } catch (exception) {
      console.log('saveEmployeeActivity Save Error', exception);
      errorStorageEvent({
        action: DBAction.Save,
        reason: 'saveEmployeeActivity error',
        exception,
        privateDataPayload: { employeeActivity: JSONUtils.stringify(jsonEntity) },
      });
      checkException(exception);
    }
  }

  public static getEmployeeActivity() {
    try {
      return RealmManager.getRealmInstance().objects<EmployeeActivityType>('EmployeeActivity');
    } catch (exception) {
      console.log('getEmployeeActivityError', exception);
      errorStorageEvent({ action: DBAction.Read, reason: 'getEmployeeActivity error', exception });
      checkException(exception);
    }
  }

  public static deleteEmployeeActivity() {
    const employeeActivities = DAL.getEmployeeActivity();
    try {
      if (Boolean(employeeActivities)) {
        RealmManager.getRealmInstance().write(() => {
          RealmManager.getRealmInstance().delete(employeeActivities);
        });
      }
    } catch (exception) {
      console.log('deleteEmployeeActivityError', exception);
      errorStorageEvent({ action: DBAction.Delete, reason: 'deleteEmployeeActivity error', exception });
      checkException(exception);
    }
  }

  public static saveExcludedFirstTimeAction(jsonEntity: Record<string, any>) {
    try {
      RealmManager.getRealmInstance().write(() => {
        RealmManager.getRealmInstance().create('ExcludedFirstTimeAction', jsonEntity, UpdateMode.All);
      });
    } catch (exception) {
      console.log('saveExcludedFirstTimeAction Save Error', exception);
      errorStorageEvent({
        action: DBAction.Save,
        reason: 'saveExcludedFirstTimeAction error',
        exception,
        privateDataPayload: { excludedFirstTimeAction: JSONUtils.stringify(jsonEntity) },
      });
      checkException(exception);
    }
  }

  public static getExcludedFirstTimeActionByTransactionId(transactionId: string) {
    try {
      return head(
        RealmManager.getRealmInstance().objects<ExcludedFirstTimeActionType>('ExcludedFirstTimeAction').filtered('transactionId == $0', transactionId)
      );
    } catch (exception) {
      console.log('getExcludedFirstTimeActionByTransactionIdError', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getExcludedFirstTimeActionByTransactionId error',
        exception,
        privateDataPayload: { transactionId },
      });
      checkException(exception);
    }
  }

  // Product
  public static saveProduct(jsonEntity: any) {
    try {
      const DEFAULT_PRODUCT = { skuNumber: null, barcode: null, category: null };
      const parseEntity = Object.assign({}, DEFAULT_PRODUCT, jsonEntity);
      const { _id, variationValues, variations, skuNumber, priceBooks, ...remain } = parseEntity;
      const newPriceBooks = getPriceBooks(priceBooks);
      const variationValuesJson = JSON.stringify(variationValues);
      const variationsJson = JSON.stringify(variations);
      RealmManager.getRealmInstance().write(() => {
        RealmManager.getRealmInstance().create(
          'Product',
          {
            productId: _id,
            variationValuesJson,
            variationsJson,
            skuId: skuNumber,
            priceBooks: newPriceBooks,
            ...remain,
          },
          UpdateMode.All
        );
      });
    } catch (exception) {
      console.log('Save Product Error', exception);
      errorStorageEvent({
        action: DBAction.Save,
        reason: 'saveProduct error',
        exception,
        privateDataPayload: { product: JSONUtils.stringify(jsonEntity) },
      });
      checkException(exception);
    }
  }

  public static saveProductInBatch(items) {
    try {
      RealmManager.getRealmInstance().write(() => {
        for (const item of items) {
          if (item.isDeleted) {
            const searchItem = DAL.getProductById(item._id);
            if (searchItem) {
              RealmManager.getRealmInstance().delete(searchItem);
            }
          } else {
            // Create or update Product data
            const DEFAULT_PRODUCT = { skuNumber: null, barcode: null, category: null };
            const parseEntity = Object.assign({}, DEFAULT_PRODUCT, item);
            const { _id, variationValues, variations, priceBooks, skuNumber, ...remain } = parseEntity;
            const newPriceBooks = getPriceBooks(priceBooks);
            RealmManager.getRealmInstance().create(
              'Product',
              {
                productId: _id,
                variationValuesJson: JSON.stringify(variationValues),
                variationsJson: JSON.stringify(variations),
                skuId: skuNumber,
                priceBooks: newPriceBooks,
                tags: [],
                ...remain,
              },
              UpdateMode.All
            );
          }
        }
      });
    } catch (exception) {
      console.log('saveProductInBatch Error', exception, exception.message);
      errorStorageEvent({
        action: DBAction.Save,
        reason: 'saveProductInBatch error',
        exception,
        privateDataPayload: { products: JSONUtils.stringify(items) },
      });
      checkException(exception);
    }
  }

  public static saveProductInBatchNoCopy(items) {
    try {
      RealmManager.getRealmInstance().write(() => {
        for (const item of items) {
          if (item.isDeleted) {
            const searchItem = DAL.getProductById(item._id);
            if (searchItem) {
              RealmManager.getRealmInstance().delete(searchItem);
            }
          } else {
            // Create or update Product data
            const DEFAULT_PRODUCT = { skuNumber: null, barcode: null, category: null };
            const parseEntity = Object.assign({}, DEFAULT_PRODUCT, item);
            const { variationValues, variations, priceBooks, skuNumber } = parseEntity;
            const newPriceBooks = getPriceBooks(priceBooks);
            parseEntity.productId = parseEntity._id;
            parseEntity.variationValuesJson = JSON.stringify(variationValues);
            parseEntity.variationsJson = JSON.stringify(variations);
            parseEntity.skuId = skuNumber;
            parseEntity.priceBooks = newPriceBooks;
            parseEntity.tags = [];

            RealmManager.getRealmInstance().create('Product', parseEntity, UpdateMode.All);
          }
        }
      });
    } catch (exception) {
      console.log('saveProductInBatch Error', exception, exception.message);
      errorStorageEvent({
        action: DBAction.Save,
        reason: 'saveProductInBatch error',
        exception,
        privateDataPayload: { products: JSONUtils.stringify(items) },
      });
      checkException(exception);
    }
  }

  public static deleteProductById(id: string) {
    const item = DAL.getProductById(id);
    if (item !== undefined) {
      try {
        RealmManager.getRealmInstance().write(() => {
          RealmManager.getRealmInstance().delete(item);
        });
      } catch (exception) {
        console.log('delete product error', exception);
        errorStorageEvent({
          action: DBAction.Delete,
          reason: 'deleteProductById error',
          exception,
          privateDataPayload: { productId: id },
        });
        checkException(exception);
      }
    }
  }

  public static getProductById(id: string): ProductType {
    try {
      return RealmManager.getRealmInstance().objectForPrimaryKey<ProductType>('Product', id);
    } catch (exception) {
      console.log('getProductById error', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getProductById error',
        exception,
        privateDataPayload: { productId: id },
      });
      checkException(exception);
    }
  }

  public static getProductsWithThumbnail(): Results<ProductType> {
    try {
      return RealmManager.getRealmInstance().objects<ProductType>('Product').filtered('hasThumbnail == true');
    } catch (exception) {
      console.log('getProductsWithThumbnail error', exception);
      errorStorageEvent({ action: DBAction.Read, reason: 'getProductsWithThumbnail error', exception });
      checkException(exception);
    }
  }

  public static getProductByBarcode(barcode: string): ProductType {
    try {
      // The products support multiple bacode, so need to search with all the conditions.
      return head(
        RealmManager.getRealmInstance()
          .objects<ProductType>('Product')
          .filtered('barcode == $0 || barcode like $1 || barcode like $2 || barcode like $3', barcode, `${barcode},*`, `*,${barcode}`, `*,${barcode},*`)
      );
    } catch (exception) {
      console.log('getProductByBarCodeError', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getProductByBarcode error',
        exception,
        privateDataPayload: { barcode: JSONUtils.stringify(barcode) },
      });
      checkException(exception);
    }
  }

  public static getProductByParentId(parentId: string): Results<ProductType> {
    try {
      return RealmManager.getRealmInstance().objects<ProductType>('Product').filtered('parentProductId == $0', parentId);
    } catch (exception) {
      console.log('getProductByParentIdError', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getProductByParentId error',
        exception,
        privateDataPayload: { parentId: JSONUtils.stringify(parentId) },
      });
      checkException(exception);
    }
  }

  public static searchProduct(keyword: string, includeParentProducts: boolean, allowEmptyKewyword?: boolean): Results<ProductType> {
    try {
      if (allowEmptyKewyword && keyword === '') {
        return RealmManager.getRealmInstance().objects<ProductType>('Product');
      }
      if (!includeParentProducts) {
        return RealmManager.getRealmInstance()
          .objects<ProductType>('Product')
          .filtered('(title CONTAINS[c] $0 || skuId CONTAINS[c] $1 || barcode CONTAINS[c] $2 ) && ' + DAL.sellableProductsQuery(), keyword, keyword, keyword);
      }
      return RealmManager.getRealmInstance()
        .objects<ProductType>('Product')
        .filtered('title CONTAINS[c] $0 || skuId CONTAINS[c] $1 || barcode CONTAINS[c] $2', keyword, keyword, keyword);
    } catch (exception) {
      console.log('searchProductError', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'searchProduct error',
        exception,
        privateDataPayload: { keyword: JSONUtils.stringify(keyword), includeParentProducts, allowEmptyKewyword },
      });
      checkException(exception);
    }
  }

  public static searchProductsByPage(
    pageNo: number,
    pageSize: number,
    keyword: string,
    includeParentProducts: boolean,
    allowEmptyKewyword?: boolean
  ): ProductType[] {
    try {
      let result;
      if (allowEmptyKewyword && isEmpty(keyword)) {
        result = RealmManager.getRealmInstance().objects<ProductType>('Product');
      } else if (!includeParentProducts) {
        result = RealmManager.getRealmInstance()
          .objects<ProductType>('Product')
          .filtered('(title CONTAINS[c] $0 || skuId CONTAINS[c] $1 || barcode CONTAINS[c] $2 ) && ' + DAL.sellableProductsQuery(), keyword, keyword, keyword);
      } else {
        result = RealmManager.getRealmInstance()
          .objects<ProductType>('Product')
          .filtered('title CONTAINS[c] $0 || skuId CONTAINS[c] $1 || barcode CONTAINS[c] $2', keyword, keyword, keyword);
      }
      const finalResult = [];
      if (result) {
        for (let index = pageNo * pageSize; index < result.length; index++) {
          finalResult.push(result[index]);
          if (finalResult.length === pageSize) {
            break;
          }
        }
      }
      return finalResult;
    } catch (exception) {
      console.log('searchProductsByPage', exception, exception.message);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'searchProductsByPage error',
        exception,
        privateDataPayload: { keyword: JSONUtils.stringify(keyword), includeParentProducts, allowEmptyKewyword },
      });
      checkException(exception);
    }
  }

  public static sellableProductsQuery() {
    return "(trackInventory != true || productType == 'Simple' || parentProductId != null || isChild == true)";
  }

  public static getSellableProductsByPage(pageNo: number, pageSize: number, condition: SearchProductsCondition = {}): ProductType[] {
    const { keyword, includeParentProducts = false, allowEmptyKewyword = false, barcode } = condition;
    try {
      let result;
      if (!isEmpty(keyword)) {
        if (allowEmptyKewyword && keyword === '') {
          result = RealmManager.getRealmInstance().objects<ProductType>('Product').sorted('title');
        } else if (!includeParentProducts) {
          result = RealmManager.getRealmInstance()
            .objects<ProductType>('Product')
            .filtered('(title CONTAINS[c] $0 || skuId CONTAINS[c] $1 || barcode CONTAINS[c] $2 ) && ' + DAL.sellableProductsQuery(), keyword, keyword, keyword)
            .sorted('title');
        } else {
          result = RealmManager.getRealmInstance()
            .objects<ProductType>('Product')
            .filtered('title CONTAINS[c] $0 || skuId CONTAINS[c] $1 || barcode CONTAINS[c] $2', keyword, keyword, keyword)
            .sorted('title');
        }
      } else if (!isEmpty(barcode)) {
        result = [
          head(
            RealmManager.getRealmInstance()
              .objects<ProductType>('Product')
              .filtered('barcode == $0 || barcode like $1 || barcode like $2 || barcode like $3', barcode, `${barcode},*`, `*,${barcode}`, `*,${barcode},*`)
          ),
        ];
      } else {
        result = RealmManager.getRealmInstance().objects<ProductType>('Product').filtered(DAL.sellableProductsQuery()).sorted('title');
      }

      const finalResult = [];
      if (result) {
        for (let index = pageNo * pageSize; index < result.length; index++) {
          finalResult.push(result[index]);
          if (finalResult.length === pageSize) {
            break;
          }
        }
      }
      return finalResult;
    } catch (exception) {
      console.log('getSellableProductsByPageError', exception);
      errorStorageEvent({ action: DBAction.Read, reason: 'getSellableProductsByPage error', exception });
      checkException(exception);
    }
  }

  public static getEditableProductsByPage(pageNo: number, pageSize: number, exceptList: string[], keyword?: string): ProductType[] {
    try {
      let result;
      if (!isEmpty(keyword)) {
        // 过滤掉productId在exceptList的product
        result = RealmManager.getRealmInstance()
          .objects<ProductType>('Product')
          .filtered(
            '(title CONTAINS[c] $0 || skuId CONTAINS[c] $1 || barcode CONTAINS[c] $2) && (isChild != true || parentProductId == null) && NOT (productId IN $3)',
            keyword,
            keyword,
            keyword,
            exceptList
          )
          .sorted('title');
      } else {
        result = RealmManager.getRealmInstance()
          .objects<ProductType>('Product')
          .filtered('(isChild != true || parentProductId == null) && NOT (productId IN $0)', exceptList)
          .sorted('title');
      }

      const finalResult = [];
      if (result) {
        for (let index = pageNo * pageSize; index < result.length; index++) {
          finalResult.push(result[index]);
          if (finalResult.length === pageSize) {
            break;
          }
        }
      }
      return finalResult;
    } catch (exception) {
      console.log('getEditableProductsByPageError', exception);
      errorStorageEvent({ action: DBAction.Read, reason: 'getEditableProductsByPage error', exception });
      checkException(exception);
    }
  }

  public static getSellableProduct(): Results<ProductType> {
    try {
      return RealmManager.getRealmInstance().objects<ProductType>('Product').filtered(DAL.sellableProductsQuery());
    } catch (exception) {
      console.log('getSellableProductError', exception);
      errorStorageEvent({ action: DBAction.Read, reason: 'getSellableProduct error', exception });
      checkException(exception);
    }
  }

  public static getEditableProduct(): Results<ProductType> {
    try {
      return RealmManager.getRealmInstance().objects<ProductType>('Product').filtered('isChild != true || parentProductId == null');
    } catch (exception) {
      console.log('getEditableProduct', exception);
      errorStorageEvent({ action: DBAction.Read, reason: 'getEditableProduct error', exception });
      checkException(exception);
    }
  }

  public static searchEditableProduct(keyword: string): Results<ProductType> {
    try {
      return RealmManager.getRealmInstance()
        .objects<ProductType>('Product')
        .filtered(
          '(title CONTAINS[c] $0 || skuId CONTAINS[c] $1 || barcode CONTAINS[c] $2) && (isChild != true || parentProductId == null)',
          keyword,
          keyword,
          keyword
        );
    } catch (exception) {
      console.log('searchProductError', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'searchEditableProduct error',
        exception,
        privateDataPayload: { productKeyword: JSONUtils.stringify(keyword) },
      });
      checkException(exception);
    }
  }

  // Price Books
  public static savePriceBook(jsonEntity: any) {
    try {
      RealmManager.getRealmInstance().write(() => {
        RealmManager.getRealmInstance().create('PriceBook', jsonEntity, UpdateMode.All);
      });
    } catch (exception) {
      console.log('Save PriceBook Error', exception);
      errorStorageEvent({
        action: DBAction.Save,
        reason: 'savePriceBook error',
        exception,
        privateDataPayload: { priceBook: JSONUtils.stringify(jsonEntity) },
      });
      checkException(exception);
    }
  }

  public static saveSyncPriceBooks(jsonEntity: any[]) {
    if (isEmpty(jsonEntity) || !isArray(jsonEntity)) {
      return;
    }
    try {
      RealmManager.getRealmInstance().write(() => {
        for (const item of jsonEntity) {
          if (item.isDeleted) {
            const searchItem = head(RealmManager.getRealmInstance().objects<PriceBookType>('PriceBook').filtered('id == $0', item.id));
            if (searchItem !== undefined) {
              RealmManager.getRealmInstance().delete(searchItem);
            }
          } else {
            RealmManager.getRealmInstance().create('PriceBook', item, UpdateMode.All);
          }
        }
      });
    } catch (exception) {
      console.log('saveSyncPriceBooks', exception);
      errorStorageEvent({
        action: DBAction.Save,
        reason: 'saveSyncPriceBooks',
        exception,
        privateDataPayload: { priceBook: JSONUtils.stringify(jsonEntity) },
      });
      checkException(exception);
    }
  }

  public static deletePriceBookById(id: string) {
    try {
      const item = DAL.getPriceBookById(id);
      if (item !== undefined) {
        RealmManager.getRealmInstance().write(() => {
          RealmManager.getRealmInstance().delete(item);
        });
      }
    } catch (exception) {
      console.log('deletePriceBookByIdError', exception);
      errorStorageEvent({
        action: DBAction.Delete,
        reason: 'deletePriceBookById error',
        exception,
        privateDataPayload: { priceBookId: JSONUtils.stringify(id) },
      });
      checkException(exception);
    }
  }

  public static getPriceBookById(id: string): PriceBookType {
    try {
      return head(RealmManager.getRealmInstance().objects<PriceBookType>('PriceBook').filtered('id == $0', id));
    } catch (exception) {
      console.log('getPriceBookByIdError', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getPriceBookById error',
        exception,
        privateDataPayload: { priceBookId: JSONUtils.stringify(id) },
      });
      checkException(exception);
    }
  }

  public static getPromotions(): Results<PromotionType> {
    try {
      return RealmManager.getRealmInstance().objects<PromotionType>('Promotion').filtered('isEnabled == true').sorted('ordering', false);
    } catch (exception) {
      console.log('getPromotions error: ', exception);
      errorStorageEvent({ action: DBAction.Read, reason: 'getPromotions error', exception });
      checkException(exception);
    }
  }

  public static savePromotion(jsonEntity: any) {
    try {
      const DEFAULT_PROMOTION = { requiredProducts: [], conditions: [] };
      let appliedStores = [];
      if (Boolean(jsonEntity.appliedStores)) {
        appliedStores = jsonEntity.appliedStores;
      }
      let validDays = [];
      if (Boolean(jsonEntity.validDays)) {
        validDays = jsonEntity.validDays;
      }

      RealmManager.getRealmInstance().write(() => {
        const promotion = Object.assign({}, DEFAULT_PROMOTION, jsonEntity, {
          appliedStores,
          validDays,
          promotionId: jsonEntity._id,
        });
        RealmManager.getRealmInstance().create('Promotion', promotion, UpdateMode.All);
      });
    } catch (exception) {
      console.log('savePromotion error : ', exception);
      errorStorageEvent({
        action: DBAction.Save,
        reason: 'savePromotion error',
        exception,
        privateDataPayload: { promotion: JSONUtils.stringify(jsonEntity) },
      });
      checkException(exception);
    }
  }

  public static saveSyncPromotions(jsonEntity: any[]) {
    if (isEmpty(jsonEntity) || !isArray(jsonEntity)) {
      return;
    }
    try {
      RealmManager.getRealmInstance().write(() => {
        for (const item of jsonEntity) {
          if (item.isDeleted) {
            const searchItem = head(RealmManager.getRealmInstance().objects<PromotionType>('Promotion').filtered('promotionId == $0', item._id));

            if (searchItem !== undefined) {
              RealmManager.getRealmInstance().delete(searchItem);
            }
          } else {
            const DEFAULT_PROMOTION = { requiredProducts: [], conditions: [] };
            let appliedStores = [];
            if (Boolean(item.appliedStores)) {
              appliedStores = item.appliedStores;
            }
            let validDays = [];
            if (Boolean(item.validDays)) {
              validDays = item.validDays;
            }
            const promotion = Object.assign({}, DEFAULT_PROMOTION, item, {
              appliedStores,
              validDays,
              promotionId: item._id,
            });
            RealmManager.getRealmInstance().create('Promotion', promotion, UpdateMode.All);
          }
        }
      });
    } catch (exception) {
      console.log('saveSyncPromotions', exception);
      errorStorageEvent({
        action: DBAction.Save,
        reason: 'saveSyncPromotions',
        exception,
        privateDataPayload: { promotion: JSONUtils.stringify(jsonEntity) },
      });
      checkException(exception);
    }
  }

  public static deletePromotionById(id: string) {
    try {
      const item = DAL.getPromotionById(id);
      if (item !== undefined) {
        RealmManager.getRealmInstance().write(() => {
          RealmManager.getRealmInstance().delete(item);
        });
      }
    } catch (exception) {
      console.log('deletePromotionById error: ', exception);
      errorStorageEvent({
        action: DBAction.Delete,
        reason: 'deletePromotionById error',
        exception,
        privateDataPayload: { promotionId: JSONUtils.stringify(id) },
      });
      checkException(exception);
    }
  }

  public static getPromotionById(id: string): PromotionType {
    try {
      return head(RealmManager.getRealmInstance().objects<PromotionType>('Promotion').filtered('promotionId == $0', id));
    } catch (exception) {
      console.log('getPromotionById error: ', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getPromotionById error',
        exception,
        privateDataPayload: { promotionId: JSONUtils.stringify(id) },
      });
      checkException(exception);
    }
  }

  public static getAllPriceBooks(): Results<PriceBookType> {
    try {
      return RealmManager.getRealmInstance().objects<PriceBookType>('PriceBook');
    } catch (exception) {
      console.log('getAllPriceBooks', exception);
      errorStorageEvent({ action: DBAction.Read, reason: 'getAllPriceBooks error', exception });
      checkException(exception);
    }
  }

  // Shift
  public static getLastShift(): ShiftType {
    try {
      return head(RealmManager.getRealmInstance().objects<ShiftType>('Shift').sorted('openTime', true));
    } catch (exception) {
      console.log('getLastShift', exception);
      errorStorageEvent({ action: DBAction.Read, reason: 'getLastShift error', exception });
      checkException(exception);
    }
  }

  public static getNotUploadedShifts(): Results<ShiftType> {
    try {
      return RealmManager.getRealmInstance().objects<ShiftType>('Shift').filtered('closeTime != null AND (isUploaded == null OR isUploaded == false)');
    } catch (exception) {
      console.log('getNotUploadedShifts', exception);
      errorStorageEvent({ action: DBAction.Read, reason: 'getNotUploadedShifts error', exception });
      checkException(exception);
    }
  }

  public static saveShift(jsonEntity: any) {
    try {
      RealmManager.getRealmInstance().write(() => {
        RealmManager.getRealmInstance().create('Shift', jsonEntity, UpdateMode.All);
      });
      return { success: true, reason: '' };
    } catch (exception) {
      console.log('Save Shift Error', exception);
      checkException(exception);
      return { success: false, reason: exception.message };
    }
  }

  public static updateShift(shiftId: string, entity: any) {
    try {
      RealmManager.getRealmInstance().write(() => {
        RealmManager.getRealmInstance().create('Shift', { ...entity, shiftId }, UpdateMode.All);
      });
    } catch (exception) {
      console.log('Update Shift Error', exception);
      errorStorageEvent({
        action: DBAction.Update,
        reason: 'updateShift error',
        exception,
        privateDataPayload: { shiftId: JSONUtils.stringify(shiftId) },
      }); // TODO: enity is realobjct, so skip log
      checkException(exception);
    }
  }

  public static getShiftById(shiftId: string): ShiftType {
    try {
      return RealmManager.getRealmInstance().objectForPrimaryKey<ShiftType>('Shift', shiftId);
    } catch (exception) {
      console.log('getShiftByIdError', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getShiftById error',
        exception,
        privateDataPayload: { shiftId: JSONUtils.stringify(shiftId) },
      });
      checkException(exception);
    }
  }

  // Transaction
  public static saveTransaction(entity: any) {
    try {
      RealmManager.getRealmInstance().write(() => {
        RealmManager.getRealmInstance().create('Transaction', entity, UpdateMode.All);
      });
      return true;
    } catch (exception) {
      const shiftId = get(entity, 'shiftId');
      const orderId = get(entity, 'receiptNumber');
      const transactionId = get(entity, 'transactionId');
      console.log('Save Transaction Error', exception, exception.message);
      errorStorageEvent({
        action: DBAction.Save,
        reason: 'Save Transaction to Local failed',
        exception,
        orderId,
        privateDataPayload: { shiftId: JSONUtils.stringify(shiftId), transactionId, transactionJSON: JSONUtils.stringify(entity) },
      });
      checkException(exception);
      return false;
    }
  }

  public static updateTransaction(transactionId, entity) {
    try {
      RealmManager.getRealmInstance().write(() => {
        RealmManager.getRealmInstance().create('Transaction', { ...entity, transactionId }, UpdateMode.All);
      });
      return true;
    } catch (exception) {
      const orderId = get(entity, 'receiptNumber');
      RNFS.getFSInfo().then(fsInfo => {
        errorTransactionEvent({
          action: SaleFlowAction.SaveTransactiontoLocal,
          orderId,
          exception,
          reason: 'Update Transaction to Local failed',
          transaction: { ...entity, transactionId },
          privateDataPayload: {
            transactionId,
            freeSpaceInMB: (fsInfo.freeSpace / 1024 / 1024).toFixed(2),
          },
        });
      });
      checkException(exception);
      console.log('Update Transaction Error', exception.message);
      return false;
    }
  }

  public static updateTransactionsInBatch(transactions: { transactionId: string }[]) {
    try {
      if (transactions.length > 0) {
        RealmManager.getRealmInstance().write(() => {
          for (const item of transactions) {
            RealmManager.getRealmInstance().create<TransactionType>('Transaction', item, UpdateMode.All);
          }
        });
      }
      return true;
    } catch (exception) {
      console.log('updateTransactionsInBatch Error', exception, exception.message);
      errorStorageEvent({ action: DBAction.Save, reason: 'updateTransactionsInBatch error', exception });
      checkException(exception);
      return false;
    }
  }

  public static updateOpenOrder(entity) {
    // PS-5130 临时方案 数据修复，避免订单中有多个itemType === "Discount"的item
    const items = get(entity, 'items', []);
    if (items.length > 0) {
      for (let index = 0; index < items.length; index++) {
        const item = items[index];
        const subTotal = get(item, 'subTotal', 0);
        const discountInputValue = get(item, 'discountInputValue', 0);
        if (subTotal > 0 && item.itemType === 'Discount' && discountInputValue <= 0) {
          delete item.itemType;
        }
      }
    }
    // -------------------------------------------------------------------------------------
    let receiptNumber = '';
    try {
      const transaction = DAL.getTrancationById(entity.transactionId);
      if (Boolean(transaction)) {
        receiptNumber = transaction.receiptNumber;
      } else {
        receiptNumber = get(entity, 'receiptNumber', '');
      }
      RealmManager.getRealmInstance().write(() => {
        if (transaction && entity) {
          if (!entity.loyaltyDiscounts && transaction.loyaltyDiscounts) {
            RealmManager.getRealmInstance().delete(transaction.loyaltyDiscounts);
          }
          // TODO: entity.customerId = null
          // When we remove the Customer in the Open Order, we need to update the CustomerID stored in the database
          if (!entity.customerId && transaction.customerId) {
            transaction.customerId = undefined;
          }
        }

        RealmManager.getRealmInstance().create('Transaction', { ...entity }, UpdateMode.All);
      });
      return true;
    } catch (exception) {
      console.error('updateOpenOrder error', exception);
      errorStorageEvent({
        action: DBAction.Update,
        reason: 'updateOpenOrder error',
        exception,
        orderId: receiptNumber,
        privateDataPayload: {
          receiptNumber,
          transactionId: entity.transactionId,
          transaction: TransactionHelper.serializeTransaction(entity),
        },
      });
      checkException(exception);
      return false;
    }
  }

  public static deleteTransactionById(transactionId) {
    try {
      const transaction = DAL.getTransactionById(transactionId);
      if (Boolean(transaction)) {
        RealmManager.getRealmInstance().write(() => {
          RealmManager.getRealmInstance().delete(transaction);
        });
      }
      return true;
    } catch (exception) {
      console.log('deleteTransactionByIdError', exception);
      errorStorageEvent({
        action: DBAction.Delete,
        reason: 'deleteTransactionById error',
        exception,
        privateDataPayload: { transactionId: JSONUtils.stringify(transactionId) },
      });
      checkException(exception);
      return false;
    }
  }

  public static deleteOpenOrderById(transactionId) {
    try {
      const transaction = DAL.getTransactionById(transactionId);
      if (transaction && transaction.isOpen) {
        RealmManager.getRealmInstance().write(() => {
          RealmManager.getRealmInstance().delete(transaction);
        });
      }
      return true;
    } catch (exception) {
      console.log('deleteOpenOrderByIdError', exception);
      errorStorageEvent({
        action: DBAction.Delete,
        reason: 'deleteOpenOrderById error',
        exception,
        privateDataPayload: { transactionId: JSONUtils.stringify(transactionId) },
      });
      checkException(exception);
      return false;
    }
  }

  public static getTransactionsWithEmptyReceiptNumber(): Results<TransactionType> {
    try {
      return RealmManager.getRealmInstance()
        .objects<TransactionType>('Transaction')
        .filtered('isOpen != true && receiptNumber == null')
        .sorted('createdDate', false);
    } catch (exception) {
      console.log(exception);
      errorStorageEvent({ action: DBAction.Read, reason: 'getTransactionsWithEmptyReceiptNumber error', exception });
      checkException(exception);
    }
  }

  // the situation of MRS Is taken into account here, and this should be the situation that was taken into account before, which is a bug
  // MRS Can cause POS locally stored orders that do not belong to the current register, which can cause problems when taking MaxReceiptSequence
  public static getMaxReceiptSequenceInfo(registerId: string): TransactionType {
    try {
      return head(
        RealmManager.getRealmInstance()
          .objects<TransactionType>('Transaction')
          .filtered('sequenceNumber != null AND isOpen != true AND registerId == $0', registerId)
          .sorted('sequenceNumber', true)
      );
    } catch (exception) {
      console.log('getMaxReceiptSequenceInfo', exception);
      errorStorageEvent({ action: DBAction.Read, reason: 'getMaxReceiptSequenceInfo error', exception });
      checkException(exception);
    }
  }

  public static getFirstTransaction(registerId: string): TransactionType {
    try {
      return head(
        RealmManager.getRealmInstance()
          .objects<TransactionType>('Transaction')
          .filtered('sequenceNumber != null AND isOpen != true AND registerId == $0', registerId)
          .sorted('createdDate', false)
      );
    } catch (exception) {
      console.log('getFirstTransaction', exception.message);
      errorStorageEvent({ action: DBAction.Read, reason: 'getFirstTransaction error', exception });
      checkException(exception);
    }
  }

  public static getTransactionsByDateRange(registerId: string, start: Date, end: Date = new Date()): Results<TransactionType> {
    try {
      return RealmManager.getRealmInstance()
        .objects<TransactionType>('Transaction')
        .filtered('createdDate >= $0 AND createdDate <= $1 AND isCancelled == false AND registerId == $2 AND isOpen != true', start, end, registerId);
    } catch (exception) {
      console.log('getTransactionsByDateRange', exception.message);
      errorStorageEvent({ action: DBAction.Read, reason: 'getTransactionsByDateRange error', exception });
      checkException(exception);
    }
  }

  public static getMaxReceiptInvoiceSeqNumber(registerId: string): TransactionType {
    try {
      return head(
        RealmManager.getRealmInstance()
          .objects<TransactionType>('Transaction')
          .filtered('invoiceSeqNumber != null AND isOpen != true AND registerId == $0', registerId)
          .sorted('invoiceSeqNumber', true)
      );
    } catch (exception) {
      console.log('getMaxReceiptInvoiceSeqNumber', exception);
      errorStorageEvent({ action: DBAction.Read, reason: 'getMaxReceiptInvoiceSeqNumber error', exception });
      checkException(exception);
    }
  }

  public static getLastTakeawayOrderInCurShift(registerId: string, currentShiftId: string): TransactionType {
    try {
      return head(
        RealmManager.getRealmInstance()
          .objects<TransactionType>('Transaction')
          .filtered(
            'salesChannel == $0 AND isOpen != true AND registerId == $1 AND transactionType == $2 AND isOnlineOrder != true AND shiftId == $3',
            SalesChannelType.TAKEAWAY,
            registerId,
            TransactionFlowType.Sale,
            currentShiftId
          )
          .sorted('createdDate', true)
      );
    } catch (exception) {
      console.log('getLastTakeawayOrderInCurShift error --> ', exception.message);
      errorStorageEvent({ action: DBAction.Read, reason: 'getLastTakeawayOrderInCurShift error', exception });
      checkException(exception);
    }
  }

  public static getTransactionList(): Results<TransactionType> {
    try {
      return RealmManager.getRealmInstance()
        .objects<TransactionType>('Transaction')
        .filtered('isOpen != true && shippingType == null && isOnlineOrder != true')
        .sorted('createdDate', true);
    } catch (exception) {
      console.log('getTransactionList error', exception);
      errorStorageEvent({ action: DBAction.Read, reason: 'getTransactionList error', exception });
      checkException(exception);
    }
  }

  public static getAllTransactionCount(): number {
    try {
      return RealmManager.getRealmInstance().objects<TransactionType>('Transaction').length;
    } catch (exception) {
      console.log('getTransactionList error', exception);
      errorStorageEvent({ action: DBAction.Read, reason: 'getTransactionList error', exception });
      checkException(exception);
    }
  }

  public static getFixingTransactionList(): Results<TransactionType> {
    try {
      return RealmManager.getRealmInstance().objects<TransactionType>('Transaction').filtered('isOpen != true').sorted('createdDate', false);
    } catch (exception) {
      console.log('getFixingTransactionList error', exception);
      errorStorageEvent({ action: DBAction.Read, reason: 'getFixingTransactionList error', exception });
      checkException(exception);
    }
  }

  public static getTransactionListByPage(pageNo, pageSize): TransactionType[] {
    try {
      const result = RealmManager.getRealmInstance()
        .objects<TransactionType>('Transaction')
        .filtered('isOpen != true && shippingType == null && isOnlineOrder != true')
        .sorted('createdDate', true);
      const finalResult = [];
      for (let index = pageNo * pageSize; index < result.length; index++) {
        finalResult.push(result[index]);
        if (finalResult.length === pageSize) {
          break;
        }
      }
      return finalResult;
    } catch (exception) {
      console.log('getTransactionList error', exception);
      errorStorageEvent({ action: DBAction.Read, reason: 'getTransactionList error', exception });
      checkException(exception);
    }
  }

  public static getOpenOrderList(): Results<TransactionType> {
    try {
      return RealmManager.getRealmInstance()
        .objects<TransactionType>('Transaction')
        .filtered('isOpen == true && isDeleted != true')
        .sorted('createdDate', true);
    } catch (exception) {
      console.log('getOpenOrderList error', exception);
      errorStorageEvent({ action: DBAction.Read, reason: 'getOpenOrderList error', exception });
      checkException(exception);
    }
  }

  public static getMrsOpenOrderList(): Results<TransactionType> {
    try {
      return RealmManager.getRealmInstance().objects<TransactionType>('Transaction').filtered('isOpen == true && mrs == true && isDeleted != true');
    } catch (exception) {
      console.log('getMrsOpenOrderList error', exception);
      errorStorageEvent({ action: DBAction.Read, reason: 'getMrsOpenOrderList error', exception });
      checkException(exception);
    }
  }

  public static deleteMrsOpenOrders() {
    try {
      const mrsOpenOrders = RealmManager.getRealmInstance().objects<TransactionType>('Transaction').filtered('isOpen == true && mrs == true');
      const plainOrderList = [];

      if (mrsOpenOrders && mrsOpenOrders.length > 0) {
        // upload logs
        for (const order of mrsOpenOrders) {
          const plainOrder = JSONUtils.realmObjectToJSObject(order);
          if (plainOrder) {
            plainOrder.isDeleted = true;
            plainOrderList.push(plainOrder);
          }
        }
        RealmManager.getRealmInstance().write(() => {
          RealmManager.getRealmInstance().delete(mrsOpenOrders);
        });
      }
      return plainOrderList;
    } catch (exception) {
      console.log('deleteMrsOpenOrders error', exception);
      errorStorageEvent({ action: DBAction.Delete, reason: 'deleteMrsOpenOrders error', exception });
      checkException(exception);
      return null;
    }
  }

  public static getMRSNotSyncedOpenOrders(): TransactionType[] {
    try {
      const trans = RealmManager.getRealmInstance().objects<TransactionType>('Transaction').filtered('isOpen == true && isDeleted != true && mrs != true');
      return reduce(
        trans,
        (list, cur) => {
          const data = JSONUtils.realmObjectToJSObject(cur, 'getMRSNotSyncedOpenOrders');
          if (data) {
            list.push(data);
          }
          return list;
        },
        []
      );
    } catch (exception) {
      console.log('getMRSNotSyncedOpenOrders error', exception);
      errorStorageEvent({ action: DBAction.Read, reason: 'getMRSNotSyncedOpenOrders error', exception });
      checkException(exception);
      return [];
    }
  }

  public static getTransactionsByIds(ids: string[]): TransactionType[] {
    try {
      const transactionList: TransactionType[] = [];
      ids.forEach(id => {
        const trx = RealmManager.getRealmInstance().objectForPrimaryKey<TransactionType>('Transaction', id);
        transactionList.push(trx);
      });
      return transactionList;
    } catch (exception) {
      console.log(exception);
      checkException(exception);
    }
  }

  public static getReturnsTrxByOriginalReceiptNumber(originalReceiptNumber: string): Results<TransactionType> {
    try {
      return RealmManager.getRealmInstance().objects<TransactionType>('Transaction').filtered('originalReceiptNumber == $0', originalReceiptNumber);
    } catch (exception) {
      console.log('getReturnsTrxByOriginalReceiptNumber error', exception);
      errorStorageEvent({ action: DBAction.Read, reason: 'getReturnsTrxByOriginalReceiptNumber error', exception });
      checkException(exception);
    }
  }

  public static getPreOrderList(): Results<TransactionType> {
    try {
      return RealmManager.getRealmInstance().objects<TransactionType>('Transaction').filtered('transactionType == "PreOrder"').sorted('createdDate', true);
    } catch (exception) {
      console.log('getPreOrderList error', exception);
      errorStorageEvent({ action: DBAction.Read, reason: 'getPreOrderList error', exception });
      checkException(exception);
    }
  }

  /**
   * @deprecated typo! use getTransactionById(id: string)
   */
  public static getTrancationById(id: string): TransactionType {
    try {
      return RealmManager.getRealmInstance().objectForPrimaryKey<TransactionType>('Transaction', id);
    } catch (exception) {
      console.log('getTrancationById', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getTrancationById error',
        exception,
        privateDataPayload: { trasactionId: JSONUtils.stringify(id) },
      });
      checkException(exception);
    }
  }

  public static getTransactionById(id: string): TransactionType {
    try {
      return RealmManager.getRealmInstance().objectForPrimaryKey<TransactionType>('Transaction', id);
    } catch (exception) {
      console.log('getTransactionById', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getTransactionById error',
        exception,
        privateDataPayload: { transactionId: JSONUtils.stringify(id) },
      });
      checkException(exception);
    }
  }

  public static searchTrxBykeyword(keyword: string): Results<TransactionType> {
    try {
      return RealmManager.getRealmInstance()
        .objects<TransactionType>('Transaction')
        .filtered('isOnlineOrder != true && (receiptNumber CONTAINS $0 || items.sn CONTAINS $0)', keyword);
    } catch (exception) {
      console.log('searchTrxBykeyword', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'searchTrxBykeyword error',
        exception,
        privateDataPayload: { keyword: JSONUtils.stringify(keyword) },
      });
      checkException(exception);
    }
  }

  public static searchCompleteTrxBykeyword(keyword: string): Results<TransactionType> {
    try {
      return RealmManager.getRealmInstance()
        .objects<TransactionType>('Transaction')
        .filtered(
          'isOnlineOrder != true && isOpen != true && (receiptNumber CONTAINS $0 || items.sn CONTAINS $0 || originalReceiptNumber  CONTAINS $0)',
          keyword
        )
        .sorted('createdDate', true)
        .snapshot();
    } catch (exception) {
      console.log('searchCompleteTrxBykeyword', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'searchCompleteTrxBykeyword error',
        exception,
        privateDataPayload: { keyword: JSONUtils.stringify(keyword) },
      });
      checkException(exception);
    }
  }

  public static searchTrxByReceiptNumber(receiptNumber: string): Results<TransactionType> {
    try {
      return RealmManager.getRealmInstance().objects<TransactionType>('Transaction').filtered('receiptNumber == $0', receiptNumber);
    } catch (exception) {
      console.log('searchTrxByReceiptNumber', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'searchTrxByReceiptNumber error',
        exception,
        privateDataPayload: { receiptNumber: JSONUtils.stringify(receiptNumber) },
      });
      checkException(exception);
    }
  }

  public static searchOpenOrderByCommentKeyword(keyword: string, allowEmptyKewyword?: boolean): Results<TransactionType> {
    try {
      if (allowEmptyKewyword && keyword === '') {
        return DAL.getOpenOrderList();
      }
      return RealmManager.getRealmInstance()
        .objects<TransactionType>('Transaction')
        .filtered('isOpen == true && isDeleted != true && comment CONTAINS $0', keyword);
    } catch (exception) {
      console.log('searchOpenOrderByCommentKeyword', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'searchOpenOrderByCommentKeyword error',
        exception,
        privateDataPayload: { keyword: JSONUtils.stringify(keyword), allowEmptyKewyword },
      });
      checkException(exception);
    }
  }

  public static searchOpenOrderByTableIdKeyword(keyword: string, allowEmptyKewyword?: boolean): Results<TransactionType> {
    try {
      if (allowEmptyKewyword && keyword === '') {
        return DAL.getOpenOrderList();
      }
      return RealmManager.getRealmInstance()
        .objects<TransactionType>('Transaction')
        .filtered('isOpen == true && isDeleted != true && tableId CONTAINS $0', keyword);
    } catch (exception) {
      console.log('searchOpenOrderByTableIdKeyword', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'searchOpenOrderByTableIdKeyword error',
        exception,
        privateDataPayload: { keyword: JSONUtils.stringify(keyword), allowEmptyKewyword },
      });
      checkException(exception);
    }
  }

  public static hasOpenOrderWithSameTableId(tableId: string, transactionId: string): boolean {
    try {
      const results = RealmManager.getRealmInstance()
        .objects<TransactionType>('Transaction')
        .filtered('transactionId != $0 && isOpen == true && isCancelled == false && tableId == $1', transactionId, tableId);
      if (results.length > 0) {
        return true;
      } else {
        return false;
      }
    } catch (exception) {
      console.log('hasOpenOrderWithSameTableId', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'hasOpenOrderWithSameTableId error',
        exception,
        privateDataPayload: {
          transactionId: JSONUtils.stringify(transactionId),
          tableId: JSONUtils.stringify(tableId),
        },
      });
      checkException(exception);
    }
  }

  public static getNotUploadedTransaction(registerObjectId: string) {
    try {
      return RealmManager.getRealmInstance().objects<TransactionType>('Transaction').filtered(
        // 如果是mrs订单，避免上报其他registerId的订单。
        `(uploadedDate == null OR uploadedDate <= modifiedDate) &&  (isOpen == null || isOpen == false) && isOnlineOrder != true && (mrs != true || isCancelled == true || registerId == '${registerObjectId}')`
      );
    } catch (exception) {
      console.log('getNotUploadedTransactionError', exception);
      errorStorageEvent({ action: DBAction.Read, reason: 'getNotUploadedTransaction error', exception });
      checkException(exception);
      return [];
    }
  }

  public static getTransactionsInThisShift(start: Date, end: Date, registerObjectId: string): TransactionType[] {
    try {
      let result;
      if (!Boolean(end)) {
        // when the preOrderDate do not match the date, it means that the deposit was not paid in the current date period
        result = RealmManager.getRealmInstance()
          .objects<TransactionType>('Transaction')
          .filtered(
            '((createdDate >= $0 AND isCancelled == false) OR (isCancelled == true AND cancelledAt >= $0)) AND registerId == $1 AND (isOpen == null || isOpen == false)',
            start,
            registerObjectId
          )
          .snapshot();
      } else {
        result = RealmManager.getRealmInstance()
          .objects<TransactionType>('Transaction')
          .filtered(
            '((createdDate >= $0 AND createdDate <= $1 AND isCancelled == false) OR (isCancelled == true AND cancelledAt >= $0 AND cancelledAt <= $1)) AND registerId == $2 AND (isOpen == null || isOpen == false)',
            start,
            end,
            registerObjectId
          )
          .snapshot();
      }
      return result;
    } catch (exception) {
      console.log('getTransactionsInThisShift', exception.message);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getTransactionsInThisShift error',
        exception,
        privateDataPayload: { start: JSONUtils.stringify(start), end: JSONUtils.stringify(end), registerObjectId: JSONUtils.stringify(registerObjectId) },
      });
      checkException(exception);
    }
  }

  public static getTransactionsInThisShift2(start: Date, end: Date, registerObjectId: string): TransactionType[] {
    try {
      let result;
      if (!Boolean(end)) {
        // when the preOrderDate do not match the date, it means that the deposit was not paid in the current date period
        result = RealmManager.getRealmInstance()
          .objects<TransactionType>('Transaction')
          .filtered(
            '((createdDate >= $0 AND isCancelled == false) OR (isCancelled == true AND cancelledAt >= $0)) AND registerId == $1 AND (isOpen == null || isOpen == false)',
            start,
            registerObjectId
          )
          .snapshot();
      } else {
        // preOrderDate
        result = RealmManager.getRealmInstance()
          .objects<TransactionType>('Transaction')
          .filtered(
            '((createdDate >= $0 AND createdDate <= $1 AND isCancelled == false) OR (isCancelled == true AND cancelledAt >= $0 AND cancelledAt <= $1)  OR (preOrderDate >= $0 AND preOrderDate <= $1)) AND registerId == $2 AND (isOpen == null || isOpen == false)',
            start,
            end,
            registerObjectId
          )
          .snapshot();
      }
      return result;
    } catch (exception) {
      console.log('getTransactionsInThisShift', exception.message);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getTransactionsInThisShift error',
        exception,
        privateDataPayload: { start: JSONUtils.stringify(start), end: JSONUtils.stringify(end), registerObjectId: JSONUtils.stringify(registerObjectId) },
      });
      checkException(exception);
    }
  }

  // Transaction Aggregate action Method
  public static getSalesGroupedByPaymentSince(start: Date, end: Date, type: string, registerObjectId: string): SalesGroup {
    try {
      let result;
      if (!Boolean(end)) {
        // when the preOrderDate do not match the date, it means that the deposit was not paid in the current date period
        result = RealmManager.getRealmInstance()
          .objects('Payment')
          .filtered(
            'owner.createdDate >= $0 AND owner.transactionType == $1 AND owner.isCancelled == false AND type != $2 AND owner.registerId == $3',
            start,
            type,
            'Voucher',
            registerObjectId
          );
      } else {
        result = RealmManager.getRealmInstance()
          .objects('Payment')
          .filtered(
            'owner.createdDate >= $0 AND owner.createdDate <= $1 AND owner.transactionType == $2 AND owner.isCancelled == false AND type != $3 AND owner.registerId == $4',
            start,
            end,
            type,
            'Voucher',
            registerObjectId
          );
      }
      const groupList = {};
      chain(result)
        .groupBy(item => {
          return item.paymentMethodId;
        })
        .forEach((value, key) => {
          groupList[key] = {
            totalAmount: sumBy(value, 'amount'),
            totalCashRounded: sumBy(value, 'roundedAmount'),
            count: value.length,
            paymentMethodId: key,
          };
        })
        .value();
      return groupList;
    } catch (exception) {
      console.log('getSalesGroupedByPaymentSince', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getSalesGroupedByPaymentSince error',
        exception,
        privateDataPayload: {
          start: JSONUtils.stringify(start),
          end: JSONUtils.stringify(end),
          type: JSONUtils.stringify(type),
        },
      });
      checkException(exception);
    }
  }

  // Count the total of all orders in Shift
  public static getTotalAmountByTotalSince(start: Date, end: Date, type: string = TransactionFlowType.Sale) {
    try {
      let payments;
      if (!Boolean(end)) {
        // when the preOrderDate do not match the date, it means that the deposit was not paid in the current date period
        payments = RealmManager.getRealmInstance()
          .objects('Payment')
          .filtered('owner.createdDate >= $0 AND owner.transactionType == $1 AND owner.isCancelled == false', start, type);
      } else {
        payments = RealmManager.getRealmInstance()
          .objects('Payment')
          .filtered('owner.createdDate >= $0 AND owner.createdDate <= $1 AND owner.transactionType == $2 AND owner.isCancelled == false', start, end, type);
      }

      const transactionIds = [];
      let totalAmountOfTotal = 0;
      let totalVoucherAmount = 0;
      forEach(payments, paymentItem => {
        if (paymentItem.type == 'Voucher') {
          totalVoucherAmount += get(paymentItem, 'amount', 0);
        }
        const linkings = paymentItem.linkingObjects('Transaction', 'payments');
        forEach(linkings, transactionItem => {
          const transactionId = get(transactionItem, 'transactionId');
          if (indexOf(transactionIds, transactionId) < 0) {
            transactionIds.push(transactionId);
            const totalAmount = get(transactionItem, 'total', 0);
            totalAmountOfTotal += totalAmount;
          }
        });
      });
      return totalAmountOfTotal - totalVoucherAmount;
    } catch (exception) {
      console.log('getTotalAmountByTotalSince error', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getTotalAmountByTotalSince error',
        exception,
        privateDataPayload: {
          start: JSONUtils.stringify(start),
          end: JSONUtils.stringify(end),
          type: JSONUtils.stringify(type),
        },
      });
      checkException(exception);
    }
  }

  // Transaction Actual amount Aggregate action Method
  public static getActualSalesGroupedByPaymentSince(start: Date, end: Date, type: string, registerObjectId: string): SalesGroup {
    try {
      let result;
      if (!Boolean(end)) {
        // when the preOrderDate do not match the date, it means that the deposit was not paid in the current date period
        result = RealmManager.getRealmInstance()
          .objects('Payment')
          .filtered(
            'owner.createdDate >= $0 AND owner.transactionType == $1 AND owner.isCancelled == false AND type != $2 AND (owner.preOrderDate == null OR owner.preOrderDate >= $0 OR (owner.preOrderDate < $0 AND isDeposit == false)) AND owner.registerId == $3',
            start,
            type,
            'Voucher',
            registerObjectId
          );
      } else {
        result = RealmManager.getRealmInstance()
          .objects('Payment')
          .filtered(
            'owner.createdDate >= $0 AND owner.createdDate <= $1 AND owner.transactionType == $2 AND owner.isCancelled == false AND type != $3 AND (owner.preOrderDate == null OR (owner.preOrderDate >= $0 AND owner.preOrderDate <= $1) OR (owner.preOrderDate < $0 AND isDeposit == false)) AND owner.registerId == $4',
            start,
            end,
            type,
            'Voucher',
            registerObjectId
          );
      }
      const groupList = {};
      chain(result)
        .groupBy(item => {
          return item.paymentMethodId;
        })
        .forEach((value, key) => {
          groupList[key] = {
            totalAmount: sumBy(value, 'amount'),
            totalCashRounded: sumBy(value, 'roundedAmount'),
            count: value.length,
            paymentMethodId: key,
          };
        })
        .value();
      return groupList;
    } catch (exception) {
      console.log('getActualSalesGroupedByPaymentSince error', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getActualSalesGroupedByPaymentSince error',
        exception,
        privateDataPayload: {
          start: JSONUtils.stringify(start),
          end: JSONUtils.stringify(end),
          type: JSONUtils.stringify(type),
        },
      });
      checkException(exception);
    }
  }

  // Transaction Aggregate action Method
  public static getDepositGroupedByPaymentSince(start: Date, end: Date, registerObjectId: string): SalesGroup {
    try {
      let result;
      if (!Boolean(end)) {
        result = RealmManager.getRealmInstance()
          .objects('Payment')
          .filtered(
            'owner.preOrderDate >= $0 AND owner.transactionType == $1 AND isDeposit == true AND owner.isCancelled == false AND type != $2 AND owner.registerId == $3',
            start,
            TransactionFlowType.PreOrder,
            'Voucher',
            registerObjectId
          );
      } else {
        result = RealmManager.getRealmInstance()
          .objects('Payment')
          .filtered(
            'owner.preOrderDate >= $0 AND owner.preOrderDate <= $1 AND owner.transactionType == $2 AND isDeposit == true AND owner.isCancelled == false AND type != $3 AND owner.registerId == $4',
            start,
            end,
            TransactionFlowType.PreOrder,
            'Voucher',
            registerObjectId
          );
      }
      const groupList = {};
      chain(result)
        .groupBy(item => {
          return item.paymentMethodId;
        })
        .forEach((value, key) => {
          groupList[key] = {
            totalAmount: sumBy(value, 'amount'),
            totalCashRounded: sumBy(value, 'roundedAmount'),
            count: value.length,
            paymentMethodId: key,
          };
        })
        .value();
      return groupList;
    } catch (exception) {
      console.log('getDepositGroupedByPaymentSince', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getDepositGroupedByPaymentSince error',
        exception,
        privateDataPayload: { start: JSONUtils.stringify(start), end: JSONUtils.stringify(end) },
      });
      checkException(exception);
    }
  }

  // Transaction Aggregate action Method
  public static getSalesGroupedByLoyaltySince(start: Date, end: Date, type: string, registerObjectId: string): SalesItem {
    try {
      let result;
      if (!Boolean(end)) {
        result = RealmManager.getRealmInstance()
          .objects('LoyaltyDiscount')
          .filtered(
            'owner.createdDate >= $0 AND owner.transactionType == $1 AND owner.isCancelled == false AND owner.registerId == $2',
            start,
            type,
            registerObjectId
          );
      } else {
        result = RealmManager.getRealmInstance()
          .objects('LoyaltyDiscount')
          .filtered(
            'owner.createdDate >= $0 AND owner.createdDate <= $1 AND owner.transactionType == $2 AND owner.isCancelled == false AND owner.registerId == $3',
            start,
            end,
            type,
            registerObjectId
          );
      }
      let totalAmount = 0;
      chain(result)
        .forEach((value, key) => {
          totalAmount += value.displayDiscount;
        })
        .value();
      return {
        totalAmount,
        totalCashRounded: 0,
        count: result.length,
        paymentMethodId: DefaultPaymentOptionType.Loyalty,
      };
    } catch (exception) {
      console.log('getSalesGroupedByLoyaltySince', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getSalesGroupedByLoyaltySince error',
        exception,
        privateDataPayload: {
          start: JSONUtils.stringify(start),
          end: JSONUtils.stringify(end),
          type: JSONUtils.stringify(type),
        },
      });
      checkException(exception);
    }
  }

  /* Get deposits summary is related to preorder, whcih doesn't supported currently
    public static getDepositSince(start: string, end: string) {
    } */

  public static getDiscountedTransactionFrom(start: Date, end: Date, registerObjectId: string) {
    try {
      if (start === null) return {};
      if (end === null) {
        end = new Date();
      }
      const result: Results<TransactionType> = RealmManager.getRealmInstance()
        .objects<TransactionType>('Transaction')
        .filtered(
          'createdDate >= $0 AND createdDate <= $1 AND isCancelled == false AND discount > 0 AND (isOpen == null || isOpen == false) AND registerId == $2',
          start,
          end,
          registerObjectId
        );
      const groupList = {};
      chain(result)
        .groupBy(item => {
          return item.transactionType;
        })
        .forEach((currentItem, key) => {
          groupList[key] = {
            amount: sumBy(currentItem, 'discount'),
            count: currentItem.length,
          };
        })
        .value();
      return groupList;
    } catch (exception) {
      console.log('getDiscountedTransactionFrom error', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getDiscountedTransactionFrom error',
        exception,
        privateDataPayload: { start: JSONUtils.stringify(start), end: JSONUtils.stringify(end) },
      });
      checkException(exception);
    }
  }

  /* Get all local transactionId which between start and end */
  public static getTransactionIdsFrom(start: Date, end: Date, isBeepOrder: boolean, registerObjectId: string) {
    try {
      if (start === null) return [];
      if (end === null) {
        end = new Date();
      }

      const filterBeepOrder = isBeepOrder ? ' AND owner.isOnlineOrder == true' : ' AND owner.isOnlineOrder != true';
      const payments = RealmManager.getRealmInstance()
        .objects('Payment')
        .filtered(
          `(owner.createdDate >= $0 AND owner.createdDate <= $1 AND owner.isCancelled == false${filterBeepOrder} AND owner.registerId == $2) OR (owner.cancelledAt >= $0 AND owner.cancelledAt <= $1${filterBeepOrder} AND owner.registerId == $2)`,
          start,
          end,
          registerObjectId
        );
      const transactions = [];
      forEach(payments, paymentItem => {
        const linkings = paymentItem.linkingObjects('Transaction', 'payments');
        forEach(linkings, transactionItem => {
          const transactionId = get(transactionItem, 'transactionId');
          const transactionType = get(transactionItem, 'transactionType', 'Sale');
          const isCancelled = get(transactionItem, 'isCancelled', false);
          if (findIndex(transactions, transaction => transaction.transactionId == transactionId) < 0) {
            transactions.push({ transactionId, transactionType, isCancelled });
          }
        });
      });
      return transactions;
    } catch (exception) {
      console.log('getTransactionIdsFrom ERROR : ', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getTransactionIdsFrom error',
        exception,
        privateDataPayload: { start: JSONUtils.stringify(start), end: JSONUtils.stringify(end), isBeepOrder },
      });
      checkException(exception);
    }
  }

  public static getTransactionIdsByTime(start: Date, end: Date) {
    try {
      const registerTransactions = [];
      const beepTransactions = [];
      if (start === null) return { registerTransactions, beepTransactions };
      if (end === null) {
        end = new Date();
      }
      const payments = RealmManager.getRealmInstance()
        .objects('Payment')
        .filtered(
          '(owner.createdDate >= $0 AND owner.createdDate <= $1 AND owner.isCancelled == false) OR (owner.cancelledAt >= $0 AND owner.cancelledAt <= $1)',
          start,
          end
        );
      forEach(payments, paymentItem => {
        const linkings = paymentItem.linkingObjects('Transaction', 'payments');
        forEach(linkings, transactionItem => {
          const transactionId = get(transactionItem, 'transactionId', '');
          const receiptNumber = get(transactionItem, 'receiptNumber', '');
          const isOnlineOrder = get(transactionItem, 'isOnlineOrder');
          if (Boolean(isOnlineOrder)) {
            if (findIndex(beepTransactions, transaction => transaction.transactionId == transactionId) < 0) {
              beepTransactions.push({ transactionId, receiptNumber });
            }
          } else {
            if (findIndex(registerTransactions, transaction => transaction.transactionId == transactionId) < 0) {
              registerTransactions.push({ transactionId, receiptNumber });
            }
          }
        });
      });
      return { registerTransactions: registerTransactions.sort(), beepTransactions: beepTransactions.sort() };
    } catch (exception) {
      console.log('getTransactionIdsByTime ERROR : ', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getTransactionIdsByTime error',
        exception,
        privateDataPayload: { start: JSONUtils.stringify(start), end: JSONUtils.stringify(end) },
      });
      checkException(exception);
    }
  }

  public static getTransactionIdsByShiftId(shiftId: string) {
    try {
      const registerTransactions = [];
      const beepTransactions = [];
      if (!Boolean(shiftId)) return { registerTransactions, beepTransactions };
      const payments = RealmManager.getRealmInstance()
        .objects('Payment')
        .filtered('owner.shiftId == $0 OR owner.shiftIdOfPreOrder == $0 OR owner.shiftIdOfCancel == $0', shiftId);
      forEach(payments, paymentItem => {
        const linkings = paymentItem.linkingObjects('Transaction', 'payments');
        forEach(linkings, transactionItem => {
          const transactionId = get(transactionItem, 'transactionId', '');
          const receiptNumber = get(transactionItem, 'receiptNumber', '');
          const isOnlineOrder = get(transactionItem, 'isOnlineOrder');
          if (Boolean(isOnlineOrder)) {
            if (findIndex(beepTransactions, transaction => transaction.transactionId == transactionId) < 0) {
              beepTransactions.push({ transactionId, receiptNumber });
            }
          } else {
            if (findIndex(registerTransactions, transaction => transaction.transactionId == transactionId) < 0) {
              registerTransactions.push({ transactionId, receiptNumber });
            }
          }
        });
      });
      return { registerTransactions: registerTransactions.sort(), beepTransactions: beepTransactions.sort() };
    } catch (exception) {
      console.log('getTransactionIdsByShiftId ERROR : ', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getTransactionIdsByShiftId error',
        exception,
        privateDataPayload: { shiftId: JSONUtils.stringify(shiftId) },
      });
      checkException(exception);
    }
  }

  /* Get all local transactionId which between start and end */
  public static getReceiptNumbersFrom(start: Date, end: Date, isBeepOrder: boolean, registerObjectId: string) {
    try {
      if (start === null) return [];
      if (end === null) {
        end = new Date();
      }

      const filterBeepOrder = isBeepOrder ? ' AND owner.isOnlineOrder == true' : ' AND owner.isOnlineOrder != true';
      const payments = RealmManager.getRealmInstance()
        .objects('Payment')
        .filtered(
          `(owner.createdDate >= $0 AND owner.createdDate <= $1 AND owner.isCancelled == false${filterBeepOrder} AND owner.registerId == $2) OR (owner.cancelledAt >= $0 AND owner.cancelledAt <= $1${filterBeepOrder} AND owner.registerId == $2)`,
          start,
          end,
          registerObjectId
        );
      const receiptNumbers = [];
      forEach(payments, paymentItem => {
        const linkings = paymentItem.linkingObjects('Transaction', 'payments');
        forEach(linkings, transactionItem => {
          let receiptNumber = get(transactionItem, 'receiptNumber');
          if (isEmpty(receiptNumber)) {
            receiptNumber = get(transactionItem, 'transactionId');
          }
          const transactionType = get(transactionItem, 'transactionType', 'Sale');
          const isCancelled = get(transactionItem, 'isCancelled', false);
          if (!isEmpty(receiptNumber) && findIndex(receiptNumbers, id => receiptNumber == id) < 0) {
            receiptNumbers.push({ receiptNumber, transactionType, isCancelled });
          }
        });
      });
      return receiptNumbers;
    } catch (exception) {
      console.log('getReceiptNumbersFrom ERROR : ', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getReceiptNumbersFrom error',
        exception,
        privateDataPayload: { start: JSONUtils.stringify(start), end: JSONUtils.stringify(end), isBeepOrder },
      });
      checkException(exception);
    }
  }

  public static getLoyaltyTransactionFrom(start: Date, end: Date, registerObjectId: string) {
    try {
      if (start === null) return {};
      if (end === null) {
        end = new Date();
      }
      const result: Results<TransactionType> = RealmManager.getRealmInstance()
        .objects<TransactionType>('Transaction')
        .filtered(
          'createdDate >= $0 AND createdDate <= $1 AND isCancelled == false AND loyaltyEarned > 0 AND (isOpen == null || isOpen == false) AND registerId == $2',
          start,
          end,
          registerObjectId
        );
      const groupList = {};
      let totalLoyaltyDiscountCount = 0;
      chain(result)
        .filter(item => {
          return item.transactionType === TransactionFlowType.Sale;
        })
        .forEach(item => {
          if (item.discount - item.loyaltyEarned < 0.001) {
            totalLoyaltyDiscountCount++;
          }
        })
        .groupBy(item => {
          return item.transactionType;
        })
        .forEach((currentItem, key) => {
          groupList[key] = {
            amount: sumBy(currentItem, 'loyaltyEarned'),
            count: currentItem.length,
          };
        })
        .value();

      return { ...groupList, totalLoyaltyDiscountCount };
    } catch (exception) {
      console.log('getDiscountedTransactionFrom', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getLoyaltyTransactionFrom error',
        exception,
        privateDataPayload: { start: JSONUtils.stringify(start), end: JSONUtils.stringify(end) },
      });
      checkException(exception);
    }
  }

  public static getLoyaltyDiscountTransactionFrom(start: Date, end: Date, type: string, registerObjectId: string) {
    try {
      let result;
      if (!Boolean(end)) {
        result = RealmManager.getRealmInstance()
          .objects('LoyaltyDiscount')
          .filtered(
            'owner.createdDate >= $0 AND owner.transactionType == $1 AND owner.isCancelled == false AND (owner.isOpen == null || owner.isOpen == false) AND owner.registerId == $2',
            start,
            type,
            registerObjectId
          );
      } else {
        result = RealmManager.getRealmInstance()
          .objects('LoyaltyDiscount')
          .filtered(
            'owner.createdDate >= $0 AND owner.createdDate <= $1 AND owner.transactionType == $2 AND owner.isCancelled == false AND (owner.isOpen == null || owner.isOpen == false) AND owner.registerId == $3',
            start,
            end,
            type,
            registerObjectId
          );
      }
      const groupList = {};
      chain(result)
        .groupBy(item => {
          return item.type;
        })
        .forEach((value, key) => {
          groupList[key] = {
            totalAmount: sumBy(value, 'spentValue'),
            totalAmountWithoutTax: sumBy(value, 'displayDiscount'),
            count: value.length,
          };
        })
        .value();
      return groupList;
    } catch (exception) {
      console.log('getLoyaltyDiscountTransactionFrom', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getLoyaltyDiscountTransactionFrom error',
        exception,
        privateDataPayload: {
          start: JSONUtils.stringify(start),
          end: JSONUtils.stringify(end),
          type: JSONUtils.stringify(type),
        },
      });
      checkException(exception);
    }
  }

  public static getRoundedAmountSince(start: Date, end: Date, registerObjectId: string) {
    try {
      let result: Results<TransactionType>;
      if (!Boolean(end)) {
        result = RealmManager.getRealmInstance()
          .objects<TransactionType>('Transaction')
          .filtered(
            'createdDate >= $0 AND isCancelled == false AND (isOpen == null || isOpen == false) AND (roundedAmount != null AND roundedAmount != 0) AND registerId == $1',
            start,
            registerObjectId
          );
      } else {
        result = RealmManager.getRealmInstance()
          .objects<TransactionType>('Transaction')
          .filtered(
            'createdDate >= $0 AND createdDate <= $1 AND isCancelled == false AND (isOpen == null || isOpen == false) AND (roundedAmount != null AND roundedAmount != 0) AND registerId == $2',
            start,
            end,
            registerObjectId
          );
      }

      const groupList = {};
      chain(result)
        .groupBy(item => {
          return item.transactionType;
        })
        .forEach((currentItem, key) => {
          groupList[key] = {
            amount: sumBy(currentItem, 'roundedAmount'),
          };
        })
        .value();
      return groupList;
    } catch (exception) {
      console.log('getRoundedAmountSince error', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getRoundedAmountSince error',
        exception,
        privateDataPayload: { start: JSONUtils.stringify(start), end: JSONUtils.stringify(end) },
      });
      checkException(exception);
    }
  }

  public static getServiceChargeSince(start: Date, end: Date, registerObjectId: string) {
    try {
      let result: Results<TransactionType>;
      if (!Boolean(end)) {
        result = RealmManager.getRealmInstance()
          .objects<TransactionType>('Transaction')
          .filtered(
            'createdDate >= $0 AND isCancelled == false AND (isOpen == null || isOpen == false) AND (serviceCharge != null AND serviceCharge != 0) AND registerId == $1',
            start,
            registerObjectId
          );
      } else {
        result = RealmManager.getRealmInstance()
          .objects<TransactionType>('Transaction')
          .filtered(
            'createdDate >= $0 AND createdDate <= $1 AND isCancelled == false AND (isOpen == null || isOpen == false) AND (serviceCharge != null AND serviceCharge != 0) AND registerId == $2',
            start,
            end,
            registerObjectId
          );
      }

      const groupList = {};
      chain(result)
        .groupBy(item => {
          return item.transactionType;
        })
        .forEach((currentItem, key) => {
          groupList[key] = {
            amount: sumBy(currentItem, 'serviceCharge'),
            count: currentItem.length,
          };
        })
        .value();
      return groupList;
    } catch (exception) {
      console.log('getServiceChargeSince error', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getServiceChargeSince error',
        exception,
        privateDataPayload: { start: JSONUtils.stringify(start), end: JSONUtils.stringify(end) },
      });
      checkException(exception);
    }
  }

  public static haveProduct(): boolean {
    try {
      return RealmManager.getRealmInstance().objects<ProductType>('Product').length > 0;
    } catch (exception) {
      console.log('haveProduct error', exception.message);
      errorStorageEvent({ action: DBAction.Read, reason: 'haveProduct error', exception });
      checkException(exception);
      return false;
    }
  }

  public static getTrxAggregatedByType(start: Date, end: Date, registerObjectId: string) {
    try {
      let result: Results<TransactionType>;
      if (!Boolean(end)) {
        result = RealmManager.getRealmInstance()
          .objects<TransactionType>('Transaction')
          .filtered(
            'createdDate >= $0 AND isCancelled == false AND transactionType != [c] $1 AND (isOpen == null || isOpen == false) AND registerId == $2',
            start,
            TransactionFlowType.PreOrder,
            registerObjectId
          );
      } else {
        result = RealmManager.getRealmInstance()
          .objects<TransactionType>('Transaction')
          .filtered(
            'createdDate >= $0 AND createdDate <= $1 AND isCancelled == false AND transactionType != [c] $2 AND (isOpen == null || isOpen == false) AND registerId == $3',
            start,
            end,
            TransactionFlowType.PreOrder,
            registerObjectId
          );
      }
      return chain(result)
        .groupBy(item => {
          return item.transactionType;
        })
        .value();
    } catch (exception) {
      console.log('getTrxAggregatedByType error', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getTrxAggregatedByType error',
        exception,
        privateDataPayload: { start: JSONUtils.stringify(start), end: JSONUtils.stringify(end) },
      });
      checkException(exception);
    }
  }

  public static getTrialAvliableTrxCount(registerObjectId: string): string[] {
    try {
      return RealmManager.getRealmInstance()
        .objects<TransactionType>('Transaction')
        .filtered('isCancelled == false AND registerId == $0', registerObjectId)
        .map(transaction => transaction.transactionId);
    } catch (exception) {
      console.log('getTrialAvliableTrxCount error', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getTrialAvliableTrxCount error',
        exception,
      });
      checkException(exception);
    }
  }

  public static getNotPreOrderTransactionsCount(start: Date, end: Date, registerObjectId: string): number {
    try {
      if (end === null) {
        end = new Date();
      }
      const result = RealmManager.getRealmInstance()
        .objects<TransactionType>('Transaction')
        .filtered(
          'createdDate >= $0 AND createdDate <= $1 AND isCancelled == false AND (isOpen == null || isOpen == false) AND transactionType != [c] $2 AND registerId == $3',
          start,
          end,
          TransactionFlowType.PreOrder,
          registerObjectId
        );
      return result.length;
    } catch (exception) {
      console.log('getNotPreOrderTransactionsCount error', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getNotPreOrderTransactionsCount error',
        exception,
        privateDataPayload: { start: JSONUtils.stringify(start), end: JSONUtils.stringify(end) },
      });
      checkException(exception);
    }
  }

  // Sales Summary of Pay By Cash
  public static getBeepQRCashSales(start: Date, end: Date, registerObjectId: string): SalesGroup {
    try {
      let result;
      if (!Boolean(end)) {
        // when the preOrderDate do not match the date, it means that the deposit was not paid in the current date period
        result = RealmManager.getRealmInstance()
          .objects('Payment')
          .filtered(
            'owner.createdDate >= $0 AND owner.transactionType == $1 AND owner.isCancelled == false AND owner.isOnlineOrder == true AND paymentMethodId == 0 AND owner.registerId == $2',
            start,
            TransactionFlowType.Sale,
            registerObjectId
          );
      } else {
        result = RealmManager.getRealmInstance()
          .objects('Payment')
          .filtered(
            'owner.createdDate >= $0 AND owner.createdDate <= $1 AND owner.transactionType == $2 AND owner.isCancelled == false AND owner.isOnlineOrder == true AND paymentMethodId == 0 AND owner.registerId == $3',
            start,
            end,
            TransactionFlowType.Sale,
            registerObjectId
          );
      }
      const groupList = {};
      chain(result)
        .groupBy(item => {
          return item.paymentMethodId;
        })
        .forEach((value, key) => {
          groupList[key] = {
            totalAmount: sumBy(value, 'amount'),
            totalCashRounded: sumBy(value, 'roundedAmount'),
            count: value.length,
            paymentMethodId: key,
          };
        })
        .value();
      return groupList;
    } catch (exception) {
      console.log('getBeepQRCashSales error', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getBeepQRCashSales error',
        exception,
        privateDataPayload: { start: JSONUtils.stringify(start), end: JSONUtils.stringify(end) },
      });
      checkException(exception);
    }
  }

  public static getCanncelTransactionSummary(start: Date, end: Date, registerObjectId: string) {
    try {
      if (end === null) {
        end = new Date();
      }
      const result: Results<TransactionType> = RealmManager.getRealmInstance()
        .objects<TransactionType>('Transaction')
        .filtered('cancelledAt >= $0 AND cancelledAt <= $1 AND registerId == $2', start, end, registerObjectId);
      return {
        totalCancelledAmount: result.sum('total'),
        count: result.length,
      };
    } catch (exception) {
      console.log('getCanncelTransactionSummary error', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getCanncelTransactionSummary error',
        exception,
        privateDataPayload: { start: JSONUtils.stringify(start), end: JSONUtils.stringify(end) },
      });
      checkException(exception);
    }
  }

  // BeepNotification

  public static upsertBeepNotification(object: Partial<BeepNotificationPlainType>) {
    const orderId = get(object, 'orderId');
    if (!orderId) {
      return;
    }
    try {
      RealmManager.getRealmInstance().write(() => {
        RealmManager.getRealmInstance().create('BeepNotification', object, UpdateMode.All);
      });
    } catch (exception) {
      console.log('upsertBeepNotification error', exception.message);
      errorStorageEvent({
        action: DBAction.Update,
        reason: 'upsertBeepNotification error',
        exception,
        privateDataPayload: { orderId: String(orderId) },
      });
      checkException(exception);
    }
  }

  public static deleteBeepNotificationById(orderId: string) {
    if (!orderId) {
      return false;
    }
    try {
      const data = DAL.getBeepNotificationById(orderId);
      if (data) {
        RealmManager.getRealmInstance().write(() => {
          RealmManager.getRealmInstance().delete(data);
        });
      }
      return true;
    } catch (exception) {
      console.log('deleteBeepNotification error', exception.message);
      errorStorageEvent({
        action: DBAction.Update,
        reason: 'deleteBeepNotification error',
        exception,
        privateDataPayload: { orderId: String(orderId) },
      });
      checkException(exception);
      return false;
    }
  }

  public static getBeepNotificationById(orderId: string) {
    try {
      return RealmManager.getRealmInstance().objectForPrimaryKey<BeepNotificationType>('BeepNotification', String(orderId));
    } catch (exception) {
      console.log('getBeepNotificationById error', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getBeepNotificationById error',
        exception,
        privateDataPayload: { orderId: JSONUtils.stringify(orderId) },
      });
      checkException(exception);
      return null;
    }
  }

  public static getBeepNotifications() {
    try {
      return RealmManager.getRealmInstance()
        .objects<BeepNotificationType>('BeepNotification')
        .filtered('jobs.@count > 0 && (isKitchenPrinted != true || isOrderSummaryPrinted != true)');
    } catch (exception) {
      console.log('getBeepNotifications error', exception);
      errorStorageEvent({ action: DBAction.Read, reason: 'getBeepNotifications error', exception });
      checkException(exception);
      return [];
    }
  }

  // SubOrderNotification
  public static getSubOrderNotificationById(submitId: string) {
    try {
      return RealmManager.getRealmInstance().objectForPrimaryKey<SubOrderNotificationType>('SubOrderNotification', String(submitId));
    } catch (exception) {
      console.log('getSubOrderNotificationById error', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getSubOrderNotificationById error',
        exception,
        privateDataPayload: { submitId: JSONUtils.stringify(submitId) },
      });
      checkException(exception);
      return null;
    }
  }

  public static getSubOrderNotifications() {
    try {
      return RealmManager.getRealmInstance()
        .objects<SubOrderNotificationType>('SubOrderNotification')
        .filtered('jobs.@count > 0 && (isKitchenPrinted != true || isOrderSummaryPrinted != true)');
    } catch (exception) {
      console.log('getSubOrderNotifications error', exception);
      errorStorageEvent({ action: DBAction.Read, reason: 'getSubOrderNotifications error', exception });
      checkException(exception);
      return [];
    }
  }

  public static upsertSubOrderNotification(object: Partial<SubOrderNotificationPlainType>) {
    try {
      RealmManager.getRealmInstance().write(() => {
        RealmManager.getRealmInstance().create('SubOrderNotification', object, UpdateMode.All);
      });
    } catch (exception) {
      console.log('upsertSubOrderNotification', exception);
      errorStorageEvent({
        action: DBAction.Update,
        reason: 'upsertSubOrderNotification error',
        exception,
        privateDataPayload: { submitId: JSONUtils.stringify(object && object.submitId) },
      });
      checkException(exception);
    }
  }

  public static getAllPrintRecords() {
    try {
      const beepNotis = RealmManager.getRealmInstance().objects<BeepNotificationType>('BeepNotification').filtered('jobTitle != null && jobs.@count > 0');

      const subNotis = RealmManager.getRealmInstance()
        .objects<SubOrderNotificationType>('SubOrderNotification')
        .filtered('jobTitle != null && jobs.@count > 0');
      const concated = [...beepNotis, ...subNotis];
      return orderBy(concated, ['transactionDate'], ['desc']);
    } catch (exception) {
      console.log('getAllPrintRecords error', exception);
      errorStorageEvent({ action: DBAction.Read, reason: 'getAllPrintRecords error', exception });
      checkException(exception);
      return [];
    }
  }

  public static deleteLocalPrinterJob() {
    try {
      RealmManager.getRealmInstance().write(() => {
        const localOrders = RealmManager.getRealmInstance().objects('BeepNotification').filtered('isBeepOrder != true');
        RealmManager.getRealmInstance().delete(localOrders);
      });
    } catch (exception) {
      console.log('deleteLocalPrinterJob', exception);
      errorStorageEvent({ action: DBAction.Delete, reason: 'deleteLocalPrinterJob error', exception });
    }
  }

  public static deleteUselessBeepNotis(count: number) {
    try {
      const endDate = moment().subtract(1, 'w').toDate();
      const data = RealmManager.getRealmInstance().objects<BeepNotificationType>('BeepNotification');
      const totalSize = data.length;
      const uselessData = data.filtered(`transactionDate != null AND transactionDate < $0 SORT(transactionDate ASC) LIMIT(${count})`, endDate);
      const deleteSize = uselessData.length;
      if (deleteSize > 0) {
        RealmManager.getRealmInstance().write(() => {
          RealmManager.getRealmInstance().delete(uselessData);
        });
      }
      return {
        remainSize: totalSize - deleteSize,
        deleteSize,
      };
    } catch (exception) {
      console.log('deleteUselessBeepNotis error', exception);
      errorStorageEvent({ action: DBAction.Delete, reason: 'deleteUselessBeepNotis error', exception });
      checkException(exception);
      return null;
    }
  }

  public static deleteUselessSubOrderNotis(count: number) {
    try {
      const endDate = moment().subtract(1, 'w').toDate();
      const data = RealmManager.getRealmInstance().objects<SubOrderNotificationType>('SubOrderNotification');
      const totalSize = data.length;
      const uselessData = data.filtered(`transactionDate != null AND transactionDate < $0 SORT(transactionDate ASC) LIMIT(${count})`, endDate);
      const deleteSize = uselessData.length;
      if (deleteSize > 0) {
        RealmManager.getRealmInstance().write(() => {
          RealmManager.getRealmInstance().delete(uselessData);
        });
      }
      return {
        remainSize: totalSize - deleteSize,
        deleteSize,
      };
    } catch (exception) {
      console.log('deleteUselessSubOrderNotis error', exception);
      errorStorageEvent({ action: DBAction.Delete, reason: 'deleteUselessSubOrderNotis error', exception });
      checkException(exception);
      return null;
    }
  }

  public static getOpenOrderByTableId(id: string) {
    try {
      return RealmManager.getRealmInstance().objects<TransactionType>('Transaction').filtered('isOpen == true && isDeleted != true && tableId == $0', id);
    } catch (exception) {
      console.log('getOpenOrderByTableId error', exception);
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getOpenOrderByTableId error',
        exception,
        privateDataPayload: { tableId: JSONUtils.stringify(id) },
      });
      checkException(exception);
      return [];
    }
  }

  public static saveLog(jsonEntity: Record<string, any>) {
    try {
      RealmManager.getRealmInstance().write(() => {
        RealmManager.getRealmInstance().create('Log', jsonEntity, UpdateMode.All);
      });
    } catch (exception) {
      console.log('save log error');
      checkException(exception);
    }
  }

  public static deleteLogById(id: string) {
    const item = DAL.getLogById(id);
    if (item !== undefined) {
      try {
        RealmManager.getRealmInstance().write(() => {
          RealmManager.getRealmInstance().delete(item);
        });
      } catch (exception) {
        console.log('delete log error', exception);
        checkException(exception);
      }
    }
  }

  public static getLogById(id: string): LogType {
    try {
      return RealmManager.getRealmInstance().objectForPrimaryKey<LogType>('Log', String(id));
    } catch (exception) {
      console.log('get logs error', exception);
      checkException(exception);
    }
  }

  public static getAllLogs(): Results<LogType> {
    try {
      return RealmManager.getRealmInstance().objects<LogType>('Log');
    } catch (exception) {
      console.log('getAllLogs error', exception);
      checkException(exception);
    }
  }

  // ----------------mrs---------------
  public static getTransactionsLogByPid(pid: number): TransactionsLogType {
    let result = null;
    try {
      result = RealmManager.getRealmInstance().objectForPrimaryKey<TransactionsLogType>('TransactionsLog', pid);
    } catch (exception) {
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getTransactionsLogByPid error',
        exception,
        privateDataPayload: { pid: JSONUtils.stringify(pid) },
      });
      console.log('getTransactionsLogByPid error', exception);
      checkException(exception);
    }
    return result;
  }

  public static getTransactionsLogsFromPid(pid: number): TransactionsLogType[] {
    let result = null;
    try {
      result = RealmManager.getRealmInstance().objects<TransactionsLogType>('TransactionsLog').filtered('pid >= $0', pid);
    } catch (exception) {
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getTransactionsLogsFromPid error',
        exception,
        privateDataPayload: { pid: JSONUtils.stringify(pid) },
      });
      console.log('getTransactionsLogsFromPid error', exception);
      checkException(exception);
    }
    return result;
  }

  public static getTransactionsLogsFromPidWithLimit(pid: number, count: number): TransactionsLogType[] {
    let result = null;
    try {
      result = RealmManager.getRealmInstance().objects<TransactionsLogType>('TransactionsLog').filtered(`pid >= ${pid} SORT(pid ASC) LIMIT(${count})`);
    } catch (exception) {
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getTransactionsLogsFromPidWithLimit error',
        exception,
        privateDataPayload: { pid: JSONUtils.stringify(pid), count: JSONUtils.stringify(count) },
      });
      console.log('getTransactionsLogsFromPidWithLimit error', exception);
      checkException(exception);
    }
    return result;
  }

  public static deleteUselessTransactionsLogs(backupPid: number, count: number) {
    try {
      const data = RealmManager.getRealmInstance().objects<TransactionsLogType>('TransactionsLog').filtered(`pid < ${backupPid} SORT(pid ASC) LIMIT(${count})`);
      const fromPid = data.at(0)?.pid;
      const toPid = data.at(data.length - 1)?.pid;
      if (data.length > 0) {
        RealmManager.getRealmInstance().write(() => {
          RealmManager.getRealmInstance().delete(data);
        });
      }
      return {
        fromPid,
        toPid,
      };
    } catch (exception) {
      errorStorageEvent({
        action: DBAction.Delete,
        reason: 'deleteUselessTransactionsLogs error',
        exception,
        privateDataPayload: { pid: JSONUtils.stringify(backupPid), count: JSONUtils.stringify(count) },
      });
      console.log('deleteUselessTransactionsLogs error', exception);
      checkException(exception);
    }
    return null;
  }

  public static getMaxPidFromTransactionsLog(): number {
    const logs = RealmManager.getRealmInstance().objects<TransactionsLogType>('TransactionsLog').filtered('pid >= 0 SORT(pid DESC) LIMIT(1)');
    if (logs && logs.length === 1) {
      return logs[0].pid;
    }
    return ERROR_PID;
  }

  public static getMaxSnapshotVersionFromTransactionsLog(): number {
    const logs = RealmManager.getRealmInstance().objects<TransactionsLogType>('TransactionsLog').filtered('snapshotVersion != null SORT(pid DESC) LIMIT(1)');
    if (logs && logs.length === 1) {
      return logs[0].snapshotVersion;
    }
    return INIT_SNAPSHOT_VERSION;
  }

  public static getMaxSnapshotPidByVersion(snapshotVersion: number): number {
    if (snapshotVersion) {
      const logs = RealmManager.getRealmInstance()
        .objects<TransactionsLogType>('TransactionsLog')
        .filtered('snapshotVersion == $0 SORT(pid DESC) LIMIT(1)', snapshotVersion);
      if (logs && logs.length === 1) {
        return logs[0].pid;
      }
      return INIT_PID;
    } else {
      return INIT_PID;
    }
  }

  public static getSnapshotIdFromTransactionsLog() {
    const snapshotBlock = RealmManager.getRealmInstance().objects<TransactionsLogType>('TransactionsLog').filtered('snapshotVersion != null SORT(pid DESC)');
    let snapshotVersion = INIT_SNAPSHOT_VERSION,
      snapshotPid = INIT_PID,
      backupPid = INIT_PID;
    if (snapshotBlock.length > 0) {
      snapshotVersion = snapshotBlock.max('snapshotVersion') as number;
      snapshotPid = snapshotBlock.filtered(`snapshotVersion == ${snapshotVersion}`).min('pid') as number;
      const _backupPid = snapshotBlock.filtered(`snapshotVersion == ${snapshotVersion - 1}`).min('pid') as number;
      if (_backupPid) {
        backupPid = _backupPid;
      }
    }

    return { snapshotVersion, backupPid, snapshotPid };
  }

  public static saveTransactionsLog(entity: PlainTransactionsLogType) {
    try {
      RealmManager.getRealmInstance().write(() => {
        RealmManager.getRealmInstance().create('TransactionsLog', entity, UpdateMode.All);
      });
      return true;
    } catch (exception) {
      errorStorageEvent({
        action: DBAction.Save,
        reason: 'saveTransactionsLog error',
        exception,
        privateDataPayload: {
          jsonEntities: JSONUtils.stringify(entity),
        },
      });
      console.log('saveTransactionsLog error', exception);
      checkException(exception);
      return false;
    }
  }
  public static async saveTransactions2BackupRealm(list: TransactionType[]) {
    try {
      if (isEmpty(list)) {
        return true;
      }
      const realm = RealmManager.getRealmInstance();
      if (!realm) return false;

      const backupRealm = await Realm.open({
        ...RealmManager.getRealmConfig(),
        path: 'backup.realm',
      });
      console.log('backupRealm', backupRealm);
      if (backupRealm) {
        const prevOpenOrders = backupRealm.objects<TransactionType>('Transaction').filtered('isOpen == true && mrs == true && isDeleted == true');
        backupRealm.write(() => {
          if (prevOpenOrders.length > 0) {
            console.log('prevOpenOrders', prevOpenOrders);
            backupRealm.delete(prevOpenOrders);
          }
          forEach(list, element => {
            backupRealm.create('Transaction', element, UpdateMode.All);
          });
        });
        console.log('backupRealmTransactions', backupRealm.objects('Transaction'));
        backupRealm.close();
        return true;
      }
      return false;
    } catch (exception) {
      errorStorageEvent({
        action: DBAction.Save,
        reason: 'saveTransactions2BackupRealm error',
        exception,
        privateDataPayload: {
          jsonEntities: JSONUtils.stringify(list),
        },
      });
      console.log('saveTransactions2BackupRealm error', exception);
      checkException(exception);
      return false;
    }
  }
  public static saveTransactionsLogInBatch(entities: PlainTransactionsLogType[]) {
    try {
      RealmManager.getRealmInstance().write(() => {
        for (const entity of entities) {
          RealmManager.getRealmInstance().create('TransactionsLog', entity, UpdateMode.All);
        }
      });
      return true;
    } catch (exception) {
      errorStorageEvent({
        action: DBAction.Save,
        reason: 'saveTransactionsLogInBatch error',
        exception,
        privateDataPayload: {
          jsonEntities: JSONUtils.stringify(entities),
        },
      });
      console.log('saveTransactionsLogInBatch error', exception);
      checkException(exception);
      return false;
    }
  }

  public static deleteAllTransactionsLog() {
    try {
      const allLogs = RealmManager.getRealmInstance().objects('TransactionsLog');
      RealmManager.getRealmInstance().write(() => {
        RealmManager.getRealmInstance().delete(allLogs);
      });
    } catch (exception) {
      errorStorageEvent({
        action: DBAction.Delete,
        reason: 'deleteAllTransactionsLog error',
        exception,
      });
      console.log('deleteAllTransactionsLog error', exception);
      checkException(exception);
    }
  }

  public static saveOrUpdateTransaction(transaction: TransactionType) {
    try {
      RealmManager.getRealmInstance().write(() => {
        RealmManager.getRealmInstance().create('Transaction', transaction, UpdateMode.All);
      });
      return true;
    } catch (exception) {
      errorStorageEvent({
        action: DBAction.Save,
        reason: 'saveOrUpdateTransaction error',
        exception,
        privateDataPayload: {
          transaction: JSONUtils.stringify(transaction),
        },
      });
      console.log('saveOrUpdateTransaction error', exception);
      checkException(exception);
      return false;
    }
  }

  public static saveLogAndTransactions(pid: number, data: ProposeDataType[]) {
    try {
      // @ts-ignore
      const transactionsLog: TransactionsLogType = {
        pid: pid,
        changesLog: JSON.stringify(data),
        createdTime: new Date(),
      };
      RealmManager.getRealmInstance().write(() => {
        RealmManager.getRealmInstance().create('TransactionsLog', transactionsLog, UpdateMode.All);
        data.forEach(item => {
          DAL.updateTransactionByPropose(item);
        });
      });
      return true;
    } catch (exception) {
      errorStorageEvent({
        action: DBAction.Save,
        reason: 'saveLogAndTransactions error',
        exception,
        privateDataPayload: {
          pid: JSONUtils.stringify(pid),
          data: JSONUtils.stringify(data),
        },
      });
      console.log('saveLogAndTransactions error', exception);
      checkException(exception);
      return false;
    }
  }

  public static updateTransactionByPropose(item: ProposeDataType) {
    const { operationType, transaction } = item;
    if (!transaction && operationType === OperationType.DELETE) {
      return;
    }
    const transactionId = transaction.transactionId;
    const transactionRealmObj = RealmManager.getRealmInstance().objectForPrimaryKey<TransactionType>('Transaction', transactionId);

    if (operationType === OperationType.DELETE) {
      if (transactionRealmObj && transactionRealmObj.isOpen) {
        RealmManager.getRealmInstance().delete(transactionRealmObj);
      }
    } else {
      transaction.mrs = true;
      RealmManager.getRealmInstance().create('Transaction', transaction, UpdateMode.All);
    }
  }

  public static saveTransactionsByLog(changesLog: ProposeDataType[]) {
    try {
      let transaction;
      RealmManager.getRealmInstance().write(() => {
        changesLog.forEach(item => {
          transaction = item.transaction;
          if (item.operationType === OperationType.DELETE) {
            if (!transaction) {
              return;
            }
            const trx = DAL.getTransactionById(transaction.transactionId);
            if (trx && trx.isOpen) {
              RealmManager.getRealmInstance().delete(trx);
            }
          } else {
            transaction.mrs = true;
            RealmManager.getRealmInstance().create('Transaction', transaction, UpdateMode.All);
          }
        });
      });
      return true;
    } catch (exception) {
      errorStorageEvent({
        action: DBAction.Save,
        reason: 'saveTransactionsByLog error',
        exception,
        privateDataPayload: {
          changesLog: JSONUtils.stringify(changesLog),
        },
      });
      console.log('saveTransactionsByLog error', exception);
      checkException(exception);
    }
    return false;
  }

  public static deleteTransaction(transactionId: string) {
    try {
      RealmManager.getRealmInstance().write(() => {
        const transaction = RealmManager.getRealmInstance().objectForPrimaryKey('Transaction', transactionId);
        if (Boolean(transaction)) {
          RealmManager.getRealmInstance().delete(transaction);
        }
      });
      return true;
    } catch (exception) {
      errorStorageEvent({
        action: DBAction.Delete,
        reason: 'deleteTransaction error',
        exception,
        privateDataPayload: {
          transactionId: JSONUtils.stringify(transactionId),
        },
      });
      console.log('deleteTransaction error', exception);
      checkException(exception);
      return false;
    }
  }

  public static isInTransaction() {
    return RealmManager.getRealmInstance().isInTransaction;
  }

  public static getPlainTransactionById(id: string) {
    try {
      const trx = DAL.getTransactionById(id);
      if (trx) {
        return JSONUtils.realmObjectToJSObject(trx, 'getPlainTransactionById');
      }
    } catch (exception) {
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getPlainTransactionById error',
        exception,
        privateDataPayload: {
          id,
        },
      });
      console.log('getPlainTransactionById error', exception);
      checkException(exception);
    }
    return null;
  }

  public static getJsTransactionByIds(ids: string[]): TransactionType[] {
    try {
      const transactionList: TransactionType[] = [];
      ids.forEach(id => {
        const trx = RealmManager.getRealmInstance().objectForPrimaryKey<TransactionType>('Transaction', id);
        if (trx) {
          transactionList.push(trx);
        }
      });
      return reduce(
        transactionList,
        (list, cur) => {
          const data = JSONUtils.realmObjectToJSObject(cur, 'getJsTransactionByIds');
          if (data) {
            list.push(data);
          }
          return list;
        },
        []
      );
    } catch (exception) {
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getJsTransactionByIds error',
        exception,
        privateDataPayload: {
          ids,
        },
      });
      console.log('getJsTransactionByIds error', exception);
      checkException(exception);
      return [];
    }
  }

  // #region kds
  public static getKdsTransaction(pageSize: number, startQueryDate: Date, lastQueryDate?: Date) {
    try {
      // basic query
      let query = `createdDate != null && modifiedDate != null && transactionType == '${TransactionFlowType.Sale}'`;
      // kds
      query += ' && cookingStatus != null && pushKdsDate != null';
      // last two hours
      query += ' && pushKdsDate > $0';
      if (lastQueryDate) {
        query += ' && pushKdsDate < $1';
      }
      query += ` SORT(pushKdsDate DESC) LIMIT(${pageSize})`;

      const trans = RealmManager.getRealmInstance().objects<TransactionType>('Transaction').filtered(query, startQueryDate, lastQueryDate);
      const nextQueryDate = last(trans)?.pushKdsDate;
      let data;
      const list = reduce(
        trans,
        (list, cur) => {
          data = undefined;
          if (cur.isOpen || cur.isPayLater) {
            if (cur.modifiedDate > startQueryDate) {
              data = JSONUtils.realmObjectToJSObject(cur, 'getKdsTransaction');
            }
          } else {
            if (cur.pushKdsDate > startQueryDate) {
              data = JSONUtils.realmObjectToJSObject(cur, 'getKdsTransaction');
            }
          }

          if (data) {
            list.push(data);
          }

          return list;
        },
        []
      );

      return { list, nextQueryDate };
    } catch (exception) {
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getKdsTransaction error',
        exception,
        privateDataPayload: {
          lastQueryDate,
          pageSize,
        },
      });
      console.log('getKdsTransaction error', exception);
      checkException(exception);
      return null;
    }
  }

  /**
   * sync pendingTime and servedTime to BO
   * @param registerObjectId
   * @returns
   */
  public static getKDSReportTransaction(registerObjectId: string, receiptNumber?: string) {
    try {
      // own sale transaction
      let query = `transactionType == '${TransactionFlowType.Sale}'`;
      // paid transaction
      if (receiptNumber) {
        query += ` && receiptNumber == '${receiptNumber}'`;
      } else {
        query += ' && receiptNumber != null';
      }
      // transaction uploaded or online Order
      query += ' && (uploadedDate != null || isOnlineOrder == true)';
      // not upload
      query += ' && pushKdsDate != null && servedTime != null && servedTimeUploaded != true';

      return RealmManager.getRealmInstance().objects<TransactionType>('Transaction').filtered(query);
    } catch (exception) {
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getKDSReportTransaction error',
        exception,
      });
      console.log('getKDSReportTransaction error', exception);
      checkException(exception);
      return [];
    }
  }

  // #endregion

  // #region ncs
  public static getNcsTransaction(registerObjectId: string, pageSize: number, cookingStatus: CookingStatus, startQueryDate: Date, lastQueryDate?: Date) {
    try {
      // basic query
      let query = `createdDate != null && transactionType == '${TransactionFlowType.Sale}'`;
      // cooking status
      query += ` && cookingStatus == '${cookingStatus}' && pushKdsDate != null`;
      // no pay later and no open order
      query += ' && isPayLater != true && isOpen != true && isOpenOrder != true && mrs != true';
      // last two hours
      query += ' && pushKdsDate > $0';

      if (lastQueryDate) {
        query += ' && pushKdsDate < $1';
      }
      query += ` SORT(pushKdsDate DESC) LIMIT(${pageSize})`;

      const trans = RealmManager.getRealmInstance().objects<TransactionType>('Transaction').filtered(query, startQueryDate, lastQueryDate);
      const nextQueryDate = last(trans)?.pushKdsDate;
      const list = reduce(
        trans,
        (list, cur) => {
          const data: TransactionType = JSONUtils.realmObjectToJSObject(cur, 'getNcsTransaction');
          if (data) {
            list.push(confirmNcsDate(data, registerObjectId));
          }
          return list;
        },
        []
      );
      return { list, nextQueryDate };
    } catch (exception) {
      errorStorageEvent({
        action: DBAction.Read,
        reason: 'getNcsTransaction error',
        exception,
        privateDataPayload: {
          lastQueryDate,
          cookingStatus,
          pageSize,
        },
      });
      console.log('getNcsTransaction error', exception);
      checkException(exception);
      return null;
    }
  }

  // #endregion
}

const getPriceBooks = priceBooks => {
  return map(priceBooks, priceBookItem => {
    return {
      priceBookId: priceBookItem.priceBookId,
      unitPrice: priceBookItem.unitPrice,
      taxCode: priceBookItem.taxCode,
      minQuant: priceBookItem.min,
      maxQuant: priceBookItem.max,
    };
  });
};

export default DAL;
