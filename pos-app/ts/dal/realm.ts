import { get, head } from 'lodash';
import Realm from 'realm';
import { TransactionFlowType } from '../constants';
import { ShiftType } from '../typings';

import { DBAction, infoStorageEvent } from '../utils/logComponent';
import { getUUIDValue } from '../utils/string';
import AppliedPromotionSchema from './schema/AppliedPromotion';
import BeepNotificationSchema from './schema/BeepNotification';
import BeerDocketSettingsSchema from './schema/BeerDocketSettings';
import CalculationSchema from './schema/Calculation';
import CalculationDiscountItemSchema from './schema/CalculationDiscountItem';
import CalculationOriginalValuesSchema from './schema/CalculationOriginalValues';
import CalculationTaxItemSchema from './schema/CalculationTaxItem';
import EmployeeSchema from './schema/Employee';
import EmployeeActivitySchema from './schema/EmployeeActivity';
import ExcludedFirstTimeActionSchema from './schema/ExcludedFirstTimeAction';
import LogSchema from './schema/Log';
import LoyaltyDiscountSchema from './schema/LoyaltyDiscount';
import PaymentSchema from './schema/Payment';
import PriceBookSchema from './schema/PriceBook';
import PriceBookItemSchema from './schema/PriceBookItem';
import PrinterJobSchema from './schema/PrinterJob';
import ProductSchema from './schema/Product';
import PromotionSchema from './schema/Promotion';
import PromotionConditionSchema from './schema/PromotionCondition';
import PurchasedItemSchema from './schema/PurchasedItem';
import Shift from './schema/Shift';
import ShiftCashPayout from './schema/ShiftCashPayout';
import SpecialDiscountSchema from './schema/SpecialDiscount';
import { SubOrderSchema } from './schema/SubOrder';
import SubOrderNotificationSchema from './schema/SubOrderNotificationSchema';
import TransactionSchema from './schema/Transaction';
import TransactionsLogSchema from './schema/TransactionsLog';

/*
    if there's data migration, put the logic in here

version: Changes logs.
      0: Init for 1.0.0.
      1: Updated in 1.3.0.
        - Change TransactionSchema for open order.
      2: Updated in 1.4.0.
        - Add backOfficeAccess, limitBackOfficeAccess, backOfficeDetailAccesses in EmployeeSchema for employee access control.
      3: Updated in 1.5.0.
        - Add loyaltyEarned in TransactionSchema.
        - Add EmployeeActivitySchema, ExcludedFirstTimeActionSchema for employee activity log.
      4: Updated in 1.5.2
        - Add index for sequenceNumber
      5: Updated in 1.6.0.
        - Add AppliedPromotionSchema, PromotionSchema, PromotionConditionSchema for promotion.
        - Add hasThumbnail to ProductSchema.
      6: Updated in 1.6.7
        - Change validDays in PromotionSchema
      7: Updated in 1.7.0
        - Add BeepNotificationSchema for table ordering.
        - Add LoyaltyDiscountSchema in TransactionSchema
        - Refactor comment in TransactionSchema
        - Add enableCashBack in TransactionSchema
      8: Updated in 1.8.0
        - Add manualApproveInfo in PaymentSchema
      9: Updated in 1.8.2
        - Add LogSchema
        - Add uploaded in BeepNotificationSchema
      10: Updated in 1.10.0
        - Add taxRate in PurchasedItem
      11: Updated in 1.11.0
        - Add type in PaymentType
      12: Updated in 1.12.0
        - Add itemChannel, takeawayCharges in PurchasedItem
        - Add takeawayCharge, takeawayCharges, salesChannel in TransactionSchema
      13: Updated in 1.15.0
        - add 'type' on LoyaltyDiscountType
      14: Updated in 1.16.0
        - add 'shippingType' and 'isPayByCash' on TransactionSchema
      15: Updated in 1.17.1
        - add 'receiptPrinted' on BeepNotificationSchema, to save the printed records of kitchen and receipt respectively
      16: Update in 1.19.1 Pay Later Phase 1
        - Add SubOrderNotificationSchema for Pay Later Suborder
        - add 'isPayLater' on TransactionSchema
        - add 'isOnlineOrder' on TransactionSchema
        - add 'submitId' on PurchasedItem
      17: Update in 1.19.3 Fix Non-Stop printing issue
        - add 'unprintedItemsIds' on BeepNotificationSchema
        - add 'unprintedItemsIds' on SubOrderNotificationSchema
      18: Update in 1.25.0 Improve Printing Error Parse1
        - add PrinterJobSchema
        - add 'receiptNumber' on BeepNotificationSchema
        - add 'isKitchenPrinted' on BeepNotificationSchema
        - add 'isOrderSummaryPrinted' on BeepNotificationSchema
        - add 'jobs' on BeepNotificationSchema and SubOrderNotificationSchema
        - add 'jobTitle' on BeepNotificationSchema and SubOrderNotificationSchema
        - add 'transactionDate' on BeepNotificationSchema and SubOrderNotificationSchema
        - add 'isBeepOrder'on BeepNotificationSchema and SubOrderNotificationSchema
        - delete 'unprintedItemsIds' on BeepNotificationSchema and SubOrderNotificationSchema
      19: Update in 1.27.0
        - update 'isCancelled' default value on PrinterJobSchema
        - update 'printerId' nullable on PrinterJobSchema
        - update 'printerName' nullable on PrinterJobSchema
        * And log to track Shift Report related issue
        - add 'shiftId' on TransactionSchema
        - add 'shiftIdOfPreOrder' on TransactionSchema
        - add 'shiftIdOfCancel' on TransactionSchema
        - add 'version' on Shift
      20: Update in 1.29.0
        * And add transaction soft delete
        - add 'isDeleted' on TransactionSchema
      21: Update in 1.30.0
        - add 'isOrderSummaryPrinted' on SubOrderNotificationSchema
      22: Updated in 1.32.0 multiple register sync
        - Add TransactionsLogSchema
        - Transaction add mrs field
      24: Updated in 1.38.0 BIR Update 1
        - Add SpecialDiscountSchema
        - Transaction add addonBirCompliance field
        - PurchasedItem add isVatExempted field
      25: Updated in 1.38.1 BIR Update 1
        - PurchasedItem add athleteAndCoachDiscount field
        - PurchasedItem add medalOfValorDiscount field
      26: Updated in 1.39.0 BIR Retail
        - ProductSchema add isBasicNecessitiesPH field
        - PurchasedItem add isBasicNecessitiesPH field
      27: Updated in 1.43.0 Beer Dockets
        - ProductSchema add BeerDockSettings field
      28: Updated in 1.45.0 PS-5092 bugfix
        - ProductSchema add rate field (service charge)
      29: Updated in 1.48.0 Beep takeaway items
        - PurchasedItemSchema add isTakeaway and takeawayCharge for beep takeaway item
          Updated in 1.48.0 Online Order BIR
        - Transaction add seqNumberCreatedTime field
        - Transaction add registerNumber field
      30: Updated in 1.50.0 KDS
        - Transaction add cookingStatus field
        - Transaction add cookingStatusType field
        - Transaction add pushKdsDate field
        - Transaction add cookingEndDate field
        - PurchasedItem add pendingDate field
        - PurchasedItem add preparedDate field
        - PurchasedItem add servedDate field
      31: Updated in 1.53.0
          unique promo:
        - PromotionSchema add enabledUniquePromotionCode field
        - AppliedPromotionSchema add uniquePromotionCodeId field
        - AppliedPromotionSchema add uniquePromotionCode field
          refund issue fix:
        - PurchasedItem add loyaltyDiscount field
        - PurchasedItem add zeroRatedSales field
        - PurchasedItem add totalDeductedTax field
      32: Updated in 1.55.0
          CM-4155
        - TransactionSchema add lastRegisterId field
      33: Updated in 1.56.0
          KDS online orders:
        - TransactionSchema add channel field
        - TransactionSchema add status field
        - TransactionSchema add expectDeliveryDateFrom field
        - TransactionSchema add expectDeliveryDateTo field
      34: Updated in 1.59.0
        - EmployeeSchema add isDeleted field
      35: Updated in 1.61.0
          Employee Clock in with FaceCapture:
        - Employee add clockedInWithFaceCapture field
          add a product with Min/Max Price on POS:
        - ProductSchema add maxRefUnitPrice field
        - ProductSchema add minRefUnitPrice field
      36: Updated in 1.62.0 
        - TransactionSchema add takeawayId field
      37: Updated in 1.66.0 
        - SDK upgrade
      38: Updated in 1.68.0 
        - PromotionSchema add isRepeatable
      39: Updated in 1.69.0 
        - TransactionSchema add calculation field
        - PurchasedItem add calculation field
      40: Updated in 1.70.0
        - TransactionSchema add servedTimeUploaded filed
        - TransactionSchema add servedTime filed
      41: Updated in 1.71.0
        - PrinterJobSchema add previousTableId field
      42: Updated in 1.72.0
        - ProductSchema add isSoloParentDiscountApplicable filed
        - SpecialDiscountSchema add soloParentDiscount filed
        - PurchasedItemSchema add soloParentDiscount filed
      43: Updated in 1.73.0
        - PurchasedItem add isServiceChargeNotApplicable field
        - update the CalculationDiscountItem fields type
      44: Updated in 1.75.0
        - TransactionSchema add index of modifiedDate
        - TransactionSchema add index of pushKdsDate
        - TransactionSchema add isOpenOrder field
      45: Updated in 1.77.0
        - TransactionSchema add subOrders field
        - Add SubOrderSchema
      46: Updated in 1.78.0
        - PrinterJobSchema add subOrderFormat field
      47: Updated in 1.79.0
        - TransactionsLog add snapshotVersion field
        - Transaction add index for createdDate
      48: Updated in 1.82.0
        - Calculation add taxes field
        - Transaction add amusementTax field
      50: Updated in 1.87.0
        - Modify the fields promotionCode, status, validFrom and validTo in UniquePromotionCodeInfoType to be optional attributes.
      52: Updated in 1.88.0
        - remove taxrate from loyaltyDiscounts
      53: Update in 1.89.0
        - PurchasedItem add employeeId field
        - PurchasedItem add employeeName field
*/

// @ts-ignore
declare module 'realm' {
  // @ts-ignore
  interface Realm {
    // @ts-ignore
    deleteSafe(object: any): void;
  }
}

// @ts-ignore
Realm.prototype.deleteSafe = function <T>(target: T | T[]): void {
  if (Array.isArray(target)) {
    target.forEach(item => this.deleteSafe(item));
    return;
  }
  if (target == null) return;

  try {
    this.delete(target);
  } catch (error) {
    console.warn('deleteSafe failed:', error);
  }
};

let realm: Realm;
let realmConfig;
export default class RealmManager {
  static getRealmInstance = () => {
    return realm;
  };

  static setRealmInstanceWithStore = storeInfo => {
    realmConfig = {
      schema: [
        EmployeeSchema,
        EmployeeActivitySchema,
        ExcludedFirstTimeActionSchema,
        Shift,
        ShiftCashPayout,
        TransactionSchema,
        PurchasedItemSchema,
        PaymentSchema,
        ProductSchema,
        PriceBookItemSchema,
        PriceBookSchema,
        AppliedPromotionSchema,
        PromotionSchema,
        PromotionConditionSchema,
        BeepNotificationSchema,
        LoyaltyDiscountSchema,
        LogSchema,
        SubOrderNotificationSchema,
        PrinterJobSchema,
        TransactionsLogSchema,
        SpecialDiscountSchema,
        BeerDocketSettingsSchema,
        CalculationSchema,
        CalculationDiscountItemSchema,
        CalculationOriginalValuesSchema,
        CalculationTaxItemSchema,
        SubOrderSchema,
      ],
      schemaVersion: 96,
      migration: (oldRealm, newRealm) => {
        if (oldRealm.schemaVersion < 7) {
          infoStorageEvent({ action: DBAction.Migration, privateDataPayload: { currentDBVersion: oldRealm.schemaVersion, migrateToDBVersion: 7 } });
          const oldTransactions: any = oldRealm.objects('Transaction');
          const newTransactions: any = newRealm.objects('Transaction');
          for (let i = 0; i < oldTransactions.length; i++) {
            const oldTransaction = oldTransactions[i];
            const newTransaction = newTransactions[i];
            const originalComment = oldTransaction.comment;
            newTransaction.comment = null;
            if (newTransaction.transactionType === TransactionFlowType.Return || newTransaction.isCancelled) {
              newTransaction.otherReason = originalComment;
            } else if (newTransaction.isOpen || storeInfo.get('assignTableID')) {
              newTransaction.tableId = originalComment;
            } else if (storeInfo.get('autoOrderId')) {
              newTransaction.pickUpId = originalComment;
            } else {
              newTransaction.comment = originalComment;
            }
          }
        }
        if (oldRealm.schemaVersion < 13) {
          infoStorageEvent({ action: DBAction.Migration, privateDataPayload: { currentDBVersion: oldRealm.schemaVersion, migrateToDBVersion: 13 } });
          const oldTransactions: any = oldRealm.objects('Transaction');
          const newTransactions: any = newRealm.objects('Transaction');
          for (let i = 0; i < oldTransactions.length; i++) {
            const oldTransaction = oldTransactions[i];
            const newTransaction = newTransactions[i];
            const loyaltyDiscounts = oldTransaction.loyaltyDiscounts;
            if (loyaltyDiscounts && loyaltyDiscounts.length > 0) {
              const newLoyaltyDiscounts = [];
              for (let j = 0; j < loyaltyDiscounts.length; j++) {
                const oldLoyaltyDiscount = loyaltyDiscounts[j];
                const newLoyaltyDiscount = {
                  ...oldLoyaltyDiscount,
                  type: Boolean(oldLoyaltyDiscount.loyaltyType) ? oldLoyaltyDiscount.loyaltyType : 'cashback',
                };
                newLoyaltyDiscounts.push(newLoyaltyDiscount);
              }
              newTransaction.loyaltyDiscounts = newLoyaltyDiscounts;
            }
          }
        }
        if (oldRealm.schemaVersion < 16) {
          infoStorageEvent({ action: DBAction.Migration, privateDataPayload: { currentDBVersion: oldRealm.schemaVersion, migrateToDBVersion: 16 } });
          const oldTransactions: any = oldRealm.objects('Transaction');
          const newTransactions: any = newRealm.objects('Transaction');
          for (let i = 0; i < oldTransactions.length; i++) {
            const oldTransaction = oldTransactions[i];
            const newTransaction = newTransactions[i];
            newTransaction.isOnlineOrder = oldTransaction.isPayByCash;
          }
        }

        // update BeepNotification from POS 1.17.0
        if (oldRealm.schemaVersion > 6 && oldRealm.schemaVersion < 18) {
          infoStorageEvent({ action: DBAction.Migration, privateDataPayload: { currentDBVersion: oldRealm.schemaVersion, migrateToDBVersion: 18 } });
          const oldBeepNotis: any = oldRealm.objects('BeepNotification');
          const newBeepNotis: any = newRealm.objects('BeepNotification');

          for (let i = 0; i < oldBeepNotis.length; i++) {
            const oldBeepNoti = oldBeepNotis[i];
            const newBeepNoti = newBeepNotis[i];
            newBeepNoti.isBeepOrder = true;
            newBeepNoti.jobs = [
              {
                jobId: getUUIDValue(),
                printerId: '',
                printerTags: [],
                printerName: '',
                unprintedItemsIds: oldBeepNoti.unprintedItemsIds,
                unprintedDiffItems: [],
                businessType: 'KITCHEN_TICKET',
                errorCode: -1,
                isCancelled: false,
              },
            ];
          }
        }

        // update BeepNotification from POS 1.19.1
        if (oldRealm.schemaVersion > 15 && oldRealm.schemaVersion < 18) {
          infoStorageEvent({ action: DBAction.Migration, privateDataPayload: { currentDBVersion: oldRealm.schemaVersion, migrateToDBVersion: 18 } });
          const oldSubNotis: any = oldRealm.objects('SubOrderNotification');
          const newSubNotis: any = newRealm.objects('SubOrderNotification');

          for (let i = 0; i < oldSubNotis.length; i++) {
            const oldSubNoti = oldSubNotis[i];
            const newSubNoti = newSubNotis[i];
            newSubNoti.isBeepOrder = true;
            newSubNoti.jobs = [
              {
                jobId: getUUIDValue(),
                printerId: '',
                printerTags: [],
                printerName: '',
                unprintedItemsIds: oldSubNoti.unprintedItemsIds,
                unprintedDiffItems: [],
                businessType: 'KITCHEN_TICKET',
                errorCode: -1,
                isCancelled: false,
              },
            ];
          }
        }

        if (oldRealm.schemaVersion < 22) {
          infoStorageEvent({ action: DBAction.Migration, privateDataPayload: { currentDBVersion: oldRealm.schemaVersion, migrateToDBVersion: 22 } });
          const currentShift: ShiftType = head(oldRealm.objects('Shift').sorted('openTime', true));
          const openTime = get(currentShift, 'openTime', '');
          const registerObjectId = storeInfo.get('registerObjectId', '');

          if (openTime && registerObjectId) {
            const oldTransactions: any = oldRealm.objects('Transaction');
            const newTransactions: any = newRealm.objects('Transaction');
            for (let i = 0; i < oldTransactions.length; i++) {
              const oldTransaction = oldTransactions[i];
              if (oldTransaction.createdDate && oldTransaction.createdDate >= openTime) {
                const newTransaction = newTransactions[i];
                newTransaction.registerId = registerObjectId;
              }
            }
          }
        }
      },
    };
    // @ts-ignore
    realm = new Realm(realmConfig);
  };

  static getRealmConfig = () => {
    return realmConfig;
  };
}
