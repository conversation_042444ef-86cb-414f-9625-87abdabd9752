const PurchasedItemSchema = {
  name: 'PurchasedItem',
  properties: {
    adhocDiscount: 'double?',
    discount: 'double?',
    id: 'string?',
    submitId: 'string?',
    itemType: 'string?',
    notes: 'string?',
    options: 'string?',
    orderingValue: 'int?',
    originalItemId: 'string?',
    productId: 'string?',
    pwdDiscount: { type: 'double', default: 0, optional: true }, // PH related  default to 0
    athleteAndCoachDiscount: { type: 'double', default: 0, optional: true }, // PH related  default to 0
    soloParentDiscount: { type: 'double', default: 0, optional: true }, // PH related  default to 0
    medalOfValorDiscount: { type: 'double', default: 0, optional: true }, // PH related  default to 0
    quantity: 'double?',
    seniorDiscount: { type: 'double', default: 0, optional: true }, // PH related  default to 0
    isVatExempted: { type: 'bool', default: false, optional: true }, // PH related  default to false
    isServiceChargeNotApplicable: { type: 'bool', default: false, optional: true }, // PH related  default to false
    isBasicNecessitiesPH: { type: 'bool', default: false, optional: true }, // PH related  default to false
    isSoloParentDiscountApplicable: { type: 'bool', default: false, optional: true }, // PH related  default to false
    loyaltyDiscount: { type: 'double', default: 0, optional: true }, // PH related default to 0
    sn: 'string?',
    subTotal: 'double?',
    tax: 'double?',
    taxableAmount: { type: 'double', default: 0, optional: true }, // PH related  default to 0
    taxCode: 'string?',
    taxRate: 'double?',
    taxExemptAmount: { type: 'double', default: 0, optional: true }, // PH related  default to 0
    zeroRatedSales: { type: 'double', default: 0, optional: true }, // PH related  default to 0
    totalDeductedTax: { type: 'double', default: 0, optional: true }, // PH related  default to 0
    title: 'string?',
    total: 'double?',
    unitPrice: 'double?',
    discountInputValue: { type: 'double', default: 0, optional: true },
    rate: 'double?',
    discountType: 'string?',
    owner: { type: 'linkingObjects', objectType: 'Transaction', property: 'items' },
    promotions: { type: 'list', objectType: 'AppliedPromotion' },
    itemChannel: 'int?',
    takeawayCharges: 'double?',
    cookingStatus: { type: 'string', indexed: true, optional: true },
    pendingDate: 'date?',
    preparedDate: 'date?',
    servedDate: 'date?',
    isTakeaway: 'bool?', // for beep
    takeawayCharge: 'double?', // for beep
    calculation: 'Calculation?',
    kitchenStation: 'string?',
    employeeId: 'string?',
    employeeName: 'string?',
  },
};

export default PurchasedItemSchema;
