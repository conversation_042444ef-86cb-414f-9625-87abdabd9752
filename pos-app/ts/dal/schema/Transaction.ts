const TransactionSchema = {
  name: 'Transaction',
  primaryKey: 'transactionId',
  properties: {
    appVersion: 'string?',
    cancelledAt: 'date?',
    cancelledBy: 'string?',
    // Deprecated   cashTendered: 'double?',
    comment: 'string?',
    createdDate: { type: 'date', indexed: true, optional: true },
    customerId: 'string?',
    loyaltyEarned: 'double?',
    depositAmount: 'double?',
    discount: 'double?',
    // Deprecated emailReceipt: 'bool?',
    employeeId: 'string?',
    headcount: 'int?',
    invoiceSeqNumber: 'int?',
    isDeleted: { type: 'bool', default: false, optional: true },
    isCancelled: { type: 'bool', default: false, optional: true },
    isCompleted: { type: 'bool', default: false, optional: true },
    isOpen: 'bool?',
    isOriginalOnline: 'bool?',
    lastSyncTime: 'date?', // for open order
    manuallyRemovedServiceCharge: 'bool?',
    modifiedDate: { type: 'date', indexed: true, optional: true },
    originalReceiptNumber: 'string?',
    pax: 'int?',
    // Deprecated paymentMethodId:    'int?',
    pickUpDate: 'date?',
    shiftId: 'string?',
    shiftIdOfPreOrder: 'string?',
    shiftIdOfCancel: 'string?',
    preOrderBy: 'string?',
    preOrderDate: 'date?',
    seqNumberCreatedTime: 'date?',
    preOrderId: 'string?',
    pwdCount: 'int?',
    amusementTax: 'double?',
    pwdDiscount: 'double?',
    receiptNumber: { type: 'string', indexed: true, optional: true },
    registerId: 'string?',
    registerNumber: 'int?',
    returnReason: 'string?',
    returnStatus: 'string?',
    roundedAmount: 'double?',
    seniorDiscount: 'double?',
    seniorsCount: 'int?',
    sequenceNumber: { type: 'int', indexed: true, optional: true },
    voidNumber: 'string?',
    serviceCharge: 'double?',
    serviceChargeTax: 'double?',
    serviceChargeRate: 'double?',
    serviceChargeTaxId: 'string?',
    shippingType: 'string?',
    subtotal: 'double?',
    tax: 'double?',
    taxableSales: 'double?',
    taxExemptedSales: 'double?',
    total: 'double?',
    totalDeductedTax: 'double?',
    transactionId: { type: 'string', indexed: true },
    transactionType: 'string?',
    uploadedDate: 'date?',
    zeroRatedSales: 'double?',
    items: { type: 'list', objectType: 'PurchasedItem' },
    payments: { type: 'list', objectType: 'Payment' },
    promotions: { type: 'list', objectType: 'AppliedPromotion' },
    loyaltyDiscounts: { type: 'list', objectType: 'LoyaltyDiscount' },
    calculation: 'Calculation?',
    otherReason: 'string?',
    pickUpId: 'string?',
    tableId: 'string?',
    takeawayCharges: 'double?',
    takeawayCharge: 'double?',
    salesChannel: 'int?',
    isPayByCash: 'bool?',
    isPayLater: 'bool?',
    isOpenOrder: 'bool?',
    isOnlineOrder: 'bool?',
    mrs: 'bool?',
    addonBirCompliance: 'SpecialDiscount?',
    cookingStatus: { type: 'string', indexed: true, optional: true },
    cookingStatusType: 'int?', // order status setting
    pushKdsDate: { type: 'date', indexed: true, optional: true }, // first push kds, pending time
    cookingEndDate: 'date?', // served time on POS
    servedTime: 'date?', // first served time
    servedTimeUploaded: 'bool?', // served time uploaded
    channel: 'int?',
    status: 'string?', // OnlineOrderStatus
    expectDeliveryDateFrom: 'date?', // online order preOrder
    expectDeliveryDateTo: 'date?', // online order preOrder
    lastRegisterId: 'string?',
    takeawayId: 'string?',
    subOrders: { type: 'list', objectType: 'SubOrder', default: [] },
    isMarkedForDeletion: 'bool?', // for mark for deletion
  },
};

export default TransactionSchema;
