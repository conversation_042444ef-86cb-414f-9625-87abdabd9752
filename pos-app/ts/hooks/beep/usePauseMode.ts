import { useCreation } from 'ahooks';
import moment from 'moment';

import { useSelector } from 'react-redux';
import { BeepPauseModeSettings, closeDelivery, openForDelivery, updatePauseMode } from '../../actions';
import { selectStorePauseInfo } from '../../sagas/selector';
import { useActions } from '../redux';

export interface PauseModeType {
  paused: boolean;
  pausedUntil: number;
  reopenTime: string;
}

const usePauseMode = () => {
  const storeInfo: BeepPauseModeSettings = useSelector(selectStorePauseInfo);

  const actions = useActions({ closeDelivery, openForDelivery, updatePauseMode });

  return useCreation(() => {
    const pauseModeInfo = {
      ...storeInfo,
      reopenTime: moment(storeInfo.pausedUntil).format('LT'),
    };
    return {
      closeDelivery: actions.closeDelivery,
      openForDelivery: actions.openForDelivery,
      updatePauseMode: actions.updatePauseMode,
      pauseModeInfo,
    };
  }, [storeInfo, actions]);
};

export default usePauseMode;
