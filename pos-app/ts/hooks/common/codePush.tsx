import React, { useEffect, useState } from 'react';
import { StyleProp, StyleSheet, Text } from 'react-native';
import { currentThemes } from '../../constants';
import DeviceInfoData from '../../utils/deviceInfo';

import { getCurrentVersionInfo } from 'react-native-update/src/core';
import { useSelector } from 'react-redux';
import { selectRegisterObjectId } from '../../sagas/selector';
import { generateLogIdFromRegisterId } from '../../utils/log';
import { useDateTime } from './useTimestamp';

async function checkForUpdate() {
  try {
    const update = await getCurrentVersionInfo();
    console.log('getOTAVersion update', update);
    if (update && update.name) {
      return `${DeviceInfoData.version}(${update.name})`;
    }
  } catch (err) {
    console.log('getOTAVersion Error', err);
  }
  return null;
}

export function useOTAVersion() {
  const [appVersion, setAppVersion] = useState(DeviceInfoData.version);

  useEffect(() => {
    checkForUpdate().then(OTAVersion => {
      if (OTAVersion) {
        setAppVersion(OTAVersion);
      }
    });
  }, []);

  return { appVersion };
}

export function useDeviceInfo() {
  const [infoData, setInfoData] = useState(DeviceInfoData);

  useEffect(() => {
    checkForUpdate().then(OTAVersion => {
      if (OTAVersion) {
        setInfoData(DeviceInfoData);
      }
    });
  }, []);

  return infoData;
}

interface DefaultStyleProps {
  style?: StyleProp<any>;
  country?: string;
}

export function AppVersionLabel(props: DefaultStyleProps) {
  const { appVersion } = useOTAVersion();
  const { country } = props;
  const versionText = country === 'PH' ? `2.7.0 (${appVersion})` : appVersion;
  return <Text style={[styles.versionText, props.style]}>{`Version: ${versionText}`}</Text>;
}

export function AppName(props: DefaultStyleProps) {
  const deviceInfo = useDeviceInfo();

  return <Text style={[styles.versionText, props.style]}>{`App Name: ${deviceInfo.appName}`}</Text>;
}

export function AppId(props: DefaultStyleProps) {
  const deviceInfo = useDeviceInfo();

  return <Text style={[styles.versionText, props.style]}>{`App ID: ${deviceInfo.appId}`}</Text>;
}

export function DeviceModel(props: DefaultStyleProps) {
  const deviceInfo = useDeviceInfo();

  return <Text style={[styles.versionText, props.style]}>{`Device Model: ${deviceInfo.deviceModel}`}</Text>;
}

export function RegisterId(props: DefaultStyleProps) {
  const registerId = useSelector(selectRegisterObjectId);

  return <Text style={[styles.versionText, props.style]}>{`${registerId}`}</Text>;
}

export function SystemVersion(props: DefaultStyleProps) {
  const deviceInfo = useDeviceInfo();

  return <Text style={[styles.versionText, props.style]}>{`System Version: ${deviceInfo.systemVersion}`}</Text>;
}

export function Timestamp(props: DefaultStyleProps) {
  const dateTime = useDateTime();

  return <Text style={[styles.versionText, props.style]}>{`${dateTime}`}</Text>;
}

export function UniqueTimestamp(props: DefaultStyleProps) {
  return <Text style={[styles.versionText, props.style]}>{`${generateLogIdFromRegisterId()}`}</Text>;
}

const styles = StyleSheet.create({
  versionText: {
    color: '#60636B',
    fontSize: currentThemes.fontSize16,
    fontWeight: '400',
  },
});
