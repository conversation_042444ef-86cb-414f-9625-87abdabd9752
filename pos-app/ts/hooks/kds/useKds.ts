import { useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { registerKds, unregisterKds } from '../../actions/kds';
import { selectAuthToken, selectIsKDSPurchasedANDAssigned, selectIsWebsocketLoaded } from '../../sagas/selector';

const useKds = () => {
  const isWebsocketLoaded = useSelector(selectIsWebsocketLoaded);
  const enabledKds = useSelector(selectIsKDSPurchasedANDAssigned);
  const autoToken = useSelector(selectAuthToken);
  const kdsEnabled = useMemo(() => enabledKds && autoToken && isWebsocketLoaded, [enabledKds, autoToken, isWebsocketLoaded]);
  const dispatch = useDispatch();
  // kds switch
  useEffect(() => {
    if (kdsEnabled) {
      dispatch(registerKds());
    }

    return () => {
      dispatch(unregisterKds());
    };
  }, [kdsEnabled]);
};

export default useKds;
