import AsyncStorage from '@react-native-async-storage/async-storage';
import { useEffect } from 'react';
import { LoggingLevel, POSBasicAction } from '../../utils/logComponent';
import { LogSettingOptions } from '../../utils/logComponent/local-logging/libs/NativeFileLogger';
import LocalLogger, { LocalLoggerStorageKey } from '../../utils/logComponent/local-logging/LocalLogger';

export default function useLocalLogger() {
  const start = async () => {
    try {
      const configure = await AsyncStorage.getItem(LocalLoggerStorageKey);
      if (configure) {
        const options: LogSettingOptions = JSON.parse(configure);
        // start local logger
        await LocalLogger.updateLogSetting(options);
        LocalLogger.log({ level: LoggingLevel.Debug, message: 'open app', payload: options, action: POSBasicAction.LAUNCH });
      }
    } catch (ex) {
      console.error('Local Logger start failed: ', ex);
    }
  };

  useEffect(() => {
    start();
    return () => {
      // stop local logger
      console.log('日志结束');
      LocalLogger.updateLogSetting({ enabled: false });
    };
  }, []);
}
