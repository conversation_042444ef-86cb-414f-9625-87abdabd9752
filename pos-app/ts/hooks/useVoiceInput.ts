import { useState, useEffect, useCallback } from 'react';
import Voice, { SpeechRecognizedEvent, SpeechResultsEvent, SpeechErrorEvent } from '@react-native-voice/voice';
import { Platform, PermissionsAndroid } from 'react-native';

export interface VoiceInputState {
  isListening: boolean;
  isRecognizing: boolean;
  transcript: string;
  error: string | null;
  isSupported: boolean;
  hasPermission: boolean;
}

export interface VoiceInputActions {
  startListening: () => Promise<void>;
  stopListening: () => Promise<void>;
  clearTranscript: () => void;
  requestPermissions: () => Promise<boolean>;
}

export const useVoiceInput = (): [VoiceInputState, VoiceInputActions] => {
  const [state, setState] = useState<VoiceInputState>({
    isListening: false,
    isRecognizing: false,
    transcript: '',
    error: null,
    isSupported: true,
    hasPermission: false,
  });

  const requestPermissions = useCallback(async (): Promise<boolean> => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.RECORD_AUDIO, {
          title: 'Microphone Permission',
          message: 'This app needs access to your microphone for voice input to the AI assistant.',
          buttonNeutral: 'Ask Me Later',
          buttonNegative: 'Cancel',
          buttonPositive: 'OK',
        });
        const hasPermission = granted === PermissionsAndroid.RESULTS.GRANTED;
        setState(prev => ({ ...prev, hasPermission }));
        return hasPermission;
      } catch (err) {
        console.warn('Permission request error:', err);
        setState(prev => ({ ...prev, hasPermission: false }));
        return false;
      }
    }
    // iOS permissions are handled automatically when the feature is used
    setState(prev => ({ ...prev, hasPermission: true }));
    return true;
  }, []);

  useEffect(() => {
    const initializeVoice = async () => {
      try {
        await requestPermissions();
        // Check if voice recognition is supported
        if (!(await Voice.isAvailable())) {
          setState(prev => ({
            ...prev,
            isSupported: false,
            hasPermission: false,
            error: 'Speech recognition not supported on this device',
          }));
          return;
        }

        setState(prev => ({ ...prev, isSupported: true }));

        // Request permissions upfront if supported
      } catch (error) {
        console.error('Voice initialization error:', error);
        let errorMessage = 'Speech recognition not available';

        if (error instanceof Error && error.message.includes('SpeechRecognizer$Connection')) {
          errorMessage = 'Speech recognition service not available. Please ensure Google app is installed and updated.';
        }

        setState(prev => ({
          ...prev,
          isSupported: false,
          hasPermission: false,
          error: errorMessage,
        }));
      }
    };

    initializeVoice();

    // Set up voice event listeners
    Voice.onSpeechStart = onSpeechStart;
    Voice.onSpeechRecognized = onSpeechRecognized;
    Voice.onSpeechEnd = onSpeechEnd;
    Voice.onSpeechError = onSpeechError;
    Voice.onSpeechResults = onSpeechResults;
    Voice.onSpeechPartialResults = onSpeechPartialResults;

    return () => {
      // Clean up voice listeners
      Voice.destroy().then(Voice.removeAllListeners);
    };
  }, [requestPermissions]);

  const onSpeechStart = useCallback(() => {
    setState(prev => ({
      ...prev,
      isListening: true,
      isRecognizing: true,
      error: null,
    }));
  }, []);

  const onSpeechRecognized = useCallback((e: SpeechRecognizedEvent) => {
    setState(prev => ({
      ...prev,
      isRecognizing: true,
    }));
  }, []);

  const onSpeechEnd = useCallback(() => {
    setState(prev => ({
      ...prev,
      isListening: false,
      isRecognizing: false,
    }));
  }, []);

  const onSpeechError = useCallback((e: SpeechErrorEvent) => {
    console.error('Speech recognition error:', e.error);
    let errorMessage = 'Speech recognition error';

    // Handle specific Android errors
    if (e.error?.message?.includes('SpeechRecognizer$Connection') || e.error?.message?.includes('Service not registered')) {
      errorMessage = 'Speech recognition service not available. Please ensure Google app is installed and updated.';
    } else if (e.error?.message?.includes('network')) {
      errorMessage = 'Network error. Please check your internet connection.';
    } else if (e.error?.message?.includes('permission')) {
      errorMessage = 'Microphone permission denied';
    } else if (e.error?.message?.includes('no match')) {
      errorMessage = 'No speech detected. Please try again.';
    } else if (e.error?.message) {
      errorMessage = e.error.message;
    }

    setState(prev => ({
      ...prev,
      isListening: false,
      isRecognizing: false,
      error: errorMessage,
    }));
  }, []);

  const onSpeechResults = useCallback((e: SpeechResultsEvent) => {
    const transcript = e.value?.[0] || '';
    setState(prev => ({
      ...prev,
      transcript,
      isListening: false,
      isRecognizing: false,
      error: null,
    }));
  }, []);

  const onSpeechPartialResults = useCallback((e: SpeechResultsEvent) => {
    const partialTranscript = e.value?.[0] || '';
    setState(prev => ({
      ...prev,
      transcript: partialTranscript,
    }));
  }, []);

  const startListening = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, error: null, transcript: '' }));

      // Check if voice recognition is supported
      if (!state.isSupported) {
        setState(prev => ({ ...prev, error: 'Speech recognition not supported on this device' }));
        return;
      }

      // Check permissions first
      if (!state.hasPermission) {
        const hasPermission = await requestPermissions();
        if (!hasPermission) {
          setState(prev => ({ ...prev, error: 'Microphone permission denied' }));
          return;
        }
      }

      await Voice.start('en-US'); // You can make this configurable based on user locale
    } catch (err) {
      console.error('Start listening error:', err);
      let errorMessage = 'Failed to start voice recognition';

      if (err instanceof Error) {
        if (err.message.includes('SpeechRecognizer$Connection') || err.message.includes('Service not registered')) {
          errorMessage = 'Speech recognition service not available. Please ensure Google app is installed and updated.';
        } else if (err.message.includes('network')) {
          errorMessage = 'Network error. Please check your internet connection.';
        } else if (err.message.includes('permission')) {
          errorMessage = 'Microphone permission denied';
        }
      }

      setState(prev => ({
        ...prev,
        error: errorMessage,
        isListening: false,
        isRecognizing: false,
      }));
    }
  }, [state.hasPermission, state.isSupported, requestPermissions]);

  const stopListening = useCallback(async () => {
    try {
      await Voice.stop();
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to stop voice recognition',
        isListening: false,
        isRecognizing: false,
      }));
    }
  }, []);

  const clearTranscript = useCallback(() => {
    setState(prev => ({ ...prev, transcript: '', error: null }));
  }, []);

  const actions: VoiceInputActions = {
    startListening,
    stopListening,
    clearTranscript,
    requestPermissions,
  };

  return [state, actions];
};
