import { call, select } from 'redux-saga/effects';
import { TIMEZONE } from '../../../config';
import { getStoreInfo } from '../../../sagas/printing/common';
import {
  selectEnableLoyalty,
  selectEnablePoints,
  selectEnableStoreCredit,
  selectIndustry,
  selectLocalCountryMap,
  selectReceiptFontSize,
  selectStoreId,
  selectSubscriptionPlan,
  selectTimezone,
} from '../../../sagas/selector';

export function* getReceiptConfig() {
  const {
    companyName,
    storeName,
    address,
    phone,
    currency,
    registerId,
    business,
    country,
    gstIdNo,
    minNo,
    sstIdNo,
    showStoreName,
    showNotes,
    showBarcode,
    showCustomerInfo,
    taxNameOnReceipt,
    poweredBy,
    brn,
    includingTaxInDisplay,
    notes,
    defaultLoyaltyRatio,
    enableCashback,
    birAccredited,
    assignTableID,
    autoOrderId,
    disableCashbackFromPOS,
    disableTitle,
    membershipEnabled,
    serialNo,
    isVATRegistered,
    birAccrInfo,
    birAccrNo,
    ptu,
    ptuDateIssued,
  } = yield call(getStoreInfo);

  const subscriptionPlan: number = yield select(selectSubscriptionPlan);
  const enableLoyalty: boolean = yield select(selectEnableLoyalty);
  const storeId: string = yield select(selectStoreId);
  const industry = yield select(selectIndustry);
  const receiptFontSize = yield select(selectReceiptFontSize);
  const enableStoreCredit = yield select(selectEnableStoreCredit);
  const localCountryMap = yield select(selectLocalCountryMap);
  const enablePoints = yield select(selectEnablePoints);
  const storeTimezone = yield select(selectTimezone);

  return {
    country,
    companyName,
    storeName,
    address,
    phone,
    currency,
    registerId,
    business,
    gstIdNo,
    minNo,
    sstIdNo,
    showStoreName,
    showNotes,
    showBarcode,
    showCustomerInfo,
    taxNameOnReceipt,
    poweredBy,
    brn,
    includingTaxInDisplay,
    notes,
    defaultLoyaltyRatio,
    enableCashback,
    birAccredited,
    assignTableID,
    autoOrderId,
    disableCashbackFromPOS,
    disableTitle,
    membershipEnabled,
    subscriptionPlan,
    enableLoyalty,
    storeId,
    industry,
    receiptFontSize,
    serialNo,
    isVATRegistered,
    birAccrInfo,
    birAccrNo,
    ptu,
    ptuDateIssued,
    enableStoreCredit,
    localCountryMap,
    enablePoints,
    timezone: storeTimezone || TIMEZONE,
  };
}

export type ReceiptConfig = {
  country: string;
  companyName: any;
  storeName: any;
  address: any;
  phone: any;
  currency: any;
  registerId: any;
  business: any;
  gstIdNo: any;
  minNo: any;
  sstIdNo: any;
  showStoreName: boolean;
  showNotes: any;
  showBarcode: any;
  showCustomerInfo: any;
  taxNameOnReceipt: any;
  poweredBy: any;
  brn: any;
  includingTaxInDisplay: any;
  notes: any;
  defaultLoyaltyRatio: any;
  enableCashback: any;
  birAccredited: any;
  assignTableID: any;
  autoOrderId: any;
  disableCashbackFromPOS: any;
  disableTitle: any;
  membershipEnabled: any;
  subscriptionPlan: number;
  enableLoyalty: boolean;
  storeId: string;
  industry: any;
  receiptFontSize: number;
  serialNo: any;
  isVATRegistered: any;
  birAccrInfo: any;
  birAccrNo: any;
  ptu: any;
  ptuDateIssued: any;
  enableStoreCredit: any;
  localCountryMap: any;
  enablePoints: any;
  timezone: string;
};
