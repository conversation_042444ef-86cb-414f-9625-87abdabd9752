import { SagaReturnType, select } from 'redux-saga/effects';

import DAL from '../../../../dal';
import {
  selectEmployeeId,
  selectGstIdNo,
  selectMinNo,
  selectSerialNo,
  selectStoreCity,
  selectStoreCompanyName,
  selectStoreName,
  selectStorePostalCode,
  selectStoreState,
  selectStoreStreet1,
  selectStoreStreet2,
} from '../../../../sagas/selector';
import { getReceiptFontScale, ReceiptFontSize } from '../../../../utils/printer';

export function* getSMXReadingReportConfig() {
  const businessName = yield select(selectStoreCompanyName);
  const storeName = yield select(selectStoreName);

  const street1 = yield select(selectStoreStreet1);
  const street2 = yield select(selectStoreStreet2);
  const city = yield select(selectStoreCity);
  const state = yield select(selectStoreState);
  const postalCode = yield select(selectStorePostalCode);
  const storeAddress = [street1, street2, city, state, postalCode].filter(v => Boolean(v)).join(' ');

  const gstIdNo = yield select(selectGstIdNo);
  const minNo = yield select(selectMinNo);
  const serialNo = yield select(selectSerialNo);
  const employeeId = yield select(selectEmployeeId);
  const employee = DAL.getEmployeeById(employeeId) as any;
  const employeeName = employee ? `${employee.firstName} ${employee.lastName}` : '';

  return {
    businessName,
    storeName,
    storeAddress,
    vatRegTin: `VAT REG TIN: ${gstIdNo}`,
    minNo: `MIN: ${minNo}`,
    serialNo: `SERIAL NO.: ${serialNo}`,
    reportTitle: 'X-Reading',
    employeeName,
    receiptFontScale: getReceiptFontScale(ReceiptFontSize.MEDIUM),
  };
}

export type SMXReadingReportConfig = SagaReturnType<typeof getSMXReadingReportConfig>;
