import { SagaReturnType, call, select } from 'redux-saga/effects';

import { getStoreInfo } from '../../../../sagas/printing/common';
import { selectEmployeeId, selectShiftOpenStatus } from '../../../../sagas/selector';

export function* getShiftReportConfig() {
  const {
    companyName,
    storeName,
    address,
    phone,
    currency,
    registerId,
    gstIdNo,
    minNo,
    enableCashback,
    birAccrNo,
    serialNo,
    ptu,
    country,
    birAccredited,
    isVATRegistered,
  } = yield call(getStoreInfo);

  const shiftOpenStatus = yield select(selectShiftOpenStatus);
  const curEmployeeId = yield select(selectEmployeeId);

  return {
    country,
    companyName,
    storeName,
    address,
    phone,
    currency,
    registerId,
    gstIdNo,
    minNo,
    birAccredited,
    serialNo,
    isVATRegistered,
    birAccrNo,
    ptu,
    shiftOpenStatus,
    curEmployeeId,
    enableCashback,
  };
}

export type ShiftReportConfig = SagaReturnType<typeof getShiftReportConfig>;
