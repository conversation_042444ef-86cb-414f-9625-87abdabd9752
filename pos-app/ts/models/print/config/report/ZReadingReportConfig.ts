import { SagaReturnType, call, select } from 'redux-saga/effects';

import { getStoreInfo } from '../../../../sagas/printing/common';
import { selectIndustry } from '../../../../sagas/selector';

export function* getZReadingReportConfig() {
  const { storeName, registerId, gstIdNo, minNo, birAccrNo, serialNo, ptu, country, birAccredited, isVATRegistered, enableServiceCharge } = yield call(
    getStoreInfo
  );

  const industry = yield select(selectIndustry);

  return {
    country,
    storeName,
    registerId,
    gstIdNo,
    minNo,
    birAccredited,
    serialNo,
    isVATRegistered,
    birAccrNo,
    ptu,
    enableServiceCharge,
    industry,
  };
}

export type ZReadingReportConfig = SagaReturnType<typeof getZReadingReportConfig>;
