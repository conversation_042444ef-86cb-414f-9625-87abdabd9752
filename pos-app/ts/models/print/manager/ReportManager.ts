import { call } from 'redux-saga/effects';
import { PrintObserver } from '../observer/PrintObserver';

import { BeepFlowAction } from '../../../utils/logComponent';
import { SMXReadingReportTask } from '../task/report/SMXReadingReportTask';
import { ZReadingReportTask } from '../task/report/ZReadingReportTask';
import { TaskFactory } from './../task/TaskFactory';

export enum ReportEvent {
  shiftReport = 'CM-6663',
  zReadingReport = 'CM-6664',
  dailyReport = 'CM-6668',
}

export class ReportManager {
  static *printShiftReport(report) {
    if (!report) {
      return;
    }

    const task = TaskFactory.createShiftReportTask(report);
    task.observer = new PrintObserver(null, 'shiftReport', BeepFlowAction.PrintReport);

    yield call([task, task.execute], null);
  }

  static *printDailyReport(dailyReportSummary) {
    if (!dailyReportSummary) {
      return;
    }
    const task = TaskFactory.createDailyReportTask(dailyReportSummary);
    task.observer = new PrintObserver(null, 'dailyReport', BeepFlowAction.PrintReport);
    yield call([task, task.execute], null);
  }

  static *printZReadingReport(report) {
    if (!report) {
      return;
    }
    const task: ZReadingReportTask = TaskFactory.createZReadingReportTask(report);
    task.observer = new PrintObserver(null, 'ZReadingReport', BeepFlowAction.PrintReport);
    yield call([task, task.execute], null);
  }

  static *printSMXReadingReport(report) {
    if (!report) {
      return;
    }
    const task: SMXReadingReportTask = TaskFactory.createSMXReadingReportTask(report);
    task.observer = new PrintObserver(null, 'SMXReadingReport', BeepFlowAction.PrintReport);
    yield call([task, task.execute], null);
  }
}
