import { forEach, get, groupBy, isEmpty, values } from 'lodash';
import moment from 'moment';

import { PrinterConfigType } from '../../../../actions';
import { IsAndroid, IsIOS, ItemChannelType, PrintingBusinessType, SalesChannelType, ShippingType, SubOrderInfoType, t } from '../../../../constants';
import { formatDateString } from '../../../../sagas/printing/common';
import { PurchasedItemType } from '../../../../typings';
import { convertToNumber, getUnNullValue } from '../../../../utils';
import { isBeepQROrder } from '../../../../utils/beep';
import { getReceiptFontScale } from '../../../../utils/printer';
import { KitchenTransaction } from '../../../transaction/KitchenTransaction';
import { KitchenConfig } from '../../config/KitchenConfig';
import { PrinterSetting } from '../../printer/PrinterSetting';
import { KitchenPrintRequestData } from '../../task/AbstractPrintTask';
import { KitchenPrintItem, KitchenPrintItemData } from './KitchenPrintItem';

export const ITEM_MOCK_ID_PREFIX = 'mock_';

enum KitchenBlockHeightEnum {
  HeaderPadding = 44,
  HeaderRow = 48, // date, pax, orderType, employeeName, pickupId
  OrderNumber = 147,
  SummaryTitle = 66, // 45
  ReprintTitle = 64,
  ItemTitle = 51, // 35
  ItemTitleWithDesc = 42.1, // 29
  ItemOption = 42,
  ItemNote = 45, // 31
  TakeawayTitle = 118,
  subOrderSeparator = 98,
  EmployeeName = 49,
  OrderNote = 120,
}

/**
 * Kitchen打印模型, 用于创建打印数据
 */
export class KitchenPrintModel {
  private kitchenItems: KitchenPrintItem[] = [];
  private summaryItems: KitchenPrintItem[] = [];

  readonly needPrintTakeawayItems: boolean;

  readonly isOpenOrder: boolean;
  readonly isOnlineOrder: boolean;

  readonly tableId: string;
  readonly pickUpId: string;

  readonly orderNumberTitle: string;
  readonly orderNumber: string;

  readonly bixOrderNumber: string;
  readonly extraOrderNumber: string;

  readonly orderTypeName: string;

  readonly pax?: string;

  readonly receiptDate: string; // format: YYYY.MM.DD HH:mm:ss
  readonly bixReceiptDate: string;

  readonly noteTitle = 'Note';

  readonly isTakeaway: boolean;
  readonly note: string;

  readonly salesChannel: SalesChannelType;

  readonly employeeName?: string;

  readonly subOrders?: SubOrderInfoType[];

  readonly receiptFontScale: number;
  private readonly config: KitchenConfig;

  private isReprint = false;

  private subOrderFormat = false;

  private isTestReceipt = false;

  public setSubOrderFormat(value: boolean) {
    this.subOrderFormat = value;
  }

  public setIsReprint(value: boolean) {
    this.isReprint = value;
  }

  private previousTableId: string | null = null;

  public setPreviousTableId(value: string) {
    if (value) {
      this.previousTableId = value;
    }
  }

  constructor(transaction: KitchenTransaction, config: KitchenConfig) {
    this.config = config;
    this.isOpenOrder = false;
    this.subOrders = transaction.subOrders;
    this.isOnlineOrder = transaction.isOnlineOrder;
    this.pickUpId = transaction.pickUpId;
    this.tableId = transaction.tableId;

    const orderNumberInfo = this.getOrderNumberInfo(transaction);
    this.orderNumberTitle = orderNumberInfo.orderNumberTitle;
    this.orderNumber = orderNumberInfo.orderNumber;
    this.extraOrderNumber = orderNumberInfo.extraOrderNumber;
    this.bixOrderNumber = orderNumberInfo.bixOrderNumber;

    this.orderTypeName = this.getOrderTypeName(transaction);

    const dateInfo = this.getReceiptDate();
    this.receiptDate = dateInfo.receiptDate;
    this.bixReceiptDate = dateInfo.bixReceiptDate;

    this.isTakeaway = transaction.salesChannel === SalesChannelType.TAKEAWAY;
    this.salesChannel = transaction.salesChannel;

    if (transaction.pax) {
      this.pax = `Pax: ${transaction.pax}`;
    }

    this.isTestReceipt = transaction.isTestReceipt;

    if (this.config.printEmployeeName) {
      if (transaction.isTestReceipt) {
        this.employeeName = 'Employee Name';
      } else {
        this.employeeName = isBeepQROrder(transaction) ? 'QR Order and Pay' : this.config.employeeName;
      }
    }

    this.note = transaction.getNote(config.timezone);
    // offline order : filter all items
    // beep order: only filter the dine items( since the takeaway order has the different format)
    this.needPrintTakeawayItems = (this.isOnlineOrder && transaction.shippingType === ShippingType.DINE_IN) || !this.isOnlineOrder;
    this.receiptFontScale = getReceiptFontScale(config.kitchenDocketFontSize);
  }

  public getKitchenItems(): KitchenPrintItem[] {
    return this.kitchenItems;
  }

  /**
   * 设置KitchenItems
   * @param items PurchasedItems
   * @param config KitchenConfig
   */
  public setKitchenItems(items: PurchasedItemType[]) {
    this.kitchenItems = items.reduce<KitchenPrintItem[]>((result, item) => {
      const kitchenItem = this.getKitchenItemFromPurchasedItem(item);
      if (kitchenItem) {
        result.push(kitchenItem);
      }
      return result;
    }, []);
  }

  public getSummaryItems(): KitchenPrintItem[] {
    return this.summaryItems;
  }

  /**
   * 设置SummaryItems
   * @param items PurchasedItems
   * @param config KitchenConfig
   * @param summaryPrinter string
   */
  public setSummaryItems(items: PurchasedItemType[], summaryPrinter: string) {
    this.summaryItems = items.reduce<KitchenPrintItem[]>((result, item) => {
      const kitchenItem = this.getKitchenItemFromPurchasedItem(item);
      if (kitchenItem) {
        kitchenItem.setSummaryPrinter(summaryPrinter);
        result.push(kitchenItem);
      }
      return result;
    }, []);
  }

  /**
   * 设置SummaryItems
   * @param items PurchasedItems
   * @param config KitchenConfig
   * @param summaryPrinter string
   */
  public setSummaryItemsWithoutPrinter(items: PurchasedItemType[]) {
    this.summaryItems = items.reduce<KitchenPrintItem[]>((result, item) => {
      const kitchenItem = this.getKitchenItemFromPurchasedItem(item);
      if (kitchenItem) {
        result.push(kitchenItem);
      }
      return result;
    }, []);
  }

  private getKitchenItemFromPurchasedItem(item: PurchasedItemType) {
    if (item.itemType) {
      return null;
    }
    const kitchenItem = new KitchenPrintItem(item, this.config);
    kitchenItem.setBixOrderNumber(this.bixOrderNumber);
    kitchenItem.setBixReceiptDate(this.bixReceiptDate);
    return kitchenItem;
  }

  /**
   * 获取需要的厨房打印机
   * @returns
   */
  public getKitchenPrinters() {
    const tags: string[] = [];
    for (const item of this.kitchenItems) {
      const kitchenPrinter = item.getKitchenPrinter();
      if (kitchenPrinter && !tags.includes(kitchenPrinter)) {
        tags.push(kitchenPrinter);
      }
    }
    return tags;
  }

  /**
   * scenario：print ordering items for customer
   * @param printerSetting PrinterSetting
   * @param config KitchenConfig
   * @returns KitchenPrintingModel[] | null
   */
  public getSummaryRequestData(printerSetting: PrinterSetting, summaryPrinter: string, includeOfflinePrinters = false): KitchenPrintRequestData[] | null {
    if (this.summaryItems.length === 0) {
      return null;
    }
    const printers: PrinterConfigType[] = includeOfflinePrinters
      ? printerSetting.getPrintersByTag(summaryPrinter)
      : printerSetting.getOnlinePrintersByTag(summaryPrinter);

    if (printers.length === 0) {
      return null;
    }
    const requestPrintData: KitchenPrintRequestData[] = [];

    if (!this.subOrderFormat) {
      this.getCommonSummary(printers, requestPrintData);
    } else {
      this.getSubOrderFormatSummary(printers, requestPrintData);
    }
    return requestPrintData.length > 0 ? requestPrintData : null;
  }

  private getCommonSummary(printers: PrinterConfigType[], requestPrintData: KitchenPrintRequestData[]) {
    const total = this.summaryItems.reduce((acc, item) => {
      return acc + Math.abs(convertToNumber(item.quantity));
    }, 0);

    const printItems = this.summaryItems.map(item => {
      const itemData = item.getPrintData();
      itemData.total = total;
      return itemData;
    });

    const printContent = this.getPrintData();
    const splitItems = this.getSplitItems(printItems);
    printContent.docketTitle = 'Order Summary';
    printContent.total = total;
    printContent.purchasedItems = splitItems.purchasedItems;
    printContent.takeawayItems = splitItems.takeawayItems;
    printContent.takeawayTitle = this.getTakeawayTitle(printContent.takeawayItems);
    for (const printer of printers) {
      const data = this.formatPrintData(printContent, printer);
      if (data) {
        requestPrintData.push({
          printerId: printer.printerId,
          businessType: PrintingBusinessType.KITCHEN_TICKET,
          data,
        });
      }
    }
  }

  /**
   * subOrderFormat
   * format: subOrderA --- subOrderB
   * dine, online pay later orders
   * no changes for label printers
   * @param printers
   * @returns
   */
  private getSubOrderFormatSummary(printers: PrinterConfigType[], requestPrintData: KitchenPrintRequestData[]) {
    const labelPrinters = printers.filter(it => it.isLabelPrinter);
    if (labelPrinters.length > 0) {
      this.getCommonSummary(labelPrinters, requestPrintData);
    }

    const thermalPrinters = printers.filter(it => !it.isLabelPrinter);

    if (thermalPrinters.length > 0) {
      const subOrderGroupedItems = values(
        groupBy(
          this.summaryItems.filter(it => it.submitId),
          'submitId'
        )
      );
      if (subOrderGroupedItems.length) {
        const printContent = this.getPrintData();
        printContent.subOrders = subOrderGroupedItems.map((subOrderItems: KitchenPrintItem[], index: number): SubOrderPrintItem => {
          const printItems = subOrderItems.map(item => {
            return item.getPrintData();
          });
          const splitItems = this.getSplitItems(printItems);
          return {
            purchasedItems: splitItems.purchasedItems,
            takeawayItems: splitItems.takeawayItems,
            isLastSubOrder: index === subOrderGroupedItems.length - 1,
            note: this.subOrders.find(it => it.submitId === subOrderItems[0].submitId)?.comments,
          };
        });
        printContent.docketTitle = 'Order Summary';
        printContent.takeawayTitle = t('Takeaway');
        // remove order level note
        printContent.note = '';
        for (const printer of thermalPrinters) {
          requestPrintData.push({
            printerId: printer.printerId,
            businessType: PrintingBusinessType.KITCHEN_TICKET,
            data: this.formatPrintData(printContent, printer),
          });
        }
      }
    }
  }

  /**
   * scenario：print ordering items for customer
   * @param printerSetting PrinterSetting
   * @param config KitchenConfig
   * @returns KitchenPrintingModel[] | null
   */
  public getTestReceiptRequestData(printerSetting: PrinterSetting): KitchenPrintRequestData[] | null {
    if (this.summaryItems.length === 0) {
      return null;
    }
    const printers: PrinterConfigType[] = [printerSetting.getReceiptPrinter()].filter(Boolean);

    if (printers.length === 0) {
      return null;
    }

    const total = this.summaryItems.reduce((acc, item) => {
      return acc + Math.abs(convertToNumber(item.quantity));
    }, 0);

    const printItems = this.summaryItems.map(item => {
      const itemData = item.getPrintData();
      itemData.total = total;
      return itemData;
    });

    const printContent = this.getPrintData();
    const splitItems = this.getSplitItems(printItems);
    printContent.docketTitle = 'SAMPLE';
    printContent.total = total;
    printContent.purchasedItems = [
      {
        id: '',
        title: 'Burger',
        options: [
          {
            variationId: '6389621b2375720007b174be',
            variationName: 'Add-ons:',
            optionValues: ['- Onions', '- Extra Patty', '- Lettuce'],
          },
          {
            variationId: '6389621b2375720007b174bc',
            variationName: 'Spiciness:',
            optionValues: ['- Extra Spicy'],
          },
        ],
        optionDescription: 'Onions, Extra Patty, Lettuce, Extra Spicy',
        notesTitle: '',
        notes: null,
        isQuantityPositive: true,
        quantityStr: '1',
        quantity: 1,
        isUnitPrice: false,
        bixOrderNumber: '0001',
        bixReceiptDate: '2024.08.29 15:40',
        itemChannel: 1,
        isTakeaway: false,
        total: 1,
      },
    ];
    printContent.takeawayItems = splitItems.takeawayItems;
    printContent.takeawayTitle = this.getTakeawayTitle(printContent.takeawayItems);
    const requestPrintData: KitchenPrintRequestData[] = [];
    for (const printer of printers) {
      const data = this.formatPrintData(printContent, printer);
      if (data) {
        requestPrintData.push({
          printerId: printer.printerId,
          businessType: PrintingBusinessType.KITCHEN_TICKET,
          data,
        });
      }
    }
    return requestPrintData.length > 0 ? requestPrintData : null;
  }

  /**
   * get kitchen data that send to printer
   * target: kitchen
   * scenario：Print the items that need to be prepared to the kitchen
   * @param printerSetting PrinterSetting
   * @param config KitchenConfig
   * @returns KitchenPrintingModel[] | null
   */
  public getKitchenRequestData(printerSetting: PrinterSetting, includeOfflinePrinters = false): KitchenPrintRequestData[] | null {
    if (this.kitchenItems.length === 0) {
      return null;
    }
    const groupedItems = groupBy(this.kitchenItems, 'kitchenPrinter');
    const tags = Object.keys(groupedItems);
    const requestPrintData: KitchenPrintRequestData[] = [];

    for (const tag of tags) {
      if (tag) {
        const printers = includeOfflinePrinters ? printerSetting.getPrintersByTag(tag) : printerSetting.getOnlinePrintersByTag(tag);

        if (printers.length === 0) {
          continue;
        }
        const tagItems = groupedItems[tag];
        if (!this.subOrderFormat) {
          this.getCommonKitchen(tagItems, printers, requestPrintData);
        } else {
          this.getSubOrderFormatKitchen(tagItems, printers, requestPrintData);
        }
      }
    }

    return requestPrintData.length > 0 ? requestPrintData : null;
  }

  private getCommonKitchen(tagItems: KitchenPrintItem[], printers: PrinterConfigType[], requestPrintData: KitchenPrintRequestData[]) {
    // tag的total
    const total = tagItems.reduce((acc, item) => {
      return acc + Math.abs(convertToNumber(item.quantity));
    }, 0);
    const printItems = tagItems.map<KitchenPrintItemData>(item => {
      const itemData = item.getPrintData();
      itemData.total = total;
      return itemData;
    });
    const addPrintingModels = (items: KitchenPrintItemData[]) => {
      const printContent = this.getPrintData();
      const splitItems = this.getSplitItems(items);
      printContent.total = total;
      printContent.purchasedItems = splitItems.purchasedItems;
      printContent.takeawayItems = splitItems.takeawayItems;
      printContent.takeawayTitle = this.getTakeawayTitle(printContent.takeawayItems);
      for (const printer of printers) {
        const data = this.formatPrintData(printContent, printer);
        if (data) {
          requestPrintData.push({
            printerId: printer.printerId,
            businessType: PrintingBusinessType.KITCHEN_TICKET,
            data,
          });
        }
      }
    };
    // kitchenSeparate
    if (!this.config.kitchenSeparate) {
      addPrintingModels(printItems);
    } else {
      let previousTotal = 0;
      for (const printItem of printItems) {
        previousTotal += Math.abs(convertToNumber(printItem.quantity));
        printItem.previousTotal = previousTotal;
        addPrintingModels([printItem]);
      }
    }
  }

  /**
   * subOrderFormat
   * format:tagA__subOrderA-subOrderB, tagB__subOrderA-subOrderB
   * dine, online pay later orders
   * no changes for label printers
   * @param tagItems
   * @param printers
   * @param requestPrintData
   */
  private getSubOrderFormatKitchen(tagItems: KitchenPrintItem[], printers: PrinterConfigType[], requestPrintData: KitchenPrintRequestData[]) {
    const labelPrinters = printers.filter(it => it.isLabelPrinter);
    if (labelPrinters.length > 0) {
      this.getCommonKitchen(tagItems, labelPrinters, requestPrintData);
    }

    const thermalPrinters = printers.filter(it => !it.isLabelPrinter);

    if (thermalPrinters.length > 0) {
      const printItems = tagItems.map<KitchenPrintItemData>(item => {
        return item.getPrintData();
      });
      const addPrintingModels = (items: KitchenPrintItemData[]) => {
        const subOrderGroupedItems = values(
          groupBy(
            items.filter(it => it.submitId),
            'submitId'
          )
        );
        if (subOrderGroupedItems.length) {
          const printContent = this.getPrintData();
          printContent.takeawayTitle = t('Takeaway');
          // remove order level note
          printContent.note = '';
          printContent.subOrders = subOrderGroupedItems.map((subOrderItems: KitchenPrintItemData[], index: number) => {
            const splitItems = this.getSplitItems(subOrderItems);
            return {
              purchasedItems: splitItems.purchasedItems,
              takeawayItems: splitItems.takeawayItems,
              isLastSubOrder: index === subOrderGroupedItems.length - 1,
              note: this.subOrders.find(it => it.submitId === subOrderItems[0].submitId)?.comments,
            };
          });
          for (const printer of thermalPrinters) {
            requestPrintData.push({
              printerId: printer.printerId,
              businessType: PrintingBusinessType.KITCHEN_TICKET,
              data: this.formatPrintData(printContent, printer),
            });
          }
        }
      };
      // kitchenSeparate
      if (!this.config.kitchenSeparate) {
        addPrintingModels(printItems);
      } else {
        for (const printItem of printItems) {
          addPrintingModels([printItem]);
        }
      }
    }
  }

  /**
   * Get additional kitchen print data
   * scenario: Place an order for a specified product, and print an additional receipt to redeem the product
   * @param kitchenPrintingModels
   * @param config
   * @returns
   */
  public getBeerRequestData(kitchenPrintingModels: KitchenPrintRequestData[] | null) {
    // do not print beer dockets when changing table
    if (this.previousTableId) {
      return null;
    }
    if (!kitchenPrintingModels) {
      return null;
    }
    const requestPrintData: KitchenPrintRequestData[] = [];
    for (const printingModel of kitchenPrintingModels) {
      const { data, businessType, printerId } = printingModel;
      const purchasedItems = getUnNullValue(data, 'purchasedItems', []);
      for (const item of purchasedItems) {
        const product = this.kitchenItems.find(v => v.id === item.id)?.product;
        if (get(product, ['beerDocketSettings', 'isEnable'], false)) {
          const docketCount = getUnNullValue(product, ['beerDocketSettings', 'docketCount'], 0);
          let expirationDateString = '';
          const expirationDateDuration = getUnNullValue(product, ['beerDocketSettings', 'expirationDateDuration'], 0);
          if (expirationDateDuration != -1) {
            expirationDateString = `Expires on ${moment().add(expirationDateDuration, 'days').format('YYYY-MM-DD')}`;
          }
          for (let i = 0; i < docketCount; i++) {
            const docketCountString = `${i + 1}/${docketCount}`;
            if (item.quantity > 1) {
              for (let index = 0; index < item.quantity; index++) {
                const newItem: KitchenPrintItemData = { ...item, quantity: 1, quantityStr: '1' };
                const beerDocketModel: KitchenPrintData = {
                  ...data,
                  purchasedItems: [newItem],
                  docketCountString,
                  expirationDateString,
                  minContentHeight: 0,
                };
                requestPrintData.push({
                  businessType,
                  printerId,
                  data: beerDocketModel,
                });
              }
            } else if (item.quantity > 0) {
              const beerDocketModel: KitchenPrintData = {
                ...data,
                purchasedItems: [item],
                docketCountString,
                expirationDateString,
                minContentHeight: 0,
              };
              requestPrintData.push({
                businessType,
                printerId,
                data: beerDocketModel,
              });
            }
          }
        }
      }
    }
    return requestPrintData.length > 0 ? requestPrintData : null;
  }

  private getOrderNumberInfo(transaction: KitchenTransaction) {
    const { tableId, pickUpId, takeawayId, salesChannel, isTestReceipt } = transaction;
    const isOfflineTakeawayOrder = salesChannel === SalesChannelType.TAKEAWAY;

    let orderNumberTitle = null;
    let orderNumber = null;
    let extraOrderNumber = null;
    let bixOrderNumber = null;

    if (!isOfflineTakeawayOrder) {
      if (tableId) {
        orderNumberTitle = 'Table #';
        orderNumber = `${tableId}`;
        bixOrderNumber = tableId;
        if (pickUpId) {
          extraOrderNumber = `Order #: ${pickUpId}`;
        }
      } else if (pickUpId) {
        orderNumberTitle = 'Order #';
        orderNumber = `${pickUpId}`;
        bixOrderNumber = pickUpId;
      }
    } else {
      bixOrderNumber = t('Takeaway');
      if (tableId) {
        orderNumberTitle = 'Table #';
        orderNumber = `${tableId}`;
      } else if (takeawayId) {
        orderNumberTitle = 'Order #';
        orderNumber = `${takeawayId}`;
      } else if (pickUpId) {
        orderNumberTitle = 'Order #';
        orderNumber = `${pickUpId}`;
      }
    }
    if (isTestReceipt) {
      orderNumberTitle = 'Order #';
      extraOrderNumber = 'Table #: 1';
      orderNumber = '0001';
    }

    return { orderNumberTitle, orderNumber, extraOrderNumber, bixOrderNumber };
  }

  private getOrderTypeName(transaction: KitchenTransaction) {
    let orderTypeName = null;
    const { shippingType, salesChannel, tableId } = transaction;
    const isOfflineTakeawayOrder = salesChannel === SalesChannelType.TAKEAWAY;

    if (!isOfflineTakeawayOrder && !tableId && this.isOnlineOrder) {
      if (shippingType === ShippingType.DELIVERY) {
        orderTypeName = 'Delivery';
      } else if (shippingType === ShippingType.PICKUP) {
        orderTypeName = 'Pick Up';
      } else if (shippingType === ShippingType.TAKEAWAY) {
        orderTypeName = 'Take Away';
      }
    }

    return orderTypeName;
  }

  private getReceiptDate() {
    const date = new Date().toISOString();
    let bixReceiptDate = '';

    const receiptDate = formatDateString(date);
    if (receiptDate) {
      const dateStrings = receiptDate.split(':');
      if (dateStrings.length == 3) {
        bixReceiptDate = dateStrings[0] + ':' + dateStrings[1];
      } else {
        bixReceiptDate = receiptDate;
      }
    }
    return { receiptDate, bixReceiptDate };
  }

  public getPrintData(): KitchenPrintData {
    return {
      reprintTitle: this.isReprint ? 'Reprinted' : '',
      // takeawaySubtitle: null, // after add takeaway ID, no need print receipt number anymore
      previousTableId: this.previousTableId,
      receiptDate: this.receiptDate,
      bixReceiptDate: this.bixReceiptDate,
      isOpenOrder: this.isOpenOrder,
      isBeepOrder: this.isOnlineOrder,
      kitchenDocketVariantIsMultipleLine: this.config.kitchenDocketVariantIsMultipleLine,
      orderNumberTitle: this.orderNumberTitle,
      orderTypeName: this.orderTypeName,
      extraOrderNumber: this.extraOrderNumber,
      orderNumber: this.orderNumber,
      isTakeaway: this.isTakeaway,
      noteTitle: this.noteTitle,
      note: this.note,
      pickUpId: this.pickUpId,
      tableId: this.tableId,
      bixOrderNumber: this.bixOrderNumber,
      pax: this.pax,
      employeeName: this.employeeName,
      purchasedItems: null,
      takeawayItems: null,
      takeawayTitle: null,
      subOrders: null,
      receiptFontScale: this.receiptFontScale,
    };
  }

  /**
   * 拆分purchasedItems和takeawayItems
   * @param kitchenItems KitchenPrintItemData[]
   * @returns
   */
  private getSplitItems(kitchenItems: KitchenPrintItemData[]) {
    let purchasedItems = kitchenItems;
    let takeawayItems = null;

    if (this.needPrintTakeawayItems) {
      purchasedItems = kitchenItems.filter(it => it.itemChannel !== ItemChannelType.TAKEAWAY && !it.isTakeaway);
      takeawayItems = kitchenItems.filter(it => it.itemChannel === ItemChannelType.TAKEAWAY || it.isTakeaway);
    }

    return {
      purchasedItems: purchasedItems.length > 0 ? purchasedItems : null,
      takeawayItems: takeawayItems && takeawayItems.length > 0 ? takeawayItems : null,
    };
  }

  private getTakeawayTitle(takeawayItems: KitchenPrintItemData[] | null) {
    return this.salesChannel !== SalesChannelType.TAKEAWAY && !isEmpty(takeawayItems) ? t('Takeaway') : null;
  }

  /**
   * [Label Printer]
   * 1 Exclude deleted item
   * 2 Set font scale to 1
   *
   * @param printData
   * @param isLabelPrinter
   * @returns
   */
  private formatLabelPrintData(printData: KitchenPrintData, isLabelPrinter = false): KitchenPrintData | null {
    if (isLabelPrinter) {
      // need a copy
      const data = { ...printData };
      if (data.takeawayItems) {
        data.takeawayItems = printData.takeawayItems.filter(it => it.quantity > 0);
      }
      if (data.purchasedItems) {
        data.purchasedItems = printData.purchasedItems.filter(it => it.quantity > 0);
      }
      data.receiptFontScale = 1;
      // label printer do not support subOrderFormat now
      if (this.subOrderFormat) {
        data.note = '';
      }
      if (isEmpty(data.takeawayItems) && isEmpty(data.purchasedItems)) {
        return null;
      } else {
        return data;
      }
    } else {
      return printData;
    }
  }
  private formatPrintData(printData: KitchenPrintData, printer: PrinterConfigType): KitchenPrintData | null {
    const data = this.formatLabelPrintData(printData, printer.isLabelPrinter);
    return this.fillMinContentHeight(data, printer);
  }

  /**
   * check docket not fully printing issue
   * @param data
   * @param isLabelPrinter
   * @returns
   */
  private fillMinContentHeight(data: KitchenPrintData | null, printer: PrinterConfigType): KitchenPrintData | null {
    if (!this.config.trackConfig.trackEnabled) {
      return data;
    }
    // do not check beer dockets, takeaway dockets
    if (
      printer.isLabelPrinter ||
      (printer.printerPaperWidth !== 'Print80' && IsAndroid) ||
      !data ||
      data.receiptFontScale !== 1 ||
      data.docketCountString ||
      data.isTakeaway
    ) {
      if (data) {
        data.minContentHeight = 0;
      }
      return data;
    }

    if (this.config.trackConfig.trackMinHeight > 0) {
      data.minContentHeight = this.config.trackConfig.trackMinHeight;
      return data;
    }
    let minContentHeight = KitchenBlockHeightEnum.HeaderPadding;
    if (data.docketTitle) {
      minContentHeight += KitchenBlockHeightEnum.SummaryTitle;
    }
    if (data.orderNumber) {
      minContentHeight += KitchenBlockHeightEnum.OrderNumber;
    }
    if (data.orderTypeName) {
      minContentHeight += KitchenBlockHeightEnum.HeaderRow;
    }
    if (data.extraOrderNumber) {
      minContentHeight += KitchenBlockHeightEnum.HeaderRow;
    }
    if (data.pax) {
      minContentHeight += KitchenBlockHeightEnum.HeaderRow;
    }
    if (data.receiptDate) {
      minContentHeight += KitchenBlockHeightEnum.HeaderRow;
    }
    if (data.employeeName) {
      minContentHeight += KitchenBlockHeightEnum.EmployeeName;
    }
    if (data.reprintTitle) {
      minContentHeight += KitchenBlockHeightEnum.ReprintTitle;
    }
    const addItemsHeight = items => {
      if (!items) {
        return;
      }
      let hasDesc = false;
      forEach(items, item => {
        hasDesc = false;
        if (item.optionDescription || item.options?.length > 0) {
          hasDesc = true;
          minContentHeight += KitchenBlockHeightEnum.ItemOption;
        }
        if (item.notes) {
          hasDesc = true;
          minContentHeight += KitchenBlockHeightEnum.ItemNote;
        }
        if (hasDesc) {
          minContentHeight += KitchenBlockHeightEnum.ItemTitleWithDesc;
        } else {
          minContentHeight += KitchenBlockHeightEnum.ItemTitle;
        }
      });
    };
    addItemsHeight(data.purchasedItems);

    if (data.takeawayItems) {
      addItemsHeight(data.takeawayItems);
      if (data.takeawayTitle) {
        minContentHeight += KitchenBlockHeightEnum.TakeawayTitle;
      }
    }

    if (data.subOrders) {
      minContentHeight += KitchenBlockHeightEnum.subOrderSeparator * (data.subOrders.length - 1);
      forEach(data.subOrders, it => {
        if (it.takeawayItems && data.takeawayTitle) {
          minContentHeight += KitchenBlockHeightEnum.TakeawayTitle;
        }
        addItemsHeight(it.purchasedItems);
        addItemsHeight(it.takeawayItems);
      });
    }

    if (data.note && data.noteTitle) {
      minContentHeight += KitchenBlockHeightEnum.OrderNote;
    }
    // minContentHeight is calculated from pixel 2.75, width 818
    if (IsIOS) {
      minContentHeight *= 0.8;
    }
    data.minContentHeight = minContentHeight * this.config.trackConfig.trackSlope - this.config.trackConfig.trackThreshold;
    if (isNaN(data.minContentHeight)) {
      data.minContentHeight = 0;
    }
    return data;
  }
}

export interface KitchenPrintData {
  reprintTitle?: string;
  docketTitle?: string;
  previousTableId: string | null;
  receiptDate: string;
  bixReceiptDate: string;
  isOpenOrder: boolean;
  isBeepOrder: boolean;
  kitchenDocketVariantIsMultipleLine: boolean;
  orderNumberTitle: string | null;
  orderTypeName: string | null;
  extraOrderNumber: string | null;
  orderNumber: string | null;
  subOrders?: SubOrderPrintItem[] | null;
  purchasedItems: KitchenPrintItemData[] | null;
  takeawayItems: KitchenPrintItemData[] | null;
  takeawayTitle: string | null;
  // takeawaySubtitle: null, // after add takeaway ID, no need print receipt number anymore
  isTakeaway: boolean;
  total?: number;
  noteTitle: string;
  note: string | null;
  pickUpId: string | null;
  tableId: string | null;
  bixOrderNumber: string | null;
  pax: string | null;
  employeeName: string | null;

  docketCountString?: string;
  expirationDateString?: string;
  receiptFontScale: number;
  isTestReceipt?: boolean;
  minContentHeight?: number;
}

export interface SubOrderPrintItem {
  purchasedItems: KitchenPrintItemData[] | null;
  takeawayItems: KitchenPrintItemData[] | null;
  note?: string | null;
  isLastSubOrder?: boolean | null;
}
