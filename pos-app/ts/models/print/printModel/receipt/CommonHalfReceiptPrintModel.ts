import { receipt } from 'calculator-lib';
import { get, map } from 'lodash';
import { call, put, select, take } from 'redux-saga/effects';
import * as Actions from '../../../../actions';
import { PrinterConfigType } from '../../../../actions';
import { PrintingBusinessType, SuccessCode } from '../../../../constants';
import { t } from '../../../../constants/i18n';
import { SalesChannelType, TransactionFlowType } from '../../../../constants/transaction';
import DAL from '../../../../dal';
import { checkMRSBeforePrintHalfReceipt, onMRSInterceptor } from '../../../../sagas/mrs/checkSync';
import { columnTitles4, columnTitles5, columnWidths4, columnWidths5 } from '../../../../sagas/printing';
import {
  selectEmployeeId,
  selectEnableTakeaway,
  selectEnableUnitPriceRounding,
  selectLocalCountryMap,
  selectReceiptFontSize,
  selectRoundAllTransactions,
  selectRountTo,
  selectStoreInfo,
} from '../../../../sagas/selector';
import { generateSSTInfo } from '../../../../sagas/transaction/common';
import { EmployeeType, PromotionType } from '../../../../typings';
import {
  getDisplayPriceLocaleNumberString,
  getFixedNumber,
  getLocaleNumberString,
  getNumberValue,
  getReceiptQtyString,
  newConvertCurrencyToSymbol,
  roundingToAmount,
} from '../../../../utils';
import { getReceiptFontScale } from '../../../../utils/printer';
import { generatePromotionTitle } from '../../../../utils/promotion';
import { checkIsThaiVatRegisted } from '../../../../utils/store';
import { generateDescriptionString } from '../../../../utils/transaction';
import { PrinterSetting } from '../../printer/PrinterSetting';
import { ReceiptTransaction } from '../../../transaction/ReceiptTransaction';
import { BaseReceiptPrintModel } from './BaseReceiptPrintModel';

export class CommonHalfReceiptPrintModel extends BaseReceiptPrintModel {
  constructor(receiptTransaction: ReceiptTransaction) {
    super(receiptTransaction);
  }

  public *getReceiptPrintingModel(PrinterSetting: PrinterSetting) {
    return yield call([this, this.generateHalfReceiptPrintingModel], PrinterSetting);
  }

  private *generateHalfReceiptPrintingModel(PrinterSetting: PrinterSetting) {
    const printer: PrinterConfigType = PrinterSetting.getReceiptPrinter();
    if (!printer) {
      return null;
    }

    const data = yield call([this, this.generateHalfReceiptPrintingModelData]);
    if (!data) {
      return null;
    }
    return [
      {
        printerId: printer.printerId,
        businessType: PrintingBusinessType.TRANSACTION,
        data: data,
      },
    ];
  }

  private *generateHalfReceiptPrintingModelData() {
    const checkResult: Actions.MRSError = yield call(checkMRSBeforePrintHalfReceipt, {
      ...(this.receiptTransaction as any),
      isOnlineOrder: Boolean(this.receiptTransaction.isOnlineTransaction),
    });
    if (checkResult.errorCode !== SuccessCode) {
      onMRSInterceptor(checkResult);
      return null;
    }

    let employeeId = this.receiptTransaction.employeeId;
    if (!Boolean(employeeId)) {
      employeeId = yield select(selectEmployeeId);
    }
    const employee: EmployeeType = DAL.getEmployeeById(employeeId) as any;
    const cashier = employee ? `${employee.firstName} ${employee.lastName}` : '';

    const { storeName, currency, registerId, country, showStoreName, taxNameOnReceipt, poweredBy, includingTaxInDisplay, notes, minNo, showCustomerInfo } =
      this.receiptConfig;
    const localCountryMap = yield select(selectLocalCountryMap);
    const currencySymbol = newConvertCurrencyToSymbol(localCountryMap, currency);
    const usingDiscountLayout = true;
    const unitPriceUnrounding = yield select(selectEnableUnitPriceRounding);

    let receiptTansaction;
    try {
      // TODO: Should move this when pos-calculate support for Philippine receipt
      receiptTansaction = receipt(this.receiptTransaction, includingTaxInDisplay, 'MY', usingDiscountLayout, unitPriceUnrounding);
    } catch (err) {
      console.log('receipt function error', err);
      return null;
    }
    const {
      receiptNumber: receiptId,
      serviceChargeRate,
      items,
      transactionType,
      isOpen,
      isCancelled,
      tableId,
      pickUpId,
      takeawayCharge,
      salesChannel,
      customerId,
    } = receiptTansaction;

    const isThaiVatRegisted = checkIsThaiVatRegisted(country);

    const purchasedItems = map(items, _item => {
      const { itemType, title, options, selectedOptions, notes, subTotal, comments, sn, receipt, itemChannel, isTakeaway } = _item;
      const optionsString = generateDescriptionString(this.receiptTransaction.isOfflineTransactionInEditing ? options : selectedOptions);
      const price = getNumberValue(receipt, 'price', 0.0);
      const qty = getNumberValue(_item, 'quantity', 0.0);
      const total = getNumberValue(receipt, 'total', 0.0);
      const a4Total = getNumberValue(receipt, 'a4Total', 0.0);
      const discount = getNumberValue(receipt, 'discount', 0.0);
      const discountWithoutPromo = getNumberValue(receipt, 'discountWithoutPromo', 0.0);

      const promotions = map(get(_item, 'promotions'), promotion => {
        const realmPromotion: PromotionType = DAL.getPromotionById(promotion.promotionId);
        const title = generatePromotionTitle(realmPromotion, promotion);
        const discount = get(promotion, 'discount', 0);
        return {
          promotionName: title,
          discount: discount > 0 ? `${usingDiscountLayout ? '-' : '— '}${getLocaleNumberString(discount)}` : null, // '— ', '-'
        };
      });

      const displayTakeawayCharge = Boolean(_item.takeawayCharge) ? _item.takeawayCharge : takeawayCharge;
      return {
        itemType: itemType || null,
        discount: getLocaleNumberString(discount), // discount - _totalPromotionDiscount maybe very small value
        quantity: getReceiptQtyString(qty),
        subTotal: getLocaleNumberString(subTotal),
        total: getLocaleNumberString(total),
        a4Total: getLocaleNumberString(a4Total),
        itemName: title,
        options: optionsString,
        notes: notes || comments || null,
        price: getDisplayPriceLocaleNumberString(price, unitPriceUnrounding),
        itemDiscountName: '*Item Discount',
        sn: Boolean(sn) ? `S/N: ${sn}` : null,
        promotions,
        itemDiscountValue:
          usingDiscountLayout && Boolean(discountWithoutPromo) && discountWithoutPromo > 0 ? `— ${getLocaleNumberString(discountWithoutPromo)}` : null,
        enableTakeaway: false, // CM-5743 there will not display the takeawayCharge under the item
        takeawayTxt: t('TakeawayTxt'),
        takeawayCharge: Boolean(displayTakeawayCharge) ? `(${currencySymbol} ${getLocaleNumberString(displayTakeawayCharge)})` : null,
      };
    });

    const { gstEffective, sstEffective } = yield call(generateSSTInfo, this.receiptTransaction as any);
    const discount = get(receiptTansaction, 'discount', 0.0);
    const serviceCharge = get(receiptTansaction, 'serviceCharge', 0.0);
    const tax = get(receiptTansaction, 'tax', 0.0);
    const total = get(receiptTansaction.receipt, 'total', 0.0);

    const roundingTo = yield select(selectRountTo);
    const roundAllTransactions = yield select(selectRoundAllTransactions);
    const enableTakeaway = yield select(selectEnableTakeaway);
    let roundingTotal = total;
    let roundedAmount;
    if (roundAllTransactions) {
      roundingTotal = roundingToAmount(total, roundingTo);
      roundedAmount = roundingTotal - total;
    }

    const roundingStr =
      Boolean(roundedAmount) && Number(roundedAmount) !== 0
        ? roundedAmount > 0
          ? `${currencySymbol} ${getLocaleNumberString(roundedAmount)}`
          : `— ${currencySymbol} ${getLocaleNumberString(Math.abs(roundedAmount))}`
        : null;

    let condition = null;
    if (TransactionFlowType.Return === transactionType) {
      condition = 'return';
    } else if (TransactionFlowType.PreOrder === transactionType) {
      condition = 'preorder';
    } else if ((isThaiVatRegisted === true || gstEffective === true || sstEffective === true) && !(isOpen === true && total < 0)) {
      condition = 'taxInvoice';
    }
    let receiptTitle = null;
    if (condition === 'return') {
      receiptTitle = 'RETURNS RECEIPT';
    }

    const inTH = country === 'TH';
    let showServiceCharge = false;
    for (let x = 0; x < purchasedItems.length; x++) {
      const item = purchasedItems[x];
      if ('ServiceCharge' === item.itemType) {
        showServiceCharge = true;
        break;
      }
    }

    let taxTitle = Boolean(taxNameOnReceipt) ? taxNameOnReceipt : 'Tax';
    if (gstEffective === true) {
      taxTitle = 'GST';
    }

    let orderNoName = null;
    let orderNo = null;

    if (enableTakeaway && salesChannel === SalesChannelType.TAKEAWAY) {
      orderNo = t('Takeaway');
    } else {
      if (Boolean(pickUpId)) {
        orderNoName = 'Order #';
        orderNo = pickUpId;
      }
      if (Boolean(tableId)) {
        orderNoName = 'Table #';
        orderNo = tableId;
      }
    }

    let customerInfo = null;
    if (Boolean(customerId) && showCustomerInfo.length > 0) {
      try {
        const immutableStoreInfo = yield select(selectStoreInfo);
        const business = immutableStoreInfo.get('name');

        yield put(Actions.getCustomerById({ customerId, bn: business }));
        const responseAction = yield take([Actions.getCustomerById.toString() + '.success', Actions.getCustomerById.toString() + '.failure']);
        if (responseAction.type === Actions.getCustomerById.toString() + '.success') {
          customerInfo = '';
          const customer = responseAction.payload;
          const { firstName, lastName, city, state, taxIdNo, phone, street1, street2, postalCode } = customer;
          if (Boolean(firstName)) {
            customerInfo += `${t('Name')}: `;
            customerInfo += firstName;
          }
          if (Boolean(lastName)) {
            customerInfo += ` ${lastName}`;
          }
          if (Boolean(street1)) {
            const addressParts = [street1];
            if (Boolean(street2)) {
              addressParts.push(street2);
            }
            if (Boolean(postalCode)) {
              addressParts.push(postalCode);
            }
            customerInfo += `\n${t('Address')}:  ${addressParts.join(', ')}`;
          }
          if (Boolean(city) && Boolean(state)) {
            customerInfo += '\n';
            customerInfo += inTH ? t('District and Province: ') : t('City and State: ');
            customerInfo += `${city}, ${state}`;
          }
          if (Boolean(phone)) {
            customerInfo += '\n';
            customerInfo += `${t('Phone Number')}: `;
            customerInfo += `${phone}`;
          }
          if (inTH === true && Boolean(taxIdNo)) {
            customerInfo += '\n';
            customerInfo += `${t('Tax ID')}: `;
            customerInfo += `${taxIdNo}`;
          }
        } else {
          console.log('getCustomerById error:', responseAction.payload);
        }
      } catch (err) {
        console.log('getCustomerById error:', err);
      }
    }

    const subtotalTitle = `${t('Total Sales')} (${inTH ? t('Exc VAT') : t('Exc Tax')})`;
    const receiptTotalExcTax = getNumberValue(receiptTansaction.receipt, 'receiptTotalExcTax', 0.0);
    const takeawayCharges = getNumberValue(receiptTansaction.receipt, 'takeawayCharges', 0.0);
    const takeawayFeeName = 'Take away fee';
    const takeawayFeeValue = Boolean(takeawayCharges) ? `${currencySymbol} ${getLocaleNumberString(takeawayCharges)}` : null;
    const orderNumber = transactionType === TransactionFlowType.Sale && isCancelled !== true && Boolean(pickUpId) ? `Order #${pickUpId}` : null;
    const tableNumber =
      transactionType === TransactionFlowType.Sale && isCancelled !== true && !Boolean(pickUpId) && Boolean(tableId) ? `Table #${tableId}` : null;

    const key = `HalfReceipt-${orderNo}`;
    const model = {
      key,
      country,
      storeName: showStoreName ? storeName : null,
      birAccredited: false,
      address: null,
      phone: null,
      companyName: null,
      brn: null,
      sstEffective,
      sstIdNo: null,
      gstIdNo: null,
      receipt: null,
      customerInfo: customerInfo,
      receiptTitle,
      orderNoName,
      orderNo,
      orderNumber,
      tableNumber,
      receiptDate: null,
      receiptNumber: inTH && Boolean(receiptId) ? `${inTH ? 'No.' : 'Receipt #'}: ${receiptId}` : null,
      cashierInfo: `Cashier: ${cashier || ''}`,
      registerNumber: isThaiVatRegisted === false ? `Register # : ${registerId}` : null,
      minNumber: isThaiVatRegisted !== false ? `POS # : ${minNo}` : null,
      // minNumber: null,
      columnTitleString: usingDiscountLayout ? columnTitles5 : columnTitles4,
      a4ColumnTitleString: columnTitles5,
      columnWidths: usingDiscountLayout ? columnWidths5 : columnWidths4,
      purchasedItems: purchasedItems.filter(item => Boolean(item.itemName)),
      // Summary
      subtotal: Boolean(receiptTotalExcTax) ? `${currencySymbol} ${getLocaleNumberString(receiptTotalExcTax)}` : null,
      subtotalTitle,
      discountTitle: this.getDiscountTitle(),
      discount: `- ${currencySymbol} ${getLocaleNumberString(discount || 0)}`,
      serviceChargeTitle:
        Boolean(serviceChargeRate) && Boolean(serviceCharge > 0)
          ? `${t('Service Charge')} (${Number((serviceChargeRate * 100).toFixed(2))}%)`
          : t('Service Charge'),
      serviceCharge: showServiceCharge ? `${currencySymbol} ${getLocaleNumberString(serviceCharge)}` : null,
      taxTitle,
      tax: `${currencySymbol} ${getLocaleNumberString(tax || 0)}`,
      takeawayFeeName,
      takeawayFeeValue,
      roundingTitle: this.getRoundingTitle(),
      rounding: roundingStr,
      totalTitle: this.getTotalTitle(),
      total: `${currencySymbol} ${getLocaleNumberString(roundingTotal)}`,
      // remove payment for printing open order
      payment: [],
      // TaxSummary
      showTaxSummary: false,
      taxSummaryTitleString: null,
      taxSummaryItems: [],
      showReceiptStoreCredit: false,
      receiptStoreCreditTitleString: [],
      loyaltyEarned: null,
      loyaltyBalance: null,
      loyaltySpent: null,
      defaultLoyaltyRatio: null,
      enablePrintCashback: false,
      enableCashback: false,
      cashbackUrl: null,
      footerLabelString: Boolean(notes) ? notes : t('Thank you for shopping at our store'),
      storehubPoweredInfo: poweredBy ? t('Powered by Storehub.com Cloud POS') : null,
      receiptId,
      showBarcode: false,
      usingDiscountLayout,

      // font size
      receiptFontScale: getReceiptFontScale(yield select(selectReceiptFontSize)),
    };

    const transactionId = this.receiptTransaction.transactionId;
    const ExcludedFirstTimeAction = DAL.getExcludedFirstTimeActionByTransactionId(transactionId);
    if (Boolean(ExcludedFirstTimeAction)) {
      yield put(
        Actions.employeePrintHalfReceipt({
          transactionId,
          isReprint: false,
        })
      );
    } else {
      DAL.saveExcludedFirstTimeAction({ action: 'print_half_receipt', transactionId });
    }
    return model;
  }
}
