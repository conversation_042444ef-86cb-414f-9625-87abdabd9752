import { receipt } from 'calculator-lib';
import { get, isEmpty, map } from 'lodash';
import moment from 'moment';
import { call, put, select, take } from 'redux-saga/effects';
import { PrinterConfigType } from '../../../../actions';
import { CASHBACK_URL, SHORT_MEMBERSHIP_DETAIL_URL, SHORT_NEW_JOIN_MEMBERSHIP_URL } from '../../../../config';
import PaymentOptions from '../../../../config/paymentOption';
import { JoinMemberShipResource, PrintingBusinessType } from '../../../../constants';
import { t } from '../../../../constants/i18n';
import { OnlineOrderStatus, OrderChannel, ShippingType, TransactionFlowType } from '../../../../constants/transaction';
import DAL from '../../../../dal';
import { formatGstIdNo } from '../../../../sagas/printing/PrintingMY';
import { formatDateString } from '../../../../sagas/printing/common';
import { generateFDIReceiptTransaction, generateSSTInfo } from '../../../../sagas/transaction/common';
import { SubscriptionPlan } from '../../../../sagas/transaction/saleFlow';
import { PromotionType } from '../../../../typings';
import {
  getDisplayPriceLocaleNumberString,
  getLocaleNumberString,
  getNumberValue,
  getReceiptQtyString,
  getWithKeyString,
  isValidNumber,
  newConvertCurrencyToSymbol,
} from '../../../../utils';
import { isFDIOrder, shouldDisplayContainerFee, shouldDisplaySmallOrderFee } from '../../../../utils/beep';
import { createDate } from '../../../../utils/datetime';
import { getReceiptFontScale } from '../../../../utils/printer';
import { generatePromotionTitle } from '../../../../utils/promotion';
import { checkIsThaiVatRegisted } from '../../../../utils/store';
import { generateDescriptionString, isLocalPreOrder, isPreOrderPickUp } from '../../../../utils/transaction';
import { ReceiptTransaction } from '../../../transaction/ReceiptTransaction';
import { PrinterSetting } from '../../printer/PrinterSetting';

// import reactotron from 'reactotron-react-native';
import * as Actions from '../../../../actions';
import { generateEInvoiceUrl } from '../../../../sagas/printing';
import {
  selectBusinessName,
  selectEInvoiceEnabled,
  selectEnableUnitPriceRounding,
  selectGBEnableEInvoiceBeepWebstore,
  selectGBEnableEInvoicePhase2,
  selectPrintEInvoiceOnReceipt,
  selectStoreInfo,
  selectStoreMembershipTiersLevel,
} from '../../../../sagas/selector';
import { BaseReceiptPrintModel } from './BaseReceiptPrintModel';

export class MYReceiptPrintModel extends BaseReceiptPrintModel {
  constructor(receiptTransaction: ReceiptTransaction) {
    super(receiptTransaction);
  }

  public *getReceiptPrintingModel(printerModel: PrinterSetting) {
    return yield call([this, this.generateMYReceiptPrintingModel], printerModel);
  }

  private inTh(): boolean {
    return checkIsThaiVatRegisted(this.receiptConfig.country);
  }

  private getBrn() {
    return !this.inTh() && Boolean(this.receiptConfig.brn) ? `${'Business Registration Number:'}${this.receiptConfig.brn}` : null;
  }

  private hasCustomer(): boolean {
    return Boolean(this.receiptTransaction.customer) && this.receiptTransaction.customer.customerId;
  }

  private isTPOrder(): boolean {
    return isFDIOrder(this.receiptTransaction.channel);
  }

  private getReceiptDate(modifiedReceiptTansaction) {
    let _receiptDate = modifiedReceiptTansaction.createdDate;
    if (!_receiptDate) {
      _receiptDate = modifiedReceiptTansaction.createdTime;
    }
    return Boolean(_receiptDate) ? `${this.inTh() ? t('Date') : 'Receipt Date'}: ${formatDateString(_receiptDate)}` : null;
  }

  private getReprintDate(modifiedReceiptTansaction) {
    let _receiptDate = Date.now() / 1000;
    return Boolean(_receiptDate) ? `${'Reprinted Date'}: ${formatDateString(_receiptDate)}` : null;
  }

  private getGstIdNo() {
    return formatGstIdNo(this.receiptConfig.gstIdNo, this.receiptConfig.country);
  }

  private getMYRegisterNumber() {
    return this.inTh() === false ? `Register # : ${this.receiptConfig.registerId}` : null;
  }

  private getMYMinNumber() {
    return this.inTh() !== false ? `POS # : ${this.receiptConfig.minNo}` : null;
  }

  private generateMYModifiedReceiptTransaction(unitPriceUnrounding: boolean) {
    const country = this.receiptConfig.country;
    const _fakeCountry = country === 'PH' ? country : 'MY';
    const usingDiscountLayout = this.isUsingDiscountLayout();
    try {
      return this.isTheOrderAfterFDIBIR()
        ? generateFDIReceiptTransaction(this.receiptTransaction, country)
        : receipt(this.receiptTransaction, this.receiptConfig.includingTaxInDisplay, _fakeCountry, usingDiscountLayout, unitPriceUnrounding);
    } catch (exception) {
      console.log('calculate error', exception);
      return null;
    }
  }

  private generateMYReceiptTitle(transactionType: any, modifiedReceiptTansaction: any, gstEffective, sstEffective, isOpen, _customer, total) {
    let condition = null;
    if (TransactionFlowType.Return === transactionType) {
      condition = 'return';
    } else if (TransactionFlowType.PreOrder === transactionType) {
      condition = 'preorder';
    } else if (this.isOfflineTakeawayOrder(modifiedReceiptTansaction)) {
      // if takeaway order, then no receiptTitle
      condition = 'takeaway';
    } else if ((this.inTh() === true || gstEffective === true || sstEffective === true) && !(isOpen === true && total < 0)) {
      condition = 'taxInvoice';
    }
    let receiptTitle = null;
    let isReceiptTitleBig = false;
    const isCancelled = modifiedReceiptTansaction?.isCancelled ?? modifiedReceiptTansaction?.status === OnlineOrderStatus.Cancelled;
    if (isCancelled) {
      receiptTitle = 'CANCELLATION RECEIPT';
      isReceiptTitleBig = true;
    } else if (this.inTh() === true && condition === 'taxInvoice' && Boolean(this.receiptConfig.gstIdNo)) {
      receiptTitle = Boolean(_customer.taxIdNo) ? 'Receipt / Tax Invoice / VAT Included' : 'Receipt / Tax Invoice (ABB) / VAT Included';
    } else if (this.inTh() === true && condition === 'taxInvoice' && !Boolean(this.receiptConfig.gstIdNo)) {
      receiptTitle = 'Receipt';
    } else if (condition === 'return') {
      receiptTitle = 'RETURNS RECEIPT';
      isReceiptTitleBig = true;
    } else if (condition === 'preorder') {
      receiptTitle = t('Preorder Receipt');
      isReceiptTitleBig = false;
    } else if (Boolean(this.receiptConfig.sstIdNo) && condition === 'taxInvoice') {
      receiptTitle = 'INVOICE';
      isReceiptTitleBig = true;
    }

    if (!isOpen && this.receiptTransaction.isReprint) {
      if (Boolean(receiptTitle)) {
        receiptTitle = receiptTitle + '-' + 'Reprinted';
      } else {
        receiptTitle = 'Reprinted';
      }
    }

    return { receiptTitle, isReceiptTitleBig };
  }

  private generateMYPurchasedItems(items: any[], currencySymbol: string, takeawayCharge: number, showNotes: boolean, unitPriceUnrounding: boolean): any[] {
    return map(items, _item => {
      const { itemType, title, selectedOptions, options, notes, comments, receipt, sn } = _item;
      const optionsString = generateDescriptionString(selectedOptions || options);

      const price = getNumberValue(receipt, 'price', 0.0);
      const qty = getNumberValue(_item, 'quantity', 0.0);
      const total = getNumberValue(receipt, 'total', 0.0);
      const a4Total = getNumberValue(receipt, 'a4Total', 0.0);
      const discount = getNumberValue(receipt, 'discount', 0.0);
      const discountWithoutPromo = getNumberValue(receipt, 'discountWithoutPromo', 0.0);

      const promotions = (get(_item, 'promotions') || []).map(promotion => {
        const realmPromotion: PromotionType = DAL.getPromotionById(promotion.promotionId);
        const title = generatePromotionTitle(realmPromotion, promotion);
        const discount = get(promotion, 'discount', 0);
        return {
          promotionName: title,
          discount: discount > 0 ? `${this.isUsingDiscountLayout() === true ? '-' : '— '}${getLocaleNumberString(discount)}` : null, // '— ', '-'
        };
      });

      const displayTakeawayCharge = Boolean(_item.takeawayCharge) ? _item.takeawayCharge : takeawayCharge;
      return {
        itemType,
        discount: getLocaleNumberString(discount),
        quantity: getReceiptQtyString(qty),
        itemName: title,
        total: getLocaleNumberString(total),
        a4Total: getLocaleNumberString(a4Total),
        options: optionsString,
        notes: showNotes ? notes || comments : null,
        price: getDisplayPriceLocaleNumberString(price, unitPriceUnrounding),
        sn: Boolean(sn) ? `S/N: ${sn}` : null,
        promotions,
        itemDiscountName: '*Item Discount',
        itemDiscountValue: Boolean(discountWithoutPromo) && discountWithoutPromo > 0 ? `— ${getLocaleNumberString(discountWithoutPromo)}` : null,
        enableTakeaway: false, // CM-5743 there will not display the takeawayCharge under the item
        takeawayTxt: t('TakeawayTxt'),
        takeawayCharge: Boolean(displayTakeawayCharge) ? `(${currencySymbol} ${getLocaleNumberString(displayTakeawayCharge)})` : null,
      };
    });
  }

  private generateMYPayments(
    payments: any[],
    currencySymbol: string,
    enableCashback: boolean,
    transactionType: any,
    isTPOrder: boolean,
    orderPaymentMethod?: string
  ) {
    let depositAmountTitle = null;

    if (isTPOrder) {
      return { depositAmountTitle, _payments: null };
    }
    const _payments = map(payments, _item => {
      const { paymentMethod, paymentMethodId, cashTendered, amount, roundedAmount, subType, isDeposit, type, isOnline } = _item;
      let _name: string = null;
      if (paymentMethod && isOnline) {
        // Beep Ordering payment method name
        _name = paymentMethod || type;
      } else if (this.receiptTransaction.isOnlineTransaction && !isOnline) {
        // Pay By Cash Ordering payment method name
        _name = paymentMethod;
      } else {
        _name = PaymentOptions.getPaymentNameForReceipt(paymentMethodId, enableCashback);
      }
      if (isEmpty(_name)) {
        _name = paymentMethod || type;
      }
      let _cashTendered = cashTendered;
      if (!isValidNumber(_cashTendered)) {
        _cashTendered = amount;
      }
      let paymentMethodName = _name && _name.toUpperCase();
      let amountValue = `${currencySymbol} ${getLocaleNumberString(amount)}`;
      let changeValue = null;
      if (Boolean(isDeposit) && transactionType !== TransactionFlowType.PreOrder) {
        paymentMethodName = `${paymentMethodName} ${t('DEPOSIT')}`;
      } else if (paymentMethodId === 0) {
        amountValue = `${currencySymbol} ${getLocaleNumberString(_cashTendered)}`;
        changeValue = `${currencySymbol} ${getLocaleNumberString(_cashTendered - amount)}`;
      }
      if (this.receiptTransaction.isOnlineTransaction && this.receiptTransaction.isReprint) {
        changeValue = `${currencySymbol} ${getLocaleNumberString(_item?.changeAmount ?? 0)}`;
      }
      if (orderPaymentMethod === 'Online') {
        changeValue = null;
      }
      if (Boolean(isDeposit)) {
        depositAmountTitle = `${paymentMethodName} ${t('DEPOSIT')}`;
      }
      if (paymentMethodName === 'CASH') {
        paymentMethodName = t('CASH');
      }
      return {
        paymentMethodId: isValidNumber(paymentMethodId) ? Number(paymentMethodId) : 0,
        paymentMethodName,
        cashTendered: _cashTendered,
        amount: amountValue,
        roundedAmount,
        subType,
        changeTitle: t('CHANGE'),
        changeValue,
      };
    });

    return { depositAmountTitle, _payments };
  }

  private generateReasonString(
    transactionType: any,
    isCancelled: boolean,
    tableId: string,
    otherReason: string,
    comment: string,
    autoOrderId: boolean,
    assignTableID: boolean,
    isOnlineTransaction: boolean,
    isOnlineOrder: boolean
  ): string {
    let reasonString = '';

    if ((transactionType === TransactionFlowType.Return || isCancelled) && (Boolean(otherReason) || Boolean(tableId))) {
      if (Boolean(otherReason)) {
        reasonString = 'Cancellation Reason: ' + otherReason;
      } else {
        reasonString = 'Table #: ' + tableId;
      }
    } else if ((Boolean(tableId) || Boolean(comment)) && transactionType !== TransactionFlowType.PreOrder && (Boolean(assignTableID) || Boolean(autoOrderId))) {
      reasonString = Boolean(autoOrderId) ? '' : 'Table #: ' + (tableId ? tableId : comment);
    }

    return reasonString;
  }

  private generateMYServiceChargePrefixAndAggregatedTaxPrefix(items: any[], aggregatedTax: any): string {
    let serviceChargePrefix = '';

    if (Boolean(aggregatedTax)) {
      map(items, item => {
        if (!Boolean(item.itemType)) {
          const { taxCode, title } = item;
          const aggregatedTaxItem = aggregatedTax[taxCode];
          if (Boolean(aggregatedTaxItem)) {
            item.title = `${aggregatedTaxItem[0]}${title}`;
          }
        } else if (item.itemType === 'ServiceCharge') {
          const { taxCode } = item;
          const scAggregatedTaxItem = aggregatedTax[taxCode];
          if (Boolean(scAggregatedTaxItem)) {
            serviceChargePrefix = scAggregatedTaxItem[0];
          }
        }
      });
    }

    return serviceChargePrefix;
  }

  private *generateMYReceiptPrintingModel(printerModel: PrinterSetting) {
    const printer: PrinterConfigType = printerModel.getReceiptPrinter();
    if (!printer) {
      return null;
    }

    const data = yield call([this, this.generateMYReceiptPrintingModelData]);

    // reactotron.log('🌕data:', data);

    const requestPrintingModel = [
      {
        printerId: printer.printerId,
        businessType: PrintingBusinessType.TRANSACTION,
        data: data,
      },
    ];
    if (!!data.needPrintTwoCopies && !this.receiptTransaction.isReprint) {
      requestPrintingModel.push({
        printerId: printer.printerId,
        businessType: PrintingBusinessType.TRANSACTION,
        data: data,
      });
    }
    return requestPrintingModel;
  }

  private *generateMYReceiptPrintingModelData() {
    const {
      currency,
      business,
      country,
      sstIdNo,
      showNotes,
      showBarcode,
      showCustomerInfo,
      taxNameOnReceipt,
      defaultLoyaltyRatio,
      enableCashback,
      birAccredited,
      assignTableID,
      autoOrderId,
      disableCashbackFromPOS,
      membershipEnabled,
      receiptFontSize,
      enableStoreCredit,
      localCountryMap,
      enablePoints,
    } = this.receiptConfig;

    const unitPriceUnrounding = yield select(selectEnableUnitPriceRounding);

    const modifiedReceiptTansaction = this.generateMYModifiedReceiptTransaction(unitPriceUnrounding);

    if (!modifiedReceiptTansaction) return;

    const {
      receiptNumber: receiptId,
      items,
      payments,
      roundedAmount,
      transactionType,
      isOpen,
      tableId,
      pickUpId,
      serviceChargeRate,
      comment,
      shippingType,
      contactDetail,
      channel,
      otherReason,
      isCancelled,
      depositAmount,
      takeawayCharge,
      customerId,
      isOnlineOrder,
      pickUpDate,
      paymentMethod,
      // version, only for FDI BIR, if version is 1, then use the new format, else keep the original format
    } = modifiedReceiptTansaction;

    // reactotron.log('🌕modifiedReceiptTansaction', modifiedReceiptTansaction);

    const total = get(modifiedReceiptTansaction.receipt, 'total', 0.0);
    const unPaidBalance = total - depositAmount || 0;
    const salesChannel = get(modifiedReceiptTansaction, 'salesChannel', 0);

    const { gstEffective, sstEffective, taxSummary, aggregatedTax } = yield call(generateSSTInfo, this.receiptTransaction as any);

    const serviceChargePrefix = this.generateMYServiceChargePrefixAndAggregatedTaxPrefix(get(this.receiptTransaction, 'items', []), aggregatedTax);

    const hasCustomerId = !isEmpty(customerId);

    let _hasCustomer = false;
    let responseCustomer = null;
    if (this.receiptTransaction.isOnlineTransaction) {
      const { customerId } = this.receiptTransaction;
      if (!this.receiptTransaction.customer && customerId) {
        try {
          const immutableStoreInfo = yield select(selectStoreInfo);
          const business = immutableStoreInfo.get('name');

          yield put(Actions.getCustomerById({ customerId, bn: business }));
          const responseAction = yield take([Actions.getCustomerById.toString() + '.success', Actions.getCustomerById.toString() + '.failure']);
          if (responseAction.type === Actions.getCustomerById.toString() + '.success') {
            _hasCustomer = true;
            responseCustomer = responseAction.payload;
          } else {
            console.log('getCustomerById error:', responseAction.payload);
          }
        } catch (err) {
          console.log('getCustomerById error:', err);
        }
      }
    }

    const _enablePrintCashback =
      !disableCashbackFromPOS &&
      enableCashback &&
      !this.hasCustomer() &&
      !hasCustomerId &&
      transactionType !== TransactionFlowType.Return &&
      !membershipEnabled;
    let _customer: any = {};

    let customerInfo = '';
    if (this.hasCustomer() || _hasCustomer) {
      _customer = Object.assign({}, this.receiptTransaction.customer || responseCustomer);
      if (showCustomerInfo.length > 0) {
        const street1 = get(this.receiptTransaction.customer, 'street1', '');
        const street2 = get(this.receiptTransaction.customer, 'street2', '');
        const postalCode = get(this.receiptTransaction.customer, 'postalCode');

        const city = get(this.receiptTransaction.customer, 'city', '');
        const state = get(this.receiptTransaction.customer, 'state', '');

        const customerAddress = [street1, street2, postalCode].filter(v => Boolean(v)).join(`${this.getComma()} `);
        _customer.address = customerAddress;

        const cityStr = [city, state].filter(v => Boolean(v)).join(`${this.getComma()} `);
        _customer.cityAndState = cityStr;

        const { firstName, lastName, cityAndState, taxIdNo, address, phone } = _customer;
        if (Boolean(firstName)) {
          customerInfo += `${t('Name')}: `;
          customerInfo += firstName;
        }
        if (Boolean(lastName)) {
          customerInfo += ` ${lastName}`;
        }
        if (Boolean(address)) {
          customerInfo += '\n';
          customerInfo += `${t('Address')}: `;
          customerInfo += `${address}`;
        }
        if (Boolean(cityAndState)) {
          customerInfo += '\n';
          customerInfo += this.inTh() ? t('District and Province: ') : t('City and State: ');
          customerInfo += `${isEmpty(_customer.address) ? '\n' : ''}${cityAndState}`;
        }
        if (Boolean(phone)) {
          customerInfo += '\n';
          customerInfo += `${t('Phone Number')}: `;
          customerInfo += `${phone}`;
        }
        if (this.inTh() === true && Boolean(taxIdNo)) {
          customerInfo += '\n';
          customerInfo += `${t('Tax ID')}: `;
          customerInfo += `${taxIdNo}`;
        }
      }
    }

    const { receiptTitle, isReceiptTitleBig } = this.generateMYReceiptTitle(
      transactionType,
      modifiedReceiptTansaction,
      gstEffective,
      sstEffective,
      isOpen,
      _customer,
      total
    );

    const _reasonString = this.generateReasonString(
      transactionType,
      isCancelled ?? modifiedReceiptTansaction?.status === OnlineOrderStatus.Cancelled,
      tableId,
      otherReason,
      comment,
      autoOrderId,
      assignTableID,
      this.receiptTransaction.isOnlineTransaction,
      isOnlineOrder
    );

    const currencySymbol = newConvertCurrencyToSymbol(localCountryMap, currency);

    const purchasedItems = this.generateMYPurchasedItems(items, currencySymbol, takeawayCharge, showNotes, unitPriceUnrounding);

    const { depositAmountTitle, _payments } = this.generateMYPayments(
      payments,
      currencySymbol,
      enableCashback,
      transactionType,
      this.isTPOrder(),
      paymentMethod
    );

    const showTaxSummary = (Boolean(sstEffective) || Boolean(gstEffective)) && taxSummary.length > 0;
    const taxSummaryItems = [];
    if (showTaxSummary) {
      map(taxSummary, item => {
        const { title, amount, tax } = item;
        taxSummaryItems.push({
          title,
          amount: getLocaleNumberString(amount),
          tax: getLocaleNumberString(tax),
        });
      });
    }

    let showServiceCharge = false;
    if (this.isOfflineTakeawayOrder(modifiedReceiptTansaction)) {
      showServiceCharge = false;
    } else {
      for (let x = 0; x < purchasedItems.length; x++) {
        const item = purchasedItems[x];
        if ('ServiceCharge' === item.itemType) {
          showServiceCharge = true;
          break;
        }
      }
    }

    let roundingValue =
      Boolean(roundedAmount) && Number(roundedAmount) !== 0
        ? roundedAmount > 0
          ? `${currencySymbol} ${getLocaleNumberString(roundedAmount)}`
          : `— ${currencySymbol} ${getLocaleNumberString(Math.abs(roundedAmount))}`
        : null;

    let taxTitle = Boolean(taxNameOnReceipt) ? taxNameOnReceipt : 'Tax';
    if (gstEffective === true) {
      taxTitle = 'GST';
    }
    const subscriptionPlan = this.receiptConfig.subscriptionPlan;
    const isLocalPreOrderRecord = !Boolean(this.receiptTransaction.isOnlineTransaction) && !isOnlineOrder && isLocalPreOrder(this.receiptTransaction as any);

    const showReceiptStoreCredit =
      _customer.isStoreCredit === true &&
      !isLocalPreOrderRecord &&
      ((enableStoreCredit && !isOnlineOrder && subscriptionPlan > SubscriptionPlan.Small) || (enableCashback && !enablePoints));

    const { loyaltyEarned, loyaltyBalance, loyaltySpent, cashbackExpirationDate } = _customer;

    const pd = moment(pickUpDate);
    let formattedPickUpDate = null;
    if (isLocalPreOrderRecord && !isPreOrderPickUp(this.receiptTransaction as any) && pd.isValid()) {
      formattedPickUpDate = t('Pick up Date & Time: ') + pd.format('YYYY.MM.DD HH:mm');
    }

    let cashbackExpirationDesc = null;
    if (cashbackExpirationDate && enableCashback) {
      cashbackExpirationDesc = `Cashback expires on ${createDate(cashbackExpirationDate, 'YYYY.MM.DD')}`;
    }

    const takeawayCharges = getNumberValue(modifiedReceiptTansaction.receipt, 'takeawayCharges', 0.0);
    const takeawayFeeName = 'Take away fee';
    const takeawayFeeValue = Boolean(takeawayCharges) ? `${currencySymbol} ${getLocaleNumberString(takeawayCharges)}` : null;

    let shippingFeeName = null;
    let shippingFeeValue = null;
    const deliveryInformation = this.receiptTransaction.isOnlineTransaction ? this.receiptTransaction.deliveryInformation : null;
    const deliveryOrContactInfo: any = {};
    let deliveryInformationComment = null;
    if (
      (channel === OrderChannel.OrderChannelQRCode || channel === OrderChannel.OrderChannelOnline || this.isTPOrder()) &&
      shippingType === ShippingType.DELIVERY
    ) {
      if (deliveryInformation && deliveryInformation.length > 0) {
        let address = '';
        if (Boolean(deliveryInformation[0].address.address)) {
          address = address + deliveryInformation[0].address.address + ',';
        }
        if (Boolean(deliveryInformation[0].address.city)) {
          address = address + deliveryInformation[0].address.city + ',';
        }
        if (Boolean(deliveryInformation[0].address.postCode)) {
          address = address + deliveryInformation[0].address.postCode + ',';
        }
        if (Boolean(deliveryInformation[0].address.state)) {
          address = address + deliveryInformation[0].address.state + ',';
        }
        if (Boolean(deliveryInformation[0].address.country)) {
          address = address + deliveryInformation[0].address.country;
        }
        if (Boolean(deliveryInformation[0].shippingFee)) {
          const { shippingFee } = deliveryInformation[0];
          shippingFeeName = 'Delivery Fee:';
          shippingFeeValue = Boolean(shippingFee) ? `${currencySymbol} ${getLocaleNumberString(shippingFee)}` : null;
        }
        if (channel === OrderChannel.OrderChannelQRCode || channel === OrderChannel.OrderChannelOnline) {
          deliveryOrContactInfo.shippingToName = 'Shipping To';
          deliveryOrContactInfo.name = getWithKeyString(`${t('Name')}: `, deliveryInformation[0].address.name);
          deliveryOrContactInfo.phone = getWithKeyString(`${t('Phone Number')}: `, deliveryInformation[0].address.phone);
          deliveryOrContactInfo.address = getWithKeyString(`${t('Address')}: `, address);
          deliveryInformationComment = deliveryInformation[0].comments;
        } else if (this.isTPOrder()) {
          deliveryInformationComment = deliveryInformation[0].comments;
        }
      }
    } else if ((channel === OrderChannel.OrderChannelQRCode || this.isTPOrder()) && !Boolean(tableId) && Boolean(pickUpId)) {
      if (channel === OrderChannel.OrderChannelQRCode) {
        deliveryOrContactInfo.shippingToName = null;
        deliveryOrContactInfo.name = Boolean(contactDetail) && Boolean(contactDetail.name) ? contactDetail.name : null;
        deliveryOrContactInfo.phone = Boolean(contactDetail) && Boolean(contactDetail.phone) ? contactDetail.phone : null;
        deliveryOrContactInfo.address = null;
      }
    }
    deliveryOrContactInfo.notes = (Boolean(comment) ? `${comment}.` : '').concat(
      Boolean(deliveryInformationComment) ? `${Boolean(comment) ? '\n' : ''}${deliveryInformationComment}.` : ''
    );
    if (Boolean(deliveryOrContactInfo.notes)) deliveryOrContactInfo.notesName = 'Note';

    let showDeliveryOrContactInfo = false;
    const deliveryOrContactInfoKeys = Object.keys(deliveryOrContactInfo);
    if (deliveryOrContactInfoKeys.length > 0) {
      for (let i = 0; i < Object.keys(deliveryOrContactInfo).length; i++) {
        if (Boolean(deliveryOrContactInfo[deliveryOrContactInfoKeys[i]])) {
          showDeliveryOrContactInfo = true;
          break;
        }
      }
    }

    const receiptTotalExcTax = getNumberValue(modifiedReceiptTansaction.receipt, 'receiptTotalExcTax', 0.0);
    let subtotalTitle = `${t('Total Sales')} (${this.inTh() ? t('Exc VAT') : t('Exc Tax')})`;
    const subtotalValue = Boolean(receiptTotalExcTax) ? `${currencySymbol} ${getLocaleNumberString(receiptTotalExcTax)}` : null;
    const discount = get(modifiedReceiptTansaction.receipt, 'discount', 0.0);
    const serviceCharge = getNumberValue(modifiedReceiptTansaction.receipt, 'serviceCharge', 0.0);
    const tax = get(modifiedReceiptTansaction.receipt, 'tax', 0.0);
    let discountValue = Boolean(discount) && discount > 0 ? `— ${currencySymbol} ${getLocaleNumberString(discount)}` : null;
    let taxValue = `${currencySymbol} ${getLocaleNumberString(tax || 0)}`;
    let serviceChargeValue = showServiceCharge ? `${currencySymbol} ${getLocaleNumberString(serviceCharge)}` : null;
    let totalValue = `${currencySymbol} ${getLocaleNumberString(Boolean(total) ? total : 0)}`;
    let smallOrderFeeTxt;
    let smallOrderFeeValue;
    let containerFeeTxt;
    let containerFeeValue;
    if (this.isTheOrderAfterFDIBIR()) {
      const fdiReceipt = get(modifiedReceiptTansaction, 'receipt', {});
      const { total: fdiReceiptTotal, containerFee: fdiReceiptContainerFee, smallOrderFee: fdiReceiptSmallOrderFee, discount: fdiReceiptDiscount } = fdiReceipt;
      smallOrderFeeTxt = t('Small Order Fee');
      smallOrderFeeValue =
        shouldDisplaySmallOrderFee(modifiedReceiptTansaction) && Boolean(fdiReceiptSmallOrderFee) ? getLocaleNumberString(fdiReceiptSmallOrderFee) : null;
      containerFeeTxt = t('Container Fee');
      containerFeeValue =
        shouldDisplayContainerFee(modifiedReceiptTansaction) && Boolean(fdiReceiptContainerFee) ? getLocaleNumberString(fdiReceiptContainerFee) : null;
      totalValue = getLocaleNumberString(Boolean(fdiReceiptTotal) ? fdiReceiptTotal : 0);
      discountValue = Boolean(fdiReceiptDiscount) && fdiReceiptDiscount > 0 ? getLocaleNumberString(fdiReceiptDiscount) : null;

      shippingFeeValue = taxValue = subtotalTitle = serviceChargeValue = roundingValue = null;
    } else {
      const smallOrderFee = get(this.receiptTransaction, 'smallOrderFee', null);
      smallOrderFeeTxt =
        channel === OrderChannel.OrderChannelShopee ? t('Small Order Fee') : channel === OrderChannel.OrderChannelFoodPanda ? t('Difference to Minimum') : '';
      smallOrderFeeValue = smallOrderFee !== null && smallOrderFee !== 0 ? `${currencySymbol} ${getLocaleNumberString(smallOrderFee)}` : null;
    }

    const showOrderSummary =
      !isEmpty(subtotalTitle) ||
      !isEmpty(discountValue) ||
      !isEmpty(shippingFeeValue) ||
      !isEmpty(serviceChargeValue) ||
      !isEmpty(smallOrderFeeValue) ||
      !isEmpty(containerFeeValue) ||
      !isEmpty(taxValue) ||
      !isEmpty(roundingValue);

    const hasCustomerOrCustomerId = this.hasCustomer() || hasCustomerId;
    const enablePrintCashback = _enablePrintCashback && Boolean(receiptId) && !this.isTPOrder();
    const enableEInvoice = yield select(selectEInvoiceEnabled);
    const enableEInvoiceOnReceipt = yield select(selectPrintEInvoiceOnReceipt);
    const enableEInvoicePhase2 = yield select(selectGBEnableEInvoicePhase2);
    const enableEInvoiceBeepWebstore = yield select(selectGBEnableEInvoiceBeepWebstore);

    const isTransactionEInvoicePrintable = () => {
      if (isOnlineOrder) {
        if (!enableEInvoicePhase2) {
          return false;
        }
        if (!enableEInvoiceBeepWebstore && paymentMethod !== 'Offline') {
          // exclude pay at counter
          return false;
        }
        if (payments) {
          for (const payment of payments) {
            const paymentOption = PaymentOptions.getPaymentOptionByName(payment.paymentMethod);
            if (paymentOption && paymentOption.isExcludedFromEInvoice) {
              return false;
            }
          }
        }
      } else {
        if (!enableEInvoicePhase2) {
          return true;
        }
        if (payments) {
          for (const payment of payments) {
            const paymentOption = PaymentOptions.getPaymentOptionById(payment.paymentMethodId);
            if (paymentOption && paymentOption.isExcludedFromEInvoice) {
              return false;
            }
          }
        }
      }
      return true;
    };

    const eInvoiceUrl = generateEInvoiceUrl({
      merchantName: yield select(selectBusinessName),
      receiptNumber: receiptId,
      channel: channel,
    });
    console.log('eInvoiceUrl', eInvoiceUrl);

    const isRefund = transactionType === TransactionFlowType.Return;

    let enablePrintMemberShip = membershipEnabled && transactionType !== TransactionFlowType.Return;
    if (!enablePoints && hasCustomerOrCustomerId) {
      enablePrintMemberShip = false;
    }

    let qrCodeAboveInfo = null;
    let qrCodeUnderInfo = null;
    const membershipUrl = SHORT_NEW_JOIN_MEMBERSHIP_URL(
      business,
      this.receiptConfig.storeId,
      this.receiptTransaction.isOnlineTransaction ? channel : 2,
      receiptId,
      enablePoints && !enableCashback
    );

    let membershipSmallTitle = null;
    let membershipBoildTitle = null;
    let membershipLargeContentTitle1 = null;
    let membershipLargeContentTitle2 = null;
    let membershipSmallBottomTitle = null;
    let cashbackRate = defaultLoyaltyRatio === 0 ? '0' : ((1 / defaultLoyaltyRatio) * 100).toFixed(0);

    if (hasCustomerOrCustomerId) {
      yield put(
        Actions.getCustomer({
          business,
          customerId,
          needPointsBalance: true,
          needPointsTotalSpend: true,
          needMembershipDetail: true,
        })
      );
      const responseAction = yield take([Actions.getCustomer.toString() + '.success', Actions.getCustomer.toString() + '.failure']);
      if (responseAction.type === Actions.getCustomer.toString() + '.success') {
        if (responseAction.payload && responseAction.payload.customer) {
          const customer = responseAction.payload.customer;
          if (customer && customer.membershipTierInfo && customer.membershipTierInfo.membershipTierLevel) {
            const tierLevel = yield select(selectStoreMembershipTiersLevel(customer.membershipTierInfo.membershipTierLevel));
            if (tierLevel && tierLevel.cashbackRate) {
              cashbackRate = String(tierLevel.cashbackRate);
              console.log('cashbackRate', cashbackRate);
            }
          }
        }
      }
    }
    if (membershipEnabled && !hasCustomerOrCustomerId) {
      if (enableCashback && !enablePoints) {
        qrCodeAboveInfo = t('Expires in 12 hours');
        qrCodeUnderInfo = t('Use this cashback on your next visit');

        membershipSmallTitle = t('You’ve earned');
        membershipLargeContentTitle1 = `${cashbackRate}% Cashback`;
        membershipSmallBottomTitle = t('Scan to claim');
      } else if (enablePoints && !enableCashback) {
        qrCodeAboveInfo = t('Expires in 12 hours');
        qrCodeUnderInfo = t('Redeem rewards on your next visit');

        membershipSmallTitle = t('You’ve earned');
        membershipLargeContentTitle1 = 'Points';
        membershipSmallBottomTitle = t('Scan to claim');
      } else if (enableCashback && enablePoints) {
        qrCodeAboveInfo = t('Expires in 12 hours');
        qrCodeUnderInfo = t('Redeem rewards on your next visit');

        membershipSmallTitle = t('You’ve earned');
        membershipLargeContentTitle1 = 'Points &';
        membershipLargeContentTitle2 = `${cashbackRate}% Cashback`;
        membershipSmallBottomTitle = t('Scan to claim');
      } else if (!enablePoints && !enableCashback) {
        qrCodeAboveInfo = t('Expires in 12 hours');
        qrCodeUnderInfo = t('Redeem rewards on your next visit');

        membershipBoildTitle = t('Scan to join as a Member');
        membershipSmallBottomTitle = t('Exclusive discounts, promos, and more!');
      }
    } else if (enablePrintMemberShip && hasCustomerOrCustomerId) {
      if (enablePoints && !enableCashback) {
        qrCodeAboveInfo = t('Expires in 12 hours');
        qrCodeUnderInfo = t('Redeem rewards on your next visit');

        membershipSmallTitle = t('You’ve earned');
        membershipLargeContentTitle1 = 'Points';
      } else if (enablePoints && enableCashback) {
        qrCodeAboveInfo = t('Expires in 12 hours');
        qrCodeUnderInfo = t('Redeem rewards on your next visit');

        membershipSmallTitle = t('You’ve earned');
        membershipLargeContentTitle1 = 'Points &';
        membershipLargeContentTitle2 = `${cashbackRate}% Cashback`;
      }
    }

    const enablePrintQRCode = enablePrintCashback || enablePrintMemberShip;
    const key = `Receipt-${receiptId}`;

    // new printing layout process to include promotion in discount and subtract promotion from total amount
    // putting at the end of the printing model creating flow to avoid unknown issues
    return {
      key,
      business: business, // membership
      country: country,
      transactionId: this.receiptTransaction.transactionId,
      storeName: this.getStoreName(),
      birAccredited: birAccredited,
      preOrderDate: this.getPreOrderDate(),
      shippingType,
      channel,
      address: this.getAddress(),
      phone: this.getPhone(),
      companyName: this.getCompanyName(),
      brn: this.getBrn(),
      sstEffective,
      sstIdNo: !this.inTh() && sstEffective && Boolean(sstIdNo) ? `${'SST ID No.:'}${sstIdNo}` : null,
      gstIdNo: this.getGstIdNo(),
      customerInfo: !isEmpty(customerInfo) ? customerInfo : null,
      receiptTitle,
      isReceiptTitleBig,
      orderNoName: this.getOrderNoInfo(modifiedReceiptTansaction).orderNoName,
      orderNo: this.getOrderNoInfo(modifiedReceiptTansaction).orderNo,
      receiptDate: this.getReceiptDate(modifiedReceiptTansaction),
      reprintDate: this.receiptTransaction.isReprint ? this.getReprintDate(modifiedReceiptTansaction) : null,
      receiptNumber: Boolean(receiptId) ? `${this.inTh() ? t('No') : 'Receipt #'}: ${receiptId}` : null,
      cashierInfo: this.getCashierInfo(),
      registerNumber: this.getMYRegisterNumber(),
      minNumber: this.getMYMinNumber(),
      reasonString: _reasonString,
      noteString: Boolean(comment) && transactionType === TransactionFlowType.PreOrder && channel !== 1 ? 'Note: ' + comment : null,
      columnTitleString: this.getColumnTitleString(),
      a4ColumnTitleString: this.getA4ColumnTitleString(),
      columnWidths: this.getColumnWidths(),
      showPurchasedItemsDiscount: false,
      purchasedItems: purchasedItems.filter(item => Boolean(item.itemName)),
      // Summary
      subtotalTitle,
      subtotal: subtotalValue,
      discountTitle: t('DISCOUNT'),
      discount: discountValue,
      a4Discount: discountValue,
      takeawayFeeName,
      takeawayFeeValue,
      shippingFeeName,
      shippingFeeValue,
      serviceChargeTitle: Boolean(serviceChargeRate)
        ? `${serviceChargePrefix}${t('Service Charge')} (${Number((serviceChargeRate * 100).toFixed(2))}%)`
        : `${serviceChargePrefix}${t('Service Charge')}`,
      serviceCharge: serviceChargeValue,
      smallOrderFeeTxt,
      smallOrderFeeValue,
      containerFeeTxt,
      containerFeeValue,
      taxTitle,
      tax: taxValue,
      roundingTitle: t('Rounding'),
      rounding: roundingValue,
      showOrderSummary,
      totalTitle: t('TOTAL'),
      total: totalValue,
      // Pre order
      showPreorderSummary: TransactionFlowType.PreOrder === transactionType,
      depositAmountTitle,
      depositAmount: `${currencySymbol} ${getLocaleNumberString(depositAmount)}`,
      unPaidBalanceTitle: this.getUnPaidBalanceTitle(),
      unPaidBalance: `${currencySymbol} ${getLocaleNumberString(unPaidBalance)}`,
      // payments
      payment: _payments,
      // TaxSummary
      showTaxSummary,
      taxSummaryTitleString: [sstEffective ? 'SST Summary' : gstEffective ? 'GST Summary' : 'Tax Summary', t('Amount'), 'Tax'],
      taxSummaryItems,
      showReceiptStoreCredit,
      receiptStoreCreditTitleString: this.getReceiptStoreCreditTitleString(),
      earnedTitle: this.getEarnedTitle(),
      balanceTitle: this.getBalanceTitle(),
      spentTitle: this.getSpentTitle(),
      loyaltyEarned: getLocaleNumberString(loyaltyEarned),
      loyaltyBalance: getLocaleNumberString(loyaltyBalance),
      loyaltySpent: getLocaleNumberString(loyaltySpent),
      cashbackExpirationDesc,
      defaultLoyaltyRatio: defaultLoyaltyRatio,
      cashBackTxt: 'Cashback',
      qrCodeAboveInfo,
      qrCodeUnderInfo,
      enableCashback: enableCashback,
      enablePrintCashback,
      cashbackUrl: CASHBACK_URL() && enablePrintCashback ? CASHBACK_URL().replace('www', business) : '',
      enablePrintQRCode, // membbership
      enablePrintMemberShip, // membbership
      membershipSource: enablePrintMemberShip ? JoinMemberShipResource.ReceiptMembershipQR : null, // membbership
      membershipUrl: membershipUrl, // membbership
      membershipSmallTitle, // membbership
      membershipBoildTitle, // membbership
      membershipLargeContentTitle1, // membbership
      membershipLargeContentTitle2, // membbership
      membershipSmallBottomTitle, // membbership
      footerLabelString: this.getFooterLabelString(),
      storehubPoweredInfo: this.getStorehubPoweredInfo(),
      receiptId,
      showBarcode: showBarcode,
      showVatSummary: false,
      showDeliveryOrContactInfo,
      deliveryOrContactInfo,
      takeawayCharge,
      salesChannel,
      pickUpDate: formattedPickUpDate,
      usingDiscountLayout: this.isUsingDiscountLayout(),
      receiptFontScale: getReceiptFontScale(receiptFontSize),
      eInvoiceUrl: enableEInvoice && enableEInvoiceOnReceipt && isTransactionEInvoicePrintable() ? eInvoiceUrl : '',
      eInvoiceDescription: isRefund ? t('SCAN_TO_VIEW_CREDIT_NOTE') : isCancelled ? t('SCAN_TO_VIEW_E_INVOICE') : t('SCAN_TO_CLAIM_E_INVOICE'),
      enablePrintEInvoice: enableEInvoice && enableEInvoiceOnReceipt && isTransactionEInvoicePrintable(),
    };
  }
}
