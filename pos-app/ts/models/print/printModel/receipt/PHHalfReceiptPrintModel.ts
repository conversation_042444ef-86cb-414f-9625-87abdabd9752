import { receipt } from 'calculator-lib';
import { get, includes, map } from 'lodash';
import { call, put, select, take } from 'redux-saga/effects';
import * as Actions from '../../../../actions';
import { PrinterConfigType } from '../../../../actions';
import { BIRDiscountType, BIRStoreType, PrintingBusinessType, SuccessCode } from '../../../../constants';
import { t } from '../../../../constants/i18n';
import { SalesChannelType, TransactionFlowType } from '../../../../constants/transaction';
import DAL from '../../../../dal';
import { checkMRSBeforePrintHalfReceipt, onMRSInterceptor } from '../../../../sagas/mrs/checkSync';
import { columnTitles4, columnTitles5, columnWidths4, columnWidths5 } from '../../../../sagas/printing';
import {
  selectEmployeeId,
  selectEnableTakeaway,
  selectEnableUnitPriceRounding,
  selectLocalCountryMap,
  selectReceiptFontSize,
  selectStoreInfo,
} from '../../../../sagas/selector';
import { generateSSTInfo } from '../../../../sagas/transaction/common';
import { EmployeeType, PromotionType } from '../../../../typings';
import {
  getDisplayPriceLocaleNumberString,
  getLocaleNumberString,
  getNumberValue,
  getReceiptQtyString,
  isPositive,
  newConvertCurrencyToSymbol,
} from '../../../../utils';
import { getReceiptFontScale } from '../../../../utils/printer';
import { generatePromotionTitle } from '../../../../utils/promotion';
import { generateDescriptionString, isBirDiscountEffective } from '../../../../utils/transaction';
import { PrinterSetting } from '../../printer/PrinterSetting';
import { ReceiptTransaction } from '../../../transaction/ReceiptTransaction';
import { BaseReceiptPrintModel } from './BaseReceiptPrintModel';
import { isEmpty } from '../../../../utils/validator';

export class PHHalfReceiptPrintModel extends BaseReceiptPrintModel {
  constructor(receiptTransaction: ReceiptTransaction) {
    super(receiptTransaction);
  }

  public *getReceiptPrintingModel(PrinterSetting: PrinterSetting) {
    return yield call([this, this.generateHalfReceiptPrintingModel], PrinterSetting);
  }

  private getPHRegisterNumber() {
    return 'Register #: ' + this.receiptConfig.registerId;
  }

  private *generateHalfReceiptPrintingModel(PrinterSetting: PrinterSetting) {
    const printer: PrinterConfigType = PrinterSetting.getReceiptPrinter();
    if (!printer) {
      return null;
    }

    const data = yield call([this, this.generateHalfReceiptPrintingModelData]);
    if (!data) {
      return null;
    }
    return [
      {
        printerId: printer.printerId,
        businessType: PrintingBusinessType.TRANSACTION,
        data: data,
      },
    ];
  }

  private *generateHalfReceiptPrintingModelData() {
    const checkResult: Actions.MRSError = yield call(checkMRSBeforePrintHalfReceipt, {
      ...(this.receiptTransaction as any),
      isOnlineOrder: Boolean(this.receiptTransaction.isOnlineTransaction),
    });
    if (checkResult.errorCode !== SuccessCode) {
      onMRSInterceptor(checkResult);
      return null;
    }

    let employeeId = this.receiptTransaction.employeeId;
    if (!Boolean(employeeId)) {
      employeeId = yield select(selectEmployeeId);
    }
    const employee: EmployeeType = DAL.getEmployeeById(employeeId) as any;
    const cashier = employee ? `${employee.firstName} ${employee.lastName}` : '';

    const { storeName, currency, country, showStoreName, taxNameOnReceipt, poweredBy, includingTaxInDisplay, notes, birAccredited, showCustomerInfo } =
      this.receiptConfig;
    const localCountryMap = yield select(selectLocalCountryMap);
    const currencySymbol = newConvertCurrencyToSymbol(localCountryMap, currency);
    const usingDiscountLayout = !birAccredited;
    // const usingDiscountLayout = true;
    const unitPriceUnrounding = yield select(selectEnableUnitPriceRounding);

    let receiptTansaction;
    try {
      // TODO: Should move this when pos-calculate support for Philippine receipt
      receiptTansaction = receipt(this.receiptTransaction, includingTaxInDisplay, 'PH', usingDiscountLayout, unitPriceUnrounding);
    } catch (err) {
      console.log('receipt function error', err);
      return null;
    }
    const {
      receiptNumber: receiptId,
      serviceChargeRate,
      items,
      transactionType,
      isOpen,
      isCancelled,
      tableId,
      pickUpId,
      takeawayCharge,
      salesChannel,
      customerId,
      taxExemptedSales,
      zeroRatedSales,
      roundedAmount,
      pwdCount,
      seniorsCount,
    } = receiptTansaction;

    let addonBirCompliance = receiptTansaction.addonBirCompliance;
    if (isPositive(pwdCount) || isPositive(seniorsCount)) {
      addonBirCompliance = { discountType: BIRDiscountType.SCAndPWD };
    }

    const industry = this.receiptConfig.industry;
    const isRetail = industry == BIRStoreType.RetailStore;

    const purchasedItems = map(items, _item => {
      const { itemType, title, options, selectedOptions, notes, subTotal, comments, sn, receipt, itemChannel, isTakeaway } = _item;
      const optionsString = generateDescriptionString(this.receiptTransaction.isOfflineTransactionInEditing ? options : selectedOptions);
      const price = getNumberValue(receipt, 'price', 0.0);
      const qty = getNumberValue(_item, 'quantity', 0.0);
      const total = getNumberValue(receipt, 'total', 0.0);
      const a4Total = getNumberValue(receipt, 'a4Total', 0.0);
      const discount = getNumberValue(receipt, 'discount', 0.0);
      const discountWithoutPromo = getNumberValue(receipt, 'discountWithoutPromo', 0.0);

      const promotions = map(get(_item, 'promotions'), promotion => {
        const realmPromotion: PromotionType = DAL.getPromotionById(promotion.promotionId);
        const title = generatePromotionTitle(realmPromotion, promotion);
        const discount = get(promotion, 'discount', 0);
        return {
          promotionName: title,
          discount: discount > 0 ? `${usingDiscountLayout ? '-' : '— '}${getLocaleNumberString(discount)}` : null, // '— ', '-'
        };
      });

      const displayTakeawayCharge = Boolean(_item.takeawayCharge) ? _item.takeawayCharge : takeawayCharge;
      return {
        itemType: itemType || null,
        discount: getLocaleNumberString(discount), // discount - _totalPromotionDiscount maybe very small value
        quantity: getReceiptQtyString(qty),
        subTotal: getLocaleNumberString(subTotal),
        total: getLocaleNumberString(total),
        a4Total: getLocaleNumberString(a4Total),
        itemName: title,
        options: optionsString,
        notes: notes || comments || null,
        price: getDisplayPriceLocaleNumberString(price, unitPriceUnrounding),
        itemDiscountName: '*Item Discount',
        sn: Boolean(sn) ? `S/N: ${sn}` : null,
        promotions,
        itemDiscountValue:
          usingDiscountLayout && Boolean(discountWithoutPromo) && discountWithoutPromo > 0 ? `— ${getLocaleNumberString(discountWithoutPromo)}` : null,
        enableTakeaway: false, // CM-5743 there will not display the takeawayCharge under the item
        takeawayTxt: t('TakeawayTxt'),
        takeawayCharge: Boolean(displayTakeawayCharge) ? `(${currencySymbol} ${getLocaleNumberString(displayTakeawayCharge)})` : null,
      };
    });

    const { gstEffective, sstEffective } = yield call(generateSSTInfo, this.receiptTransaction as any);
    const pwdDiscount = get(receiptTansaction.receipt, 'pwdDiscount', 0.0);
    const discountable = get(receiptTansaction.receipt, 'discountable', 0.0);
    const seniorDiscount = get(receiptTansaction.receipt, 'seniorDiscount', 0.0);
    const athleteAndCoachDiscount = get(receiptTansaction.receipt, 'athleteAndCoachDiscount', 0.0);
    const soloParentDiscount = get(receiptTansaction.receipt, 'soloParentDiscount', 0.0);
    const medalOfValorDiscount = get(receiptTansaction.receipt, 'medalOfValorDiscount', 0.0);

    const lessVAT = get(receiptTansaction.receipt, 'lessVAT', 0.0);
    const serviceCharge = getNumberValue(receiptTansaction.receipt, 'serviceCharge', 0.0);
    const total = get(receiptTansaction.receipt, 'total', 0.0);
    // subtotal
    const subtotal = get(receiptTansaction.receipt, 'subtotal', 0.0);
    // adhoc discount
    const adhocDiscount = get(receiptTansaction.receipt, 'adhocDiscount', 0.0);
    // vat 12
    const vatAmount = get(receiptTansaction.receipt, 'vatAmount', 0.0);
    // amusement tax
    const amusementTax = get(receiptTansaction.receipt, 'amusementTax', 0.0);

    const enableTakeaway = yield select(selectEnableTakeaway);
    let showServiceCharge = false;
    for (let x = 0; x < purchasedItems.length; x++) {
      const item = purchasedItems[x];
      if ('ServiceCharge' === item.itemType) {
        showServiceCharge = true;
        break;
      }
    }

    let taxTitle = Boolean(taxNameOnReceipt) ? taxNameOnReceipt : 'Tax';
    if (gstEffective === true) {
      taxTitle = 'GST';
    }

    let orderNoName = null;
    let orderNo = null;

    if (enableTakeaway && salesChannel === SalesChannelType.TAKEAWAY) {
      orderNo = t('Takeaway');
    } else {
      if (Boolean(pickUpId)) {
        orderNoName = 'Order #';
        orderNo = pickUpId;
      }
      if (Boolean(tableId)) {
        orderNoName = 'Table #';
        orderNo = tableId;
      }
    }

    let customerInfo = null;
    if (Boolean(customerId) && showCustomerInfo.length > 0) {
      try {
        const immutableStoreInfo = yield select(selectStoreInfo);
        const business = immutableStoreInfo.get('name');

        yield put(Actions.getCustomerById({ customerId, bn: business }));
        const responseAction = yield take([Actions.getCustomerById.toString() + '.success', Actions.getCustomerById.toString() + '.failure']);
        if (responseAction.type === Actions.getCustomerById.toString() + '.success') {
          customerInfo = '';
          const customer = responseAction.payload;
          const { firstName, lastName, city, state, taxIdNo, phone, street1, street2, postalCode } = customer;
          if (Boolean(firstName)) {
            customerInfo += `${t('Name')}: `;
            customerInfo += firstName;
          }
          if (Boolean(lastName)) {
            customerInfo += ` ${lastName}`;
          }
          if (Boolean(street1)) {
            const addressParts = [street1];
            if (Boolean(street2)) {
              addressParts.push(street2);
            }
            if (Boolean(postalCode)) {
              addressParts.push(postalCode);
            }
            customerInfo += `\n${t('Address')}:  ${addressParts.join(', ')}`;
          }
          if (Boolean(city) && Boolean(state)) {
            customerInfo += '\n';
            customerInfo += t('City and State: ');
            customerInfo += `${city}, ${state}`;
          }
          if (Boolean(phone)) {
            customerInfo += '\n';
            customerInfo += `${t('Phone Number')}: `;
            customerInfo += `${phone}`;
          }
        } else {
          console.log('getCustomerById error:', responseAction.payload);
        }
      } catch (err) {
        console.log('getCustomerById error:', err);
      }
    }

    let subtotalValue = Boolean(subtotal) ? getLocaleNumberString(subtotal) : null;
    let discountValue = birAccredited ? getLocaleNumberString(discountable) : Boolean(adhocDiscount) ? '—' + getLocaleNumberString(adhocDiscount) : null;
    let lessVATValue = lessVAT > 0 && birAccredited ? `—${getLocaleNumberString(lessVAT)}` : null;
    if (birAccredited && !isBirDiscountEffective(addonBirCompliance, pwdDiscount, seniorDiscount)) {
      subtotalValue = getLocaleNumberString(discountable);
      discountValue = null;
      lessVATValue = null;
    }
    const discountNumberValue = get(this.receiptTransaction, 'discount', 0.0);
    const a4Discount = Boolean(discountNumberValue) && discountNumberValue > 0 ? `— ${currencySymbol} ${getLocaleNumberString(discountNumberValue)}` : null;
    if (Boolean(addonBirCompliance) && includes([BIRDiscountType.AthletesAndCoaches, BIRDiscountType.MedalofValor], addonBirCompliance.discountType)) {
      lessVATValue = null;
    }
    const totalValue = getLocaleNumberString(Boolean(total) ? total : 0);

    const takeawayCharges = getNumberValue(receiptTansaction.receipt, 'takeawayCharges', 0.0);
    const takeawayFeeName = 'Take away fee:';
    const takeawayFeeValue = Boolean(takeawayCharges) ? `${currencySymbol} ${getLocaleNumberString(takeawayCharges)}` : null;

    const subtotalTitle = 'SUBTOTAL:';
    const discountTitle = birAccredited ? 'DISCOUNTABLE:' : `${t('DISCOUNT')}:`;
    const adhocDiscountValue = birAccredited && adhocDiscount > 0 ? `—${getLocaleNumberString(adhocDiscount)}` : null;
    const seniorDiscountValue = Boolean(seniorDiscount) ? `—${getLocaleNumberString(seniorDiscount)}` : null;
    const pwdDiscountValue = Boolean(pwdDiscount) ? `—${getLocaleNumberString(pwdDiscount)}` : null;
    const athleteAndCoachDiscountValue = Boolean(athleteAndCoachDiscount) ? `—${getLocaleNumberString(athleteAndCoachDiscount)}` : null;
    const soloParentDiscountValue = Boolean(soloParentDiscount) ? `—${getLocaleNumberString(soloParentDiscount)}` : null;
    const medalOfValorDiscountValue = Boolean(medalOfValorDiscount) ? `—${getLocaleNumberString(medalOfValorDiscount)}` : null;
    const vatOf12Value = vatAmount > 0 && birAccredited ? getLocaleNumberString(vatAmount) : null;
    const amusementTaxValue = amusementTax > 0 && birAccredited ? getLocaleNumberString(amusementTax) : null;
    const serviceChargeValue = serviceCharge > 0 ? getLocaleNumberString(serviceCharge) : null;
    const roundingValue = roundedAmount ? `${getLocaleNumberString(roundedAmount)}` : null;

    const showOrderSummary =
      !isEmpty(subtotalTitle) ||
      !isEmpty(lessVATValue) ||
      !isEmpty(vatOf12Value) ||
      !isEmpty(discountValue) ||
      !isEmpty(adhocDiscountValue) ||
      !isEmpty(seniorDiscountValue) ||
      !isEmpty(pwdDiscountValue) ||
      !isEmpty(athleteAndCoachDiscountValue) ||
      !isEmpty(medalOfValorDiscountValue) ||
      !isEmpty(serviceChargeValue) ||
      !isEmpty(amusementTaxValue) ||
      !isEmpty(roundingValue);

    const model = {
      country,
      storeName: showStoreName ? storeName : null,
      birAccredited,
      address: null,
      phone: null,
      companyName: null,
      brn: null,
      sstEffective,
      sstIdNo: null,
      gstIdNo: null,
      receipt: null,
      customerInfo: customerInfo,
      receiptTitle: null,
      orderNoName,
      orderNo,
      // orderNoName: Boolean(tableId) ? 'Table #' : Boolean(pickUpId) ? 'Order #' : null,
      // orderNo: Boolean(tableId) ? tableId : Boolean(pickUpId) ? pickUpId : null,
      orderNumber: transactionType === TransactionFlowType.Sale && isCancelled !== true && Boolean(pickUpId) ? `Order #${pickUpId}` : null,
      tableNumber: transactionType === TransactionFlowType.Sale && isCancelled !== true && !Boolean(pickUpId) && Boolean(tableId) ? `Table #${tableId}` : null,
      receiptDate: null,
      receiptNumber: null,
      cashierInfo: `Cashier: ${cashier || ''}`,
      registerNumber: this.getPHRegisterNumber(),
      minNumber: null,
      columnTitleString: usingDiscountLayout ? columnTitles5 : columnTitles4,
      a4ColumnTitleString: columnTitles5,
      columnWidths: usingDiscountLayout ? columnWidths5 : columnWidths4,
      purchasedItems: purchasedItems.filter(item => Boolean(item.itemName)),
      // Summary
      subtotalTitle,
      subtotal: subtotalValue,
      takeawayFeeName,
      takeawayFeeValue,
      discountTitle,
      discount: discountValue,
      a4Discount,
      less12vatTitle: 'LESS 12% VAT:',
      less12vat: lessVATValue,
      adhocDiscountTitle: 'AD-HOC DISCOUNT:',
      adhocDiscount: adhocDiscountValue,
      seniorDiscountTitle: `SENIOR CITIZEN ${isRetail ? 5 : 20}%:`,
      seniorDiscount: seniorDiscountValue,
      pwdDiscountTitle: `PERSON WITH DISABILITY ${isRetail ? 5 : 20}%:`,
      pwdDiscount: pwdDiscountValue,
      athleteAndCoachDiscountTitle: 'NATIONAL ATHLETE/COACH 20%:',
      athleteAndCoachDiscount: athleteAndCoachDiscountValue,
      soloParentDiscountTitle: 'SOLO PARENT 10%',
      soloParentDiscount: soloParentDiscountValue,
      medalOfValorDiscountTitle: 'MEDAL OF VALOR 20%:',
      medalOfValorDiscount: medalOfValorDiscountValue,
      vatOf12Title: '12% VAT:',
      showOrderSummary,
      vatOf12: vatOf12Value,
      amusementTaxTitle: 'Amusement Tax:',
      amusementTax: amusementTaxValue,
      serviceChargeTitle:
        Boolean(serviceChargeRate) && Boolean(serviceCharge > 0)
          ? `${t('Service Charge')} (${Number((serviceChargeRate * 100).toFixed(2))}%)`
          : t('Service Charge'),
      serviceCharge: serviceChargeValue,
      roundingTitle: 'ROUNDING:',
      rounding: roundingValue,
      totalTitle: `${t('TOTAL')}:`,
      total: totalValue,
      payment: [],
      // TaxSummary
      showTaxSummary: false,
      taxSummaryTitleString: null,
      taxSummaryItems: [],
      showReceiptStoreCredit: false,
      receiptStoreCreditTitleString: [],
      loyaltyEarned: null,
      loyaltyBalance: null,
      loyaltySpent: null,
      defaultLoyaltyRatio: null,
      enablePrintCashback: false,
      enableCashback: false,
      vatRegisterFooterInfo: birAccredited ? 'THIS DOCUMENT IS NOT VALID FOR CLAIM OF INPUT TAX.' : null,
      cashbackUrl: null,
      footerLabelString: Boolean(notes) ? notes : t('Thank you for shopping at our store'),
      storehubPoweredInfo: poweredBy ? t('Powered by Storehub.com Cloud POS') : null,
      receiptId,
      showBarcode: false,
      usingDiscountLayout,

      // font size
      receiptFontScale: getReceiptFontScale(yield select(selectReceiptFontSize)),
    };
    const transactionId = this.receiptTransaction.transactionId;
    const ExcludedFirstTimeAction = DAL.getExcludedFirstTimeActionByTransactionId(transactionId);
    if (Boolean(ExcludedFirstTimeAction)) {
      yield put(
        Actions.employeePrintHalfReceipt({
          transactionId,
          isReprint: false,
        })
      );
    } else {
      DAL.saveExcludedFirstTimeAction({ action: 'print_half_receipt', transactionId });
    }
    return model;
  }
}
