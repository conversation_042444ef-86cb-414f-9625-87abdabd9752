import { receipt } from 'calculator-lib';
import { get, includes, map, toUpper } from 'lodash';
import moment from 'moment';
import { call, put, select, take } from 'redux-saga/effects';
import { PrinterConfigType } from '../../../../actions';
import { CASHBACK_URL, SHORT_MEMBERSHIP_DETAIL_URL, SHORT_NEW_JOIN_MEMBERSHIP_URL } from '../../../../config';
import PaymentOptions from '../../../../config/paymentOption';
import { BIRDiscountType, BIRStoreType, JoinMemberShipResource, PrintingBusinessType } from '../../../../constants';
import { t } from '../../../../constants/i18n';
import { OnlineOrderStatus, OrderChannel, ShippingType, TransactionFlowType } from '../../../../constants/transaction';
import DAL from '../../../../dal';
import { formatDateString } from '../../../../sagas/printing/common';
import { generateBIRReceiptFields, generateFDIReceiptTransaction } from '../../../../sagas/transaction/common';
import { SubscriptionPlan } from '../../../../sagas/transaction/saleFlow';
import { PromotionType } from '../../../../typings';
import {
  convertToNumber,
  getDisplayPriceLocaleNumberString,
  getLocaleNumberString,
  getNumberValue,
  getReceiptQtyString,
  getWithKeyString,
  isPositive,
  isValidNumber,
  isZero,
  newConvertCurrencyToSymbol,
} from '../../../../utils';
import { isAfterFDIBIROrder, isFDIOrder, shouldDisplayContainerFee, shouldDisplaySmallOrderFee } from '../../../../utils/beep';
import { createDate } from '../../../../utils/datetime';
import { getReceiptFontScale } from '../../../../utils/printer';
import { generatePromotionTitle } from '../../../../utils/promotion';
import { generateDescriptionString, isBirDiscountEffective, isLocalPreOrder, isPreOrderPickUp } from '../../../../utils/transaction';
import { ReceiptTransaction } from '../../../transaction/ReceiptTransaction';
import { PrinterSetting } from '../../printer/PrinterSetting';

import { BaseReceiptPrintModel } from './BaseReceiptPrintModel';
import { selectEnableUnitPriceRounding, selectGBEnableVoidReceipt, selectStoreInfo, selectStoreMembershipTiersLevel } from '../../../../sagas/selector';
import * as Actions from '../../../../actions';
import { isEmpty } from '../../../../utils/validator';

export class PHReceiptPrintModel extends BaseReceiptPrintModel {
  constructor(receiptTransaction: ReceiptTransaction) {
    super(receiptTransaction);
  }

  public *getReceiptPrintingModel(PrinterSetting: PrinterSetting) {
    return yield call([this, this.generatePHReceiptPrintingModel], PrinterSetting);
  }

  private getPHRegisterNumber() {
    return 'Register #: ' + this.receiptConfig.registerId;
  }

  private generatePHModifiedReceiptTransaction(unitPriceUnrounding: boolean) {
    const isTheOrderAfterFDIBIR = isAfterFDIBIROrder(this.receiptTransaction);
    const country = this.receiptConfig.country;

    try {
      return isTheOrderAfterFDIBIR
        ? generateFDIReceiptTransaction(this.receiptTransaction, country)
        : receipt(this.receiptTransaction, this.receiptConfig.includingTaxInDisplay, 'PH', this.isUsingDiscountLayout(), unitPriceUnrounding);
    } catch (exception) {
      console.log('calculate error', exception);
      return null;
    }
  }

  private generatePHTransactionNumber(
    receiptId: string,
    officialReceiptNumber: string,
    birAccredited: boolean,
    isBIROfficialReceipt: boolean,
    isCanceledOrderWithVoidNumber: boolean
  ): string | null {
    if (!birAccredited) {
      return `Transaction #: ${receiptId}`;
    } else if (isCanceledOrderWithVoidNumber) {
      return `Transaction Ref #: ${officialReceiptNumber || receiptId}`;
    } else if (isBIROfficialReceipt && !isEmpty(officialReceiptNumber)) {
      return `Transaction #: ${officialReceiptNumber}`;
    }
    return null;
  }

  private hasCustomer(): boolean {
    return Boolean(this.receiptTransaction.customer) && this.receiptTransaction.customer.customerId;
  }

  private isTPOrder(): boolean {
    return isFDIOrder(this.receiptTransaction.channel);
  }

  private generatePHOrderNumber(isBIROfficialReceipt: boolean, birReceiptId: string | null, isRetail: boolean): string | null {
    return isBIROfficialReceipt && !isEmpty(birReceiptId) ? 'SI #: ' + birReceiptId : null;
  }

  private generatePHOrderId(needPrintOrderId: boolean, receiptId: string): string | null {
    return needPrintOrderId ? `Order ID: ${receiptId}` : null;
  }

  private generatePHPurchasedItems(
    items: any[],
    currencySymbol: string,
    takeawayCharge: number,
    showNotes: boolean,
    usingDiscountLayout: boolean,
    unitPriceUnrounding: boolean
  ): any[] {
    return map(items, _item => {
      const { itemType, subTotal, title, selectedOptions, options, notes, comments, receipt, sn, itemChannel, isTakeaway } = _item;
      const optionsString = generateDescriptionString(selectedOptions || options);

      const price = getNumberValue(receipt, 'price', 0.0);
      const qty = getNumberValue(_item, 'quantity', 0.0);
      const total = getNumberValue(receipt, 'total', 0.0);
      const a4Total = getNumberValue(receipt, 'a4Total', 0.0);
      const discount = getNumberValue(receipt, 'discount', 0.0);
      const discountWithoutPromo = getNumberValue(receipt, 'discountWithoutPromo', 0.0);

      const promotions = map(get(_item, 'promotions'), promotion => {
        const realmPromotion: PromotionType = DAL.getPromotionById(promotion.promotionId);
        const title = generatePromotionTitle(realmPromotion, promotion);
        const discount = get(promotion, 'discount', 0);
        return {
          promotionName: title,
          discount: discount > 0 ? `${this.isUsingDiscountLayout() === true ? '-' : '— '}${getLocaleNumberString(discount)}` : null, // '— ', '-'
        };
      });
      const displayTakeawayCharge = Boolean(_item.takeawayCharge) ? _item.takeawayCharge : takeawayCharge;

      return {
        itemType,
        discount: getLocaleNumberString(discount),
        quantity: getReceiptQtyString(qty),
        subTotal: getLocaleNumberString(subTotal),
        itemName: title,
        total: getLocaleNumberString(total),
        a4Total: getLocaleNumberString(a4Total),
        options: optionsString,
        notes: showNotes ? notes || comments : null,
        price: getDisplayPriceLocaleNumberString(price, unitPriceUnrounding),
        sn: Boolean(sn) ? `S/N: ${sn}` : null,
        promotions,
        itemDiscountName: '*Item Discount',
        itemDiscountValue:
          usingDiscountLayout && Boolean(discountWithoutPromo) && discountWithoutPromo > 0 ? `- ${getLocaleNumberString(discountWithoutPromo)}` : null,
        enableTakeaway: false, // CM-5743 there will not display the takeawayCharge under the item
        takeawayTxt: t('TakeawayTxt'),
        takeawayCharge: Boolean(displayTakeawayCharge) ? `(${currencySymbol} ${getLocaleNumberString(displayTakeawayCharge)})` : null,
      };
    });
  }

  private generatePHPayments(
    payments: any[],
    currencySymbol: string,
    enableCashback: boolean,
    transactionType: any,
    isTPOrder: boolean,
    orderPaymentMethod?: string
  ) {
    let depositAmountTitle = null;

    if (isTPOrder) {
      return { depositAmountTitle, _payments: null };
    }

    const _payments = map(payments, _item => {
      const { paymentMethod, paymentMethodId, cashTendered, amount, roundedAmount, subType, isDeposit, type, isOnline } = _item;

      let _name: string = null;
      if (paymentMethod && isOnline) {
        // Beep Ordering payment method name
        _name = paymentMethod || type;
      } else if (this.receiptTransaction.isOnlineTransaction && !isOnline) {
        // Pay By Cash Ordering payment method name
        _name = paymentMethod;
      } else {
        _name = PaymentOptions.getPaymentNameForReceipt(paymentMethodId, enableCashback);
      }

      if (isEmpty(_name)) {
        _name = paymentMethod || type;
      }

      let paymentMethodName = toUpper(_name);
      let _cashTendered = cashTendered;
      if (!isValidNumber(_cashTendered)) {
        _cashTendered = amount;
      }

      let amountValue = `${currencySymbol} ${getLocaleNumberString(amount)}`;
      let changeValue = null;
      if (Boolean(isDeposit) && transactionType !== TransactionFlowType.PreOrder) {
        paymentMethodName = `${paymentMethodName} ${t('DEPOSIT')}`;
      } else if (paymentMethodId === 0) {
        amountValue = `${currencySymbol} ${getLocaleNumberString(_cashTendered)}`;
        changeValue = `${currencySymbol} ${getLocaleNumberString(_cashTendered - amount)}`;
      }
      if (this.receiptTransaction.isOnlineTransaction && this.receiptTransaction.isReprint) {
        changeValue = `${currencySymbol} ${getLocaleNumberString(_item?.changeAmount ?? 0)}`;
      }
      if (Boolean(isDeposit)) {
        depositAmountTitle = `${paymentMethodName} ${t('DEPOSIT')}`;
      }
      if (orderPaymentMethod === 'Online') {
        changeValue = null;
      }

      return {
        paymentMethodId: isValidNumber(paymentMethodId) ? Number(paymentMethodId) : 0,
        paymentMethodName,
        cashTendered: _cashTendered,
        amount: amountValue,
        roundedAmount,
        subType,
        changeTitle: t('CHANGE'),
        changeValue,
      };
    });

    return { depositAmountTitle, _payments };
  }

  private *generatePHReceiptPrintingModel(PrinterSetting: PrinterSetting) {
    const printer: PrinterConfigType = PrinterSetting.getReceiptPrinter();
    if (!printer) {
      return null;
    }

    const data = yield call([this, this.generatePHReceiptPrintingModelData]);

    const requestPrintingModel = [
      {
        printerId: printer.printerId,
        businessType: PrintingBusinessType.TRANSACTION,
        data: data,
      },
    ];
    if (!!data.needPrintTwoCopies && !this.receiptTransaction.isReprint) {
      requestPrintingModel.push({
        printerId: printer.printerId,
        businessType: PrintingBusinessType.TRANSACTION,
        data: data,
      });
    }
    return requestPrintingModel;
  }

  private *generatePHReceiptPrintingModelData() {
    // TODO : Online Order BIR
    // 打印前对online order做拦截，需要生成seqNumber，如果生成成功那么就打印正式receipt，失败就打印非正式receipt
    yield call(generateBIRReceiptFields, this.receiptTransaction);

    const {
      country,
      currency,
      gstIdNo,
      minNo,
      showNotes,
      enableCashback,
      assignTableID,
      autoOrderId,
      showCustomerInfo,
      serialNo,
      birAccredited,
      isVATRegistered,
      birAccrInfo,
      birAccrNo,
      ptu,
      ptuDateIssued,
      defaultLoyaltyRatio,
      business,
      showBarcode,
      disableCashbackFromPOS,
      membershipEnabled,
      enableStoreCredit,
      localCountryMap,
      enablePoints,
    } = this.receiptConfig;
    const unitPriceUnrounding = yield select(selectEnableUnitPriceRounding);

    const isTPOrder = isFDIOrder(this.receiptTransaction.channel);
    const isTheOrderAfterFDIBIR = isAfterFDIBIROrder(this.receiptTransaction);
    const currencySymbol = newConvertCurrencyToSymbol(localCountryMap, currency);
    const modifiedReceiptTansaction = this.generatePHModifiedReceiptTransaction(unitPriceUnrounding);
    if (!modifiedReceiptTansaction) return;

    const {
      receiptNumber: receiptId,
      officialReceiptNumber, // BIR
      birReceiptId, // BIR
      isBIROfficialReceipt = false, // BIR
      needPrintOrderId = false, // BIR
      createdDate: receiptDate,
      items,
      payments,
      roundedAmount,
      transactionType,
      isOpen,
      comment,
      tableId,
      pickUpId,
      pickUpDate,
      serviceChargeRate,
      isCancelled,
      returnReason,
      otherReason,
      channel,
      depositAmount,
      taxableSales,
      taxExemptedSales,
      zeroRatedSales,
      shippingType,
      contactDetail,
      takeawayCharge,
      salesChannel,
      customerId,
      isOnlineOrder,
      pwdCount,
      seniorsCount,
      birVoidId,
      cancelledAt,
      modifiedDate,
      modifiedTime,
      cancelledDate,
      status,
      cancelReasonDetail,
      cancelReason,
      // version, only for FDI BIR, if version is 1, then use the new format, else keep the original format
      paymentMethod,
    } = modifiedReceiptTansaction;
    const isCancelledOrder = isCancelled ?? modifiedReceiptTansaction.status === OnlineOrderStatus.Cancelled;
    let addonBirCompliance = modifiedReceiptTansaction.addonBirCompliance;
    if (isPositive(pwdCount) || isPositive(seniorsCount)) {
      addonBirCompliance = { discountType: BIRDiscountType.SCAndPWD };
    }

    const isOfflineTakeawayOrder = this.isOfflineTakeawayOrder(modifiedReceiptTansaction);
    const industry = this.receiptConfig.industry;
    const isRetail = industry == BIRStoreType.RetailStore;

    const gbEnableVoidReceipt = yield select(selectGBEnableVoidReceipt);
    const isCanceledOrderWithVoidNumber = birAccredited && gbEnableVoidReceipt && isCancelledOrder;

    // const transactionNumber = isBIROfficialReceipt && !isEmpty(officialReceiptNumber) ? 'Transaction #: ' + officialReceiptNumber : null;
    const transactionNumber = this.generatePHTransactionNumber(
      receiptId,
      officialReceiptNumber,
      birAccredited,
      isBIROfficialReceipt,
      isCanceledOrderWithVoidNumber
    );

    const orderNumber = this.generatePHOrderNumber(isBIROfficialReceipt, birReceiptId, this.receiptConfig.industry === BIRStoreType.RetailStore);

    const usingDiscountLayout = !birAccredited;
    const orderId = this.generatePHOrderId(needPrintOrderId, receiptId);
    const purchasedItems = this.generatePHPurchasedItems(items, currencySymbol, takeawayCharge, showNotes, usingDiscountLayout, unitPriceUnrounding);

    const { depositAmountTitle, _payments } = this.generatePHPayments(payments, currencySymbol, enableCashback, transactionType, isTPOrder, paymentMethod);

    const pwdDiscount = get(modifiedReceiptTansaction.receipt, 'pwdDiscount', 0.0);
    const discountable = get(modifiedReceiptTansaction.receipt, 'discountable', 0.0);
    const seniorDiscount = get(modifiedReceiptTansaction.receipt, 'seniorDiscount', 0.0);
    const athleteAndCoachDiscount = get(modifiedReceiptTansaction.receipt, 'athleteAndCoachDiscount', 0.0);
    const soloParentDiscount = get(modifiedReceiptTansaction.receipt, 'soloParentDiscount', 0.0);
    const medalOfValorDiscount = get(modifiedReceiptTansaction.receipt, 'medalOfValorDiscount', 0.0);

    const lessVAT = get(modifiedReceiptTansaction.receipt, 'lessVAT', 0.0);
    const serviceCharge = getNumberValue(modifiedReceiptTansaction.receipt, 'serviceCharge', 0.0);
    const total = get(modifiedReceiptTansaction.receipt, 'total', 0.0);
    const unPaidBalance = total - depositAmount || 0;
    // subtotal
    const subtotal = get(modifiedReceiptTansaction.receipt, 'subtotal', 0.0);
    // adhoc discount
    const adhocDiscount = get(modifiedReceiptTansaction.receipt, 'adhocDiscount', 0.0);
    // vat 12
    const vatAmount = get(modifiedReceiptTansaction.receipt, 'vatAmount', 0.0);
    // amusement tax
    const amusementTax = get(modifiedReceiptTansaction.receipt, 'amusementTax', 0.0);

    const customer = this.receiptTransaction.customer;
    let _customer: any = {};
    const hasCustomer = Boolean(customer) && customer.customerId;
    const hasCustomerId = !isEmpty(customerId);

    let _hasCustomer = false;
    let responseCustomer = null;
    if (this.receiptTransaction.isOnlineTransaction) {
      const { customerId } = this.receiptTransaction;
      if (!this.receiptTransaction.customer && customerId) {
        try {
          const immutableStoreInfo = yield select(selectStoreInfo);
          const business = immutableStoreInfo.get('name');

          yield put(Actions.getCustomerById({ customerId, bn: business }));
          const responseAction = yield take([Actions.getCustomerById.toString() + '.success', Actions.getCustomerById.toString() + '.failure']);
          if (responseAction.type === Actions.getCustomerById.toString() + '.success') {
            _hasCustomer = true;
            responseCustomer = responseAction.payload;
          } else {
            console.log('getCustomerById error:', responseAction.payload);
          }
        } catch (err) {
          console.log('getCustomerById error:', err);
        }
      }
    }

    let _enablePrintCashback =
      !disableCashbackFromPOS && enableCashback && !hasCustomer && !hasCustomerId && transactionType !== TransactionFlowType.Return && !membershipEnabled;

    if ((customer && customer.customerId) || _hasCustomer) {
      _customer = Object.assign({}, customer || responseCustomer);

      const street1 = get(customer, 'street1', '');
      const street2 = get(customer, 'street2', '');
      const postalCode = get(customer, 'postalCode');

      const city = get(customer, 'city', '');
      const state = get(customer, 'state', '');
      const customerAddress = [street1, street2, postalCode].filter(v => Boolean(v)).join(',');
      _customer.address = customerAddress;

      const cityStr = [city, state].filter(v => Boolean(v)).join(',');
      _customer.cityAndState = cityStr;
      _enablePrintCashback = false;
    }

    let _receiptDate = receiptDate;
    // Beep order doesn't have createdDate field
    if (!_receiptDate) {
      _receiptDate = modifiedReceiptTansaction.createdTime;
    }

    if (moment().diff(_receiptDate, 'minutes') > 30) {
      _enablePrintCashback = false;
    }

    const subscriptionPlan = this.receiptConfig.subscriptionPlan;
    const isLocalPreOrderRecord = !Boolean(this.receiptTransaction.isOnlineTransaction) && !isOnlineOrder && isLocalPreOrder(this.receiptTransaction as any);

    const showReceiptStoreCredit =
      _customer.isStoreCredit === true &&
      !isLocalPreOrderRecord &&
      ((enableStoreCredit && !isOnlineOrder && subscriptionPlan > SubscriptionPlan.Small) || (enableCashback && !enablePoints));

    const { loyaltyEarned, loyaltyBalance, loyaltySpent, cashbackExpirationDate } = _customer;

    const pd = moment(pickUpDate);
    let formattedPickUpDate = null;
    if (isLocalPreOrderRecord && !isPreOrderPickUp(this.receiptTransaction as any) && pd.isValid()) {
      formattedPickUpDate = t('Pick up Date & Time: ') + pd.format('YYYY.MM.DD HH:mm');
    }

    let cashbackExpirationDesc = null;
    if (cashbackExpirationDate && enableCashback) {
      cashbackExpirationDesc = `Cashback expires on ${createDate(cashbackExpirationDate, 'YYYY.MM.DD')}`;
    }

    // resonString
    let _reasonString = '';
    const avliableReason = otherReason || cancelReasonDetail || cancelReason || returnReason;
    if (isCanceledOrderWithVoidNumber && !isEmpty(avliableReason)) {
      _reasonString = 'Cancellation Reason: ' + avliableReason;
    } else if ((transactionType === TransactionFlowType.Return || isCancelled) && (Boolean(avliableReason) || Boolean(tableId))) {
      if (Boolean(avliableReason)) {
        _reasonString = 'Refund Reason: ' + avliableReason;
      } else {
        _reasonString = 'Table #: ' + tableId;
      }
    } else if ((Boolean(tableId) || Boolean(comment)) && transactionType !== TransactionFlowType.PreOrder && (Boolean(assignTableID) || Boolean(autoOrderId))) {
      _reasonString = Boolean(autoOrderId) ? '' : 'Table #: ' + (tableId ? tableId : comment);
    }

    let receiptTitle = null;
    let receiptTitlePH = null;
    let receiptTitlePHLabel = null;
    let condition = 'sale';
    if (transactionType === TransactionFlowType.Return) {
      condition = 'return';
    } else if (transactionType === TransactionFlowType.PreOrder) {
      condition = 'preorder';
    } else if (isOfflineTakeawayOrder) {
      condition = 'taxInvoice';
    } else if (birAccredited) {
      condition = 'taxInvoice';
    } else if (transactionType === TransactionFlowType.Sale && Boolean(pickUpDate)) {
      condition = 'pickup';
    }

    if (isCancelledOrder) {
      receiptTitle = 'CANCELLATION RECEIPT';
    } else {
      if (condition === 'return') {
        receiptTitle = 'RETURN RECEIPT';
      } else if (condition === 'preorder') {
        receiptTitle = 'Pre-order Receipt';
      } else if (condition === 'pickup') {
        receiptTitle = 'Receipt';
      } else if (condition === 'taxInvoice') {
        receiptTitle = 'TAX INVOICE';
      } else if (condition === 'QUOTATION') {
        receiptTitle = 'QUOTATION';
      }
    }

    if (isCanceledOrderWithVoidNumber) {
      receiptTitle = 'CANCELLATION RECEIPT';
      receiptTitlePH = null;
    } else if (birAccredited && (condition === 'taxInvoice' || transactionType === 'pickup')) {
      if (isBIROfficialReceipt) {
        // retail
        receiptTitle = null;
        receiptTitlePH = 'SALES INVOICE';
        // VAT-EXEMPT SALE : Transaction includes VAT Exempt Sales only
        // ZERO-RATED SALE : Transaction includes Zero-Rated Sales only
        if (isPositive(taxExemptedSales) && isZero(zeroRatedSales) && isZero(taxableSales)) {
          receiptTitlePHLabel = 'VAT-EXEMPT SALE';
          // 这里的判断条件不是很严谨，但是在实际应用场景中应该是满足的
          // 因为实际应用场景中一般不会出现订单金额时0的情况
        } else if (isPositive(zeroRatedSales) && isZero(taxExemptedSales) && isZero(taxableSales)) {
          receiptTitlePHLabel = 'ZERO-RATED SALE';
        }
      } else receiptTitlePH = null; // if  informal receipt, then no title
    }

    if (!isOpen && this.receiptTransaction.isReprint) {
      if (!isEmpty(receiptTitlePH)) {
        receiptTitlePH = receiptTitlePH + '-' + 'Reprinted';
      } else if (!isEmpty(receiptTitle)) {
        receiptTitle = receiptTitle + '-' + 'Reprinted';
      } else {
        receiptTitle = 'Reprinted';
      }
    }

    if (!isEmpty(receiptTitlePH) && !isEmpty(receiptTitlePHLabel)) {
      receiptTitlePH = `${receiptTitlePH}\n${receiptTitlePHLabel}`;
    }

    let _vatRegisterFooterInfo = null;
    if (birAccredited) {
      if (isVATRegistered && transactionType === TransactionFlowType.Sale && !isCanceledOrderWithVoidNumber) {
        _vatRegisterFooterInfo = 'This serves as your sales invoice.';
      } else {
        _vatRegisterFooterInfo = 'THIS DOCUMENT IS NOT VALID FOR CLAIM OF INPUT TAX.';
      }
    }

    // if  informal receipt, then no title
    if (birAccredited && !isBIROfficialReceipt) {
      _vatRegisterFooterInfo = 'THIS DOCUMENT IS NOT VALID FOR CLAIM OF INPUT TAX.';
    }

    let customerInfo = '';
    if (_customer && _customer.customerId && showCustomerInfo && showCustomerInfo.length > 0) {
      const { firstName, lastName, street1, street2, postalCode, city, state } = _customer;
      if (Boolean(firstName) || Boolean(lastName)) {
        customerInfo += `${t('Name')}: `;
        customerInfo = customerInfo + (Boolean(firstName) ? firstName + ' ' : '') + (lastName || '') + '\n';
      }
      if (Boolean(street1) || Boolean(street2) || Boolean(postalCode) || Boolean(city) || Boolean(state)) {
        const customerAddressInfo = [street1, street2, postalCode, city, state].filter(item => Boolean(item)).join(`${this.getComma()} `);
        customerInfo += `${t('Address')}: `;
        customerInfo = customerInfo + (Boolean(customerAddressInfo) ? customerAddressInfo + '\n' : '');
      }
      if (Boolean(_customer.phone)) {
        customerInfo += `${t('Phone Number')}: `;
        customerInfo = customerInfo + _customer.phone;
      }
    }

    let subtotalValue = Boolean(subtotal) ? getLocaleNumberString(subtotal) : null;
    let discountValue = birAccredited ? getLocaleNumberString(discountable) : Boolean(adhocDiscount) ? '—' + getLocaleNumberString(adhocDiscount) : null;
    let lessVATValue = lessVAT > 0 && birAccredited ? `—${getLocaleNumberString(lessVAT)}` : null;
    if (birAccredited && !isBirDiscountEffective(addonBirCompliance, pwdDiscount, seniorDiscount)) {
      subtotalValue = getLocaleNumberString(discountable);
      discountValue = null;
      lessVATValue = null;
    }
    const discountNumberValue = get(this.receiptTransaction, 'discount', 0.0);
    const a4Discount = Boolean(discountNumberValue) && discountNumberValue > 0 ? `— ${getLocaleNumberString(discountNumberValue)}` : null;
    if (Boolean(addonBirCompliance) && includes([BIRDiscountType.AthletesAndCoaches, BIRDiscountType.MedalofValor], addonBirCompliance.discountType)) {
      lessVATValue = null;
    }
    let totalValue = getLocaleNumberString(Boolean(total) ? total : 0);

    const vatExemptSales = birAccredited ? `${getLocaleNumberString(taxExemptedSales)}` : null;

    const zeroRatedSalesValue = birAccredited ? `${getLocaleNumberString(zeroRatedSales)}` : null;

    const takeawayCharges = getNumberValue(modifiedReceiptTansaction.receipt, 'takeawayCharges', 0.0);
    const takeawayFeeName = 'Take away fee:';
    const takeawayFeeValue = Boolean(takeawayCharges) ? getLocaleNumberString(takeawayCharges) : null;

    let shippingFeeName = null;
    let shippingFeeValue = null;

    const deliveryOrContactInfo: any = {};
    let deliveryInformationComment = null;
    const deliveryInformation = this.receiptTransaction.deliveryInformation;
    if ((channel === OrderChannel.OrderChannelQRCode || channel === OrderChannel.OrderChannelOnline || isTPOrder) && shippingType === ShippingType.DELIVERY) {
      if (deliveryInformation && deliveryInformation.length > 0) {
        let address = '';
        if (Boolean(deliveryInformation[0].address.address)) {
          address = address + deliveryInformation[0].address.address + ',';
        }
        if (Boolean(deliveryInformation[0].address.city)) {
          address = address + deliveryInformation[0].address.city + ',';
        }
        if (Boolean(deliveryInformation[0].address.postCode)) {
          address = address + deliveryInformation[0].address.postCode + ',';
        }
        if (Boolean(deliveryInformation[0].address.state)) {
          address = address + deliveryInformation[0].address.state + ',';
        }
        if (Boolean(deliveryInformation[0].address.country)) {
          address = address + deliveryInformation[0].address.country;
        }
        if (Boolean(deliveryInformation[0].shippingFee)) {
          const { shippingFee } = deliveryInformation[0];
          shippingFeeName = 'Delivery Fee:';
          shippingFeeValue = Boolean(shippingFee) ? getLocaleNumberString(shippingFee) : null;
        }
        if (channel === OrderChannel.OrderChannelQRCode || channel === OrderChannel.OrderChannelOnline) {
          deliveryOrContactInfo.shippingToName = 'Shipping To';
          deliveryOrContactInfo.name = getWithKeyString(`${t('Name')}: `, deliveryInformation[0].address.name);
          deliveryOrContactInfo.phone = getWithKeyString(`${t('Phone Number')}: `, deliveryInformation[0].address.phone);
          deliveryOrContactInfo.address = getWithKeyString(`${t('Address')}: `, address);
          deliveryInformationComment = deliveryInformation[0].comments;
        } else if (isTPOrder) {
          deliveryInformationComment = deliveryInformation[0].comments;
        }
      }
    } else if ((channel === OrderChannel.OrderChannelQRCode || isTPOrder) && !Boolean(tableId) && Boolean(pickUpId)) {
      if (channel === OrderChannel.OrderChannelQRCode) {
        deliveryOrContactInfo.shippingToName = null;
        deliveryOrContactInfo.name = Boolean(contactDetail) && Boolean(contactDetail.name) ? contactDetail.name : null;
        deliveryOrContactInfo.phone = Boolean(contactDetail) && Boolean(contactDetail.phone) ? contactDetail.phone : null;
        deliveryOrContactInfo.address = null;
      }
    }
    deliveryOrContactInfo.notes = (Boolean(comment) ? `${comment}.` : '').concat(
      Boolean(deliveryInformationComment) ? `${Boolean(comment) ? '\n' : ''}${deliveryInformationComment}.` : ''
    );
    if (Boolean(deliveryOrContactInfo.notes)) deliveryOrContactInfo.notesName = 'Note';

    let showDeliveryOrContactInfo = false;
    const deliveryOrContactInfoKeys = Object.keys(deliveryOrContactInfo);
    if (deliveryOrContactInfoKeys.length > 0) {
      for (let i = 0; i < Object.keys(deliveryOrContactInfo).length; i++) {
        if (Boolean(deliveryOrContactInfo[deliveryOrContactInfoKeys[i]])) {
          showDeliveryOrContactInfo = true;
          break;
        }
      }
    }

    // bir company name
    let _birCompanyName = null;
    if (birAccredited && isBIROfficialReceipt) {
      if (isVATRegistered) {
        _birCompanyName = 'VAT REG TIN: ';
      } else {
        _birCompanyName = 'NON-VAT REG TIN: ';
      }
      if (Boolean(gstIdNo)) {
        _birCompanyName = _birCompanyName + gstIdNo;
      }
    }

    // BIR Info
    let birInfoList = null;
    let addOnBirInfoList = null;
    if (birAccredited && isBIROfficialReceipt) {
      birInfoList = [];
      addOnBirInfoList = [];
      //  ['Name:', 'SC/PWD ID:', 'Address:', 'TIN:', 'Business Style:']
      const collectedInfo = get(addonBirCompliance, 'collectedInfo', {});
      birInfoList.push({ name: 'Name:', value: get(collectedInfo, 'name', null) });
      switch (get(addonBirCompliance, 'discountType')) {
        case BIRDiscountType.SCAndPWD:
          birInfoList.push({ name: 'SC/PWD ID:', value: null });
          addOnBirInfoList.push({ name: 'Signature:', value: null, needDedicatedSpace: true });
          break;
        case BIRDiscountType.SoloParent:
          birInfoList.push({ name: 'SPIC Number:', value: get(collectedInfo, 'spicId', null) });
          birInfoList.push({ name: 'Child/Children Name:', value: get(collectedInfo, 'nameOfChild', null) });
          addOnBirInfoList.push({ name: 'Signature:', value: null, needDedicatedSpace: true });
          break;
        case BIRDiscountType.AthletesAndCoaches:
          birInfoList.push({ name: 'PNSTM ID:', value: get(collectedInfo, 'pnstmId', null) });
          addOnBirInfoList.push({ name: 'Signature:', value: null, needDedicatedSpace: true });
          break;
        case BIRDiscountType.MedalofValor:
          birInfoList.push({ name: 'Medal of Valor ID:', value: get(collectedInfo, 'movId', null) });
          break;
        case BIRDiscountType.Diplomats:
          birInfoList.push({ name: 'DFA ID / VIC:', value: get(collectedInfo, 'dfaOrVicId', null) });
          break;
        default:
          birInfoList.push({ name: 'SC/PWD ID:', value: null });
          break;
      }
      birInfoList.push({ name: 'Address:', value: get(collectedInfo, 'address', null) });
      birInfoList.push({ name: 'TIN:', value: null });
      birInfoList.push({ name: 'Business Style:', value: null });
      birInfoList = birInfoList.concat(addOnBirInfoList);
    }

    let subtotalTitle = 'SUBTOTAL:';
    let discountTitle = birAccredited ? 'DISCOUNTABLE:' : `${t('DISCOUNT')}:`;
    let adhocDiscountValue = birAccredited && adhocDiscount > 0 ? `—${getLocaleNumberString(adhocDiscount)}` : null;
    let seniorDiscountValue = Boolean(seniorDiscount) ? `—${getLocaleNumberString(seniorDiscount)}` : null;
    let pwdDiscountValue = Boolean(pwdDiscount) ? `—${getLocaleNumberString(pwdDiscount)}` : null;
    let athleteAndCoachDiscountValue = Boolean(athleteAndCoachDiscount) ? `—${getLocaleNumberString(athleteAndCoachDiscount)}` : null;
    const soloParentDiscountValue = Boolean(soloParentDiscount) ? `—${getLocaleNumberString(soloParentDiscount)}` : null;
    let medalOfValorDiscountValue = Boolean(medalOfValorDiscount) ? `—${getLocaleNumberString(medalOfValorDiscount)}` : null;
    let vatOf12Value = vatAmount > 0 && birAccredited ? getLocaleNumberString(vatAmount) : null;
    let amusementTaxValue = amusementTax > 0 && birAccredited ? getLocaleNumberString(amusementTax) : null;
    let serviceChargeValue = serviceCharge > 0 ? getLocaleNumberString(serviceCharge) : null;
    let roundingValue = roundedAmount ? `${getLocaleNumberString(roundedAmount)}` : null;

    let smallOrderFeeTxt;
    let smallOrderFeeValue;
    let containerFeeTxt;
    let containerFeeValue;
    if (isTheOrderAfterFDIBIR) {
      const fdiReceipt = get(modifiedReceiptTansaction, 'receipt', {});
      const { total: fdiReceiptTotal, containerFee: fdiReceiptContainerFee, smallOrderFee: fdiReceiptSmallOrderFee, discount: fdiReceiptDiscount } = fdiReceipt;
      smallOrderFeeTxt = t('Small Order Fee');
      smallOrderFeeValue =
        shouldDisplaySmallOrderFee(modifiedReceiptTansaction) && Boolean(fdiReceiptSmallOrderFee) ? getLocaleNumberString(fdiReceiptSmallOrderFee) : null;
      containerFeeTxt = t('Container Fee');
      containerFeeValue =
        shouldDisplayContainerFee(modifiedReceiptTansaction) && Boolean(fdiReceiptContainerFee) ? getLocaleNumberString(fdiReceiptContainerFee) : null;
      totalValue = getLocaleNumberString(Boolean(fdiReceiptTotal) ? fdiReceiptTotal : 0);
      discountTitle = `${t('DISCOUNT')}:`;
      discountValue = Boolean(fdiReceiptDiscount) && fdiReceiptDiscount > 0 ? getLocaleNumberString(fdiReceiptDiscount) : null;

      shippingFeeValue =
        lessVATValue =
        subtotalTitle =
        adhocDiscountValue =
        seniorDiscountValue =
        pwdDiscountValue =
        athleteAndCoachDiscountValue =
        medalOfValorDiscountValue =
        vatOf12Value =
        serviceChargeValue =
        roundingValue =
        amusementTaxValue =
          null;
    } else {
      const smallOrderFee = get(this.receiptTransaction, 'smallOrderFee', null);
      smallOrderFeeTxt =
        channel === OrderChannel.OrderChannelShopee ? t('Small Order Fee') : channel === OrderChannel.OrderChannelFoodPanda ? t('Difference to Minimum') : '';
      smallOrderFeeValue = smallOrderFee !== null && smallOrderFee !== 0 ? getLocaleNumberString(smallOrderFee) : null;
    }
    if (!isEmpty(smallOrderFeeTxt)) {
      smallOrderFeeTxt = `${smallOrderFeeTxt}: `;
    }
    if (!isEmpty(containerFeeTxt)) {
      containerFeeTxt = `${containerFeeTxt}: `;
    }

    const showOrderSummary =
      !isEmpty(subtotalTitle) ||
      !isEmpty(lessVATValue) ||
      !isEmpty(vatOf12Value) ||
      !isEmpty(amusementTaxValue) ||
      !isEmpty(discountValue) ||
      !isEmpty(adhocDiscountValue) ||
      !isEmpty(seniorDiscountValue) ||
      !isEmpty(pwdDiscountValue) ||
      !isEmpty(athleteAndCoachDiscountValue) ||
      !isEmpty(medalOfValorDiscountValue) ||
      !isEmpty(shippingFeeValue) ||
      !isEmpty(serviceChargeValue) ||
      !isEmpty(smallOrderFeeValue) ||
      !isEmpty(containerFeeValue) ||
      !isEmpty(roundingValue);

    const hasCustomerOrCustomerId = this.hasCustomer() || hasCustomerId;
    const enablePrintCashback = _enablePrintCashback && Boolean(receiptId) && !this.isTPOrder();

    let enablePrintMemberShip = membershipEnabled && transactionType !== TransactionFlowType.Return;
    if (!enablePoints && hasCustomerOrCustomerId) {
      enablePrintMemberShip = false;
    }

    let qrCodeAboveInfo = null;
    let qrCodeUnderInfo = null;
    const membershipUrl = SHORT_NEW_JOIN_MEMBERSHIP_URL(
      business,
      this.receiptConfig.storeId,
      this.receiptTransaction.isOnlineTransaction ? channel : 2,
      receiptId,
      enablePoints && !enableCashback
    );

    let membershipSmallTitle = null;
    let membershipBoildTitle = null;
    let membershipLargeContentTitle1 = null;
    let membershipLargeContentTitle2 = null;
    let membershipSmallBottomTitle = null;
    let cashbackRate = defaultLoyaltyRatio === 0 ? '0' : ((1 / defaultLoyaltyRatio) * 100).toFixed(0);

    if (hasCustomerOrCustomerId) {
      yield put(
        Actions.getCustomer({
          business,
          customerId,
          needPointsBalance: true,
          needPointsTotalSpend: true,
          needMembershipDetail: true,
        })
      );
      const responseAction = yield take([Actions.getCustomer.toString() + '.success', Actions.getCustomer.toString() + '.failure']);
      if (responseAction.type === Actions.getCustomer.toString() + '.success') {
        if (responseAction.payload && responseAction.payload.customer) {
          const customer = responseAction.payload.customer;
          if (customer && customer.membershipTierInfo && customer.membershipTierInfo.membershipTierLevel) {
            const tierLevel = yield select(selectStoreMembershipTiersLevel(customer.membershipTierInfo.membershipTierLevel));
            if (tierLevel && tierLevel.cashbackRate) {
              cashbackRate = String(tierLevel.cashbackRate);
              console.log('cashbackRate', cashbackRate);
            }
          }
        }
      }
    }
    if (membershipEnabled && !hasCustomerOrCustomerId) {
      if (enableCashback && !enablePoints) {
        qrCodeAboveInfo = t('Expires in 12 hours');
        qrCodeUnderInfo = t('Use this cashback on your next visit');

        membershipSmallTitle = t('You’ve earned');
        membershipLargeContentTitle1 = `${cashbackRate}% Cashback`;
        membershipSmallBottomTitle = t('Scan to claim');
      } else if (enablePoints && !enableCashback) {
        qrCodeAboveInfo = t('Expires in 12 hours');
        qrCodeUnderInfo = t('Redeem rewards on your next visit');

        membershipSmallTitle = t('You’ve earned');
        membershipLargeContentTitle1 = 'Points';
        membershipSmallBottomTitle = t('Scan to claim');
      } else if (enableCashback && enablePoints) {
        qrCodeAboveInfo = t('Expires in 12 hours');
        qrCodeUnderInfo = t('Redeem rewards on your next visit');

        membershipSmallTitle = t('You’ve earned');
        membershipLargeContentTitle1 = 'Points &';
        membershipLargeContentTitle2 = `${cashbackRate}% Cashback`;
        membershipSmallBottomTitle = t('Scan to claim');
      } else if (!enablePoints && !enableCashback) {
        qrCodeAboveInfo = t('Expires in 12 hours');
        qrCodeUnderInfo = t('Redeem rewards on your next visit');

        membershipBoildTitle = t('Scan to join as a Member');
        membershipSmallBottomTitle = t('Exclusive discounts, promos, and more!');
      }
    } else if (enablePrintMemberShip && hasCustomerOrCustomerId) {
      if (enablePoints && !enableCashback) {
        qrCodeAboveInfo = t('Expires in 12 hours');
        qrCodeUnderInfo = t('Redeem rewards on your next visit');

        membershipSmallTitle = t('You’ve earned');
        membershipLargeContentTitle1 = 'Points';
      } else if (enablePoints && enableCashback) {
        qrCodeAboveInfo = t('Expires in 12 hours');
        qrCodeUnderInfo = t('Redeem rewards on your next visit');

        membershipSmallTitle = t('You’ve earned');
        membershipLargeContentTitle1 = 'Points &';
        membershipLargeContentTitle2 = `${cashbackRate}% Cashback`;
      }
    }

    const enablePrintQRCode = enablePrintCashback || enablePrintMemberShip;

    // [Retail & F&B] Receipt Copy Requirements: 2 Copies for SC/PWD, 1 Copy for Non SC/PWD (except Return Receipts)
    let needPrintTwoCopies = false;

    if (convertToNumber(pwdCount) > 0 || convertToNumber(seniorsCount) > 0) {
      needPrintTwoCopies = true;
    }
    const key = `Receipt-${receiptId}`;
    const voidNo = isCanceledOrderWithVoidNumber && !isEmpty(birVoidId) ? 'Void #: ' + birVoidId : null;

    let displayReceiptDate = null;
    if (isCanceledOrderWithVoidNumber && (!isEmpty(cancelledAt) || !isEmpty(cancelledDate) || !isEmpty(modifiedTime) || !isEmpty(modifiedDate))) {
      displayReceiptDate = 'Receipt Date: ' + formatDateString(cancelledAt || cancelledDate || modifiedDate || modifiedTime);
    } else if (!isEmpty(_receiptDate)) {
      displayReceiptDate = 'Receipt Date: ' + formatDateString(_receiptDate);
    }

    return {
      key,
      business, // membership
      country,
      birAccredited,
      transactionId: this.receiptTransaction.transactionId,
      storeName: this.getStoreName(),
      shippingType,
      address: this.getAddress(),
      phone: this.getPhone(),
      companyName: this.getCompanyName(),
      birCompanyName: _birCompanyName,
      customerInfo: !isEmpty(customerInfo) ? customerInfo : null,
      preOrderDate: this.getPreOrderDate(),
      receiptDate: displayReceiptDate,
      voidNo,
      reprintDate: this.receiptTransaction.isReprint ? 'Reprinted Date: ' + formatDateString(Date.now() / 1000) : null,
      orderId,
      receiptNumber: transactionNumber,
      orderNumber,
      minNumber: birAccredited && isBIROfficialReceipt ? 'MIN: ' + minNo : null,
      serialNumber: birAccredited && isBIROfficialReceipt ? 'Serial #: ' + serialNo : null,
      reasonString: _reasonString,
      cashierInfo: this.getCashierInfo(),
      registerNumber: this.getPHRegisterNumber(),
      noteString: Boolean(comment) && transactionType === TransactionFlowType.PreOrder && channel !== 1 ? 'Note: ' + comment : null,
      receiptTitle,
      receiptTitlePH,
      isReceiptTitleBig: false,
      orderNoName: this.getOrderNoInfo(modifiedReceiptTansaction).orderNoName,
      orderNo: this.getOrderNoInfo(modifiedReceiptTansaction).orderNo,
      showPurchasedItemsDiscount: false,
      columnTitleString: this.getColumnTitleString(),
      a4ColumnTitleString: this.getA4ColumnTitleString(),
      columnWidths: this.getColumnWidths(),
      purchasedItems: purchasedItems.filter(item => Boolean(item.itemName)),
      subtotalTitle,
      subtotal: subtotalValue,
      takeawayFeeName,
      takeawayFeeValue,
      shippingFeeName,
      shippingFeeValue,
      discountTitle,
      discount: discountValue,
      a4Discount,
      containerFeeTxt,
      containerFeeValue,
      smallOrderFeeTxt,
      smallOrderFeeValue,
      less12vatTitle: 'LESS 12% VAT:',
      less12vat: lessVATValue,
      adhocDiscountTitle: 'AD-HOC DISCOUNT:',
      adhocDiscount: adhocDiscountValue,
      seniorDiscountTitle: `SENIOR CITIZEN ${isRetail ? 5 : 20}%:`,
      seniorDiscount: seniorDiscountValue,
      pwdDiscountTitle: `PERSON WITH DISABILITY ${isRetail ? 5 : 20}%:`,
      pwdDiscount: pwdDiscountValue,
      athleteAndCoachDiscountTitle: 'NATIONAL ATHLETE/COACH 20%:',
      athleteAndCoachDiscount: athleteAndCoachDiscountValue,
      soloParentDiscountTitle: 'SOLO PARENT 10%',
      soloParentDiscount: soloParentDiscountValue,
      medalOfValorDiscountTitle: 'MEDAL OF VALOR 20%:',
      medalOfValorDiscount: medalOfValorDiscountValue,
      vatOf12Title: '12% VAT:',
      vatOf12: vatOf12Value,
      amusementTaxTitle: 'Amusement Tax:',
      amusementTax: amusementTaxValue,
      serviceChargeTitle: `${t('Service Charge')} (${Number((serviceChargeRate * 100).toFixed(2))}%):`,
      serviceCharge: serviceChargeValue,
      roundingTitle: 'ROUNDING:',
      rounding: roundingValue,
      showOrderSummary,
      totalTitle: `${t('TOTAL')}:`,
      total: totalValue,
      payment: _payments,
      amountOutStandingTitle: 'AMOUNT OUTSTANDING',
      amountOutStanding: transactionType === TransactionFlowType.PreOrder ? `${total - depositAmount}` : null,
      // Pre order
      showPreorderSummary: TransactionFlowType.PreOrder === transactionType,
      depositAmountTitle,
      depositAmount: getLocaleNumberString(depositAmount),
      unPaidBalanceTitle: this.getUnPaidBalanceTitle(),
      unPaidBalance: getLocaleNumberString(unPaidBalance),
      vatableSalesTitle: 'VATable sales',
      vatableSales: birAccredited ? getLocaleNumberString(taxableSales) : null,
      showVatSummary: birAccredited && isBIROfficialReceipt,
      vatAmountTitle: 'VAT Amount',
      vatAmount: birAccredited ? getLocaleNumberString(vatAmount) : null,
      vatExemptSalesTitle: 'VAT-Exempt Sales',
      vatExemptSales,
      zeroRatedSalesTitle: 'Zero-Rated Sales',
      zeroRatedSales: zeroRatedSalesValue,
      birInfoList,
      onlineChannelNotesTitle: null,
      onlineChannelNotesContent: null,
      onlinePickUpNoteTitle: 'Pick Up Note:',
      onlineOrderNoteContent: null,
      preOrderNotes: transactionType === TransactionFlowType.PreOrder && Boolean(pickUpDate) ? 'Pick up date: ' + formatDateString(pickUpDate) : null,
      footerLabelString: this.getFooterLabelString(),
      birAccrInfo: birAccredited && isBIROfficialReceipt ? birAccrInfo.replace(/\r\n\r\n/, '\r\n\r\nPOS Provider: ') : null,
      accrNumber: birAccredited && isBIROfficialReceipt ? 'Accr.#: ' + birAccrNo : null,
      ptuNumber: birAccredited && isBIROfficialReceipt ? 'PTU #: ' + ptu : null,
      dateIssueNumber: birAccredited ? `Date Issued: ${Boolean(ptuDateIssued) ? ptuDateIssued : ''}` : null,
      vatRegisterFooterInfo: _vatRegisterFooterInfo,
      storehubPoweredInfo: this.getStorehubPoweredInfo(),
      receiptId,
      showReceiptStoreCredit,
      receiptStoreCreditTitleString: this.getReceiptStoreCreditTitleString(),
      earnedTitle: this.getEarnedTitle(),
      balanceTitle: this.getBalanceTitle(),
      spentTitle: this.getSpentTitle(),
      loyaltyEarned: getLocaleNumberString(loyaltyEarned),
      loyaltyBalance: getLocaleNumberString(loyaltyBalance),
      loyaltySpent: getLocaleNumberString(loyaltySpent),
      cashbackExpirationDesc,
      defaultLoyaltyRatio,
      cashBackTxt: t('Cashback'),
      enablePrintCashback,
      qrCodeAboveInfo, // membbership
      qrCodeUnderInfo, // membbership
      enableCashback,
      cashbackUrl: CASHBACK_URL() && enablePrintCashback ? CASHBACK_URL().replace('www', business) : '',
      enablePrintQRCode, // membbership
      enablePrintMemberShip, // membbership
      membershipSource: enablePrintMemberShip ? JoinMemberShipResource.ReceiptMembershipQR : null, // membbership
      membershipUrl: membershipUrl, // membbership
      membershipSmallTitle, // membbership
      membershipBoildTitle, // membbership
      membershipLargeContentTitle1, // membbership
      membershipLargeContentTitle2, // membbership
      membershipSmallBottomTitle, // membbership
      showBarcode,
      showDeliveryOrContactInfo,
      deliveryOrContactInfo,
      salesChannel,
      takeawayCharge,
      pickUpDate: formattedPickUpDate,
      usingDiscountLayout,
      needPrintTwoCopies,
      receiptFontScale: getReceiptFontScale(this.receiptConfig.receiptFontSize),
    };
  }
}
