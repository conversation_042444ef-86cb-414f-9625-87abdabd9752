import { forEach, get, isArray, last, map, pick } from 'lodash';
import { PrintingBusinessType } from '../../../../constants';
import { getCountNumberString, getLocaleNumberString, getUnNullPrintString } from '../../../../utils';
import { SMXReadingReportConfig } from '../../config/report/SMXReadingReportConfig';
import { PrinterSetting } from '../../printer/PrinterSetting';

export class SMXReadingReportModel {
  private config: SMXReadingReportConfig;
  private report;
  constructor(report, config: SMXReadingReportConfig) {
    this.report = report;
    this.config = config;
  }

  private model: Partial<SMXReadingReportPrintData> = {};

  private getSummaryLines() {
    const { grossSales, grossWithVoid, discount, netSalesWithVAT, salesBreakdown, totalRefunds, vatAdjustment, netSales, voidedSales, voidedBilled } =
      this.report;
    const summaryLines: CommonLineItem[] = [];

    summaryLines.push({ name: 'Gross Sales', value: getLocaleNumberString(grossSales), inRight: true });
    summaryLines.push({ name: 'Gross w/ void', value: getLocaleNumberString(grossWithVoid), inRight: true });
    summaryLines.push({ name: 'Net Sales w/ VAT', value: getLocaleNumberString(netSalesWithVAT), inRight: true });

    // discount
    const pwdDiscount = get(discount, 'pwd', 0);
    const seniorCitizenDiscount = get(discount, 'seniorCitizenDiscount', 0);
    const totalDiscounts = get(discount, 'totalDiscounts', 0);
    summaryLines.push({ name: 'Discount', value: null, inRight: true, isTitle: true, hasMargin: true });
    summaryLines.push({ name: 'PWD', value: getLocaleNumberString(pwdDiscount), inRight: true });
    summaryLines.push({ name: 'MEMC Discount', value: getLocaleNumberString(0), inRight: true });
    summaryLines.push({ name: 'Senior Citizen Ceiling', value: getLocaleNumberString(0), inRight: true });
    summaryLines.push({ name: 'Senior Citizen Discount', value: getLocaleNumberString(seniorCitizenDiscount), inRight: true });

    // salesBreakdown
    const vatableSales = get(salesBreakdown, 'vatableSales', 0);
    const vatAmount = get(salesBreakdown, 'vatAmount', 0);
    const vatExemptSales = get(salesBreakdown, 'vatExemptSales', 0);
    const zeroRatedSales = get(salesBreakdown, 'zeroRatedSales', 0);
    summaryLines.push({ name: 'Total Discounts', value: getLocaleNumberString(totalDiscounts), inRight: true, hasMargin: true });
    summaryLines.push({ name: 'Total Refunds', value: getLocaleNumberString(totalRefunds), inRight: true });
    summaryLines.push({ name: 'VAT ADJUSMENT', value: getLocaleNumberString(vatAdjustment), inRight: true });

    summaryLines.push({ name: 'Breakdown of Sales', value: null, inRight: true, isTitle: true, hasMargin: true });
    summaryLines.push({ name: 'VATABLE SALES', value: getLocaleNumberString(vatableSales), inRight: true });
    summaryLines.push({ name: 'VAT AMOUNT', value: getLocaleNumberString(vatAmount), inRight: true });
    summaryLines.push({ name: 'Vat Exempt Sales', value: getLocaleNumberString(vatExemptSales), inRight: true });
    summaryLines.push({ name: 'Zero Rated Sales', value: getLocaleNumberString(zeroRatedSales), inRight: true });

    summaryLines.push({ name: 'Net Sales', value: getLocaleNumberString(netSales), inRight: true, hasMargin: true });

    summaryLines.push({ name: 'Voided Sales', value: getLocaleNumberString(voidedSales), inRight: true, hasMargin: true });
    summaryLines.push({ name: 'Voided Billed', value: getLocaleNumberString(voidedBilled), inRight: true });
    this.model.summaryLines = summaryLines;
  }

  private getServiceTypeSaleLines() {
    const serviceTypeSaleLines: CommonLineItem[] = [];
    // serviceTypeSales
    const qsrDineIn = get(this.report.serviceTypeSales, 'qsrDineIn', 0);
    const serviceTypeSalesCount = get(this.report.serviceTypeSales, 'count', 0);
    serviceTypeSaleLines.push({ name: 'Service Type Sale', value: null, inRight: true, isTitle: true });
    serviceTypeSaleLines.push({ name: 'QSR Dine-in', value: getLocaleNumberString(qsrDineIn), inRight: true });
    serviceTypeSaleLines.push({ name: 'Count', value: getCountNumberString(serviceTypeSalesCount), inRight: true });
    this.model.serviceTypeSaleLines = serviceTypeSaleLines;
  }

  private getHourlySales() {
    const hourlySalesLines: CommonLineItem[] = [];
    hourlySalesLines.push({ name: 'Hourly Sales', value: '', inRight: false, hasMargin: true, isTitle: true });
    const hourlySales = this.report.hourlySales;
    forEach(Object.keys(hourlySales), (name, index) => {
      const hourlySale = get(hourlySales, name, {});
      const { trxCount, sales } = hourlySale;
      hourlySalesLines.push({
        name,
        count: getCountNumberString(trxCount),
        value: getLocaleNumberString(sales),
        inRight: false,
        hasMargin: index === 0,
        isTitle: false,
      });
    });

    hourlySalesLines.push({
      name: 'Total',
      count: getCountNumberString(this.report.totalHourlyTrxCount),
      value: getLocaleNumberString(this.report.totalHourlySales),
      inRight: false,
      hasMargin: true,
      isTitle: false,
    });
    this.model.hourlySalesLines = hourlySalesLines;
  }

  private getBreakDownOfTenderLines() {
    const breakDownOfTenderLines: CommonLineItem[] = [];
    const breakdownTender = this.report.breakdownTender;
    breakDownOfTenderLines.push({ name: 'Breakdown of Tender', value: null, inRight: true, isTitle: true });
    forEach(breakdownTender, (value, key) => {
      breakDownOfTenderLines.push({
        name: key,
        value: getLocaleNumberString(value),
        inRight: false,
        isTitle: false,
      });
    });
    this.model.breakDownOfTenderLines = breakDownOfTenderLines;
  }

  private getIncomeHeadSalesLines() {
    const incomeHeadSalesLines: CommonLineItem[] = [];
    const incomeHeadSales = this.report.incomeHeadSales;
    isArray(incomeHeadSales) &&
      forEach(incomeHeadSales, (item, index) => {
        const { name, totalUnit, totalAmount } = item;
        incomeHeadSalesLines.push({
          name: getUnNullPrintString(name),
          count: getCountNumberString(totalUnit),
          value: getLocaleNumberString(totalAmount),
          inRight: false,
          hasMargin: index === 0,
          isTitle: false,
        });
      });
    this.model.incomeHeadSalesLines = incomeHeadSalesLines;
  }

  private getCashDrawerLines() {
    const cashDrawerLines: CommonLineItem[] = [];
    cashDrawerLines.push({ name: 'Cash Drawer', value: null, inRight: false, isTitle: true });
    cashDrawerLines.push({ name: `Terminal ${this.report.terminal}`, value: null, inRight: false, isTitle: false });
    const cashDrawerNames = ['Total Cash Value', 'Cash Float', 'Cash Sales', 'Over/Short'];
    const cashDrawerValues = Object.values<number | string>(this.report.cashDrawer);
    forEach(cashDrawerValues, (value, index) => {
      cashDrawerLines.push({
        name: cashDrawerNames[index],
        value: getLocaleNumberString(value),
        inRight: false,
        isTitle: false,
      });
    });
    this.model.cashDrawerLines = cashDrawerLines;
  }

  private getCapitalizedLines() {
    const { lastBillNo, transactionCount, guestCount, averagePerTransaction } = this.report;
    const capitalizedLines: CommonLineItem[] = [];
    capitalizedLines.push({ name: 'LAST BILL NO.', value: lastBillNo, inRight: true });
    capitalizedLines.push({ name: 'TRANSACTION COUNT', value: getCountNumberString(transactionCount), inRight: true });
    capitalizedLines.push({ name: 'GUEST COUNT', value: getCountNumberString(guestCount), inRight: true });
    capitalizedLines.push({ name: 'AVERAGE PER TRANSACTION', value: getLocaleNumberString(averagePerTransaction), inRight: true });
    this.model.capitalizedLines = capitalizedLines;
  }

  private getHeader() {
    Object.assign(
      this.model,
      pick(this.config, ['businessName', 'storeName', 'storeAddress', 'vatRegTin', 'minNo', 'serialNo', 'reportTitle', 'receiptFontScale'])
    );
    this.getSummaryLines();
    this.getServiceTypeSaleLines();
  }

  private getSecond() {
    this.getHourlySales();
    this.getBreakDownOfTenderLines();
    this.getIncomeHeadSalesLines();
    this.getCashDrawerLines();
    this.getCapitalizedLines();
  }

  private getProductMixSummary() {
    const mixSummaryLinesGroups: MixSummaryLineItem[][] = [[]];
    const productMixSummary = this.report.productMixSummary;
    mixSummaryLinesGroups[0].push({ names: ['PRODUCT MIX SUMMARY'], isTitle: true, isSingleLine: true });
    if (isArray(productMixSummary)) {
      const serviceTypeSet = new Set(map(productMixSummary, item => item.serviceType));
      const sortProductMixSummary = data => {
        const serviceTypeOrder = Array.from(serviceTypeSet);
        return data.sort((a, b) => {
          const indexA = serviceTypeOrder.indexOf(a.serviceType);
          const indexB = serviceTypeOrder.indexOf(b.serviceType);
          return indexA - indexB;
        });
      };

      const sortedProductMixSummary: any[] = sortProductMixSummary([...productMixSummary]);
      forEach(sortedProductMixSummary, (summary, index) => {
        const { serviceType, groupName, items, totalQuantity, totalAmount } = summary;
        const curMixSummaryLinesIndex = mixSummaryLinesGroups.length - 1;
        let curMixSummaryLines = mixSummaryLinesGroups[curMixSummaryLinesIndex];
        if (index !== 0) {
          curMixSummaryLines.push({ names: [], isDivider: true });
        }
        if (serviceTypeSet.has(serviceType)) {
          curMixSummaryLines.push({ names: [`Service Type: ${serviceType}`], isSingleLine: true });
          serviceTypeSet.delete(serviceType);
        }
        curMixSummaryLines.push({ names: [`Group name: ${groupName}`], isSingleLine: true });
        if (isArray(items)) {
          curMixSummaryLines.push({ names: ['Description'], isSingleLine: true });
          forEach(items, item => {
            // segmented printing
            if (curMixSummaryLines.length >= 15) {
              mixSummaryLinesGroups.push([]);
              curMixSummaryLines = last(mixSummaryLinesGroups);
            }
            const { name, totalQuantity, unitPrice, totalPrice } = item;
            curMixSummaryLines.push({ names: [name], isSingleLine: true });
            curMixSummaryLines.push({
              names: [getCountNumberString(totalQuantity), getLocaleNumberString(unitPrice), getLocaleNumberString(totalPrice)],
            });
          });
        }
        curMixSummaryLines.push({ names: [`Total Quantity: ${getCountNumberString(totalQuantity)}`], isSingleLine: true });
        curMixSummaryLines.push({ names: [`Total Amount: ${getLocaleNumberString(totalAmount)}`], isSingleLine: true });
      });
    }
    this.model.mixSummaryLinesGroups = mixSummaryLinesGroups;
  }

  private getTotalLines() {
    const totalLines: CommonLineItem[] = [];
    totalLines.push({ name: 'Grand Total Qty: ', value: getCountNumberString(this.report.grandTotalQty), inRight: true });
    totalLines.push({ name: 'Grand Total Amt: ', value: getLocaleNumberString(this.report.grandTotalAmt), inRight: true });
    this.model.totalLines = totalLines;
  }
  private getDateLines() {
    const dateLines: CommonLineItem[] = [];
    dateLines.push({ name: 'Receipt Printed', value: this.report.receiptPrintedDate, inRight: false });
    dateLines.push({ name: 'on', value: this.report.receiptPrintedTime, inRight: false });
    dateLines.push({ name: 'POS Date', value: this.report.reportDate, inRight: false });
    this.model.dateLines = dateLines;
  }
  private getFooterLines() {
    const footerLines: CommonLineItem[] = [];
    footerLines.push({ name: 'Shift #: ', value: getUnNullPrintString(this.report.shift), inRight: false, hasMargin: true });
    footerLines.push({ name: 'Terminal #: ', value: getUnNullPrintString(this.report.terminal), inRight: false });
    footerLines.push({ name: 'Generated by: ', value: getUnNullPrintString(this.config.employeeName), inRight: false });
    this.model.footerLines = footerLines;
  }

  private getFooter() {
    this.getTotalLines();
    this.getDateLines();
    this.getFooterLines();
  }

  public getRequestData(printerSetting: PrinterSetting) {
    const printerId = printerSetting.getOnlineReceiptPrinterId();
    if (!printerId) {
      return null;
    }

    this.getHeader();
    this.getSecond();
    this.getProductMixSummary();
    this.getFooter();

    return [
      {
        printerId,
        businessType: PrintingBusinessType.SM_XReading_REPORT,
        data: this.model,
      },
    ];
  }
}

export interface CommonLineItem {
  name: string;
  count?: string;
  value?: string | null;
  inRight?: boolean;
  hasMargin?: boolean;
  isTitle?: boolean;
}

export interface MixSummaryLineItem {
  names: string[];
  isDivider?: boolean;
  isTitle?: boolean;
  isSingleLine?: boolean;
}

export interface SMXReadingReportPrintData {
  businessName: string;
  storeName: string;
  storeAddress: string;
  vatRegTin: string;
  minNo: string;
  serialNo: string;
  reportTitle: string;
  summaryLines: CommonLineItem[];
  serviceTypeSaleLines: CommonLineItem[];
  hourlySalesLines: CommonLineItem[];
  breakDownOfTenderLines: CommonLineItem[];
  incomeHeadSalesLines: CommonLineItem[];
  cashDrawerLines: CommonLineItem[];
  capitalizedLines: CommonLineItem[];
  mixSummaryLinesGroups: MixSummaryLineItem[][];
  totalLines: CommonLineItem[];
  dateLines: CommonLineItem[];
  footerLines: CommonLineItem[];
  // font size
  receiptFontScale: number;
}
