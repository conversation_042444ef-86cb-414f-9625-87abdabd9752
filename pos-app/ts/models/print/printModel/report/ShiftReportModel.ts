import { filter, find, forEach, get, map, reduce, unionBy } from 'lodash';
import PaymentOptions, { DefaultPaymentOptionType } from '../../../../config/paymentOption';
import { PrintingBusinessType, TransactionFlowType, t } from '../../../../constants';
import {
  ShiftReportSalesSummaryTitles,
  cancelAndDiscountSummaryTitles,
  cashBackSummaryTitleStrings,
  cashDrawerSummaryTitles,
  paymentSummaryTitleStrings,
  serviceChargeSummaryTitles,
} from '../../../../sagas/printing';
import { formatDateString } from '../../../../sagas/printing/common';
import { getCountNumberString, getFixedNumber, getFixedSumString, getIntegerNumberString, getLocaleNumberString } from '../../../../utils';
import { createDate } from '../../../../utils/datetime';
import { formatEmployeeName } from '../../../../utils/employee';
import { ShiftReportConfig } from '../../config/report/ShiftReportConfig';
import { PrinterSetting } from '../../printer/PrinterSetting';

export class ShiftReportModel {
  private config: ShiftReportConfig;
  private report;
  private payments;
  constructor(report, config: ShiftReportConfig) {
    this.report = report;
    this.config = config;
  }

  public getRequestData(printerSetting: PrinterSetting) {
    const printerId = printerSetting.getOnlineReceiptPrinterId();
    if (!printerId) {
      return null;
    }
    const { country, birAccredited } = this.config;
    const withBIR = country === 'PH' && birAccredited === true;

    return [
      {
        printerId,
        businessType: PrintingBusinessType.SHIFT_REPORT,
        data: withBIR ? this.getBIRData() : this.getCommonData(),
      },
    ];
  }

  /**
   * NonBIRModel
   * @returns
   */
  private getCommonData() {
    const { address, phone } = this.config;

    return {
      printedDate: null,
      gstIdNo: null,
      birAccrNo: null,
      serialNo: null,
      minNo: null,
      ptu: null,
      address: Boolean(address) ? `${t('Address')}: ${address}` : null,
      phone: Boolean(phone) ? `${t('Phone')}: ${phone}` : null,
      reportTitle: 'Z Sales Shift Report',
      openBy: null,
      closeBy: null,
      printedBy: null,
      cashier: null,
      ...this.getHeaderData(),
      ...this.getPaymentsAndDepositsSummary(),
      ...this.getSalesSummary(),
      ...this.getStoreCreditSummary(),
      ...this.getCancelAndDiscountSummary(),
      ...this.getServiceChargeSummary(),
      ...this.getTaxSummaryList(),
      ...this.getCashDrawerSummary(),
      ...this.getFooterData(),
    };
  }

  /**
   * BIR Model
   * @returns
   */
  private getBIRData() {
    const { birAccrNo, serialNo, minNo, ptu, isVATRegistered, gstIdNo, curEmployeeId } = this.config;
    const openByEmployeeId = this.report.openBy;
    const openBy = `Open By: ${formatEmployeeName(openByEmployeeId)}`;

    const closeByEmployeeId = this.report.closeBy;
    const closeBy = `Close By: ${formatEmployeeName(closeByEmployeeId)}`;

    const curEmployeeName = formatEmployeeName(curEmployeeId);
    const printedBy = `Printed By: ${curEmployeeName}`;

    const cashier = `Cashier: ${curEmployeeName}`;

    const vatTitle = isVATRegistered ? 'VAT Reg Tin #: ' : 'NON-VAT REG TIN #: ';

    const printedDate = createDate(new Date(), 'MMMM DD, YYYY');

    return {
      printedDate: Boolean(printedDate) ? `Date Printed: ${printedDate}` : 'Date Printed: ',
      gstIdNo: Boolean(gstIdNo) ? `${vatTitle}${gstIdNo}` : vatTitle,
      birAccrNo: Boolean(birAccrNo) ? `Accred #: ${birAccrNo}` : 'Accred #: ',
      serialNo: Boolean(serialNo) ? `Serial #: ${serialNo}` : 'Serial #: ',
      minNo: Boolean(minNo) ? `MIN #: ${minNo}` : 'MIN #: ',
      ptu: Boolean(ptu) ? `PTU #: ${ptu}` : 'PTU #: ',
      address: null,
      phone: null,
      reportTitle: 'X Reading Report',
      openBy,
      closeBy,
      printedBy,
      cashier,
      ...this.getHeaderData(),
      ...this.getPaymentsAndDepositsSummary(),
      ...this.getSalesSummary(),
      ...this.getStoreCreditSummary(),
      ...this.getCancelAndDiscountSummary(),
      ...this.getServiceChargeSummary(),
      ...this.getTaxSummaryList(),
      ...this.getCashDrawerSummary(),
      ...this.getFooterData(),
    };
  }

  private getHeaderData() {
    const { country, birAccredited, companyName, storeName, currency, registerId } = this.config;
    const reportDate = formatDateString(new Date());
    const shiftOpenTime = formatDateString(get(this.report, 'openTime'));
    const shiftCloseTime = formatDateString(get(this.report, 'closeTime'));

    const employeeId = this.config.shiftOpenStatus ? this.report.openBy : this.report.closeBy;
    const manager = formatEmployeeName(employeeId);

    return {
      country,
      birAccredited,
      companyName,
      storeName,
      currency,
      shiftOpenTime: Boolean(shiftOpenTime) ? `Shift Open Time: ${shiftOpenTime}` : 'Shift Open Time: ',
      shiftCloseTime: Boolean(shiftCloseTime) ? `Shift Close Time: ${shiftCloseTime}` : 'Shift Close Time: N/A',
      registerId: Boolean(registerId) ? `Register: ${registerId}` : 'Register: ',
      reportDate: Boolean(reportDate) ? `Report Date: ${reportDate}` : 'Report Date: ',
      manager: Boolean(manager) ? `Manager: ${manager}` : 'Manager: ',
    };
  }

  private getPayments() {
    if (this.payments) {
      return this.payments;
    }
    const report = this.report;
    const salesList = Object.keys(get(report, 'sales', {})).map(Number);
    const returnsList = Object.keys(get(report, 'returns', {})).map(Number);
    const depositsList = Object.keys(get(report, 'deposits', {})).map(Number);
    const supportPayments = unionBy(PaymentOptions.getSupportedPaymentIds(), salesList, returnsList, depositsList, v => String(v)).map(v => Number(v));

    this.payments = map(supportPayments, id => {
      const salesQuantity = get(report, ['sales', id, 'count'], 0);
      const salesAmount = get(report, ['sales', id, 'totalAmount'], 0);
      const actualSalesQuantity = get(report, ['actualSales', id, 'count'], 0);
      const actualSalesAmount = get(report, ['actualSales', id, 'totalAmount'], 0);
      const depositsQuantity = get(report, ['deposits', id, 'count'], 0);
      const depositsAmount = get(report, ['deposits', id, 'totalAmount'], 0);
      const refundsQuantity = get(report, ['returns', id, 'count'], 0);
      const refundsAmount = get(report, ['returns', id, 'totalAmount'], 0);
      const netQuantity = salesQuantity + refundsQuantity;
      const netAmount = salesAmount - refundsAmount;
      const actualNetAmount = actualSalesAmount - refundsAmount;
      const title = PaymentOptions.getPaymentNameById(id) + ' ' + t('Summary');
      const salesRoundedAmount = get(report, ['sales', id, 'totalCashRounded'], 0);
      const refundsRoundedAmount = get(report, ['returns', id, 'totalCashRounded'], 0);
      const totalRoundedAmount = salesRoundedAmount - refundsRoundedAmount;

      return {
        paymentId: id,
        title: title,
        headerStrings: [title, t('Qty'), t('Amount')],
        salesQuantity,
        salesAmount,
        salesRoundedAmount,
        refundsRoundedAmount,
        totalRoundedAmount,
        actualSalesQuantity,
        actualSalesAmount,
        depositsQuantity,
        depositsAmount,
        refundsQuantity,
        refundsAmount,
        netQuantity,
        netAmount,
        actualNetAmount,
      };
    });
    console.log('payments======>>>>', this.payments);
    return this.payments;
  }

  /**
   * payments + depositsSummary
   * @returns
   */
  private getPaymentsAndDepositsSummary() {
    const depositsSummary = [];
    let totalDepositQuantity = 0;
    let totalDepositAmount = 0;
    const filteredPayments = filter(this.getPayments(), item => get(item, 'paymentId') !== DefaultPaymentOptionType.Loyalty).map(item => {
      const {
        actualSalesQuantity,
        actualSalesAmount,
        depositsQuantity,
        depositsAmount,
        refundsQuantity,
        refundsAmount,
        netQuantity,
        actualNetAmount,
        paymentId,
        salesRoundedAmount,
        refundsRoundedAmount,
        totalRoundedAmount,
      } = item;
      const paymentName = PaymentOptions.getPaymentNameById(paymentId);
      const depositsTitle = paymentName + ' ' + t('deposits');
      depositsSummary.push({
        title: depositsTitle,
        quantity: getIntegerNumberString(depositsQuantity),
        amount: getLocaleNumberString(depositsAmount),
      });
      totalDepositQuantity += depositsQuantity;
      totalDepositAmount += depositsAmount;
      let displaySalesAmount = actualSalesAmount;
      let displayRefundsAmount = refundsAmount;
      let roundingTitle = null;
      if (paymentId === DefaultPaymentOptionType.Cash) {
        displaySalesAmount = actualSalesAmount - salesRoundedAmount;
        displayRefundsAmount = refundsAmount - refundsRoundedAmount;
        roundingTitle = paymentName + ' ' + 'Rounded';
      }
      return {
        ...item,
        salesQuantity: getIntegerNumberString(actualSalesQuantity),
        salesAmount: getLocaleNumberString(displaySalesAmount),
        depositsQuantity: getIntegerNumberString(depositsQuantity),
        depositsAmount: getLocaleNumberString(depositsAmount),
        refundsQuantity: getIntegerNumberString(refundsQuantity),
        refundsAmount: getLocaleNumberString(displayRefundsAmount),
        roundingTitle,
        roundingAmount: getLocaleNumberString(totalRoundedAmount),
        netQuantity: getIntegerNumberString(netQuantity),
        netAmount: getLocaleNumberString(actualNetAmount),
      };
    });

    return {
      paymentSummaryTitleStrings,
      payments: filteredPayments,
      shiftReportDepositssHeaderStrings: ['Deposits Summary', t('Qty'), t('Amount')],
      depositsSummary,
      totalDepositTitle: t('Total deposits'),
      totalDepositQuantity: getIntegerNumberString(totalDepositQuantity),
      totalDepositAmount: getLocaleNumberString(totalDepositAmount),
    };
  }

  /**
   * salesSummary
   * @returns
   */
  private getSalesSummary() {
    const registerTransactions = get(this.report, 'registerTransactions', []);
    const beepTransactions = get(this.report, 'beepTransactions', []);
    const totalSalesQuantity =
      filter(
        registerTransactions,
        registerTransaction => registerTransaction.transactionType === TransactionFlowType.Sale && !Boolean(registerTransaction.isCancelled)
      ).length +
      filter(beepTransactions, beepTransaction => beepTransaction.transactionType === TransactionFlowType.Sale && !Boolean(beepTransaction.isCancelled)).length;

    const totalRefundQuantity =
      filter(
        registerTransactions,
        registerTransaction => registerTransaction.transactionType === TransactionFlowType.Return && !Boolean(registerTransaction.isCancelled)
      ).length +
      filter(beepTransactions, beepTransaction => beepTransaction.transactionType === TransactionFlowType.Return && !Boolean(beepTransaction.isCancelled))
        .length;

    const totalSalesNetQuantity = totalSalesQuantity - totalRefundQuantity;

    let salesSummary = reduce(
      this.getPayments(),
      (result, payment) => {
        // Skip the loyalty payment to aggregate sales
        const { salesQuantity, salesAmount, refundsQuantity, refundsAmount, netQuantity, netAmount } = result;
        if (payment.paymentId === DefaultPaymentOptionType.Loyalty) {
          return {
            salesQuantity: getCountNumberString(salesQuantity),
            salesAmount,
            refundsQuantity: getCountNumberString(refundsQuantity),
            refundsAmount,
            netQuantity: getCountNumberString(netQuantity),
            netAmount,
          };
        } else {
          return {
            salesQuantity: totalSalesQuantity,
            salesAmount: getFixedSumString(salesAmount, payment.salesAmount),
            refundsQuantity: totalRefundQuantity,
            refundsAmount: getFixedSumString(refundsAmount, payment.refundsAmount),
            netQuantity: totalSalesNetQuantity,
            netAmount: getFixedSumString(netAmount, payment.actualNetAmount),
          };
        }
      },
      {
        salesQuantity: '0',
        salesAmount: '0.00',
        refundsQuantity: '0',
        refundsAmount: '0.00',
        netQuantity: '0',
        netAmount: '0.00',
      }
    );
    salesSummary = {
      salesQuantity: getCountNumberString(salesSummary.salesQuantity),
      salesAmount: getLocaleNumberString(salesSummary.salesAmount),
      refundsQuantity: getCountNumberString(salesSummary.refundsQuantity),
      refundsAmount: getLocaleNumberString(salesSummary.refundsAmount),
      netQuantity: getCountNumberString(salesSummary.netQuantity),
      netAmount: getLocaleNumberString(salesSummary.netAmount),
    };
    return {
      shiftReportItemsHeaderStrings: ['Sales Summary', t('Qty'), t('Amount')],
      salesSummaryTitles: ShiftReportSalesSummaryTitles,
      salesSummary,
    };
  }

  /**
   * cancelAndDiscountSummary
   * @returns
   */
  private getCancelAndDiscountSummary() {
    const report = this.report;
    const _cancelSummary = report.cancelSummary();
    let _discountCount = 0;
    let _discountAmount = 0;
    forEach(Object.keys(report.discounts), key => {
      if (key !== TransactionFlowType.PreOrder) {
        const { count, amount } = report.discounts[key];
        _discountCount += count;
        if (key === TransactionFlowType.Sale) {
          _discountAmount += amount;
        } else if (key === TransactionFlowType.Return) {
          _discountAmount -= amount;
        }
      }
    });
    const registerTransactions = get(report, 'registerTransactions', []);
    const beepTransactions = get(report, 'beepTransactions', []);
    const totalCanceledQuantity =
      filter(registerTransactions, registerTransaction => Boolean(registerTransaction.isCancelled)).length +
      filter(beepTransactions, beepTransaction => Boolean(beepTransaction.isCancelled)).length;
    return {
      cancelAndDiscountSummaryHeaderStrings: ['Cancels/Dis Summary', t('Qty'), t('Amount')],
      cancelAndDiscountSummaryTitles,
      cancelAndDiscountSummary: {
        discountQuantity: getIntegerNumberString(_discountCount),
        discountAmount: getLocaleNumberString(_discountAmount),
        cancelQuantity: getIntegerNumberString(totalCanceledQuantity),
        cancelAmount: getLocaleNumberString(_cancelSummary.totalCancelledAmount),
      },
    };
  }

  private getServiceChargeSummary() {
    const report = this.report;

    return {
      serviceChargeSummaryHeaderStrings: ['Service Charge Summary', t('Qty'), t('Amount')],
      serviceChargeSummaryTitles,
      serviceChargeSummary: {
        salesQuantity: getIntegerNumberString(get(report.serviceCharge, ['sales', 'count'], 0)),
        salesAmount: getLocaleNumberString(get(report.serviceCharge, ['sales', 'amount'], 0)),
        refundsQuantity: getIntegerNumberString(get(report.serviceCharge, ['refunds', 'count'], 0)),
        refundsAmount: getLocaleNumberString(get(report.serviceCharge, ['refunds', 'amount'], 0)),
      },
    };
  }

  private getTaxSummaryList() {
    const taxSummaryArray = (this.report.taxSummary && Object.values(this.report.taxSummary())) || [];
    const taxSummaryList = [];
    if (taxSummaryArray.length > 0) {
      forEach(taxSummaryArray, item => {
        const { taxName, taxRate, amount } = item as any;
        taxSummaryList.push({
          taxName,
          taxRate: `${getFixedNumber(taxRate * 100, 1)}%`,
          amount: getLocaleNumberString(amount),
        });
      });
    }
    return {
      taxSummaryHeaderStrings: ['Tax Summary', 'Tax Rate', t('Amount')],
      taxSummaryArray: taxSummaryList,
    };
  }

  private getCashDrawerSummary() {
    const report = this.report;

    return {
      cashDrawerSummaryHeaderStrings: ['Cash Drawer Summary', t('Qty'), t('Amount')],
      cashDrawerSummaryTitles,
      cashDrawerSummary: {
        openingAmount: getLocaleNumberString(get(report, 'openingAmount', 0)),
        cashSalesQuantity: getIntegerNumberString(get(report.actualSales, ['0', 'count'], 0)),
        cashSalesAmount: getLocaleNumberString(get(report.actualSales, ['0', 'totalAmount'], 0)),
        cashDepositsQuantity: getIntegerNumberString(get(report.deposits, ['0', 'count'], 0)),
        cashDepositsAmount: getLocaleNumberString(get(report.deposits, ['0', 'totalAmount'], 0)),
        cashRefundsQuantity: getIntegerNumberString(get(report.returns, ['0', 'count'], 0)),
        cashRefundsAmount: getLocaleNumberString(get(report.returns, ['0', 'totalAmount'], 0)),
        payOutQuantity: getIntegerNumberString(get(report, 'payoutQuantity', 0)),
        payOutAmount: getLocaleNumberString(get(report, 'payoutTotal', 0)),
        payInQuantity: getIntegerNumberString(get(report, 'payinQuantity', 0)),
        payInAmount: getLocaleNumberString(get(report, 'payinTotal', 0)),
      },
    };
  }

  /**
   * storeCreditSummary
   * @returns
   */
  private getStoreCreditSummary() {
    const storeCreditSummary = {
      discountQuantity: '0',
      discountAmount: '0.00',
    };

    const storeCreditPaymentItem = find(this.getPayments(), item => get(item, 'paymentId') === DefaultPaymentOptionType.Loyalty);
    if (storeCreditPaymentItem) {
      const { salesQuantity, salesAmount } = storeCreditPaymentItem;
      storeCreditSummary.discountQuantity = getIntegerNumberString(salesQuantity);
      storeCreditSummary.discountAmount = getLocaleNumberString(salesAmount);
    }
    return {
      storeCreditSummaryHeaderStrings: [this.config.enableCashback ? 'Cashback Summary' : 'Store Credit Summary', t('Qty'), t('Amount')],
      storeCreditSummaryTitles: cashBackSummaryTitleStrings,
      storeCreditSummary,
    };
  }

  private getFooterData() {
    const expectedDrawer = isNaN(Number(this.report.expectedDrawer)) ? 0 : Number(this.report.expectedDrawer);
    const actualDrawer = isNaN(Number(this.report.closingAmount)) ? 0 : Number(this.report.closingAmount);
    const overShort = actualDrawer - expectedDrawer;
    return {
      expectedDrawerTitle: 'Expected Drawer',
      expectedDrawer: getLocaleNumberString(expectedDrawer),
      actualDrawerTitle: 'Actual Drawer',
      actualDrawer: getLocaleNumberString(actualDrawer),
      overShortTitle: 'Over/Short',
      overShort: getLocaleNumberString(overShort),
    };
  }
}
