import { forEach } from 'lodash';
import { PrinterSetting } from '../../printer/PrinterSetting';
import { ZReadingReportConfig } from '../../config/report/ZReadingReportConfig';
import { createDate } from '../../../../utils/datetime';
import { getCountNumberString, getLocaleNumberString, isValidNumber } from '../../../../utils';
import moment from 'moment';
import { BIRStoreType, PrintingBusinessType } from '../../../../constants';
// import reactotron from 'reactotron-react-native';

export class ZReadingReportModel {
  private config: ZReadingReportConfig;
  private report;
  constructor(report, config: ZReadingReportConfig) {
    this.report = report;
    this.config = config;
  }

  public getRequestData(printerSetting: PrinterSetting) {
    const printerId = printerSetting.getOnlineReceiptPrinterId();
    if (!printerId) {
      return null;
    }

    const {
      closeTime,
      oldGross,
      newGross,
      grossSales,
      totalDeductedVat,
      netSales,
      vatAbleSales,
      vatAmount,
      vatExemptSales,
      zeroRatedSales,
      amusementTax,
      scDiscount,
      pwdDiscount,
      athleteAndCoachDiscount,
      medalOfValorDiscount,
      soloParentDiscount,
      regularDiscount,
      serviceCharge,
      serviceChargeTax,
      refundAmount,
      refundTrxCount,
      salesTrxCount,
      paymentCollections,
      zCount,
      startORNumber,
      endORNumber,
      startTrxNumber,
      endTrxNumber,
      oldNet,
      newNet,
    } = this.report;

    const { country, birAccredited, storeName, gstIdNo, minNo, birAccrNo, serialNo, ptu, enableServiceCharge, isVATRegistered, registerId, industry } =
      this.config;

    const printedDate = createDate(new Date(), 'YYYY-MM-DD HH:mm');

    const totalDiscount = scDiscount + pwdDiscount + regularDiscount + athleteAndCoachDiscount + medalOfValorDiscount + soloParentDiscount;
    const lessDiscount = (grossSales || 0) - (refundAmount || 0) - (totalDeductedVat || 0) - (netSales || 0);
    const lessRefund = refundAmount;

    let totalSalesCount = 0;
    let totalSalesAmount = 0;
    const paymentSummaryList = [];

    forEach(paymentCollections, payment => {
      const { name, count, amount } = payment;
      paymentSummaryList.push({
        name: name,
        count: `${getCountNumberString(count || 0)}`,
        amount: `${getLocaleNumberString(amount || 0)}`,
      });
      totalSalesCount += count;
      totalSalesAmount += amount;
    });
    const totalVatRelatedSales = vatAbleSales + vatAmount + vatExemptSales + zeroRatedSales;

    let totalServiceCharge = 0;
    if (enableServiceCharge) {
      totalServiceCharge = serviceCharge + serviceChargeTax;
    }

    const totalTrxCount = salesTrxCount + refundTrxCount;
    let closeTimeValue = 'N/A';
    if (Boolean(closeTime)) {
      // 由于某天的24天会被format为第二天，所以这里减1秒
      closeTimeValue = moment(new Date(closeTime)).subtract(1, 'second').format('MMMM DD, YYYY');
    }
    const vatTitle = isVATRegistered ? 'VAT Reg Tin #: ' : 'NON-VAT REG TIN #: ';

    const isRetail = industry == BIRStoreType.RetailStore;

    const model = {
      country,
      birAccredited,
      isRetail,
      printedDate: Boolean(printedDate) ? `Date Printed: ${printedDate}` : 'Date Printed: ',
      storeName,
      vatRegNo: Boolean(gstIdNo) ? `${vatTitle}${gstIdNo}` : vatTitle,
      // gstIdNo,
      accredNo: Boolean(birAccrNo) ? `Accred #: ${birAccrNo}` : 'Accred #: ',
      // birAccrNo,
      serialNo: Boolean(serialNo) ? `Serial #: ${serialNo}` : 'Serial #: ',
      minNo: Boolean(minNo) ? `MIN #: ${minNo}` : 'MIN #: ',
      ptu: Boolean(ptu) ? `PTU #: ${ptu}` : 'PTU #: ',
      // 'Z Reading Report'
      closeTime: closeTimeValue,
      registerInfo: Boolean(registerId) ? `Terminal #: ${registerId}` : 'Terminal #: ',
      // registerId,
      zCount: Boolean(zCount) ? `Z-Count: ${zCount}` : 'Z-Count: ',
      // 'grossSales',
      grossSales: `${getLocaleNumberString(grossSales || 0)}`,
      // 'Less Discount',
      lessDiscount: `${getLocaleNumberString(lessDiscount || 0)}`,
      // 'Less Refund',
      lessRefund: `${getLocaleNumberString(lessRefund || 0)}`,
      // 'VAT from SC/PWD/Others',
      totalDeductedVat: `${getLocaleNumberString(totalDeductedVat || 0)}`,
      // 'Net Sales',
      netSales: `${getLocaleNumberString(netSales || 0)}`,
      // 'Collections'
      paymentSummaryList,
      // 'Total Collection'
      totalSalesCount: `${totalSalesCount || 0}`,
      totalSalesAmount: getLocaleNumberString(totalSalesAmount || 0),
      // '--------------------------- Other Info -----------------------------'
      // 'VATable Sales'
      vatAbleSales: `${getLocaleNumberString(vatAbleSales || 0)}`,
      // 'VAT Amount'
      vatAmount: `${getLocaleNumberString(vatAmount || 0)}`,
      // 'VAT-Exempt Sales'
      vatExemptSales: `${getLocaleNumberString(vatExemptSales || 0)}`,
      // 'Zero Rated Sales'
      zeroRatedSales: `${getLocaleNumberString(zeroRatedSales || 0)}`,
      // 'Amusement Tax'
      amusementTax: isValidNumber(amusementTax) ? `${getLocaleNumberString(amusementTax)}` : null,
      // 'Total'
      totalVatRelatedSales: `${getLocaleNumberString(totalVatRelatedSales || 0)}`,
      // '----------------------------- Discount -----------------------------'
      serviceCharge: `${getLocaleNumberString(serviceCharge || 0)}`,
      serviceChargeTax: `${getLocaleNumberString(serviceChargeTax || 0)}`,
      totalServiceCharge: `${getLocaleNumberString(totalServiceCharge || 0)}`,
      // 'SC Discount'
      scDiscount: `${getLocaleNumberString(scDiscount || 0)}`,
      // 'PWD Discount'
      pwdDiscount: `${getLocaleNumberString(pwdDiscount || 0)}`,
      athleteAndCoachDiscountName: 'National athletes and Coaches Discount',
      athleteAndCoachDiscount: `${getLocaleNumberString(athleteAndCoachDiscount || 0)}`,
      medalOfValorDiscountName: 'Medal of Valor Holder Discount',
      medalOfValorDiscount: `${getLocaleNumberString(medalOfValorDiscount || 0)}`,
      // 'Regualr Discount'
      regularDiscount: getLocaleNumberString(regularDiscount || 0),
      // solo Parent Discount
      soloParentDiscount: getLocaleNumberString(soloParentDiscount || 0),
      // 'Total Discount'
      totalDiscount: `${getLocaleNumberString(totalDiscount || 0)}`,
      // 'Beg. OR'
      startORNumber: startORNumber || 'N/A',
      // 'End. OR'
      endORNumber: endORNumber || 'N/A',
      // 'Start Trx No.'
      startTrxNumber: startTrxNumber || 'N/A',
      // 'End Trx No.'
      endTrxNumber: endTrxNumber || 'N/A',
      // 'Trx Count'
      totalTrxCount: getCountNumberString(totalTrxCount),
      // 'Sales Trx Count'
      salesTrxCount: getCountNumberString(salesTrxCount),
      // 'Refund Trx Count'
      refundTrxCount: getCountNumberString(refundTrxCount),
      // 'Refund Amount'
      refundAmount: `${getLocaleNumberString(refundAmount || 0)}`,
      // 'Change Item Trx'
      changeItemTrx: '0', // iOS hard code 0
      // 'Chanagee Item Amount'
      changeItemAmount: getLocaleNumberString(0), // iOS hard code 0
      // 'Old Gross'
      oldGross: `${getLocaleNumberString(oldGross || 0)}`,
      // 'Accumulated Grand Total Saless'
      newGross: `${getLocaleNumberString(newGross || 0)}`,
      // 'Old Net Sales'
      oldNet: `${getLocaleNumberString(oldNet || 0)}`,
      // 'Accumulated Net Sales'
      newNet:  `${getLocaleNumberString(newNet || 0)}`,
    };

    // reactotron.log('🌕model:', model);

    return [
      {
        printerId,
        businessType: PrintingBusinessType.Z_READING_REPORT,
        data: model,
      },
    ];
  }
}
