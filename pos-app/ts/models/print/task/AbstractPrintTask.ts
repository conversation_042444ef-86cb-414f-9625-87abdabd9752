import { call, select } from 'redux-saga/effects';
import { PrintingBusinessType } from '../../../constants';
import { selectAllPrinters } from '../../../sagas/selector';
import { PrinterJobPlainType } from '../../../typings';
import { AbstractTransaction } from '../../transaction/AbstractTransaction';
import { PrintObserver } from '../observer/PrintObserver';
import { KitchenPrintData } from '../printModel/kitchen/KitchenPrintModel';
import { PrinterSetting } from '../printer/PrinterSetting';
import { TaskError } from './TaskError';

/**
 * 打印抽象类
 */
export abstract class AbstractPrintTask<Transaction extends AbstractTransaction, Strategy, Config> {
  private _observer: PrintObserver;
  public get observer(): PrintObserver {
    if (!this._observer) {
      this._observer = new PrintObserver(this.transaction, 'unknown');
    }
    return this._observer;
  }
  public set observer(value: PrintObserver) {
    this._observer = value;
  }

  private _printerSetting: PrinterSetting;
  protected get printerSetting(): PrinterSetting {
    return this._printerSetting;
  }

  /**
   * 初始化打印机模型
   */
  protected *initPrinterSetting() {
    const settings = yield select(selectAllPrinters);
    this._printerSetting = new PrinterSetting(settings);
  }

  protected strategy: Strategy;
  protected readonly returnResult: PrintReturnResult = {
    status: false,
    errorJobs: [],
  };
  protected readonly transaction: Transaction;

  protected config: Config;
  protected abstract initConfig();

  protected abstract onStart();
  protected abstract onCheck();
  protected abstract onCreatePrintingModel();
  protected abstract onPrint();
  protected abstract onSuccess();
  protected abstract onFailed(taskError: TaskError);
  protected abstract onEnd();

  constructor(transaction: Transaction) {
    this.transaction = transaction;
  }

  *execute(strategy: Strategy) {
    this.strategy = strategy;
    try {
      yield call([this, this.onStart]);
      yield call([this, this.onCheck]);
      yield call([this, this.onCreatePrintingModel]);
      yield call([this, this.onPrint]);
      yield call([this, this.onSuccess]);
    } catch (error) {
      yield call([this, this.onFailed], error);
    } finally {
      yield call([this, this.onEnd]);
    }
    return this.returnResult;
  }
}

export type KitchenPrintRequestData = PrintRequestData<KitchenPrintData>;

export type ReceiptPrintRequestData = PrintRequestData<any>;

export type ReportPrintRequestData = PrintRequestData<any>;

export type PrintRequestData<D> = {
  printerId: string;
  businessType: PrintingBusinessType;
  workflowId?: string; // trace RN and native print task
  data: D;
  uri?: string[]; // images
};

export type PrintReturnResult = {
  status: boolean;
  errorCode?: number;
  errorMessage?: string;
  errorJobs?: PrinterJobPlainType[];
  isPrinted?: boolean;
};

export interface PrintTaskResult {
  printerId: string;
  taskIndex: number;
  timestamp: number;
  status: 'success' | 'error';
  message?: string;
}
