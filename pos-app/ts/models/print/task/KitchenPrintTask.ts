import { concat, findIndex, forEach, get, isEmpty, isNil, map, uniq } from 'lodash';

import { call, put, spawn } from 'redux-saga/effects';
import { markPrinted, updatePrinterSettings, UpdateSinglePrinterType, uploadPrintingImages, upsertPrinterErrorJobs } from '../../../actions';
import { closeTopNotification, ContentType, openPrinterErrorToast, openPrinterOfflineToast } from '../../../components/common/TopNotification';

import * as Actions from '../../../actions';
import { ENABLE_DUMP_PRINTING_IMAGES } from '../../../config';
import { BEEP_ORDER_SUMMARY_PRINTER_TAG, IsIOS, OnlineOrderStatus, PrinterJobBusinessType, t } from '../../../constants';
import DAL from '../../../dal';
import { PrinterJobHelper } from '../../../dal/helper';
import { setDefaultNetworkAfterKitchenOrOrderSummaryPrintingSuccess } from '../../../sagas/printing/common';
import { PrinterJobPlainType, PurchasedItemType, SubOrderNotificationPlainType } from '../../../typings';
import { getUnNullValue } from '../../../utils';
import { isBeepQROrder } from '../../../utils/beep';
import PrinterManager, { ErrorDataType, NativePrintResultType } from '../../../utils/printer';
import { getUUIDValue } from '../../../utils/string';
import { KitchenTransaction } from '../../transaction/KitchenTransaction';
import { getKitchenConfig, KitchenConfig } from '../config/KitchenConfig';
import { PrinterSetting } from '../printer/PrinterSetting';
import { KitchenPrintItem, KitchenPrintItemData } from '../printModel/kitchen/KitchenPrintItem';
import { ITEM_MOCK_ID_PREFIX, KitchenPrintData, KitchenPrintModel } from '../printModel/kitchen/KitchenPrintModel';
import { injectWorkflowId } from '../printModel/printModelHelper';
import { AbstractKitchenPrintStrategy } from '../strategy/kitchen/AbstractKitchenPrintStrategy';
import { AbstractPrintTask, KitchenPrintRequestData } from './AbstractPrintTask';
import { PrintBizCodeMsg, PrintBuzCode, TaskError } from './TaskError';

/**
 * Kitchen打印任务
 * Kitchen Docket + Beer Docket + OrderSummary
 */
export class KitchenPrintTask<Strategy extends AbstractKitchenPrintStrategy> extends AbstractPrintTask<KitchenTransaction, Strategy, KitchenConfig> {
  private subOrderFormat = false;
  private isReprint = false;
  // for failed job retry
  private errorPrinterId: string;

  private needUpdateOnlineOrderStatus = true;

  private static listOnlineOrderPrinting: Set<string> = new Set();

  public setNeedUpdateOnlineOrderStatus(value: boolean) {
    this.needUpdateOnlineOrderStatus = value;
  }

  private previousTableId: string | null = null;
  public setErrorPrinterId(value: string) {
    this.errorPrinterId = value;
  }

  public setIsReprint(value: boolean) {
    this.isReprint = value;
  }

  public setSubOrderFormat(value: boolean) {
    // online pay later orders, it is dine orders
    this.subOrderFormat = this.transaction.subOrders?.length > 0 && value;
  }

  public getSubOrderFormat() {
    return this.subOrderFormat;
  }

  public setPreviousTableId(value: string) {
    if (value) {
      this.previousTableId = value;
    }
  }

  public *createFailedJobs(strategy: Strategy) {
    this.strategy = strategy;
    try {
      yield call([this, this.onStart]);
      // #region onCheck
      yield call([this, this.checkKitchenService]);
      yield call([this, this.checkPrintItems]);
      // #endregion
      yield call([this, this.onCreatePrintingModel], true);
      yield call([this, this.onMockPrintFailed]);
      yield call([this, this.savePrintRecord]);
    } catch (error) {
      yield call([this, this.onFailed], error);
    }
  }

  /**
   * record dockets when orders reprint failed
   */
  private onMockPrintFailed() {
    const errMessage = 'order reprint failed';
    const errCode = this.transaction.isOnlineOrder ? PrintBuzCode.PAY_LATER_REPRINT_FAILED : PrintBuzCode.OPEN_ORDER_REPRINT_FAILED;
    if (this.kitchenPrintData) {
      this.kitchenPrintResult = {
        errCode,
        errMessage,
        data: this.kitchenPrintData.map((it, index) => ({
          errCode,
          errMessage,
          printerId: it.printerId,
          taskIndex: index,
        })),
      };
    }
    if (this.summaryPrintData) {
      this.summaryPrintResult = {
        errCode,
        errMessage,
        data: this.summaryPrintData.map((it, index) => ({
          errCode,
          errMessage,
          printerId: it.printerId,
          taskIndex: index,
        })),
      };
    }
  }

  /**
   * __DEV__
   */
  private onMockPrintSuccess() {
    const errMessage = 'open order reprint success';
    if (this.kitchenPrintData) {
      this.kitchenPrintResult = {
        errCode: PrintBuzCode.Success, // 0 is true
        errMessage,
        data: this.kitchenPrintData.map((it, index) => ({
          errCode: PrintBuzCode.Success,
          errMessage,
          printerId: it.printerId,
          taskIndex: index,
        })),
      };
    }
    if (this.summaryPrintData) {
      this.summaryPrintResult = {
        errCode: PrintBuzCode.Success,
        errMessage,
        data: this.summaryPrintData.map((it, index) => ({
          errCode: PrintBuzCode.Success,
          errMessage,
          printerId: it.printerId,
          taskIndex: index,
        })),
      };
    }
  }

  private checkDuplicatedOnlinePrinting = () => {
    if (this.transaction.onlineOrderIdentifier) {
      if (KitchenPrintTask.listOnlineOrderPrinting.has(this.transaction.onlineOrderIdentifier)) {
        console.warn('checkDuplicatedOnlinePrinting', this.transaction.onlineOrderIdentifier);
        const taskError = new TaskError();
        taskError.status = false;
        taskError.code = PrintBuzCode.DUPLICATED_PRINTING;
        throw taskError;
      } else {
        KitchenPrintTask.listOnlineOrderPrinting.add(this.transaction.onlineOrderIdentifier);
      }
    }
  };

  private filterRequestDataByErrorPrinterId(requestData: KitchenPrintRequestData[] | null) {
    if (requestData && this.errorPrinterId && requestData.some(data => data.printerId === this.errorPrinterId)) {
      // if one kitchen tag is assigned to different printers, only print with the errorPrinterId when retrying failed job
      // but if the errorPrinterId can not be found any more, just following the tag logic
      return requestData.filter(data => data.printerId === this.errorPrinterId);
    } else {
      return requestData;
    }
  }

  /**
   * 初始化打印配置
   */
  protected override *initConfig() {
    if (!this.config) {
      this.config = yield getKitchenConfig();
    }
  }

  /**
   * 初始化Kitchen配置
   * 初始化打印机配置
   * 初始化打印模型
   */
  protected override *onStart() {
    this.observer.onEventStart(true);
    this.checkDuplicatedOnlinePrinting();
    yield call([this, this.initConfig]);
    yield call([this, this.initPrinterSetting]);
    yield call([this, this.initKitchenPrintModel]);
  }

  /**
   * 确认是否需要厨房打印服务
   * 确认是否存在打印项
   * 确认需要的打印机是否正确配置
   */
  protected override *onCheck() {
    if (!this.transaction.isTestReceipt) {
      yield call([this, this.checkKitchenService]);
      yield call([this, this.checkPrintItems]);
      yield call([this, this.checkPrinters]);
    } else {
      yield call([this, this.checkTestPrinters]);
    }
  }

  /**
   * 根据策略创建打印数据
   */
  protected override onCreatePrintingModel(includeOfflinePrinters = false) {
    if (this.strategy.needPrintKitchen()) {
      this.kitchenPrintData = this.filterRequestDataByErrorPrinterId(this.kitchenPrintModel.getKitchenRequestData(this.printerSetting, includeOfflinePrinters));
      if (!this.kitchenPrintData) {
        this.observer.uploadLog('emptyKitchenPrintData');
      }
      injectWorkflowId(this.kitchenPrintData, this.observer.getTaskId());
      this.beerPrintData = this.kitchenPrintModel.getBeerRequestData(this.kitchenPrintData);
      injectWorkflowId(this.beerPrintData, this.observer.getTaskId());
    }
    if (this.needPrintOrderSummary) {
      this.summaryPrintData = this.filterRequestDataByErrorPrinterId(
        this.kitchenPrintModel.getSummaryRequestData(this.printerSetting, this.summaryPrinter, includeOfflinePrinters)
      );
      if (!this.summaryPrintData) {
        this.observer.uploadLog('emptySummaryPrintData');
      }
      injectWorkflowId(this.summaryPrintData, this.observer.getTaskId());
    }

    if (this.transaction.isTestReceipt) {
      this.summaryPrintData = this.kitchenPrintModel.getTestReceiptRequestData(this.printerSetting);
      injectWorkflowId(this.summaryPrintData, this.observer.getTaskId());
    }
  }

  /**
   * 调用打印
   */
  protected override async onPrint() {
    if (__DEV__) {
      const { getMockPrintValue } = require('../../../components/test/printer/PrintTestPage');
      if (getMockPrintValue() !== null) {
        console.log('getMockPrintValue()', getMockPrintValue());
        return getMockPrintValue() ? this.onMockPrintSuccess() : this.onMockPrintFailed();
      }
    }
    PrinterManager.bindService();
    const printQueue = [];
    if (this.beerPrintData) {
      printQueue.push(
        PrinterManager.requestPrinting(this.beerPrintData).then((nativeResult: NativePrintResultType) => {
          this.beerPrintResult = nativeResult;
          const taskError = new TaskError();
          taskError.code = nativeResult.errCode;
          taskError.message = nativeResult.errMessage;
          taskError.status = nativeResult.errCode === PrintBuzCode.Success;
          this.observer.uploadLog('BeerPrintFinished', taskError);
        })
      );
    }

    if (this.kitchenPrintData) {
      printQueue.push(
        PrinterManager.requestPrintingWithBuzz(this.kitchenPrintData).then((nativeResult: NativePrintResultType) => {
          this.kitchenPrintResult = this.mergePrintResult(nativeResult, this.kitchenPrintResult);
          const taskError = new TaskError();
          taskError.code = nativeResult.errCode;
          taskError.message = nativeResult.errMessage;
          taskError.status = nativeResult.errCode === PrintBuzCode.Success;
          taskError.logData = this.kitchenPrintData;
          this.observer.uploadLog('KitchenPrintFinished', taskError);
        })
      );
    }

    if (this.summaryPrintData) {
      printQueue.push(
        PrinterManager.requestPrinting(this.summaryPrintData).then((nativeResult: NativePrintResultType) => {
          this.summaryPrintResult = this.mergePrintResult(nativeResult, this.summaryPrintResult);
          const taskError = new TaskError();
          taskError.code = nativeResult.errCode;
          taskError.message = nativeResult.errMessage;
          taskError.status = nativeResult.errCode === PrintBuzCode.Success;
          taskError.logData = this.summaryPrintData;
          this.observer.uploadLog('SummaryPrintFinished', taskError);
        })
      );
    }

    await Promise.all(printQueue);
  }

  /**
   * merge online printer result and offline printer result
   * @param nativeResult
   * @param offlinePrintResult
   * @returns
   */
  private mergePrintResult(nativeResult: NativePrintResultType, offlinePrintResult: NativePrintResultType) {
    nativeResult.data = nativeResult.data || [];
    console.log('offlinePrintResult', offlinePrintResult);
    console.log('nativeResult', nativeResult);

    if (offlinePrintResult) {
      for (const element of offlinePrintResult.data) {
        if (element.printerId === this.errorPrinterId) {
          // ignore errorPrinter because the failed job is reprinted from other online printers
          continue;
        }
        const index = findIndex(nativeResult.data, it => {
          return it.printerId === element.printerId || it.printer?.printerId === element.printerId;
        });
        if (index < 0 && element.errCode === PrintBuzCode.CONNECTION_ERROR) {
          nativeResult.data.push(element);
          if (nativeResult.errCode === PrintBuzCode.Success) {
            nativeResult.errCode = PrintBuzCode.CONNECTION_ERROR;
            nativeResult.errMessage = 'Printer Offline';
          }
        }
      }
    }
    return nativeResult;
  }

  /**
   * 打印成功
   * 更新打印机信息(lastPrintedTime, markPrinterOffline)
   * 自动设置Default Network
   * 记录错误的任务
   */
  protected override *onSuccess() {
    yield call([this, this.updatePrinterAndDefaultNetwork]);

    const errorJobs = yield call([this, this.savePrintRecord]);

    // beer暂时没有记录失败的任务 this.isBeerSuccess，会影响polling的重打。导致只有beer失败，kitchen和summary会被重复打印。
    this.returnResult.status = this.isKitchenSuccess && this.isSummarySuccess;
    this.returnResult.errorJobs = errorJobs;
    if (this.returnResult.status) {
      this.returnResult.errorCode = PrintBuzCode.Success;
      this.returnResult.errorMessage = 'Success';
    } else if (!this.isKitchenSuccess) {
      this.returnResult.errorCode = get(this.kitchenPrintResult, 'errCode');
      this.returnResult.errorMessage = get(this.kitchenPrintResult, 'errMessage');
    } else {
      this.returnResult.errorCode = get(this.summaryPrintResult, 'errCode');
      this.returnResult.errorMessage = get(this.summaryPrintResult, 'errMessage');
    }
  }

  /**
   * 打印失败
   * 上报日志
   * @param taskError
   */
  protected override onFailed(taskError: TaskError) {
    this.returnResult.status = get(taskError, 'status', false);
    this.returnResult.errorCode = get(taskError, 'code');
    this.returnResult.errorMessage = get(taskError, 'message');
    this.observer.uploadLog('onFailed', taskError);
  }

  /**
   * onPrintEnd
   * update the status of onlineOrder
   */
  protected override *onEnd() {
    // remove duplicated lock
    if (this.transaction.onlineOrderIdentifier) {
      KitchenPrintTask.listOnlineOrderPrinting.delete(this.transaction.onlineOrderIdentifier);
    }
    this.returnResult.isPrinted = Boolean(this.kitchenPrintData) || Boolean(this.beerPrintData) || Boolean(this.summaryPrintData);
    yield spawn([this, this.markOnlineOrderPrinted]);
    if (ENABLE_DUMP_PRINTING_IMAGES) {
      yield put(uploadPrintingImages());
    }
    const taskError = new TaskError();

    taskError.status = this.returnResult.status;
    taskError.logData = this.returnResult;
    taskError.message = this.returnResult.errorMessage;
    taskError.code = this.returnResult.errorCode;
    this.observer.onEventEnd(taskError);
  }

  protected kitchenPrintModel: KitchenPrintModel;

  protected kitchenPrintData: KitchenPrintRequestData[];
  protected beerPrintData: KitchenPrintRequestData[];
  protected summaryPrintData: KitchenPrintRequestData[];

  protected kitchenPrintResult: NativePrintResultType = {
    errCode: PrintBuzCode.Success,
    errMessage: 'No Need to Print Kitchen Dockets',
    data: [],
  };
  protected beerPrintResult: NativePrintResultType = {
    errCode: PrintBuzCode.Success,
    errMessage: 'No Need to Print Beer Dockets',
    data: [],
  };
  protected summaryPrintResult: NativePrintResultType = {
    errCode: PrintBuzCode.Success,
    errMessage: 'No Need to Print Summary Dockets',
    data: [],
  };

  get isKitchenSuccess() {
    return get(this.kitchenPrintResult, 'errCode') === PrintBuzCode.Success;
  }

  get isBeerSuccess() {
    return get(this.beerPrintResult, 'errCode') === PrintBuzCode.Success;
  }

  get isSummarySuccess() {
    return get(this.summaryPrintResult, 'errCode') === PrintBuzCode.Success;
  }

  get summaryPrinter() {
    if (this.transaction.isOnlineOrder) {
      // 在线订单summary
      if (this.printerSetting.isBeepSummaryAssigned) {
        return BEEP_ORDER_SUMMARY_PRINTER_TAG;
      }
    } else {
      // 本地订单summary
      return this.config.orderSummaryPrinter;
    }
    return null;
  }

  get kitchenPrinters() {
    if (this.kitchenPrintModel) {
      return this.kitchenPrintModel.getKitchenPrinters();
    } else {
      return [];
    }
  }

  get needPrintOrderSummary() {
    // print beep order summary if the order is dine-in or takeaway
    if (this.transaction.isOnlineOrder && !isBeepQROrder(this.transaction)) {
      return false;
    }
    return this.strategy.needPrintSummary() && this.summaryPrinter;
  }

  /**
   * 根据策略+配置，初始化打印模型
   */
  private initKitchenPrintModel() {
    const kitchenPrintModel = new KitchenPrintModel(this.transaction, this.config);
    kitchenPrintModel.setIsReprint(this.isReprint);
    kitchenPrintModel.setSubOrderFormat(this.subOrderFormat);
    kitchenPrintModel.setPreviousTableId(this.previousTableId);
    if (this.strategy.needPrintKitchen()) {
      kitchenPrintModel.setKitchenItems(this.strategy.getKitchenItems());
    }
    if (this.needPrintOrderSummary) {
      kitchenPrintModel.setSummaryItems(this.strategy.getSummaryItems(), this.summaryPrinter);
    }
    if (this.transaction.isTestReceipt) {
      kitchenPrintModel.setSummaryItemsWithoutPrinter(this.strategy.getSummaryItems());
    }

    this.kitchenPrintModel = kitchenPrintModel;
  }

  /**
   * update printer info
   * mark printer offline
   * auto-update defaultNetwork
   */
  protected *updatePrinterAndDefaultNetwork() {
    const updatePrinterList: UpdateSinglePrinterType[] = [];
    const successPrinterIds: Set<string> = new Set();
    const offlinePrinterIds: Set<string> = new Set();

    const now = new Date().toISOString();

    const handleErrorData = (errorData: ErrorDataType[]) => {
      if (Array.isArray(errorData)) {
        for (const data of errorData) {
          const errorCode = get(data, 'errCode');
          const printerId = get(data, 'printer.printerId');
          const printer = get(data, 'printer');

          if (printerId) {
            if (
              errorCode === PrintBuzCode.CONNECTION_ERROR ||
              errorCode === PrintBuzCode.PRINTING_TIMEOUT ||
              errorCode === PrintBuzCode.PRINTING_TIMEOUT_2 ||
              errorCode === PrintBuzCode.PRINTING_ERROR_2 ||
              (errorCode === PrintBuzCode.CONNECTION_TIMEOUT && IsIOS) // for iOS LANXPrinter
            ) {
              if (!printer?.isReceiptPrinter) {
                offlinePrinterIds.add(printerId);
              }
              const updatePrinter = updatePrinterList.find(it => it.printerId === printerId);
              if (updatePrinter) {
                updatePrinter.data.isOnline = false;
              } else {
                updatePrinterList.push({
                  printerId,
                  data: {
                    isOnline: false,
                    errorCode,
                  },
                });
              }
            } else if (errorCode === PrintBuzCode.Success) {
              successPrinterIds.add(printerId);
              const updatePrinter = updatePrinterList.find(it => it.printerId === printerId);
              if (updatePrinter) {
                updatePrinter.data.lastPrintedTime = now;
              } else {
                updatePrinterList.push({
                  printerId,
                  data: {
                    lastPrintedTime: now,
                    errorCode,
                  },
                });
              }
            }
          }
        }
      }
    };

    handleErrorData(getUnNullValue(this.beerPrintResult, 'data', []));
    handleErrorData(getUnNullValue(this.kitchenPrintResult, 'data', []));
    handleErrorData(getUnNullValue(this.summaryPrintResult, 'data', []));

    if (updatePrinterList.length > 0) {
      yield put(updatePrinterSettings(updatePrinterList));
      if (offlinePrinterIds.size > 0) {
        const text = t('{{count}} printer(s) offline', { count: offlinePrinterIds.size });
        openPrinterOfflineToast(text);
        const taskError = new TaskError();
        taskError.logData = updatePrinterList.map(it => this.printerSetting.getPrinterById(it.printerId));
        this.observer.uploadLog('markPrinterOffline', taskError);
      }
    }

    // if print successfully，set defaultNetwork automatically
    if (successPrinterIds.size > 0) {
      yield call(setDefaultNetworkAfterKitchenOrOrderSummaryPrintingSuccess, Array.from(successPrinterIds));
    }
  }

  /**
   * 确认是否需要打印服务，如果不需要，返回true
   */
  protected checkKitchenService() {
    if (this.config.kitchenPrinters.length === 0 && !this.summaryPrinter) {
      const taskError = new TaskError();
      taskError.status = true;
      taskError.code = PrintBuzCode.NoNeedKitchenService;
      throw taskError;
    }
  }

  /**
   * 确认订单是否存在需要打印item，返回true
   */
  protected checkPrintItems() {
    if (this.kitchenPrintModel.getKitchenItems().length === 0 && this.kitchenPrintModel.getSummaryItems().length === 0) {
      const taskError = new TaskError();
      taskError.status = true;
      taskError.code = PrintBuzCode.NoPrintItems;
      throw taskError;
    }
  }

  /**
   * 确认需要的打印机是否正确配置
   */
  protected *checkPrinters() {
    let neededPrinters = [];
    if (this.strategy.needPrintKitchen()) {
      neededPrinters = neededPrinters.concat(this.kitchenPrinters);
    }
    if (this.needPrintOrderSummary && !neededPrinters.includes(this.summaryPrinter)) {
      neededPrinters.push(this.summaryPrinter);
    }

    if (neededPrinters.length > 0) {
      // 检查打印需要的KitchenPrinter是否分配，是否在线
      const { unassignedCount, unassignedTags, offlineTags, offlineCount, dirtyTags, offlinePrinterCount } = yield call(
        [this.printerSetting, this.printerSetting.checkKitchenPrinterWhenPrint],
        neededPrinters,
        this.config.kitchenPrinters
      );
      const taskError = new TaskError();
      taskError.logData = {
        unassignedTags,
        offlineTags,
        dirtyTags,
      };

      if (unassignedCount === neededPrinters.length) {
        // 需要的tags都没有设置，返回false
        taskError.status = false;
        taskError.code = PrintBuzCode.NoPrinterAssigned;
        throw taskError;
      } else if (offlinePrinterCount > 0) {
        // 只要有打印机离线就创建打印数据
        // prepare offlinePrintResult
        yield call([this, this.onCreatePrintingModel], true);

        if (this.kitchenPrintData) {
          this.kitchenPrintResult = {
            errCode: PrintBuzCode.CONNECTION_ERROR,
            errMessage: 'Printer Offline',
            data: this.kitchenPrintData.map(it => {
              const printer = this.printerSetting.getPrinterById(it.printerId);
              // printer offline
              const isOffline = !printer.isOnline;
              return {
                errCode: isOffline ? PrintBuzCode.CONNECTION_ERROR : PrintBuzCode.Success, // 在线打印机标记为成功
                errMessage: isOffline ? 'Printer Offline' : PrintBizCodeMsg[PrintBuzCode.Success],
                printerId: it.printerId,
                printer: printer,
                printData: it.data,
              };
            }),
          };
        }

        if (this.summaryPrintData) {
          this.summaryPrintResult = {
            errCode: PrintBuzCode.CONNECTION_ERROR,
            errMessage: 'Printer Offline',
            data: this.summaryPrintData.map(it => {
              const printer = this.printerSetting.getPrinterById(it.printerId);
              // printer offline
              const isOffline = !printer.isOnline;
              return {
                errCode: isOffline ? PrintBuzCode.CONNECTION_ERROR : PrintBuzCode.Success, // 在线打印机标记为成功
                errMessage: isOffline ? 'Printer Offline' : PrintBizCodeMsg[PrintBuzCode.Success],
                printerId: it.printerId,
                printer: printer,
                printData: it.data,
              };
            }),
          };
        }

        if (offlineCount === neededPrinters.length) {
          // save errorJobs directly
          const errorJobs = yield call([this, this.savePrintRecord]);
          this.returnResult.errorJobs = errorJobs;
          taskError.status = false;
          taskError.code = PrintBuzCode.NoPrinterOnline;
          throw taskError;
        }
      } else if (dirtyTags.length > 0 || unassignedTags.length > 0 || offlineTags.length > 0) {
        taskError.status = true;
        taskError.code = PrintBuzCode.Success;
        taskError.message = 'has unusual tags';
        this.observer.uploadLog('onCheckTags', taskError);
      }
    } else {
      const taskError = new TaskError();
      taskError.status = true;
      taskError.code = PrintBuzCode.NoPrinterNeeded;
      throw taskError;
    }
  }

  protected *checkTestPrinters() {
    if (!this.printerSetting.getReceiptPrinter()) {
      yield put(Actions.toggleToastInfo({ visible: true, text: t('NoPrinterErrorForReceipt') }));
    }
  }

  /**
   * 打印记录
   * 避免重复打印
   * 记录失败的item + 错误提示
   * @returns errorJobs: PrinterJobPlainType
   */
  protected *savePrintRecord() {
    let errorJobs: PrinterJobPlainType[] = [];
    if (this.transaction.printRecordId) {
      errorJobs = errorJobs.concat(this.getErrorJob(PrinterJobBusinessType.KITCHEN_TICKET));
      // 暂时不记录BEER_DOCKET
      errorJobs = errorJobs.concat(this.getErrorJob(PrinterJobBusinessType.ORDER_SUMMARY));
      errorJobs = this.strategy.updateErrorJobs(errorJobs);

      const jobInfo: Partial<SubOrderNotificationPlainType> = {
        transactionDate: this.transaction.printJobDate,
        jobs: errorJobs,
        jobTitle: this.transaction.printJobTitle,
        isBeepOrder: this.transaction.isOnlineOrder,
      };
      if (this.strategy.needPrintKitchen()) {
        jobInfo.isKitchenPrinted = Boolean(this.kitchenPrintData) && !errorJobs.some(it => it.businessType === PrinterJobBusinessType.KITCHEN_TICKET);
      }

      if (this.needPrintOrderSummary) {
        jobInfo.isOrderSummaryPrinted = Boolean(this.summaryPrintData) && !errorJobs.some(it => it.businessType === PrinterJobBusinessType.ORDER_SUMMARY);
      }

      if (this.transaction.isOnlineOrder) {
        // Online order need to save the print record
        if (!this.transaction.isSubOrder) {
          DAL.upsertBeepNotification({
            orderId: this.transaction.printRecordId,
            receiptNumber: this.transaction.receiptNumber,
            ...jobInfo,
          });
        } else {
          DAL.upsertSubOrderNotification({
            submitId: this.transaction.printRecordId,
            orderId: this.transaction.receiptNumber,
            ...jobInfo,
          });
        }
      } else {
        if (errorJobs.length > 0) {
          DAL.upsertBeepNotification({
            orderId: this.transaction.printRecordId,
            receiptNumber: this.transaction.receiptNumber,
            ...jobInfo,
          });
        } else {
          const deleteSuccess = DAL.deleteBeepNotificationById(this.transaction.printRecordId);
          if (deleteSuccess && this.transaction.isOpen && this.transaction.isDeleted) {
            DAL.deleteTransactionById(this.transaction.transactionId);
          }
        }
      }
    }
    // error notification
    const printersErrorCount = PrinterJobHelper.getPrinterErrorMap();
    yield put(upsertPrinterErrorJobs(printersErrorCount));
    if (errorJobs.length > 0) {
      openPrinterErrorToast();
    } else {
      closeTopNotification([ContentType.PrinterErrorToast]);
    }

    console.log('errorJobs', errorJobs);

    return errorJobs;
  }

  protected *markOnlineOrderPrinted() {
    if (this.needUpdateOnlineOrderStatus && this.returnResult.status && this.transaction.needMarkOnlineOrderStatus) {
      const orderId = this.transaction.receiptNumber;
      const submitId = this.transaction.submitId;
      const taskError = new TaskError();
      taskError.logData = {
        submitId,
        orderId,
        status: this.transaction.status,
        __type: this.transaction.__type__,
      };
      const onSuccess = () => {
        if (submitId) {
          DAL.upsertSubOrderNotification({ submitId, uploaded: true, isBeepOrder: true });
        } else if (orderId) {
          DAL.upsertBeepNotification({ orderId, status: OnlineOrderStatus.Confirmed, uploaded: true, isBeepOrder: true });
        }
        this.observer.uploadLog('markOnlineOrderPrinted', taskError);
        console.log(`Update online order successfully, orderId: ${orderId}, submitId: ${submitId}`);
      };
      const onFailure = response => {
        if (submitId) {
          DAL.upsertSubOrderNotification({ submitId, uploaded: false, isBeepOrder: true });
        } else if (orderId) {
          DAL.upsertBeepNotification({ orderId, status: OnlineOrderStatus.Confirmed, uploaded: false, isBeepOrder: true });
        }
        taskError.status = false;
        taskError.code = PrintBuzCode.MarkOnlineStatusFailed;
        taskError.message = get(response, 'message', 'Update online order failed');
        taskError.logData['response'] = {
          url: response.fetchUrl ?? '',
          httpStatusCode: response.status ?? 0,
          message: response.message ?? '',
          errorCode: String(response.errCode || ''),
        };
        this.observer.uploadLog('markOnlineOrderPrinted', taskError);
        console.log(`Update online order failed, orderId: ${orderId}, submitId: ${submitId}`);
      };
      yield put(
        markPrinted({
          orderId,
          submitId,
          onSuccess: {
            callback: onSuccess,
          },
          onFailure: {
            callback: onFailure,
          },
        })
      );
    }
  }

  private getErrorJob = (businessType: PrinterJobBusinessType) => {
    const needRecordItem = this.transaction.isOpen || this.transaction.isSubOrder;
    let errorData: ErrorDataType[] = [];
    let requestPrintData: KitchenPrintRequestData[] = [];
    let kitchenPrintItems: KitchenPrintItem[] = [];
    let taskIndex: number;
    // Kitchen和Summary分开记录
    const errorJobMap: Record<string, PrinterJobPlainType> = {};

    if (businessType === PrinterJobBusinessType.KITCHEN_TICKET) {
      errorData = getUnNullValue(this.kitchenPrintResult, 'data', []);
      requestPrintData = this.kitchenPrintData || [];
      kitchenPrintItems = this.kitchenPrintModel.getKitchenItems();
    } else if (businessType === PrinterJobBusinessType.ORDER_SUMMARY) {
      errorData = getUnNullValue(this.summaryPrintResult, 'data', []);
      requestPrintData = this.summaryPrintData || [];
      kitchenPrintItems = this.kitchenPrintModel.getSummaryItems();
    }

    if (Array.isArray(errorData)) {
      forEach(errorData, (item, index) => {
        // errorData的每一项是和requestPrintingData的每一项是对应的
        const { errCode } = item;
        taskIndex = item.taskIndex;
        if (isNil(taskIndex)) {
          taskIndex = index;
        }
        const printer = getUnNullValue(item, 'printer', {});
        const printerId = printer.printerId || item.printerId;
        if (errCode !== PrintBuzCode.Success && printerId) {
          const printData: KitchenPrintData = get(requestPrintData, `${taskIndex}.data`, null) || item.printData;
          if (isEmpty(printData)) {
            return;
          }
          const unPrintedItems: KitchenPrintItemData[] = this.getUnPrintItemsFromPrintData(printData);
          const failedJob: PrinterJobPlainType = errorJobMap[printerId] || {
            jobId: getUUIDValue(),
            printerId,
            printerName: this.printerSetting.getPrinterNameById(printerId),
            errorCode: errCode,
            businessType,
            previousTableId: printData.previousTableId,
            printerTags: [],
            unprintedDiffItems: [],
            unprintedItemsIds: [],
            subOrderFormat: this.subOrderFormat,
          };

          forEach(unPrintedItems, unPrintedItem => {
            const itemId = unPrintedItem.id;
            const kitchenPrintItem = kitchenPrintItems.find(item => item.id === itemId);
            if (itemId.startsWith(ITEM_MOCK_ID_PREFIX) || unPrintedItem.quantity < 0 || needRecordItem) {
              // record full item because itemId not existed or item can be deleted
              if (kitchenPrintItem) {
                failedJob.unprintedDiffItems.push(kitchenPrintItem.getRecordData() as PurchasedItemType);
              }
            } else {
              // 如果是beer dockets失败的话，可能会出现多个itemId相同的情况
              failedJob.unprintedItemsIds.push(itemId);
            }
            const kitchenPrinter = kitchenPrintItem?.getKitchenPrinter();
            if (kitchenPrinter) {
              failedJob.printerTags.push(PrinterSetting.mapTagNameIfNecessary(kitchenPrinter));
            }
          });
          failedJob.printerTags = uniq(failedJob.printerTags);
          failedJob.unprintedItemsIds = uniq(failedJob.unprintedItemsIds);

          if ((failedJob.unprintedItemsIds.length > 0 || failedJob.unprintedDiffItems.length > 0) && !errorJobMap[printerId]) {
            errorJobMap[printerId] = failedJob;
          }
        }
      });
    }
    return Object.values(errorJobMap);
  };

  private getUnPrintItemsFromPrintData(printData: KitchenPrintData): KitchenPrintItemData[] {
    if (!this.subOrderFormat) {
      return getUnNullValue(printData, 'takeawayItems', []).concat(getUnNullValue(printData, 'purchasedItems', []));
    } else {
      return map(printData.subOrders, subOrder => concat(getUnNullValue(subOrder, 'takeawayItems', []), getUnNullValue(subOrder, 'purchasedItems', []))).flat();
    }
  }
}
