import { get, isEmpty } from 'lodash';

import { call, put } from 'redux-saga/effects';
import * as Actions from '../../../actions';
import { ENABLE_DUMP_PRINTING_IMAGES } from '../../../config';
import { t } from '../../../constants';
import DAL from '../../../dal';
import { setDefaultNetworkAfterReceiptPrintingSuccess } from '../../../sagas/printing/common';
import PrinterManager, { NativePrintResultType } from '../../../utils/printer';
import { ReceiptTransaction } from '../../transaction/ReceiptTransaction';
import { ReceiptConfig, getReceiptConfig } from '../config/ReceiptConfig';
import { injectWorkflowId } from '../printModel/printModelHelper';
import { BaseReceiptPrintModel } from '../printModel/receipt/BaseReceiptPrintModel';
import { CommonHalfReceiptPrintModel } from '../printModel/receipt/CommonHalfReceiptPrintModel';
import { MYReceiptPrintModel } from '../printModel/receipt/MYReceiptPrintModel';
import { PHHalfReceiptPrintModel } from '../printModel/receipt/PHHalfReceiptPrintModel';
import { PHReceiptPrintModel } from '../printModel/receipt/PHReceiptPrintModel';
import { ReceiptStrategy } from '../strategy/receipt/ReceiptStrategy';
import { AbstractPrintTask, ReceiptPrintRequestData } from './AbstractPrintTask';
import { PrintBuzCode, TaskError } from './TaskError';

export class ReceiptTask extends AbstractPrintTask<ReceiptTransaction, ReceiptStrategy, ReceiptConfig> {
  protected printModel: BaseReceiptPrintModel;

  protected receiptPrintingModel: ReceiptPrintRequestData[];

  protected receiptPrintResult: NativePrintResultType = {
    errCode: PrintBuzCode.Success,
    errMessage: '',
    data: [],
  };

  constructor(transaction) {
    super(transaction);
  }

  get isReceiptSuccess() {
    return get(this.receiptPrintResult, 'errCode') === PrintBuzCode.Success;
  }

  protected initReceiptPrintModel() {
    const { country } = this.config;
    let printModel;
    if (this.strategy.isHalfReceipt) {
      if (country === 'PH') {
        printModel = new PHHalfReceiptPrintModel(this.transaction);
      } else {
        printModel = new CommonHalfReceiptPrintModel(this.transaction);
      }
    } else if (country === 'PH') {
      printModel = new PHReceiptPrintModel(this.transaction);
    } else if (country === 'MY') {
      printModel = new MYReceiptPrintModel(this.transaction);
    } else {
      printModel = new MYReceiptPrintModel(this.transaction);
    }
    printModel.setReceiptConfig(this.config);
    this.printModel = printModel;
  }

  protected override *initConfig() {
    if (!this.config) {
      const receiptConfig: ReceiptConfig = yield call(getReceiptConfig);
      this.config = receiptConfig;
    }
    return this.config;
  }

  protected override *onStart() {
    this.observer.onEventStart();
    yield call([this, this.initConfig]);
    yield call([this, this.initPrinterSetting]);
    yield call([this, this.initReceiptPrintModel]);
  }

  protected override *onCheck() {
    const receiptPrinter = this.printerSetting.getOnlineReceiptPrinter();
    if (!receiptPrinter) {
      yield put(Actions.toggleToastInfo({ visible: true, text: t('NoPrinterError') }));

      const taskError = new TaskError();
      taskError.status = false;
      taskError.code = PrintBuzCode.NoPrinterOnline;
      throw taskError;
    }
  }

  protected override *onCreatePrintingModel() {
    this.receiptPrintingModel = yield call([this.printModel, this.printModel.getReceiptPrintingModel], this.printerSetting);
    injectWorkflowId(this.receiptPrintingModel, this.observer.getTaskId());
    if (isEmpty(this.receiptPrintingModel)) {
      this.observer.uploadLog('emptyReceiptPrintingModel');

      const taskError = new TaskError();
      taskError.status = false;
      taskError.code = PrintBuzCode.NoPrintItems;
      throw taskError;
    }
  }

  protected override *onPrint() {
    PrinterManager.bindService();
    const nativeResult = yield call(PrinterManager.requestPrinting, this.receiptPrintingModel);
    this.receiptPrintResult = nativeResult;
    this.observer.uploadLog('ReceiptPrintFinished');
  }

  protected override *onSuccess() {
    const { errCode, errMessage } = this.receiptPrintResult;
    const receiptPrinterId = this.printerSetting.getReceiptPrinterId();

    this.returnResult.status = this.isReceiptSuccess;
    this.returnResult.errorCode = this.isReceiptSuccess ? PrintBuzCode.Success : errCode;
    this.returnResult.errorMessage = this.isReceiptSuccess ? 'Success' : errMessage;

    if (this.isReceiptSuccess) {
      yield put(Actions.updatePrinterLastPrintedTime({ printerId: receiptPrinterId }));
      yield call(setDefaultNetworkAfterReceiptPrintingSuccess, receiptPrinterId);
    } else {
      yield put(
        Actions.updateSinglePrinter({
          printerId: receiptPrinterId,
          data: {
            errorCode: errCode,
            isOnline: false,
          },
        })
      );
      yield put(Actions.toggleToastInfo({ visible: true, text: String(errMessage) }));
    }
  }

  protected override onFailed(taskError: TaskError) {
    console.log('ReceiptTask: onFailed:', taskError);
    this.returnResult.status = false;
    this.returnResult.errorCode = get(taskError, 'code');
    this.returnResult.errorMessage = get(taskError, 'message');
    this.observer.uploadLog('onFailed', taskError, this.returnResult.status);
  }

  protected override *onEnd() {
    console.log('ReceiptTask: onEnd');
    const taskError = new TaskError();
    if (!this.strategy.isHalfReceipt) {
      const { transactionId, isReprint } = this.transaction;
      yield put(Actions.employeePrintReceipt({ transactionId, isReprint }));
      if (ENABLE_DUMP_PRINTING_IMAGES) {
        yield put(Actions.uploadPrintingImages());
      }
      if (this.transaction.isOnlineOrder && this.returnResult.status && this.transaction.receiptNumber) {
        console.log('mark receipt local:', this.transaction.receiptNumber);
        taskError.logData = 'mark receiptPrinted true';
        DAL.upsertBeepNotification({
          orderId: this.transaction.receiptNumber,
          receiptPrinted: this.returnResult.status,
        });
      }
    }
    taskError.status = this.returnResult.status;
    taskError.code = this.returnResult.errorCode;
    taskError.message = this.returnResult.errorMessage;
    this.observer.onEventEnd(taskError);
  }
}
