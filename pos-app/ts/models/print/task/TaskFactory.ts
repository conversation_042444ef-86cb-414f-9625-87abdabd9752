import { KitchenTransaction } from '../../transaction/KitchenTransaction';
import { ReceiptTransaction } from '../../transaction/ReceiptTransaction';
import { KitchenPrintTask } from './KitchenPrintTask';
import { ReceiptTask } from './ReceiptTask';
import { DailyReportTask } from './report/DailyReportTask';
import { ShiftReportTask } from './report/ShiftReportTask';
import { SMXReadingReportTask } from './report/SMXReadingReportTask';
import { ZReadingReportTask } from './report/ZReadingReportTask';

/**
 * @class TaskFactory 任务工厂类
 */
export class TaskFactory {
  static createKitchenPrintTask(kitchenTransaction: KitchenTransaction) {
    return new KitchenPrintTask(kitchenTransaction);
  }

  static createReceiptTask(receiptTransaction: ReceiptTransaction) {
    return new ReceiptTask(receiptTransaction);
  }

  static createShiftReportTask(report) {
    return new ShiftReportTask(report);
  }

  static createDailyReportTask(summary) {
    return new DailyReportTask(summary);
  }

  static createZReadingReportTask(report) {
    return new ZReadingReportTask(report);
  }

  static createSMXReadingReportTask(report) {
    return new SMXReadingReportTask(report);
  }
}
