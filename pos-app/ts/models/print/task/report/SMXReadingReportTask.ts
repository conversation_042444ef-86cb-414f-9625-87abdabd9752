import { call } from 'redux-saga/effects';
import { getSMXReadingReportConfig, SMXReadingReportConfig } from '../../config/report/SMXReadingReportConfig';
import { SMXReadingReportModel } from '../../printModel/report/SMXReadingReportModel';
import { AbstractReportPrintTask } from './AbstractReportPrintTask';
export class SMXReadingReportTask extends AbstractReportPrintTask<SMXReadingReportConfig, SMXReadingReportModel> {
  private report;
  constructor(report) {
    super();
    this.report = report;
  }

  protected override *getConfig() {
    return yield call(getSMXReadingReportConfig);
  }

  protected override getPrintModel() {
    return new SMXReadingReportModel(this.report, this.config);
  }

  protected override getPrintingModel() {
    return this.printModel.getRequestData(this.printerSetting);
  }
}
