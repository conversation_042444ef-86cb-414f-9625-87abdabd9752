import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createDrawerNavigator } from '@react-navigation/drawer';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import * as Sentry from '@sentry/react-native';
import React, { memo, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import {
  ModalCollectPreorder,
  ModalColourPicker,
  ModalCreateFloor,
  ModalCreateNewSection,
  ModalDynamicBeepQrCode,
  ModalEWalletQRCode,
  ModalFailedPrintJobList,
  ModalFixingData,
  ModalFullBillDiscount,
  ModalInfo,
  ModalInfoManually,
  ModalManagerPin,
  ModalNewPreOrder,
  ModalPax,
  ModalPaxAndTable,
  ModalProductCountSelector,
  ModalProductDetail,
  ModalProductOptionsSelector,
  ModalProductVariablePrice,
  ModalProductVariableWeight,
  ModalSerialNumberSelector,
  ModalSplitPayment,
  ModalSwitchMasterAuthorize,
  ModalTableEdit,
  ModalTableId,
  ModalTableLayout,
  ModalTableLayoutManagement,
  ModalTemporaryPin,
  ModalWelcomeFreeTrial,
  ModalWelcomeFreeTrialOld,
} from '../components/modal';

import { initPrinters, notifyPrinterJob } from '../actions';
import { closeTopNotification } from '../components/common/TopNotification';
import ModalPureTableId from '../components/modal/ModalPureTableId';
import * as NavigationService from '../navigation/navigatorService';

import { Activation, FreeTrialAccount, Landing, LockScreen, RegisterSelectModal, SignIn, WebViewPage } from '../containers/Auth';
import { Checkout, PaymentConfirm, RefundReason } from '../containers/Checkout';
import { AddCustomer, CustomerDetail, NewCustomer } from '../containers/Customer';
import DrawerContent from '../containers/DrawerContent';
import { ModalShiftChange, Shift } from '../containers/OpenCloseShift';
import { AddOrUpdateProduct, Products } from '../containers/Products';
import { AddOrEditTab, AddProductToTile, OpenOrderLists, Register } from '../containers/Register';
import SplitOrder from '../containers/Register/SplitOrder';
import { BirReport, CashPayInOut, PayInOutList, ShiftReport } from '../containers/ShfitReport';
import { Support } from '../containers/Support';
import { Sync } from '../containers/Sync';
import { TableLayout } from '../containers/TableLayout';
import { SelectTable } from '../containers/TableLayout/SelectTable';
import { TableLayoutEdit } from '../containers/TableLayout/TableLayoutEdit';
import { CancelReason, EmailReceiptModal, Transactions } from '../containers/Transactions';

import { ModalInfoParams } from '../components/modal/ModalInfo';

import ModalMRSLearn from '../components/mrs/ModalMRSLearn';
import ModalResetMRS, { ModalResetMRSParams } from '../components/mrs/ModalResetMRS';

import ModalReportData from '../components/shiftReport/ModalReportData';

import RegisterLayoutEdit from '../containers/Register/RegisterLayoutEdit';

import { get } from 'lodash';
import ModalCustomerQR from '../components/modal/ModalCustomerQR';
import ModalSwitchKitchenDocketLayout from '../components/settings/ModalSwitchKitchenDocketLayout';
import NfcPay from '../containers/Checkout/NfcPay';
import Settings from '../containers/Settings/Settings';
import { AddBIRDiscount } from '../containers/SpecialDiscount';

import { createStackNavigator } from '@react-navigation/stack';
import { ModalApproveManually } from '../components/modal/ModalApproveManually';
import ModalAyalaMallPrompt from '../components/modal/ModalAyalaMallPrompt';
import ModalCloseCurZReading from '../components/modal/ModalCloseCurZReading';
import ModalCloseLastZReading from '../components/modal/ModalCloseLastZReading';
import ModalError from '../components/modal/ModalError';
import { ModalForceApproveManually } from '../components/modal/ModalForceApproveManually';
import ModalGhlPay from '../components/modal/ModalGhlPay';
import { ModalInfoPopup } from '../components/modal/ModalInfoPopup';
import ModalManagerPinFC from '../components/modal/ModalManagerPinFC';
import ModalRemoveOtherPromotions from '../components/modal/ModalRemoveOtherPromotions';
import ModalRobinsonMallPrompt from '../components/modal/ModalRobinsonMallPrompt';
import ModalSMXReadingReport from '../components/modal/ModalSMXReadingReport';
import { ModalSocketSystem } from '../components/modal/ModalSocketSystem';
import { ModalStorageCheck } from '../components/modal/ModalStorageCheck';
import ModalTemporaryAllowOutOfStock from '../components/modal/ModalTemporaryAllowOutOfStock';
import ModalUniquePromoConditionNotMeet from '../components/modal/ModalUniquePromoConditionNotMeet';
import ModalUniquePromoRemoved from '../components/modal/ModalUniquePromoRemoved';
import ModalChangeNcsName from '../components/ncs/ModalChangeNcsName';
import { IS_DEV_FAT } from '../config';
import { IsAndroid } from '../constants';
import ModalSelectSalesPerson from '../containers/Customer/ModalSelectSalesPerson';
import ModalSelectUniquePromo from '../containers/Customer/ModalSelectUniquePromo';
import ModalUniquePromoDetails from '../containers/Customer/ModalUniquePromoDetails';
import { stringify } from '../utils/json';
import { infoPOSBasicEvent, POSBasicAction } from '../utils/logComponent';
import AnrMonitor from '../utils/monitor';
import { routingInstrumentation } from '../utils/sentry';
import { getCurrentRouteName } from './navigatorService';

// The TableLayout page must be placed at the bottom because the TableLayout page is the default Home page when the TableLayout function is enabled
const MainTabStack = createBottomTabNavigator();
const TableLayoutScreens = memo(props => (
  <MainTabStack.Navigator screenOptions={{ headerShown: false }} tabBar={props => null}>
    <MainTabStack.Screen name='TableLayout' component={TableLayout} />
    <MainTabStack.Screen name='Register' component={Register} />
  </MainTabStack.Navigator>
));
export type AuthNavigatorParamList = {
  ModalInfo: ModalInfoParams;
  ModalResetMRS: ModalResetMRSParams;
};
const AuthNavigator = createStackNavigator();
const UnLoginStackNavigator = createNativeStackNavigator();
const Drawer = createDrawerNavigator();
const MainStack = createNativeStackNavigator();
const AppContext = React.createContext(undefined);
let currentRouteName = 'Lunch';

const MainScreens = memo(props => (
  <AppContext.Consumer>
    {({ enableTableLayout }) => (
      <MainStack.Navigator>
        <MainStack.Group screenOptions={{ headerShown: false, presentation: 'card', gestureEnabled: false, animation: 'none' }}>
          <MainStack.Screen name='TableLayoutScreens' component={enableTableLayout ? TableLayoutScreens : Register} />
          <MainStack.Screen name='SelectTable' component={SelectTable} />
          <MainStack.Screen name='Shift' component={Shift} />
          <MainStack.Screen name='Sync' component={Sync} />
          <MainStack.Screen name='ShiftReport' component={ShiftReport} />
          <MainStack.Screen name='Settings' component={Settings} />
          {IS_DEV_FAT && <MainStack.Screen name='MrsTestPage' component={require('../components/test/mrs/MrsTestPage').default} />}
          {IS_DEV_FAT && <MainStack.Screen name='KDSTestPage' component={require('../components/test/kds/KDSTestPage').default} />}
          {__DEV__ && <MainStack.Screen name='LocalDBTest' component={require('../components/transactions/LocalDBTestPage').default} />}
          {__DEV__ && <MainStack.Screen name='IminLcdTestPage' component={require('../components/test/iminLcd/IminLcdTestPage').default} />}
          {IS_DEV_FAT && <MainStack.Screen name='PrintTestPage' component={require('../components/test/printer/PrintTestPage').default} />}
          {__DEV__ && <MainStack.Screen name='NewPrinterTestPage' component={require('../components/test/printer/NewPrinterTestPage').default} />}
          {IS_DEV_FAT && <MainStack.Screen name='LoggerTestPage' component={require('../components/test/locallogger/LoggerTestPage').default} />}
          <MainStack.Screen name='Support' component={Support} />
          <MainStack.Screen name='Products' component={Products} />
          <MainStack.Screen name='OpenOrderLists' component={OpenOrderLists} />
          <MainStack.Screen name='TableLayoutEdit' component={TableLayoutEdit} />
          <MainStack.Screen name='SplitOrder' component={SplitOrder} />
          <MainStack.Screen name='MainWebViewPage' component={WebViewPage} />
          <MainStack.Screen name={'Transactions'} component={Transactions} />
        </MainStack.Group>
      </MainStack.Navigator>
    )}
  </AppContext.Consumer>
));

const UnLoginRoute = props => (
  <UnLoginStackNavigator.Navigator>
    <UnLoginStackNavigator.Group screenOptions={{ headerShown: false, animation: 'none' }}>
      <UnLoginStackNavigator.Screen name='Activation' component={Activation} />
      <UnLoginStackNavigator.Screen name='FreeTrialAccount' component={FreeTrialAccount} />
      <UnLoginStackNavigator.Screen name='WebViewPage' component={WebViewPage} />
    </UnLoginStackNavigator.Group>
    <UnLoginStackNavigator.Group screenOptions={{ presentation: 'containedTransparentModal', headerShown: false, animation: 'none' }}>
      <UnLoginStackNavigator.Screen name='RegisterSelectModal' component={RegisterSelectModal} />
      {IS_DEV_FAT && <UnLoginStackNavigator.Screen name='CredentialModal' component={require('../components/test/CredentialModal').default} />}
    </UnLoginStackNavigator.Group>
  </UnLoginStackNavigator.Navigator>
);

const LoginedRoute = () => (
  <Drawer.Navigator
    screenOptions={{ headerShown: false, swipeEnabled: false, drawerType: 'slide' }}
    // useLegacyImplementation={true}
    // @ts-ignore
    drawerContent={props => <DrawerContent {...props} />}
  >
    <Drawer.Screen name={'MainScreens'} component={MainScreens} />
  </Drawer.Navigator>
);
// Because React-Navigation remove the SwitchNavigator from 5.x, so we need to use the props to show the corresponding Screen.
// And we need to make sure these props have corret value for the target route.
type RootStackType = {
  authToken: string;
  currentEmployeeId: string;
  isReduxLoaded: boolean;
  enableTableLayout: boolean;
  accountExpired: boolean;
  accountOffline: boolean;
  isNewLayout: boolean;
};

const SIGN_IN = 'SignIn';
let lastNavigationState;
const onNavigationStateChange = state => {
  const newRouteName = getCurrentRouteName();
  if (newRouteName === SIGN_IN && lastNavigationState) {
    infoPOSBasicEvent({
      action: POSBasicAction.BackToSignIn,
      privateDataPayload: {
        routers: stringify(get(lastNavigationState, 'routes', [])),
      },
    });
  }
  if (currentRouteName !== newRouteName) {
    Sentry.addBreadcrumb({
      category: 'router',
      message: `${currentRouteName} ---> ${newRouteName}`,
      level: 'info',
    });
    currentRouteName = newRouteName;
  }
  lastNavigationState = state;
  AnrMonitor.setLastRoute(newRouteName);
};

export const RootStack = (props: RootStackType) => {
  const { authToken, currentEmployeeId, isReduxLoaded, enableTableLayout, accountExpired, accountOffline, isNewLayout } = props;
  const shouldLockApp = Boolean(accountExpired) || Boolean(accountOffline);
  const dispatch = useDispatch();
  useEffect(() => {
    if (isReduxLoaded && authToken && currentEmployeeId) {
      // logined try notify
      dispatch(notifyPrinterJob(false));
    } else {
      closeTopNotification();
    }
  }, [isReduxLoaded, authToken, currentEmployeeId, dispatch]);

  useEffect(() => {
    if (authToken) {
      dispatch(initPrinters({}));
    }
  }, [authToken, dispatch]);

  const navigation = React.useRef();
  NavigationService.setTopLevelNavigator(navigation.current);
  return (
    <AppContext.Provider value={{ enableTableLayout }}>
      <NavigationContainer
        {...props}
        ref={navigation}
        onStateChange={onNavigationStateChange}
        onReady={() => {
          if (Boolean(routingInstrumentation)) {
            routingInstrumentation.registerNavigationContainer(navigation);
          }
        }}
      >
        <AuthNavigator.Navigator
          initialRouteName='Landing'
          // @ts-ignore
          screenOptions={
            IsAndroid
              ? // @ts-ignore
                { headerShown: false, aniamtion: 'none', animationEnabled: false }
              : {
                  headerShown: false,
                  animationEnabled: false,
                }
          }
        >
          {!Boolean(isReduxLoaded) && <AuthNavigator.Screen name='Landing' component={Landing} />}
          {!Boolean(authToken) ? (
            <AuthNavigator.Screen name='UnLoginRoute' component={UnLoginRoute} />
          ) : !Boolean(currentEmployeeId) ? (
            shouldLockApp ? (
              <AuthNavigator.Screen name='LockScreen' component={LockScreen} />
            ) : (
              <AuthNavigator.Screen name={SIGN_IN} component={SignIn} />
            )
          ) : (
            <AuthNavigator.Screen name='LoginedRoute' component={LoginedRoute} />
          )}
          <AuthNavigator.Group
            screenOptions={
              IsAndroid
                ? {
                    animationEnabled: false,
                    presentation: 'transparentModal',
                  }
                : {
                    animationEnabled: true,
                    presentation: 'transparentModal',
                    transitionSpec: {
                      open: {
                        animation: 'timing',
                        config: {
                          duration: 50,
                        },
                      },
                      close: {
                        animation: 'timing',
                        config: {
                          duration: 50,
                        },
                      },
                    },
                  }
            }
          >
            <AuthNavigator.Screen name='WebViewPage' component={WebViewPage} />
            <AuthNavigator.Screen name='Checkout' component={Checkout} />
            <AuthNavigator.Screen name='PaymentConfirm' component={PaymentConfirm} />
            <AuthNavigator.Screen name='RegisterLayoutEdit' component={RegisterLayoutEdit} />
            <AuthNavigator.Screen name='ModalProductOptionsSelector' component={ModalProductOptionsSelector} />
            <AuthNavigator.Screen name='RefundReason' component={RefundReason} />
            <AuthNavigator.Screen name='CancelReason' component={CancelReason} />
            <AuthNavigator.Screen name='AddCustomer' component={AddCustomer} />
            <AuthNavigator.Screen name='NewCustomer' component={NewCustomer} />
            <AuthNavigator.Screen name='ModalSelectSalesPerson' component={ModalSelectSalesPerson} />
            <AuthNavigator.Screen name='ModalPax' component={ModalPax} />
            <AuthNavigator.Screen name='ModalPaxAndTable' component={ModalPaxAndTable} />
            <AuthNavigator.Screen name='ModalApproveManually' component={ModalApproveManually} />
            <AuthNavigator.Screen name='ModalForceApproveManually' component={ModalForceApproveManually} />
            <AuthNavigator.Screen name='NfcPay' component={NfcPay} />
            <AuthNavigator.Screen name='ModalProductCountSelector' component={ModalProductCountSelector} />
            <AuthNavigator.Screen name='ModalTemporaryPin' component={ModalTemporaryPin} />
            <AuthNavigator.Screen name='ModalTableLayoutManagement' component={ModalTableLayoutManagement} />
            <AuthNavigator.Screen name='ModalDynamicBeepQrCode' component={ModalDynamicBeepQrCode} />
            <AuthNavigator.Screen name='ModalProductVariablePrice' component={ModalProductVariablePrice} />
            <AuthNavigator.Screen name='ModalProductVariableWeight' component={ModalProductVariableWeight} />
            <AuthNavigator.Screen name='ModalFullBillDiscount' component={ModalFullBillDiscount} />
            <AuthNavigator.Screen name='ModalShiftChange' component={ModalShiftChange} />
            <AuthNavigator.Screen name='AddBIRDiscount' component={AddBIRDiscount} />
            <AuthNavigator.Screen name='CustomerDetail' component={CustomerDetail} />
            <AuthNavigator.Screen name='CashPayInOut' component={CashPayInOut} />
            <AuthNavigator.Screen name='AddOrUpdateProduct' component={AddOrUpdateProduct} />
            <AuthNavigator.Screen name='AddOrEditTab' component={AddOrEditTab} />
            <AuthNavigator.Screen name='EmailReceiptModal' component={EmailReceiptModal} />
            <AuthNavigator.Screen name='PayInOutList' component={PayInOutList} />
            <AuthNavigator.Screen name='ModalTableId' component={ModalTableId} />
            <AuthNavigator.Screen name='ModalPureTableId' component={ModalPureTableId} />
            <AuthNavigator.Screen name='ModalRemoveOtherPromotions' component={ModalRemoveOtherPromotions} />
            <AuthNavigator.Screen name='ModalUniquePromoConditionNotMeet' component={ModalUniquePromoConditionNotMeet} />
            <AuthNavigator.Screen name='ModalUniquePromoRemoved' component={ModalUniquePromoRemoved} />
            <AuthNavigator.Screen name='ModalColourPicker' component={ModalColourPicker} />
            <AuthNavigator.Screen name='ModalManagerPin' component={ModalManagerPin} />
            <AuthNavigator.Screen name='ModalManagerPinFC' component={ModalManagerPinFC} />
            <AuthNavigator.Screen name='ModalTemporaryAllowOutOfStock' component={ModalTemporaryAllowOutOfStock} />
            <AuthNavigator.Screen name='AddProductToTile' component={AddProductToTile} />
            <AuthNavigator.Screen name='ModalProductDetail' component={ModalProductDetail} />
            <AuthNavigator.Screen name='ModalSerialNumberSelector' component={ModalSerialNumberSelector} />
            <AuthNavigator.Screen name='ModalInfo' component={ModalInfo} />
            <AuthNavigator.Screen name='ModalInfoPopup' component={ModalInfoPopup} />
            <AuthNavigator.Screen name='ModalSocketSystem' component={ModalSocketSystem} />
            <AuthNavigator.Screen name='ModalStorageCheck' component={ModalStorageCheck} />
            <AuthNavigator.Screen name='ModalCloseLastZReading' component={ModalCloseLastZReading} />
            <AuthNavigator.Screen name='ModalCloseCurZReading' component={ModalCloseCurZReading} />
            <AuthNavigator.Screen name='ModalRobinsonMallPrompt' component={ModalRobinsonMallPrompt} />
            <AuthNavigator.Screen name='ModalSMXReadingReport' component={ModalSMXReadingReport} />
            <AuthNavigator.Screen name='ModalAyalaMallPrompt' component={ModalAyalaMallPrompt} />
            {IS_DEV_FAT && <AuthNavigator.Screen name='ModalCustomerDisplay' component={require('../components/test/iminLcd/ModalCustomerDisplay').default} />}
            <AuthNavigator.Screen name='ModalError' component={ModalError} />
            <AuthNavigator.Screen name='ModalInfoManually' component={ModalInfoManually} />
            <AuthNavigator.Screen name='ModalWelcomeFreeTrial' component={ModalWelcomeFreeTrial} />
            <AuthNavigator.Screen name='ModalWelcomeFreeTrialOld' component={ModalWelcomeFreeTrialOld} />
            <AuthNavigator.Screen name='ModalFixingData' component={ModalFixingData} />
            <AuthNavigator.Screen name='ModalSplitPayment' component={ModalSplitPayment} />
            <AuthNavigator.Screen name='ModalEWalletQRCode' component={ModalEWalletQRCode} />
            <AuthNavigator.Screen name='ModalNewPreOrder' component={ModalNewPreOrder} />
            <AuthNavigator.Screen name='ModalCollectPreorder' component={ModalCollectPreorder} />
            <AuthNavigator.Screen name='BirReport' component={BirReport} />
            {IS_DEV_FAT && <AuthNavigator.Screen name='ModalEnvironmentSwitch' component={require('../components/test/ModalEnvironmentSwitch').default} />}
            <AuthNavigator.Screen name='ModalCreateFloor' component={ModalCreateFloor} />
            <AuthNavigator.Screen name='ModalCreateNewSection' component={ModalCreateNewSection} />
            <AuthNavigator.Screen name='ModalTableLayout' component={ModalTableLayout} />
            <AuthNavigator.Screen name='ModalTableEdit' component={ModalTableEdit} />
            <AuthNavigator.Screen name='ModalFailedPrintJobList' component={ModalFailedPrintJobList} />
            <AuthNavigator.Screen name='ModalSwitchKitchenDocketLayout' component={ModalSwitchKitchenDocketLayout} />
            <AuthNavigator.Screen name='ModalSwitchMasterAuthorize' component={ModalSwitchMasterAuthorize} />
            <AuthNavigator.Screen name='ModalMRSLearn' component={ModalMRSLearn} />
            <AuthNavigator.Screen name='ModalResetMRS' component={ModalResetMRS} />
            <AuthNavigator.Screen name='ModalReportData' component={ModalReportData} />
            <AuthNavigator.Screen name='ModalCustomerQR' component={ModalCustomerQR} />
            <AuthNavigator.Screen name='ModalGhlPay' component={ModalGhlPay} />
            <AuthNavigator.Screen name='ModalSelectUniquePromo' component={ModalSelectUniquePromo} />
            <AuthNavigator.Screen name='ModalUniquePromoDetails' component={ModalUniquePromoDetails} />
            <AuthNavigator.Screen name='ModalChangeNcsName' component={ModalChangeNcsName} />
            {IS_DEV_FAT && <AuthNavigator.Screen name='ModalLanXPrinterUdp' component={require('../components/test/printer/ModalLanXPrinterUdp').default} />}
          </AuthNavigator.Group>
        </AuthNavigator.Navigator>
      </NavigationContainer>
    </AppContext.Provider>
  );
};
