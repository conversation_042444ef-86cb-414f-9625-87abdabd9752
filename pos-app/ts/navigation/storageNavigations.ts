import { goBack, navigate } from './navigatorService';
import { <PERSON>Handler, Linking, Platform } from 'react-native';
import IntentLauncher from 'react-native-intent-launcher';
import RNFS from 'react-native-fs';

const freeStorageWarningThreshold = 500; // MB
const freeStorageCriticalThreshold = 200; // MB

// export const isFreeStorageInsufficient = async () => {
//   const info = await RNFS.getFSInfo();
//   return info && info.freeSpace && info.freeSpace / 1024 / 1024 < freeStorageWarningThreshold;
// };
// export const isFreeStorageCritical = async () => {
//   const info = await RNFS.getFSInfo();
//   return info && info.freeSpace && info.freeSpace / 1024 / 1024 < freeStorageCriticalThreshold;
// };
//
// export const checkFreeStorage = async (navigation, onContinue: () => void = () => {}) => {
//   if (await isFreeStorageInsufficient()) {
//     navigateToStorageInsufficientModel(navigation, onContinue);
//     return;
//   }
//   if (await isFreeStorageCritical()) {
//     navigateToStorageCriticalModel(navigation, onContinue);
//     return;
//   }
//   onContinue();
// };

const openStorageSettings = () => {
  if (Platform.OS === 'android') {
    IntentLauncher.startActivity({
      action: 'android.settings.INTERNAL_STORAGE_SETTINGS',
    });
  } else {
    Linking.openSettings();
  }
  // BackHandler.exitApp();
};

export const navigateToStorageInsufficientModel = (navigation, onContinue: () => void) => {
  navigation.navigate('ModalStorageCheck', {
    title: 'Storage is Running Low',
    content: 'Warning: Please clean up the storage, or the next shift may be blocked. You can manage your storage in the Settings.',
    message: 'How to clear up storage on POS?',
    link: 'https://care.storehub.com/en/articles/11483163-issue-handling-when-pos-has-no-available-space',
    icon: 'warn',
    submitText: 'Go to Settings',
    cancelText: 'Continue Anyway',
    onSubmitHandler: () => {
      openStorageSettings();
    },
    onCancelHandler: () => {
      onContinue();
    },
  });
};

export const navigateToStorageCriticalModel = (navigation, size, onContinue: () => void) => {
  navigation.navigate('ModalStorageCheck', {
    title: 'Storage Full',
    content: `POS cannot continue to work properly due to insufficient space on your device. At least ${size}MB of free space is required. You can clear your storage in Settings.`,
    message: 'How to clear up storage on POS?',
    link: 'https://care.storehub.com/en/articles/11483163-issue-handling-when-pos-has-no-available-space',
    icon: 'error',
    submitText: 'Go to Settings',
    onSubmitHandler: () => {
      openStorageSettings();
    },
    onCancelHandler: () => {
      onContinue();
    },
  });
};

export const navigateToStorageErrorModel = () => {
  navigate({
    routeName: 'ModalStorageCheck',
    params: {
      title: 'Storage Full',
      content: 'POS cannot continue to work properly due to insufficient space on your device. You can clear your storage in Settings.',
      message: 'How to clear up storage on POS?',
      link: 'https://care.storehub.com/en/articles/11483163-issue-handling-when-pos-has-no-available-space',
      icon: 'error',
      submitText: 'Go to Settings',
      onSubmitHandler: () => {
        openStorageSettings();
        BackHandler.exitApp();
      },
      onCancelHandler: () => {},
    },
  });
};

export const navigateToSagaErrorModel = () => {
  navigate({
    routeName: 'ModalInfoPopup',
    params: {
      title: 'Unexpected Error',
      message: 'Please restart StoreHub POS app.',
      icon: 'error',
      submitText: 'Restart',
      onSubmitHandler: () => {
        throw new Error('Unexpected Error');
      },
      onCancelHandler: () => {},
    },
  });
};
