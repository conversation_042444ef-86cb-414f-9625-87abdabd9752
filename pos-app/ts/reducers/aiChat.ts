import { handleActions } from 'redux-actions';
import * as Immutable from 'immutable';
import { ActionButton } from '../actions/aiChat';

export interface ChatMessage {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
  type?: 'text' | 'navigation' | 'order' | 'error' | 'system' | 'tool_call' | 'tool_result';
  actionButtons?: ActionButton[];
  toolCall?: {
    toolName: string;
    parameters?: any;
  };
  toolResult?: {
    toolName: string;
    result: any;
    error?: string;
  };
}

export interface AIChatState {
  messages: ChatMessage[];
  isVisible: boolean;
  isMinimized: boolean;
}

const initialState = Immutable.fromJS({
  messages: [],
  isVisible: false,
  isMinimized: false,
});

export default handleActions(
  {
    SEND_CHAT_MESSAGE: (state: any, action: any) => {
      const userMessage: ChatMessage = {
        id: Date.now().toString(),
        text: action.payload.message,
        isUser: true,
        timestamp: new Date(),
        type: 'text',
      };

      return state.update('messages', (messages: any) => messages.push(Immutable.fromJS(userMessage)));
    },

    RECEIVE_CHAT_MESSAGE: (state: any, action: any) => {
      return state.update('messages', (messages: any) => messages.push(Immutable.fromJS(action.payload.message)));
    },

    PROCESS_AI_RESPONSE: (state: any, action: any) => {
      const { response, intent, actionButtons, toolCall } = action.payload;

      const aiMessage: ChatMessage = {
        id: Date.now().toString() + '_ai',
        text: response,
        isUser: false,
        timestamp: new Date(),
        type: intent,
        actionButtons: actionButtons,
        toolCall: toolCall,
      };

      return state.update('messages', (messages: any) => messages.push(Immutable.fromJS(aiMessage)));
    },

    SET_CHAT_VISIBILITY: (state: any, action: any) => {
      return state.set('isVisible', action.payload.isVisible);
    },

    TOGGLE_CHAT_MINIMIZED: (state: any) => {
      return state.set('isMinimized', !state.get('isMinimized'));
    },

    CLEAR_CHAT_HISTORY: (state: any) => {
      return state.set('messages', Immutable.List());
    },
  },
  initialState
);
