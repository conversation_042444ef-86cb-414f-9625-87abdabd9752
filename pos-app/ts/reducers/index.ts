import { combineReducers } from 'redux-immutable';
import aiChat from './aiChat';
import { DevPanel } from './DevPanel';
import { AWS } from './aws';
import { BadgeNumber } from './badgeNumber';
import { CFD } from './cfd';
import { CfdApp } from './cfdApp';
import * as Common from './common';
import { CustomAppState } from './customAppState';
import { EditingOpenOrder } from './editingOpenOrder';
import CurrentEmployee from './employee';
import { EnvSetting } from './envSetting';
import EWallet from './ewallet';
import Ghl from './ghl';
import { GrowthBookFeatures } from './growthbook';
import { KDS } from './kds';
import { localCountryMap } from './localCountryMap';
import { MRS } from './mrs';
import { NCS } from './ncs';
import { PauseMode } from './pauseMode';
import { Printer } from './printer';
import { Settings } from './settings';
import { Shift } from './shiftStatus';
import { Storage } from './storage';
import { TransactionSession } from './transaction';
import { Websocket } from './websocket';
import { Printing2 } from './printing2';

export default combineReducers({
  ...Common,
  aiChat,
  Websocket,
  Storage,
  TransactionSession,
  Shift,
  CurrentEmployee,
  Settings,
  EWallet,
  BadgeNumber,
  MRS,
  EnvSetting,
  Printer,
  PauseMode,
  EditingOpenOrder,
  KDS,
  NCS,
  CfdApp,
  CFD,
  GrowthBookFeatures,
  DevPanel,
  Ghl,
  localCountryMap,
  AWS,
  CustomAppState,
  Printing2,
});
