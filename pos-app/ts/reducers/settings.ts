import * as Immutable from 'immutable';
import { get } from 'lodash';
import { Action, handleActions } from 'redux-actions';

import * as Actions from '../actions';

const DefaultSettingValue = Immutable.fromJS({
  generalSettings: Immutable.fromJS({
    autoSyncAfterOpenShift: true,
    enableCustomerShortCut: true,
    enableCustomerQR: false,
    enableCloseLastShiftNotification: true,
    shouldLoadOnlineOpenOrders: true,
  }),
  printerGeneralSettings: Immutable.Map(),
  printerTagsSettings: Immutable.List(),
  accountSettings: Immutable.fromJS({ expired: false, offline: false }),
  customerDisplaySettings: Immutable.Map(),
  tableLayoutSettings: Immutable.fromJS({ enableTableLayout: false }),
  defaultWifi: Immutable.Map(),
  notificationToken: '',
});

const updateSinglePrinterReducer = (state, action: Action<Actions.UpdateSinglePrinterType>) => {
  if (action.payload) {
    const printerId = get(action.payload, 'printerId');
    const preList = state.get('printerTagsSettings');
    const index = preList.findIndex(v => v && v.get('printerId') == printerId);
    if (index >= 0) {
      const newValue = get(action.payload, 'data');
      if (!newValue) {
        // delete
        return state.deleteIn(['printerTagsSettings', index]);
      } else {
        // update
        const prevPrinter = state.getIn(['printerTagsSettings', index]);
        const newPrinter = prevPrinter.mergeDeep({ ...newValue, printerId });
        return state.setIn(['printerTagsSettings', index], newPrinter);
      }
    } else {
      return state;
    }
  } else {
    return state;
  }
};

export const Settings = handleActions(
  {
    [Actions.updateGeneralSettings.toString()]: (state, action) => {
      if (action.payload) {
        return state.mergeDeep({
          generalSettings: action.payload,
        });
      } else {
        return state;
      }
    },
    [Actions.updatePrinterGeneralSettings.toString()]: (state, action) => {
      if (action.payload) {
        return state.mergeDeep({
          printerGeneralSettings: action.payload,
        });
      } else {
        return state;
      }
    },
    [Actions.updatePrinterTagsSettings.toString()]: (state, action) => {
      if (action.payload) {
        return state.set('printerTagsSettings', Immutable.fromJS(action.payload));
      } else {
        return state;
      }
    },
    [Actions.updateSinglePrinter.toString()]: updateSinglePrinterReducer,
    [Actions.updatePrinterSettings.toString()]: (state, action: Action<Actions.UpdateSinglePrinterType[]>) => {
      let updated = state;
      if (action.payload) {
        for (const payload of action.payload) {
          if (payload.printerId) {
            updated = updateSinglePrinterReducer(updated, Actions.updateSinglePrinter(payload));
          }
        }
      }
      return updated;
    },
    [Actions.updatePrinterLastPrintedTime.toString()]: (state, action) => {
      if (action.payload) {
        const printerId = get(action.payload, 'printerId');
        const lastPrintedTime = new Date().toISOString();
        const printerTagsSettingsState = state.get('printerTagsSettings');
        if (!printerTagsSettingsState) {
          return state;
        }
        const index = printerTagsSettingsState.findIndex(v => v && v.get('printerId') == printerId);
        if (index >= 0) {
          return state.setIn(['printerTagsSettings', index, 'lastPrintedTime'], lastPrintedTime).setIn(['printerTagsSettings', index, 'errorCode'], 0);
        } else {
          return state;
        }
      } else {
        return state;
      }
    },
    [Actions.updateCustomerDisplaySettings.toString()]: (state, action) => {
      if (action.payload) {
        return state.mergeDeep({
          customerDisplaySettings: action.payload,
        });
      } else {
        return state;
      }
    },
    [Actions.updateTableLayoutSettings.toString()]: (state, action) => {
      if (action.payload) {
        return state.mergeDeep({
          tableLayoutSettings: action.payload,
        });
      } else {
        return state;
      }
    },
    [Actions.updateAccountSettings.toString()]: (state, action) => {
      if (action.payload) {
        return state.mergeDeep({
          accountSettings: action.payload,
        });
      } else {
        return state;
      }
    },
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    [Actions.clearSetting.toString()]: (state, action) => {
      return DefaultSettingValue;
    },
    [Actions.setDefaultNetwork.toString()]: (state, action) => {
      return state.setIn(['defaultWifi'], Immutable.fromJS(action.payload));
    },
    [Actions.autoSetDefaultNetworkAfterSuccessfulPrinting.toString()]: state => {
      return state.setIn(['autoSetDefaultNetwork'], true);
    },
    [Actions.updateNotificationToken.toString()]: (state, action) => {
      if (action.payload) {
        return state.set('notificationToken', action.payload);
      }
      return state;
    },
  },
  DefaultSettingValue
);
