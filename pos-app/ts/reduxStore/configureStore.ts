import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Sentry from '@sentry/react-native';

import { Alert } from 'react-native';
import Config from 'react-native-config';

import { applyMiddleware, compose, legacy_createStore as createStore, StoreCreator } from 'redux';
import { autoRehydrate, persistStore } from 'redux-persist-immutable';
import createSagaMiddleware, { SagaMonitor } from 'redux-saga';
import { t } from '../constants';
import reducers from '../reducers';
import sagas from '../sagas';

import { EnvironmentType } from '../config';
import { getCurrentRouteName } from '../navigation/navigatorService';
import * as JSONUtils from '../utils/json';
import { errorPOSBasicEvent, errorStorageEvent, infoStorageEvent, POSBasicAction, ReduxPersistAction } from '../utils/logComponent';
import { initSentry } from '../utils/sentry';
import { navigateToSagaErrorModel, navigateToStorageErrorModel } from '../navigation/storageNavigations';

let Reactotron = null;
if (__DEV__) {
  Reactotron = require('../config/ReactotronConfig').default;
}
const onSagaError = (error: Error) => {
  console.error('saga error = ', error, error.stack);
  const { name, message, stack, ...otherInfo } = error || {};
  errorPOSBasicEvent({
    action: POSBasicAction.SagaError,
    reason: 'saga error',
    privateDataPayload: { sagaName: name, sagaMessage: message, sagaStack: stack, sagaInfo: otherInfo },
  });
  const scope = new Sentry.Scope();
  const routeName = getCurrentRouteName();
  scope.setExtras({ routeName });
  Sentry.captureException(error, scope);
  if (error.message.includes('database or disk is full') || error.message.includes('space left')) {
    navigateToStorageErrorModel();
  } else {
    navigateToSagaErrorModel();
    // Alert.alert(t('Error'), t('please restart this application to potentially resolve it'), [{ text: 'OK', onPress: () => null }], { cancelable: false });
  }
};

const createSagaMonitor = () => {
  let sagaMonitor: SagaMonitor | null = null;

  if (__DEV__) {
    sagaMonitor = Reactotron && Reactotron.createSagaMonitor && Reactotron.createSagaMonitor();
  }

  return sagaMonitor || undefined;
};

// create saga middleware
const sagaMiddleware = createSagaMiddleware({ onError: onSagaError, sagaMonitor: createSagaMonitor() });

const composeFuncs = [applyMiddleware(sagaMiddleware)];

// add persist autoRehydrate, note: it's not a middleware
composeFuncs.push(autoRehydrate());
// Use undefined to init Redux State,
const initialState = undefined;

if (__DEV__) {
  const _window = window as any; // depress window warnning
  // integrate React Native Debugger, only in debug mode can get window instance
  typeof _window === 'object' && _window.__REDUX_DEVTOOLS_EXTENSION__ && composeFuncs.push(_window.__REDUX_DEVTOOLS_EXTENSION__());

  if (Reactotron) {
    composeFuncs.push(Reactotron.createEnhancer());
  }
}

const createStoreWithMiddleware: any = compose(...composeFuncs)(createStore) as StoreCreator;

export default () => {
  const store = createStoreWithMiddleware(reducers, initialState);
  const whitelist = ['Storage', 'Settings', 'MRS', 'Shift', 'KDS', 'GrowthBookFeatures', 'NCS', 'CfdApp', 'localCountryMap', 'AWS', 'NetInfo'];
  if (Config.ENVIRONMENT === EnvironmentType.Fat || Config.ENVIRONMENT === EnvironmentType.Debug) {
    whitelist.push('EnvSetting');
  }
  // run saga
  sagaMiddleware.run(sagas);
  // This will set-up the store to persist to AsyncStorage
  persistStore(
    store,
    {
      debounce: 300,
      storage: AsyncStorage,
      whitelist,
    },
    (err, state) => {
      console.log('rehydration complete');
      if (err) {
        errorStorageEvent({
          action: ReduxPersistAction.REHYDRATE,
          reason: 'Rehydrate Error',
          exception: err,
          privateDataPayload: {
            state: JSONUtils.stringify(state),
          },
        });
      } else {
        initSentry();
        infoStorageEvent({ action: ReduxPersistAction.REHYDRATE });
      }
    }
  );

  return { store };
};
