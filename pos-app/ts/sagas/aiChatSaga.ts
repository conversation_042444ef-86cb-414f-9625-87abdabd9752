import { call, put, takeEvery } from 'redux-saga/effects';
import * as NavigationService from '../navigation/navigatorService';
import { processAIResponse, receiveChatMessage, ChatMessage } from '../actions/aiChat';
import { geminiAIService, POSAgentTools } from '../services/geminiAIService';

function* handleSendMessage(action: any) {
  try {
    const { message } = action.payload;

    // Process the message with Gemini AI service
    const aiResponse = yield call([geminiAIService, 'processMessage'], message);

    // Handle tool call intent
    if (aiResponse.intent === 'tool_call' && aiResponse.toolCall) {
      yield call(handleToolCall, aiResponse.toolCall, message);
      return; // Exit early as tool call will generate its own response
    }

    // Handle navigation intent - but don't send confirmation message since AI will respond
    if (aiResponse.intent === 'navigation' && aiResponse.params?.route) {
      yield call(handleNavigationIntent, aiResponse.params.route);
    }

    // Only include actionButtons if they exist and have content
    const responsePayload: any = {
      userMessage: message,
      response: aiResponse.response,
      intent: aiResponse.intent,
      params: aiResponse.params,
    };

    // Only add actionButtons if they exist and are not empty
    if (aiResponse.actionButtons && aiResponse.actionButtons.length > 0) {
      responsePayload.actionButtons = aiResponse.actionButtons;
    }

    // Dispatch the AI response
    yield put(processAIResponse(responsePayload));
  } catch (error) {
    console.error('AI Chat Error:', error);

    // Handle error with appropriate message
    let errorMessage = "Sorry, I'm having trouble processing your request right now. Please try again.";

    if (error.message && error.message.includes('GEMINI_API_KEY')) {
      errorMessage = 'AI service is not configured. Please contact your administrator.';
    }

    const errorChatMessage: ChatMessage = {
      id: Date.now().toString(),
      text: errorMessage,
      isUser: false,
      timestamp: new Date(),
      type: 'error',
    };

    yield put(receiveChatMessage({ message: errorChatMessage }));
  }
}

function* handleToolCall(toolCall: any, originalMessage: string) {
  try {
    // Show tool call message to user
    const toolCallMessage: ChatMessage = {
      id: Date.now().toString(),
      text: 'Gathering information...',
      isUser: false,
      timestamp: new Date(),
      type: 'tool_call',
      toolCall: toolCall,
    };

    yield put(receiveChatMessage({ message: toolCallMessage }));

    // Execute the agent tool
    const toolResult = yield call(POSAgentTools.executeAgentTool, toolCall.toolName, toolCall.parameters);

    console.log('SHAI', 'toolResult', toolResult);
    // Create tool result message
    const toolResultMessage: ChatMessage = {
      id: Date.now().toString() + '_result',
      text: 'Retrieved data',
      isUser: true,
      timestamp: new Date(),
      type: 'tool_result',
      toolResult: {
        toolName: toolCall.toolName,
        result: toolResult,
      },
    };

    yield put(receiveChatMessage({ message: toolResultMessage }));

    // Process tool result with AI to get final response
    const aiResponse = yield call([geminiAIService, 'processToolResult'], {
      toolName: toolCall.toolName,
      result: toolResult,
    });

    // Dispatch the AI response after processing tool result
    const responsePayload: any = {
      userMessage: originalMessage,
      response: aiResponse.response,
      intent: aiResponse.intent,
      params: aiResponse.params,
    };

    // Only add actionButtons if they exist and are not empty
    if (aiResponse.actionButtons && aiResponse.actionButtons.length > 0) {
      responsePayload.actionButtons = aiResponse.actionButtons;
    }

    yield put(processAIResponse(responsePayload));
  } catch (error) {
    console.error('Tool Call Error:', error);

    const errorMessage: ChatMessage = {
      id: Date.now().toString(),
      text: `Sorry, I couldn't execute the ${toolCall.toolName} tool. Please try again.`,
      isUser: false,
      timestamp: new Date(),
      type: 'error',
    };

    yield put(receiveChatMessage({ message: errorMessage }));
  }
}

function* handleClearChatHistory(action: any) {
  try {
    // Reset the AI conversation session when chat history is cleared
    yield call([geminiAIService, 'resetConversation']);
  } catch (error) {
    console.error('Error resetting AI conversation:', error);
  }
}

function* handleNavigationIntent(route: string) {
  try {
    // Navigate using the exact route provided by the AI service
    NavigationService.navigate({ routeName: route });

    // Don't send confirmation message here - let the AI service handle the response
  } catch (error) {
    console.error('Navigation Error:', error);

    // Only send error message if navigation fails
    const errorMessage: ChatMessage = {
      id: Date.now().toString(),
      text: `Sorry, I couldn't navigate to ${route}. Please try again or navigate manually.`,
      isUser: false,
      timestamp: new Date(),
      type: 'error',
    };

    yield put(receiveChatMessage({ message: errorMessage }));
  }
}

export default function* aiChatSaga() {
  yield takeEvery('SEND_CHAT_MESSAGE', handleSendMessage);
  yield takeEvery('CLEAR_CHAT_HISTORY', handleClearChatHistory);
}
