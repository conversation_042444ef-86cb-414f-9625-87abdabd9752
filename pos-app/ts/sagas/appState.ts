import { get } from 'lodash';
import { Action } from 'redux-actions';
import { eventChannel } from 'redux-saga';
import { call, fork, put, select, spawn, take, takeLatest } from 'redux-saga/effects';
import * as Actions from '../actions';
import { genCustomerQR, getCustomerQRSetting, setQrLoginOverlay, stopCheckCustomerInfoFromQR } from '../actions';
import { IsIOS, TransactionFlowType } from '../constants';
import globalConfig from '../utils/globalConfig';
import LogUtils from '../utils/log';
import { infoPOSBasicEvent, POSBasicAction } from '../utils/logComponent';
import LocalLogger from '../utils/logComponent/local-logging/LocalLogger';
import { isEmpty } from '../utils/validator';
import { setQrLoginOverlaySaga } from './cfd';
import { genCustomerQRSaga, getCustomerQRSettingSaga } from './customer';
import {
  selectCurrentNetworkInfo,
  selectEnableCustomerDisplay,
  selectIsAppReadyForTrackingNetInfo,
  selectIsMallIntegrationEnabled,
  selectTransactionSession,
} from './selector';

let beepWorkerChannel;
let activationWorkerChannel;
let checkUpdateDateChannel;
let checkReportUploadStatusChannel;
let checkCustomerInfoFromQRChannel;

const generateIntervalChannel = period => {
  return eventChannel(emitter => {
    const _tmr = setInterval(() => {
      emitter({ tick: 1 });
    }, period);
    return () => {
      clearInterval(_tmr);
    };
  });
};

export const setCfdDisplay = function* () {
  const immutableTransaction = yield select(selectTransactionSession);
  let transactionId;
  if (Boolean(immutableTransaction)) {
    transactionId = immutableTransaction.get('transactionId');
  }
  if (transactionId !== undefined) {
    const transaction = immutableTransaction.toJS();
    yield put(Actions.setCfdTransaction(transaction));
  }
};
export const appStateSaga = function* (action) {
  const status = action.payload;
  if (status === 'init' || status === 'active') {
    if (status === 'active') {
      if (IsIOS) {
        const enableCustomerDisplay = yield select(selectEnableCustomerDisplay);
        if (enableCustomerDisplay) {
          yield put(Actions.startIOSDisplay());
        }
      }
      yield setCfdDisplay();
      yield put(Actions.syncActivationStatus({}));
    }

    if (checkUpdateDateChannel != null) {
      checkUpdateDateChannel.close();
      checkUpdateDateChannel = null;
    }

    checkUpdateDateChannel = generateIntervalChannel(2 * 60 * 60 * 1000); //  every 2 hours to check app update
    yield fork(checkUpdateWorker, checkUpdateDateChannel);

    // fork beep ordering worker
    if (beepWorkerChannel != null) {
      beepWorkerChannel.close();
      beepWorkerChannel = null;
    }
    beepWorkerChannel = generateIntervalChannel(3 * 60 * 1000); //  every 3 mins to check beep orders
    yield fork(checkingBeepWorker, beepWorkerChannel);

    if (activationWorkerChannel != null) {
      activationWorkerChannel.close();
      activationWorkerChannel = null;
    }
    activationWorkerChannel = generateIntervalChannel(24 * 60 * 60 * 1000); // every 24 hours to check activation status
    yield fork(checkingActivationWorker, activationWorkerChannel);
  } else if (status === 'background') {
    infoPOSBasicEvent({
      action: POSBasicAction.Background,
      destination: 'local',
    });
    console.log('Background');
    // Close channels
    beepWorkerChannel && beepWorkerChannel.close();
    beepWorkerChannel = null;
    activationWorkerChannel && activationWorkerChannel.close();
    activationWorkerChannel = null;
    checkUpdateDateChannel && checkUpdateDateChannel.close();
    checkUpdateDateChannel = null;
    checkReportUploadStatusChannel && checkReportUploadStatusChannel.close();
    if (IsIOS) {
      const enableCustomerDisplay = yield select(selectEnableCustomerDisplay);
      if (enableCustomerDisplay) {
        yield put(Actions.stopIOSDisplay());
      }
    }
    // flush log
    LogUtils.flush();
    LocalLogger.flushSync();
  }
};

export const checkReportUpdateStatusSaga = function* () {
  const IsMallIntegrationEnabled = yield select(selectIsMallIntegrationEnabled);
  if (!IsMallIntegrationEnabled) {
    return;
  }
  if (checkReportUploadStatusChannel != null) {
    checkReportUploadStatusChannel.close();
    checkReportUploadStatusChannel = null;
  }
  checkReportUploadStatusChannel = generateIntervalChannel(30 * 1000); //  every 30 seconds to check report upload status
  yield fork(checkingReportUpdateStatusWorker, checkReportUploadStatusChannel);
};

export const checkReportUpdateStatusEndSaga = function () {
  checkReportUploadStatusChannel && checkReportUploadStatusChannel.close();
  checkReportUploadStatusChannel = null;
};

export const checkUpdateWorker = function* (chan) {
  try {
    while (true) {
      yield take(chan);
      yield put(Actions.checkUpdate({ checkInstall: false }));
    }
  } finally {
    chan.close();
    console.log('checkUpdateWorker terminated.');
  }
};

export const checkingBeepWorker = function* (chan) {
  try {
    while (true) {
      yield take(chan);
      yield put(Actions.checkUnPrintedBeepOrdersBegin({}));
    }
  } finally {
    chan.close();
    console.log('checkingBeepWorker terminated.');
  }
};

export const checkingReportUpdateStatusWorker = function* (chan) {
  try {
    while (true) {
      yield take(chan);
      yield put(Actions.checkReportUpdateStatusBegin({}));
    }
  } finally {
    console.log('checkingReportUpdateStatusWorker terminated.');
  }
};

// TODO
export const checkCustomerInfoFromQRWorker = function* (chan) {
  try {
    while (true) {
      yield take(chan);
      yield put(Actions.checkCustomerInfoFromQRBegin({}));
    }
  } finally {
    console.log('checkCustomerInfoFromQRWorker terminated.', checkCustomerInfoFromQRChannel);
  }
};

export const checkingActivationWorker = function* (chan) {
  try {
    while (true) {
      yield take(chan);
      yield put(Actions.syncActivationStatus({}));
    }
  } finally {
    chan.close();
    console.log('checkingActivation terminated.');
  }
};

export const checkCustomerInfoFromQRSaga = function* (action: Action<Actions.CheckCustomerInfoFromQRType>) {
  // if (IsIOS) return;
  const customer = get(action, ['payload', 'currentRecord', 'customer']);
  const transactionType = get(action, ['payload', 'currentRecord', 'transactionType']);
  if (Boolean(customer) || transactionType === TransactionFlowType.Return) {
    yield call(stopCheckCustomerInfoFromQRSaga, stopCheckCustomerInfoFromQR({}));
    return;
  }
  const isCustomerQREnabled = yield call(getCustomerQRSettingSaga, getCustomerQRSetting({}));
  if (!isCustomerQREnabled) return;

  // get customer QR
  globalConfig.customerQRFlag.isListening = true;
  globalConfig.customerQRFlag.needListening = true;
  if (isEmpty(globalConfig.customerQRFlag.QrCodeId) || isEmpty(globalConfig.customerQRFlag.QrCode)) {
    const info = yield call(genCustomerQRSaga, genCustomerQR({}));
    if (info && info.QRCode) {
      globalConfig.customerQRFlag.QrCodeId = info.id;
      globalConfig.customerQRFlag.QrCode = info.QRCode;
    }
  } else if (checkCustomerInfoFromQRChannel !== null) {
    // the worker is running
    return;
  }
  if (isEmpty(globalConfig.customerQRFlag.QrCodeId) || isEmpty(globalConfig.customerQRFlag.QrCode)) return;
  yield call(setQrLoginOverlaySaga, setQrLoginOverlay({ status: 'visible', qrData: globalConfig.customerQRFlag.QrCode }));
  checkCustomerInfoFromQRChannel = generateIntervalChannel(3 * 1000); //  every 3 seconds to check report upload status
  yield spawn(checkCustomerInfoFromQRWorker, checkCustomerInfoFromQRChannel);
};

export const stopCheckCustomerInfoFromQRSaga = function* (action: Action<Actions.StopCheckCustomerInfoFromQRType>) {
  // if (IsIOS) return;
  const isTemporary = get(action, ['payload', 'isTemporary'], false);
  const inForeground = get(action, ['payload', 'inForeground'], false);
  const oldNeedListening = globalConfig.customerQRFlag.needListening;
  const oldQrCodeId = globalConfig.customerQRFlag.QrCodeId;
  const oldQrCode = globalConfig.customerQRFlag.QrCode;
  checkCustomerInfoFromQRChannel && checkCustomerInfoFromQRChannel.close();
  checkCustomerInfoFromQRChannel = null;
  globalConfig.customerQRFlag = {
    needListening: isTemporary ? oldNeedListening : false,
    isListening: false,
    QrCodeId: isTemporary ? oldQrCodeId : null,
    QrCode: isTemporary ? oldQrCode : null,
  };
  if (!inForeground) {
    yield call(setQrLoginOverlaySaga, setQrLoginOverlay({ status: 'hidden' }));
  }
};

function* setNetInfoSaga(action) {
  const preNetInfo = (yield select(selectCurrentNetworkInfo)).toJS();
  const prevIsInternetReachable = get(preNetInfo, 'isInternetReachable', false);
  const newIsInternetReachable = get(action.payload, 'isInternetReachable', false);

  const appReadyForTrackingNetInfo = yield select(selectIsAppReadyForTrackingNetInfo);

  if (appReadyForTrackingNetInfo) {
    if (prevIsInternetReachable && newIsInternetReachable === false) {
      console.log('🛜 The network has been disconnected');
    } else if (prevIsInternetReachable === false && newIsInternetReachable) {
      console.log('🛜 The network has been reconnected');
    }
  }

  yield put(Actions.setNetInfo(action.payload));
}

function* appState() {
  yield takeLatest(Actions.syncAppState.toString(), appStateSaga);
  yield takeLatest(Actions.checkReportUpdateStatusEnd.toString(), checkReportUpdateStatusEndSaga);
  yield takeLatest(Actions.checkReportUpdateStatus.toString(), checkReportUpdateStatusSaga);
  yield takeLatest(Actions.checkCustomerInfoFromQR.toString(), checkCustomerInfoFromQRSaga);
  yield takeLatest(Actions.stopCheckCustomerInfoFromQR.toString(), stopCheckCustomerInfoFromQRSaga);
  yield takeLatest(Actions.setNetInfoAction.toString(), setNetInfoSaga);
}

export default fork(appState);
