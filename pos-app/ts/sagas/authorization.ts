import React from 'react';
import { call, fork, select, takeLatest, put } from 'redux-saga/effects';
import {
  checkCloseZReadingAccess,
  jumpToSettingsAction,
  RequestActionAccessResult,
  requestAuthorizedAction,
  RequestAuthorizedActionType,
  requestCameraPermissionAction,
  setCameraPermissionStatusAction,
} from '../actions';

import { AuthorizationAccessType, AuthorizationType, CashierActions, IsIOS, MallIntegrationChannel } from '../constants';
import DAL from '../dal';
import * as NavigationService from '../navigation/navigatorService';
import { selectEmployeeId, selectMallIntegrationChannel, selectPosSettingAccess, selectStore } from './selector';
import RNFaceCaptureManager from '../utils/faceCapture';
import IntentLauncher from 'react-native-intent-launcher';
import DeviceInfo from 'react-native-device-info';
import { requestPermissions } from '../../libs/react-native-uvc-camera/handlePermissions';
import { CameraManager } from '../../libs/react-native-uvc-camera/UvcCamera';

export type ManagerPinParams = {
  title?: string;
  renderIcon?: () => React.ReactNode;
  hint?: string;
  showBackground?: boolean;
  goHomeWhenClose?: boolean;
};
export const openManagerModel = function (needDelay = false, params: ManagerPinParams = {}): Promise<RequestActionAccessResult> {
  return new Promise(resolve => {
    const onDismissHandler = (result: RequestActionAccessResult) => {
      resolve(result);
    };
    if (needDelay) {
      const timeout = setTimeout(() => {
        NavigationService.navigate({ routeName: 'ModalManagerPin', params: { onDismissHandler, ...params } });
        Boolean(timeout) && clearTimeout(timeout);
      }, 100);
    } else {
      NavigationService.navigate({ routeName: 'ModalManagerPin', params: { onDismissHandler, ...params } });
    }
  });
};

export const getCashierAccessConfig = function (store, configKey) {
  return store.getIn(['cashierAccesses', configKey]);
};

export const doCashierAction = function* (payload: RequestAuthorizedActionType, authorizationAccessType: AuthorizationAccessType) {
  const { employeeId, onSuccess, onFailure, name, goHomeWhenClose = false } = payload;
  const employee = DAL.getEmployeeById(employeeId);
  // AnyCashier
  if (authorizationAccessType === AuthorizationAccessType.AnyCashier) {
    onSuccess && onSuccess.callback && onSuccess.callback({ ok: false });
    return;
  }

  // Disable
  if (authorizationAccessType === AuthorizationAccessType.Disable) {
    onFailure && onFailure.callback && onFailure.callback({ ok: true });
    return;
  }

  // NeedManagerGranted
  // Always
  if (authorizationAccessType === AuthorizationAccessType.Always || !employee.isManager) {
    const grantedResult: RequestActionAccessResult = yield call(
      openManagerModel,
      name === CashierActions.DeleteOrder ||
        name === CashierActions.SwitchMaster ||
        ((name === CashierActions.POSSetting || name === CashierActions.AyalaMallCloseZReading) && IsIOS),
      { goHomeWhenClose }
    );
    const { ok, message } = grantedResult;

    if (ok) {
      onSuccess && onSuccess.callback && onSuccess.callback({ ok });
    } else {
      onFailure && onFailure.callback && onFailure.callback({ ok, message });
    }
  } else {
    onSuccess && onSuccess.callback && onSuccess.callback({ ok: true });
  }
  return;
};

export const requestCashierActionSaga = function* (action) {
  const payload = action.payload;
  const { content, name } = payload;

  const store = yield select(selectStore);
  let authorizationAccessType = AuthorizationAccessType.NeedManagerGranted;
  switch (name) {
    case CashierActions.DeleteOrder:
      if (content.isNew) {
        authorizationAccessType = getCashierAccessConfig(store, 'deleteItemOfNew');
      } else if (content.isSaved) {
        authorizationAccessType = getCashierAccessConfig(store, 'deleteItemOfSaved');
      }
      break;
    case CashierActions.Discount:
      authorizationAccessType = getCashierAccessConfig(store, 'discount');
      break;
    case CashierActions.Refund:
      authorizationAccessType = getCashierAccessConfig(store, 'refund');
      break;
    case CashierActions.Cancel:
      authorizationAccessType = getCashierAccessConfig(store, 'cancel');
      break;
    case CashierActions.RePrint:
      authorizationAccessType = getCashierAccessConfig(store, 'reprintReceipt');
      break;
    case CashierActions.ReSend:
      authorizationAccessType = getCashierAccessConfig(store, 'reprintReceipt');
      break;
    case CashierActions.OpenCloseShift:
      authorizationAccessType = getCashierAccessConfig(store, 'openCloseShift');
      break;
    case CashierActions.ViewEditTransaction:
      authorizationAccessType = getCashierAccessConfig(store, 'viewEditTransaction');
      break;
    case CashierActions.POSSetting:
      authorizationAccessType = yield select(selectPosSettingAccess);
      break;
    case CashierActions.AyalaMallCloseZReading:
      authorizationAccessType = AuthorizationAccessType.Always;
      break;
    case CashierActions.TapResetMRSButton:
      authorizationAccessType = AuthorizationAccessType.Always;
      break;
    case CashierActions.SwitchMaster:
      authorizationAccessType = AuthorizationAccessType.Always;
      break;
    default:
      return;
  }
  yield call(doCashierAction, payload, authorizationAccessType);
};

export function* requestAuthorizedActionSaga(action) {
  const payload: RequestAuthorizedActionType = action.payload;
  if (!payload) {
    return;
  }
  const { type } = payload;
  if (type === AuthorizationType.Cashier) {
    yield call(requestCashierActionSaga, action);
  }
}

export function* requestCameraAuthorizedActionSaga() {
  if (IsIOS) {
    const result = yield call(RNFaceCaptureManager.requestCameraPermissionWithResolve);
    yield put(setCameraPermissionStatusAction(result));
  } else {
    const result = yield call(requestPermissions, false, CameraManager);
    console.log('requestCameraPermissionWithResolve', result);
    yield put(setCameraPermissionStatusAction(result));
  }
}

export function* jumpToSettingsSaga() {
  if (IsIOS) {
    yield call(RNFaceCaptureManager.jumpToSettings);
  } else {
    IntentLauncher.startActivity({
      action: 'android.settings.APPLICATION_DETAILS_SETTINGS',
      data: 'package:' + DeviceInfo.getBundleId(),
    });
  }
}

export function* checkCloseZReadingAccessSaga(action) {
  const { onSuccess, onFailure } = action.payload;
  const mallIntegrationChannel = yield select(selectMallIntegrationChannel);
  if (mallIntegrationChannel === MallIntegrationChannel.AYALA_MALL) {
    const employeeId = yield select(selectEmployeeId);
    yield put(
      requestAuthorizedAction({
        name: CashierActions.AyalaMallCloseZReading,
        employeeId,
        type: AuthorizationType.Cashier,
        onSuccess,
        onFailure,
      })
    );
  } else {
    onSuccess && onSuccess.callback && onSuccess.callback({ ok: true, needDoubleConfirm: true });
  }
}

function* authorization() {
  yield takeLatest(requestAuthorizedAction.toString(), requestAuthorizedActionSaga);
  yield takeLatest(checkCloseZReadingAccess.toString(), checkCloseZReadingAccessSaga);
  yield takeLatest(requestCameraPermissionAction.toString(), requestCameraAuthorizedActionSaga);
  yield takeLatest(jumpToSettingsAction.toString(), jumpToSettingsSaga);
}

export default fork(authorization);
