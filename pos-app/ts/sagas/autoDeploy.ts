import { fork, takeLatest } from 'redux-saga/effects';
import { checkAutoDeployments } from '../actions/autoDeploy';
import CodePush from 'react-native-code-push';

export function* autoDeploy() {
  yield takeLatest(checkAutoDeployments.toString(), checkAutoDeploymentsSaga);
}

function* checkAutoDeploymentsSaga() {
  try {
    const deploymentKey = 'EyrvOo1N6pt-wa3fFIToPKOwfH_7id_0';
    const remotePackage = yield CodePush.checkForUpdate(deploymentKey);
    if (remotePackage) {
      remotePackage.download;
    }
    console.log('AutoDeploy remotePackage:', remotePackage);

    const result1 = yield CodePush.sync({ deploymentKey, installMode: CodePush.InstallMode.IMMEDIATE });
    console.log('AutoDeploy sync:', result1);
    // const result2 = yield CodePush.checkForUpdate();
    // console.log('AutoDeploy checkForUpdate:', result2);

    const appVersion = '1.85.0';
    const host = 'http://**********:3000';
    const clientUniqueId = 'd7523535-8c62-4ee2-9dc1-f3c1bc7bb692';

    const data = yield checkCodePushUpdate(host, deploymentKey, appVersion, clientUniqueId);
    console.log('AutoDeploy checkCodePushUpdate:', data);

    // const result = yield CodePush.checkForUpdate('lOBBMVosFGn62tVA4MklnN3y75mYid_0');
    // console.log('AutoDeploy', result);
  } catch (error) {
    console.log('AutoDeploy', error);
  }
}

export const checkCodePushUpdate = async (host: string, deploymentKey: string, appVersion: string, clientUniqueId: string) => {
  try {
    const url = `${host}/v0.1/public/codepush/update_check?deployment_key=${deploymentKey}&app_version=${appVersion}&client_unique_id=${clientUniqueId}`;
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error('Error checking CodePush update:', error);
    throw error;
  }
};

export default fork(autoDeploy);
