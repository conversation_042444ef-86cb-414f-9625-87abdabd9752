import { filter, head } from 'lodash';
import { call, fork, select, takeLatest } from 'redux-saga/effects';
import * as Actions from '../actions';
import { getStoreState } from '../config';
import AUManager from '../utils/autoUpdate';
import DeviceInfoData from '../utils/deviceInfo';
import { PushyConfig, PushyVersionItem } from '../utils/growthbook';
import { CheckUpdateAction } from '../utils/logComponent';
import { warnCheckUpdateEvent } from '../utils/logComponent/buz/forceupdate';
import { getPushyVersion, pushySync } from '../utils/pushy';
import { selectBusinessName, selectPushyConfig } from './selector';

// eslint-disable-next-line @typescript-eslint/no-unused-vars, require-yield
export const checkUpdateSaga = function* (action) {
  // Pushy
  const pushyEnabled: PushyConfig = yield select(selectPushyConfig);

  if (pushyEnabled && pushyEnabled.data) {
    const version = DeviceInfoData.version;
    const pushyConfigItem: PushyVersionItem = head(filter(pushyEnabled.data, v => v.mobileVersion === version));
    if (pushyConfigItem) {
      // Current version has a hotfix
      const hotfixLabel = yield call(getPushyVersion);
      if (pushyConfigItem.hotfixLabel && hotfixLabel != pushyConfigItem.hotfixLabel) {
        // Current version not updated
        pushySync();
      }
    }
  }

  // Native APK Upgrade
  const { checkInstall } = action.payload;
  const businessName = selectBusinessName(getStoreState());
  const { errCode, errMessage, updateInfo } = yield call(AUManager.checkUpdate, checkInstall, businessName);
  if (errCode !== 0) {
    warnCheckUpdateEvent({ action: CheckUpdateAction.CheckUpdate, reason: errMessage, updateInfo });
  }
};

function* autoUpdate() {
  yield takeLatest(Actions.checkUpdate.toString(), checkUpdateSaga);
}

export default fork(autoUpdate);
