import { filter, get, isArray, map } from 'lodash';

import { Action } from 'redux-actions';
import { call, fork, put, select, take, takeLatest } from 'redux-saga/effects';
import * as Actions from '../actions';
import { stopCheckCustomerInfoFromQR, TransactionTypeWithDisplay } from '../actions';
import { IsAndroid, IsIOS } from '../constants';
import DAL from '../dal';
import { PromotionType } from '../typings';
import { convertToNumber, getUnNullValue, newConvertCurrencyToSymbol, roundingToAmount } from '../utils';
import CfdManager from '../utils/cfd';
import { createDate } from '../utils/datetime';
import { generatePromotionTitle } from '../utils/promotion';
import { generateDescriptionString, generateSingleDescriptionString, getSCDisplayBirDiscountTitle } from '../utils/transaction';
import { getStoreInfo } from './printing/common';
import { CFDAction, logCFDEvent } from '../utils/logComponent/buz/cfd';
import { LogResult } from '../utils/logComponent';
import {
  selectCurrency,
  selectEnableCashback,
  selectEnableLoyalty,
  selectEnablePoints,
  selectLocalCountryMap,
  selectLoyaltyRatio,
  selectMembershipEnabled,
  selectRoundAllTransactions,
  selectRountTo,
  selectStoreId,
  selectStoreMembershipTiers,
} from './selector';
import { updateCfdConfiguration, updateCfdMembership, updateCfdQrLogin, updateCfdTransactionModel } from '../actions/cfd';
import { getCurrentRouteName } from '../navigation/navigatorService';

export const setTransactionSessionSaga = function* (action) {
  const transaction: TransactionTypeWithDisplay = action.payload;
  if (!transaction || transaction.subtotal === undefined) {
    if (IsIOS) {
      CfdManager.sendTransactionRecord({
        eventType: 'sale',
        total: 0,
        customer: {},
        items: [],
      });
    }
    yield put(updateCfdTransactionModel(null));
    return;
  }
  if (IsAndroid || IsIOS) {
    const { storeName, address, phone, currency, registerId, business, enableServiceCharge = false } = yield call(getStoreInfo);
    const localCountryMap = yield select(selectLocalCountryMap);
    const currencySymbol = newConvertCurrencyToSymbol(localCountryMap, currency);
    const { receiptNumber: receiptId, createdDate: receiptDate, items = [], payments, display } = transaction;
    const _registerId = get(transaction, 'registerId') || registerId;
    const subtotal = convertToNumber(getUnNullValue(display, 'subtotal', 0));
    const discount = getUnNullValue(display, 'discount', 0);
    const birDiscountTitle = getSCDisplayBirDiscountTitle(transaction);
    const birDiscount = get(display, 'birDiscount');

    const tax = get(display, 'tax');
    const total = get(display, 'total');
    const serviceCharge = getUnNullValue(display, 'serviceCharge', 0);

    const data = [...items];

    if (transaction.promotions && transaction.promotions.length > 0) {
      data.push({
        // @ts-ignore
        promotions: transaction.promotions,
      });
    }
    const purchasedItems = filter(data, _ => !Boolean(_.itemType)).map(_item => {
      const { quantity, title, options, notes, unitPrice, display } = _item;

      const total = get(display, 'total');
      const subTotal = get(display, 'subtotal');

      const optionsString = generateDescriptionString(options);

      const promotions = map(get(_item, 'promotions'), promotion => {
        const realmPromotion: PromotionType = DAL.getPromotionById(promotion.promotionId);
        const title = generatePromotionTitle(realmPromotion, promotion);
        const discount = get(promotion, ['display', 'discount'], 0);
        return {
          promotionName: title,
          discount: -discount,
        };
      });

      return {
        quantity,
        subTotal,
        title,
        total,
        options: optionsString,
        notes,
        unitPrice: isNaN(unitPrice) ? 0.0 : unitPrice,
        promotions,
      };
    });

    const _payments = map(payments, _item => {
      const { paymentMethodId, cashTendered, amount, roundedAmount, subType } = _item;
      return {
        paymentMethodId,
        cashTendered,
        amount,
        roundedAmount,
        subType,
      };
    });
    const roundAllTransactions = yield select(selectRoundAllTransactions);
    const model: any = {
      business,
      storeName,
      address,
      phone,
      currency,
      receiptId: receiptId || '',
      receiptDate: createDate(receiptDate),
      registerId: _registerId,
      cashier: '',
      purchasedItems,
      subtotal,
      discount: isNaN(discount) ? 0.0 : discount,
      birDiscountTitle,
      birDiscount,
      tax,
      total,
      payments: _payments,
      currencySymbol,
    };
    if (enableServiceCharge && serviceCharge > 0) {
      model.serviceCharge = serviceCharge;
    }
    const roundingTo = yield select(selectRountTo);
    if (roundAllTransactions) {
      const roundedAmount = roundingToAmount(total, roundingTo);
      const rounding = roundingToAmount(roundedAmount - total, 0.001);
      model.rounding = rounding;
      model.total = roundedAmount;
    }
    // if (IsAndroid) {
    //   CfdManager.updateCfdTransactionModel(model);
    // }

    if (getCurrentRouteName() !== 'Transactions') {
      yield put(updateCfdTransactionModel(model));
    }
  }

  if (IsIOS) {
    const depositAmount = get(transaction, 'depositAmount', 0);
    const { items = [] } = transaction;
    const data = [...items];

    const purchasedItems = map(data, (_item, index) => {
      let title;
      let qty;
      let priceWithoutDiscounts;
      let variants = [];
      if (_item.itemType == 'Discount') {
        title = 'Ad-hoc Discount';
        const price = get(_item, 'display.discount', 0); // full bill discount
        return {
          title,
          price: price.toFixed(2),
          variants: [],
        };
      } else if (_item.itemType == 'ServiceCharge') {
        title = 'Service Charge';
        return {
          title,
          price: (_item.total + _item.tax).toFixed(2),
          variants: [],
        };
      } else {
        title = _item.title || '';
        qty = _item.quantity;
        if (_item.discount > 0) {
          // when taxinclusive, item.display.subtotal != item.subtotal. But display.subtotal can be null sometimes. Below is a temporate solution,
          // need to check calculator lib to find the root cause.
          priceWithoutDiscounts = _item.display && _item.display.subtotal ? _item.display.subtotal.toFixed(2) : _item.subTotal.toFixed(2);
        }
        variants = filter(_item.options, v => Boolean(v)).map(v => generateSingleDescriptionString(v));
      }
      return {
        variants,
        id: _item.productId + index,
        title,
        qty,
        priceWithoutDiscounts,
        // calculatorlib issue, item.display can be null sometimes
        price: _item.display && _item.display.total ? _item.display.total.toFixed(2) : _item.total.toFixed(2),
      };
    });
    map(get(data, 'promotions'), promotion => {
      const realmPromotion: PromotionType = DAL.getPromotionById(promotion.promotionId);
      const title = generatePromotionTitle(realmPromotion, promotion);

      const promotionItem = {
        id: realmPromotion.promotionId,
        title,
        qty: promotion.quantity,
        price: -(promotion.discount - promotion.tax),
        variants: [],
        priceWithoutDiscounts: '',
      };
      purchasedItems.push(promotionItem);
    });
    let total;
    if (depositAmount > 0) {
      total = transaction.display && transaction.display.total ? transaction.display.total - depositAmount : transaction.total - depositAmount;
    } else {
      total = transaction.display && transaction.display.total ? transaction.display.total : transaction.total;
    }
    let customer = {};
    if (transaction.customer) {
      customer = {
        name: `${transaction.customer.firstName} ${transaction.customer.lastName}`,
      };
    }
    const roundingToValue = yield select(selectRountTo);
    const roundingTo = typeof roundingToValue === 'number' ? roundingToValue : 0;
    const roundingToTotal = roundingToAmount(total, roundingTo).toFixed(2);
    const model: any = {
      eventType: 'sale',
      total: roundingToTotal,
      customer,
      items: purchasedItems,
    };
    CfdManager.sendTransactionRecord(model);
  }
};

export const setCfdTransactionSaga = function* (action) {
  yield setTransactionSessionSaga(action);
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars,require-yield
// stop the checkCustomerFormQR
export const clearTransactionSessionSaga = function* (action) {
  if (IsAndroid) {
    // CfdManager.clearDisplay();
  } else {
    CfdManager.sendTransactionRecord({
      eventType: 'sale',
      total: 0,
      customer: {},
      items: [],
    });
  }
  yield put(updateCfdTransactionModel(null));
  yield put(updateCfdMembership(null));
  yield put(stopCheckCustomerInfoFromQR({}));
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const setCfdConfigurationSaga = function* (action) {
  // if (IsAndroid || IsIOS) {
  const storeInfo = yield call(getStoreInfo);
  if (!Boolean(storeInfo)) return;

  const currency = yield select(selectCurrency);
  const localCountryMap = yield select(selectLocalCountryMap);
  const currencySymbol = newConvertCurrencyToSymbol(localCountryMap, currency);

  const enableCashback = yield select(selectEnableCashback);
  const enableLoyalty = yield select(selectEnableLoyalty);
  const enableMembership = yield select(selectMembershipEnabled);
  const enablePoints = yield select(selectEnablePoints);

  const loyaltyRatio = yield select(selectLoyaltyRatio);

  const membershipTiers = yield select(selectStoreMembershipTiers);

  const baseInfo = {
    currency: currency,
    currencySymbol: currencySymbol,
    enableCashback: enableCashback,
    enableLoyalty: enableLoyalty,
    enableMembership: enableMembership,
    enablePoints: enablePoints,
    loyaltyRatio: loyaltyRatio,
    membershipTiers: membershipTiers,
  };

  const { cfdDisplay, storeName, business, enableServiceCharge, roundAllTransactions } = storeInfo;
  const { cfdDisplayId, type, text } = cfdDisplay;
  if (cfdDisplayId) {
    yield put(Actions.getCfdImages({ businessName: business, cfdDisplayId }));

    const responseAction = yield take([Actions.getCfdImages.toString() + '.success', Actions.getCfdImages.toString() + '.failure']);
    if (responseAction.type === Actions.getCfdImages.toString() + '.success') {
      const _payload = responseAction.payload;
      if (_payload) {
        const { img } = _payload;
        let imagesAppendBusiness = img;
        if (isArray(img)) {
          imagesAppendBusiness = img.map(item => {
            return item + `?bn=${business}`;
          });
        }
        const config = {
          images: imagesAppendBusiness,
          storeName,
          text,
          type,
          enableServiceCharge,
          roundAllTransactions,
        };

        // @ts-ignore
        yield put(updateCfdConfiguration({ ...config, ...baseInfo }));
      }
    }
  } else {
    // @ts-ignore
    yield put(updateCfdConfiguration({ storeName, enableServiceCharge, roundAllTransactions, ...baseInfo }));
  }
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars, require-yield
export const setCfdQRCodeSaga = function* (action) {
  const { qrData, qrCodeType, qrCodeProvider, currency, amount, showPayAmount } = action.payload;
  const localCountryMap = yield select(selectLocalCountryMap);
  const currencySymbol = newConvertCurrencyToSymbol(localCountryMap, currency);
  const config = {
    type: 'qrCode',
    qrData,
    qrCodeType,
    qrCodeProvider,
    payAccount: showPayAmount ? `${currencySymbol} ${(Math.round(amount * 100) / 100).toFixed(2)}` : null,
  };
  // if (IsAndroid) {
  //   CfdManager.setConfiguration(config);
  // }
  // @ts-ignore
  yield put(updateCfdConfiguration(config));
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars, require-yield
export const setCfdSettingsSaga = function* (action) {
  const { enableCustomerDisplay } = action.payload;
  if (enableCustomerDisplay) {
    if (IsIOS) {
      yield put(Actions.startIOSDisplay());
    }
  } else {
    if (IsIOS) {
      CfdManager.stopDisplay();
    }
  }
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars, require-yield
export const startIOSDisplaySaga = function* (action) {
  if (IsIOS) {
    const storeId = yield select(selectStoreId);
    const { registerId, currency, business, cfdDisplay } = yield call(getStoreInfo);
    const { cfdDisplayId } = cfdDisplay;
    const localCountryMap = yield select(selectLocalCountryMap);
    const currencySymbol = newConvertCurrencyToSymbol(localCountryMap, currency);
    const config = {
      eventType: 'config',
      storeId,
      registerId,
      handshake: '19addc17-c867-43de-b85b-410f276cb056',
      currency: currencySymbol,
      isUpdate: false,
      accountName: business,
      cfdId: cfdDisplayId || '',
    };
    CfdManager.startDisplayWithConfig(config);
    logCFDEvent({ action: CFDAction.Start, result: LogResult.Succeed });
  }
};

export const stopIOSDisplaySaga = function* (action) {
  if (IsIOS) {
    CfdManager.stopDisplay();
  }
  yield;
};

/**
 * only android
 * Customer QR CFD
 * @param action
 */
export const setQrLoginOverlaySaga = function* (action: Action<Actions.SetQrLoginOverlayType>) {
  const { status, qrData } = action.payload;
  let imageSrc = qrData;
  if (imageSrc) {
    imageSrc = imageSrc.replace(/^data:image\/(png|jpeg|jpg);base64,/, '');
  }
  const enableCashback = yield select(selectEnableCashback);
  const membershipEnabled = yield select(selectMembershipEnabled);
  const pointsEnabled = yield select(selectEnablePoints);
  let title;
  let desc;
  let isSmallFont = false;
  if (pointsEnabled) {
    title = 'Scan to claim\npoints';
    desc = null;
  } else if (membershipEnabled) {
    title = 'Scan to become\na member';
    desc = 'Exclusive Rewards and Savings';
    isSmallFont = true;
  } else if (enableCashback) {
    const loyaltyRatio = yield select(selectLoyaltyRatio);
    const cashbackRatio = Math.round(100 / loyaltyRatio);
    title = `${cashbackRatio}% Cashback`;
    desc = 'Scan to claim';
  } else {
    title = 'Scan to become\na member';
    desc = null;
  }
  // if (IsAndroid) {
  //   yield call(CfdManager.setQrLoginOverlay, { status, qrData: imageSrc, title, desc, isSmallFont });
  // }
  yield put(updateCfdQrLogin({ status, qrData: imageSrc, title, desc, isSmallFont }));
};

function* cfd() {
  yield takeLatest(Actions.setTransactionSession.toString(), setTransactionSessionSaga);
  yield takeLatest(Actions.setCfdTransaction.toString(), setCfdTransactionSaga);
  yield takeLatest(Actions.clearTransactionSession.toString(), clearTransactionSessionSaga);
  yield takeLatest(Actions.setCfdConfigurationBegin.toString(), setCfdConfigurationSaga);
  yield takeLatest(Actions.setCfdQRCode.toString(), setCfdQRCodeSaga);
  yield takeLatest(Actions.updateCustomerDisplaySettings.toString(), setCfdSettingsSaga);
  yield takeLatest(Actions.startIOSDisplay.toString(), startIOSDisplaySaga);
  yield takeLatest(Actions.stopIOSDisplay.toString(), stopIOSDisplaySaga);
  yield takeLatest(Actions.setQrLoginOverlay.toString(), setQrLoginOverlaySaga);
}

export default fork(cfd);
