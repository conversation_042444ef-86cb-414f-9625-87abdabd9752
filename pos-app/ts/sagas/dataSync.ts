import Intercom from '@intercom/intercom-react-native';
import { find, get, includes, intersection, isEmpty as isEmptyObj, isEqual, map, omit } from 'lodash';
import moment from 'moment';
import { call, delay, fork, put, SagaReturnType, select, take, takeLatest, takeLeading, throttle } from 'redux-saga/effects';
import * as Actions from '../actions';
import {
  CheckPrinterAssignedResult,
  PrinterConfigType,
  stopCheckCustomerInfoFromQR,
  SyncInfoType,
  syncLargeProductThumbnails,
  syncPromotions,
  updateSequence,
} from '../actions';
import PaymentOptions from '../config/paymentOption';
import { CancelledSource, INIT_PID, INIT_SNAPSHOT_VERSION, IsAndroid, IsIOS, ORDER_SUMMARY_PRINTER_TAG, SubscriptionStatus, SyncType, t } from '../constants';
import DAL from '../dal';
import Dao<PERSON>ache from '../dal/daoCache';
import { TransactionHelper } from '../dal/helper';

import DeviceInfo from 'react-native-device-info';
import { check, checkMultiple, checkNotifications, PERMISSIONS, RESULTS } from 'react-native-permissions';
import countryMap from '../../ts/config/countryMap.json';
import { closeTopNotification, openPrintAssignedToast, openPrinterOfflineToast, openSuccessToast } from '../components/common/TopNotification';
import { BeepNotificationType, EmployeeActivityType, ShiftType, SubOrderNotificationType, TransactionsLogType, TransactionType } from '../typings';
import { getNumberValue, getUnNullValue, mapAndroidDeviceModelToReadableName, mapAutoSignOutValue, roundTo2DecimalPlaces, safeCallback } from '../utils';
import { deleteThumbnailPromise, s3DownloadThumbnail, uploadJsonToS3 } from '../utils/awsS3Helper';
import { getLatestModifiedTime } from '../utils/datetime';
import DeviceInfoData from '../utils/deviceInfo';
import * as JSONUtils from '../utils/json';
import {
  DataSyncAction,
  errorShiftReportEvent,
  errorZReadingEvent,
  infoPOSBasicEvent,
  infoShiftReportEvent,
  infoZReadingEvent,
  logCustomDataSyncEvent,
  logFailedDataSyncEvent,
  LoggingLevel,
  logSucceedDataSyncEvent,
  POSBasicAction,
  PrinterAction,
  ShiftReportAction,
  trackLogEvent,
  ZReadingAction,
} from '../utils/logComponent';
import WsManager from '../utils/mrs';
import PrinterManager, { fontSizeMap, getFontSizeDescriptionForMetabase, uploadPrinters } from '../utils/printer';
import { isEmpty } from '../utils/validator';
import { generateLocation } from './geo';
import { syncKdsServedTime } from './kds/transaction/sync';

import RNFS, { StatResult } from 'react-native-fs';
import { KdsSettingType } from '../reducers/kds';
import { MixpanelManagerInstance } from '../utils/Analytics';
import eventBus, { PRODUCTS_UPDATED } from '../utils/eventBus';
import { isOnSameNetwork } from '../utils/network';
import { pushSettingsToKDSSaga } from './kds/settings';
// eslint-disable-next-line import/no-unresolved
import PerformanceStats from 'react-native-performance-stats';
import { resetCfd } from '../actions/cfd';
import { getStorageDistribution } from '../components/outline/getFileSize';
import { API_URL, IS_DEV_FAT } from '../config';
import RealmManager from '../dal/realm';
import { NewProductSyncFlowType } from '../utils/growthbook';
import { logProductImageSyncEvent } from '../utils/logComponent/buz/datasync';
import { LocalStorageAction, logLocalStorageEventInfo } from '../utils/logComponent/buz/localStorage';
import LocalLogger from '../utils/logComponent/local-logging/LocalLogger';
import { PingUtils } from '../utils/ping';
import { getPushyVersion } from '../utils/pushy';
import { updateGrowthBookSaga } from './growthbook';
import { initialDevice, syncLargeProductThumbnailsSaga } from './initital';
import { ensureIsOnlineMainRegister } from './kds/transaction/helper';
import { proposeSyncInfo } from './mrs/roles/proposer';
import { getMRSClientRunning } from './mrs/roles/slave';
import {
  selectAllowOutOfStock,
  selectAlreadyMigrateTableLayout,
  selectAlwaysPrintReceipt,
  selectAlwaysPrintReceiptWithBeepQRPayAtCounter,
  selectAlwaysPrintReceiptWithBeepQRPayOnline,
  selectAlwaysPrintReceiptWithOnlineDelivery,
  selectAuthToken,
  selectAutoSignOutCount,
  selectAutoSyncAfterOpenShift,
  selectAvailableKds,
  selectBackupPid,
  selectBirAccredited,
  selectBusinessName,
  selectCfd,
  selectCloseLastShiftNotification,
  selectCountry,
  selectCurrentNetworkInfo,
  selectCurrentNetworkType,
  selectCurrentWifiSSID,
  selectDefaultNetwork,
  selectDefaultNetworkName,
  selectDefaultNetworkType,
  selectDevice,
  selectDeviceFirmwareVersion,
  selectDeviceModel,
  selectDeviceSerialNumber,
  selectEmployeeId,
  selectEnableCashback,
  selectEnableCustomerDisplay,
  selectEnableCustomerQR,
  selectEnableCustomerShortCut,
  selectEnableOpenOrders,
  selectEnableOpenOrdersShortCut,
  selectGBEnableLargeProductQuantitySync,
  selectGBEnableStorageStatistics,
  selectGBEnableTrackDownloadThumbnail,
  selectGBEnableVoidReceipt,
  selectIPAddress,
  selectIsBOEnabledMRS,
  selectIsCfdPaired,
  selectIsEnabledMRS,
  selectIsKDSPurchasedANDAssigned,
  selectIsMaster,
  selectIsNCSPurchasedANDAssigned,
  selectIsNetConnected,
  selectKds,
  selectKitchenDocketFontSize,
  selectKitchenDocketVariantIsMultipleLine,
  selectKitchenPrinters,
  selectLastEmployeeSyncTime,
  selectLastProductSyncTime,
  selectLastPromotionSyncTime,
  selectLastQuickLayoutSyncTime,
  selectLastSyncTime,
  selectLastTrxCancelledFromBOSyncTime,
  selectLastZReadingCloseTime,
  selectLocalCountryMap,
  selectMasterState,
  selectMRSClients,
  selectNcs,
  selectNewCFDAllowed,
  selectNewProductSyncFlow,
  selectOnlineSyncTime,
  selectOrderSummaryPrinter,
  selectPlatform,
  selectPrintEmployeeName,
  selectPrinterTagsSettings,
  selectPrintUploadMemory,
  selectReceiptFontSize,
  selectRegisterId,
  selectRegisterObjectId,
  selectSequence,
  selectSlavePid,
  selectSnapshotPid,
  selectSnapshotVersion,
  selectStoreId,
  selectStoreInfo,
  selectStoreInfoUnsafe,
  selectSyncInfo,
  selectTableLayoutEnabled,
} from './selector';
import { generateShiftReportSaga, ShiftReport } from './shift/common';
import { updateSequenceSaga } from './shift/shiftFlow';
import { checkFreeStorageSaga, cleanupLocalStorageSaga, deleteAllProductThumbnailsSaga, voidAllProductThumbnailsSaga } from './storageCleanup';

export const SYNC_PRODUCTS_LIMIT = 400;
export const LARGE_PRODUCT_SYNC = false;

export function* dataSync() {
  yield takeLatest(Actions.syncAllStoreInfo.toString(), syncSaga);
  yield takeLatest(Actions.syncTransactionsAction.toString(), syncTransactionsSaga);
  yield takeLatest(Actions.syncShiftsAction.toString(), syncShiftsSaga);
  yield takeLatest(Actions.syncProductsAction.toString(), syncProductsSaga);
  yield takeLatest(Actions.syncEmployeesAction.toString(), syncEmployeesSaga);
  yield takeLatest(Actions.syncQuickSelectLayoutAction.toString(), syncQuickSelectLayoutSaga);
  yield takeLatest(Actions.syncTableLayoutAction.toString(), syncTableLayoutSaga);
  yield takeLatest(Actions.syncStoreInfoAction.toString(), syncStoreInfoSaga);
  yield takeLatest(Actions.syncPriceBookAction.toString(), syncPriceBookSaga);
  yield takeLatest(Actions.syncPromotionsBegin.toString(), syncPromotionsSaga);
  yield takeLatest(Actions.syncActivationStatus.toString(), syncActivationStatusSaga);
  yield takeLatest(Actions.setStoreInfo.toString(), setStoreInfoSaga);
  yield takeLatest(Actions.syncEmployeeActivityBegin.toString(), syncEmployeeActivitySaga);
  yield takeLatest(Actions.syncSequentialStartInfoBegin.toString(), syncSequentialStartInfoSaga);
  yield takeLatest(Actions.migrateTableLayoutAction.toString(), migrateTableLayoutSaga);
  yield takeLatest(Actions.readFaceCaptureIntro.toString(), readFaceCaptureIntroSaga);
  yield takeLatest(Actions.trackLocalSettings.toString(), trackLocalSettingsSaga);
  yield takeLeading(Actions.uploadLocalDataToS3.toString(), uploadLocalDataToS3Saga);
  yield takeLatest(Actions.migrateToNewPOS.toString(), migrateToNewPOSSaga);
  yield takeLatest(Actions.syncLastZReadingCloseTime.toString(), syncLastZReadingCloseTimeSaga);
  yield takeLatest(Actions.initLocalCountryMap.toString(), initLocalCountryMapSaga);
  yield takeLatest(Actions.logForDefaultNetwork.toString(), logForDefaultNetworkSaga);
  yield throttle(10 * 1000, Actions.uploadMemoryInformation.toString(), uploadMemoryInformationSaga);

  yield takeLatest(Actions.trackLocalStorage.toString(), trackLocalStorageSaga);
  yield takeLatest(Actions.cleanLocalStorage.toString(), cleanupLocalStorageSaga);

  yield takeLeading(Actions.measurePerformanceStatus.toString(), measurePerformanceStatus);

  yield takeLatest(Actions.parseProductJson.toString(), parseProductJsonSaga);
  yield takeLatest(Actions.syncLargeProductsAction.toString(), syncLargeProductsSaga);
  yield takeLatest(Actions.syncLargeProductThumbnails.toString(), syncLargeProductThumbnailsSaga);
  yield takeLatest(Actions.deleteAllProductThumbnails.toString(), deleteAllProductThumbnailsSaga);
  yield takeLatest(Actions.voidAllProductThumbnails.toString(), voidAllProductThumbnailsSaga);

  yield takeLatest(Actions.checkFreeStorage.toString(), checkFreeStorageSaga);
}

export const syncSaga = function* (action) {
  const syncInfo = yield select(selectSyncInfo);
  logSucceedDataSyncEvent({ action: DataSyncAction.SyncInfo, privateDataPayload: Boolean(syncInfo) ? syncInfo : null });

  // prop needToSyncAll is to handle the case that user cant sync data when version updated while sync time remain the same.
  // calling syncLargeProductsSaga will not take effect if productInfoSync == true
  if (!Boolean(syncInfo) || syncInfo.get('needToSyncAll') || !syncInfo.get('productInfoSync')) {
    const assignPayload = action.payload ? { payload: { isInitial: true, ...action.payload } } : { payload: { isInitial: true } };
    const syncProductsSagaAction = Object.assign({}, action, assignPayload); // syncAllStoreInfo is only called on Sign page.
    yield call(updateGrowthBookSaga);
    yield call(syncEmployeesSaga, action);
    yield call(syncQuickSelectLayoutSaga, action);
    yield call(syncPriceBookSaga, action);
    yield call(syncStoreInfoSaga, action);
    yield call(syncLastZReadingCloseTimeSaga);
    const largeProductSync = yield select(selectGBEnableLargeProductQuantitySync);
    if (largeProductSync) {
      yield call(syncLargeProductsSaga, syncProductsSagaAction);
    } else {
      yield call(syncProductsSaga, syncProductsSagaAction);
    }
    yield call(initLocalCountryMapSaga);
    yield put(Actions.setSyncInfo({ needToSyncAll: false }));
  }
};

export const syncEmployeesSaga = function* (action) {
  const { onComplete, onSyncProgress } = action.payload;

  const storeInfo = yield select(selectStoreInfoUnsafe);
  let syncTime = '';
  const syncInfo = yield select(selectSyncInfo);
  if (syncInfo !== undefined && !syncInfo.get('needToSyncAll')) {
    const syncData = syncInfo.toJS();
    syncTime = syncData.lastEmployeeSyncTime;
  }
  if (storeInfo === undefined) return;
  const store = storeInfo.toJS();
  yield put(
    Actions.syncEmployees({
      business: store.name,
      storeId: store.store._id,
      syncTime,
    })
  );
  const responseAction = yield take([Actions.syncEmployees.toString() + '.success', Actions.syncEmployees.toString() + '.failure']);
  const resp = responseAction.payload;
  if (responseAction.type === Actions.syncEmployees.toString() + '.success') {
    const employeeList = resp.asArray || [];
    yield call(setSyncEmployeesSaga, employeeList, syncTime);
    const currentEmployeeId = yield select(selectEmployeeId);
    const foundEmployee = find(employeeList, item => item.employeeId === currentEmployeeId);
    const currentEmployeeDeleted = Boolean(foundEmployee) && foundEmployee.isDeleted === true;
    const currentEmployeeName = Boolean(foundEmployee) ? `${foundEmployee.firstName} ${foundEmployee.lastName}` : '';

    logSucceedDataSyncEvent({ action: DataSyncAction.SyncEmployees });
    onComplete &&
      onComplete.callback &&
      onComplete.callback({
        result: true,
        currentEmployeeDeleted,
        currentEmployeeName,
      });
    onSyncProgress && onSyncProgress(SyncType.SyncEmployee);
  } else {
    // Sync fail
    logFailedDataSyncEvent({ action: DataSyncAction.SyncEmployees, reason: resp.message });
    onComplete && onComplete.callback && onComplete.callback({ result: false, message: resp.message });
    onSyncProgress && onSyncProgress(SyncType.SyncEmployee);
  }
};

export const setSyncEmployeesSaga = function* (payload, date) {
  let data = [];
  let latestModifiedTime = date;
  if (Array.isArray(payload)) {
    data = payload;
    latestModifiedTime = getLatestModifiedTime(latestModifiedTime, payload);
  }
  const curEmployeeId = yield select(selectEmployeeId);
  const curEmployee = find(data, employee => employee.employeeId === curEmployeeId);
  if (Boolean(curEmployee) && curEmployee.isDeleted) {
    yield put(Actions.signOut({ event: 'employeeDeleted' }));
  }

  DAL.saveSyncEmployees(data);

  yield put(
    Actions.setSyncInfo({
      employeeInfoSync: true,
      lastEmployeeSyncTime: latestModifiedTime,
    })
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const syncEmployeeActivitySaga = function* (action) {
  const storeInfo = yield select(selectStoreInfoUnsafe);
  if (!Boolean(storeInfo)) {
    return;
  }
  const registerId = storeInfo.get('registerId');
  const storeId = storeInfo.getIn(['store', '_id']);
  const employeeActivities = DAL.getEmployeeActivity();
  if (!Boolean(employeeActivities)) {
    return;
  }
  const userActionLogs = map(employeeActivities, employeeActivity => {
    return {
      additionalInfo: JSONUtils.parse(employeeActivity.additionalInfo, null),
      time: employeeActivity.time.toISOString(),
      action: employeeActivity.action,
      user: employeeActivity.user,
    };
  });
  yield put(
    Actions.syncEmployeeActivity({
      userActionLogs,
      registerId,
      storeId,
    })
  );
  const responseAction = yield take([Actions.syncEmployeeActivity.toString() + '.success', Actions.syncEmployeeActivity.toString() + '.failure']);
  if (responseAction.type === Actions.syncEmployeeActivity.toString() + '.success') {
    DAL.deleteEmployeeActivity();
  }
};

export const syncTransactionOnCheckoutSaga = function* (action) {
  const { onComplete, transactionId } = action.payload;
  const business = yield select(selectBusinessName);
  const storeId = yield select(selectStoreId);
  if (transactionId && business && storeId) {
    const record = DAL.getTransactionById(transactionId);
    if (!record) {
      logFailedDataSyncEvent({
        action: DataSyncAction.SyncTransactions,
        orderId: '',
        transactionId,
        reason: 'can not find the order',
      });
      safeCallback(onComplete, { result: false, message: 'can not find the order' });
      return false;
    }

    const location = yield call(generateLocation);
    const recordJSON = TransactionHelper.serializeTransaction(record);

    yield put(
      Actions.syncTransaction({
        business,
        storeId,
        lastRegisterId: record.lastRegisterId || record.registerId || '',
        recordJSON,
        location,
      })
    );

    const responseAction = yield take([Actions.syncTransaction.toString() + '.success', Actions.syncTransaction.toString() + '.failure']);

    const resp = responseAction.payload;
    if (responseAction.type === Actions.syncTransaction.toString() + '.success') {
      logSucceedDataSyncEvent({
        action: DataSyncAction.SyncTransactions,
        orderId: record.receiptNumber,
        transactionId: record.transactionId,
      });
      const now = new Date();
      console.log('resp = ', resp);
      const loyaltyInfo = get(resp, 'loyaltyInfo');
      // @ts-ignore
      const updateEntity: TransactionType = {
        uploadedDate: now.toISOString(),
        transactionId: record.transactionId,
      };
      let saveSuccess = false;
      if (loyaltyInfo) {
        updateEntity.loyaltyEarned = getNumberValue(loyaltyInfo, 'loyaltyEarned', 0.0);
        saveSuccess = DAL.updateTransaction(record.transactionId, updateEntity);
      } else {
        saveSuccess = DAL.updateTransaction(record.transactionId, updateEntity);
      }
      safeCallback(onComplete, { loyaltyInfo, result: true });
      // kds
      if (record.receiptNumber && record.servedTime && !record.servedTimeUploaded) {
        const servedTxList: SagaReturnType<typeof syncKdsServedTime> = yield call(syncKdsServedTime, record.receiptNumber);
        updateEntity.servedTimeUploaded = Boolean(get(servedTxList, '0.servedTimeUploaded'));
      }
      // mrs
      if (record.mrs && saveSuccess) {
        const plainTrx = JSONUtils.realmObjectToJSObject(record, 'syncTransactionOnCheckoutSaga');
        yield call(proposeSyncInfo, [
          {
            operationType: Actions.OperationType.UPDATE,
            updateSilence: true,
            transaction: { ...plainTrx, ...updateEntity },
          },
        ]);
      }
      return true;
    } else {
      const isNetConnected = yield select(selectIsNetConnected);
      // After checking out the order, the order will be uploaded in real time. In this case, the log is uploaded only when the Internet is connected
      if (isNetConnected) {
        const shiftId = get(record, 'shiftId');
        logFailedDataSyncEvent({
          action: DataSyncAction.SyncTransactions,
          orderId: get(recordJSON, 'receiptNumber'),
          transactionId: get(recordJSON, 'transactionId'),
          reason: resp.message,
          privateDataPayload: {
            shiftId,
            transactionJSON: JSON.stringify(recordJSON),
          },
        });
      }
      safeCallback(onComplete, { result: false, message: resp.message });
      return false;
    }
  } else {
    logFailedDataSyncEvent({
      action: DataSyncAction.SyncTransactions,
      orderId: '',
      transactionId,
      reason: 'wrong information',
      privateDataPayload: {
        extrasJSON: JSON.stringify({ transactionId, business, storeId }),
      },
    });
    safeCallback(onComplete, { result: false, message: 'error' });
    return false;
  }
};

export const syncAllTransactionsSaga = function* (action) {
  const { onComplete, isCheckingBeforeCloseZreading = false } = action.payload;
  const business = yield select(selectBusinessName);
  const storeId = yield select(selectStoreId);
  const registerObjectId = yield select(selectRegisterObjectId);
  if (!business || !storeId || !registerObjectId) {
    logFailedDataSyncEvent({
      action: DataSyncAction.SyncTransactions,
      orderId: '',
      transactionId: '',
      reason: 'wrong information',
      privateDataPayload: {
        extrasJSON: JSON.stringify({ business, storeId, registerObjectId }),
      },
    });
    safeCallback(onComplete, { result: false, message: 'error' });
    return false;
  }
  const recordList = DAL.getNotUploadedTransaction(registerObjectId);
  const location = yield call(generateLocation);
  const mrsProposalTransactions: Actions.ProposeDataType[] = [];
  const completePayload = { result: true, message: 'success' };
  let isNetConnected = true;
  let saveSuccess = false;
  for (const record of recordList) {
    const recordJSON: TransactionType = TransactionHelper.serializeTransaction(record);
    yield put(
      Actions.syncTransaction({
        business,
        storeId,
        lastRegisterId: record.lastRegisterId || record.registerId || '',
        recordJSON,
        location,
      })
    );

    const responseAction = yield take([Actions.syncTransaction.toString() + '.success', Actions.syncTransaction.toString() + '.failure']);

    const resp = responseAction.payload;
    if (responseAction.type === Actions.syncTransaction.toString() + '.success') {
      logSucceedDataSyncEvent({
        action: DataSyncAction.SyncTransactions,
        orderId: record.receiptNumber,
        transactionId: record.transactionId,
      });
      const now = new Date();
      const loyaltyInfo = get(resp, 'loyaltyInfo');
      // @ts-ignore
      const updateEntity: TransactionType = {
        uploadedDate: now.toISOString(),
        transactionId: record.transactionId,
      };
      if (loyaltyInfo) {
        updateEntity.loyaltyEarned = getNumberValue(loyaltyInfo, 'loyaltyEarned', 0.0);
        saveSuccess = DAL.updateTransaction(record.transactionId, updateEntity);
      } else {
        saveSuccess = DAL.updateTransaction(record.transactionId, updateEntity);
      }
      if (record.mrs && saveSuccess) {
        const plainTrx = JSONUtils.realmObjectToJSObject(record, 'syncAllTransactionsSaga');
        mrsProposalTransactions.push({
          operationType: Actions.OperationType.UPDATE,
          updateSilence: true,
          transaction: { ...plainTrx, ...updateEntity },
        });
      }
    } else {
      // ignore invalid orders that don't have the registerId
      if (recordJSON.registerId) {
        completePayload.result = false;
        completePayload.message = resp.message;
      }

      isNetConnected = yield select(selectIsNetConnected);
      // After checking out the order, the order will be uploaded in real time. In this case, the log is uploaded only when the Internet is connected
      if (isNetConnected) {
        const shiftId = get(record, 'shiftId');
        logFailedDataSyncEvent({
          action: DataSyncAction.SyncTransactions,
          orderId: get(recordJSON, 'receiptNumber'),
          transactionId: get(recordJSON, 'transactionId'),
          reason: resp.message,
          privateDataPayload: {
            shiftId,
            transactionJSON: JSON.stringify(recordJSON),
          },
        });
      } else {
        break;
      }
    }
  }
  // kds
  if (isNetConnected) {
    const servedTxList: SagaReturnType<typeof syncKdsServedTime> = yield call(syncKdsServedTime);
    if (servedTxList) {
      servedTxList.forEach(it => {
        const proposalTx = mrsProposalTransactions.find(m => m.transaction.transactionId === it.transactionId)?.transaction;
        if (proposalTx) {
          proposalTx.servedTimeUploaded = it.servedTimeUploaded;
        }
      });
    }
  }
  // mrs
  yield call(proposeSyncInfo, mrsProposalTransactions);

  // sync up the cannelled trx from BO
  yield call(syncCancelledTrxFromBO, isCheckingBeforeCloseZreading, completePayload, isNetConnected);

  safeCallback(onComplete, completePayload);
  return completePayload.result;
};

export const syncCancelledTrxFromBO = function* (isCheckingBeforeCloseZreading, completePayload, isNetConnected) {
  // 分页获取
  const storeId = yield select(selectStoreId);
  let pageNumber = 1;
  const PageSize = 200;
  // sync the transactionRecords Cancelled From BO
  const gbEnableVoidReceipt = yield select(selectGBEnableVoidReceipt);
  const birAccredited = yield select(selectBirAccredited);
  if (birAccredited && gbEnableVoidReceipt && !isCheckingBeforeCloseZreading && isNetConnected) {
    const lastTrxCancelledFromBOSyncTime = yield select(selectLastTrxCancelledFromBOSyncTime);
    const resParams: Actions.SyncTransactionCancelledFromBOType = {
      storeId,
      cancelledSources: [CancelledSource.BO, CancelledSource.PUBLIC_API],
      pageSize: PageSize,
    };
    if (!isEmpty(lastTrxCancelledFromBOSyncTime)) {
      resParams.cancelledAtFrom = new Date(lastTrxCancelledFromBOSyncTime).toISOString();
    }
    while (true) {
      resParams.pageNumber = pageNumber;
      yield put(Actions.syncTransactionCancelledFromBO(resParams));

      const responseAction = yield take([
        Actions.syncTransactionCancelledFromBO.toString() + '.success',
        Actions.syncTransactionCancelledFromBO.toString() + '.failure',
      ]);

      const resp = responseAction.payload;
      if (responseAction.type === Actions.syncTransactionCancelledFromBO.toString() + '.success') {
        const trxCannelledFromBOList = resp.asArray || [];
        // upload all the cancelled transaction to Local DB
        // upload the last sync time to Redux
        const latestModifiedTime = new Date().toISOString();
        yield put(
          Actions.setSyncInfo({
            lastTrxCancelledFromBOSyncTime: latestModifiedTime,
          })
        );
        for (const record of trxCannelledFromBOList) {
          const { transactionId, voidNumber, modifiedTime, cancelledAt, cancelledBy, returnReason, otherReason } = record;
          const originTrx = DAL.getTransactionById(transactionId);
          if (originTrx) {
            const modifiedDate = modifiedTime || originTrx.modifiedDate;
            const now = new Date();
            const uploadedDate = now.toISOString();
            const result = DAL.updateTransaction(transactionId, {
              voidNumber,
              isCancelled: true,
              modifiedDate,
              cancelledAt,
              cancelledBy,
              returnReason,
              otherReason,
              uploadedDate,
            });
            console.log('result = ', { result, transaction: record });
          }
        }

        if (trxCannelledFromBOList.length < PageSize) {
          break;
        } else {
          pageNumber++;
        }
      } else {
        completePayload.result = false;
        completePayload.message = resp.message;
        logFailedDataSyncEvent({
          action: DataSyncAction.SyncCannelledTransactions,
          reason: resp.message,
        });
        break;
      }
    }
  }
};

export const syncTransactionsSaga = function* (action) {
  const { transactionId } = action.payload;
  if (transactionId) {
    return yield call(syncTransactionOnCheckoutSaga, action);
  } else {
    return yield call(syncAllTransactionsSaga, action);
  }
};

const verifyShiftReport = (openTime, closeTime, shiftId, shiftReport) => {
  const transactionIdsByTime = DAL.getTransactionIdsByTime(openTime, closeTime);
  const transactionIdsByShiftId = DAL.getTransactionIdsByShiftId(shiftId);
  const totalAmountByPayment = shiftReport.totalSalesAmount;
  const totalAmountByTotal = DAL.getTotalAmountByTotalSince(openTime, closeTime);
  const transactionIdsByTimeJSON = JSON.stringify(transactionIdsByTime);
  const transactionIdsByShiftIdJSON = JSON.stringify(transactionIdsByShiftId);

  if (roundTo2DecimalPlaces(totalAmountByPayment) !== roundTo2DecimalPlaces(totalAmountByTotal)) {
    errorShiftReportEvent({
      action: ShiftReportAction.ShiftReportSalesAmountIncorrect,
      reason: 'Shift Report Sales Amount is Incorrect',
      shiftId,
      shiftReport: { ...shiftReport, totalAmountByTotal, totalAmountByPayment },
      transactionIdsByShiftIdJSON,
      transactionIdsByTimeJSON,
    });
  } else if (!isEqual(transactionIdsByTimeJSON, transactionIdsByShiftIdJSON)) {
    errorShiftReportEvent({
      action: ShiftReportAction.GenerateShiftReport,
      reason: 'Generated Shift Report is Incorrect',
      shiftId,
      shiftReport,
      transactionIdsByShiftIdJSON,
      transactionIdsByTimeJSON,
    });
  } else {
    infoShiftReportEvent({
      action: ShiftReportAction.GenerateShiftReport,
      reason: 'Generated Shift Report is correct',
      shiftId,
      shiftReport,
    });
  }
};

export const syncLastZReadingCloseTimeSaga = function* () {
  // get LastZReadingCloseTime
  const birAccredited = yield select(selectBirAccredited);
  if (birAccredited) {
    const oldLastZReadingCloseTime = yield select(selectLastZReadingCloseTime);
    yield put(Actions.getLastZReadingCloseTime());
    const responseAction = yield take([Actions.getLastZReadingCloseTime.toString() + '.success', Actions.getLastZReadingCloseTime.toString() + '.failure']);
    if (responseAction.type === Actions.getLastZReadingCloseTime.toString() + '.success') {
      const lastZReadingClosedTime = get(responseAction.payload, 'lastZReadingCloseTime', '');
      if (lastZReadingClosedTime) {
        yield put(Actions.setSyncInfo({ lastZReadingCloseTime: lastZReadingClosedTime }));
      }
      infoZReadingEvent({
        action: ZReadingAction.SyncLastZReadingCloseTime,
        privateDataPayload: { lastZReadingCloseTime: lastZReadingClosedTime, oldLastZReadingCloseTime },
      });
    } else {
      const resp = responseAction.payload;
      errorZReadingEvent({
        action: ZReadingAction.SyncLastZReadingCloseTime,
        reason: get(resp, 'message'),
        privateDataPayload: { oldLastZReadingCloseTime },
      });
    }
  }
};

export const initLocalCountryMapSaga = function* () {
  const existingCountryMap = yield select(selectLocalCountryMap);
  // console.log('existingCountryMap', JSON.stringify(existingCountryMap));
  if (!isEmptyObj(existingCountryMap)) {
    return;
  }

  const countryCode = yield select(selectCountry);
  // console.log('countryCode', countryCode);
  if (!countryCode) {
    return;
  }

  const matchedData = findCountryData(countryMap, countryCode);
  if (matchedData) {
    yield put(Actions.setLocalCountryMap(matchedData));
  }
};

const logForDefaultNetworkSaga = function* () {
  const defaultNetwork = (yield select(selectDefaultNetwork)).toJS();
  const currentNetwork = (yield select(selectCurrentNetworkInfo)).toJS();
  const errorInfo = isOnSameNetwork(currentNetwork, defaultNetwork);
  const { isSameNetwork, errorDes, errorGroupDes } = errorInfo;
  if (!isSameNetwork) {
    MixpanelManagerInstance.throttledTrack('Default Network Error', {
      'Error type': errorDes,
      'Error Group': errorGroupDes,
    });
  }
  return errorInfo;
};

const findCountryData = (countryMap, countryCode) => {
  return find(countryMap, country => country.cca2 === countryCode || country.cca3 === countryCode);
};

export const syncShiftsSaga = function* (action) {
  // get LastZReadingCloseTime
  yield put(Actions.syncLastZReadingCloseTime());
  yield put(Actions.syncEmployeeActivityBegin({}));
  const { onComplete } = action.payload;
  const storeInfo = yield select(selectStoreInfo);
  const settings = storeInfo.toJS();

  // Get Unsync shift data
  const result = DAL.getNotUploadedShifts();

  if (Boolean(result) && result.length > 0) {
    const enableCashback = yield select(selectEnableCashback);
    for (const shift of result) {
      const report = yield call(generateShiftReportSaga, shift);
      const formData = ShiftReport.convertToFormData(report, enableCashback);
      if (get(shift, 'version', 0) > 0) {
        const { openTime, closeTime, shiftId } = report;
        verifyShiftReport(openTime, closeTime, shiftId, formData);
      }
      // datafix for PS-5234
      const { closeBy, openBy } = formData;
      if (isEmpty(openBy) || isEmpty(closeBy)) {
        const employeeId = yield select(selectEmployeeId);
        if (isEmpty(openBy)) {
          formData.openBy = employeeId;
        }
        if (isEmpty(closeBy)) {
          formData.closeBy = employeeId;
        }
      }
      formData.registerId = settings.registerId;
      formData.storeId = settings.store._id;
      formData.registerObjectId = settings.registerObjectId;
      formData.enableCashback = enableCashback;
      // Uploading Shift data
      yield put(Actions.syncShiftReport(formData));
      const responseAction = yield take([Actions.syncShiftReport.toString() + '.success', Actions.syncShiftReport.toString() + '.failure']);
      const resp = responseAction.payload;
      const shiftId = get(shift, 'shiftId');
      if (responseAction.type === Actions.syncShiftReport.toString() + '.success') {
        logSucceedDataSyncEvent({ action: DataSyncAction.SyncShifts, privateDataPayload: { shiftId } });
        DAL.updateShift(shift.shiftId, { isUploaded: true });
      } else {
        // Sync fail
        logFailedDataSyncEvent({
          action: DataSyncAction.SyncShifts,
          reason: resp.message,
          privateDataPayload: { shiftId, formDataJSON: JSONUtils.stringify(formData) },
        });
        safeCallback(onComplete, { result: false, message: resp.message });
        return;
      }
    }
  }

  safeCallback(onComplete, { result: true });
};

const parseProductJsonSaga = function* (action) {
  const { onSyncProgress } = action.payload || ({} as any);
  const chunkSize = 1024 * 1024; // 16KB chunks
  let buffer = '';
  let position = 0;
  const filePath = RNFS.DocumentDirectoryPath + '/synced-products.json';

  console.log('parseLargeJsonArray', filePath);

  const time = Date.now();
  let index = 0;
  const marked = true;
  // try {
  const fileStat = yield RNFS.stat(filePath);
  const fileSize = fileStat.size;
  //
  // yield put(
  //   Actions.setSyncInfo({
  //     lastProductSyncFileSize: fileSize,
  //   })
  // );

  while (position < fileSize) {
    let time1 = Date.now();
    const chunk = yield RNFS.read(filePath, chunkSize, position, 'utf8');
    position += chunkSize;
    console.log('parseProductJson', chunkSize, chunk.length);

    buffer += chunk;

    let start = position === chunkSize ? 1 : 0; // skip first [
    let end = buffer.length;

    console.log('parseProductJson', `timestamp 0: ${Date.now() - time1}`);
    time1 = Date.now();
    while (start < end && (end = buffer.lastIndexOf('},{"_', end - 1)) !== -1) {
      try {
        // Parse the JSON object directly
        const objs = JSON.parse('[' + buffer.slice(start, end + 1) + ']');
        console.log('parseProductJson', `timestamp 1: ${Date.now() - time1}`);
        time1 = Date.now();
        DAL.saveProductInBatch(objs);
        console.log('parseProductJson', `PO: ${(index += objs.length)}`);
        console.log('parseProductJson', `timestamp 2: ${Date.now() - time1}`);
        time1 = Date.now();
      } catch (err) {
        console.error('parseProductJson', `Invalid JSON at index ${index}:`, buffer.slice(start, end + 1));
      }
      start = end + 2;
      index++;
    }

    buffer = buffer.slice(start);

    if ((position / chunkSize) % Math.floor(fileSize / chunkSize / 200) === 0) {
      console.log('parseProductJson', `progress: ${index} ${position}`);
      onSyncProgress && onSyncProgress(SyncType.SyncProductParsing, position / fileSize);
      yield true;
    }
  }

  if (buffer.trim()) {
    const finalObjectString = buffer.endsWith(']')
      ? buffer.slice(0, -1) // Remove the closing bracket
      : buffer;
    const jsonObject = JSON.parse(finalObjectString);

    DAL.saveProduct(jsonObject);
  }
  onSyncProgress && onSyncProgress(SyncType.SyncProductParsing, 1);
  const syncInfo: SyncInfoType = (yield select(selectSyncInfo)).toJS();

  yield put(
    Actions.setSyncInfo({
      lastProductSyncTime: syncInfo.lastProductSyncStartedTime,
      lastProductSyncStartedTime: null,
      lastProductSyncPosition: 0,
    })
  );
};

export const syncProductsToJsonByPageSaga = function* (action) {
  const { onSyncProgress } = action.payload || ({} as any);
  const business = yield select(selectBusinessName);
  const startTime = moment().toISOString();
  const config: NewProductSyncFlowType = yield select(selectNewProductSyncFlow);
  const syncInfo: SyncInfoType = (yield select(selectSyncInfo)).toJS();
  const isInitial = !syncInfo || !syncInfo.productInfoSync;

  // yield put(
  //   Actions.setSyncInfo({
  //     lastProductSyncStartedTime: startTime,
  //   })
  // );
  console.log('syncProductsToLocal isInitial', isInitial);
  try {
    const filePath = RNFS.DocumentDirectoryPath + '/synced-products.json';

    let page = 0;
    let downloaded = 0;
    let lastProductSyncTime = '';

    if (!isInitial) {
      lastProductSyncTime = syncInfo.lastProductSyncTime;
    }

    console.log('syncProductsToLocal lastProductSyncTime', lastProductSyncTime);
    onSyncProgress && onSyncProgress(SyncType.SyncProductPages, 0);

    while (true) {
      page++;
      console.log('syncProductsToLocal unlink', page);
      const exist: boolean = yield RNFS.exists(filePath);
      if (exist) {
        yield RNFS.unlink(filePath);
      }
      console.log('syncProductsToLocal download', page);
      const result: RNFS.DownloadResult = yield RNFS.downloadFile({
        toFile: filePath,
        fromUrl: `${API_URL()}/api/syncProducts?bn=${business}&isInitial=${isInitial}&page=${page}&limit=${config.pageSize}&timestamp=${lastProductSyncTime}`,
        headers: {
          'storehub-version': '2.51.1.0',
          'storehub-business': `${yield select(selectBusinessName)}`,
          'storehub-registerid': `${yield select(selectRegisterObjectId)}`,
          'storehub-token': `${yield select(selectAuthToken)}`,
        },
        readTimeout: 60000,
        // progress: ({ contentLength, bytesWritten }) => {
        // onSyncProgress && onSyncProgress(SyncType.SyncProductDownloadStream, bytesWritten);
        // },
        // progressInterval: 200,
      }).promise;
      console.log('syncProductsToLocal result', page);
      if (result.statusCode === 200) {
        // yield put(
        //   Actions.setSyncInfo({
        //     lastProductSyncStartedTime: startTime,
        //     lastProductSyncPosition: 0,
        //   })
        // );
        // const filePath = RNFS.DocumentDirectoryPath + '/synced-products.json';
        // const startTime1 = Date.now();
        const stat: StatResult = yield RNFS.stat(filePath);
        const chunk: string = yield RNFS.read(filePath, stat.size, 0, 'utf8');
        const products = chunk.length == 0 ? [] : JSON.parse(chunk);
        DAL.saveProductInBatch(products);
        downloaded += products.length;

        console.log('syncProductsToLocal saved', downloaded);

        if (products.length < config.pageSize) {
          console.log('syncProductsToLocal preparing thumbnails');
          yield put(
            Actions.setSyncInfo({
              productInfoSync: true,
              lastProductSyncTime: startTime,
            })
          );
          if (isInitial && config.asyncThumbnail != null) {
            if (config.asyncThumbnail) {
              console.log('syncProductsToLocal preparing thumbnails', 'INITIAL_CALL');
              yield call(syncLargeProductThumbnailsSaga, action);
            } else {
              console.log('syncProductsToLocal preparing thumbnails', 'INITIAL_PUT');
              yield put(syncLargeProductThumbnails());
            }
          }
          if (!isInitial && config.asyncThumbnailForSync != null) {
            if (config.asyncThumbnailForSync) {
              console.log('syncProductsToLocal preparing thumbnails', 'SYNC_CALL');
              yield call(syncLargeProductThumbnailsSaga, action);
            } else {
              console.log('syncProductsToLocal preparing thumbnails', 'SYNC_PUT');
              yield put(syncLargeProductThumbnails());
            }
          }
          onSyncProgress && onSyncProgress(SyncType.SyncProductFinished, 1);
          return true;
        }
        onSyncProgress && onSyncProgress(SyncType.SyncProductPages, downloaded);
      } else {
        onSyncProgress && onSyncProgress(SyncType.SyncProductFailed, result);
        return false;
      }
    }
  } catch (e) {
    console.error('syncProductsToLocal', e);
    onSyncProgress && onSyncProgress(SyncType.SyncProductFailed, e);
    return false;
  }
};

export const syncLargeProductsSaga = function* (action) {
  const { onComplete, isInitial } = action.payload || ({} as any);
  const business = yield select(selectBusinessName);

  if (!business) {
    safeCallback(onComplete, { result: false, message: 'Business is null' });
    return;
  }
  const syncInfo: SyncInfoType = (yield select(selectSyncInfo)).toJS();
  // prevent needToSyncAll call product sync
  if (isInitial && syncInfo && syncInfo.productInfoSync) {
    safeCallback(onComplete, { result: true });
    return;
  }
  const success: boolean = yield syncProductsToJsonByPageSaga(action);
  safeCallback(onComplete, { result: success });

  if (!syncInfo || !syncInfo.productInfoSync) {
    yield put(
      Actions.setSyncInfo({
        productInfoSync: success,
      })
    );
  }
};

export const syncProductsSaga = function* (action) {
  // isInitial will let server filter the deleted products.
  const { onComplete, onSyncProgress, isInitial } = action.payload || ({} as any);
  const business = yield select(selectBusinessName);

  if (!business) {
    safeCallback(onComplete, { result: false, message: 'Business is null' });
    return;
  }

  let allDone = true;
  let errorMessage = '';
  let syncTime = '';
  let page = 1;
  let hasNewProducts = false;
  const syncInfo = yield select(selectSyncInfo);
  // need to check needToSyncAll, if true use empty string as syncTime to make sure the response data start from the earliest one.
  if (syncInfo !== undefined && !syncInfo.get('needToSyncAll')) {
    const syncData = syncInfo.toJS();
    syncTime = syncData.lastProductSyncTime;
  }
  let latestModifiedTime = syncTime;
  while (true) {
    yield put(Actions.syncProducts({ business, syncTime, isInitial, page, limit: SYNC_PRODUCTS_LIMIT }));
    const responseAction = yield take([Actions.syncProducts.toString() + '.success', Actions.syncProducts.toString() + '.failure']);
    const resp = responseAction.payload;
    if (responseAction.type === Actions.syncProducts.toString() + '.success') {
      const products = resp.asArray;
      const length = get(products, 'length', 0);
      hasNewProducts = hasNewProducts || length > 0;
      yield call(setSyncProductsSaga, products, syncTime, business);
      latestModifiedTime = getLatestModifiedTime(syncTime, products);
      // if current fetched products length is less than or bigger the given limit, it's supposed to reach the last page. Or limit doesn't work properly
      if (length < SYNC_PRODUCTS_LIMIT) {
        onSyncProgress && onSyncProgress(SyncType.SyncProduct, -1);
        yield put(
          Actions.setSyncInfo({
            lastProductSyncTime: latestModifiedTime,
          })
        );
        break;
      }
      onSyncProgress && onSyncProgress(SyncType.SyncProduct, page);
      page += 1; // Increase the page to fetch next page.
    } else {
      allDone = false;
      errorMessage = resp.message;
      break;
    }
  }

  if (allDone) {
    safeCallback(onComplete, { result: true });
    logSucceedDataSyncEvent({ action: DataSyncAction.SyncProducts });
    yield put(
      Actions.setSyncInfo({
        productInfoSync: true, // it's for the sign in flow, make sure all the products is synced before go to Register
      })
    );
  } else {
    // Sync fail
    logFailedDataSyncEvent({ action: DataSyncAction.SyncProducts, reason: errorMessage });
    safeCallback(onComplete, { result: false, message: errorMessage });
  }

  if (hasNewProducts) {
    eventBus.emit(PRODUCTS_UPDATED);
  }
};

/**
 * 1. save products in db
 * 2. download images
 * @param payload products need to be stored
 */
export const setSyncProductsSaga = function* (payload, date, storeName) {
  let data = [];
  let latestModifiedTime = date;
  const enableTrackDownloadThumbnail = yield select(selectGBEnableTrackDownloadThumbnail);
  if (Array.isArray(payload)) {
    latestModifiedTime = getLatestModifiedTime(latestModifiedTime, payload);
    if (latestModifiedTime === date) {
      Boolean(enableTrackDownloadThumbnail) &&
        logProductImageSyncEvent({
          privateDataPayload: {
            action: 'NoNewProducts',
            storeName: storeName,
            date: date,
            latestModifiedTime: latestModifiedTime,
          },
        });
      return;
    } else {
      Boolean(enableTrackDownloadThumbnail) &&
        logProductImageSyncEvent({
          privateDataPayload: {
            action: 'NewProducts',
            storeName: storeName,
            date: date,
            latestModifiedTime: latestModifiedTime,
          },
        });
    }
    data = payload;
    yield call(updateProductImage, data, storeName);
    DAL.saveProductInBatch(data);
  }
};

export const updateProductImage = function* (products, storeName) {
  const enableTrackDownloadThumbnail = yield select(selectGBEnableTrackDownloadThumbnail);
  const needToDownloadImages = [];
  for (const product of products) {
    if (product.hasThumbnail) {
      const dbProduct = DAL.getProductById(product._id);
      if (Boolean(dbProduct)) {
        const lastUpdateThumbnail = dbProduct.lastUpdateThumbnail;
        if (Boolean(lastUpdateThumbnail) && moment(lastUpdateThumbnail).isSameOrAfter(product.lastUpdateThumbnail)) {
          Boolean(enableTrackDownloadThumbnail) &&
            logProductImageSyncEvent({
              privateDataPayload: {
                action: 'NoNeedToDownloadImages',
                productId: product._id,
                lastUpdateThumbnail: lastUpdateThumbnail,
                productLastUpdateThumbnail: product.lastUpdateThumbnail,
              },
            });
          continue;
        }
      }
      needToDownloadImages.push(product._id);
    } else {
      try {
        Boolean(enableTrackDownloadThumbnail) &&
          logProductImageSyncEvent({
            privateDataPayload: {
              action: 'NoThumbnail',
              productId: product._id,
            },
          });
        yield call(deleteThumbnailPromise, product._id);
      } catch (error) {
        console.log('delete thumbnail error');
      }
    }
  }
  if (needToDownloadImages.length > 0) {
    Boolean(enableTrackDownloadThumbnail) &&
      logProductImageSyncEvent({
        privateDataPayload: {
          action: 'NeedToDownloadImages',
          needToDownloadImages: needToDownloadImages,
        },
      });
    for (let i = 0; i < needToDownloadImages.length; i++) {
      const id = needToDownloadImages[i];
      // Android will deliver all the download tasks to a thread pool, for the CF-1395 now don't wait for the image downloading is done.
      yield call(s3DownloadThumbnail, storeName, id);
    }
  }
};

export const syncQuickSelectLayoutSaga = function* (action) {
  const { onComplete, onSyncProgress } = action.payload || ({} as any);
  const storeInfo = yield select(selectStoreInfoUnsafe);
  if (storeInfo === undefined) return;
  const store = storeInfo.toJS();

  yield put(
    Actions.syncQuickSelectLayout({
      business: store.name,
      registerId: store.registerObjectId,
    })
  );

  const responseAction = yield take([Actions.syncQuickSelectLayout.toString() + '.success', Actions.syncQuickSelectLayout.toString() + '.failure']);
  const resp = responseAction.payload;
  if (responseAction.type === Actions.syncQuickSelectLayout.toString() + '.success') {
    yield put(Actions.setQuickLayout(resp.categories));
    yield put(Actions.setShareQuickLayoutStores(resp.usedInStores));
    yield put(Actions.setQuickLayoutId(resp.quickSelectLayoutId));
    logSucceedDataSyncEvent({ action: DataSyncAction.SyncQuickSelectLayout });
    safeCallback(onComplete, { result: true });
    onSyncProgress && onSyncProgress(SyncType.SyncQuickSelectLayout);
  } else {
    // Sync fail
    logFailedDataSyncEvent({ action: DataSyncAction.SyncQuickSelectLayout, reason: resp.message });
    safeCallback(onComplete, { result: false, message: resp.message });
    onSyncProgress && onSyncProgress(SyncType.SyncQuickSelectLayout);
  }
};

export const syncTableLayoutSaga = function* (action) {
  const { onComplete } = action.payload || ({} as any);
  const storeInfo = yield select(selectStoreInfoUnsafe);
  if (storeInfo === undefined) return;
  const store = storeInfo.toJS();

  yield put(
    Actions.getTableLayout({
      storeId: store.store._id,
      businessName: store.name,
    })
  );

  const responseAction = yield take([Actions.getTableLayout.toString() + '.success', Actions.getTableLayout.toString() + '.failure']);
  const resp = responseAction.payload;
  if (responseAction.type === Actions.getTableLayout.toString() + '.success') {
    const sections = get(resp, ['tableLayout', 'sections']);
    yield put(Actions.saveTableLayoutSections({ sections }));
    logSucceedDataSyncEvent({ action: DataSyncAction.SyncTableLayout });
    safeCallback(onComplete, { result: true });
  } else {
    // Sync fail
    logFailedDataSyncEvent({ action: DataSyncAction.SyncTableLayout, reason: resp.message });
    safeCallback(onComplete, { result: false, message: resp.message });
  }
};

export const onCheckPrinters = (result: CheckPrinterAssignedResult) => {
  if (!result.isAllAssigned) {
    openPrintAssignedToast(t('Kitchen Station not assigned'));
  }
  if (result.offlinePrinters?.length > 0) {
    openPrinterOfflineToast(t('{{count}} printer(s) offline', { count: result.offlinePrinters.length }));
  }
};

export const syncPrinterSetting = function* (settings) {
  // update tag setting
  const kitchenPrinters = getUnNullValue(settings, 'kitchenPrinters', '');
  const kitchenTagsOnBO = kitchenPrinters.split(',').filter(Boolean);
  const printerSettings = (yield select(selectPrinterTagsSettings)).toJS() as PrinterConfigType[];
  printerSettings.forEach(config => {
    config.tags = intersection(config.tags || [], kitchenTagsOnBO);
  });
  uploadPrinters(printerSettings);
  trackLogEvent({
    action: PrinterAction.RefreshPrinter,
    level: LoggingLevel.Info,
    privateDataPayload: {
      kitchenPrinters,
      source: 'syncPrinterSetting',
    },
  });
  yield put(Actions.updatePrinterTagsSettings(printerSettings));
  yield put(Actions.checkAssignedPrinter({ onComplete: onCheckPrinters }));
};

export const syncStoreInfoSaga = function* (action) {
  const { onComplete, onSyncProgress } = action.payload;
  const storeInfo = yield select(selectStoreInfoUnsafe);
  if (storeInfo === undefined) return;
  const store = storeInfo.toJS();
  yield put(
    Actions.syncStoreInfo({
      business: store.name,
      registerId: store.registerObjectId,
      includeAllStores: true,
    })
  );

  const responseAction = yield take([Actions.syncStoreInfo.toString() + '.success', Actions.syncStoreInfo.toString() + '.failure']);
  const resp = responseAction.payload;
  if (responseAction.type === Actions.syncStoreInfo.toString() + '.success') {
    // The createdTime has been provide at activate API and store under StoreInfo node,
    // there is no need to save the duplciated createdTime under store, hence remove the
    // createdTime from remain here.
    const { allStores, registerName = '', createdTime, ...remain } = resp;
    const enablePayByCash = get(remain, 'enablePayByCash', false);
    const enablePayLater = get(remain, 'enablePayLater', false);
    console.log('resp ======>>>>>> ', resp);
    if (enablePayByCash || enablePayLater) {
      yield put(Actions.updateGeneralSettings({ shouldLoadOnlineOpenOrders: true }));
    }
    yield put(Actions.setStoreInfo({ allStores, store: remain, registerName }));
    yield call(syncPrinterSetting, remain);
    yield fork(tryToPushStoreInfoToKDSSaga);

    logSucceedDataSyncEvent({ action: DataSyncAction.SyncStoreInfo });
    safeCallback(onComplete, { result: true, tableLayoutEnabled: remain.tableLayoutEnabled });
    onSyncProgress && onSyncProgress(SyncType.SyncStoreInfo);
  } else {
    // Sync fail
    logFailedDataSyncEvent({ action: DataSyncAction.SyncStoreInfo, reason: resp.message });
    safeCallback(onComplete, { result: false, message: resp.message });
    onSyncProgress && onSyncProgress(SyncType.SyncStoreInfo);
  }
};

export const syncPriceBookSaga = function* (action) {
  const { onComplete, onSyncProgress } = action.payload || ({} as any);
  let syncTime = '';
  const syncInfo = yield select(selectSyncInfo);
  if (syncInfo !== undefined && !syncInfo.get('needToSyncAll')) {
    const syncData = syncInfo.toJS();
    syncTime = syncData.lastPriceBookSyncTime;
  }

  yield put(
    Actions.syncPriceBook({
      syncTime,
    })
  );

  const responseAction = yield take([Actions.syncPriceBook.toString() + '.success', Actions.syncPriceBook.toString() + '.failure']);
  const resp = responseAction.payload;
  if (responseAction.type === Actions.syncPriceBook.toString() + '.success') {
    yield call(setSyncPriceBookSaga, resp.asArray, syncTime);
    logSucceedDataSyncEvent({ action: DataSyncAction.SyncPriceBook });
    safeCallback(onComplete, { result: true });
    onSyncProgress && onSyncProgress(SyncType.SyncPriceBook);
  } else {
    // Sync fail
    logFailedDataSyncEvent({ action: DataSyncAction.SyncPriceBook, reason: resp.message });
    safeCallback(onComplete, { result: false, message: resp.message });
    onSyncProgress && onSyncProgress(SyncType.SyncPriceBook);
  }
};

export const setSyncPriceBookSaga = function* (payload, date) {
  let data = [];
  let latestModifiedTime = date;
  if (Array.isArray(payload)) {
    data = payload;
    latestModifiedTime = getLatestModifiedTime(latestModifiedTime, payload);
  }

  DAL.saveSyncPriceBooks(data);

  yield put(
    Actions.setSyncInfo({
      priceBookInfoSync: true,
      lastPriceBookSyncTime: latestModifiedTime,
    })
  );
};

export const syncPromotionsSaga = function* (action) {
  const { onComplete } = action.payload || ({} as any);
  let syncTime = '';
  const syncInfo = yield select(selectSyncInfo);
  if (syncInfo !== undefined) {
    const syncData = syncInfo.toJS();
    syncTime = syncData.lastPromotionSyncTime;
  }

  yield put(syncPromotions({ syncTime }));

  const responseAction = yield take([syncPromotions.toString() + '.success', syncPromotions.toString() + '.failure']);
  const resp = responseAction.payload;
  if (responseAction.type === syncPromotions.toString() + '.success') {
    yield call(setPromotionsSaga, resp.asArray, syncTime);
    logSucceedDataSyncEvent({ action: DataSyncAction.SyncPromotions });
    safeCallback(onComplete, { result: true });
    DaoCache.clearCache('promotionsDao'); // Clear dao cache.
  } else {
    logFailedDataSyncEvent({ action: DataSyncAction.SyncPromotions, reason: resp.message });
    safeCallback(onComplete, { result: false, message: resp.message });
  }
};

export const setPromotionsSaga = function* (payload, date) {
  let data = [];
  let lastestModifiedTime = date;
  if (Array.isArray(payload)) {
    data = payload;
    lastestModifiedTime = getLatestModifiedTime(lastestModifiedTime, payload);
  }

  DAL.saveSyncPromotions(data);

  yield put(Actions.setSyncInfo({ promotionInfoSync: true, lastPromotionSyncTime: lastestModifiedTime }));
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const syncActivationStatusSaga = function* (action) {
  const onSuccess = get(action, ['payload', 'onSuccess']);
  const location = yield call(generateLocation);
  const locationStr = Boolean(location) ? JSON.stringify(location) : undefined; // api.js will delete that urlparam with undefined value
  const device = yield select(selectDevice);
  const deviceStr = Boolean(device) ? JSON.stringify(device.toJS()) : undefined;
  const businessName = yield select(selectBusinessName);
  const authToken = yield select(selectAuthToken);
  // Since checkActivationStatus is added to the Initial file, return is required to prevent this from being called in the Deactivate state
  if (!Boolean(businessName) || !Boolean(authToken)) return;
  yield put(Actions.getActivationStatus({ location: locationStr, device: deviceStr }));

  const responseAction = yield take([Actions.getActivationStatus.toString() + '.success', Actions.getActivationStatus.toString() + '.failure']);
  const resp = responseAction.payload;

  if (responseAction.type === Actions.getActivationStatus.toString() + '.success') {
    const { subscriptionStatus, isActivated, subscriptionPlan } = resp;
    logSucceedDataSyncEvent({
      action: DataSyncAction.SyncActivationStatus,
      privateDataPayload: { subscriptionStatus, isActivated, subscriptionPlan },
    });
    if (!isActivated) {
      // Deactivate
      yield call(forceDeactivation);
    } else if (subscriptionStatus === 'Expired') {
      // show up lock screen
      yield put(Actions.updateAccountSettings({ expired: true, offline: false }));
      yield put(Actions.signOut({ event: 'subscriptionExpired' })); // Clear employee to jump to lock screen
    } else {
      yield put(Actions.setFreeTrial(subscriptionStatus === SubscriptionStatus.Trial));
      // update last online date
      const now = new Date();
      yield put(
        Actions.setSyncInfo({
          onlineSyncTime: now.toISOString(),
        })
      );
      onSuccess && onSuccess.callback();
      yield put(Actions.setSubscriptionPlan(subscriptionPlan));
      yield put(Actions.updateAccountSettings({ expired: false, offline: false }));
    }
  } else {
    // Sync fail, checking max 30 days unsync
    const onlineSyncTime = yield select(selectOnlineSyncTime);
    logFailedDataSyncEvent({
      action: DataSyncAction.SyncActivationStatus,
      reason: resp.message,
      privateDataPayload: { onlineSyncTime },
    });
    if (onlineSyncTime !== undefined) {
      const lockTime = moment(onlineSyncTime).add(30, 'days');
      const currentTime = moment();
      if (currentTime.diff(lockTime, 'days') > 0) {
        // show up lock screen
        yield put(Actions.updateAccountSettings({ expired: false, offline: true }));
        yield put(Actions.signOut({ event: 'lockScreen' })); // Clear employee to jump to lock screen
      }
    }
  }
};

export const forceDeactivation = function* () {
  yield call(cleanData);
  // deactive - intercom logout
  Intercom.logout();
  closeTopNotification();
  yield call(initializeDeviceAfterDeactivationSaga);
};

export const cleanData = function* () {
  infoPOSBasicEvent({ action: POSBasicAction.DEACTIVATE });
  yield put(Actions.clearStoreInfo());
  yield put(Actions.clearSetting());
  yield put(Actions.clearLocalCountryMap());
  // mrs
  yield put(Actions.clearMRSState({}));
  yield put(Actions.unRegisterMrsAction());
  yield put(Actions.unregisterWebsocket());
  yield put(Actions.cleanKdsSetting());
  yield put(Actions.cleanNcsSetting());
  yield put(resetCfd());
  yield put(Actions.clearEmployee());
  yield put(Actions.clearEWalletPay());
  yield put(Actions.setShiftStatus(false));
  yield put(Actions.updateGrowthBook());
  yield put(stopCheckCustomerInfoFromQR({}));
  yield put(Actions.resetAWSConfig());
  DAL.clearDB();
  yield call([LocalLogger, LocalLogger.updateLogModel], {
    employeeId: '',
    storeId: '',
    business: '',
    registerId: '',
    registerNumber: -1,
  });
};

export const setStoreInfoSaga = function* (action) {
  if (!action.payload) {
    return;
  }
  const logo = get(action.payload, ['store', 'logo'], []);
  const paymentOptions = get(action.payload, ['store', 'paymentOptions']);
  const storeId = get(action.payload, ['store', '_id']);
  // const lastZReadingClosedTime = get(action.payload, 'lastZReadingCloseTime', '');
  // if (!isEmpty(lastZReadingClosedTime)) {
  //   yield put(Actions.setSyncInfo({ lastZReadingCloseTime: lastZReadingClosedTime }));
  // }
  PaymentOptions.updatePaymentOptions(paymentOptions, storeId);
  yield PrinterManager.setStoreInfo({ logo });
  yield put(Actions.setCfdConfigurationBegin({}));
};

const initializeDeviceAfterDeactivationSaga = function* () {
  yield call(initialDevice, {});

  // Other initialization
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const syncSequentialStartInfoSaga = function* (action) {
  const immutableStoreInfo = yield select(selectStoreInfo);
  const sequentialReceiptNumber = immutableStoreInfo.get('sequentialReceiptNumber');
  if (!sequentialReceiptNumber) {
    yield put(Actions.setSyncInfo({ needFixsequential: false }));
    return;
  }

  const immutableSequence = yield select(selectSequence);
  const { receiptNumberStart, receiptDateStart }: Actions.SetSequenceType = immutableSequence.toJS();

  const transactions = DAL.getTransactionsWithEmptyReceiptNumber();
  const hasEmptyReceiptNumberTranasaction = transactions && transactions.length > 0;
  if (receiptNumberStart && receiptDateStart && !hasEmptyReceiptNumberTranasaction) {
    yield put(Actions.setSyncInfo({ needFixsequential: false }));
    return;
  }

  const registerSeq = immutableStoreInfo.get('registerId') - 1;
  const registerObjectId = immutableStoreInfo.get('registerObjectId');
  const businessName = immutableStoreInfo.get('name');

  // Need to sync sequential info
  yield put(Actions.syncSequentialStartInfo({ businessName, registerObjectId }));
  const responseAction = yield take([Actions.syncSequentialStartInfo.toString() + '.success', Actions.syncSequentialStartInfo.toString() + '.failure']);
  const resp = responseAction.payload;
  if (responseAction.type === Actions.syncSequentialStartInfo.toString() + '.success') {
    const { receiptNumberStart: newReceiptNumberStart, receiptDateStart: newReceiptDateStart } = resp;
    logSucceedDataSyncEvent({
      action: DataSyncAction.SyncSequentialStartInfo,
      privateDataPayload: { receiptNumberStart, receiptDateStart, newReceiptNumberStart, newReceiptDateStart },
    });
    if (newReceiptNumberStart && newReceiptDateStart) {
      const usePreviousNumberStart = Number(receiptNumberStart) > Number(newReceiptNumberStart);
      let _seqStart = usePreviousNumberStart ? Number(receiptNumberStart) : Number(newReceiptNumberStart);
      let _seqDate = usePreviousNumberStart ? receiptDateStart : newReceiptDateStart;
      if (transactions && transactions.length > 0) {
        for (const record of transactions) {
          const now = new Date();

          const receiptNumber = String(registerSeq).padStart(3, '0') + String(_seqStart).padStart(8, '0');
          const sequenceNumber = _seqStart;
          const modifiedDate = now.toISOString();
          DAL.updateTransaction(record.transactionId, {
            receiptNumber,
            sequenceNumber,
            modifiedDate,
          });
          _seqStart += 1;
          _seqDate = now.toISOString();
          yield call(
            updateSequenceSaga,
            updateSequence({
              receiptNumberStart: _seqStart,
              receiptDateStart: _seqDate,
              isStart: true,
              from: 'syncSequentialStartInfo1',
            })
          );
          yield delay(10);
        }

        yield put(
          Actions.syncTransactionsAction({
            onComplete: {},
          })
        );
      } else {
        yield call(
          updateSequenceSaga,
          updateSequence({
            receiptNumberStart: _seqStart,
            receiptDateStart: _seqDate,
            isStart: true,
            from: 'syncSequentialStartInfo2',
          })
        );
      }
      yield put(Actions.setSyncInfo({ needFixsequential: false }));
    }
  } else {
    logFailedDataSyncEvent({ action: DataSyncAction.SyncSequentialStartInfo, reason: resp.message });
  }

  yield put(Actions.setSyncInfo({ needFixsequential: false }));
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const migrateTableLayoutSaga = function* (action) {
  const { onComplete } = action.payload || ({} as any);
  const alreadyMigrateTableLayout = yield select(selectAlreadyMigrateTableLayout);
  if (!alreadyMigrateTableLayout) {
    const businessName = yield select(selectBusinessName);
    const storeId = yield select(selectStoreId);

    yield put(
      Actions.migrateTableLayoutHttpAction({
        businessName,
        storeId,
      })
    );
    const responseAction = yield take([
      Actions.migrateTableLayoutHttpAction.toString() + '.success',
      Actions.migrateTableLayoutHttpAction.toString() + '.failure',
    ]);
    const resp = responseAction.payload;
    console.log(resp);
    if (responseAction.type === Actions.migrateTableLayoutHttpAction.toString() + '.success') {
      const migrateIosNativeTableLayout = get(resp, 'migrateIosNativeTableLayout', '');
      if (migrateIosNativeTableLayout === 'SUCCESS') {
        yield put(
          Actions.updateTableLayoutSettings({
            alreadyMigrateTableLayout: true,
            needShowEditTableLayoutToast: true,
            needShowNewTableLayoutTag: true,
            needShowMigrateSuccessToast: true,
          })
        );
        yield put(Actions.syncTableLayoutAction({ onComplete }));
      } else if (migrateIosNativeTableLayout === 'NO_NEED') {
        yield put(
          Actions.updateTableLayoutSettings({
            alreadyMigrateTableLayout: true,
            needShowEditTableLayoutToast: false,
          })
        );
        yield put(Actions.syncTableLayoutAction({ onComplete }));
      }
    }
  }
};

export const readFaceCaptureIntroSaga = function* () {
  yield put(Actions.setHaveUsedFaceCapture(true));
};

export const trackLocalSettingsSaga = function* () {
  const record = {
    enableCloseLastShiftNotification: yield select(selectCloseLastShiftNotification), // Close Last Shift Notification
    enableOpenOrders: yield select(selectEnableOpenOrders), // Enable Open Order
    enableOpenOrdersShortCut: yield select(selectEnableOpenOrdersShortCut), // Add Shortcut Button To checkout
    enableCustomerShortCut: yield select(selectEnableCustomerShortCut), // Add Customer Shortcut To Orders
    enableCustomerQR: yield select(selectEnableCustomerQR), // QR Display for Loyalty Redemption
    enableAlwaysPrintReceipt: yield select(selectAlwaysPrintReceipt), // Always Print Receipt
    enableCustomerDisplay: yield select(selectEnableCustomerDisplay), // Enable Customer Display
    enableOutOfStockSales: yield select(selectAllowOutOfStock), // Allow Out of Stock Sales

    autoSignOutTimeout: yield select(selectAutoSignOutCount), // Auto sign-out - Never / 30s / 1m / 2m / 5m / 10m
    enableKitchenDocketMultipleLineLayout: yield select(selectKitchenDocketVariantIsMultipleLine), // Kitchen Docket Variant Layout - Single Line / Multiple Line
    defaultNetworkType: yield select(selectDefaultNetworkType), // Enable Customer Display
    currentNetworkType: yield select(selectCurrentNetworkType), // Enable Customer Display
    cfdType: (yield select(selectEnableCustomerDisplay)) ? (IsIOS ? 'oldApp' : 'buildIn') : 'disconnected',
    receiptFontSize: getFontSizeDescriptionForMetabase(yield select(selectReceiptFontSize)),
    kitchenDocketFontSize: getFontSizeDescriptionForMetabase(yield select(selectKitchenDocketFontSize)),
    deviceModel: yield select(selectDeviceModel),
    isCfdPaired: (yield select(selectIsCfdPaired)) ? 'Paired' : 'NotPaired',
  };

  // console.log('trackLocalSettingsSaga' + JSON.stringify(record));

  yield put(
    Actions.syncLocalSettings({
      settings: Object.entries(record).map(([key, value]) => {
        return { key, value: value === true ? 'On' : value === false ? 'Off' : String(value) };
      }),
    })
  );
  const responseAction = yield take([Actions.syncLocalSettings.toString() + '.success', Actions.syncLocalSettings.toString() + '.failure']);
  // console.log('trackLocalSettingsSaga' + JSON.stringify(responseAction.payload));
};

const roundMemoryToNearestGB = sizeMB => {
  const gbSizes = [0, 1, 2, 3, 4, 6, 8, 12, 16, 32, 64, 128];
  const sizeGB = sizeMB / 1024; // Convert MB to GB

  const closest = gbSizes.reduce((prev, curr) => (Math.abs(curr - sizeGB) < Math.abs(prev - sizeGB) ? curr : prev));

  return closest + 'GB';
};

const roundStorageToNearestGB = sizeMB => {
  const gbSizes = [0, 1, 2, 4, 8, 16, 32, 64, 128, 256, 512, 1024, 2048, 4096, 8192];
  const sizeGB = sizeMB / 1024; // Convert MB to GB

  const closest = gbSizes.reduce((prev, curr) => (Math.abs(curr - sizeGB) < Math.abs(prev - sizeGB) ? curr : prev));

  return closest + 'GB';
};

export const trackLocalStorageSaga = function* () {
  const enableStorageStatistics = yield select(selectGBEnableStorageStatistics);
  const getTransactionsSize = () => {
    try {
      return RealmManager.getRealmInstance().objects<TransactionType>('Transaction').length;
    } catch (exception) {
      return 0;
    }
  };
  const getKdsTransactionsSize = () => {
    try {
      return RealmManager.getRealmInstance()
        .objects<TransactionType>('Transaction')
        .filtered(
          "createdDate != null && modifiedDate != null && transactionType == '${TransactionFlowType.Sale}' && cookingStatus != null && pushKdsDate != null"
        ).length;
    } catch (exception) {
      return 0;
    }
  };
  const getSalesTransactionsSize = () => {
    try {
      return RealmManager.getRealmInstance().objects<TransactionType>('Transaction').filtered('transactionType == "Sale"').length;
    } catch (exception) {
      return 0;
    }
  };
  const getEmployeeActivitySize = () => {
    try {
      return RealmManager.getRealmInstance().objects<EmployeeActivityType>('EmployeeActivity').length;
    } catch (exception) {
      return 0;
    }
  };
  const getShiftSize = () => {
    try {
      return RealmManager.getRealmInstance().objects<ShiftType>('Shift').length;
    } catch (exception) {
      return 0;
    }
  };
  const getTransactionLogSize = () => {
    try {
      return RealmManager.getRealmInstance().objects<TransactionsLogType>('TransactionsLog').length;
    } catch (exception) {
      return 0;
    }
  };

  const getBeepNotificationSize = () => {
    try {
      return RealmManager.getRealmInstance().objects<BeepNotificationType>('BeepNotification').length;
    } catch (exception) {
      return 0;
    }
  };

  const getSubOrderNotificationSize = () => {
    try {
      return RealmManager.getRealmInstance().objects<SubOrderNotificationType>('SubOrderNotification').length;
    } catch (exception) {
      return 0;
    }
  };
  if (enableStorageStatistics && IsAndroid) {
    try {
      const documentPath = RNFS.DocumentDirectoryPath;
      if (documentPath.endsWith('/files')) {
        const rootPath = documentPath.substring(0, documentPath.length - 6);
        const externalCachesDirectoryPath = RNFS.ExternalCachesDirectoryPath;
        const [rootDir, rootDirTotal] = yield call(getStorageDistribution, rootPath);
        const [documentsDir, documentsDirTotal] = yield call(getStorageDistribution, RNFS.DocumentDirectoryPath);
        const [logsDir, logsDirTotal] = yield call(getStorageDistribution, externalCachesDirectoryPath + '/logs');
        const [databaseDir, databaseDirTotal] = yield call(getStorageDistribution, rootPath + '/databases');
        const [appWebviewDir, appWebviewDirTotal] = yield call(getStorageDistribution, rootPath + '/app_webview');
        const [cacheDir, cacheDirTotal] = yield call(getStorageDistribution, rootPath + '/cache');
        const fsInfo = yield call(RNFS.getFSInfo);
        logLocalStorageEventInfo({
          action: LocalStorageAction.Track,
          privateDataPayload: {
            rootDir: JSON.stringify(rootDir),
            rootDirTotalInMB: parseFloat(rootDirTotal),
            documentsDir: JSON.stringify(documentsDir),
            documentsDirTotalInMB: parseFloat(documentsDirTotal),
            databaseDir: JSON.stringify(databaseDir),
            databaseDirTotalInMB: parseFloat(databaseDirTotal),
            appWebviewDir: JSON.stringify(appWebviewDir),
            appWebviewDirTotalInMB: parseFloat(appWebviewDirTotal),
            cacheDir: JSON.stringify(cacheDir),
            cacheDirTotalInMB: parseFloat(cacheDirTotal),
            logsDir: JSON.stringify(logsDir),
            logsDirTotalInMB: parseFloat(logsDirTotal),
            realmTotalInMB: parseFloat(documentsDir['default.realm']),
            freeTotalInMB: parseFloat((fsInfo.freeSpace / 1024 / 1024).toFixed(2)),
            realmLocalTxnSize: getTransactionsSize(),
            externalCachesDirectoryPath,
            // realmSalesTxnSize: getSalesTransactionsSize(),
            // realmKdsTxnSize: getKdsTransactionsSize(),
            realmEmployeeActivitySize: getEmployeeActivitySize(),
            realmShiftSize: getShiftSize(),
            realmTxnLogSize: getTransactionLogSize(),
            realmBeepNotificationSize: getBeepNotificationSize(),
            realmSubOrderNotification: getSubOrderNotificationSize(),
            totalMemory: (yield DeviceInfo.getTotalMemory()) / (1024 * 1024),
            maxMemory: (yield DeviceInfo.getMaxMemory()) / (1024 * 1024),
            usedMemory: (yield DeviceInfo.getUsedMemory()) / (1024 * 1024),
            totalDiskCapacity: (yield DeviceInfo.getTotalDiskCapacity()) / (1024 * 1024),
            freeDiskCapacity: (yield DeviceInfo.getFreeDiskStorage()) / (1024 * 1024),
            exactRam: roundMemoryToNearestGB((yield DeviceInfo.getTotalDiskCapacity()) / (1024 * 1024)),
            exactRom: roundStorageToNearestGB((yield DeviceInfo.getFreeDiskStorage()) / (1024 * 1024)),
          },
          level: LoggingLevel.Info,
        });
      }
      // DAL.getTransactionList().length;
    } catch (e) {
      console.log(e);
    }
  }
  if (enableStorageStatistics && IsIOS) {
    try {
      // const externalCachesDirectoryPath = RNFS.ExternalCachesDirectoryPath;
      const [documentsDir, documentsDirTotal] = yield call(getStorageDistribution, RNFS.DocumentDirectoryPath);
      const [databaseDir, databaseDirTotal] = yield call(getStorageDistribution, RNFS.CachesDirectoryPath);
      const [cacheDir, cacheDirTotal] = yield call(getStorageDistribution, RNFS.CachesDirectoryPath);
      // const [logsDir, logsDirTotal] = yield call(getStorageDistribution, externalCachesDirectoryPath + '/logs');
      const fsInfo = yield call(RNFS.getFSInfo);
      logLocalStorageEventInfo({
        action: LocalStorageAction.Track,
        privateDataPayload: {
          // externalCachesDirectoryPath,
          documentsDir: JSON.stringify(documentsDir),
          documentsDirTotalInMB: parseFloat(documentsDirTotal),
          databaseDir: JSON.stringify(databaseDir),
          databaseDirTotalInMB: parseFloat(databaseDirTotal),
          cacheDir: JSON.stringify(cacheDir),
          cacheDirTotalInMB: parseFloat(cacheDirTotal),
          // logsDir: JSON.stringify(logsDir),
          // logsDirTotalInMB: parseFloat(logsDirTotal),
          freeTotalInMB: parseFloat((fsInfo.freeSpace / 1024 / 1024).toFixed(2)),
          realmTotalInMB: parseFloat(documentsDir['default.realm']),
          realmLocalTxnSize: getTransactionsSize(),
          // realmSalesTxnSize: getSalesTransactionsSize(),
          // realmKdsTxnSize: getKdsTransactionsSize(),
          realmEmployeeActivitySize: getEmployeeActivitySize(),
          realmShiftSize: getShiftSize(),
          realmTxnLogSize: getTransactionLogSize(),
          realmBeepNotificationSize: getBeepNotificationSize(),
          realmSubOrderNotification: getSubOrderNotificationSize(),
          totalMemory: (yield DeviceInfo.getTotalMemory()) / (1024 * 1024),
          maxMemory: (yield DeviceInfo.getMaxMemory()) / (1024 * 1024),
          usedMemory: (yield DeviceInfo.getUsedMemory()) / (1024 * 1024),
          totalDiskCapacity: (yield DeviceInfo.getTotalDiskCapacity()) / (1024 * 1024),
          freeDiskCapacity: (yield DeviceInfo.getFreeDiskStorage()) / (1024 * 1024),
          exactRam: roundMemoryToNearestGB((yield DeviceInfo.getTotalDiskCapacity()) / (1024 * 1024)),
          exactRom: roundStorageToNearestGB((yield DeviceInfo.getFreeDiskStorage()) / (1024 * 1024)),
        },
        level: LoggingLevel.Info,
      });
    } catch (e) {
      console.log(e);
    }
  }
};
export const uploadLocalDataToS3Saga = function* () {
  try {
    const registerInfo = yield call(assembleRegisterInfo);
    const peripheralRegisters = yield call(assemblePeripheralRegisters);
    const hardwareInfo = yield call(assembleHardwareInfo);
    const performanceInfo = yield call(measurePerformanceStatus);
    const combinedInfo = {
      ...registerInfo,
      hardwareInfo: { printers: hardwareInfo },
      peripheralRegisters,
      performanceInfo,
    };
    console.log('combinedInfo', combinedInfo);
    const fileName = 'diagnoseLog.json';

    yield call(uploadJsonToS3, {
      fileName,
      data: combinedInfo,
      onSuccess: () => {
        logCustomDataSyncEvent({
          action: DataSyncAction.SyncLocalDataToIST,
          level: LoggingLevel.Info,
          result: 'Succeed',
          privateDataPayload: {
            fileName,
            data: JSON.stringify(combinedInfo),
            ...omit(performanceInfo, 'performStatistics'),
          },
        });
      },
      onFailure: () => {
        logCustomDataSyncEvent({
          action: DataSyncAction.SyncLocalDataToIST,
          level: LoggingLevel.Error,
          result: 'Failed',
          privateDataPayload: {
            fileName,
            data: JSON.stringify(combinedInfo),
          },
        });
      },
    });
  } catch (error) {
    logCustomDataSyncEvent({
      action: DataSyncAction.SyncLocalDataToIST,
      level: LoggingLevel.Error,
      result: 'Failed',
      privateDataPayload: {
        data: JSONUtils.stringify(error),
      },
    });
    console.log('uploadLocalDataToS3Saga error', error);
  }
};

const assembleRegisterInfo = function* () {
  const createTime: string = moment().format('yyyy-MM-DD HH:mm:ss Z');
  const deviceModel = DeviceInfoData.deviceModel;
  let manufacturerSync = DeviceInfo.getManufacturerSync();
  if (manufacturerSync === 'neostra') {
    manufacturerSync = 'iMin';
  }
  const registerModel = `${manufacturerSync} ${mapAndroidDeviceModelToReadableName(deviceModel)}`;
  const osVersion: string = DeviceInfoData.systemVersion;
  const platform = yield select(selectPlatform);
  const firmwareVersion = yield select(selectDeviceFirmwareVersion);
  const serialNumber = yield select(selectDeviceSerialNumber);
  const appVersion: string = DeviceInfoData.version;
  const buildNumber: string = DeviceInfoData.buildNumber;
  const { status } = yield call(checkNotifications);
  const pushNotificationPermissionGranted: boolean = status === 'granted';
  let cameraPermissionGranted = false;
  if (IsIOS) {
    cameraPermissionGranted = Object.values(yield call(checkMultiple, [PERMISSIONS.IOS.CAMERA]))[0] === 'granted';
  } else {
    cameraPermissionGranted = Object.values(yield call(checkMultiple, [PERMISSIONS.ANDROID.CAMERA]))[0] === 'granted';
  }
  const localNetworkPermissionGranted = yield call(checkLanPermission);

  const autoSyncAfterOpenShift: boolean = yield select(selectAutoSyncAfterOpenShift);
  const closeLastShiftNotification: boolean = yield select(selectCloseLastShiftNotification);
  const enableOpenOrders: boolean = yield select(selectEnableOpenOrders);
  const enableOpenOrdersShortCut: boolean = yield select(selectEnableOpenOrdersShortCut);
  const enableCustomerShortCut: boolean = yield select(selectEnableCustomerShortCut);
  const autoSignOutValue: string = yield select(selectAutoSignOutCount);
  const autoSignOut: string = mapAutoSignOutValue(autoSignOutValue);
  const enableTableLayout: boolean = yield select(selectTableLayoutEnabled);
  const enableCustomerQR = yield select(selectEnableCustomerQR);
  const allowOutOfStock = yield select(selectAllowOutOfStock);
  const alwaysPrintReceipt: boolean = yield select(selectAlwaysPrintReceipt);
  const alwaysPrintReceiptWithBeepQRPayOnline: boolean = yield select(selectAlwaysPrintReceiptWithBeepQRPayOnline);
  const alwaysPrintReceiptWithBeepQRPayAtCounter: boolean = yield select(selectAlwaysPrintReceiptWithBeepQRPayAtCounter);
  const alwaysPrintReceiptWithOnlineDelivery: boolean = yield select(selectAlwaysPrintReceiptWithOnlineDelivery);
  const kitchenDocketVariantLayout: string = (yield select(selectKitchenDocketVariantIsMultipleLine)) ? 'Multiple Lines' : 'Single Line';
  const printEmployeeName: boolean = yield select(selectPrintEmployeeName);
  const receiptFontSize: string = fontSizeMap(yield select(selectReceiptFontSize));
  const kitchenDocketFontSize: string = fontSizeMap(yield select(selectKitchenDocketFontSize));
  const enableCustomerDisplay: boolean = yield select(selectEnableCustomerDisplay);
  const defaultNetworkType: string = yield select(selectDefaultNetworkType);
  const defaultNetworkName: string = yield select(selectDefaultNetworkName);
  const currentNetworkType: string = yield select(selectCurrentNetworkType);
  const ssid: string = yield select(selectCurrentWifiSSID);
  const deviceIpAddress: string = yield select(selectIPAddress);
  const defaultNetworkError = yield call(logForDefaultNetworkSaga);
  const isOnlineMainRegister = yield call(ensureIsOnlineMainRegister, false);
  // no gateway ip address
  // no network packet loss
  const enabledMRS: boolean = yield select(selectIsEnabledMRS);
  const boEnabledMRS = yield select(selectIsBOEnabledMRS);
  const isMaster = yield select(selectIsMaster);
  const mrs: string = !enabledMRS ? 'Not enabled' : isMaster ? 'Local server' : 'Client';
  const pid = yield select(selectSlavePid);

  let pidOfRegister = 'N/A';
  let mrsState = Actions.MasterState.OFFLINE;
  let isServerRunning = false;
  let isClientRunning = false;
  let snapshotPid = INIT_PID,
    backupPid = INIT_PID,
    snapshotVersion = INIT_SNAPSHOT_VERSION;

  if (enabledMRS && isMaster) {
    pidOfRegister = `${pid}`;
  }

  let pidOfClient = 'N/A';
  if (enabledMRS && !isMaster) {
    pidOfClient = `${pid}`;
  }
  if (enabledMRS) {
    snapshotPid = yield select(selectSnapshotPid);
    snapshotVersion = yield select(selectSnapshotVersion);
    backupPid = yield select(selectBackupPid);
    mrsState = yield select(selectMasterState);
    isServerRunning = yield call(WsManager.isServerRunning);
    isClientRunning = yield call(getMRSClientRunning);
  }
  const selfRegisterId = String(yield select(selectRegisterId));
  const registers = yield select(selectMRSClients);
  const clientResiger = JSON.stringify(registers.filter(register => register.registerId !== selfRegisterId));
  const storeId = yield select(selectStoreId);
  const registerId = yield select(selectRegisterObjectId);
  const registerNumber = selfRegisterId;

  const businessCountry = yield select(selectCountry);
  const localTrxCount = DAL.getAllTransactionCount();
  const hotFixLable = yield call(getPushyVersion);
  const lastSyncTime = yield select(selectLastSyncTime);
  const transactionCount = localTrxCount;
  const kitchenPrinters = yield select(selectKitchenPrinters);

  // last sync time
  const lastProductSyncTime = yield select(selectLastProductSyncTime);
  const lastQuickLayoutSyncTime = yield select(selectLastQuickLayoutSyncTime);
  const lastEmployeeSyncTime = yield select(selectLastEmployeeSyncTime);
  const lastPromotionSyncTime = yield select(selectLastPromotionSyncTime);
  const lastActivationStatusSyncTime = yield select(selectOnlineSyncTime);
  const lastZReadingCloseTime = yield select(selectLastZReadingCloseTime);
  return {
    createTime,
    registerModel,
    osVersion,
    appVersion,
    hotFixLable,
    buildNumber,
    pushNotificationPermissionGranted,
    cameraPermissionGranted,
    autoSyncAfterOpenShift,
    closeLastShiftNotification,
    enableOpenOrders,
    enableOpenOrdersShortCut,
    enableCustomerShortCut,
    allowOutOfStock,
    autoSignOut,
    enableTableLayout,
    alwaysPrintReceipt,
    alwaysPrintReceiptWithBeepQRPayOnline,
    alwaysPrintReceiptWithBeepQRPayAtCounter,
    alwaysPrintReceiptWithOnlineDelivery,
    kitchenDocketVariantLayout,
    printEmployeeName,
    receiptFontSize,
    kitchenDocketFontSize,
    enableCustomerDisplay,
    defaultNetworkType,
    defaultNetworkName,
    currentNetworkType,
    defaultNetworkError,
    ssid,
    deviceIpAddress,
    boEnabledMRS,
    mrs,
    pidOfRegister,
    pidOfClient,
    snapshotPid,
    snapshotVersion,
    backupPid,
    clientResiger,
    mrsState,
    isClientRunning,
    isServerRunning,
    storeId,
    registerId,
    businessCountry,
    localTrxCount,
    lastProductSyncTime,
    lastQuickLayoutSyncTime,
    lastEmployeeSyncTime,
    lastPromotionSyncTime,
    lastActivationStatusSyncTime,
    lastZReadingCloseTime,
    registerNumber,
    lastSyncTime,
    transactionCount,
    localNetworkPermissionGranted,
    firmwareVersion,
    serialNumber,
    platform,
    enableCustomerQR,
    isOnlineMainRegister,
    kitchenPrinters,
  };
};

function* checkLanPermission() {
  if (IsAndroid) {
    const localNetworkPermissionGranted = yield call(check, PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION);
    return localNetworkPermissionGranted === RESULTS.GRANTED;
  } else {
    return yield call(PingUtils.getLocalNetworkAuthorization);
  }
}

function* assemblePeripheralRegisters() {
  const kdsEnabled = yield select(selectIsKDSPurchasedANDAssigned);
  const kdsRegisters = yield select(selectKds);
  const pairedKds = kdsRegisters.filter(kds => kds.isPaired);
  const unPairedKds = kdsRegisters.filter(kds => !kds.isPaired);

  const ncsEnabled = yield select(selectIsNCSPurchasedANDAssigned);
  const ncsRegisters = yield select(selectNcs);
  const pairedNcs = ncsRegisters.filter(ncs => ncs.isPaired);
  const unPairedNcs = ncsRegisters.filter(ncs => !ncs.isPaired);

  const cfdEnabled = yield select(selectNewCFDAllowed);
  let cfdRegisters = yield select(selectCfd);
  cfdRegisters = cfdRegisters.map(cfd => ({ ...cfd, appVersion: cfd.version }));
  const pairedCfd = cfdRegisters.filter(cfd => cfd.isPaired);
  const unPairedCfd = cfdRegisters.filter(cfd => !cfd.isPaired);

  return {
    kdsEnabled,
    pairedKds,
    unPairedKds,
    ncsEnabled,
    pairedNcs,
    unPairedNcs,
    cfdEnabled,
    pairedCfd,
    unPairedCfd,
  };
}

const assembleHardwareInfo = function* () {
  const immutablePrinters = yield select(selectPrinterTagsSettings);
  const printers = immutablePrinters.toJS();
  const printersList: any[] = Object.values(printers);
  for (const printer of printersList) {
    if (!Array.isArray(printer.tags)) {
      printer.tags = [];
    }
    if (!!printer.isBeepOrderSummaryPrinter) {
      if (!includes(printer.tags, 'QR Order Summary')) {
        printer.tags.push('QR Order Summary');
      }
    }
    if (!!printer.isReceiptPrinter) {
      if (!includes(printer.tags, 'Receipt')) {
        printer.tags.push('Receipt');
      }
      const summaryPrinter = yield select(selectOrderSummaryPrinter);
      if (summaryPrinter === ORDER_SUMMARY_PRINTER_TAG) {
        printer.tags.push('Order Summary');
      }
    }
  }

  return printersList;
};

export const migrateToNewPOSSaga = function* (action) {
  const { onSuccess, onFailure, token, business, registerId } = action.payload;

  yield put(Actions.replaceToken({ business, registerId, token }));
  const responseAction = yield take([Actions.replaceToken.toString() + '.success', Actions.replaceToken.toString() + '.failure']);
  const data = responseAction.payload;
  if (responseAction.type === Actions.replaceToken.toString() + '.success') {
    if (onSuccess) {
      onSuccess(data);
    }
  } else {
    if (onFailure) {
      onFailure(data);
    }
    console.error('replaceToken failed' + responseAction.payload);
  }
};

const checkIfKDSAndNCSSubscriptionUpdated = function* (remain: any) {
  const preKdsAssigned = yield select(selectIsKDSPurchasedANDAssigned);
  const preNcsAssigned = yield select(selectIsNCSPurchasedANDAssigned);

  const newKdsAssigned = get(remain, 'kds', false);
  const newNcsAssigned = get(remain, 'ncs', false);

  if (newKdsAssigned && !preKdsAssigned) {
    // active kds subscription
    openSuccessToast(t('Kitchen Display System activated'));
  } else if (!newKdsAssigned && preKdsAssigned) {
    openSuccessToast(t('Kitchen Display System deactivated'));
  }

  if (newNcsAssigned && !preNcsAssigned) {
    // active ncs subscription
    openSuccessToast(t('Number Calling System activated'));
  } else if (!newNcsAssigned && preNcsAssigned) {
    openSuccessToast(t('Number Calling System deactivated'));
  }
};

export const tryToPushStoreInfoToKDSSaga = function* () {
  const available: KdsSettingType[] = yield select(selectAvailableKds);
  if (isEmpty(available)) {
    return;
  }

  for (const kds of available) {
    yield call(pushSettingsToKDSSaga, kds.id);
  }
};

function* measurePerformanceStatus() {
  const performStatistics = [];
  const listener = PerformanceStats.addListener(stats => {
    performStatistics.push(stats);
    console.log(stats);
  });

  PerformanceStats.start(true);
  yield delay(3000);
  PerformanceStats.stop();
  listener.remove();
  let memoryStatistics = 0;
  let cpuStatistics = 0;
  performStatistics.forEach(it => {
    memoryStatistics += it.usedRam;
    cpuStatistics += it.usedCpu;
  });

  const maxMemoryOfPOS = Math.floor(DeviceInfo.getMaxMemorySync() / 1024 / 1024); // MB
  const totalMemoryOfDevice = DeviceInfo.getTotalMemorySync() / 1024 / 1024; // MB
  const usedMemoryOfPOS = Math.round(memoryStatistics / performStatistics.length); // MB
  const usedCpuPercentOfPOS = Math.round(cpuStatistics / performStatistics.length);
  const value: any = {
    usedMemoryOfPOS,
    availableMemoryOfPOS: maxMemoryOfPOS === -1 ? Math.floor(totalMemoryOfDevice - usedMemoryOfPOS) : Math.floor(maxMemoryOfPOS - usedMemoryOfPOS),
    maxMemoryOfPOS,
    totalMemoryOfDevice: Math.round((totalMemoryOfDevice / 1024) * 100) / 100, // GB
    usedCpuPercentOfPOS,
    performStatistics,
  };
  const memoryInfo = yield call(PingUtils.getDeviceMemory);
  if (memoryInfo) {
    value.availableMemoryOfDevice = Math.floor(memoryInfo.availMem / 1024 / 1024);
    value.usedMemoryOfDevice = Math.floor(memoryInfo.usedMem / 1024 / 1024);
    value.usedMemoryPercentOfDevice = Math.round((memoryInfo.usedMem / memoryInfo.totalMem) * 100);
    if (memoryInfo.threshold) {
      value.memoryThresholdOfDevice = Math.floor(memoryInfo.threshold / 1024 / 1024);
    }
    if (memoryInfo.lowMemory !== undefined) {
      value.lowMemoryOfDevice = memoryInfo.lowMemory;
    }
  }

  if (IS_DEV_FAT) {
    infoPOSBasicEvent({
      action: POSBasicAction.IST,
      privateDataPayload: {
        message: JSON.stringify({
          ...value,
          sn: yield select(selectDeviceSerialNumber),
          firmwareVersion: yield select(selectDeviceFirmwareVersion),
          localNetworkPermissionGranted: yield call(checkLanPermission),
        }),
      },
    });
  }
  console.log('value', value);
  return value;
}

export function* uploadMemoryInformationSaga() {
  const enable = yield select(selectPrintUploadMemory);
  if (!enable) {
    return;
  }
  const memoryInfo = yield call(PingUtils.getDeviceMemory);
  const value: any = {};
  if (memoryInfo) {
    if (memoryInfo.availMem) {
      value.availableMemoryOfDevice = Math.floor(memoryInfo.availMem / 1024 / 1024);
    }
    if (memoryInfo.totalMem) {
      value.totalMemoryOfDevice = Math.floor(memoryInfo.totalMem / 1024 / 1024);
    }
    if (memoryInfo.usedMemOfApp) {
      value.usedMemoryOfPOS = Math.floor(memoryInfo.usedMemOfApp / 1024 / 1024);
    }
    if (memoryInfo.threshold) {
      value.memoryThresholdOfDevice = Math.floor(memoryInfo.threshold / 1024 / 1024);
    }
    value.lowMemoryOfDevice = memoryInfo.lowMemory;
    value.maxMemoryOfPOS = Math.floor(DeviceInfo.getMaxMemorySync() / 1024 / 1024); // MB

    trackLogEvent({
      action: PrinterAction.MemoryInfo,
      level: LoggingLevel.Warn,
      privateDataPayload: value,
    });
  }
}

export default fork(dataSync);
