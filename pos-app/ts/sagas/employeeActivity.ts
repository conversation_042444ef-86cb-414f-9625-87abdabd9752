import { Action } from 'redux-actions';
import { call, delay, fork, put, race, select, take, takeLatest } from 'redux-saga/effects';
import * as Actions from '../actions';
import { EmployeeEInvoiceActivityType, PauseModeEnum, toggleLoadingMask } from '../actions';
import {
  EmployeeApplyManualDiscountType,
  EmployeeApprovePaymentManuallyType,
  EmployeeCancelOrderType,
  EmployeeClockInType,
  EmployeeClockOutType,
  EmployeeDeleteTransactionType,
  EmployeeOpenCashDrawerType,
  EmployeePrintHalfReceiptType,
  EmployeePrintReceiptType,
  EmployeeReduceItemType,
  EmployeeRefundType,
  EmployeeRemoveItemType,
} from '../actions/employeeActivity';
import DAL from '../dal';

import { selectBusinessName, selectCameraPermissionStatus, selectEmployeeId, selectEnableFaceCapture } from './selector';
import { AnyAction } from 'redux';
import { safeCallback } from '../utils';
import RNFaceCaptureManager from '../utils/faceCapture';
import { Alert } from 'react-native';
import RNFS from 'react-native-fs';
import { IsAndroid, t } from '../constants';
import { AWS_S3_BUCKET, AWS_S3_DB_ACL, AWS_S3_REGION } from '../config';
import { FaceCaptureAction, logFaceCaptureEvent } from '../utils/logComponent/buz/faceCapture';
import { uploadFileToS3WithCheckInvalidSaga } from './s3';

const REDUCE_ITEM = 'reduce_item';
const REMOVE_ITEM = 'remove_item';
const PRINT_HALF_RECEIPT = 'print_half_receipt';
const PRINT_RECEIPT = 'print_receipt';
const DELETE_TRANSACTION = 'delete_transaction';
const OPEN_CASH_DRAWER = 'open_cash_drawer';
const APPROVE_MANUALLY = 'manual_approve';
const ENABLED_PAUSE_MODE = 'enable_pause_mode';
const DISABLED_PAUSE_MODE = 'disable_pause_mode';
const CANCEL_ORDER = 'cancel_order';
const APPLY_MANUAL_DISCOUNT = 'apply_manual_discount';
const REFUND = 'refund';
const E_INVOICE_CANCEL_INVOICE = 'e_invoice_cancel_invoice';
const E_INVOICE_CANCEL_WITHOUT_INVOICE = 'e_invoice_cancel_without_invoice';
const E_INVOICE_REQUEST_REFUND = 'e_invoice_request_refund';
const E_INVOICE_PRINT_QR = 'e_invoice_print_qr';
const OOS_ENABLE = 'oos_enable';
const OOS_DISABLE = 'oos_disable';
const OOS_TEMPORARY_ENABLE_SINGLE_TXN = 'oos_temporary_enable_single_txn';
const OOS_TEMPORARY_ENABLE_NEXT_30_MIN = 'oos_temporary_enable_next_30_min';
const OOS_TEMPORARY_ENABLE_NEXT_60_MIN = 'oos_temporary_enable_next_60_min';
const OOS_TEMPORARY_ENABLE_REST_OF_DAY = 'oos_temporary_enable_rest_of_day';

export const employeeCancelInvoiceSaga = function* (action: AnyAction & { payload: EmployeeEInvoiceActivityType }) {
  const { transactionId } = action.payload;
  const info = { transactionId };
  yield call(saveEmployeeActivity, E_INVOICE_CANCEL_INVOICE, info);
};
export const employeeCancelWithoutEInvoiceSaga = function* (
  action: AnyAction & {
    payload: EmployeeEInvoiceActivityType;
  }
) {
  const { transactionId } = action.payload;
  const info = { transactionId };
  yield call(saveEmployeeActivity, E_INVOICE_CANCEL_WITHOUT_INVOICE, info);
};
export const employeeRequestRefundSaga = function* (action: AnyAction & { payload: EmployeeEInvoiceActivityType }) {
  const { transactionId } = action.payload;
  const info = { transactionId };
  yield call(saveEmployeeActivity, E_INVOICE_REQUEST_REFUND, info);
};
export const employeePrintEInvoiceQrReceiptSaga = function* (
  action: AnyAction & {
    payload: EmployeeEInvoiceActivityType;
  }
) {
  const { transactionId } = action.payload;
  const info = { transactionId };
  yield call(saveEmployeeActivity, E_INVOICE_PRINT_QR, info);
};

export function* employeeActivity() {
  yield takeLatest(Actions.employeeReduceItem.toString(), reduceItem);
  yield takeLatest(Actions.employeeRemoveItem.toString(), removeItem);
  yield takeLatest(Actions.employeeDeleteTransaction.toString(), deleteOrder);
  yield takeLatest(Actions.employeePrintReceipt.toString(), printReceipt);
  yield takeLatest(Actions.employeePrintHalfReceipt.toString(), printHalfReceipt);
  yield takeLatest(Actions.employeeOpenCashDrawer.toString(), openCashDrawer);
  yield takeLatest(Actions.employeeApprovePaymentManually.toString(), approvePaymentManually);
  yield takeLatest(Actions.enablePauseMode.toString(), enablePauseMode);
  yield takeLatest(Actions.disablePauseMode.toString(), disablePauseMode);
  yield takeLatest(Actions.employeeClockIn.toString(), employeeClockInSaga);
  yield takeLatest(Actions.employeeClockOut.toString(), employeeClockOutSaga);
  yield takeLatest(Actions.employeeCancelOrder.toString(), employeeCancelOrderSaga);
  yield takeLatest(Actions.employeeRefund.toString(), employeeRefundSaga);
  yield takeLatest(Actions.employeeApplyManualDiscount.toString(), employeeApplyManualDiscountSaga);

  yield takeLatest(Actions.employeeCancelEInvoice.toString(), employeeCancelInvoiceSaga);
  yield takeLatest(Actions.employeeCancelWithoutEInvoice.toString(), employeeCancelWithoutEInvoiceSaga);
  yield takeLatest(Actions.employeeRequestRefund.toString(), employeeRequestRefundSaga);
  yield takeLatest(Actions.employeePrintEInvoiceQrReceipt.toString(), employeePrintEInvoiceQrReceiptSaga);

  yield takeLatest(Actions.employeeOOSEnable.toString(), employeeOOSEnableSaga);
  yield takeLatest(Actions.employeeOOSDisable.toString(), employeeOOSDisableSaga);
  yield takeLatest(Actions.employeeOOSTemporaryEnableSingleTxn.toString(), employeeOOSTemporaryEnableSingleTxnSaga);
  yield takeLatest(Actions.employeeOOSTemporaryEnableNext30Min.toString(), employeeOOSTemporaryEnableNext30MinSaga);
  yield takeLatest(Actions.employeeOOSTemporaryEnableNext60Min.toString(), employeeOOSTemporaryEnableNext60MinSaga);
  yield takeLatest(Actions.employeeOOSTemporaryEnableRestOfDay.toString(), employeeOOSTemporaryEnableRestOfDaySaga);
}

export const employeeOOSEnableSaga = function* (action: AnyAction) {
  yield call(saveEmployeeActivity, OOS_ENABLE, null);
};

export const employeeOOSDisableSaga = function* (action: AnyAction) {
  yield call(saveEmployeeActivity, OOS_DISABLE, null);
};

export const employeeOOSTemporaryEnableSingleTxnSaga = function* (action: AnyAction) {
  yield call(saveEmployeeActivity, OOS_TEMPORARY_ENABLE_SINGLE_TXN, null);
};

export const employeeOOSTemporaryEnableNext30MinSaga = function* (action: AnyAction) {
  yield call(saveEmployeeActivity, OOS_TEMPORARY_ENABLE_NEXT_30_MIN, null);
};

export const employeeOOSTemporaryEnableNext60MinSaga = function* (action: AnyAction) {
  yield call(saveEmployeeActivity, OOS_TEMPORARY_ENABLE_NEXT_60_MIN, null);
};

export const employeeOOSTemporaryEnableRestOfDaySaga = function* (action: AnyAction) {
  yield call(saveEmployeeActivity, OOS_TEMPORARY_ENABLE_REST_OF_DAY, null);
};

export const reduceItem = function* (action: AnyAction & { payload: EmployeeReduceItemType }) {
  const { transactionId, productName, productId, quantity } = action.payload;
  const info = {
    transactionId,
    productName,
    productId,
    qty: quantity,
  };
  yield call(saveEmployeeActivity, REDUCE_ITEM, info);
};

export const removeItem = function* (action: AnyAction & { payload: EmployeeRemoveItemType }) {
  const { transactionId, productName, productId, quantity } = action.payload;
  const info = {
    transactionId,
    productName,
    productId,
    qty: quantity,
  };
  yield call(saveEmployeeActivity, REMOVE_ITEM, info);
};

export const printHalfReceipt = function* (action: AnyAction & { payload: EmployeePrintHalfReceiptType }) {
  const { transactionId } = action.payload;
  const info = { transactionId };
  yield call(saveEmployeeActivity, PRINT_HALF_RECEIPT, info);
};

export const printReceipt = function* (action: AnyAction & { payload: EmployeePrintReceiptType }) {
  const { transactionId, isReprint } = action.payload;
  const info = { transactionId, isReprint };
  yield call(saveEmployeeActivity, PRINT_RECEIPT, info);
};

export const deleteOrder = function* (action: AnyAction & { payload: EmployeeDeleteTransactionType }) {
  const { transactionId, isOpenOrder, amount } = action.payload;
  const info = { transactionId, isOpenOrder, amount };
  yield call(saveEmployeeActivity, DELETE_TRANSACTION, info);
};

export const openCashDrawer = function* (action: AnyAction & { payload: EmployeeOpenCashDrawerType }) {
  const { reason } = action.payload;
  const info = { reason };
  yield call(saveEmployeeActivity, OPEN_CASH_DRAWER, info);
};

export const approvePaymentManually = function* (action: AnyAction & { payload: EmployeeApprovePaymentManuallyType }) {
  const { paymentId, transactionId } = action.payload;
  const info = { transactionId, paymentOption: paymentId };
  yield call(saveEmployeeActivity, APPROVE_MANUALLY, info);
};

export const enablePauseMode = function* (action: Action<PauseModeEnum>) {
  const pauseMode = action.payload;
  const info = { pauseMode };
  yield call(saveEmployeeActivity, ENABLED_PAUSE_MODE, info);
};
export const disablePauseMode = function* () {
  yield call(saveEmployeeActivity, DISABLED_PAUSE_MODE, {});
};

export const saveEmployeeActivity = function* (activityName, info) {
  const employeeId = yield select(selectEmployeeId);
  const now = new Date();
  const employeeActivity = {
    action: activityName,
    additionalInfo: Boolean(info) ? JSON.stringify(info) : null,
    user: employeeId,
    time: now.toISOString(),
  };
  DAL.saveEmployeeActivity(employeeActivity);
};

export const employeeClockInSaga = function* (action: Action<EmployeeClockInType>) {
  const { employeeId, storeId, clockInTime, onSuccess, onError, faceCaptureUri } = action.payload;
  const enableFaceCapture = yield select(selectEnableFaceCapture);
  const cameraPermissionStatus = yield select(selectCameraPermissionStatus);

  if (IsAndroid && enableFaceCapture && faceCaptureUri) {
    logFaceCaptureEvent({
      action: FaceCaptureAction.Start,
      tags: ['clock-in'],
    });

    logFaceCaptureEvent({
      action: FaceCaptureAction.CameraAvailable,
      tags: ['clock-in'],
    });
    logFaceCaptureEvent({
      action: FaceCaptureAction.ImageCaptured,
      tags: ['clock-in'],
    });
    const uploadResult = yield call(uploadImageAndroid, faceCaptureUri, employeeId, true);
    const imageName = faceCaptureUri.split('/').pop().split('.')[0];
    if (uploadResult) {
      yield call(performClockInRequest, employeeId, storeId, clockInTime, onSuccess, onError, imageName);
    } else {
      const userAction = yield call(showAlert, t('Upload Failed'), t('Please try again to retake the photo or skip to proceed'));
      if (userAction === 'skip') {
        logFaceCaptureEvent({
          action: FaceCaptureAction.SkipAfterUploadFailed,
          tags: ['clock-in'],
        });
        yield call(performClockInRequest, employeeId, storeId, clockInTime, onSuccess, onError);
      } else if (userAction === 'retry') {
        logFaceCaptureEvent({
          action: FaceCaptureAction.RetryAfterUploadFailed,
          tags: ['clock-in'],
        });
        yield call(employeeClockInSaga, action);
      }
    }
  } else if (IsAndroid || !enableFaceCapture || (enableFaceCapture && cameraPermissionStatus === false)) {
    yield call(performClockInRequest, employeeId, storeId, clockInTime, onSuccess, onError);
  } else {
    // enableFaceCapture && cameraPermissionStatus === true, face capture flow
    logFaceCaptureEvent({
      action: FaceCaptureAction.Start,
      tags: ['clock-in'],
    });
    const isCameraAvailable = yield call(RNFaceCaptureManager.checkIfCameraAvailableWithResolve);
    if (!isCameraAvailable) {
      logFaceCaptureEvent({
        action: FaceCaptureAction.CameraNotAvailable,
        tags: ['clock-in'],
      });
      const userAction = yield call(showAlert, 'Camera Not Available', t('Please try again to retake the photo or skip to proceed'));
      if (userAction === 'skip') {
        logFaceCaptureEvent({
          action: FaceCaptureAction.SkipAfterCameraNotAvailable,
          tags: ['clock-in'],
        });
        yield call(performClockInRequest, employeeId, storeId, clockInTime, onSuccess, onError);
      } else if (userAction === 'retry') {
        logFaceCaptureEvent({
          action: FaceCaptureAction.RetryAfterCameraNotAvailable,
          tags: ['clock-in'],
        });
        yield call(employeeClockInSaga, action);
      }
    } else {
      logFaceCaptureEvent({
        action: FaceCaptureAction.CameraAvailable,
        tags: ['clock-in'],
      });
      // enableFaceCapture && cameraPermissionStatus === true && Camera is available
      const imageName: string = yield call(RNFaceCaptureManager.openCameraWithResolve);
      if (imageName && imageName.trim().length > 0) {
        logFaceCaptureEvent({
          action: FaceCaptureAction.ImageCaptured,
          tags: ['clock-in'],
        });
        const uploadResult = yield call(uploadImage, imageName, employeeId, true);
        if (uploadResult) {
          yield call(performClockInRequest, employeeId, storeId, clockInTime, onSuccess, onError, imageName);
        } else {
          const userAction = yield call(showAlert, t('Upload Failed'), t('Please try again to retake the photo or skip to proceed'));
          if (userAction === 'skip') {
            logFaceCaptureEvent({
              action: FaceCaptureAction.SkipAfterUploadFailed,
              tags: ['clock-in'],
            });
            yield call(performClockInRequest, employeeId, storeId, clockInTime, onSuccess, onError);
          } else if (userAction === 'retry') {
            logFaceCaptureEvent({
              action: FaceCaptureAction.RetryAfterUploadFailed,
              tags: ['clock-in'],
            });
            yield call(employeeClockInSaga, action);
          }
        }
      } else {
        logFaceCaptureEvent({
          action: FaceCaptureAction.ImageNotCaptured,
          tags: ['clock-in'],
        });
        safeCallback(onError, { imageNotAvailable: true });
      }
    }
  }
};

export const employeeClockOutSaga = function* (action: AnyAction & { payload: EmployeeClockOutType }) {
  const { employeeId, storeId, business, clockOutTime, onSuccess, onError, faceCaptureUri } = action.payload;
  const enableFaceCapture = yield select(selectEnableFaceCapture);
  const cameraPermissionStatus = yield select(selectCameraPermissionStatus);

  if (IsAndroid && enableFaceCapture && faceCaptureUri) {
    logFaceCaptureEvent({
      action: FaceCaptureAction.Start,
      tags: ['clock-out'],
    });

    logFaceCaptureEvent({
      action: FaceCaptureAction.CameraAvailable,
      tags: ['clock-out'],
    });
    logFaceCaptureEvent({
      action: FaceCaptureAction.ImageCaptured,
      tags: ['clock-out'],
    });
    const uploadResult = yield call(uploadImageAndroid, faceCaptureUri, employeeId, false);
    const imageName = faceCaptureUri.split('/').pop().split('.')[0];
    if (uploadResult) {
      yield call(performClockOutRequest, employeeId, storeId, business, clockOutTime, onSuccess, onError, imageName);
    } else {
      const userAction = yield call(showAlert, t('Upload Failed'), t('Please try again to retake the photo or skip to proceed'));
      if (userAction === 'skip') {
        logFaceCaptureEvent({
          action: FaceCaptureAction.SkipAfterUploadFailed,
          tags: ['clock-out'],
        });
        yield call(performClockOutRequest, employeeId, storeId, business, clockOutTime, onSuccess, onError);
      } else if (userAction === 'retry') {
        logFaceCaptureEvent({
          action: FaceCaptureAction.RetryAfterUploadFailed,
          tags: ['clock-out'],
        });
        yield call(employeeClockOutSaga, action);
      }
    }
  } else if (IsAndroid || !enableFaceCapture || (enableFaceCapture && cameraPermissionStatus === false)) {
    yield call(performClockOutRequest, employeeId, storeId, business, clockOutTime, onSuccess, onError);
  } else {
    // enableFaceCapture && cameraPermissionStatus === true
    logFaceCaptureEvent({
      action: FaceCaptureAction.Start,
      tags: ['clock-out'],
    });
    const isCameraAvailable = yield call(RNFaceCaptureManager.checkIfCameraAvailableWithResolve);
    if (!isCameraAvailable) {
      logFaceCaptureEvent({
        action: FaceCaptureAction.CameraNotAvailable,
        tags: ['clock-out'],
      });
      const userAction = yield call(showAlert, t('Camera Not Available'), t('Please try again to retake the photo or skip to proceed'));
      if (userAction === 'skip') {
        logFaceCaptureEvent({
          action: FaceCaptureAction.SkipAfterCameraNotAvailable,
          tags: ['clock-out'],
        });
        yield call(performClockOutRequest, employeeId, storeId, business, clockOutTime, onSuccess, onError);
      } else if (userAction === 'retry') {
        logFaceCaptureEvent({
          action: FaceCaptureAction.RetryAfterCameraNotAvailable,
          tags: ['clock-out'],
        });
        yield call(employeeClockOutSaga, action);
      }
    } else {
      logFaceCaptureEvent({
        action: FaceCaptureAction.CameraAvailable,
        tags: ['clock-out'],
      });
      // enableFaceCapture && cameraPermissionStatus === true && Camera is available
      const imageName: string = yield call(RNFaceCaptureManager.openCameraWithResolve);
      if (imageName && imageName.trim().length > 0) {
        logFaceCaptureEvent({
          action: FaceCaptureAction.ImageCaptured,
          tags: ['clock-out'],
        });
        const uploadResult = yield call(uploadImage, imageName, employeeId, false);
        if (uploadResult) {
          yield call(performClockOutRequest, employeeId, storeId, business, clockOutTime, onSuccess, onError, imageName);
        } else {
          const userAction = yield call(showAlert, t('Upload Failed'), t('Please try again to retake the photo or skip to proceed'));
          if (userAction === 'skip') {
            logFaceCaptureEvent({
              action: FaceCaptureAction.SkipAfterUploadFailed,
              tags: ['clock-out'],
            });
            yield call(performClockOutRequest, employeeId, storeId, business, clockOutTime, onSuccess, onError);
          } else if (userAction === 'retry') {
            logFaceCaptureEvent({
              action: FaceCaptureAction.RetryAfterUploadFailed,
              tags: ['clock-out'],
            });
            yield call(employeeClockOutSaga, action);
          }
        }
      } else {
        logFaceCaptureEvent({
          action: FaceCaptureAction.ImageNotCaptured,
          tags: ['clock-out'],
        });
        safeCallback(onError, { imageNotAvailable: true });
      }
    }
  }
};

function* performClockInRequest(employeeId: string, storeId: string, clockInTime: string, onSuccess: any, onError: any, clockInImage?: string) {
  yield put(
    Actions.clockIn({
      employeeId,
      storeId,
      clockInTime,
      clockInImage,
    })
  );
  const responseAction = yield take([Actions.clockIn.toString() + '.success', Actions.clockIn.toString() + '.failure']);
  const resp = responseAction.payload;
  if (responseAction.type === Actions.clockIn.toString() + '.success') {
    if (clockInImage) {
      logFaceCaptureEvent({
        action: FaceCaptureAction.CallClockInAPIWithImageSuccess,
        tags: ['clock-in'],
        privateDataPayload: { employeeId, storeId, clockInTime, clockInImage },
      });
    }
    safeCallback(onSuccess, resp);
  } else if (responseAction.type === Actions.clockIn.toString() + '.failure') {
    if (clockInImage) {
      logFaceCaptureEvent({
        action: FaceCaptureAction.CallClockInAPIWithImageFailure,
        tags: ['clock-in'],
        privateDataPayload: { employeeId, storeId, clockInTime, clockInImage },
      });
    }
    safeCallback(onError, resp);
  }
}

function* performClockOutRequest(
  employeeId: string,
  storeId: string,
  business: string,
  clockOutTime: string,
  onSuccess: any,
  onError: any,
  clockOutImage?: string
) {
  yield put(
    Actions.clockOut({
      employeeId,
      storeId,
      business,
      clockOutTime,
      clockOutImage,
    })
  );
  const responseAction = yield take([Actions.clockOut.toString() + '.success', Actions.clockOut.toString() + '.failure']);
  const resp = responseAction.payload;
  if (responseAction.type === Actions.clockOut.toString() + '.success') {
    if (clockOutImage) {
      logFaceCaptureEvent({
        action: FaceCaptureAction.CallClockOutAPIWithImageSuccess,
        tags: ['clock-out'],
      });
    }
    safeCallback(onSuccess, resp);
  } else if (responseAction.type === Actions.clockOut.toString() + '.failure') {
    if (clockOutImage) {
      logFaceCaptureEvent({
        action: FaceCaptureAction.CallClockOutAPIWithImageFailure,
        tags: ['clock-out'],
      });
    }
    safeCallback(onError, resp);
  }
}

function showAlert(title, message) {
  return new Promise((resolve, reject) => {
    Alert.alert(
      title,
      message,
      [
        { text: t('Try Again'), onPress: () => resolve('retry') },
        { text: t('Skip Face Capture'), onPress: () => resolve('skip') },
      ],
      { cancelable: false }
    );
  });
}

function* uploadImageAndroid(imageFilePath: string, employeeId: string, clockIn: boolean) {
  const exist = yield call(RNFS.exists, imageFilePath);

  if (exist) {
    yield put(toggleLoadingMask({ visible: true, disableInteractions: true }));
    const businessName = yield select(selectBusinessName);
    const file = {
      uri: imageFilePath,
      name: imageFilePath.split('/').pop().split('.')[0] + '.png',
      type: 'image/png',
    };

    const options = {
      acl: AWS_S3_DB_ACL,
      keyPrefix: `${businessName}/employee/${employeeId}/`,
      bucket: AWS_S3_BUCKET,
      region: AWS_S3_REGION,
      // accessKey: AWS_ACESS_KEY_ID,
      // secretKey: AWS_SECRET_ACCESS_KEY,
      successActionStatus: 201,
    };

    const tags = clockIn ? ['clock-in'] : ['clock-out'];

    try {
      const { response, timeout } = yield race({
        response: call(uploadFileToS3WithCheckInvalidSaga, { type: '', payload: { file, options } }),
        timeout: delay(10000), // 5 seconds timeout
      });
      yield put(toggleLoadingMask({ visible: false, disableInteractions: true }));
      if (timeout) {
        logFaceCaptureEvent({
          action: FaceCaptureAction.ImageUploadFailed,
          tags,
          reason: 'Network timeout',
        });
        return false;
      }
      if (response.status === 201) {
        logFaceCaptureEvent({
          action: FaceCaptureAction.ImageUploadedSuccessfully,
          tags,
        });
        return true;
      } else {
        logFaceCaptureEvent({
          action: FaceCaptureAction.ImageUploadFailed,
          tags,
          reason: String(response.body),
        });
        return false;
      }
    } catch (error) {
      logFaceCaptureEvent({
        action: FaceCaptureAction.ImageUploadFailed,
        tags,
        reason: String(error),
      });
      yield put(toggleLoadingMask({ visible: false, disableInteractions: true }));
      console.error('Network error:', error);
      return false;
    }
  } else {
    return false;
  }
}

function* uploadImage(fileName: string, employeeId: string, clockIn: boolean) {
  const appDirectory = RNFS.DocumentDirectoryPath;
  const imageFilePath = `${appDirectory}/${fileName}` + '.png';

  const exist = yield call(RNFS.exists, imageFilePath);

  if (exist) {
    yield put(toggleLoadingMask({ visible: true, disableInteractions: true }));
    const businessName = yield select(selectBusinessName);
    const file = {
      uri: IsAndroid ? `file://${imageFilePath}` : imageFilePath,
      name: fileName + '.png',
      type: 'image/png',
    };

    const options = {
      acl: AWS_S3_DB_ACL,
      keyPrefix: `${businessName}/employee/${employeeId}/`,
      bucket: AWS_S3_BUCKET,
      region: AWS_S3_REGION,
      // accessKey: AWS_ACESS_KEY_ID,
      // secretKey: AWS_SECRET_ACCESS_KEY,
      successActionStatus: 201,
    };

    const tags = clockIn ? ['clock-in'] : ['clock-out'];

    try {
      const response = yield call(uploadFileToS3WithCheckInvalidSaga, { type: '', payload: { file, options } });
      yield put(toggleLoadingMask({ visible: false, disableInteractions: true }));
      if (response.status === 201) {
        logFaceCaptureEvent({
          action: FaceCaptureAction.ImageUploadedSuccessfully,
          tags,
        });
        return true;
      } else {
        logFaceCaptureEvent({
          action: FaceCaptureAction.ImageUploadFailed,
          tags,
          reason: String(response.body),
        });
        return false;
      }
    } catch (error) {
      logFaceCaptureEvent({
        action: FaceCaptureAction.ImageUploadFailed,
        tags,
        reason: String(error),
      });
      yield put(toggleLoadingMask({ visible: false, disableInteractions: true }));
      console.error('Network error:', error);
      return false;
    }
  } else {
    return false;
  }
}

export const employeeCancelOrderSaga = function* (action: AnyAction & { payload: EmployeeCancelOrderType }) {
  const { transactionId } = action.payload;
  const info = { transactionId };
  yield call(saveEmployeeActivity, CANCEL_ORDER, info);
};

export const employeeRefundSaga = function* (action: AnyAction & { payload: EmployeeRefundType }) {
  const { transactionId } = action.payload;
  const info = { transactionId };
  yield call(saveEmployeeActivity, REFUND, info);
};

export const employeeApplyManualDiscountSaga = function* (
  action: AnyAction & {
    payload: EmployeeApplyManualDiscountType;
  }
) {
  const { transactionId } = action.payload;
  const info = { transactionId };
  yield call(saveEmployeeActivity, APPLY_MANUAL_DISCOUNT, info);
};

export default fork(employeeActivity);
