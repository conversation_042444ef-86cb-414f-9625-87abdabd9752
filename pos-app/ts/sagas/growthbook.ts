import { GrowthBook, configureCache, setPolyfills } from '@growthbook/growthbook';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { Platform } from 'react-native';

import { debounce, isEmpty } from 'lodash';

import { Action } from 'redux-actions';
import { call, fork, select, takeLatest } from 'redux-saga/effects';
import { GetGBFeatureType, clearGrowthBook, getGBFeature, initGrowthBook, setGrowthBookFeatures, updateGrowthBook } from '../actions';
import { GB_HOST, GB_KEY } from '../config';
import { IsAndroid, IsIOS } from '../constants';
import DeviceInfoData from '../utils/deviceInfo';
import eventBus, { UPDATE_GLOBAL_GROWTHBOOK } from '../utils/eventBus';
import { GBAttr, GBFeatureDefaultValue } from '../utils/growthbook';
import { GBLogEvent, logGBEvent } from '../utils/logComponent/buz/growthbook';
import { EventSource } from '../utils/polyfills';
import PrinterManager from '../utils/printer';
import ScannerManager from '../utils/scanner';
import { updateLocalLoggerConfigurationSaga } from './locallogger';
import {
  selectBusinessName,
  selectCountry,
  selectDeviceFirmwareVersion,
  selectDeviceSerialNumber,
  selectEnableCashback,
  selectEnableLoyalty,
  selectGBEnableIminScannerFix,
  selectGBEnableNewScannerFlow,
  selectIndustry,
  selectMallIntegrationChannel,
  selectRegisterId,
  selectStoreId,
  selectUseAsyncWebViewPrintingFlow,
} from './selector';

type GrowthBookInstanceType = GrowthBook | null;
let GBInstance: GrowthBookInstanceType = null;

export const GB_CACHE_KEY = 'GROWTH_BOOK_CACHE_KEY';
configureCache({
  // staleTTL default 1 minute
  cacheKey: GB_CACHE_KEY,
  disableCache: true,
});

setPolyfills({
  EventSource: EventSource,
  localStorage: {
    setItem: (key, value) => {
      return AsyncStorage.setItem(key, value);
    },
    getItem: async key => {
      return await AsyncStorage.getItem(key);
    },
  },
});

export function* prepareGBAttr() {
  const businessName = yield select(selectBusinessName);
  const country = yield select(selectCountry);
  const deviceId = DeviceInfoData.uniqueId;
  const deviceModel = DeviceInfoData.deviceModel;
  const cashback = yield select(selectEnableCashback);
  const loyalty = yield select(selectEnableLoyalty);
  const mallIntegrationChannel = yield select(selectMallIntegrationChannel);
  const appVersion = DeviceInfoData.version;
  const buildNumber = DeviceInfoData.buildNumber;
  const registerNumber = yield select(selectRegisterId);
  const storeId = yield select(selectStoreId);
  const sn = yield select(selectDeviceSerialNumber);
  const firmwareVersion = yield select(selectDeviceFirmwareVersion);
  const industry = yield select(selectIndustry);
  const attrs: GBAttr = {
    id: deviceId,
    deviceId,
    country,
    appVersion: appVersion,
    mobileVersion: appVersion, // 1.72.0
    osVersion: DeviceInfoData.systemVersion, // 1.72.0
    business: businessName,
    enableCashback: loyalty && cashback,
    appPlatform: Platform.OS,
    deviceModel,
    product: IsIOS ? 'RN POS iOS' : 'RN POS Android',
    buildNumber,
    mallIntegrationChannel,
    // 1.86.0
    registerNumber,
    mobileStoreId: storeId,
    sn,
    firmwareVersion,
    // 1.91.0
    storeIndustry: String(industry),
  };
  return attrs;
}

const refreshFeatures = (instance: GrowthBook, streaming = true) => {
  return new Promise(resolve => {
    if (instance) {
      instance
        .init({
          timeout: 2000,
          streaming, // Enable SSE streaming (was default in 0.x)
        })
        .then(result => {
          // Enhanced error handling with new result format
          console.log('GrowthBook init result:', result);
          // result.success - true if successful
          // result.source - 'network', 'cache', 'init', 'error', or 'timeout'
          // result.error - Error object if success is false
          resolve(result.success);
          if (!result.success) {
            logGBEvent(GBLogEvent.REFRESH_ERROR, {
              reason: 'refreshFeatures error',
              message: { errorMessage: result.error && result.error.message, error: result.error },
            });
          }
        })
        .catch(error => {
          console.log('refreshFeatures error = ', error.message);
          resolve(false);
          logGBEvent(GBLogEvent.REFRESH_ERROR, { reason: 'refreshFeatures error', message: { errorMessage: error && error.message, error } });
        });
    } else {
      resolve(false);
    }
  });
};
const debounceLogUpdateFeature = debounce(message => logGBEvent(GBLogEvent.UPDATE_FEATURE, { message }), 1000, { trailing: true });

const sendRenderer = (instance: GrowthBook) => {
  if (instance) {
    instance.setRenderer(() => {
      const globalEventBody: any = {};
      if (isEmpty(instance.getFeatures())) {
        // do not update if has no network
        return;
      }
      GBFeatureDefaultValue.forEach((defaultValue, key) => {
        globalEventBody[key] = instance.getFeatureValue(key, defaultValue);
      });
      debounceLogUpdateFeature(globalEventBody);
      eventBus.emit(UPDATE_GLOBAL_GROWTHBOOK, globalEventBody);
    });
  }
};

export function* getGBSingleInstance() {
  if (!GBInstance) {
    const attrs: GBAttr = yield call(prepareGBAttr);
    GBInstance = new GrowthBook({
      apiHost: GB_HOST,
      clientKey: GB_KEY,
      enableDevMode: false,
      disableCache: true,
      attributes: attrs,
      trackingCallback: (experiment, result) => {
        console.log('Experiment Viewed', {
          experimentId: experiment.key,
          variationId: result.variationId,
        });
      },
      onFeatureUsage: (key, result) => {
        // logGBEvent(GBLogEvent.GET_FEATURE, { message: { key, result } });
      },
    });
    sendRenderer(GBInstance);
    // Properly await initialization with streaming enabled
    yield call(refreshFeatures, GBInstance, true);
    logGBEvent(GBLogEvent.INIT, { message: { attrs } });
    return GBInstance;
  }
  return GBInstance;
}

export function* initGrowthBookSaga() {
  yield call(getGBSingleInstance);
}

export function* updateGBFeaturesSaga() {
  const instance: GrowthBook | null = yield call(getGBSingleInstance);
  if (instance) {
    yield call(refreshFeatures, instance);
  }
}
export function* updateGBAttrSaga() {
  const instance: GrowthBook | null = yield call(getGBSingleInstance);
  if (instance) {
    const attrs: GBAttr = yield call(prepareGBAttr);
    logGBEvent(GBLogEvent.UPDATE_ATTR, { message: { attrs } });
    instance.setAttributes(attrs);
  }
}

export function* updateGrowthBookSaga() {
  yield call(updateGBAttrSaga);
  yield call(updateGBFeaturesSaga);
}

export function* getGrowthBookFeatureSaga(action: Action<GetGBFeatureType>) {
  const { name, defaultValue, onComplete } = action.payload;
  const instance: GrowthBook | null = yield call(getGBSingleInstance);
  if (instance) {
    const value = instance.getFeatureValue(name, defaultValue);
    onComplete && onComplete(value);
    return value;
  }
  logGBEvent(GBLogEvent.GET_FEATURE, { reason: 'instance is null' });
  onComplete(defaultValue);
  return defaultValue;
}

export function* clearGrowthBookSaga() {
  const instance: GrowthBookInstanceType = yield call(getGBSingleInstance);
  if (instance) {
    instance.destroy();
  }
  GBInstance = null;
}

export function* setGrowthBookFeaturesSaga() {
  console.warn('GrowthBook', 'setGrowthBookFeaturesSaga');
  if (IsAndroid) {
    const newFlow = yield select(selectUseAsyncWebViewPrintingFlow);
    yield PrinterManager.setFlowFlag(newFlow);
  }

  if (IsAndroid) {
    const newFlow = yield select(selectGBEnableNewScannerFlow);
    if (newFlow) {
      yield ScannerManager.setScannerFixFlag(0);
      console.warn('ScannerManager', 'setScannerFixFlag', 0);
    } else {
      const scannerFix = yield select(selectGBEnableIminScannerFix);
      yield ScannerManager.setScannerFixFlag(scannerFix);
      console.warn('ScannerManager', 'setScannerFixFlag', scannerFix);
    }
  }
  yield call(updateLocalLoggerConfigurationSaga);
}

export function* growthBookQueue() {
  yield takeLatest(initGrowthBook.toString(), initGrowthBookSaga);
  yield takeLatest(getGBFeature.toString(), getGrowthBookFeatureSaga);
  yield takeLatest(clearGrowthBook.toString(), clearGrowthBookSaga);
  yield takeLatest(updateGrowthBook.toString(), updateGrowthBookSaga);
  yield takeLatest(setGrowthBookFeatures.toString(), setGrowthBookFeaturesSaga);
}

const growthBookTask = fork(growthBookQueue);
export default growthBookTask;
