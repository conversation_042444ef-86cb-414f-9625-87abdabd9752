import { Map } from 'immutable';
import { get, startsWith } from 'lodash';
import Config from 'react-native-config';
import { call, delay, fork, put, race, select, takeEvery, takeLatest } from 'redux-saga/effects';
import { graphQLClient, graphqlQueryRequest, setGraphQLClient } from './../utils/graphqlQueryRequest';

import { syncActivationStatus, toggleLoadingMask, toggleToastInfo } from '../actions';
import * as http from '../actions/http';
import { FetchOption, requests } from '../actions/http/setup';
import { callApiAsync } from '../utils/api';

import CryptoJS from 'crypto-js';
import moment from 'moment';
import { CORE_API_URL, POS_BFF_API_URL } from '../config';
import { NETWORK_ERROR_PREFIX, STOREHUB_SOURCE, t } from '../constants';
import * as NavigationService from '../navigation/navigatorService';
import DeviceInfoData from '../utils/deviceInfo';

import { TrackApiResponseTimeConfigType } from '../utils/growthbook';
import { HTTPAction, LoggingLevel, logHttpEvent, LogResult } from '../utils/logComponent';
import { selectGBTrackApiResponseTime, selectStoreInfoUnsafe } from './selector';

import { isEmpty } from '../utils/validator';
import { getResponseCache, setResponseCache } from './throttle';

type entity = {
  fetchArgs: (...args: any[]) => any;
  // eslint-disable-next-line @typescript-eslint/no-unsafe-function-type
  success: Function;
  // eslint-disable-next-line @typescript-eslint/no-unsafe-function-type
  failure: Function;
  disableLoadingMask: boolean;
  disableInteractions?: boolean;
  disableErrorInfo: boolean;
  errorMessage?: string;
  timeout?: number;
  name?: string;
  isEWalletPay?: boolean;
  timeoutMessage?: string;
  cancelMessage?: string;
  cacheResponse?: number; // ms
};

type responseType = {
  ok: boolean;
  status?: number;
  message?: string;
  res?: any;
  headers?;
  editableTimeAfterForPayLaterOrder?: string;
  errCode?: string;
};

type api = [string, any];

const shouldDisableSpinny = function (entity: entity, payload: any) {
  return entity.disableLoadingMask || (payload && (payload.disableLoadingMask === true || payload.refreshing === true));
};

const getSortedJsonKeys = function (inputJSON: any) {
  return Object.keys(inputJSON).sort();
};

const sortJsonByKeys = function (inputJSON: any) {
  const returnedJson: any = {};
  const sortedJsonkeys = getSortedJsonKeys(inputJSON);
  for (const key of sortedJsonkeys) {
    returnedJson[key] = inputJSON[key];
  }

  return returnedJson;
};

// {dailySalesReport(businessName:"kelip2usj18"storeId:"5fbdcb9d90de1c0006971cff"){reportDatesalesByTransactionChannel{channelqtyamount}}}  ->  dailySalesReport
// {beepPauseModeSettings(businessName:"thekoreaterrace",storeId:"667a285b1c6f370007d7fa09"){pausedpausedUntil}}  ->  beepPauseModeSettings
// mutationcreateShareInfoRequest($merchantName:String!$registerId:String$storeId:String){createShareInfoRequest(merchantName:$merchantNameregisterId:$registerIdstoreId:$storeId){idQRCode}} -> createShareInfoRequest
// queryshareInfoRequest($id:ID!){shareInfoRequest(id:$id){idmerchantNameregisterIdexpiredDatescannedDatesourceconsumerIdcustomer{idcustomerIdfirstNamelastNameemailphonestreet1street2citystatepostalCodemodifiedTimecreatedTimetagsbirthdaymemberIdtaxIdNototalSpentlastPurchaseDatetotalTransactionsstoreCreditsBalance}sharedInfoDateisNewCustomerisNewMember}} -> shareInfoRequest
// mutationgenerateInvoiceSequenceNumber($receiptNumber:String!$businessName:String!$registerId:String!$registerNumber:Int!$invoiceSeqNumber:Int!$sequenceNumber:Int!$storeId:String!){generateInvoiceSequenceNumber(receiptNumber:$receiptNumber,businessName:$businessName,registerId:$registerId,registerNumber:$registerNumberinvoiceSeqNumber:$invoiceSeqNumber,sequenceNumber:$sequenceNumber,storeId:$storeId,){versionstatusisCancelledcancelledDatechannelregisterIdshippingTypeisPayLatertotalsubtotaltaxdiscountshippingFeeshippingFeeDiscounttakeawayChargesstoreIdroundedAmountconsumerIdcustomerIdsessionIdcreatedTimemodifiedTimereceiptNumbertransactionIdexpectDeliveryDateFromexpectDeliveryDateTosmallOrderFeeseniorsCountpwdCountheadCounttotalDeductedTaxtaxableSalestaxExemptedSaleszeroRatedSalesseniorDiscountcontainerFeepwdDiscountregisterNumberinvoiceSeqNumbersequenceNumberseqNumberCreatedTimeprintKitchenDocketprintReceiptcalculation{discounts{typediscountdeductedTaxsubTypepromotionId}original{taxsubtotaltotal}}display{subtotaldiscountserviceChargetaxtotal}contactDetail{emailnamephone}promotions{promotionIddiscounttaxtaxCodepromotionCodepromotionNamediscountTypeshippingFeeDiscount}tableIdpickUpIdotherReasondisplayDiscountpaymentMethodserviceChargetransactionTypeserviceChargeRateserviceChargeTaxcreatedVoucherCodescommentloyaltyDiscounts{loyaltyTypedisplayDiscountspentValue}subOrders{commentssubmitIdsubmittedTimeisPrintedprintedTimesubmittedBysubmittedByPhonesubmittedFrom}items{idsubmitIditemTypediscountquantitydiscountTypediscountValueproductIdtaxtaxableAmounttaxExemptAmountzeroRatedSalestotalDeductedTaxseniorDiscountpwdDiscountathleteAndCoachDiscountmedalOfValorDiscounttaxCodetaxRatetotaltitlesubTotalisTakeawaytakeawayChargecalculation{fullPricediscounts{typediscountdeductedTaxsubTypepromotionId}original{taxsubtotaltotal}}display{subtotaltotaltax}unitPricenotespromotions{promotionIddiscounttaxtaxCodepromotionCodepromotionNamediscountTypeshippingFeeDiscount}manualDiscount{typeinputValue}selectedOptions{variationIdoptionIdoptionValuequantity}}payments{amountpaymentMethodroundedAmountisOnlinepaymentGatewaycardName}deliveryInformation{shippingFeecommentsaddress{namephoneaddresscityaddressDetailspostCodestatecountry}}appliedVoucher{voucherIdvoucherCodevaluecostpurchaseChannelcoveredBySH}receipt{totalcontainerFeesmallOrderFeediscount}}} -> generateInvoiceSequenceNumber
// mutationregister($deviceToken:String!,$storeId:String!,$registerId:String!$merchantName:String!){register(deviceToken:$deviceToken,storeId:$storeIdregisterId:$registerIdmerchantName:$merchantNamepushPlatform:RN_ANDROID){success}} -> register
// querytestV2($id:ID!){testV2(id:$id){idmerchantNameregisterIdexpiredDatescannedDatesourceconsumerIdcustomer{idcustomerIdfirstNamelastNameemailphonestreet1street2citystatepostalCodemodifiedTimecreatedTimetagsbirthdaymemberIdtaxIdNototalSpentlastPurchaseDatetotalTransactionsstoreCreditsBalance}sharedInfoDateisNewCustomerisNewMember}} -> testV2
const getRequestPath = (requestBody: string) => {
  // 从传入的requestBody中提取出请求的路径
  const reg = /{(.+?)\(/;
  const result = reg.exec(requestBody);
  if (result) {
    return result[1];
  }
  return '';
};

const getAuthHeader = (body: any) => {
  const key = 'AAIK3KN1C3500';
  const clientSecret = 'KJNY0ASZIC19LE13AK34333XK530K';

  const salt = `${Math.floor(Date.now() / 30000) * 30000}`; // Every 5 minutes milliseconds time stamp

  const secret = `${clientSecret}.${salt}`;

  const sortedRequestBodyString = JSON.stringify(sortJsonByKeys(body));
  const hashedBodyString = CryptoJS.HmacSHA256(sortedRequestBodyString, secret).toString().toUpperCase();

  return `${key}:${hashedBodyString}`;
};

export const appendCommonHeader = function* (fetchOptions) {
  if (fetchOptions.excludedAuthToken) {
    fetchOptions.headers = {
      ...fetchOptions.headers,
      'storehub-version': Config.Storehub_App_Version,
    };
    return;
  }
  const immutableStoreInfo = yield select((state: Map<string, any>) => state.getIn(['Storage', 'storeInfo']));
  if (immutableStoreInfo === undefined) return;

  if (fetchOptions.includeAuthorization) {
    fetchOptions.headers = {
      ...fetchOptions.headers,
      'storehub-version': Config.Storehub_App_Version,
      'storehub-business': immutableStoreInfo.get('name'),
      'storehub-registerid': immutableStoreInfo.get('registerObjectId'),
      authorization: getAuthHeader(fetchOptions.body),
    };
    return;
  }
  // eslint-disable-next-line require-atomic-updates
  fetchOptions.headers = {
    ...fetchOptions.headers,
    'storehub-version': Config.Storehub_App_Version,
    'storehub-business': immutableStoreInfo.get('name'),
    'storehub-registerid': immutableStoreInfo.get('registerObjectId'),
    'storehub-token': immutableStoreInfo.get('apiToken'),
  };
};

const appendGraphQLHeader = function* (fetchOptions: FetchOption) {
  const storeInfo = yield select(selectStoreInfoUnsafe);
  if (!Boolean(storeInfo)) {
    return;
  }

  const headers = {
    'storehub-business': storeInfo.get('name'),
    'storehub-registerid': storeInfo.get('registerObjectId'),
    'storehub-token': storeInfo.get('apiToken'),
    'storehub-version': String(DeviceInfoData.version),
    'storehub-source': STOREHUB_SOURCE,
  };

  let currentEmployeeId;
  if (fetchOptions.requireEmployeeId !== false) {
    currentEmployeeId = yield select((state: Map<string, any>) => state.getIn(['CurrentEmployee', 'employeeId']));
    // If no employee has logined in, all authorization requests should be failed in this case.
    if (!Boolean(currentEmployeeId)) {
      return;
    }
    headers['storehub-access-control'] = 'enable';
    headers['storehub-employeeid'] = currentEmployeeId;
  } else {
    headers['storehub-access-control'] = 'disable';
    headers['storehub-employeeid'] = 'default'; // Don't remove it because AWS GateWay need this header.
  }

  graphQLClient.setHeaders(headers);
};

const appendThirdMiddleWareHeader = fetchOptions => {
  fetchOptions.headers = {
    Accept: 'application/json',
    Authorization: 'Basic YWRtaW46czJyaHViJEFkbWlu',
  };
};

const logAPIRequestResult = function* ({ fetchUrl, fetchOptions, responseTime, response }) {
  let url = fetchUrl;
  let requestBody = '';
  let gqlMessage = '';
  if (fetchOptions.isGraphQL) {
    if (fetchOptions.isNewGraphQL) {
      url = POS_BFF_API_URL();
    } else {
      url = CORE_API_URL();
    }
    gqlMessage = get(response, 'gqlMessage', '');
  }
  let requestPath = '';
  if (!isEmpty(fetchOptions.body)) {
    if (typeof fetchOptions.body === 'string') {
      requestBody = fetchOptions.body.replace(/\s+/g, '');
      requestPath = getRequestPath(requestBody);
    } else {
      requestBody = JSON.stringify(fetchOptions.body);
    }
  }

  const isSuccess = response.ok;
  logHttpEvent({
    action: HTTPAction.HTTP_REQUEST,
    level: isSuccess ? LoggingLevel.Info : LoggingLevel.Warn,
    result: isSuccess ? LogResult.Succeed : LogResult.Failed,
    url,
    method: fetchOptions.method,
    responseTime,
    httpStatusCode: response.status,
    errorCode: String(response.errCode || ''),
    message: response.message,
    requestBody,
    requestName: fetchOptions.name,
    isGraphQL: get(fetchOptions, 'isGraphQL', false),
    requestPath,
    gqlMessage,
  });
};

export const fetchEntity = function* (entity: entity, { payload }: any) {
  const [fetchUrl, fetchOptions, variables] = yield call(entity.fetchArgs, payload);
  if (fetchOptions.isGraphQL) {
    if (fetchOptions.isNewGraphQL) {
      setGraphQLClient(POS_BFF_API_URL());
    } else {
      setGraphQLClient(CORE_API_URL());
    }
    yield call(appendGraphQLHeader, fetchOptions);
  } else if (fetchOptions.isThirdMiddleWare) {
    appendThirdMiddleWareHeader(fetchOptions);
  } else {
    yield call(appendCommonHeader, fetchOptions);
  }
  const disableLoadingMask = shouldDisableSpinny(entity, payload);
  const { isEWalletPay, timeoutMessage } = entity;
  const disableErrorInfo = entity.disableErrorInfo || (payload && payload.disableErrorInfo === true);
  if (!disableLoadingMask) {
    const disableInteractions = entity.disableInteractions === false ? false : true;
    yield put(toggleLoadingMask({ visible: true, disableInteractions }));
  }

  // cache response
  const cacheResponse = entity.cacheResponse && entity.cacheResponse > 0;
  if (cacheResponse) {
    const responseCache = getResponseCache(
      {
        fetchUrl,
        fetchOptions,
        variables,
      },
      entity.cacheResponse
    );
    console.log('cacheResponse cache', {
      fetchUrl,
      fetchOptions,
      variables,
    });
    if (responseCache) {
      yield put(entity.success(responseCache));
      console.log('cacheResponse cache success', fetchUrl);
      return;
    }
    console.log('cacheResponse cache failure', fetchUrl);
  }

  // fetch API has no client side connection timeout support
  const timeout = entity.timeout ? entity.timeout : 60 * 1000;
  const requestTimeStart = Date.now();
  const result = yield race({
    io: call(fetchOptions.isGraphQL ? graphqlQueryRequest : callApiAsync, fetchUrl, fetchOptions, variables), // Do real fetch thing.
    timeout: delay(timeout),
  });

  if (!disableLoadingMask) {
    yield put(toggleLoadingMask({ visible: false }));
  }

  let response = {} as responseType;
  if (result.timeout) {
    // timeout
    response = { ok: false, message: timeoutMessage || 'fetch timeout' };
  } else {
    // fetch result
    response = result.io;
  }
  const requestTimeEnd = Date.now();
  const requestTime = requestTimeEnd - requestTimeStart;
  if (response.ok) {
    const successResponse = Object.assign({}, Array.isArray(response.res) ? { asArray: response.res } : response.res, get(payload, ['onSuccess', 'payload']));

    // Dispatch http success action.
    if (cacheResponse) {
      setResponseCache(
        {
          fetchUrl,
          fetchOptions,
          variables,
        },
        successResponse
      );
    }
    yield put(entity.success(successResponse));
    if (payload && payload.onSuccess && typeof payload.onSuccess.callback === 'function') {
      const successPayload = Object.assign({}, response, get(payload, ['onSuccess', 'payload']));
      successPayload.fetchUrl = fetchUrl;
      // Call onSuccess.callback.
      payload.onSuccess.callback(successPayload);
    }

    // Log API response time
    const trackApiResponseTime = (yield select(selectGBTrackApiResponseTime)) as TrackApiResponseTimeConfigType;
    const { enabled, responseTimeThreshold } = trackApiResponseTime;
    const shouldLogResponseTime = requestTime >= responseTimeThreshold;
    if (enabled && shouldLogResponseTime) {
      // console.log('api response time:', requestTime);
      yield call(logAPIRequestResult, { fetchUrl, fetchOptions, responseTime: requestTime, response });
    }
  } else {
    // Failed on auth token
    if (response.status === 403 && !isEWalletPay) {
      yield put(syncActivationStatus({}));
    }

    // Log failed API request
    yield call(logAPIRequestResult, { fetchUrl, fetchOptions, responseTime: requestTime, response });

    const failurePayload = Object.assign({}, response, payload && payload.onFailure && payload.onFailure.payload);
    // Dispatch http failure action.
    yield put(entity.failure(failurePayload));
    if (!disableErrorInfo && response.status !== 502 && (Boolean(entity.errorMessage) || response.message || response.editableTimeAfterForPayLaterOrder)) {
      if (response.editableTimeAfterForPayLaterOrder) {
        const editableTimeAfterForPayLaterOrder = moment(response.editableTimeAfterForPayLaterOrder).format('hh:mmA');
        NavigationService.navigate({
          routeName: 'ModalInfo',
          params: {
            title: t('This order is being checked out by customer'),
            isShowTitle: true,
            textAlign: 'center',
            info: t('Order can be edited at', { editableTimeAfterForPayLaterOrder }),
          },
        });
      } else {
        const errorMessage = Boolean(entity.errorMessage) ? entity.errorMessage : response.message;
        if (!startsWith(errorMessage, NETWORK_ERROR_PREFIX)) {
          yield put(toggleToastInfo({ visible: true, text: errorMessage }));
        }
      }
    }
    if (payload && payload.onFailure && typeof payload.onFailure.callback === 'function') {
      // Call onFaiulre.callback
      payload.onFailure.callback(failurePayload);
    }
  }
};

//  Reduce to only HTTP actions, then setup a saga for each api.
const sagas = Object.entries(http)
  .filter(([apiname, _]: api) => requests[apiname] !== undefined) // eslint-disable-line @typescript-eslint/no-unused-vars
  .map(([apiname, apifn]: api) => {
    const apiSaga = fetchEntity.bind(null, requests[apiname]);
    const { needTakeEvery } = requests[apiname];
    if (needTakeEvery) {
      return function* () {
        yield takeEvery(apifn.toString(), apiSaga);
      };
    } else {
      return function* () {
        yield takeLatest(apifn.toString(), apiSaga);
      };
    }
  });

export default sagas.map(fn => fork(fn));
