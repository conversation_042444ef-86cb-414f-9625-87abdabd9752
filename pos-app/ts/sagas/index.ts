import { all, fork } from 'redux-saga/effects';
import aiChatSaga from './aiChatSaga';
import appState from './appState';
import authorization from './authorization';
import autoUpdate from './autoUpdate';
import beepQRTask from './beepQR';
import cfd from './cfd';
import cfdTask from './cfd/index';
import customerTask from './customer';
import dataSync from './dataSync';
import employeeActivity from './employeeActivity';
import fixData from './fixData';
import geo from './geo';
import growthBookTask from './growthbook';
import http from './http';
import initial from './initital';
import kdsTask from './kds';
import mrs from './mrs/index';
import navigator from './navigator';
import ncsTask from './ncs';
import pauseMode from './pauseMode';
import printer from './printing';
import quickLayout from './quickLayout';
import s3Task from './s3';
import { ShiftFlow } from './shift/index';
import tableOrdering from './tableOrdering';
import timezone from './timezone';
import toast from './toast';
import {
  cancelFlow,
  eInvoiceSaga,
  ewalletPaySaga,
  ghlSaga,
  loyaltyFlow,
  manualReturnFlow,
  openOrderFlow,
  preOrderSaga,
  promitionSaga,
  refundFlow,
  saleFlowSaga,
} from './transaction/index';
import websocketTask from './websocket';

import mallIntegration from './mallIntegration';
import printing2 from './printing2';
import autoDeploy from './autoDeploy';
import testing from './testing';

export default function* () {
  yield toast;
  yield all(http);
  yield dataSync;
  yield cancelFlow;
  yield loyaltyFlow;
  yield manualReturnFlow;
  yield openOrderFlow;
  yield refundFlow;
  yield saleFlowSaga;
  yield ShiftFlow;
  yield printer;
  yield cfd;
  yield autoUpdate;
  yield initial;
  yield timezone;
  yield authorization;
  yield geo;
  yield quickLayout;
  yield employeeActivity;
  yield fixData;
  yield appState;
  yield tableOrdering;
  yield ewalletPaySaga;
  yield preOrderSaga;
  yield navigator;
  yield pauseMode;
  yield mrs;
  yield customerTask;
  yield growthBookTask;
  yield websocketTask;
  yield kdsTask;
  yield ncsTask;
  yield cfdTask;
  yield promitionSaga;
  yield beepQRTask;
  yield s3Task;
  yield mallIntegration;
  yield ghlSaga;
  yield eInvoiceSaga;
  yield printing2;
  yield autoDeploy;
  yield testing;
  yield fork(aiChatSaga);
}
