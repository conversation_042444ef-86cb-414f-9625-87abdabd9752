import * as Immutable from 'immutable';
import { get } from 'lodash';
import DeviceInfo from 'react-native-device-info';
import RNFS from 'react-native-fs';
import { REHYDRATE } from 'redux-persist';
import { all, call, fork, put, select, takeLatest } from 'redux-saga/effects';
import { StoreHubIminLcd } from '../../libs/storehub-imin-lcd';
import { StoreHubSunmiService } from '../../libs/storehub-sunmi-service';
import * as Actions from '../actions';
import {
  getCurrentShiftStatus,
  initialNetworkQualityMonitoringSaga,
  removeCreatedTime,
  setCurrentDevice,
  SetSequenceType,
  setSyncInfo,
  setVersion,
  syncLargeProductThumbnails,
  syncSequentialStartInfoBegin,
  updatePrinterGeneralSettings,
  updatePrinterTagsSettings,
} from '../actions';
import { updateCookingStatusType } from '../actions/kds';
import PaymentOptions from '../config/paymentOption';
import { IsAndroid, IsIOS } from '../constants';
import DAL from '../dal';
import RealmManager from '../dal/realm';
import { isProductThumbnailUpToDate, s3DownloadThumbnail } from '../utils/awsS3Helper';
import DeviceInfoData from '../utils/deviceInfo';
import { LocalLoggingRemoteConfig, NewProductSyncFlowType } from '../utils/growthbook';
import { CookingStatusType } from '../utils/kds/status';
import { infoPOSBasicEvent, initBasicFileds, initMobileData, LoggingLevel, POSBasicAction, PrinterAction, trackLogEvent } from '../utils/logComponent';
import LocalLogger from '../utils/logComponent/local-logging/LocalLogger';
import AnrMonitor from '../utils/monitor';
import SHPeripheralManager from '../utils/peripheral';
import { getPushyVersion } from '../utils/pushy';
import { compareVersions } from '../utils/string';
import { forceDeactivation } from './dataSync';
import { initMRSStateSaga } from './mrs/roles/master';
import {
  selectAlwaysPrintReceipt,
  selectAuthToken,
  selectBusinessName,
  selectCountry,
  selectDeviceFirmwareVersion,
  selectDeviceSerialNumber,
  selectEmployeeId,
  selectEnableCustomerShortCut,
  selectLastProductSyncTime,
  selectLocalLoggingConfig,
  selectMerchantHasPrinter,
  selectNewProductSyncFlow,
  selectPrinterTagsSettings,
  selectQuickLayout,
  selectRegisterId,
  selectRegisterObjectId,
  selectSettings,
  selectStoreId,
  selectStoreInfo,
} from './selector';
import { cleanupPrintingRecordsSaga } from './storageCleanup';

export const initialSaga = function* (action) {
  yield call(initialRealm, action);
  yield call(initialMerchantHasPrinterSaga, action);
  yield call(initialLogNormalField, action);
  yield call(initialMigrationField, action);
  yield call(initialMRS);
  yield put(Actions.initGrowthBook());
  yield all([call(initialPaymentOption, action), call(initialVersion, action), call(initialDevice, action), call(initialFixData, action)]);
  yield fork(checkProductImages, action);
  yield fork(initialNetworkQualityMonitoringSaga, action);

  yield put(Actions.cleanLocalStorage(true));

  yield call(checkIfFromIcloudBackup);
};

export function* initialMRS() {
  yield call(initMRSStateSaga);
}

// eslint-disable-next-line require-yield
export const initialPaymentOption = function* (action) {
  const Storage = get(action, ['payload', 'Storage']);
  if (!Boolean(Storage)) {
    return;
  }
  const paymentOptions = Storage.getIn(['storeInfo', 'store', 'paymentOptions']);
  const storeId = Storage.getIn(['storeInfo', 'store', '_id']);
  PaymentOptions.updatePaymentOptions(paymentOptions, storeId);
};

// eslint-disable-next-line require-yield
export const initialLogNormalField = function* (action) {
  const Storage = get(action, ['payload', 'Storage']);
  if (!Boolean(Storage)) {
    return;
  }
  yield call(initialLogNormalFieldsSaga);
  infoPOSBasicEvent({ action: POSBasicAction.LAUNCH });
};

// eslint-disable-next-line require-yield
export const initialMigrationField = function* (action) {
  const Settings = get(action, ['payload', 'Settings']);
  if (!Boolean(Settings)) {
    return;
  }
  const alwaysPrintReceipt = yield select(selectAlwaysPrintReceipt);
  const alwaysPrintReceiptWithBeepQRPayOnline = Settings.getIn(['printerGeneralSettings', 'alwaysPrintReceiptWithBeepQRPayOnline']);
  const alwaysPrintReceiptWithBeepQRPayAtCounter = Settings.getIn(['printerGeneralSettings', 'alwaysPrintReceiptWithBeepQRPayAtCounter']);

  if (alwaysPrintReceipt && alwaysPrintReceiptWithBeepQRPayOnline === undefined && alwaysPrintReceiptWithBeepQRPayAtCounter === undefined) {
    yield put(
      updatePrinterGeneralSettings({
        alwaysPrintReceiptWithBeepQRPayOnline: true,
        alwaysPrintReceiptWithBeepQRPayAtCounter: true,
      })
    );
  }

  // CM-4126 DOUBLE as default strategy and can not be edited by user
  yield put(updateCookingStatusType(CookingStatusType.DOUBLE));
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const initialRealm = function* (action) {
  const immutableStoreInfo = yield select(selectStoreInfo);
  RealmManager.setRealmInstanceWithStore(immutableStoreInfo);
  // make sure when getCurrentShiftStatus realm is initialized.
  yield put(getCurrentShiftStatus());
  yield call(cleanupPrintingRecordsSaga);
};

export const initialVersion = function* (action) {
  const Storage = get(action, ['payload', 'Storage']);
  if (!Boolean(Storage)) {
    return;
  }
  const preVersion = Storage.get('version');
  const version = DeviceInfoData.version;
  if (preVersion && compareVersions(preVersion, '1.86.0') < 0) {
    // CM-8687, default true for existing merchants that < 1.86.0
    yield put(Actions.updatePrinterGeneralSettings({ alwaysPrintReceiptWithOnlineDelivery: true }));
  }
  if (!Boolean(preVersion) || version !== preVersion) {
    yield call(reduxStorageMigration, preVersion, version);
    // TODO: will apply this in the feature version
    // yield call(initCustomerQR);
    yield call(enableAutoSyncAfterUpgrade, preVersion, version);
    yield put(setVersion(version));
    yield put(setSyncInfo({ needToSyncAll: true }));
  }
};

export const reduxStorageMigration = function* (preVersion, currentVersion) {
  // Need to clean the old cache since 1.8.0 changed the printer settings struct
  if (currentVersion === '1.8.0') {
    console.log('reduxStoreageMigration');
    trackLogEvent({
      action: PrinterAction.RefreshPrinter,
      level: LoggingLevel.Warn,
      mobileData: {
        reason: 'set printers empty',
      },
      privateDataPayload: {
        source: 'reduxStorageMigration',
      },
    });
    yield put(updatePrinterTagsSettings([]));
  }

  if (currentVersion === '1.49.0') {
    // The business account created time was save at twice place. remove this redundant one.
    yield put(removeCreatedTime());
  }
};

/**
 * init release customer QR feature
 * CM-2555
 */
export const initCustomerQR = function* () {
  const enableCustomerShortCut = yield select(selectEnableCustomerShortCut);
  yield put(
    Actions.updateGeneralSettings({
      enableCustomerQR: enableCustomerShortCut,
    })
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const initialDevice = function* (action) {
  const model = DeviceInfoData.deviceModel;
  const version = DeviceInfoData.readableVersion;
  const platform = IsAndroid ? 'Android' : 'RN-iOS';
  const systemVersion = DeviceInfoData.systemVersion;
  const firmwareVersion = yield call(getFirmwareVersion);
  const sn = yield call(getSerialNumber);

  yield put(setCurrentDevice({ platform, model, version, systemVersion, firmwareVersion, sn }));
};

function* getSerialNumber() {
  let sn;
  if (IsAndroid) {
    const isMin = yield call(StoreHubIminLcd.isIMinDevice);
    console.log('isIMinDevice', isMin);
    if (isMin) {
      sn = yield call(StoreHubIminLcd.getDeviceSn);
      console.log('iMin_getDeviceSn', sn);
      if (!sn) {
        sn = yield call(StoreHubSunmiService.getDeviceSnFromSystem);
        console.log('iMin_system', sn);
      }
    } else {
      sn = yield call(StoreHubSunmiService.getDeviceSnFromSystem);
      console.log('system', sn);
      if (!sn && isSunMinDevice()) {
        sn = yield call(StoreHubSunmiService.getDeviceSn);
        console.log('sunMin', sn);
      }
    }
  } else {
    sn = DeviceInfo.getSerialNumberSync();
    console.log('iOS', sn);
  }

  if (sn && sn !== 'unknown') {
    return sn;
  } else {
    return 'N/A';
  }
}

function* getFirmwareVersion() {
  let version;
  if (isSunMinDevice()) {
    version = yield call(StoreHubSunmiService.getDeviceFirmwareVersion);
  }
  return version || DeviceInfo.getDisplaySync();
}

function isSunMinDevice() {
  return DeviceInfoData.brand === 'SUNMI';
}

export const initialFixData = function* (action) {
  const Storage = get(action, ['payload', 'Storage']);
  if (!Boolean(Storage)) {
    return;
  }

  const sequentialReceiptNumber = Storage.getIn(['storeInfo', 'sequentialReceiptNumber']);
  if (sequentialReceiptNumber) {
    const immutableSequence = Storage.get('sequence', Immutable.Map());
    const { receiptNumberStart, receiptDateStart }: SetSequenceType = immutableSequence.toJS();
    const transactions = DAL.getTransactionsWithEmptyReceiptNumber();
    const hasEmptyReceiptNumberTranasaction = transactions && transactions.length > 0;
    const needFixsequential = Boolean(!receiptNumberStart || !receiptDateStart || hasEmptyReceiptNumberTranasaction);
    yield put(setSyncInfo({ needFixsequential }));
    if (needFixsequential) {
      yield put(syncSequentialStartInfoBegin({}));
    }
  } else {
    yield put(setSyncInfo({ needFixsequential: false }));
  }
};

export const checkProductImages = function* (action) {
  console.warn('product thumbnail', 'prepared');
  const Storage = get(action, ['payload', 'Storage']);
  if (!Boolean(Storage)) {
    console.warn('product thumbnail', 'stopped no storage');
    return;
  }

  const storeName = yield select(selectBusinessName);
  if (!Boolean(storeName)) {
    console.warn('product thumbnail', 'stopped no store');
    return;
  }

  const lastProductSyncTime = yield select(selectLastProductSyncTime);
  if (!lastProductSyncTime) {
    console.warn('product thumbnail', 'stopped no last sync time');
    return;
  }

  yield put(syncLargeProductThumbnails());

  // const largeProductSync = yield select(selectGBEnableLargeProductQuantitySync);
  // console.warn('product thumbnail', 'enabled', largeProductSync);
  // if (largeProductSync) {
  //   yield put(syncLargeProductThumbnails());
  //   return;
  // }
  //
  // // check the products with thumbnail if have local images, if not download them
  // const products = DAL.getProductsWithThumbnail();
  // const syncFlow: NewProductSyncFlowType = yield select(selectNewProductSyncFlow);
  // if (products && products.length > 0) {
  //   for (const product of products) {
  //     if (Boolean(product) && Boolean(product.productId)) {
  //       const productId = product.productId;
  //       const isExist = yield call(isProductThumbnailExist, productId, syncFlow.thumbnailThreshold);
  //       if (isExist) {
  //         continue;
  //       }
  //       const thumbnail = yield call(s3DownloadThumbnail, storeName, productId);
  //       console.warn('thumbnail downloaded', thumbnail);
  //     }
  //   }
  // }
};

// export const downloadProductThumbnails = function* (
//   products: {
//     _id: string;
//   }[]
// ) {
//   const storeName = yield select(selectBusinessName);
//   const hasProducts = products && products.length > 0;
//   if (!hasProducts) {
//     return;
//   }
//   yield put(
//     Actions.setSyncInfo({
//       lastProductImagesSyncCount: products.length,
//     })
//   );
//
//   for (const productJson of products) {
//     if (productJson && productJson._id) {
//       const product = DAL.getProductById(productJson._id);
//       const productId = product.productId;
//       if (!product.hasThumbnail) {
//         yield call(deleteThumbnailPromise, productId);
//         continue;
//       }
//       const isExist = yield call(isProductThumbnailUpToDate, productId, product.lastUpdateThumbnail.getTime());
//       if (isExist) {
//         continue;
//       }
//       yield call(s3DownloadThumbnail, storeName, productId);
//     }
//   }
//
//   yield put(
//     Actions.setSyncInfo({
//       lastProductImagesSyncCount: 0,
//     })
//   );
// };

export const syncLargeProductThumbnailsSaga = function* (action) {
  // quick layout thumbnails
  const syncFlow: NewProductSyncFlowType = yield select(selectNewProductSyncFlow);
  const quickLayout = (yield select(selectQuickLayout)).toJS();
  const hasQuickLayout = quickLayout && quickLayout.length > 0;
  if (hasQuickLayout) {
    let total = 0;
    let added = 0;
    for (const layout of quickLayout) {
      const storeName = yield select(selectBusinessName);
      if (!storeName) break;
      if (layout && layout.items) {
        for (const item of layout.items) {
          if (item && item.productId) {
            const productId = item.productId;
            const product = DAL.getProductById(item.productId);
            if (product && product.hasThumbnail) {
              const isExist = yield call(isProductThumbnailUpToDate, productId, product.lastUpdateThumbnail.getTime(), syncFlow.thumbnailThreshold);
              if (isExist) {
                continue;
              }
              total++;
              const storeName = yield select(selectBusinessName);
              if (!storeName) break;
              const thumbnail = yield call(s3DownloadThumbnail, storeName, productId);
              if (thumbnail && thumbnail.status) {
                added++;
              }
              console.warn('quick layout thumbnail', 'downloaded', thumbnail);
            }
          }
        }
        yield put(
          Actions.setSyncInfo({
            lastProductImagesSyncCount: total,
            lastProductImagesSyncProgress: added,
          })
        );
      }
    }
    yield put(
      Actions.setSyncInfo({
        lastProductImagesSyncCount: 0,
        lastProductImagesSyncProgress: 0,
      })
    );
  }

  // db thumbnails
  const products = DAL.getProductsWithThumbnail();
  const hasProducts = products && products.length > 0;
  yield put(
    Actions.setSyncInfo({
      lastProductImagesSyncCount: hasProducts ? products.length : 0,
      lastProductImagesSyncProgress: 0,
      lastProductImagesSyncFailedProgress: 0,
    })
  );

  let synced = 0;
  let downed = 0;
  let failed = 0;

  const startTime = Date.now();
  if (hasProducts) {
    const syncFlow: NewProductSyncFlowType = yield select(selectNewProductSyncFlow);
    for (const product of products) {
      synced++;
      if (product && product.productId) {
        const productId = product.productId;
        const isExist = yield call(isProductThumbnailUpToDate, productId, product.lastUpdateThumbnail.getTime(), syncFlow.thumbnailThreshold);
        if (isExist) {
          continue;
        }
        const storeName = yield select(selectBusinessName);
        if (!storeName) break;
        const thumbnail = yield call(s3DownloadThumbnail, storeName, productId);

        if (!thumbnail || !thumbnail.status) {
          failed++;
        } else {
          downed++;
        }
        // console.warn('product thumbnail', 'downloaded', thumbnail);
      }
      if (downed % 25 === 0) {
        yield put(
          Actions.setSyncInfo({
            lastProductImagesSyncProgress: synced,
            lastProductImagesSyncFailedProgress: failed,
          })
        );
      }
    }
  }

  // console.log('product thumbnail sync time', products.length, Date.now() - startTime);
  yield put(
    Actions.setSyncInfo({
      lastProductImagesSyncCount: 0,
      lastProductImagesSyncProgress: products.length,
    })
  );

  yield call(deleteUnusedProductThumbnailsSaga, action);
};

export const deleteUnusedProductThumbnailsSaga = function* (action) {
  try {
    const products = DAL.getProductsWithThumbnail();
    const validProductIds = new Set(products.map(p => p.productId));
    const files = yield call([RNFS, RNFS.readDir], RNFS.DocumentDirectoryPath);

    for (const file of files) {
      if (file.isFile() && file.name.endsWith('.png')) {
        const productIdFromFile = file.name.replace('.png', '');
        if (!validProductIds.has(productIdFromFile)) {
          yield call(RNFS.unlink, file.path);
          console.log(`Deleted unused image: ${file.name}`);
        }
      }
    }
  } catch (error) {
    console.error('Error cleaning product images:', error);
  }
};

export let LogNormarField = {} as Record<string, any>;
export let merchantHasPrinter = true;

export const initialLogNormalFieldsSaga = function* () {
  const storeId = yield select(selectStoreId);
  const business = yield select(selectBusinessName);
  const registerId = yield select(selectRegisterObjectId);
  const registerNumber = yield select(selectRegisterId);
  const businessCountry = yield select(selectCountry);
  const version = DeviceInfoData.version;
  const appName = DeviceInfoData.appName;
  const appId = DeviceInfoData.appId;
  const osVersion = DeviceInfoData.systemVersion;
  // Use unique id instead of device id, since the later one is restricted to obtain since Android 10.
  const deviceId = DeviceInfoData.uniqueId;
  const deviceModel = DeviceInfoData.deviceModel;
  const buildNumber = yield select(selectDeviceFirmwareVersion);
  const platform = IsAndroid ? 'RN-Android' : 'RN-iOS';
  const project = 'posV3Mobile';
  const sn = yield select(selectDeviceSerialNumber);
  const employeeId = yield select(selectEmployeeId);

  // initial log event basic fiedls and mobile data, these functions need to be called ASAP
  initBasicFileds({ platform, project, deviceId, business });
  initMobileData({
    appId,
    appName,
    version,
    osVersion,
    deviceModel,
    registerId,
    storeId,
    businessCountry,
    sn,
    registerNumber: String(registerNumber),
    buildNumber,
  });

  LogNormarField = {
    appName,
    appId,
    project,
    storeId,
    business,
    registerId,
    deviceId,
    deviceModel,
    version,
    osVersion,
    platform,
    businessCountry,
    registerNumber,
    buildNumber,
  };
  const settings = yield select(selectSettings);
  const hotfixLabel = yield call(getPushyVersion);
  infoPOSBasicEvent({ action: POSBasicAction.Init, privateDataPayload: { hotfixLabel, settings: settings.toJS() } });
  SHPeripheralManager.initLogHeader(LogNormarField);
  const localLoggingConfig: LocalLoggingRemoteConfig = yield select(selectLocalLoggingConfig);
  if (localLoggingConfig.enabled) {
    yield call([LocalLogger, LocalLogger.updateLogModel], {
      storeId,
      business,
      employeeId,
      registerId,
      registerNumber,
      version,
    });
  }

  AnrMonitor.start();
  merchantHasPrinter = Boolean(yield select(selectMerchantHasPrinter));
};

export const initialMerchantHasPrinterSaga = function* (action) {
  const _merchantHasPrinter = yield select(selectMerchantHasPrinter);
  // just for first install or old version upgrade
  if (_merchantHasPrinter === null) {
    const immutablePrinters = yield select(selectPrinterTagsSettings);
    const printers = immutablePrinters.toJS();
    const everHasPrinter = get(printers, 'length', 0) > 0;
    yield put(Actions.updatePrinterGeneralSettings({ merchantHasPrinter: Boolean(everHasPrinter) }));
  }
};

const updateMerchantHasPrinter = function (action) {
  const _merchantHasPrinter = get(action, ['payload', 'merchantHasPrinter'], undefined);
  if (_merchantHasPrinter !== undefined) {
    merchantHasPrinter = Boolean(_merchantHasPrinter);
  }
};

const enableAutoSyncAfterUpgrade = function* (preVersion, currentVersion) {
  if (compareVersions(preVersion, '1.86.0') < 0 && compareVersions(currentVersion, '1.86.0') >= 0) {
    yield put(Actions.updateGeneralSettings({ autoSyncAfterOpenShift: true }));
  }
};

const checkIfFromIcloudBackup = function* () {
  if (!IsIOS) {
    return;
  }
  const authToken = yield select(selectAuthToken);
  if (!authToken) {
    yield call(forceDeactivation);
  }
};

function* initial() {
  yield takeLatest(REHYDRATE, initialSaga);
  yield takeLatest(Actions.initialLogNormalFields.toString(), initialLogNormalFieldsSaga);
  yield takeLatest(Actions.updatePrinterGeneralSettings.toString(), updateMerchantHasPrinter);
}

export default fork(initial);
