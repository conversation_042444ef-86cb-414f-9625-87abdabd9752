import { find, forEach, get, pick } from 'lodash';

import { call, select } from 'redux-saga/effects';
import { openKdsErrorToast } from '../../../components/common/TopNotification';
import { OnlineOrderStatus, OrderChannel, TransactionFlowType, t } from '../../../constants';
import DAL from '../../../dal';
import { ItemKDSInfo, PurchasedItemType, TransactionType } from '../../../typings';
import { getUnNullValue } from '../../../utils';
import { isDelivery, isPickUpOrTakeAway } from '../../../utils/beep';

import { CookingStatus, CookingStatusType, getItemDateProp, getItemInitCookingStatus, getTransactionInitCookingStatus } from '../../../utils/kds/status';
import { generateDescriptionFromOptionStr, isSameProduct } from '../../../utils/transaction';
import { confirmNcsDate } from '../../ncs/transaction/helper';
import { selectCookingStatusType, selectIsKdsPaired, selectQRRegisters, selectRegisterObjectId } from './../../selector';
import { getCookingStatusFromItems } from './update';

/**
 * restore Item KDS Status
 * @param dbTransaction
 * @param onlineTransaction
 */
export function* restoreKDSInfo(dbTransaction: TransactionType | any, onlineTransaction: TransactionType | any) {
  const isKdsPaired = yield select(selectIsKdsPaired);
  if (!isKdsPaired) {
    return;
  }
  if (!dbTransaction || !dbTransaction.cookingStatus) {
    yield call(initKdsTransactionSaga, onlineTransaction);
    return;
  }
  if (dbTransaction) {
    restoreOrderLevelKdsInfo(dbTransaction, onlineTransaction);
    const onlineItems = get(onlineTransaction, 'items', []);
    const dbItems = get(dbTransaction, 'items', []);
    forEach(onlineItems, onlineItem => {
      const itemId = get(onlineItem, 'id', '');
      const cookingStatus = get(onlineItem, 'cookingStatus', '');
      const dbItem = find(dbItems, it => it.id === itemId);
      if (itemId && !cookingStatus && dbItem && dbItem.cookingStatus) {
        onlineItem.cookingStatus = dbItem.cookingStatus;
        onlineItem.pendingDate = dbItem.pendingDate;
        onlineItem.preparedDate = dbItem.preparedDate;
        onlineItem.servedDate = dbItem.servedDate;
      }
    });
  }
}

export function restoreOrderLevelKdsInfo(dbTransaction: TransactionType, curTransaction: TransactionType) {
  // fix payAtCounter(payFirst + pay later) orders with KDS
  if (dbTransaction.registerId && !curTransaction.registerId) {
    curTransaction.registerId = dbTransaction.registerId;
  }

  if (dbTransaction.registerNumber && !curTransaction.registerNumber) {
    curTransaction.registerNumber = dbTransaction.registerNumber;
  }

  curTransaction.cookingStatusType = dbTransaction.cookingStatusType;
  curTransaction.cookingStatus = dbTransaction.cookingStatus;

  curTransaction.pushKdsDate = dbTransaction.pushKdsDate;
  curTransaction.pushNcsDate = dbTransaction.pushNcsDate;
  curTransaction.cookingEndDate = dbTransaction.cookingEndDate;

  curTransaction.servedTime = dbTransaction.servedTime;
  curTransaction.servedTimeUploaded = dbTransaction.servedTimeUploaded;
}

export function getKdsInfoFromItem(item: PurchasedItemType) {
  return pick(item, ['cookingStatus', 'pendingDate', 'preparedDate', 'servedDate', 'mergedCookingStatus']) as ItemKDSInfo;
}

/**
 * before push KDS/NCS, transaction must be saved and has the cookingStatus info, pushKdsDate, registerId
 * @param transaction need to read from DB
 * @returns
 */
export function* ensureKdsByDB(transaction: TransactionType) {
  if (transaction.transactionId) {
    const registerObjectId = yield select(selectRegisterObjectId);
    if (transaction.cookingStatus && transaction.createdDate) {
      // saved before
      return confirmNcsDate(transaction, registerObjectId);
    } else {
      const transactionList = DAL.getJsTransactionByIds([transaction.transactionId]);
      const dbTrans = transactionList[0];
      if (!dbTrans) {
        transaction = yield call(initKdsTransactionSaga, transaction);
        const saveResult = DAL.saveTransaction(transaction);
        transaction = saveResult ? transaction : null;
      } else {
        transaction = dbTrans;
        if (!transaction.cookingStatus) {
          transaction = yield call(initKdsTransactionSaga, dbTrans);
        }
        const saveResult = DAL.saveTransaction(transaction);
        transaction = saveResult ? transaction : null;
      }
      return confirmNcsDate(transaction, registerObjectId);
    }
  } else {
    // empty transactionId
    return null;
  }
}

export function* getConfirmedKDSTransaction(transactionList: TransactionType[]) {
  const confirmedList: TransactionType[] = [];
  const isMainOnlineRegister = yield call(ensureIsOnlineMainRegister, false);
  for (const transaction of transactionList) {
    if (transaction.isOnlineOrder && !isMainOnlineRegister) {
      // only main register can push online order
      continue;
    }
    const confirmed = yield call(ensureKdsByDB, transaction);
    if (confirmed) {
      confirmedList.push(confirmed);
    }
  }
  return confirmedList;
}

/**
 * init KDS info
 * @param transaction
 * @returns
 */
export function* initKdsTransactionSaga(transaction: TransactionType) {
  const isKdsPaired = yield select(selectIsKdsPaired);
  if (!isKdsPaired) {
    return transaction;
  }
  const cookingStatusType = yield select(selectCookingStatusType);
  const initStatus = getTransactionInitCookingStatus(cookingStatusType);

  const now = new Date();
  const nowISO = now.toISOString();
  transaction.cookingStatusType = cookingStatusType;
  transaction.cookingStatus = initStatus;
  transaction.pushKdsDate = nowISO;
  if (!transaction.createdDate) {
    // @ts-ignore
    transaction.createdDate = nowISO;
  }

  forEach(transaction.items, item => {
    // only product item
    if (!item.itemType && !item.cookingStatus) {
      item.cookingStatus = getItemInitCookingStatus(cookingStatusType);
      const dateProp = getItemDateProp(initStatus);
      if (dateProp) {
        item[dateProp] = nowISO;
      }
    }
  });
  return transaction;
}

type UpdateItemsType = PurchasedItemType & { updates?: string[]; mergedCookingStatus?: boolean };
/**
 * if the quantity of item is increased and its description is changed, you should update its cooking status to pending
 * @param statusType
 * @param toItems
 * @param fromItems
 * @param updateItemStatus
 */

export function updateItemCookingStatusAfterUpdate(statusType: CookingStatusType, toItems: UpdateItemsType[], fromItems: UpdateItemsType[]) {
  const initStatus = getTransactionInitCookingStatus(statusType);
  const dateProp = getItemDateProp(initStatus);
  const initKdsItemInfo = {
    cookingStatus: initStatus,
    [dateProp]: new Date().toISOString(),
  };
  const fromList = fromItems.filter(item => {
    if (item.id) {
      item.updates = [];
    }
    return item.id;
  });
  const toList = toItems.filter(item => {
    if (item.id) {
      item.updates = [];
      if (!item.cookingStatus) {
        Object.assign(item, initKdsItemInfo);
      }
    }
    return item.id;
  });

  while (fromList.length > 0) {
    const fromItem = fromList.shift();
    // try to find the same product
    let finIndex = toList.findIndex(it => isSameProduct(fromItem, it));
    finIndex = finIndex >= 0 ? finIndex : 0;
    const toItem = toList[finIndex];
    if (!toItem) {
      continue;
    }
    if (fromItem.productId === toItem.productId) {
      // delete modified items and left added items
      toList.splice(finIndex, 1);
      let resetStatus = false;
      if (fromItem.quantity - toItem.quantity < 0 && !toItem.mergedCookingStatus) {
        // increased
        resetStatus = true;
      }

      if (generateDescriptionFromOptionStr(fromItem.options) !== generateDescriptionFromOptionStr(toItem.options)) {
        // options changed
        resetStatus = true;
      }
      if (resetStatus) {
        Object.assign(toItem, initKdsItemInfo);
      } else if (fromItem.cookingStatus && toItem.cookingStatus !== fromItem.cookingStatus) {
        // restore served status
        Object.assign(toItem, getKdsInfoFromItem(fromItem));
      }
    }
  }
}

/**
 * Item.options should be string
 * @param transaction
 * @param originalTx
 * @returns
 */
export function* updateCookingStatusFromItems(transaction: TransactionType, originalTransaction?: TransactionType, isKdsPaired?: boolean) {
  if (isKdsPaired === undefined) {
    isKdsPaired = yield select(selectIsKdsPaired);
  }
  if (!isKdsPaired) {
    return transaction;
  }
  let originalTx = originalTransaction;
  if (!originalTx) {
    originalTx = DAL.getTransactionById(transaction.transactionId);
  }
  const cookingStatusType = yield select(selectCookingStatusType);
  if (get(transaction, 'items.length', 0) > 0) {
    yield call(updateItemCookingStatusAfterUpdate, cookingStatusType, transaction.items || [], getUnNullValue(originalTx, 'items', []));
    transaction.cookingStatus = getCookingStatusFromItems(transaction.items || [], cookingStatusType);
  }
  // record servedTime time
  if (transaction.cookingStatus === CookingStatus.served && !transaction.servedTime) {
    transaction.servedTime = new Date().toISOString();
  }

  if (!transaction.cookingStatusType) {
    transaction.cookingStatusType = cookingStatusType;
  }
  const now = new Date().toISOString();
  if (!transaction.pushKdsDate) {
    transaction.pushKdsDate = now;
  }

  return transaction;
}
export const supportOnlineOrderChannels = [
  OrderChannel.OrderChannelQRCode,
  OrderChannel.OrderChannelOffline,
  OrderChannel.OrderChannelGrab,
  OrderChannel.OrderChannelFoodPanda,
  OrderChannel.OrderChannelShopee,
];
export function* tryInitKDSRecordSaga(currentRecord: TransactionType) {
  if (currentRecord.cookingStatus) {
    // has initialized
    return;
  }

  const supportOfflineOrder = !currentRecord.isOnlineOrder;
  const supportOnlineOrder = currentRecord.isOnlineOrder && !currentRecord.isPayLater && supportOnlineOrderChannels.includes(currentRecord.channel);
  const isNeedInit = currentRecord.transactionType === TransactionFlowType.Sale && (supportOfflineOrder || supportOnlineOrder);
  if (isNeedInit) {
    yield call(initKdsTransactionSaga, currentRecord);
  }
}

/**
 * kds need to set a main register
 */
export function* ensureIsOnlineMainRegister(showToast = true) {
  const qrRegisters = yield select(selectQRRegisters);
  const curRegisterId = yield select(selectRegisterObjectId);
  const hasMainRegister = qrRegisters && qrRegisters.size > 0;
  if (!hasMainRegister && showToast) {
    openKdsErrorToast(t('Please set the main register on BO first'), '');
  }
  return hasMainRegister && curRegisterId && qrRegisters.indexOf(curRegisterId) >= 0;
}

export const KDSPayAtCounterStatus: string[] = [OnlineOrderStatus.Paid, OnlineOrderStatus.Accepted, OnlineOrderStatus.Confirmed];

const KDSDeliveryStatus = [OnlineOrderStatus.LogisticsConfirmed, OnlineOrderStatus.Confirmed];
const KDSPickUpOrTakeawayStatus = [OnlineOrderStatus.Accepted, OnlineOrderStatus.Confirmed];
const KDSDineInStatus = [OnlineOrderStatus.Paid, OnlineOrderStatus.Confirmed];

/**
 * check online order push notification for KDS
 * @param onlineOrder
 * @returns
 */
export function onlineOrderNeedPushKds(onlineOrder) {
  if (!onlineOrder) {
    return false;
  }
  const channel = get(onlineOrder, 'channel');
  const isPayLater = get(onlineOrder, 'isPayLater');
  const isSupported = supportOnlineOrderChannels.includes(channel);
  if (!isSupported) {
    return false;
  }
  const status = get(onlineOrder, 'status');

  const isBeepPayFirstCanceled = channel === OrderChannel.OrderChannelQRCode && status === OnlineOrderStatus.Cancelled && !isPayLater;
  if (isBeepPayFirstCanceled) {
    return true;
  }
  if (isDelivery(onlineOrder)) {
    return KDSDeliveryStatus.includes(status);
  } else if (isPickUpOrTakeAway(onlineOrder)) {
    return KDSPickUpOrTakeawayStatus.includes(status);
  } else if (isPayLater) {
    return true;
  } else {
    return KDSDineInStatus.includes(status);
  }
}

export function payByCashNeedPushKds(transaction) {
  return (
    transaction.transactionType === TransactionFlowType.Sale &&
    transaction.isPayByCash &&
    !transaction.isPayLater &&
    transaction.isOnlineOrder &&
    supportOnlineOrderChannels.includes(transaction.channel)
  );
}
