import { first, get, last, map, uniqBy } from 'lodash';
import { Action } from 'redux-actions';
import { call, delay, put, select } from 'redux-saga/effects';
import { pushAllKdsTransactions, PushAllKdsTransactionsType, sendKdsMessage } from '../../../actions/kds';
import DAL from '../../../dal';
import { KdsActionEnum, KdsOtherEvent, KdsTransactionLogEvent, logKDSEvent, logKdsTransaction } from './../../../utils/logComponent/buz/kds';

import { MRSRole, OrderChannel, TransactionFlowType } from '../../../constants';

import {
  HttpActionCallback,
  HttpActionFailurePayload,
  HttpActionSuccessPayload,
  KdsTransactionType,
  markKDS,
  Message,
  OperationType,
  ProposeDataType,
} from '../../../actions';
import { TransactionHelper } from '../../../dal/helper';
import { PurchasedItemType, TransactionType } from '../../../typings';
import { getUnNullValue } from '../../../utils';
import eventBus from '../../../utils/eventBus';
import { stringify } from '../../../utils/json';
import { KDS_PAGE_SIZE } from '../../../utils/kds';
import { KdsBuzCode } from '../../../utils/kds/errorCode';
import { KDSCommand, PageKdsTransType } from '../../../utils/kds/message';
import { OrderOperationEnum, WorkflowStatus } from '../../../utils/logComponent';
import { sendNcsTransactionList } from '../../ncs/message/send';
import {
  selectDefaultKitchenPrinter,
  selectIsEnabledMRS,
  selectIsKdsPaired,
  selectIsNcsPaired,
  selectMRSRole,
  selectRegisterObjectId,
  selectSocketIdByKdsId,
} from '../../selector';
import { PayloadOptions } from '../message/create';
import { sendKdsMessageSaga, sendKdsTransactionList } from '../message/send';
import {
  ensureIsOnlineMainRegister,
  ensureKdsByDB,
  getConfirmedKDSTransaction,
  restoreKDSInfo,
  supportOnlineOrderChannels,
  updateCookingStatusFromItems,
} from './helper';

/**
 * 支持的订单
 * 1. 当前register创建的非OpenOrder订单
 * 2. 被取消的订单
 * 3. payAtCounter支付方式的beep payFirst订单（过滤支持的channel）
 * and ensure transaction must be saved and has the cookingStatus info, pushKdsDate, registerId
 * @param dbTransaction
 * @param registerId
 * @returns
 */
function* checkPushTransaction(dbTransaction: TransactionType, registerId: string, isOnlineMainRegister: boolean) {
  if (!dbTransaction) {
    return null;
  }

  const isOnlineOrder = dbTransaction.isOnlineOrder;
  const transactionType = dbTransaction.transactionType;
  const isLocalOrder = dbTransaction.transactionType === TransactionFlowType.Sale && !isOnlineOrder;

  const isPayByCashOrder =
    isOnlineMainRegister &&
    transactionType === TransactionFlowType.Sale &&
    dbTransaction.isPayByCash &&
    !dbTransaction.isPayLater &&
    isOnlineOrder &&
    supportOnlineOrderChannels.includes(dbTransaction.channel);

  const isBeepCancel =
    dbTransaction.isCancelled &&
    isOnlineMainRegister &&
    transactionType === TransactionFlowType.Sale &&
    isOnlineOrder &&
    dbTransaction.channel === OrderChannel.OrderChannelQRCode;

  if (isLocalOrder || isPayByCashOrder || isBeepCancel) {
    const pushTransaction: TransactionType | null = yield call(ensureKdsByDB, dbTransaction);
    return pushTransaction;
  } else {
    return null;
  }
}

/**
 * 根据ids从本地数据库获取支持的订单push到KDS/NCS
 * 1. 当前register创建的非OpenOrder订单
 * 2. 被取消的订单
 * 3. payAtCounter支付方式的beep payFirst订单（过滤支持的channel）
 * @param transactionIds string[]
 */
function* getPushTransactionByIds(transactionIds: string[]) {
  const transactionList = DAL.getJsTransactionByIds(transactionIds);
  const curRegisterId = yield select(selectRegisterObjectId);
  const isOnlineMainRegister = yield call(ensureIsOnlineMainRegister, false);
  const pushList: TransactionType[] = [];
  for (const singleTrans of transactionList) {
    const pushTransaction: TransactionType | null = yield call(checkPushTransaction, singleTrans, curRegisterId, isOnlineMainRegister);
    if (pushTransaction) {
      if (pushTransaction.isOnlineOrder) {
        logKDSEvent('kds_push', KdsTransactionLogEvent.pushTransaction, {
          message: {
            source: 'pushLocalTransactionsSaga',
          },
          queryInfo: {
            orderIds: pushTransaction.transactionId,
          },
        });
      }
      pushList.push(pushTransaction);
    }
  }
  return pushList;
}

export type KdsOpenOrderOptions = {
  cancelledId?: string;
  checkoutId?: string;
  mergeFromTransactionList?: TransactionType[];
  mergeToId?: string;
  mergeAndPay?: boolean;
  splitFromId?: string;
  splitToId?: string;
  splitAndPay?: boolean;
  splitFromTransaction?: KdsTransactionType;
  deletedTransaction?: TransactionType;
};

const updateCancelledTx = (transactionList: TransactionType[], options?: KdsOpenOrderOptions) => {
  if (options?.cancelledId) {
    // cancel open order
    const now = new Date().toISOString();
    const tx = transactionList.find(it => it.transactionId === options?.cancelledId);
    if (!tx.isCancelled) {
      tx.isCancelled = true;
      // @ts-ignore
      tx.cancelledAt = now;
    }
  }
};

const updateCheckedOutTx = (transactionList: TransactionType[], options?: KdsOpenOrderOptions) => {
  if (options?.checkoutId) {
    // cancel open order
    const tx = transactionList.find(it => it.transactionId === options?.checkoutId);
    if (tx) {
      tx.isOpen = true;
    }
  }
};

const updateDeletedTx = (transactionList: TransactionType[], options?: KdsOpenOrderOptions) => {
  if (options?.deletedTransaction && transactionList.findIndex(it => it.transactionId === options.deletedTransaction.transactionId) < 0) {
    transactionList.push(options.deletedTransaction);
  }
};

const updateMergedTx = (transactionList: KdsTransactionType[], options?: KdsOpenOrderOptions) => {
  if (!options) {
    return;
  }
  if (options.mergeToId && options.mergeFromTransactionList) {
    if (options.mergeAndPay) {
      transactionList.forEach(tx => (tx.isOpen = true));
    }
    const tx = transactionList.find(it => it.transactionId === options.mergeToId);
    tx.mergeFromTransactionList = options.mergeFromTransactionList;
  }
};

const updateSplitTx = (transactionList: KdsTransactionType[], options?: KdsOpenOrderOptions) => {
  if (!options) {
    return;
  }
  if (options.splitFromId && options.splitToId) {
    let splitFrom = transactionList.find(it => it.transactionId === options.splitFromId);
    const splitTo = first(DAL.getJsTransactionByIds([options.splitToId]));
    if (!splitFrom && !options.splitFromTransaction) {
      return;
    }

    if (splitFrom) {
      splitFrom.splitToTransaction = splitTo;
    } else if (!splitFrom && options.splitFromTransaction) {
      splitFrom = options.splitFromTransaction;
      // splitFromTransaction has been deleted
      splitFrom.isCancelled = true;
      // @ts-ignore
      splitFrom.cancelledAt = new Date().toISOString();
      splitFrom.splitToTransaction = splitTo;
      transactionList.push(splitFrom);
    }
    if (options.splitAndPay) {
      splitTo && (splitTo.isOpen = true);
      splitFrom && (splitFrom.isOpen = true);
    }
  }
};

export function* pushLocalOpenOrdersSaga(transactionIds: string[], options?: KdsOpenOrderOptions) {
  const isKdsPaired = yield select(selectIsKdsPaired);
  if (!isKdsPaired) {
    return;
  }
  const pushList: TransactionType[] = yield call(getPushTransactionByIds, transactionIds);
  updateCancelledTx(pushList, options);
  updateCheckedOutTx(pushList, options);
  updateDeletedTx(pushList, options);
  updateMergedTx(pushList, options);
  updateSplitTx(pushList, options);

  if (pushList.length > 0) {
    yield call(sendKdsTransactionList, pushList);
  }
}

function getSummaryFromMessage(message: Message<ProposeDataType[]>): OrderOperationEnum {
  if (message.summary) {
    return message.summary;
  }
  // version compatible
  const data = uniqBy(message.data, 'operationType');
  if (data.length === 2 && message.data.find(it => it.transaction.isSplitMaster)) {
    return OrderOperationEnum.SplitAndSave;
  } else if (data.length === 2 && data[0].operationType === OperationType.UPDATE && data[1].operationType === OperationType.DELETE) {
    return OrderOperationEnum.MergeAndSave;
  } else if (data.length === 1) {
    const transaction = data[0].transaction;
    if (transaction.splitFromId) {
      return OrderOperationEnum.SplitAndCheckOut;
    }
    if (!transaction.isOpen) {
      return OrderOperationEnum.CheckOut;
    }
    const operationType = data[0].operationType;
    if (operationType === OperationType.INSERT) {
      return OrderOperationEnum.Save;
    } else if (operationType === OperationType.UPDATE) {
      return OrderOperationEnum.Update;
    } else if (operationType === OperationType.DELETE) {
      return OrderOperationEnum.Delete;
    }
  }
  return OrderOperationEnum.Unknown;
}

export function* pushMrsOrdersSaga(message: Message<ProposeDataType[]>) {
  const { data } = message;
  if (!data) {
    return;
  }
  const summary = getSummaryFromMessage(message);
  if (summary === OrderOperationEnum.Unknown) {
    // ignore
    return;
  }
  const transactionIds = data.map(it => {
    return it.transaction.transactionId;
  });
  const pushIds: string[] = [];
  let options: KdsOpenOrderOptions = undefined;
  if (summary === OrderOperationEnum.MergeAndCheckOut || summary === OrderOperationEnum.MergeAndSave) {
    const mergeToId = data.find(it => it.operationType === OperationType.UPDATE)?.transaction.transactionId;
    if (mergeToId) {
      pushIds.push(mergeToId);
      options = {
        mergeAndPay: true,
        mergeToId,
        mergeFromTransactionList: data.map(it => it.transaction).filter(it => it.transactionId !== mergeToId),
      };
    }
  } else if (summary === OrderOperationEnum.SplitAndSave || summary === OrderOperationEnum.SplitAndCheckOut) {
    const splitFromTransaction = data.find(it => it.transaction.isSplitMaster)?.transaction;
    const splitFromId = data?.length === 1 ? get(data, '0.transaction.splitFromId') : splitFromTransaction?.transactionId;
    if (splitFromId) {
      pushIds.push(splitFromId);
      options = {
        splitFromId,
        splitToId: transactionIds.filter(id => id !== splitFromId)[0],
        splitAndPay: true,
        splitFromTransaction,
      };
    }
  } else if (summary === OrderOperationEnum.CheckOut) {
    pushIds.push(transactionIds[0]);
    options = { checkoutId: transactionIds[0] };
  } else if (summary === OrderOperationEnum.Delete) {
    const deletedTransaction = data[0].transaction;
    if (deletedTransaction && deletedTransaction.items?.length > 0) {
      pushIds.push(transactionIds[0]);
      // @ts-ignore
      options = { deletedTransaction: { ...deletedTransaction, isCancelled: true, cancelledAt: new Date().toISOString() } };
    }
  } else if (summary === OrderOperationEnum.Save || summary === OrderOperationEnum.Update || summary === OrderOperationEnum.ChangeTableId) {
    pushIds.push(...transactionIds);
  }
  if (pushIds.length > 0) {
    yield call(pushLocalOpenOrdersSaga, pushIds, options);
  }
}
/**
 * send local, payAtCounter and cancelled transactions to KDS and NCS by ids
 * @param transactionIds
 * @returns
 */
export function* pushLocalTransactionsSaga(transactionIds: string[]) {
  const isKdsPaired = yield select(selectIsKdsPaired);
  const isNcsPaired = yield select(selectIsNcsPaired);

  if (!isKdsPaired) {
    return;
  }
  const pushList: TransactionType[] = yield call(getPushTransactionByIds, transactionIds);
  if (pushList.length > 0) {
    if (isKdsPaired) {
      yield call(sendKdsTransactionList, pushList);
    }
    if (isNcsPaired) {
      yield call(sendNcsTransactionList, pushList);
    }
  }
}

/**
 * push onlineOrder to KDS/NCS when polling or push
 * @param onlineOrders
 * @param messageId
 * @returns
 */
export function* pushOnlineTransactionsSaga(onlineOrders: any[], messageId?: string, splittedOrder?: any) {
  const isKdsPaired = yield select(selectIsKdsPaired);
  const isNcsPaired = yield select(selectIsNcsPaired);

  if (!isKdsPaired) {
    return;
  }
  const isOnlineMainRegister = yield call(ensureIsOnlineMainRegister);
  if (!isOnlineMainRegister) {
    return;
  }
  const registerObjectId = yield select(selectRegisterObjectId);

  const isSplittedFromReceiptNumber = splittedOrder && splittedOrder.isSplittedFromReceiptNumber;

  const transactionIds: string[] = [];
  let tempTrx: TransactionType = undefined;

  for (const onlineOrder of onlineOrders) {
    const orderId = getUnNullValue(onlineOrder, 'receiptNumber', '');
    if (!orderId) {
      continue;
    }
    if (onlineOrder && supportOnlineOrderChannels.includes(onlineOrder.channel)) {
      const dbTrans = DAL.getTransactionById(onlineOrder.transactionId);
      let formattedOnlineOrder: TransactionType = TransactionHelper.saveKdsOnlineOrder(onlineOrder, dbTrans);
      if (orderId === isSplittedFromReceiptNumber) {
        tempTrx = Object.assign({}, dbTrans);
        if (dbTrans?.items) {
          tempTrx.items = dbTrans.items.map(item => ({ ...item }) as PurchasedItemType);
        }
      }
      yield call(restoreKDSInfo, dbTrans, formattedOnlineOrder);

      // recalculate cookingStatus
      yield call(updateCookingStatusFromItems, formattedOnlineOrder, dbTrans);
      if (formattedOnlineOrder.subOrders === null) {
        formattedOnlineOrder.subOrders = [];
      }

      formattedOnlineOrder = first(yield call(addKitchenStationToTrans, [formattedOnlineOrder]));
      // can not use updateOpenOrder, it will delete loyaltyDiscounts
      const savedResult = DAL.saveTransaction(formattedOnlineOrder);
      if (savedResult) {
        transactionIds.push(formattedOnlineOrder.transactionId);
        const onFailure: HttpActionCallback<HttpActionFailurePayload> = {
          callback: payload => {
            console.error('markKDSFailure', payload);
            logKDSEvent(KdsActionEnum.markKDS, KdsOtherEvent.mark_failed, {
              reason: 'API markKDSFailure',
              message: {
                payload,
                messageId,
                status: get(onlineOrder, 'status'),
              },
              queryInfo: {
                orderIds: orderId,
              },
            });
          },
        };
        const onSuccess: HttpActionCallback<HttpActionSuccessPayload> = {
          callback: () => {
            const isPayLater = get(onlineOrder, 'isPayLater', false);
            if (isPayLater) {
              setTimeout(() => {
                eventBus.emit('RefreshOnlineOpenOrder');
              }, 2000);
            }
            logKDSEvent(KdsActionEnum.markKDS, KdsOtherEvent.mark_success, {
              message: {
                messageId,
                status: get(onlineOrder, 'status'),
              },
              queryInfo: {
                orderIds: orderId,
              },
            });
          },
        };
        yield put(markKDS({ orderId, registerObjectId, onFailure, onSuccess }));
      } else {
        logKDSEvent('kds_transaction', KdsTransactionLogEvent.saveTransaction, {
          reason: 'save DB failed',
          message: {
            messageId,
            order: stringify(onlineOrder),
          },
          queryInfo: {
            orderIds: orderId,
          },
        });
      }
    }
  }

  let formattedSplittedOrder: TransactionType = undefined;
  let splittedOrderSavedResult = false;
  if (isSplittedFromReceiptNumber) {
    const dbTrans = DAL.getTransactionById(splittedOrder.transactionId);
    formattedSplittedOrder = TransactionHelper.saveKdsOnlineOrder(splittedOrder, dbTrans);
    yield call(restoreKDSInfo, dbTrans, formattedSplittedOrder);

    // split order的item的cookingStatus跟主order一致
    const tempTrxItems = tempTrx?.items ?? [];
    for (const item of tempTrxItems) {
      for (const splittedItem of formattedSplittedOrder.items) {
        if (item.id === splittedItem.id) {
          splittedItem.cookingStatus = item.cookingStatus;
        }
      }
    }

    // recalculate cookingStatus
    yield call(updateCookingStatusFromItems, formattedSplittedOrder, dbTrans);

    if (formattedSplittedOrder.subOrders === null) {
      formattedSplittedOrder.subOrders = [];
    }
    formattedSplittedOrder = first(yield call(addKitchenStationToTrans, [formattedSplittedOrder]));
    // can not use updateOpenOrder, it will delete loyaltyDiscounts
    splittedOrderSavedResult = DAL.saveTransaction(formattedSplittedOrder);
    if (!splittedOrderSavedResult) {
      logKDSEvent('kds_transaction', KdsTransactionLogEvent.saveTransaction, {
        reason: 'save DB failed',
        message: {
          messageId,
          order: stringify(formattedSplittedOrder),
        },
      });
    }
  }

  if (transactionIds.length !== 0) {
    // get cooking status from DB
    const pushOrders = DAL.getJsTransactionByIds(transactionIds);
    const confirmedTrans: TransactionType[] = yield call(getConfirmedKDSTransaction, pushOrders);

    if (splittedOrderSavedResult) {
      for (const trans of confirmedTrans) {
        if (trans.receiptNumber === isSplittedFromReceiptNumber) {
          formattedSplittedOrder.registerId = trans.registerId;
          formattedSplittedOrder.registerNumber = trans.registerNumber;
          // @ts-ignore
          trans.splitToTransaction = formattedSplittedOrder;
          break;
        }
      }
    }

    if (isKdsPaired) {
      yield call(sendKdsTransactionList, confirmedTrans);
    }
    if (isNcsPaired) {
      yield call(sendNcsTransactionList, confirmedTrans);
    }
  }
}

export function* addKitchenStationToTrans(trans: TransactionType[]) {
  const defaultKitchenPrinter = yield select(selectDefaultKitchenPrinter);
  for (const tran of trans) {
    for (const item of tran.items) {
      if (item.itemType === 'ServiceCharge' || item.itemType === 'Discount') {
        continue;
      }
      const product = DAL.getProductById(item.productId);
      if (!product) {
        continue;
      }
      const { kitchenPrinter } = product;
      item.kitchenStation = kitchenPrinter || defaultKitchenPrinter;
    }
  }
  return trans;
}

/**
 * push all unserved and historical orders from DB
 * push and refresh flow
 * @param socketId
 */
export function* pushAllKdsTransactionsSaga(action: Action<PushAllKdsTransactionsType>) {
  const { kdsId, refreshId } = action.payload;
  const socketId = yield select(selectSocketIdByKdsId(kdsId));
  const mrsRole = yield select(selectMRSRole);

  if (socketId) {
    let errorMessage = '';
    const startResponse = yield call(
      sendKdsMessageSaga,
      sendKdsMessage({ command: KDSCommand.posPushStart, socketId, headerOptions: { responseId: refreshId } })
    );
    const workflowId = refreshId || kdsId;
    const now = Date.now();
    if (!startResponse) {
      logKdsTransaction(KdsTransactionLogEvent.pushTransactionStart, {
        workflowId,
        reason: startResponse ? '' : 'pushAllKdsTransactionsSaga: push start failed!',
        queryInfo: {
          command: KDSCommand.posPushStart,
          kdsId,
          responseId: refreshId,
          errorCode: KdsBuzCode.posPushError,
        },
      });
      return;
    } else {
      logKdsTransaction(KdsTransactionLogEvent.pushTransactionStart, {
        workflowId,
        message: now,
        queryInfo: {
          command: KDSCommand.posPushStart,
          kdsId,
          responseId: refreshId,
        },
      });
    }
    let transLength = 0;
    // last two hours
    const startQueryDate = new Date(now - 2 * 60 * 60 * 1000 - 60 * 1000); // 2h + 1m
    let lastQueryDate: Date;
    while (true) {
      const queryResult = DAL.getKdsTransaction(KDS_PAGE_SIZE, startQueryDate, lastQueryDate);
      if (!queryResult) {
        break;
      }
      const { list: kdsTransactions, nextQueryDate } = queryResult;
      lastQueryDate = nextQueryDate as Date;

      let confirmedTrans: TransactionType[] = yield call(getConfirmedKDSTransaction, kdsTransactions);
      if (mrsRole === MRSRole.Client) {
        confirmedTrans = confirmedTrans.filter(it => !it.isOpen && !it.isOpenOrder && !it.mrs);
      }
      if (confirmedTrans.length !== 0) {
        const lastConfirmedDate = get(last(confirmedTrans), 'pushKdsDate', '');
        transLength += confirmedTrans.length;
        const pushData: PageKdsTransType = { list: confirmedTrans, total: confirmedTrans.length, lastPushDate: new Date(lastConfirmedDate).toISOString() };
        const sendResult = yield call(
          sendKdsMessageSaga,
          sendKdsMessage({
            command: KDSCommand.posPushOrder,
            socketId,
            payloadOptions: { data: pushData },
            headerOptions: { responseId: refreshId },
          })
        );

        if (!sendResult) {
          errorMessage = 'send failed';
          logKdsTransaction(KdsTransactionLogEvent.pushTransactionError, {
            workflowId,
            reason: 'pushAllKdsTransactionsSaga: send failed!',
            message: { transLength },
            queryInfo: {
              command: KDSCommand.posPushOrder,
              kdsId,
              responseId: refreshId,
              orderIds: map(confirmedTrans, it => it.transactionId).join(','),
              errorCode: KdsBuzCode.posPushError,
            },
          });
          break;
        }
      }
      if (kdsTransactions.length < KDS_PAGE_SIZE || !lastQueryDate) {
        // END SUCCESS
        break;
      }
      // wait kds render
      yield delay(500); // todo: 100
    }
    const payload: PayloadOptions = {
      code: errorMessage ? KdsBuzCode.posPushError : KdsBuzCode.ok,
      data: !Boolean(errorMessage),
      errorMessage,
    };
    yield call(
      sendKdsMessageSaga,
      sendKdsMessage({ command: KDSCommand.posPushEnd, socketId, payloadOptions: payload, headerOptions: { responseId: refreshId } })
    );
    logKdsTransaction(KdsTransactionLogEvent.pushTransactionEnd, {
      workflowId,
      message: {
        costTime: (Date.now() - now) / 1000,
        length: transLength,
      },
      reason: errorMessage,
      workflowStatus: WorkflowStatus.End,
      queryInfo: {
        command: KDSCommand.posPushEnd,
        kdsId,
        responseId: refreshId,
      },
    });
  }
}

export function* pushOrdersToConnectedKdsWhenSwitchLocalServer(kdsIds: string[]) {
  const isMRSEnabled = yield select(selectIsEnabledMRS);
  if (isMRSEnabled) {
    for (const id of kdsIds) {
      yield call(pushAllKdsTransactionsSaga, pushAllKdsTransactions({ kdsId: id }));
      // release JS Thread
      yield delay(1000);
    }
  }
}
