import { call, put, select, take } from 'redux-saga/effects';

import { selectBusinessName, selectIsKdsPaired, selectRegisterObjectId } from '../../selector';

import { kdsMetrics } from '../../../actions';
import DAL from '../../../dal';
import { TransactionType } from '../../../typings';
import { KdsActionEnum, KdsOtherEvent, logKDSEvent } from '../../../utils/logComponent/buz/kds';

function* uploadKdsInfo(transaction: TransactionType, businessName: string) {
  const { receiptNumber, pushKdsDate, servedTime, isOnlineOrder } = transaction;
  yield put(
    kdsMetrics({
      businessName,
      receiptNumber,
      pendingTime: new Date(pushKdsDate).toISOString(),
      servedTime: new Date(servedTime).toISOString(),
      isOnline: Boolean(isOnlineOrder),
    })
  );
  const responseAction = yield take([kdsMetrics.toString() + '.success', kdsMetrics.toString() + '.failure']);
  if (responseAction.type === kdsMetrics.toString() + '.failure') {
    const payload = responseAction.payload;
    logKDSEvent(KdsActionEnum.reportKDS, KdsOtherEvent.report_failed, {
      message: {
        response: payload,
        isOnlineOrder,
        pushKdsDate,
        servedTime,
      },
      queryInfo: {
        orderIds: receiptNumber,
      },
    });
  } else {
    logKDSEvent(KdsActionEnum.reportKDS, KdsOtherEvent.report_success, {
      queryInfo: {
        orderIds: receiptNumber,
      },
    });
  }

  return responseAction.type === kdsMetrics.toString() + '.success';
}
export function* syncKdsServedTime(receiptNumber?: string) {
  const isKdsPaired = yield select(selectIsKdsPaired);
  if (!isKdsPaired) {
    return;
  }
  const registerId = yield select(selectRegisterObjectId);
  const businessName = yield select(selectBusinessName);
  if (registerId && businessName) {
    const transactions = DAL.getKDSReportTransaction(registerId, receiptNumber) as TransactionType[];
    let saveBuffer: { transactionId: string; servedTimeUploaded: true }[] = [];
    for (const element of transactions) {
      const uploadResult = yield call(uploadKdsInfo, element, businessName);
      if (uploadResult) {
        saveBuffer.push({ transactionId: element.transactionId, servedTimeUploaded: true });
        if (saveBuffer.length === 50) {
          DAL.updateTransactionsInBatch(saveBuffer);
          saveBuffer = [];
        }
      } else {
        break;
      }
    }
    DAL.updateTransactionsInBatch(saveBuffer);
    return saveBuffer;
  }
}
