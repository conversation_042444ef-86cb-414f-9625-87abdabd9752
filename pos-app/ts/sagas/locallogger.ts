import AsyncStorage from '@react-native-async-storage/async-storage';
import { pick } from 'lodash';
import moment from 'moment';
import { call, select } from 'redux-saga/effects';
import DeviceInfoData from '../utils/deviceInfo';
import { LocalLoggingRemoteConfig } from '../utils/growthbook';
import { stringify } from '../utils/json';
import { errorPOSBasicEvent, infoPOSBasicEvent, POSBasicAction } from '../utils/logComponent';
import LocalLogger, { LocalLoggerDeleteKey, LocalLoggerStorageKey, LocalLoggerUploadKey } from '../utils/logComponent/local-logging/LocalLogger';
import { uploadLogZipToS3 } from './s3';
import { selectBusinessName, selectEmployeeId, selectLocalLoggingConfig, selectRegisterId, selectRegisterObjectId, selectStoreId } from './selector';

export function* updateLocalLoggerConfigurationSaga() {
  const localLoggingConfig: LocalLoggingRemoteConfig = yield select(selectLocalLoggingConfig);
  try {
    const persistConfig = yield call(AsyncStorage.getItem, LocalLoggerStorageKey);
    const newConfig = JSON.stringify(pick(localLoggingConfig, ['enabled', 'logLevel', 'maximumFileSize', 'maximumNumberOfFiles']));
    if (persistConfig !== newConfig) {
      console.warn('persistConfig', persistConfig);
      console.warn('newConfig', newConfig);
      yield call(AsyncStorage.setItem, LocalLoggerStorageKey, newConfig);
      yield call(toggleLocalLoggerSaga, localLoggingConfig);
    }
  } catch (error) {
    console.error('updateLocalLoggerConfigurationSaga error', error);
    errorPOSBasicEvent({
      action: POSBasicAction.LocalLogger,
      reason: 'Store local logging config error',
      privateDataPayload: { extrasJSON: stringify(error) },
    });
  }
  yield call(tryUploadLocalLoggerSaga, localLoggingConfig);
  yield call(tryDeleteLocalLoggerSaga, localLoggingConfig);
}

export function* toggleLocalLoggerSaga(localLoggingConfig: LocalLoggingRemoteConfig) {
  if (localLoggingConfig.enabled) {
    // update configuration
    yield call([LocalLogger, LocalLogger.updateLogSetting], localLoggingConfig);
  }
  if (LocalLogger.getEnabled() != localLoggingConfig.enabled) {
    if (localLoggingConfig.enabled) {
      const storeId = yield select(selectStoreId);
      const business = yield select(selectBusinessName);
      const registerId = yield select(selectRegisterObjectId);
      const registerNumber = yield select(selectRegisterId);
      const employeeId = yield select(selectEmployeeId);
      const version = DeviceInfoData.version;
      yield call([LocalLogger, LocalLogger.updateLogModel], {
        storeId,
        business,
        employeeId,
        registerId,
        registerNumber,
        version,
      });
    } else {
      yield call([LocalLogger, LocalLogger.updateLogSetting], {
        enabled: false,
      });
    }
  }
}

export function* tryUploadLocalLoggerSaga(localLoggingConfig: LocalLoggingRemoteConfig) {
  if (localLoggingConfig.uploadKey) {
    try {
      const uploadedKey = yield call(AsyncStorage.getItem, LocalLoggerUploadKey);
      if (uploadedKey && uploadedKey === localLoggingConfig.uploadKey) {
        console.warn('上传日志成功，跳过');
        return;
      }
      const zipPath = yield call(LocalLogger.compressLogs);
      if (!zipPath) {
        throw new Error('compressLogs error');
      }
      const businessName = yield select(selectBusinessName);
      const registerId = yield select(selectRegisterId);
      const fileName = moment().format('YYYY-MM-DD_HH:mm:ss') + '.zip';
      const success = yield call(uploadLogZipToS3, zipPath, fileName, `${businessName}/${registerId}/log-zips/`);
      if (!success) {
        throw new Error('uploadLogZipToS3 error');
      }
      yield call(AsyncStorage.setItem, LocalLoggerUploadKey, localLoggingConfig.uploadKey);
      console.log('上传日志成功', zipPath, fileName);
      infoPOSBasicEvent({
        action: POSBasicAction.LocalLogger,
        reason: `uploadLogs success. ${fileName}`,
      });
    } catch (error) {
      errorPOSBasicEvent({
        action: POSBasicAction.LocalLogger,
        reason: 'uploadLogs error',
        privateDataPayload: { extrasJSON: stringify(error) },
      });
      console.error('上传日志失败', error);
    }
  }
}

export function* tryDeleteLocalLoggerSaga(localLoggingConfig: LocalLoggingRemoteConfig) {
  if (localLoggingConfig.deleteKey) {
    try {
      const deletedKey = yield call(AsyncStorage.getItem, LocalLoggerDeleteKey);
      if (deletedKey && deletedKey === localLoggingConfig.deleteKey) {
        console.warn('清除日志成功，跳过');
        return;
      }
      yield call(LocalLogger.clearLogs);
      yield call(AsyncStorage.setItem, LocalLoggerDeleteKey, localLoggingConfig.deleteKey);
      console.log('清除日志成功');
      infoPOSBasicEvent({
        action: POSBasicAction.LocalLogger,
        reason: 'clearLogs success',
      });
    } catch (error) {
      errorPOSBasicEvent({
        action: POSBasicAction.LocalLogger,
        reason: 'clearLogs error',
        privateDataPayload: { extrasJSON: stringify(error) },
      });
      console.error('清除日志失败', error);
    }
  }
}
