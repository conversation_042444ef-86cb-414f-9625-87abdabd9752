import { get, isEmpty } from 'lodash';
import moment from 'moment';
import { call, fork, put, select, take, takeLatest } from 'redux-saga/effects';
import * as Actions from '../actions';
import { ProcessToPrintAyalaEODType, ProcessToPrintSMEODType, printAyalaMallReport, printOrtigasEODReport } from '../actions';
import { MallIntegrationChannel, MallIntegrationMSG } from '../constants';
import DAL from '../dal';
import { ReportManager } from '../models/print/manager/ReportManager';
import { safeCallback } from '../utils';
import { selectIsMallIntegrationEnabled, selectMallIntegrationChannel, selectRegisterId, selectRegisterObjectId, selectStoreId } from './selector';

export const processToPrintAyalaEODSaga = function* (action) {
  const payload: ProcessToPrintAyalaEODType = action.payload;
  const { date, onPrintSuccess, onPrintFailed } = payload;
  const storeID = yield select(selectStoreId);
  yield put(Actions.getAyalaMallDailyReport({ storeID, date }));
  const response = yield take([Actions.getAyalaMallDailyReport.toString() + '.success', Actions.getAyalaMallDailyReport.toString() + '.failure']);
  if (get(response, 'type') === Actions.getAyalaMallDailyReport.toString() + '.success') {
    const status = get(response, ['payload', 'status']);
    if (status === 'SUCCESS') {
      const ayalaMallReport = get(response, ['payload', 'data'], {});
      if (!isEmpty(ayalaMallReport)) {
        yield put(printAyalaMallReport({ ayalaMallReport }));
      } else {
        // TODO ayalaMallReport is empty
      }
      safeCallback(onPrintSuccess, { res: { message: MallIntegrationMSG.ROBINSON_MALL_ERR_MSG } });
    } else {
      safeCallback(onPrintFailed, { res: { message: get(response, ['payload', 'message']) } });
    }
  } else {
    // if EOD failed, then prompt
    safeCallback(onPrintFailed, { res: { message: MallIntegrationMSG.ROBINSON_MALL_ERR_MSG } });
  }
};

export const processToCallEODSaga = function* (action) {
  const { onEODSuccess, onEODFailed, needEOD = false, isZReadingClosed } = action.payload;
  const isMallIntegrationEnabled = yield select(selectIsMallIntegrationEnabled);
  const mallIntegrationChannel = yield select(selectMallIntegrationChannel);
  if (isMallIntegrationEnabled) {
    const storeId = yield select(selectStoreId);
    const registerId = yield select(selectRegisterId);
    let transactionDate = moment().format('YYYY-MM-DD');
    let response;
    switch (mallIntegrationChannel) {
      case MallIntegrationChannel.AYALA_MALL: // ayala mall
        // nothing need do
        break;

      case MallIntegrationChannel.ROBINSON_MALL:
      case MallIntegrationChannel.MEGAWORLD:
      case MallIntegrationChannel.ROCKWELL:
      case MallIntegrationChannel.FBDC:
      case MallIntegrationChannel.FILINVEST:
      case MallIntegrationChannel.SFDC:
      case MallIntegrationChannel.EVER_GOTESCO: // Robinson mall
        if (isZReadingClosed) {
          if (needEOD) {
            const currentShift = DAL.getLastShift();
            transactionDate = moment(currentShift.openTime).format('YYYY-MM-DD');
          }
          yield put(Actions.endDay({ onSuccess: onEODSuccess, transactionDate, storeId, registerId }));
          response = yield take([Actions.endDay.toString() + '.success', Actions.endDay.toString() + '.failure']);
          if (get(response, 'type') === Actions.endDay.toString() + '.success') {
            const endDayPayload = get(response, 'payload', {});
            if (get(endDayPayload, 'status', '') === 'FAILED' || get(endDayPayload, 'message', '') === 'FAILED') {
              yield put(Actions.checkReportUpdateStatus());
            }
          } else {
            // if EOD failed, then prompt
            safeCallback(onEODFailed, { res: { message: MallIntegrationMSG.ROBINSON_MALL_ERR_MSG } });
            yield put(Actions.checkReportUpdateStatus());
          }
        } else {
          // if z-reading close failed, then prompt
          safeCallback(onEODFailed, { res: { message: MallIntegrationMSG.ROBINSON_MALL_ERR_MSG } });
        }
        break;
      default:
        break;
    }
  }
};

export const processToPrintSMEODSaga = function* (action) {
  const payload: ProcessToPrintSMEODType = action.payload;
  const { date, onPrintSuccess, onPrintFailed } = payload;
  const storeID = yield select(selectStoreId);
  yield put(Actions.getSMDailyReport({ storeID, date }));
  const response = yield take([Actions.getSMDailyReport.toString() + '.success', Actions.getSMDailyReport.toString() + '.failure']);
  if (get(response, 'type') === Actions.getSMDailyReport.toString() + '.success') {
    const success = get(response, ['payload', 'success'], false);
    if (success) {
      const smReports: any[] = get(response, ['payload', 'data'], []);
      for (const smReport of smReports) {
        if (!isEmpty(smReport)) {
          smReport.reportDate = date;
          yield put(Actions.printSMEODReport({ smReport }));
        }
      }
      safeCallback(onPrintSuccess, { res: { message: MallIntegrationMSG.ROBINSON_MALL_ERR_MSG } });
    } else {
      safeCallback(onPrintFailed, { res: { message: get(response, ['payload', 'message']) } });
    }
  } else {
    // if EOD failed, then prompt
    safeCallback(onPrintFailed, { res: { message: get(response, ['payload', 'message']) } });
  }
};

export const processToPrintSMXReadingSaga = function* (action) {
  const payload: Actions.ProcessToPrintSMXReadingType = action.payload;
  const { date, shiftId, shiftIndex } = payload;
  const storeId = yield select(selectStoreId);
  const registerId = yield select(selectRegisterObjectId);
  yield put(Actions.toggleLoadingMask({ visible: true, disableInteractions: false }));
  yield put(Actions.getSMXReadingReport({ storeId, date, shiftId, registerId }));
  const response = yield take([Actions.getSMXReadingReport.toString() + '.success', Actions.getSMXReadingReport.toString() + '.failure']);
  if (get(response, 'type') === Actions.getSMXReadingReport.toString() + '.success') {
    const success = get(response, ['payload', 'success'], false);
    if (success) {
      const reports: any[] = get(response, ['payload', 'data'], []);
      if (isEmpty(reports)) {
        yield put(Actions.toggleToastInfo({ visible: true, text: get(response, ['payload', 'message'], 'Server Error') }));
      } else {
        for (const report of reports) {
          if (!isEmpty(report)) {
            report.reportDate = date;
            report.shift = shiftIndex;
            yield call(ReportManager.printSMXReadingReport, report);
          }
        }
      }
    } else {
      yield put(Actions.toggleToastInfo({ visible: true, text: get(response, ['payload', 'message'], 'Server Error') }));
    }
  } else {
    yield put(Actions.toggleToastInfo({ visible: true, text: get(response, ['payload', 'message'], 'Server Error') }));
  }
  yield put(Actions.toggleLoadingMask({ visible: false, disableInteractions: false }));
};

const testData = [
  {
    zCount: 14,
    terminalNumber: '04',
    tenderBreakdown: {
      GCash: '7131.00',
      'Food Panda': '1211.00',
      'Food Panda Voucher/GC': '100.00',
      DebitCard: '6885.57',
      Cash: '21841.99',
      CreditCard: '7822.43',
    },
    totalTenders: '44991.99',
    vatableSales: {
      grossVatableSales: '38217.00',
      promoSalesAmount: 0,
      discountCards: 0,
      otherDiscounts: 0,
      refundAmount: 0,
      returnedAmount: 0,
      giftCertificates: 0,
      serviceChargeAmount: 0,
      deliveryChargeAmount: 0,
      otherTaxes: 0,
      voidAmount: 0,
      totalVatableAmount: '38217.00',
      vatAmount: '4094.80',
      totalSalesAmount: '34122.20',
    },
    nonVatableSales: {
      grossNonVatableSales: '10162.41',
      seniorCitizen: '1693.71',
      promoSalesAmount: 0,
      discountCards: 0,
      otherDiscounts: 0,
      refundAmount: 0,
      returnedAmount: 0,
      giftCertificates: 0,
      serviceChargeAmount: 0,
      deliveryChargeAmount: 0,
      voidAmount: 0,
      totalNonVatableAmount: '8468.70',
    },
    grandTotalNetSales: '40897.19',
    firstReceipt: 1216,
    lastReceipt: 1311,
    transactionCount: 96,
    oldAccumulatedSales: '579941.37',
    newAccumulatedSales: '624933.36',
  },
];

export const processToPrintOrtigasEODSaga = function* (action) {
  const payload: ProcessToPrintSMEODType = action.payload;
  const { date, onPrintSuccess, onPrintFailed } = payload;
  const storeID = yield select(selectStoreId);
  yield put(Actions.getOrtigasEODReport({ storeID, date }));
  const response = yield take([Actions.getOrtigasEODReport.toString() + '.success', Actions.getOrtigasEODReport.toString() + '.failure']);
  if (get(response, 'type') === Actions.getOrtigasEODReport.toString() + '.success') {
    const success = get(response, ['payload', 'success'], false);
    if (success) {
      const ortigasZreadings: any[] = get(response, ['payload', 'data'], []);
      for (const ortigasZreading of ortigasZreadings) {
        if (!isEmpty(ortigasZreading)) {
          ortigasZreading.reportDate = moment(date, 'YYYY-MM-DD').format('MM/DD/YYYY');
          yield put(printOrtigasEODReport({ ortigasZreading }));
        }
      }
      safeCallback(onPrintSuccess, { res: { message: '' } });
    } else {
      safeCallback(onPrintFailed, { res: { message: get(response, ['payload', 'message']) } });
    }
  } else {
    // if EOD failed, then prompt
    safeCallback(onPrintFailed, { res: { message: get(response, ['payload', 'message']) } });
  }
};

function* mallIntegration() {
  yield takeLatest(Actions.processToPrintAyalaEOD.toString(), processToPrintAyalaEODSaga);
  yield takeLatest(Actions.processToPrintSMEOD.toString(), processToPrintSMEODSaga);
  yield takeLatest(Actions.processToPrintSMXReading.toString(), processToPrintSMXReadingSaga);
  yield takeLatest(Actions.processToCallEOD.toString(), processToCallEODSaga);
  yield takeLatest(Actions.processToPrintOrtigasEOD.toString(), processToPrintOrtigasEODSaga);
}

export default fork(mallIntegration);
