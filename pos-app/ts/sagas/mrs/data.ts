import { isEmpty, last } from 'lodash';

import { call, put, select } from 'redux-saga/effects';
import {
  LearnDataType,
  LearnResponseData,
  Message,
  OperationType,
  ProposeDataType,
  updateLearnProgressAction,
  updateMRSOpenOrder,
  updateMRSSate,
  updatePidAction,
} from '../../actions';
import { INIT_PID, LEARN_COUNT_PER_TIME, MessageCode } from '../../constants';
import DAL from '../../dal';
import { TransactionsLogHelper } from '../../dal/helper';
import { PlainTransactionsLogType, TransactionsLogType } from '../../typings/schema/TransactionsLogType';
import globalConfig from '../../utils/globalConfig';

import { LearnFlow, LoggingLevel, logMRSEvent, MRSAction, ProposalFlow } from '../../utils/logComponent';
import { MRS_KDS_VERSION, MRS_NEW_DATA_VERSION } from '../../utils/mrs';
import { updateCookingStatusFromItems } from '../kds/transaction/helper';

import { selectBackupPid, selectMRSOutDatedClient, selectSlavePid } from '../selector';
import { updateMRSOpenOrderSaga } from '../transaction/openOrderFlow';
import { updatePidActionSaga } from './roles/master';
import { PlaceHolderChangesLog } from './snapshot';

/**
 * role: client
 * for Acceptor persist data, and slave persist chosen
 * @param message
 * @returns
 */
export const clientSaveProposeData = function* (message: Message<ProposeDataType[]>, source = 'acceptor') {
  // chosen will ignore proposer status
  const { pid, data } = message;
  if (isEmpty(data)) {
    logMRSEvent(MRSAction.Proposal, ProposalFlow.ACCEPTOR_SAVE_ERROR, {
      isDataOperation: true,
      level: LoggingLevel.Error,
      reason: 'data is empty when persistData',
      flowId: message.uuid || '',
      data: {
        source,
        message,
      },
    });
    return false;
  }

  // step 1: save db, contains save log and operate transaction db
  const status = yield call(DAL.saveLogAndTransactions, pid, data);
  if (!status) {
    logMRSEvent(MRSAction.Proposal, ProposalFlow.ACCEPTOR_SAVE_ERROR, {
      isDataOperation: true,
      level: LoggingLevel.Error,
      reason: 'save db logs failed',
      flowId: message.uuid || '',
      data: {
        source,
        message,
      },
    });
    return false;
  }
  const outDatedClient = yield select(selectMRSOutDatedClient);
  if (outDatedClient) {
    yield put(updateMRSSate({ outDatedClient: false }));
  }
  yield call(updatePidActionSaga, updatePidAction({ updateStatus: false }));
  yield call(updateMRSOpenOrderSaga, updateMRSOpenOrder({ transactionsData: data, proposer: message.proposer }));
  return true;
};

const formatClientTransaction = function* (item: ProposeDataType, version: number, isKdsPaired: boolean) {
  item.transaction.mrs = true;
  if (!version || version < MRS_KDS_VERSION) {
    item.transaction.isOpenOrder = true;
  }
  if (isKdsPaired) {
    if (!item.transaction.items?.length) {
      const originTransaction = DAL.getJsTransactionByIds([item.transaction.transactionId])[0];
      if (originTransaction) {
        if (item.operationType === OperationType.DELETE) {
          // record full transaction for KDS
          item.transaction = originTransaction;
        } else {
          // restore items for KDS
          item.transaction.items = originTransaction.items;
        }
      } else {
        logMRSEvent(MRSAction.Proposal, ProposalFlow.ACCEPTOR_ORDER_NOT_FOUND, {
          isDataOperation: true,
          level: LoggingLevel.Error,
          reason: 'origin transaction can not be found',
          type: item.operationType,
          data: {
            order: {
              transactionId: item.transaction?.transactionId,
              orderId: item.transaction?.receiptNumber,
            },
          },
        });
      }
    }
    yield call(updateCookingStatusFromItems, item.transaction, undefined, isKdsPaired);
  }
};

const saveProposalTransactionToDB = function* (message: Message<ProposeDataType[]>, isKdsPaired: boolean) {
  const { data, version } = message;
  let isSavedSuccess = true;
  for (const item of data) {
    if (!item.transaction) {
      logMRSEvent(MRSAction.Proposal, ProposalFlow.ACCEPTOR_ORDER_EMPTY, {
        isDataOperation: true,
        level: LoggingLevel.Error,
        reason: 'transaction is empty',
        flowId: message.uuid || '',
        data: {
          message,
        },
      });
      continue;
    }
    yield call(formatClientTransaction, item, version, isKdsPaired);
    const transaction = item.transaction;
    if (item.operationType === OperationType.DELETE) {
      isSavedSuccess = DAL.deleteOpenOrderById(transaction.transactionId);
    } else {
      transaction.mrs = true;
      isSavedSuccess = DAL.updateTransaction(transaction.transactionId, transaction);
    }
    if (!isSavedSuccess) {
      return false;
    }
  }
  return true;
};
export const saveProposalData = function* (message: Message<ProposeDataType[]>, isKdsPaired: boolean, source = 'acceptor') {
  const { pid, data } = message;
  const trxSaved = yield call(saveProposalTransactionToDB, message, isKdsPaired);
  if (trxSaved) {
    const transactionsLog: PlainTransactionsLogType = {
      pid: pid,
      changesLog: JSON.stringify(data),
      createdTime: new Date(),
    };
    const logSaved = DAL.saveTransactionsLog(transactionsLog);
    if (logSaved) {
      yield call(
        updatePidActionSaga,
        updatePidAction({
          updateStatus: false,
        })
      );
      return true;
    }
  }
  // save error
  logMRSEvent(MRSAction.Proposal, ProposalFlow.ACCEPTOR_MASTER_SAVE_ERROR, {
    isDataOperation: true,
    level: LoggingLevel.Error,
    reason: trxSaved ? 'fail to save transaction' : 'fail to save logs',
    flowId: message.uuid || '',
    data: {
      source,
      message,
      order: {
        orderId: data.map(it => it.transaction?.receiptNumber).join(','),
        transactionId: data.map(it => it.transaction?.transactionId).join(','),
      },
    },
  });
  return false;
};

export type SaveLearnDataType = {
  transactionLogs: TransactionsLogType[];
  fromPid: number;
  toPid: number;
  proposer?: string;
};
/**
 * role: client
 * 1. (cleanMrsOpenOrders)
 * 2. save orders
 * 3. save logs
 * 4. notify openOrder update
 * 5. updateLearnProgress
 * 6. updatePid
 * @param message
 * @returns
 */
export const doLearningData = function* (message: Message<LearnResponseData>) {
  const startTime = Date.now();
  const { data, ip: proposer } = message;
  const { diffData: transactionLogs, fromPid, toPid, cleanOpenOrder, dataType } = data;
  const logDataError = (reason: string, source = 'doLearningData') => {
    logMRSEvent(MRSAction.Learn, LearnFlow.SAVE_LEARN_DATA, {
      isDataOperation: true,
      reason,
      flowId: globalConfig.learnId,
      data: {
        source,
        fromPid,
        toPid,
      },
    });
  };

  let transactionData: ProposeDataType[];
  let learnStatus: boolean;
  try {
    transactionData = getTransactionDataFromLogs(transactionLogs);
    if (isEmpty(transactionData)) {
      throw new Error('getTransactionDataFromLogs error');
    }
    if (cleanOpenOrder) {
      const deletedOrders = DAL.deleteMrsOpenOrders();
      // if (deletedOrders?.length > 0) {
      //   yield call(DAL.saveTransactions2BackupRealm, deletedOrders);
      // }
      console.warn('deletedOrders', deletedOrders);
      logMRSEvent(MRSAction.Snapshot, LearnFlow.DELETE_USELESS_ORDERS, {
        isDataOperation: true,
        flowId: globalConfig.learnId,
        data: {
          message: deletedOrders,
        },
      });
      learnStatus = Boolean(deletedOrders);
      if (!learnStatus) {
        throw new Error('Failed to deleteMrsOpenOrders');
      }
    }
    const currentPid = yield select(selectSlavePid);
    if (cleanOpenOrder || currentPid === INIT_PID) {
      // outDatedClient or new register
      yield put(updateMRSSate({ outDatedClient: true }));
    }
    const outDatedClient = yield select(selectMRSOutDatedClient);
    if (dataType === 'snapshot' && !outDatedClient) {
      // no need to save orders from snapshot
      console.log('skip save orders due to snapshot');
      learnStatus = true;
    } else {
      if (dataType === 'snapshot') {
        console.log('client save data by snapshot');
      }
      console.log('learn data', transactionData);
      learnStatus = DAL.saveTransactionsByLog(transactionData);
    }

    if (!learnStatus) {
      throw new Error('Failed to saveOrders');
    }

    learnStatus = DAL.saveTransactionsLogInBatch(transactionLogs);
    if (!learnStatus) {
      throw new Error('Failed to saveTransactionsLog');
    }
    if (outDatedClient && dataType === 'commit') {
      // sync snapshot completely
      yield put(updateMRSSate({ outDatedClient: false }));
    }
  } catch (ex) {
    logDataError(ex.message);
    return false;
  }

  yield call(updateMRSOpenOrderSaga, updateMRSOpenOrder({ transactionsData: transactionData, proposer: proposer }));
  yield put(updateLearnProgressAction({ currentPid: toPid }));
  yield call(updatePidActionSaga, updatePidAction({ updateSnapshot: dataType === 'snapshot' }));

  logMRSEvent(dataType === 'snapshot' ? MRSAction.Snapshot : MRSAction.Learn, LearnFlow.SAVE_LEARN_DATA, {
    isDataOperation: true,
    flowId: globalConfig.learnId,
    data: {
      fromPid,
      toPid,
      cleanOpenOrder,
      dataType,
      costTime: (Date.now() - startTime) / 1000,
    },
  });

  return true;
};

export const getTransactionDataFromLogs = (transactionLogs: PlainTransactionsLogType[]) => {
  let changeLogs: ProposeDataType[] = [];

  try {
    for (const log of transactionLogs) {
      if (log && log.changesLog) {
        changeLogs = changeLogs.concat(JSON.parse(log.changesLog));
      } else {
        return [];
      }
    }
    return changeLogs;
  } catch (error) {
    logMRSEvent(MRSAction.Learn, LearnFlow.SAVE_LEARN_DATA, {
      isDataOperation: true,
      reason: 'getTransactionDataFromLogs error',
      flowId: globalConfig.learnId,
      data: {
        source: 'getTransactionDataFromLogs',
        errorCode: MessageCode.CODE_JSON_PARSE_ERROR,
        errorMessage: 'getTransactionDataFromLogs error: ' + error.message,
      },
    });
    return [];
  }
};

/**
 * server: should sync commits or snapshot one time
 * only sync logs >= backupPid
 * @param clientMessage
 * @returns
 */
export function* getLearningDataOnLocalServer(clientMessage: Message) {
  const startTime = Date.now();
  const clientVersion = clientMessage.version;
  let clientPid = clientMessage.pid;
  const backupPid = yield select(selectBackupPid);
  const serverPid = yield select(selectSlavePid);
  let cleanOpenOrder = false;
  if (clientPid < backupPid) {
    // regard as new register, sync from backupPid
    logMRSEvent(MRSAction.Snapshot, LearnFlow.OUTDATED_REGISTER, {
      flowId: clientMessage.uuid,
      data: {
        cleanOpenOrder,
        fromPid: clientPid,
        backupPid,
        serverPid,
        clientIp: clientMessage.ip,
      },
    });
    cleanOpenOrder = true;
    clientPid = backupPid;
  }

  const returnValue: { type: LearnDataType; data: PlainTransactionsLogType[]; cleanOpenOrder: boolean; toPid: number } = {
    type: 'commit',
    data: undefined,
    cleanOpenOrder,
    toPid: undefined,
  };
  const dataLogs = DAL.getTransactionsLogsFromPidWithLimit(clientPid, LEARN_COUNT_PER_TIME);
  if (dataLogs) {
    const snapShotLogIndex = dataLogs.findIndex(it => it.snapshotVersion);
    if (snapShotLogIndex < 0) {
      // no snapshot
      returnValue.data = dataLogs.map(TransactionsLogHelper.serializeTransactionsLog);
    } else if (snapShotLogIndex === 0) {
      // only sync snapshot
      returnValue.type = 'snapshot';
      const version = dataLogs[0].snapshotVersion;
      returnValue.data = dataLogs.filter(it => it.snapshotVersion === version).map(TransactionsLogHelper.serializeTransactionsLog);
    } else {
      // only sync commits
      returnValue.data = dataLogs.slice(0, snapShotLogIndex).map(TransactionsLogHelper.serializeTransactionsLog);
    }
  } else {
    returnValue.type = 'error';
    returnValue.data = [];
  }
  console.log('clientVersion', clientVersion);
  if (clientVersion < MRS_NEW_DATA_VERSION && returnValue.type === 'snapshot') {
    // old clients can not handle snapshot correctly
    returnValue.type = 'commit';
    returnValue.data = returnValue.data.map(it => {
      it.changesLog = PlaceHolderChangesLog;
      it.snapshotVersion = null;
      return it;
    });
    console.log('no snapshot for old clients');
  }
  returnValue.toPid = last<PlainTransactionsLogType>(returnValue.data)?.pid + 1;
  logMRSEvent(returnValue.type === 'snapshot' ? MRSAction.Snapshot : MRSAction.Learn, LearnFlow.GET_LEARN_DATA, {
    isDataOperation: true,
    flowId: clientMessage.uuid,
    data: {
      cleanOpenOrder,
      fromPid: clientPid,
      toPid: returnValue.toPid,
      clientIp: clientMessage.ip,
      backupPid,
      dataType: returnValue.type,
      serverPid,
      costTime: (Date.now() - startTime) / 1000,
    },
  });
  return returnValue;
}
