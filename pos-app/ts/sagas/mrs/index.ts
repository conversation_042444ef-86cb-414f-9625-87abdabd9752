import { cancel, fork, put, select, take } from 'redux-saga/effects';
import { DataType, initialMrsAction, Message, receivedMessageAction, unRegisterMrsAction } from '../../actions';
import { MESSAGE_TIMEOUT, MessageCode } from '../../constants';
import { MRS_VERSION, writeToClient, writeToServer } from '../../utils/mrs';
import { selectIsMaster, selectServerIp } from '../selector';
import { addMrsTimeout } from '../websocket/timer';
import message from './message';
import learner from './roles/learner';
import master from './roles/master';
import proposer from './roles/proposer';
import slave from './roles/slave';
import switchMaster from './switchMaster';

export function* initialMrs() {
  yield message;
  yield master;
  yield slave;
  yield proposer;
  yield learner;
  yield switchMaster;
}

export function* mrs() {
  while (true) {
    yield take(initialMrsAction.toString());
    const task = yield fork(initialMrs);
    yield take(unRegisterMrsAction.toString());

    yield cancel(task);
  }
}

export default fork(mrs);

export const sendMessageToServer = function* (message: Message, withTimeout = true) {
  const isMaster = yield select(selectIsMaster);
  if (isMaster) {
    const serverIp = yield select(selectServerIp);
    message.ip = serverIp;
    yield put(receivedMessageAction({ message, code: MessageCode.CODE_NORMAL_MRS_MESSAGE, version: MRS_VERSION }));
  } else {
    writeToServer(MessageCode.CODE_NORMAL_MRS_MESSAGE, message);
    if (withTimeout) {
      addMrsTimeout(MESSAGE_TIMEOUT, MessageCode.CODE_NORMAL_MRS_MESSAGE, Object.assign({}, message, { dataType: DataType.TIMEOUT, data: '' }));
    }
  }
};

export const sendMessageToClient = function* (ip: string, code: MessageCode, resMessage: Message) {
  const serverIp = yield select(selectServerIp);
  if (ip === serverIp) {
    yield put(receivedMessageAction({ message: resMessage, code, version: MRS_VERSION }));
  } else {
    writeToClient(ip, code, resMessage);
  }
};
