import { forEach } from 'lodash';
import { cleanUselessTransactionsLog, forceResetMRSState, initMRSState, receivedMessageAction } from './../../../actions/mrs';
import {
  selectBusinessName,
  selectIPAddress,
  selectIsEnabledMRS,
  selectIsMaster,
  selectMasterState,
  selectMRSClients,
  selectRegisterId,
  selectRegisterName,
  selectRegisterObjectId,
  selectServerIp,
  selectSlavePid,
  selectStoreId,
  selectStoreName,
} from './../../selector';

import { Action } from 'redux-actions';
import { call, debounce, delay, fork, put, select, spawn, takeLatest, takeLeading } from 'redux-saga/effects';
import {
  AddressType,
  clearMRSState,
  ClearMRSStateType,
  DataType,
  errorMasterState,
  MasterState,
  Message,
  MessageSender,
  normalMasterState,
  rebootMRSAction,
  resetMRSAction,
  startMrsServerAction,
  updateMasterStateAction,
  UpdateMasterStateType,
  updateMRSSate,
  updatePidAction,
  UpdatePidActionType,
} from '../../../actions';
import { MessageCode, SuccessMessage, t } from '../../../constants';

import { SingleClient } from '../../../reducers/mrs';
import { Base64Utils } from '../../../utils/base64';
import globalConfig from '../../../utils/globalConfig';
import { LoggingLevel, logMRSEvent, logUnhandledMRSError, MRSAction, MRSTypeEnum, SetUpFlow } from '../../../utils/logComponent';

import WsManager, { MRSHeaderType, serverBroadcast } from '../../../utils/mrs';

import { stringify } from '../../../utils/json';
import {
  checkHugeDataVersionOnLocalServer,
  cleanUselessTransactionsLogSaga,
  getMrsIdsFromDatabase,
  getSlavePidFromDatabase,
  requestCheckHugeDataVersionOnLocalServer,
  saveSnapshot,
} from '../snapshot';
import { resetSwitchMasterFlag } from '../switchMaster';
import { resetLearningFlags } from './learner';
import { resetProposalFlags } from './proposer';
import { getMRSClientRunning, updateSlaveStatusSaga } from './slave';

export const handleSlaveMessage = function (message: Message) {
  const { dataType } = message;
  switch (dataType) {
    // case DataType.UPDATE_CLIENT_STATUS:
    //   yield call(handleSlaveStatusMessage, message);
    //   break;
    default:
      logUnhandledMRSError(message, 'handleSlaveMessage');
      break;
  }
};

export const handleServerStart = function* (addr: AddressType) {
  console.log('handleServerStart', addr);
  yield put(
    updateMRSSate({
      serverRunning: true,
      serverInfo: addr,
      isMaster: true,
      resetFlag: false,
      outDatedClient: false,
    })
  );
  yield call(
    updateMasterStateActionSaga,
    updateMasterStateAction({
      masterState: MasterState.ONLINE,
      source: 'handleServerStart',
    })
  );
  yield put(
    receivedMessageAction({
      ip: addr.ip,
      port: Number(addr.port),
      event: 'CLIENT_CONNECTED',
    })
  );
  yield spawn(requestCheckHugeDataVersionOnLocalServer);
  const isEnabledMRS = yield select(selectIsEnabledMRS);
  logMRSEvent(MRSAction.Set_Up, SetUpFlow.SERVER_START, {
    flowId: globalConfig.setMasterId,
    type: !isEnabledMRS ? MRSTypeEnum.FIRST_ENABLE : MRSTypeEnum.NONE,
    data: { message: stringify(addr) },
  });
};

export const handleServerStop = function* (addr: AddressType) {
  console.log('handleServerStop', addr);
  yield put(
    updateMRSSate({
      serverRunning: false,
      serverInfo: { ip: '' },
    })
  );
  yield put(
    receivedMessageAction({
      code: 0,
      port: Number(addr.port),
      ip: addr.ip,
      event: 'CLIENT_CLOSE',
    })
  );
  logMRSEvent(MRSAction.Set_Up, SetUpFlow.SERVER_STOP, { data: { message: stringify(addr) } });
};

export const handleClientChanged = function* (clients: SingleClient[] = []) {
  if (clients) {
    const mrsClients: SingleClient[] = yield select(selectMRSClients);
    // set all disconnected
    forEach(mrsClients, mc => {
      mc.isConnected = false;
    });

    // update new client
    forEach(clients, client => {
      if (!client.registerId) {
        logMRSEvent(MRSAction.Set_Up, SetUpFlow.PROGRAM_ERROR, {
          level: LoggingLevel.Error,
          reason: `invalid registerId. client is ${JSON.stringify(client)}`,
          data: {
            source: 'handleClientChanged',
          },
        });
        return;
      }
      const index = mrsClients.findIndex(mc => mc.registerId === client.registerId);
      const registerName = Base64Utils.decode(client.registerName);
      if (index >= 0) {
        mrsClients[index].registerName = registerName;
        mrsClients[index].isConnected = true;
        mrsClients[index].ip = client.ip;
      } else {
        mrsClients.push({
          ip: client.ip,
          isConnected: true,
          registerId: client.registerId,
          registerName,
        });
      }
    });

    yield put(
      updateMRSSate({
        clients: mrsClients,
      })
    );

    yield call(broadcastMasterStateToSlave);
  }
  logMRSEvent(MRSAction.Set_Up, SetUpFlow.CLIENT_CHANGED, {
    data: {
      message: `online clients(${clients.length}) is ${JSON.stringify(clients)}.`,
    },
  });
};

export const updateMasterStateActionSaga = function* (action: Action<UpdateMasterStateType>) {
  const { onComplete, source = '' } = action.payload;
  const oldMasterState = yield select(selectMasterState);

  let newState = action.payload.masterState;
  if (errorMasterState.includes(oldMasterState) && newState === MasterState.OFFLINE) {
    newState = oldMasterState;
  }

  yield put(updateMRSSate({ masterState: newState }));
  yield call(broadcastMasterStateToSlave, newState);
  if (errorMasterState.includes(newState) && oldMasterState !== newState) {
    // wait broadcastMasterStateToSlave
    yield delay(500);
    yield call(closeMRSService);
    logMRSEvent(MRSAction.Set_Up, SetUpFlow.MASTER_ERROR, {
      data: { source, message: `old state is ${oldMasterState}` },
      reason: `master is on error ${newState}.`,
    });
  }

  if (onComplete) {
    yield call(onComplete, SuccessMessage());
  }
};

export const broadcastMasterStateToSlave = function* (state?: MasterState) {
  const isMaster = yield select(selectIsMaster);
  if (!isMaster) {
    return;
  }
  const isServerRunning = yield call(WsManager.isServerRunning);
  if (!isServerRunning) {
    return;
  }
  let masterState: MasterState = state;
  if (!masterState) {
    masterState = yield select(selectMasterState);
  }
  const serverIp = yield select(selectServerIp);
  const pid = yield select(selectSlavePid);
  const sendMessage: Message = {
    messageSender: MessageSender.MASTER,
    dataType: DataType.MASTER_STATE,
    pid,
    data: masterState,
    proposer: serverIp,
  };
  if (normalMasterState.includes(masterState)) {
    serverBroadcast(MessageCode.CODE_NORMAL_MRS_MESSAGE, sendMessage);
  } else {
    serverBroadcast(MessageCode.CODE_ERROR_MESSAGE, sendMessage);
  }
};

/**
 * update pid
 * update snapshotId
 * update client status
 * @param action
 * @returns
 */
export function* updatePidActionSaga(action: Action<UpdatePidActionType>) {
  const payload = action.payload;
  const updateStatus = payload.updateStatus ?? true;
  if (payload.updateSnapshot) {
    const { pid, snapshotPid, backupPid, snapshotVersion } = getMrsIdsFromDatabase();
    yield put(updateMRSSate({ pid, snapshotPid, backupPid, snapshotVersion }));
  } else {
    const pid = getSlavePidFromDatabase();
    yield put(updateMRSSate({ pid }));
  }
  if (updateStatus) {
    yield call(updateSlaveStatusSaga);
  }
}

export function* resetMRSActionSaga(action: Action<ClearMRSStateType>) {
  const { source } = action.payload;
  const ids = getMrsIdsFromDatabase();
  logMRSEvent(MRSAction.Set_Up, SetUpFlow.RESET, { data: { source, ...ids } });
  yield put(forceResetMRSState(ids));
  yield call(stopMRSWebsocketSaga, {
    type: '',
    payload: {
      source,
    },
  });
  resetMRSFlags();
}

export function* closeMRSService() {
  yield call(WsManager.stopWebSocketServer);
  yield call(WsManager.closeWebSocketClient);
  yield put(
    updateMRSSate({
      serverRunning: false,
      clientConnected: false,
      isMaster: false,
      resetFlag: true,
    })
  );
  resetMRSFlags();
}

/**
 * stop server, MDNS server, client
 * update client and server status
 * @param action
 */
export const stopMRSWebsocketSaga = function* (action: Action<ClearMRSStateType>) {
  const { onComplete, source = '' } = action.payload;
  logMRSEvent(MRSAction.Set_Up, SetUpFlow.STOP, {
    data: {
      source,
    },
  });

  // master state
  yield call(
    updateMasterStateActionSaga,
    updateMasterStateAction({
      masterState: MasterState.OFFLINE,
      source,
    })
  );

  yield call(WsManager.stopWebSocketServer);
  yield call(WsManager.closeWebSocketClient);
  // mrs state
  // fix master turn off wifi
  yield put(
    updateMRSSate({
      serverRunning: false,
    })
  );

  yield call(updateSlaveStatusSaga);
  onComplete && onComplete(SuccessMessage());
};

/**
 * header for server start and client connection
 * @returns
 */
export const createMRSHeader = function* () {
  const ip = yield select(selectIPAddress);
  const business = yield select(selectBusinessName);
  const storeName = yield select(selectStoreName);

  if (!ip || !business || !storeName) {
    return null;
  }
  const registerObjectId = yield select(selectRegisterObjectId);
  const registerId = yield select(selectRegisterId);
  const registerName = yield select(selectRegisterName);
  const storeId = yield select(selectStoreId);
  const header: MRSHeaderType = {
    business: Base64Utils.encode(business, true),
    registerObjectId,
    registerId: String(registerId),
    storeName: Base64Utils.encode(storeName, true),
    storeId: storeId,
    ip,
    registerName: Base64Utils.encode(registerName) || t('unknown'),
  };
  return header;
};

/**
 * try to find server and continue
 * @param source
 * @returns
 */
export const startClient = function* (source = '') {
  const isClientRunning = yield call(getMRSClientRunning);
  if (isClientRunning) return;
  const header = yield call(createMRSHeader);

  if (header) {
    logMRSEvent(MRSAction.Set_Up, SetUpFlow.START_CLIENT, {
      data: { source },
      destination: 'local',
    });
    yield call(WsManager.discoverAndConnectServer, header);
  }
};

/**
 * check MRS state
 * @returns
 */
export const rebootMRSSaga = function* () {
  // register discovery service
  yield call(startClient, 'rebootMRSSaga');
  const isEnabledMRS = yield select(selectIsEnabledMRS);
  if (!isEnabledMRS) {
    return;
  }

  const ip = yield select(selectIPAddress);
  if (!ip) {
    yield call(stopMRSWebsocketSaga, {
      type: '',
      payload: {
        source: 'rebootMRSSaga, ip is empty',
      },
    });
    return;
  }
  const isIpChanged = globalConfig.lastIpAddress !== null && globalConfig.lastIpAddress !== ip;
  if (isIpChanged) {
    console.log('isIpChanged', isIpChanged);
    logMRSEvent(MRSAction.Set_Up, SetUpFlow.IP_CHANGED, {
      data: {
        source: 'rebootMRSSaga',
        message: `last: ${globalConfig.lastIpAddress}, current: ${ip}`,
      },
    });
    yield call(stopMRSWebsocketSaga, {
      type: '',
      payload: {
        source: 'rebootMRSSaga, ipChanged',
      },
    });
    // wait service closed
    yield delay(500);
  } else {
    logMRSEvent(MRSAction.Set_Up, SetUpFlow.IP_NOT_CHANGED, {
      data: {
        source: 'rebootMRSSaga',
        message: `current: ${ip}`,
      },
      destination: 'local',
    });
  }
  globalConfig.lastIpAddress = ip;

  const isServerRunning = yield call(WsManager.isServerRunning);
  const isMaster = yield select(selectIsMaster);

  if (isMaster && (!isServerRunning || isIpChanged)) {
    yield call(startMrsServerActionSaga, 'rebootMRSSaga, isMaster but server not running');
  }

  if (!isMaster && isServerRunning) {
    logMRSEvent(MRSAction.Set_Up, SetUpFlow.STOP_WEBSOCKET, {
      reason: 'is client but server is running',
      data: {
        source: 'rebootMRSSaga',
      },
    });
    yield call(WsManager.stopWebSocketServer);
  }
  yield call(updateSlaveStatusSaga);
};

/**
 * start server and start MDNS server
 * @param source
 */
export function* startMrsServerActionSaga(source = '') {
  const header = yield call(createMRSHeader);
  if (header) {
    logMRSEvent(MRSAction.Set_Up, SetUpFlow.START_WEBSOCKET, { data: { source } });
    yield call(WsManager.startWebSocketServer, header);
    yield put(
      updateMRSSate({
        isMaster: true,
        resetFlag: false,
      })
    );
  }
}

/**
 * clear ws server, ws client, MDNS server, MDNS client
 */
export function* clearMRSStateSaga() {
  logMRSEvent(MRSAction.Set_Up, SetUpFlow.CLEAR);
  yield call(broadcastMasterStateToSlave, MasterState.OFFLINE);
  yield call(WsManager.onExitApp);
  resetMRSFlags();
}

export function resetMRSFlags() {
  resetSwitchMasterFlag();
  resetProposalFlags();
  resetLearningFlags();
}
/**
 * after realm initialized
 * 1. clear connect/learn/switch information
 * 2. restore ids from database
 * 3. clean useless transactionsLog
 */
export function* initMRSStateSaga() {
  const isEnabledMRS = yield select(selectIsEnabledMRS);
  if (isEnabledMRS) {
    const { pid, snapshotPid, backupPid, snapshotVersion } = getMrsIdsFromDatabase();
    yield put(initMRSState({ pid, snapshotVersion, snapshotPid, backupPid }));
    yield call(cleanUselessTransactionsLogSaga);
  } else {
    yield put(initMRSState(null));
  }
}
export function* masterSaga() {
  yield debounce(1000, rebootMRSAction.toString(), rebootMRSSaga);
  yield takeLatest(startMrsServerAction.toString(), startMrsServerActionSaga);
  yield takeLatest(updateMasterStateAction.toString(), updateMasterStateActionSaga);
  yield takeLatest(updatePidAction.toString(), updatePidActionSaga);
  yield takeLatest(resetMRSAction.toString(), resetMRSActionSaga);
  yield takeLatest(clearMRSState.toString(), clearMRSStateSaga);
  yield takeLeading(cleanUselessTransactionsLog.toString(), cleanUselessTransactionsLogSaga);
  // -----------test------------
  yield takeLatest('requestCheckHugeDataVersionOnLocalServer', requestCheckHugeDataVersionOnLocalServer);
  yield takeLatest('checkHugeDataVersionOnLocalServer', checkHugeDataVersionOnLocalServer);
  yield takeLatest('saveSnapshot', saveSnapshot);
  // -----------test------------
  yield put(rebootMRSAction());
}

export default fork(masterSaga);
