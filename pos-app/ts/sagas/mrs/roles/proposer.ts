import { get } from 'lodash';
import { Action } from 'redux-actions';
import { call, delay, fork, put, SagaReturnType, select, take, takeLatest } from 'redux-saga/effects';
import { sendMessageToServer } from '..';
import {
  checkLimitBeforeExecute,
  CheckLimitBeforeExecuteType,
  completeMergeSplitOpenOrder,
  DataType,
  exchangeSplitOrder,
  initSplitOrder,
  Message,
  MessageSender,
  MRSError,
  OperationType,
  performLearnerAction,
  ProposeDataType,
  proposerCompleteAction,
  ProposeType,
  requestProposalAction,
  setMergedBranchOrderIds,
  toggleLoadingMask,
  updateMRSSate,
} from '../../../actions';
import {
  createMrsUUID,
  ForbiddenCancelError,
  ForbiddenCheckOutError,
  ForbiddenMergePayError,
  ForbiddenRefundError,
  ForbiddenSplitPayError,
  InvalidTransactionError,
  IsLearningError,
  NeedLearnError,
  OnlyCheckoutError,
  ProposalTimeOutError,
  ProposerNoNetError,
  ProposerOfflineError,
  ProposerReceivedError,
  SuccessCode,
  SuccessMessage,
} from '../../../constants';

import { TransactionType } from '../../../typings';
import globalConfig from '../../../utils/globalConfig';

import { AbstractTransaction } from '../../../models/transaction/AbstractTransaction';
import { logMRSEvent, logUnhandledMRSError, MRSAction, OperationFlow, OrderOperationEnum, ProposalFlow, WorkflowStatus } from '../../../utils/logComponent';
import { checkProposeStateError } from '../checkSync';
import {
  selectClientIp,
  selectIPAddress,
  selectIsEnabledMRS,
  selectIsMaster,
  selectMergedBranchOrderIds,
  selectSlavePid,
  selectSplitTargetOrder,
} from './../../selector';
import { handleChosenMessage } from './acceptor';
import { getMRSClientRunning } from './slave';

export const handleAcceptorMessage = function* (message: Message) {
  const { dataType } = message;
  switch (dataType) {
    case DataType.RESPONSE:
      yield call(handleProposalResult, message);
      break;
    case DataType.CHOSEN:
      yield call(handleChosenMessage, message);
      break;
    default:
      logUnhandledMRSError(message, 'handleAcceptorMessage');
      break;
  }
};

export function* checkLimitBeforeExecuteSaga(action: Action<CheckLimitBeforeExecuteType>) {
  const { transaction, orderOperation, onComplete } = action.payload;
  const onFinished = (error: MRSError) => {
    if (error.errorCode !== SuccessCode) {
      logMRSEvent(MRSAction.Operation, OperationFlow.LIMIT, {
        type: orderOperation,
        reason: error.errorMessage || '',
        data: {
          errorCode: error.errorCode,
          message: error.errorMessage || '',
          order: {
            orderId: transaction.receiptNumber,
            transactionId: transaction.transactionId,
            orderType: AbstractTransaction.getOrderTypeFromTransaction(transaction),
          },
        },
      });
    }
    onComplete && onComplete(error);
  };

  if (!transaction) {
    onFinished(InvalidTransactionError);
    return InvalidTransactionError;
  }
  const isEnabledMRS = yield select(selectIsEnabledMRS);

  if (!isEnabledMRS) {
    onFinished(SuccessMessage());
    return SuccessMessage();
  }

  const mrsFlag = transaction.mrs;
  const isClientRunning = yield call(getMRSClientRunning);
  const ip = yield select(selectIPAddress);
  const isMaster = yield select(selectIsMaster);

  // only historical local orders for client can be refund, Cancel
  const allowOperations = [OrderOperationEnum.Cancel, OrderOperationEnum.Refund];
  const isOldLocalOrder = !mrsFlag && !transaction.isOnlineOrder;
  if (allowOperations.includes(orderOperation) && isOldLocalOrder) {
    onFinished(SuccessMessage());
    return SuccessMessage();
  }

  const checkoutOperations = [OrderOperationEnum.CheckOut, OrderOperationEnum.MergeAndCheckOut, OrderOperationEnum.SplitAndCheckOut];
  if (!isMaster && checkoutOperations.includes(orderOperation)) {
    if (orderOperation === OrderOperationEnum.MergeAndCheckOut) {
      onFinished(ForbiddenMergePayError);
      return ForbiddenMergePayError;
    }
    if (orderOperation === OrderOperationEnum.SplitAndCheckOut) {
      onFinished(ForbiddenSplitPayError);
      return ForbiddenSplitPayError;
    }

    const isHistoricalOpenOrder = !mrsFlag && transaction.isOpen && !transaction.isOnlineOrder;
    // only historical open orders for client can be checkout
    if (isHistoricalOpenOrder) {
      onFinished(SuccessMessage());
      return SuccessMessage();
    }

    onFinished(ForbiddenCheckOutError);
    return ForbiddenCheckOutError;
  }

  if (!isMaster && orderOperation === OrderOperationEnum.Cancel) {
    onFinished(ForbiddenCancelError);
    return ForbiddenCancelError;
  }

  if (!isMaster && orderOperation === OrderOperationEnum.Refund) {
    onFinished(ForbiddenRefundError);
    return ForbiddenRefundError;
  }

  const offline = !isClientRunning || !ip;
  const saveOperations = [OrderOperationEnum.Save, OrderOperationEnum.MergeAndSave, OrderOperationEnum.SplitAndSave];
  if (!mrsFlag && offline && !saveOperations.includes(orderOperation)) {
    // historical local orders can not be updated or deleted
    if (!isMaster && !transaction.isOnlineOrder) {
      onFinished(OnlyCheckoutError);
      return OnlyCheckoutError;
    } else if (transaction.isOnlineOrder) {
      onFinished(SuccessMessage());
      return SuccessMessage();
    }
  }

  if (!ip) {
    onFinished(ProposerNoNetError);
    return ProposerNoNetError;
  }

  if (!isClientRunning) {
    yield put(updateMRSSate({ clientConnected: false }));
    onFinished(ProposerOfflineError);
    return ProposerOfflineError;
  }

  onFinished(SuccessMessage());
  return SuccessMessage();
}

export function* requestProposalActionSaga(action: Action<ProposeType>) {
  const pid = yield select(selectSlavePid);
  const proposal = action.payload;

  const { data, onComplete, operation, showLoading = true, retryFlag } = proposal;
  if (showLoading) {
    yield put(toggleLoadingMask({ visible: true }));
  }

  const result: SagaReturnType<typeof checkProposeStateError> = yield call(checkProposeStateError, data);
  if (result.errorCode === IsLearningError.errorCode && !retryFlag) {
    yield delay(3 * 1000);
    action.payload.retryFlag = true;
    return yield call(requestProposalActionSaga, action);
  }
  if (result.errorCode !== SuccessCode) {
    resetProposalFlags();
    logMRSEvent(MRSAction.Proposal, ProposalFlow.PROPOSER_CHECK_ERROR, {
      type: operation,
      data: {
        errorCode: result.errorCode,
        message: proposal,
      },
      reason: result.errorMessage,
    });
    if (onComplete) yield call(onComplete, result);
    if (showLoading) {
      yield put(toggleLoadingMask({ visible: false }));
    }
    return result;
  }
  globalConfig.proposalId = String(Date.now());
  globalConfig.isProposing = true;

  const clientIp = yield select(selectClientIp);
  const message: Message = {
    messageSender: MessageSender.PROPOSER,
    data,
    uuid: globalConfig.proposalId,
    pid,
    dataType: DataType.PROPOSAL,
    proposer: clientIp,
    summary: operation,
  };
  yield call(sendMessageToServer, message);

  const _action = yield take(proposerCompleteAction.toString());
  resetProposalFlags();
  const errorCode = get(_action, 'payload.errorCode', -1);
  if (errorCode === NeedLearnError.errorCode && !retryFlag) {
    yield delay(3 * 1000);
    action.payload.retryFlag = true;
    return yield call(requestProposalActionSaga, action);
  }

  const errorMessage = get(_action, 'payload.errorMessage', '');

  logMRSEvent(MRSAction.Proposal, retryFlag ? ProposalFlow.PROPOSAL_RETRY_COMPLETE : ProposalFlow.PROPOSAL_COMPLETE, {
    reason: errorCode !== SuccessCode ? errorMessage : '',
    type: operation,
    flowId: globalConfig.proposalId,
    workflowStatus: WorkflowStatus.End,
    data: {
      message: errorCode !== SuccessCode ? message : '',
      errorCode: errorCode,
      order: {
        orderId: data.map(it => it.transaction?.receiptNumber).join(','),
        transactionId: data.map(it => it.transaction?.transactionId).join(','),
      },
    },
  });
  if (showLoading) {
    yield put(toggleLoadingMask({ visible: false }));
  }
  if (onComplete) yield call(onComplete, { errorCode, errorMessage });
  return { errorCode, errorMessage };
}

export const handleProposalResult = function* (message: Message) {
  if (globalConfig.proposalId === message.uuid) {
    const { isAccepted, needSyncUp, result } = message.data;
    yield put(proposerCompleteAction(isAccepted ? SuccessMessage('proposal succeed!') : result));
    if (needSyncUp) {
      yield put(performLearnerAction({ source: 'handleProposalResult' }));
    }
  } else {
    yield put(
      proposerCompleteAction({
        errorCode: ProposerReceivedError.errorCode,
        errorMessage: `globalConfig.proposalId: ${globalConfig.proposalId}, uuid: ${message.uuid}`,
      })
    );
  }
};

export const handleProposalTimeout = function* (message: Message) {
  if (globalConfig.proposalId === message.uuid) {
    resetProposalFlags();
    yield put(proposerCompleteAction(ProposalTimeOutError));
  }
};

/**
 * save upload information to transactionLogs and increase PID
 * @param transaction
 * @returns
 */
export const proposeSyncInfo = function* (data: ProposeDataType[]) {
  const isMaster = yield select(selectIsMaster);
  if (isMaster && data.length > 0) {
    yield call(sendMessageToServer, {
      messageSender: MessageSender.PROPOSER,
      data,
      uuid: createMrsUUID(),
      dataType: DataType.UPLOAD_ORDER,
    });
  }
};

/**
 * save bump/unbump to transactionLogs and increase PID
 * @param transaction
 * @returns
 */
export const sendKdsUpdateToMRS = function* (transaction: TransactionType) {
  if (!transaction) {
    return;
  }
  const isMaster = yield select(selectIsMaster);
  if (!isMaster || transaction.isOnlineOrder) {
    return;
  }
  if (transaction.mrs || transaction.isOpen || transaction.isOpenOrder) {
    const data: ProposeDataType[] = [
      {
        operationType: OperationType.UPDATE,
        updateSilence: true,
        // ignore the registerId changes from confirmNcsDate
        transaction: { ...transaction, registerId: undefined } as TransactionType,
      },
    ];
    yield call(sendMessageToServer, {
      messageSender: MessageSender.PROPOSER,
      data,
      uuid: createMrsUUID(),
      dataType: DataType.BUMP_ORDER,
    });
  }
};

// #region edits on client
/**
 * limit bump/unbump when order is editing by MRS, avoiding the loss of cookingStatus
 */
function* clientSplitOrderSaga() {
  const isMrsEnabled = yield select(selectIsEnabledMRS);
  if (!isMrsEnabled) {
    return;
  }
  const splitMasterOrder: TransactionType = yield select(selectSplitTargetOrder);
  const transactionId = splitMasterOrder?.transactionId;
  if (transactionId) {
    const message: Message = {
      messageSender: MessageSender.PROPOSER,
      data: [transactionId],
      uuid: String(Date.now()),
      dataType: DataType.EDIT_OPEN_ORDER,
      summary: OrderOperationEnum.Split,
    };
    yield call(sendMessageToServer, message, false);
  }
}

function* clientMergeOrderSaga() {
  const isMrsEnabled = yield select(selectIsEnabledMRS);
  if (!isMrsEnabled) {
    return;
  }
  const ids: string[] = yield select(selectMergedBranchOrderIds);
  if (ids.length > 0) {
    const message: Message = {
      messageSender: MessageSender.PROPOSER,
      data: ids,
      uuid: String(Date.now()),
      dataType: DataType.EDIT_OPEN_ORDER,
      summary: OrderOperationEnum.Merge,
    };
    yield call(sendMessageToServer, message, false);
  }
}

function* clientCompleteEditOrderSaga() {
  const isMrsEnabled = yield select(selectIsEnabledMRS);
  if (!isMrsEnabled) {
    return;
  }
  const message: Message = {
    messageSender: MessageSender.PROPOSER,
    uuid: createMrsUUID(),
    dataType: DataType.EDIT_OPEN_ORDER,
    summary: OrderOperationEnum.CompleteEdit,
  };
  yield call(sendMessageToServer, message, false);
}

// #endregion

export function* proposerSaga() {
  yield takeLatest(checkLimitBeforeExecute.toString(), checkLimitBeforeExecuteSaga);
  yield takeLatest(requestProposalAction.toString(), requestProposalActionSaga);
  yield takeLatest([initSplitOrder.toString(), exchangeSplitOrder.toString()], clientSplitOrderSaga);
  yield takeLatest(setMergedBranchOrderIds.toString(), clientMergeOrderSaga);
  yield takeLatest(completeMergeSplitOpenOrder.toString(), clientCompleteEditOrderSaga);
}

export default fork(proposerSaga);

export const resetProposalFlags = () => {
  globalConfig.isProposing = false;
  globalConfig.proposalId = '';
};
