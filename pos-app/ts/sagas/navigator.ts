import Intercom from '@intercom/intercom-react-native';
import { call, fork, put, select, takeLatest } from 'redux-saga/effects';
import { clearEmployee, deactivateAllRegisters, deactivateRegister, setCurrentEmployee, signOut, stopCheckCustomerInfoFromQR } from '../actions';
import * as NavigationService from '../navigation/navigatorService';
import { infoPOSBasicEvent, insertMobileData, POSBasicAction } from '../utils/logComponent';
import { selectRegisterId } from './selector';

import * as Actions from '../actions';
import { getUnNullValue } from '../utils';
import LocalLogger from '../utils/logComponent/local-logging/LocalLogger';
import { forceDeactivation } from './dataSync';

export const signOutSaga = function* (action) {
  infoPOSBasicEvent({ action: POSBasicAction.SignOut, privateDataPayload: { event: getUnNullValue(action, 'payload.event', '') }, destination: 'both' });
  insertMobileData({ employeeId: '' });
  yield put(stopCheckCustomerInfoFromQR({ isTemporary: true }));
  NavigationService.resetToLogOut();
  yield put(clearEmployee());
  yield call(employeeSignOutSaga);
  Intercom.logout();
};

export const deactivateRegisterSaga = function* (action) {
  const registerId = yield select(selectRegisterId);
  yield put(Actions.deactivate({ registerId }));
  yield call(forceDeactivation);
};

export const deactivateAllRegistersSaga = function* (action) {
  const registerId = yield select(selectRegisterId);
  for (let i = 0; i < 50; i++) {
    if (i !== registerId) {
      yield put(Actions.deactivate({ registerId: String(i) }));
    }
  }
  yield put(Actions.deactivate({ registerId }));
  yield call(forceDeactivation);
};

// used when sign out or deactivate
export const employeeSignInSaga = function* (action) {
  yield put(Actions.cleanLocalStorage(false));
  yield put(Actions.monitoringNetworkQuality(false));
  yield call([LocalLogger, LocalLogger.updateLogModel], {
    employeeId: getUnNullValue(action, 'payload.employeeId', ''),
  });
  infoPOSBasicEvent({ action: POSBasicAction.SignIn, destination: 'local' });
};

// used when sign out or deactivate
export const employeeSignOutSaga = function* () {
  yield put(Actions.cleanLocalStorage(true));
  yield put(Actions.monitoringNetworkQuality(true));
  yield call([LocalLogger, LocalLogger.updateLogModel], {
    employeeId: '',
  });
};

function* navigatorSaga() {
  yield takeLatest(signOut.toString(), signOutSaga);
  yield takeLatest(deactivateRegister.toString(), deactivateRegisterSaga);
  yield takeLatest(deactivateAllRegisters.toString(), deactivateAllRegistersSaga);
  yield takeLatest(setCurrentEmployee.toString(), employeeSignInSaga);
}
export default fork(navigatorSaga);
