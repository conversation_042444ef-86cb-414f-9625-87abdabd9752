import { get, last, map } from 'lodash';
import { call, select, spawn } from 'redux-saga/effects';
import DAL from '../../../dal';
import { NcsSettingType } from '../../../reducers/ncs';
import { PageKdsTransType } from '../../../utils/kds/message';
import { CookingStatus } from '../../../utils/kds/status';
import { logNcsTransaction, NcsTransactionLogEvent } from '../../../utils/logComponent/buz/kds';
import { NCS_PAGE_SIZE } from '../../../utils/ncs';
import { WsBuzCode, WsCommand } from '../../../utils/websocket';
import { getConfirmedKDSTransaction } from '../../kds/transaction/helper';
import { selectAvailableNcs, selectRegisterObjectId, selectSocketIdByNcsId } from '../../selector';
import { PayloadOptions } from '../message/create';
import { sendNcsMessageSaga } from '../message/send';

function getPreparingTransactions(startQueryDate: Date, registerId: string) {
  const result = DAL.getNcsTransaction(registerId, NCS_PAGE_SIZE, CookingStatus.pending, startQueryDate, undefined);
  return result?.list ?? [];
}

function getReadyTransactions(startQueryDate: Date, registerId: string) {
  const result = DAL.getNcsTransaction(registerId, NCS_PAGE_SIZE, CookingStatus.served, startQueryDate, undefined);
  return result?.list ?? [];
}

export function* getAllNcsTransactions(registerId: string) {
  const now = Date.now();
  const startQueryDate = new Date(now - 2 * 60 * 60 * 1000 - 60 * 1000); // 2h + 1m

  const transactions = getPreparingTransactions(startQueryDate, registerId);
  transactions.push(...getReadyTransactions(startQueryDate, registerId));
  return yield call(getConfirmedKDSTransaction, transactions);
}

export function* pushAllNcsTransactionsSaga(ncsId: string) {
  const registerId = yield select(selectRegisterObjectId);
  const socketId = yield select(selectSocketIdByNcsId(ncsId));

  if (socketId) {
    let errorMessage = '';
    const startResponse = yield call(sendNcsMessageSaga, { command: WsCommand.posPushStart, socketId });
    if (!startResponse) {
      return;
    }
    const transactions = yield call(getAllNcsTransactions, registerId);

    if (transactions.length !== 0) {
      const lastPushDate = get(last(transactions), 'pushNcsDate', '');
      const pushData: PageKdsTransType = { list: transactions, total: transactions.length, lastPushDate: new Date(lastPushDate).toISOString() };
      logNcsTransaction(NcsTransactionLogEvent.pushAll, {
        queryInfo: {
          orderIds: map(transactions, it => it.transactionId).join(','),
          ncsId,
          socketId,
          command: WsCommand.posPushOrder,
        },
      });
      const sendResult = yield call(sendNcsMessageSaga, {
        command: WsCommand.posPushOrder,
        socketId,
        payloadOptions: { data: pushData },
      });

      if (!sendResult) {
        errorMessage = 'send failed';
      }
    }
    const payload: PayloadOptions = {
      code: errorMessage ? WsBuzCode.posPushError : WsBuzCode.ok,
      data: !Boolean(errorMessage),
      errorMessage,
    };
    yield call(sendNcsMessageSaga, { command: WsCommand.posPushEnd, socketId, payloadOptions: payload });
  }
}

/**
 * bump/unbump order
 */
export function* pushNcsWhenUpdateOrder(payloadOptions: PayloadOptions) {
  if (get(payloadOptions, 'data.isOpenOrder') || get(payloadOptions, 'data.isOpen') || get(payloadOptions, 'data.isPayLater')) {
    return;
  }
  const cookingStatus = get(payloadOptions, 'data.cookingStatus', '');
  if (cookingStatus === CookingStatus.pending || cookingStatus === CookingStatus.served) {
    const availableNcs: NcsSettingType[] = yield select(selectAvailableNcs);
    for (const ncs of availableNcs) {
      logNcsTransaction(NcsTransactionLogEvent.updateTransaction, {
        queryInfo: {
          ncsId: ncs.id,
          orderIds: get(payloadOptions, 'data.transactionId', ''),
        },
      });
      yield spawn(sendNcsMessageSaga, {
        command: WsCommand.posUpdateOrder,
        payloadOptions,
        socketId: ncs.socketId,
      });
    }
  }
}
