import { createSelector } from 'reselect';
import { MallIntegrationChannel } from '../constants';
import { selectMallIntegrationChannel } from './selector';

export const selectSmMallLimited = createSelector(selectMallIntegrationChannel, channel =>
  [MallIntegrationChannel.SM_Hypermarket, MallIntegrationChannel.SM_Supermarket, MallIntegrationChannel.SM_Savemore_Market].includes(
    channel as MallIntegrationChannel
  )
);
