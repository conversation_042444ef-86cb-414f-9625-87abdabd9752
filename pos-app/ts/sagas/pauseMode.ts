import { get } from 'lodash';
import moment from 'moment';
import { Action } from 'redux-actions';
import { fork, put, select, take, takeLatest } from 'redux-saga/effects';
import {
  AsycCallbackType,
  CloseDeliveryType,
  PauseModeEnum,
  closeDelivery,
  disablePauseMode,
  enablePauseMode,
  openForDelivery,
  pauseBeepDelivery,
  queryBeepPauseModeSettings,
  resumeBeepDelivery,
  setPauseMode,
  updateGeneralSettings,
  updatePauseMode,
} from '../actions';
import { selectBusinessName, selectIsBeepDeliveryEnabled, selectPauseBeepDelivery, selectStoreId } from './selector';

export const closeDeliverySaga = function* (action: Action<CloseDeliveryType>) {
  const { pauseMode, onSuccess, onFailure } = action.payload;
  const storeId = yield select(selectStoreId);
  const businessName = yield select(selectBusinessName);
  yield put(
    pauseBeepDelivery({
      storeId,
      businessName,
      pauseMode,
    })
  );
  const responseAction = yield take([pauseBeepDelivery.toString() + '.success', pauseBeepDelivery.toString() + '.failure']);
  const resp = responseAction.payload;
  if (responseAction.type === pauseBeepDelivery.toString() + '.success') {
    yield put(setPauseMode(resp.pauseBeep));
    yield put(
      updateGeneralSettings({
        pauseBeepDelivery: pauseMode,
      })
    );
    yield put(enablePauseMode(pauseMode));
    onSuccess && onSuccess(resp.pauseBeep);
  } else {
    onFailure && onFailure(resp.pauseBeep);
  }
};

export const openForDeliverySaga = function* (action: Action<AsycCallbackType>) {
  const { onSuccess, onFailure } = action.payload;
  const storeId = yield select(selectStoreId);
  const businessName = yield select(selectBusinessName);
  yield put(
    resumeBeepDelivery({
      storeId,
      businessName,
    })
  );
  const responseAction = yield take([resumeBeepDelivery.toString() + '.success', resumeBeepDelivery.toString() + '.failure']);
  const resp = responseAction.payload;
  if (responseAction.type === resumeBeepDelivery.toString() + '.success') {
    yield put(setPauseMode({ paused: false }));
    yield put(
      updateGeneralSettings({
        pauseBeepDelivery: null,
      })
    );
    onSuccess && onSuccess(resp);
    yield put(disablePauseMode());
  } else {
    onFailure && onFailure(resp);
  }
};

export const updatePauseModeSaga = function* () {
  const isBeepDeliveryEnabled = yield select(selectIsBeepDeliveryEnabled);
  if (!isBeepDeliveryEnabled) {
    return;
  }
  const storeId = yield select(selectStoreId);
  const businessName = yield select(selectBusinessName);
  yield put(
    queryBeepPauseModeSettings({
      storeId,
      businessName,
    })
  );
  const responseAction = yield take([queryBeepPauseModeSettings.toString() + '.success', queryBeepPauseModeSettings.toString() + '.failure']);
  const resp = responseAction.payload;
  if (responseAction.type === queryBeepPauseModeSettings.toString() + '.success') {
    yield put(setPauseMode(resp.beepPauseModeSettings));
    const isPaused = get(resp.beepPauseModeSettings, 'paused', false);
    const pauseValue = yield select(selectPauseBeepDelivery);
    // case: multiple registers
    if (!isPaused && pauseValue) {
      yield put(
        updateGeneralSettings({
          pauseBeepDelivery: null,
        })
      );
    } else if (isPaused) {
      const pausedUntil = get(resp.beepPauseModeSettings, 'pausedUntil', 0);
      let pauseBeepDelivery = null;
      if (pausedUntil === moment().endOf('day').valueOf()) {
        pauseBeepDelivery = PauseModeEnum.EOD;
      } else if (moment().add(30, 'minutes').valueOf() > pausedUntil) {
        pauseBeepDelivery = PauseModeEnum.MINS_30;
      } else if (pausedUntil) {
        pauseBeepDelivery = PauseModeEnum.MINS_60;
      }
      yield put(
        updateGeneralSettings({
          pauseBeepDelivery,
        })
      );
    }
  }
};

export function* pauseMode() {
  yield takeLatest(closeDelivery.toString(), closeDeliverySaga);
  yield takeLatest(openForDelivery.toString(), openForDeliverySaga);
  yield takeLatest(updatePauseMode.toString(), updatePauseModeSaga);
}

export default fork(pauseMode);
