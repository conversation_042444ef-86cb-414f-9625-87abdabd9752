import * as Immutable from 'immutable';
import { find, get, isNumber } from 'lodash';

import { call, put, select } from 'redux-saga/effects';
import { PrinterConfigType, autoSetDefaultNetworkAfterSuccessfulPrinting, setDefaultNetwork } from '../../../actions';
import { TIMEZONE } from '../../../config';
import { t } from '../../../constants';

import { <PERSON>er<PERSON><PERSON><PERSON><PERSON><PERSON>, TransactionHelper } from '../../../dal/helper';
import { PurchasedItemType } from '../../../typings';
import { getUnNullValue } from '../../../utils';
import { MixpanelManagerInstance } from '../../../utils/Analytics';
import { createDate } from '../../../utils/datetime';

import {
  selectAutoSetDefaultNetworkAfterSuccessfulPrinting,
  selectCurrentNetworkInfo,
  selectDefaultNetwork,
  selectEnableAutoAssignDefaultNetwork,
  selectEnablePoints,
  selectIsClient,
  selectPrinterTagsSettings,
  selectStoreInfo,
  selectTimezone,
} from './../../selector';

export const getStoreInfo = function* () {
  const immutableStoreInfo = yield select(selectStoreInfo);
  const registerId = String(immutableStoreInfo.get('registerId', ''));
  const business = immutableStoreInfo.get('name', '');

  const immutableStore = immutableStoreInfo.get('store', Immutable.Map());

  const companyName = immutableStore.get('companyName', '');
  const storeName = immutableStore.get('name');
  const street1 = immutableStore.get('street1');
  const street2 = immutableStore.get('street2');
  const city = immutableStore.get('city');
  const state = immutableStore.get('state');
  const postalCode = immutableStore.get('postalCode');
  const address = [street1, street2, city, state, postalCode].filter(v => Boolean(v)).join(' ');
  const phone = immutableStore.get('phone', '');
  const currency = immutableStore.get('currency', '');
  const receiptType = immutableStore.get('receiptType', '');
  const disableTitle = immutableStore.get('disableTitle', false);

  const country = immutableStore.get('country', '');
  const gstIdNo = immutableStore.get('gstIdNo', '');
  const minNo = immutableStore.get('minNo', '');
  const sstIdNo = immutableStore.get('sstIdNo', '');
  const serviceChargeRate = immutableStore.get('serviceChargeRate', 0.0);

  const showStoreName = immutableStore.get('showStoreName', true);
  const showBarcode = immutableStore.get('showBarcode', true);
  const showNotes = immutableStore.get('showNotes', true);
  const taxNameOnReceipt = immutableStore.get('taxNameOnReceipt', '');
  const poweredBy = immutableStore.get('poweredBy', true);
  const showCustomerInfo = immutableStore.get('showCustomerInfo') ? immutableStore.get('showCustomerInfo').toJS() : [];

  const autoOrderId = immutableStore.get('autoOrderId', '');
  const assignTableID = immutableStore.get('assignTableID', '');

  const includingTaxInDisplay = immutableStore.get('includingTaxInDisplay', false);

  const brn = immutableStore.get('brn', '');
  const notes = immutableStore.get('notes', '');
  const immutatleCfdDisplay = immutableStore.get('cfdDisplay') || Immutable.Map();
  const cfdDisplay = immutatleCfdDisplay.toJS();
  const defaultLoyaltyRatio = immutableStore.get('defaultLoyaltyRatio', 0.0);
  const enableCashback = immutableStore.get('enableCashback', false);
  const disableCashbackFromPOS = immutableStore.get('disableCashbackFromPOS', false);
  const enableServiceCharge = immutableStore.get('enableServiceCharge', false);
  const birAccrNo = immutableStore.get('birAccrNo', '');
  const birAccredited = immutableStore.get('birAccredited', false);
  const serialNo = immutableStore.get('serialNo', '');
  const ptu = immutableStore.get('ptu', '');
  const ptuDateIssued = immutableStore.get('ptuDateIssued', '');
  const birAccrInfo = immutableStore.get('birAccrInfo', '');
  const isVATRegistered = immutableStore.get('isVATRegistered', '');
  const roundAllTransactions = immutableStore.get('roundAllTransactions', false);
  const membershipEnabled = immutableStore.get('membershipEnabled', false);
  const enablePoints = yield select(selectEnablePoints);

  return {
    registerId,
    business,
    companyName,
    storeName,
    address,
    phone,
    currency,
    country,
    gstIdNo,
    minNo,
    sstIdNo,
    serviceChargeRate,
    showStoreName,
    showBarcode,
    showCustomerInfo,
    showNotes,
    taxNameOnReceipt,
    poweredBy,
    brn,
    autoOrderId,
    assignTableID,
    includingTaxInDisplay,
    notes,
    cfdDisplay,
    defaultLoyaltyRatio,
    enableCashback,
    enableServiceCharge,
    birAccredited,
    birAccrNo,
    serialNo,
    ptu,
    ptuDateIssued,
    birAccrInfo,
    isVATRegistered,
    disableCashbackFromPOS,
    roundAllTransactions,
    receiptType,
    disableTitle,
    membershipEnabled,
    enablePoints,
  };
};

export const formatDateString = datetime => {
  return createDate(datetime);
};

export const copyRealmTransaction = realmTransaction => {
  if (!realmTransaction) {
    return null;
  } else {
    const result: any = TransactionHelper.serializeTransaction(realmTransaction);
    if (result && !isNumber(result.serviceCharge)) {
      result.serviceCharge = 0.0;
    }
    return result;
  }
};

export const getReceiptPrinterId = function* () {
  const printerTagsSettings = yield select(selectPrinterTagsSettings);
  const printers = printerTagsSettings.toJS();
  const receiptPrinter = find(printers, settings => {
    return settings && settings.isOnline && settings.isReceiptPrinter === true;
  });
  return get(receiptPrinter, 'printerId', '');
};

export const getBuiltInPrinterId = function* () {
  const printerTagsSettings = yield select(selectPrinterTagsSettings);
  const printers = printerTagsSettings.toJS();
  const receiptPrinter = find(printers, settings => {
    return settings && settings.isOnline && settings.isBuiltInPrinter === true;
  });
  return get(receiptPrinter, 'printerId', '');
};

//  online order selectedOptions -> options
//  printerJob  kitchenPrinter  diffIdprinterJob needed
export type AllPurchasedItemType = PurchasedItemType & { selectedOptions?: any[] } & { kitchenPrinter?: string; diffId?: string; comments?: string };

export const getPrinterName = (printer: PrinterConfigType) => {
  let _name;
  if (Boolean(printer.printerName)) {
    _name = printer.printerName;
  } else {
    _name = `${printer.printerConnectType} Printer (${printer.usbPath})`;
  }
  return _name === 'Generic Bulk Device' ? t('Built-in Printer') : _name;
};

export function* getCurrentTimezone() {
  const storeTimezone = yield select(selectTimezone);
  return storeTimezone || TIMEZONE;
}

export const getPrinterNotification = (schemaId: string, isSubOrder = false) => {
  let notification;
  if (isSubOrder) {
    notification = PrinterJobHelper.getSubOrderNotificationById(schemaId);
  } else {
    notification = PrinterJobHelper.getBeepNotificationById(schemaId);
  }
  return notification;
};

export const findPrinterConnectionTypeByPrinterId = function* (printerId: string) {
  const printerTagsSettings = yield select(selectPrinterTagsSettings);
  const printers = printerTagsSettings.toJS();
  const printer = printers.find(v => v.printerId === printerId);
  return printer ? printer.printerConnectType : '';
};

export const haveSetDefaultNetwork = function* () {
  const ImmutableDefaultNetwork = yield select(selectDefaultNetwork);
  const defaultNetwork = ImmutableDefaultNetwork.toJS();
  const defaultWifiName = getUnNullValue(defaultNetwork, 'details.ssid', '');
  const hasDefaultWifi = !!defaultWifiName;
  const hadDefaultEnternet = !!getUnNullValue(defaultNetwork, 'details.ipAddress', '');
  return hasDefaultWifi || hadDefaultEnternet;
};

export const checkIfAnyPrinterIsLAN = (printerIds, printers) => {
  return printerIds.some(printerId => {
    const printer = printers.find(p => p.printerId === printerId);
    return printer && printer.printerConnectType === 'LAN';
  });
};

export const checkIfIsMRSClient = function* () {
  return yield select(selectIsClient);
};

export const setDefaultNetworkAfterReceiptPrintingSuccess = function* (printerId: string) {
  const isClient = yield checkIfIsMRSClient();
  if (isClient) {
    return;
  }

  const enableAutoAssignDefaultNetwork = yield select(selectEnableAutoAssignDefaultNetwork);
  if (!enableAutoAssignDefaultNetwork) {
    return;
  }

  const haveSetDefaultNetworkBefore = yield select(selectAutoSetDefaultNetworkAfterSuccessfulPrinting);
  if (haveSetDefaultNetworkBefore) {
    return;
  }

  const printerConnectType = yield call(findPrinterConnectionTypeByPrinterId, printerId);
  if (printerConnectType !== 'LAN') {
    return;
  }

  const immutableNetInfo = yield select(selectCurrentNetworkInfo);
  const netInfo = immutableNetInfo.toJS();

  const networkType = get(netInfo, 'type');
  let networkName = '';
  if (networkType === 'wifi') {
    networkName = getUnNullValue(netInfo, 'details.ssid', '');
  } else if (networkType === 'ethernet') {
    networkName = getUnNullValue(netInfo, 'details.ipAddress', '');
  }
  MixpanelManagerInstance.throttledTrack('Auto assign default network', {
    Network: networkName,
  });

  yield put(setDefaultNetwork(netInfo));
  yield put(autoSetDefaultNetworkAfterSuccessfulPrinting());
};

export const setDefaultNetworkAfterKitchenOrOrderSummaryPrintingSuccess = function* (printerIds: string[]) {
  const isClient = yield checkIfIsMRSClient();
  if (isClient) {
    return;
  }

  const enableAutoAssignDefaultNetwork = yield select(selectEnableAutoAssignDefaultNetwork);
  if (!enableAutoAssignDefaultNetwork) {
    return;
  }

  const haveSetDefaultNetworkBefore = yield select(selectAutoSetDefaultNetworkAfterSuccessfulPrinting);
  if (haveSetDefaultNetworkBefore) {
    return;
  }

  const printerTagsSettings = yield select(selectPrinterTagsSettings);
  const printers = printerTagsSettings.toJS();
  const hasLAN = checkIfAnyPrinterIsLAN(printerIds, printers);
  if (!hasLAN) {
    return;
  }

  const immutableNetInfo = yield select(selectCurrentNetworkInfo);
  const netInfo = immutableNetInfo.toJS();

  const networkType = get(netInfo, 'type');
  let networkName = '';
  if (networkType === 'wifi') {
    networkName = getUnNullValue(netInfo, 'details.ssid', '');
  } else if (networkType === 'ethernet') {
    networkName = getUnNullValue(netInfo, 'details.ipAddress', '');
  }
  MixpanelManagerInstance.throttledTrack('Auto assign default network', {
    Network: networkName,
  });

  yield put(setDefaultNetwork(netInfo));
  yield put(autoSetDefaultNetworkAfterSuccessfulPrinting());
};
