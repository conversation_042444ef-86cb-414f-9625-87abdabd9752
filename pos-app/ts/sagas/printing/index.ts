import { forEach, get, isArray, isEmpty, join, last, map } from 'lodash';
import { all, call, delay, fork, put, select, take, takeEvery, takeLatest, takeLeading } from 'redux-saga/effects';
import * as Actions from '../../actions';
import { DissmissPrinterJobType, IntervalPrintTestPayload, notifyPrinterJob, PrintEInvoiceQrReceiptType, upsertPrinterErrorJobs } from '../../actions';

import { BIRStoreType, isLanXPrinter, OrderChannel, PrintingBusinessType, t, TransactionFlowType } from '../../constants';
import DAL from '../../dal';
import { AyalaMallReportPrintType, BeepNotificationType, SubOrderNotificationType } from '../../typings';
import { getCountNumberString, getLocaleNumberString, getLocaleNumberStringWithDefault, getUnNullPrintString, safeCallback } from '../../utils';
import {
  selectAllPrin<PERSON>,
  selectAutoReprintByUdpTime,
  selectAutoReprintEnable,
  selectAutoSearchEnable,
  selectAutoSearchInterval,
  selectBusinessName,
  selectEmployeeId,
  selectGstIdNo,
  selectIndustry,
  selectMinNo,
  selectReceiptFontSize,
  selectSerialNo,
  selectStoreBrn,
  selectStoreCity,
  selectStoreCompanyName,
  selectStoreId,
  selectStoreName,
  selectStorePhone,
  selectStorePostalCode,
  selectStoreState,
  selectStoreStreet1,
  selectStoreStreet2,
} from './../selector';

import PrinterManager, { getReceiptFontScale, ReceiptFontSize } from '../../utils/printer';

import { getBuiltInPrinterId, getReceiptPrinterId, getSampleReceiptData } from './common';

import { createDate } from '../../utils/datetime';
import { logDBOperationEvent, LoggingLevel, PrinterAction, trackLogEvent, WorkflowStatus } from '../../utils/logComponent';

import { Action } from 'redux-actions';

import { openPrinterErrorToast } from '../../components/common/TopNotification';
import { PrinterJobHelper } from '../../dal/helper';
import { realmObjectToJSObject, tryFreeRealmObject } from '../../utils/json';

import moment from 'moment';

import { getPosEInvoiceDomain } from '../../config';

import { KitchenEvent, KitchenManager } from '../../models/print/manager/KitchenManager';
import { ReceiptManager } from '../../models/print/manager/ReceiptManager';
import { ReportManager } from '../../models/print/manager/ReportManager';
import { hexMd5 } from '../../utils/md5';

import { JobItemType } from '../../dal/helper/printerJobHelper';
import { PrintTaskResult } from '../../models/print/task/AbstractPrintTask';
import {
  checkAssignedPrinterSaga,
  forgetPrinterSaga,
  handleUpdatePrinter,
  initPrinterSaga,
  pingLanPrintersSaga,
  searchPrinterSaga,
  updatePrinterTagSaga,
} from './printer';

export const ShiftReportSalesSummaryTitles = {
  totalSalesTitle: t('Total Sales'),
  totalRefundsTitle: 'Total Refunds',
  totalNetTitle: 'Total Net',
};

export const paymentSummaryTitleStrings = {
  salesTitle: 'Sales',
  refundsTitle: 'Refunds',
  netTitle: 'Net',
};

export const cashBackSummaryTitleStrings = {
  discountTitle: 'Discount',
};

export const cancelAndDiscountSummaryTitles = {
  discountTitle: 'Discount',
  cancelTxnsTitle: 'Cancel Txns',
};

export const serviceChargeSummaryTitles = {
  salesTitle: 'Sales',
  refundsTitle: 'Refunds',
};

export const cashDrawerSummaryTitles = {
  openingAmountTitle: 'Opening Amount',
  cashSalesTitle: 'Cash Sales',
  cashDepositsTitle: 'Cash Deposits',
  cashRefundsTitle: 'Cash Refunds',
  payOutTitle: 'Pay out',
  payInTitle: 'Pay In',
};

export const columnTitles5 = [t('LItem'), t('Price'), t('Qty'), t('Discount'), t('Amount')];
export const columnWidths5 = ['12%', '25%', '18%', '25%', '25%'];

export const columnTitles4 = [t('LItem'), t('Price'), t('Qty'), t('Amount')];
export const columnWidths4 = ['33%', '21%', '18%', '28%'];

export const routePrintReceiptSaga = function* (action) {
  yield call(ReceiptManager.newPrintReceipt, action);
};

export const printBeepQRTicketSaga = function* (action) {
  if (!action.payload) {
    return { status: false, message: 'payload is null' };
  }
  const { tableName, expirationHours, url, qrCode } = action.payload;

  PrinterManager.bindService();

  const receiptPrinterId = yield call(getReceiptPrinterId);

  if (!Boolean(receiptPrinterId)) {
    yield put(Actions.toggleToastInfo({ visible: true, text: t('NoPrinterError') }));
    return { status: false, message: "Receipt Printer hasn't been setup yet. Please go to Settings to setup first." };
  }

  const expiredTimeStr = moment().subtract(-expirationHours, 'hour').format('YYYY/MM/DD h:mm A');
  const model = {
    title: t('Scan to Order Now'),
    url,
    qrCode,
    tableName,
    expiredTime: t('Expires at data time', { time: expiredTimeStr }),
    footer: t('POWERED BY'),
    receiptFontScale: 1.0,
  };

  // const receiptFontSize = yield select(selectReceiptFontSize);
  // model.receiptFontScale = getReceiptFontScale(receiptFontSize)

  const requestPrintingModel = [
    {
      printerId: receiptPrinterId,
      businessType: PrintingBusinessType.DYNAMIC_BEEP_QR,
      data: model,
    },
  ];

  const { errCode, errMessage } = yield call(PrinterManager.requestPrinting, requestPrintingModel);

  const status = errCode === 0;

  if (!status) {
    yield put(Actions.toggleToastInfo({ visible: true, text: String(errMessage) }));
  }
  return { status, message: errMessage };
};

const generateMarkPaidSign = (params, salt) => {
  const keys = Object.keys(params);
  let str = '';
  keys.sort().forEach(key => (str += `${key}=${params[key]}&`));
  str = str.substr(0, str.length - 1);
  str += salt;
  return hexMd5(str);
};

export const generateEInvoiceUrl = (params: { merchantName: string; receiptNumber: string; channel: number }) => {
  const salt = params.receiptNumber;
  const qs = Object.entries({
    h: generateMarkPaidSign(params, salt),
    ...params,
  })
    .map(([key, val]) => `${encodeURIComponent(key)}=${encodeURIComponent(val)}`)
    .join('&');
  const url = getPosEInvoiceDomain() + `?${qs}`;
  console.log(`Signature = ${generateMarkPaidSign(params, salt)}, ${JSON.stringify(params)}`);
  console.log(url);
  return url;
};

export const printEInvoiceQrReceiptSaga = function* (action) {
  if (!action.payload) {
    return { status: false, message: 'payload is null' };
  }
  const { transaction } = action.payload as PrintEInvoiceQrReceiptType;

  PrinterManager.bindService();

  const receiptPrinterId = yield call(getReceiptPrinterId);

  const url = generateEInvoiceUrl({
    merchantName: yield select(selectBusinessName),
    receiptNumber: transaction.receiptNumber,
    channel: transaction.channel,
  });

  if (!Boolean(receiptPrinterId)) {
    yield put(Actions.toggleToastInfo({ visible: true, text: t('NoPrinterError') }));
    return { status: false, message: "Receipt Printer hasn't been setup yet. Please go to Settings to setup first." };
  }

  const printedDate = createDate(transaction.createdDate ?? (transaction as any).createdTime, 'YYYY-MM-DD HH:mm');

  const name = yield select(selectStoreCompanyName);
  const address = `${yield select(selectStoreStreet1)} ${yield select(selectStoreCity)} ${yield select(selectStoreState)} ${yield select(
    selectStorePostalCode
  )}`;

  const isRefund = transaction.transactionType === TransactionFlowType.Return;
  const isCancelled = transaction.isCancelled;

  const model = {
    storeName: name,
    storeAddress: address,
    storeBrn: 'Business Registration Number: ' + (yield select(selectStoreBrn)),
    no: 'Receipt No. : ' + transaction.receiptNumber,
    date: 'Date: ' + printedDate,
    total: 'Total: RM ' + getLocaleNumberString(transaction.total),
    qrData: url,
    qrDescription: isRefund ? t('SCAN_TO_VIEW_CREDIT_NOTE') : isCancelled ? t('SCAN_TO_VIEW_E_INVOICE') : t('SCAN_TO_CLAIM_E_INVOICE'),
    receiptFontScale: 1.0,
  };

  const requestPrintingModel = [
    {
      printerId: receiptPrinterId,
      businessType: PrintingBusinessType.E_INVOICE_QR,
      data: model,
    },
  ];

  const { errCode, errMessage } = yield call(PrinterManager.requestPrinting, requestPrintingModel);

  const status = errCode === 0;

  if (!status) {
    yield put(Actions.toggleToastInfo({ visible: true, text: String(errMessage) }));
  }
  return { status, message: errMessage };
};

export const printShiftReportSaga = function* (action) {
  const report = action.payload;
  yield call(ReportManager.printShiftReport, report);
};

export const openCashDrawerSaga = function* (action) {
  const reason = action.payload;
  if (Boolean(reason)) {
    yield put(Actions.employeeOpenCashDrawer({ reason }));
  }
  PrinterManager.bindService();
  const receiptPrinterId = yield call(getReceiptPrinterId);
  const builtInPrinterId = yield call(getBuiltInPrinterId);

  const openCashDrawerModel = [];
  if (Boolean(receiptPrinterId)) {
    openCashDrawerModel.push({
      printerId: receiptPrinterId,
    });
  }

  if (builtInPrinterId && builtInPrinterId != receiptPrinterId) {
    openCashDrawerModel.push({
      printerId: builtInPrinterId,
    });
  }
  const { errCode, errMessage } = yield call(PrinterManager.openCashDrawer, openCashDrawerModel);
  if (errCode !== 0 && errCode !== 10401) {
    yield put(Actions.toggleToastInfo({ visible: true, text: String(errMessage) }));
  }
};

export const newPrintOpenOrderReceiptSaga = function* (action) {
  const receiptPrinterId = yield call(getReceiptPrinterId);
  if (!Boolean(receiptPrinterId)) {
    yield put(Actions.toggleToastInfo({ visible: true, text: t('NoPrinterError') }));
    return;
  }
  yield call(ReceiptManager.newPrintHalfReceipt, action);
};

const printZReadingReportSaga = function* (action) {
  if (!action.payload || get(action.payload, 'length', 0) === 0) {
    return;
  }

  for (const zreadingReportData of action.payload) {
    yield call(ReportManager.printZReadingReport, zreadingReportData);
  }
};

export const printTodayBeepPreOrderSaga = function* (action) {
  const { onComplete, deliveryDate } = action.payload;
  const businessName = yield select(selectBusinessName);
  const storeId = yield select(selectStoreId);
  yield put(
    Actions.getOnlineOrderList({
      businessName,
      storeId,
      channel: OrderChannel.OrderChannelQRCode,
      status: [], // isPreOrder=true，接口忽略status参数
      syncTime: '',
      isPreOrder: true,
      fromDate: deliveryDate,
      toDate: deliveryDate,
    })
  );
  const responseAction = yield take([Actions.getOnlineOrderList.toString() + '.success', Actions.getOnlineOrderList.toString() + '.failure']);
  const actionPayload = responseAction.payload;
  const orderList = get(actionPayload, 'results');
  if (get(orderList, 'length') <= 0) {
    yield put(Actions.toggleToastInfo({ visible: true, text: String(t('No Pre-Orders today')) }));
    safeCallback(onComplete, {});
    return;
  }
  const printFailedOrderIds = [];
  for (const orderItem of orderList) {
    const orderId = get(orderItem, 'orderId');
    const pickUpId = get(orderItem, 'pickUpId');
    yield put(Actions.getOnlineOrderDetails({ orderId }));
    const detailResponseAction = yield take([Actions.getOnlineOrderDetails.toString() + '.success', Actions.getOnlineOrderDetails.toString() + '.failure']);

    if (detailResponseAction.type === Actions.getOnlineOrderDetails.toString() + '.failure') {
      printFailedOrderIds.push(pickUpId);
    } else {
      const order = detailResponseAction.payload;

      if (Boolean(order)) {
        order.isRemote = true; // set tag for print receipt
        const onComplete = {
          callback: ({ status, message }) => {
            if (!Boolean(status)) {
              printFailedOrderIds.push(pickUpId);
            }
          },
        };

        yield put(
          Actions.newPrintReceiptAction({
            transactionId: orderId,
            customer: null,
            isReprint: false,
            isNeedLog: false,
            onlineTransaction: order,
            onComplete,
            eventName: 'printTodayBeepPreOrder',
          })
        );
      } else {
        printFailedOrderIds.push(pickUpId);
      }
    }
  }
  safeCallback(onComplete, { printFailedOrderIds });
};
// printerJob

export const dismissPrinterJobSaga = function* (action: Action<DissmissPrinterJobType>) {
  const { submitId, orderId, jobId } = action.payload;

  if (submitId) {
    const subNoti = PrinterJobHelper.getSubOrderNotificationById(submitId);
    if (subNoti) {
      const oldJobs = subNoti.jobs;
      const index = oldJobs.findIndex(v => v.jobId === jobId);
      if (index !== -1) {
        const oldJob = oldJobs[index];
        oldJobs.splice(index, 1, { ...oldJob, isCancelled: true });
      }
      yield call(DAL.upsertSubOrderNotification, { submitId, jobs: oldJobs });
    }
  } else if (orderId) {
    const beepNoti = PrinterJobHelper.getBeepNotificationById(orderId);
    if (beepNoti) {
      const oldJobs = beepNoti.jobs;
      const index = oldJobs.findIndex(v => v.jobId === jobId);
      if (index !== -1) {
        if (beepNoti.isBeepOrder) {
          const oldJob = oldJobs[index];
          oldJobs.splice(index, 1, { ...oldJob, isCancelled: true });
        } else {
          oldJobs.splice(index, 1);
        }
      }
      if (oldJobs.length === 0 && !beepNoti.isBeepOrder) {
        // delete local order record
        yield call(DAL.deleteBeepNotificationById, orderId);
      } else {
        yield call(DAL.upsertBeepNotification, {
          orderId,
          jobs: oldJobs,
        });
      }
    }
  }
  logDBOperationEvent('dismissPrinterJob', null, WorkflowStatus.End, { orderId, submitId });

  yield call(notifyPrinterJobSaga, notifyPrinterJob(true));
};

export const dismissAllPrinterJobsSaga = function* () {
  const beepNotis = DAL.getBeepNotifications();
  if (beepNotis) {
    for (const beepNoti of beepNotis) {
      const { orderId, isBeepOrder } = beepNoti;
      const oldJobs = beepNoti.jobs;
      if (!isEmpty(oldJobs)) {
        if (isBeepOrder) {
          yield call(DAL.upsertBeepNotification, { orderId, jobs: [] });
        } else {
          yield call(DAL.deleteBeepNotificationById, orderId);
        }
        logDBOperationEvent('dismissAllPrinterJobs', null, WorkflowStatus.End, { orderId });
      }
    }
  }

  const subNotis = DAL.getSubOrderNotifications();
  if (subNotis) {
    for (const subNoti of subNotis) {
      const oldJobs = subNoti.jobs;
      const { submitId, orderId } = subNoti;
      if (!isEmpty(oldJobs)) {
        const newJobs = map(oldJobs, job => {
          const plainJob = tryFreeRealmObject(job, true);
          return { ...plainJob, isCancelled: true };
        });
        yield call(DAL.upsertSubOrderNotification, { submitId, jobs: newJobs });
        logDBOperationEvent('dismissAllPrinterJobs', null, WorkflowStatus.End, { submitId, orderId });
      }
    }
  }

  yield call(notifyPrinterJobSaga, notifyPrinterJob(true));
};

/**
 * true means only just update the failed job, don't show the error toast
 * @param action
 * @returns
 */
export const notifyPrinterJobSaga = function* (action: Action<boolean>) {
  const status = action.payload;
  const printersErrorCount = PrinterJobHelper.getPrinterErrorMap();
  yield put(upsertPrinterErrorJobs(printersErrorCount));
  const currentEmployeeId = yield select(selectEmployeeId);

  if (!status && currentEmployeeId && !isEmpty(printersErrorCount)) {
    openPrinterErrorToast();
  }

  return true;
};

export const printDailyReportSaga = function* (action) {
  const summary = get(action, ['payload', 'dailyReportSummary']);
  yield call(ReportManager.printDailyReport, summary);
};

export const printAyalaMallReportSaga = function* (action) {
  const ayalaMallReport = get(action, ['payload', 'ayalaMallReport'], {});
  const {
    contractName,
    mallName,
    location,
    dailySales,
    totalDiscount,
    totalRefund,
    totalCancelledAndVoid,
    totalVAT,
    totalServiceCharge,
    totalNonTaxable,
    rawGross,
    transactionCount,
    customerCount,
    cash,
    debit,
    masterCard,
    visa,
    date,
  } = ayalaMallReport;
  const reportLineItems = [];
  reportLineItems.push({ name: 'Daily Sales', value: getLocaleNumberString(dailySales) });
  reportLineItems.push({ name: 'Total Discount', value: getLocaleNumberString(totalDiscount) });
  reportLineItems.push({ name: 'Total Refund', value: getLocaleNumberString(totalRefund) });
  reportLineItems.push({ name: 'Total Cancelled / Void', value: getLocaleNumberString(totalCancelledAndVoid) });
  reportLineItems.push({ name: 'Total VAT', value: getLocaleNumberString(totalVAT) });
  reportLineItems.push({ name: 'Total Service Charge', value: getLocaleNumberString(totalServiceCharge) });
  reportLineItems.push({ name: 'Total Non Taxable', value: getLocaleNumberString(totalNonTaxable) });
  reportLineItems.push({ name: 'Row Gross', value: getLocaleNumberString(rawGross) });
  reportLineItems.push({ name: 'Transaction Count', value: getLocaleNumberString(transactionCount, 0) });
  reportLineItems.push({ name: 'Custom Count', value: getLocaleNumberString(customerCount, 0) });
  reportLineItems.push({ name: 'CASH', value: getLocaleNumberString(cash) });
  reportLineItems.push({ name: 'DEBIT', value: getLocaleNumberString(debit) });
  reportLineItems.push({ name: 'MASTERCARD', value: getLocaleNumberString(masterCard) });
  reportLineItems.push({ name: 'VISA', value: getLocaleNumberString(visa) });

  map(get(ayalaMallReport, 'birInfoList', []), (birInfo, index) => {
    const { birPermit, serialNo } = birInfo;
    reportLineItems.push({ name: `BIR PERMIT ${index + 1}: `, value: birPermit });
    reportLineItems.push({ name: `SERIAL NO. ${index + 1}: `, value: serialNo });
  });
  const ayalaMallReportModel: AyalaMallReportPrintType = {
    storeName: isEmpty(contractName) ? null : contractName,
    mallName: isEmpty(mallName) ? null : mallName,
    address: isEmpty(location) ? null : location,
    reportTitle: 'CONSOLIDATED REPORT Z_READ',
    reportLineItems,
    reportDate: Boolean(date) ? `DATE : ${moment(date, 'YYYY-MM-DD').format('DD/MM/YYYY')}` : null,
    // font size
    receiptFontScale: getReceiptFontScale(yield select(selectReceiptFontSize)),
  };
  const receiptPrinterId = yield call(getReceiptPrinterId);
  const requestPrintingModel = [
    {
      printerId: receiptPrinterId,
      businessType: PrintingBusinessType.AYALA_MALL_REPORT,
      data: ayalaMallReportModel,
    },
  ];
  PrinterManager.bindService();
  const { errCode } = yield call(PrinterManager.requestPrinting, requestPrintingModel);
  if (errCode === 0) {
    yield put(Actions.updatePrinterLastPrintedTime({ printerId: receiptPrinterId }));
  } else {
    yield put(Actions.updateSinglePrinter({ printerId: receiptPrinterId, data: { errorCode: errCode } }));
  }
};

export function* printKitchenDocketSaga(action: Action<Actions.PrintKitchenDocketType>) {
  const { eventName } = action.payload;
  switch (eventName) {
    case KitchenEvent.manualResendToKitchen:
      yield call(KitchenManager.manualResendToKitchen, get(action.payload, 'transaction'));
      break;
    case KitchenEvent.printFailedJob:
      yield call(KitchenManager.printFailedJob, action.payload as Actions.ReprintFailedJobPayload);
      break;
    case KitchenEvent.printOpenOrder:
      yield call(KitchenManager.printOpenOrder, action.payload as Actions.PrintOpenOrderKitchenPayload);
      break;
    case KitchenEvent.resendOpenOrder:
      yield call(KitchenManager.resendOpenOrder, get(action.payload, 'transaction'));
      break;
    case KitchenEvent.printOnUpdateOnlineStatus:
      yield call(KitchenManager.printOnUpdateOnlineStatus, get(action.payload, 'transaction'));
      break;
    case KitchenEvent.printTestReceipt:
      yield call(KitchenManager.printTestReceipt, get(action.payload, 'transaction'));
      break;
    default:
      break;
  }
}

export const printSMEODReportSaga = function* (action) {
  const smReport = get(action, ['payload', 'smReport'], {});
  const {
    begBal,
    endingBal,
    begSI,
    endSI,
    begBill,
    endBill,
    begVoid,
    endVoid,
    begVoidAmt,
    endVoidAmt,
    grossSales,
    grossWithVoid,
    netSalesWithVAT,
    discount,
    totalDiscounts,
    totalRefunds,
    vatAdjustment,
    salesBreakdown,
    netSales,
    voidedSales,
    voidedBilled,
    serviceTypeSales,
    hourlySales,
    totalHourlyTrxCount,
    totalHourlySales,
    tenderBreakdown,
    incomeHeadSales,
    cashDrawer,
    lastBillNo,
    transactionCount,
    guestCount,
    averagePerTransaction,
    zCounter,
    oldGrandTotal,
    grandTotal,
    productMixSummary,
    grandTotalQty,
    grandTotalAmt,
    receiptPrintedDate,
    receiptPrintedTime,
    shift,
    terminal,
    reportDate,
  } = smReport;
  // discount
  const pwdDiscount = get(discount, 'pwd', 0);
  const seniorCitizenDiscount = get(discount, 'seniorCitizenDiscount', 0);
  // salesBreakdown
  const vatableSales = get(salesBreakdown, 'vatableSales', 0);
  const vatAmount = get(salesBreakdown, 'vatAmount', 0);
  const vatExemptSales = get(salesBreakdown, 'vatExemptSales', 0);
  const zeroRatedSales = get(salesBreakdown, 'zeroRatedSales', 0);
  // serviceTypeSales
  const qsrDineIn = get(serviceTypeSales, 'qsrDineIn', 0);
  const serviceTypeSalesCount = get(serviceTypeSales, 'count', 0);

  const businessName = yield select(selectStoreCompanyName);
  const storeName = yield select(selectStoreName);

  const street1 = yield select(selectStoreStreet1);
  const street2 = yield select(selectStoreStreet2);
  const city = yield select(selectStoreCity);
  const state = yield select(selectStoreState);
  const postalCode = yield select(selectStorePostalCode);
  const storeAddress = [street1, street2, city, state, postalCode].filter(v => Boolean(v)).join(' ');

  const gstIdNo = yield select(selectGstIdNo);
  const minNo = yield select(selectMinNo);
  const serialNo = yield select(selectSerialNo);
  const employeeId = yield select(selectEmployeeId);
  const employee = DAL.getEmployeeById(employeeId) as any;
  const generaterName = employee ? `${employee.firstName} ${employee.lastName}` : '';

  const noLines = [];
  noLines.push({ name: 'Beg. Bal.', value: getUnNullPrintString(begBal), inRight: false });
  noLines.push({ name: 'Ending Bal.', value: getUnNullPrintString(endingBal), inRight: false });
  noLines.push({ name: 'Beg. SI#', value: getUnNullPrintString(begSI), inRight: false });
  noLines.push({ name: 'End. SI#', value: getUnNullPrintString(endSI), inRight: false });
  noLines.push({ name: 'Beg. Bill#', value: getUnNullPrintString(begBill), inRight: false });
  noLines.push({ name: 'End. Bill#', value: getUnNullPrintString(endBill), inRight: false });
  noLines.push({ name: 'Beg. Void#', value: getUnNullPrintString(begVoid), inRight: false });
  noLines.push({ name: 'End. Void#', value: getUnNullPrintString(endVoid), inRight: false });
  noLines.push({ name: 'Beg. Void Amt.', value: getUnNullPrintString(begVoidAmt), inRight: true });
  noLines.push({ name: 'End. Void Amt.', value: getUnNullPrintString(endVoidAmt), inRight: true });

  const summaryLines = [];
  summaryLines.push({ name: 'Gross Sales', value: getLocaleNumberString(grossSales), inRight: true });
  summaryLines.push({ name: 'Gross w/ void', value: getLocaleNumberString(grossWithVoid), inRight: true });
  summaryLines.push({ name: 'Net Sales w/ VAT', value: getLocaleNumberString(netSalesWithVAT), inRight: true });

  summaryLines.push({ name: 'Discount', value: null, inRight: true, isTitle: true, hasMargin: true });
  summaryLines.push({ name: 'PWD', value: getLocaleNumberString(pwdDiscount), inRight: true });
  summaryLines.push({ name: 'MEMC Discount', value: getLocaleNumberString(0), inRight: true });
  summaryLines.push({ name: 'Senior Citizen Ceiling', value: getLocaleNumberString(0), inRight: true });
  summaryLines.push({ name: 'Senior Citizen Discount', value: getLocaleNumberString(seniorCitizenDiscount), inRight: true });

  summaryLines.push({ name: 'Total Discounts', value: getLocaleNumberString(totalDiscounts), inRight: true, hasMargin: true });
  summaryLines.push({ name: 'Total Refunds', value: getLocaleNumberString(totalRefunds), inRight: true });
  summaryLines.push({ name: 'VAT ADJUSMENT', value: getLocaleNumberString(vatAdjustment), inRight: true });

  summaryLines.push({ name: 'Breakdown of Sales', value: null, inRight: true, isTitle: true, hasMargin: true });
  summaryLines.push({ name: 'VATABLE SALES', value: getLocaleNumberString(vatableSales), inRight: true });
  summaryLines.push({ name: 'VAT AMOUNT', value: getLocaleNumberString(vatAmount), inRight: true });
  summaryLines.push({ name: 'Vat Exempt Sales', value: getLocaleNumberString(vatExemptSales), inRight: true });
  summaryLines.push({ name: 'Zero Rated Sales', value: getLocaleNumberString(zeroRatedSales), inRight: true });

  summaryLines.push({ name: 'Net Sales', value: getLocaleNumberString(netSales), inRight: true, hasMargin: true });

  summaryLines.push({ name: 'Voided Sales', value: getLocaleNumberString(voidedSales), inRight: true, hasMargin: true });
  summaryLines.push({ name: 'Voided Billed', value: getLocaleNumberString(voidedBilled), inRight: true });

  const serviceTypeSaleLines = [];
  serviceTypeSaleLines.push({ name: 'Service Type Sale', value: null, inRight: true, isTitle: true });
  serviceTypeSaleLines.push({ name: 'QSR Dine-in', value: getLocaleNumberString(qsrDineIn), inRight: true });
  serviceTypeSaleLines.push({ name: 'Count', value: getCountNumberString(serviceTypeSalesCount), inRight: true });

  serviceTypeSaleLines.push({ name: 'Hourly Sales', value: '', inRight: false, hasMargin: true, isTitle: true });

  const hourlySalesLines = [];

  map(Object.keys(hourlySales), (name, index) => {
    const hourlySale = get(hourlySales, name, {});
    const { trxCount, sales } = hourlySale;
    hourlySalesLines.push({
      name,
      count: getCountNumberString(trxCount),
      value: getLocaleNumberString(sales),
      inRight: false,
      hasMargin: index === 0,
      isTitle: false,
    });
  });

  hourlySalesLines.push({
    name: 'Total',
    count: getCountNumberString(totalHourlyTrxCount),
    value: getLocaleNumberString(totalHourlySales),
    inRight: false,
    hasMargin: true,
    isTitle: false,
  });

  const breakDownOfTenderLines = [];
  breakDownOfTenderLines.push({ name: 'Breakdown of Tender', value: null, inRight: true, isTitle: true });
  const tenderBreakdownNames = Object.keys(tenderBreakdown);
  const tenderBreakdownValues = Object.values<number | string>(tenderBreakdown);
  map(tenderBreakdownValues, (value, index) => {
    breakDownOfTenderLines.push({
      name: tenderBreakdownNames[index],
      value: getLocaleNumberString(value),
      inRight: false,
      isTitle: false,
    });
  });

  const incomeHeadSalesLines = [];
  isArray(incomeHeadSales) &&
    map(incomeHeadSales, (item, index) => {
      const { name, totalUnit, totalAmount } = item;
      incomeHeadSalesLines.push({
        name: getUnNullPrintString(name),
        count: getCountNumberString(totalUnit),
        value: getLocaleNumberString(totalAmount),
        inRight: false,
        hasMargin: index === 0,
        isTitle: false,
      });
    });

  const cashDrawerLines = [];
  cashDrawerLines.push({ name: 'Cash Drawer', value: null, inRight: false, isTitle: true });
  cashDrawerLines.push({ name: `Terminal ${terminal}`, value: null, inRight: false, isTitle: false });
  const cashDrawerNames = ['Total Cash Value', 'Cash Float', 'Cash Sales', 'Over/Short'];
  const cashDrawerValues = Object.values<number | string>(cashDrawer);
  map(cashDrawerValues, (value, index) => {
    cashDrawerLines.push({
      name: cashDrawerNames[index],
      value: getLocaleNumberString(value),
      inRight: false,
      isTitle: false,
    });
  });

  const capitalizedLines = [];
  capitalizedLines.push({ name: 'LAST BILL NO.', value: lastBillNo, inRight: true });
  capitalizedLines.push({ name: 'TRANSACTION COUNT', value: getCountNumberString(transactionCount), inRight: true });
  capitalizedLines.push({ name: 'GUEST COUNT', value: getCountNumberString(guestCount), inRight: true });
  capitalizedLines.push({ name: 'AVERAGE PER TRANSACTION', value: getLocaleNumberString(averagePerTransaction), inRight: true });
  capitalizedLines.push({ name: 'Z-COUNTER', value: getCountNumberString(zCounter), inRight: true });
  capitalizedLines.push({ name: 'OLD GRAND TOTAL', value: getLocaleNumberString(oldGrandTotal), inRight: true });
  capitalizedLines.push({ name: 'GRAND TOTAL', value: getLocaleNumberString(grandTotal), inRight: true });

  const mixSummaryLinesGroups = getMixSummaryLinesGroups(productMixSummary);

  const totalLines = [];
  totalLines.push({ name: 'Grand Total Qty: ', value: getCountNumberString(grandTotalQty), inRight: true });
  totalLines.push({ name: 'Grand Total Amt: ', value: getLocaleNumberString(grandTotalAmt), inRight: true });

  const dateLines = [];
  dateLines.push({ name: 'Receipt Printed', value: receiptPrintedDate, inRight: false });
  dateLines.push({ name: 'on', value: receiptPrintedTime, inRight: false });
  dateLines.push({ name: 'POS Date', value: reportDate, inRight: false });

  const footerLines = [];
  footerLines.push({ name: 'Shift #: ', value: getUnNullPrintString(shift), inRight: false, hasMargin: true });
  footerLines.push({ name: 'Terminal #: ', value: getUnNullPrintString(terminal), inRight: false });
  footerLines.push({ name: 'Generated by: ', value: getUnNullPrintString(generaterName), inRight: false });

  const smEODReportModel = {
    businessName,
    storeName,
    storeAddress,
    vatRegTin: `VAT REG TIN: ${gstIdNo}`,
    minNo: `MIN: ${minNo}`,
    serialNo: `SERIAL NO.: ${serialNo}`,
    reportTitle: 'Z-Reading',
    noLines,
    summaryLines,
    serviceTypeSaleLines,
    hourlySalesLines,
    breakDownOfTenderLines,
    cashDrawerLines,
    capitalizedLines,
    incomeHeadSalesLines,
    mixSummaryLinesGroups,
    totalLines,
    dateLines,
    footerLines,
    // font size
    receiptFontScale: getReceiptFontScale(ReceiptFontSize.MEDIUM),
  };
  const receiptPrinterId = yield call(getReceiptPrinterId);
  if (!receiptPrinterId) {
    yield put(Actions.toggleToastInfo({ visible: true, text: t('NoPrinterError') }));
    return;
  }
  const requestPrintingModel = [
    {
      printerId: receiptPrinterId,
      businessType: PrintingBusinessType.SM_EOD_REPORT,
      data: smEODReportModel,
    },
  ];
  PrinterManager.bindService();
  const { errCode } = yield call(PrinterManager.requestPrinting, requestPrintingModel);
  if (errCode === 0) {
    yield put(Actions.updatePrinterLastPrintedTime({ printerId: receiptPrinterId }));
  } else {
    yield put(Actions.updateSinglePrinter({ printerId: receiptPrinterId, data: { errorCode: errCode } }));
  }
};

export const getMixSummaryLinesGroups = productMixSummary => {
  const mixSummaryLinesGroups = [[]];
  mixSummaryLinesGroups[0].push({ names: ['PRODUCT MIX SUMMARY'], isTitle: true, isSingleLine: true });
  if (isArray(productMixSummary)) {
    const serviceTypeSet = new Set(map(productMixSummary, item => item.serviceType));
    const sortProductMixSummary = data => {
      const serviceTypeOrder = Array.from(serviceTypeSet);
      return data.sort((a, b) => {
        const indexA = serviceTypeOrder.indexOf(a.serviceType);
        const indexB = serviceTypeOrder.indexOf(b.serviceType);
        return indexA - indexB;
      });
    };

    const sortedProductMixSummary: any[] = sortProductMixSummary([...productMixSummary]);
    map(sortedProductMixSummary, (summary, index) => {
      const { serviceType, groupName, items, totalQuantity, totalAmount } = summary;
      const curMixSummaryLinesIndex = mixSummaryLinesGroups.length - 1;
      let curMixSummaryLines = mixSummaryLinesGroups[curMixSummaryLinesIndex];
      if (index !== 0) {
        curMixSummaryLines.push({ names: [], isDivider: true });
      }
      if (serviceTypeSet.has(serviceType)) {
        curMixSummaryLines.push({ names: [`Service Type: ${serviceType}`], isSingleLine: true });
        serviceTypeSet.delete(serviceType);
      }
      curMixSummaryLines.push({ names: [`Group name: ${groupName}`], isSingleLine: true });
      if (isArray(items)) {
        curMixSummaryLines.push({ names: ['Description'], isSingleLine: true });
        map(items, item => {
          // segmented printing
          if (curMixSummaryLines.length >= 15) {
            mixSummaryLinesGroups.push([]);
            curMixSummaryLines = last(mixSummaryLinesGroups);
          }
          const { name, totalQuantity, unitPrice, totalPrice } = item;
          curMixSummaryLines.push({ names: [name], isSingleLine: true });
          curMixSummaryLines.push({
            names: [getCountNumberString(totalQuantity), getLocaleNumberString(unitPrice), getLocaleNumberString(totalPrice)],
          });
        });
      }
      curMixSummaryLines.push({ names: [`Total Quantity: ${getCountNumberString(totalQuantity)}`], isSingleLine: true });
      curMixSummaryLines.push({ names: [`Total Amount: ${getLocaleNumberString(totalAmount)}`], isSingleLine: true });
    });
  }
  return mixSummaryLinesGroups;
};

export const printOrtigasEODReportSaga = function* (action) {
  const ortigasZreading = get(action, ['payload', 'ortigasZreading'], {});
  const {
    zCount,
    terminalNumber,
    tenderBreakdown,
    totalTenders,
    vatableSales = {},
    nonVatableSales = {},
    grandTotalNetSales,
    firstReceipt,
    lastReceipt,
    transactionCount,
    oldAccumulatedSales,
    newAccumulatedSales,
    reportDate,
  } = ortigasZreading;

  const industry = yield select(selectIndustry);
  const isRetail = industry == BIRStoreType.RetailStore;

  const storeName = yield select(selectStoreName);

  const street1 = yield select(selectStoreStreet1);
  const street2 = yield select(selectStoreStreet2);
  const city = yield select(selectStoreCity);
  const state = yield select(selectStoreState);
  const postalCode = yield select(selectStorePostalCode);
  const cityAndState = [city, state, postalCode].filter(v => Boolean(v)).join(' ');

  const gstIdNo = yield select(selectGstIdNo);
  const minNo = yield select(selectMinNo);
  const serialNo = yield select(selectSerialNo);
  const storePhone = yield select(selectStorePhone);

  // no
  const noLines = [];
  noLines.push({ name: 'ZCOUNT: ', value: getUnNullPrintString(zCount) });
  noLines.push({ name: 'TERMINAL NO: ', value: getUnNullPrintString(terminalNumber) });

  // desc
  const descLines = []; // { name, value, isDivider, isNBold, isVBold, isTitleColumn }
  descLines.push({ name: 'DESCRIPTION', value: 'AMOUNT', isTitleColumn: true });
  if (!isEmpty(tenderBreakdown)) {
    const tenderBreakdownNames = Object.keys(tenderBreakdown);
    const tenderBreakdownValues = Object.values<string | number>(tenderBreakdown);
    map(tenderBreakdownValues, (value, index) => {
      const name = getUnNullPrintString(tenderBreakdownNames[index]).toUpperCase();
      descLines.push({ name: name, value: getLocaleNumberStringWithDefault(value) });
    });
  }
  descLines.push({ isDivider: true });
  descLines.push({ name: 'TOTAL TENDERS', value: getLocaleNumberStringWithDefault(totalTenders), isNBold: false, isVBold: true });
  descLines.push({ isDivider: true });

  // vatableSales
  const {
    grossVatableSales,
    promoSalesAmount,
    discountCards,
    otherDiscounts,
    refundAmount,
    returnedAmount,
    giftCertificates,
    serviceChargeAmount,
    deliveryChargeAmount,
    otherTaxes,
    voidAmount,
    totalVatableAmount,
    vatAmount,
    totalSalesAmount,
  } = vatableSales;
  const vatableSalesLines = []; // { advance, name, value, isDivider, isNBold, isVBold, isTitleColumn }
  vatableSalesLines.push({ name: 'GROSS VATABLE SALES', value: getLocaleNumberStringWithDefault(grossVatableSales), isNBold: true, isTitleColumn: true });
  vatableSalesLines.push({ advance: 'LESS: ', name: 'PROMO SALES AMOUNT', value: getLocaleNumberStringWithDefault(promoSalesAmount) });
  vatableSalesLines.push({ name: 'DISCOUNT CARDS', value: getLocaleNumberStringWithDefault(discountCards) });
  vatableSalesLines.push({ name: 'OTHER DISCOUNTS', value: getLocaleNumberStringWithDefault(otherDiscounts) });
  vatableSalesLines.push({ name: 'REFUND AMOUNT', value: getLocaleNumberStringWithDefault(refundAmount) });
  vatableSalesLines.push({ name: 'RETURNED AMOUNT', value: getLocaleNumberStringWithDefault(returnedAmount) });
  vatableSalesLines.push({ name: 'GIFT CERTIFICATES/CHECKS REDEEMED', value: getLocaleNumberStringWithDefault(giftCertificates) });
  if (!isRetail) {
    vatableSalesLines.push({ name: 'SERVICE CHARGE AMOUNT', value: getLocaleNumberStringWithDefault(serviceChargeAmount) });
    vatableSalesLines.push({ name: 'DELIVERY CHARGE AMOUNT', value: getLocaleNumberStringWithDefault(deliveryChargeAmount) });
  }
  vatableSalesLines.push({ name: 'OTHER TAXES', value: getLocaleNumberStringWithDefault(otherTaxes) });
  vatableSalesLines.push({ name: 'VOID AMOUNT', value: getLocaleNumberStringWithDefault(voidAmount) });
  vatableSalesLines.push({ isDivider: true });

  vatableSalesLines.push({ name: 'TOTAL VATABLE SALES', value: getLocaleNumberStringWithDefault(totalVatableAmount), isVBold: true, isTitleColumn: true });
  vatableSalesLines.push({ name: 'VAT AMOUNT', value: getLocaleNumberStringWithDefault(vatAmount), isTitleColumn: true });
  vatableSalesLines.push({ isDivider: true });
  vatableSalesLines.push({ name: 'TOTAL SALES AMOUNT', value: getLocaleNumberStringWithDefault(totalSalesAmount), isVBold: true, isTitleColumn: true });
  vatableSalesLines.push({ isDivider: true });

  // nonVatableSales
  const {
    grossNonVatableSales,
    seniorCitizen,
    promoSalesAmount: nonPromoSalesAmount,
    discountCards: nonDiscountCards,
    otherDiscounts: nonOtherDiscounts,
    refundAmount: nonRefundAmount,
    returnedAmount: nonReturnedAmount,
    giftCertificates: nonGiftCertificates,
    serviceChargeAmount: nonServiceChargeAmount,
    deliveryChargeAmount: nonDeliveryChargeAmount,
    voidAmount: nonVoidAmount,
    totalNonVatableAmount,
  } = nonVatableSales;
  const nonVatableSalesLines = []; // { advance, name, value, isDivider, isNBold, isVBold, isTitleColumn }

  nonVatableSalesLines.push({
    name: isRetail ? 'GROSS NON-VATABLE SALES (DIPLOMAT SALES)' : 'GROSS NON-VATABLE SALES',
    value: getLocaleNumberStringWithDefault(grossNonVatableSales),
    isNBold: true,
    isVBold: isRetail,
    isTitleColumn: true,
  });

  if (!isRetail) {
    nonVatableSalesLines.push({ advance: 'LESS: ', name: 'SENIOR CITIZEN', value: getLocaleNumberStringWithDefault(seniorCitizen) });
    nonVatableSalesLines.push({ name: 'PROMO SALES AMOUNT', value: getLocaleNumberStringWithDefault(nonPromoSalesAmount) });
    nonVatableSalesLines.push({ name: 'DISCOUNT CARDS', value: getLocaleNumberStringWithDefault(nonDiscountCards) });
    nonVatableSalesLines.push({ name: 'OTHER DISCOUNTS', value: getLocaleNumberStringWithDefault(nonOtherDiscounts) });
    nonVatableSalesLines.push({ name: 'REFUND AMOUNT', value: getLocaleNumberStringWithDefault(nonRefundAmount) });
    nonVatableSalesLines.push({ name: 'RETURNED AMOUNT', value: getLocaleNumberStringWithDefault(nonReturnedAmount) });
    nonVatableSalesLines.push({ name: 'GIFT CERTIFICATES/CHECKS REDEEMED', value: getLocaleNumberStringWithDefault(nonGiftCertificates) });
    nonVatableSalesLines.push({ name: 'SERVICE CHARGE AMOUNT', value: getLocaleNumberStringWithDefault(nonServiceChargeAmount) });
    nonVatableSalesLines.push({ name: 'DELIVERY CHARGE AMOUNT', value: getLocaleNumberStringWithDefault(nonDeliveryChargeAmount) });
    nonVatableSalesLines.push({ name: 'VOID AMOUNT', value: getLocaleNumberStringWithDefault(nonVoidAmount) });
    nonVatableSalesLines.push({ isDivider: true });
    nonVatableSalesLines.push({
      name: 'TOTAL NON-VATABLE SALES',
      value: getLocaleNumberStringWithDefault(totalNonVatableAmount),
      isVBold: true,
      isTitleColumn: true,
    });
    nonVatableSalesLines.push({ isDivider: true });
  }

  // footer
  const footerLines = [];
  footerLines.push({ name: 'GRAND TOTAL NET SALES', value: getLocaleNumberStringWithDefault(grandTotalNetSales), isTitleColumn: true });
  footerLines.push({ name: 'FIRST RECEIPT', value: getUnNullPrintString(firstReceipt) });
  footerLines.push({ name: 'LAST RECEIPT', value: getUnNullPrintString(lastReceipt) });
  footerLines.push({ name: 'TRANSACTION COUNT', value: getCountNumberString(transactionCount) });
  footerLines.push({ name: 'OLD ACCUMULATED SALES', value: getLocaleNumberStringWithDefault(oldAccumulatedSales) });
  footerLines.push({ name: 'NEW ACCUMULATED SALES', value: getLocaleNumberStringWithDefault(newAccumulatedSales) });

  const ortigasEODReportModel = {
    storeName,
    storeAddress1: street1,
    storeAddress2: street2,
    cityAndState,
    vatRegTin: `TIN: ${gstIdNo}`,
    minNo: `MIN: ${minNo}`,
    serialNo: `SERIAL NO.: ${serialNo}`,
    storePhone: `PHONE: ${storePhone}`,
    reportTitle1: 'TERMINAL READING',
    reportDate,
    noLines,
    descLines,
    reportTitle2: 'OTHER INFORMATION',
    vatableSalesLines,
    nonVatableSalesLines,
    footerLines,
    footer: '................................END OF REPORT................................',
    // font size
    receiptFontScale: getReceiptFontScale(ReceiptFontSize.MEDIUM),
  };
  const receiptPrinterId = yield call(getReceiptPrinterId);
  const requestPrintingModel = [
    {
      printerId: receiptPrinterId,
      businessType: PrintingBusinessType.ORTIGAS_EOD_REPORT,
      data: ortigasEODReportModel,
    },
  ];
  PrinterManager.bindService();
  const { errCode } = yield call(PrinterManager.requestPrinting, requestPrintingModel);
  if (errCode === 0) {
    yield put(Actions.updatePrinterLastPrintedTime({ printerId: receiptPrinterId }));
  } else {
    yield put(Actions.updateSinglePrinter({ printerId: receiptPrinterId, data: { errorCode: errCode } }));
  }
};

export const testPrintOnAllTagedPrintersSaga = function* () {
  try {
    const allPrinters = yield select(selectAllPrinters);

    const taggedPrinters = allPrinters.filter(
      printer => printer.isOnline && (printer.tags?.length > 0 || printer.isReceiptPrinter || printer.isBeepOrderSummaryPrinter)
    );

    if (taggedPrinters.length === 0) {
      yield put(
        Actions.toggleToastInfo({
          visible: true,
          text: t('没有找到已配置且在线的打印机'),
        })
      );
      return;
    }

    const sampleReceiptData = getSampleReceiptData();

    const requestPrintingModel: any[] = taggedPrinters.map(printer => ({
      printerId: printer.printerId,
      businessType: PrintingBusinessType.TRANSACTION,
      data: sampleReceiptData,
    }));

    console.log('🖨️ requestPrintingModel', requestPrintingModel);

    PrinterManager.bindService();
    const { errCode, data: errorData } = yield call(PrinterManager.requestPrinting, requestPrintingModel);

    if (errCode === 0) {
      for (const printer of taggedPrinters) {
        yield put(Actions.updatePrinterLastPrintedTime({ printerId: printer.printerId }));
      }
      yield put(
        Actions.toggleToastInfo({
          visible: true,
          text: `已在 ${taggedPrinters.length} 台打印机上执行测试打印`,
        })
      );
    } else {
      yield put(
        Actions.toggleToastInfo({
          visible: true,
          text: t('打印失败'),
        })
      );
    }
  } catch (error) {
    console.error('批量测试打印失败:', error);
    yield put(
      Actions.toggleToastInfo({
        visible: true,
        text: t('批量测试打印失败'),
      })
    );
  }
};

function* printTestReceipt(printerIds: string[], itemCount = 1) {
  const sampleReceiptData = getSampleReceiptData(itemCount);

  const requestPrintingModel: any[] = printerIds.map(printerId => ({
    printerId,
    businessType: PrintingBusinessType.TRANSACTION,
    data: sampleReceiptData,
  }));

  PrinterManager.bindService();
  const { errCode, data: errorData } = yield call(PrinterManager.requestPrinting, requestPrintingModel);
  return errCode;
}

export const multipleItemsPrintTestSaga = function* (action) {
  const { itemCount } = action.payload;
  const receiptPrinterId = yield call(getReceiptPrinterId);
  yield call(printTestReceipt, [receiptPrinterId], itemCount);
  yield put(
    Actions.toggleToastInfo({
      visible: true,
      text: `已打印 ${itemCount} 个项目`,
    })
  );
};

function* singlePrinterTask(printerId: string, times: number, intervals: number[], onTaskComplete?: (result: PrintTaskResult) => void) {
  for (let i = 0; i < times; i++) {
    const errorCode = yield call(printTestReceipt, [printerId]);

    const result: PrintTaskResult = {
      printerId,
      taskIndex: i + 1,
      timestamp: Date.now(),
      status: errorCode === 0 ? 'success' : 'error',
      message: errorCode === 0 ? undefined : `打印失败 (错误码: ${errorCode})`,
    };

    if (onTaskComplete) {
      onTaskComplete(result);
    }

    if (errorCode === 0) {
      yield put(Actions.updatePrinterLastPrintedTime({ printerId }));
      yield put(
        Actions.toggleToastInfo({
          visible: true,
          text: `打印机 ${printerId} 完成第 ${i + 1}/${times} 次打印`,
        })
      );
    } else {
      yield put(
        Actions.toggleToastInfo({
          visible: true,
          text: `打印机 ${printerId} 第 ${i + 1}/${times} 次打印失败`,
        })
      );
    }

    if (i < times - 1 && intervals && intervals[i]) {
      yield delay(intervals[i]);
    }
  }
}

export const intervalPrintTestSaga = function* (action: Action<IntervalPrintTestPayload>) {
  try {
    const { mode, times, intervals, printerIds = [], onTaskComplete } = action.payload;

    if (printerIds.length === 0) {
      yield put(
        Actions.toggleToastInfo({
          visible: true,
          text: '请先选择打印机',
        })
      );
      return;
    }

    if (mode === 'single') {
      const errorCode = yield call(printTestReceipt, printerIds);

      for (const printerId of printerIds) {
        const result: PrintTaskResult = {
          printerId,
          taskIndex: 1,
          timestamp: Date.now(),
          status: errorCode === 0 ? 'success' : 'error',
          message: errorCode === 0 ? undefined : `打印失败 (错误码: ${errorCode})`,
        };

        if (onTaskComplete) {
          onTaskComplete(result);
        }

        if (errorCode === 0) {
          yield put(Actions.updatePrinterLastPrintedTime({ printerId }));
        }
      }

      yield put(
        Actions.toggleToastInfo({
          visible: true,
          text: errorCode === 0 ? `已在 ${printerIds.length} 台打印机上完成打印` : `打印失败 (错误码: ${errorCode})`,
        })
      );
    } else if (mode === 'interval') {
      const printerTasks = printerIds.map(printerId => fork(singlePrinterTask, printerId, times, intervals, onTaskComplete));

      yield all(printerTasks);

      yield put(
        Actions.toggleToastInfo({
          visible: true,
          text: `已在 ${printerIds.length} 台打印机上开始 ${times} 次打印测试`,
        })
      );
    } else {
      const printerTasks = printerIds.map(printerId => fork(singlePrinterTask, printerId, times, intervals, onTaskComplete));

      yield all(printerTasks);

      yield put(
        Actions.toggleToastInfo({
          visible: true,
          text: `已在 ${printerIds.length} 台打印机上开始 ${times} 次打印测试`,
        })
      );
    }
  } catch (error) {
    console.error('间隔打印测试执行出错:', error);
    yield put(
      Actions.toggleToastInfo({
        visible: true,
        text: '打印测试失败',
      })
    );
  }
};

/**
 * auto search lanXPrinters by udp and reprint failed jobs after printing kitchen dockets
 * @param action
 * @returns
 */
export const tryAutoReprintFailedJobsSaga = function* (action: Action<Actions.TryAutoReprintFailedJobsType>) {
  const enable = yield select(selectAutoSearchEnable);
  const interval = yield select(selectAutoSearchInterval);
  if (!enable || interval < 0) {
    return;
  }
  const lastTime = yield select(selectAutoReprintByUdpTime);
  const nextTime = lastTime + interval * 60 * 1000; // interval 5 minutes
  const currentTime = Date.now();
  if (currentTime < nextTime) {
    // per [interval] minutes
    return;
  }

  const orderId = action.payload.orderId;
  const printerSettings: Actions.PrinterConfigType[] = yield select(selectAllPrinters);
  const offlineLanXPrinterJobs: JobItemType[] = [];

  const getOfflineLanXPrinterJobs = (record: BeepNotificationType | SubOrderNotificationType) => {
    if (!record) {
      return;
    }
    if (!isEmpty(record.jobs)) {
      for (const job of record.jobs) {
        if (job.printerId && !job.isCancelled) {
          const printer = printerSettings.find(it => it.printerId === job.printerId);
          if (isLanXPrinter(printer)) {
            const reprintJob: JobItemType = realmObjectToJSObject(job, 'getOfflineLanXPrinterJobs');
            reprintJob.isBeepOrder = record.isBeepOrder;
            reprintJob.orderId = record.orderId;
            reprintJob.submitId = get(record, 'submitId', '');
            reprintJob.receiptNumber = get(record, 'receiptNumber', '');
            reprintJob.transactionDate = record.transactionDate;
            reprintJob.jobTitle = record.jobTitle;
            offlineLanXPrinterJobs.push(reprintJob);
          }
        }
      }
    }
  };
  let record: BeepNotificationType | SubOrderNotificationType;
  if (orderId) {
    record = DAL.getBeepNotificationById(orderId);
    getOfflineLanXPrinterJobs(record);
  } else if (!isEmpty(action.payload.submitIds)) {
    forEach(action.payload.submitIds, submitId => {
      record = DAL.getSubOrderNotificationById(submitId);
      getOfflineLanXPrinterJobs(record);
    });
  }
  console.log('offlineLanXPrinterJobs', offlineLanXPrinterJobs);
  if (offlineLanXPrinterJobs.length > 0) {
    const searchId = currentTime.toString();
    trackLogEvent({
      action: PrinterAction.UdpSearch,
      level: LoggingLevel.Info,
      mobileData: {
        order: {
          orderId,
          submitId: join(action.payload.submitIds, '_'),
          receiptNumber: get(record, 'receiptNumber', ''),
          isBeepOrder: record?.isBeepOrder,
        },
        printer: {
          printEvent: action.payload.eventName,
        },
        workflowId: searchId,
      },
      privateDataPayload: { printerJSON: JSON.stringify(printerSettings), event: 'start' },
    });
    yield put(Actions.updatePrinterSearching(true));
    yield call(PrinterManager.restoreLanXPrintersByUDP, searchId);
    yield put(Actions.updatePrinterSearching(false));
    const udpFinishedAction: Action<Actions.FindNewPrintersType> = yield take(
      (action: Action<Actions.FindNewPrintersType>) => action.type === Actions.udpSearchFinished.toString() && action.payload.udpRestoreId === searchId
    );
    console.log('udpFinishedAction', udpFinishedAction);
    const newPrinters: Actions.PrinterConfigType[] = yield select(selectAllPrinters);

    trackLogEvent({
      action: PrinterAction.UdpSearch,
      level: LoggingLevel.Info,
      mobileData: {
        workflowId: searchId,
      },
      privateDataPayload: {
        event: 'end',
        printerJSON: JSON.stringify({ udpFindPrinters: udpFinishedAction.payload.printers }),
      },
    });
    const autoReprint = yield select(selectAutoReprintEnable);
    if (!isEmpty(udpFinishedAction.payload.printers) && autoReprint) {
      for (const failedJob of offlineLanXPrinterJobs) {
        const printer = newPrinters.find(it => it.printerId === failedJob.printerId);
        if (printer && printer.isOnline) {
          const result = yield call(KitchenManager.printFailedJob, {
            printerJob: failedJob,
            eventName: 'autoReprintByUdp',
            udpSearchId: searchId,
          });
          console.log('retryResult', result);
        }
      }
    }
    yield put(Actions.updateAutoReprintByUdpTime());
  }
};

function* printer() {
  yield takeLatest(Actions.newPrintReceiptAction.toString(), routePrintReceiptSaga);
  yield takeLatest(Actions.printBeepQRTicket.toString(), printBeepQRTicketSaga);
  yield takeLatest(Actions.printEInvoiceQrReceipt.toString(), printEInvoiceQrReceiptSaga);
  yield takeLatest(Actions.printShiftReport.toString(), printShiftReportSaga);
  yield takeLatest(Actions.openDrawer.toString(), openCashDrawerSaga);
  yield takeLatest(Actions.searchPrinters.toString(), searchPrinterSaga);
  yield takeLatest(Actions.initPrinters.toString(), initPrinterSaga);
  yield takeEvery(Actions.printKitchenDocket.toString(), printKitchenDocketSaga);
  yield takeLatest(Actions.newPrintOpenOrderReceipt.toString(), newPrintOpenOrderReceiptSaga);
  yield takeLatest(Actions.printZReadingReport.toString(), printZReadingReportSaga);
  yield takeLatest(Actions.printTodayBeepPreOrderAction.toString(), printTodayBeepPreOrderSaga);
  yield takeLatest(Actions.notifyPrinterJob.toString(), notifyPrinterJobSaga);
  yield takeLatest(Actions.dismissPrinterJob.toString(), dismissPrinterJobSaga);
  yield takeLatest(Actions.dismissAllPrinterJobs.toString(), dismissAllPrinterJobsSaga);
  yield takeLatest(Actions.printDailyReport.toString(), printDailyReportSaga);
  yield takeLatest(Actions.printAyalaMallReport.toString(), printAyalaMallReportSaga);
  yield takeLatest(Actions.printSMEODReport.toString(), printSMEODReportSaga);
  yield takeLatest(Actions.printOrtigasEODReport.toString(), printOrtigasEODReportSaga);
  yield takeLatest(Actions.checkAssignedPrinter.toString(), checkAssignedPrinterSaga);
  yield takeLeading(Actions.pingLanPrinters.toString(), pingLanPrintersSaga);
  yield takeLatest(Actions.forgetPrinter.toString(), forgetPrinterSaga);
  yield fork(handleUpdatePrinter);
  yield takeLatest(Actions.updatePrinterTag.toString(), updatePrinterTagSaga);
  yield takeLatest(Actions.testPrintOnAllTagedPrinters.toString(), testPrintOnAllTagedPrintersSaga);
  yield takeLatest(Actions.intervalPrintTest.toString(), intervalPrintTestSaga);
  yield takeLatest(Actions.multipleItemsPrintTest.toString(), multipleItemsPrintTestSaga);
  yield takeLeading(Actions.tryAutoReprintFailedJobs.toString(), tryAutoReprintFailedJobsSaga);
}

export default fork(printer);
