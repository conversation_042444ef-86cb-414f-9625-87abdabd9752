import { chain, concat, filter, find, findIndex, forEach, get, isEmpty, reduce, uniq } from 'lodash';
import moment, { Moment } from 'moment';
import { Action } from 'redux-actions';
import { buffers } from 'redux-saga';
import { CallEffect, actionChannel, all, call, put, select, spawn, take } from 'redux-saga/effects';
import {
  CheckAssignedPrinterType,
  InitPrinterType,
  PingLanPrintersType,
  PrinterConfigType,
  SearchPrinterType,
  UpdatePrinterTagType,
  checkAssignedPrinter,
  pingLanPrinters,
  updatePrinterAssignedStatus,
  updatePrinterGeneralSettings,
  updatePrinterSearching,
  updatePrinterTagsSettings,
  updateSinglePrinter,
} from '../../actions';
import { openPrinterOfflineToast } from '../../components/common/TopNotification';
import { ORDER_SUMMARY_PRINTER_TAG, PrinterConnectType, t } from '../../constants';
import { safeCallback } from '../../utils';
import { stringify } from '../../utils/json';
import { LoggingLevel, PrinterAction, PrintingDataValidationAction, errorPrintingEvent, infoPrintingEvent, trackLogEvent } from '../../utils/logComponent';
import { PingUtils } from '../../utils/ping';
import PrinterManager, { hasOnlinePrinter } from '../../utils/printer';
import {
  selectAllPrinters,
  selectAssignedPrinters,
  selectIPAddress,
  selectIsNetConnected,
  selectIsPrinterSearching,
  selectKitchenPrinters,
  selectOfflinePrintersCount,
  selectOrderSummaryPrinter,
  selectPrinterTagsSettings,
  selectReceiptType,
} from '../selector';
import { FindNewPrintersType, findNewPrinters, udpSearchFinished } from './../../actions/printer';

export const checkAssignedPrinterSaga = function* (actions: Action<CheckAssignedPrinterType>) {
  const { extraTags = [], onComplete, printerTagsSettings, order } = actions.payload;
  const printerTagMapItems = actions.payload.printerTagMapItems || {};
  const kitchenTags = [];
  const kitchenTagsString = yield select(selectKitchenPrinters);
  const boSettingTags = kitchenTagsString.split(',');

  if (isEmpty(printerTagMapItems) && isEmpty(extraTags)) {
    if (!kitchenTagsString) {
      const result = { isPrinterAssigned: true, noAssignedTags: [], offlinePrinters: [], isAllAssigned: true };
      yield put(updatePrinterAssignedStatus(result));
      onComplete && onComplete(result);
      return result;
    }
    kitchenTags.push(...boSettingTags);
    const orderSummaryPrinter = yield select(selectOrderSummaryPrinter);
    if (orderSummaryPrinter === ORDER_SUMMARY_PRINTER_TAG) {
      // need receipt printer
      kitchenTags.push(orderSummaryPrinter);
    }
  } else {
    const tagNeeded = concat(Object.keys(printerTagMapItems), extraTags);
    for (const pt of tagNeeded) {
      if (!pt) {
        continue;
      }
      if (boSettingTags.includes(pt) || pt === ORDER_SUMMARY_PRINTER_TAG) {
        kitchenTags.push(pt);
      } else {
        // dirtyTag
        const dirtyItems = printerTagMapItems[pt];
        forEach(dirtyItems, di => {
          const { productId, title } = di;
          errorPrintingEvent({
            action: PrintingDataValidationAction.DirtyProductTag,
            reason: 'tag has been deleted in bo setting but product still has the tag',
            order,
            privateDataPayload: { productId, productName: title, tag: pt },
          });
        });
      }
    }
  }

  const settings: PrinterConfigType[] = printerTagsSettings || (yield select(selectPrinterTagsSettings)).toJS();

  const allKitchenPrinters = settings.filter(printer => {
    return printer.tags?.length > 0;
  });

  const offlinePrinters = allKitchenPrinters.filter(printer => !printer.isOnline);

  const assignedTags = uniq(
    settings
      .filter(printer => printer.isOnline)
      .reduce((tags, printer) => {
        if (printer.isReceiptPrinter) {
          // tag for receipt printer is used to print order summary
          tags.push(ORDER_SUMMARY_PRINTER_TAG);
        }
        const printerTags = printer.tags;
        if (!isEmpty(printerTags)) {
          return tags.concat(printerTags);
        }
        return tags;
      }, [])
  );
  const noAssignedTags = [];
  for (const tag of kitchenTags) {
    const isAssigned = assignedTags.findIndex(t => t === tag) !== -1;
    if (!isAssigned) {
      if (tag === ORDER_SUMMARY_PRINTER_TAG) {
        noAssignedTags.push(t('Receipt'));
      } else {
        noAssignedTags.push(tag);
      }
    }
  }

  // 计算所有分配的标签(不考虑在线状态)
  const allAssignedTags = uniq(
    settings.reduce((tags, printer) => {
      if (printer.isReceiptPrinter) {
        tags.push(ORDER_SUMMARY_PRINTER_TAG);
      }
      const printerTags = printer.tags;
      if (!isEmpty(printerTags)) {
        return tags.concat(printerTags);
      }
      return tags;
    }, [])
  );
  // 检查是否所有标签都已分配
  const allNoAssignedTags = [];
  for (const tag of kitchenTags) {
    const isAssigned = allAssignedTags.findIndex(t => t === tag) !== -1;
    if (!isAssigned) {
      if (tag === ORDER_SUMMARY_PRINTER_TAG) {
        allNoAssignedTags.push(t('Receipt'));
      } else {
        allNoAssignedTags.push(tag);
      }
    }
  }

  const result = {
    isPrinterAssigned: noAssignedTags.length === 0,
    noAssignedTags,
    offlinePrinters,
    isAllAssigned: allNoAssignedTags.length === 0, // 是否所有标签都已被分配（不考虑打印机的在线状态）
    noAssignedTagsIgnoreOffline: allNoAssignedTags, // 不考虑在线状态的未分配标签
  };

  yield put(updatePrinterAssignedStatus(result));
  onComplete && onComplete(result);
  return result;
};

export const forgetPrinterSaga = function* (action: Action<string>) {
  const printerId = action.payload;
  yield put(updateSinglePrinter({ printerId }));
  yield put(checkAssignedPrinter({}));
  trackLogEvent({
    action: PrinterAction.ForgetPrinter,
    level: LoggingLevel.Info,
    privateDataPayload: { printerId },
  });
};

export const notifyOfflinePrinters = function* () {
  const offlineCount = yield select(selectOfflinePrintersCount);
  if (offlineCount > 0) {
    const text = t('{{count}} printer(s) offline', { count: offlineCount });
    openPrinterOfflineToast(text);
  }
};

export const pingSinglePrinter = function* (lanPrinter: PrinterConfigType, showToast = false, isSearch = false) {
  if (lanPrinter) {
    const printerId = lanPrinter.printerId;
    const hostIP = yield select(selectIPAddress);
    const isNetworkAvailable = yield select(selectIsNetConnected);
    const { pingWorking, pingStatus, modifiedDate, ...printerInfo } = lanPrinter;

    let isOnline;
    if (lanPrinter.isOnline) {
      const response = yield call(PingUtils.getPacketLossFloat, lanPrinter.lanIp, { count: 5, timeout: 5 });
      const pingValue = response.data;

      // online -> offline, online -> online
      isOnline = pingValue >= 0 && pingValue < 100;
      yield put(updateSinglePrinter({ printerId, data: { pingStatus: pingValue, pingWorking: false, isOnline } }));

      trackLogEvent({
        action: PrinterAction.PingPrinter,
        level: LoggingLevel.Info,
        mobileData: { host: { hostIP, isNetworkAvailable } },
        privateDataPayload: { ping: pingValue, pingErrorMessage: response.message, ...printerInfo },
      });
    } else if (!lanPrinter.isOnline && !isSearch) {
      // no need to check port after searching
      const response = yield call(PrinterManager.connectTcp, { printerId, ip: lanPrinter.lanIp, port: Number(lanPrinter.lanPort), timeout: 3000 });
      isOnline = response.errCode === 0;

      // offline -> offline, offline -> online
      yield put(updateSinglePrinter({ printerId, data: { pingWorking: false, pingStatus: isOnline ? 0 : 100, isOnline } }));
      trackLogEvent({
        action: PrinterAction.CheckPrinterPort,
        level: LoggingLevel.Info,
        mobileData: { host: { hostIP, isNetworkAvailable } },
        privateDataPayload: { checkErrorCode: String(response.errCode), checkErrorMessage: String(response.errMessage), ...printerInfo },
      });
    }
    if (!isOnline && showToast) {
      yield call(notifyOfflinePrinters);
    }
  }
};

export const pingLanPrintersSaga = function* (action: Action<PingLanPrintersType>) {
  const { onlyAssigned, showToast = false, isSearch = false, source } = action.payload;
  const isPrinterSearching = yield select(selectIsPrinterSearching);
  if (isPrinterSearching) {
    console.log('isPrinterSearching');
    return;
  }
  let printers: PrinterConfigType[] = yield select(selectAllPrinters);
  trackLogEvent({
    action: PrinterAction.PingPrinter,
    level: LoggingLevel.Info,
    privateDataPayload: {
      source,
      printerJSON: JSON.stringify({
        printers,
        onlyAssigned,
        showToast,
        isSearch,
      }),
    },
  });
  if (onlyAssigned) {
    printers = yield select(selectAssignedPrinters);
  }
  printers = filter(printers, (printer: PrinterConfigType) =>
    Boolean(printer && printer.lanIp && printer.lanPort && printer.printerConnectType === 'LAN' && printer.printerModelType !== 'AIRPRINTER')
  );

  const tasks: CallEffect<void>[] = [];
  for (const lanPrinter of printers) {
    tasks.push(call(pingSinglePrinter, lanPrinter, showToast, isSearch));
  }
  yield all(tasks);
  // ping结束之后，更新一下assignedPrinter的状态
  yield spawn(checkAssignedPrinterSaga, checkAssignedPrinter({}));
};

/**
 * remove unused offline printer
 * @param printer
 * @param endOfDate
 * @param kitchenTagsFromBO
 * @returns
 */
export const printerNeedToRemove = (printer: PrinterConfigType, endOfDate: Moment, kitchenTagsFromBO: string) => {
  const isOutOfDate = printer.lastPrintedTime && moment(printer.lastPrintedTime).isBefore(endOfDate);
  const isUsed = checkPrinterIsUsed(printer, kitchenTagsFromBO);
  return !printer.isOnline && (isOutOfDate || !isUsed);
};

/**
 * 根据printerId去重
 * 保留isOnline:true, lastPrintedTime最大的
 * @param printers
 * @returns
 */
export const sortAndUniquePrinters = (printers: PrinterConfigType[]) => {
  return chain(printers)
    .orderBy([printer => printer.isOnline || false, printer => (printer.lastPrintedTime ? new Date(printer.lastPrintedTime) : new Date(0))], ['desc', 'desc'])
    .uniqBy('printerId')
    .value();
};
/**
 * remove unAssigned printer
 * mark all printers offline
 * @returns printers
 */
export const handlePrintersBeforeRefresh = function* ({ flowId }) {
  const kitchenTagsFromBO = yield select(selectKitchenPrinters);
  const originalPrinters: PrinterConfigType[] = yield select(selectAllPrinters);
  // remove unused offline printer
  let printers = originalPrinters.filter(printer => printer.isOnline || checkPrinterIsUsed(printer, kitchenTagsFromBO));

  // 去重，增加容错
  printers = sortAndUniquePrinters(printers);

  // Set all assigned printer isOffline
  // get initPrinters
  const initPrinters = reduce(
    printers,
    (acc, curPrinter) => {
      // 避免init将offline的XPrinter标记为online
      if (curPrinter.isOnline) {
        acc.push(curPrinter);
      }
      curPrinter.isOnline = false;
      return acc;
    },
    []
  );

  yield put(updatePrinterTagsSettings(printers));
  trackLogEvent({
    action: PrinterAction.RefreshPrinter,
    level: LoggingLevel.Info,
    privateDataPayload: {
      flowId,
      kitchenPrinters: kitchenTagsFromBO,
      source: 'beforeRefresh',
      printerJSON: JSON.stringify({
        originalPrinters,
        printers,
      }),
    },
  });
  return { printers, initPrinters };
};

/**
 * remove offline and no print last 2 weeks
 * @returns
 */
export const handlePrintersAfterRefresh = function* ({ flowId }) {
  const originalPrinters = yield select(selectAllPrinters);

  const kitchenTagsFromBO = yield select(selectKitchenPrinters);
  const last2Weeks = moment().subtract(2, 'weeks');
  const printers = yield call(
    autoAssignUsbXPrinterTag,
    originalPrinters.filter(printer => !printerNeedToRemove(printer, last2Weeks, kitchenTagsFromBO))
  );
  yield put(updatePrinterTagsSettings(printers));
  trackLogEvent({
    action: PrinterAction.RefreshPrinter,
    level: LoggingLevel.Info,
    privateDataPayload: {
      flowId,
      kitchenPrinters: kitchenTagsFromBO,
      endOfDate: last2Weeks.toISOString(),
      source: 'afterRefresh',
      printerJSON: JSON.stringify({
        originalPrinters,
        printers,
      }),
    },
  });
  return printers;
};

export const autoAssignUsbXPrinterTag = function (printers: PrinterConfigType[]) {
  const newUsbXPrinters = printers.filter(
    value =>
      value.printerConnectType === PrinterConnectType.USB &&
      value.printerModelType === 'XPRINTER' &&
      !value.isReceiptPrinter &&
      !value.isBeepOrderSummaryPrinter &&
      (!value.tags || value.tags.length === 0) &&
      value.isOnline
  );
  // has 1 new unassigned usb xprinter
  if (newUsbXPrinters.length === 1) {
    const usedUsbXPrinters = printers.filter(
      value =>
        value.printerConnectType === PrinterConnectType.USB &&
        value.printerModelType === 'XPRINTER' &&
        (value.isReceiptPrinter || value.isBeepOrderSummaryPrinter || (value.tags && value.tags.length > 0))
    );
    // has 1 offline assigned usb xprinter
    if (usedUsbXPrinters.length === 1 && !usedUsbXPrinters[0].isOnline) {
      const usedUsbXPrinter = usedUsbXPrinters[0];
      const newUsbXPrinter = newUsbXPrinters[0];
      newUsbXPrinter.isReceiptPrinter = usedUsbXPrinter.isReceiptPrinter;
      newUsbXPrinter.isBeepOrderSummaryPrinter = usedUsbXPrinter.isBeepOrderSummaryPrinter;
      newUsbXPrinter.tags = usedUsbXPrinter.tags;
      // remove old offline usb xprinter
      printers = printers.filter(value => value.printerId !== usedUsbXPrinter.printerId);
    }
  }

  return printers;
};

export const searchPrinterSaga = function* (action) {
  const payload: SearchPrinterType = get(action, 'payload', {});
  const { onComplete } = payload;
  try {
    yield put(updatePrinterSearching(true));
    PrinterManager.bindService();
    const receiptType = yield select(selectReceiptType);
    const flowId = Date.now().toString();
    const { printers } = yield call(handlePrintersBeforeRefresh, { flowId });
    let result;
    if (receiptType === 'a4') {
      result = yield call(PrinterManager.searchPrinters, 'A4');
    } else {
      result = yield call(PrinterManager.searchPrinters, 'Thermal');
    }

    const newPrinters = yield call(handlePrintersAfterRefresh, { flowId });

    infoPrintingEvent({
      action: PrinterAction.SearchPrinterManually,
      privateDataPayload: {
        flowId,
        receiptType,
        prevPrinters: stringify(printers),
        nextPrinters: stringify(newPrinters),
        errCode: String(get(result, 'errCode', '')),
        errMessage: String(get(result, 'errMessage', '')),
      },
    });
    yield put(updatePrinterSearching(false));
    yield put(pingLanPrinters({ isSearch: true, source: 'afterSearchPrinter' }));
    safeCallback(onComplete, result);
  } catch (ex) {
    safeCallback(onComplete, null);
    console.log('searchPrinterSaga exception', ex);
  }
};

export const initPrinterSaga = function* (action) {
  const payload: InitPrinterType = get(action, 'payload', {});
  const { onComplete } = payload;
  try {
    PrinterManager.bindService();
    const receiptType = yield select(selectReceiptType);
    const flowId = Date.now().toString();
    const { initPrinters } = yield call(handlePrintersBeforeRefresh, { flowId });
    let result;
    if (receiptType === 'a4') {
      result = yield call(PrinterManager.initPrinters, 'A4', initPrinters);
    } else {
      result = yield call(PrinterManager.initPrinters, 'Thermal', initPrinters);
    }

    const newPrinters = yield call(handlePrintersAfterRefresh, { flowId });
    infoPrintingEvent({
      action: PrinterAction.InitPrinter,
      privateDataPayload: {
        receiptType,
        flowId,
        prevPrinters: stringify(initPrinters),
        nextPrinters: stringify(newPrinters),
        errCode: String(get(result, 'errCode', '')),
        errMessage: String(get(result, 'errMessage', '')),
      },
    });
    // for loggly track
    if (hasOnlinePrinter(newPrinters)) {
      yield put(updatePrinterGeneralSettings({ merchantHasPrinter: true }));
    }

    safeCallback(onComplete, result);
  } catch (ex) {
    console.log('initPrinterSaga exception', ex);
  }
};

/**
 * init + search printers
 * Callback: update_printer_list from Native
 * TCP send all online printers to RN
 * @param action
 */
export const findNewPrintersSaga = function* (action: Action<FindNewPrintersType>) {
  const udpRestoreId = action.payload.udpRestoreId;
  if (udpRestoreId) {
    return;
  }

  const nextPrinters = filter<PrinterConfigType>(action.payload.printers, Boolean);
  // update printers
  const prevRecordPrinters: PrinterConfigType[] = yield select(selectAllPrinters);
  forEach(nextPrinters, next => {
    if (next.isLabelPrinter) {
      next.isReceiptPrinter = false;
    }

    const nextPrinterInfo = Object.assign({}, next, {
      migrationPrinterIdV3: null,
      isOnline: true,
      pingWorking: false,
      pingStatus: 0,
      modifiedDate: new Date().toISOString(),
    });

    const prevAssignedIndex = findIndex(prevRecordPrinters, prev => {
      // match order:
      // 1st: isBuiltInPrinter
      // 2nd: printerId: prev.printerId === next.printerId
      // 3rd: prev.printerId === next.prevPrinterId: for the older versions, the printerId is not generated by macAddress but IP&Port, so give the chance to migrate the tags from them.
      // 4rd: avoid printers with same IP
      // 第5个匹配规则（iOS migration用）：prev.migrationPrinterIdV3有值就代表着是刚从iOS native迁移过来的，也只有这一次会有值，后续合并完之后就会被清空
      // 第6个匹配规则（iOS migration用）：iOS native迁移过来，XPrinter经过了init, 清空migrationPrinterIdV3，但printerId值还是之前migrationPrinterIdV3的值
      // 后续触发了XPrinter的搜索后可以拿新生成的migrationPrinterIdV3值来匹配，如果匹配上了，就代表着是同一个XPrinter，可以合并，并用正确的printerId来替换
      return (
        (prev &&
          ((prev.isBuiltInPrinter === true && next.isBuiltInPrinter === true) ||
            prev.printerId === next.printerId ||
            prev.printerId === next.prevPrinterId ||
            (prev.prevPrinterId && prev.prevPrinterId === next.prevPrinterId))) ||
        (prev.migrationPrinterIdV3 && next.migrationPrinterIdV3 && prev.migrationPrinterIdV3 === next.migrationPrinterIdV3) ||
        (prev.printerId && next.migrationPrinterIdV3 && prev.printerId === next.migrationPrinterIdV3)
      );
    });

    if (prevAssignedIndex !== -1) {
      // assigned before, should remember the tags
      const recordPrinter = prevRecordPrinters[prevAssignedIndex];
      Object.assign(recordPrinter, nextPrinterInfo);
    } else {
      // unAssigned printer
      prevRecordPrinters.push(nextPrinterInfo);
    }
  });

  for (const prevRecordPrinter of prevRecordPrinters) {
    if (isUsedXPrinter(prevRecordPrinter)) {
      prevRecordPrinter.isOnline = nextPrinters.filter(value => value.printerId === prevRecordPrinter.printerId).length > 0;
    }
  }

  const reassigned = yield call(
    autoAssignUsbXPrinterTag,
    prevRecordPrinters.filter(value => {
      if (isUnUsedXPrinter(value)) {
        for (const nextPrinter of nextPrinters) {
          if (nextPrinter.printerId === value.printerId) {
            return true;
          }
        }
        return false;
      }
      return true;
    })
  );
  yield put(updatePrinterTagsSettings(reassigned));
};

/**
 * restoreLanXPrintersByUDP
 * Callback: update_printer_list when get UDP result from Native
 * UDP only send restore LanXPrinter to RN
 * @param action
 */
export const restoreLanXPrintersSaga = function* (action: Action<FindNewPrintersType>) {
  const udpRestoreId = action.payload.udpRestoreId;
  if (!udpRestoreId) {
    return;
  }
  console.log('restoreLanXPrintersSaga 0', action.payload);

  const restoredPrinters = filter<PrinterConfigType>(action.payload.printers, Boolean);
  // update printers
  let hasRestoredPrinters = false;
  const prevRecordPrinters: PrinterConfigType[] = yield select(selectAllPrinters);
  forEach(restoredPrinters, next => {
    const prevPrinter = find(prevRecordPrinters, prev => {
      // only match printerId
      return prev.printerId === next.printerId;
    });
    if (prevPrinter && (!prevPrinter.isOnline || prevPrinter.lanIp !== next.lanIp)) {
      trackLogEvent({
        action: PrinterAction.UdpSearch,
        level: LoggingLevel.Info,
        mobileData: {
          workflowId: udpRestoreId,
          printer: {
            printerId: prevPrinter.printerId,
            isOnline: prevPrinter.isOnline,
            lanIp: prevPrinter.lanIp,
            newLanIp: next.lanIp,
          },
        },
        privateDataPayload: { printerJSON: JSON.stringify(next), event: 'printerFixedByUdp' },
      });
      // offline or lanIp changed
      hasRestoredPrinters = true;
      if (next.isLabelPrinter) {
        next.isReceiptPrinter = false;
      }

      const nextPrinterInfo = Object.assign({}, next, {
        migrationPrinterIdV3: null,
        isOnline: true,
        pingWorking: false,
        pingStatus: 0,
        modifiedDate: new Date().toISOString(),
      });
      Object.assign(prevPrinter, nextPrinterInfo);
    }
  });

  if (hasRestoredPrinters) {
    console.log('restoreLanXPrintersSaga 1', prevRecordPrinters);
    yield put(updatePrinterTagsSettings(prevRecordPrinters));
  }
  yield put(udpSearchFinished(action.payload));
};

export const isUnUsedXPrinter = (value: PrinterConfigType) => {
  return (
    value.printerConnectType === PrinterConnectType.USB &&
    value.printerModelType === 'XPRINTER' &&
    !value.isReceiptPrinter &&
    !value.isBeepOrderSummaryPrinter &&
    (!value.tags || value.tags.length === 0)
  );
};

export const isUsedXPrinter = (value: PrinterConfigType) => {
  return (
    value.printerConnectType === PrinterConnectType.USB &&
    value.printerModelType === 'XPRINTER' &&
    (value.isReceiptPrinter || value.isBeepOrderSummaryPrinter || (value.tags && value.tags.length > 0))
  );
};

export const handleUpdatePrinter = function* () {
  const chan = yield actionChannel(findNewPrinters.toString(), buffers.expanding(5));

  try {
    while (true) {
      const _action: Action<FindNewPrintersType> = yield take(chan);
      if (_action.payload.udpRestoreId) {
        yield call(restoreLanXPrintersSaga, _action);
      } else {
        yield call(findNewPrintersSaga, _action);
      }
    }
  } finally {
    console.log('handleUpdatePrinter canceled.');
  }
};

export const filterKitchenTags = (boKitchenTags: string[], cacheTags: string[]) =>
  filter(cacheTags, tag => findIndex(boKitchenTags, item => tag === item) !== -1);

export const checkPrinterIsUsed = (printer: PrinterConfigType, kitchenTagsFromBO: string) => {
  if (printer.isReceiptPrinter && !Boolean(printer.isLabelPrinter)) {
    return true;
  }
  if (printer.isBeepOrderSummaryPrinter) {
    return true;
  }

  const kitchenPrinterArray = (kitchenTagsFromBO && kitchenTagsFromBO.split(',')) || [];
  const tags = filterKitchenTags(kitchenPrinterArray, printer.tags);
  return !isEmpty(tags);
};

export const updatePrinterTagSaga = function* (action: Action<UpdatePrinterTagType>) {
  const { printerId, title, value } = action.payload;
  const nextPrinters: PrinterConfigType[] = yield select(selectAllPrinters);
  const recordIndex = findIndex(nextPrinters, printer => printer.printerId === printerId);
  if (recordIndex < 0) {
    return;
  }
  if (title === t('Receipt')) {
    forEach(nextPrinters, (printer, index) => {
      if (index === recordIndex) {
        printer.isReceiptPrinter = value;
      } else {
        printer.isReceiptPrinter = false;
      }
    });
  } else if (title === t('QR Order Summary')) {
    nextPrinters[recordIndex].isBeepOrderSummaryPrinter = value;
  } else if (value) {
    // add tags
    nextPrinters[recordIndex].tags = Array.from(new Set(concat(nextPrinters[recordIndex].tags, title).filter(Boolean)));
  } else {
    const tags = nextPrinters[recordIndex].tags;
    if (tags) {
      const tagIndex = findIndex(tags, t => t === title);
      if (tagIndex !== -1) {
        tags.splice(tagIndex, 1);
      }
    }
  }

  yield put(updatePrinterTagsSettings(nextPrinters));
  yield put(checkAssignedPrinter({}));
};
