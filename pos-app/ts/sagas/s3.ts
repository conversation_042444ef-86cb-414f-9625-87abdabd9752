import { get, isEmpty, startsWith } from 'lodash';
import moment from 'moment';
import RNFS from 'react-native-fs';
import { call, fork, put, select, takeEvery, takeLatest } from 'redux-saga/effects';
import * as Actions from '../actions';
import { updateAWSConfig } from '../actions';
import { AWS_S3_DB_ACL, AWS_S3_DB_BUCKET, AWS_S3_PRINTING_IMAGE, AWS_S3_REGION, ENABLE_DUMP_PRINTING_IMAGES } from '../config';
import { IsAndroid } from '../constants';
import { RNS3 } from '../utils/aws/RNS3';
import { getAWSConfig, s3DownloadThumbnail } from '../utils/awsS3Helper';
import { selectAWSConfig, selectBusinessName, selectPrintDoUpload, selectRegisterId } from './selector';

function* uploadPrintingImagesSaga() {
  if (!ENABLE_DUMP_PRINTING_IMAGES) {
    return;
  }

  const businessName = yield select(selectBusinessName);
  const registerId = yield select(selectRegisterId);

  try {
    const cacheDir = RNFS.CachesDirectoryPath;

    const files = yield call(RNFS.readDir, cacheDir);
    if (!isEmpty(files)) {
      for (let index = 0; index < files.length; index++) {
        const f = files[index];
        if (startsWith(f.name, 'HalfReceipt')) {
          yield call(uploadImageToS3, f, `${businessName}/${registerId}/half_receipt_images/`);
        } else if (startsWith(f.name, 'Receipt')) {
          yield call(uploadImageToS3, f, `${businessName}/${registerId}/receipt_images/`);
        } else if (startsWith(f.name, 'OrderSummary')) {
          yield call(uploadImageToS3, f, `${businessName}/${registerId}/order_summary_images/`);
        } else if (startsWith(f.name, 'KitchenTicket')) {
          yield call(uploadImageToS3, f, `${businessName}/${registerId}/kitchen_images/`);
        } else if (startsWith(f.name, 'BeerKitchen')) {
          yield call(uploadImageToS3, f, `${businessName}/${registerId}/beer_images/`);
        }
      }
    }
  } catch (error) {
    console.log('uploadPrintingImagesSaga error: ', error);
  }
}

function* uploadIncompletePrintingImageSaga() {
  const doUpload = yield select(selectPrintDoUpload);
  const businessName = yield select(selectBusinessName);
  const registerId = yield select(selectRegisterId);

  try {
    const cacheDir = RNFS.CachesDirectoryPath;

    const files = yield call(RNFS.readDir, cacheDir);
    if (!isEmpty(files)) {
      for (let index = 0; index < files.length; index++) {
        const f = files[index];
        if (startsWith(f.name, 'RENDER_NOT_COMPLETE')) {
          if (!doUpload) {
            // delete saved images
            RNFS.unlink(f.path);
          } else {
            const uploaded = yield call(uploadImageToS3, f, `${businessName}/${registerId}/incomplete_images/`);
            if (!uploaded) {
              // delete also
              RNFS.unlink(f.path);
            }
          }
        }
      }
    }
  } catch (error) {
    console.log('uploadPrintingImagesSaga error: ', error);
  }
}

function* uploadImageToS3(f, path) {
  const file = {
    uri: IsAndroid ? `file://${f.path}` : f.path,
    name: f.name,
    type: 'application/plain',
  };

  console.log('upload printing image:', f.path);

  const options = {
    acl: AWS_S3_DB_ACL,
    keyPrefix: path,
    bucket: AWS_S3_PRINTING_IMAGE,
    region: AWS_S3_REGION,
    // accessKey: AWS_ACESS_KEY_ID,
    // secretKey: AWS_SECRET_ACCESS_KEY,
    successActionStatus: 201,
  };
  const response = yield call(uploadFileToS3WithCheckInvalidSaga, { type: '', payload: { file, options } });

  if (response.status === 201) {
    console.log('Succeeded');
    // delete the uploaded file
    RNFS.unlink(f.path);
    return true;
  } else {
    console.log('Failed response: ', response);
    return false;
  }
}

export function* uploadLogZipToS3(filePath: string, fileName: string, savePath: string) {
  const file = {
    uri: IsAndroid ? `file://${filePath}` : filePath,
    name: fileName,
    type: 'application/zip',
  };

  console.log('upload log zip:', filePath);

  const options = {
    acl: AWS_S3_DB_ACL,
    keyPrefix: savePath,
    bucket: AWS_S3_DB_BUCKET,
    region: AWS_S3_REGION,
    successActionStatus: 201,
  };
  const response = yield call(uploadFileToS3WithCheckInvalidSaga, { type: '', payload: { file, options } });
  if (response.status === 201) {
    // delete the uploaded file
    RNFS.unlink(filePath);
    return true;
  } else {
    return false;
  }
}

export function* uploadFileToS3WithCheckInvalidSaga(action) {
  const { file, options, onComplete, isRetry = false } = action.payload;
  const awsConfig = yield call(getAWSConfig);
  // console.log('uploadFileToS3WithCheckInvalidSaga ===>>>>', { action, awsConfig });
  let response;
  if (Boolean(awsConfig) && !isEmpty(awsConfig.accessKeyId)) {
    options.accessKey = awsConfig.accessKeyId;
    options.secretKey = awsConfig.accessKeySecret;
    options.sessionToken = awsConfig.sessionToken;
    response = yield call(RNS3.put, file, options);
    // console.log(`uploadFileToS3WithCheckInvalidSaga ${isRetry ? 'Retry' : ''} = `, response);
    if ([403, 400].includes(get(response, 'status')) && !isRetry) {
      // accessDenid
      yield call(getAWSConfig, true);
      return yield call(uploadFileToS3WithCheckInvalidSaga, { type: '', payload: { file, options, onComplete, isRetry: true } });
    }
  } else {
    response = { status: 0 };
  }
  onComplete && onComplete(response);
  return response || { status: 0 };
}

// ---- test -----
export function* testDownloadSaga() {
  const businessName = yield select(selectBusinessName);
  yield call(s3DownloadThumbnail, businessName, '64c8a99cd9b0520007befa75');
}

export function* testUploadSaga() {
  const appDirectory = RNFS.DocumentDirectoryPath;
  const realmFilePath = `${appDirectory}/default.realm`;
  const businessName = yield select(selectBusinessName);
  const registerId = yield select(selectRegisterId);
  const time = moment().format('yyyy-MM-DD_HH:mm:ss');

  const exist = yield call(RNFS.exists, realmFilePath);
  if (exist) {
    const file = {
      uri: IsAndroid ? `file://${realmFilePath}` : realmFilePath,
      name: 'default.realm',
      type: 'application/plain',
    };

    const options = {
      acl: AWS_S3_DB_ACL,
      keyPrefix: `${businessName}/${registerId}/${time}/`,
      bucket: AWS_S3_DB_BUCKET,
      region: AWS_S3_REGION,
      // accessKey: AWS_ACESS_KEY_ID,
      // secretKey: AWS_SECRET_ACCESS_KEY,
      successActionStatus: 201,
    };
    yield call(uploadFileToS3WithCheckInvalidSaga, { type: '', payload: { file, options } });
  }
}

export function* mockAWSAccessInvalidSaga() {
  const awsConfig = yield select(selectAWSConfig);
  yield put(updateAWSConfig({ ...awsConfig, accessKeyId: 'accessKeyId', expiration: new Date().toISOString() }));
}

function* s3() {
  yield takeEvery(Actions.testDownload, testDownloadSaga);
  yield takeEvery(Actions.testUpload, testUploadSaga);
  yield takeEvery(Actions.mockAWSAccessInvalid, mockAWSAccessInvalidSaga);
  yield takeEvery(Actions.uploadPrintingImages, uploadPrintingImagesSaga);
  yield takeLatest(Actions.uploadIncompletePrintingImage, uploadIncompletePrintingImageSaga);
  yield takeEvery(Actions.uploadFileToS3WithCheckInvalid, uploadFileToS3WithCheckInvalidSaga);
}

export default fork(s3);
