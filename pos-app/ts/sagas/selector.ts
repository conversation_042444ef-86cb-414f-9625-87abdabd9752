import * as Immutable from 'immutable';
import { filter, find, get, isEmpty, isEqual } from 'lodash';
import { createSelector, createSelectorCreator, defaultMemoize } from 'reselect';
import { GhlApiResultType, GhlBaseResponse, MasterState, Message, PrinterConfigType, TransactionTypeWithDisplay } from '../actions';
import { BIRStoreType, INIT_PID, INIT_SNAPSHOT_VERSION, MRSRole } from '../constants';

import DAL from '../dal';
import { CfdState } from '../reducers/cfd';
import { CfdSettingType } from '../reducers/cfdApp';
import { GhlStatus } from '../reducers/ghl';
import { KdsSettingType } from '../reducers/kds';
import { LocalCountryMapType } from '../reducers/localCountryMap';
import { SingleClient } from '../reducers/mrs';
import { NcsSettingType } from '../reducers/ncs';
import { defaultAssignedStatus } from '../reducers/printer';
import { RootState } from '../typings';
import {
  defaultInsufficientStorageWarning,
  defaultKdsLandingPageUrls,
  DefaultLocalLoggingConfig,
  defaultNetworkQualityMonitoring,
  defaultNetworkReachabilityConfig,
  defaultNewProductSyncFlowConfig,
  defaultPushyConfig,
  defaultSettingTagConfig,
  defaultStorageAutoCleanConfig,
  defaultTransactionDBAutoCleanConfig,
  disabledSupportPhoneNumberMessages,
  GBFeature,
  NetworkQualityMonitoring,
  NewProductSyncFlowType,
  PushyConfig,
  ScannerFix,
  SupportPhoneNumber,
  TrackApiResponseTimeConfigType,
} from '../utils/growthbook';
import { CookingStatusType } from '../utils/kds/status';
import { ReceiptFontSize } from '../utils/printer';
import { WebsocketStateEnum } from '../utils/websocket';
import { checkPrinterIsUsed } from './printing/printer';

const EMPTY_IMMUTABLE_MAP = Immutable.Map<string, any>();
const EMPTY_IMMUTABLE_LIST = Immutable.List();

export const createDeepEqualSelector = createSelectorCreator(defaultMemoize, isEqual);

export const selectTransactionSession = (state: RootState) => state.get('TransactionSession');

export const selectTransactionSessionTotal = (state: RootState) => state.getIn(['TransactionSession', 'total']);

export const selectSyncInfo = (state: RootState) => state.getIn(['Storage', 'syncInfo']);
export const selectSyncInfoLastProductImagesSyncProgress = (state: RootState) => state.getIn(['Storage', 'syncInfo', 'lastProductImagesSyncProgress']);

export const selectLastProductSyncTime = (state: RootState) => state.getIn(['Storage', 'syncInfo', 'lastProductSyncTime']);

export const selectLastTrxCancelledFromBOSyncTime = (state: RootState) => state.getIn(['Storage', 'syncInfo', 'lastTrxCancelledFromBOSyncTime']);

export const selectLastQuickLayoutSyncTime = (state: RootState) => state.getIn(['Storage', 'syncInfo', 'lastQuickLayoutSyncTime']);

export const selectLastEmployeeSyncTime = (state: RootState) => state.getIn(['Storage', 'syncInfo', 'lastEmployeeSyncTime']);

export const selectLastPromotionSyncTime = (state: RootState) => state.getIn(['Storage', 'syncInfo', 'lastPromotionSyncTime']);

export const selectStoreInfo = (state: RootState) => state.getIn(['Storage', 'storeInfo'], EMPTY_IMMUTABLE_MAP);

export const selectStoreInfoUnsafe = (state: RootState) => state.getIn(['Storage', 'storeInfo']);

export const selectStore = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store'], EMPTY_IMMUTABLE_MAP);

export const selectOrderSummaryPrinter = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'orderSummaryPrinter'], '');

export const selectDefaultKitchenPrinter = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'defaultKitchenPrinter'], '');

export const selectBusinessName = (state: RootState): string => state.getIn(['Storage', 'storeInfo', 'name'], '');

export const selectCountry = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'country'], '');

export const selectLayoutId = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'quickSelectLayoutId']);

export const selectQuickLayout = (state: RootState) => state.getIn(['Storage', 'quickLayout']);

export const selectEmployeeId = (state: RootState): string | undefined => state.getIn(['CurrentEmployee', 'employeeId']);

export const selectQRRegisters = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'qrRegisters'], EMPTY_IMMUTABLE_LIST);

export const selectStoreId = (state: RootState): string => state.getIn(['Storage', 'storeInfo', 'store', '_id']);

export const selectRegisterObjectId = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'registerObjectId'], '');

export const selectRegisterId = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'registerId'], undefined);

export const selectRegisterName = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'registerName'], '');

export const selectNextId = (state: RootState) => state.getIn(['Storage', 'nextId']);

export const selectIncludingTaxInDisplay = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'includingTaxInDisplay']);

export const selectSequence = (state: RootState) => state.getIn(['Storage', 'sequence'], EMPTY_IMMUTABLE_MAP);

export const selectPrinterTagsSettings = (state: RootState) => state.getIn(['Settings', 'printerTagsSettings'], EMPTY_IMMUTABLE_LIST);
export const selectIsPrinterSearching = (state: RootState): boolean => state.getIn(['Printer', 'isSearching'], false);
export const selectAutoReprintByUdpTime = (state: RootState) => state.getIn(['Printer', 'autoReprintByUdpTime'], 0);

export const selectAllPrinters = createSelector(selectPrinterTagsSettings, settings => {
  return filter(settings.toJS(), v => Boolean(v));
});

export const selectPrinterById = (printerId: string) =>
  createSelector(selectAllPrinters, (settings: PrinterConfigType[]) => {
    return find(settings, v => v && v.printerId === printerId);
  });

export const selectOfflinePrintersCount = createSelector(selectAllPrinters, (printers: PrinterConfigType[]) => {
  return filter(printers, (v: PrinterConfigType) => !v.isOnline).length;
});

export const selectAllOnlinePrinters = createSelector(selectAllPrinters, (printers: PrinterConfigType[]) => {
  return filter(printers, (v: PrinterConfigType) => v.isOnline);
});

export const selectIsPingWorking = createSelector(selectAllPrinters, (printers: PrinterConfigType[]) => {
  return Boolean(printers.find(printer => printer.pingWorking));
});

export const selectKitchenPrinters = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'kitchenPrinters'], '');

export const selectAssignedPrinters = createSelector(selectAllPrinters, selectKitchenPrinters, (printers, tags) => {
  return filter(printers, printer => checkPrinterIsUsed(printer, tags));
});

export const selectUnAssignedPrinters = createSelector(selectAllPrinters, selectKitchenPrinters, (printers, tags) => {
  return filter(printers, printer => !checkPrinterIsUsed(printer, tags));
});

export const selectSettings = (state: RootState) => state.get('Settings', EMPTY_IMMUTABLE_MAP);

export const selectMerchantHasPrinter = (state: RootState) => state.getIn(['Settings', 'printerGeneralSettings', 'merchantHasPrinter'], null);

export const selectKitchenSeparate = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'separateKitchenItems'], false);
export const selectIsOneZReadingPerDayEnabled = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'isOneZReadingPerDayEnabled'], false);

export const selectGeo = (state: RootState) => state.getIn(['Storage', 'geo']);

export const selectDevice = (state: RootState) => state.getIn(['Storage', 'device']);

export const selectDeviceModel = (state: RootState) => state.getIn(['Storage', 'device', 'model']);

export const selectOnlineSyncTime = (state: RootState) => state.getIn(['Storage', 'syncInfo', 'onlineSyncTime']);
export const selectLastSyncTime = (state: RootState) => state.getIn(['Storage', 'syncInfo', 'lastSyncTime'], '');

export const selectOpenCashDrawerWhenCloseShift = (state: RootState) =>
  state.getIn(['Storage', 'storeInfo', 'store', 'cashierAccesses', 'openCashDrawerWhenCloseShift']);

export const selectPrintReportAfterCloseShift = (state: RootState) =>
  state.getIn(['Storage', 'storeInfo', 'store', 'cashierAccesses', 'printReportAfterCloseShift']);
export const selectPosSettingAccess = (state: RootState) => state.getIn<string>(['Storage', 'storeInfo', 'store', 'cashierAccesses', 'posSetting'], '0');
// #region netInfo
export const selectCurrentNetworkType = (state: RootState) => state.getIn(['NetInfo', 'type'], 'none');
export const selectCurrentNetworkInfo = (state: RootState) => state.get('NetInfo', EMPTY_IMMUTABLE_MAP);
export const selectIPAddress = (state: RootState) => {
  const ipAddress = state.getIn(['NetInfo', 'details', 'ipAddress'], '');
  return ipAddress && ipAddress !== '0.0.0.0' ? ipAddress : '';
};
export const selectPureIP = (state: RootState) => {
  return state.getIn(['NetInfo', 'details', 'ipAddress'], '');
};
export const selectDefaultNetwork = (state: RootState) => state.getIn(['Settings', 'defaultWifi'], EMPTY_IMMUTABLE_MAP);

export const selectDefaultNetworkType = (state: RootState) => state.getIn(['Settings', 'defaultWifi', 'type'], 'none');
export const selectDefaultNetworkName = (state: RootState) => {
  const defaultNetworkType = state.getIn(['Settings', 'defaultWifi', 'type'], 'none') as string;
  let defaultNetworkName = '';
  if (defaultNetworkType === 'wifi') {
    defaultNetworkName = state.getIn(['Settings', 'defaultWifi', 'details', 'ssid'], '');
  } else if (defaultNetworkType === 'ethernet') {
    defaultNetworkName = state.getIn(['Settings', 'defaultWifi', 'details', 'ipAddress'], '');
  }
  return defaultNetworkName;
};

export const selectCurrentWifiSSID = (state: RootState) => state.getIn(['NetInfo', 'details', 'ssid'], '');

export const selectIsNetConnected = (state: RootState): boolean => state.getIn(['NetInfo', 'isConnected'], false);
// #endregion   If either beepQR or beepDelivery is enabled, then this value is true.
export const selectIsBeepQREnabled = (state: RootState): boolean => state.getIn(['Storage', 'storeInfo', 'store', 'isQROrderingEnabled'], false);
export const selectIsBeepDeliveryEnabled = (state: RootState): boolean => state.getIn(['Storage', 'storeInfo', 'store', 'isBeepDeliveryEnabled'], false);

export const selectTestEnv = (state: RootState) => state.getIn(['EnvSetting', 'testEnvName'], 'Default');

export const selectRountTo = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'roundingTo']);

export const selectRoundAllTransactions = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'roundAllTransactions'], false);

export const selectTimezone = (state: RootState) => state.getIn(['Storage', 'storeTimezone']);

export const selectAlreadyMigrateTableLayout = (state: Immutable.Map<string, any>) =>
  state.getIn(['Settings', 'tableLayoutSettings', 'alreadyMigrateTableLayout'], false);

export const selectNeedShowEditTableLayoutToast = (state: Immutable.Map<string, any>) =>
  state.getIn(['Settings', 'tableLayoutSettings', 'needShowEditTableLayoutToast'], false);

export const selectNeedShowMigrateSuccessToast = (state: Immutable.Map<string, any>) =>
  state.getIn(['Settings', 'tableLayoutSettings', 'needShowMigrateSuccessToast'], false);

export const selectNeedShowNewTableLayoutTag = (state: Immutable.Map<string, any>) =>
  state.getIn(['Settings', 'tableLayoutSettings', 'needShowNewTableLayoutTag'], false);
export const selectEnableTableLayout = (state: RootState): boolean => state.getIn(['Settings', 'tableLayoutSettings', 'enableTableLayout'], false);
export const selectTableLayout = (state: RootState) => state.getIn(['Storage', 'tableLayout'], EMPTY_IMMUTABLE_LIST);
export const selectShareQuickLayoutStores = (state: RootState) => state.getIn(['Storage', 'shareQuickLayoutStores'], EMPTY_IMMUTABLE_LIST);

export const selectTableLayoutEnabled = (state: RootState): boolean => state.getIn(['Storage', 'storeInfo', 'store', 'tableLayoutEnabled'], false);

export const selectEnableTakeaway = (state: RootState): boolean => state.getIn(['Storage', 'storeInfo', 'store', 'enableTakeaway']);
export const selectEnableCashback = (state: RootState): boolean => state.getIn(['Storage', 'storeInfo', 'store', 'enableCashback'], false);
export const selectLoyaltyRatio = (state: RootState): number => state.getIn(['Storage', 'storeInfo', 'store', 'defaultLoyaltyRatio'], 0);

export const selectEnableLoyalty = (state: RootState): boolean => state.getIn(['Storage', 'storeInfo', 'store', 'enableLoyalty'], false);

export const selectEnableStoreCredit = (state: RootState): boolean => {
  const enableLoyalty = selectEnableLoyalty(state);
  const enableCashback = selectEnableCashback(state);

  return enableLoyalty && !enableCashback;
};

export const selectEnablePoints = (state: RootState): boolean => state.getIn(['Storage', 'storeInfo', 'store', 'pointsEnabled'], false);

export const selectMembershipEnabled = (state: RootState): boolean => state.getIn(['Storage', 'storeInfo', 'store', 'membershipEnabled'], false);

export const selectEnableSelfPickupAlert = (state: RootState): boolean => state.getIn(['Storage', 'storeInfo', 'store', 'enableSelfPickupAlert']);

export const selectAlwaysPrintReceipt = (state: RootState) => state.getIn(['Settings', 'printerGeneralSettings', 'alwaysPrintReceipt'], false);
export const selectAlwaysPrintReceiptWithBeepQRPayOnline = (state: RootState) =>
  state.getIn(['Settings', 'printerGeneralSettings', 'alwaysPrintReceiptWithBeepQRPayOnline'], false);
export const selectAlwaysPrintReceiptWithBeepQRPayAtCounter = (state: RootState) =>
  state.getIn(['Settings', 'printerGeneralSettings', 'alwaysPrintReceiptWithBeepQRPayAtCounter'], false);
export const selectAlwaysPrintReceiptWithOnlineDelivery = (state: RootState) =>
  state.getIn(['Settings', 'printerGeneralSettings', 'alwaysPrintReceiptWithOnlineDelivery'], false);

export const selectEnablePayLater = (state: RootState): boolean => state.getIn(['Storage', 'storeInfo', 'store', 'enablePayLater'], false);
export const selectEnablePayByCash = (state: RootState): boolean => state.getIn(['Storage', 'storeInfo', 'store', 'enablePayByCash'], false);

export const selectDisablePolling = (state: RootState) => state.getIn(['Settings', 'generalSettings', 'disablePolling'], false);
export const selectAuthToken = (state: RootState): string | undefined => state.getIn(['Storage', 'storeInfo', 'apiToken']);

export const selectAccountExpired = (state: RootState): boolean => state.getIn(['Settings', 'accountSettings', 'expired'], false);
export const selectAccountOffline = (state: RootState): boolean => state.getIn(['Settings', 'accountSettings', 'offline'], false);

export const selectDisablePushNotification = (state: RootState): boolean => state.getIn(['Settings', 'generalSettings', 'disablePushNotification'], false);

export const selectIsReduxLoaded = (state: RootState): boolean => state.getIn(['ReduxPersist', 'isReduxLoaded']);
export const selectIsWebsocketLoaded = (state: RootState): boolean => state.getIn(['Websocket', 'isWebsocketLoaded']);

export const selectShiftOpenStatus = (state: RootState): boolean => state.getIn(['Shift', 'ShiftOpenStatus']);
export const selectDisableMRS = (state: Immutable.Map<string, any>) => state.getIn(['Settings', 'generalSettings', 'disableMRS'], false);

export const selectFreeTrial = (state: RootState) => state.getIn(['Storage', 'freeTrial']);
export const selectBeepBadgeNumber = (state: RootState) => state.getIn(['BadgeNumber', 'beepBadgeNumber'], 0);

export const selectIsBeepPreOrderEnabled = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'enablePreOrder']);
export const selecIsVATRegistered = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'isVATRegistered'], false);
export const selectEnableCustomerShortCut = (state: RootState) => state.getIn(['Settings', 'generalSettings', 'enableCustomerShortCut'], false);
export const selectEnableCustomerQR = (state: RootState) => state.getIn(['Settings', 'generalSettings', 'enableCustomerQR'], false);
export const selectAllowOutOfStock = (state: RootState) => state.getIn(['Settings', 'generalSettings', 'allowOutOfStock'], true);
export const selectAllowOutOfStockUntil = (state: RootState) => state.getIn(['Settings', 'generalSettings', 'allowOutOfStockUntil'], 0);
// use this for out of stock sales flow check
export const selectAllowOutOfStockForSalesFlow = (state: RootState) => {
  const allowOutOfStockGB = selectGBAllowOutOfStock(state);
  const allowOutOfStock = selectAllowOutOfStock(state);
  const allowOutOfStockUntil = selectAllowOutOfStockUntil(state);
  const allowOutOfStockInTime = allowOutOfStockUntil == 0 || allowOutOfStockUntil == -1 || allowOutOfStockUntil > Date.now();
  return (allowOutOfStock && allowOutOfStockInTime) || !allowOutOfStockGB;
};

export const selectEnableOpenOrdersShortCut = (state: RootState) => state.getIn(['Settings', 'generalSettings', 'enableOpenOrdersShortCut'], false);
export const selectEnableOpenOrders = (state: RootState) => state.getIn(['Settings', 'generalSettings', 'enableOpenOrders'], false);
export const selectPauseBeepDelivery = (state: RootState) => state.getIn(['Settings', 'generalSettings', 'pauseBeepDelivery'], null);

export const selectAutoOrderId = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'autoOrderId']);
export const selectAssignTableID = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'assignTableID']);

export const selectServiceChargeTax = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'serviceChargeTax']);
export const selectServiceChargeRate = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'serviceChargeRate']);
export const selectEnableServiceCharge = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'enableServiceCharge'], false);
export const selectBirAccredited = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'birAccredited']);
export const selectCurrency = (state: RootState): string => state.getIn(['Storage', 'storeInfo', 'store', 'currency']);
export const selectViewEditTransaction = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'cashierAccesses', 'viewEditTransaction']);
export const selectIsOnline = (state: RootState): boolean => state.getIn(['Storage', 'storeInfo', 'store', 'isOnline'], false);
export const selectIsCanPrintDynamicQRCode = (state: RootState): boolean => state.getIn(['Storage', 'storeInfo', 'store', 'isCanPrintDynamicQRCode'], false);
export const selectSequentialReceiptNumber = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'sequentialReceiptNumber'], false);
export const selectAutoSyncAfterOpenShift = (state: RootState) => state.getIn(['Settings', 'generalSettings', 'autoSyncAfterOpenShift'], false);
export const selectCloseLastShiftNotification = (state: RootState) => state.getIn(['Settings', 'generalSettings', 'enableCloseLastShiftNotification'], false);
export const selectPrintEmployeeName = (state: RootState) => state.getIn(['Settings', 'printerGeneralSettings', 'printEmployeeName'], false);
export const selectPrintEInvoiceOnReceipt = (state: RootState) => {
  return selectGBEnableEInvoiceAutoPrintOption(state) && state.getIn(['Settings', 'printerGeneralSettings', 'printEInvoiceOnReceipt'], false);
};
export const selectEnableCustomerDisplay = (state: RootState) => state.getIn(['Settings', 'customerDisplaySettings', 'enableCustomerDisplay'], false);
export const selectDefaultTax = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'tax']);
export const selectSubscriptionPlan = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'subscriptionPlan'], 0);

// if industry == 1, then the industry of this store is retail
export const selectIndustry = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'industry']);

export const selectIsRetail = (state: RootState) => {
  const industry = state.getIn(['Storage', 'storeInfo', 'store', 'industry']);
  return industry == BIRStoreType.RetailStore;
};

export const selectIsFAB = (state: RootState) => {
  const industry = state.getIn(['Storage', 'storeInfo', 'store', 'industry']);
  return industry == BIRStoreType.FABStore;
};

export const selectAWSConfig = (state: RootState) => {
  const accessKeyId = state.getIn(['AWS', 'accessKeyId']);
  const accessKeySecret = state.getIn(['AWS', 'accessKeySecret']);
  const sessionToken = state.getIn(['AWS', 'sessionToken']);
  const expiration = state.getIn(['AWS', 'expiration']);
  return { accessKeyId, accessKeySecret, sessionToken, expiration };
};

export const selectSerialNo = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'serialNo'], '');
export const selectMinNo = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'minNo'], '');
export const selectGstIdNo = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'gstIdNo'], '');
export const selectStoreName = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'name'], '');
export const selectStoreCompanyName = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'companyName'], '');
export const selectStoreCity = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'city'], '');
export const selectStoreState = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'state'], '');
export const selectStorePostalCode = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'postalCode'], '');
export const selectStoreEInvoiceReady = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'isEInvoiceReady'], false);
export const selectStoreStreet1 = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'street1'], '');
export const selectStoreStreet2 = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'street2'], '');
export const selectStoreBrn = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'brn'], '');
export const selectStorePhone = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'phone'], '');

// printerJob
export const selectErrorPrinters = (state: RootState): Immutable.Map<string, number> => state.getIn(['Printer', 'errorPrinters'], EMPTY_IMMUTABLE_MAP);
export const selectPrinterErrorCount = createSelector(selectErrorPrinters, errorPrinters => errorPrinters.reduce((acc, count) => acc + count, 0));

export const defaultAssignedStatusImmutable = Immutable.fromJS(defaultAssignedStatus);
export const selectPrinterAssignedStatus = createSelector(
  (state: RootState): Immutable.Map<string, number> => state.getIn(['Printer', 'assignedStatus'], defaultAssignedStatusImmutable),
  status => {
    return status.toJS();
  }
);

// pauseMode
export const selectStorePauseInfo = createSelector(
  (state: RootState) => state.getIn(['PauseMode', 'storeInfo'], EMPTY_IMMUTABLE_MAP),
  info => {
    return info.toJS();
  }
);

export const selectKitchenDocketVariantIsMultipleLine = (state: RootState) =>
  state.getIn(['Settings', 'printerGeneralSettings', 'kitchenDocketVariantIsMultipleLine'], false);

export const selectReceiptFontSize = (state: RootState) => state.getIn(['Settings', 'printerGeneralSettings', 'receiptFontSize'], ReceiptFontSize.MEDIUM);
export const selectKitchenDocketFontSize = (state: RootState) =>
  state.getIn(['Settings', 'printerGeneralSettings', 'kitchenDocketFontSize'], ReceiptFontSize.MEDIUM);

export const selectDiscountAccessType = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'cashierAccesses', 'discount'], '1');

export const selectOperationHours = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'operationHours'], 0);
export const selectShouldLoadOnlineOpenOrders = (state: RootState): boolean => state.getIn(['Settings', 'generalSettings', 'shouldLoadOnlineOpenOrders'], true);

export const selectInMergeOpenOrder = (state: RootState): boolean => state.getIn(['EditingOpenOrder', 'inMerge'], false);
export const selectInSplitOpenOrder = (state: RootState): boolean => state.getIn(['EditingOpenOrder', 'inSplit'], false);
export const selectInMoveOpenOrder = (state: RootState): boolean => state.getIn(['EditingOpenOrder', 'inMove'], false);
export const selectOnChangingTable = (state: RootState): boolean => state.getIn(['EditingOpenOrder', 'onChangingTable'], false);
export const selectInSplitOpenRemainingAmount = (state: RootState) =>
  get(state.getIn(['EditingOpenOrder', 'splitMasterOrder'], EMPTY_IMMUTABLE_MAP).toJS(), ['display', 'total'], 0);

export const selectSplitTargetOrder = (state: RootState): TransactionTypeWithDisplay =>
  state.getIn(['EditingOpenOrder', 'splitMasterOrder'], EMPTY_IMMUTABLE_MAP).toJS();

export const selectSplitedOrder = (state: RootState): TransactionTypeWithDisplay =>
  state.getIn(['EditingOpenOrder', 'splitedOrder'], EMPTY_IMMUTABLE_MAP).toJS();
export const selectMergedBranchOrderIds = (state: RootState) => state.getIn(['EditingOpenOrder', 'mergedBranchOrderIds'], EMPTY_IMMUTABLE_LIST);
// #region MRS
export const selectIsMaster = (state: RootState) => state.getIn(['MRS', 'isMaster'], false);

export const selectMRS = (state: RootState) => state.getIn(['MRS'], EMPTY_IMMUTABLE_MAP);

export const selectSlavePid = (state: RootState) => state.getIn(['MRS', 'pid'], INIT_PID);
export const selectClientCheckOrders = (state: RootState) => {
  return state.getIn(['MRS', 'clientCheckOrders'], EMPTY_IMMUTABLE_MAP).toJS();
};

export const selectSnapshotPid = (state: RootState) => state.getIn(['MRS', 'snapshotPid'], INIT_PID);
export const selectBackupPid = (state: RootState) => state.getIn(['MRS', 'backupPid'], INIT_PID);
export const selectSnapshotVersion = (state: RootState) => state.getIn(['MRS', 'snapshotVersion'], INIT_SNAPSHOT_VERSION);

export const selectServerRunning = (state: RootState) => state.getIn(['MRS', 'serverRunning'], false);

export const selectClientConnected = (state: RootState) => state.getIn(['MRS', 'clientConnected'], false);

export const selectMasterState = (state: RootState) => state.getIn(['MRS', 'masterState'], MasterState.OFFLINE);

export const selectServerIp = (state: RootState) => state.getIn(['MRS', 'serverInfo', 'ip'], '');

export const selectClientIp = (state: RootState) => state.getIn(['MRS', 'clientInfo', 'ip'], '');
export const selectMRSOutDatedClient = (state: RootState) => state.getIn(['MRS', 'outDatedClient'], false);

export const selectMRSClients = (state: RootState): SingleClient[] => {
  const clients = state.getIn(['MRS', 'clients'], EMPTY_IMMUTABLE_LIST);
  return clients ? clients.toJS() : [];
};

export const selectSwitchList = (state: RootState): Message[] => {
  const switchList = state.getIn(['MRS', 'switchMasterList'], EMPTY_IMMUTABLE_LIST);
  return switchList ? switchList.toJS() : [];
};

export const selectIsEnabledMRS = (state: RootState) => state.getIn(['MRS', 'isEnabledMRS'], false);

export const selectIsBOEnabledMRS = (state: RootState) => {
  const enableMultipleIpads = state.getIn(['Storage', 'storeInfo', 'store', 'enableMultipleIpads'], false);
  const newMRSEnabled = state.getIn(['Storage', 'storeInfo', 'store', 'newMRSEnabled'], false);
  return enableMultipleIpads && newMRSEnabled;
};

export const selectIsClient = createSelector(selectIsEnabledMRS, selectIsMaster, (isEnabledMRS, isMaster) => {
  return isEnabledMRS && !isMaster;
});

export const selectMRSRole = createSelector(selectIsEnabledMRS, selectIsMaster, (isEnabledMRS, isMaster) => {
  if (!isEnabledMRS) {
    return MRSRole.None;
  }
  return isMaster ? MRSRole.Server : MRSRole.Client;
});

// GBFeature.CM_4068
// enable: turn on/off this feature
// minSupportPid: support merchants that pid >= minSupportPid, the minimum value is 1
// minCommitHeight: min height between snapshots
// maxCommitHeight: max height between snapshots
export const selectMrsSnapshotEnabled = (state: RootState) => state.getIn(['GrowthBookFeatures', GBFeature.CM_4068, 'enable'], true);
export const selectMrsMinSupportPidOfSnapshot = (state: RootState) => state.getIn(['GrowthBookFeatures', GBFeature.CM_4068, 'minSupportPid'], 1);
export const selectMrsMaxCommitHeight = (state: RootState) => state.getIn(['GrowthBookFeatures', GBFeature.CM_4068, 'maxCommitHeight'], 5000);
export const selectMrsMinCommitHeight = (state: RootState) => state.getIn(['GrowthBookFeatures', GBFeature.CM_4068, 'minCommitHeight'], 3000);
export const selectMrsDeleteSizeOfLogs = (state: RootState) => state.getIn(['GrowthBookFeatures', GBFeature.CM_4068, 'deleteSize'], 5000);
export const selectMrsDeleteLoop = (state: RootState) => state.getIn(['GrowthBookFeatures', GBFeature.CM_4068, 'deleteLoop'], true);
export const selectMrsDeleteInterval = (state: RootState) => state.getIn(['GrowthBookFeatures', GBFeature.CM_4068, 'deleteInterval'], 5); // seconds

export const selectMRSClientCheckOrdersEnable = (state: RootState) => state.getIn(['GrowthBookFeatures', GBFeature.CM_8521, 'enable'], false);
export const selectMRSClientCheckOrdersInterval = (state: RootState) => state.getIn(['GrowthBookFeatures', GBFeature.CM_8521, 'interval'], 60); // minutes

export const selectResetFlag = (state: RootState) => state.getIn(['MRS', 'resetFlag'], false);
export const selectNeedCleanLogs = (state: RootState) => state.getIn(['MRS', 'needCleanLogs'], false);

export const selectMRSLearn = (state: RootState) => state.getIn(['MRS', 'learn'], EMPTY_IMMUTABLE_MAP);

export const selectBirInfo = (state: RootState) => state.getIn(['TransactionSession', 'birInfo'], EMPTY_IMMUTABLE_MAP);

export const selectLastZReadingCloseTime = (state: Immutable.Map<string, any>) => state.getIn(['Storage', 'syncInfo', 'lastZReadingCloseTime']);

export const selectIsMallIntegrationEnabled = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'isMallIntegrationEnabled'], false);
export const selectMallIntegrationChannel = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'mallIntegrationChannel'], ''); // 'ROBINSON_MALL', 'AYALA_MALL'
// #endregion

export const selectReceiptType = createSelector(selectStoreInfo, (storeInfo: Immutable.Map<string, any>) =>
  storeInfo.getIn(['store', 'receiptType'], 'Thermal')
);

export const selectLoadingVisible = (state: RootState) => state.getIn(['CommonUI', 'loadingMaskIsVisible'], false);
export const selectLoadingInteractions = (state: RootState) => state.getIn<boolean>(['CommonUI', 'disableInteractions'], true);

export const selectEnableFaceCapture = (state: RootState) =>
  state.getIn<boolean>(['Storage', 'storeInfo', 'store', 'cashierAccesses', 'enableFaceCapture'], false);

export const selectCameraPermissionStatus = (state: RootState) => state.getIn<boolean>(['Storage', 'faceCapture', 'cameraPermissionStatus'], false);

export const selectHaveUsedFaceCapture = (state: RootState) => state.getIn<boolean>(['Storage', 'faceCapture', 'haveUsedFaceCapture'], false);

export const selectEnablePax = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'enablePax'], false);
export const selectAutoSignOutCount = (state: RootState) => state.getIn<string>(['Settings', 'generalSettings', 'autoSignOutCount'], '');

export const selectSupportPhoneNumberMessagesInput = (state: RootState) => {
  try {
    const store = selectStore(state);
    const tags = store.get('businessTags', EMPTY_IMMUTABLE_LIST.toJS()) as any[];
    const msg = JSON.parse(JSON.stringify(state.getIn<SupportPhoneNumber>(['GrowthBookFeatures', GBFeature.CM_3509], disabledSupportPhoneNumberMessages)));
    // const msg = defaultSupportPhoneNumberMessages
    msg.visible = tags && tags.includes('TOP_MRR');
    return msg;
  } catch (e) {
    return disabledSupportPhoneNumberMessages;
  }
};

export const selectSentryConfig = createSelector(
  (state: Immutable.Map<string, any>) => state.getIn(['GrowthBookFeatures', GBFeature.CM_7327], EMPTY_IMMUTABLE_MAP),
  sentryConfig => sentryConfig.toJS()
);
export const selectSupportPhoneNumberMessages = createDeepEqualSelector(selectSupportPhoneNumberMessagesInput, message => message);

export const selectEnableFaceSnapshotOnAndroid = (state: RootState) => {
  return state.getIn(['GrowthBookFeatures', GBFeature.CM_4760], false);
};

export const selectEnableAutoAssignDefaultNetwork = (state: RootState) => {
  return state.getIn(['GrowthBookFeatures', GBFeature.CM_5028], false);
};

// KDS and NCS Subscription Status
export const selectIsKDSPurchased = (state: RootState) => {
  const gbStatus = state.getIn(['GrowthBookFeatures', GBFeature.CM_3591], false);
  const storeStatus = state.getIn(['Storage', 'storeInfo', 'store', 'kdsPurchased'], false);
  return gbStatus || storeStatus;
};

export const selectIsNCSPurchased = (state: RootState) => {
  const gbStatus = state.getIn(['GrowthBookFeatures', GBFeature.CM_4994], false);
  const storeStatus = state.getIn(['Storage', 'storeInfo', 'store', 'ncsPurchased'], false);
  return gbStatus || storeStatus;
};

export const selectIsKDSPurchasedANDAssigned = (state: RootState) => {
  const gbStatus = state.getIn(['GrowthBookFeatures', GBFeature.CM_3591], false);
  const storeStatus = state.getIn(['Storage', 'storeInfo', 'store', 'kds'], false);
  return gbStatus || storeStatus;
};

export const selectIsNCSPurchasedANDAssigned = (state: RootState) => {
  const gbStatus = state.getIn(['GrowthBookFeatures', GBFeature.CM_4994], false);
  const storeStatus = state.getIn(['Storage', 'storeInfo', 'store', 'ncs'], false);
  return gbStatus || storeStatus;
};

export const selectIsKDSPurchasedButNotAssigned = createSelector(
  selectIsKDSPurchased,
  selectIsKDSPurchasedANDAssigned,
  (isPurchased, isPurchasedAndAssigned) => isPurchased && !isPurchasedAndAssigned
);

export const selectIsNCSPurchasedButNotAssigned = createSelector(
  selectIsNCSPurchased,
  selectIsNCSPurchasedANDAssigned,
  (isPurchased, isPurchasedAndAssigned) => isPurchased && !isPurchasedAndAssigned
);

// #region ncs
export const selectNcs = createSelector(
  (state: RootState) => state.getIn(['NCS', 'settings'], Immutable.fromJS([])),
  (settings: Immutable.List<NcsSettingType>): NcsSettingType[] => {
    const ncsArr: NcsSettingType[] = settings.toJS();
    return sortedByWebsocketState(ncsArr) as NcsSettingType[];
  }
);
export const selectPairedNcs = createSelector(selectNcs, (settings): NcsSettingType[] => {
  return settings.filter(ncs => ncs.isPaired);
});
export const selectIsNcsPaired = createSelector(selectIsNCSPurchasedANDAssigned, selectPairedNcs, (isNcsEnabled, pairedNcs) => {
  return isNcsEnabled && !isEmpty(pairedNcs);
});

export const selectNcsById = (id: string) =>
  createSelector(selectNcs, settings => {
    return settings.find(it => it.id === id);
  });

export const selectNcsBySocketId = (socketId: string) =>
  createSelector(selectNcs, settings => {
    return settings.find(it => it.socketId === socketId);
  });

export const selectNcsIdBySocketId = (socketId: string) =>
  createSelector(selectNcs, settings => {
    const ncs = settings.find(it => it.socketId === socketId);
    return ncs && ncs.id;
  });

export const selectSocketIdByNcsId = (ncsId: string) =>
  createSelector(selectNcs, settings => {
    const ncs = settings.find(it => it.id === ncsId);
    return ncs && ncs.socketId;
  });

export const selectConnectedNcs = createSelector(selectNcs, (settings): NcsSettingType[] => {
  return settings.filter(ncs => ncs.status === 'Connected' && ncs.socketId);
});

export const selectAvailableNcs = createSelector(selectNcs, (settings): NcsSettingType[] => {
  return settings.filter(ncs => ncs.status === 'Connected' && ncs.socketId && ncs.isPaired === true);
});

export const selectUnpairedNcs = createSelector(selectNcs, (settings): NcsSettingType[] => {
  return settings.filter(ncs => !ncs.isPaired);
});
// #endregion
export const selectEnableViewLocalSettingsOnIST = (state: RootState) => {
  return state.getIn(['GrowthBookFeatures', GBFeature.CM_5443], false);
};

// #region kds
export const selectIsDynamicBeepQREnabled = (state: RootState) => state.getIn(['GrowthBookFeatures', GBFeature.CM_4170], false);

export const sortedByWebsocketState = (arr: { status: WebsocketStateEnum }[]) => {
  return arr.sort((a, b) => {
    if (a.status === WebsocketStateEnum.Connected && b.status !== WebsocketStateEnum.Connected) {
      return -1;
    }
    if (b.status === WebsocketStateEnum.Connected && a.status !== WebsocketStateEnum.Connected) {
      return 1;
    }
    return 0;
  });
};
export const selectKds = createSelector(
  (state: RootState) => state.getIn(['KDS', 'settings'], Immutable.fromJS([])),
  (settings: Immutable.List<KdsSettingType>): KdsSettingType[] => {
    const kdsArr: KdsSettingType[] = settings.toJS();
    return sortedByWebsocketState(kdsArr) as KdsSettingType[];
  }
);
export const selectPairedKds = createSelector(selectKds, (settings): KdsSettingType[] => {
  return settings.filter(kds => kds.isPaired);
});
export const selectIsKdsPaired = createSelector(selectIsKDSPurchasedANDAssigned, selectPairedKds, (isKdsEnabled, pairedKds) => {
  return isKdsEnabled && !isEmpty(pairedKds);
});

export const selectKdsById = (id: string) =>
  createSelector(selectKds, settings => {
    return settings.find(it => it.id === id);
  });

export const selectKdsBySocketId = (socketId: string) =>
  createSelector(selectKds, settings => {
    return settings.find(it => it.socketId === socketId);
  });

export const selectKdsIdBySocketId = (socketId: string) =>
  createSelector(selectKds, settings => {
    const kds = settings.find(it => it.socketId === socketId);
    return kds && kds.id;
  });

export const selectSocketIdByKdsId = (kdsId: string) =>
  createSelector(selectKds, settings => {
    const kds = settings.find(it => it.id === kdsId);
    return kds && kds.socketId;
  });

export const selectConnectedKds = createSelector(selectKds, (settings): KdsSettingType[] => {
  return settings.filter(kds => kds.status === 'Connected' && kds.socketId);
});

export const selectAvailableKds = createSelector(selectKds, (settings): KdsSettingType[] => {
  return settings.filter(kds => kds.status === 'Connected' && kds.socketId && kds.isPaired === true);
});

export const selectUnpairedKds = createSelector(selectKds, (settings): KdsSettingType[] => {
  return settings.filter(kds => !kds.isPaired);
});

export const selectCookingStatusType = (state: RootState) => state.getIn(['KDS', 'cookingStatusType'], CookingStatusType.DOUBLE);

// #endregion

// #region cfd
export const selectIsNewCfdFeatureEnabled = (state: RootState) => state.getIn(['GrowthBookFeatures', GBFeature.CM_4611], false);

export const selectIsCfdEnabled = (state: RootState) => true;

export const selectCfd = createSelector(
  (state: RootState) => state.getIn(['CfdApp', 'settings'], Immutable.fromJS([])),
  (settings: Immutable.List<CfdSettingType>): CfdSettingType[] => {
    const cfdArr: CfdSettingType[] = settings.toJS();
    return sortedByWebsocketState(cfdArr) as CfdSettingType[];
  }
);
export const selectPairedCfd = createSelector(selectCfd, (settings): CfdSettingType[] => {
  return settings.filter(kds => kds.isPaired);
});
export const selectIsCfdPaired = createSelector(selectIsCfdEnabled, selectPairedCfd, (isKdsEnabled, pairedKds) => {
  return isKdsEnabled && !isEmpty(pairedKds);
});

export const selectCfdById = (id: string) =>
  createSelector(selectCfd, settings => {
    return settings.find(it => it.id === id);
  });

export const selectCfdBySocketId = (socketId: string) =>
  createSelector(selectCfd, settings => {
    return settings.find(it => it.socketId === socketId);
  });

export const selectCfdIdBySocketId = (socketId: string) =>
  createSelector(selectCfd, settings => {
    const kds = settings.find(it => it.socketId === socketId);
    return kds && kds.id;
  });

export const selectSocketIdByCfdId = (kdsId: string) =>
  createSelector(selectCfd, settings => {
    const kds = settings.find(it => it.id === kdsId);
    return kds && kds.socketId;
  });

export const selectConnectedCfd = createSelector(selectCfd, (settings): CfdSettingType[] => {
  return settings.filter(cfd => cfd.status === 'Connected' && cfd.socketId);
});

export const selectAvailableCfd = createSelector(selectCfd, (settings): CfdSettingType[] => {
  return settings.filter(cfd => cfd.status === 'Connected' && cfd.socketId && cfd.isPaired === true);
});

export const selectUnpairedCfd = createSelector(selectCfd, (settings): CfdSettingType[] => {
  return settings.filter(cfd => !cfd.isPaired);
});

// #endregion
// export const selectDealClubFeature = (state: RootState) => state.getIn(['GrowthBookFeatures', GBFeature.CM_3517], EMPTY_IMMUTABLE_MAP).toJS();

export const selectDealClubFeature = createDeepEqualSelector(
  (state: RootState) => state.getIn(['GrowthBookFeatures', GBFeature.CM_3517], EMPTY_IMMUTABLE_MAP).toJS(),
  value => value
);

export const selectDevPanelData = (state: RootState) => state.getIn(['DevPanel'], EMPTY_IMMUTABLE_MAP).toJS();

// export const selectUniquePromos = (state: RootState) => {
//   const uniquePromos = state.getIn(['TransactionSession', 'uniquePromos']);
//   return get(uniquePromos, ['data'], []);
// };

export const selectUniquePromos = createDeepEqualSelector(
  (state: RootState) => {
    const uniquePromos = state.getIn(['TransactionSession', 'uniquePromos']);
    return get(uniquePromos, ['data'], []);
  },
  uniquePromos => uniquePromos
);

export const selectSelectedUniquePromo = createDeepEqualSelector(
  (state: RootState) => {
    const uniquePromos = state.getIn(['TransactionSession', 'uniquePromos']);
    return get(uniquePromos, ['selectedUniquePromo'], null);
  },
  selectedUniquePromo => selectedUniquePromo
);

export const selectUniquePromosErrorInput = (state: RootState) => {
  const uniquePromos = state.getIn(['TransactionSession', 'uniquePromos']);
  return get(uniquePromos, 'error', []);
};

export const selectUniquePromosError = createDeepEqualSelector(selectUniquePromosErrorInput, promoError => promoError);

export const selectCfdConfigurations = (state: RootState) => state.getIn(['CFD'], EMPTY_IMMUTABLE_MAP).toJS() as CfdState;

export const selectServiceChargeExclusiveSetting = (state: RootState) =>
  state.getIn(['Storage', 'storeInfo', 'store', 'excludeServiceChargeProductCondition'], EMPTY_IMMUTABLE_MAP).toJS();

export const selectExpirationSeconds = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'subscriptionExpirationSeconds'], Number.MAX_VALUE);

export const selectIsAutoRenewal = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'isAutoRenewal'], false);

export const selectLocalCountryMap = createDeepEqualSelector(
  (state: RootState) => state.getIn(['localCountryMap'], EMPTY_IMMUTABLE_MAP).toJS() as LocalCountryMapType,
  countryMap => countryMap
);

// #region PrintRefactoring
export const selectAutoSetDefaultNetworkAfterSuccessfulPrinting = (state: RootState) => state.getIn(['Settings', 'autoSetDefaultNetwork'], false);

// #endregion
export const selectGhl = (state: RootState) => state.getIn(['Ghl'], {});

export const selectGhlStatus = (state: RootState) => state.getIn(['Ghl', 'status'], GhlStatus.Idle);

export const selectGhlResultType = (state: RootState) => state.getIn(['Ghl', 'resultType'], GhlApiResultType.Undefined);

export const selectGhlResponse = (state: RootState) => state.getIn(['Ghl', 'ghlResponse'], null)?.toJS() as GhlBaseResponse;

// export const selectEnableXenditPayment = (state: RootState) => state.getIn(['GrowthBookFeatures', GBFeature.CM_6364], false);

export const selectEInvoiceEnabled = (state: RootState) => state.getIn(['GrowthBookFeatures', GBFeature.CM_5949], false) && selectStoreEInvoiceReady(state);
export const selectGBProductLayoutRevampEnabled = (state: RootState) => state.getIn(['GrowthBookFeatures', GBFeature.CM_6676], false);

export const selectAccountCreatedTime = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'createdTime'], '');

const isAccountCreationDateValid = (createdTime: string) => {
  if (!createdTime) {
    return false;
  }
  const createdDate = new Date(createdTime);
  return createdDate >= new Date('2024-08-20T16:00:00.000Z');
};

export const selectNewCFDAllowed = createSelector([selectAccountCreatedTime, selectIsNewCfdFeatureEnabled], (createdTime, isNewCfdFeatureEnabled) => {
  const isCreationDateValid = isAccountCreationDateValid(createdTime);
  return isNewCfdFeatureEnabled || isCreationDateValid;
});

export const selectGBCfdBeepMembershipCashback = (state: RootState) => state.getIn(['GrowthBookFeatures', GBFeature.CM_6669], false);
export const selectGBAllowOutOfStock = (state: RootState) => true;

export const selectStoreMembershipTiers = (state: RootState) => state.getIn(['Storage', 'storeInfo', 'store', 'membershipTiers'], EMPTY_IMMUTABLE_LIST).toJS();
export const selectStoreMembershipTiersLevel1 = (state: RootState) =>
  state
    .getIn(['Storage', 'storeInfo', 'store', 'membershipTiers'], EMPTY_IMMUTABLE_LIST)
    .toJS()
    .find(value => value.level === 2);

export const selectStoreMembershipTiersLevel = (level: number) =>
  createSelector(selectStoreMembershipTiers, tiers => {
    return tiers.find(value => value.level === level);
  });

export const selectIsAppReadyForTrackingNetInfo = (state: RootState) => {
  const jsObj = state.get('CustomAppState', {}) as any;
  return jsObj.appReadyForTrackingNetInfo;
};

export const selectUseAsyncWebViewPrintingFlow = (state: RootState) => state.getIn(['GrowthBookFeatures', GBFeature.CM_9076], false);

export const selectIntersectLink = (state: RootState): string => state.getIn(['GrowthBookFeatures', GBFeature.CM_7515]);

export const selectTrialOrderLimit = (state: RootState) => state.getIn(['GrowthBookFeatures', GBFeature.CB_9607], false);
export const selectGBEnableStorageStatistics = (state: RootState) => {
  return state.getIn(['GrowthBookFeatures', GBFeature.CM_7829], false);
};

export const selectDeviceFirmwareVersion = (state: RootState) => state.getIn(['Storage', 'device', 'firmwareVersion'], '');
export const selectDeviceSerialNumber = (state: RootState) => state.getIn(['Storage', 'device', 'sn'], '');
export const selectPlatform = (state: RootState) => state.getIn(['Storage', 'device', 'platform'], '');

export const defaultTransactionDBAutoCleanConfigImmutable = Immutable.fromJS(defaultTransactionDBAutoCleanConfig);
export const selectGBEnableTransactionDBAutoClean = (state: RootState) => {
  return state.getIn(['GrowthBookFeatures', GBFeature.CM_8239], defaultTransactionDBAutoCleanConfigImmutable).toJS();
};

export const selectGBTrackApiResponseTime = (state: RootState): TrackApiResponseTimeConfigType => {
  const config = state.getIn(['GrowthBookFeatures', GBFeature.CM_8378], EMPTY_IMMUTABLE_MAP);
  return config.toJS();
};

export const defaultNewProductSyncFlowConfigImmutable = Immutable.fromJS(defaultNewProductSyncFlowConfig);
export const selectNewProductSyncFlow = (state: RootState): NewProductSyncFlowType => {
  return state.getIn(['GrowthBookFeatures', GBFeature.CM_8309], defaultNewProductSyncFlowConfigImmutable).toJS();
};

export const selectGBEnableLargeProductQuantitySync = (state: RootState) => {
  return selectNewProductSyncFlow(state).enabled;
};

export const selectGBEnableEInvoicePhase2 = (state: RootState) => {
  return true;
};

export const selectGBEnableEInvoiceBeepWebstore = (state: RootState) => {
  return state.getIn(['GrowthBookFeatures', GBFeature.CM_8440], false);
};

export const selectGBEnableEInvoiceAutoPrintOption = (state: RootState) => {
  return state.getIn(['GrowthBookFeatures', GBFeature.CM_8420], false);
};

export const selectGBEnableIminScannerFix = (state: RootState) => {
  return state.getIn(['GrowthBookFeatures', GBFeature.CM_6713], ScannerFix.Default);
};

export const defaultNetworkQualityMonitoringImmutable = Immutable.fromJS(defaultNetworkQualityMonitoring);

export const selectNetworkQualityMonitoring = (state: RootState): NetworkQualityMonitoring => {
  return state.getIn(['GrowthBookFeatures', GBFeature.CM_8284], defaultNetworkQualityMonitoringImmutable).toJS();
};

export const selectGBEnableNewScannerFlow = (state: RootState) => {
  return state.getIn(['GrowthBookFeatures', GBFeature.CM_8683], false);
};

export const selectGBEnableTrackDownloadThumbnail = (state: RootState) => state.getIn(['GrowthBookFeatures', GBFeature.CM_8681], false);

export const selectGBEnableVoidReceipt = (state: RootState) => state.getIn(['GrowthBookFeatures', GBFeature.CM_8310], false);

export const defaultStorageAutoCleanConfigImmutable = Immutable.fromJS(defaultStorageAutoCleanConfig);
export const selectEnableStorageAutoClean = (state: RootState) => {
  return state.getIn(['GrowthBookFeatures', GBFeature.CM_8423], defaultStorageAutoCleanConfigImmutable).toJS();
};

export const selectPrintTrackEnabled = (state: RootState) => state.getIn(['GrowthBookFeatures', GBFeature.CM_8910, 'enable'], false);
export const selectPrintTrackMinContentHeight = (state: RootState) => state.getIn(['GrowthBookFeatures', GBFeature.CM_8910, 'minContentHeight'], 0);
export const selectPrintTrackSlope = (state: RootState) => state.getIn(['GrowthBookFeatures', GBFeature.CM_8910, 'slope'], 0.91);
export const selectPrintTrackThreshold = (state: RootState) => state.getIn(['GrowthBookFeatures', GBFeature.CM_8910, 'threshold'], 10);
export const selectPrintDoUpload = (state: RootState) => state.getIn(['GrowthBookFeatures', GBFeature.CM_8910, 'doUpload'], false);
export const selectPrintUploadMemory = (state: RootState) => state.getIn(['GrowthBookFeatures', GBFeature.CM_8910, 'uploadMemory'], false);

export const defaultPushyConfigImmutable = Immutable.fromJS(defaultPushyConfig);
export const selectPushyConfig = (state: RootState): PushyConfig => {
  return state.getIn(['GrowthBookFeatures', GBFeature.CM_8927], defaultPushyConfigImmutable).toJS();
};
export const selectEnableUnitPriceRounding = (state: RootState) => {
  return state.getIn(['GrowthBookFeatures', GBFeature.CM_9039], false);
};
export const selectEnableSalespersonAssignment = (state: RootState) => {
  return state.getIn(['GrowthBookFeatures', GBFeature.CM_9366], false);
};

export const defaultNetworkReachabilityConfigImmutable = Immutable.fromJS(defaultNetworkReachabilityConfig);
export const selectNetworkReachability = (state: RootState) => {
  return state.getIn(['GrowthBookFeatures', GBFeature.CM_9659], defaultNetworkReachabilityConfigImmutable).toJS();
};

export const selectLocalLoggingConfig = (state: RootState) => {
  const value = state.getIn(['GrowthBookFeatures', GBFeature.CM_9450], EMPTY_IMMUTABLE_MAP);
  if (value) {
    return value.toJS();
  }
  return DefaultLocalLoggingConfig;
};

export const selectLocalLoggingMailLocalLogPin = (state: RootState) => {
  return state.getIn(['GrowthBookFeatures', GBFeature.CM_9450, 'mailPin'], '');
};
export const selectLocalLoggingMailAddress = (state: RootState) => {
  return state.getIn(['GrowthBookFeatures', GBFeature.CM_9450, 'mailAddress'], '');
};

const defaultSettingTagConfigImmutable = Immutable.fromJS(defaultSettingTagConfig);
export const selectGBSettingTag = (state: RootState) => {
  const value: any = state.getIn(['GrowthBookFeatures', GBFeature.CM_9538]);
  try {
    if (!value || value.isEmpty()) {
      return defaultSettingTagConfigImmutable;
    }
    return value;
  } catch (error) {
    console.error(error);
    return defaultSettingTagConfigImmutable;
  }
};

const defaultKdsLandingPageUrlsImmutable = Immutable.fromJS(defaultKdsLandingPageUrls);
export const selectKdsLandingPageUrls = (state: RootState) => state.getIn(['GrowthBookFeatures', GBFeature.CM_9551], defaultKdsLandingPageUrlsImmutable);

export const selectNotificationToken = (state: RootState) => state.getIn(['Settings', 'notificationToken'], '');

export const selectInsufficientStorageWarning = (state: RootState) =>
  state.getIn(['GrowthBookFeatures', GBFeature.CM_9506], Immutable.fromJS(defaultInsufficientStorageWarning)).toJS();

export const selectEnableAiAssistant = (state: RootState) => {
  return state.getIn(['GrowthBookFeatures', GBFeature.CM_9966], false);
};

export const selectMakeStoreCreditAsPayment = (state: RootState) => {
  return state.getIn(['GrowthBookFeatures', GBFeature.CM_9952], false);
};

export const selectCurrentEmployee = createDeepEqualSelector(selectEmployeeId, employeeId => {
  if (!employeeId) return null;
  return DAL.getEmployeeById(employeeId);
});

export const selectViewCustomerInfo = (state: RootState) => {
  const currentEmployee = selectCurrentEmployee(state);
  if (currentEmployee && currentEmployee.isManager) {
    return true;
  }

  return state.getIn(['Storage', 'storeInfo', 'store', 'cashierAccesses', 'viewCustomerInfo'], true);
};

export const selectAutoSearchEnable = (state: RootState) => state.getIn(['GrowthBookFeatures', GBFeature.CM_5779, 'enable'], false);
export const selectAutoSearchInterval = (state: RootState) => state.getIn(['GrowthBookFeatures', GBFeature.CM_5779, 'interval'], 5); // minutes
export const selectAutoReprintEnable = (state: RootState) => state.getIn(['GrowthBookFeatures', GBFeature.CM_5779, 'doPrint'], false);
