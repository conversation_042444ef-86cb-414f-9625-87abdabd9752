import { select } from 'redux-saga/effects';
import { selectMakeStoreCreditAsPayment, selectRegisterObjectId, selectStore } from '../../selector';
import { ShiftReport } from './shiftReport';
import { logShiftEvent } from '../../../utils/logComponent/buz/shift';
import { LoggingLevel, ShiftReportAction } from '../../../utils/logComponent';
import * as JSONUtils from '../../../utils/json';

export function* generateShiftReportSaga(shift) {
  let shiftReport;
  let reason = '';
  if (shift === undefined || shift === null) {
    shiftReport = null;
    reason = 'Shift is null';
  } else {
    const immutableStore = yield select(selectStore);
    const registerObjectId = yield select(selectRegisterObjectId);
    const makeStoreCreditAsPayment = yield select(selectMakeStoreCreditAsPayment);
    shiftReport = new ShiftReport(immutableStore, shift, registerObjectId, makeStoreCreditAsPayment);
    if (!shiftReport) {
      reason = 'shiftReport generated failed';
    }
  }
  if (!shiftReport) {
    logShiftEvent({
      event: ShiftReportAction.GenerateShiftReport,
      level: LoggingLevel.Error,
      reason: reason,
      privateDataPayload: shift ? { shift: JSONUtils.stringify(shift), shiftId: shift.shiftId } : null,
    });
  }

  return shiftReport;
}
