import { filter, forEach, get, includes, unionBy } from 'lodash';

import PaymentOptions, { DefaultPaymentOptionType } from '../../../config/paymentOption';
import { TransactionFlowType } from '../../../constants';
import DAL, { SalesGroup } from '../../../dal';
import { ShiftHelper } from '../../../dal/helper';
import { localeNumber } from '../../../utils';

import { getTaxItemWithStore } from '../../transaction/common';
import { isEmpty } from '../../../utils/validator';

export const getPayinTotal = function (shift) {
  let value = 0;
  if (shift.payins) {
    forEach(shift.payins, payInItem => {
      value += payInItem.amount;
    });
  }
  return value;
};

export const getPayoutTotal = function (shift) {
  let value = 0;
  if (shift.payouts) {
    forEach(shift.payouts, payOutItem => {
      value += payOutItem.amount;
    });
  }
  return value;
};

/*
{
    "Sale": { "taxId1" : { "taxName" : "name1", "taxRate": x1, "amount": x2 }, "taxId2" : {} ... }
    "Return": { "taxId1" : { "taxName" : "name2", "taxRate": x3, "amount": x4 }, "taxId2" : {} ... }
}
 */

export type SaleSummary = {
  grossSales?: string;
  grossReturn?: string;
  discount?: string;
  loyaltyDiscount?: string;
  netSales?: string;
  deposit?: string;
  rounded?: string;
  totalTax?: string;
  total?: string;
  serviceCharge?: string;
};

export type CashDrawerSummary = {
  openingAmount?: string;
  totalCashSales?: string;
  registerCashSales?: string;
  beepQrCashCashSales?: string;
  cashDeposit?: string;
  cashReturn?: string;
  cashRounded?: string;
  payinoutSum?: string;
  actualDrawer?: string;
  expectedDrawer?: string;
  overShort?: string;
  payListEnable?: boolean;
};

export type PaymentsItem = {
  paymentName?: string;
  paymentAmount?: string;
  _id?: string;
  paymentId?: number | string;
};

export type PaymentsSummary = {
  paymentsList?: PaymentsItem[];
  total?: string;
};

export type TopOverviewFields = {
  netSales?: string;
  count?: string;
  avgAmount?: string;
  timeDate?: Date;
  employeeId?: string;
};

export type RegisterTransactionItemType = {
  transactionId?: string;
  transactionType?: string;
  isCancelled?: boolean;
};

export type beepTransactionItemType = {
  receiptNumber?: string;
  transactionType?: string;
  isCancelled?: boolean;
};

export type TaxSummary = {
  [key: string]: {
    [key: string]: {
      taxName: string;
      taxRate: number;
      amount: number;
    };
  };
};

export type ServiceChargeGroup = {
  [key: string]: { amount: number; count: number };
};

export type DiscountGroup = {
  [key: string]: { amount: number; count: number };
};

export class ShiftReport {
  private shiftId: string;
  private registerObjectId: string;
  private openTime: Date;
  private openBy?: string;
  private openingAmount?: number;
  private closeTime?: Date;
  private closeBy?: string;
  private closingAmount?: number;
  private expectedDrawer?: number;
  private transactionCount?: number = 0;
  private taxSummaryAggregatedByTransactionType: any = { Sale: {}, Return: {} };
  private sales: SalesGroup = {};
  private actualSales: SalesGroup = {};
  private deposits: SalesGroup = {};
  private discounts: DiscountGroup = {};
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  private saleLoyaltysDiscounts: SalesGroup = {};
  private returnLoyaltysDiscounts: SalesGroup = {};
  private returns: SalesGroup = {};
  private serviceCharge: ServiceChargeGroup = {};
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore, it's used in getIntegerNumberString(get(report, 'payinQuantity', 0)),
  private payinQuantity;
  private payinTotal;
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore, it's used in getIntegerNumberString(get(report, 'payoutQuantity', 0))
  private payoutQuantity;
  private payoutTotal;
  private cashSales?: number;
  private cashRefunds?: number;
  private cashSalesRoundedAmount: number;
  private cashReturnsRoundedAmount: number;
  private salesRoundedAmount?: number = 0;
  private returnsRoundedAmount?: number = 0;
  private totalNetSales: number;
  private totalNetReturns: number;
  private roundedAmount: number;
  private netSales: number;
  private depositAmount: number;
  private depositRoundedAmount?: number = 0;
  private cashDeposit: number;
  private cashDepositRounded: number;
  private beepQRSales: SalesGroup = {};
  private registerTransactions: RegisterTransactionItemType[] = [];
  private beepTransactions: beepTransactionItemType[] = [];
  private makeStoreCreditAsPayment: boolean = false;

  constructor(immutableStore, shift, registerObjectId = '', makeStoreCreditAsPayment = false) {
    this.shiftId = shift.shiftId;
    this.registerObjectId = registerObjectId;
    this.openTime = shift.openTime;
    this.openBy = shift.openBy;
    this.openingAmount = shift.openingAmount;
    this.closeTime = shift.closeTime;
    this.closeBy = shift.closeBy;
    this.closingAmount = shift.closingAmount;
    this.makeStoreCreditAsPayment = makeStoreCreditAsPayment;
    // console.log('Start = ', new Date());
    const transactionsInThisShift = DAL.getTransactionsInThisShift(this.openTime, this.closeTime, this.registerObjectId);
    // console.log('Middle = ', new Date());
    forEach(transactionsInThisShift, transaction => {
      const {
        payments = [],
        transactionType,
        loyaltyDiscounts = [],
        preOrderDate,
        discount = 0,
        isOnlineOrder = false,
        serviceCharge = 0,
        roundedAmount = 0,
        transactionId,
        isCancelled = false,
        receiptNumber,
        items,
      } = transaction;
      if (!isCancelled) {
        if (transactionType === TransactionFlowType.Sale) {
          //  this.sales
          forEach(
            filter(payments, payment => get(payment, 'type') !== 'Voucher'),
            payment => {
              const { paymentMethodId = '', amount = 0, roundedAmount = 0, isDeposit = false } = payment;
              const inSaleItem = get(this.sales, paymentMethodId);
              this.sales[paymentMethodId] = {
                totalAmount: amount + get(inSaleItem, 'totalAmount', 0),
                totalCashRounded: roundedAmount + get(inSaleItem, 'totalCashRounded', 0),
                count: 1 + get(inSaleItem, 'count', 0),
                paymentMethodId,
              };
              // this.actualSales  1. 不是preorder  2.在当前shift下单且collect的    3. 在之前的shift下单但在当前shift collect的订单，只取非deposit部分
              if (!(preOrderDate && preOrderDate < this.openTime && isDeposit)) {
                const inActualSaleItem = get(this.actualSales, paymentMethodId);
                this.actualSales[paymentMethodId] = {
                  totalAmount: amount + get(inActualSaleItem, 'totalAmount', 0),
                  totalCashRounded: roundedAmount + get(inActualSaleItem, 'totalCashRounded', 0),
                  count: 1 + get(inActualSaleItem, 'count', 0),
                  paymentMethodId,
                };
              }
            }
          );

          //  this.sales[DefaultPaymentOptionType.Loyalty]
          forEach(loyaltyDiscounts, loyaltyDiscount => {
            const { displayDiscount = 0, type = '', spentValue = 0 } = loyaltyDiscount;
            const inSaleItem = get(this.sales, DefaultPaymentOptionType.Loyalty);
            this.sales[DefaultPaymentOptionType.Loyalty] = {
              totalAmount: displayDiscount + get(inSaleItem, 'totalAmount', 0),
              totalCashRounded: 0,
              count: 1 + get(inSaleItem, 'count', 0),
              paymentMethodId: DefaultPaymentOptionType.Loyalty,
            };

            // Get sale loyalty summary
            const inSaleLoyaltysDiscountsItem = get(this.saleLoyaltysDiscounts, type);
            this.saleLoyaltysDiscounts[type] = {
              totalAmount: spentValue + get(inSaleLoyaltysDiscountsItem, 'totalAmount', 0),
              totalAmountWithoutTax: displayDiscount + get(inSaleLoyaltysDiscountsItem, 'totalAmountWithoutTax', 0),
              count: 1 + get(inSaleLoyaltysDiscountsItem, 'count', 0),
            };
          });

          // salesRoundedAmount
          this.salesRoundedAmount += roundedAmount || 0;
        } else if (transactionType === TransactionFlowType.PreOrder) {
          // this.deposits
          // 这里有差异化行为，但是问题不大
          // 之前这里是使用preOrderDate来匹配订单的，而preorder date是在setupPreorder时赋值的，应该是略晚于createDate
          // 为了避免发生问题，这里应该在checkout preorder时，重新赋值preorderDate，以让preorderDate与createDate对齐
          // 这样更合理，因为既然是统计到账，那么就应该以收到钱也就是结算时间为准
          // It is used to calculate Cash summary and Payment summary.Since PreOrder is included in sales amount only after collect,
          // Cash summary and Payment summary need real-time statistics. So there may be a difference between Cash summary/Payment summary and Sales summary
          forEach(
            filter(payments, payment => payment.isDeposit === true),
            payment => {
              const { paymentMethodId, amount = 0, roundedAmount = 0 } = payment;
              const inDepositsItem = get(this.deposits, paymentMethodId);
              this.deposits[paymentMethodId] = {
                totalAmount: amount + get(inDepositsItem, 'totalAmount', 0),
                totalCashRounded: roundedAmount + get(inDepositsItem, 'totalCashRounded', 0),
                count: 1 + get(inDepositsItem, 'count', 0),
                paymentMethodId,
              };
            }
          );

          // depositRoundedAmount
          this.depositRoundedAmount += roundedAmount || 0;
        } else if (transactionType === TransactionFlowType.Return) {
          // Get returns summary
          // this.returns
          forEach(payments, payment => {
            const { paymentMethodId, amount = 0, roundedAmount = 0 } = payment;
            const inReturnsItem = get(this.returns, paymentMethodId);
            this.returns[paymentMethodId] = {
              totalAmount: amount + get(inReturnsItem, 'totalAmount', 0),
              totalCashRounded: roundedAmount + get(inReturnsItem, 'totalCashRounded', 0),
              count: 1 + get(inReturnsItem, 'count', 0),
              paymentMethodId,
            };
          });

          //  this.sales[DefaultPaymentOptionType.Loyalty]
          forEach(loyaltyDiscounts, loyaltyDiscount => {
            const { displayDiscount = 0, type = '', spentValue = 0 } = loyaltyDiscount;
            const inReturnsItem = get(this.returns, DefaultPaymentOptionType.Loyalty);
            this.returns[DefaultPaymentOptionType.Loyalty] = {
              totalAmount: displayDiscount + get(inReturnsItem, 'totalAmount', 0),
              totalCashRounded: get(inReturnsItem, 'totalCashRounded', 0),
              count: 1 + get(inReturnsItem, 'count', 0),
              paymentMethodId: DefaultPaymentOptionType.Loyalty,
            };

            // Get return loyalty summary
            const inReturnLoyaltysDiscountsItem = get(this.returnLoyaltysDiscounts, type);
            this.returnLoyaltysDiscounts[type] = {
              totalAmount: spentValue + get(inReturnLoyaltysDiscountsItem, 'totalAmount', 0),
              totalAmountWithoutTax: displayDiscount + get(inReturnLoyaltysDiscountsItem, 'totalAmountWithoutTax', 0),
              count: 1 + get(inReturnLoyaltysDiscountsItem, 'count', 0),
            };
          });

          // returnsRoundedAmount
          this.returnsRoundedAmount += roundedAmount || 0;
        }

        // Get discount summary
        if (!isEmpty(discount) && discount > 0) {
          const inDiscountsItem = get(this.discounts, transactionType);
          this.discounts[transactionType] = {
            amount: discount + get(inDiscountsItem, 'amount', 0),
            count: 1 + get(inDiscountsItem, 'count', 0),
          };
        }

        // Get PayByCash Sales summary
        if (isOnlineOrder) {
          forEach(
            filter(payments, payment => payment.paymentMethodId === 0),
            payment => {
              const { paymentMethodId, amount = 0, roundedAmount = 0 } = payment;
              const inBeepQRSalesItem = get(this.beepQRSales, paymentMethodId);
              this.beepQRSales[paymentMethodId] = {
                totalAmount: amount + get(inBeepQRSalesItem, 'totalAmount', 0),
                totalCashRounded: roundedAmount + get(inBeepQRSalesItem, 'totalCashRounded', 0),
                count: 1 + get(inBeepQRSalesItem, 'count', 0),
                paymentMethodId,
              };
            }
          );
        }

        // Get service charge summary
        if (serviceCharge && serviceCharge !== 0 && includes([TransactionFlowType.Sale, TransactionFlowType.Return], transactionType)) {
          const curKey = transactionType === TransactionFlowType.Sale ? 'sales' : 'refunds';
          const inServiceChargeItem = get(this.serviceCharge, curKey);
          this.serviceCharge[curKey] = {
            amount: serviceCharge + get(inServiceChargeItem, 'amount', 0),
            count: 1 + get(inServiceChargeItem, 'count', 0),
          };
        }

        // this.transactionCount
        if (transactionType !== TransactionFlowType.PreOrder) {
          this.transactionCount += 1;
        }

        // this.taxSummaryAggregatedByTransactionType
        if (includes([TransactionFlowType.Sale, TransactionFlowType.Return], transactionType)) {
          const curKey = transactionType === TransactionFlowType.Sale ? 'Sale' : 'Return';
          forEach(items, item => {
            const { tax = 0, taxCode } = item;
            if (!isEmpty(taxCode)) {
              if (!this.taxSummaryAggregatedByTransactionType[curKey][taxCode]) {
                const taxItem = getTaxItemWithStore(immutableStore, taxCode);
                this.taxSummaryAggregatedByTransactionType[curKey][taxCode] = {
                  taxName: get(taxItem, 'name', ''),
                  taxRate: get(taxItem, 'rate', 0),
                  amount: tax,
                };
              } else {
                this.taxSummaryAggregatedByTransactionType[curKey][taxCode].amount += tax || 0;
              }
            }
          });
        }
      }

      if (isOnlineOrder) {
        // beepTransactions
        this.beepTransactions.push({ receiptNumber: receiptNumber || transactionId, transactionType, isCancelled });
      } else {
        // registerTransactions
        this.registerTransactions.push({ transactionId, transactionType, isCancelled });
      }
    });
    // console.log('End = ', new Date());
    this.cashSalesRoundedAmount = get(this.sales, [String(DefaultPaymentOptionType.Cash), 'totalCashRounded'], 0);

    this.cashReturnsRoundedAmount = get(this.returns, [String(DefaultPaymentOptionType.Cash), 'totalCashRounded'], 0);

    const cashIncome = this.salesByPaymentMethodId(String(DefaultPaymentOptionType.Cash));
    // the preorder transaction will changed to SaleTransaction after picked up, so the deposit of it should not include
    const depositIncome = this.depositByPaymentMethodId(String(DefaultPaymentOptionType.Cash));
    const returnSum = this.returnsByPaymentMethodId(String(DefaultPaymentOptionType.Cash));
    this.cashDepositRounded = get(this.deposits, [String(DefaultPaymentOptionType.Cash), 'totalCashRounded'], 0);
    this.cashDeposit = depositIncome - this.cashDepositRounded;
    this.payinQuantity = get(shift, ['payins', 'length'], 0);
    this.payoutQuantity = get(shift, ['payouts', 'length'], 0);
    this.payinTotal = getPayinTotal(shift);
    this.payoutTotal = getPayoutTotal(shift);
    // Because the CashSales included tax and Rounding and the Rounding was in CashRounded so the Rounding should be taken off
    this.cashSales = cashIncome - this.cashSalesRoundedAmount;
    this.cashRefunds = returnSum - this.cashReturnsRoundedAmount;

    this.expectedDrawer = cashIncome + depositIncome + this.openingAmount + this.payinTotal - this.payoutTotal - returnSum;

    this.totalNetSales = this.getNetTotalSales();
    this.totalNetReturns = this.getNetTotalReturns();
    this.roundedAmount = this.getRoundedAmount();
    this.netSales = this.getNetSales();
    this.depositAmount = this.getTotalDeposit();
  }

  private salesByPaymentMethodId = (paymentMethodId: string) => {
    const dict = this.actualSales[paymentMethodId];
    if (dict) {
      return dict.totalAmount;
    }
    return 0;
  };

  private depositByPaymentMethodId = (paymentMethodId: string) => {
    const dict = this.deposits[paymentMethodId];
    if (dict) {
      return dict.totalAmount;
    }
    return 0;
  };

  private returnsByPaymentMethodId = (paymentMethodId: string) => {
    const dict = this.returns[paymentMethodId];
    if (dict) {
      return dict.totalAmount;
    }
    return 0;
  };

  private getActualTotalSales = () => {
    let total = 0;
    const _salesValues: any[] = Object.values(this.actualSales);
    for (const value of _salesValues) {
      total += value.totalAmount;
    }
    return total;
  };

  private getNetTotalSales = () => {
    let total = 0;
    const _keys: any[] = Object.keys(this.sales);
    for (const itemKey of _keys) {
      if (itemKey === `${DefaultPaymentOptionType.Loyalty}` && !this.makeStoreCreditAsPayment) {
        continue;
      }
      total += this.sales[itemKey].totalAmount;
    }
    return total;
  };

  private getTotalReturns = () => {
    let total = 0;
    const _returnsValues: any[] = Object.values(this.returns);
    for (const value of _returnsValues) {
      total += value.totalAmount;
    }
    return total;
  };

  private getNetTotalReturns = () => {
    let total = 0;
    const _keys: any[] = Object.keys(this.returns);
    for (const itemKey of _keys) {
      if (itemKey === `${DefaultPaymentOptionType.Loyalty}` && !this.makeStoreCreditAsPayment) {
        continue;
      }
      total += this.returns[itemKey].totalAmount;
    }
    return total;
  };

  private getTotalDeposit = () => {
    let total = 0;
    const _depositValues: any[] = Object.values(this.deposits);
    for (const value of _depositValues) {
      total += value.totalAmount;
    }
    return total;
  };

  private getSaleLoyaltyDiscount = (enableCashback: boolean) => {
    return enableCashback
      ? get(this.saleLoyaltysDiscounts, ['cashback', 'totalAmountWithoutTax'], 0)
      : get(this.saleLoyaltysDiscounts, ['storeCredit', 'totalAmountWithoutTax'], 0);
  };

  private getSaleLoyaltyCount = (enableCashback: boolean) => {
    return enableCashback ? get(this.saleLoyaltysDiscounts, ['cashback', 'count'], 0) : get(this.saleLoyaltysDiscounts, ['storeCredit', 'count'], 0);
  };

  private getReturnLoyaltyDiscount = (enableCashback: boolean) => {
    return enableCashback
      ? get(this.returnLoyaltysDiscounts, ['cashback', 'totalAmountWithoutTax'], 0)
      : get(this.returnLoyaltysDiscounts, ['storeCredit', 'totalAmountWithoutTax'], 0);
  };

  private getReturnLoyaltyCount = (enableCashback: boolean) => {
    return enableCashback ? get(this.returnLoyaltysDiscounts, ['cashback', 'count'], 0) : get(this.returnLoyaltysDiscounts, ['storeCredit', 'count'], 0);
  };

  private getTaxTotalByType = (type: string) => {
    let total = 0;
    const summary: any[] = (this.taxSummaryAggregatedByTransactionType && this.taxSummaryAggregatedByTransactionType[type]) || {};
    for (const value of Object.values(summary)) {
      total += value.amount;
    }
    return total;
  };

  private taxSummary = () => {
    const summary: any = {};
    const salesSummary = this.taxSummaryAggregatedByTransactionType.Sale;
    for (const taxId of Object.keys(salesSummary)) {
      summary[taxId] = Object.assign({}, salesSummary[taxId]);
    }
    const returnSummary = this.taxSummaryAggregatedByTransactionType.Return;
    for (const taxId of Object.keys(returnSummary)) {
      if (!summary[taxId]) {
        summary[taxId] = Object.assign({}, returnSummary[taxId]);
        summary[taxId]['amount'] = 0.0;
      }
      summary[taxId]['amount'] -= returnSummary[taxId]['amount'];
    }
    return summary;
  };

  private cancelSummary = () => {
    return DAL.getCanncelTransactionSummary(this.openTime, this.closeTime, this.registerObjectId);
  };

  // sale summary
  private getGrossSales = () => {
    const totalSales = this.totalNetSales;
    const totalSalesTax = this.getTaxTotalByType(TransactionFlowType.Sale);
    const salesDiscountsAmount = get(this.discounts, ['Sale', 'amount'], 0);
    const totalServiceCharge = get(this.serviceCharge, ['sales', 'amount'], 0);
    return Number(totalSales - totalSalesTax + salesDiscountsAmount - this.salesRoundedAmount - totalServiceCharge);
  };

  private getGrossReturn = () => {
    const totalReturn = this.totalNetReturns;
    const totalReturnTax = this.getTaxTotalByType(TransactionFlowType.Return);
    const returnsDiscountsAmount = get(this.discounts, ['Return', 'amount'], 0);
    const returnsServiceChargeAmunt = get(this.serviceCharge, ['refunds', 'amount'], 0);
    const grossReturnAmount = totalReturn - totalReturnTax + returnsDiscountsAmount - this.returnsRoundedAmount - returnsServiceChargeAmunt;
    return Number(grossReturnAmount > 0.001 ? -grossReturnAmount : 0);
  };

  private getDiscount = (enableCashback: boolean) => {
    const salesDiscountsAmount = get(this.discounts, ['Sale', 'amount'], 0);
    const returnsDiscountsAmount = get(this.discounts, ['Return', 'amount'], 0);
    const saleLoyaltyDiscount = this.getSaleLoyaltyDiscount(enableCashback);
    const returnLoyaltyDiscount = this.getReturnLoyaltyDiscount(enableCashback);
    return salesDiscountsAmount - saleLoyaltyDiscount - (returnsDiscountsAmount - returnLoyaltyDiscount);
  };

  private getServiceCharge = () => {
    const salesServiceChargeAmount = get(this.serviceCharge, ['sales', 'amount'], 0);
    const returnsServiceChargeAmount = get(this.serviceCharge, ['refunds', 'amount'], 0);
    return salesServiceChargeAmount - returnsServiceChargeAmount;
  };

  private getLoyaltyDiscount = (enableCashback: boolean) => {
    const saleLoyaltyDiscount = this.getSaleLoyaltyDiscount(enableCashback);
    const returnLoyaltyDiscount = this.getReturnLoyaltyDiscount(enableCashback);
    return saleLoyaltyDiscount - returnLoyaltyDiscount;
  };

  private getLoyaltyDiscountCount = (enableCashback: boolean) => {
    const saleLoyaltyCount = this.getSaleLoyaltyCount(enableCashback);
    const returnLoyaltyCount = this.getReturnLoyaltyCount(enableCashback);
    return saleLoyaltyCount - returnLoyaltyCount;
  };

  private getRoundedAmount = () => {
    return this.salesRoundedAmount - this.returnsRoundedAmount;
  };

  private getNetSales = () => {
    const totalSales = this.totalNetSales;
    const totalSalesTax = this.getTaxTotalByType(TransactionFlowType.Sale);
    const totalReturn = this.totalNetReturns;
    const totalReturnTax = this.getTaxTotalByType(TransactionFlowType.Return);
    const totalServiceCharge = get(this.serviceCharge, ['sales', 'amount'], 0);
    const returnsServiceChargeAmunt = get(this.serviceCharge, ['refunds', 'amount'], 0);
    return Number(totalSales - totalSalesTax - totalReturn + totalReturnTax - this.roundedAmount - totalServiceCharge + returnsServiceChargeAmunt);
  };

  private getTotalTax = () => {
    const totalSalesTax = this.getTaxTotalByType(TransactionFlowType.Sale);
    const totalReturnTax = this.getTaxTotalByType(TransactionFlowType.Return);
    return Number(totalSalesTax - totalReturnTax);
  };

  private getTotal = () => {
    return Number(this.totalNetSales - this.totalNetReturns);
  };

  public getSaleSummary = (enableCashback: boolean): SaleSummary => {
    const grossSales = localeNumber(this.getGrossSales());
    const grossReturn = localeNumber(this.getGrossReturn());
    const discount = localeNumber(this.getDiscount(enableCashback));
    const loyaltyDiscount = localeNumber(this.getLoyaltyDiscount(enableCashback));
    const netSales = localeNumber(this.netSales);
    const deposit = localeNumber(this.depositAmount - this.depositRoundedAmount);
    const roundedAmount = this.roundedAmount + this.depositRoundedAmount;
    const rounded = localeNumber(Number(Math.abs(roundedAmount) < 0.001 ? 0 : roundedAmount));
    const totalTax = localeNumber(this.getTotalTax());
    const total = localeNumber(this.getTotal() + this.depositAmount);
    const serviceCharge = localeNumber(this.getServiceCharge());
    return { grossSales, grossReturn, discount, loyaltyDiscount, netSales, deposit, serviceCharge, rounded, totalTax, total };
  };

  // cash summary
  private getBeepQRCashSales = () => {
    const beepQRCashIncome = get(this.beepQRSales, [String(DefaultPaymentOptionType.Cash), 'totalAmount'], 0);
    const beepQRCashRoundedAmount = get(this.beepQRSales, [String(DefaultPaymentOptionType.Cash), 'totalCashRounded'], 0);
    return beepQRCashIncome - beepQRCashRoundedAmount;
  };

  private getOpeningAmount = () => {
    return isNaN(Number(this.openingAmount)) ? 0 : Number(this.openingAmount);
  };

  private getCashRoundedAmount = () => {
    return this.cashSalesRoundedAmount - this.cashReturnsRoundedAmount + this.cashDepositRounded;
  };

  private getActualDrawerNum = () => {
    return isNaN(Number(this.closingAmount)) ? 0 : Number(this.closingAmount);
  };

  private getCashOverShort = () => {
    return this.getActualDrawerNum() - Number(this.expectedDrawer);
  };

  public getCashDrawerSummary = (): CashDrawerSummary => {
    const openingAmount = localeNumber(this.getOpeningAmount());
    // Because now Loyalty is not regarded as a payment method, but as a discount, Loyalty is not included in the calculation of TotalSale
    const totalCashSales = localeNumber(this.cashSales);
    const beepQrCashSalesAmount = this.getBeepQRCashSales();
    const registerCashSales = localeNumber(this.cashSales - beepQrCashSalesAmount);
    const beepQrCashCashSales = localeNumber(beepQrCashSalesAmount);
    const cashDeposit = localeNumber(this.cashDeposit);
    this.depositByPaymentMethodId(String(DefaultPaymentOptionType.Cash));

    const cashReturn = localeNumber(Number(this.cashRefunds > 0.001 ? -this.cashRefunds : 0));
    const cashRoundedAmount = this.getCashRoundedAmount();
    console.log('SaleFlow', 'getCashDrawerSummary', cashRoundedAmount);
    const cashRounded = localeNumber(Number(Math.abs(cashRoundedAmount) < 0.001 ? 0 : cashRoundedAmount));
    const payListEnable = this.payinTotal > 0 || this.payoutTotal > 0;
    const payinoutSum = localeNumber(this.payinTotal) + '/-' + localeNumber(this.payoutTotal);
    const actualDrawer = localeNumber(this.getActualDrawerNum());
    const expectedDrawer = localeNumber(this.expectedDrawer);
    const overShort = localeNumber(this.getCashOverShort());
    return {
      openingAmount,
      totalCashSales,
      registerCashSales,
      beepQrCashCashSales,
      cashDeposit,
      cashReturn,
      cashRounded,
      payListEnable,
      payinoutSum,
      actualDrawer,
      expectedDrawer,
      overShort,
    };
  };

  // payment summary
  private getSalesNumberStringByPaymentId = paymentId => {
    return this.salesByPaymentMethodId(paymentId) - this.returnsByPaymentMethodId(paymentId);
  };

  private getPaymentsTotal = () => {
    return Number(this.getActualTotalSales() - this.getTotalReturns());
  };

  public getPaymentsSummary = (paymentOptions): PaymentsSummary => {
    const paymentsList = [];

    for (const paymentOption of paymentOptions) {
      const { name, paymentId, isDeleted, isDisabled, _id } = paymentOption;
      if (paymentId === 2 && !this.makeStoreCreditAsPayment) continue;
      if (!isDeleted && !isDisabled) {
        const paymentAmount = localeNumber(this.getSalesNumberStringByPaymentId(String(paymentId)) + this.depositByPaymentMethodId(String(paymentId)));
        paymentsList.push({
          paymentName: name,
          paymentAmount,
          paymentId: paymentId,
          _id,
        });
      }
    }

    const total = localeNumber(this.getPaymentsTotal() + this.depositAmount);
    return { paymentsList, total };
  };

  // Total Net Sales
  // Because now Loyalty is not regarded as a payment method, but as a discount, Loyalty is not included in the calculation of TotalSale
  // However, loyalty needs to be reflected in the Report of Payment, so it needs to count loyalty in the Transaction of Sales and Returns
  public getTotalNetSalesAndAvg = (shiftOpenStatus): TopOverviewFields => {
    const netSalesMount = this.netSales;
    const netSales = localeNumber(netSalesMount);
    const count = this.transactionCount.toFixed(0);
    const avgAmount = localeNumber(Number(this.transactionCount > 0 ? netSalesMount / this.transactionCount : 0));
    const timeDate = shiftOpenStatus ? this.openTime : this.closeTime;
    const employeeId = shiftOpenStatus ? this.openBy : this.closeBy;
    return { netSales, count, avgAmount, timeDate, employeeId };
  };

  // TODO syncShift 上传 Register Cash Sales 和 Beep QR Cash Sales
  static convertToFormData(report: ShiftReport, enableCashback: boolean) {
    const jsonObj: any = {
      shiftId: report.shiftId,
      closeBy: report.closeBy,
      closeTime: report.closeTime ? report.closeTime.toISOString() : '',
      closingAmount: report.closingAmount,
      openBy: report.openBy,
      openingAmount: report.openingAmount,
      openTime: report.openTime ? report.openTime.toISOString() : '',
      salesRoundedAmount: report.salesRoundedAmount,
      returnsRoundedAmount: report.returnsRoundedAmount,
      cashSalesRoundedAmount: report.cashSalesRoundedAmount,
      cashReturnsRoundedAmount: report.cashReturnsRoundedAmount,
      registerTransactions: report.registerTransactions,
      beepTransactions: report.beepTransactions,
    };

    if (report.serviceCharge) {
      jsonObj.serviceCharge = report.serviceCharge;
    }

    const salesKeys = Object.keys(get(report, 'actualSales', {}));
    const returnsKeys = Object.keys(get(report, 'returns', {}));
    const depositsKeys = Object.keys(get(report, 'deposits', {}));
    const supportedPaymentIds = PaymentOptions.getSupportedPaymentIds().map(String);
    const supportPayments = unionBy(supportedPaymentIds, salesKeys, returnsKeys, depositsKeys, v => String(v)).map(v => Number(v));

    // Sales parse
    const sales = [];

    for (const paymentMethod of supportPayments) {
      let amount = get(report, ['actualSales', paymentMethod, 'totalAmount'], 0);
      // Cash payment is special, amount need to minus the rounded amout
      if (paymentMethod === DefaultPaymentOptionType.Cash) {
        amount = amount - get(report, ['actualSales', paymentMethod, 'totalCashRounded'], 0);
      }
      if (paymentMethod === DefaultPaymentOptionType.Loyalty && !report.makeStoreCreditAsPayment) continue;

      sales.push({
        paymentMethod: PaymentOptions.getUploadPaymentMethod(paymentMethod),
        amount,
        count: get(report, ['actualSales', paymentMethod, 'count'], 0),
      });
    }

    jsonObj.sales = filter(sales, item => !isEmpty(item.paymentMethod));
    jsonObj.totalSalesAmount = report.totalNetSales;
    // returns parse
    const returns = [];
    for (const paymentMethod of supportPayments) {
      let amount = get(report, ['returns', paymentMethod, 'totalAmount'], 0);
      // Cash payment is special, amount need to minus the rounded amout
      if (paymentMethod === DefaultPaymentOptionType.Cash) {
        amount = amount - get(report, ['returns', paymentMethod, 'totalCashRounded'], 0);
      }
      if (paymentMethod === DefaultPaymentOptionType.Loyalty && !report.makeStoreCreditAsPayment) continue;
      returns.push({
        paymentMethod: PaymentOptions.getUploadPaymentMethod(paymentMethod),
        amount,
        count: get(report, ['returns', paymentMethod, 'count'], 0),
      });
    }
    jsonObj.returns = filter(returns, item => !isEmpty(item.paymentMethod));

    // Tax summary
    jsonObj.taxSummary = report.taxSummary();
    // returned loyalty summary
    const totalLoyaltyDiscountCount = report.getLoyaltyDiscountCount(enableCashback);
    jsonObj.loyaltyDiscounts = {
      sales: {
        amount: report.getLoyaltyDiscount(enableCashback),
        count: totalLoyaltyDiscountCount,
      },
      totalLoyaltyDiscountCount,
    };

    // deposit parse
    const deposits = [];
    for (const paymentMethod of supportPayments) {
      if (paymentMethod === DefaultPaymentOptionType.Loyalty && !report.makeStoreCreditAsPayment) continue;
      deposits.push({
        paymentMethod: PaymentOptions.getUploadPaymentMethod(paymentMethod),
        amount: get(report, ['deposits', paymentMethod, 'totalAmount'], 0),
        count: get(report, ['deposits', paymentMethod, 'count'], 0),
      });
    }
    jsonObj.deposits = filter(deposits, item => !isEmpty(item.paymentMethod));

    jsonObj.discountedAmount = report.getDiscount(enableCashback);

    const salesDiscountsCount = get(report, ['discounts', 'Sale', 'count'], 0);
    const returnsDiscountsCount = get(report, ['discounts', 'Return', 'count'], 0);
    jsonObj.discountedCount = salesDiscountsCount + returnsDiscountsCount;

    const cancelSummary = report.cancelSummary();
    jsonObj.cancelledCount = cancelSummary && cancelSummary.count;
    jsonObj.cancelledAmount = cancelSummary && cancelSummary.totalCancelledAmount;
    jsonObj.expectedDrawer = report.expectedDrawer;

    const shift = DAL.getShiftById(report.shiftId) as any;

    jsonObj.payouts = ShiftHelper.serializeCashPayInOut(shift.payouts);
    jsonObj.payIns = ShiftHelper.serializeCashPayInOut(shift.payins);
    return jsonObj;
  }
}
