import * as Immutable from 'immutable';

import { get } from 'lodash';
import { Action } from 'redux-actions';
import { call, fork, put, select, spawn, take, takeLatest } from 'redux-saga/effects';
import * as Actions from '../../actions';
import { toggleLoadingMask, updateSequence } from '../../actions';
import { ContentType, closeTopNotification, openShiftNotCloseToast, openSuccessToast } from '../../components/common/TopNotification';
import DAL from '../../dal';
import { safeCallback } from '../../utils';
import { isLastShiftNotClosed } from '../../utils/datetime';
import * as JSONUtils from '../../utils/json';
import {
  LoggingLevel,
  SaleFlowAction,
  ShiftFlowAction,
  ZReadingAction,
  errorTransactionEvent,
  errorZReadingEvent,
  infoTransactionEvent,
  infoZReadingEvent,
} from '../../utils/logComponent';
import { logShiftEvent } from '../../utils/logComponent/buz/shift';
import { isEmpty } from '../../utils/validator';
import { syncTransactionsSaga } from '../dataSync';
import { requestCheckHugeDataVersionOnLocalServer } from '../mrs/snapshot';
import { getCurSequence } from '../transaction/common';
import {
  selectCloseLastShiftNotification,
  selectEmployeeId,
  selectLastZReadingCloseTime,
  selectOpenCashDrawerWhenCloseShift,
  selectOperationHours,
  selectPrintReportAfterCloseShift,
  selectRegisterId,
  selectShiftOpenStatus,
  selectStoreId,
} from './../selector';
import { generateShiftReportSaga } from './common';

const uuidv4 = require('uuid/v4');

export function* openShiftSaga(action) {
  const amount = action.payload;
  const employeeId = yield select(selectEmployeeId);

  const newShift: any = {};
  newShift.shiftId = uuidv4();
  newShift.openBy = employeeId;
  const now = new Date();
  newShift.openTime = now.toISOString();
  newShift.openingAmount = amount;
  newShift.version = 1;
  const { success, reason } = DAL.saveShift(newShift);
  yield call(logShiftEvent, {
    event: ShiftFlowAction.OpenShift,
    level: success ? LoggingLevel.Info : LoggingLevel.Error,
    reason: reason,
    privateDataPayload: { shift: JSONUtils.stringify(newShift), shiftId: newShift.shiftId },
  });
  yield put(Actions.checkAutoOpenDrawer({}));
  yield put(Actions.pingLanPrinters({ onlyAssigned: true, showToast: true, source: 'openShift' }));
  yield spawn(requestCheckHugeDataVersionOnLocalServer);
}

export function* closeShiftSaga(action) {
  const amount = action.payload;

  const employeeId = yield select(selectEmployeeId);
  const currentShift = DAL.getLastShift();
  const now = new Date();
  const newShift = {
    shiftId: currentShift.shiftId,
    closeBy: employeeId,
    closeTime: now.toISOString(),
    closingAmount: amount,
  };
  const { success, reason } = DAL.saveShift(newShift);
  yield call(logShiftEvent, {
    event: ShiftFlowAction.CloseShift,
    level: success ? LoggingLevel.Info : LoggingLevel.Error,
    reason: reason,
    privateDataPayload: { shift: JSONUtils.stringify(newShift), shiftId: newShift.shiftId },
  });
  closeTopNotification([ContentType.ShiftNotCloseToast]);
  // Check auto update after close shift
  yield put(Actions.checkUpdate({ checkInstall: true }));
  yield checkAutoPrintShiftReport();
}

export function* payInOutSaga(action) {
  const { payType, type, amount, comment, onComplete } = action.payload;
  const employeeId = yield select((state: Immutable.Map<string, any>) => state.getIn(['CurrentEmployee', 'employeeId']));
  const currentShift = DAL.getLastShift();
  const updateEntity: any = {
    shiftId: currentShift.shiftId,
  };

  const newItems = [];
  let oldItems = [];

  const addToNewItems = item => {
    newItems.push({
      actedBy: item.actedBy,
      amount: item.amount,
      comment: item.comment,
      createdDate: item.createdDate,
      payType: item.payType,
      type: item.type,
    });
  };

  if (payType === 0) {
    // Pay out
    oldItems = currentShift.payouts;
    updateEntity.payouts = newItems;
  } else {
    // Pay in
    oldItems = currentShift.payins;
    updateEntity.payins = newItems;
  }

  if (Boolean(oldItems) && oldItems.length > 0) {
    // Copy old items to new items
    for (const item of oldItems) {
      addToNewItems({
        actedBy: item.actedBy,
        amount: item.amount,
        comment: item.comment,
        createdDate: item.createdDate,
        payType: item.payType,
        type: item.type,
      });
    }
  }

  // New Item
  const now = new Date();
  addToNewItems({
    actedBy: employeeId,
    amount: amount,
    comment: comment,
    createdDate: now.toISOString(),
    payType: payType,
    type: type,
  });

  // Try to update Shfit
  yield put(Actions.openDrawer(payType === 1 ? 'cash_payin' : 'cash_payout'));
  const { success, reason } = DAL.saveShift(updateEntity);
  yield call(logShiftEvent, {
    event: ShiftFlowAction.CloseShift,
    level: success ? LoggingLevel.Info : LoggingLevel.Error,
    reason: reason,
    privateDataPayload: { shift: JSONUtils.stringify(updateEntity), shiftId: updateEntity.shiftId },
  });
  onComplete.callback();
}

export function* checkAutoPrintShiftReport() {
  const currentShift = DAL.getLastShift();
  const printReportAfterCloseShift = yield select(selectPrintReportAfterCloseShift);
  if (printReportAfterCloseShift === '1') {
    const report = yield call(generateShiftReportSaga, currentShift);
    yield put(Actions.printShiftReport(report));
  }
}

export function* getCurrentShiftStatusSaga() {
  const currentShift = DAL.getLastShift();
  let status = false;
  if (currentShift !== undefined && (currentShift.closeTime === undefined || currentShift.closeTime === null)) {
    status = true;
  }
  yield put(Actions.setShiftStatus(status));
}

export function* safeCloseZReadingSaga(action) {
  yield put(toggleLoadingMask({ visible: true }));
  const result = yield call(syncTransactionsSaga, { payload: { isCheckingBeforeCloseZreading: true } });
  const { onSuccess, onFailure, onEODSuccess, onEODFailed, needEOD = false } = action.payload;
  const oldLastZReadingCloseTime = yield select(selectLastZReadingCloseTime);
  const operationHours = yield select(selectOperationHours);
  if (result) {
    yield put(Actions.closeZReading());
    const responseAction = yield take([Actions.closeZReading.toString() + '.success', Actions.closeZReading.toString() + '.failure']);
    if (responseAction.type === Actions.closeZReading.toString() + '.success') {
      // server side will generate seq number for the online order which have no seq number
      // This solution is that some online orders will generate seqNumber when printing,
      // but there must be some orders that are not printed or fail to generate when printing,
      // but these orders will be included in z-reading. Therefore,
      // we need to tell the server laststSeqnumber when requesting close z-reading,
      // and the server generates seqNumber for online orders that have not generated seqNumber in batches.
      // The latest laststSeqnumber is then returned to the POS, which updates the local data
      const sequenceStartInfo = get(responseAction, ['payload', 'sequenceStartInfo'], {});
      yield call(updateSequenceSaga, updateSequence({ ...sequenceStartInfo, isStart: true, from: 'safeCloseZReadingSaga' }));
      const payload = responseAction.payload;
      const lastZReadingCloseTime = get(payload, 'closeTime', '');
      if (lastZReadingCloseTime) {
        yield put(Actions.setSyncInfo({ lastZReadingCloseTime }));
      }
      infoZReadingEvent({ action: ZReadingAction.CloseZReading, privateDataPayload: { lastZReadingCloseTime, oldLastZReadingCloseTime, operationHours } });
      safeCallback(onSuccess, payload);
      yield put(Actions.processToCallEOD({ onEODSuccess, onEODFailed, needEOD, isZReadingClosed: true }));
    } else {
      const resp = responseAction.payload;
      errorZReadingEvent({
        action: ZReadingAction.CloseZReading,
        reason: get(resp, 'message'),
        privateDataPayload: { oldLastZReadingCloseTime, operationHours },
      });
      safeCallback(onFailure, resp);
      yield put(Actions.processToCallEOD({ onEODSuccess, onEODFailed, needEOD, isZReadingClosed: false }));
    }
  } else {
    safeCallback(onFailure, null);
    yield put(Actions.processToCallEOD({ onEODSuccess, onEODFailed, needEOD, isZReadingClosed: false }));
  }
  yield put(toggleLoadingMask({ visible: false }));
}

export function* generateTodayShiftReportSaga(action) {
  const { onComplete } = action.payload;
  const currentShift = DAL.getLastShift();
  const report = yield call(generateShiftReportSaga, currentShift);
  if (onComplete) {
    onComplete.callback(report);
  }
}

export function* checkAutoOpenDrawerSaga() {
  const openCashDrawerWhenCloseShift = yield select(selectOpenCashDrawerWhenCloseShift);

  if (openCashDrawerWhenCloseShift === '1') {
    yield put(Actions.openDrawer('close_shift'));
  }
}

export function* checkCloseLastShiftNotificationSaga() {
  const operationHours = yield select(selectOperationHours);
  const shiftOpenStatus = yield select(selectShiftOpenStatus);
  const enableCloseLastShiftNotification = yield select(selectCloseLastShiftNotification);
  if (shiftOpenStatus && enableCloseLastShiftNotification) {
    const lastShiftNotClosed = isLastShiftNotClosed(operationHours);
    if (lastShiftNotClosed) {
      openShiftNotCloseToast();
    }
  }
}

export function* checkMallIntegratioinReportUplaodStatusSaga() {
  const registerId = yield select(selectRegisterId);
  const storeId = yield select(selectStoreId);
  if (isEmpty(storeId) || isEmpty(registerId)) {
    yield put(Actions.checkReportUpdateStatusEnd());
    return;
  }
  yield put(
    Actions.retryUpload({
      registerId,
      storeId,
    })
  );
  const responseAction = yield take([Actions.retryUpload.toString() + '.success', Actions.retryUpload.toString() + '.failure']);
  if (responseAction.type === Actions.retryUpload.toString() + '.success') {
    const actionPayload = responseAction.payload;
    if (actionPayload.status === 'NORMAL' && actionPayload.message) {
      openSuccessToast(actionPayload.message);
      yield put(Actions.checkReportUpdateStatusEnd());
    }
  }
}

export function* updateSequenceSaga(action) {
  const { receiptNumberStart, invoiceNumberStart, receiptDateStart, isStart = false, minimumSequence, from, transaction } = action.payload;
  if (!Boolean(receiptNumberStart) && !Boolean(invoiceNumberStart) && !Boolean(receiptDateStart)) return;
  let minimumSeqObj = minimumSequence;
  if (!Boolean(minimumSeqObj)) minimumSeqObj = yield call(getCurSequence);
  const newSequence: any = {};
  let logmsg = `from: ${from} \n `;
  let hasError = false;
  if (Boolean(receiptNumberStart)) {
    const minimumReceiptNumberStart = Number(minimumSeqObj.receiptNumberStart | 0);
    const newReceiptNumberStart = isStart ? Number(receiptNumberStart) : Number(receiptNumberStart) + 1;
    if (newReceiptNumberStart < minimumReceiptNumberStart) {
      hasError = true;
      logmsg += `generate seq number error: newReceiptNumberStart is ${newReceiptNumberStart} which less than the current receiptNumberStart ${minimumReceiptNumberStart} \n`;
    } else {
      newSequence.receiptNumberStart = newReceiptNumberStart;
      logmsg += `generate seq number info: newReceiptNumberStart is ${newReceiptNumberStart} AND current receiptNumberStart ${minimumReceiptNumberStart} \n`;
    }
  }
  if (Boolean(invoiceNumberStart)) {
    const minimumInvoiceNumberStart = Number(minimumSeqObj.invoiceNumberStart | 0);
    const newInvoiceNumberStart = isStart ? Number(invoiceNumberStart) : Number(invoiceNumberStart) + 1;
    if (newInvoiceNumberStart < minimumInvoiceNumberStart) {
      hasError = true;
      logmsg += `generate invoice seq number error: newInvoiceNumberStart is ${newInvoiceNumberStart} which less than the current invoiceNumberStart ${minimumInvoiceNumberStart}`;
    } else {
      newSequence.invoiceNumberStart = newInvoiceNumberStart;
      logmsg += `generate invoice seq number info: newInvoiceNumberStart is ${newInvoiceNumberStart} AND current invoiceNumberStart ${minimumInvoiceNumberStart}`;
    }
  }
  if (Boolean(receiptDateStart)) {
    newSequence.receiptDateStart = receiptDateStart;
  }
  if (Object.keys(newSequence).length > 0) {
    yield put(Actions.setSequence(newSequence));
  }
  // console.log('update Sequence message  = ', logmsg);
  const logParams: any = { action: SaleFlowAction.GenerateOnlineInvoiceSequenceNumber, reason: logmsg };
  if (Boolean(transaction)) {
    if (hasError) logParams.transaction = transaction;
    logParams.orderId = transaction.receiptNumber || transaction.orderId;
  }

  if (hasError) {
    errorTransactionEvent(logParams);
  } else {
    infoTransactionEvent(logParams);
  }
}

export function* setSequenceSaga(action: Action<Actions.SetSequenceType>) {
  yield call(logShiftEvent, {
    event: ShiftFlowAction.SetSequence,
    privateDataPayload: {
      payload: action.payload,
    },
  });
}
function* shiftFlowSagas() {
  yield takeLatest(Actions.openShift.toString(), openShiftSaga);
  yield takeLatest(Actions.closeShift.toString(), closeShiftSaga);
  yield takeLatest(Actions.payInOut.toString(), payInOutSaga);
  yield takeLatest(Actions.safeCloseZReading.toString(), safeCloseZReadingSaga);
  yield takeLatest(Actions.getCurrentShiftStatus.toString(), getCurrentShiftStatusSaga);
  yield takeLatest(Actions.generateShiftReport.toString(), generateTodayShiftReportSaga);
  yield takeLatest(Actions.checkAutoOpenDrawer.toString(), checkAutoOpenDrawerSaga);
  yield takeLatest(Actions.checkCloseLastShiftNotification.toString(), checkCloseLastShiftNotificationSaga);
  yield takeLatest(Actions.checkReportUpdateStatusBegin.toString(), checkMallIntegratioinReportUplaodStatusSaga);
  yield takeLatest(Actions.updateSequence.toString(), updateSequenceSaga);
  yield takeLatest(Actions.setSequence.toString(), setSequenceSaga);
}

export default fork(shiftFlowSagas);
