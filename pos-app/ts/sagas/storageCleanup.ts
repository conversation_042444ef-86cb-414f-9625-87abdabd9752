import { head, last } from 'lodash';
import RNFS, { ReadDirItem } from 'react-native-fs';
import { call, delay, select } from 'redux-saga/effects';
import { notify<PERSON>rinterJob } from '../actions';
import { TransactionFlowType } from '../constants';
import DAL from '../dal';
import RealmManager from '../dal/realm';
import { ShiftType, TransactionType } from '../typings';
import { StorageAutoCleanConfigType, TransactionDBAutoCleanConfigType } from '../utils/growthbook';
import { logStorageCleanupEventError, logStorageCleanupEventInfo, StorageCleanupAction } from '../utils/logComponent/buz/storageCleanup';
import { loopCleanTransactionsLogSaga } from './mrs/snapshot';
import { notifyPrinterJobSaga } from './printing';
import {
  selectEmployeeId,
  selectEnableStorageAutoClean,
  selectGBEnableTransactionDBA<PERSON>Clean,
  selectInsufficientStorageWarning,
  selectRegisterId,
  selectShiftOpenStatus,
} from './selector';
import { navigateToStorageCriticalModel, navigateToStorageInsufficientModel } from '../navigation/storageNavigations';

export const TRANSACTION_SIZE_THRESHOLD = 100_000_000; // 12000 on gb
export const SHIFT_SIZE_THRESHOLD = 100_000_000; // 90 on gb

enum CleanupResult {
  CONTINUE,
  STOP_REALM_NOT_LOADED,
  STOP_NO_REGISTER_ID,
  STOP_SIGNED_IN,
  STOP_SHIFT_NOT_CLOSED,
  STOP_EMPTY_SHIFTS,
  STOP_SHIFTS_THRESHOLD,
  STOP_TXN_THRESHOLD,
  STOP_TXN_NOT_UPLOADED,
}

// eslint-disable-next-line require-yield
export function* cleanupLocalStorageSaga(action) {
  const storageConfig: StorageAutoCleanConfigType = yield select(selectEnableStorageAutoClean);
  if (storageConfig.enabled) {
    if (storageConfig.cleanCpuProfile) {
      try {
        let totalSize = 0;
        const files: ReadDirItem[] = yield RNFS.readDir(RNFS.CachesDirectoryPath);
        const cpuprofiles = files.filter(file => file.isFile() && file.name.endsWith('.cpuprofile'));
        for (const file of cpuprofiles) {
          totalSize += file.size;
          yield RNFS.unlink(file.path);
        }
        logStorageCleanupEventInfo({
          action: StorageCleanupAction.CacheDirClean,
          privateDataPayload: {
            transactionDBClean: {
              jobId: 0,
              result: 'STOP_CPU_PROFILE_NOT_CLEAN',
              size: totalSize / 1024 / 1024,
            },
          },
        });
      } catch (e) {
        console.log(e);
        logStorageCleanupEventError({
          action: StorageCleanupAction.CacheDirClean,
          reason: e.message,
          privateDataPayload: {
            transactionDBClean: {
              jobId: 0,
              result: 'STOP_CPU_PROFILE_NOT_CLEAN',
            },
          },
        });
      }
    }
  }

  if (!action.payload) {
    console.log('StorageCleanup', 'stopped', 'ACTION_STOP');
    return;
  }

  // cleanup once if signs out
  yield call(cleanupPrintingRecordsSaga);

  const config: TransactionDBAutoCleanConfigType = yield select(selectGBEnableTransactionDBAutoClean);
  console.log('StorageCleanup', config);
  if (!config.enabled) {
    return;
  }

  console.log('StorageCleanup', 'waiting');
  yield delay(config.delay * 1000);
  console.log('StorageCleanup', 'started');
  yield call(loopCleanTransactionsLogSaga);
  let index = 0;
  while (true) {
    index++;

    console.log('StorageCleanup', `job ${index} started `);
    const result: CleanupResult = yield call(cleanupTransactionsInEarliestShift, index);
    logStorageCleanupEventInfo({
      action: StorageCleanupAction.TransactionDBClean,
      privateDataPayload: {
        transactionDBClean: {
          jobId: index,
          result: CleanupResult[result],
        },
      },
    });
    console.log('StorageCleanup', `job ${index} stopped ${CleanupResult[result]}`);
    if (result > 0) {
      break;
    }
    yield delay(config.interval * 1000);
  }
}

// cleanup offline online transactions from oldest to latest 8 per time
export function* cleanupTransactionsInEarliestShift(index) {
  const realm = RealmManager.getRealmInstance();
  if (!realm) {
    return CleanupResult.STOP_REALM_NOT_LOADED;
  }

  // const all = DAL.objects<TransactionType>('Transaction');
  // console.log('StorageCleanup', 'totalTransactions ', all.length);
  // console.log('StorageCleanup', 'totalTransactions ', all);

  const registerId = yield select(selectRegisterId);
  if (!registerId) {
    return CleanupResult.STOP_NO_REGISTER_ID;
  }
  const shiftOpenStatus = yield select(selectShiftOpenStatus);
  if (shiftOpenStatus) {
    return CleanupResult.STOP_SHIFT_NOT_CLOSED;
  }
  const signedIn = yield select(selectEmployeeId);
  if (signedIn) {
    return CleanupResult.STOP_SIGNED_IN;
  }
  const config: TransactionDBAutoCleanConfigType = yield select(selectGBEnableTransactionDBAutoClean);

  const transactionThreshold = config.transactionThreshold;
  if (realm.objects<TransactionType>('Transaction').length <= transactionThreshold) {
    return CleanupResult.STOP_TXN_THRESHOLD;
  }
  const shiftThreshold = config.shiftThreshold;
  if (realm.objects<ShiftType>('Shift').filtered('deleted != true').length <= shiftThreshold) {
    return CleanupResult.STOP_SHIFTS_THRESHOLD;
  }

  // const all = DAL.objects<TransactionType>('Transaction');
  // console.log('StorageCleanup', 'totalTransactions ', all.length);
  // console.log('StorageCleanup', 'totalTransactions ', all);

  const allShifts = realm.objects<ShiftType>('Shift').sorted('openTime', false); // from oldest to latest
  console.log('StorageCleanup', `job ${index} checked allShifts `, allShifts);
  const lastShift = last(allShifts);

  // for the oldest shift that are
  // 1 not deleted before
  // 2 closed
  // 3 uploaded
  // 4 non-latest

  if (!lastShift) {
    return CleanupResult.STOP_EMPTY_SHIFTS;
  }

  const cleanableShifts = allShifts
    .filtered('deleted != true')
    .filtered('closeTime != null')
    .filtered('isUploaded == true')
    .filtered(`shiftId != '${lastShift.shiftId}'`);

  if (cleanableShifts.isEmpty()) {
    // no cleanable shift
    return CleanupResult.STOP_EMPTY_SHIFTS;
  }

  const shiftToClean = head(cleanableShifts);

  if (!shiftToClean) {
    // no cleanable shift
    return CleanupResult.STOP_EMPTY_SHIFTS;
  }
  // console.log('storageCleanup transactions', realm.objects<TransactionType>('Transaction'));
  // console.log('storageCleanup shifts', realm.objects<ShiftType>('Shift'));

  console.log('StorageCleanup', `job ${index} checked shift `, JSON.stringify(shiftToClean.shiftId));

  const salesTransactionNoUploaded = realm
    .objects<TransactionType>('Transaction')
    .filtered(`shiftId == '${shiftToClean.shiftId}'`)
    .filtered(`transactionType == '${TransactionFlowType.Sale}' || transactionType == '${TransactionFlowType.Return}'`)
    .filtered('(uploadedDate == null || uploadedDate < modifiedDate) && isOnlineOrder != true')
    .filtered(`registerId == '${registerId}'`);

  if (salesTransactionNoUploaded.length > 0) {
    return CleanupResult.STOP_TXN_NOT_UPLOADED;
  }

  // for all transactions in this shift that are
  // [0] not marked as deleted
  // [1] in current shift by id
  // [2] sales or refund
  // [3] uploaded local order or online order
  const salesTransactionToDelete = realm
    .objects<TransactionType>('Transaction')
    .filtered(`shiftId == '${shiftToClean.shiftId}'`)
    .filtered(`transactionType == '${TransactionFlowType.Sale}' || transactionType == '${TransactionFlowType.Return}'`)
    .filtered('(uploadedDate != null && uploadedDate >= modifiedDate) || (uploadedDate == null && isOnlineOrder == true)');

  logStorageCleanupEventInfo({
    action: StorageCleanupAction.TransactionDBClean,
    privateDataPayload: {
      transactionDBClean: {
        jobId: index,
        shiftId: shiftToClean.shiftId,
        salesTxnCount: salesTransactionToDelete.length,
      },
    },
  });

  console.log('StorageCleanup', `job ${index} cleaned sales `, salesTransactionToDelete.length);
  realm.write(() => {
    salesTransactionToDelete.forEach(transaction => {
      transaction.items.forEach(item => {
        if (item) {
          if (item.calculation) {
            realm.deleteSafe(item.calculation.discounts);
            realm.deleteSafe(item.calculation.taxes);
            realm.deleteSafe(item.calculation.original);
            realm.deleteSafe(item.calculation);
          }
          realm.deleteSafe(item.promotions);
          realm.deleteSafe(item);
        }
      });

      if (transaction.calculation) {
        realm.deleteSafe(transaction.calculation.discounts);
        realm.deleteSafe(transaction.calculation.taxes);
        realm.deleteSafe(transaction.calculation.original);
        realm.deleteSafe(transaction.calculation);
      }
      realm.deleteSafe(transaction.promotions);
      realm.deleteSafe(transaction.payments);
      realm.deleteSafe(transaction.loyaltyDiscounts);
      realm.deleteSafe(transaction.loyaltyDiscounts);
      realm.deleteSafe(transaction.addonBirCompliance);
      realm.delete(transaction);
    });
  });

  // const endDate = new Date(Date.now() - 24 * 60 * 60 * 1000);
  // // for all transactions in this shift that are
  // // [1] in current shift by time
  // // [2] sales
  // // [3] uploaded local order or online order
  // // [4] kds expired
  // // [5] kds serve time
  // const kdsTransactionToDelete = DAL.objects<TransactionType>('Transaction')
  //   .filtered('createdDate > $0 && createdDate < $1', shift.openTime, shift.closeTime)
  //   .filtered(`transactionType == '${TransactionFlowType.Sale}''`)
  //   .filtered('(uploadedDate != null && uploadedDate >= modifiedDate) || (uploadedDate == null && isOnlineOrder == true)')
  //   .filtered('pushKdsDate != null && modifiedDate != null && modifiedDate < $1', endDate)
  //   .filtered('servedTime == null || servedTimeUploaded == true');
  //
  // DAL.beginTransaction();
  // DAL.delete(kdsTransactionToDelete);
  // DAL.commitTransaction();

  // for all transactions in this shift that are
  // [0] not marked as deleted
  // [1] in current shift by time
  // [2] sales or refund
  // [3] uploaded local order or online order
  // [4] mrs from other registers
  const otherRegisterTransactionToDelete = realm
    .objects<TransactionType>('Transaction')
    .filtered('createdDate >= $0 && createdDate <= $1', shiftToClean.openTime, shiftToClean.closeTime)
    .filtered(`transactionType == '${TransactionFlowType.Sale}' || transactionType == '${TransactionFlowType.Return}'`)
    // .filtered('(uploadedDate != null && uploadedDate >= modifiedDate) || (uploadedDate == null && isOnlineOrder == true)')
    .filtered(`mrs == true && isOpen != true && registerId != '${registerId}'`);

  console.log('StorageCleanup', `job ${index} cleaned others `, otherRegisterTransactionToDelete.length);

  logStorageCleanupEventInfo({
    action: StorageCleanupAction.TransactionDBClean,
    privateDataPayload: {
      transactionDBClean: {
        jobId: index,
        shiftId: shiftToClean.shiftId,
        otherTxnCount: otherRegisterTransactionToDelete.length,
      },
    },
  });

  realm.write(() => {
    otherRegisterTransactionToDelete.forEach(transaction => {
      realm.delete(transaction);
    });
  });

  // if (__DEV__) {
  //   const testTransactions = realm.objects<TransactionType>('Transaction').filtered('isCompleted == true LIMIT(1000)');
  //   const length = testTransactions.length;
  //   const startTime = Date.now();
  //   realm.write(() => {
  //     testTransactions.forEach(transaction => {
  //       realm.delete(transaction);
  //     });
  //   });
  //   console.log('StorageCleanup', `${index} deleted ${length} records takes ${Date.now() - startTime}ms`);
  //   return CleanupResult.CONTINUE;
  // }

  console.log('StorageCleanup', `job ${index} deleting shift `);

  realm.write(() => {
    realm.delete(shiftToClean);
  });

  return CleanupResult.CONTINUE;
}

export function* deleteAllProductThumbnailsSaga(action) {
  const files: ReadDirItem[] = yield RNFS.readDir(RNFS.DocumentDirectoryPath);
  const pngFiles = files.filter(file => file.isFile() && file.name.endsWith('.png'));
  for (const file of pngFiles) {
    yield RNFS.unlink(file.path);
  }
}

export function* voidAllProductThumbnailsSaga(action) {
  const files: ReadDirItem[] = yield RNFS.readDir(RNFS.DocumentDirectoryPath);
  const pngFiles = files.filter(file => file.isFile() && file.name.endsWith('.png'));
  for (const file of pngFiles) {
    yield RNFS.touch(file.path, new Date(null), new Date(null));
  }
}

/**
 * cleanup printing records from 1 week ago when syncing shift
 * @returns
 */
export function* cleanupPrintingRecordsSaga() {
  const size = 5000;
  const time = Date.now();
  const order = DAL.deleteUselessBeepNotis(size);
  let subOrder;
  if (!order?.deleteSize) {
    // delete one by one
    subOrder = DAL.deleteUselessSubOrderNotis(size);
  }
  yield call(notifyPrinterJobSaga, notifyPrinterJob(true));
  console.log({
    order,
    subOrder,
    costTimeNumber: Date.now() - time,
  });
  if (order?.deleteSize > 0 || subOrder?.deleteSize > 0) {
    logStorageCleanupEventInfo({
      action: StorageCleanupAction.RecordsClean,
      privateDataPayload: {
        recordsClean: {
          order,
          subOrder,
          costTimeNumber: Date.now() - time,
        },
      },
    });
  }
}

export function* checkFreeStorageSaga(action) {
  const { navigation, onContinue } = action.payload;
  const info = yield RNFS.getFSInfo();
  const warning = yield select(selectInsufficientStorageWarning);
  if (!info || !warning.enabled) {
    onContinue();
    return;
  }
  if (info && info.freeSpace && info.freeSpace / 1024 / 1024 < warning.criticalThreshold) {
    navigateToStorageCriticalModel(navigation, warning.criticalThreshold, onContinue);
    return;
  }
  if (info && info.freeSpace && info.freeSpace / 1024 / 1024 < warning.warningThreshold) {
    navigateToStorageInsufficientModel(navigation, onContinue);
    return;
  }
  onContinue();
}
