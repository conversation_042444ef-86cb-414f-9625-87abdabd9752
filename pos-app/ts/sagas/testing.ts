import { delay, fork, put, select, takeLatest } from 'redux-saga/effects';

import { populateTransactions, toggleToastInfo } from '../actions';
import RealmManager from '../dal/realm';
import { forEach, range } from 'lodash';
import { createKdsTransaction } from '../components/test/kds/test/transaction';
import { UpdateMode } from 'realm';
import { selectRegisterObjectId } from './selector';

export const populateTransactionsSaga = function* (action) {
  const count = action.payload;
  const registerObjectId = yield select(selectRegisterObjectId);
  RealmManager.getRealmInstance().write(() => {
    forEach(range(count), () => {
      const entity = createKdsTransaction(registerObjectId);
      RealmManager.getRealmInstance().create('Transaction', entity, UpdateMode.All);
    });
  });
};

function* testing() {
  yield takeLatest(populateTransactions.toString(), populateTransactionsSaga);
}

export default fork(testing);
