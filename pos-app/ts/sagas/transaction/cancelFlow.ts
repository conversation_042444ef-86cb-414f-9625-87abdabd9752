import { filter, get, isArray } from 'lodash';

import { Action } from 'redux-actions';
import { call, fork, put, select, spawn, take, takeLatest } from 'redux-saga/effects';
import * as Actions from '../../actions';
import { CancelTransactionType, toggleLoadingMask } from '../../actions';
import { OnlineOrderStatus, OrderNotFindError, SuccessCode, SuccessMessage } from '../../constants';
import DAL from '../../dal';
import { TransactionHelper } from '../../dal/helper';
import { CancelFlowAction, OrderOperationEnum, errorTransactionEvent, infoTransactionEvent } from '../../utils/logComponent';

import { isEmpty } from '../../utils/validator';
import { pushLocalTransactionsSaga } from '../kds/transaction/push';
import { requestProposalActionSaga } from '../mrs/roles/proposer';
import { selectBirAccredited, selectBusinessName, selectEmployeeId, selectGBEnableVoidReceipt, selectStoreId } from '../selector';
import { isValidNumber } from '../../utils';
import { generateLocation } from '../geo';

export const cancelTransactionSaga = function* (action: Action<CancelTransactionType>) {
  const { isBeepOrder } = action.payload;
  const gbEnableVoidReceipt = yield select(selectGBEnableVoidReceipt);
  const birAccredited = yield select(selectBirAccredited);
  if (isBeepOrder) {
    yield call(cancelBeepTransactionSaga, action);
  } else if (gbEnableVoidReceipt && birAccredited) {
    yield call(cancelPOSTransactionWithVoidNumberSaga, action);
  } else {
    yield call(cancelPOSTransactionSaga, action);
  }
};

// void number is generated by BE
// so there will upload the transaction first and then save to local DB
export const cancelPOSTransactionWithVoidNumberSaga = function* (action: Action<CancelTransactionType>) {
  yield put(toggleLoadingMask({ visible: true }));
  const { transactionId, transaction, onlineTransaction, returnReason, otherReason, onComplete } = action.payload;
  // Cancel each payments
  const employeeId = yield select(selectEmployeeId);
  const business = yield select(selectBusinessName);
  const storeId = yield select(selectStoreId);
  const location = yield call(generateLocation);

  const now = new Date();
  const currentShift = DAL.getLastShift();
  const currentShiftId = get(currentShift, 'shiftId');

  let recordJSON;
  let lastRegisterId;
  let originTrx;

  // mapping the local order to remote order
  if (!onlineTransaction) {
    originTrx = DAL.getTransactionById(transactionId);
    if (!originTrx) {
      onComplete && onComplete(OrderNotFindError);
      return;
    }
    lastRegisterId = originTrx.lastRegisterId || originTrx.registerId || '';
    recordJSON = TransactionHelper.serializeTransaction(originTrx);
  } else {
    recordJSON = onlineTransaction;
    lastRegisterId = recordJSON.lastRegisterId || recordJSON.registerId || '';
  }
  const cancelledTrx = {
    ...recordJSON,
    transactionId,
    isCancelled: true,
    // @ts-ignore
    modifiedTime: now.toISOString(),
    // @ts-ignore
    cancelledAt: now.toISOString(),
    cancelledBy: employeeId,
    returnReason,
    otherReason,
  };

  // upload the cancel transaction to generate voidNumber first
  yield put(
    Actions.syncTransaction({
      business,
      storeId,
      lastRegisterId,
      recordJSON: cancelledTrx,
      location,
    })
  );
  const responseAction = yield take([Actions.syncTransaction.toString() + '.success', Actions.syncTransaction.toString() + '.failure']);
  const resp = responseAction.payload;
  if (responseAction.type === Actions.syncTransaction.toString() + '.success') {
    // resp is the order detail
    const localCancelledTrx = TransactionHelper.mappingRemoteToLocal(resp);
    localCancelledTrx.shiftIdOfCancel = currentShiftId;
    let saveToLocalSuccess = false;

    if (!onlineTransaction && transaction && transaction.mrs) {
      const result: Actions.MRSError = yield call(
        requestProposalActionSaga,
        Actions.requestProposalAction({
          data: [
            {
              operationType: Actions.OperationType.UPDATE,
              transaction: localCancelledTrx,
            },
          ],
          operation: OrderOperationEnum.Cancel,
          onComplete,
        })
      );
      saveToLocalSuccess = result.errorCode === SuccessCode;
      if (result.errorCode !== SuccessCode) {
        return;
      }
    } else if (!originTrx) {
      // if the order is not in local DB, we need to save it to local DB
      saveToLocalSuccess = DAL.saveTransaction({
        ...localCancelledTrx,
        isCancelled: true,
        shiftIdOfCancel: currentShiftId,
      });
      onComplete && onComplete(SuccessMessage());
    } else {
      saveToLocalSuccess = DAL.updateTransaction(transactionId, {
        isCancelled: true,
        modifiedDate: now.toISOString(),
        cancelledAt: now.toISOString(),
        cancelledBy: employeeId,
        shiftIdOfCancel: currentShiftId,
        returnReason,
        otherReason,
        voidNumber: resp.voidNumber,
      });
      onComplete && onComplete(SuccessMessage());
    }

    if (saveToLocalSuccess) {
      yield spawn(pushLocalTransactionsSaga, [transactionId]);
      infoTransactionEvent({
        action: CancelFlowAction.UpdateOrderDBCancelStatus,
        transaction: localCancelledTrx,
      });
    } else {
      errorTransactionEvent({
        action: CancelFlowAction.UpdateOrderDBCancelStatus,
        transaction: localCancelledTrx,
      });
    }
    yield put(Actions.employeeCancelOrder({ transactionId }));
    const printParams: Actions.PrintReceiptType = {
      transactionId: transactionId,
      isReprint: false,
      eventName: 'printReceiptWhenCancelRegisterOrder',
    };
    if (!saveToLocalSuccess) {
      printParams.onlineTransaction = localCancelledTrx;
    }
    yield put(Actions.newPrintReceiptAction(printParams));
  } else {
    // TODO: handle failure
    errorTransactionEvent({
      action: CancelFlowAction.CancelTrxWithVoidNumber,
      transaction: cancelledTrx,
    });
    onComplete &&
      onComplete({
        errorCode: -1,
        errorMessage: resp ? resp.message : null,
      });
  }
  yield put(toggleLoadingMask({ visible: false }));
};

export const cancelPOSTransactionSaga = function* (action: Action<CancelTransactionType>) {
  const { transactionId, transaction, onlineTransaction, returnReason, otherReason, onComplete } = action.payload;
  // Cancel each payments
  const employeeId = yield select(selectEmployeeId);
  const now = new Date();
  const currentShift = DAL.getLastShift();
  const currentShiftId = get(currentShift, 'shiftId');
  let cancelSuccess = false;
  if (!onlineTransaction) {
    if (transaction && transaction.mrs) {
      const originTrx = DAL.getPlainTransactionById(transactionId);
      if (!originTrx) {
        onComplete && onComplete(OrderNotFindError);
        return;
      }
      const result: Actions.MRSError = yield call(
        requestProposalActionSaga,
        Actions.requestProposalAction({
          data: [
            {
              operationType: Actions.OperationType.UPDATE,
              transaction: {
                ...originTrx,
                transactionId,
                isCancelled: true,
                // @ts-ignore
                modifiedDate: now.toISOString(),
                // @ts-ignore
                cancelledAt: now.toISOString(),
                cancelledBy: employeeId,
                shiftIdOfCancel: currentShiftId,
                returnReason,
                otherReason,
              },
            },
          ],
          operation: OrderOperationEnum.Cancel,
          onComplete,
        })
      );
      cancelSuccess = result.errorCode === SuccessCode;
      if (result.errorCode !== SuccessCode) {
        return;
      }
    } else {
      cancelSuccess = DAL.updateTransaction(transactionId, {
        isCancelled: true,
        modifiedDate: now.toISOString(),
        cancelledAt: now.toISOString(),
        cancelledBy: employeeId,
        shiftIdOfCancel: currentShiftId,
        returnReason,
        otherReason,
      });
      onComplete && onComplete(SuccessMessage());
    }
    const serialized = TransactionHelper.serializeTransaction(transaction);
    infoTransactionEvent({
      action: CancelFlowAction.UpdateOrderDBCancelStatus,
      transaction: {
        ...serialized,
        isCancelled: true,
        modifiedDate: now.toISOString(),
        cancelledAt: now.toISOString(),
        cancelledBy: employeeId,
        shiftIdOfCancel: currentShiftId,
        returnReason,
        otherReason,
      },
    });
  } else {
    cancelSuccess = DAL.updateTransaction(transactionId, {
      ...onlineTransaction,
      isCancelled: true,
      modifiedDate: now.toISOString(),
      cancelledAt: now.toISOString(),
      cancelledBy: employeeId,
      shiftIdOfCancel: currentShiftId,
      createdDate: onlineTransaction.createdDate ? onlineTransaction.createdDate : onlineTransaction.createdTime,
      returnReason,
      otherReason,
    });
    onComplete && onComplete(SuccessMessage());
    infoTransactionEvent({
      action: CancelFlowAction.UpdateOrderDBCancelStatus,
      transaction: {
        ...onlineTransaction,
        isCancelled: true,
        modifiedDate: now.toISOString(),
        cancelledAt: now.toISOString(),
        cancelledBy: employeeId,
        shiftIdOfCancel: currentShiftId,
        createdDate: onlineTransaction.createdDate ? onlineTransaction.createdDate : onlineTransaction.createdTime,
        returnReason,
        otherReason,
      },
    });
  }
  if (cancelSuccess) {
    yield spawn(pushLocalTransactionsSaga, [transactionId]);
    yield put(Actions.employeeCancelOrder({ transactionId }));
  }

  // Try to sync transaction data
  yield put(Actions.syncTransactionsAction({ transactionId }));
};

export const cancelBeepTransactionSaga = function* (action: Action<CancelTransactionType>) {
  const { transactionId, onlineTransaction, returnReason, otherReason, onCancelOfflineBeepOrder, onComplete } = action.payload;
  // Cancel each payments
  const employeeId = yield select(selectEmployeeId);
  const gbEnableVoidReceipt = yield select(selectGBEnableVoidReceipt);
  const birAccredited = yield select(selectBirAccredited);
  const now = new Date();
  const currentShift = DAL.getLastShift();
  const currentShiftId = get(currentShift, 'shiftId');
  const businessName = yield select(selectBusinessName);
  yield put(
    Actions.updateOnlineOrderStatus({
      orderId: onlineTransaction.receiptNumber,
      status: OnlineOrderStatus.Cancelled,
      businessName,
      employeeId,
      cancelReason: returnReason,
      otherReason,
    })
  );
  const responseAction = yield take([Actions.updateOnlineOrderStatus.toString() + '.success', Actions.updateOnlineOrderStatus.toString() + '.failure']);
  if (responseAction.type === Actions.updateOnlineOrderStatus.toString() + '.success') {
    onComplete && onComplete(SuccessMessage());
    const cancelledOrder = get(responseAction, ['payload', 'updateOrderStatus', 'order']);
    if (cancelledOrder) {
      const { voidNumber, orderId } = cancelledOrder;
      const localTransaction = DAL.getTrancationById(cancelledOrder.id);
      let saveToLocalSuccess = false;
      if (Boolean(localTransaction)) {
        const trxUpdateFields: any = {
          isCancelled: true,
          modifiedDate: now.toISOString(),
          cancelledAt: now.toISOString(),
          shiftIdOfCancel: currentShiftId,
          cancelledBy: employeeId,
          isOnlineOrder: true,
          returnReason,
          otherReason,
        };
        if (gbEnableVoidReceipt && birAccredited && isValidNumber(voidNumber)) {
          trxUpdateFields.voidNumber = voidNumber;
        }
        saveToLocalSuccess = DAL.updateTransaction(transactionId, trxUpdateFields);
      } else if (cancelledOrder.paymentMethod === 'Offline') {
        saveToLocalSuccess = DAL.updateTransaction(transactionId, {
          ...cancelledOrder,
          isCancelled: true,
          modifiedDate: now.toISOString(),
          cancelledAt: now.toISOString(),
          cancelledBy: employeeId,
          shiftIdOfCancel: currentShiftId,
          isOnlineOrder: true,
          transactionType: 'Sale',
          returnReason,
          otherReason,
        });
      }
      if (saveToLocalSuccess) {
        yield spawn(pushLocalTransactionsSaga, [transactionId]);
        yield put(Actions.employeeCancelOrder({ transactionId }));
      }

      let needRoundAmount = 0;
      const roundPaymentMethods = [];

      const payments = get(cancelledOrder, 'payments', []);
      const offlineRoundPayments = filter(payments, payment => payment.paymentMethod !== 'Voucher');

      if (offlineRoundPayments.length > 0) {
        for (const offlineRoundPayment of offlineRoundPayments) {
          needRoundAmount += offlineRoundPayment.amount;
          const { paymentMethod } = offlineRoundPayment;
          roundPaymentMethods.push(paymentMethod);
        }
      }

      const originalPayments = get(onlineTransaction, 'payments', []);
      const paymentProviders = [];
      if (isArray(originalPayments)) {
        for (const originalPayment of originalPayments) {
          const { paymentProvider } = originalPayment;
          if (!isEmpty(paymentProvider)) {
            paymentProviders.push(paymentProvider);
          }
        }
      }

      if (needRoundAmount > 0) {
        if (cancelledOrder.paymentMethod === 'Offline') {
          onCancelOfflineBeepOrder &&
            onCancelOfflineBeepOrder({ success: true, isOfflinePayment: true, refundAmount: needRoundAmount, paymentMethod: 'Cash', paymentProviders });
        } else {
          onCancelOfflineBeepOrder &&
            onCancelOfflineBeepOrder({
              success: true,
              isOfflinePayment: false,
              refundAmount: needRoundAmount,
              paymentMethod: roundPaymentMethods.join(', '),
              paymentProviders,
            });
        }
      } else {
        onCancelOfflineBeepOrder &&
          onCancelOfflineBeepOrder({
            success: true,
            isOfflinePayment: false,
            refundAmount: needRoundAmount,
            paymentMethod: roundPaymentMethods.join(', '),
            paymentProviders,
          });
      }
      if (gbEnableVoidReceipt && birAccredited) {
        const printParams: Actions.PrintReceiptType = {
          transactionId: transactionId,
          isReprint: false,
          eventName: 'printReceiptWhenCancelBeepOrder',
        };
        if (!saveToLocalSuccess) {
          printParams.onlineTransaction = { ...cancelledOrder, receiptNumber: orderId };
        }
        yield put(Actions.newPrintReceiptAction(printParams));
      }
      infoTransactionEvent({ action: CancelFlowAction.UpdateBeepOrderCancelStatus, transaction: onlineTransaction });
    } else {
      errorTransactionEvent({ action: CancelFlowAction.UpdateBeepOrderCancelStatus, transaction: onlineTransaction });
      onCancelOfflineBeepOrder && onCancelOfflineBeepOrder({ success: false });
    }
  }
  if (responseAction.type === Actions.updateOnlineOrderStatus.toString() + '.failure') {
    errorTransactionEvent({ action: CancelFlowAction.UpdateBeepOrderCancelStatus, transaction: onlineTransaction });
    onCancelOfflineBeepOrder && onCancelOfflineBeepOrder({ success: false });
    onComplete && onComplete(SuccessMessage());
  }
};

function* cancelFlowSagas() {
  yield takeLatest(Actions.cancelTransaction.toString(), cancelTransactionSaga);
}

export default fork(cancelFlowSagas);
