import * as Immutable from 'immutable';
import { filter, get, map, round } from 'lodash';
import moment from 'moment';

import { call, put, select, take } from 'redux-saga/effects';
import * as Actions from '../../../actions';
import { updateSequence } from '../../../actions';
import { t, TransactionFlowType } from '../../../constants';
import DAL from '../../../dal';
import { PurchasedItemType, TransactionType } from '../../../typings';
import { convertToNumber, getNumberValue, getUnNullValue } from '../../../utils';
import { isAfterFDIBIROrder, isOnlineBIROrder } from '../../../utils/beep';
import * as DateTimeUtils from '../../../utils/datetime';
import * as JSONUtils from '../../../utils/json';
import { SaleFlowAction, errorTransactionEvent } from '../../../utils/logComponent';
import { gstEffectiveForDate, isBirDiscountAvailable, sstEffectiveForDate } from '../../../utils/transaction';
import { isEmpty } from '../../../utils/validator';
import { selectBusinessName, selectRegisterId, selectRegisterObjectId, selectSequence, selectStore, selectStoreId, selectStoreInfo } from '../../selector';
import { updateSequenceSaga } from '../../shift/shiftFlow';

export const getTaxRateByIdWithStore = (immutableStore: Immutable.Map<string, any>, taxCode) => {
  const taxCodes = immutableStore.get('taxCodes', Immutable.List());
  const result = taxCodes.find(tax => tax.get('_id') === taxCode);

  if (!Boolean(result) || result.get('rate') === undefined || result.get('rate') === null || result.get('isDeleted')) {
    const finalTaxCode = immutableStore.get('defaultTaxCode', '');
    const matchTaxObj = taxCodes.find(tax => tax.get('_id') === finalTaxCode);
    const isVatExempted = Boolean(matchTaxObj) ? matchTaxObj.get('isVatExempted', false) : false;
    const isAmusementTax = Boolean(matchTaxObj) ? matchTaxObj.get('isAmusementTax', false) : false;
    return {
      taxCode: finalTaxCode,
      taxRate: immutableStore.get('tax'),
      taxName: '',
      isVatExempted,
      isAmusementTax,
    };
  }
  return {
    taxCode,
    taxRate: result.get('rate'),
    isVatExempted: result.get('isVatExempted', false),
    isAmusementTax: result.get('isAmusementTax', false),
    taxName: result.get('name'),
  };
};

export type TaxItemType = {
  rate: number;
  name: string;
  _id: string;
  isDeleted: boolean;
};

export const getTaxItemWithStore = (immutableStore: Immutable.Map<string, any>, taxId: string): TaxItemType | undefined => {
  const taxCodes = immutableStore.get('taxCodes', Immutable.List());
  const result = taxCodes.find(tax => tax.get('_id') === taxId);
  return Boolean(result) ? result.toJS() : undefined;
};

export const checkProductTaxRateWithBIR = (country, birAccredited, originalItem, purchasedItem, transactionSession, callback?) => {
  const { taxRate, isAmusementTax } = purchasedItem;
  if (
    country === 'PH' &&
    birAccredited &&
    ((!Boolean(isAmusementTax) && taxRate !== 0 && taxRate !== 0.12) || (Boolean(isAmusementTax) && isBirDiscountAvailable(transactionSession)))
  ) {
    let message = t('Due to BIR requirements');
    if (Boolean(isAmusementTax) && isBirDiscountAvailable(transactionSession)) {
      message = 'Cannot add product with Amusement Tax while a special discount is applied. ';
    } else if (purchasedItem.minQuantWithAppliedPriceBook) {
      const { targetCustomerType } = purchasedItem;
      message = `This #${targetCustomerType} product is unable to be added.`;
    } else if (!isEmpty(originalItem)) {
      const { targetCustomerType } = originalItem;
      message = `The quantity of this product in #${targetCustomerType || 'all customer'} is out of range.`;
    }
    callback && callback({ enable: false, message });
    return false;
  }
  callback && callback({ enable: true });
  return true;
};

export const getMessageForUpdateQuantityWithBIR = (item, newItem): string => {
  let message;
  if (newItem.minQuantWithAppliedPriceBook) {
    const { targetCustomerType } = newItem;
    message = `This #${targetCustomerType} product is unable to be added.`;
  } else {
    const { targetCustomerType } = item;
    message = `The quantity of this product in #${targetCustomerType || 'all customer'} is out of range.`;
  }
  return message;
};

export const hasProductAmusementTax = (transactionSession): boolean => {
  const items = get(transactionSession, 'items', []);
  return filter(items, item => item.isAmusementTax).length > 0;
};

export const getTaxNameById = function* (taxId) {
  const item = yield call(getTaxItemById, taxId);
  if (item !== undefined) return item.name;
  return '';
};

export const getTaxRateById = function* (taxId) {
  const immutableStore = yield select(selectStore);

  const item = yield call(getTaxItemById, taxId);
  if (item !== undefined) return item.rate;
  return immutableStore.get('tax'); // Default Tax
};

export const getTaxItemById = function* (taxId) {
  const immutableStore = yield select(selectStore);
  const taxCodes = immutableStore.get('taxCodes', Immutable.List());
  const result = taxCodes.find(tax => tax.get('_id') === taxId);
  return Boolean(result) ? result.toJS() : undefined;
};

export const generateOrderId = function* () {
  const immutableStore = yield select(selectStore);
  let lastAutoOrderId = immutableStore.get('lastAutoOrderId', null);

  if (lastAutoOrderId === null) {
    lastAutoOrderId = 0;
  } else {
    lastAutoOrderId = (lastAutoOrderId + 1) % 100;
  }
  yield put(Actions.setLastAutoOrderId(lastAutoOrderId));
  return lastAutoOrderId;
};

const getItemOptions = function (item) {
  let options = getUnNullValue(item, 'options', '');
  // avoid unexpected case
  if (options === '[]') {
    options = '';
  }
  return options;
};

export const isSamePurchasedItem = (srcItem, destItem): boolean => {
  if (!srcItem || !destItem) {
    return false;
  }

  if (srcItem.productId !== destItem.productId) {
    return false;
  }

  // origin is null current is []
  if (getItemOptions(srcItem) !== getItemOptions(destItem)) {
    return false;
  }

  return true;
};

export type CalculateItemsType = {
  transactionId: string;
  isOpen?: boolean;
  items?: (PurchasedItemType & { kitchenPrinter?: string })[];
};
export const calculateItemsToKitchenOrder = (transaction: CalculateItemsType, originalTransaction?: CalculateItemsType) => {
  const { transactionId } = transaction;
  const items = transaction.items || [];
  const originalRecord = originalTransaction || DAL.getTransactionById(transactionId);

  if (get(originalRecord, ['items', 'length'], 0) === 0) {
    if (transaction.isOpen) {
      // First time saved open order will print kitchen order for all itmes
      return { itemsDiff: map(items, item => ({ ...item, options: JSONUtils.parse(item.options, null) })), isDiffOperation: false };
    } else {
      return { itemsDiff: [], isDiffOperation: false };
    }
  }

  const itemsDiff = [];
  let isDiffOperation = false;
  const originalItems = originalRecord.items || [];

  const filtedOriginalItmes = [];
  const usedIndexArray = map(items, () => false);

  if (Boolean(originalItems) && originalItems.length > 0) {
    for (const originalItem of originalItems) {
      if (Boolean(originalItem.itemType)) {
        continue;
      }

      let findOne = false;

      for (let n = 0; n < items.length; n++) {
        const item = items[n];
        if (Boolean(item.itemType)) {
          continue;
        }

        if (
          !usedIndexArray[n] &&
          isSamePurchasedItem(originalItem, item) &&
          getUnNullValue(originalItem, 'quantity', 0) === getUnNullValue(item, 'quantity', 0)
        ) {
          findOne = true;
          usedIndexArray[n] = true;
          break;
        }
      }

      if (!findOne) {
        filtedOriginalItmes.push(originalItem);
      }
    }
  }

  const filtedCurrentItems = filter(items, (item, index) => !usedIndexArray[index] && !Boolean(get(item, 'itemType')));

  // This is current record item index
  let currentRecordItemIndex = 0;
  for (let n = 0; n < get(filtedOriginalItmes, 'length', 0); n++) {
    const originalItem = filtedOriginalItmes[n];
    if (Boolean(originalItem.itemType)) {
      continue;
    }

    let deleted = true;

    const item = filtedCurrentItems[currentRecordItemIndex];
    if (item) {
      const { title, options, notes, productId, itemChannel, quantity, unitPrice, kitchenPrinter } = item;
      const compareValue = isSamePurchasedItem(originalItem, item);

      // Search modified item
      if (compareValue) {
        const quantityDiff = quantity - originalItem.quantity;
        if (quantityDiff !== 0) {
          itemsDiff.push({
            title,
            productId,
            options: JSONUtils.parse(options, []),
            notes,
            itemChannel,
            quantity: quantityDiff,
            unitPrice,
            kitchenPrinter,
            isModified: true,
          });
          isDiffOperation = true;
        }
        deleted = false;
        currentRecordItemIndex = currentRecordItemIndex + 1;
      } else {
        isDiffOperation = true;
      }
    }

    if (deleted) {
      isDiffOperation = true;
      let kitchenPrinter = null;
      const product = DAL.getProductById(originalItem.productId);
      if (Boolean(product)) {
        kitchenPrinter = product.kitchenPrinter;
      }
      const { title, options, notes, productId, itemChannel, quantity, unitPrice } = originalItem;
      itemsDiff.push({
        title,
        productId,
        options: JSONUtils.parse(options, []),
        notes,
        itemChannel,
        quantity: 0 - quantity, // Need to assign a negative number to know this item removed
        unitPrice,
        kitchenPrinter,
        isDeleted: true,
      });
    }
  }

  // added items
  for (let i = currentRecordItemIndex; i < filtedCurrentItems.length; i++) {
    const item = filtedCurrentItems[i];

    const { title, options, notes, productId, quantity, itemChannel, unitPrice, kitchenPrinter } = item;
    itemsDiff.push({
      title,
      productId,
      options: JSONUtils.parse(options, []),
      notes,
      itemChannel,
      quantity,
      unitPrice,
      kitchenPrinter,
      isAdded: true,
    });
  }

  return { itemsDiff, isDiffOperation };
};

export const getCurSequence = function* () {
  const immutableSequence = yield select(selectSequence);
  const { invoiceNumberStart, receiptNumberStart, receiptDateStart }: Actions.SetSequenceType = immutableSequence.toJS();
  const registerId = yield select(selectRegisterObjectId);
  const lastInvoiceRecord = DAL.getMaxReceiptInvoiceSeqNumber(registerId);
  const lastRecord = DAL.getMaxReceiptSequenceInfo(registerId);
  const isLastRecordOnline = isOnlineBIROrder(lastRecord);
  const lastLocalInvoiceSeqNumber = get(lastInvoiceRecord, 'invoiceSeqNumber');
  // Deal with invoice sequence number
  let finalInvoiceNumberStart;
  if (lastLocalInvoiceSeqNumber) {
    finalInvoiceNumberStart = Math.max(invoiceNumberStart, lastLocalInvoiceSeqNumber + 1);
  } else {
    finalInvoiceNumberStart = invoiceNumberStart;
  }
  // Deal with sequence number
  const lastLocalSequenceNumber = get(lastRecord, 'sequenceNumber');
  const createdDate = get(lastRecord, 'createdDate');
  let lastLocalReceiptDateStart = get(lastRecord, 'seqNumberCreatedTime');
  if (lastRecord && !isLastRecordOnline && !lastLocalReceiptDateStart) {
    lastLocalReceiptDateStart = createdDate;
  }
  let finalReceiptNumberStart;
  let finalReceiptDateStart;
  if (lastLocalSequenceNumber) {
    if (receiptNumberStart >= lastLocalSequenceNumber + 1) {
      finalReceiptNumberStart = receiptNumberStart;
      finalReceiptDateStart = receiptDateStart;
    } else {
      finalReceiptNumberStart = lastLocalSequenceNumber + 1;
      finalReceiptDateStart = lastLocalReceiptDateStart;
    }
  } else {
    finalReceiptNumberStart = receiptNumberStart;
    finalReceiptDateStart = receiptDateStart;
  }
  return { invoiceNumberStart: finalInvoiceNumberStart, receiptNumberStart: finalReceiptNumberStart, receiptDateStart: finalReceiptDateStart };
};

export const generateReceiptNumber = function* (transaction) {
  // CM-2770 add seq number to online order
  // then the seq number is only can be generated by redux data, the lastInvoiceRecord does not make sence anymore
  const immutableStoreInfo: Immutable.Map<string, any> = yield select(selectStoreInfo);
  if (immutableStoreInfo.isEmpty()) {
    return false;
  }
  const registerSeq = immutableStoreInfo.get('registerId') - 1;
  const sequentialReceiptNumber = immutableStoreInfo.get('sequentialReceiptNumber');
  const birAccredited = immutableStoreInfo.getIn(['store', 'birAccredited']);
  const now = new Date();
  let isValid = false;
  if (sequentialReceiptNumber) {
    const { invoiceNumberStart, receiptNumberStart, receiptDateStart } = yield call(getCurSequence);
    // Deal with invoiceSeqNumber
    if (birAccredited && transaction.transactionType === TransactionFlowType.Sale) {
      // eslint-disable-next-line require-atomic-updates
      transaction.invoiceSeqNumber = invoiceNumberStart;
    }

    if (receiptNumberStart && DateTimeUtils.isBefore(receiptDateStart, now)) {
      const receiptNumber = String(registerSeq).padStart(3, '0') + String(receiptNumberStart).padStart(8, '0');
      // eslint-disable-next-line require-atomic-updates
      transaction.receiptNumber = receiptNumber;
      // eslint-disable-next-line require-atomic-updates
      transaction.sequenceNumber = receiptNumberStart;
      transaction.seqNumberCreatedTime = now.toISOString();
      isValid = true;
    } else {
      console.warn(`can not use receiptNumberStart: ${receiptNumberStart} and receiptDateStart: ${receiptDateStart}. `);
      isValid = false;
    }
  } else {
    const randownGen = Math.round(Math.random() * 10);
    const receiptNumber = String(registerSeq).padStart(3, '0') + moment(now).format('YYMMDDHHmmss') + randownGen;
    transaction.receiptNumber = receiptNumber;
    isValid = Boolean(receiptNumber);
  }

  return isValid;
};

export const generateBIRReceiptFields = function* (transaction) {
  // TODO online order包括 beep order, EC order, third part order, 如何判定？
  const immutableStoreInfo = yield select(selectStoreInfo);
  const sequentialReceiptNumber = immutableStoreInfo.get('sequentialReceiptNumber');
  const birAccredited = immutableStoreInfo.getIn(['store', 'birAccredited']);
  if (sequentialReceiptNumber && birAccredited) {
    const isOnlineBIR = isOnlineBIROrder(transaction);
    // CM-2770 add seq number to online order
    // then the seq number is only can be generated by redux data, the lastInvoiceRecord does not make sence anymore
    const registerId = yield select(selectRegisterId);
    const now = new Date();
    // if online and no sequenceNumber
    if (isOnlineBIR && isEmpty(transaction.sequenceNumber)) {
      // online order
      const immutableSequence = yield call(getCurSequence);
      const { invoiceNumberStart, receiptNumberStart, receiptDateStart }: Actions.SetSequenceType = immutableSequence;
      if (DateTimeUtils.isBefore(receiptDateStart, now)) {
        const { receiptNumber } = transaction;
        const businessName = yield select(selectBusinessName);
        const registerObjectId = yield select(selectRegisterObjectId);
        const storeId = yield select(selectStoreId);
        yield put(
          Actions.generateSeqNumberForOnlineOrder({
            receiptNumber,
            businessName,
            registerId: registerObjectId,
            registerNumber: Number(registerId),
            invoiceSeqNumber: invoiceNumberStart,
            sequenceNumber: receiptNumberStart,
            storeId,
          })
        );
        const responseAction = yield take([
          Actions.generateSeqNumberForOnlineOrder.toString() + '.success',
          Actions.generateSeqNumberForOnlineOrder.toString() + '.failure',
        ]);
        const resp = responseAction.payload;
        if (responseAction.type === Actions.generateSeqNumberForOnlineOrder.toString() + '.success') {
          // online generate seq Number success
          const onlineTransaction = get(resp, 'generateInvoiceSequenceNumber', {});
          const { invoiceSeqNumber, sequenceNumber, seqNumberCreatedTime, registerNumber } = onlineTransaction;
          // update invoiceNumberStart, receiptNumberStart, receiptDateStart to redux
          yield call(
            updateSequenceSaga,
            updateSequence({
              receiptNumberStart: sequenceNumber,
              invoiceNumberStart: invoiceSeqNumber,
              receiptDateStart: seqNumberCreatedTime,
              minimumSequence: { invoiceNumberStart: Number(invoiceNumberStart | 0) + 1, receiptNumberStart: Number(receiptNumberStart | 0) + 1 },
              isStart: false,
              transaction,
              from: 'generateSeqNumberForOnlineOrder',
            })
          );
          transaction.invoiceSeqNumber = Number(invoiceSeqNumber);
          transaction.sequenceNumber = Number(sequenceNumber);
          transaction.seqNumberCreatedTime = invoiceSeqNumber;
          transaction.registerNumber = registerNumber;
          transaction.registerId = onlineTransaction.registerId;
        } else {
          // faild
          const errMsg = get(resp, 'message');
          const logmsg = `from: ${responseAction.type} \n errorMessage:${errMsg} \n generate seq number info: curReceiptNumberStart is ${receiptNumberStart} AND curInvoiceNumberStart ${invoiceNumberStart} \n`;
          errorTransactionEvent({ action: SaleFlowAction.GenerateOnlineInvoiceSequenceNumber, orderId: receiptNumber, transaction, reason: logmsg });
        }
      } else {
        console.warn(`can not use receiptNumberStart: ${receiptNumberStart} and receiptDateStart: ${receiptDateStart}. `);
      }
    }
    const registerNumber = get(transaction, 'registerNumber', registerId);
    // generate birReceiptId and SI/OR
    const { invoiceSeqNumber, sequenceNumber, transactionType, voidNumber } = transaction;
    if (Boolean(invoiceSeqNumber) && Boolean(registerNumber)) {
      // offline: only sale
      // generate SI/OR
      const birReceiptId = String(registerNumber - 1).padStart(3, '0') + String(invoiceSeqNumber).padStart(8, '0');
      transaction.birReceiptId = birReceiptId;
    }

    if (Boolean(voidNumber)) {
      // offline: only cancel order
      // generate Void #
      const birVoidId = voidNumber;
      transaction.birVoidId = birVoidId;
    }

    if (Boolean(sequenceNumber)) {
      // offline: only sale or refund, no preorder
      // if offline order,should be receipt number, if online order ,should be generated by register number and seq number
      if (isOnlineBIR) {
        if (Boolean(registerNumber)) {
          const officialReceiptNumber = String(registerNumber - 1).padStart(3, '0') + String(sequenceNumber).padStart(8, '0');
          transaction.officialReceiptNumber = officialReceiptNumber;
        }
      } else {
        transaction.officialReceiptNumber = transaction.receiptNumber;
      }
    }
    let isBIROfficialReceipt = false;
    if (transactionType === TransactionFlowType.Sale) {
      // when the trasaction type is sale ,  and both sequenceNumber and invoiceSeqNumber are generated
      isBIROfficialReceipt = !isEmpty(transaction.officialReceiptNumber) && !isEmpty(transaction.birReceiptId);
    } else {
      // when the trasaction type is refund , and the sequenceNumber are generated
      isBIROfficialReceipt = !isEmpty(transaction.officialReceiptNumber);
    }

    transaction.isBIROfficialReceipt = isBIROfficialReceipt;
    transaction.needPrintOrderId = !isBIROfficialReceipt || isOnlineBIR;
  }
};

export function getTaxSummaryPerTaxCode(payload) {
  const transaction: TransactionType = payload;
  const taxes = {};
  let indicator = '*';

  for (let i = 0; i < get(transaction, ['items', 'length'], 0); i++) {
    const item = get(transaction, ['items', i]);
    const taxCode = item.taxCode;
    if (taxCode != null && taxCode.length > 0) {
      // The value of every taxCode in taxes dictionary is an array,
      // with array[0] stores the corresponding indicator of the tax code,
      // array[1] stores the total quantity of the corresponding items,
      // array[2] stores the total amount payable excluding tax of the corresponding items,
      // array[3] stores the total tax of the corresponding items,
      // array[4] stores the total tax exempt amount if available.
      if (taxes[taxCode]) {
        taxes[taxCode][1] += item.quantity;
        taxes[taxCode][2] += convertToNumber(item.taxableAmount) !== 0 ? item.taxableAmount : item.total;
        taxes[taxCode][3] += item.tax;
        taxes[taxCode][4] += convertToNumber(item.taxExemptAmount);
      } else {
        taxes[taxCode] = [
          indicator,
          item.quantity,
          convertToNumber(item.taxableAmount) !== 0 ? item.taxableAmount : item.total,
          item.tax,
          convertToNumber(item.taxExemptAmount),
        ];
        indicator += '*';
      }
    }
  }
  return taxes;
}

export function* generateSSTInfo(transaction: TransactionType) {
  const immutableStore = yield select(selectStore);
  const aggregatedTax = yield call(getTaxSummaryPerTaxCode, transaction);

  const country = immutableStore.get('country');
  const gstIdNo = immutableStore.get('gstIdNo');
  const sstIdNo = immutableStore.get('sstIdNo');
  const createdDate = get(transaction, 'createdDate');
  const isTheOrderAfterFDIBIR = isAfterFDIBIROrder(transaction);

  const taxSummary = [];
  const gstEffective = gstEffectiveForDate(country, gstIdNo, createdDate);
  const sstEffective = sstEffectiveForDate(country, sstIdNo, createdDate);
  for (const key of Object.keys(aggregatedTax)) {
    const _item = aggregatedTax[key];
    let prefix = '';

    if (gstEffective || sstEffective) {
      prefix = _item[0];
    }
    const taxName = yield call(getTaxNameById, key);
    const taxRate = yield call(getTaxRateById, key);
    const title = `${prefix}${taxName} @ ${round(taxRate * 100, 1).toFixed(1)}%`;
    taxSummary.push({
      title,
      amount: _item[2],
      tax: isTheOrderAfterFDIBIR ? get(transaction, 'tax', 0) : _item[3],
    });
  }
  return {
    aggregatedTax,
    gstEffective,
    sstEffective,
    taxSummary,
  };
}

export const getServiceChargeTaxRateWithStore = (immutableStore: Immutable.Map<string, any>, serviceChargeTax: string): number => {
  let taxRate = 0;
  const birAccredited = immutableStore.get('birAccredited');
  const country = immutableStore.get('country');
  if (country === 'PH' && birAccredited) {
    return 0;
  }
  const _taxItem = getTaxItemWithStore(immutableStore, serviceChargeTax);

  if (_taxItem !== undefined && !_taxItem.isDeleted) {
    taxRate = _taxItem.rate;
  }
  return taxRate;
};

export const generateFDIReceiptTransaction = (transaction, country) => {
  const items = get(transaction, 'items', []);
  map(items, item => {
    const price = getNumberValue(item, 'unitPrice', 0.0);
    const qty = getNumberValue(item, 'quantity', 0.0);
    const total = getNumberValue(item, 'total', 0.0);
    const discount = getNumberValue(item, 'discount', 0.0);
    item.receipt = { price, qty, total, discount };
  });
  const receipt = get(transaction, 'receipt', {});
  const tax = get(transaction, 'tax', 0);
  transaction.receipt = {
    ...receipt,
    vatAmount: tax,
  };
  return transaction;
};

export const stringifyItemOptions = options => {
  let result = null;
  if (options) {
    if (typeof options === 'string') {
      if (options.length > 0) {
        result = options;
      }
    } else {
      result = JSONUtils.stringify(options);
    }
  }
  return result;
};
