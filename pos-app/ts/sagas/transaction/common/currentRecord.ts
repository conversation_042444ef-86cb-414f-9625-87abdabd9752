import { floor, get, head, map } from 'lodash';
import { call, select } from 'redux-saga/effects';
import { selectEmployeeId, selectRegisterId, selectRegisterObjectId } from '../../selector';

import { ItemChannelType, PromotionDiscountType, TransactionFlowType } from '../../../constants';
import DAL from '../../../dal';
import { PurchasedItemType } from '../../../typings';
import { isValidNumber } from '../../../utils';
import { isLocalPreOrder } from '../../../utils/transaction';

import { getKdsInfoFromItem, tryInitKDSRecordSaga } from '../../kds/transaction/helper';

/**
 * transform redux transactionSession to the database schema data.
 * only checkout can invoke
 */
export const getCurrentRecord = function* (currentRecord) {
  const employeeId = yield select(selectEmployeeId);
  const items = [];
  let index = 0;
  let realServiceChargeRate;
  for (const item of currentRecord.items) {
    let newItem: PurchasedItemType;

    // No need to save 'discount' type item
    if (item.itemType === 'Discount') {
      if (currentRecord.transactionType != TransactionFlowType.PreOrder || !item.isDiscountEnable) {
        continue;
      }
      // @ts-ignore
      newItem = {
        itemType: item.itemType,
        discountInputValue: item.discountInputValue,
        discountType: item.discountType,
        isDiscountEnable: item.isDiscountEnable,
        taxRate: item.taxRate,
      };
    } else if (item.itemType === 'ServiceCharge') {
      // @ts-ignore
      newItem = {
        itemType: item.itemType,
        subTotal: item.subTotal,
        discount: item.discount,
        tax: item.tax,
        taxRate: item.taxRate,
        taxCode: item.taxCode,
        rate: item.rate,
        total: item.total,
        quantity: 1,
        unitPrice: item.unitPrice,
        pwdDiscount: item.pwdDiscount,
        seniorDiscount: item.seniorDiscount,
        taxableAmount: item.taxableAmount,
        taxExemptAmount: item.taxExemptAmount,
        adhocDiscount: item.adhocDiscount,
        zeroRatedSales: item.zeroRatedSales,
        totalDeductedTax: item.totalDeductedTax,
      };
      realServiceChargeRate = item.rate;
    } else {
      const options = [];
      if (Boolean(item.options) && item.options.length > 0) {
        for (const option of item.options) {
          // remove priceDiff when save the transaction
          const { priceDiff, ...remain } = option as any;
          options.push(remain);
        }
      }

      // Calculate promotion quantity
      const promotions = map(item.promotions, promotion => {
        let quantity = Boolean(item.promotionAppliedQuantityMap) ? item.promotionAppliedQuantityMap[promotion.promotionId] || 1 : 1;
        if (promotion.discountType === PromotionDiscountType.BuyXFreeY) {
          const realmPromotion = DAL.getPromotionById(promotion.promotionId);
          const condition = head(realmPromotion.conditions);
          const minQuantity = get(condition, 'minQuantity', 1);
          const discountValue = get(condition, 'discountValue', 1) === 0 ? 1 : get(condition, 'discountValue', 1);
          quantity = floor((quantity / minQuantity) * discountValue);
        }
        if (promotion.discountType === PromotionDiscountType.Percentage || promotion.discountType === PromotionDiscountType.Absolute) {
          quantity = 1;
        }
        promotion.quantity = quantity;
        return promotion;
      });
      const kdsInfo = getKdsInfoFromItem(item);
      // @ts-ignore
      newItem = {
        id: item.id,
        submitId: item.submitId,
        productId: item.productId,
        unitPrice: item.unitPrice,
        options: options.length === 0 ? '' : JSON.stringify(options),
        quantity: item.quantity,
        subTotal: item.subTotal,
        total: item.total,
        title: item.title,
        notes: item.notes,
        discount: item.discount,
        tax: item.tax,
        isVatExempted: item.isVatExempted,
        isBasicNecessitiesPH: item.isBasicNecessitiesPH,
        isSoloParentDiscountApplicable: item.isSoloParentDiscountApplicable,
        loyaltyDiscount: item.loyaltyDiscount || 0,
        taxRate: item.taxRate,
        itemType: item.itemType,
        taxCode: item.taxCode || '',
        adhocDiscount: item.adhocDiscount,
        sn: item.sn,
        promotions,
        isServiceChargeNotApplicable: item.isServiceChargeNotApplicable,
        trackInventory: item.trackInventory,
        discountType: item.discountType,
        discountInputValue: item.discountInputValue,
        seniorDiscount: item.seniorDiscount,
        pwdDiscount: item.pwdDiscount,
        taxableAmount: item.taxableAmount,
        taxExemptAmount: item.taxExemptAmount,
        zeroRatedSales: item.zeroRatedSales,
        totalDeductedTax: item.totalDeductedTax,
        athleteAndCoachDiscount: item.athleteAndCoachDiscount,
        soloParentDiscount: item.soloParentDiscount,
        medalOfValorDiscount: item.medalOfValorDiscount,
        takeawayCharges: item.takeawayCharges,
        itemChannel: item.itemChannel || ItemChannelType.DEFAULT,
        isTakeaway: item.isTakeaway,
        takeawayCharge: item.takeawayCharge,
        cookingStatus: item.cookingStatus,
        pendingDate: item.pendingDate,
        preparedDate: item.preparedDate,
        servedDate: item.servedDate,
        calculation: item.calculation,
        kitchenStation: item.kitchenStation,
        employeeId: item.employeeId,
        employeeName: item.employeeName,
        ...kdsInfo,
      };
      if (newItem.calculation && !newItem.calculation.taxes) {
        newItem.calculation.taxes = [];
      }
    }
    newItem.orderingValue = index++;
    items.push(newItem);
  }

  currentRecord.items = items;
  const now = new Date();

  currentRecord.createdDate = now.toISOString();
  currentRecord.employeeId = employeeId;
  currentRecord.modifiedDate = now.toISOString();
  currentRecord.registerId = yield select(selectRegisterObjectId);
  const registerNumber = yield select(selectRegisterId);
  if (isValidNumber(realServiceChargeRate)) {
    currentRecord.serviceChargeRate = realServiceChargeRate;
  }
  if (isValidNumber(registerNumber)) {
    currentRecord.registerNumber = registerNumber;
  }
  const currentShift = DAL.getLastShift();
  const currentShiftId = get(currentShift, 'shiftId');
  if (isLocalPreOrder(currentRecord)) {
    currentRecord.preOrderDate = now.toISOString();
    currentRecord.shiftIdOfPreOrder = currentShiftId;
  } else {
    currentRecord.shiftId = currentShiftId;
  }

  currentRecord.promotions = map(currentRecord.promotions, promotion => {
    promotion.quantity = 1;
    return promotion;
  });
  yield call(tryInitKDSRecordSaga, currentRecord);
  return currentRecord;
};
