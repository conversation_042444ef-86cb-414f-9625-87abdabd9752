import { get } from 'lodash';
import { LoyaltyType } from '../../../constants';
import { isBirDiscountAvailable } from '../../../utils/transaction';

export const checkItemsDiffTax = function (currentRecord, enableCashback, enableLoyalty) {
  const items = currentRecord.items || [];
  let discountIndex = -1;
  for (let i = 0; i < items.length; i++) {
    if (items[i].itemType === 'Discount') {
      discountIndex = i;
      break;
    }
  }

  let isDiscountCanEnable = true;
  let isLoyaltyCanEnable = true;
  const birDiscountAvailable = isBirDiscountAvailable(currentRecord);
  for (let i = 0; i < items.length; i++) {
    const item = items[i];
    if (item.itemType !== 'Discount' && item.itemType !== 'ServiceCharge') {
      // if (lastTaxRate === undefined) {
      //   lastTaxRate = item.taxRate;
      // } else if (lastTaxRate !== item.taxRate) {
      //   isDiscountCanEnable = false;
      //   isLoyaltyCanEnable = false;
      //   break;
      // } else {
      //   lastTaxRate = item.taxRate;
      // }

      if (birDiscountAvailable) {
        if (item.isDiscountEnable && Boolean(item.itemLevelDiscount)) {
          item.isDiscountEnableBeforeBIR = item.isDiscountEnable;
          item.itemLevelDiscountBeforeBIR = item.itemLevelDiscount;
          item.isDiscountEnable = false;
          delete item.itemLevelDiscount;
        }
      } else {
        if (Boolean(item.itemLevelDiscountBeforeBIR)) {
          item.isDiscountEnable = item.isDiscountEnableBeforeBIR;
          item.itemLevelDiscount = item.itemLevelDiscountBeforeBIR;
          delete item.isDiscountEnableBeforeBIR;
          delete item.itemLevelDiscountBeforeBIR;
        }
      }

      if (birDiscountAvailable) {
        delete item.promotions;
        delete item.promotionAppliedQuantityMap;
      }
    }
  }

  if (birDiscountAvailable) delete currentRecord.promotions;

  if (birDiscountAvailable) isLoyaltyCanEnable = false;
  const { customer, isLoyaltyEnable } = currentRecord;
  if (enableLoyalty) {
    // currentRecord.isLoyaltyEnable = isLoyaltyEnable && isLoyaltyCanEnable;
    const type = enableCashback ? LoyaltyType.CASHBACK : LoyaltyType.STORECREDIT;
    if (!isLoyaltyCanEnable) {
      delete currentRecord.loyaltyDiscounts;
    } else if (isLoyaltyEnable) {
      const originalLoyaltyDiscounts = get(currentRecord, ['loyaltyDiscounts', 0]);
      let inputValue = get(customer, 'loyalty', 0);
      if (originalLoyaltyDiscounts && originalLoyaltyDiscounts.loyaltyType === LoyaltyType.STORECREDIT && type === LoyaltyType.STORECREDIT) {
        inputValue = Math.min(get(originalLoyaltyDiscounts, 'inputValue', 0), get(customer, 'loyalty', 0));
      }
      currentRecord.loyaltyDiscounts = [
        {
          type,
          loyaltyType: type,
          inputValue,
        },
      ];
    }
  }
  if (birDiscountAvailable) isDiscountCanEnable = false;
  if (discountIndex > -1) {
    const discountPurchasedItem = items[discountIndex];
    const { displayFullBillDiscountEnable, displayFullBillDiscountValue } = discountPurchasedItem;
    discountPurchasedItem.isDiscountEnable = displayFullBillDiscountEnable && isDiscountCanEnable;
    discountPurchasedItem.inputValue = discountPurchasedItem.isDiscountEnable ? displayFullBillDiscountValue : 0;
    // if (Boolean(discountPurchasedItem.taxRate)) {
    // if (isDiscountCanEnable) {
    //   discountPurchasedItem.taxRate = lastTaxRate;
    // } else {
    //   discountPurchasedItem.taxRate = null;
    // }
    // }
  }
  currentRecord.isFullBillDiscountCanEnable = isDiscountCanEnable;
  currentRecord.isLoyaltyCanEnable = isLoyaltyCanEnable;
};
