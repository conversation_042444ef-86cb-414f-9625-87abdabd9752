import { filter, get } from 'lodash';
import { Action } from 'redux-actions';
import { call, fork, put, select, take, takeLatest } from 'redux-saga/effects';
import * as Actions from '../../actions';
import { autoApplyCustomer, checkCustomerInfoFromQR, CustomerType, getUniquePromos } from '../../actions';
import { updateCfdMembership } from '../../actions/cfd';
import {
  BIRDiscountType,
  BIRStoreType,
  BIRType,
  FAB_SCPWD_DISCOUNT_RATE,
  Retail_SCPWD_DISCOUNT_RATE,
  SalesChannelType,
  TransactionFlowType,
} from '../../constants';
import { getCurrentRouteName, getNavigatorRef } from '../../navigation/navigatorService';
import { isValidNumber } from '../../utils';
import globalConfig from '../../utils/globalConfig';
import { CustomerAction, errorTransactionEvent, logSucceedServerRequest, LoyaltyFlowAction, WorkflowStatus } from '../../utils/logComponent';
import { getDisplayItemsCount, isBirDiscountAvailable, isPreOrderPickUp } from '../../utils/transaction';
import { isEmpty } from '../../utils/validator';
import {
  selectBusinessName,
  selectEnableCashback,
  selectEnableLoyalty,
  selectGBCfdBeepMembershipCashback,
  selectIncludingTaxInDisplay,
  selectIndustry,
  selectMembershipEnabled,
  selectStore,
} from '../selector';
import {
  checkItemsDiffTax,
  conversionDiscountInLoyaltyDiscounts,
  getAppliedPriceBooks,
  getCurrentTransaction,
  tryApplyPromotion,
  updateTransactionWithAppliedPriceBooks,
} from './common';
import { clearUniquePromosSaga } from './promotion';
import calculateP from './common/calculator';

const disableLoyaltyFromTransaction = currentRecord => {
  delete currentRecord.loyaltyDiscounts;
  currentRecord.isLoyaltyEnable = false;
  for (let index = 0; index < currentRecord.items.length; index++) {
    delete currentRecord.items[index].loyaltyDiscount;
    delete currentRecord.items[index].loyaltyDiscountInfo;
  }
};

export const initBIRDiscountFields = function* (currentRecord) {
  const seniorsCount = 0;
  const pwdCount = 0;
  const headCount = 1;
  const industry = yield select(selectIndustry);
  const isRetail = industry == BIRStoreType.RetailStore;
  const birType = isRetail ? BIRType.Retail : BIRType.FAB;
  currentRecord.birInfo = {
    type: birType,
    discountRate: 0,
    seniorsCount, // If there's pwdCount, able to be 0
    pwdCount, // If there's seniorsCount, able to be 0
    headCount, // at least 1
  };
  currentRecord.headcount = headCount;
  currentRecord.seniorsCount = seniorsCount;
  currentRecord.pwdCount = pwdCount;
  currentRecord.addonBirCompliance = null;
};

export const removeBIRDiscountSaga = function* () {
  let currentRecord = yield call(getCurrentTransaction);
  yield call(initBIRDiscountFields, currentRecord);
  if (getDisplayItemsCount(currentRecord.items) > 0) {
    const enableCashback = yield select(selectEnableCashback);
    const enableLoyalty = yield select(selectEnableLoyalty);
    yield call(checkItemsDiffTax, currentRecord, enableCashback, enableLoyalty);
    yield call(tryApplyPromotion, currentRecord);
    try {
      const includingTaxInDisplay = yield select(selectIncludingTaxInDisplay);
      currentRecord = calculateP(currentRecord, includingTaxInDisplay || false);
      conversionDiscountInLoyaltyDiscounts(currentRecord);
    } catch (exception) {
      console.log('deleteCustomerFromTransactionSaga exception', exception);
      errorTransactionEvent({
        action: LoyaltyFlowAction.RemoveBIRDiscountToTransaction,
        reason: 'calculate exception',
        exception,
        transaction: currentRecord,
      });
    }
  }
  yield put(Actions.setTransactionSession(currentRecord));
  yield put(Actions.warnToRemoveUniquePromo());
};

export const addBIRDiscountToTransactionSaga = function* (action) {
  const { discountType, collectedInfo } = action.payload;
  const headCount = get(action.payload, 'headCount', 1);
  const seniorsCount = get(action.payload, 'seniorsCount', 0);
  const pwdCount = get(action.payload, 'pwdCount', 0);
  let currentRecord = yield call(getCurrentTransaction);
  currentRecord.headcount = headCount;
  currentRecord.seniorsCount = seniorsCount;
  currentRecord.pwdCount = pwdCount;
  const transactionId = get(currentRecord, 'transactionId');
  const industry = yield select(selectIndustry);
  const isRetail = industry == BIRStoreType.RetailStore;
  const birType = isRetail ? BIRType.Retail : BIRType.FAB;

  switch (discountType) {
    // 20% discount, no tax
    case BIRDiscountType.SCAndPWD:
      {
        currentRecord.birInfo = {
          type: birType,
          discountType,
          discountRate: birType == BIRType.FAB ? FAB_SCPWD_DISCOUNT_RATE : Retail_SCPWD_DISCOUNT_RATE,
          seniorsCount, // If there's pwdCount, able to be 0
          pwdCount, // If there's seniorsCount, able to be 0
          headCount, // at least 1
        };
      }
      break;
    // 10% discount and no tax
    case BIRDiscountType.SoloParent:
      {
        currentRecord.birInfo = {
          type: birType,
          discountType,
          discountRate: 0.1, // default to 0.1
          seniorsCount, // If there's pwdCount, able to be 0
          pwdCount, // If there's seniorsCount, able to be 0
          headCount, // at least 1
          collectedInfo,
        };
      }
      break;
    // 20% discount only
    case BIRDiscountType.AthletesAndCoaches:
    case BIRDiscountType.MedalofValor:
      {
        currentRecord.birInfo = {
          type: birType,
          discountType,
          discountRate: 0.2, // default to 0.2
          seniorsCount, // If there's pwdCount, able to be 0
          pwdCount, // If there's seniorsCount, able to be 0
          headCount, // at least 1
          collectedInfo,
        };
      }
      break;
    // no tax only
    case BIRDiscountType.Diplomats:
      {
        currentRecord.birInfo = {
          type: birType,
          discountType,
          discountRate: 0, // default to 0
          seniorsCount, // If there's pwdCount, able to be 0
          pwdCount, // If there's seniorsCount, able to be 0
          headCount, // at least 1
          collectedInfo,
        };
      }
      break;

    default:
      return;
  }
  const enableCashback = yield select(selectEnableCashback);
  const enableLoyalty = yield select(selectEnableLoyalty);
  yield call(checkItemsDiffTax, currentRecord, enableCashback, enableLoyalty);
  const includingTaxInDisplay = yield select(selectIncludingTaxInDisplay);
  try {
    currentRecord = calculateP(currentRecord, includingTaxInDisplay || false);
    conversionDiscountInLoyaltyDiscounts(currentRecord);
  } catch (exception) {
    console.log('calculate exception', exception);
    errorTransactionEvent({
      action: LoyaltyFlowAction.AddBIRDiscountToTransaction,
      reason: 'calculate exception',
      exception,
      transaction: currentRecord,
    });
  }
  yield put(Actions.setTransactionSession(currentRecord));
  yield put(Actions.warnToRemoveUniquePromo());
  yield put(Actions.employeeApplyManualDiscount({ transactionId }));
};

type checkCustomerWithBIRType = {
  enable: boolean;
  message?: string;
};

type AddCustomerToRecordType = {
  currentRecord: any;
  immutableStore: any;
  appliedPriceBooks: any;
  customer: CustomerType;
  customerId: string;
  checkCustomerWithBIR?(res: checkCustomerWithBIRType): void;
};

// will return displayItemsCount and success
// displayItemsCount: the count of item on transaction
// success: return false on add customer to transaction failure and true on success
// Adding Customer may cause the tax rate of the item to change, and failure may be added when BIR is turned on
// TODO When the transaction has a fullBillDiscount?
export const addCustomerToRecord = function* (params: AddCustomerToRecordType) {
  const { currentRecord, immutableStore, appliedPriceBooks, customer, customerId, checkCustomerWithBIR } = params;
  const country = immutableStore.get('country');
  const birAccredited = immutableStore.get('birAccredited');
  const newItems = yield call(updateTransactionWithAppliedPriceBooks, {
    transaction: currentRecord,
    appliedPriceBooks,
  });
  if (country === 'PH' && birAccredited) {
    if (newItems.findIndex(item => !Boolean(item.itemType) && !Boolean(item.isAmusementTax) && item.taxRate !== 0 && item.taxRate !== 0.12) !== -1) {
      const cannotAppliedProductNames = [];
      newItems.map(item => {
        if (!Boolean(item.itemType) && !Boolean(item.isAmusementTax) && item.taxRate !== 0 && item.taxRate !== 0.12) {
          cannotAppliedProductNames.push(`\n· ${item.title} - ${item.taxRate}`);
        }
      });
      const message = `These products in the order do not meet the BIR tax requirements of 0% or 12%: ${cannotAppliedProductNames.join()}`;
      errorTransactionEvent({
        action: LoyaltyFlowAction.AddCustomerToTransaction,
        reason: message,
        transaction: currentRecord,
      });
      checkCustomerWithBIR && checkCustomerWithBIR({ enable: false, message });
      // if (currentRecord.customer && currentRecord.customerId) {
      //   yield put(Actions.deleteCustomerFromTransaction());
      // }
      return { displayItemsCount: 0, success: false };
    }
  }
  checkCustomerWithBIR && checkCustomerWithBIR({ enable: true });
  /* eslint-disable require-atomic-updates */
  currentRecord.customer = customer;
  currentRecord.customerId = customerId;
  currentRecord.appliedPriceBooks = appliedPriceBooks;
  currentRecord.items = newItems;
  /* eslint-enable */
  const enableLoyalty = immutableStore.get('enableLoyalty');
  const enableCashback = immutableStore.get('enableCashback');
  yield call(checkItemsDiffTax, currentRecord, enableCashback, enableLoyalty);
  const displayItemsCount = getDisplayItemsCount(newItems);
  if (displayItemsCount > 0) {
    yield call(tryApplyPromotion, currentRecord);
  }
  return { displayItemsCount, success: true };
};

export const addCustomerToTransactionSaga = function* (action) {
  const { customerId, customer, checkCustomerWithBIR, transaction, isInCheckOut } = action.payload;
  let currentRecord = transaction;
  if (!currentRecord) {
    currentRecord = yield call(getCurrentTransaction);
  }
  const isOnlineOrder = get(currentRecord, 'isOnlineOrder', false);
  const immutableStore = yield select(selectStore);
  const includingTaxInDisplay = immutableStore.get('includingTaxInDisplay');
  const appliedPriceBooks = yield call(getAppliedPriceBooks, customer);
  const { displayItemsCount, success } = yield call(addCustomerToRecord, {
    currentRecord,
    immutableStore,
    appliedPriceBooks,
    customer,
    customerId,
    checkCustomerWithBIR,
  });
  if (!success) return;

  if (displayItemsCount > 0) {
    try {
      currentRecord = calculateP(currentRecord, includingTaxInDisplay || false);
      conversionDiscountInLoyaltyDiscounts(currentRecord);
    } catch (exception) {
      console.log('addCustomerToTransactionSaga exception', exception);
      errorTransactionEvent({
        action: LoyaltyFlowAction.AddCustomerToTransaction,
        reason: 'calculate exception',
        exception,
        transaction: currentRecord,
      });
    }
  }
  yield put(Actions.setTransactionSession(currentRecord));

  if (!isInCheckOut && !isOnlineOrder) {
    yield call(fetchUniquePromosSaga, { selectedUniquePromoId: null });
  }
  const enableCashback = yield select(selectEnableCashback);
  const enableBeepMembership = yield select(selectMembershipEnabled);
  const enableCfdMembership = yield select(selectGBCfdBeepMembershipCashback);

  if ((enableCashback || enableBeepMembership) && enableCfdMembership) {
    yield put(updateCfdMembership(null));
    const businessName = yield select(selectBusinessName);
    yield put(
      Actions.getCustomer({
        business: businessName,
        customerId,
        needPointsBalance: true,
        needPointsTotalSpend: true,
        needMembershipDetail: true,
      })
    );
    const responseAction = yield take([Actions.getCustomer.toString() + '.success', Actions.getCustomer.toString() + '.failure']);
    if (responseAction.type === Actions.getCustomer.toString() + '.success') {
      if (responseAction.payload && responseAction.payload.customer) {
        yield put(
          updateCfdMembership({
            customer: {
              ...customer,
              ...responseAction.payload.customer,
            },
            cashbackSummary: null,
            qrUrl: null,
            checkout: false,
          })
        );
        console.log('getCustomer success, response', responseAction.payload);
      } else {
        console.log('getCustomer empty, response', responseAction.payload);
      }
    } else {
      console.log('getCustomer error, response', responseAction.payload);
    }
  }

  if (isInCheckOut && !isOnlineOrder) {
    yield call(clearUniquePromosSaga);
  }
  yield put(Actions.stopCheckCustomerInfoFromQR({}));
};

export const fetchUniquePromosSaga = function* (action) {
  const selectedUniquePromoId = action?.payload?.selectedUniquePromoId ?? null;

  const businessName = yield select(selectBusinessName);
  const navigator = getNavigatorRef();

  if (!navigator) return;

  const currentRecord = yield call(getCurrentTransaction);
  const customerId = currentRecord.customerId;

  if (!customerId) return;

  yield put(Actions.getUniquePromos({ businessName, customerId }));
  const responseAction = yield take([getUniquePromos.toString() + '.success', getUniquePromos.toString() + '.failure']);

  if (responseAction.type === getUniquePromos.toString() + '.success') {
    const data = responseAction.payload;
    const uniquePromos = get(data, 'uniquePromos', []);
    console.log('data.uniquePromos', uniquePromos);

    if (Array.isArray(uniquePromos) && uniquePromos.length > 0) {
      navigator.navigate('ModalSelectUniquePromo', {
        promoData: uniquePromos,
        selectedUniquePromoId,
      });
      yield put(Actions.setUniquePromos(uniquePromos));
    } else {
      yield put(Actions.clearUniquePromos());
    }
  } else {
    console.log('error, response', responseAction.payload);
    yield put(Actions.clearUniquePromos());
  }
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const deleteCustomerFromTransactionSaga = function* (action) {
  const { checkCustomerWithBIR } = action.payload;
  let currentRecord = yield call(getCurrentTransaction);
  const includingTaxInDisplay = yield select(selectIncludingTaxInDisplay);
  const immutableStore = yield select(selectStore);
  const country = immutableStore.get('country');
  const birAccredited = immutableStore.get('birAccredited');
  const appliedPriceBooks = yield call(getAppliedPriceBooks, {});

  const newItems = yield call(updateTransactionWithAppliedPriceBooks, {
    transaction: currentRecord,
    appliedPriceBooks,
  });

  if (country === 'PH' && birAccredited) {
    const disabledIndex = newItems.findIndex(item => !Boolean(item.itemType) && !Boolean(item.isAmusementTax) && item.taxRate !== 0 && item.taxRate !== 0.12);
    if (disabledIndex !== -1) {
      const targetCustomerType = currentRecord.items[disabledIndex].targetCustomerType;
      const message = `This #${targetCustomerType} customer is unable to unlink.`;
      errorTransactionEvent({
        action: LoyaltyFlowAction.DeleteCustomerFromTransaction,
        reason: message,
        transaction: currentRecord,
      });
      checkCustomerWithBIR && checkCustomerWithBIR({ enable: false, message });
      return;
    }
  }
  checkCustomerWithBIR && checkCustomerWithBIR({ enable: true });

  // TODO: customerId = null
  currentRecord.customer = undefined;
  currentRecord.customerId = undefined;
  currentRecord.appliedPriceBooks = appliedPriceBooks;
  currentRecord.items = newItems;
  delete currentRecord.loyaltyDiscounts;

  if (getDisplayItemsCount(newItems) > 0) {
    yield call(tryApplyPromotion, currentRecord);
    try {
      currentRecord = calculateP(currentRecord, includingTaxInDisplay || false);
      conversionDiscountInLoyaltyDiscounts(currentRecord);
    } catch (exception) {
      console.log('deleteCustomerFromTransactionSaga exception', exception);
      errorTransactionEvent({
        action: LoyaltyFlowAction.DeleteCustomerFromTransaction,
        reason: 'calculate exception',
        exception,
        transaction: currentRecord,
      });
    }
  }
  yield put(Actions.setTransactionSession(currentRecord));

  yield put(Actions.clearUniquePromos());

  const enableCashback = yield select(selectEnableCashback);
  const enableBeepMembership = yield select(selectMembershipEnabled);
  const enableCfdMembership = yield select(selectGBCfdBeepMembershipCashback);

  if ((enableCashback || enableBeepMembership) && enableCfdMembership) {
    yield put(updateCfdMembership(null));
  }

  yield put(checkCustomerInfoFromQR({ currentRecord }));
};

export const updateCustomerInTransactionSaga = function* (action) {
  const { customer } = action.payload;
  const currentRecord = yield call(getCurrentTransaction);
  currentRecord.customer = customer;
  yield put(Actions.setTransactionSession(currentRecord));
};

type SetLoyaltyDiscountsType = {
  currentRecord: any;
  inputValue: number;
  isRefund?: boolean;
  type: string;
};

export const setLoyaltyDiscounts = (params: SetLoyaltyDiscountsType) => {
  const { currentRecord, inputValue, type } = params;
  const { items } = currentRecord;
  const loyaltyDiscountItem = {
    type,
    loyaltyType: type,
    inputValue: isValidNumber(inputValue) ? Number(inputValue) : 0,
  };
  currentRecord.isLoyaltyEnable = true;
  currentRecord.loyaltyDiscounts = [loyaltyDiscountItem];
  return getDisplayItemsCount(items);
};

export function* updateLoyaltyDiscountsSaga(action: Action<Actions.UpdateLoyaltyDiscountsType>) {
  const { type, inputValue, isRefund = false } = action.payload;

  let currentRecord = yield call(getCurrentTransaction);
  const displayItemsCount = setLoyaltyDiscounts({ currentRecord, inputValue, isRefund, type });
  if (displayItemsCount > 0) {
    yield call(tryApplyPromotion, currentRecord);
    const includingTaxInDisplay = yield select(selectIncludingTaxInDisplay);
    try {
      currentRecord = calculateP(currentRecord, includingTaxInDisplay || false);
    } catch (exception) {
      errorTransactionEvent({
        action: LoyaltyFlowAction.UpdateLoyaltyDiscounts,
        reason: 'calculate exception',
        exception,
        transaction: currentRecord,
      });
    }
  }
  conversionDiscountInLoyaltyDiscounts(currentRecord);
  yield put(Actions.setTransactionSession(currentRecord));
  yield put(Actions.warnToRemoveUniquePromo());
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const disableLoyaltyDiscountsSaga = function* (action) {
  const includingTaxInDisplay = yield select(selectIncludingTaxInDisplay);
  let currentRecord = yield call(getCurrentTransaction);
  disableLoyaltyFromTransaction(currentRecord);
  yield call(tryApplyPromotion, currentRecord);
  try {
    currentRecord = calculateP(currentRecord, includingTaxInDisplay || false);
  } catch (exception) {
    console.log('disableLoyaltyDiscountsSaga exception', exception);
    errorTransactionEvent({
      action: LoyaltyFlowAction.DisableLoyaltyDiscounts,
      reason: 'calculate exception',
      exception,
      transaction: currentRecord,
    });
  }
  yield put(Actions.setTransactionSession(currentRecord));
  yield put(Actions.warnToRemoveUniquePromo());
};

export const setTakeAwayToTransactionSaga = function* (action) {
  const { salesChannel, currentTransaction } = action.payload;
  let currentRecord;
  if (currentTransaction) {
    currentRecord = currentTransaction;
  } else {
    currentRecord = yield call(getCurrentTransaction);
  }
  const isOnlineOrder = get(currentRecord, 'isOnlineOrder', false);
  if (isOnlineOrder) return;
  const immutableStore = yield select(selectStore);
  const includingTaxInDisplay = immutableStore.get('includingTaxInDisplay');
  const enableTakeaway = immutableStore.get('enableTakeaway');
  const takeawayCharge = immutableStore.get('takeawayCharge');
  const lastSalesChannel = get(currentRecord, 'salesChannel', SalesChannelType.DEFAULT);

  if (salesChannel !== undefined) {
    currentRecord.salesChannel = salesChannel;
  } else {
    currentRecord.salesChannel = SalesChannelType.DEFAULT;
  }
  if (salesChannel === SalesChannelType.TAKEAWAY && currentRecord.transactionType === TransactionFlowType.PreOrder) {
    currentRecord.transactionType = TransactionFlowType.Sale;
    delete currentRecord.preOrderDate;
    delete currentRecord.depositAmount;
  }
  if (enableTakeaway && takeawayCharge) {
    currentRecord.takeawayCharge = takeawayCharge;
  }

  currentRecord.salesChannel = enableTakeaway ? (salesChannel ? salesChannel : SalesChannelType.DINE_IN) : SalesChannelType.DEFAULT;
  if (currentRecord.items) {
    if (currentRecord.salesChannel === SalesChannelType.TAKEAWAY) {
      // new salesChannel is TAKEAWAY
      currentRecord.items.map(item => {
        item.itemChannel = SalesChannelType.TAKEAWAY;
        item.isTakeaway = true;
      });
    } else if (lastSalesChannel === SalesChannelType.TAKEAWAY) {
      // last salesChannel is TAKEAWAY and new salesChannel is DINE_IN
      currentRecord.items.map(item => {
        item.itemChannel = SalesChannelType.DINE_IN;
        item.isTakeaway = false;
      });
    }
    // if last salesChannel is DINE_IN and new salesChannel is DINE_IN, need do nothing
  }
  if (salesChannel === SalesChannelType.TAKEAWAY) {
    yield call(resetTransactionTableIdAndPaxSaga, currentRecord);
  }
  if (getDisplayItemsCount(currentRecord.items) > 0) {
    yield call(tryApplyPromotion, currentRecord);
    try {
      currentRecord = calculateP(currentRecord, includingTaxInDisplay || false);
      conversionDiscountInLoyaltyDiscounts(currentRecord);
    } catch (exception) {
      console.log('setTakeAwayToTransactionSaga exception', exception);
      errorTransactionEvent({
        action: LoyaltyFlowAction.SetTakeAwayToTransaction,
        reason: 'calculate exception',
        exception,
        transaction: currentRecord,
      });
    }
  }
  yield put(Actions.setTransactionSession(currentRecord));
};

export const updateTransactionPaxSaga = function* (action) {
  const { pax } = action.payload;
  const transaction = yield call(getCurrentTransaction);
  if (Boolean(pax)) {
    transaction.pax = pax;
  } else {
    delete transaction.pax;
  }
  yield put(Actions.setTransactionSession(transaction));
};

export const updateTransactionTableIdAndPaxSaga = function* (action) {
  const { pax, tableId } = action.payload;
  const transaction = yield call(getCurrentTransaction);
  if (Boolean(tableId)) transaction.tableId = tableId;
  if (Boolean(pax)) {
    transaction.pax = pax;
  } else {
    delete transaction.pax;
  }

  yield put(Actions.setTransactionSession(transaction));
};

export const resetTransactionTableIdAndPaxSaga = function (transaction) {
  if (transaction) {
    transaction.tableId = null;
    transaction.pax = null;
  } else {
    console.log('Transaction is null or undefined.');
  }
};

export const checkCustomerInfoFromQRBeginSaga = function* () {
  if (isEmpty(globalConfig.customerQRFlag.QrCodeId) || !globalConfig.customerQRFlag.isListening) {
    yield put(Actions.stopCheckCustomerInfoFromQR({}));
    return;
  }
  yield put(
    Actions.shareInfoRequest({
      id: globalConfig.customerQRFlag.QrCodeId,
    })
  );
  const responseAction = yield take([Actions.shareInfoRequest.toString() + '.success', Actions.shareInfoRequest.toString() + '.failure']);
  if (responseAction.type === Actions.shareInfoRequest.toString() + '.success') {
    const actionPayload = responseAction.payload;
    const shareInfo = get(actionPayload, 'shareInfoRequest', {});
    const { customer } = shareInfo;
    if (customer) {
      // if customer, should hide the Customer QR Code, clear the global flag and stop the listener
      logSucceedServerRequest(CustomerAction.SHARE_INFO, ['customer'], WorkflowStatus.End, null, shareInfo);
      const isInCheckOut = getCurrentRouteName() === 'Checkout';
      yield call(autoApplyCustomerSaga, autoApplyCustomer({ customer, isInCheckOut }));
    }
  }
};

/**
 * after customer scan the QR code, check and apply cashback
 * @param action
 */
export const autoApplyCustomerSaga = function* (action: Action<Actions.AutoApplyCustomerType>) {
  const { customer, isInCheckOut } = action.payload;
  const currentRecord = yield call(getCurrentTransaction);
  if (customer.phone) {
    const canApplyCashBack = yield call(checkCanApplyCashBackSaga, {
      type: '',
      payload: { transaction: currentRecord },
    });
    // if set isLoyaltyEnable true, addCustomerToTransaction will auto apply cashback
    currentRecord.isLoyaltyEnable = canApplyCashBack;
  }
  yield call(
    addCustomerToTransactionSaga,
    Actions.addCustomerToTransaction({
      customer: {
        ...customer,
        loyalty: customer.storeCreditsBalance,
      },
      customerId: customer.customerId,
      transaction: currentRecord,
      isInCheckOut,
    })
  );
};

type CheckCanApplyCashBackType = {
  transaction: any;
};
export const checkCanApplyCashBackSaga = function* (action: Action<CheckCanApplyCashBackType>) {
  const { transaction } = action.payload;
  const enableCashback = yield select(selectEnableCashback);
  const enableLoyalty = yield select(selectEnableLoyalty);
  const { transactionType, isLoyaltyCanEnable, isOnlineOrder = false } = transaction;
  const isPreOrder = transactionType === TransactionFlowType.PreOrder;
  const isManualReturn = transactionType === TransactionFlowType.Return;
  const isPickUp = isPreOrderPickUp(transaction);
  const hasBirDiscount = isBirDiscountAvailable(transaction);
  const loyaltyDiscountDisabled = isLoyaltyCanEnable === false || hasBirDiscount;
  return enableLoyalty && enableCashback && !isPreOrder && !isManualReturn && !isPickUp && !isOnlineOrder && !loyaltyDiscountDisabled;
};

function* loyaltyFlowSagas() {
  yield takeLatest(Actions.removeBIRDiscount.toString(), removeBIRDiscountSaga);
  yield takeLatest(Actions.addBIRDiscountToTransaction.toString(), addBIRDiscountToTransactionSaga);
  yield takeLatest(Actions.addCustomerToTransaction.toString(), addCustomerToTransactionSaga);
  yield takeLatest(Actions.deleteCustomerFromTransaction.toString(), deleteCustomerFromTransactionSaga);
  yield takeLatest(Actions.updateLoyaltyDiscounts.toString(), updateLoyaltyDiscountsSaga);
  yield takeLatest(Actions.disableLoyaltyDiscounts.toString(), disableLoyaltyDiscountsSaga);
  yield takeLatest(Actions.checkCustomerInfoFromQRBegin.toString(), checkCustomerInfoFromQRBeginSaga);
  yield takeLatest(Actions.autoApplyCustomer.toString(), autoApplyCustomerSaga);
  yield takeLatest(Actions.updateCustomerInTransaction.toString(), updateCustomerInTransactionSaga);
  yield takeLatest(Actions.setTakeAwayToTransaction.toString(), setTakeAwayToTransactionSaga);
  yield takeLatest(Actions.updateTransactionTableIdAndPax.toString(), updateTransactionTableIdAndPaxSaga);
  yield takeLatest(Actions.updateTransactionPax.toString(), updateTransactionPaxSaga);
  yield takeLatest(Actions.resetTransactionTableIdAndPax.toString(), resetTransactionTableIdAndPaxSaga);
  yield takeLatest(Actions.fetchUniquePromo.toString(), fetchUniquePromosSaga);
}

export default fork(loyaltyFlowSagas);
