import {
  PaySplitOnlineOrderType,
  SaveSplitOnlineOrderType,
  SetOnlineOrderToSplitMasterType,
  setSplitedOrder,
  setSplitMasterOrder,
  UpdateSplitOrderAfterCheckOutType,
} from './../../actions/openOrder';
import { setOnlineOpenOrderToTransaction } from './../../actions/transaction';
import {
  selectBirAccredited,
  selectBusinessName,
  selectClientIp,
  selectEmployeeId,
  selectEnableCashback,
  selectEnableLoyalty,
  selectEnableTableLayout,
  selectIndustry,
  selectInMergeOpenOrder,
  selectInSplitOpenOrder,
  selectIsBeepQREnabled,
  selectIsEnabledMRS,
  selectIsKdsPaired,
  selectRegisterObjectId,
  selectSplitedOrder,
  selectSplitTargetOrder,
  selectStore,
  selectStoreId,
  selectStoreInfo,
  selectTableLayoutEnabled,
  selectTransactionSession,
} from './../selector';

import { chain, filter, find, findIndex, forEach, get, groupBy, isArray, isEmpty, map, values } from 'lodash';
import { call, delay, fork, put, SagaReturnType, select, spawn, take, takeLatest, takeLeading } from 'redux-saga/effects';
import * as Actions from '../../actions';
import {
  checkLimitBeforeExecute,
  EditSplitOrderType,
  ManualDiscountInput,
  MergeOpenOrderType,
  MoveOpenOrderType,
  OpenOrderUpdatedEvent,
  OperationType,
  OrderItemInputType,
  OrderPreview,
  ProposeDataType,
  requestProposalAction,
  SaveSplitOrderType,
  setOpenOrderTransactionSession,
  SplitOrderInput,
  TransactionTypeWithDisplay,
} from '../../actions';
import {
  BIRDiscountType,
  BIRStoreType,
  BIRType,
  createToastError,
  EditOnlineOrderErrorMap,
  FAB_SCPWD_DISCOUNT_RATE,
  LEARN_COUNT_PER_TIME,
  OrderNotFindError,
  registerPages,
  Retail_SCPWD_DISCOUNT_RATE,
  SuccessCode,
  SuccessMessage,
  t,
  TransactionFlowType,
  UnknownError,
} from '../../constants';
import DAL from '../../dal';
import { PurchasedItemType, TransactionType } from '../../typings';
import { getUnNullValue, isValidNumber } from '../../utils';
import * as JSONUtils from '../../utils/json';
import {
  BeepFlowAction,
  BeepFlowTag,
  errorTransactionEvent,
  infoTransactionEvent,
  logFailedServerRequest,
  logSucceedServerRequest,
  OpenOrderFlowAction,
  OrderOperationEnum,
  WorkflowStatus,
} from '../../utils/logComponent';
import { getDisplayItemsCount } from '../../utils/transaction';
import {
  calculateItemsToKitchenOrder,
  checkItemsDiffTax,
  checkPriceBooks,
  conversionDiscountInLoyaltyDiscounts,
  getAppliedPriceBooks,
  getCurrentTransaction,
  getInitialTransaction,
  getServiceChargeTaxRateWithStore,
  stringifyItemOptions,
  tryApplyPromotion,
  updateTransactionWithAppliedPriceBooks,
} from './common';
import { addCustomerToRecord, setLoyaltyDiscounts } from './loyaltyFlow';
import { addFullBillDiscountToRecord, addPurchasedItemToRecord, setServiceChargeToRecord } from './saleFlow';

import moment from 'moment';

import { DeviceEventEmitter } from 'react-native';

import { Action } from 'redux-actions';
import { openErrorToast, openSuccessToast } from '../../components/common/TopNotification';

import { TransactionHelper } from '../../dal/helper';
import { KitchenEvent, KitchenManager } from '../../models/print/manager/KitchenManager';
import * as NavigationService from '../../navigation/navigatorService';
import { getCurrentRouteName, navigate, showModal } from '../../navigation/navigatorService';
import { getBeepOrderShippingTag, getDisplayedStatus, getPreOrderTag } from '../../utils/beep';
import { signatureSaveOrder } from '../../utils/signature';
import { getUUIDValue } from '../../utils/string';
import { getKdsInfoFromItem, restoreOrderLevelKdsInfo, updateCookingStatusFromItems } from '../kds/transaction/helper';
import { pushLocalOpenOrdersSaga } from '../kds/transaction/push';
import { checkIsNeedMRSSaga, onMRSInterceptor } from '../mrs/checkSync';
import { performLearnerActionSaga } from '../mrs/roles/learner';
import { checkLimitBeforeExecuteSaga, requestProposalActionSaga } from '../mrs/roles/proposer';
import { getPrinterNotification } from '../printing/common';
import calculateP from './common/calculator';

export const updateOpenOrderSaga = function* (action) {
  const { onComplete } = action.payload;
  const transaction = yield call(getCurrentTransaction);
  if (transaction.isOpen) {
    const now = new Date();
    transaction.modifiedDate = now.toISOString();
    const items = map(transaction.items, item => {
      return { ...item, options: item.options ? JSON.stringify(item.options) : null };
    });
    transaction.items = items;
    const originalTx = DAL.getTransactionById(transaction.transactionId);
    if (!originalTx) {
      onComplete && onComplete(OrderNotFindError);
      return false;
    }
    yield call(updateCookingStatusFromItems, transaction, originalTx);
    const { itemsDiff } = calculateItemsToKitchenOrder(transaction, originalTx);
    const isLimit: SagaReturnType<typeof checkLimitBeforeExecuteSaga> = yield call(
      checkLimitBeforeExecuteSaga,
      checkLimitBeforeExecute({
        transaction,
        orderOperation: OrderOperationEnum.Update,
      })
    );
    if (isLimit.errorCode !== SuccessCode) {
      onComplete && onComplete(isLimit);
      return false;
    }

    const isNeedMRS = yield call(checkIsNeedMRSSaga, OrderOperationEnum.Update, transaction);
    const originalRecordItems = TransactionHelper.serializeItems(originalTx.items);
    let error = SuccessMessage('no need MRS');
    if (!isNeedMRS) {
      const updateSuccess = DAL.updateOpenOrder(transaction);
      if (updateSuccess) {
        yield spawn(pushLocalOpenOrdersSaga, [transaction.transactionId]);
      }
    } else {
      transaction.mrs = true;
      error = yield call(
        requestProposalActionSaga,
        requestProposalAction({
          data: [
            {
              operationType: Actions.OperationType.UPDATE,
              transaction: { ...transaction, loyaltyDiscounts: transaction.loyaltyDiscounts ?? [], customerId: transaction.customerId ?? null },
            },
          ],
          operation: OrderOperationEnum.Update,
        })
      );
    }
    if (error.errorCode === SuccessCode) {
      if (Boolean(transaction.previousTableId) && transaction.previousTableId !== transaction.tableId) {
        // function: print the original items with ChangeTableTitle(for example: A1 -> A2)
        yield spawn(KitchenManager.printOnMoveTable, transaction.previousTableId, { ...transaction, items: originalRecordItems });
      }
      yield put(
        Actions.printKitchenDocket({
          transaction,
          tags: ['updateOpenOrder'],
          printItems: itemsDiff,
          eventName: KitchenEvent.printOpenOrder,
        })
      );
    }
    infoTransactionEvent({ action: OpenOrderFlowAction.UpdateOpenOrder, transaction, privateDataPayload: { mrs: isNeedMRS } });
    if (onComplete) yield call(onComplete, error);
    const isTableChanged = get(transaction, 'isTableChanged', false);
    if (isTableChanged && error.errorCode === SuccessCode) {
      openSuccessToast(t('Order Moved Successfully'));
    }
  }
};

export const generatePayByCashTransactionSession = function* (onlineTransaction, isInSalePayment = false) {
  const transaction = onlineTransaction;
  const {
    discount,
    subtotal,
    total,
    tax,
    items,
    id,
    customerId,
    amusementTax,
    contactDetail,
    payments,
    loyaltyDiscounts,
    createdTime,
    modifiedTime,
    takeawayCharges,
  } = onlineTransaction;
  let totalAmount = isValidNumber(total) ? Number(total) : 0;
  let voucherAmount = 0;
  let discountAmount = isValidNumber(discount) ? Number(discount) : 0;
  if (Boolean(payments) && isArray(payments) && payments.length > 0) {
    const voucherPayment = find(payments, item => item.paymentMethod == 'Voucher');
    if (Boolean(voucherPayment)) {
      voucherPayment.type = voucherPayment.paymentMethod;
      voucherPayment.isOnline = true;
      voucherAmount = isValidNumber(voucherPayment.amount) ? Number(voucherPayment.amount) : 0;
      totalAmount = totalAmount - voucherAmount;
      discountAmount = discountAmount + voucherAmount;
    }
    if (isInSalePayment) {
      onlineTransaction.payments = Boolean(voucherPayment) ? [voucherPayment] : [];
      onlineTransaction.roundedAmount = 0;
    }
  }
  transaction.discount = discountAmount;
  transaction.total = totalAmount;
  transaction.isOpen = true;
  transaction.takeawayCharges = takeawayCharges;
  transaction.previousTableId = transaction.tableId;
  transaction.amusementTax = amusementTax || 0;
  let serviceCharge = 0;
  const newItems = [];
  if (Boolean(items)) {
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      const { subTotal, total, tax, taxRate, promotions, itemType, selectedOptions } = item;
      if (itemType == 'ServiceCharge') {
        serviceCharge = subTotal;
      }
      const realTaxRate = isValidNumber(taxRate) ? Number(taxRate) : 0;
      if (Boolean(promotions) && isArray(promotions) && promotions.length > 0) {
        for (let j = 0; j < promotions.length; j++) {
          const promotion = promotions[j];
          const { promotionName, discount, discountType } = promotion;
          promotion.display = { promotionName, discount, discountType };
        }
      }
      newItems.push({ ...item, taxRate: realTaxRate, display: { subtotal: subTotal, total, tax }, options: selectedOptions });
    }
  }
  const fullPromotions = [];
  if (Boolean(onlineTransaction.promotions) && isArray(onlineTransaction.promotions)) {
    for (let index = 0; index < onlineTransaction.promotions.length; index++) {
      const promotion = onlineTransaction.promotions[index];
      const { discount, promotionName } = promotion;
      fullPromotions.push({ ...promotion, title: promotionName, display: { discount } });
    }
  }
  transaction.promotions = fullPromotions;

  const loyaltyDiscountList = [];
  if (Boolean(loyaltyDiscounts) && loyaltyDiscounts.length > 0) {
    forEach(loyaltyDiscounts, loyaltyDiscount => {
      const { loyaltyType, displayDiscount } = loyaltyDiscount;
      loyaltyDiscountList.push({
        ...loyaltyDiscount,
        type: loyaltyType,
        inputValue: displayDiscount,
      });
    });
  }
  transaction.loyaltyDiscounts = loyaltyDiscountList;

  transaction.display = onlineTransaction.display || { discount: discountAmount, subtotal, total: totalAmount, serviceCharge, tax };
  transaction.serviceCharge = serviceCharge;
  transaction.items = newItems;
  if (isEmpty(transaction.transactionId) && !isEmpty(id)) transaction.transactionId = id;
  transaction.transactionType = TransactionFlowType.Sale;
  transaction.isPayByCash = true;
  transaction.isOnlineOrder = true;
  transaction.createdDate = createdTime;
  transaction.modifiedDate = modifiedTime;
  // Just in case, make sure the data is accurate, because PreOrderDate will affect the ShiftReport statistics
  if (Boolean(transaction.preOrderDate)) {
    delete transaction.preOrderDate;
  }

  if (Boolean(customerId)) {
    const business = yield select(selectBusinessName);
    yield put(Actions.getCustomerById({ customerId, bn: business }));
    const responseAction = yield take([Actions.getCustomerById.toString() + '.success', Actions.getCustomerById.toString() + '.failure']);
    if (responseAction.type === Actions.getCustomerById.toString() + '.success') {
      const resp = responseAction.payload;
      if (Boolean(resp)) {
        transaction.customer = resp;
      }
    }
  } else if (Boolean(contactDetail)) {
    const { name, phone, email } = contactDetail;
    transaction.customer = { firstName: name, phone, email: email || '' };
  }
  return transaction;
};

export const generatePayLaterTransactionSession = function* (onlineTransaction, isInSalePayment = false) {
  const transaction = onlineTransaction;
  const {
    discount,
    subtotal,
    total,
    tax,
    items,
    id,
    customerId,
    payments,
    loyaltyDiscounts,
    subOrders,
    createdTime,
    modifiedTime,
    takeawayCharges,
    amusementTax,
  } = onlineTransaction;
  let totalAmount = isValidNumber(total) ? Number(total) : 0;
  let voucherAmount = 0;
  let discountAmount = isValidNumber(discount) ? Number(discount) : 0;
  if (Boolean(payments) && isArray(payments) && payments.length > 0) {
    const voucherPayment = find(payments, item => item.paymentMethod == 'Voucher');
    if (Boolean(voucherPayment)) {
      voucherPayment.type = voucherPayment.paymentMethod;
      voucherPayment.isOnline = true;
      voucherAmount = isValidNumber(voucherPayment.amount) ? Number(voucherPayment.amount) : 0;
      totalAmount = totalAmount - voucherAmount;
      discountAmount = discountAmount + voucherAmount;
    }
    if (isInSalePayment) {
      onlineTransaction.payments = Boolean(voucherPayment) ? [voucherPayment] : [];
      onlineTransaction.roundedAmount = 0;
    }
  }
  transaction.discount = discountAmount;
  transaction.total = totalAmount;
  transaction.isOpen = true;
  transaction.amusementTax = amusementTax || 0;
  transaction.takeawayCharges = takeawayCharges;
  transaction.previousTableId = transaction.tableId;
  let serviceCharge = 0;
  const itemTypeItems = [];
  const unSavedItems = [];
  const submitedItems = [];

  if (Boolean(items)) {
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      const { id, submitId, subTotal, total, tax, taxRate, promotions, itemType, selectedOptions } = item;
      if (itemType == 'ServiceCharge') {
        serviceCharge = total;
      }
      const realTaxRate = isValidNumber(taxRate) ? Number(taxRate) : 0;
      if (Boolean(promotions) && isArray(promotions) && promotions.length > 0) {
        for (let j = 0; j < promotions.length; j++) {
          const promotion = promotions[j];
          const { promotionName, discount, discountType } = promotion;
          promotion.display = { promotionName, discount, discountType };
        }
      }
      const newItem = { ...item, taxRate: realTaxRate, display: item.display || { subtotal: subTotal, total, tax }, options: selectedOptions };

      const matchedSubmit = find(subOrders, subOrder => subOrder.submitId == submitId);
      if (Boolean(matchedSubmit)) {
        const { submittedBy, submittedByPhone, submittedFrom, submittedTime, comments } = matchedSubmit;
        if (Boolean(submittedTime)) {
          newItem.submittedTime = moment(submittedTime).format('hh:mm A');
          newItem.submitNotes = comments;
        } else {
          newItem.submittedTime = moment().format('hh:mm A');
        }
        if (submittedFrom === SubmittedFromType.Employee) {
          if (Boolean(submittedBy)) {
            const employee = DAL.getEmployeeById(submittedBy);
            if (employee) {
              const firstName = get(employee, 'firstName', '');
              const lastName = get(employee, 'lastName', '');
              newItem.submittedFrom = `${firstName}  ${lastName}`;
            }
          }
        } else if (submittedFrom === SubmittedFromType.Consumer) {
          if (submittedByPhone) {
            newItem.submittedFrom = `Customer (${submittedByPhone})`;
          } else {
            newItem.submittedFrom = 'Customer';
          }
        } else {
          newItem.submittedFrom = submittedFrom;
        }
      }

      if (Boolean(itemType)) {
        itemTypeItems.push(newItem);
      } else if (Boolean(id)) {
        submitedItems.push(newItem);
      } else {
        unSavedItems.push(newItem);
      }
    }
  }
  // Tag the Sumbit Sections Dividing line
  const groupedSubmitedItems = values(groupBy(submitedItems, item => item.submitId));
  for (let i = 0; i < groupedSubmitedItems.length; i++) {
    const groupeItems = groupedSubmitedItems[i];
    if (groupeItems && groupeItems.length > 0) {
      groupeItems[0].isGroupFirst = true;
      groupeItems[groupeItems.length - 1].isGroupLast = true;
    }
  }
  if (unSavedItems && unSavedItems.length > 0) {
    unSavedItems[0].isGroupFirst = true;
    unSavedItems[unSavedItems.length - 1].isGroupLast = true;
  }
  const newItems = groupedSubmitedItems.flat().concat(unSavedItems).concat(itemTypeItems);
  const fullPromotions = [];
  if (Boolean(onlineTransaction.promotions) && isArray(onlineTransaction.promotions)) {
    for (let index = 0; index < onlineTransaction.promotions.length; index++) {
      const promotion = onlineTransaction.promotions[index];
      const { discount, promotionName } = promotion;
      fullPromotions.push({ ...promotion, title: promotionName, display: { discount } });
    }
  }
  transaction.promotions = fullPromotions;

  const loyaltyDiscountList = [];
  if (Boolean(loyaltyDiscounts) && loyaltyDiscounts.length > 0) {
    forEach(loyaltyDiscounts, loyaltyDiscount => {
      const { loyaltyType, displayDiscount } = loyaltyDiscount;
      loyaltyDiscountList.push({
        ...loyaltyDiscount,
        type: loyaltyType,
        inputValue: displayDiscount,
      });
    });
  }
  transaction.loyaltyDiscounts = loyaltyDiscountList;

  transaction.display = onlineTransaction.display
    ? { ...onlineTransaction.display, total: totalAmount }
    : { discount: discountAmount, subtotal, total: totalAmount, serviceCharge, tax };
  transaction.serviceCharge = serviceCharge;
  transaction.items = newItems;
  transaction.originalItems = newItems;
  if (isEmpty(transaction.transactionId) && !isEmpty(id)) transaction.transactionId = id;
  transaction.transactionType = TransactionFlowType.Sale;
  transaction.isPayLater = true;
  transaction.isOnlineOrder = true;
  transaction.createdDate = createdTime;
  transaction.modifiedDate = modifiedTime;
  // Just in case, make sure the data is accurate, because PreOrderDate will affect the ShiftReport statistics
  if (Boolean(transaction.preOrderDate)) {
    delete transaction.preOrderDate;
  }
  if (Boolean(customerId)) {
    const business = yield select(selectBusinessName);
    yield put(Actions.getCustomerById({ customerId, bn: business }));
    const responseAction = yield take([Actions.getCustomerById.toString() + '.success', Actions.getCustomerById.toString() + '.failure']);
    if (responseAction.type === Actions.getCustomerById.toString() + '.success') {
      const resp = responseAction.payload;
      if (Boolean(resp)) {
        transaction.customer = resp;
      }
    }
  }
  return transaction;
};

export const printOnlineOpenOrderReceiptSaga = function* (action: Action<Actions.printOnlineOpenOrderReceiptType>) {
  const { onlineTransaction } = action.payload;
  const transaction = yield call(generatePayByCashTransactionSession, onlineTransaction);
  yield put(Actions.newPrintOpenOrderReceipt({ onlineTransaction: transaction }));
};

export const setOnlineOpenOrderToTransactionSaga = function* (action: Action<Actions.setOnlineOpenOrderToTransactionType>) {
  yield put(Actions.clearTransactionSession());
  const { onlineTransaction, isInSalePayment = false } = action.payload;
  const { isPayLater } = onlineTransaction;
  let transaction;
  if (isPayLater) {
    transaction = yield call(generatePayLaterTransactionSession, onlineTransaction, isInSalePayment);
  } else {
    transaction = yield call(generatePayByCashTransactionSession, onlineTransaction, isInSalePayment);
  }
  yield put(Actions.setTransactionSession(transaction));
  infoTransactionEvent({ action: OpenOrderFlowAction.SetOnlineOpenOrderToTransaction, transaction });
};

export const setOpenOrderTransactionSaga = function* (action) {
  const { transactionId } = action.payload;
  const immutableStoreInfo = yield select(selectStoreInfo);
  const business = immutableStoreInfo.get('name');
  yield put(Actions.clearTransactionSession());

  const openOrder = DAL.getTransactionById(transactionId);

  const items = openOrder.items;
  if (getDisplayItemsCount(items) > 0) {
    yield call(restoreOpenOrder, items, openOrder, business);
  }
};

export const saveOpenOrderSaga = function* (action) {
  const { onComplete } = action.payload;
  const transaction = yield call(getCurrentTransaction);
  transaction.isOpen = true;
  transaction.isOpenOrder = true;
  const isLimit: SagaReturnType<typeof checkLimitBeforeExecuteSaga> = yield call(
    checkLimitBeforeExecuteSaga,
    checkLimitBeforeExecute({
      transaction,
      orderOperation: OrderOperationEnum.Save,
    })
  );
  if (isLimit.errorCode !== SuccessCode) {
    onComplete && onComplete(isLimit);
    return false;
  }
  const now = new Date();
  const employeeId = yield select(selectEmployeeId);
  transaction.createdDate = now.toISOString();
  transaction.employeeId = employeeId;
  transaction.modifiedDate = now.toISOString();
  const items = map(transaction.items, item => {
    return { ...item, options: item.options ? JSON.stringify(item.options) : null };
  });

  transaction.items = items;
  const { itemsDiff } = calculateItemsToKitchenOrder(transaction);
  yield call(updateCookingStatusFromItems, transaction);
  let error = SuccessMessage();
  const isNeedMRS = yield call(checkIsNeedMRSSaga, OrderOperationEnum.Save, transaction);
  if (!isNeedMRS) {
    yield put(
      Actions.printKitchenDocket({
        transaction,
        printItems: itemsDiff,
        tags: ['saveOpenOrder'],
        eventName: KitchenEvent.printOpenOrder,
      })
    );
    const saveSuccess = DAL.saveTransaction(transaction);
    if (saveSuccess) {
      yield spawn(pushLocalOpenOrdersSaga, [transaction.transactionId]);
    }
    if (onComplete) yield call(onComplete, SuccessMessage('no need MRS'));
  } else {
    error = yield call(
      requestProposalActionSaga,
      requestProposalAction({
        data: [{ operationType: OperationType.INSERT, transaction }],
        operation: OrderOperationEnum.Save,
      })
    );
    if (error.errorCode === SuccessCode) {
      yield put(
        Actions.printKitchenDocket({
          transaction,
          printItems: itemsDiff,
          tags: ['saveOpenOrder'],
          eventName: KitchenEvent.printOpenOrder,
        })
      );
    }
    if (onComplete) yield call(onComplete, error);
  }
  infoTransactionEvent({ action: OpenOrderFlowAction.SaveOpenOrder, transaction, privateDataPayload: { mrs: isNeedMRS } });

  const isTableChanged = get(transaction, 'isTableChanged', false);
  if (isTableChanged && error.errorCode === SuccessCode) {
    openSuccessToast(t('Order Moved Successfully'));
  }
};

export const restoreBirInfoToTransaction = function* (record, currentRecord) {
  const birAccredited = yield select(selectBirAccredited);
  if (!birAccredited) return;
  const discountType = get(record, ['addonBirCompliance', 'discountType']);
  const collectedInfo = get(record, ['addonBirCompliance', 'collectedInfo']);
  const seniorsCount = get(record, 'seniorsCount', 0);
  const pwdCount = get(record, 'pwdCount', 0);
  const headcount = get(record, 'headcount', 1);
  currentRecord.headcount = headcount;
  currentRecord.seniorsCount = seniorsCount;
  currentRecord.pwdCount = pwdCount;
  const industry = yield select(selectIndustry);
  const isRetail = industry == BIRStoreType.RetailStore;
  const birType = isRetail ? BIRType.Retail : BIRType.FAB;
  switch (discountType) {
    case BIRDiscountType.SCAndPWD:
      {
        currentRecord.birInfo = {
          type: birType,
          discountType,
          discountRate: birType == BIRType.FAB ? FAB_SCPWD_DISCOUNT_RATE : Retail_SCPWD_DISCOUNT_RATE,
          seniorsCount: Boolean(seniorsCount) ? seniorsCount : 0, // If there's pwdCount, able to be 0
          pwdCount: Boolean(pwdCount) ? pwdCount : 0, // If there's seniorsCount, able to be 0
          headCount: Boolean(headcount) ? headcount : 1, // at least 1
        };
      }
      break;
    case BIRDiscountType.SoloParent:
      {
        currentRecord.birInfo = {
          type: birType,
          discountType,
          discountRate: 0.1, // default to 0.1
          seniorsCount: 0, // If there's pwdCount, able to be 0
          pwdCount: 0, // If there's seniorsCount, able to be 0
          headCount: 1, // at least 1
          collectedInfo: Boolean(collectedInfo) ? JSON.parse(collectedInfo) : collectedInfo,
        };
      }
      break;
    case BIRDiscountType.AthletesAndCoaches:
    case BIRDiscountType.MedalofValor:
      {
        currentRecord.birInfo = {
          type: birType,
          discountType,
          discountRate: 0.2, // default to 0.2
          seniorsCount: 0, // If there's pwdCount, able to be 0
          pwdCount: 0, // If there's seniorsCount, able to be 0
          headCount: 1, // at least 1
          collectedInfo: Boolean(collectedInfo) ? JSON.parse(collectedInfo) : collectedInfo,
        };
      }
      break;
    case BIRDiscountType.Diplomats:
      {
        currentRecord.birInfo = {
          type: birType,
          discountType,
          discountRate: 0, // default to 0.2
          seniorsCount: 0, // If there's pwdCount, able to be 0
          pwdCount: 0, // If there's seniorsCount, able to be 0
          headCount: 1, // at least 1
          collectedInfo: Boolean(collectedInfo) ? JSON.parse(collectedInfo) : collectedInfo,
        };
      }
      break;
    default:
      {
        let discountRate = 0;
        let birDiscountType = null;
        if (seniorsCount > 0 || pwdCount > 0) {
          birDiscountType = BIRDiscountType.SCAndPWD;
          discountRate = birType == BIRType.FAB ? FAB_SCPWD_DISCOUNT_RATE : Retail_SCPWD_DISCOUNT_RATE;
        }
        currentRecord.birInfo = {
          type: birType,
          discountType: birDiscountType,
          discountRate,
          seniorsCount, // If there's pwdCount, able to be 0
          pwdCount, // If there's seniorsCount, able to be 0
          headCount: headcount, // at least 1
        };
      }
      break;
  }
};

export const restoreOpenOrder = function* (items: PurchasedItemType[], record: TransactionType, business: string) {
  let itemDiscount = 0;
  const currentRecord = yield call(getCurrentTransaction);
  const { transactionId, tableId, createdDate, pax, salesChannel, takeawayCharge, customerId, mrs, isOnlineOrder, comment } = record;
  const immutableStore = yield select(selectStore);
  const immutableStoreInfo = yield select(selectStoreInfo);
  const enableCashback = yield select(selectEnableCashback);
  const enableLoyalty = yield select(selectEnableLoyalty);
  const includingTaxInDisplay = immutableStore.get('includingTaxInDisplay');
  const enableTakeaway = immutableStore.get('enableTakeaway');
  currentRecord.transactionId = transactionId;
  currentRecord.isOpen = true;
  currentRecord.isOpenOrder = true;
  currentRecord.tableId = tableId;
  currentRecord.previousTableId = tableId;
  currentRecord.createdDate = createdDate;
  currentRecord.pax = pax;
  currentRecord.mrs = mrs;
  currentRecord.isOnlineOrder = isOnlineOrder;
  currentRecord.comment = comment;
  restoreOrderLevelKdsInfo(record, currentRecord);

  if (enableTakeaway) {
    currentRecord.salesChannel = salesChannel;
    currentRecord.takeawayCharge = takeawayCharge;
  }

  let index = 0;
  let discountIndex = -1;
  let serviceChargeItem;
  if (Boolean(items) && items.length > 0) {
    for (const item of items) {
      const itemType = get(item, 'itemType');
      if (itemType !== 'ServiceCharge' && itemType !== 'Discount') {
        const {
          productId,
          options,
          quantity,
          notes,
          sn,
          unitPrice,
          originalQuantity,
          title,
          taxCode,
          itemChannel,
          discountInputValue,
          employeeId,
          employeeName,
        } = item;
        const discountType = getUnNullValue(item, 'discountType', 'amount');
        const discountOption = { inputValue: discountInputValue || 0, type: discountType };
        const variablePrice = unitPrice;
        const parsedOptions = JSONUtils.parse(options, null);
        const kdsInfo = getKdsInfoFromItem(item);
        addPurchasedItemToRecord({
          currentRecord,
          productId,
          options: parsedOptions,
          quantity,
          variablePrice,
          notes,
          sn,
          originalQuantity,
          taxCode,
          title,
          itemChannel,
          return: item.return,
          discountOption,
          immutableStore,
          immutableStoreInfo,
          kdsInfo,
          employeeId,
          employeeName,
        });
        // ----------------------------------------------------------------------------------------------------------------
        if (discountType === 'percent') {
          itemDiscount += ((item.unitPrice * discountInputValue) / 100) * quantity;
        } else {
          itemDiscount += discountInputValue;
        }
      }

      if (itemType === 'Discount') {
        discountIndex = index;
      }
      if (itemType === 'ServiceCharge') {
        serviceChargeItem = item;
      }
      index++;
    }
    if (record.appVersion === '2.23.1.0') {
      // version before android 1.6 doesn't save fullBillDiscount in database
      const discount = get(record, 'discount');
      const inputValue = discount - itemDiscount;
      const type = 'amount';
      addFullBillDiscountToRecord({
        currentRecord,
        inputValue,
        type,
        displayFullBillDiscountValue: inputValue,
        isDiscountEnable: true,
        displayFullBillDiscountEnable: true,
      });
    } else if (discountIndex > -1) {
      const item = items[discountIndex];
      const inputValue = get(item, 'discountInputValue');
      const type = get(item, 'discountType');
      if (!isNaN(inputValue) && Boolean(type)) {
        addFullBillDiscountToRecord({
          currentRecord,
          inputValue,
          type,
          displayFullBillDiscountValue: inputValue,
          isDiscountEnable: true,
          displayFullBillDiscountEnable: true,
        });
      }
    }
    const searchedIndex = findIndex(currentRecord.items, (item: PurchasedItemType) => item.itemType === 'ServiceCharge');
    let serviceChargeRate = record.serviceChargeRate;
    const serviceChargeTax = record.serviceChargeTaxId;
    if (get(serviceChargeItem, 'subTotal', 0) === 0 && searchedIndex !== -1) {
      serviceChargeRate = 0;
    }
    if (Boolean(serviceChargeItem)) {
      const rate = get(serviceChargeItem, 'rate');
      if (isValidNumber(rate)) {
        serviceChargeRate = rate;
      }
    }
    const taxRate = getServiceChargeTaxRateWithStore(immutableStore, record.serviceChargeTaxId);
    setServiceChargeToRecord({ currentRecord, itemIndex: searchedIndex, serviceChargeRate, serviceChargeTax, taxRate });
  }
  yield call(restoreBirInfoToTransaction, record, currentRecord);
  yield call(checkItemsDiffTax, currentRecord, enableCashback, enableLoyalty);

  yield call(tryApplyPromotion, currentRecord);
  const newItems = yield call(updateTransactionWithAppliedPriceBooks, {
    transaction: currentRecord,
    appliedPriceBooks: currentRecord.appliedPriceBooks,
    inTransaction: true,
  });
  currentRecord.items = newItems;
  let hasException = false;
  try {
    calculateP(currentRecord, includingTaxInDisplay || false);
    conversionDiscountInLoyaltyDiscounts(currentRecord);
    yield put(Actions.setTransactionSession(currentRecord));
  } catch (exception) {
    hasException = true;
    if (exception) {
      yield put(Actions.toggleToastInfo({ visible: true, text: exception.message }));
      errorTransactionEvent({ action: OpenOrderFlowAction.RestoreOpenOrder, reason: 'calcutate exception', exception, transaction: currentRecord });
    }
  }

  let haveCustomer = false,
    haveLoyalty = false;
  // addCustomerToTransactionSaga
  if (Boolean(customerId)) {
    yield put(Actions.getCustomerById({ customerId, bn: business }));
    const responseAction = yield take([Actions.getCustomerById.toString() + '.success', Actions.getCustomerById.toString() + '.failure']);
    if (responseAction.type === Actions.getCustomerById.toString() + '.success') {
      const resp = responseAction.payload;
      if (Boolean(resp)) {
        const { loyaltyDiscounts } = record;
        if (enableLoyalty && Boolean(loyaltyDiscounts) && loyaltyDiscounts.length > 0) {
          haveLoyalty = true;
          currentRecord.isLoyaltyEnable = true;
        }
        const appliedPriceBooks = yield call(getAppliedPriceBooks, resp);
        const { success } = yield call(addCustomerToRecord, { currentRecord, immutableStore, appliedPriceBooks, customer: resp, customerId });
        haveCustomer = success;
      }
    }
  }

  // updateLoyaltyDiscountsSaga
  // const { loyaltyDiscounts } = record;
  // if (enableLoyalty && Boolean(loyaltyDiscounts) && loyaltyDiscounts.length > 0) {
  //   haveLoyalty = true;
  //   const { type, inputValue, taxRate } = loyaltyDiscounts[0];
  //   setLoyaltyDiscounts({ currentRecord, inputValue, isRefund: false, curTaxRate: taxRate, type });
  // }
  let newCurrentRecord: any = currentRecord;
  if (haveCustomer || haveLoyalty) {
    try {
      newCurrentRecord = calculateP(currentRecord, includingTaxInDisplay || false);
      conversionDiscountInLoyaltyDiscounts(newCurrentRecord);
      yield put(Actions.setTransactionSession(newCurrentRecord));
    } catch (exception) {
      hasException = true;
      console.log('addCustomerToTransactionSaga exception', exception);
      errorTransactionEvent({ action: OpenOrderFlowAction.RestoreOpenOrder, reason: 'calcutate exception', exception, transaction: currentRecord });
    }
  }
  if (!hasException) {
    infoTransactionEvent({ action: OpenOrderFlowAction.RestoreOpenOrder, transaction: newCurrentRecord });
  }
  yield put(Actions.checkCustomerInfoFromQR({ currentRecord: newCurrentRecord }));
};

export function* deleteOpenOrderSaga(action) {
  const { transactionId, onComplete } = action.payload;
  const transaction = yield call(getCurrentTransaction);
  const isLimit: SagaReturnType<typeof checkLimitBeforeExecuteSaga> = yield call(checkLimitBeforeExecuteSaga, {
    type: '',
    payload: {
      transaction,
      orderOperation: OrderOperationEnum.Delete,
    },
  });
  if (isLimit.errorCode !== SuccessCode) {
    onComplete && onComplete(isLimit);
    return isLimit;
  }
  if (transaction.isOpen) {
    const originalItems = transaction.items;
    transaction.items = []; // Need empty the items to calculate the deleted items
    const { itemsDiff } = calculateItemsToKitchenOrder(transaction);
    const isNeedMRS: SagaReturnType<typeof checkIsNeedMRSSaga> = yield call(checkIsNeedMRSSaga, OrderOperationEnum.Delete, transaction);
    let result = SuccessMessage();
    if (!isNeedMRS) {
      // Print kitchen order
      const notification = DAL.getBeepNotificationById(transactionId);
      yield spawn(pushLocalOpenOrdersSaga, [transactionId], { cancelledId: transactionId });
      if (isEmpty(get(notification, 'jobs', []))) {
        DAL.deleteTransactionById(transactionId);
      } else {
        DAL.updateTransaction(transactionId, { isDeleted: true });
      }
    } else {
      result = yield call(
        requestProposalActionSaga,
        requestProposalAction({
          data: [{ operationType: OperationType.DELETE, transaction }],
          operation: OrderOperationEnum.Delete,
        })
      );
    }
    if (result.errorCode !== SuccessCode) {
      if (onComplete) yield call(onComplete, result);
      return result;
    }
    yield put(
      Actions.printKitchenDocket({
        transaction,
        tags: ['deleteOpenOrder'],
        printItems: itemsDiff,
        eventName: KitchenEvent.printOpenOrder,
      })
    );
    infoTransactionEvent({
      action: OpenOrderFlowAction.DeleteOpenOrder,
      transaction: { ...transaction, items: originalItems },
      privateDataPayload: { mrs: isNeedMRS },
    });
    const enableTableLayout = yield select(selectEnableTableLayout);
    const tableLayoutEnabled = yield select(selectTableLayoutEnabled);
    if (enableTableLayout && tableLayoutEnabled) {
      const { tableId } = transaction;
      const newObject = yield call(getInitialTransaction);
      if (Boolean(tableId)) newObject.tableId = tableId;
      yield put(Actions.stopCheckCustomerInfoFromQR({}));
      yield put(Actions.setTransactionSession(newObject));
    } else {
      yield put(Actions.clearTransactionSession());
    }
    if (onComplete) yield call(onComplete, result);
    return result;
  }
}

export function* deleteOnlineOpenOrderSaga(action) {
  const { receiptNumber } = action.payload;
  const employeeId = yield select(selectEmployeeId);
  yield put(Actions.cancelOrder({ receiptNumber, employeeId }));
  // the orderInfo be tracked in loggly
  const orderInfo = {
    orderId: receiptNumber,
  };
  const responseAction = yield take([Actions.cancelOrder.toString() + '.success', Actions.cancelOrder.toString() + '.failure']);
  if (responseAction.type === Actions.cancelOrder.toString() + '.success') {
    yield put(Actions.clearTransactionSession());
    // cancel online order success
    const transaction = responseAction.payload.cancelOrder;
    const isPayLater = get(transaction, 'isPayLater', false);
    const tags = [
      BeepFlowTag.Beep,
      getBeepOrderShippingTag(transaction),
      getPreOrderTag(transaction),
      isPayLater ? BeepFlowTag.PayLater : BeepFlowTag.PayFirst,
    ];
    logSucceedServerRequest(BeepFlowAction.CancelOrder, tags, WorkflowStatus.End, orderInfo);
    yield spawn(KitchenManager.printOnDeleteOnlineOpenOrder, transaction);
    infoTransactionEvent({ action: OpenOrderFlowAction.DeleteOnlineOpenOrder, transaction });
  } else {
    const tags = [BeepFlowTag.Beep, BeepFlowTag.PayLater];
    logFailedServerRequest(BeepFlowAction.CancelOrder, tags, get(responseAction, 'payload', ''), null, orderInfo);
  }
}

export function* navigateToRegisterSaga(action) {
  const { navigation } = action.payload;
  const enableTableLayout = yield select(selectEnableTableLayout);
  const tableLayoutEnabled = yield select(selectTableLayoutEnabled);
  navigation.navigate(enableTableLayout && tableLayoutEnabled ? 'Register' : 'TableLayoutScreens');
}

export function* navigateToHomeSaga(action) {
  const { navigation } = action.payload;
  const enableTableLayout = yield select(selectEnableTableLayout);
  const tableLayoutEnabled = yield select(selectTableLayoutEnabled);
  navigation.navigate(enableTableLayout && tableLayoutEnabled ? 'TableLayout' : 'TableLayoutScreens');
}

export function* unlockPayLaterOrderSage(action) {
  let receiptNumber = get(action, ['payload', 'receiptNumber']);
  if (!Boolean(receiptNumber)) {
    const currentRecord = yield call(getCurrentTransaction);
    receiptNumber = currentRecord.receiptNumber;
  }
  if (!Boolean(receiptNumber)) return;
  const employeeId = yield select(selectEmployeeId);
  yield put(Actions.unlockOrder({ receiptNumber, employeeId }));
  const responseAction = yield take([Actions.unlockOrder.toString() + '.success', Actions.unlockOrder.toString() + '.failure']);
  const resp = responseAction.payload;
  const onFailed = get(action, ['payload', 'onFailed']);
  if (responseAction.type === Actions.unlockOrder.toString() + '.success') {
    // unlock online order success
    const onlineTransaction = responseAction.payload.unlockOrder;
    onlineTransaction.isOnlineOrder = true;
    onlineTransaction.isFullBillDiscountCanEnable = true;
    const onSuccess = get(action, ['payload', 'onSuccess']);
    // prepare the manual discount equivalentValue
    const items: any[] = get(onlineTransaction, 'items', []);
    const fullBillDiscountItemIndex = findIndex(items, item => item.itemType === 'Discount');
    const hasFullbillDiscount = fullBillDiscountItemIndex > -1;
    const hasItemLevelDiscount = findIndex(items, item => Boolean(item.manualDiscount)) > -1;
    const hasManualDiscount = hasFullbillDiscount || hasItemLevelDiscount;
    if (hasManualDiscount) {
      // if has manual discount, only need to calculate the equivalentValue and set to the transaction
      // NOT REAL ORDER_PREVIEW
      const businessName = yield select(selectBusinessName);
      const storeId = yield select(selectStoreId);
      const { channel, shippingType } = onlineTransaction;
      let fullBillDiscountItem = null;
      if (hasFullbillDiscount) {
        const discountItem = items[fullBillDiscountItemIndex];
        if (discountItem && discountItem.discountValue && discountItem.discountType) {
          fullBillDiscountItem = {
            inputValue: discountItem.discountValue,
            type: discountItem.discountType,
          };
        }
      }
      const realItems = filter(items, i => !Boolean(i.itemType));
      const itemsParam: OrderItemInputType[] = [];
      for (let index = 0; index < realItems.length; index++) {
        const item = realItems[index];
        const { id, productId, quantity, selectedOptions, notes, manualDiscount } = item;
        const inputValue = getUnNullValue(manualDiscount, 'inputValue', undefined);
        itemsParam.push({ id, productId, quantity, selectedOptions, notes, itemLevelDiscount: inputValue ? manualDiscount : null });
      }
      yield put(
        Actions.orderPreview({
          businessName,
          storeId,
          channel,
          shippingType,
          items: itemsParam,
          fullBillDiscount: fullBillDiscountItem,
        })
      );
      const previewResponseAction = yield take([Actions.orderPreview.toString() + '.success', Actions.orderPreview.toString() + '.failure']);
      if (previewResponseAction.type === Actions.orderPreview.toString() + '.success') {
        const previewOrder = get(previewResponseAction, ['payload', 'orderPreview']);
        const previewOrderItems = get(previewOrder, 'items', []);
        for (let index = 0; index < previewOrderItems.length; index++) {
          const previewOrderItem = previewOrderItems[index];

          if (Boolean(previewOrderItem.type)) {
            if (previewOrderItem.type === 'Discount') {
              items[fullBillDiscountItemIndex].equivalentValue = get(previewOrderItem, 'equivalentValue', 0);
            }
          } else if (Boolean(get(previewOrderItem, ['manualDiscount', 'inputValue']))) {
            const correspondItemIndex = findIndex(items, item => item.id === previewOrderItem.id);
            if (correspondItemIndex > -1) {
              const correspondItem = items[correspondItemIndex];
              if (correspondItem && Boolean(correspondItem.manualDiscount)) {
                correspondItem.manualDiscount.equivalentValue = previewOrderItem.manualDiscount.equivalentValue;
              }
            }
          }
        }
        yield call(setOnlineOpenOrderToTransactionSaga, setOnlineOpenOrderToTransaction({ onlineTransaction }));
        infoTransactionEvent({ action: OpenOrderFlowAction.UnlockPayLaterOrder, orderId: receiptNumber });
        onSuccess && onSuccess.callback();
      } else {
        const tags = [BeepFlowTag.Beep, getBeepOrderShippingTag(onlineTransaction), getPreOrderTag(onlineTransaction), BeepFlowTag.PayLater];
        logFailedServerRequest(BeepFlowAction.PrepareEquivalentValue, tags, get(previewResponseAction, 'payload', ''), null, onlineTransaction);
        onFailed && onFailed.callback(previewResponseAction.payload);
      }
    } else {
      yield call(setOnlineOpenOrderToTransactionSaga, setOnlineOpenOrderToTransaction({ onlineTransaction }));
      infoTransactionEvent({ action: OpenOrderFlowAction.UnlockPayLaterOrder, orderId: receiptNumber });
      onSuccess && onSuccess.callback();
    }
  } else {
    onFailed && onFailed.callback(resp);
    errorTransactionEvent({ action: OpenOrderFlowAction.UnlockPayLaterOrder, reason: resp.message, orderId: receiptNumber });
  }
}

export function getOnlineInputItemLevelDiscount(manualDiscount) {
  const isDiscountNull = get(manualDiscount, 'inputValue', null) === null;
  let itemLevelDiscount = null;
  if (!isDiscountNull) {
    const { type, inputValue } = manualDiscount;
    itemLevelDiscount = { type, inputValue };
  }
  return itemLevelDiscount;
}

export function* saveOnlineOrderSage(action) {
  const { onSuccess, onFailed, goCheckOutAfterSave } = action.payload;
  const currentRecord = yield call(getCurrentTransaction);
  const { receiptNumber, items, modifiedTime } = currentRecord;
  const employeeId = yield select(selectEmployeeId);
  const itemsParam: OrderItemInputType[] = map(
    filter(items, i => !Boolean(i.itemType)),
    item => {
      const { id, productId, quantity, selectedOptions, notes, manualDiscount, isTakeaway } = item;
      return { id, productId, quantity, selectedOptions, notes, isTakeaway, itemLevelDiscount: getOnlineInputItemLevelDiscount(manualDiscount) };
    }
  );

  const discountIndex = findIndex(items, (item: PurchasedItemType) => item.itemType === 'Discount');
  let fullBillDiscount: ManualDiscountInput = null;
  if (discountIndex) {
    const discountItem = items[discountIndex];
    if (discountItem && discountItem.discountValue && discountItem.discountType) {
      fullBillDiscount = {
        inputValue: discountItem.discountValue,
        type: discountItem.discountType,
      };
    }
  }
  yield put(
    Actions.editOnlineOrder({
      receiptNumber,
      modifiedTime: new Date(modifiedTime),
      employeeId,
      items: itemsParam,
      fullBillDiscount,
    })
  );
  const responseAction = yield take([Actions.editOnlineOrder.toString() + '.success', Actions.editOnlineOrder.toString() + '.failure']);
  const tags = [BeepFlowTag.Beep, getBeepOrderShippingTag(currentRecord), getPreOrderTag(currentRecord), BeepFlowTag.PayLater];
  // the orderInfo be tracked in loggly
  const orderInfo = {
    orderId: currentRecord.receiptNumber,
    status: currentRecord.status,
    displayedStatus: getDisplayedStatus(currentRecord),
  };
  if (responseAction.type === Actions.editOnlineOrder.toString() + '.success') {
    const onlineTransaction = responseAction.payload.editOrderItems;
    logSucceedServerRequest(BeepFlowAction.SubmitOrder, tags, null, orderInfo);
    yield spawn(KitchenManager.printOnEditPayLater, currentRecord, onlineTransaction);

    if (goCheckOutAfterSave) {
      // avoid the conflict with printing on checkout
      onlineTransaction.noNeedPrint = true;
      yield call(setOnlineOpenOrderToTransactionSaga, setOnlineOpenOrderToTransaction({ onlineTransaction }));
    } else {
      yield put(Actions.clearTransactionSession());
    }
    onSuccess && onSuccess.callback();
  } else {
    logFailedServerRequest(BeepFlowAction.SubmitOrder, tags, get(responseAction, 'payload', ''), null, orderInfo);
    const { errCode } = get(responseAction, 'payload', {});
    let title = t('Pay Later Save order error');
    let messageInfo = t('Please try again');
    if (errCode === '393479') {
      title = 'Cannot add product';
      messageInfo = 'Only products with tax rates of 0% or 12% can be added to the order';
    }
    onFailed && onFailed.callback({ title, message: messageInfo });
  }
}

export const genarateMultipleOpenOrderSage = function* (action: Action<Actions.GenerateMergeOpenOrderType>) {
  const { selectedOpenOrderIds, onResult, operation } = action.payload;
  if (isEmpty(selectedOpenOrderIds)) {
    onResult && onResult({ mergedTransaction: null });
    return null;
  }
  const currentRecord = yield call(getInitialTransaction);
  const immutableStore = yield select(selectStore);
  const immutableStoreInfo = yield select(selectStoreInfo);
  const enableCashback = yield select(selectEnableCashback);
  const enableLoyalty = yield select(selectEnableLoyalty);
  const inMerge = yield select(selectInMergeOpenOrder);
  const inSplit = yield select(selectInSplitOpenOrder);
  const includingTaxInDisplay = immutableStore.get('includingTaxInDisplay');
  const enableTakeaway = immutableStore.get('enableTakeaway');
  const business = immutableStoreInfo.get('name');
  let masterOpenOrder;
  let masterItems;
  let discountIndex = -1;
  let serviceChargeItem;
  let itemDiscount = 0;
  let totalPax = 0;
  const mergedBranchOrderIds = [];
  let lastTableId;
  let withSameTableId = true;
  for (let i = 0; i < selectedOpenOrderIds.length; i++) {
    const transactionId = selectedOpenOrderIds[i];
    const openOrder = DAL.getTransactionById(transactionId);
    if (!openOrder || !openOrder.isOpen) {
      onResult && onResult({ mergedTransaction: null });
      return null;
    }
    const items = openOrder.items;
    const pax = get(openOrder, 'pax', 0);
    const tableId = get(openOrder, 'tableId');
    if (!Boolean(tableId)) {
      withSameTableId = false;
    } else {
      if (!Boolean(lastTableId)) {
        lastTableId = tableId;
      } else {
        withSameTableId = withSameTableId && lastTableId === tableId;
      }
    }
    totalPax += pax;
    // Keep the basic properties of the master open order
    mergedBranchOrderIds.push(transactionId);
    if (i === 0) {
      masterOpenOrder = openOrder;
      masterItems = items;
      const { transactionId, tableId, createdDate, salesChannel, takeawayCharge, mrs } = masterOpenOrder;
      currentRecord.transactionId = transactionId;
      currentRecord.isOpen = true;
      currentRecord.isOpenOrder = true;
      currentRecord.tableId = tableId;
      currentRecord.createdDate = createdDate;
      currentRecord.mrs = mrs;
      restoreOrderLevelKdsInfo(masterOpenOrder, currentRecord);
      if (enableTakeaway) {
        currentRecord.salesChannel = salesChannel;
        currentRecord.takeawayCharge = takeawayCharge;
      }

      if (Boolean(items) && items.length > 0) {
        for (let j = 0; j < items.length; j++) {
          const item = items[j];
          const itemType = get(item, 'itemType');
          if (itemType !== 'ServiceCharge' && itemType !== 'Discount') {
            const {
              productId,
              options,
              quantity,
              notes,
              sn,
              unitPrice,
              originalQuantity,
              title,
              taxCode,
              itemChannel,
              discountInputValue,
              employeeId,
              employeeName,
            } = item;
            const discountType = getUnNullValue(item, 'discountType', 'amount');
            const discountOption = { inputValue: discountInputValue || 0, type: discountType };
            const variablePrice = unitPrice;
            const parsedOptions = JSONUtils.parse(options, null);
            const kdsInfo = getKdsInfoFromItem(item);
            addPurchasedItemToRecord({
              currentRecord,
              productId,
              options: parsedOptions,
              quantity,
              variablePrice,
              notes,
              sn,
              originalQuantity,
              taxCode,
              title,
              itemChannel,
              return: item.return,
              discountOption,
              immutableStore,
              immutableStoreInfo,
              kdsInfo,
              employeeId,
              employeeName,
            });
            // ----------------------------------------------------------------------------------------------------------------
            if (discountType === 'percent') {
              itemDiscount += ((item.unitPrice * discountInputValue) / 100) * quantity;
            } else {
              itemDiscount += discountInputValue;
            }
          }

          if (itemType === 'Discount') {
            discountIndex = j;
          }
          if (itemType === 'ServiceCharge') {
            serviceChargeItem = item;
          }
        }
      }
    } else {
      if (Boolean(items) && items.length > 0) {
        for (let j = 0; j < items.length; j++) {
          const item = items[j];
          const itemType = get(item, 'itemType');
          if (itemType !== 'ServiceCharge' && itemType !== 'Discount') {
            const { productId, options, quantity, notes, sn, unitPrice, taxCode, itemChannel, employeeId, employeeName } = item;
            const variablePrice = unitPrice;
            const parsedOptions = JSONUtils.parse(options, null);
            const kdsInfo = getKdsInfoFromItem(item);
            const addResult = addPurchasedItemToRecord({
              currentRecord,
              productId,
              options: parsedOptions,
              quantity,
              variablePrice,
              notes,
              sn,
              taxCode,
              itemChannel,
              immutableStore,
              immutableStoreInfo,
              kdsInfo,
              employeeId,
              employeeName,
              conflictEmployeeId: operation === 'mergeAndPay' ? employeeId : null,
            });
            if (addResult.mergeConflict) {
              onResult && onResult({ mergedTransaction: null, mergeConflict: true });
              return null;
            }
          }
        }
      }
    }
  }

  if (inMerge) {
    currentRecord.mergedBranchOrderIds = mergedBranchOrderIds;
    yield put(Actions.setMergedBranchOrderIds(mergedBranchOrderIds));
  } else if (inSplit) {
    currentRecord.isSplitMaster = true;
  }
  currentRecord.pax = totalPax;
  let originalFullBillDiscount = 0;
  if (masterOpenOrder.appVersion === '2.23.1.0') {
    // version before android 1.6 doesn't save fullBillDiscount in database
    const discount = get(masterOpenOrder, 'discount');
    const inputValue = discount - itemDiscount;
    const type = 'amount';
    originalFullBillDiscount = inputValue;
    addFullBillDiscountToRecord({
      currentRecord,
      inputValue,
      type,
      displayFullBillDiscountValue: inputValue,
      isDiscountEnable: true,
      displayFullBillDiscountEnable: true,
    });
  } else if (discountIndex > -1) {
    const item = masterItems[discountIndex];
    const inputValue = get(item, 'discountInputValue');
    originalFullBillDiscount = inputValue;
    const type = get(item, 'discountType');
    if (!isNaN(inputValue) && Boolean(type)) {
      addFullBillDiscountToRecord({
        currentRecord,
        inputValue,
        type,
        displayFullBillDiscountValue: inputValue,
        isDiscountEnable: true,
        displayFullBillDiscountEnable: true,
      });
    }
  }
  // ---------------------------------------Full Bill Discount and Service Charge-----------------------------------------------
  let serviceChargeRate = masterOpenOrder.serviceChargeRate;
  const serviceChargeTax = masterOpenOrder.serviceChargeTaxId;
  const searchedIndex = findIndex(currentRecord.items, (item: PurchasedItemType) => item.itemType === 'ServiceCharge');
  if (get(serviceChargeItem, 'subTotal', 0) === 0 && searchedIndex != -1) {
    serviceChargeRate = 0;
  }
  if (Boolean(serviceChargeItem)) {
    const rate = get(serviceChargeItem, 'rate');
    if (isValidNumber(rate)) {
      serviceChargeRate = rate;
    }
  }
  const taxRate = getServiceChargeTaxRateWithStore(immutableStore, masterOpenOrder.serviceChargeTaxId);
  setServiceChargeToRecord({ currentRecord, itemIndex: searchedIndex, serviceChargeRate, serviceChargeTax, taxRate });

  yield call(restoreBirInfoToTransaction, masterOpenOrder, currentRecord);

  yield call(checkItemsDiffTax, currentRecord, enableCashback, enableLoyalty);

  yield call(tryApplyPromotion, currentRecord, false);
  // ------------------------------------------------------------------------------------------------------------------

  const newItems = yield call(updateTransactionWithAppliedPriceBooks, {
    transaction: currentRecord,
    appliedPriceBooks: currentRecord.appliedPriceBooks,
    inTransaction: true,
  });
  currentRecord.items = newItems;
  const now = new Date();
  const employeeId = yield select(selectEmployeeId);
  currentRecord.employeeId = employeeId;
  currentRecord.modifiedDate = now.toISOString();
  try {
    calculateP(currentRecord, includingTaxInDisplay || false);
    conversionDiscountInLoyaltyDiscounts(currentRecord);
  } catch (ex) {
    if (ex) {
      yield put(Actions.toggleToastInfo({ visible: true, text: ex.message }));
    }
  }

  let haveCustomer = false;
  // addCustomerToTransactionSaga
  const { customerId, loyaltyDiscounts } = masterOpenOrder;
  if (Boolean(customerId)) {
    yield put(Actions.getCustomerById({ customerId, bn: business }));
    const responseAction = yield take([Actions.getCustomerById.toString() + '.success', Actions.getCustomerById.toString() + '.failure']);
    if (responseAction.type === Actions.getCustomerById.toString() + '.success') {
      const resp = responseAction.payload;
      if (Boolean(resp)) {
        const appliedPriceBooks = yield call(getAppliedPriceBooks, resp);
        const { success } = yield call(addCustomerToRecord, { currentRecord, immutableStore, appliedPriceBooks, customer: resp, customerId });
        haveCustomer = success;
      }
    }
  }
  const haveLoyalty = enableLoyalty && Boolean(loyaltyDiscounts) && loyaltyDiscounts.length > 0;
  const needApplyLoyaltyDiscount = haveLoyalty && currentRecord.isLoyaltyCanEnable;
  // updateLoyaltyDiscountsSaga
  if (needApplyLoyaltyDiscount) {
    const { type, inputValue } = loyaltyDiscounts[0];
    setLoyaltyDiscounts({ currentRecord, inputValue, isRefund: false, type });
  }
  if (haveCustomer || needApplyLoyaltyDiscount) {
    try {
      calculateP(currentRecord, includingTaxInDisplay || false);
      conversionDiscountInLoyaltyDiscounts(currentRecord);
    } catch (ex) {
      console.log('addCustomerToTransactionSaga exception', ex);
    }
  }

  let warning;
  if ((originalFullBillDiscount > 0 && currentRecord.isFullBillDiscountCanEnable === false) || (haveLoyalty && currentRecord.isLoyaltyCanEnable === false)) {
    warning = { title: t('Selected orders have different tax rates'), message: t('If you choose to merge the order discounts and cashback will be removed') };
  }
  onResult && onResult({ mergedTransaction: currentRecord, warning, withSameTableId });
  return currentRecord;
};

// mergeToSave: merge the order and save the merged OpenOrder to local DB， and delete the original Open orders
export function* mergeToSaveSage(action: Action<MergeOpenOrderType>) {
  const { mergedTransaction, onComplete } = action.payload;
  const items = map(mergedTransaction.items, item => {
    const { options, seniorDiscount, pwdDiscount } = item;
    const newItem = { ...item, options: stringifyItemOptions(options) };
    if (!Boolean(seniorDiscount)) delete newItem.seniorDiscount;
    if (!Boolean(pwdDiscount)) delete newItem.pwdDiscount;
    return newItem;
  });
  const proposeData: ProposeDataType[] = [];
  // @ts-ignore
  mergedTransaction.items = items;
  const isLimit: SagaReturnType<typeof checkLimitBeforeExecuteSaga> = yield call(
    checkLimitBeforeExecuteSaga,
    checkLimitBeforeExecute({
      transaction: mergedTransaction,
      orderOperation: OrderOperationEnum.MergeAndSave,
    })
  );
  if (isLimit.errorCode !== SuccessCode) {
    onMRSInterceptor(isLimit);
    onComplete(isLimit);
    return isLimit;
  }
  const mergedBranchOrderIds: string[] = getUnNullValue(mergedTransaction, 'mergedBranchOrderIds', []);

  const isNeedMRS = yield call(checkIsNeedMRSSaga, OrderOperationEnum.MergeAndSave, mergedTransaction);
  let mergeSavedSuccess = true;
  const isKdsPaired = yield select(selectIsKdsPaired);
  const mergeFromTransactionList =
    isKdsPaired && !isNeedMRS ? DAL.getJsTransactionByIds(mergedBranchOrderIds.filter(it => it !== mergedTransaction.transactionId)) : [];
  if (isNeedMRS) {
    mergedTransaction.mrs = true;
    proposeData.push({
      operationType: OperationType.UPDATE,
      // @ts-ignore
      transaction: { ...mergedTransaction, loyaltyDiscounts: mergedTransaction.loyaltyDiscounts ?? [], customerId: mergedTransaction.customerId ?? null },
    });
  } else {
    yield call(updateCookingStatusFromItems, mergedTransaction);
    const saved = yield call(DAL.updateOpenOrder, mergedTransaction);
    mergeSavedSuccess = mergeSavedSuccess && saved;
  }

  if (mergedBranchOrderIds.length > 0) {
    for (const branchOpenOrderId of mergedBranchOrderIds) {
      if (branchOpenOrderId == mergedTransaction.transactionId) {
        // All the orders are merged into the first order, we reuse the first order
        // to generate the mergedTransaction, which should not be deleted.
        continue;
      }
      if (isNeedMRS) {
        const branchTransaction = DAL.getTransactionById(branchOpenOrderId);
        if (branchTransaction) {
          proposeData.push({
            operationType: OperationType.DELETE,
            // @ts-ignore
            transaction: { transactionId: branchOpenOrderId, isOpen: true, tableId: branchTransaction.tableId },
          });
        }
      } else {
        const notification = getPrinterNotification(branchOpenOrderId);
        const noFailedJob = !notification || (isEmpty(notification.jobs) && notification.isKitchenPrinted);
        if (noFailedJob) {
          const saved = yield call(DAL.deleteTransactionById, branchOpenOrderId);
          mergeSavedSuccess = mergeSavedSuccess && saved;
        } else {
          const saved = DAL.updateTransaction(branchOpenOrderId, {
            isDeleted: true,
            tableId: mergedTransaction.tableId,
            pickUpId: mergedTransaction.pickUpId,
          });
          mergeSavedSuccess = mergeSavedSuccess && saved;
        }
      }
    }
  }
  infoTransactionEvent({
    action: OpenOrderFlowAction.MergeOpenOrder,
    transaction: mergedTransaction,
    privateDataPayload: { mrs: isNeedMRS, mergedBranchOrderIds },
  });
  if (mergeSavedSuccess && mergeFromTransactionList.length > 0) {
    yield spawn(pushLocalOpenOrdersSaga, [mergedTransaction.transactionId], { mergeToId: mergedTransaction.transactionId, mergeFromTransactionList });
  }

  let result = SuccessMessage();
  if (proposeData.length > 0) {
    result = yield call(
      requestProposalActionSaga,
      requestProposalAction({
        data: proposeData,
        operation: OrderOperationEnum.MergeAndSave,
      })
    );
    if (result.errorCode !== SuccessCode) {
      openErrorToast(t('Merge failed, please try again'));
      onComplete(result);
      return result;
    }
  }
  yield call(completeMergeToSave, mergedBranchOrderIds);
  openSuccessToast();
  onComplete(result);
  return result;
}

export function* completeMergeToSave(mergedBranchOrderIds: string[]) {
  // fix CM-7848
  if (mergedBranchOrderIds && mergedBranchOrderIds.length > 0) {
    const immutableTransaction = yield select(selectTransactionSession);
    if (immutableTransaction && mergedBranchOrderIds.includes(immutableTransaction.get('transactionId'))) {
      yield put(Actions.clearTransactionSession());
    }
  }
  yield put(Actions.completeMergeSplitOpenOrder());
}

// mergeToPay: merge the order and setTransaction to redux to display, and go to CheckOut page
// once paid success, save the new order and delete the original Open orders
export function* mergeToPaySage(action) {
  const { mergedTransaction } = action.payload;
  yield put(Actions.setTransactionSession(mergedTransaction));
}

export function* moveOpenOrderSage(action: Action<MoveOpenOrderType>) {
  const { pendingMoveOrders, tableId, onComplete } = action.payload;

  const localOrderIds = chain(pendingMoveOrders)
    .filter(v => !v.isPayLater)
    .map(v => v.id)
    .value();

  const payLaterOrder = chain(pendingMoveOrders)
    .filter(v => v.isPayLater)
    .get(0)
    .value();

  let offlineError = SuccessMessage();
  let onlineError = SuccessMessage();
  const movedLocalOpenOrderIds: string[] = [];
  if (payLaterOrder) {
    const { id, modifiedTime, srcTableId } = payLaterOrder;
    const employeeId = yield select(selectEmployeeId);
    const businessName = yield select(selectBusinessName);
    const storeId = yield select(selectStoreId);
    yield put(Actions.editOrderTable({ receiptNumber: id, modifiedTime, employeeId, tableId, businessName, storeId }));
    const responseAction = yield take([Actions.editOrderTable.toString() + '.success', Actions.editOrderTable.toString() + '.failure']);
    if (responseAction.type === Actions.editOrderTable.toString() + '.success') {
      const success = responseAction.payload.editOrderTable.success;
      const code = responseAction.payload.editOrderTable.code;
      if (success) {
        // reprint the kitchen docket, in which, print the source table with strikethrogh and destination table,
        // once the order table change success.
        yield put(Actions.getOnlineOrderDetail({ orderId: id }));
        const detailResponseAction = yield take([Actions.getOnlineOrderDetail.toString() + '.success', Actions.getOnlineOrderDetail.toString() + '.failure']);
        if (detailResponseAction.type === Actions.getOnlineOrderDetail.toString() + '.success') {
          const payLaterOrderDetail = detailResponseAction.payload;
          yield spawn(KitchenManager.printOnMoveOnlineOrder, payLaterOrderDetail?.onlineOrder, srcTableId, tableId);
        }
      } else {
        onlineError = createToastError(EditOnlineOrderErrorMap.get(code));
      }
    } else {
      if (responseAction.payload.editableTimeAfterForPayLaterOrder) {
        const editableTimeAfterForPayLaterOrder = moment(responseAction.payload.editableTimeAfterForPayLaterOrder).format('hh:mmA');
        NavigationService.navigate({
          routeName: 'ModalInfo',
          params: {
            title: t('This order is being checked out by customer'),
            isShowTitle: true,
            textAlign: 'center',
            info: t('Order can be edited at', { editableTimeAfterForPayLaterOrder }),
          },
        });
        yield put(Actions.completeMoveOpenOrder());
        return;
      } else {
        onlineError = createToastError();
      }
    }
  }

  const proposeData: ProposeDataType[] = [];
  const offlinePrintParams: { previousTableId: string; order: TransactionType }[] = [];

  if (Boolean(localOrderIds) && localOrderIds.length > 0) {
    const modifiedDate = new Date().toISOString();
    for (const orderId of localOrderIds) {
      const transaction = DAL.getPlainTransactionById(orderId);
      if (!transaction) {
        continue;
      }
      const isLimit: SagaReturnType<typeof checkLimitBeforeExecuteSaga> = yield call(
        checkLimitBeforeExecuteSaga,
        checkLimitBeforeExecute({
          transaction,
          orderOperation: OrderOperationEnum.ChangeTableId,
        })
      );
      if (isLimit.errorCode !== SuccessCode) {
        offlineError = isLimit;
        continue;
      }
      offlinePrintParams.push({
        previousTableId: transaction.tableId,
        order: { ...transaction, tableId: tableId, modifiedDate },
      });

      const isNeedMRS = yield call(checkIsNeedMRSSaga, OrderOperationEnum.ChangeTableId, transaction);
      if (isNeedMRS) {
        proposeData.push({
          operationType: Actions.OperationType.UPDATE,
          transaction: {
            ...transaction,
            tableId,
            // @ts-ignore
            modifiedDate,
          },
        });
      } else {
        const saved = yield call(DAL.updateTransaction, orderId, { tableId, modifiedDate });
        if (saved) {
          movedLocalOpenOrderIds.push(orderId);
        }
      }
    }
  }
  if (movedLocalOpenOrderIds.length > 0) {
    yield spawn(pushLocalOpenOrdersSaga, movedLocalOpenOrderIds);
  }

  if (proposeData.length > 0) {
    offlineError = yield call(
      requestProposalActionSaga,
      requestProposalAction({
        data: proposeData,
        operation: OrderOperationEnum.ChangeTableId,
      })
    );
  }

  infoTransactionEvent({ action: OpenOrderFlowAction.MoveOpenOrder, privateDataPayload: { orders: pendingMoveOrders, tableId } });

  if (offlineError.errorCode === SuccessCode && offlinePrintParams.length > 0) {
    for (const printParams of offlinePrintParams) {
      yield spawn(KitchenManager.printOnMoveTable, printParams.previousTableId, printParams.order);
    }
  }

  if (onlineError.errorCode !== SuccessCode && offlineError.errorCode !== SuccessCode) {
    openErrorToast();
    onComplete(UnknownError);
  } else if (onlineError.errorCode !== SuccessCode) {
    openErrorToast(onlineError.errorMessage);
    onComplete(onlineError);
  } else if (offlineError.errorCode !== SuccessCode) {
    onMRSInterceptor(offlineError, t('Error moving offline orders'));
    onComplete(offlineError);
  } else {
    onComplete(SuccessMessage());
    openSuccessToast(t('Order Moved Successfully'), Boolean(payLaterOrder) ? 0 : 3 * 1000);
  }
  yield put(Actions.completeMoveOpenOrder());
}

export function* changeOpenOrderTableSaga(action) {
  const { tableId } = action.payload;
  const transaction = yield call(getCurrentTransaction);
  transaction.isTableChanged = true;
  transaction.tableId = tableId;
  infoTransactionEvent({ action: OpenOrderFlowAction.ChangeOpenOrderTable, transaction });
  yield put(Actions.setTransactionSession(transaction));
}

export function* setOrderToSplitMasterSaga(action: Action<Actions.SetOrderToSplitTargetType>) {
  let transactionId = get(action, ['payload', 'transactionId']);
  const onComplete = get(action, ['payload', 'onComplete']);
  // if the transactionId is empty, thet means we are re-init the split master order
  if (!Boolean(transactionId)) {
    const splitTargetTransaction = yield select(selectSplitTargetOrder);
    transactionId = get(splitTargetTransaction, 'transactionId');
  }
  const transaction = yield call(genarateMultipleOpenOrderSage, Actions.genarateMergeOpenOrder({ selectedOpenOrderIds: [transactionId] }));
  if (transaction) {
    yield put(Actions.initSplitOrder(transaction));
    onComplete && onComplete(true);
  } else {
    openErrorToast(t('The order has been deleted'));
    onComplete && onComplete(false);
  }
}

export function* setOnlineOrderToSplitMasterSaga(action: Action<SetOnlineOrderToSplitMasterType>) {
  const { receiptNumber, transactionId, tableId } = action.payload;
  const orderInfo = {
    orderId: receiptNumber,
  };
  const tags = [BeepFlowTag.Beep, BeepFlowTag.SplitOrder, BeepFlowTag.PayLater];
  yield put(Actions.onlineOrderSplittedPreview({ receiptNumber, splittedItems: [] }));
  const responseAction = yield take([Actions.onlineOrderSplittedPreview.toString() + '.success', Actions.onlineOrderSplittedPreview.toString() + '.failure']);
  const mainOrder = get(responseAction, 'payload.orderSplittedPreview.mainOrder', undefined);

  if (responseAction.type === Actions.onlineOrderSplittedPreview.toString() + '.success' && mainOrder) {
    const order = mapSplitPayLaterOrder(mainOrder, { receiptNumber, transactionId, tableId });
    yield put(Actions.initSplitOrder(order));
    navigate({
      routeName: 'SplitOrder',
    });
    logSucceedServerRequest(BeepFlowAction.SplitPayLaterOrder, tags, null, orderInfo);
  } else {
    openErrorToast(t('Failed to split order'));
    logFailedServerRequest(BeepFlowAction.SplitPayLaterOrder, tags, get(responseAction, 'payload', ''), null, orderInfo);
  }
}

type SplitOrderAddonInfo = {
  receiptNumber: string;
  transactionId?: string;
  tableId?: string;
};
export function mapSplitPayLaterOrder(previewOrder: OrderPreview, addonInfo: SplitOrderAddonInfo) {
  const { receiptNumber, transactionId = '', tableId = '' } = addonInfo;
  const mappedOrder: any = {
    receiptNumber,
    transactionId,
    tableId,
    isPayLater: true,
    isOnlineOrder: true,
    isOpen: true,
    ...previewOrder,
  };
  if (mappedOrder.items) {
    mappedOrder.items = map(mappedOrder.items, it => {
      return {
        ...it,
        itemType: it.type,
        display: {
          total: it.total,
          subtotal: it.subTotal,
          tax: it.tax,
        },
        options: it.selectedOptions,
      };
    });
  }
  mappedOrder.display = {
    subtotal: previewOrder.subTotal,
    discount: previewOrder.discount,
    serviceCharge: previewOrder.serviceCharge,
    tax: previewOrder.tax,
    total: previewOrder.total,
  };
  return mappedOrder as TransactionTypeWithDisplay;
}

export const parsedTransaction = transaction => {
  if (!isEmpty(transaction)) {
    if (transaction.items) {
      transaction.items = JSON.parse(JSON.stringify(transaction.items));
    }

    if (transaction.serialNumbers) {
      transaction.serialNumbers = JSON.parse(JSON.stringify(transaction.serialNumbers));
    }

    if ((transaction.transactionType === TransactionFlowType.Return && Boolean(transaction.originalReceiptNumber)) || Boolean(transaction.isCompleted)) {
      transaction.appliedPriceBooks = [];
    }
  }
};

export const getSplitSaveOrder = function (transaction, employeeId: string, isSplitMaster: boolean) {
  const transactionId = get(transaction, 'transactionId');
  infoTransactionEvent({ action: OpenOrderFlowAction.SplitOpenOrder, transaction, privateDataPayload: { isSplitMaster } });
  if (!transactionId) {
    return null;
  }
  parsedTransaction(transaction);
  transaction.isOpen = true;
  transaction.isOpenOrder = true;
  const now = new Date();
  transaction.employeeId = employeeId;
  transaction.modifiedDate = now.toISOString();
  const items = map(transaction.items, item => {
    return { ...item, options: stringifyItemOptions(item.options) };
  });
  transaction.items = items;
  if (!isSplitMaster) {
    transaction.createdDate = now.toISOString();
  }
  return transaction;
};

export function* saveSplitOrderWithMRSSaga(action: Action<SaveSplitOrderType>) {
  const { onComplete } = action.payload;
  const employeeId = yield select(selectEmployeeId);
  const splitTargetTransaction = yield select(selectSplitTargetOrder);

  let error = SuccessMessage();
  const proposeData: ProposeDataType[] = [];
  const isLimit: SagaReturnType<typeof checkLimitBeforeExecuteSaga> = yield call(
    checkLimitBeforeExecuteSaga,
    checkLimitBeforeExecute({
      transaction: splitTargetTransaction,
      orderOperation: OrderOperationEnum.SplitAndSave,
    })
  );
  if (isLimit.errorCode !== SuccessCode) {
    onMRSInterceptor(isLimit);
    onComplete(isLimit);
    return isLimit;
  }
  const isMasterTransactionEmpty = getDisplayItemsCount(get(splitTargetTransaction, 'items', [])) === 0;
  if (isMasterTransactionEmpty) {
    const transactionId = get(splitTargetTransaction, 'transactionId');
    if (transactionId) {
      proposeData.push({ operationType: OperationType.DELETE, transaction: splitTargetTransaction });
    }
  } else {
    const transaction = getSplitSaveOrder(splitTargetTransaction, employeeId, true);
    if (transaction) {
      transaction.mrs = true;
      proposeData.push({
        operationType: OperationType.UPDATE,
        // JSON.parse(JSON.stringify({b: undefined})) will remove property b
        transaction: { ...transaction, loyaltyDiscounts: transaction.loyaltyDiscounts ?? [], customerId: transaction.customerId ?? null },
      });
    }
  }

  const splitedTransaction = yield select(selectSplitedOrder);

  const transaction = getSplitSaveOrder(splitedTransaction, employeeId, false);
  if (transaction) {
    transaction.mrs = true;
    proposeData.push({
      operationType: OperationType.INSERT,
      transaction,
    });
  }

  if (proposeData.length > 0) {
    error = yield call(
      requestProposalActionSaga,
      requestProposalAction({
        data: proposeData,
        operation: OrderOperationEnum.SplitAndSave,
      })
    );
  }

  if (error.errorCode === SuccessCode) {
    yield call(completeSaveSplitOrder, splitTargetTransaction?.transactionId);
  } else {
    openErrorToast(t('Split failed, please try again'));
  }
  onComplete(error);
  return error;
}

export function* saveSplitOrderLocalSaga(action: Action<SaveSplitOrderType>) {
  const splitTargetTransaction = yield select(selectSplitTargetOrder);
  const { onComplete } = action.payload;
  const employeeId = yield select(selectEmployeeId);
  let splitSavedSuccess = true;
  // let splitFromTransaction;
  const isMasterTransactionEmpty = getDisplayItemsCount(get(splitTargetTransaction, 'items', [])) === 0;
  if (isMasterTransactionEmpty) {
    const transactionId = get(splitTargetTransaction, 'transactionId');
    if (transactionId) {
      const notification = DAL.getBeepNotificationById(transactionId);
      // Print kitchen order
      if (isEmpty(get(notification, 'jobs', []))) {
        const saved = DAL.deleteTransactionById(transactionId);
        splitSavedSuccess = splitSavedSuccess && saved;
      } else {
        const saved = DAL.updateTransaction(transactionId, { isDeleted: true });
        splitSavedSuccess = splitSavedSuccess && saved;
      }
    }
  } else {
    const transaction = getSplitSaveOrder(splitTargetTransaction, employeeId, true);
    if (transaction) {
      yield call(updateCookingStatusFromItems, transaction);
      const saved = DAL.updateOpenOrder(transaction);
      splitSavedSuccess = splitSavedSuccess && saved;
    }
  }

  const splitedTransaction = yield select(selectSplitedOrder);

  const transaction = getSplitSaveOrder(splitedTransaction, employeeId, false);
  if (transaction) {
    yield call(updateCookingStatusFromItems, transaction);
    const saved = DAL.saveTransaction(transaction);
    splitSavedSuccess = splitSavedSuccess && saved;
  }

  if (splitSavedSuccess && splitTargetTransaction?.transactionId) {
    yield spawn(pushLocalOpenOrdersSaga, [splitTargetTransaction.transactionId], {
      splitToId: splitedTransaction.transactionId,
      // splitFromTransaction,
      splitFromId: splitTargetTransaction.transactionId,
    });
  }
  yield call(completeSaveSplitOrder, splitTargetTransaction?.transactionId);
  const error = SuccessMessage();
  onComplete(error);
  return error;
}
export function* saveSplitOrderSaga(action: Action<SaveSplitOrderType>) {
  const splitTargetTransaction = yield select(selectSplitTargetOrder);
  const isNeedMRS: SagaReturnType<typeof checkIsNeedMRSSaga> = yield call(checkIsNeedMRSSaga, OrderOperationEnum.SplitAndSave, splitTargetTransaction);
  if (isNeedMRS) {
    return yield call(saveSplitOrderWithMRSSaga, action);
  } else {
    return yield call(saveSplitOrderLocalSaga, action);
  }
}

function* completeSaveSplitOrder(splitMasterTransactionId?: string) {
  const immutableTransaction = yield select(selectTransactionSession);
  // fix CM-7848
  if (immutableTransaction && splitMasterTransactionId && splitMasterTransactionId === immutableTransaction.get('transactionId')) {
    yield put(Actions.clearTransactionSession());
  }
  yield put(Actions.completeMergeSplitOpenOrder());
}

export function* saveSplitOnlineOrderSaga(action: Action<SaveSplitOnlineOrderType>) {
  const { onComplete } = action.payload;
  const employeeId = yield select(selectEmployeeId);
  const businessName = yield select(selectBusinessName);
  const splitTargetOrder = yield select(selectSplitTargetOrder);
  const { receiptNumber, modifiedTime } = splitTargetOrder;
  const splitOrder = yield select(selectSplitedOrder);
  const signature = signatureSaveOrder(
    {
      businessName,
      receiptNumber,
      employeeId,
    },
    splitTargetOrder.transactionId
  );
  const length = get(splitOrder, 'items.length', 0);
  if (length === 0) {
    yield put(Actions.completeMergeSplitOpenOrder());
    onComplete({ success: false, isCancelled: true, needGoBack: true });
    return;
  }
  const splitItems = chain(splitOrder.items)
    .filter(it => it.itemType !== 'ServiceCharge')
    .map(it => ({ id: it.id, quantity: String(it.quantity) }))
    .value();

  const splittedOrders: [SplitOrderInput] = [
    {
      tableId: splitOrder.tableId,
      items: splitItems,
      transactionId: splitOrder.transactionId || getUUIDValue(),
    },
  ];
  const orderInfo = {
    orderId: receiptNumber,
  };
  const tags = [BeepFlowTag.Beep, BeepFlowTag.SplitOrder, BeepFlowTag.PayLater];
  yield put(Actions.splitOnlineOrder({ receiptNumber, modifiedTime, employeeId, splittedOrders, signature }));
  const responseAction = yield take([Actions.splitOnlineOrder.toString() + '.success', Actions.splitOnlineOrder.toString() + '.failure']);
  if (responseAction.type === Actions.splitOnlineOrder.toString() + '.success') {
    logSucceedServerRequest(BeepFlowAction.SaveSplitOrder, tags, null, orderInfo);

    const success = responseAction.payload.splitOrder.success;
    const code = responseAction.payload.splitOrder.code;
    if (!success) {
      onComplete({ success: false, tableCode: code });
      return;
    } else {
      yield put(Actions.completeMergeSplitOpenOrder());
      onComplete({ success: true, needGoBack: true });
    }
  } else {
    logFailedServerRequest(BeepFlowAction.SaveSplitOrder, tags, get(responseAction, 'payload', ''), null, orderInfo);
    onComplete({ success: false, editableTimeAfterForPayLaterOrder: get(responseAction.payload, 'editableTimeAfterForPayLaterOrder', '') });
  }
}

export function* editSplitOrderSaga(action) {
  const { itemIndex, isPutBack } = action.payload;
  const splitTargetTransaction = yield select(selectSplitTargetOrder);
  parsedTransaction(splitTargetTransaction);
  let splitedTransaction = yield select(selectSplitedOrder);
  if (isEmpty(splitedTransaction)) {
    splitedTransaction = yield call(getInitialTransaction);
    splitedTransaction.splitFromId = get(splitTargetTransaction, 'transactionId');
    const enableTableLayout = yield select(selectEnableTableLayout);
    const tableLayoutEnabled = yield select(selectTableLayoutEnabled);
    const masterTableId = get(splitTargetTransaction, 'tableId');
    if (enableTableLayout && tableLayoutEnabled && Boolean(masterTableId)) {
      splitedTransaction.tableId = masterTableId;
    } else {
      splitedTransaction.tableId = 'split 1';
    }
    splitedTransaction.isOpen = true;
    splitedTransaction.isOpenOrder = true;
  } else {
    parsedTransaction(splitedTransaction);
  }
  let reducedTransaction = isPutBack ? splitedTransaction : splitTargetTransaction;
  const increasedTransaction = isPutBack ? splitTargetTransaction : splitedTransaction;
  const item = { ...reducedTransaction.items[itemIndex] };
  const { productId } = item;
  if (!Boolean(productId) || isEmpty(item)) return;
  const product = DAL.getProductById(item.productId);
  if (!Boolean(product)) return;
  // ------------------- go on -------------------
  const immutableStore = yield select(selectStore);
  const includingTaxInDisplay = immutableStore.get('includingTaxInDisplay');
  const immutableStoreInfo = yield select(selectStoreInfo);
  const subscriptionPlan = immutableStoreInfo.get('subscriptionPlan');
  const enableCashback = yield select(selectEnableCashback);
  const enableLoyalty = yield select(selectEnableLoyalty);

  const prevQuantity = item.quantity;
  const difference = prevQuantity >= 1 ? 1 : prevQuantity;
  const newQuantity = prevQuantity - difference;

  // if the splited order become to empty after the reduce, then init the split
  if (isPutBack && newQuantity <= 0 && filter(get(reducedTransaction, 'items', []), item => !Boolean(item.itemType)).length === 1) {
    const { transactionId } = splitTargetTransaction;
    yield put(Actions.setOrderToSplitMaster({ transactionId }));
    return;
  }

  // ------------------- update increasedTransaction order -------------------
  //  qunatity+1

  const { options, notes, sn, unitPrice, taxCode, itemChannel, originalQuantity, employeeId, employeeName } = item;
  const variablePrice = unitPrice;
  const addPurchasedItemSuccess = addPurchasedItemToRecord({
    currentRecord: increasedTransaction,
    productId,
    options,
    quantity: difference,
    variablePrice,
    notes,
    sn,
    taxCode,
    itemChannel,
    immutableStore,
    immutableStoreInfo,
    kdsInfo: getKdsInfoFromItem(item),
    employeeId,
    employeeName,
  });
  if (!addPurchasedItemSuccess) return;

  yield call(checkItemsDiffTax, increasedTransaction, enableCashback, enableLoyalty);

  if (!Boolean(originalQuantity)) {
    yield call(tryApplyPromotion, increasedTransaction, false);
  }

  const newItems = yield call(updateTransactionWithAppliedPriceBooks, {
    transaction: increasedTransaction,
    appliedPriceBooks: increasedTransaction.appliedPriceBooks,
    inTransaction: true,
  });
  increasedTransaction.items = newItems;
  try {
    calculateP(increasedTransaction, includingTaxInDisplay || false);
    conversionDiscountInLoyaltyDiscounts(increasedTransaction);
  } catch (exception) {
    if (exception) {
      console.log('calculate exception: splitorder_increase   -->>', exception.message);
      yield put(Actions.toggleToastInfo({ visible: true, text: exception.message }));
    }
  }

  // ------------------- update reducedTransaction order -------------------
  //  qunatity-1

  item.quantity = newQuantity;
  if (newQuantity === 0) {
    reducedTransaction.items.splice(itemIndex, 1);
  } else {
    const newPurchasedItem = checkPriceBooks({ purchasedItem: item, transaction: reducedTransaction, product, immutableStore, subscriptionPlan });
    reducedTransaction.items[itemIndex] = newPurchasedItem;
  }

  yield call(checkItemsDiffTax, reducedTransaction, enableCashback, enableLoyalty);
  yield call(tryApplyPromotion, reducedTransaction, false);

  try {
    reducedTransaction = calculateP(reducedTransaction, includingTaxInDisplay || false);
    conversionDiscountInLoyaltyDiscounts(reducedTransaction);
  } catch (exception) {
    console.log('calculate exception: splitorder_reduce   -->>', exception.message);
    // yield put(Actions.toggleToastInfo({ visible: true, text: exception.message }));
  }

  if (isPutBack) {
    yield put(Actions.setSplitMasterOrder(increasedTransaction));
    yield put(Actions.setSplitedOrder(reducedTransaction));
  } else {
    yield put(Actions.setSplitMasterOrder(reducedTransaction));
    yield put(Actions.setSplitedOrder(increasedTransaction));
  }
}

export function* editSplitOnlineOrderSaga(action: Action<EditSplitOrderType>) {
  const { itemIndex, isPutBack } = action.payload;
  const mainTransaction = yield select(selectSplitTargetOrder);
  let splitTransaction = yield select(selectSplitedOrder);
  if (isEmpty(splitTransaction)) {
    splitTransaction = {
      items: [],
    };
  }
  const reducedTransaction = isPutBack ? splitTransaction : mainTransaction;
  const editItem = get(reducedTransaction, ['items', itemIndex], undefined);
  if (!editItem) {
    openErrorToast(t('Failed to split order'));
    return;
  }

  if (isPutBack) {
    editItem.quantity -= 1;
    if (editItem.quantity <= 0) {
      splitTransaction.items.splice(itemIndex, 1);
    }
  } else {
    const splitItem = find(splitTransaction.items, v => v.id === editItem.id);
    if (splitItem) {
      splitItem.quantity += 1;
    } else {
      splitTransaction.items.push({ id: editItem.id, quantity: 1 });
    }
  }

  const receiptNumber = get(mainTransaction, 'receiptNumber', '');
  const transactionId = get(mainTransaction, 'transactionId', '');

  const orderInfo = {
    orderId: receiptNumber,
  };
  const tags = [BeepFlowTag.Beep, BeepFlowTag.SplitOrder, BeepFlowTag.PayLater];

  if (receiptNumber && transactionId) {
    const splittedItems = chain(splitTransaction.items)
      .filter(it => it.itemType !== 'ServiceCharge')
      .map(it => ({ id: it.id, quantity: it.quantity }))
      .value();
    yield put(Actions.onlineOrderSplittedPreview({ receiptNumber, splittedItems }));
    const responseAction = yield take([Actions.onlineOrderSplittedPreview.toString() + '.success', Actions.onlineOrderSplittedPreview.toString() + '.failure']);
    const mainOrder = get(responseAction, 'payload.orderSplittedPreview.mainOrder', undefined);
    if (responseAction.type === Actions.onlineOrderSplittedPreview.toString() + '.success' && mainOrder) {
      const splittedOrder = responseAction.payload.orderSplittedPreview.splittedOrder;
      logSucceedServerRequest(BeepFlowAction.SplitPayLaterOrder, tags, null, orderInfo);
      const tableId = get(mainTransaction, 'tableId', '');
      const masterOrder = mapSplitPayLaterOrder(mainOrder, { receiptNumber, transactionId, tableId });
      if (splittedOrder) {
        yield put(setSplitMasterOrder(masterOrder));
        const sTableId = get(splitTransaction, 'tableId', '');
        yield put(setSplitedOrder(mapSplitPayLaterOrder(splittedOrder, { receiptNumber, tableId: sTableId })));
      } else {
        yield put(Actions.initSplitOrder(masterOrder));
      }
    } else {
      openErrorToast(t('Failed to split order'));
      logFailedServerRequest(BeepFlowAction.SplitPayLaterOrder, tags, get(responseAction, 'payload', ''), null, orderInfo);
    }
  } else {
    openErrorToast(t('Failed to split order'));
  }
}

export function* updateSplitOnlineOrderAfterCheckOutSaga(action: Action<UpdateSplitOrderAfterCheckOutType>) {
  const { isSplitMaster } = action.payload;
  if (!isSplitMaster) {
    const splitTargetTransaction = yield select(selectSplitTargetOrder);
    const { receiptNumber, transactionId, tableId } = splitTargetTransaction;
    const orderInfo = {
      orderId: receiptNumber,
    };
    const tags = [BeepFlowTag.Beep, BeepFlowTag.SplitOrder, BeepFlowTag.PayLater];
    yield put(Actions.onlineOrderSplittedPreviewNoLoading({ receiptNumber, splittedItems: [] }));
    const responseAction = yield take([
      Actions.onlineOrderSplittedPreviewNoLoading.toString() + '.success',
      Actions.onlineOrderSplittedPreviewNoLoading.toString() + '.failure',
    ]);
    const mainOrder = get(responseAction, 'payload.orderSplittedPreview.mainOrder', undefined);
    if (responseAction.type === Actions.onlineOrderSplittedPreviewNoLoading.toString() + '.success' && mainOrder) {
      const order = mapSplitPayLaterOrder(mainOrder, { receiptNumber, transactionId, tableId });
      logSucceedServerRequest(BeepFlowAction.SplitPayLaterOrder, tags, null, orderInfo);

      yield put(Actions.initSplitOrder(order));
    } else {
      logFailedServerRequest(BeepFlowAction.SplitPayLaterOrder, tags, get(responseAction, 'payload', ''), null, orderInfo);
      openErrorToast(t('Failed to split order'));
    }
  } else {
    yield put(Actions.completeMergeSplitOpenOrder());
  }
}

export function* paySplitOrderSaga(action) {
  const { isSplitMaster } = action.payload;
  const targetTransaction = yield select(selectSplitTargetOrder);
  const splitTransaction = yield select(selectSplitedOrder);

  const isKdsPaired = yield select(selectIsKdsPaired);
  if (isKdsPaired) {
    // update cooking status before paid and saved
    if (!isEmpty(targetTransaction)) {
      yield call(updateCookingStatusFromItems, targetTransaction);
      yield put(setSplitMasterOrder(targetTransaction));
    }

    if (!isEmpty(splitTransaction)) {
      yield call(updateCookingStatusFromItems, splitTransaction);
      yield put(setSplitedOrder(splitTransaction));
    }
  }

  if (isSplitMaster) {
    yield put(Actions.setTransactionSession(targetTransaction));
  } else {
    yield put(Actions.setTransactionSession(splitTransaction));
  }
}

export function* paySplitOnlineOrderSaga(action: Action<PaySplitOnlineOrderType>) {
  const { isSplitMaster, onComplete } = action.payload;
  const targetTransaction = yield select(selectSplitTargetOrder);
  const isPayLater = targetTransaction.isPayLater;
  if (isSplitMaster) {
    const tags = [
      BeepFlowTag.Beep,
      getBeepOrderShippingTag(targetTransaction),
      getPreOrderTag(targetTransaction),
      isPayLater ? BeepFlowTag.PayLater : BeepFlowTag.PayFirst,
    ];
    // the orderInfo be tracked in loggly
    const orderInfo = {
      orderId: targetTransaction.receiptNumber,
      status: targetTransaction.status,
      displayedStatus: getDisplayedStatus(targetTransaction),
    };

    const receiptNumber = targetTransaction.receiptNumber;
    // get online order detail, save db and print receipt
    // preview order is not reliable to print receipt
    yield put(Actions.getOnlineOrderDetail({ orderId: receiptNumber }));
    const detailResponseAction = yield take([Actions.getOnlineOrderDetail.toString() + '.success', Actions.getOnlineOrderDetail.toString() + '.failure']);
    if (detailResponseAction.type === Actions.getOnlineOrderDetail.toString() + '.success') {
      const onlineTransaction = detailResponseAction.payload.onlineOrder;
      // use for updateSplitOrder After check out
      onlineTransaction.isSplitMaster = true;
      onlineTransaction.isPayLaterSplittedOrder = false;
      yield call(setOnlineOpenOrderToTransactionSaga, setOnlineOpenOrderToTransaction({ onlineTransaction }));
      onComplete(true);
      return;
    } else {
      logFailedServerRequest(BeepFlowAction.GetOrderDetailError, tags, WorkflowStatus.End, get(detailResponseAction, 'payload', ''), orderInfo);
      onComplete(false);
      return;
    }
  } else {
    const splitTransaction = yield select(selectSplitedOrder);
    if (!splitTransaction.tableId && isPayLater) {
      splitTransaction.tableId = targetTransaction.tableId;
    }
    splitTransaction.transactionId = splitTransaction.transactionId || getUUIDValue();
    splitTransaction.isSplitMaster = false;
    splitTransaction.isPayLaterSplittedOrder = true;
    yield put(Actions.setTransactionSession(splitTransaction));
    onComplete(true);
    return;
  }
}

/**
 * 通知MRS对openOrder的更新
 * @param action
 * @returns
 */
export function* updateMRSOpenOrderSaga(action: Action<Actions.UpdatePidType>) {
  const { proposer, transactionsData: data } = action.payload;
  const transactionsData = filter(data, it => !it.updateSilence);

  if (transactionsData && transactionsData.length > 0) {
    // mrs mergeToSave need this
    DeviceEventEmitter.emit(OpenOrderUpdatedEvent, {
      transactionsData,
      proposer,
    });
    const clientIp = yield select(selectClientIp);
    if (proposer && clientIp === proposer) {
      // don't apply proposer its own changes
      return;
    }
    const immutableTransaction = yield select(selectTransactionSession);
    if (!immutableTransaction) return;
    const currentTransaction = immutableTransaction.toJS();

    if (!currentTransaction.isOpen || currentTransaction.isOnlineOrder) return;

    const foundTransactionItem = transactionsData.find(item => item && get(item, 'transaction.transactionId') === currentTransaction.transactionId);
    if (!foundTransactionItem) return;

    const currentRouteName = getCurrentRouteName();
    const canShowModal = registerPages.includes(currentRouteName);
    const updatedTransaction = DAL.getTransactionById(currentTransaction.transactionId);
    if (!updatedTransaction || updatedTransaction.isDeleted) {
      canShowModal && showModal(t('Order Deleted'), t('This order has been deleted by another register'));
      yield put(Actions.clearTransactionSession());
      return;
    }
    if (updatedTransaction.isOpen) {
      canShowModal && showModal(t('Order Updated'), t('This order was updated by another register'));
      yield call(setOpenOrderTransactionSaga, setOpenOrderTransactionSession({ transactionId: currentTransaction.transactionId }));
    } else {
      canShowModal && showModal(t('Order is checked out'), t('Order is checked out info'));
      yield put(Actions.clearTransactionSession());
    }
  }
}

export function* refreshMrsOpenOrdersSaga(action: Action<{ source: string }>) {
  const isEnabledMRS = yield select(selectIsEnabledMRS);
  const { source } = action.payload;
  if (isEnabledMRS) {
    const result: Actions.MRSError = yield call(performLearnerActionSaga, Actions.performLearnerAction({ source }));
    if (result.errorCode === SuccessCode) {
      const previousOrders = DAL.getMRSNotSyncedOpenOrders();
      console.log('previousOrders', previousOrders.length);
      for (let index = 0; index < previousOrders.length; index += LEARN_COUNT_PER_TIME) {
        const proposeData: ProposeDataType[] = previousOrders.slice(index, index + LEARN_COUNT_PER_TIME).map(it => ({
          operationType: OperationType.INSERT,
          transaction: it,
        }));
        const syncResult: Actions.MRSError = yield call(
          requestProposalActionSaga,
          requestProposalAction({
            data: proposeData,
            operation: OrderOperationEnum.Save,
            showLoading: false,
          })
        );
        if (syncResult.errorCode !== SuccessCode) {
          console.log('syncResult', syncResult);
          break;
        }
        // wait sync orders
        yield delay(1000);
      }
    }
  }
}

export enum SubmittedFromType {
  Consumer = 'consumer',
  Employee = 'employee',
}

export function* refreshBadgeNumberSaga() {
  const isBeepQREnabled = yield select(selectIsBeepQREnabled);
  if (!isBeepQREnabled) return;
  const registerObjectId = yield select(selectRegisterObjectId);
  const business = yield select(selectBusinessName);
  yield put(Actions.getBadgeNumber({ registerId: registerObjectId, business }));
  const badgeNumberResponseAction = yield take([Actions.getBadgeNumber.toString() + '.success', Actions.getBadgeNumber.toString() + '.failure']);
  if (badgeNumberResponseAction.type === Actions.getBadgeNumber.toString() + '.success') {
    const beepBadgeNumber = get(badgeNumberResponseAction, ['payload', 'beepBadgeNumber'], 0);
    yield put(Actions.setBeepBadgeNumber(beepBadgeNumber));
  }
}

export function* resetOnlineBadgeNumberSaga() {
  const registerObjectId = yield select(selectRegisterObjectId);
  const business = yield select(selectBusinessName);
  yield put(Actions.resetBadgeNumber({ registerId: registerObjectId, business, isOnline: true }));
  const resetBadgeNumberResponseAction = yield take([Actions.resetBadgeNumber.toString() + '.success', Actions.resetBadgeNumber.toString() + '.failure']);
  if (resetBadgeNumberResponseAction.type === Actions.resetBadgeNumber.toString() + '.success') {
    yield put(Actions.setBeepBadgeNumber(0));
  }
}

function* openOrderFlowSagas() {
  yield takeLatest(Actions.setOnlineOpenOrderToTransaction.toString(), setOnlineOpenOrderToTransactionSaga);
  yield takeLatest(Actions.setOpenOrderTransactionSession.toString(), setOpenOrderTransactionSaga);
  yield takeLatest(Actions.saveOpenOrder.toString(), saveOpenOrderSaga);
  yield takeLatest(Actions.saveOnlineOrder.toString(), saveOnlineOrderSage);
  yield takeLatest(Actions.updateOpenOrder.toString(), updateOpenOrderSaga);
  yield takeLatest(Actions.deleteOpenOrder.toString(), deleteOpenOrderSaga);
  yield takeLatest(Actions.deleteOnlineOpenOrder.toString(), deleteOnlineOpenOrderSaga);
  yield takeLatest(Actions.navigateToRegister.toString(), navigateToRegisterSaga);
  yield takeLatest(Actions.navigateToHome.toString(), navigateToHomeSaga);
  yield takeLatest(Actions.unlockPayLaterOrder.toString(), unlockPayLaterOrderSage);
  yield takeLatest(Actions.genarateMergeOpenOrder.toString(), genarateMultipleOpenOrderSage);
  yield takeLatest(Actions.mergeToSave.toString(), mergeToSaveSage);
  yield takeLatest(Actions.mergeToPay.toString(), mergeToPaySage);
  yield takeLatest(Actions.moveOpenOrder.toString(), moveOpenOrderSage);
  yield takeLatest(Actions.changeOpenOrderTable.toString(), changeOpenOrderTableSaga);
  yield takeLatest(Actions.setOrderToSplitMaster.toString(), setOrderToSplitMasterSaga);
  yield takeLatest(Actions.setOnlineOrderToSplitMaster.toString(), setOnlineOrderToSplitMasterSaga);
  yield takeLatest(Actions.saveSplitOrder.toString(), saveSplitOrderSaga);
  yield takeLatest(Actions.saveSplitOnlineOrder.toString(), saveSplitOnlineOrderSaga);
  yield takeLatest(Actions.editSplitOrder.toString(), editSplitOrderSaga);
  yield takeLatest(Actions.editSplitOnlineOrder.toString(), editSplitOnlineOrderSaga);
  yield takeLatest(Actions.updateSplitOnlineOrderAfterCheckOut.toString(), updateSplitOnlineOrderAfterCheckOutSaga);
  yield takeLatest(Actions.paySplitOrder.toString(), paySplitOrderSaga);
  yield takeLatest(Actions.paySplitOnlineOrder.toString(), paySplitOnlineOrderSaga);
  yield takeLatest(Actions.printOnlineOpenOrderReceipt.toString(), printOnlineOpenOrderReceiptSaga);
  yield takeLeading(Actions.refreshMrsOpenOrders.toString(), refreshMrsOpenOrdersSaga);
  yield takeLeading(Actions.refreshBadgeNumber.toString(), refreshBadgeNumberSaga);
  yield takeLeading(Actions.resetOnlineBadgeNumber.toString(), resetOnlineBadgeNumberSaga);
}

export default fork(openOrderFlowSagas);
