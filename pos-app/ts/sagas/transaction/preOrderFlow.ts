import { findIndex, get } from 'lodash';
import moment from 'moment';
import { call, fork, put, select, take, takeLatest } from 'redux-saga/effects';
import * as Actions from '../../actions';
import { SalesChannelType, TransactionFlowType } from '../../constants';
import DAL from '../../dal';

import { PurchasedItemType, TransactionType } from '../../typings';
import * as JSONUtils from '../../utils/json';

import { getDisplayItemsCount } from '../../utils/transaction';
import { selectEmployeeId, selectEnableCashback, selectEnableLoyalty, selectNextId, selectRegisterId, selectStore, selectStoreInfo } from '../selector';
import { SetupPreOrderType } from './../../actions/transaction';

import { AnyAction } from 'redux';
import PaymentOptions, { defaultPaymentOptions } from '../../config/paymentOption';
import { isValidNumber } from '../../utils';
import { errorTransactionEvent, infoTransactionEvent, OpenOrderFlowAction, PreOrderFlowAction } from '../../utils/logComponent';
import {
  checkItemsDiffTax,
  conversionDiscountInLoyaltyDiscounts,
  getAppliedPriceBooks,
  getCurrentTransaction,
  tryApplyPromotion,
  updateTransactionWithAppliedPriceBooks,
} from './common';
import calculateP from './common/calculator';
import { getServiceChargeTaxRateWithStore } from './common/commonUtils';
import { addCustomerToRecord, resetTransactionTableIdAndPaxSaga, setLoyaltyDiscounts, setTakeAwayToTransactionSaga } from './loyaltyFlow';
import { restoreBirInfoToTransaction } from './openOrderFlow';
import { addFullBillDiscountToRecord, addPurchasedItemToRecord, AddPurchasedItemToRecordParams, setServiceChargeToRecord } from './saleFlow';

export function* createPreOrderSaga() {
  let currentRecord = yield call(getCurrentTransaction);
  currentRecord.transactionType = TransactionFlowType.PreOrder;
  delete currentRecord.loyaltyDiscounts;
  currentRecord.isLoyaltyEnable = false;

  if (currentRecord.salesChannel === SalesChannelType.TAKEAWAY) {
    yield call(setTakeAwayToTransactionSaga, Actions.setTakeAwayToTransaction({ salesChannel: SalesChannelType.DEFAULT, currentTransaction: currentRecord }));
  } else {
    const immutableStore = yield select(selectStore);
    const includingTaxInDisplay = immutableStore.get('includingTaxInDisplay');
    yield call(resetTransactionTableIdAndPaxSaga, currentRecord);
    if (getDisplayItemsCount(currentRecord.items) > 0) {
      yield call(tryApplyPromotion, currentRecord);
      try {
        currentRecord = calculateP(currentRecord, includingTaxInDisplay || false);
      } catch (exception) {
        console.log('deleteCustomerFromTransactionSaga exception', exception);
        errorTransactionEvent({ action: PreOrderFlowAction.CreatePreOrder, reason: 'calculate exception', exception, transaction: currentRecord });
      }
    }
    yield put(Actions.setTransactionSession(currentRecord));
  }
}

export function* setupPreOrderSaga(action: AnyAction & { payload: SetupPreOrderType }) {
  const { date, time, deposit, comment, onComplete } = action.payload;

  const pickUpDate = moment(date + ' ' + time);
  const now = new Date();
  const currentRecord = yield call(getCurrentTransaction);
  currentRecord.comment = comment;
  currentRecord.preOrderDate = now.toISOString();
  currentRecord.preOrderBy = yield select(selectEmployeeId);
  currentRecord.pickUpDate = pickUpDate.toISOString();
  currentRecord.depositAmount = deposit;
  const registerId = yield select(selectRegisterId);
  const datePart = moment().format('YYMMDD');
  let nextId = yield select(selectNextId);
  if (Boolean(nextId)) {
    nextId += 1;
  } else {
    nextId = 1;
  }
  yield put(Actions.setNextId(nextId));
  const preOrderId = registerId + datePart + nextId;
  currentRecord.preOrderId = preOrderId;
  currentRecord.receiptNumber = preOrderId;
  yield put(Actions.setTransactionSession(currentRecord));
  infoTransactionEvent({ action: PreOrderFlowAction.SetupPreOrder, transaction: currentRecord });
  onComplete && onComplete.callback && onComplete.callback();
}

export function* getLocalPreOrdersSaga(action) {
  const result = DAL.getPreOrderList();

  const immutableStoreInfo = yield select(selectStoreInfo);
  const businessName = immutableStoreInfo.get('name');

  let uncommittedTxs = [];
  for (const localTx of result.values()) {
    uncommittedTxs.push(localTx);
  }
  action.payload.onComplete(uncommittedTxs);

  for (const localTx of result.values()) {
    yield put(Actions.getTransactionsNoBlocking({ business: businessName, receiptNumber: localTx.receiptNumber }));
    const responseAction = yield take([Actions.getTransactionsNoBlocking.toString() + '.success', Actions.getTransactionsNoBlocking.toString() + '.failure']);
    const response = responseAction.payload;
    if (responseAction.type === Actions.getTransactionsNoBlocking.toString() + '.success') {
      if (localTx.transactionId === get(response, 'transactionId')) {
        // uncommittedTxs.push(localTx);
        // action.payload.onComplete(uncommittedTxs);
      }
    } else {
      if (response.status === 404) {
        // 404 means this preorder is checkout already
        uncommittedTxs = uncommittedTxs.filter(tx => tx.receiptNumber !== localTx.receiptNumber);
        action.payload.onComplete(uncommittedTxs);
        DAL.deleteTransaction(localTx.transactionId);
      } else {
        // uncommittedTxs.push(localTx);
        // action.payload.onComplete(uncommittedTxs);
      }
    }
  }
}

export function* restoreLocalPreOrderSaga(action) {
  const { transactionId } = action.payload;
  if (Boolean(transactionId)) {
    const immutableStoreInfo = yield select(selectStoreInfo);

    const business = immutableStoreInfo.get('name');
    yield put(Actions.clearTransactionSession());

    const preOrder = DAL.getTrancationById(transactionId);
    const items = preOrder.items;
    if (getDisplayItemsCount(items) > 0) {
      yield call(restorePreOrderTransaction, items, preOrder, business);
    }
    const { tableId, createdDate, pickUpId, depositAmount, preOrderId, preOrderDate, roundedAmount = 0 } = preOrder;
    const transaction = yield call(getCurrentTransaction);
    transaction.depositAmount = depositAmount;
    transaction.tableId = tableId;
    transaction.createdDate = createdDate;
    transaction.pickUpId = pickUpId;
    transaction.totalPaid = depositAmount;
    transaction.transactionType = TransactionFlowType.Sale;
    transaction.salesChannel = SalesChannelType.DEFAULT;
    transaction.preOrderId = preOrderId;
    transaction.transactionId = transactionId;
    transaction.preOrderDate = preOrderDate;
    transaction.roundedAmount = roundedAmount;
    transaction.isCollectPreorder = true;

    const payments = get(preOrder, 'payments', []);
    const paymentsArr = [];

    if (payments.length > 0) {
      for (const itemObj of payments) {
        const item: any = itemObj;
        paymentsArr.push({
          amount: item.amount,
          paymentMethodId: item.paymentMethodId,
          cashTendered: item.cashTendered,
          roundedAmount: item.roundedAmount,
          isDeposit: item.isDeposit,
          subType: item.subType,
          type: item.type,
          mPOSTxnId: item.mPOSTxnId,
          isOnline: item.isOnline,
          manualApproveInfo: Boolean(item.manualApproveInfo) ? item.manualApproveInfo : undefined,
        });
      }
    }
    transaction.payments = paymentsArr;

    // yield call(generateReceiptNumber, transaction);
    yield put(Actions.setTransactionSession(transaction));
    infoTransactionEvent({ action: PreOrderFlowAction.RestoreLocalPreOrder, transaction });
  }
}

export function* restoreSearchedPreOrderSaga(action) {
  const preOrder = action.payload.transaction;
  if (Boolean(preOrder)) {
    const immutableStoreInfo = yield select(selectStoreInfo);
    const business = immutableStoreInfo.get('name');
    yield put(Actions.clearTransactionSession());
    const items = preOrder.items;
    if (getDisplayItemsCount(items) > 0) {
      yield call(restorePreOrderTransaction, items, preOrder, business, false); // error
    }
    const { tableId, createdDate, pickUpId, registerId, depositAmount, preOrderId, preOrderDate, transactionId, roundedAmount } = preOrder;
    const transaction = yield call(getCurrentTransaction);
    transaction.lastRegisterId = registerId;
    transaction.depositAmount = depositAmount;
    transaction.tableId = tableId;
    transaction.createdDate = createdDate;
    transaction.pickUpId = pickUpId;
    transaction.totalPaid = depositAmount;
    transaction.transactionType = TransactionFlowType.Sale;
    transaction.salesChannel = SalesChannelType.DEFAULT;
    transaction.preOrderId = preOrderId;
    transaction.transactionId = transactionId;
    transaction.preOrderDate = preOrderDate;
    transaction.roundedAmount = roundedAmount;
    transaction.isCollectPreorder = true;

    const payments = get(preOrder, 'payments', []);
    const paymentsArr = [];
    if (payments.length > 0) {
      for (const itemObj of payments) {
        const item: any = itemObj;
        const { paymentMethod } = item;
        const finalPaymentType = isValidNumber(paymentMethod) ? PaymentOptions.getPaymentTypeById(Number(paymentMethod)) : paymentMethod;
        const finalPaymentMethodId = isValidNumber(paymentMethod) ? Number(paymentMethod) : findIndex(defaultPaymentOptions, i => i.name === paymentMethod);
        paymentsArr.push({
          amount: item.amount,
          paymentMethodId: finalPaymentMethodId,
          cashTendered: item.cashTendered,
          roundedAmount: item.roundedAmount,
          isDeposit: item.isDeposit,
          subType: item.subType,
          type: finalPaymentType,
          mPOSTxnId: item.mPOSTxnId,
          isOnline: item.isOnline,
          manualApproveInfo: Boolean(item.manualApproveInfo) ? JSON.stringify(item.manualApproveInfo) : undefined,
        });
      }
    }
    transaction.payments = paymentsArr;

    // yield call(generateReceiptNumber, transaction);
    yield put(Actions.setTransactionSession(transaction));
    infoTransactionEvent({ action: PreOrderFlowAction.RestoreSearchedPreOrder, transaction });
  }
}

export const restorePreOrderTransaction = function* (items: PurchasedItemType[], record: TransactionType, business: string, isLocal = true) {
  const currentRecord = yield call(getCurrentTransaction);
  const { transactionId, tableId, createdDate, pax, salesChannel, takeawayCharge, customerId, comment } = record;
  const immutableStore = yield select(selectStore);
  const immutableStoreInfo = yield select(selectStoreInfo);
  const enableCashback = yield select(selectEnableCashback);
  const enableLoyalty = yield select(selectEnableLoyalty);
  const includingTaxInDisplay = immutableStore.get('includingTaxInDisplay');
  const enableTakeaway = immutableStore.get('enableTakeaway');
  currentRecord.transactionId = transactionId;
  currentRecord.tableId = tableId;
  currentRecord.createdDate = createdDate;
  currentRecord.pax = pax;
  currentRecord.comment = comment;
  if (enableTakeaway) {
    currentRecord.salesChannel = salesChannel;
    currentRecord.takeawayCharge = takeawayCharge;
  }

  let serviceChargeItem;
  if (Boolean(items) && items.length > 0) {
    for (const item of items) {
      const itemType = get(item, 'itemType');
      if (itemType !== 'ServiceCharge' && itemType !== 'Discount') {
        const {
          productId,
          options,
          quantity,
          isDiscountEnable,
          discountInputValue,
          discountType,
          discountValue,
          discount = 0,
          notes,
          sn,
          unitPrice,
          originalQuantity,
          title,
          taxCode,
          itemChannel,
          employeeId,
          employeeName,
        } = item;
        // Prepare the required parameters for calculate lib to calculate discount of order item.
        const variablePrice = unitPrice;
        const parsedOptions = isLocal ? JSONUtils.parse(options, null) : get(item, 'selectedOptions', []);
        const addPurchasedItemParams: AddPurchasedItemToRecordParams = {
          currentRecord,
          productId,
          options: parsedOptions,
          quantity,
          isDiscountEnable,
          discount,
          variablePrice,
          notes,
          sn,
          originalQuantity,
          taxCode,
          title,
          itemChannel,
          return: item.return,
          immutableStore,
          immutableStoreInfo,
          employeeId,
          employeeName,
        };

        const inputValue = isLocal ? discountInputValue : discountValue;
        if (Boolean(inputValue)) {
          const discountOption = { type: discountType, inputValue };
          addPurchasedItemParams.discountOption = discountOption;
        }

        addPurchasedItemToRecord(addPurchasedItemParams);
      }

      if (itemType === 'ServiceCharge') {
        serviceChargeItem = item;
      }
      if (itemType === 'Discount') {
        const discountInputValue = (isLocal ? item.discountInputValue : item.discountValue) || 0;
        addFullBillDiscountToRecord({
          currentRecord,
          inputValue: discountInputValue,
          type: item.discountType || 'amount',
          displayFullBillDiscountValue: discountInputValue,
          isDiscountEnable: true,
          displayFullBillDiscountEnable: true,
        });
      }
    }
    const searchedIndex = findIndex(currentRecord.items, (item: PurchasedItemType) => item.itemType === 'ServiceCharge');
    let serviceChargeRate = record.serviceChargeRate;
    const serviceChargeTax = record.serviceChargeTaxId;
    if (get(serviceChargeItem, 'subTotal', 0) === 0 && searchedIndex != -1) {
      serviceChargeRate = 0;
    }
    const taxRate = getServiceChargeTaxRateWithStore(immutableStore, record.serviceChargeTaxId);
    setServiceChargeToRecord({ currentRecord, itemIndex: searchedIndex, serviceChargeRate, serviceChargeTax, taxRate });
  }
  yield call(restoreBirInfoToTransaction, record, currentRecord);

  yield call(checkItemsDiffTax, currentRecord, enableCashback, enableLoyalty);

  yield call(tryApplyPromotion, currentRecord);

  const newItems = yield call(updateTransactionWithAppliedPriceBooks, {
    transaction: currentRecord,
    appliedPriceBooks: currentRecord.appliedPriceBooks,
    inTransaction: true,
  });
  currentRecord.items = newItems;
  try {
    calculateP(currentRecord, includingTaxInDisplay || false);
    conversionDiscountInLoyaltyDiscounts(currentRecord);
    yield put(Actions.setTransactionSession(currentRecord));
  } catch (exception) {
    if (exception) {
      yield put(Actions.toggleToastInfo({ visible: true, text: exception.message }));
      errorTransactionEvent({ action: OpenOrderFlowAction.RestorePreorder, reason: 'calcutate exception', exception, transaction: currentRecord });
    }
  }

  let haveCustomer = false,
    haveLoyalty = false;
  // addCustomerToTransactionSaga
  if (Boolean(customerId)) {
    yield put(Actions.getCustomerById({ customerId, bn: business }));
    const responseAction = yield take([Actions.getCustomerById.toString() + '.success', Actions.getCustomerById.toString() + '.failure']);
    if (responseAction.type === Actions.getCustomerById.toString() + '.success') {
      const resp = responseAction.payload;
      if (Boolean(resp)) {
        const appliedPriceBooks = yield call(getAppliedPriceBooks, resp);
        const { success } = yield call(addCustomerToRecord, { currentRecord, immutableStore, appliedPriceBooks, customer: resp, customerId });
        haveCustomer = success;
      }
    }
  }

  // updateLoyaltyDiscountsSaga
  const { loyaltyDiscounts } = record;
  if (enableLoyalty && Boolean(loyaltyDiscounts) && loyaltyDiscounts.length > 0) {
    haveLoyalty = true;
    const { type, inputValue } = loyaltyDiscounts[0];
    setLoyaltyDiscounts({ currentRecord, inputValue, isRefund: false, type });
  }
  if (haveCustomer || haveLoyalty) {
    try {
      const newCurrentRecord: any = calculateP(currentRecord, includingTaxInDisplay || false);
      conversionDiscountInLoyaltyDiscounts(newCurrentRecord);
      yield put(Actions.setTransactionSession(newCurrentRecord));
      infoTransactionEvent({ action: OpenOrderFlowAction.RestorePreorder, transaction: newCurrentRecord });
    } catch (exception) {
      console.log('addCustomerToTransactionSaga exception', exception);
      errorTransactionEvent({ action: OpenOrderFlowAction.RestorePreorder, reason: 'calcutate exception', exception, transaction: currentRecord });
    }
  }
};

function* preOrderFlowSagas() {
  yield takeLatest(Actions.createPreOrder.toString(), createPreOrderSaga);
  yield takeLatest(Actions.setupPreOrder.toString(), setupPreOrderSaga);
  yield takeLatest(Actions.restoreLocalPreOrder.toString(), restoreLocalPreOrderSaga);
  yield takeLatest(Actions.restoreSearchedPreOrder.toString(), restoreSearchedPreOrderSaga);
  yield takeLatest(Actions.getLocalPreOrders.toString(), getLocalPreOrdersSaga);
}

export default fork(preOrderFlowSagas);
