import { compact, get } from 'lodash';
import { Action } from 'redux-actions';
import { call, fork, put, select, take, takeLatest } from 'redux-saga/effects';
import * as Actions from '../../actions';
import { lockPromo, returnPromo } from '../../actions';
import { navigate } from '../../navigation/navigatorService';
import { PromotionType } from '../../typings';
import { getDisplayItemsCount } from '../../utils/transaction';
import { selectBusinessName, selectIncludingTaxInDisplay, selectSelectedUniquePromo, selectUniquePromosError } from '../selector';
import { getCurrentTransaction, hasAnyGenericPromotionApplied, tryApplyPromotion } from './common';
import calculateP from './common/calculator';
import { clearPromotionSaga } from './saleFlow';
import { errorUniquePromoEvent, infoUniquePromoEvent, UniquePromoLogAction } from '../../utils/logComponent/buz/uniquePromo';

export const setUniquePromosSaga = function* (action: Action<PromotionType[]>) {
  const business = yield select(selectBusinessName);

  const uniquePromos = action.payload as any;
  const modifiedPromos = uniquePromos.map(promo => {
    const { id, conditions, ...rest } = promo;
    const newConditions = conditions || [];
    return {
      _id: id,
      conditions: newConditions,
      business: business,
      ...rest,
    };
  });

  const currentRecord = yield call(getCurrentTransaction);
  if (currentRecord) {
    if (!currentRecord.uniquePromos) {
      currentRecord.uniquePromos = {};
    }
    currentRecord.uniquePromos.data = modifiedPromos;
  }
  yield put(Actions.setTransactionSession(currentRecord));

  infoUniquePromoEvent({
    action: UniquePromoLogAction.setUniquePromos,
    privateDataPayload: {
      uniquePromosData: modifiedPromos,
    },
  });
};

export const setSelectedUniquePromoSaga = function* (action: Action<PromotionType>) {
  const uniquePromo = action.payload;
  const uniquePromoId = get(uniquePromo, ['uniquePromotionCodeInfo', 'id'], '');

  const currentRecord = yield call(getCurrentTransaction);
  if (currentRecord) {
    if (!currentRecord.uniquePromos) {
      currentRecord.uniquePromos = {};
    }
    const matchedPromo = currentRecord.uniquePromos.data?.find((promo: any) => promo?.uniquePromotionCodeInfo?.id === uniquePromoId);

    currentRecord.uniquePromos.selectedUniquePromo = matchedPromo;

    infoUniquePromoEvent({
      action: UniquePromoLogAction.setSelectedUniquePromo,
      privateDataPayload: {
        selectedUniquePromo: uniquePromo,
      },
    });
  }
  yield put(Actions.setTransactionSession(currentRecord));

  const isStackable = get(uniquePromo, ['isStackable'], false);
  if (isStackable) {
    yield call(applyUniquePromoAndRecalculateSaga);
  } else {
    const hasGenericPromotion = yield call(hasAnyGenericPromotionApplied);
    if (hasGenericPromotion) {
      infoUniquePromoEvent({
        action: UniquePromoLogAction.ModalRemoveOtherPromotions,
        privateDataPayload: {
          selectedUniquePromo: uniquePromo,
        },
      });
      requestAnimationFrame(() => {
        navigate({ routeName: 'ModalRemoveOtherPromotions' });
      });
    } else {
      infoUniquePromoEvent({
        action: UniquePromoLogAction.applyUniquePromo,
        privateDataPayload: {
          selectedUniquePromo: uniquePromo,
        },
      });
      yield call(applyUniquePromoAndRecalculateSaga);
    }
  }
};

export const applyUniquePromoAndRecalculateSaga = function* () {
  let currentRecord = yield call(getCurrentTransaction);
  const { items } = currentRecord;
  const totalCount = getDisplayItemsCount(items);
  if (totalCount > 0) {
    yield call(tryApplyPromotion, currentRecord);
    const includingTaxInDisplay = yield select(selectIncludingTaxInDisplay);
    try {
      currentRecord = calculateP(currentRecord, includingTaxInDisplay || false);
    } catch (exception) {
      console.log('applyPromotionAndRecalculate exception', exception);
    }
    yield put(Actions.setTransactionSession(currentRecord));
  }

  const uniquePromoError = yield select(selectUniquePromosError);
  if (compact(uniquePromoError).length > 0) {
    requestAnimationFrame(() => {
      navigate({
        routeName: 'ModalUniquePromoConditionNotMeet',
        params: {
          needToChooseAnotherPromo: false,
        },
      });
    });

    errorUniquePromoEvent({
      action: UniquePromoLogAction.applyUniquePromoError,
      privateDataPayload: {
        uniquePromoError: uniquePromoError,
      },
    });
  }
};

export const clearUniquePromosSaga = function* () {
  let currentRecord = yield call(getCurrentTransaction);
  if (currentRecord) {
    if (!currentRecord.uniquePromos) {
      currentRecord.uniquePromos = {};
    }
    currentRecord.uniquePromos.data = [];
    currentRecord.uniquePromos.selectedUniquePromo = null;
    currentRecord.uniquePromos.error = null;
  }

  yield call(clearPromotionSaga);
  const { items } = currentRecord;
  const totalCount = getDisplayItemsCount(items);
  if (totalCount > 0) {
    yield call(tryApplyPromotion, currentRecord);
    const includingTaxInDisplay = yield select(selectIncludingTaxInDisplay);
    try {
      currentRecord = calculateP(currentRecord, includingTaxInDisplay || false);
    } catch (exception) {
      console.log('exception', exception);
    }
  }

  yield put(Actions.setTransactionSession(currentRecord));
  infoUniquePromoEvent({ action: UniquePromoLogAction.clearUniquePromos });
};

export const clearSelectedUniquePromoSaga = function* () {
  let currentRecord = yield call(getCurrentTransaction);
  if (currentRecord) {
    if (!currentRecord.uniquePromos) {
      currentRecord.uniquePromos = {};
    }
    currentRecord.uniquePromos.selectedUniquePromo = null;
    currentRecord.uniquePromos.error = null;
  }
  yield put(Actions.setTransactionSession(currentRecord));

  yield call(clearPromotionSaga);
  const { items } = currentRecord;
  const totalCount = getDisplayItemsCount(items);
  if (totalCount > 0) {
    yield call(tryApplyPromotion, currentRecord);
    const includingTaxInDisplay = yield select(selectIncludingTaxInDisplay);
    try {
      currentRecord = calculateP(currentRecord, includingTaxInDisplay || false);
    } catch (exception) {
      console.log('clearSelectedUniquePromoSaga exception', exception);
    }
  }

  yield put(Actions.setTransactionSession(currentRecord));
  infoUniquePromoEvent({ action: UniquePromoLogAction.clearSelectedUniquePromo });
};

export const recordApplyUniquePromoError = function* (action: any) {
  const error = action;

  const currentRecord = yield call(getCurrentTransaction);
  if (currentRecord && currentRecord.uniquePromos) {
    currentRecord.uniquePromos.error = error;
  }
  yield put(Actions.setTransactionSession(currentRecord));
};

export interface lockUniquePromoType {
  onSuccess: any;
  onFailure: any;
}

export const lockUniquePromoSaga = function* (action: Action<lockUniquePromoType>) {
  const { onSuccess, onFailure } = action.payload;

  const uniquePromo = yield select(selectSelectedUniquePromo);

  const promotionId = get(uniquePromo, ['_id'], '');
  const promotionCodeId = get(uniquePromo, ['uniquePromotionCodeInfo', 'id'], '');
  const businessName = yield select(selectBusinessName);
  const currentRecord = yield call(getCurrentTransaction);
  const { transactionId } = currentRecord;
  const customerId = get(currentRecord, ['customer', 'customerId'], '');

  yield put(Actions.lockPromo({ promotionId, promotionCodeId, businessName, transactionId, customerId }));
  const responseAction = yield take([lockPromo.toString() + '.success', lockPromo.toString() + '.failure']);
  if (responseAction.type === lockPromo.toString() + '.success') {
    const data = responseAction.payload;
    const success = get(data, ['lockPromo', 'success'], false);
    if (success) {
      if (onSuccess) {
        onSuccess();
      }
    } else {
      if (onFailure) {
        onFailure();
      }
    }
  } else {
    console.error('lockPromo failure', responseAction.payload);
  }
};

export interface returnUniquePromoType {
  promotionId: string;
  promotionCodeId: string;
  transactionId: string;
  customerId: string;
}
export const returnUniquePromoSaga = function* (action?: Action<returnUniquePromoType>) {
  const businessName = yield select(selectBusinessName);
  let promotionId = '';
  let promotionCodeId = '';
  let transactionId = '';
  let customerId = '';
  if (action && action.payload) {
    const {
      promotionId: actionPromotionId,
      promotionCodeId: actionPromotionCodeId,
      transactionId: actionTransactionId,
      customerId: actionCustomerId,
    } = action.payload;

    promotionId = actionPromotionId;
    promotionCodeId = actionPromotionCodeId;
    transactionId = actionTransactionId;
    customerId = actionCustomerId;
  } else {
    const uniquePromo = yield select(selectSelectedUniquePromo);
    promotionId = get(uniquePromo, ['_id'], '');
    promotionCodeId = get(uniquePromo, ['uniquePromotionCodeInfo', 'id'], '');
    const currentRecord = yield call(getCurrentTransaction);
    transactionId = currentRecord.transactionId;
    customerId = get(currentRecord, ['customer', 'customerId'], '');
  }

  if (!promotionId || !promotionCodeId || !businessName || !transactionId) {
    console.error('returnUniquePromo missing required parameters', { promotionId, promotionCodeId, businessName, transactionId, customerId });
    return;
  }

  yield put(Actions.returnPromo({ promotionId, promotionCodeId, businessName, transactionId, customerId }));
  const responseAction = yield take([returnPromo.toString() + '.success', returnPromo.toString() + '.failure']);
  if (responseAction.type === returnPromo.toString() + '.success') {
    console.log('returnPromo success', responseAction.payload);
  } else {
    console.error('returnPromo failure', responseAction.payload);
  }
};

export const warnToRemoveUniquePromoSaga = function* () {
  const uniquePromo = yield select(selectSelectedUniquePromo);
  if (uniquePromo) {
    infoUniquePromoEvent({ action: UniquePromoLogAction.warnToRemoveUniquePromo });
    yield call(clearSelectedUniquePromoSaga);

    requestAnimationFrame(() => {
      navigate({ routeName: 'ModalUniquePromoRemoved' });
    });
  }
};

function* promitionSaga() {
  yield takeLatest(Actions.setUniquePromos.toString(), setUniquePromosSaga);
  yield takeLatest(Actions.setSelectedUniquePromo.toString(), setSelectedUniquePromoSaga);
  yield takeLatest(Actions.clearUniquePromos.toString(), clearUniquePromosSaga);
  yield takeLatest(Actions.clearSelectedUniquePromo.toString(), clearSelectedUniquePromoSaga);
  yield takeLatest(Actions.lockUniquePromo.toString(), lockUniquePromoSaga);
  yield takeLatest(Actions.returnUniquePromo.toString(), returnUniquePromoSaga);
  yield takeLatest(Actions.applyUniquePromoAndRecalculate.toString(), applyUniquePromoAndRecalculateSaga);
  yield takeLatest(Actions.warnToRemoveUniquePromo.toString(), warnToRemoveUniquePromoSaga);
}

export default fork(promitionSaga);
