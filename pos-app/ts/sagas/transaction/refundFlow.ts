import { cloneDeep, filter, find, forEach, get, isNumber, map } from 'lodash';

import { call, fork, put, select, spawn, take, takeLatest } from 'redux-saga/effects';
import * as Actions from '../../actions';
import PaymentOptions from '../../config/paymentOption';
import { t } from '../../constants';
import DAL from '../../dal';

import { CancelFlowAction, errorTransactionEvent, infoTransactionEvent, RefundFlowAction } from '../../utils/logComponent';

import { getDisplayItemsCount } from '../../utils/transaction';
import { selectIncludingTaxInDisplay, selectBirAccredited, selectGBEnableVoidReceipt, selectStoreInfo } from './../selector';
import { clearPromotions, getCurrentTransaction } from './common';

import { Action } from 'redux-actions';
import { RefundHelper, TransactionHelper } from '../../dal/helper';
import { roundTo2DecimalPlaces } from '../../utils';
import { pushLocalTransactionsSaga } from '../kds/transaction/push';

export const mergeTransaction = function (localRecords, remoteRecords) {
  const result = new Object();

  const addItemToResult = item => {
    result[item.transactionId] = {
      transactionId: item.transactionId,
      transactionType: item.transactionType,
      modifiedTime: item.modifiedTime,
      createdDate: item.createdDate || item.createdTime,
      createdTime: item.createdTime,
      employeeId: item.employeeId,
      total: item.total,
      customerId: item.customerId,
      discount: item.discount,
      subtotal: item.subtotal,
      receiptNumber: item.receiptNumber,
      isCancelled: item.isCancelled,
      tax: item.tax,
      payments: item.payments,
      items: RefundHelper.serializeItems(item),
      serviceCharge: item.serviceCharge,
      promotions: get(item, 'promotions', []),
      roundedAmount: item.roundedAmount,
      serviceChargeTax: item.serviceChargeTax,
      serviceChargeRate: item.serviceChargeRate,
      registerNumber: item.registerNumber, // TODO refund
      isRemote: item.isRemote,
      registerId: item.registerId,
      itemChannel: item.itemChannel,
      tableId: item.tableId, // TODO refund
      pickUpId: item.pickUpId, // TODO refund
      headCount: item.headcount, // TODO refund
      pwdCount: item.pwdCount,
      takeawayCharges: item.takeawayCharges,
      takeawayCharge: item.takeawayCharge,
      salesChannel: item.salesChannel,
      enableCashback: item.enableCashback,
      originalReceiptNumber: item.originalReceiptNumber,
      comment: item.comment,
      returnStatus: item.returnStatus,
      returnReason: item.returnReason,
      pax: item.pax,
      seniorDiscount: item.seniorDiscount,
      pwdDiscount: item.pwdDiscount,
      taxableSales: item.taxableSales,
      taxExemptedSales: item.taxExemptedSales,
      zeroRatedSales: item.zeroRatedSales,
      totalDeductedTax: item.totalDeductedTax,
      shippingFee: item.shippingFee,
      shippingFeeDiscount: item.shippingFeeDiscount,
      sequenceNumber: item.sequenceNumber,
      invoiceSeqNumber: item.invoiceSeqNumber,
      pickUpDate: item.pickUpDate,
      otherReason: item.otherReason,
      cancelledAt: item.cancelledAt,
      cancelledBy: item.cancelledBy,
      preOrderId: item.preOrderId,
      preOrderBy: item.preOrderBy,
      preOrderDate: item.preOrderDate,
      amusementTax: item.amusementTax,
      addonBirCompliance: item.addonBirCompliance,
      loyaltyDiscounts: item.loyaltyDiscounts,
      voidNumber: item.voidNumber,
      // fix printing receipt from remote
      calculation: item.calculation,
    };
  };

  if (Boolean(localRecords) && localRecords.length > 0) {
    for (const item of localRecords) {
      addItemToResult(item);
    }
  }

  if (Boolean(remoteRecords) && remoteRecords.length > 0) {
    for (const itemIterator of remoteRecords) {
      const item: any = itemIterator;
      item.isRemote = true;
      item.employeeId = item.employeeNumber;
      item.payments = map(item.payments, payment => {
        if (Boolean(payment.paymentMethod)) {
          payment.paymentMethodId = PaymentOptions.getDownloadPaymentMethodId(payment.paymentMethod);
        }
        return payment;
      });
      const duplicateItem = result[item.transactionId];
      if (duplicateItem === null || duplicateItem === undefined) {
        addItemToResult(item);
      } else {
        const date = new Date(duplicateItem.modifiedTime);
        const currentDate = new Date(item.modifiedTime);
        if (currentDate < date) {
          addItemToResult(item);
        }
      }
    }
  }

  return result;
};

export const checkRefundStatusSaga = function* (action: Action<Actions.CheckTrxRefundStatusType>) {
  // check connectInfo
  const {
    receiptNumber,
    onComplete,
    transactionId,
    needCustomerInfo = false,
    needWithInternet = false,
    alreadyRefundErrMsg = t('This sale has already been refunded'),
  } = action.payload;
  const returnedTrxs = DAL.getReturnsTrxByOriginalReceiptNumber(receiptNumber);

  // checkMRSBeforeExecute
  // requestSync
  const immutableStoreInfo = yield select(selectStoreInfo);
  const business = immutableStoreInfo.get('name');
  yield put(Actions.getTransactions({ business, receiptNumber }));
  const responseAction = yield take([Actions.getTransactions.toString() + '.success', Actions.getTransactions.toString() + '.failure']);
  const resp = responseAction.payload;
  if (responseAction.type === Actions.getTransactions.toString() + '.success' || resp.status === 404) {
    if (get(resp, 'isCancelled', false)) {
      errorTransactionEvent({ action: RefundFlowAction.CheckTransactionRefundStatus, reason: 'AlreadyCancelError', orderId: receiptNumber, transactionId });
      return onComplete.callback({
        hasRefunded: false,
        errorMsg: t('AlreadyCancelError'),
      });
    }
    yield put(
      Actions.getRefundTransactions({
        business,
        originalReceiptNumber: receiptNumber,
      })
    );
    const responseSubAction = yield take([Actions.getRefundTransactions.toString() + '.success', Actions.getRefundTransactions.toString() + '.failure']);
    let remoteRecords = [];
    // remoteRecords是后端返回的订单关联的所有refund订单，可能没有也可能是多个，因为有部分退款
    if (Array.isArray(responseSubAction.payload.asArray)) {
      remoteRecords = responseSubAction.payload.asArray;
    }

    if (Boolean(transactionId)) {
      // merge 订单关联的所有refund订单,如果前后端有相同的数据，以后端为准
      // 最终获取到的是一个 订单关联的所有refund订单的列表（数组）
      const result = yield mergeTransaction(returnedTrxs, remoteRecords);
      // 原sale订单
      const returnRecord = DAL.getTrancationById(transactionId) || resp;
      const newCurrentRecord = RefundHelper.serializeTransaction(returnRecord);
      if (needCustomerInfo && newCurrentRecord.customerId) {
        yield put(Actions.getCustomerById({ customerId: newCurrentRecord.customerId, bn: business }));
        const responseAction = yield take([Actions.getCustomerById.toString() + '.success', Actions.getCustomerById.toString() + '.failure']);
        if (responseAction.type === Actions.getCustomerById.toString() + '.success') {
          newCurrentRecord.customer = responseAction.payload;
        } else {
          console.log('getCustomerById error:', responseAction.payload);
        }
      }
      // TODO partial refund
      newCurrentRecord.originalRecord = cloneDeep(newCurrentRecord);
      // ------------------------------------------------------------------------
      const allReturnedRecords = Object.values<any>(result);
      if (Boolean(allReturnedRecords) && allReturnedRecords.length > 0) {
        const items = get(newCurrentRecord, 'items', []);
        map(items, (item: Actions.PurchansedItemTypeInRefund) => {
          if (!Boolean(item.itemType)) {
            for (const record of allReturnedRecords) {
              if (Boolean(record.items) && record.items.length > 0) {
                for (const returnedItem of record.items) {
                  if (
                    returnedItem.productId === item.productId &&
                    (!returnedItem.sn || (returnedItem.sn && returnedItem.sn === item.sn)) &&
                    getItemOptionText(item) === getItemOptionText(returnedItem)
                  ) {
                    const quantity = isNaN(get(item, 'quantity')) ? 0 : item.quantity - get(returnedItem, 'quantity', 0);
                    item.quantity = quantity;
                    item.return.quantity = quantity;
                  }
                }
              }
            }
          }
        });
        newCurrentRecord.items = items.filter(item => {
          return Boolean(item) && item.quantity > 0;
        });
        if (getDisplayItemsCount(items) > 0) {
          yield put(Actions.clearTransactionSession());
          yield call(calculateReturnTransaction, newCurrentRecord, false, -1);
          // yield put(Actions.setTransactionSession(newCurrentRecord));
          return onComplete.callback({
            hasRefunded: false,
            errorMsg: '',
          });
        } else {
          return onComplete.callback({
            hasRefunded: true,
            errorMsg: alreadyRefundErrMsg,
          });
        }
      } else {
        yield put(Actions.clearTransactionSession());
        yield put(Actions.setTransactionSession(newCurrentRecord));
        return onComplete.callback({
          hasRefunded: false,
          errorMsg: '',
        });
      }
    }
    // ------------------------------------------------------------------------
  } else if (needWithInternet) {
    return onComplete.callback({
      hasRefunded: false,
      errorMsg: t('Please connect to the internet to use this function'),
      errorTitle: t('No Internet Connection'),
    });
  }
  // Offline or network not reachable
  const hasRefunded = Boolean(returnedTrxs.length);
  return onComplete.callback({
    hasRefunded,
    errorMsg: hasRefunded ? alreadyRefundErrMsg : null,
    errorTitle: hasRefunded ? t('Warning') : null,
  });
};

export const checkCancellableWithRefundStatusSaga = function* (action: Action<Actions.CheckTrxRefundStatusType>) {
  // check connectInfo
  const { receiptNumber, onComplete, transactionId, needWithInternet = false, alreadyRefundErrMsg = t('This sale has already been refunded') } = action.payload;
  const returnedTrxs = DAL.getReturnsTrxByOriginalReceiptNumber(receiptNumber);
  const gbEnableVoidReceipt = yield select(selectGBEnableVoidReceipt);
  const birAccredited = yield select(selectBirAccredited);
  const isInternetNeeded = needWithInternet || (gbEnableVoidReceipt && birAccredited);
  // checkMRSBeforeExecute
  // requestSync
  const immutableStoreInfo = yield select(selectStoreInfo);
  const business = immutableStoreInfo.get('name');
  yield put(Actions.getTransactions({ business, receiptNumber }));
  const responseAction = yield take([Actions.getTransactions.toString() + '.success', Actions.getTransactions.toString() + '.failure']);
  const resp = responseAction.payload;

  if (responseAction.type === Actions.getTransactions.toString() + '.success' || resp.status === 404) {
    if (get(resp, 'isCancelled', false)) {
      const gbEnableVoidReceipt = yield select(selectGBEnableVoidReceipt);
      const birAccredited = yield select(selectBirAccredited);
      if (gbEnableVoidReceipt && birAccredited) {
        const currentShift = DAL.getLastShift();
        const currentShiftId = get(currentShift, 'shiftId');
        const saveSuccess = DAL.updateTransaction(transactionId, {
          isCancelled: true,
          modifiedDate: resp.modifiedTime,
          cancelledAt: resp.createdTime,
          cancelledBy: resp.cancelledBy,
          shiftIdOfCancel: currentShiftId,
          returnReason: resp.returnReason || resp.cancelReason,
          otherReason: resp.otherReason || resp.cancelReasonDetail,
          voidNumber: resp.voidNumber,
        });
        const localCancelledTrx = TransactionHelper.mappingRemoteToLocal(resp);
        if (saveSuccess) {
          yield spawn(pushLocalTransactionsSaga, [transactionId]);
          infoTransactionEvent({
            action: CancelFlowAction.UpdateOrderDBCancelStatus,
            transaction: localCancelledTrx,
          });
        } else {
          errorTransactionEvent({
            action: CancelFlowAction.UpdateOrderDBCancelStatus,
            transaction: localCancelledTrx,
          });
        }
        yield put(
          Actions.newPrintReceiptAction({
            transactionId: transactionId,
            isReprint: false,
            onlineTransaction: localCancelledTrx,
            eventName: 'printReceiptWhenCancelRegisterOrder',
          })
        );
      }
      errorTransactionEvent({ action: RefundFlowAction.CheckTransactionRefundStatus, reason: 'AlreadyCancelError', orderId: receiptNumber, transactionId });
      return onComplete.callback({
        hasRefunded: false,
        errorMsg: t('AlreadyCancelError'),
      });
    }
    yield put(
      Actions.getRefundTransactions({
        business,
        originalReceiptNumber: receiptNumber,
      })
    );
    const responseSubAction = yield take([Actions.getRefundTransactions.toString() + '.success', Actions.getRefundTransactions.toString() + '.failure']);
    let remoteRecords = [];
    // remoteRecords是后端返回的订单关联的所有refund订单，可能没有也可能是多个，因为有部分退款
    if (Array.isArray(responseSubAction.payload.asArray)) {
      remoteRecords = responseSubAction.payload.asArray;
    }
    const hasRefunded = Boolean(returnedTrxs.length) || Boolean(remoteRecords.length);
    return onComplete.callback({
      hasRefunded,
      errorMsg: hasRefunded ? alreadyRefundErrMsg : null,
      errorTitle: hasRefunded ? t('Warning') : null,
    });
  } else if (isInternetNeeded) {
    return onComplete.callback({
      hasRefunded: false,
      errorMsg: t('Please connect to the internet to use this function'),
      errorTitle: t('No Internet Connection'),
    });
  }
  // Offline or network not reachable
  const hasRefunded = Boolean(returnedTrxs.length);
  return onComplete.callback({
    hasRefunded,
    errorMsg: hasRefunded ? alreadyRefundErrMsg : null,
    errorTitle: hasRefunded ? t('Warning') : null,
  });
};

// 当refundRecord.items 发生编辑时
export const updateReturnItemSaga = function* (action) {
  const { itemIndex, quantity, total, isDelete = false } = action.payload;
  // 暂时不考虑ungroup的情况
  // 当删除最后一个item时，应该从flow上去提示是否要取消 refund
  // isEditingQty： 保留 unitPrice不变, qty和total变
  // isEditingTotal： 保留 qty不变, total和unitPrice变
  const currentRecord = yield call(getCurrentTransaction);
  const originalRecord = currentRecord.originalRecord;
  const item = currentRecord.items[itemIndex];
  if (!Boolean(item.return)) {
    item.return = {};
  }
  const editedItemId = item.id;
  if (isDelete) {
    currentRecord.items.splice(itemIndex, 1);
  } else {
    // if (quantity === 0) return;
    if (isNumber(quantity)) {
      if (quantity >= item.originalQuantity) {
        const originalItem = find(originalRecord.items, i => i.id === item.id);
        currentRecord.items[itemIndex] = { ...originalItem };
      } else {
        item.return.quantity = quantity;
      }
    }
    if (isNumber(total)) {
      if (total > item.quantity * item.unitPrice) {
        return;
      }
      item.return.total = total;
      item.return.unitPrice = item.return.total / (item.return.quantity || 1);
    }
  }
  // 对比是否是与之前一样？
  const items = get(currentRecord, 'items', []);
  const originalItems = get(originalRecord, 'items', []);
  let isOriginal = true;
  if (items.length === originalItems.length) {
    for (let index = 0; index < items.length; index++) {
      const item = items[index];
      const originalItem = originalItems[index];
      if (Boolean(item.itemType)) continue;
      if (item.return.total !== originalItem.return.total || item.return.quantity !== originalItem.return.quantity) {
        isOriginal = false;
        break;
      }
    }
  } else {
    isOriginal = false;
  }
  yield call(calculateReturnTransaction, currentRecord, isOriginal, editedItemId);
};

// 部分退款
export const calculateReturnTransaction = function* (currentRecord, isOriginal, editedItemId) {
  // 暂时不考虑ungroup的情况
  // ------------------------------------   items   ------------------------------------
  const items = get(currentRecord, 'items', []);
  const originalRecord = currentRecord.originalRecord;
  const originalItems = get(originalRecord, 'items', []);
  const includingTaxInDisplay = yield select(selectIncludingTaxInDisplay);

  if (!isOriginal) {
    let pureTransactionTotal = 0;
    let serviceChargeAbleTotal = 0;
    let transactionSubtotal = 0;
    let transactionTotal = 0;
    let transactionTax = 0;
    let transactionSeniorDiscount = 0;
    let transactionPwdDiscount = 0;
    let transactionAthleteAndCoachDiscount = 0;
    let transactionMedalOfValorDiscount = 0;
    let transactionSoloParentDiscount = 0;

    let transactionTaxableSales = 0;
    let transactionTaxExemptedSales = 0;
    let transactionZeroRatedSales = 0;
    let transactionTotalDeductedTax = 0;

    let transactionTotalLoyaltyDiscount = 0;
    let transactionTotalLoyaltySpent = 0;
    let transactionTotalDisplayLoyalty = 0;
    let amusementTax = 0;

    // TODO: calculate the calculation field value for refund order
    let serviceChargeItemIndex = -1;
    for (let index = 0; index < items.length; index++) {
      const item = items[index];
      const originalItem = find(originalItems, i => i.id === item.id);
      if ((Boolean(editedItemId) && item.id === editedItemId) || editedItemId === -1) {
        const coefficient = originalItem.return.unitPrice <= 0 ? 0 : item.return.unitPrice / originalItem.return.unitPrice;
        const radio = (coefficient * item.return.quantity) / originalItem.return.quantity;
        item.quantity = item.return.quantity;
        item.total = roundTo2DecimalPlaces(item.return.unitPrice * item.quantity);
        // subtotal应该是以原unitPrice计算而来，当total改变时，应修改discount
        item.subTotal = roundTo2DecimalPlaces((originalItem.subTotal / originalItem.return.quantity) * item.return.quantity);
        item.tax = roundTo2DecimalPlaces(originalItem.tax * radio);
        item.discount = roundTo2DecimalPlaces(item.subTotal - item.total);
        // item.adhocDiscount = roundTo2DecimalPlaces(originalItem.adhocDiscount * coefficient);
        // loyalty
        item.loyaltyDiscount = roundTo2DecimalPlaces((originalItem.loyaltyDiscount || 0) * radio);
        item.display = { total: item.total, subtotal: item.subTotal, tax: item.tax };
        // PH BIR
        item.pwdDiscount = roundTo2DecimalPlaces((originalItem.pwdDiscount || 0) * radio);
        item.seniorDiscount = roundTo2DecimalPlaces((originalItem.seniorDiscount || 0) * radio);
        item.athleteAndCoachDiscount = roundTo2DecimalPlaces((originalItem.athleteAndCoachDiscount || 0) * radio);
        item.medalOfValorDiscount = roundTo2DecimalPlaces((originalItem.medalOfValorDiscount || 0) * radio);
        item.soloParentDiscount = roundTo2DecimalPlaces((originalItem.soloParentDiscount || 0) * radio);
        item.taxableAmount = roundTo2DecimalPlaces((originalItem.taxableAmount || 0) * radio);
        item.taxExemptAmount = roundTo2DecimalPlaces((originalItem.taxExemptAmount || 0) * radio);
        item.zeroRatedSales = roundTo2DecimalPlaces((originalItem.zeroRatedSales || 0) * radio);
        item.totalDeductedTax = roundTo2DecimalPlaces((originalItem.totalDeductedTax || 0) * radio);
        item.return = { quantity: item.quantity, total: item.total, unitPrice: item.return.unitPrice };

        //  promotions
        if (item.promotions && item.promotions.length > 0) {
          const originalPromotions = get(originalItem, 'promotions', []);
          map(item.promotions, (promotion, i) => {
            const originalPromotion = originalPromotions[i];
            if (Boolean(originalPromotion)) {
              promotion.discount = roundTo2DecimalPlaces((originalPromotion.discount || 0) * radio);
              promotion.pwdDiscount = roundTo2DecimalPlaces((originalPromotion.pwdDiscount || 0) * radio);
              promotion.seniorDiscount = roundTo2DecimalPlaces((originalPromotion.seniorDiscount || 0) * radio);
              promotion.tax = roundTo2DecimalPlaces((originalPromotion.tax || 0) * radio);
              promotion.taxableAmount = roundTo2DecimalPlaces((originalPromotion.taxableAmount || 0) * radio);
              promotion.taxExemptAmount = roundTo2DecimalPlaces((originalPromotion.taxExemptAmount || 0) * radio);
              promotion.uniquePromotionCodeId = get(originalPromotion, 'uniquePromotionCodeId');
              promotion.uniquePromotionCode = get(originalPromotion, 'uniquePromotionCode');
              promotion.display = { discount: promotion.discount };
            }
          });
        }
      }
      if (!Boolean(item.itemType)) {
        pureTransactionTotal += item.total;
        if (!item.isServiceChargeNotApplicable) {
          serviceChargeAbleTotal += item.total;
        }
        if (get(item, ['calculation', 'taxes', 0, 'isAmusementTax'])) {
          amusementTax += item.tax;
        }
        transactionSubtotal += item.subTotal;
        transactionTotal += item.total + item.tax;
        transactionTax += item.tax;
        transactionSeniorDiscount += item.seniorDiscount;
        transactionPwdDiscount += item.pwdDiscount;
        transactionAthleteAndCoachDiscount += item.athleteAndCoachDiscount;
        transactionMedalOfValorDiscount += item.medalOfValorDiscount;
        transactionSoloParentDiscount += item.soloParentDiscount;

        transactionTaxableSales += item.taxableAmount;
        transactionTaxExemptedSales += item.taxExemptAmount;
        transactionZeroRatedSales += item.zeroRatedSales;
        transactionTotalDeductedTax += item.totalDeductedTax;
        // 如果是taxInclusive，item.loyaltyDiscount是taxInclusive的,他们的和应该等于loyaltyDiscount.spentValue
        // 反之，item.loyaltyDiscount是taxExclusive的,他们的和应该等于loyaltyDiscount.displayDiscount
        transactionTotalLoyaltyDiscount += item.loyaltyDiscount;
        if (includingTaxInDisplay) {
          transactionTotalLoyaltySpent += item.loyaltyDiscount;
          transactionTotalDisplayLoyalty += item.loyaltyDiscount / (1 + item.taxRate || 0);
        } else {
          transactionTotalLoyaltySpent += item.loyaltyDiscount * (1 + item.taxRate || 0);
          transactionTotalDisplayLoyalty += item.loyaltyDiscount;
        }
      }

      if (item.itemType === 'ServiceCharge') {
        serviceChargeItemIndex = index;
      }
    }

    let serviceChargeTotal = 0;
    let serviceChargeTax = 0;
    let serviceChargeDiscount = 0;
    let serviceChargeSubtotal = 0;

    if (serviceChargeItemIndex > -1) {
      const serviceChargeItem = items[serviceChargeItemIndex];
      const originalServiceChargeItem = find(originalItems, item => item.itemType === 'ServiceCharge');
      const originalServiceChargeTotal = get(originalServiceChargeItem, 'total', 1);
      const originalServiceChargeDiscount = get(originalServiceChargeItem, 'discount', 0);

      const { rate, taxRate } = serviceChargeItem;
      serviceChargeTotal = roundTo2DecimalPlaces(serviceChargeAbleTotal * rate);
      serviceChargeTax = roundTo2DecimalPlaces(serviceChargeTotal * taxRate);
      serviceChargeDiscount = roundTo2DecimalPlaces((serviceChargeTotal / originalServiceChargeTotal) * originalServiceChargeDiscount);
      serviceChargeSubtotal = roundTo2DecimalPlaces(serviceChargeTotal + serviceChargeDiscount);
      currentRecord.items[serviceChargeItemIndex] = {
        ...serviceChargeItem,
        total: serviceChargeTotal,
        subTotal: serviceChargeSubtotal,
        tax: serviceChargeTax,
        discount: serviceChargeDiscount,
        display: { total: serviceChargeTotal, subtotal: serviceChargeSubtotal, tax: serviceChargeTax },
        return: { quantity: serviceChargeItem.quantity, total: serviceChargeTotal, unitPrice: serviceChargeTotal / (serviceChargeItem.quantity || 1) },
      };
    }

    // ------------------------------------   transaction   ------------------------------------
    currentRecord.total = roundTo2DecimalPlaces(transactionTotal + serviceChargeTotal + serviceChargeTax);
    currentRecord.subtotal = roundTo2DecimalPlaces(transactionSubtotal);
    currentRecord.serviceCharge = roundTo2DecimalPlaces(serviceChargeTotal);
    currentRecord.tax = roundTo2DecimalPlaces(transactionTax + serviceChargeTax);
    currentRecord.amusementTax = roundTo2DecimalPlaces(amusementTax);
    currentRecord.discount = roundTo2DecimalPlaces(currentRecord.subtotal + currentRecord.tax + currentRecord.serviceCharge - currentRecord.total);
    // adhocDiscount在当前的方案中没有办法很好的在部分退款中计算出来，这是否会影响一些report的统计？
    // loyaltyDiscounts
    if (currentRecord.loyaltyDiscounts && currentRecord.loyaltyDiscounts.length > 0) {
      const originalLoyaltyDiscount = currentRecord.loyaltyDiscounts[0];
      const spentValue = roundTo2DecimalPlaces(transactionTotalLoyaltySpent);
      if (spentValue !== originalLoyaltyDiscount.spentValue) {
        const newLoyaltyDiscount: any = {
          type: originalLoyaltyDiscount.type,
          loyaltyType: originalLoyaltyDiscount.type,
          inputValue: originalLoyaltyDiscount.inputValue || 0, // 可忽略
          spentValue: spentValue,
        };
        // displayDiscount永远是taxExclusive的
        const displayDiscount = roundTo2DecimalPlaces(transactionTotalDisplayLoyalty);
        newLoyaltyDiscount.displayDiscount = displayDiscount;
        newLoyaltyDiscount.display = {
          discount: displayDiscount,
          discountedTax: roundTo2DecimalPlaces(spentValue - displayDiscount),
        };
        currentRecord.loyaltyDiscounts = [newLoyaltyDiscount];
      }
    }
    // PH BIR
    currentRecord.pwdDiscount = roundTo2DecimalPlaces(transactionPwdDiscount);
    currentRecord.seniorDiscount = roundTo2DecimalPlaces(transactionSeniorDiscount);
    currentRecord.taxableSales = roundTo2DecimalPlaces(transactionTaxableSales);
    currentRecord.taxExemptedSales = roundTo2DecimalPlaces(transactionTaxExemptedSales);
    currentRecord.zeroRatedSales = roundTo2DecimalPlaces(transactionZeroRatedSales);
    currentRecord.totalDeductedTax = roundTo2DecimalPlaces(transactionTotalDeductedTax);
    currentRecord.display = {
      subtotal: currentRecord.subtotal,
      discount: currentRecord.discount,
      serviceCharge: currentRecord.serviceCharge,
      tax: currentRecord.tax,
      total: currentRecord.total,
    };
    // TODO: promotions

    const addonBirCompliance = get(currentRecord, 'addonBirCompliance');
    if (Boolean(addonBirCompliance)) {
      currentRecord.addonBirCompliance = {
        ...addonBirCompliance,
        athleteAndCoachDiscount: roundTo2DecimalPlaces(transactionAthleteAndCoachDiscount),
        medalOfValorDiscount: roundTo2DecimalPlaces(transactionMedalOfValorDiscount),
        soloParentDiscount: roundTo2DecimalPlaces(transactionSoloParentDiscount),
      };
    }
  } else {
    currentRecord = { ...originalRecord, originalRecord };
  }
  // infoTransactionEvent({ action: RefundFlowAction.UpdateTransactionReturnItem, transaction: currentRecord });
  yield put(Actions.setTransactionSession(currentRecord));
};

const getItemOptionText = item => {
  const options = item.options || item.selectedOptions || [];
  const optionText = [];
  forEach(options, option => {
    if (option.value > 1) {
      optionText.push(`${option.quantity} ${option.optionValue}`);
    } else {
      optionText.push(`${option.optionValue}`);
    }
  });
  return optionText.join(',');
};

export const searchTransactionSaga = function* (action) {
  const { keyword, linkedRefundTrx = false, onComplete } = action.payload;

  // Search local and remote records, return the merged result.
  const localRecords = linkedRefundTrx ? DAL.searchCompleteTrxBykeyword(keyword) : DAL.searchTrxBykeyword(keyword);

  const immutableStoreInfo = yield select(selectStoreInfo);
  const birAccredited = yield select(selectBirAccredited);
  const gbEnableVoidReceipt = yield select(selectGBEnableVoidReceipt);
  const business = immutableStoreInfo.get('name');
  const storeId = immutableStoreInfo.getIn(['store', '_id']);
  yield put(Actions.searchRemoteTransactions({ business, storeId, receiptNumber: keyword, isIncludeCancel: birAccredited && gbEnableVoidReceipt }));

  const responseAction = yield take([Actions.searchRemoteTransactions.toString() + '.success', Actions.searchRemoteTransactions.toString() + '.failure']);
  if (Array.isArray(responseAction.payload.asArray)) {
    const mergedResult = mergeTransaction(localRecords, responseAction.payload.asArray);
    const values = Object.values(mergedResult);
    values.sort((a: any, b: any) => {
      const dateA = new Date(a.createdDate);
      const dateB = new Date(b.createdDate);
      return dateA > dateB ? -1 : 1;
    });
    onComplete.callback(values, null);
    return;
  }
  onComplete.callback(localRecords, null);
};

export function* checkRefundTransactionValiditySaga(action) {
  const { onComplete } = action.payload;
  const transaction = yield call(getCurrentTransaction);
  const items = get(transaction, 'items', []);
  let isValidate = true;
  if (getDisplayItemsCount(items) <= 0) {
    isValidate = false;
    onComplete && onComplete.callBack({ isValidate, errorMsg: t('You must select at least one item to refund') });
  }
  forEach(items, item => {
    let errorMsg;
    if (!Boolean(item.itemType) && Boolean(item.return) && item.return.quantity === 0) {
      isValidate = false;
      errorMsg = t('0 quantity is not allowed');
      errorTransactionEvent({ action: RefundFlowAction.CheckRefundTransactionValidity, reason: '0 quantity is not allowed', transaction });
      onComplete.callBack && onComplete.callBack({ isValidate, errorMsg });
    }
  });
  if (isValidate) {
    infoTransactionEvent({ action: RefundFlowAction.CheckRefundTransactionValidity, transaction });
    onComplete && onComplete.callBack({ isValidate: true });
  }
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export function* clearPaymentSaga(action) {
  const transaction = yield call(getCurrentTransaction);
  transaction.payments = null;
  infoTransactionEvent({ action: RefundFlowAction.ClearRefundTransactionPayment, transaction });
  yield put(Actions.setTransactionSession(transaction));
}

export const clearRefundPromotionSaga = function* () {
  const currentRecord = yield call(getCurrentTransaction);
  // 删除所有的promotion，这有点confuse！！！

  if (getDisplayItemsCount(currentRecord.items) > 0) {
    // item level promotion: 修改每个item中分到的total
    const items = get(currentRecord, 'items', []);
    const displayItems = filter(items, item => !Boolean(item.itemType));
    for (let index = 0; index < displayItems.length; index++) {
      const item = displayItems[index];
      const promotions = get(item, 'promotions', []);
      const subTotal = get(item, 'subTotal', 0);
      let newTotal = item.total || 0;
      forEach(promotions, promotion => {
        const discount = get(promotion, 'discount', 0);
        newTotal += discount;
      });
      item.total = Math.min(subTotal, roundTo2DecimalPlaces(newTotal));
    }
    // order level promotion: 按照比例修改每个item中分到的total
    const promotions = get(currentRecord, 'promotions', []);
    const subtotal = get(currentRecord, 'subtotal');
    forEach(promotions, promotion => {
      const discount = get(promotion, 'discount', 0);
      const discountRadio = Boolean(subtotal) ? discount / subtotal : 0;
      let totalFairValue = 0;
      for (let index = 0; index < displayItems.length; index++) {
        const item = displayItems[index];
        const itemSubtotal = get(item, 'subTotal', 0);
        const itemTotal = get(item, 'total', 0);
        if (index === displayItems.length - 1) {
          const shouldFairValue = roundTo2DecimalPlaces(discount - totalFairValue);
          const expectedTotal = roundTo2DecimalPlaces(itemTotal + shouldFairValue);
          const finalTotal = Math.min(itemSubtotal, expectedTotal);
          item.total = finalTotal;
        } else {
          const shouldFairValue = roundTo2DecimalPlaces(discountRadio * itemSubtotal);
          const expectedTotal = roundTo2DecimalPlaces(itemTotal + shouldFairValue);
          const finalTotal = Math.min(itemSubtotal, expectedTotal);
          const finalFairValue = Math.max(0, roundTo2DecimalPlaces(finalTotal - itemTotal));
          item.total = finalTotal;
          totalFairValue += finalFairValue;
        }
      }
    });

    for (let index = 0; index < displayItems.length; index++) {
      const item = displayItems[index];
      item.return.total = item.total;
      item.return.unitPrice = item.return.total / (item.return.quantity || 1);
    }

    yield call(clearPromotions, currentRecord);
    yield call(calculateReturnTransaction, currentRecord, false, -1);
  }
};

function* transactionSagas() {
  yield takeLatest(Actions.checkTrxRefundStatus.toString(), checkRefundStatusSaga);
  yield takeLatest(Actions.checkCancellableWithRefundStatus.toString(), checkCancellableWithRefundStatusSaga);
  yield takeLatest(Actions.updateReturnItem.toString(), updateReturnItemSaga);
  yield takeLatest(Actions.searchTransactions, searchTransactionSaga);
  yield takeLatest(Actions.checkRefundTransactionValidity.toString(), checkRefundTransactionValiditySaga);
  yield takeLatest(Actions.clearPayment.toString(), clearPaymentSaga);
  yield takeLatest(Actions.clearRefundPromotion.toString(), clearRefundPromotionSaga);
}

export default fork(transactionSagas);
