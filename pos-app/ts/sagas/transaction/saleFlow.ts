import { TransactionType } from './../../typings/schema/TransactionType';

import { filter, find, findIndex, first, forEach, get, groupBy, isArray, isEmpty, isNil, isUndefined, keys, map, pick, split, values } from 'lodash';
import { call, delay, fork, put, SagaReturnType, select, spawn, take, takeEvery, takeLatest } from 'redux-saga/effects';
import * as Actions from '../../actions';
import {
  ItemChannelType,
  MakePaymentErrorType,
  SalesChannelType,
  SuccessCode,
  SuccessMessage,
  SystemSettingsCheckingType,
  t,
  TransactionFlowType,
} from '../../constants';
import DAL from '../../dal';
import { ItemKDSInfo, ProductType, PurchasedItemType } from '../../typings';
import { convertToNumber, getFixed2NumberString, getUnNullValue, isValidNumber, queryArray, roundingSum, roundingToAmount } from '../../utils';
import * as JSONUtils from '../../utils/json';
import {
  BeepFlowAction,
  BeepFlowTag,
  errorTransactionEvent,
  infoTransactionEvent,
  logFailedServerRequest,
  LoggingLevel,
  logSucceedServerRequest,
  OrderOperationEnum,
  POSBasicAction,
  SaleFlowAction,
  ShiftFlowAction,
  warnPOSBasicEvent,
  WorkflowStatus,
} from '../../utils/logComponent';
import * as ProductUtils from '../../utils/product';
import { getDisplayItemsCount, isPreOrderPickUp } from './../../utils/transaction';
import {
  selectAllowOutOfStockForSalesFlow,
  selectBirAccredited,
  selectBusinessName,
  selectEmployeeId,
  selectEnableCashback,
  selectEnableLoyalty,
  selectIncludingTaxInDisplay,
  selectInMergeOpenOrder,
  selectInSplitOpenOrder,
  selectIsKdsPaired,
  selectIsNetConnected,
  selectIsOneZReadingPerDayEnabled,
  selectRegisterId,
  selectRegisterObjectId,
  selectRoundAllTransactions,
  selectRountTo,
  selectSequence,
  selectSequentialReceiptNumber,
  selectShiftOpenStatus,
  selectSplitedOrder,
  selectSplitTargetOrder,
  selectStore,
  selectStoreId,
  selectStoreInfo,
  selectTimezone,
} from './../selector';

import {
  checkCustomerInfoFromQR,
  CheckoutTransactionType,
  GenericOnlineOrderPayType,
  MakePaymentType,
  ManualDiscountInput,
  MarkPaidType,
  MRSError,
  OrderItemInputType,
  PrintKitchenOnCheckOutType,
  ProposeDataType,
  requestProposalAction,
  SplitOnlineOrderType,
  SplitOrderInput,
  TransactionTypeWithDisplay,
} from '../../actions';
import { calculatePayAmount } from '../../utils/checkout';
import { signatureMd5 } from '../../utils/signature';
import { approvePaymentManually } from '../employeeActivity';

import moment from 'moment';
import PaymentOptions, { DefaultPaymentOptionType } from './../../config/paymentOption';
import {
  calculateItemsToKitchenOrder,
  checkItemsDiffTax,
  checkPriceBooks,
  checkProductTaxRateWithBIR,
  clearPromotions,
  conversionDiscountInLoyaltyDiscounts,
  generateOrderId,
  generateReceiptNumber,
  getCurrentRecord,
  getCurrentTransaction,
  getCurSequence,
  getServiceChargeTaxRateWithStore,
  getTaxRateByIdWithStore,
  stringifyItemOptions,
  tryApplyPromotion,
  updateTransactionWithAppliedPriceBooks,
} from './common';

import { Action } from 'redux-actions';
import { getBeepOrderShippingTag, getDisplayedStatus, getPreOrderTag } from '../../utils/beep';
import { MRSConfig } from '../../utils/mrs';

import momentTimezone from 'moment-timezone';

import { TIMEZONE } from '../../config';
import { KitchenEvent, KitchenManager } from '../../models/print/manager/KitchenManager';

import { navigate } from '../../navigation/navigatorService';
import { CookingStatus } from '../../utils/kds/status';
import { logShiftEvent } from '../../utils/logComponent/buz/shift';
import { payByCashNeedPushKds, restoreKDSInfo, updateCookingStatusFromItems } from '../kds/transaction/helper';
import { addKitchenStationToTrans, pushLocalOpenOrdersSaga, pushLocalTransactionsSaga } from '../kds/transaction/push';
import { checkIsNeedMRSSaga, onMRSInterceptor } from '../mrs/checkSync';
import { checkLimitBeforeExecuteSaga, requestProposalActionSaga } from '../mrs/roles/proposer';
import { getPrinterNotification } from '../printing/common';
import calculateP from './common/calculator';
import {
  generatePayLaterTransactionSession,
  getOnlineInputItemLevelDiscount,
  getSplitSaveOrder,
  updateSplitOnlineOrderAfterCheckOutSaga,
} from './openOrderFlow';

export enum SubscriptionPlan {
  Lite = 0,
  Small = 1,
  Medium = 2,
  Large = 3,
  InternalUnlimited = 100,
}

export const getChildProduct = (product, options) => {
  let resultProduct, resultOptions;
  const isParentProduct = ProductUtils.isParentProduct(product);
  if (!isParentProduct) {
    resultProduct = product;
    resultOptions = options;
    return {
      resultProduct,
      resultOptions,
    };
  }

  try {
    const result = DAL.getProductByParentId(product.productId) || [];
    let matched = null;
    const length = get(result, 'length', 0);
    for (let i = 0; i < length; i++) {
      const childProduct = get(result, i);
      matched = true;
      const variationValues = JSONUtils.parse(childProduct.variationValuesJson, []);
      for (const item of variationValues) {
        const selectItem = queryArray(options, item.variationId, 'variationId');
        if (selectItem === null) {
          matched = false;
          break;
        }
        if (!isNaN(item.value) && !isNaN(selectItem.optionValue)) {
          matched = convertToNumber(item.value) === convertToNumber(selectItem.optionValue);
          if (!matched) break;
        } else if (item.value !== selectItem.optionValue) {
          matched = false;
          break;
        }
      }
      if (matched) {
        // Found child product
        const newOptions = [];
        const variations = JSONUtils.parse(childProduct.variationsJson, []);
        for (const optionItem of options) {
          const variationItem = queryArray(variations, optionItem.variationId, '_id');
          if (variationItem !== null) {
            newOptions.push(optionItem);
          }
        }
        resultOptions = newOptions;
        resultProduct = childProduct;

        return {
          resultProduct,
          resultOptions,
        };
      }
    }
  } catch (exception) {
    console.warn('getChildProduct exception', exception);
    errorTransactionEvent({ action: SaleFlowAction.AddPurchasedItem, reason: 'getChildProduct exception', exception });
  }
  return {
    resultProduct,
    resultOptions,
  };
};

export interface MergeablePurchasedItemType {
  transactionSession: any;
  product: any;
  options: any;
  quantity: any;
  immutableStore: any;
  subscriptionPlan: number;
  enableGroupItem: boolean;
  autoAssignSalesPersonToNewItem: boolean;
  conflictEmployeeId?: string;
  employeeId?: string;
  employeeName?: string;
}
export const getMergeablePurchasedItem = ({
  transactionSession,
  product,
  options,
  quantity,
  immutableStore,
  subscriptionPlan,
  enableGroupItem,
  autoAssignSalesPersonToNewItem,
  conflictEmployeeId,
  employeeId,
  employeeName,
}) => {
  /* Need to check if it's below 4 type of product (which doesn't merge with exsiting )
    1 By Unit(PriceType === 'weight')
    2 Pirce variable
    3 product is serialized
    4 Options !== null || Options.length > 0
  */
  let allowMerge = true;
  const priceType = get(product, 'priceType');
  const isSerialized = get(product, 'isSerialized');
  const productId = get(product, 'productId');
  const parentProductId = get(product, 'parentProductId');
  const variationsJson = get(product, 'variationsJson');
  const variations = JSONUtils.parse(variationsJson, []);
  if (
    priceType === 'weight' ||
    priceType === 'variable' ||
    isSerialized ||
    get(options, 'length', 0) > 0 ||
    (variations.length > 0 && !Boolean(parentProductId))
  ) {
    allowMerge = false;
  }
  let purchasedItem = null;
  let salesPerson = null;
  // Checking exsiting product id and previous stored item shouble have no options when we want to merge it.
  if (transactionSession.items && allowMerge && (autoAssignSalesPersonToNewItem || enableGroupItem)) {
    for (const _item of transactionSession.items) {
      if (_item.productId === productId) {
        if (enableGroupItem) {
          if (conflictEmployeeId && _item.employeeId && _item.employeeId !== conflictEmployeeId) {
            // salesPerson conflict, can not merge to pay directly
            return {
              purchasedItem: null,
              salesPerson: null,
              conflict: true,
            };
          }
          // merge orders
          if (!_item.employeeId && employeeId) {
            _item.employeeId = employeeId;
            _item.employeeName = employeeName;
          }
          purchasedItem = _item;
          purchasedItem.quantity = quantity + _item.quantity;
          if (_item.cookingStatus === CookingStatus.prepared && purchasedItem.cookingStatus === CookingStatus.prepared) {
            // merge orders for KDS
            purchasedItem.mergedCookingStatus = true;
          }
          purchasedItem = checkPriceBooks({
            product,
            appliedPriceBooks: transactionSession.appliedPriceBooks,
            purchasedItem,
            immutableStore,
            subscriptionPlan,
          });
        } else if (autoAssignSalesPersonToNewItem && _item.employeeId) {
          salesPerson = {
            employeeId: _item.employeeId,
            employeeName: _item.employeeName,
          };
        }
        break;
      }
    }
  }

  return { purchasedItem, salesPerson, conflict: false };
};

type Return = {
  quantity: number | string;
  total: number | string;
};

export type AddPurchasedItemToRecordParams = {
  currentRecord: TransactionTypeWithDisplay;
  itemIndex?: number;
  productId?: string;
  options?: any;
  quantity?: number | string;
  discount?: number;
  isDiscountEnable?: boolean;
  variablePrice?: any;
  notes?: string;
  sn?: string;
  originalQuantity?: any;
  taxCode?: string;
  title?: string;
  onBIRTaxRateDisableCallBack?({ enable }: { enable: boolean }): void;
  itemChannel?: number;
  return?: Return;
  discountOption?: any;
  immutableStore?: any;
  immutableStoreInfo?: any;
  fullBillDiscount?: ManualDiscountInput;
  itemLevelDiscount?: ManualDiscountInput;
  kdsInfo?: ItemKDSInfo;
  employeeId?: string;
  conflictEmployeeId?: string;
  employeeName?: string;
  autoAssignSalesPersonToNewItem?: boolean;
};

// Get the Product from the database, build the PurchaseedItem and add it to the Transaction
// return false on failure and true on success
export const addPurchasedItemToRecord = (params: AddPurchasedItemToRecordParams) => {
  const {
    currentRecord,
    productId,
    options,
    quantity,
    isDiscountEnable,
    discount,
    variablePrice,
    notes,
    sn,
    originalQuantity,
    taxCode,
    title,
    onBIRTaxRateDisableCallBack,
    itemChannel,
    discountOption,
    immutableStore,
    immutableStoreInfo,
    kdsInfo = {},
    employeeId,
    employeeName,
    autoAssignSalesPersonToNewItem,
    conflictEmployeeId,
  } = params;
  let resultProductId = productId;
  const product = DAL.getProductById(productId);
  const country = immutableStore.get('country');
  const birAccredited = immutableStore.get('birAccredited');
  const subscriptionPlan = immutableStoreInfo.get('subscriptionPlan');
  const enableGroupItem = immutableStore.get('enableGroupItem');
  const transactionId = get(currentRecord, 'transactionId');
  if (Boolean(options) && Boolean(product)) {
    // When refund transaction(not manual), options in db dont have priceDiff
    forEach(options, option => {
      const isPriceDiffUndefined = isUndefined(option.priceDiff);
      if (isPriceDiffUndefined) {
        const variations = JSON.parse(product.variationsJson);
        forEach(variations, variation => {
          if (option.variationId === variation._id) {
            forEach(variation.optionValues, _option => {
              if (option.optionId === _option._id) {
                option.priceDiff = _option.priceDiff;
              }
            });
          }
        });
      }
    });
  }

  // Check it product exists, if not, add to cart with db data.
  if (!Boolean(product)) {
    const items = currentRecord.items || [];
    const selectedOptions = [];
    if (options) {
      for (const _option of options) {
        selectedOptions.push({
          optionValue: _option.priceDiff ?? 0,
          quantity: _option.quantity,
        });
      }
    }
    const { taxRate, isVatExempted, isAmusementTax } = getTaxRateByIdWithStore(immutableStore, taxCode);
    const productEnableWithBIR = checkProductTaxRateWithBIR(
      country,
      birAccredited,
      null,
      { taxRate, isAmusementTax },
      currentRecord,
      onBIRTaxRateDisableCallBack
    );
    if (!productEnableWithBIR) return { result: false, resultProductId };
    const purchasedItem: any = {
      id: getUniqueIdForShoppingCartOrWishlist(productId, options, items, transactionId),
      productId: productId,
      unitPrice: variablePrice,
      priceType: 'fixed',
      selectedOptions,
      options: options,
      quantity,
      taxRate,
      isVatExempted,
      isAmusementTax,
      title: title,
      total: 0,
      taxCode,
      notes,
      itemChannel:
        currentRecord.salesChannel && currentRecord.salesChannel === SalesChannelType.TAKEAWAY
          ? ItemChannelType.TAKEAWAY
          : itemChannel
            ? itemChannel
            : ItemChannelType.DEFAULT,
      isTakeaway:
        currentRecord.salesChannel && currentRecord.salesChannel === SalesChannelType.TAKEAWAY
          ? true
          : itemChannel && itemChannel === ItemChannelType.TAKEAWAY
            ? true
            : false,
      ...kdsInfo,
      employeeId,
      employeeName,
    };
    if (params.return) {
      purchasedItem.return = params.return;
    }
    if (discountOption) {
      purchasedItem.itemLevelDiscount = discountOption;
      purchasedItem.discountInputValue = discountOption.inputValue;
      purchasedItem.discountType = discountOption.type;
    }
    items.push(purchasedItem);
    currentRecord.items = items;
    return { result: true, resultProductId };
  }

  const { kitchenPrinter } = product;

  /* Checking whether it's parent product and tracking inventory
    if Both yes, then need to pass the child product's info and options
  */
  const { resultProduct, resultOptions } = getChildProduct(product, options);

  const mergedResult = getMergeablePurchasedItem({
    transactionSession: currentRecord,
    product: resultProduct,
    options: resultOptions,
    quantity,
    immutableStore,
    subscriptionPlan,
    enableGroupItem,
    autoAssignSalesPersonToNewItem,
    employeeId,
    employeeName,
    conflictEmployeeId,
  });
  if (mergedResult.conflict) {
    return { result: false, resultProductId, mergeConflict: true };
  }
  let purchasedItem = mergedResult.purchasedItem;
  // Convert options to selectedOptions
  const selectedOptions = [];
  if (resultOptions) {
    for (const _option of resultOptions) {
      selectedOptions.push({
        optionValue: _option.priceDiff ?? 0,
        quantity: _option.quantity,
      });
    }
  }

  if (Boolean(get(resultProduct, 'productId'))) {
    resultProductId = resultProduct.productId;
  }

  if (purchasedItem === null) {
    const salesPerson = mergedResult.salesPerson;
    if (!Boolean(resultProduct)) return { result: false, resultProductId };
    const items = currentRecord.items || [];
    const { taxRate, taxCode, isVatExempted, isAmusementTax } = getTaxRateByIdWithStore(immutableStore, resultProduct.taxCode);
    // Create new item
    purchasedItem = {
      id: getUniqueIdForShoppingCartOrWishlist(resultProduct.productId, resultOptions, items, transactionId),
      productId: resultProduct.productId,
      unitPrice: variablePrice || resultProduct.unitPrice,
      priceType: resultProduct.priceType,
      kitchenPrinter: resultProduct.kitchenPrinter,
      selectedOptions,
      options: resultOptions,
      quantity,
      discount,
      isDiscountEnable,
      taxRate,
      isVatExempted,
      isAmusementTax,
      isBasicNecessitiesPH: resultProduct.isBasicNecessitiesPH,
      isSoloParentDiscountApplicable: resultProduct.isSoloParentDiscountApplicable,
      title: resultProduct.title,
      total: 0,
      trackInventory: resultProduct.trackInventory,
      taxCode,
      notes,
      product: {
        category: resultProduct.category,
        tags: map(resultProduct.tags, tag => tag),
        _id: resultProduct.productId,
      },
      itemChannel:
        currentRecord.salesChannel && currentRecord.salesChannel === SalesChannelType.TAKEAWAY
          ? ItemChannelType.TAKEAWAY
          : itemChannel
            ? itemChannel
            : ItemChannelType.DEFAULT,
      isTakeaway:
        currentRecord.salesChannel && currentRecord.salesChannel === SalesChannelType.TAKEAWAY
          ? true
          : itemChannel && itemChannel === ItemChannelType.TAKEAWAY
            ? true
            : false,
      ...kdsInfo,
      employeeId,
      employeeName,
    };
    // no merged item
    // auto set salesPerson on adding new item to cart if setting is ungroup
    if (autoAssignSalesPersonToNewItem && salesPerson) {
      purchasedItem.employeeId = salesPerson.employeeId;
      purchasedItem.employeeName = salesPerson.employeeName;
    }

    if (discountOption) {
      purchasedItem.itemLevelDiscount = discountOption;
      purchasedItem.discountInputValue = discountOption.inputValue;
      purchasedItem.discountType = discountOption.type;
    }

    if (sn) {
      purchasedItem.sn = sn;
      if (Boolean(get(currentRecord, ['serialNumbers', resultProduct.productId]))) {
        currentRecord.serialNumbers[resultProduct.productId].push(sn);
      } else {
        currentRecord.serialNumbers[resultProduct.productId] = [sn];
      }
    }

    purchasedItem = checkPriceBooks({
      product: resultProduct,
      appliedPriceBooks: get(currentRecord, 'appliedPriceBooks'),
      purchasedItem,
      immutableStore,
      subscriptionPlan,
    });
    const productEnableWithBIR = checkProductTaxRateWithBIR(country, birAccredited, null, purchasedItem, currentRecord, onBIRTaxRateDisableCallBack);
    if (!productEnableWithBIR) return { result: false, resultProductId };

    if (params.return) {
      purchasedItem.return = params.return;
    }

    if (originalQuantity) {
      purchasedItem.originalQuantity = originalQuantity;
    }

    const defaultKitchenPrinter = get(immutableStore.toJS(), 'defaultKitchenPrinter', '');

    purchasedItem.kitchenStation = kitchenPrinter || defaultKitchenPrinter;

    items.push(purchasedItem);
    currentRecord.items = items;
    return { result: true, resultProductId };
  } else {
    const productEnableWithBIR = checkProductTaxRateWithBIR(country, birAccredited, null, purchasedItem, currentRecord, onBIRTaxRateDisableCallBack);
    if (!productEnableWithBIR) return { result: false, resultProductId };
    return { result: true, resultProductId };
  }
};

export const previewBeepOrder = function* (params: AddPurchasedItemToRecordParams, onUpdateResult?) {
  const { currentRecord, productId, options, quantity, itemIndex, notes, onBIRTaxRateDisableCallBack, fullBillDiscount, itemLevelDiscount } = params;
  if (currentRecord) {
    const businessName = yield select(selectBusinessName);
    const storeId = yield select(selectStoreId);
    const { items, channel, shippingType, subOrders } = currentRecord;
    const discountIndex = findIndex(items, item => item.itemType === 'Discount');
    let oldFullBillDiscount: ManualDiscountInput = null;
    if (fullBillDiscount === null || get(fullBillDiscount, 'inputValue') === 0) {
      oldFullBillDiscount = null;
    } else if (!fullBillDiscount && discountIndex) {
      const discountItem = items[discountIndex];
      if (discountItem && discountItem.discountValue && discountItem.discountType) {
        oldFullBillDiscount = {
          inputValue: discountItem.discountValue,
          type: discountItem.discountType,
        };
      }
    }

    let itemsParam;
    if (Boolean(productId)) {
      itemsParam = getMergeItems(items, productId, quantity, options, notes);
    } else {
      itemsParam = getEditedItems(items, itemIndex, quantity, itemLevelDiscount);
    }
    if (!Boolean(itemsParam)) return false;
    yield put(
      Actions.orderPreview({
        businessName,
        storeId,
        channel,
        shippingType,
        items: itemsParam,
        fullBillDiscount: fullBillDiscount ? fullBillDiscount : oldFullBillDiscount,
      })
    );
    const tags = [BeepFlowTag.Beep, getBeepOrderShippingTag(currentRecord), getPreOrderTag(currentRecord), BeepFlowTag.PayLater];
    // the orderInfo be tracked in loggly
    const orderInfo = {
      orderId: currentRecord.receiptNumber,
    };
    const responseAction = yield take([Actions.orderPreview.toString() + '.success', Actions.orderPreview.toString() + '.failure']);
    if (responseAction.type === Actions.orderPreview.toString() + '.success') {
      logSucceedServerRequest(BeepFlowAction.AddItem, tags, null, orderInfo);
      const previewOrder = get(responseAction, ['payload', 'orderPreview']);
      if (previewOrder) {
        const { items: preViewItems, total, discount, subTotal, serviceCharge, serviceChargeTax, tax, promotions, maximumDiscountInputValue } = previewOrder;
        currentRecord.total = total;
        currentRecord.discount = discount;
        currentRecord.subtotal = subTotal;
        currentRecord.serviceCharge = serviceCharge;
        currentRecord.serviceChargeTax = serviceChargeTax;
        currentRecord.maximumDiscountInputValue = maximumDiscountInputValue;
        currentRecord.tax = tax;
        currentRecord.display = previewOrder.display || {
          subtotal: subTotal,
          discount,
          serviceCharge,
          tax,
          total,
        };
        const itemTypeItems = [];
        const unSavedItems = [];
        const submitedItems = [];
        if (Boolean(preViewItems)) {
          for (let i = 0; i < preViewItems.length; i++) {
            const preViewItem = preViewItems[i];
            const { type, id, selectedOptions, total, subTotal, tax } = preViewItem;
            // map to input submitId: originalItems
            const originalItem = find(get(currentRecord, 'originalItems', []), item => Boolean(item.id) && item.id == id);
            if (Boolean(originalItem)) {
              preViewItem.submitId = originalItem.submitId;
              const matchedSubmit = find(subOrders, subOrder => subOrder.submitId == preViewItem.submitId);
              if (Boolean(matchedSubmit)) {
                const { submittedBy, submittedFrom, submittedTime, comments } = matchedSubmit;
                if (Boolean(submittedTime)) {
                  preViewItem.submittedTime = moment(submittedTime).format('hh:mm A');
                  preViewItem.submitNotes = comments;
                } else {
                  preViewItem.submittedTime = moment().format('hh:mm A');
                }

                if (submittedFrom == 'employee' && Boolean(submittedBy)) {
                  const employee = DAL.getEmployeeById(submittedBy);
                  if (employee) {
                    const firstName = get(employee, 'firstName', '');
                    const lastName = get(employee, 'lastName', '');
                    preViewItem.submittedFrom = `${firstName}  ${lastName}`;
                  }
                } else {
                  if (currentRecord.customer) {
                    if (!isEmpty(currentRecord.customer.firstName) || !isEmpty(currentRecord.customer.lastName) || !isEmpty(currentRecord.customer.phone)) {
                      preViewItem.submittedFrom = 'Customer' + (currentRecord.customer.phone ? ` (${currentRecord.customer.phone}) ` : '');
                    } else {
                      preViewItem.submittedFrom = 'Customer';
                    }
                  } else {
                    preViewItem.submittedFrom = 'Customer';
                  }
                }
              }
            } else {
              preViewItem.submittedTime = moment().format('hh:mm A');
              const employeeId = yield select(selectEmployeeId);
              const employee = DAL.getEmployeeById(employeeId);
              if (employee) {
                const firstName = get(employee, 'firstName', '');
                const lastName = get(employee, 'lastName', '');
                preViewItem.submittedFrom = `${firstName}  ${lastName}`;
              }
            }
            preViewItem.itemType = type;
            preViewItem.options = selectedOptions;
            preViewItem.display = preViewItem.display || { total, subTotal, tax };
            if (Boolean(type)) {
              itemTypeItems.push(preViewItem);
            } else if (Boolean(id)) {
              submitedItems.push(preViewItem);
            } else {
              unSavedItems.push(preViewItem);
            }
          }
        }

        const fullPromotions = [];
        if (Boolean(promotions) && isArray(promotions)) {
          for (let index = 0; index < promotions.length; index++) {
            const promotion = promotions[index];
            const { discount, promotionName } = promotion;
            fullPromotions.push({ ...promotion, title: promotionName, display: { discount } });
          }
        }
        currentRecord.promotions = fullPromotions;

        currentRecord.hasUnsavedItems = unSavedItems.length > 0;
        // Tag the Sumbit Sections Dividing line
        const groupedSubmitedItems = values(groupBy(submitedItems, item => item.submitId));
        for (let i = 0; i < groupedSubmitedItems.length; i++) {
          const groupeItems = groupedSubmitedItems[i];
          if (groupeItems && groupeItems.length > 0) {
            groupeItems[0].isGroupFirst = true;
            groupeItems[groupeItems.length - 1].isGroupLast = true;
          }
        }
        if (unSavedItems && unSavedItems.length > 0) {
          unSavedItems[0].isGroupFirst = true;
          unSavedItems[unSavedItems.length - 1].isGroupLast = true;
        }
        const newItems = groupedSubmitedItems.flat().concat(unSavedItems).concat(itemTypeItems);
        currentRecord.items = newItems;
        currentRecord.isPreview = true;
        // remove voucher when edit beep order
        const payments = get(currentRecord, 'payments', []);
        currentRecord.payments = filter(payments, payment => payment.paymentMethod !== 'Voucher');
        delete currentRecord.appliedVoucher;
        yield put(Actions.setTransactionSession(currentRecord));
      }
      onUpdateResult && onUpdateResult(true);
      onBIRTaxRateDisableCallBack && onBIRTaxRateDisableCallBack({ enable: true });
      return true;
    } else {
      logFailedServerRequest(BeepFlowAction.AddItem, tags, get(responseAction, 'payload', ''), null, orderInfo);
      onUpdateResult && onUpdateResult(false);
      onBIRTaxRateDisableCallBack && onBIRTaxRateDisableCallBack({ enable: true });
      return false;
    }
  }
};

const getEditedItems = (items: any[], itemIndex, newQuantity, newItemLevelDiscount) => {
  const realItems = filter(items, i => !Boolean(i.itemType));
  const itemsParam: OrderItemInputType[] = [];
  for (let index = 0; index < realItems.length; index++) {
    const item = realItems[index];
    const { id, productId, quantity, selectedOptions, notes, manualDiscount, isTakeaway = false } = item;
    if (index == itemIndex) {
      if (newQuantity > 0) {
        itemsParam.push({ id, productId, quantity: newQuantity, selectedOptions, notes, isTakeaway });
      }
      if (newItemLevelDiscount) {
        itemsParam.push({
          id,
          productId,
          quantity,
          selectedOptions,
          notes,
          isTakeaway,
          itemLevelDiscount: getOnlineInputItemLevelDiscount(newItemLevelDiscount),
        });
      }
    } else {
      itemsParam.push({ id, productId, quantity, selectedOptions, notes, isTakeaway, itemLevelDiscount: getOnlineInputItemLevelDiscount(manualDiscount) });
    }
  }
  return itemsParam;
};

const getMergeItems = (items: any[], productId, quantity, options, newNotes) => {
  const product = DAL.getProductById(productId);
  if (!Boolean(product)) return null;
  const itemsParam: OrderItemInputType[] = map(
    filter(items, i => !Boolean(i.itemType)),
    item => {
      const { id, productId, quantity, selectedOptions, notes, isTakeaway, manualDiscount } = item;
      return { id, productId, quantity, selectedOptions, notes, isTakeaway, itemLevelDiscount: getOnlineInputItemLevelDiscount(manualDiscount) };
    }
  );
  const { resultProduct, resultOptions } = getChildProduct(product, options);
  const priceType = get(product, 'priceType');
  const isSerialized = get(product, 'isSerialized');
  const resultProductId = get(resultProduct, 'productId');
  const parentProductId = get(product, 'parentProductId');
  const variationsJson = get(product, 'variationsJson');
  const variations = JSONUtils.parse(variationsJson, []);
  if (
    priceType === 'weight' ||
    priceType === 'variable' ||
    isSerialized ||
    get(options, 'length', 0) > 0 ||
    (variations.length > 0 && !Boolean(parentProductId))
  ) {
    // not allow merge
    const newItem: OrderItemInputType = {
      productId: resultProductId,
      quantity: Number(quantity),
      notes: newNotes,
      isTakeaway: false,
    };
    if (resultOptions && resultOptions.length > 0) {
      const newSelectedOptions = [];
      for (let index = 0; index < resultOptions.length; index++) {
        const optionItem = resultOptions[index];
        const { variationId, optionId, optionValue, quantity } = optionItem;
        newSelectedOptions.push({ variationId, optionId, optionValue, quantity });
      }
      newItem.selectedOptions = newSelectedOptions;
    }
    itemsParam.push(newItem);
  } else {
    const matchIndex = findIndex(itemsParam, item => !Boolean(item.id) && item.productId == resultProductId);
    if (matchIndex > -1) {
      itemsParam[matchIndex].quantity += Number(quantity);
    } else {
      const newItem: OrderItemInputType = {
        productId,
        quantity: Number(quantity),
        notes: newNotes,
        isTakeaway: false,
      };
      if (resultOptions) {
        const { variationId, optionId, optionValue, quantity } = resultOptions;
        newItem.selectedOptions = [{ variationId, optionId, optionValue, quantity }];
      }
      itemsParam.push(newItem);
    }
  }
  return itemsParam;
};

/**
 * Add a product to cart(probably existing a same one).
 * @param action {payload: roductId, options, quantity, variablePrice, notes, sn, originalQuantity}
 */
export const addPurchasedItemSaga = function* (action) {
  const { productId, options, discountOption, quantity, variablePrice, notes, sn, originalQuantity, taxCode, title, onBIRTaxRateDisableCallBack, itemChannel } =
    action.payload;
  const currentRecord = yield call(getCurrentTransaction);
  const immutableStore = yield select(selectStore);
  const immutableStoreInfo = yield select(selectStoreInfo);
  const includingTaxInDisplay = immutableStore.get('includingTaxInDisplay');

  const previewBeepOrderParams: AddPurchasedItemToRecordParams = {
    currentRecord,
    productId,
    options,
    quantity,
    variablePrice,
    notes,
    sn,
    originalQuantity,
    taxCode,
    title,
    onBIRTaxRateDisableCallBack,
    itemChannel,
    return: action.payload.return,
    discountOption,
    immutableStore,
    immutableStoreInfo,
  };
  if (currentRecord && currentRecord.isOnlineOrder) {
    yield call(previewBeepOrder, previewBeepOrderParams);
    return;
  }
  // offline orders
  previewBeepOrderParams.autoAssignSalesPersonToNewItem = currentRecord && !currentRecord.isOnlineOrder && !immutableStore.get('enableGroupItem');
  const { result, resultProductId } = addPurchasedItemToRecord(previewBeepOrderParams);
  if (!result) return;

  const enableCashback = yield select(selectEnableCashback);
  const enableLoyalty = yield select(selectEnableLoyalty);

  yield call(checkItemsDiffTax, currentRecord, enableCashback, enableLoyalty);

  if (!Boolean(originalQuantity)) {
    yield call(tryApplyPromotion, currentRecord);
  }

  const newItems = yield call(updateTransactionWithAppliedPriceBooks, {
    transaction: currentRecord,
    appliedPriceBooks: currentRecord.appliedPriceBooks,
    inTransaction: true,
  });
  currentRecord.items = newItems;

  try {
    calculateP(currentRecord, includingTaxInDisplay || false);
    conversionDiscountInLoyaltyDiscounts(currentRecord);
    yield put(Actions.setTransactionSession(currentRecord));
    yield put(Actions.warnToRemoveUniquePromo());
  } catch (exception) {
    if (exception) {
      errorTransactionEvent({
        action: SaleFlowAction.AddPurchasedItem,
        reason: 'Add purchase item error',
        transactionId: currentRecord.transactionId,
        exception,
      });
      yield put(Actions.toggleToastInfo({ visible: true, text: exception.message }));
    }
  }
  yield put(checkCustomerInfoFromQR({ currentRecord }));
  yield call(requestOutOfStockForProducts, { productId: resultProductId, currentRecord });
};

function isProductTrackStock(product?: ProductType) {
  return Boolean(product) && Boolean(product.trackInventory) && 'Composite' !== product.inventoryType;
}

const requestOutOfStockForProducts = function* (payload) {
  const { productId, currentRecord } = payload;
  const product = DAL.getProductById(productId);
  if (!Boolean(currentRecord)) {
    return;
  }
  const allowOutOfStock = yield select(selectAllowOutOfStockForSalesFlow);
  const isConnected = yield select(selectIsNetConnected);

  // if no internet, popup oos error
  if (!allowOutOfStock && !isConnected) {
    const txn = yield call(getCurrentTransaction);
    // avoid conflits with fade out popup animation from product with
    yield delay(200);
    txn.totalOutOfStockItemsCount = INVALID_OOS_STATUS_POPUP;
    yield put(Actions.setTransactionSession(txn));
    return;
  }

  // if not allow out of stock, always track product stock
  if (!isProductTrackStock(product)) {
    return;
  }

  const employeeId = yield select(selectEmployeeId);
  // A product with multiple choice addons are added as an item into items array in the transaction.
  // rather than accumulate the quantity of the same product in the items.

  yield put(Actions.getStocksForProduct({ productId, employeeId }));
};

/**
 *
 * @param productId The product id, whose quantity changes in the cart, we recalculate it's quantity
 * of out of stock here, and sumup to the total quantity of out of stock of all items in the cart, and
 * save it in current record to render out of stock UI in the header of cart.
 * @param currentRecord The record of current transaction.
 * @returns
 */
export const INVALID_OOS_STATUS_POPUP = -1;
export const INVALID_OOS_STATUS_BANNER = -2;
export const INVALID_OOS_STATUS_PENDING = -5;

export const updateOutOfStockStatus = function* (action) {
  const currentRecord = yield call(getCurrentTransaction);
  currentRecord.totalOutOfStockItemsCount = action.payload;
  yield put(Actions.setTransactionSession(currentRecord));
};

export const calculateOutOfStockForProductsSaga = function* (action) {
  const currentRecord = yield call(getCurrentTransaction);
  const result = action.payload;
  const productId = get(keys(result), '0');
  const product = DAL.getProductById(productId);
  const trackProductStockLevel = product && isProductTrackStock(product);
  if (!trackProductStockLevel) {
    return;
  }

  if (action.type === 'getStocksForProduct.failure') {
    const allowOutOfStock = yield select(selectAllowOutOfStockForSalesFlow);
    if (!allowOutOfStock) {
      currentRecord.totalOutOfStockItemsCount = INVALID_OOS_STATUS_POPUP;
      yield put(Actions.setTransactionSession(currentRecord));
    }
    return;
  }
  if (action.type === 'getStocksForProduct') {
    const allowOutOfStock = yield select(selectAllowOutOfStockForSalesFlow);
    if (!allowOutOfStock) {
      currentRecord.totalOutOfStockItemsCount = INVALID_OOS_STATUS_PENDING;
      yield put(Actions.setTransactionSession(currentRecord));
    }
    return;
  }

  if (!Boolean(productId)) return;

  if (getDisplayItemsCount(get(currentRecord, 'items', [])) === 0) return;
  const storeId = yield select(selectStoreId);
  // A product with multiple choice addons are added as an item into items array in the transaction.
  // rather than accumulate the quantity of the same product in the items.
  const storeStockMap = result[productId];
  if (!Boolean(storeStockMap)) {
    console.log('the value of product id return from server is not the same with that of query parameter productId');
    return;
  }
  let stockQuantity: number = convertToNumber(storeStockMap[storeId]);
  let remainStockQuantity = 0;
  let totalOutOfStockItemsCount = 0;
  get(currentRecord, 'items', [])
    .filter(item => Boolean(item.productId))
    .forEach(item => {
      if (productId == item.productId) {
        remainStockQuantity = stockQuantity - convertToNumber(item.quantity);
        // The item is out of stock and show with yellow font.
        if (remainStockQuantity < 0) {
          item.isOutOfStock = true;
        } else {
          item.isOutOfStock = false;
        }
        stockQuantity = remainStockQuantity;
      }
      if (item.isOutOfStock) {
        totalOutOfStockItemsCount += 1;
      }
    });
  currentRecord.totalOutOfStockItemsCount = totalOutOfStockItemsCount;

  yield put(Actions.setTransactionSession(currentRecord));
};

/**
 * @param productId
 * @param variations
 * @param items
 * @param transactionId
 * @returns
 */
export const getUniqueIdForShoppingCartOrWishlist = (productId, variations, items, transactionId = '') => {
  let curIndex = 1;
  const curPurchasedItems = filter(items, item => !Boolean(item.itemType));
  const curcurPurchasedItemsLength = curPurchasedItems.length;
  if (curcurPurchasedItemsLength > 0) {
    const lastItem = curPurchasedItems[curcurPurchasedItemsLength - 1];
    const lastItemId = get(lastItem, 'id', '');
    const lastItemIdIndex = get(split(lastItemId, ':'), '0', null);
    if (isValidNumber(lastItemIdIndex)) {
      curIndex = Number(lastItemIdIndex) + 1;
    } else {
      const date = moment();
      const hour = date.get('hour');
      const minute = date.get('minute');
      const second = date.get('second');
      curIndex = Number(`${hour}${minute}${second}`);
    }
  }
  const ids = [curIndex, productId];
  try {
    if (typeof variations === 'string') {
      variations = JSON.parse(variations);
    }
    if (variations && variations.length > 0) {
      variations.forEach(variation => {
        if (variation.variationId && variation.optionId) {
          ids.push(`${variation.variationId}:${variation.optionId}`);
        }
      });
    }
  } catch (error) {
    errorTransactionEvent({
      action: SaleFlowAction.getUniqueIdForShoppingCartOrWishlist,
      reason: 'invalidation variations',
      transactionId,
      privateDataPayload: { idErrorName: error.name, idErrorMessage: error.message, idErrorStack: error.stack, variations },
    });
  }
  return ids.join(':');
};

export const updatePurchasedItemSaga = function* (action) {
  const { itemIndex, quantity, discountOption, options, notes, isDiscountEnable, discountValue, onBIRQuantityDisableCallBack, itemChannel } = action.payload;
  const immutableStore = yield select(selectStore);
  const includingTaxInDisplay = immutableStore.get('includingTaxInDisplay');
  const immutableStoreInfo = yield select(selectStoreInfo);
  const subscriptionPlan = immutableStoreInfo.get('subscriptionPlan');
  let currentRecord = yield call(getCurrentTransaction);
  const item = { ...currentRecord.items[itemIndex] };
  const transactionId = get(currentRecord, 'transactionId');
  if (item) {
    if (quantity) {
      item.quantity = quantity;
    }
    if (discountOption) {
      item.itemLevelDiscount = discountOption;
      item.discountInputValue = discountOption.inputValue;
      item.discountType = discountOption.type;
    }

    if (options) {
      // Convert options to selectedOptions
      const selectedOptions = [];
      for (const option of options) {
        selectedOptions.push({
          optionValue: option.priceDiff,
          quantity: option.quantity,
        });
      }
      item.options = options;
      item.selectedOptions = selectedOptions;
    }

    if (itemChannel != undefined) {
      item.itemChannel = itemChannel;
      item.isTakeaway = itemChannel === ItemChannelType.TAKEAWAY;
    }

    item.notes = notes || '';
    const product = DAL.getProductById(item.productId);

    item.isDiscountEnable = isDiscountEnable;
    item.discountValue = discountValue;

    const newPurchasedItem = checkPriceBooks({
      purchasedItem: item,
      appliedPriceBooks: currentRecord.appliedPriceBooks,
      product,
      immutableStore,
      subscriptionPlan,
    });
    const country = immutableStore.get('country');
    const birAccredited = immutableStore.get('birAccredited');
    const productEnableWithBIR = checkProductTaxRateWithBIR(country, birAccredited, item, newPurchasedItem, currentRecord, onBIRQuantityDisableCallBack);
    if (!productEnableWithBIR) return;

    currentRecord.items[itemIndex] = newPurchasedItem;

    if (quantity) {
      const prevQuantity = item.quantity;
      const difference = prevQuantity - quantity;
      if (difference > 0) {
        const { transactionId } = currentRecord;
        const { productId, title } = item;
        yield put(
          Actions.employeeReduceItem({
            transactionId,
            productId,
            productName: title,
            quantity: difference,
          })
        );
      }
    }
  }

  yield call(tryApplyPromotion, currentRecord);
  try {
    currentRecord = calculateP(currentRecord, includingTaxInDisplay || false);
    conversionDiscountInLoyaltyDiscounts(currentRecord);
  } catch (exception) {
    console.log('calculate exception', exception);
    errorTransactionEvent({ action: SaleFlowAction.UpdatePurchasedItem, transactionId, reason: 'calculate exception', exception });
  }
  yield put(Actions.setTransactionSession(currentRecord));
  yield put(Actions.warnToRemoveUniquePromo());
};

export const updatePurchasedItemQuantitySaga = function* (action) {
  const { itemIndex, quantity, onBIRQuantityDisableCallBack, onUpdateResult } = action.payload;
  const immutableStore = yield select(selectStore);
  const includingTaxInDisplay = immutableStore.get('includingTaxInDisplay');
  const immutableStoreInfo = yield select(selectStoreInfo);
  const subscriptionPlan = immutableStoreInfo.get('subscriptionPlan');

  let currentRecord = yield call(getCurrentTransaction);
  const item = { ...currentRecord.items[itemIndex] };
  const prevQuantity = item.quantity;
  const difference = prevQuantity - quantity;
  const { isOnlineOrder = false, transactionId } = currentRecord;
  if (isOnlineOrder) {
    const previewBeepOrderParams: AddPurchasedItemToRecordParams = {
      currentRecord,
      itemIndex,
      quantity,
      onBIRTaxRateDisableCallBack: onBIRQuantityDisableCallBack,
    };
    yield call(previewBeepOrder, previewBeepOrderParams, onUpdateResult);
    return;
  }

  if (item) {
    item.quantity = quantity;
    const product = DAL.getProductById(item.productId);
    if (Boolean(product)) {
      const newPurchasedItem = checkPriceBooks({
        purchasedItem: item,
        appliedPriceBooks: currentRecord.appliedPriceBooks,
        product,
        immutableStore,
        subscriptionPlan,
      });
      const country = immutableStore.get('country');
      const birAccredited = immutableStore.get('birAccredited');
      const productEnableWithBIR = checkProductTaxRateWithBIR(country, birAccredited, item, newPurchasedItem, currentRecord, onBIRQuantityDisableCallBack);
      if (!productEnableWithBIR) return;
      currentRecord.items[itemIndex] = newPurchasedItem;
      const enableCashback = yield select(selectEnableCashback);
      const enableLoyalty = yield select(selectEnableLoyalty);

      yield call(checkItemsDiffTax, currentRecord, enableCashback, enableLoyalty);
      yield call(tryApplyPromotion, currentRecord);
    }
  }
  if (quantity) {
    if (difference > 0) {
      const { transactionId } = currentRecord;
      const { productId, title } = item;
      yield put(
        Actions.employeeReduceItem({
          transactionId,
          productId,
          productName: title,
          quantity: difference,
        })
      );
    }
  }
  try {
    currentRecord = calculateP(currentRecord, includingTaxInDisplay || false);
    conversionDiscountInLoyaltyDiscounts(currentRecord);
  } catch (exception) {
    console.log('calculate exception', exception);
    errorTransactionEvent({
      action: SaleFlowAction.UpdatePurchasedItemQuantity,
      transactionId,
      reason: 'calculate exception',
      exception,
      transaction: currentRecord,
    });
  }
  yield put(Actions.setTransactionSession(currentRecord));
  yield put(Actions.warnToRemoveUniquePromo());
  // Should remove the item first and then calculate out of stock, otherwise, the out of
  // stock information keep the same, however, it may change for ungrouped items with same
  // product id.
  yield call(requestOutOfStockForProducts, { productId: item.productId, currentRecord });
};

export const togglePayLaterItemTakeawaySaga = function* (action: Action<Actions.TogglePayLaterItemTakeawayType>) {
  const { itemIndex, isTakeaway = false } = action.payload;
  const transaction = yield call(getCurrentTransaction);
  const { isOnlineOrder = false } = transaction;
  const item = transaction.items[itemIndex];
  if (isOnlineOrder && item) {
    item.isTakeaway = isTakeaway;
    const { quantity } = item;
    const previewBeepOrderParams: AddPurchasedItemToRecordParams = {
      currentRecord: transaction,
      itemIndex,
      quantity,
    };
    yield call(previewBeepOrder, previewBeepOrderParams);
    return;
  }
};

export const UpdatePurchasedItemDiscountSaga = function* (action) {
  const { itemIndex, discountOption, isDiscountEnable, discountValue } = action.payload;
  const immutableStore = yield select(selectStore);
  const includingTaxInDisplay = immutableStore.get('includingTaxInDisplay');
  const storeInfo = yield select(selectStoreInfo);
  const subscriptionPlan = storeInfo.get('subscriptionPlan');
  let currentRecord = yield call(getCurrentTransaction);
  const transactionId = get(currentRecord, 'transactionId');
  const item = currentRecord.items[itemIndex];
  if (currentRecord && currentRecord.isOnlineOrder) {
    const itemLevelDiscount = {
      type: discountOption.type,
      inputValue: discountOption.inputValue,
    };
    const previewBeepOrderParams: AddPurchasedItemToRecordParams = {
      currentRecord,
      itemIndex,
      itemLevelDiscount,
    };
    const isSuccess = yield call(previewBeepOrder, previewBeepOrderParams);
    if (isSuccess) {
      yield put(Actions.employeeApplyManualDiscount({ transactionId }));
    }
    return;
  }
  if (item) {
    item.isDiscountEnable = isDiscountEnable;
    if (discountOption) {
      item.itemLevelDiscount = discountOption;
      item.discountInputValue = discountOption.inputValue;
      item.discountType = discountOption.type;
    }
    item.discountValue = discountValue;
    const product = DAL.getProductById(item.productId);
    checkPriceBooks({ purchasedItem: item, appliedPriceBooks: currentRecord.appliedPriceBooks, product, immutableStore, subscriptionPlan });
  }

  yield call(tryApplyPromotion, currentRecord);
  try {
    currentRecord = calculateP(currentRecord, includingTaxInDisplay || false);
    conversionDiscountInLoyaltyDiscounts(currentRecord);
  } catch (exception) {
    console.log('calculate exception', exception);
    errorTransactionEvent({ action: SaleFlowAction.UpdatePurchasedItemDiscount, transactionId, reason: 'calculate exception', exception });
  }
  yield put(Actions.setTransactionSession(currentRecord));
  yield put(Actions.employeeApplyManualDiscount({ transactionId }));
  yield put(Actions.warnToRemoveUniquePromo());
};

export const updatePurchasedItemNotesSaga = function* (action) {
  const { itemIndex, notes } = action.payload;
  const currentRecord = yield call(getCurrentTransaction);
  const items = getUnNullValue(currentRecord, 'items', []);
  const item = items[itemIndex];
  if (item) {
    item.notes = notes || '';
  } else {
    warnPOSBasicEvent({
      action: POSBasicAction.UnknownError,
      reason: 'updatePurchasedItemNotesSaga: get item error',
      privateDataPayload: {
        currentRecord: JSONUtils.stringify(currentRecord),
      },
    });
  }
  yield put(Actions.setTransactionSession(currentRecord));
};

export const setItemSalespersonSaga = function* (action: Action<Actions.SetItemSalespersonType>) {
  const { itemIndex, employeeId, employeeName } = action.payload;
  if (isNil(itemIndex)) {
    return;
  }
  const currentRecord = yield call(getCurrentTransaction);
  const items = getUnNullValue(currentRecord, 'items', []);
  const item: PurchasedItemType = items[itemIndex];
  if (item) {
    item.employeeId = employeeId || null;
    item.employeeName = item.employeeId ? employeeName : null;
    yield put(Actions.setTransactionSession(currentRecord));
  }
};

export function* updateOrderLevelNotesSaga(action) {
  const {orderLevelNotes} = action.payload;
  const currentRecord = yield call(getCurrentTransaction);
  currentRecord.comment = orderLevelNotes;
  yield put(Actions.setTransactionSession(currentRecord));
}

export const deletePurchasedItemSaga = function* (action) {
  const { itemIndex, itemId } = action.payload;
  const includingTaxInDisplay = yield select(selectIncludingTaxInDisplay);
  let currentRecord = yield call(getCurrentTransaction);
  const items: any[] = get(currentRecord, 'items', []);
  let item;
  let fixItemIndex;
  if (!isEmpty(itemId)) {
    fixItemIndex = findIndex(items, item => itemId === item.id);
    if (fixItemIndex > -1) {
      item = items[fixItemIndex];
    }
  } else {
    item = get(items, itemIndex);
    fixItemIndex = itemIndex;
  }
  const { transactionId, isOnlineOrder } = currentRecord;
  if (Boolean(item)) {
    const { productId, title, quantity } = item;
    yield put(
      Actions.employeeRemoveItem({
        transactionId,
        productId,
        productName: title,
        quantity,
      })
    );
    if (isOnlineOrder) {
      const previewBeepOrderParams: AddPurchasedItemToRecordParams = {
        currentRecord,
        itemIndex: fixItemIndex,
        quantity: 0,
      };
      yield call(previewBeepOrder, previewBeepOrderParams);
      return;
    }

    currentRecord.items.splice(fixItemIndex, 1);
    if (getDisplayItemsCount(currentRecord.items) === 0) {
      // Clear all items if there is no purchased item.
      if (currentRecord.isOpen) {
        currentRecord.items = [];
      } else {
        yield put(Actions.clearTransactionSession());
        return;
      }
    }
    const enableCashback = yield select(selectEnableCashback);
    const enableLoyalty = yield select(selectEnableLoyalty);

    yield call(checkItemsDiffTax, currentRecord, enableCashback, enableLoyalty);

    yield call(tryApplyPromotion, currentRecord);

    try {
      currentRecord = calculateP(currentRecord, includingTaxInDisplay || false);
    } catch (exception) {
      console.log('deletePurchasedItemSaga exception', exception);
      errorTransactionEvent({ action: SaleFlowAction.DeletePurchasedItem, transactionId, reason: 'calculate exception', exception });
    }
    conversionDiscountInLoyaltyDiscounts(currentRecord);
    yield put(Actions.setTransactionSession(currentRecord));
    yield put(Actions.warnToRemoveUniquePromo());
    // Should remove the item first and then calculate out of stock, otherwise, the out of
    // stock information keep the same, however, it may change for ungrouped items with same
    // product id.
    yield call(requestOutOfStockForProducts, { productId, currentRecord });
  }
};

type AddFullBillDiscountToRecordType = {
  currentRecord: any;
  inputValue: number;
  type: string;
  displayFullBillDiscountValue: number;
  isDiscountEnable: boolean;
  displayFullBillDiscountEnable: boolean;
};

export const addFullBillDiscountToRecord = (params: AddFullBillDiscountToRecordType) => {
  const { currentRecord, inputValue, type, displayFullBillDiscountValue, isDiscountEnable, displayFullBillDiscountEnable } = params;
  const fullBillItem = {
    itemType: 'Discount',
    inputValue,
    type,
    // taxRate,
    product: {},
    discountInputValue: inputValue,
    discountType: type,
    displayFullBillDiscountValue,
    isDiscountEnable,
    displayFullBillDiscountEnable,
  };
  let items = currentRecord.items;
  if (items === undefined) {
    items = [];
  }
  currentRecord.items = items.concat([fullBillItem]);
};

export const giveFullBillDiscountSaga = function* (action) {
  const { itemIndex, inputValue, type, displayFullBillDiscountValue, isDiscountEnable, isReturn } = action.payload;
  const includingTaxInDisplay = yield select(selectIncludingTaxInDisplay);
  const currentRecord = yield call(getCurrentTransaction);
  const { isFullBillDiscountCanEnable, transactionId } = currentRecord;
  const immutableStore = yield select(selectStore);
  const immutableStoreInfo = yield select(selectStoreInfo);

  if (currentRecord && currentRecord.isOnlineOrder) {
    const previewBeepOrderParams: AddPurchasedItemToRecordParams = {
      currentRecord,
      return: action.payload.return,
      immutableStore,
      immutableStoreInfo,
      fullBillDiscount:
        isDiscountEnable && inputValue !== 0
          ? {
              type,
              inputValue,
            }
          : null,
    };
    const isSuccess = yield call(previewBeepOrder, previewBeepOrderParams);
    if (isSuccess) {
      yield put(Actions.employeeApplyManualDiscount({ transactionId }));
    }
    return;
  }

  if (itemIndex === -1) {
    addFullBillDiscountToRecord({
      currentRecord,
      inputValue,
      type,
      displayFullBillDiscountValue,
      isDiscountEnable,
      displayFullBillDiscountEnable: !isFullBillDiscountCanEnable || isDiscountEnable,
    });
    const enableCashback = yield select(selectEnableCashback);
    const enableLoyalty = yield select(selectEnableLoyalty);
    yield call(checkItemsDiffTax, currentRecord, enableCashback, enableLoyalty);
  } else {
    const item = currentRecord.items[itemIndex];
    item.inputValue = inputValue;
    item.type = type;
    item.discountInputValue = inputValue;
    item.discountType = type;
    item.displayFullBillDiscountValue = displayFullBillDiscountValue;
    item.isDiscountEnable = isDiscountEnable;
    if (isFullBillDiscountCanEnable) {
      item.displayFullBillDiscountEnable = isDiscountEnable;
    }
  }

  if (getDisplayItemsCount(currentRecord.items) > 0 && !isReturn) {
    yield call(tryApplyPromotion, currentRecord);
  }
  try {
    calculateP(currentRecord, includingTaxInDisplay || false);
  } catch (exception) {
    console.log('calculate exception', exception);
    errorTransactionEvent({ action: SaleFlowAction.GiveFullBillDiscount, transactionId, reason: 'calculate exception', exception });
  }
  conversionDiscountInLoyaltyDiscounts(currentRecord);
  yield put(Actions.setTransactionSession(currentRecord));
  yield put(Actions.employeeApplyManualDiscount({ transactionId }));
  yield put(Actions.warnToRemoveUniquePromo());
};

type SetServiceChargeToRecordType = {
  currentRecord: any;
  itemIndex: number;
  serviceChargeRate: number;
  serviceChargeTax: string | number;
  taxRate: number;
};

export const setServiceChargeToRecord = (params: SetServiceChargeToRecordType) => {
  const { currentRecord, itemIndex, serviceChargeRate, serviceChargeTax, taxRate } = params;
  if (itemIndex === -1) {
    const serviceChargeItem = {
      itemType: 'ServiceCharge',
      rate: serviceChargeRate,
      taxCode: serviceChargeTax,
      taxRate,
      product: {},
    };
    let items = currentRecord.items;
    if (items === undefined) {
      items = [];
    }
    currentRecord.items = items.concat([serviceChargeItem]);
  } else {
    const item = currentRecord.items[itemIndex];
    item.rate = serviceChargeRate;
    item.taxRate = taxRate;
    item.taxCode = serviceChargeTax;
  }
};

export const toggleServiceChargeSaga = function* (action) {
  const { itemIndex, serviceChargeRate, serviceChargeTax } = action.payload;

  const immutableStore = yield select(selectStore);
  const includingTaxInDisplay = immutableStore.get('includingTaxInDisplay');
  const currentRecord = yield call(getCurrentTransaction);
  const transactionId = get(currentRecord, 'transactionId');
  const taxRate = getServiceChargeTaxRateWithStore(immutableStore, serviceChargeTax);
  setServiceChargeToRecord({ currentRecord, itemIndex, serviceChargeRate, serviceChargeTax, taxRate });
  try {
    calculateP(currentRecord, includingTaxInDisplay || false);
  } catch (exception) {
    console.log('toggleServiceChargeSaga exception', exception);
    errorTransactionEvent({ action: SaleFlowAction.ToggleServiceCharge, transactionId, reason: 'toggleServiceChargeSaga exception', exception });
  }
  conversionDiscountInLoyaltyDiscounts(currentRecord);
  yield put(Actions.setTransactionSession(currentRecord));
  yield put(Actions.warnToRemoveUniquePromo());
};

export const makeSalePaymentSaga = function* (action) {
  const { isOnlineOrder } = action.payload;
  if (isOnlineOrder) {
    yield call(processOnlineOrderPayment, action);
  } else {
    yield call(makePaymentSaga, action);
  }
};

// 处理在线订单的支付
export const processOnlineOrderPayment = function* (action) {
  const { paymentId, tenderedAmount, ewalletInfo, paymentType, splitedAmount, onComplete } = action.payload;
  let currentRecord = yield call(getCurrentTransaction);
  const roundingTo = yield select(selectRountTo);
  const roundAllTransactions = yield select(selectRoundAllTransactions);
  const transactionId = get(currentRecord, 'transactionId');
  const isInSplit = yield select(selectInSplitOpenOrder);
  const isMerge = yield select(selectInMergeOpenOrder);

  let receiptNumber = get(currentRecord, 'receiptNumber');
  const { dueAmount, unRoundedAmount } = calculatePayAmount(currentRecord, paymentId, splitedAmount, roundingTo, roundAllTransactions);
  if (tenderedAmount < dueAmount) {
    onComplete.callback({ result: false, errorCode: MakePaymentErrorType.LessAmount, err: t('The tendered amount is less than due amount') });
    errorTransactionEvent({
      action: SaleFlowAction.MakeSalePayment,
      reason: 'The tendered amount is less than due amount',
      transaction: currentRecord,
      privateDataPayload: { isInSplit, isMerge, dueAmount, unRoundedAmount, tenderedAmount, splitedAmount, roundingTo },
    });
    return;
  } else {
    infoTransactionEvent({ action: SaleFlowAction.MakeSalePayment, transaction: currentRecord, privateDataPayload: { isInSplit, isMerge } });
  }
  const isPayLater = get(currentRecord, 'isPayLater');
  const isPayLaterSplittedOrder = get(currentRecord, 'isPayLaterSplittedOrder', false);
  let beepOrderLoyaltyInfo;
  let addOnFields;
  const businessName = yield select(selectBusinessName);
  const employeeId = yield select(selectEmployeeId);
  const rounding = roundingToAmount(dueAmount - unRoundedAmount, 0.001);
  const changeAmount = roundingToAmount(tenderedAmount - dueAmount, 0.001);
  const markPaidParams = {
    businessName,
    receiptNumber,
    paymentId: String(paymentId),
    paymentMethod: PaymentOptions.getPaymentNameById(paymentId),
    amount: getFixed2NumberString(dueAmount),
    rounding: getFixed2NumberString(rounding),
    employeeId,
  };
  const sequentialReceiptNumber = yield select(selectSequentialReceiptNumber);
  const birAccredited = yield select(selectBirAccredited);
  const needGenerateSeqNumber = sequentialReceiptNumber && birAccredited;
  const registerObjectId = yield select(selectRegisterObjectId);
  const registerId = yield select(selectRegisterId);
  // paymentId: if having _id, using _id, else using name.
  // paymentMethod: payment name
  let responseAction;
  const storeId = yield select(selectStoreId);
  let curInvoiceNumberStart;
  let curReceiptNumberStart;
  const genericParams: GenericOnlineOrderPayType = {};
  if (needGenerateSeqNumber) {
    const { invoiceNumberStart, receiptNumberStart }: Actions.SetSequenceType = yield call(getCurSequence);
    curInvoiceNumberStart = invoiceNumberStart;
    curReceiptNumberStart = receiptNumberStart;
    genericParams.invoiceSeqNumber = curInvoiceNumberStart;
    genericParams.sequenceNumber = curReceiptNumberStart;
  }

  // pay SplittedOnlineOrder
  if (isInSplit && isPayLaterSplittedOrder) {
    const targetOrder = yield select(selectSplitTargetOrder);
    const { modifiedTime, receiptNumber } = targetOrder;
    const tableId = get(currentRecord, 'tableId');
    // filter serviceCharge
    const items = filter(get(currentRecord, 'items'), it => it.itemType !== 'ServiceCharge').map(it => ({ id: it.id, quantity: String(it.quantity) }));

    const splittedOrders: [SplitOrderInput] = [
      {
        tableId,
        transactionId,
        items,
        payment: pick(markPaidParams, ['paymentId', 'paymentMethod', 'amount', 'rounding']),
      },
    ];
    const signatureForSplit = signatureMd5(markPaidParams, targetOrder.transactionId);
    const params: SplitOnlineOrderType = {
      splittedOrders,
      receiptNumber,
      modifiedTime,
      employeeId,
      signature: signatureForSplit,
      registerId: registerObjectId,
      registerNumber: registerId,
      storeId,
      ...genericParams,
    };

    yield put(Actions.splitOnlineOrderNoLoading(params));
    responseAction = yield take([Actions.splitOnlineOrderNoLoading.toString() + '.success', Actions.splitOnlineOrderNoLoading.toString() + '.failure']);
  } else {
    const signature = signatureMd5(markPaidParams, transactionId);
    const params: MarkPaidType = {
      ...markPaidParams,
      signature,
      isPayLater,
      registerId: registerObjectId,
      registerNumber: registerId,
      storeId,
      changeAmount: getFixed2NumberString(changeAmount),
      ...genericParams,
    };
    yield put(Actions.markPaid(params));
    responseAction = yield take([Actions.markPaid.toString() + '.success', Actions.markPaid.toString() + '.failure']);
  }
  const resp = responseAction.payload;
  const tags = [
    BeepFlowTag.Beep,
    getBeepOrderShippingTag(currentRecord),
    getPreOrderTag(currentRecord),
    isPayLater ? BeepFlowTag.PayLater : BeepFlowTag.PayFirst,
    isPayLaterSplittedOrder ? BeepFlowTag.SplitOrder : BeepFlowTag.Undefined,
  ];
  // the orderInfo be tracked in loggly
  const orderInfo = {
    orderId: currentRecord.receiptNumber,
    status: currentRecord.status,
    isPayLaterSplittedOrder,
    displayedStatus: getDisplayedStatus(currentRecord),
  };
  if (responseAction.type === Actions.markPaid.toString() + '.failure' || responseAction.type === Actions.splitOnlineOrderNoLoading.toString() + '.failure') {
    // markPaid fail
    let needReturn = true;
    const errCode = get(resp, 'errCode');
    const errMsg = get(resp, 'message');
    const statusCode = get(resp, 'status');
    // duplicate invoice sequence number
    if (errCode === '393763') {
      const logmsg = `from: ${responseAction.type} \n errorMessage:${errMsg} \n generate seq number info: curReceiptNumberStart is ${curReceiptNumberStart} AND curInvoiceNumberStart ${curInvoiceNumberStart} \n`;
      errorTransactionEvent({
        action: SaleFlowAction.GenerateOnlineInvoiceSequenceNumber,
        orderId: receiptNumber,
        transaction: currentRecord,
        reason: logmsg,
      });
    } else {
      logFailedServerRequest(BeepFlowAction.Checkout, tags, get(responseAction, 'payload', ''), WorkflowStatus.End, orderInfo);
      if (responseAction.type === Actions.markPaid.toString() + '.failure' && isValidNumber(statusCode) && Number(statusCode) >= 500) {
        yield put(Actions.getOnlineOrderDetailNoLoading({ orderId: receiptNumber }));
        const detailResponseAction = yield take([
          Actions.getOnlineOrderDetailNoLoading.toString() + '.success',
          Actions.getOnlineOrderDetailNoLoading.toString() + '.failure',
        ]);
        if (detailResponseAction.type === Actions.getOnlineOrderDetailNoLoading.toString() + '.success') {
          const onlineTransaction = detailResponseAction.payload.onlineOrder;
          if (onlineTransaction && onlineTransaction.status === 'paid') {
            // 虽然markPaid报错500，但是实际上订单已经支付成功，可以继续后面的流程
            // TODO beepOrderLoyaltyInfo如何获取？
            needReturn = false;
            const { invoiceSeqNumber, sequenceNumber, seqNumberCreatedTime, modifiedTime, status, paymentMethod } = onlineTransaction;
            addOnFields = { modifiedDate: modifiedTime, status, paymentMethod };
            infoTransactionEvent({
              action: SaleFlowAction.MakeSalePaymentWithFix,
              orderId: receiptNumber,
              transaction: onlineTransaction,
              reason: `markPaid fail with statusCode ${statusCode}, but order is paid, continue flow`,
            });
            yield put(
              Actions.updateSequence({
                receiptNumberStart: sequenceNumber,
                invoiceNumberStart: invoiceSeqNumber,
                receiptDateStart: seqNumberCreatedTime,
                isStart: false,
                transaction: currentRecord,
                minimumSequence: { invoiceNumberStart: Number(curInvoiceNumberStart | 0) + 1, receiptNumberStart: Number(curReceiptNumberStart | 0) + 1 },
                from: 'markPaid / splitPay',
              })
            );
          }
        }
      }
    }
    if (needReturn) {
      onComplete.callback({
        result: false,
        errorCode: MakePaymentErrorType.MarkpaidFailed,
        err: errMsg,
        editableTimeAfterForPayLaterOrder: get(resp, 'editableTimeAfterForPayLaterOrder', ''),
      });
      return;
    }
  } else if (responseAction.type === Actions.splitOnlineOrderNoLoading.toString() + '.success') {
    const isSuccess = get(resp, ['splitOrder', 'success'], false);
    if (!isSuccess) {
      logFailedServerRequest(BeepFlowAction.Checkout, tags, resp, WorkflowStatus.End, orderInfo);
      onComplete.callback({
        result: false,
        errorCode: MakePaymentErrorType.MarkpaidFailed,
        err: 'Payment failed, please try again',
      });
      return;
    }
    // use for updateSplitOrder After check out
    // onlineTransaction.isSplitMaster = false;
    // onlineTransaction.isPayLaterSplittedOrder = true;
    if (needGenerateSeqNumber && genericParams) {
      const { invoiceSeqNumber, sequenceNumber } = genericParams;
      yield put(
        Actions.updateSequence({
          receiptNumberStart: sequenceNumber,
          invoiceNumberStart: invoiceSeqNumber,
          // now and format like 2025-05-08T15:53:16.103+08:00
          receiptDateStart: new Date().toISOString(),
          isStart: false,
          transaction: currentRecord,
          minimumSequence: { invoiceNumberStart: Number(curInvoiceNumberStart | 0) + 1, receiptNumberStart: Number(curReceiptNumberStart | 0) + 1 },
          from: 'markPaid / splitPay',
        })
      );
    }

    const splitReceiptNumber = get(resp, 'splitOrder.splittedReceiptNumbers.0', '');
    if (splitReceiptNumber) {
      // update new receiptNumber, before is splitMasterReceiptNumber
      receiptNumber = splitReceiptNumber;

      logSucceedServerRequest(BeepFlowAction.Checkout, tags, WorkflowStatus.End, orderInfo);
    } else {
      logFailedServerRequest(BeepFlowAction.Checkout, tags, get(responseAction, 'payload', ''), WorkflowStatus.End, orderInfo);
      onComplete.callback({
        result: false,
        errorCode: MakePaymentErrorType.MarkpaidFailed,
        err: resp.message,
        editableTimeAfterForPayLaterOrder: get(resp, 'editableTimeAfterForPayLaterOrder', ''),
      });
      return;
    }
  } else {
    const isSuccess = get(resp, ['markPaid', 'success'], false);
    if (!isSuccess) {
      logFailedServerRequest(BeepFlowAction.Checkout, tags, resp, WorkflowStatus.End, orderInfo);
      onComplete.callback({
        result: false,
        errorCode: MakePaymentErrorType.MarkpaidFailed,
        err: 'Payment failed, please try again',
      });
      return;
    }
    beepOrderLoyaltyInfo = get(resp, ['markPaid', 'cashbackSummary']);
    const newOrderDetail = get(resp, ['markPaid', 'order']);

    // modifiedTime status
    if (newOrderDetail) {
      const { invoiceSeqNumber, sequenceNumber, seqNumberCreatedTime, modifiedTime, status, paymentMethod } = newOrderDetail;
      addOnFields = { modifiedDate: modifiedTime, status, paymentMethod };
      logSucceedServerRequest(BeepFlowAction.Checkout, tags, WorkflowStatus.End, newOrderDetail);
      yield put(
        Actions.updateSequence({
          receiptNumberStart: sequenceNumber,
          invoiceNumberStart: invoiceSeqNumber,
          receiptDateStart: seqNumberCreatedTime,
          isStart: false,
          transaction: currentRecord,
          minimumSequence: { invoiceNumberStart: Number(curInvoiceNumberStart | 0) + 1, receiptNumberStart: Number(curReceiptNumberStart | 0) + 1 },
          from: 'markPaid / splitPay',
        })
      );
    }
  }

  // TODO
  if (isInSplit && isPayLaterSplittedOrder) {
    // get online order detail, save db and print receipt
    // preview order is not reliable to print receipt
    yield put(Actions.getOnlineOrderDetailNoLoading({ orderId: receiptNumber }));
    const detailResponseAction = yield take([
      Actions.getOnlineOrderDetailNoLoading.toString() + '.success',
      Actions.getOnlineOrderDetailNoLoading.toString() + '.failure',
    ]);
    if (detailResponseAction.type === Actions.getOnlineOrderDetailNoLoading.toString() + '.success') {
      const onlineTransaction = detailResponseAction.payload.onlineOrder;
      const { seqNumberCreatedTime } = onlineTransaction;
      if (seqNumberCreatedTime) {
        yield put(Actions.setSequence({ receiptDateStart: seqNumberCreatedTime }));
      }

      const { isSplitMaster } = currentRecord;
      if (isSplitMaster) {
        onlineTransaction.isSplitMaster = isSplitMaster;
      }
      if (isPayLaterSplittedOrder) {
        onlineTransaction.isPayLaterSplittedOrder = isPayLaterSplittedOrder;
      }
      currentRecord = yield call(generatePayLaterTransactionSession, onlineTransaction, true);
    } else {
      logFailedServerRequest(BeepFlowAction.GetOrderDetailError, tags, WorkflowStatus.End, get(detailResponseAction, 'payload', ''), orderInfo);
      onComplete.callback({
        result: false,
        errorCode: MakePaymentErrorType.GetOrderDetailError,
        err: resp.message,
        editableTimeAfterForPayLaterOrder: get(resp, 'editableTimeAfterForPayLaterOrder', ''),
      });
      return;
    }
  }

  let mPOSTransactionId, approveReason;
  if (Boolean(ewalletInfo)) {
    // ewallet pay
    mPOSTransactionId = ewalletInfo.mPOSTransactionId;
    approveReason = ewalletInfo.approveReason;
    yield call(
      approvePaymentManually,
      Actions.employeeApprovePaymentManually({
        transactionId,
        paymentId: String(paymentId),
      })
    );
  }

  if (Boolean(addOnFields)) {
    currentRecord.modifiedDate = addOnFields.modifiedDate;
    currentRecord.status = addOnFields.status;
    currentRecord.paymentMethod = addOnFields.paymentMethod;
  }

  let approveInfo;
  if (!isUndefined(approveReason)) {
    const employeeId = yield select(selectEmployeeId);
    const now = new Date();
    approveInfo = {
      approver: employeeId,
      approveDate: now.toISOString(),
      approveReason,
    };
  }

  const payment = {
    amount: dueAmount,
    mPOSTxnId: mPOSTransactionId,
    roundedAmount: rounding,
    isDeposit: false, // mark is deposit for pre order
    type: paymentType,
    paymentMethodId: paymentId,
    cashTendered: tenderedAmount,
    manualApproveInfo: JSON.stringify(approveInfo),
    // ...paymentExtras
  };

  let payments = currentRecord.payments;

  if (!Array.isArray(payments)) {
    payments = [];
  }
  payments = payments.concat([payment]);

  currentRecord.payments = payments;
  const totalPaid = roundingSum([get(currentRecord, 'totalPaid', 0), dueAmount]);
  const roundedAmount = roundingToAmount(rounding, 0.01);
  const totalRoundedAmount = get(currentRecord, 'roundedAmount', 0) + roundedAmount;
  currentRecord.total = totalPaid;
  currentRecord.roundedAmount = totalRoundedAmount; // Rounding to 2 decimal places
  currentRecord.isCompleted = true;

  infoTransactionEvent({ action: SaleFlowAction.MakePayment, transaction: currentRecord, privateDataPayload: { isPayLaterSplittedOrder } });

  onComplete.callback({
    result: true,
    err: null,
    transaction: currentRecord,
    total: currentRecord.total,
    dueAmount: 0,
    changeAmount,
    beepOrderLoyaltyInfo,
    transactionId: currentRecord.transactionId,
    transactionType: currentRecord.transactionType,
  });
};

export const makePaymentSaga = function* (action) {
  const { paymentId, tenderedAmount, tableId, ewalletInfo, paymentType, splitedAmount, onComplete } = action.payload;
  const currentRecord = yield call(getCurrentTransaction);
  const transactionType = get(currentRecord, 'transactionType');
  const isDeposit = transactionType === TransactionFlowType.PreOrder;
  const roundingTo = yield select(selectRountTo);
  const roundAllTransactions = yield select(selectRoundAllTransactions);
  const transactionId = get(currentRecord, 'transactionId');
  const isInSplit = yield select(selectInSplitOpenOrder);
  const isMerge = yield select(selectInMergeOpenOrder);

  const { dueAmount, unRoundedAmount } = calculatePayAmount(currentRecord, paymentId, splitedAmount, roundingTo, roundAllTransactions);
  if (tenderedAmount < dueAmount) {
    onComplete.callback({ result: false, errorCode: MakePaymentErrorType.LessAmount, err: t('The tendered amount is less than due amount') });
    errorTransactionEvent({
      action: SaleFlowAction.MakeSalePayment,
      reason: 'The tendered amount is less than due amount',
      transaction: currentRecord,
      privateDataPayload: { isInSplit, isMerge, dueAmount, unRoundedAmount, tenderedAmount, splitedAmount, roundingTo },
    });
    return;
  } else {
    infoTransactionEvent({ action: SaleFlowAction.MakeSalePayment, transaction: currentRecord, privateDataPayload: { isInSplit, isMerge } });
  }
  let mPOSTransactionId, approveReason;
  if (Boolean(ewalletInfo)) {
    // ewallet pay
    mPOSTransactionId = ewalletInfo.mPOSTransactionId;
    approveReason = ewalletInfo.approveReason;
    yield call(
      approvePaymentManually,
      Actions.employeeApprovePaymentManually({
        transactionId,
        paymentId: String(paymentId),
      })
    );
  }
  const depositAmount = get(currentRecord, 'depositAmount', 0);
  const rounding = roundingToAmount(dueAmount - unRoundedAmount, 0.001);
  const isOnlineOrder = get(currentRecord, 'isOnlineOrder', false);
  // make payment
  const changeAmount = roundingToAmount(tenderedAmount - dueAmount, 0.001);
  let approveInfo;
  if (!isUndefined(approveReason)) {
    const employeeId = yield select(selectEmployeeId);
    const now = new Date();
    approveInfo = {
      approver: employeeId,
      approveDate: now.toISOString(),
      approveReason,
    };
  }

  const payment = {
    amount: dueAmount,
    mPOSTxnId: mPOSTransactionId,
    roundedAmount: isPreOrderPickUp(currentRecord) ? 0 : rounding,
    isDeposit, // mark is deposit for pre order
    type: paymentType,
    paymentMethodId: paymentId,
    cashTendered: tenderedAmount,
    manualApproveInfo: JSON.stringify(approveInfo),
    // ...paymentExtras
  };

  let payments = currentRecord.payments;

  if (!Array.isArray(payments)) {
    payments = [];
  }
  payments = payments.concat([payment]);

  currentRecord.payments = payments;
  const totalPaid = roundingSum([get(currentRecord, 'totalPaid', 0), dueAmount, isDeposit || currentRecord.isCollectPreorder ? 0 : depositAmount]);
  const roundedAmount = roundingToAmount(rounding, 0.01);
  const totalRoundedAmount = get(currentRecord, 'roundedAmount', 0) + roundedAmount;

  // under two conditions we will checkout the transaction
  // 1. totalPaid > total
  // 2. User paid in cash or Backoffice set round all transactions and unpaid rounded amount is 0.
  let receiptNumber = get(currentRecord, 'receiptNumber');

  // payLaterSplittedOrder no need to print kitchen docket
  if (
    totalPaid >= currentRecord.total ||
    // due to the rounding setting on BO
    ((paymentId === DefaultPaymentOptionType.Cash || roundAllTransactions) && roundingToAmount(currentRecord.total - totalPaid, roundingTo) === 0)
  ) {
    currentRecord.total = totalPaid; // CAUTION!: Should not call calculate after checkout!
    // for fully paid preorder, do not assign rounding amount before picked up
    if (isPreOrderPickUp(currentRecord) && roundedAmount < 0) {
      // no-op
      console.log('SaleFlow', 'roundedAmount1', roundedAmount);
      console.log('SaleFlow', 'totalRoundedAmount1', totalRoundedAmount);
    } else {
      console.log('SaleFlow', 'roundedAmount2', roundedAmount);
      console.log('SaleFlow', 'totalRoundedAmount2', totalRoundedAmount);
      currentRecord.roundedAmount = totalRoundedAmount; // Rounding to 2 decimal places
    }

    currentRecord.isCompleted = true;
    // common sale transaction have no receiptNumber here
    // refund transaction have no receiptNumber here
    // if isPreOrderPickUp, then the receiptNumber need to re-generate
    // preorder have receiptNumber already here
    if (!receiptNumber || isPreOrderPickUp(currentRecord)) {
      const isValid = yield call(generateReceiptNumber, currentRecord);
      if (!isValid) {
        errorTransactionEvent({
          action: SaleFlowAction.MakePayment,
          reason: 'Invalid Transaction Time',
          transaction: currentRecord,
          privateDataPayload: { isPayLaterSplittedOrder: false },
        });
        onComplete.callback({ result: false, errorCode: MakePaymentErrorType.MarkpaidFailed, err: t('Invalid Transaction Time') });
        return;
      } else {
        receiptNumber = get(currentRecord, 'receiptNumber');
      }
    }

    const immutableStore = yield select(selectStore);
    const autoOrderId = immutableStore.get('autoOrderId');
    const assignTableID = immutableStore.get('assignTableID', false);
    if (currentRecord.transactionType === TransactionFlowType.Sale) {
      // Add kitchen pickUpId or tableId
      if (autoOrderId) {
        let pickUpId = yield call(generateOrderId);
        const pad = '00';
        pickUpId = pad.substring(0, pad.length - String(pickUpId).length) + pickUpId;
        const immutableStoreInfo = yield select(selectStoreInfo);
        pickUpId = `${immutableStoreInfo.get('registerId')}${pickUpId}`;
        currentRecord.pickUpId = pickUpId; // comment has been refactor in 1.7
      } else if (assignTableID) {
        currentRecord.tableId = tableId; // property comment has been refactored in 1.7
      }
      const registerNumber = yield select(selectRegisterId);
      const registerId = yield select(selectRegisterObjectId);
      generateTakeawayId(currentRecord, registerNumber, registerId);
    }
  } else {
    currentRecord.totalPaid = totalPaid;
    // for not completed order, do not assign rounding amount before complete
    currentRecord.roundedAmount = totalRoundedAmount; // total rouding amount of all payment
  }
  // if is preorder, then the depositAmount is totalPaid
  if (isDeposit) {
    currentRecord.depositAmount = totalPaid;
  }
  infoTransactionEvent({ action: SaleFlowAction.MakePayment, transaction: currentRecord, privateDataPayload: { isPayLaterSplittedOrder: false } });

  const dueAmountNext = isDeposit ? 0 : currentRecord.total - totalPaid;
  if (dueAmountNext !== 0) {
    // split Payment
    yield put(Actions.setTransactionSession(currentRecord));
  }

  onComplete.callback({
    result: true,
    err: null,
    transaction: currentRecord,
    total: currentRecord.total,
    dueAmount: dueAmountNext,
    changeAmount,
    transactionId: currentRecord.transactionId,
    transactionType: currentRecord.transactionType,
  });
};

export const printKitchenOnCheckOutSaga = function* (action: Action<PrintKitchenOnCheckOutType>) {
  const { currentRecord } = action.payload;
  const noNeedPrint = get(currentRecord, 'noNeedPrint', false);
  const isCompleted = get(currentRecord, 'isCompleted', false);
  if (noNeedPrint || !isCompleted) return;
  const isPayByCash = get(currentRecord, 'isPayByCash', false);
  const isPayLater = get(currentRecord, 'isPayLater', false);
  // mark PayByCash order to Confirmed status
  if (isPayByCash) {
    // Set isReupload FALSE : Even if MarkConfirmation fails, it should be saved locally
    // because it has already been printed, and you will only need to markConfirmation again later. There is no need to repeat the printing
    yield spawn(KitchenManager.printPayByCashOnlineOrderOnCheckout, currentRecord);
  } else if (isPayLater) {
    yield spawn(KitchenManager.printPayLaterOrderOnCheckout, currentRecord);
  } else {
    // checkout local not open order
    // including  local order , pre order
    yield spawn(KitchenManager.printLocalPayFirstOrderOnCheckout, currentRecord);
  }
};

// generate takeawayId for offline takeaway order
// 1. Takeaway ID will start from TA-1001, TA-1002, TA-1003, … for Register 1
//    a. TA-2001, TA-2002, TA-2003, . for Register 2
// 2. The takeaway ID will restart upon closing and open shift
// 3. The biggest number will be 3 digits and restart upon reaching TA-1999 for Register 1
// 4. The takeaway ID should be displayed on the kitchen docket, order summary and receipt
const generateTakeawayId = (currentRecord, registerNumber, registerId) => {
  const isOnlineOrder = get(currentRecord, 'isOnlineOrder', false);
  const salesChannel = get(currentRecord, 'salesChannel');
  const tableId = get(currentRecord, 'tableId');
  const isOfflineTakeawayOrder = !isOnlineOrder && salesChannel === SalesChannelType.TAKEAWAY;
  if (!isOfflineTakeawayOrder || !isEmpty(tableId)) return;
  const currentShift = DAL.getLastShift();
  const currentShiftId = get(currentShift, 'shiftId');
  const lastTakeawayOrder: any = DAL.getLastTakeawayOrderInCurShift(registerId, currentShiftId);
  let lastTakeawayOrderNo = 0;
  if (Boolean(lastTakeawayOrder) && lastTakeawayOrder.isValid()) {
    const lastTakeawayId = lastTakeawayOrder.takeawayId;
    if (Boolean(lastTakeawayId) && typeof lastTakeawayId === 'string') {
      const lastTakeawayOrderNoStr = lastTakeawayId.slice(-3);
      if (isValidNumber(lastTakeawayOrderNoStr)) {
        lastTakeawayOrderNo = Number(lastTakeawayOrderNoStr);
      }
    }
  }
  const curTakeawayOrderNo = lastTakeawayOrderNo === 999 ? 1 : lastTakeawayOrderNo + 1;
  currentRecord.takeawayId = `TA-${registerNumber}${String(curTakeawayOrderNo).padStart(3, '0')}`;
};

const checkTransactionFormat = currentRecord => {
  const payments = get(currentRecord, 'payments', []);
  let total = get(currentRecord, 'total', 0);
  let totalPayAmount = 0;
  for (const payment of payments) {
    const paymentAmount = get(payment, 'amount', 0);
    const paymentMethodId = get(payment, 'paymentMethodId');
    const paymentMethod = get(payment, 'paymentMethod');

    totalPayAmount += paymentAmount;
    if ((paymentMethodId === null || paymentMethodId === undefined) && !paymentMethod) {
      errorTransactionEvent({ action: SaleFlowAction.OrderHasNullPaymentMethodId, reason: 'PaymentMethodId is Null', transaction: currentRecord });
    }
    if (paymentMethod === 'Voucher') {
      // generatePayLaterTransactionSession generatePayByCashTransactionSession will subtract it
      total += paymentAmount;
    }
  }

  // When the actual amount paid is greater than the total amount of the order and the amount of payment is greater than 1, it is judged to be repeated payment
  if (totalPayAmount - total > 0.01 && payments.length > 1) {
    errorTransactionEvent({ action: SaleFlowAction.OrderMultiplePay, reason: 'Multiple Pay', transaction: currentRecord });
  }
};

export const logCheckoutTransactionEvent = (transactionCheckOutEvent: any, transaction) => {
  transactionCheckOutEvent.privateDataPayload.isSavedSuccess = transactionCheckOutEvent.isSavedSuccess;
  if (transactionCheckOutEvent.isSavedSuccess === false) {
    transactionCheckOutEvent.transaction = transaction;
    errorTransactionEvent(transactionCheckOutEvent);
  } else {
    // orderId, transactionId
    transactionCheckOutEvent.orderId = get(transaction, 'receiptNumber');
    transactionCheckOutEvent.transactionId = get(transaction, 'transactionId');
    infoTransactionEvent(transactionCheckOutEvent);
  }
};

export const checkoutTransactionSaga = function* (action: Action<CheckoutTransactionType>) {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const transactionCheckOutEvent: any = { action: SaleFlowAction.CheckoutTransaction, reason: '', isSavedSuccess: false, privateDataPayload: {} };
  const { onComplete, customer, transaction } = action.payload;
  const currentRecord: TransactionType = yield call(getCurrentRecord, transaction);
  const isInSplit = yield select(selectInSplitOpenOrder);
  const isInMerge = yield select(selectInMergeOpenOrder);
  const orderOperation = getMRSOrderOperation(currentRecord, isInMerge, isInSplit);
  if (currentRecord.transactionType === TransactionFlowType.Return) {
    let hasInventoryTrackableItems = false;
    const items = currentRecord.items;
    for (const item of items) {
      if (item.trackInventory) {
        hasInventoryTrackableItems = true;
        break;
      }
    }
    if (hasInventoryTrackableItems) {
      currentRecord.returnStatus = 'Awaiting Action';
    } else {
      currentRecord.returnStatus = 'Completed';
    }
  }
  checkTransactionFormat(currentRecord);
  const isNeedMRS = yield call(checkIsNeedMRSSaga, orderOperation, currentRecord);
  const isOnlineOrder = get(currentRecord, 'isOnlineOrder', false);
  transactionCheckOutEvent.privateDataPayload = { isNeedMRS, isOnlineOrder, isInMerge, isInSplit };
  if (currentRecord.isOpen) {
    // mrs checkout
    const result: MRSError = yield call(checkoutOpenOrderSaga, currentRecord, isNeedMRS, { isInMerge, isInSplit }, transactionCheckOutEvent);
    yield put(Actions.printKitchenOnCheckOut({ currentRecord: transaction }));
    if (result.errorCode !== SuccessCode) {
      onComplete && onComplete.callback({ mrs: isNeedMRS, errorMessage: '', ...result });
      logCheckoutTransactionEvent(transactionCheckOutEvent, currentRecord);
      return;
    }
  } else {
    conversionDiscountInLoyaltyDiscounts(currentRecord);
    const { transactionId } = currentRecord;
    if (isPreOrderPickUp(currentRecord)) {
      transactionCheckOutEvent.privateDataPayload.saveAction = 'updateTransaction';
      const saveSuccess = DAL.updateTransaction(transactionId, { ...currentRecord });
      transactionCheckOutEvent.isSavedSuccess = saveSuccess;
      yield put(Actions.printKitchenOnCheckOut({ currentRecord: transaction }));
      if (saveSuccess) {
        yield spawn(pushLocalTransactionsSaga, [transactionId]);
      }
    } else {
      if (!isNeedMRS) {
        transactionCheckOutEvent.privateDataPayload.saveAction = 'saveTransaction';
        const saveSuccess = DAL.saveTransaction(currentRecord);
        transactionCheckOutEvent.isSavedSuccess = saveSuccess;
        yield put(Actions.printKitchenOnCheckOut({ currentRecord: transaction }));
        if (saveSuccess) {
          yield spawn(pushLocalTransactionsSaga, [transactionId]);
        }
      } else {
        // mrs refund
        currentRecord.mrs = true;
        transactionCheckOutEvent.privateDataPayload.saveAction = 'requestProposalAction';
        const result = yield call(
          requestProposalActionSaga,
          requestProposalAction({
            data: [{ operationType: Actions.OperationType.UPDATE, transaction: currentRecord }],
            operation: OrderOperationEnum.Refund,
            showLoading: false,
          })
        );
        const saveSuccess = result.errorCode === SuccessCode;
        transactionCheckOutEvent.isSavedSuccess = saveSuccess;
        yield put(Actions.printKitchenOnCheckOut({ currentRecord: transaction }));
        if (!saveSuccess) {
          onComplete && onComplete.callback({ mrs: isNeedMRS, ...result });
          logCheckoutTransactionEvent(transactionCheckOutEvent, currentRecord);
          return;
        }
      }
    }
  }

  logCheckoutTransactionEvent(transactionCheckOutEvent, currentRecord);
  // yield call(checkoutSequenceSaga, currentRecord);

  if (!Boolean(isOnlineOrder)) {
    // Try to sync transaction data
    if (customer) {
      const onFinally = payload => {
        onComplete && onComplete.callback({ mrs: isNeedMRS, ...SuccessMessage(), ...payload });
      };

      yield put(Actions.syncTransactionsAction({ onComplete: { callback: onFinally }, transactionId: currentRecord.transactionId }));
    } else {
      onComplete && onComplete.callback({ mrs: isNeedMRS, ...SuccessMessage() });
      yield put(Actions.syncTransactionsAction({ transactionId: currentRecord.transactionId }));
    }
  } else {
    onComplete && onComplete.callback({ mrs: isNeedMRS, ...SuccessMessage() });
  }
};

export const getMRSOrderOperation = function (currentRecord: TransactionType, isInMerge = false, isInSplit = false) {
  let orderOperation = OrderOperationEnum.CheckOut;
  if (currentRecord.transactionType === TransactionFlowType.Return) {
    orderOperation = OrderOperationEnum.Refund;
  } else if (isInSplit) {
    orderOperation = OrderOperationEnum.SplitAndCheckOut;
  } else if (isInMerge) {
    orderOperation = OrderOperationEnum.MergeAndCheckOut;
  }
  return orderOperation;
};

function* checkoutMergeAndPayWithMRS(currentRecord: TransactionType, transactionCheckOutEvent) {
  // mrs checkout
  const proposeData: ProposeDataType[] = [];
  // need remove first
  MRSConfig.removeTransactionFromBlackList(currentRecord.transactionId);
  proposeData.push({
    // @ts-ignore
    transaction: {
      ...currentRecord,
      isOpen: false,
      mrs: true,
      loyaltyDiscounts: currentRecord.loyaltyDiscounts ?? [],
      customerId: currentRecord.customerId ?? null,
    },
    operationType: Actions.OperationType.UPDATE,
  });
  const mergedBranchOrderIds: string[] = get(currentRecord, 'mergedBranchOrderIds', []);
  if (mergedBranchOrderIds.length > 0) {
    for (const branchOpenOrderId of mergedBranchOrderIds) {
      // delete other sub orders
      if (branchOpenOrderId === currentRecord.transactionId) {
        continue;
      }
      const branchTransaction = DAL.getTransactionById(branchOpenOrderId);
      if (branchTransaction) {
        proposeData.push({
          // @ts-ignore
          transaction: { transactionId: branchOpenOrderId, isCompleted: true, tableId: currentRecord.tableId, pickUpId: currentRecord.pickUpId },
          operationType: Actions.OperationType.DELETE,
        });
      }
    }
  }
  if (proposeData.length > 0) {
    const mergeResult: MRSError = yield call(
      requestProposalActionSaga,
      requestProposalAction({
        data: proposeData,
        operation: OrderOperationEnum.MergeAndCheckOut,
        showLoading: false,
      })
    );
    if (mergeResult.errorCode === SuccessCode) {
      yield put(Actions.completeMergeSplitOpenOrder());
    }
    transactionCheckOutEvent.privateDataPayload.saveAction = 'checkoutMergeAndPayWithMRS';
    transactionCheckOutEvent.isSavedSuccess = mergeResult.errorCode === SuccessCode;
    return mergeResult;
  }
  return SuccessMessage();
}

function* checkoutMergeAndPayLocal(currentRecord: TransactionType, transactionCheckOutEvent) {
  const transaction = { ...currentRecord, isOpen: false };

  let mergeSavedSuccess = true;
  yield call(updateCookingStatusFromItems, currentRecord);
  const saveSuccess = DAL.updateOpenOrder(transaction);
  mergeSavedSuccess = mergeSavedSuccess && saveSuccess;
  const mergedBranchOrderIds: string[] = get(currentRecord, 'mergedBranchOrderIds', []);
  const isKdsPaired = yield select(selectIsKdsPaired);
  const mergeFromTransactionList = isKdsPaired ? DAL.getJsTransactionByIds(mergedBranchOrderIds.filter(it => it !== currentRecord.transactionId)) : [];
  if (mergedBranchOrderIds.length > 0) {
    for (const branchOpenOrderId of mergedBranchOrderIds) {
      // delete other sub orders
      if (branchOpenOrderId === currentRecord.transactionId) {
        continue;
      }
      const notification = getPrinterNotification(branchOpenOrderId);
      const noFailedJob = !notification || (isEmpty(notification.jobs) && notification.isKitchenPrinted);
      if (noFailedJob) {
        const saveSuccess = yield call(DAL.deleteTransactionById, branchOpenOrderId);
        mergeSavedSuccess = mergeSavedSuccess && saveSuccess;
      } else {
        const saveSuccess = DAL.updateTransaction(branchOpenOrderId, { isDeleted: true, tableId: currentRecord.tableId, pickUpId: currentRecord.pickUpId });
        mergeSavedSuccess = mergeSavedSuccess && saveSuccess;
      }
    }
  }
  if (mergeSavedSuccess && mergeFromTransactionList.length > 0) {
    yield spawn(pushLocalOpenOrdersSaga, [currentRecord.transactionId], {
      mergeToId: currentRecord.transactionId,
      mergeFromTransactionList,
      mergeAndPay: true,
    });
  }
  yield put(Actions.completeMergeSplitOpenOrder());
  transactionCheckOutEvent.privateDataPayload.saveAction = 'checkoutMergeAndPay';
  transactionCheckOutEvent.isSavedSuccess = mergeSavedSuccess;
}

function* checkoutMergeAndPay(currentRecord: TransactionType, isNeedMRS: boolean, transactionCheckOutEvent) {
  let result = SuccessMessage();
  if (!isNeedMRS) {
    yield call(checkoutMergeAndPayLocal, currentRecord, transactionCheckOutEvent);
  } else {
    result = yield call(checkoutMergeAndPayWithMRS, currentRecord, transactionCheckOutEvent);
  }
  transactionCheckOutEvent;
  return result;
}

function* checkoutSplitAndPayOnline(currentRecord: TransactionType, transactionCheckOutEvent) {
  const transaction = { ...currentRecord, isOpen: false };
  const saveSuccess = DAL.updateOpenOrder(transaction);
  transactionCheckOutEvent.privateDataPayload.saveAction = 'checkoutSplitAndPayOnline';
  transactionCheckOutEvent.isSavedSuccess = saveSuccess;
  const isSplitMaster = get(currentRecord, 'isSplitMaster', false);
  const isPayLater = get(currentRecord, 'isPayLater', false);
  if (isPayLater) {
    yield call(updateSplitOnlineOrderAfterCheckOutSaga, Actions.updateSplitOnlineOrderAfterCheckOut({ isSplitMaster }));
  }
}

function* checkoutSplitAndPayLocal(currentRecord: TransactionType, transactionCheckOutEvent) {
  const transaction = { ...currentRecord, isOpen: false } as Actions.KdsTransactionType;
  let saveSuccess = DAL.updateOpenOrder(transaction);
  const isSplitMaster = get(currentRecord, 'isSplitMaster', false);
  // if the checkouted order is split master order, then we need save the other split order
  const employeeId = yield select(selectEmployeeId);
  const splitTargetTransaction = yield select(selectSplitTargetOrder);
  const splitedTransaction = yield select(selectSplitedOrder);
  const currentTransaction = isSplitMaster ? splitedTransaction : splitTargetTransaction;

  let putAction: Action<unknown>;
  if (isSplitMaster) {
    // if the checkouted order is split master order,
    if (getDisplayItemsCount(get(currentTransaction, 'items', [])) > 0) {
      // if the splited order is not empty, then we need to save the splited order to local DB and make it to spilt master
      putAction = Actions.exchangeSplitOrder();
      currentTransaction.isSplitMaster = true;
      const updatedTx = getSplitSaveOrder(currentTransaction, employeeId, false);
      if (updatedTx) {
        const saved = DAL.saveTransaction(updatedTx);
        saveSuccess = saveSuccess && saved;
        if (saveSuccess) {
          // pay splitFrom transaction directly, it only reduces the quantity
          yield spawn(pushLocalOpenOrdersSaga, [splitTargetTransaction.transactionId], {
            splitFromId: splitTargetTransaction.transactionId,
            splitToId: splitedTransaction.transactionId,
            splitAndPay: true,
          });
        }
      }
    } else {
      // but if splited order is empty, then we need to finish the split
      putAction = Actions.completeMergeSplitOpenOrder();
      // split item1 -> checkout master order -> checkout the new splited order
      if (saveSuccess && transaction.splitFromId) {
        yield spawn(pushLocalOpenOrdersSaga, [transaction.splitFromId], {
          splitAndPay: true,
          splitToId: transaction.transactionId,
          splitFromId: transaction.splitFromId,
        });
      }
    }
  } else {
    // if the checkouted order is splited order,
    // then we need to update the master order to local DB
    if (getDisplayItemsCount(get(currentTransaction, 'items', [])) > 0) {
      putAction = Actions.clearSplitedOrder();
      const updatedTx = getSplitSaveOrder(currentTransaction, employeeId, true);
      if (updatedTx) {
        const saved = DAL.updateOpenOrder(updatedTx);
        saveSuccess = saveSuccess && saved;
        if (saveSuccess) {
          // split new order from master
          yield spawn(pushLocalOpenOrdersSaga, [splitTargetTransaction.transactionId], {
            splitToId: splitedTransaction.transactionId,
            splitAndPay: true,
            splitFromId: splitTargetTransaction.transactionId,
          });
        }
      }
    } else {
      putAction = Actions.completeMergeSplitOpenOrder();
      const transactionId = get(currentTransaction, 'transactionId');
      if (transactionId) {
        // Print kitchen order
        const notification = DAL.getBeepNotificationById(transactionId);
        if (isEmpty(get(notification, 'jobs', []))) {
          const saved = DAL.deleteTransactionById(transactionId);
          saveSuccess = saveSuccess && saved;
        } else {
          const saved = DAL.updateTransaction(transactionId, { isDeleted: true });
          saveSuccess = saveSuccess && saved;
        }
      }
    }
  }

  if (putAction) {
    yield put(putAction);
  }
  transactionCheckOutEvent.privateDataPayload.saveAction = 'checkoutSplitAndPayLocal';
  transactionCheckOutEvent.isSavedSuccess = saveSuccess;
}

function* checkoutSplitAndPayWithMRS(currentRecord: TransactionType, transactionCheckOutEvent) {
  const proposeData: ProposeDataType[] = [];
  // need remove first
  MRSConfig.removeTransactionFromBlackList(currentRecord.transactionId);
  proposeData.push({
    // @ts-ignore
    transaction: {
      ...currentRecord,
      isOpen: false,
      mrs: true,
      loyaltyDiscounts: currentRecord.loyaltyDiscounts ?? [],
      customerId: currentRecord.customerId ?? null,
    },
    operationType: Actions.OperationType.UPDATE,
  });
  const isSplitMaster = get(currentRecord, 'isSplitMaster', false);
  let updateResult = SuccessMessage();

  // if the checkouted order is split master order, then we need save the other split order
  const employeeId = yield select(selectEmployeeId);
  const splitTargetTransaction = yield select(selectSplitTargetOrder);
  const splitedTransaction = yield select(selectSplitedOrder);
  const currentTransaction = isSplitMaster ? splitedTransaction : splitTargetTransaction;

  let putAction: Action<unknown>;

  const isLimit: SagaReturnType<typeof checkLimitBeforeExecuteSaga> = yield call(
    checkLimitBeforeExecuteSaga,
    Actions.checkLimitBeforeExecute({
      transaction: currentTransaction,
      orderOperation: OrderOperationEnum.SplitAndCheckOut,
    })
  );
  if (isLimit.errorCode !== SuccessCode) {
    onMRSInterceptor(isLimit);
    return isLimit;
  }

  if (isSplitMaster) {
    // if the checkouted order is split master order,
    if (getDisplayItemsCount(get(currentTransaction, 'items', [])) > 0) {
      putAction = Actions.exchangeSplitOrder();
      // if the splited order is not empty, then we need to save the splited order to local DB and make it to spilt master
      currentTransaction.isSplitMaster = true;
      const updatedTx = getSplitSaveOrder(currentTransaction, employeeId, false);
      if (updatedTx) {
        updatedTx.mrs = true;
        proposeData.push({
          operationType: Actions.OperationType.INSERT,
          transaction: { ...updatedTx, loyaltyDiscounts: updatedTx.loyaltyDiscounts ?? [], customerId: updatedTx.customerId ?? null },
        });
      }
    } else {
      // but if splited order is empty, then we need to finish the split
      putAction = Actions.completeMergeSplitOpenOrder();
    }
  } else {
    // if the checkouted order is splited order,
    // then we need to update the master order to local DB
    if (getDisplayItemsCount(get(currentTransaction, 'items', [])) > 0) {
      putAction = Actions.clearSplitedOrder();
      const updatedTx = getSplitSaveOrder(currentTransaction, employeeId, true);
      if (updatedTx) {
        updatedTx.mrs = true;
        proposeData.push({
          operationType: Actions.OperationType.UPDATE,
          transaction: { ...updatedTx, loyaltyDiscounts: updatedTx.loyaltyDiscounts ?? [], customerId: updatedTx.customerId ?? null },
        });
      }
    } else {
      putAction = Actions.completeMergeSplitOpenOrder();
      const transactionId = get(currentTransaction, 'transactionId');
      if (transactionId) {
        proposeData.push({ operationType: Actions.OperationType.DELETE, transaction: currentTransaction });
      }
    }
  }
  if (proposeData.length > 0) {
    updateResult = yield call(
      requestProposalActionSaga,
      requestProposalAction({
        data: proposeData,
        operation: OrderOperationEnum.SplitAndCheckOut,
        showLoading: false,
      })
    );
  }
  if (updateResult.errorCode !== SuccessCode) {
    onMRSInterceptor(updateResult);
  } else if (putAction) {
    yield put(putAction);
  }
  transactionCheckOutEvent.privateDataPayload.saveAction = 'checkoutSplitAndPayWithMRS';
  transactionCheckOutEvent.isSavedSuccess = updateResult.errorCode === SuccessCode;

  return updateResult;
}

function* checkoutSplitAndPay(currentRecord: TransactionType, isNeedMRS: boolean, transactionCheckOutEvent) {
  const isPayLater = get(currentRecord, 'isPayLater', false);
  let result = SuccessMessage();
  if (isPayLater) {
    yield call(checkoutSplitAndPayOnline, currentRecord, transactionCheckOutEvent);
    return result;
  }
  if (!currentRecord.isOnlineOrder) {
    if (isNeedMRS) {
      result = yield call(checkoutSplitAndPayWithMRS, currentRecord, transactionCheckOutEvent);
    } else {
      yield call(checkoutSplitAndPayLocal, currentRecord, transactionCheckOutEvent);
    }
  }
  return result;
}

export function* checkoutOfflineOpenOrderLocal(currentRecord: TransactionType, transactionCheckOutEvent) {
  const record = { ...currentRecord, isOpen: false };
  yield call(updateCookingStatusFromItems, record as TransactionType);
  const saveSuccess = DAL.updateOpenOrder(record);
  if (saveSuccess) {
    yield spawn(pushLocalOpenOrdersSaga, [record.transactionId], { checkoutId: record.transactionId });
  }
  transactionCheckOutEvent.privateDataPayload.saveAction = 'checkoutLocalOpenOrder';
  transactionCheckOutEvent.isSavedSuccess = saveSuccess;
  return SuccessMessage();
}

export function* checkoutLocalOpenOrderWithMRS(currentRecord: TransactionType, transactionCheckOutEvent) {
  // need remove first
  MRSConfig.removeTransactionFromBlackList(currentRecord.transactionId);
  const updateResult = yield call(
    requestProposalActionSaga,
    requestProposalAction({
      data: [
        {
          // @ts-ignore
          transaction: {
            ...currentRecord,
            isOpen: false,
            mrs: true,
            loyaltyDiscounts: currentRecord.loyaltyDiscounts ?? [],
            customerId: currentRecord.customerId ?? null,
          },
          operationType: Actions.OperationType.UPDATE,
        },
      ],
      operation: OrderOperationEnum.CheckOut,
      showLoading: false,
    })
  );

  transactionCheckOutEvent.privateDataPayload.saveAction = 'checkoutLocalOpenOrderWithMRS';
  transactionCheckOutEvent.isSavedSuccess = updateResult.errorCode === SuccessCode;
  return updateResult;
}
function* checkoutLocalOpenOrder(currentRecord: TransactionType, isNeedMRS: boolean, transactionCheckOutEvent) {
  let updateResult = SuccessMessage();
  if (!currentRecord.isOnlineOrder) {
    let needPrintItems: any[] = [];

    const transaction = yield call(getCurrentTransaction);
    const items = map(transaction.items, item => {
      return { ...item, options: stringifyItemOptions(item.options) };
    });
    transaction.items = items;
    // 因为currentRecord会移除priceDiff，导致options不一致。所以要在数据库更新完成前获取itemsDiff
    const { itemsDiff } = calculateItemsToKitchenOrder(transaction);
    needPrintItems = itemsDiff;

    if (!isNeedMRS) {
      updateResult = yield call(checkoutOfflineOpenOrderLocal, currentRecord, transactionCheckOutEvent);
    } else {
      updateResult = yield call(checkoutLocalOpenOrderWithMRS, currentRecord, transactionCheckOutEvent);
    }
    if (updateResult.errorCode === SuccessCode && !isEmpty(needPrintItems)) {
      // local open order print unprinted items after checkout
      yield put(
        Actions.printKitchenDocket({
          transaction: currentRecord,
          tags: ['checkoutOpenOrder'],
          printItems: needPrintItems,
          eventName: KitchenEvent.printOpenOrder,
        })
      );
    }
  }
  return updateResult;
}

function* checkoutOnlineOpenOrder(currentRecord: TransactionType) {
  // checkout online order
  const transaction = { ...currentRecord, isOpen: false };
  const isPayByCashOrder = payByCashNeedPushKds(transaction);
  const dbTrans = DAL.getTransactionById(transaction.transactionId);
  yield call(restoreKDSInfo, dbTrans, transaction);

  let modifiedTransaction = transaction;
  // @ts-ignore
  yield call(updateCookingStatusFromItems, transaction, dbTrans);
  if (modifiedTransaction.subOrders === null) {
    modifiedTransaction.subOrders = [];
  }

  // @ts-ignore
  modifiedTransaction = first(yield call(addKitchenStationToTrans, [transaction]));

  const saveSuccess = DAL.updateOpenOrder(modifiedTransaction);
  if (saveSuccess && isPayByCashOrder) {
    yield spawn(pushLocalTransactionsSaga, [transaction.transactionId]);
  }
  return saveSuccess;
}

export const checkoutOpenOrderSaga = function* (
  currentRecord: TransactionType,
  isNeedMRS: boolean,
  { isInMerge = false, isInSplit = false },
  transactionCheckOutEvent: any = {}
) {
  let result = SuccessMessage();
  if (currentRecord.isOpen) {
    if (isInMerge) {
      result = yield call(checkoutMergeAndPay, currentRecord, isNeedMRS, transactionCheckOutEvent);
    } else if (isInSplit) {
      result = yield call(checkoutSplitAndPay, currentRecord, isNeedMRS, transactionCheckOutEvent);
    } else if (!currentRecord.isOnlineOrder) {
      result = yield call(checkoutLocalOpenOrder, currentRecord, isNeedMRS, transactionCheckOutEvent);
    } else {
      const isSavedSuccess = yield call(checkoutOnlineOpenOrder, currentRecord);
      transactionCheckOutEvent.privateDataPayload.saveAction = 'checkoutOnlineOrder';
      transactionCheckOutEvent.isSavedSuccess = isSavedSuccess;
    }
  }
  return result;
};

export const checkoutSequenceSaga = function* (currentRecord: TransactionType) {
  const immutableStoreInfo = yield select(selectStoreInfo);
  const sequentialReceiptNumber = immutableStoreInfo.get('sequentialReceiptNumber');
  const immutableSequence = yield select(selectSequence);
  const { receiptNumberStart: currentReceiptNumberStart, receiptDateStart: currentDateStart }: Actions.SetSequenceType = immutableSequence.toJS();
  if (sequentialReceiptNumber && currentReceiptNumberStart && currentDateStart) {
    const { sequenceNumber, createdDate, invoiceSeqNumber } = currentRecord;
    if (Boolean(sequenceNumber) && Boolean(createdDate)) {
      if (invoiceSeqNumber) {
        yield put(
          Actions.setSequence({
            receiptNumberStart: Number(sequenceNumber) + 1,
            receiptDateStart: createdDate,
            invoiceSeqNumber: Number(invoiceSeqNumber) + 1,
            sourceForLog: 'checkoutSequenceSaga has invoiceSeqNumber',
          })
        );
      } else {
        yield put(
          Actions.setSequence({
            receiptNumberStart: Number(sequenceNumber) + 1,
            receiptDateStart: createdDate,
            sourceForLog: 'checkoutSequenceSaga',
          })
        );
      }
    }
  }
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const cancelNextPaymentSaga = function* (action) {
  const currentRecord = yield call(getCurrentTransaction);
  currentRecord.totalPaid = 0;
  const payments = get(currentRecord, 'payments');
  const receiptNumber = get(currentRecord, 'receiptNumber');
  const transactionId = get(currentRecord, 'transactionId');
  yield put(Actions.voidEWalPaymentBegin({ payments, receiptNumber, transactionId }));
  currentRecord.payments = [];
  currentRecord.roundedAmount = 0;
  infoTransactionEvent({ action: SaleFlowAction.CancelNextPayment, transaction: currentRecord });
  yield put(Actions.setTransactionSession(currentRecord));
};

export const clearPromotionSaga = function* () {
  const currentRecord = yield call(getCurrentTransaction);
  if (getDisplayItemsCount(currentRecord.items) > 0) {
    yield call(clearPromotions, currentRecord);
    const includingTaxInDisplay = yield select(selectIncludingTaxInDisplay);
    try {
      calculateP(currentRecord, includingTaxInDisplay || false);
    } catch (exception) {
      console.log('manualReturnSaga exception', exception);
      errorTransactionEvent({ action: SaleFlowAction.ClearPromotion, reason: 'calculate exception', exception, transaction: currentRecord });
    }
  }
  conversionDiscountInLoyaltyDiscounts(currentRecord);
  yield put(Actions.setTransactionSession(currentRecord));
};

export const checkStaticsLocalTime = () => {
  let isLocalTimeAvailable = true;
  let message = '';
  let title = '';
  const type = SystemSettingsCheckingType.StaticsLocalTime;
  const staticsVerifyTime = '2024-10-01 00:00:00';
  const current = moment();
  // const mockCurDate = moment().subtract(30, 'days');
  if (current.isSameOrBefore(moment(staticsVerifyTime))) {
    isLocalTimeAvailable = false;
    title = 'Incorrect System Date';
    message = 'Device system date is incorrect. This will cause transactions to be recorded with the wrong date and time.';
  }
  return { isLocalTimeAvailable, title, message, type };
};

export function* checkPlaceOrderAvailableSaga(action) {
  const { onComplete, callLocation } = action.payload;
  const isOneZReadingPerDayEnabled = yield select(selectIsOneZReadingPerDayEnabled);
  const isNetConnected: boolean = yield select(selectIsNetConnected);
  let isPlaceOrderAvailable = true;
  let message = '';
  let title = '';
  let type;

  const checkStaticsLocalTimeResult = checkStaticsLocalTime();
  if (!checkStaticsLocalTimeResult.isLocalTimeAvailable) {
    isPlaceOrderAvailable = false;
    message = checkStaticsLocalTimeResult.message;
    title = checkStaticsLocalTimeResult.title;
    type = checkStaticsLocalTimeResult.type;
  } else if (isOneZReadingPerDayEnabled && isNetConnected) {
    // check store timezone VS local timezone
    const storeTimezone = yield select(selectTimezone);
    if (!isEmpty(storeTimezone)) {
      const storeTimezoneGMT = momentTimezone().tz(storeTimezone).format('Z');
      const localTimezoneGMT = momentTimezone().tz(TIMEZONE).format('Z');
      if (storeTimezoneGMT !== localTimezoneGMT) {
        isPlaceOrderAvailable = false;
        type = SystemSettingsCheckingType.LocalTimezoneGMT;
        title = 'Incorrect System Timezone';
        message = `The system timezone is currently set as GMT ${localTimezoneGMT}, however your account setting timezone is set as GMT ${storeTimezoneGMT}. Please change the system timezone to GMT ${storeTimezoneGMT}`;
      }
    } else {
      isPlaceOrderAvailable = false;
      type = SystemSettingsCheckingType.LocalTimezoneGMT;
      title = 'Incorrect System Timezone';
      message = 'However your account setting timezone is empty now. ';
    }
    // check server time VS local time
    if (isPlaceOrderAvailable) {
      yield put(Actions.getServerTime({}));
      const responseAction = yield take([Actions.getServerTime.toString() + '.success', Actions.getServerTime.toString() + '.failure']);
      const serverTime = get(responseAction, ['payload', 'serverTime']);
      if (!isEmpty(serverTime)) {
        const diff = moment(serverTime).diff(moment(), 'minutes');
        if (Math.abs(diff) >= 2) {
          isPlaceOrderAvailable = false;
          type = SystemSettingsCheckingType.AccuratelyLocalTime;
          title = 'Incorrect System Date';
          message = 'This is to prevent merchant from creating transactions using past or future time which may affect the Zreading data accuracy.';
        }
      }
    }
  }
  // add log to kibana
  if (!isPlaceOrderAvailable) {
    const privateDataPayload: any = { type, message, locaTime: moment().toDate() };
    const currentShift = DAL.getLastShift();
    switch (callLocation) {
      case Actions.CheckPlaceOrderAvailableLocation.Checkout:
        {
          const currentRecord = yield call(getCurrentTransaction);
          if (currentShift) currentRecord.shiftId = currentShift.shiftId;
          errorTransactionEvent({
            action: SaleFlowAction.CheckPlaceOrderAvailable,
            reason: title,
            transaction: currentRecord,
            privateDataPayload,
          });
        }
        break;
      case Actions.CheckPlaceOrderAvailableLocation.Shift:
        {
          const shiftOpenStatus = yield select(selectShiftOpenStatus);
          if (shiftOpenStatus && currentShift) {
            privateDataPayload.shift = JSONUtils.stringify(currentShift);
            privateDataPayload.shiftId = currentShift.shiftId;
          }
          logShiftEvent({
            event: ShiftFlowAction.CheckAvailable,
            level: LoggingLevel.Error,
            reason: title,
            privateDataPayload,
          });
        }
        break;

      default:
        break;
    }
  }
  onComplete && onComplete.callback({ isPlaceOrderAvailable, title, message, type });
}

export const checTrialOrderLimit = (trialOrderLimitEnabled, freeTrial, registerObjectId, transactionId?: string) => {
  if (trialOrderLimitEnabled && freeTrial) {
    const validTrxIds = DAL.getTrialAvliableTrxCount(registerObjectId);
    if (validTrxIds.length >= 20 && (!transactionId || (transactionId && !validTrxIds.includes(transactionId)))) {
      navigate({
        routeName: 'ModalInfo',
        params: {
          title: 'You have reached the account limit',
          info: 'You are not allowed to create any orders or proceed with transactions. \n\nTo fix this, please activate your account in the BackOffice.',
          isShowTitle: true,
          materialIcons: { name: 'warning', size: 24, color: '#FFA500' },
          textAligh: 'center',
          okText: t('Close'),
          onSubmitHandler: () => null,
        },
      });
      return false;
    }
  }
  return true;
};

function* transactionSagas() {
  yield takeLatest(Actions.addPurchasedItem.toString(), addPurchasedItemSaga);
  yield takeLatest(Actions.updatePurchasedItem.toString(), updatePurchasedItemSaga);
  yield takeLatest(Actions.updatePurchasedItemNotes.toString(), updatePurchasedItemNotesSaga);
  yield takeLatest(Actions.updatePurchasedItemQuantity.toString(), updatePurchasedItemQuantitySaga);
  yield takeLatest(Actions.updatePurchasedItemDiscount.toString(), UpdatePurchasedItemDiscountSaga);
  yield takeLatest(Actions.deletePurchasedItem.toString(), deletePurchasedItemSaga);
  yield takeLatest(Actions.giveFullBillDiscount.toString(), giveFullBillDiscountSaga);
  yield takeLatest(Actions.toggleServiceCharge.toString(), toggleServiceChargeSaga);
  yield takeLatest(Actions.setItemSalesperson.toString(), setItemSalespersonSaga);
  yield takeLatest(Actions.makeSalePayment.toString(), makeSalePaymentSaga);
  yield takeLatest(Actions.makePayment.toString(), makePaymentSaga);
  yield takeLatest(Actions.clearPromotion.toString(), clearPromotionSaga);
  yield takeLatest(Actions.checkoutTransaction.toString(), checkoutTransactionSaga);
  yield takeLatest(Actions.cancelNextPayment.toString(), cancelNextPaymentSaga);
  yield takeEvery(Actions.getStocksForProduct.toString() + '.success', calculateOutOfStockForProductsSaga);
  yield takeEvery(Actions.getStocksForProduct.toString() + '.failure', calculateOutOfStockForProductsSaga);
  yield takeEvery(Actions.getStocksForProduct.toString(), calculateOutOfStockForProductsSaga);
  yield takeLatest(Actions.updateOutOfStockStatus.toString(), updateOutOfStockStatus);
  yield takeLatest(Actions.updateOrderLevelNotes.toString(), updateOrderLevelNotesSaga);
  yield takeLatest(Actions.togglePayLaterItemTakeaway.toString(), togglePayLaterItemTakeawaySaga);
  yield takeLatest(Actions.checkPlaceOrderAvailable.toString(), checkPlaceOrderAvailableSaga);
  yield takeLatest(Actions.printKitchenOnCheckOut.toString(), printKitchenOnCheckOutSaga);
}

export default fork(transactionSagas);
