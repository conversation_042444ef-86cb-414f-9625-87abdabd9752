# AI Services

This directory contains external service integrations for the POS application.

## Gemini AI Service

The `geminiAIService.ts` provides AI-powered chat assistance for the POS system using Google's Gemini AI.

### Features

- **Smart Action Execution**: Executes clear user commands directly without requiring button interactions
- **Contextual Clarification**: Shows action buttons only when user intent is ambiguous
- **Real-time System Integration**: Access to live printer, network, POS, and store information
- **Agent Functions**: Direct access to system state through Redux selectors
- **Natural Language Processing**: Understands user intent and provides appropriate responses

### Configuration

The service requires a Google Gemini API key:

```env
GEMINI_API_KEY=your_google_gemini_api_key_here
```

**Important**: The API key is required. The service will throw an error if no API key is provided.

### How It Works

#### Direct Action Execution
When user intent is clear and specific:
- AI calls agent functions directly to retrieve system information
- AI executes navigation, status checks, or other actions immediately
- Response includes the action result without requiring additional user interaction

Examples:
- "Check printer status" → Immediately returns printer information
- "Navigate to settings" → Directly triggers navigation to settings
- "Show network status" → Returns current network information

#### Clarification Mode
When user intent is ambiguous or multiple options are available:
- AI generates contextual action buttons for clarification
- Buttons send specific commands back to the AI for processing
- Ensures consistent AI processing and response formatting

Examples:
- "Navigate" → Shows buttons for Products, Transactions, Customers, Settings
- "Check system" → Shows buttons for Printers, Network, POS Status
- "Help" → Shows buttons for different help topics

### Agent Functions

The service includes agent functions that provide real-time system information:

#### `getPrinterInfo()`
Returns current printer status including:
- Total number of printers
- Online/offline printer counts
- Error states and details
- Individual printer information

#### `getNetworkInfo()`
Returns network connectivity information including:
- Connection status
- Network type (WiFi, cellular, etc.)
- IP address and WiFi SSID
- Network configuration details

#### `getPOSInfo()`
Returns POS system information including:
- Register ID and business name
- Sequential receipt numbers
- Store configuration details

#### `getStoreDetails()`
Returns comprehensive store information including:
- Store name, currency, and country
- Address and contact information
- Tax configuration (GST, SST, VAT)
- Store settings and features

### Action Button System

Action buttons are generated contextually and include:

#### Button Types
- **Navigation**: For app navigation (Products, Transactions, Customers, Settings)
- **System**: For system status checks (Printers, Network, POS)
- **Order**: For order management (Add items, View cart, Checkout)
- **Help**: For assistance topics (Navigation help, Order help, System help)

#### Button Actions
All action buttons use the `send_message` action type, which:
- Sends a specific command back to the AI
- Ensures consistent processing through the AI service
- Maintains conversation context and formatting

### Usage Examples

```typescript
import { geminiAIService } from '../services/geminiAIService';

// Process user message
const response = await geminiAIService.processMessage("Check printer status");

// Response includes:
// - response: Formatted printer status information
// - intent: 'system'
// - actionButtons: [] (empty for direct execution)
// - metadata: undefined

// For ambiguous requests
const response = await geminiAIService.processMessage("Navigate");

// Response includes:
// - response: "Where would you like to navigate?"
// - intent: 'navigation'
// - actionButtons: [Products, Transactions, Customers, Settings buttons]
// - metadata: undefined
```

### Integration with Redux

The service integrates with the Redux store through:
- `getStoreState()` from `ts/config/index.ts`
- Selectors from `ts/sagas/selector.ts`
- Real-time access to application state

### Error Handling

The service includes comprehensive error handling:
- Missing API key validation
- Network request failures
- JSON parsing errors
- Agent function execution errors

All errors are logged and appropriate fallback responses are provided. 