import { GoogleGenerativeAI } from '@google/generative-ai';
import Config from 'react-native-config';
import * as Immutable from 'immutable';
import { createSelector } from 'reselect';
import { getPreservedStore } from '../config'; // Correct store import
import {
  selectTransactionSession,
  selectBusinessName,
  selectStoreId,
  selectEmployeeId,
  selectCurrency,
  selectShiftOpenStatus,
  selectIsNetConnected,
  selectStoreName,
  selectStoreCompanyName,
  selectEnablePayByCash,
  selectEnablePayLater,
  selectEnableTakeaway,
  selectEnableTableLayout,
  selectAutoSyncAfterOpenShift,
  selectEnableOpenOrders,
  selectEnableOpenOrdersShortCut,
  selectEnableCustomerShortCut,
  selectEnableCustomerQR,
  selectCloseLastShiftNotification,
  selectDisablePolling,
  selectDisablePushNotification,
  selectAutoSignOutCount,
  selectAllowOutOfStock,
  selectAlwaysPrintReceipt,
  selectMerchantHasPrinter,
  selectKitchenDocketVariantIsMultipleLine,
  selectPrintEmployeeName,
  selectReceiptFontSize,
  selectKitchenDocketFontSize,
  selectEnableCustomerDisplay,
  selectTableLayoutEnabled,
  // MRS-related selectors
  selectMRS,
  selectIsMaster,
  selectIsEnabledMRS,
  selectIsBOEnabledMRS,
  selectMasterState,
  selectMRSClients,
  selectServerRunning,
  selectClientConnected,
  selectSlavePid,
  selectSnapshotPid,
  selectBackupPid,
  selectSnapshotVersion,
  selectServerIp,
  selectClientIp,
  selectMRSOutDatedClient,
  selectMRSRole,
  selectIsClient,
  selectIPAddress,
  selectRegisterId,
  selectRegisterName,
  // Printer-related selectors
  selectAllPrinters,
  selectAllOnlinePrinters,
  selectOfflinePrintersCount,
  selectErrorPrinters,
  selectPrinterErrorCount,
  selectIsPrinterSearching,
  selectKitchenPrinters,
  selectAssignedPrinters,
  selectUnAssignedPrinters,
  selectPrinterById,
  selectIsPingWorking,
  selectPrinterTagsSettings,
  selectOrderSummaryPrinter,
  selectDefaultKitchenPrinter,
} from '../sagas/selector';
import DAL from '../dal';
import { EmployeeType, TransactionType } from '../typings/schema';
import { isValidNumber, formatCurrency } from '../utils';
import { activate, deactivateRegister, setCurrentEmployee, setFreeTrial, setSequence, setStoreInfo, setSyncInfo, signOut } from '../actions';
import { isEmpty } from '../utils/validator';
import { SubscriptionStatus, t } from '../constants';
import { infoPOSBasicEvent, insertMobileData, POSBasicAction } from '../utils/logComponent';
// MRS-related imports
import { MasterState, errorMasterState, normalMasterState } from '../actions/mrs';
import { MRSRole } from '../constants/mrs';
// Printer-related imports
import { PrinterConfigType, PrinterWithError } from '../actions/settings';
import { PrinterConnectType } from '../constants';

// Initialize the Gemini AI client
const genAI = new GoogleGenerativeAI(Config.GEMINI_API_KEY);

// Create selector for transaction session conversion
const fromImmutableTransactionSession = createSelector(
  (state: Immutable.Map<string, any>) => state.get('TransactionSession', Immutable.Map()),
  transactionSession => transactionSession.toJS()
);

export interface ActionButton {
  label: string;
  message: string; // The message to send to chat when button is clicked
  type?: 'primary' | 'secondary' | 'danger';
}

export interface AIResponse {
  response: string;
  intent: 'navigation' | 'order' | 'help' | 'general' | 'system' | 'tool_call';
  actionButtons?: ActionButton[];
  params?: {
    route?: string; // Exact route name for navigation intent
  };
  toolCall?: {
    toolName: string;
    parameters: any;
  };
}

// Agent tools for accessing POS system information
export interface POSSystemInfo {
  currentTransaction?: any;
  businessName: string;
  storeName: string;
  storeId: string;
  currentEmployeeId?: string;
  currency: string;
  isShiftOpen: boolean;
  isNetworkConnected: boolean;
  enabledFeatures: {
    payByCash: boolean;
    payLater: boolean;
    takeaway: boolean;
    tableLayout: boolean;
  };
}

class POSAgentTools {
  static getCurrentPOSInfo(): POSSystemInfo {
    const store = getPreservedStore();
    const state = store.getState();

    return {
      currentTransaction: fromImmutableTransactionSession(state),
      businessName: selectBusinessName(state),
      storeName: selectStoreName(state),
      storeId: selectStoreId(state),
      currentEmployeeId: selectEmployeeId(state),
      currency: selectCurrency(state),
      isShiftOpen: selectShiftOpenStatus(state),
      isNetworkConnected: selectIsNetConnected(state),
      enabledFeatures: {
        payByCash: selectEnablePayByCash(state),
        payLater: selectEnablePayLater(state),
        takeaway: selectEnableTakeaway(state),
        tableLayout: selectEnableTableLayout(state),
      },
    };
  }

  static getCurrentTransactionSummary(): any {
    const store = getPreservedStore();
    const state = store.getState();
    const transaction = fromImmutableTransactionSession(state);
    const currency = selectCurrency(state);

    if (!transaction || !transaction.items || transaction.items.length === 0) {
      return {
        hasTransaction: false,
        message: 'No current transaction or cart is empty.',
        itemCount: 0,
        total: 0,
        currency,
      };
    }

    return {
      hasTransaction: true,
      itemCount: transaction.items.length,
      total: transaction.display?.total || transaction.total || 0,
      currency,
      items: transaction.items.map((item: any) => ({
        name: item.name || item.productName,
        quantity: item.quantity || 1,
        price: item.price || item.unitPrice || 0,
        total: (item.quantity || 1) * (item.price || item.unitPrice || 0),
      })),
      subtotal: transaction.display?.subtotal || transaction.subtotal || 0,
      tax: transaction.display?.tax || transaction.tax || 0,
      discount: transaction.display?.discount || transaction.discount || 0,
    };
  }

  static getStoreStatus(): any {
    const store = getPreservedStore();
    const state = store.getState();

    return {
      businessName: selectBusinessName(state),
      storeName: selectStoreName(state),
      storeId: selectStoreId(state),
      isShiftOpen: selectShiftOpenStatus(state),
      isNetworkConnected: selectIsNetConnected(state),
      currency: selectCurrency(state),
      currentEmployeeId: selectEmployeeId(state),
      enabledFeatures: {
        payByCash: selectEnablePayByCash(state),
        payLater: selectEnablePayLater(state),
        takeaway: selectEnableTakeaway(state),
        tableLayout: selectEnableTableLayout(state),
      },
    };
  }

  static getPrinterStatus(): any {
    try {
      const store = getPreservedStore();
      const state = store.getState();

      const allPrinters = selectAllPrinters(state);
      const onlinePrinters = selectAllOnlinePrinters(state);
      const offlinePrintersCount = selectOfflinePrintersCount(state);
      const errorPrinters = selectErrorPrinters(state).toJS();
      const errorCount = selectPrinterErrorCount(state);
      const isSearching = selectIsPrinterSearching(state);
      const isPingWorking = selectIsPingWorking(state);
      const merchantHasPrinter = selectMerchantHasPrinter(state);

      const printersByType = {
        receipt: allPrinters.filter(p => p.isReceiptPrinter),
        kitchen: allPrinters.filter(p => p.tags && p.tags.length > 0),
        label: allPrinters.filter(p => p.isLabelPrinter),
        beepSummary: allPrinters.filter(p => p.isBeepOrderSummaryPrinter),
      };

      const printersByConnection = {
        lan: allPrinters.filter(p => p.printerConnectType === PrinterConnectType.LAN),
        bluetooth: allPrinters.filter(p => p.printerConnectType === PrinterConnectType.Bluetooth),
        usb: allPrinters.filter(p => p.printerConnectType === PrinterConnectType.USB),
        builtin: allPrinters.filter(p => p.isBuiltInPrinter),
      };

      const networkIssues = allPrinters.filter(p => p.printerConnectType === PrinterConnectType.LAN && (!p.isOnline || (p.pingStatus && p.pingStatus > 50)));

      let overallStatus = 'healthy';
      let diagnosis = 'All printers are functioning normally.';

      if (!merchantHasPrinter) {
        overallStatus = 'warning';
        diagnosis = 'Merchant has no printer configured. Enable printer in Settings > Printer.';
      } else if (offlinePrintersCount > 0) {
        overallStatus = 'critical';
        diagnosis = `${offlinePrintersCount} printer(s) are offline. Check connections and power.`;
      } else if (errorCount > 0) {
        overallStatus = 'warning';
        diagnosis = `${errorCount} print job error(s) detected. Check printer status and retry failed jobs.`;
      } else if (networkIssues.length > 0) {
        overallStatus = 'warning';
        diagnosis = `${networkIssues.length} printer(s) have network connectivity issues.`;
      }

      return {
        overallStatus,
        diagnosis,
        summary: {
          totalPrinters: allPrinters.length,
          onlinePrinters: onlinePrinters.length,
          offlinePrinters: offlinePrintersCount,
          errorJobs: errorCount,
          merchantHasPrinter,
          isSearching,
          isPingWorking,
        },
        printersByType: {
          receipt: printersByType.receipt.length,
          kitchen: printersByType.kitchen.length,
          label: printersByType.label.length,
          beepSummary: printersByType.beepSummary.length,
        },
        printersByConnection: {
          lan: printersByConnection.lan.length,
          bluetooth: printersByConnection.bluetooth.length,
          usb: printersByConnection.usb.length,
          builtin: printersByConnection.builtin.length,
        },
        networkIssues: networkIssues.length,
        troubleshootingUrl: 'https://care.storehub.com/en/articles/6780008-troubleshoot-printer-issues',
      };
    } catch (error) {
      console.error('Error getting printer status:', error);
      return {
        overallStatus: 'error',
        error: 'Failed to get printer status',
        diagnosis: 'Unable to retrieve printer status. Please try again or contact support.',
      };
    }
  }

  static getNetworkInfo(): any {
    const store = getPreservedStore();
    const state = store.getState();

    return {
      isConnected: selectIsNetConnected(state),
      connectionType: 'wifi', // This could be enhanced with actual network type detection
      signalStrength: 'strong', // This could be enhanced with actual signal strength
      lastSyncTime: new Date().toISOString(),
      apiEndpoint: 'Connected to StoreHub API',
    };
  }

  static getEmployeeInfo(): any {
    const store = getPreservedStore();
    const state = store.getState();

    return {
      currentEmployeeId: selectEmployeeId(state),
      isLoggedIn: !!selectEmployeeId(state),
      businessName: selectBusinessName(state),
      storeName: selectStoreName(state),
      permissions: {
        canProcessSales: true,
        canManageProducts: true,
        canViewReports: true,
        canManageCustomers: true,
      },
    };
  }

  static getShiftInfo(): any {
    const store = getPreservedStore();
    const state = store.getState();

    return {
      isShiftOpen: selectShiftOpenStatus(state),
      currentEmployeeId: selectEmployeeId(state),
      shiftStartTime: null, // This could be enhanced with actual shift start time
      totalSales: 0, // This could be enhanced with actual shift sales data
      transactionCount: 0, // This could be enhanced with actual transaction count
      currency: selectCurrency(state),
    };
  }

  static getProductCatalog(): any {
    // This would typically access product catalog from Redux
    // For now, return mock data - you can implement actual product catalog logic
    return {
      totalProducts: 150,
      categories: [
        { id: 'beverages', name: 'Beverages', productCount: 45 },
        { id: 'food', name: 'Food', productCount: 80 },
        { id: 'desserts', name: 'Desserts', productCount: 25 },
      ],
      featuredProducts: [
        { id: 'coffee', name: 'Coffee', price: 3.5, category: 'beverages' },
        { id: 'sandwich', name: 'Sandwich', price: 8.99, category: 'food' },
      ],
    };
  }

  static getCustomerInfo(): any {
    // This would typically access customer data from Redux
    // For now, return mock data - you can implement actual customer logic
    return {
      totalCustomers: 1250,
      loyaltyMembers: 890,
      newCustomersToday: 5,
      topCustomers: [
        { id: 'cust_001', name: 'John Doe', visits: 45, totalSpent: 567.8 },
        { id: 'cust_002', name: 'Jane Smith', visits: 32, totalSpent: 423.5 },
      ],
    };
  }

  static getPaymentMethods(): any {
    const store = getPreservedStore();
    const state = store.getState();

    return {
      availableMethods: {
        cash: selectEnablePayByCash(state),
        payLater: selectEnablePayLater(state),
        card: true, // This could be enhanced with actual card payment status
        digitalWallet: true, // This could be enhanced with actual digital wallet status
        nfc: true, // This could be enhanced with actual NFC payment status
      },
      preferredMethod: 'cash',
      currency: selectCurrency(state),
    };
  }

  // Transaction Database Tools
  static getTransactionHistory(parameters?: { limit?: number; includeOpen?: boolean; registerId?: string }): any {
    try {
      const { limit = 50, includeOpen = false } = parameters || {};

      // Get transactions from database
      let transactions;
      if (includeOpen) {
        // Get both open and closed transactions
        const openOrders = DAL.getOpenOrderList();
        const closedTransactions = DAL.getTransactionList();
        transactions = [...Array.from(openOrders || []), ...Array.from(closedTransactions || [])];
      } else {
        transactions = Array.from(DAL.getTransactionList() || []);
      }

      // Apply limit
      if (limit && transactions.length > limit) {
        transactions = transactions.slice(0, limit);
      }

      if (!transactions || transactions.length === 0) {
        return {
          success: true,
          message: 'No transactions found',
          totalCount: 0,
          transactions: [],
        };
      }

      const formattedTransactions = transactions.map((trx: TransactionType) => ({
        transactionId: trx.transactionId,
        receiptNumber: trx.receiptNumber,
        total: trx.total || 0,
        subtotal: trx.subtotal || 0,
        tax: trx.tax || 0,
        discount: trx.discount || 0,
        itemCount: trx.items?.length || 0,
        isOpen: trx.isOpen || false,
        isCancelled: trx.isCancelled || false,
        isCompleted: trx.isCompleted || false,
        createdDate: trx.createdDate,
        modifiedDate: trx.modifiedDate,
        employeeId: trx.employeeId,
        customerId: trx.customerId,
        tableId: trx.tableId,
        pickUpId: trx.pickUpId,
        transactionType: trx.transactionType,
        paymentMethods: trx.payments?.map(p => p.type) || [],
        totalPaid: trx.payments?.reduce((sum, p) => sum + (p.amount || 0), 0) || 0,
      }));

      return {
        success: true,
        totalCount: formattedTransactions.length,
        transactions: formattedTransactions,
        summary: {
          totalSales: formattedTransactions.reduce((sum, trx) => sum + (trx.total || 0), 0),
          completedTransactions: formattedTransactions.filter(trx => trx.isCompleted).length,
          openTransactions: formattedTransactions.filter(trx => trx.isOpen).length,
          cancelledTransactions: formattedTransactions.filter(trx => trx.isCancelled).length,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to get transaction history: ${error.message}`,
        transactions: [],
      };
    }
  }

  static getTransactionById(parameters: { transactionId: string }): any {
    try {
      const { transactionId } = parameters;

      if (!transactionId) {
        return {
          success: false,
          error: 'Transaction ID is required',
        };
      }

      const transaction = DAL.getTransactionById(transactionId);

      if (!transaction) {
        return {
          success: false,
          error: 'Transaction not found',
        };
      }

      return {
        success: true,
        transaction: {
          transactionId: transaction.transactionId,
          receiptNumber: transaction.receiptNumber,
          sequenceNumber: transaction.sequenceNumber,
          total: transaction.total || 0,
          subtotal: transaction.subtotal || 0,
          tax: transaction.tax || 0,
          discount: transaction.discount || 0,
          serviceCharge: transaction.serviceCharge || 0,
          roundedAmount: transaction.roundedAmount || 0,
          isOpen: transaction.isOpen || false,
          isCancelled: transaction.isCancelled || false,
          isCompleted: transaction.isCompleted || false,
          createdDate: transaction.createdDate,
          modifiedDate: transaction.modifiedDate,
          employeeId: transaction.employeeId,
          customerId: transaction.customerId,
          tableId: transaction.tableId,
          pickUpId: transaction.pickUpId,
          takeawayId: transaction.takeawayId,
          transactionType: transaction.transactionType,
          comment: transaction.comment,
          headcount: transaction.headcount || 0,
          pax: transaction.pax || 0,
          items:
            transaction.items?.map((item: any) => ({
              name: item.name || item.productName,
              quantity: item.quantity || 1,
              unitPrice: item.unitPrice || item.price || 0,
              totalPrice: (item.quantity || 1) * (item.unitPrice || item.price || 0),
              category: item.category,
              modifiers:
                item.modifiers?.map((mod: any) => ({
                  name: mod.name,
                  price: mod.price || 0,
                })) || [],
            })) || [],
          payments:
            transaction.payments?.map((payment: any) => ({
              type: payment.type,
              subType: payment.subType,
              amount: payment.amount || 0,
              cashTendered: payment.cashTendered || 0,
              roundedAmount: payment.roundedAmount || 0,
              paymentMethod: payment.paymentMethod,
              isVoided: payment.isVoided || false,
            })) || [],
          promotions:
            transaction.promotions?.map((promo: any) => ({
              name: promo.name,
              discountAmount: promo.discountAmount || 0,
              discountType: promo.discountType,
            })) || [],
          loyaltyDiscounts:
            transaction.loyaltyDiscounts?.map((loyalty: any) => ({
              type: loyalty.type,
              amount: loyalty.amount || 0,
              pointsUsed: loyalty.pointsUsed || 0,
            })) || [],
        },
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to get transaction details: ${error.message}`,
      };
    }
  }

  static searchTransactions(parameters: {
    receiptNumber?: string;
    customerId?: string;
    employeeId?: string;
    dateFrom?: string;
    dateTo?: string;
    minAmount?: number;
    maxAmount?: number;
    transactionType?: string;
    isOpen?: boolean;
    limit?: number;
  }): any {
    try {
      const { receiptNumber, customerId, employeeId, dateFrom, dateTo, minAmount, maxAmount, transactionType, isOpen, limit = 100 } = parameters || {};

      let transactions: TransactionType[] = [];

      // If searching by receipt number, use specific method
      if (receiptNumber) {
        const receiptTransactions = DAL.searchTrxByReceiptNumber(receiptNumber);
        transactions = Array.from(receiptTransactions || []);
      } else {
        // Get all transactions
        const openOrders = DAL.getOpenOrderList();
        const closedTransactions = DAL.getTransactionList();
        transactions = [...Array.from(openOrders || []), ...Array.from(closedTransactions || [])];
      }

      if (!transactions || transactions.length === 0) {
        return {
          success: true,
          message: 'No transactions found',
          totalCount: 0,
          transactions: [],
        };
      }

      // Apply filters
      let filteredTransactions = transactions.filter((trx: TransactionType) => {
        // Customer ID filter
        if (customerId && trx.customerId !== customerId) {
          return false;
        }

        // Employee ID filter
        if (employeeId && trx.employeeId !== employeeId) {
          return false;
        }

        // Transaction type filter
        if (transactionType && trx.transactionType !== transactionType) {
          return false;
        }

        // Open status filter
        if (typeof isOpen === 'boolean' && trx.isOpen !== isOpen) {
          return false;
        }

        // Amount range filter
        if (isValidNumber(minAmount) && (trx.total || 0) < minAmount) {
          return false;
        }
        if (isValidNumber(maxAmount) && (trx.total || 0) > maxAmount) {
          return false;
        }

        // Date range filter
        if (dateFrom && trx.createdDate && new Date(trx.createdDate) < new Date(dateFrom)) {
          return false;
        }
        if (dateTo && trx.createdDate && new Date(trx.createdDate) > new Date(dateTo)) {
          return false;
        }

        return true;
      });

      // Limit results
      filteredTransactions = filteredTransactions.slice(0, limit);

      const formattedTransactions = filteredTransactions.map((trx: TransactionType) => ({
        transactionId: trx.transactionId,
        receiptNumber: trx.receiptNumber,
        total: trx.total || 0,
        subtotal: trx.subtotal || 0,
        tax: trx.tax || 0,
        discount: trx.discount || 0,
        itemCount: trx.items?.length || 0,
        isOpen: trx.isOpen || false,
        isCancelled: trx.isCancelled || false,
        isCompleted: trx.isCompleted || false,
        createdDate: trx.createdDate,
        employeeId: trx.employeeId,
        customerId: trx.customerId,
        tableId: trx.tableId,
        transactionType: trx.transactionType,
      }));

      return {
        success: true,
        totalCount: formattedTransactions.length,
        transactions: formattedTransactions,
        searchCriteria: parameters,
        summary: {
          totalSales: formattedTransactions.reduce((sum, trx) => sum + (trx.total || 0), 0),
          averageTransaction:
            formattedTransactions.length > 0 ? formattedTransactions.reduce((sum, trx) => sum + (trx.total || 0), 0) / formattedTransactions.length : 0,
          completedTransactions: formattedTransactions.filter(trx => trx.isCompleted).length,
          openTransactions: formattedTransactions.filter(trx => trx.isOpen).length,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to search transactions: ${error.message}`,
        transactions: [],
      };
    }
  }

  static getTransactionStatistics(parameters?: { dateFrom?: string; dateTo?: string; employeeId?: string }): any {
    try {
      const { dateFrom, dateTo, employeeId } = parameters || {};

      // Get transaction count
      const totalTransactionCount = DAL.getAllTransactionCount();
      const localTransactionCount = DAL.getLocalTransactionCount();

      // Get recent transactions for analysis
      const openOrders = DAL.getOpenOrderList();
      const closedTransactions = DAL.getTransactionList();
      const allTransactions = [...Array.from(openOrders || []), ...Array.from(closedTransactions || [])];

      if (!allTransactions || allTransactions.length === 0) {
        return {
          success: true,
          message: 'No transactions found for statistics',
          statistics: {
            totalCount: totalTransactionCount,
            localCount: localTransactionCount,
            totalSales: 0,
            averageTransaction: 0,
            completedTransactions: 0,
            openTransactions: 0,
            cancelledTransactions: 0,
          },
        };
      }

      // Filter transactions based on parameters
      let filteredTransactions = allTransactions.filter((trx: TransactionType) => {
        if (employeeId && trx.employeeId !== employeeId) return false;
        if (dateFrom && trx.createdDate && new Date(trx.createdDate) < new Date(dateFrom)) return false;
        if (dateTo && trx.createdDate && new Date(trx.createdDate) > new Date(dateTo)) return false;
        return true;
      });

      const totalSales = filteredTransactions.reduce((sum, trx) => sum + (trx.total || 0), 0);
      const completedTransactions = filteredTransactions.filter(trx => trx.isCompleted).length;
      const openTransactions = filteredTransactions.filter(trx => trx.isOpen).length;
      const cancelledTransactions = filteredTransactions.filter(trx => trx.isCancelled).length;
      const averageTransaction = filteredTransactions.length > 0 ? totalSales / filteredTransactions.length : 0;

      // Payment method breakdown
      const paymentMethodStats: { [key: string]: { count: number; total: number } } = {};
      filteredTransactions.forEach((trx: TransactionType) => {
        trx.payments?.forEach((payment: any) => {
          const method = payment.type || 'Unknown';
          if (!paymentMethodStats[method]) {
            paymentMethodStats[method] = { count: 0, total: 0 };
          }
          paymentMethodStats[method].count++;
          paymentMethodStats[method].total += payment.amount || 0;
        });
      });

      // Hourly breakdown (last 24 hours)
      const now = new Date();
      const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      const hourlyStats: { [hour: string]: { count: number; sales: number } } = {};

      filteredTransactions
        .filter(trx => trx.createdDate && new Date(trx.createdDate) >= last24Hours)
        .forEach((trx: TransactionType) => {
          const hour = new Date(trx.createdDate!).getHours().toString().padStart(2, '0');
          if (!hourlyStats[hour]) {
            hourlyStats[hour] = { count: 0, sales: 0 };
          }
          hourlyStats[hour].count++;
          hourlyStats[hour].sales += trx.total || 0;
        });

      return {
        success: true,
        statistics: {
          totalCount: totalTransactionCount,
          localCount: localTransactionCount,
          filteredCount: filteredTransactions.length,
          totalSales,
          averageTransaction,
          completedTransactions,
          openTransactions,
          cancelledTransactions,
          paymentMethodBreakdown: paymentMethodStats,
          hourlyBreakdown: hourlyStats,
          dateRange: {
            from: dateFrom,
            to: dateTo,
          },
          filters: {
            employeeId,
          },
        },
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to get transaction statistics: ${error.message}`,
      };
    }
  }

  static getOpenOrders(parameters?: { tableId?: string; employeeId?: string; limit?: number }): any {
    try {
      const { tableId, employeeId, limit = 50 } = parameters || {};

      // Get open orders from database
      const openOrders = Array.from(DAL.getOpenOrderList() || []);

      if (!openOrders || openOrders.length === 0) {
        return {
          success: true,
          message: 'No open orders found',
          totalCount: 0,
          orders: [],
        };
      }

      // Filter by parameters
      let filteredOrders = openOrders.filter((order: TransactionType) => {
        if (tableId && order.tableId !== tableId) return false;
        if (employeeId && order.employeeId !== employeeId) return false;
        return true;
      });

      // Apply limit
      if (limit && filteredOrders.length > limit) {
        filteredOrders = filteredOrders.slice(0, limit);
      }

      const formattedOrders = filteredOrders.map((order: TransactionType) => ({
        transactionId: order.transactionId,
        receiptNumber: order.receiptNumber,
        tableId: order.tableId,
        pickUpId: order.pickUpId,
        takeawayId: order.takeawayId,
        total: order.total || 0,
        subtotal: order.subtotal || 0,
        itemCount: order.items?.length || 0,
        createdDate: order.createdDate,
        modifiedDate: order.modifiedDate,
        employeeId: order.employeeId,
        customerId: order.customerId,
        pax: order.pax || 0,
        comment: order.comment,
        cookingStatus: order.cookingStatus,
        isPayLater: order.isPayLater || false,
        items:
          order.items?.map((item: any) => ({
            name: item.name || item.productName,
            quantity: item.quantity || 1,
            unitPrice: item.unitPrice || item.price || 0,
            totalPrice: (item.quantity || 1) * (item.unitPrice || item.price || 0),
          })) || [],
      }));

      return {
        success: true,
        totalCount: formattedOrders.length,
        orders: formattedOrders,
        summary: {
          totalValue: formattedOrders.reduce((sum, order) => sum + (order.total || 0), 0),
          averageOrderValue: formattedOrders.length > 0 ? formattedOrders.reduce((sum, order) => sum + (order.total || 0), 0) / formattedOrders.length : 0,
          totalItems: formattedOrders.reduce((sum, order) => sum + (order.itemCount || 0), 0),
          payLaterOrders: formattedOrders.filter(order => order.isPayLater).length,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to get open orders: ${error.message}`,
        orders: [],
      };
    }
  }

  static getTransactionsByReceiptNumber(parameters: { receiptNumber: string }): any {
    try {
      const { receiptNumber } = parameters;

      if (!receiptNumber) {
        return {
          error: 'Receipt number is required',
        };
      }

      // Get all transactions and filter by receipt number
      const allTransactions = DAL.getTransactionList();
      const matchingTransactions = Array.from(allTransactions || []).filter((transaction: TransactionType) => {
        return transaction.receiptNumber === receiptNumber;
      });

      if (matchingTransactions.length === 0) {
        return {
          message: `No transactions found with receipt number: ${receiptNumber}`,
          transactions: [],
        };
      }

      // Get store info for currency
      const store = getPreservedStore();
      const state = store.getState();
      const currency = selectCurrency(state);

      // Format transactions for display
      const formattedTransactions = matchingTransactions.map((transaction: TransactionType) => {
        const total = transaction.total || 0;
        const itemCount = transaction.items?.length || 0;

        return {
          id: transaction.transactionId,
          receiptNumber: transaction.receiptNumber,
          total: isValidNumber(total) ? formatCurrency(total) : 'N/A',
          itemCount,
          status: transaction.isOpen ? 'Open' : 'Completed',
          employeeId: transaction.employeeId || 'N/A',
          createdDate: transaction.createdDate ? new Date(transaction.createdDate).toLocaleString() : 'N/A',
          tableId: transaction.tableId || 'N/A',
          customerId: transaction.customerId || 'N/A',
          isCancelled: transaction.isCancelled || false,
          isCompleted: transaction.isCompleted || false,
        };
      });

      return {
        message: `Found ${matchingTransactions.length} transaction(s) with receipt number: ${receiptNumber}`,
        transactions: formattedTransactions,
        totalFound: matchingTransactions.length,
      };
    } catch (error) {
      return {
        error: `Failed to get transactions by receipt number: ${error.message}`,
      };
    }
  }

  // Settings control methods
  static getCurrentSettings(): any {
    try {
      const store = getPreservedStore();
      const state = store.getState();

      const generalSettings = {
        autoSyncAfterOpenShift: selectAutoSyncAfterOpenShift(state),
        enableOpenOrders: selectEnableOpenOrders(state),
        enableOpenOrdersShortCut: selectEnableOpenOrdersShortCut(state),
        enableCustomerShortCut: selectEnableCustomerShortCut(state),
        enableCustomerQR: selectEnableCustomerQR(state),
        enableCloseLastShiftNotification: selectCloseLastShiftNotification(state),
        disablePolling: selectDisablePolling(state),
        disablePushNotification: selectDisablePushNotification(state),
        autoSignOutCount: selectAutoSignOutCount(state),
        allowOutOfStock: selectAllowOutOfStock(state),
      };

      const printerSettings = {
        alwaysPrintReceipt: selectAlwaysPrintReceipt(state),
        merchantHasPrinter: selectMerchantHasPrinter(state),
        kitchenDocketVariantIsMultipleLine: selectKitchenDocketVariantIsMultipleLine(state),
        printEmployeeName: selectPrintEmployeeName(state),
        receiptFontSize: selectReceiptFontSize(state),
        kitchenDocketFontSize: selectKitchenDocketFontSize(state),
      };

      const displaySettings = {
        enableCustomerDisplay: selectEnableCustomerDisplay(state),
      };

      const tableLayoutSettings = {
        enableTableLayout: selectEnableTableLayout(state),
        tableLayoutEnabled: selectTableLayoutEnabled(state),
      };

      return {
        message: 'Current POS settings retrieved successfully',
        settings: {
          general: generalSettings,
          printer: printerSettings,
          display: displaySettings,
          tableLayout: tableLayoutSettings,
        },
      };
    } catch (error) {
      return {
        error: `Failed to get current settings: ${error.message}`,
      };
    }
  }

  static getGeneralSettings(): any {
    try {
      const store = getPreservedStore();
      const state = store.getState();

      const generalSettings = {
        autoSyncAfterOpenShift: selectAutoSyncAfterOpenShift(state),
        enableOpenOrders: selectEnableOpenOrders(state),
        enableOpenOrdersShortCut: selectEnableOpenOrdersShortCut(state),
        enableCustomerShortCut: selectEnableCustomerShortCut(state),
        enableCustomerQR: selectEnableCustomerQR(state),
        enableCloseLastShiftNotification: selectCloseLastShiftNotification(state),
        disablePolling: selectDisablePolling(state),
        disablePushNotification: selectDisablePushNotification(state),
        autoSignOutCount: selectAutoSignOutCount(state),
        allowOutOfStock: selectAllowOutOfStock(state),
      };

      return {
        message: 'General settings retrieved successfully',
        settings: generalSettings,
      };
    } catch (error) {
      return {
        error: `Failed to get general settings: ${error.message}`,
      };
    }
  }

  static updateAutoSyncAfterOpenShift(parameters: { enabled: boolean }): any {
    try {
      const { enabled } = parameters;

      if (typeof enabled !== 'boolean') {
        return {
          error: 'Parameter "enabled" must be a boolean value (true or false)',
        };
      }

      const store = getPreservedStore();

      // Dispatch the update action
      store.dispatch({
        type: 'updateGeneralSettings',
        payload: {
          autoSyncAfterOpenShift: enabled,
        },
      });

      return {
        message: `Auto-Sync after Open Shift has been ${enabled ? 'enabled' : 'disabled'}`,
        setting: 'autoSyncAfterOpenShift',
        value: enabled,
        success: true,
      };
    } catch (error) {
      return {
        error: `Failed to update Auto-Sync after Open Shift setting: ${error.message}`,
        success: false,
      };
    }
  }

  static updateGeneralSetting(parameters: { settingName: string; value: any }): any {
    try {
      const { settingName, value } = parameters;

      if (!settingName) {
        return {
          error: 'Parameter "settingName" is required',
        };
      }

      // Define allowed settings and their validation
      const allowedSettings = {
        autoSyncAfterOpenShift: { type: 'boolean', description: 'Auto-Sync after Open Shift' },
        enableOpenOrders: { type: 'boolean', description: 'Enable Open Orders' },
        enableOpenOrdersShortCut: { type: 'boolean', description: 'Enable Open Orders Shortcut' },
        enableCustomerShortCut: { type: 'boolean', description: 'Enable Customer Shortcut' },
        enableCustomerQR: { type: 'boolean', description: 'Enable Customer QR' },
        enableCloseLastShiftNotification: { type: 'boolean', description: 'Enable Close Last Shift Notification' },
        disablePolling: { type: 'boolean', description: 'Disable Polling' },
        disablePushNotification: { type: 'boolean', description: 'Disable Push Notification' },
        allowOutOfStock: { type: 'boolean', description: 'Allow Out of Stock' },
        autoSignOutCount: { type: 'string', description: 'Auto Sign Out Count' },
      };

      if (!allowedSettings[settingName]) {
        return {
          error: `Setting "${settingName}" is not allowed to be modified. Allowed settings: ${Object.keys(allowedSettings).join(', ')}`,
        };
      }

      const settingConfig = allowedSettings[settingName];

      // Validate value type
      if (settingConfig.type === 'boolean' && typeof value !== 'boolean') {
        return {
          error: `Setting "${settingName}" requires a boolean value (true or false)`,
        };
      }

      if (settingConfig.type === 'string' && typeof value !== 'string') {
        return {
          error: `Setting "${settingName}" requires a string value`,
        };
      }

      const store = getPreservedStore();

      // Dispatch the update action
      store.dispatch({
        type: 'updateGeneralSettings',
        payload: {
          [settingName]: value,
        },
      });

      return {
        message: `${settingConfig.description} has been updated to: ${value}`,
        setting: settingName,
        value: value,
        success: true,
      };
    } catch (error) {
      return {
        error: `Failed to update general setting: ${error.message}`,
        success: false,
      };
    }
  }

  // Authentication methods
  static getAvailableCredentials(): any {
    const credentials = [
      { bn: 'stephen666', eml: '<EMAIL>', pwd: '111111', pin: '2222' },
      { bn: 'kamdar', eml: '<EMAIL>', pwd: '123456', pin: '1111' },
      { bn: 'justcoffee', eml: '<EMAIL>', pwd: '123456', pin: '2222' },
      { bn: 'feida', eml: '<EMAIL>', pwd: '123456', pin: '1111' },
      { bn: 'ancafe', eml: '<EMAIL>', pwd: '123456', pin: '1234' },
      { bn: 'kafe123', eml: '<EMAIL>', pwd: '111111', pin: '1111' },
      { bn: 'cathy6', eml: '<EMAIL>', pwd: '123456', pin: '6721' },
      { bn: 'leonphtestfood', eml: '<EMAIL>', pwd: '111111', pin: '1111', comment: 'BIR, updated by Grace' },
      { bn: 'thteststore', eml: '<EMAIL>', pwd: 'Test123', pin: '3333', comment: 'TH' },
      { bn: 'myteststore', eml: '<EMAIL>', pwd: 'Test123', pin: '1111' },
      { bn: 'birph', eml: '<EMAIL>', pwd: 'test123123', pin: '1111', comment: 'FDI' },
      { bn: 'chunyuanth', eml: '<EMAIL>', pwd: 'Ccy@123456', pin: '' },
      { bn: 'onlytestaccount', eml: '<EMAIL>', pwd: 'test123123', pin: '' },
      { bn: 'kdstest', eml: '<EMAIL>', pwd: '123456', pin: '1111' },
      { bn: 'cafe95425676', eml: '<EMAIL>', pwd: 'Divya123', pin: '2998' },
      { bn: 'productstore', eml: '<EMAIL>', pwd: '123456', pin: '1282' },
    ];

    return {
      message: 'Available test credentials for sign in',
      credentials: credentials.map(cred => ({
        businessName: cred.bn,
        email: cred.eml,
        comment: cred.comment || 'Test account',
        hasPin: !!cred.pin,
      })),
      defaultRecommended: {
        businessName: 'kafe123',
        email: '<EMAIL>',
        comment: 'Default recommended test account',
      },
    };
  }

  static signIn(parameters: { pin?: string }): any {
    try {
      const { pin } = parameters || {};

      // Test credentials
      const credentials = [
        { bn: 'stephen666', eml: '<EMAIL>', pwd: '111111', pin: '2222' },
        { bn: 'kamdar', eml: '<EMAIL>', pwd: '123456', pin: '1111' },
        { bn: 'justcoffee', eml: '<EMAIL>', pwd: '123456', pin: '2222' },
        { bn: 'feida', eml: '<EMAIL>', pwd: '123456', pin: '1111' },
        { bn: 'ancafe', eml: '<EMAIL>', pwd: '123456', pin: '1234' },
        { bn: 'kafe123', eml: '<EMAIL>', pwd: '111111', pin: '1111' },
        { bn: 'cathy6', eml: '<EMAIL>', pwd: '123456', pin: '6721' },
        { bn: 'leonphtestfood', eml: '<EMAIL>', pwd: '111111', pin: '1111', comment: 'BIR, updated by Grace' },
        { bn: 'thteststore', eml: '<EMAIL>', pwd: 'Test123', pin: '3333', comment: 'TH' },
        { bn: 'myteststore', eml: '<EMAIL>', pwd: 'Test123', pin: '1111' },
        { bn: 'birph', eml: '<EMAIL>', pwd: 'test123123', pin: '1111', comment: 'FDI' },
        { bn: 'chunyuanth', eml: '<EMAIL>', pwd: 'Ccy@123456', pin: '' },
        { bn: 'onlytestaccount', eml: '<EMAIL>', pwd: 'test123123', pin: '' },
        { bn: 'kdstest', eml: '<EMAIL>', pwd: '123456', pin: '1111' },
        { bn: 'cafe95425676', eml: '<EMAIL>', pwd: 'Divya123', pin: '2998' },
        { bn: 'productstore', eml: '<EMAIL>', pwd: '123456', pin: '1282' },
      ];

      const store = getPreservedStore();

      const employee = DAL.getEmployeeByPinCode(pin ?? '1111');
      if (employee) {
        store.dispatch(
          setCurrentEmployee({
            employeeId: employee.employeeId,
          })
        );

        return {
          message: 'Successfully signed',
          success: true,
        };
      } else {
        return {
          error: `Failed to sign in, incorrect PIN: ${pin ?? '1111'}`,
          success: false,
        };
      }
    } catch (error) {
      return {
        error: `Failed to sign in: ${error.message}`,
        success: false,
      };
    }
  }

  static signOut(): any {
    try {
      const store = getPreservedStore();
      const state = store.getState();

      // Get current employee info before signing out
      const currentEmployeeId = selectEmployeeId(state);
      const businessName = selectBusinessName(state);

      if (!currentEmployeeId) {
        return {
          message: 'No user is currently signed in.',
          success: true,
        };
      }

      // Dispatch sign out action
      store.dispatch(signOut({ event: 'ai' }));

      return {
        message: `Successfully signed out from: ${businessName}`,
        previousEmployee: currentEmployeeId,
        success: true,
      };
    } catch (error) {
      return {
        error: `Failed to sign out: ${error.message}`,
        success: false,
      };
    }
  }

  // Business activation methods
  static getAvailableBusinessCredentials(): any {
    const credentials = [
      { bn: 'stephen666', eml: '<EMAIL>', pwd: '111111', pin: '2222' },
      { bn: 'kamdar', eml: '<EMAIL>', pwd: '123456', pin: '1111' },
      { bn: 'justcoffee', eml: '<EMAIL>', pwd: '123456', pin: '2222' },
      { bn: 'feida', eml: '<EMAIL>', pwd: '123456', pin: '1111' },
      { bn: 'ancafe', eml: '<EMAIL>', pwd: '123456', pin: '1234' },
      { bn: 'kafe123', eml: '<EMAIL>', pwd: '111111', pin: '1111' },
      { bn: 'cathy6', eml: '<EMAIL>', pwd: '123456', pin: '6721' },
      { bn: 'leonphtestfood', eml: '<EMAIL>', pwd: '111111', pin: '1111', comment: 'BIR, updated by Grace' },
      { bn: 'thteststore', eml: '<EMAIL>', pwd: 'Test123', pin: '3333', comment: 'TH' },
      { bn: 'myteststore', eml: '<EMAIL>', pwd: 'Test123', pin: '1111' },
      { bn: 'birph', eml: '<EMAIL>', pwd: 'test123123', pin: '1111', comment: 'FDI' },
      { bn: 'chunyuanth', eml: '<EMAIL>', pwd: 'Ccy@123456', pin: '' },
      { bn: 'onlytestaccount', eml: '<EMAIL>', pwd: 'test123123', pin: '' },
      { bn: 'kdstest', eml: '<EMAIL>', pwd: '123456', pin: '1111' },
      { bn: 'cafe95425676', eml: '<EMAIL>', pwd: 'Divya123', pin: '2998' },
      { bn: 'productstore', eml: '<EMAIL>', pwd: '123456', pin: '1282' },
    ];

    return {
      message: 'Available business credentials for activation',
      credentials: credentials.map(cred => ({
        businessName: cred.bn,
        email: cred.eml,
        comment: cred.comment || 'Test business account',
        hasPin: !!cred.pin,
      })),
      defaultRecommended: {
        businessName: 'kafe123',
        email: '<EMAIL>',
        comment: 'Default recommended business account',
      },
    };
  }

  static activate(parameters: { businessName?: string; email?: string; password?: string }): any {
    try {
      const { businessName, email, password } = parameters || {};

      // Business credentials for activation
      const credentials = [
        { bn: 'stephen666', eml: '<EMAIL>', pwd: '111111', pin: '2222' },
        { bn: 'kamdar', eml: '<EMAIL>', pwd: '123456', pin: '1111' },
        { bn: 'justcoffee', eml: '<EMAIL>', pwd: '123456', pin: '2222' },
        { bn: 'feida', eml: '<EMAIL>', pwd: '123456', pin: '1111' },
        { bn: 'ancafe', eml: '<EMAIL>', pwd: '123456', pin: '1234' },
        { bn: 'kafe123', eml: '<EMAIL>', pwd: '111111', pin: '1111' },
        { bn: 'cathy6', eml: '<EMAIL>', pwd: '123456', pin: '6721' },
        { bn: 'leonphtestfood', eml: '<EMAIL>', pwd: '111111', pin: '1111', comment: 'BIR, updated by Grace' },
        { bn: 'thteststore', eml: '<EMAIL>', pwd: 'Test123', pin: '3333', comment: 'TH' },
        { bn: 'myteststore', eml: '<EMAIL>', pwd: 'Test123', pin: '1111' },
        { bn: 'birph', eml: '<EMAIL>', pwd: 'test123123', pin: '1111', comment: 'FDI' },
        { bn: 'chunyuanth', eml: '<EMAIL>', pwd: 'Ccy@123456', pin: '' },
        { bn: 'onlytestaccount', eml: '<EMAIL>', pwd: 'test123123', pin: '' },
        { bn: 'kdstest', eml: '<EMAIL>', pwd: '123456', pin: '1111' },
        { bn: 'cafe95425676', eml: '<EMAIL>', pwd: 'Divya123', pin: '2998' },
        { bn: 'productstore', eml: '<EMAIL>', pwd: '123456', pin: '1282' },
      ];

      // If no parameters provided, use default kafe123 account
      if (!businessName && !email && !password) {
        const defaultCred = credentials.find(c => c.bn === 'kafe123');
        if (defaultCred) {
          const store = getPreservedStore();

          // Dispatch activation action
          store.dispatch({
            type: 'activateBusiness',
            payload: {
              businessName: defaultCred.bn,
              email: defaultCred.eml,
              password: defaultCred.pwd,
            },
          });

          // Log activation event
          insertMobileData({
            employeeId: defaultCred.bn,
            result: 'Succeed',
            reason: 'ai_default_activation',
          });

          return {
            message: `Successfully activated default business: ${defaultCred.bn}`,
            businessName: defaultCred.bn,
            email: defaultCred.eml,
            success: true,
          };
        }
      }

      // Find matching credentials
      let matchedCredential = null;

      if (businessName) {
        matchedCredential = credentials.find(c => c.bn.toLowerCase() === businessName.toLowerCase());
      } else if (email) {
        matchedCredential = credentials.find(c => c.eml.toLowerCase() === email.toLowerCase());
      }

      if (!matchedCredential) {
        return {
          error: 'Invalid credentials. Business name or email not found in available accounts.',
          availableAccounts: credentials.map(c => ({ businessName: c.bn, email: c.eml })),
        };
      }

      // Validate password if provided
      if (password && matchedCredential.pwd !== password) {
        return {
          error: 'Invalid password for the selected business account.',
        };
      }

      const store = getPreservedStore();

      // Dispatch activation action
      // store.dispatch({
      //   type: 'activateBusiness',
      //   payload: {
      //     business: matchedCredential.bn,
      //     email: matchedCredential.eml,
      //     password: matchedCredential.pwd,
      //   },
      // });

      store.dispatch(
        activate({
          business: matchedCredential.bn,
          email: matchedCredential.eml,
          password: matchedCredential.pwd,
          onSuccess: {
            callback: payload => {
              // Save store , pad register info for offline usage
              const now = new Date().toISOString();
              payload.res.activatedTime = now;
              store.dispatch(setStoreInfo(payload.res));
              const { receiptNumberStart, receiptDateStart, invoiceNumberStart, subscriptionStatus } = payload.res;

              // remove this from CM-4646
              // Auto toglle on the settings below for new on-board users.
              // const effectiveDate = moment('2023-08-31T00:00:00.000Z'); // 31 Aug 2023
              // if (Boolean(createdTime) && moment(createdTime) > effectiveDate) {
              //   this.props.actions.updateGeneralSettings({ enableCustomerQR: true, enableCustomerShortCut: true });
              // }
              store.dispatch(setSequence({ receiptNumberStart, receiptDateStart, invoiceNumberStart, sourceForLog: 'activation' }));
              store.dispatch(setFreeTrial(subscriptionStatus === SubscriptionStatus.Trial));
              store.dispatch(
                setSyncInfo({
                  lastTrxCancelledFromBOSyncTime: now,
                })
              );
              // Jump to Employee Sign in
            },
          },
        })
      );

      // Log activation event
      insertMobileData({
        employeeId: matchedCredential.bn,
        result: 'Succeed',
        reason: 'ai_manual_activation',
      });

      return {
        message: `Successfully activated business: ${matchedCredential.bn}`,
        businessName: matchedCredential.bn,
        email: matchedCredential.eml,
        comment: matchedCredential.comment || 'Test business account',
        success: true,
      };
    } catch (error) {
      return {
        error: `Failed to activate business: ${error.message}`,
        success: false,
      };
    }
  }

  static deactivate(): any {
    try {
      const store = getPreservedStore();
      const state = store.getState();
      const currentBusinessName = selectBusinessName(state);

      if (!currentBusinessName) {
        return {
          success: false,
          message: 'No business is currently activated.',
          error: 'NO_ACTIVE_BUSINESS',
        };
      }

      // Log the deactivation
      insertMobileData({
        employeeId: selectEmployeeId(state) || '',
        result: 'success',
        reason: `Business deactivated: ${currentBusinessName}`,
      });

      // Dispatch deactivation action
      store.dispatch(deactivateRegister());

      return {
        success: true,
        message: `Successfully deactivated business: ${currentBusinessName}`,
        previousBusiness: currentBusinessName,
      };
    } catch (error) {
      console.error('Error deactivating business:', error);
      return {
        success: false,
        message: 'Failed to deactivate business',
        error: error.message || 'DEACTIVATION_ERROR',
      };
    }
  }

  // MRS Diagnostic Functions
  static getMRSStatus(): any {
    try {
      const store = getPreservedStore();
      const state = store.getState();

      const isEnabledMRS = selectIsEnabledMRS(state);
      const isBOEnabledMRS = selectIsBOEnabledMRS(state);
      const isMaster = selectIsMaster(state);
      const masterState = selectMasterState(state);
      const serverRunning = selectServerRunning(state);
      const clientConnected = selectClientConnected(state);
      const mrsRole = selectMRSRole(state);
      const isClient = selectIsClient(state);

      const status = {
        enabled: isEnabledMRS,
        backOfficeEnabled: isBOEnabledMRS,
        role: mrsRole,
        isMaster,
        isClient,
        masterState,
        serverRunning,
        clientConnected,
        isOnline: masterState === MasterState.ONLINE,
        hasErrors: errorMasterState.includes(masterState),
        isNormalState: normalMasterState.includes(masterState),
      };

      let diagnosis = 'MRS Status: ';
      if (!isEnabledMRS) {
        diagnosis += 'MRS is disabled. Enable it in Settings > Multiple Register Sync.';
      } else if (!isBOEnabledMRS) {
        diagnosis += 'MRS is not enabled in Back Office. Contact support to enable MRS for your account.';
      } else if (status.hasErrors) {
        diagnosis += `MRS has errors. Current state: ${masterState}. `;
        switch (masterState) {
          case MasterState.PID_ERROR:
            diagnosis += 'Data version error detected. Try resetting MRS or contact support.';
            break;
          case MasterState.MULTIPLE_MASTERS:
            diagnosis += 'Multiple local servers detected. Only one register should be the master.';
            break;
          case MasterState.MULTIPLE_MDNS:
            diagnosis += 'Multiple services detected. Check network configuration.';
            break;
          default:
            diagnosis += 'Unknown error state.';
        }
      } else if (masterState === MasterState.OFFLINE) {
        diagnosis += 'MRS is offline. Check network connectivity and ensure all registers are on the same network.';
      } else if (masterState === MasterState.ONLINE) {
        diagnosis += `MRS is online and working properly. Role: ${mrsRole}.`;
      }

      return {
        ...status,
        diagnosis,
        troubleshootingUrl: 'https://care.storehub.com/en/articles/6780008-troubleshoot-multiple-register-sync-new-ios-android',
      };
    } catch (error) {
      console.error('Error getting MRS status:', error);
      return {
        enabled: false,
        error: 'Failed to get MRS status',
        diagnosis: 'Unable to retrieve MRS status. Please try again or contact support.',
      };
    }
  }

  static getMRSNetworkInfo(): any {
    try {
      const store = getPreservedStore();
      const state = store.getState();

      const deviceIp = selectIPAddress(state);
      const serverIp = selectServerIp(state);
      const clientIp = selectClientIp(state);
      const registerId = selectRegisterId(state);
      const registerName = selectRegisterName(state);
      const isNetConnected = selectIsNetConnected(state);

      return {
        deviceIp,
        serverIp,
        clientIp,
        registerId,
        registerName,
        isNetworkConnected: isNetConnected,
        networkDiagnosis: isNetConnected
          ? 'Network connection is active'
          : 'No network connection detected. MRS requires all registers to be on the same network.',
        ipConfiguration: {
          hasDeviceIp: !!deviceIp,
          hasServerIp: !!serverIp,
          hasClientIp: !!clientIp,
          sameNetwork: serverIp && deviceIp ? serverIp.split('.').slice(0, 3).join('.') === deviceIp.split('.').slice(0, 3).join('.') : false,
        },
      };
    } catch (error) {
      console.error('Error getting MRS network info:', error);
      return {
        error: 'Failed to get MRS network information',
        diagnosis: 'Unable to retrieve network information for MRS diagnosis.',
      };
    }
  }

  static getMRSClients(): any {
    try {
      const store = getPreservedStore();
      const state = store.getState();

      const clients = selectMRSClients(state);
      const isMaster = selectIsMaster(state);
      const registerId = selectRegisterId(state);
      const outDatedClient = selectMRSOutDatedClient(state);

      const clientsInfo = clients.map(client => ({
        registerId: client.registerId,
        registerName: client.registerName,
        ip: client.ip,
        isConnected: client.isConnected || false,
        lastSeen: client.updateTime || 'Unknown',
        status: client.isConnected ? 'Connected' : 'Disconnected',
      }));

      return {
        isMaster,
        currentRegisterId: registerId,
        totalClients: clients.length,
        connectedClients: clientsInfo.filter(c => c.isConnected).length,
        disconnectedClients: clientsInfo.filter(c => !c.isConnected).length,
        hasOutdatedClient: outDatedClient,
        clients: clientsInfo,
        diagnosis: isMaster
          ? `This register is the master server with ${clients.length} client(s) registered.`
          : 'This register is a client connected to the master server.',
      };
    } catch (error) {
      console.error('Error getting MRS clients:', error);
      return {
        error: 'Failed to get MRS clients information',
        diagnosis: 'Unable to retrieve client information for MRS diagnosis.',
      };
    }
  }

  static getMRSDataSync(): any {
    try {
      const store = getPreservedStore();
      const state = store.getState();

      const slavePid = selectSlavePid(state);
      const snapshotPid = selectSnapshotPid(state);
      const backupPid = selectBackupPid(state);
      const snapshotVersion = selectSnapshotVersion(state);
      const isMaster = selectIsMaster(state);

      return {
        currentPid: slavePid,
        snapshotPid,
        backupPid,
        snapshotVersion,
        isMaster,
        syncStatus: {
          pidDifference: Math.abs(slavePid - snapshotPid),
          isInSync: slavePid === snapshotPid,
          needsSync: slavePid !== snapshotPid,
        },
        diagnosis:
          slavePid === snapshotPid
            ? 'Data is synchronized across all registers.'
            : `Data sync may be needed. PID difference: ${Math.abs(slavePid - snapshotPid)}. This could indicate pending transactions or sync delays.`,
      };
    } catch (error) {
      console.error('Error getting MRS data sync info:', error);
      return {
        error: 'Failed to get MRS data sync information',
        diagnosis: 'Unable to retrieve data synchronization status.',
      };
    }
  }

  static diagnoseMRSIssues(): any {
    try {
      const mrsStatus = this.getMRSStatus();
      const networkInfo = this.getMRSNetworkInfo();
      const clientsInfo = this.getMRSClients();
      const dataSyncInfo = this.getMRSDataSync();

      const issues = [];
      const recommendations = [];

      // Check if MRS is enabled
      if (!mrsStatus.enabled) {
        issues.push('MRS is not enabled');
        recommendations.push('Enable MRS in Settings > Multiple Register Sync');
      }

      // Check if Back Office has enabled MRS
      if (mrsStatus.enabled && !mrsStatus.backOfficeEnabled) {
        issues.push('MRS is not enabled in Back Office');
        recommendations.push('Contact StoreHub support to enable MRS for your account');
      }

      // Check for error states
      if (mrsStatus.hasErrors) {
        issues.push(`MRS error state: ${mrsStatus.masterState}`);
        switch (mrsStatus.masterState) {
          case MasterState.PID_ERROR:
            recommendations.push('Reset MRS from Settings > Multiple Register Sync > Reset MRS');
            break;
          case MasterState.MULTIPLE_MASTERS:
            recommendations.push('Ensure only one register is set as master. Reset MRS on all registers and set up again.');
            break;
          case MasterState.MULTIPLE_MDNS:
            recommendations.push('Check network configuration. Restart all registers and ensure they are on the same network.');
            break;
        }
      }

      // Check network connectivity
      if (!networkInfo.isNetworkConnected) {
        issues.push('No network connection');
        recommendations.push('Check WiFi connection and ensure all registers are connected to the same network');
      }

      // Check IP configuration
      if (networkInfo.ipConfiguration && !networkInfo.ipConfiguration.sameNetwork) {
        issues.push('Registers may not be on the same network');
        recommendations.push('Ensure all registers are connected to the same WiFi network');
      }

      // Check client connectivity
      if (clientsInfo.disconnectedClients > 0) {
        issues.push(`${clientsInfo.disconnectedClients} client(s) disconnected`);
        recommendations.push('Check network connection on disconnected registers and restart MRS if needed');
      }

      // Check data sync
      if (dataSyncInfo.syncStatus && dataSyncInfo.syncStatus.needsSync) {
        issues.push('Data synchronization may be delayed');
        recommendations.push('Monitor sync status. If issues persist, try resetting MRS');
      }

      const overallStatus = issues.length === 0 ? 'healthy' : issues.length <= 2 ? 'warning' : 'critical';

      return {
        overallStatus,
        issuesFound: issues.length,
        issues,
        recommendations,
        detailedInfo: {
          mrsStatus,
          networkInfo,
          clientsInfo,
          dataSyncInfo,
        },
        troubleshootingSteps: [
          '1. Ensure all registers are connected to the same WiFi network',
          '2. Check that MRS is enabled in Settings > Multiple Register Sync',
          '3. Verify only one register is set as the master server',
          '4. If issues persist, try resetting MRS on all registers',
          '5. Contact StoreHub support if problems continue',
        ],
      };
    } catch (error) {
      console.error('Error diagnosing MRS issues:', error);
      return {
        overallStatus: 'error',
        error: 'Failed to diagnose MRS issues',
        diagnosis: 'Unable to perform MRS diagnosis. Please try again or contact support.',
      };
    }
  }

  static getMRSTroubleshootingGuide(): any {
    return {
      commonIssues: [
        {
          issue: 'MRS shows offline',
          causes: ['Network connectivity issues', 'Registers on different networks', 'Firewall blocking connections'],
          solutions: [
            'Check WiFi connection on all registers',
            'Ensure all registers are on the same network',
            'Restart WiFi router if needed',
            'Check firewall settings',
          ],
        },
        {
          issue: 'Multiple Masters Error',
          causes: ['More than one register set as master', 'Network configuration issues'],
          solutions: ['Reset MRS on all registers', 'Set up MRS again with only one master', 'Ensure proper network configuration'],
        },
        {
          issue: 'Data Version Error (PID Error)',
          causes: ['Data synchronization issues', 'Corrupted MRS data'],
          solutions: ['Reset MRS from Settings', 'Ensure all registers have latest app version', 'Contact support if issue persists'],
        },
        {
          issue: 'Client disconnected',
          causes: ['Network interruption', 'Register went to sleep', 'App crashed'],
          solutions: ['Check network connection', 'Restart the POS app', 'Ensure register stays awake', 'Check power settings'],
        },
      ],
      preventiveMeasures: [
        'Keep all registers on the same stable WiFi network',
        'Ensure all registers have the latest app version',
        'Regularly monitor MRS status',
        'Avoid changing network settings during business hours',
        'Keep registers plugged in and prevent sleep mode',
      ],
      emergencySteps: [
        '1. If MRS fails during business hours, continue using individual registers',
        '2. Manually sync transactions after resolving MRS issues',
        '3. Contact StoreHub support for urgent assistance',
        '4. Document any error messages for support team',
      ],
      supportInfo: {
        helpUrl: 'https://care.storehub.com/en/articles/6780008-troubleshoot-multiple-register-sync-new-ios-android',
        contactSupport: 'Contact StoreHub support through the app or website',
        urgentIssues: 'For urgent issues during business hours, use individual registers and sync later',
      },
    };
  }

  // Tool execution method
  static executeAgentTool(toolName: string, parameters?: any): any {
    try {
      // eslint-disable-next-line sonarjs/max-switch-cases
      switch (toolName) {
        case 'getCurrentPOSInfo':
          return POSAgentTools.getCurrentPOSInfo();
        case 'getCurrentTransactionSummary':
          return POSAgentTools.getCurrentTransactionSummary();
        case 'getStoreStatus':
          return POSAgentTools.getStoreStatus();
        case 'getPrinterStatus':
          return POSAgentTools.getPrinterStatus();
        case 'getNetworkInfo':
          return POSAgentTools.getNetworkInfo();
        case 'getEmployeeInfo':
          return POSAgentTools.getEmployeeInfo();
        case 'getShiftInfo':
          return POSAgentTools.getShiftInfo();
        case 'getProductCatalog':
          return POSAgentTools.getProductCatalog();
        case 'getCustomerInfo':
          return POSAgentTools.getCustomerInfo();
        case 'getPaymentMethods':
          return POSAgentTools.getPaymentMethods();
        // New transaction database tools
        case 'getTransactionHistory':
          return POSAgentTools.getTransactionHistory(parameters);
        case 'getTransactionById':
          return POSAgentTools.getTransactionById(parameters);
        case 'searchTransactions':
          return POSAgentTools.searchTransactions(parameters);
        case 'getTransactionStatistics':
          return POSAgentTools.getTransactionStatistics(parameters);
        case 'getOpenOrders':
          return POSAgentTools.getOpenOrders(parameters);
        case 'getTransactionsByReceiptNumber':
          return POSAgentTools.getTransactionsByReceiptNumber(parameters);
        // New settings control tools
        case 'getCurrentSettings':
          return POSAgentTools.getCurrentSettings();
        case 'updateAutoSyncAfterOpenShift':
          return POSAgentTools.updateAutoSyncAfterOpenShift(parameters);
        case 'getGeneralSettings':
          return POSAgentTools.getGeneralSettings();
        case 'updateGeneralSetting':
          return POSAgentTools.updateGeneralSetting(parameters);
        case 'getAvailableCredentials':
          return POSAgentTools.getAvailableCredentials();
        case 'signIn':
          return POSAgentTools.signIn(parameters);
        case 'signOut':
          return POSAgentTools.signOut();
        case 'getAvailableBusinessCredentials':
          return POSAgentTools.getAvailableBusinessCredentials();
        case 'activate':
          return POSAgentTools.activate(parameters);
        case 'deactivate':
          return POSAgentTools.deactivate();
        case 'getMRSStatus':
          return POSAgentTools.getMRSStatus();
        case 'getMRSNetworkInfo':
          return POSAgentTools.getMRSNetworkInfo();
        case 'getMRSClients':
          return POSAgentTools.getMRSClients();
        case 'getMRSDataSync':
          return POSAgentTools.getMRSDataSync();
        case 'diagnoseMRSIssues':
          return POSAgentTools.diagnoseMRSIssues();
        case 'getMRSTroubleshootingGuide':
          return POSAgentTools.getMRSTroubleshootingGuide();
        // Printer diagnostic tools
        case 'getPrinterDetails':
          return POSAgentTools.getPrinterDetails();
        case 'getPrinterErrors':
          return POSAgentTools.getPrinterErrors();
        case 'getPrinterNetworkInfo':
          return POSAgentTools.getPrinterNetworkInfo();
        case 'diagnosePrinterIssues':
          return POSAgentTools.diagnosePrinterIssues();
        case 'getPrinterTroubleshootingGuide':
          return POSAgentTools.getPrinterTroubleshootingGuide();
        default:
          throw new Error(`Unknown tool: ${toolName}`);
      }
    } catch (error) {
      return {
        error: `Failed to execute tool ${toolName}: ${error.message}`,
      };
    }
  }

  static getPrinterDetails(): any {
    try {
      const store = getPreservedStore();
      const state = store.getState();

      const allPrinters = selectAllPrinters(state);
      const errorPrinters = selectErrorPrinters(state).toJS();
      const kitchenPrinters = selectKitchenPrinters(state);
      const orderSummaryPrinter = selectOrderSummaryPrinter(state);
      const defaultKitchenPrinter = selectDefaultKitchenPrinter(state);

      const formatPrinter = (printer: PrinterConfigType) => {
        const errorCount = errorPrinters[printer.printerId] || 0;

        let connectionStatus = 'Unknown';
        let connectionDetails = {};

        switch (printer.printerConnectType) {
          case PrinterConnectType.LAN:
            connectionStatus = printer.isOnline ? 'Connected' : 'Disconnected';
            connectionDetails = {
              ip: printer.lanIp,
              port: printer.lanPort,
              pingStatus: printer.pingStatus,
              pingWorking: printer.pingWorking,
            };
            break;
          case PrinterConnectType.Bluetooth:
            connectionStatus = printer.isOnline ? 'Paired' : 'Unpaired';
            connectionDetails = {
              macAddress: printer.bluetoothMacAddress,
            };
            break;
          case PrinterConnectType.USB:
            connectionStatus = printer.isOnline ? 'Connected' : 'Disconnected';
            connectionDetails = {
              usbPath: printer.usbPath,
              vendorId: printer.usbVendorId,
              productId: printer.usbProductId,
            };
            break;
        }

        return {
          printerId: printer.printerId,
          name: printer.printerName || 'Unnamed Printer',
          model: printer.printerModelType || 'Unknown Model',
          type: printer.printerConnectType,
          isOnline: printer.isOnline || false,
          connectionStatus,
          connectionDetails,
          roles: {
            isReceiptPrinter: printer.isReceiptPrinter || false,
            isKitchenPrinter: printer.tags && printer.tags.length > 0,
            isLabelPrinter: printer.isLabelPrinter || false,
            isBeepSummaryPrinter: printer.isBeepOrderSummaryPrinter || false,
            isBuiltInPrinter: printer.isBuiltInPrinter || false,
          },
          tags: printer.tags || [],
          errorCount,
          hasErrors: errorCount > 0,
          lastPrintedTime: printer.lastPrintedTime,
          paperWidth: printer.printerPaperWidth,
          serialNumber: printer.serialNumber,
          modifiedDate: printer.modifiedDate,
        };
      };

      const formattedPrinters = allPrinters.map(formatPrinter);

      return {
        success: true,
        totalPrinters: allPrinters.length,
        printers: formattedPrinters,
        configuration: {
          kitchenPrinters,
          orderSummaryPrinter,
          defaultKitchenPrinter,
        },
        summary: {
          receiptPrinters: formattedPrinters.filter(p => p.roles.isReceiptPrinter).length,
          kitchenPrinters: formattedPrinters.filter(p => p.roles.isKitchenPrinter).length,
          onlinePrinters: formattedPrinters.filter(p => p.isOnline).length,
          printersWithErrors: formattedPrinters.filter(p => p.hasErrors).length,
        },
      };
    } catch (error) {
      console.error('Error getting printer details:', error);
      return {
        success: false,
        error: 'Failed to get printer details',
        diagnosis: 'Unable to retrieve printer information. Please try again or contact support.',
      };
    }
  }

  static getPrinterErrors(): any {
    try {
      const store = getPreservedStore();
      const state = store.getState();

      const errorPrinters = selectErrorPrinters(state).toJS();
      const allPrinters = selectAllPrinters(state);
      const totalErrorCount = selectPrinterErrorCount(state);

      if (totalErrorCount === 0) {
        return {
          success: true,
          message: 'No printer errors found',
          totalErrors: 0,
          errorPrinters: [],
        };
      }

      const errorDetails = Object.entries(errorPrinters).map(([printerId, errorCount]) => {
        const printer = allPrinters.find(p => p.printerId === printerId);
        return {
          printerId,
          printerName: printer?.printerName || 'Unknown Printer',
          printerType: printer?.printerConnectType || 'Unknown',
          errorCount: errorCount as number,
          isOnline: printer?.isOnline || false,
          lastPrintedTime: printer?.lastPrintedTime,
          connectionDetails:
            printer?.printerConnectType === PrinterConnectType.LAN
              ? { ip: printer.lanIp, port: printer.lanPort }
              : printer?.printerConnectType === PrinterConnectType.Bluetooth
                ? { macAddress: printer.bluetoothMacAddress }
                : { usbPath: printer.usbPath },
        };
      });

      const commonErrorCauses = [
        'Printer is offline or disconnected',
        'Network connectivity issues',
        'Printer out of paper',
        'Printer hardware malfunction',
        'Incorrect printer configuration',
        'Print queue overflow',
      ];

      const troubleshootingSteps = [
        '1. Check printer power and connections',
        '2. Verify network connectivity for LAN printers',
        '3. Check paper supply and printer status',
        '4. Restart the printer',
        '5. Clear print queue and retry',
        '6. Reconfigure printer settings if needed',
      ];

      return {
        success: true,
        totalErrors: totalErrorCount,
        errorPrinters: errorDetails,
        commonCauses: commonErrorCauses,
        troubleshootingSteps,
        recommendation:
          totalErrorCount > 10
            ? 'High number of print errors detected. Consider checking printer hardware and network stability.'
            : 'Monitor printer status and address connection issues promptly.',
      };
    } catch (error) {
      console.error('Error getting printer errors:', error);
      return {
        success: false,
        error: 'Failed to get printer errors',
        diagnosis: 'Unable to retrieve printer error information.',
      };
    }
  }

  static getPrinterNetworkInfo(): any {
    try {
      const store = getPreservedStore();
      const state = store.getState();

      const allPrinters = selectAllPrinters(state);
      const deviceIp = selectIPAddress(state);
      const isNetConnected = selectIsNetConnected(state);

      const lanPrinters = allPrinters.filter(p => p.printerConnectType === PrinterConnectType.LAN);

      const networkAnalysis = lanPrinters.map(printer => {
        const printerIp = printer.lanIp;
        const sameNetwork = deviceIp && printerIp ? deviceIp.split('.').slice(0, 3).join('.') === printerIp.split('.').slice(0, 3).join('.') : false;

        return {
          printerId: printer.printerId,
          printerName: printer.printerName || 'Unnamed Printer',
          ip: printerIp,
          port: printer.lanPort,
          isOnline: printer.isOnline,
          pingStatus: printer.pingStatus,
          sameNetwork,
          networkIssues: !sameNetwork || (printer.pingStatus && printer.pingStatus > 50),
        };
      });

      const networkIssues = networkAnalysis.filter(p => p.networkIssues);
      const offNetworkPrinters = networkAnalysis.filter(p => !p.sameNetwork);

      let diagnosis = 'Network configuration looks good.';
      if (!isNetConnected) {
        diagnosis = 'Device is not connected to network. Check WiFi or Ethernet connection.';
      } else if (offNetworkPrinters.length > 0) {
        diagnosis = `${offNetworkPrinters.length} printer(s) appear to be on different network. Ensure all devices are on the same network.`;
      } else if (networkIssues.length > 0) {
        diagnosis = `${networkIssues.length} printer(s) have network connectivity issues. Check ping status and connections.`;
      }

      return {
        deviceIp,
        isNetworkConnected: isNetConnected,
        totalLanPrinters: lanPrinters.length,
        printersWithNetworkIssues: networkIssues.length,
        printersOnDifferentNetwork: offNetworkPrinters.length,
        networkAnalysis,
        diagnosis,
        recommendations: [
          'Ensure all printers and POS device are on the same WiFi network',
          'Check router settings and firewall configuration',
          'Verify printer IP addresses are in the same subnet',
          'Test ping connectivity to offline printers',
          'Consider using static IP addresses for printers',
        ],
      };
    } catch (error) {
      console.error('Error getting printer network info:', error);
      return {
        error: 'Failed to get printer network information',
        diagnosis: 'Unable to retrieve network information for printer diagnosis.',
      };
    }
  }

  static diagnosePrinterIssues(): any {
    try {
      const printerStatus = this.getPrinterStatus();
      const printerDetails = this.getPrinterDetails();
      const printerErrors = this.getPrinterErrors();
      const networkInfo = this.getPrinterNetworkInfo();

      const issues = [];
      const recommendations = [];

      // Check if merchant has printer enabled
      if (!printerStatus.summary.merchantHasPrinter) {
        issues.push('Merchant has no printer configured');
        recommendations.push('Enable printer in Settings > Printer > General Settings');
      }

      // Check for offline printers
      if (printerStatus.summary.offlinePrinters > 0) {
        issues.push(`${printerStatus.summary.offlinePrinters} printer(s) are offline`);
        recommendations.push('Check printer power, connections, and network connectivity');
      }

      // Check for print errors
      if (printerStatus.summary.errorJobs > 0) {
        issues.push(`${printerStatus.summary.errorJobs} print job error(s) detected`);
        recommendations.push('Clear print queue and retry failed jobs');
      }

      // Check for network issues
      if (networkInfo.printersWithNetworkIssues > 0) {
        issues.push(`${networkInfo.printersWithNetworkIssues} printer(s) have network connectivity issues`);
        recommendations.push('Check network configuration and ensure all devices are on the same network');
      }

      // Check for missing receipt printer
      const receiptPrinters = printerDetails.printers.filter(p => p.roles.isReceiptPrinter);
      if (receiptPrinters.length === 0 && printerStatus.summary.merchantHasPrinter) {
        issues.push('No receipt printer configured');
        recommendations.push('Configure at least one printer as receipt printer');
      }

      // Check for printers on different networks
      if (networkInfo.printersOnDifferentNetwork > 0) {
        issues.push(`${networkInfo.printersOnDifferentNetwork} printer(s) appear to be on different network`);
        recommendations.push('Ensure all printers and POS device are on the same WiFi network');
      }

      const overallStatus = issues.length === 0 ? 'healthy' : issues.length <= 2 ? 'warning' : 'critical';

      return {
        overallStatus,
        issuesFound: issues.length,
        issues,
        recommendations,
        detailedInfo: {
          printerStatus,
          printerDetails,
          printerErrors,
          networkInfo,
        },
        troubleshootingSteps: [
          '1. Check printer power and physical connections',
          '2. Verify network connectivity for LAN printers',
          '3. Ensure all devices are on the same WiFi network',
          '4. Check printer paper supply and status lights',
          '5. Clear print queue and retry failed jobs',
          '6. Restart printers and POS app if needed',
          '7. Reconfigure printer settings if issues persist',
        ],
        emergencySteps: [
          '1. If receipt printer fails, use backup printer or manual receipts',
          '2. For kitchen printers, use manual order tickets temporarily',
          '3. Document all failed print jobs for later processing',
          '4. Contact StoreHub support for urgent printer issues',
        ],
      };
    } catch (error) {
      console.error('Error diagnosing printer issues:', error);
      return {
        overallStatus: 'error',
        error: 'Failed to diagnose printer issues',
        diagnosis: 'Unable to perform printer diagnosis. Please try again or contact support.',
      };
    }
  }

  static getPrinterTroubleshootingGuide(): any {
    return {
      commonIssues: [
        {
          issue: 'Printer shows offline',
          causes: ['Power issues', 'Network connectivity problems', 'USB/Bluetooth connection issues'],
          solutions: [
            'Check printer power cable and ensure printer is turned on',
            'Verify network connection for LAN printers',
            'Check USB cable connection or Bluetooth pairing',
            'Restart the printer',
            'Check printer status lights for error indicators',
          ],
        },
        {
          issue: 'Print jobs failing',
          causes: ['Printer out of paper', 'Printer hardware malfunction', 'Network timeout', 'Incorrect configuration'],
          solutions: [
            'Check paper supply and reload if necessary',
            'Clear any paper jams',
            'Check printer error lights and status',
            'Clear print queue and retry',
            'Verify printer configuration settings',
          ],
        },
        {
          issue: 'Slow printing or timeouts',
          causes: ['Network congestion', 'Printer overload', 'Large print jobs', 'Hardware limitations'],
          solutions: [
            'Check network stability and speed',
            'Reduce print job size or complexity',
            'Allow printer to cool down if overheated',
            'Update printer firmware if available',
            'Consider upgrading to faster printer model',
          ],
        },
        {
          issue: 'Poor print quality',
          causes: ['Low ink/toner', 'Dirty print head', 'Wrong paper type', 'Printer settings'],
          solutions: [
            'Replace ink cartridge or toner',
            'Clean printer head and rollers',
            'Use correct paper type and size',
            'Adjust print quality settings',
            'Calibrate printer if supported',
          ],
        },
        {
          issue: 'Bluetooth printer connection issues',
          causes: ['Pairing problems', 'Distance limitations', 'Interference', 'Battery issues'],
          solutions: [
            'Re-pair the Bluetooth printer',
            'Ensure printer is within range (typically 10 meters)',
            'Remove interference sources',
            'Check printer battery level',
            'Reset Bluetooth settings if needed',
          ],
        },
        {
          issue: 'LAN printer not found',
          causes: ['Network configuration', 'IP address conflicts', 'Firewall blocking', 'Router issues'],
          solutions: [
            'Check printer IP address and network settings',
            'Ensure printer and POS are on same network',
            'Check firewall and router settings',
            'Try pinging printer IP address',
            'Restart router and printer',
          ],
        },
      ],
      preventiveMeasures: [
        'Regularly check printer paper supply',
        'Keep printers clean and dust-free',
        'Use quality paper and ink/toner',
        'Monitor printer status and error logs',
        'Keep printer firmware updated',
        'Maintain stable network connections',
        'Have backup printers for critical operations',
      ],
      emergencyProcedures: [
        '1. If receipt printer fails during transaction, complete sale and print receipt later',
        '2. Use manual receipt books as backup',
        '3. For kitchen printer failures, write orders manually',
        '4. Document all failed print jobs for later processing',
        '5. Switch to backup printer if available',
        '6. Contact StoreHub support for urgent issues',
      ],
      maintenanceTips: [
        'Clean printer heads monthly',
        'Check and replace paper regularly',
        'Monitor ink/toner levels',
        'Keep printer drivers updated',
        'Test print functionality daily',
        'Maintain proper printer environment (temperature, humidity)',
      ],
      supportInfo: {
        helpUrl: 'https://care.storehub.com/en/articles/printer-troubleshooting',
        contactSupport: 'Contact StoreHub support through the app or website',
        urgentIssues: 'For urgent printer issues during business hours, use manual backup procedures',
        commonErrorCodes: {
          '10110': 'Printing error occurred - Check printer status and paper',
          '10612': 'Printing timeout - Check network connection and printer response',
          '10617': 'Printer connection error - Verify printer connectivity',
        },
      },
    };
  }
}

export class GeminiAIService {
  private model;
  private chatSession: any; // ChatSession from Gemini

  constructor() {
    if (!Config.GEMINI_API_KEY) {
      throw new Error('GEMINI_API_KEY is required. Please add it to your .env file.');
    }
    this.model = genAI.getGenerativeModel({ model: 'gemini-2.0-flash' });
    this.initializeChatSession();
  }

  private initializeChatSession() {
    // Get current POS system information for initial context
    const posInfo = POSAgentTools.getCurrentPOSInfo();

    const systemInstruction = `You are StoreHub AI Assistant, an AI assistant for a Point of Sale (POS) system with access to real-time system information and agent tools.

Current POS System Status:
- Business: ${posInfo.businessName} (${posInfo.storeName})
- Store ID: ${posInfo.storeId}
- Current Employee: ${posInfo.currentEmployeeId || 'Not logged in'}
- Currency: ${posInfo.currency}
- Shift Status: ${posInfo.isShiftOpen ? 'Open' : 'Closed'}
- Network: ${posInfo.isNetworkConnected ? 'Connected' : 'Disconnected'}

Available Navigation Routes:
Main Screens:
- Register: Main POS register/sales screen
- TableLayout: Table management and layout view
- SelectTable: Table selection screen
- Shift: Shift management (open/close shift)
- Sync: Data synchronization
- ShiftReport: Shift reports and analytics
- Settings: Application settings
- Support: Help and support
- Products: Product catalog management
- OpenOrderLists: View open orders
- TableLayoutEdit: Edit table layouts
- SplitOrder: Split order functionality
- Transactions: Transaction history and management

Available Agent Tools:
- getCurrentPOSInfo: Get comprehensive current POS system information
- getCurrentTransactionSummary: Get detailed current transaction information
- getStoreStatus: Get current store operational status
- getPrinterStatus: Get printer connectivity and status information
- getNetworkInfo: Get network connectivity details
- getEmployeeInfo: Get current employee information
- getShiftInfo: Get current shift details
- getProductCatalog: Get available products information
- getCustomerInfo: Get customer management information
- getPaymentMethods: Get available payment options
- getTransactionHistory: Get transaction history with optional filters
- getTransactionById: Get specific transaction details by ID
- searchTransactions: Search transactions with various criteria
- getTransactionStatistics: Get transaction statistics and analytics
- getOpenOrders: Get current open orders
- getTransactionsByReceiptNumber: Get transactions by receipt number
- getCurrentSettings: Get all current POS settings (general, printer, display, table layout)
- getGeneralSettings: Get general POS settings only
- updateAutoSyncAfterOpenShift: Enable/disable Auto-Sync after Open Shift setting
- updateGeneralSetting: Update any general setting with validation
- getAvailableCredentials: Get list of available test accounts for sign in
- signIn: Sign in current user (pin)
- signOut: Sign out current user
- getAvailableBusinessCredentials: Get list of available business accounts for activation
- activate: Activate business with credentials (businessName, email, password)
- deactivate: Deactivate current business
- getMRSStatus: Get Multiple Register Sync (MRS) status and configuration
- getMRSNetworkInfo: Get MRS network configuration and IP information
- getMRSClients: Get information about connected MRS clients/registers
- getMRSDataSync: Get MRS data synchronization status and PID information
- diagnoseMRSIssues: Comprehensive MRS diagnosis with issues and recommendations
- getMRSTroubleshootingGuide: Get detailed MRS troubleshooting guide and common solutions

MRS (Multiple Register Sync) Diagnostic Capabilities:
- Check MRS enablement status (local and back office)
- Monitor master/client role and connection status
- Analyze network configuration and IP addresses
- Track data synchronization status and PID differences
- Identify common MRS issues and provide solutions
- Provide step-by-step troubleshooting guidance
- Detect error states: PID Error, Multiple Masters, Multiple MDNS, Offline
- Monitor client connectivity and outdated client status
- Provide preventive measures and emergency procedures

IMPORTANT - Settings Change Confirmation Rules:
- NEVER make changes to POS settings without explicit user confirmation
- When user requests to change a setting, ALWAYS ask for confirmation first
- Use actionButtons to provide "Confirm" and "Cancel" options
- Only execute setting changes after user explicitly confirms
- For setting changes, provide clear information about what will be changed
- Example confirmation flow:
  1. User: "Turn off auto sync"
  2. AI: "Are you sure you want to disable Auto-Sync after Open Shift? This will prevent automatic data synchronization when opening shifts." + [Confirm] [Cancel] buttons
  3. User clicks [Confirm]
  4. AI: Execute the change using tool_call

Settings that require confirmation:
- updateAutoSyncAfterOpenShift: Changes auto-sync behavior
- updateGeneralSetting: Changes any general POS setting
- signIn: Authentication action that changes current user session
- signOut: Authentication action that ends current user session
- activate: Business activation that changes current business context
- deactivate: Business deactivation that ends current business session
- Any other setting modification tools

Guidelines:
- Classify intent as: navigation, order, help, system, general, tool_call
- Only provide actionButtons when user intent is AMBIGUOUS or when CONFIRMATION is needed
- For clear requests: provide direct answers/actions
- For ambiguous requests: provide 2-4 clarification buttons
- For setting changes: ALWAYS provide confirmation buttons first
- Use real-time POS data in responses
- When you need more specific information, use tool_call intent to request agent tools
- Keep responses concise and helpful

Response Format (JSON):
{
  "intent": "navigation|order|help|general|system|tool_call",
  "response": "Your helpful response",
  "params": {
    "route": "ExactRouteName (only include this when intent is 'navigation' and route is clear)"
  },
  "actionButtons": [
    // Include this array when user intent is ambiguous OR when confirmation is needed for settings changes
    {
      "label": "Button Text",
      "message": "Message to send to chat when clicked",
      "type": "primary|secondary|danger"
    }
  ],
  "toolCall": {
    // Only include when intent is 'tool_call' AND user has confirmed the action
    "toolName": "toolName",
    "parameters": {} // optional parameters for the tool
  }
}

For actionButtons (when clarification or confirmation is needed):
- "label" should be clear, actionable text (e.g., "Confirm", "Cancel", "Yes, Disable", "No, Keep Enabled")
- "message" should be the exact message to send to chat when button is clicked
- For confirmations: use "type": "primary" for confirm actions, "type": "secondary" for cancel actions
- For dangerous actions (like disabling critical features): use "type": "danger" for confirm actions

Route Field Instructions:
- Only include the "params" object with "route" field when intent is "navigation" AND the destination is clear
- Use the EXACT route name from the available routes list above
- If navigation intent is ambiguous (e.g., just "navigate"), do NOT include params.route and provide actionButtons instead

Tool Call Instructions:
- Use "tool_call" intent when you need specific information that requires agent tools
- For setting changes: Only use tool_call AFTER user has confirmed the action
- Include toolName and any necessary parameters
- The app will execute the tool and send results back to you
- Available tools: getCurrentPOSInfo, getCurrentTransactionSummary, getStoreStatus, getPrinterStatus, getNetworkInfo, getEmployeeInfo, getShiftInfo, getProductCatalog, getCustomerInfo, getPaymentMethods, getTransactionHistory, getTransactionById, searchTransactions, getTransactionStatistics, getOpenOrders, getTransactionsByReceiptNumber, getCurrentSettings, getGeneralSettings, updateAutoSyncAfterOpenShift, updateGeneralSetting, getAvailableCredentials, signIn, signOut, getAvailableBusinessCredentials, activate, deactivate, getMRSStatus, getMRSNetworkInfo, getMRSClients, getMRSDataSync, diagnoseMRSIssues, getMRSTroubleshootingGuide, getPrinterDetails, getPrinterErrors, getPrinterNetworkInfo, diagnosePrinterIssues, getPrinterTroubleshootingGuide

Confirmation Examples:
User: "Turn off auto sync"
Response: {
  "intent": "general",
  "response": "Are you sure you want to disable Auto-Sync after Open Shift? This will prevent automatic data synchronization when opening shifts.",
  "actionButtons": [
    {"label": "Yes, Disable", "message": "Confirm disable auto sync after open shift", "type": "danger"},
    {"label": "Cancel", "message": "Cancel auto sync change", "type": "secondary"}
  ]
}

User: "Confirm disable auto sync after open shift"
Response: {
  "intent": "tool_call",
  "response": "Disabling Auto-Sync after Open Shift...",
  "toolCall": {
    "toolName": "updateAutoSyncAfterOpenShift",
    "parameters": {"enabled": false}
  }
}

- MRS troubleshooting: Use getMRSStatus, getMRSNetworkInfo, getMRSClients, getMRSDataSync, diagnoseMRSIssues, getMRSTroubleshootingGuide for Multiple Register Sync issues
- Printer troubleshooting: Use getPrinterStatus, getPrinterDetails, getPrinterErrors, getPrinterNetworkInfo, diagnosePrinterIssues, getPrinterTroubleshootingGuide for printer-related issues
- For printer problems: Check status first, then get detailed info, analyze errors, and provide troubleshooting guidance
- Common printer issues: offline printers, print job failures, network connectivity, paper jams, configuration problems

`;

    // Start chat session with system instruction
    this.chatSession = this.model.startChat({
      history: [
        {
          role: 'user',
          parts: [{ text: systemInstruction }],
        },
        {
          role: 'model',
          parts: [
            {
              text: "I understand. I'm SHAI, your POS assistant. I have access to real-time POS information and can use agent tools to get specific data when needed. I'll help you navigate, manage orders, check system status, and answer questions about your POS system. I'll provide direct answers for clear requests and clarification options when your intent is ambiguous.\n\nIMPORTANT: For any POS settings changes, I will always ask for your confirmation first before making any modifications to ensure system safety and prevent accidental changes.\n\nHow can I help you today?",
            },
          ],
        },
      ],
    });
  }

  // Reset chat session (useful for clearing conversation history)
  resetConversation() {
    this.initializeChatSession();
  }

  // Process tool result and continue conversation
  async processToolResult(toolResult: any): Promise<AIResponse> {
    try {
      const toolResultMessage = `Tool Result for ${toolResult.toolName}:
${JSON.stringify(toolResult.result, null, 2)}

Please provide a helpful response based on this information.`;

      // Send tool result to chat session
      const result = await this.chatSession.sendMessage(toolResultMessage);
      const response = result.response;
      const text = response.text();

      // Parse the JSON response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsedResponse = JSON.parse(jsonMatch[0]);
        return {
          response: parsedResponse.response,
          intent: parsedResponse.intent,
          actionButtons: parsedResponse.actionButtons,
          params: parsedResponse.params,
          toolCall: parsedResponse.toolCall,
        };
      }

      // Fallback if JSON parsing fails
      return {
        response: text,
        intent: 'general',
        actionButtons: [],
      };
    } catch (error) {
      console.error('Gemini AI Tool Result Error:', error);
      throw error;
    }
  }

  async processMessage(userMessage: string): Promise<AIResponse> {
    try {
      // Get fresh POS system information for each message
      const posInfo = POSAgentTools.getCurrentPOSInfo();

      // Add current system status to the message for context
      const contextualMessage = `Current Status: Transaction: ${posInfo.currentTransaction ? `${posInfo.currentTransaction.items?.length || 0} items, Total: ${posInfo.currency} ${posInfo.currentTransaction.display?.total || 0}` : 'No active transaction'}, Shift: ${posInfo.isShiftOpen ? 'Open' : 'Closed'}, Network: ${posInfo.isNetworkConnected ? 'Connected' : 'Disconnected'}

User Message: ${userMessage}

Please respond in JSON format as specified.`;

      // Send message to chat session (maintains conversation history automatically)
      const result = await this.chatSession.sendMessage(contextualMessage);
      const response = result.response;
      const text = response.text();

      // Parse the JSON response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsedResponse = JSON.parse(jsonMatch[0]);
        return {
          response: parsedResponse.response,
          intent: parsedResponse.intent,
          actionButtons: parsedResponse.actionButtons,
          params: parsedResponse.params,
          toolCall: parsedResponse.toolCall,
        };
      }

      // Fallback if JSON parsing fails
      return {
        response: text,
        intent: 'general',
        actionButtons: [],
      };
    } catch (error) {
      console.error('Gemini AI Error:', error);
      throw error;
    }
  }

  // Helper methods to access POS information
  getCurrentTransactionSummary(): string {
    const summary = POSAgentTools.getCurrentTransactionSummary();
    if (!summary.hasTransaction) {
      return summary.message;
    }
    return `Current transaction has ${summary.itemCount} item(s) with total of ${summary.currency} ${summary.total.toFixed(2)}`;
  }

  getStoreStatus(): string {
    const status = POSAgentTools.getStoreStatus();
    return `Store: ${status.businessName}, Shift: ${status.isShiftOpen ? 'Open' : 'Closed'}, Network: ${status.isNetworkConnected ? 'Connected' : 'Disconnected'}`;
  }

  getCurrentPOSInfo(): POSSystemInfo {
    return POSAgentTools.getCurrentPOSInfo();
  }
}

export const geminiAIService = new GeminiAIService();

// Export POSAgentTools for use in sagas
export { POSAgentTools };
