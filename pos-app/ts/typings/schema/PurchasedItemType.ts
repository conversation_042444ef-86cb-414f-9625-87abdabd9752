import { Object } from 'realm';
import { CookingStatus } from '../../utils/kds/status';
import { AppliedPromotionType } from './AppliedPromoitionType';

import { CalculationType } from './CalculationType';

import { TransactionType } from './TransactionType';

export interface ItemKDSInfo {
  cookingStatus?: CookingStatus;
  pendingDate?: Date;
  preparedDate?: Date;
  servedDate?: Date;
  mergedCookingStatus?: boolean;
}
export interface PurchasedItemPlainType {
  adhocDiscount?: number;
  discount?: number;
  id?: string;
  submitId?: string;
  itemType?: string; // ServiceCharge, Discount
  notes?: string;
  options?: string;
  orderingValue?: number;
  originalItemId?: string;
  productId?: string;
  pwdDiscount?: number;
  quantity?: number;
  originalQuantity?: number;
  isOutOfStock?: boolean;
  isVatExempted?: boolean;
  isAmusementTax?: boolean;
  isBasicNecessitiesPH?: boolean;
  seniorDiscount?: number;
  sn?: string;
  subTotal?: number;
  tax?: number;
  taxRate?: number;
  rate?: number;
  taxableAmount?: number;
  taxCode?: string;
  taxExemptAmount?: number;
  zeroRatedSales?: number;
  totalDeductedTax?: number;
  discountInputValue?: number;
  loyaltyDiscount?: number;
  discountType?: string;
  title?: string;
  display?: any;
  total?: number;
  unitPrice?: number;
  owner?: TransactionType;
  promotions?: AppliedPromotionType[];
  itemChannel?: number; // Takeaway 2
  takeawayCharges?: number;
  isTakeaway?: boolean; // for beep
  takeawayCharge?: number; // for beep
  // trackInventory?: boolean;
  displayFullBillDiscountEnable?: boolean;
  isSoloParentDiscountApplicable?: boolean;
  displayFullBillDiscountValue?: number;
  isDiscountEnable?: boolean;
  inputValue?: number;
  return?: any;
  itemLevelDiscount?: any;
  discountValue?: number;
  trackInventory?: boolean;
  medalOfValorDiscount?: any;
  athleteAndCoachDiscount?: any;
  soloParentDiscount?: any;
  cookingStatus?: CookingStatus;
  pendingDate?: Date;
  preparedDate?: Date;
  servedDate?: Date;
  calculation?: CalculationType;
  kitchenStation?: string;
  isServiceChargeNotApplicable?: boolean;
  employeeId?: string;
  employeeName?: string;
}

export interface PurchasedItemType extends PurchasedItemPlainType, Object {}
