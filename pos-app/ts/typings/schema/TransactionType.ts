import { OnlineOrderStatus, OrderChannel } from '../../constants';
import { CookingStatus, CookingStatusType } from '../../utils/kds/status';
import { AppliedPromotionType } from './AppliedPromoitionType';
import { BirInfoType } from './BirInfoType';
import { CalculationType } from './CalculationType';
import { LoyaltyDiscountType } from './LoyaltyDiscountType';
import { PaymentType } from './PaymentType';
import { PurchasedItemType } from './PurchasedItemType';
import { RealmObject } from './RealmObject';

export interface TransactionType extends RealmObject<TransactionType> {
  appVersion?: string;
  cancelledAt?: Date;
  cancelledBy?: string;
  comment?: string;
  createdDate?: Date;
  customerId?: string;
  loyaltyEarned?: number;
  depositAmount?: number;
  discount?: number;
  employeeId?: string;
  headcount?: number;
  invoiceSeqNumber?: number;
  voidNumber?: string;
  isCancelled?: boolean;
  isCompleted?: boolean;
  isDeleted?: boolean;
  isOpen?: boolean;
  isOriginalOnline?: boolean;
  lastSyncTime?: Date;
  manuallyRemovedServiceCharge?: boolean;
  modifiedDate?: Date;
  originalReceiptNumber?: string;
  pax?: number;
  pickUpDate?: Date;
  preOrderBy?: string;
  preOrderDate?: Date;
  preOrderId?: string;
  shiftId?: string;
  shiftIdOfPreOrder?: string;
  shiftIdOfCancel?: string;
  pwdCount?: number;
  pwdDiscount?: number;
  receiptNumber?: string;
  shippingType?: string;
  registerId?: string;
  lastRegisterId?: string;
  registerNumber?: number;
  returnReason?: string;
  returnStatus?: string;
  roundedAmount?: number;
  seniorDiscount?: number;
  seniorsCount?: number;
  sequenceNumber?: number;
  serviceCharge?: number;
  serviceChargeTax?: number;
  serviceChargeRate?: number;
  serviceChargeTaxId?: string;
  subtotal?: number;
  tax?: number;
  channel?: number | OrderChannel;
  taxableSales?: number;
  taxExemptedSales?: number;
  total?: number;
  isBasicNecessitiesPH?: boolean;
  totalDeductedTax?: number;
  transactionId: string;
  transactionType?: string;
  uploadedDate?: Date | string;
  zeroRatedSales?: number;
  items?: PurchasedItemType[];
  totalOutOfStockItemsCount?: number;
  payments?: PaymentType[];
  promotions?: AppliedPromotionType[];
  loyaltyDiscounts?: LoyaltyDiscountType[];
  otherReason?: string;
  pickUpId?: string;
  tableId?: string;
  enableCashback?: boolean;
  totalPaid?: number;
  birInfo?: BirInfoType;
  takeawayCharges?: number;
  takeawayCharge?: number;
  salesChannel?: number;
  isPayByCash?: boolean;
  isPayLater?: boolean;
  isOpenOrder?: boolean;
  isOnlineOrder?: boolean;
  smallOrderFee?: number;
  mrs?: boolean;
  cookingStatus?: CookingStatus;
  cookingStatusType?: CookingStatusType;
  pushKdsDate?: Date | string;
  pushNcsDate?: Date | string;
  cookingEndDate?: Date | string;
  servedTime?: Date | string;
  servedTimeUploaded?: boolean;
  status?: OnlineOrderStatus;
  expectDeliveryDateFrom?: Date | string;
  expectDeliveryDateTo?: Date | string;
  takeawayId?: string;
  calculation?: CalculationType;
  // toJSON?: () => any;
  subOrders?: any;
  isSplittedFromReceiptNumber?: string;
  addonBirCompliance?: any;
}
