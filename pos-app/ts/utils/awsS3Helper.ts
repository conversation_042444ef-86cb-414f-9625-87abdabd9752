import { get } from 'lodash';
import moment from 'moment';
import RNFS from 'react-native-fs';
import { call, put, select, take } from 'redux-saga/effects';
import { requestAWSConfig, updateAWSConfig } from '../actions';
import { AWS_S3_BUCKET, AWS_S3_DB_ACL, AWS_S3_DIAGNOSE_LOG_BUCKET, AWS_S3_REGION } from '../config';
import { IsAndroid } from '../constants';
import { uploadFileToS3WithCheckInvalidSaga } from '../sagas/s3';
import { selectAWSConfig, selectBusinessName, selectRegisterId } from '../sagas/selector';
import { isEmpty } from './validator';
const transferUtility = require('../../libs/react-native-s3').transferUtility;
const { DocumentDirectoryPath } = RNFS;

let isS3Initialized = false;

export const initS3 = function* (forceRefresh = false) {
  // get S3 config from Redux
  const awsConfig = yield call(getAWSConfig, forceRefresh);
  // console.log('initS3 = ', awsConfig);
  if (isEmpty(awsConfig)) {
    return false;
  } else {
    yield call(transferUtility.setupWithBasic, {
      type: 'BASIC',
      region: AWS_S3_REGION,
      access_key: get(awsConfig, 'accessKeyId'), // awsConfig.accessKeyId
      secret_key: get(awsConfig, 'accessKeySecret'), // awsConfig.accessKeySecret
      session_token: get(awsConfig, 'sessionToken'),
    });
    isS3Initialized = true;
    return true;
  }
};

export const getAWSConfig = function* (forceRefresh = false) {
  // console.log('getAWSConfig =====>>>>');
  let awsConfig = yield select(selectAWSConfig);
  const accessKeyId = get(awsConfig, 'accessKeyId');
  const needRefresh = forceRefresh || isEmpty(accessKeyId);

  if (needRefresh) {
    // refresh AWSConfig
    yield put(requestAWSConfig());
    const responseAction = yield take([requestAWSConfig.toString() + '.success', requestAWSConfig.toString() + '.failure']);
    if (responseAction.type === requestAWSConfig.toString() + '.success') {
      awsConfig = get(responseAction, ['payload', 'credentials']);
      if (!isEmpty(awsConfig)) {
        yield put(updateAWSConfig(awsConfig));
      } else {
        awsConfig = null;
      }
    } else {
      console.log('getAWSConfig failed ===>>> ');
      awsConfig = null;
    }
  }

  return awsConfig;
};

export const deleteThumbnailPromise = productId => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  return new Promise((resolve, reject) => {
    const path = DocumentDirectoryPath + `/${productId}.png`;
    RNFS.exists(path)
      .then(exist => {
        if (exist) {
          RNFS.unlink(path).then(() => {
            resolve(undefined);
          });
        } else {
          resolve(undefined);
        }
      })
      .catch(err => {
        console.log(err);
        resolve(err);
      });
  });
};

export const isProductThumbnailExist = async (productId: string, thumbnailThreshold: number) => {
  const filePath = DocumentDirectoryPath + `/${productId}.png`;
  try {
    const exists = await RNFS.exists(filePath);
    if (!exists) {
      return false;
    }
    const stats = await RNFS.stat(filePath);
    return stats.size > thumbnailThreshold;
  } catch (error) {
    return false;
  }
};

export const isProductThumbnailUpToDate = async (productId: string, modifiedTime: number, thumbnailThreshold: number) => {
  const filePath = DocumentDirectoryPath + `/${productId}.png`;
  try {
    const exists = await RNFS.exists(filePath);
    if (!exists) {
      return false;
    }
    const stats = await RNFS.stat(filePath);
    console.log('isProductThumbnailUpToDate 1', stats.size > thumbnailThreshold, stats.size, thumbnailThreshold);
    console.log('isProductThumbnailUpToDate 2', new Date(stats.mtime).getTime() >= modifiedTime);
    return stats.size > thumbnailThreshold && modifiedTime && new Date(stats.mtime).getTime() >= modifiedTime;
  } catch (error) {
    return false;
  }
};

export const s3DownloadThumbnail = function* (storeName, productId, withResult = false, isRetry = false) {
  // console.log('s3DownloadThumbnail ==>>>>');
  let setupS3Success = true;
  if (!isS3Initialized || isRetry) {
    setupS3Success = yield call(initS3, isRetry);
  }
  if (setupS3Success) {
    const key = `${storeName}/products/${productId}/thumbnail.png`;
    const downloadPath = DocumentDirectoryPath + `/${productId}.png`;
    const downloadFunc = IsAndroid && withResult ? transferUtility.downloadWithResult : transferUtility.download;
    const response = yield call(downloadFunc, {
      bucket: AWS_S3_BUCKET,
      key,
      file: downloadPath,
    });
    // console.log(`s3DownloadThumbnail ${isRetry ? 'Retry' : ''} = `, response);
    let needRefreshAWSConfig = false;
    if (!get(response, 'status')) {
      if (IsAndroid) {
        const regex = /Status Code:\s*(\d{3})/;
        const errorCode = get(get(response, 'message', '').match(regex), 1);
        needRefreshAWSConfig = ['403', '400'].includes(errorCode);
      } else {
        // IOS use the expiration to check the access
        const date = get(response, 'date');
        const { expiration } = yield select(selectAWSConfig);
        if (!isEmpty(date) && !isEmpty(expiration)) {
          needRefreshAWSConfig = moment(date).isAfter(moment(expiration).subtract(10, 'minute'));
        }
      }
    }
    // 为了防止发生死循环
    if (needRefreshAWSConfig && !isRetry) {
      return yield call(s3DownloadThumbnail, storeName, productId, withResult, true);
    }

    return response;
  } else {
    return null;
  }
};

export const s3DownloadThumbnailWithResult = function* (storeName, productId) {
  return yield call(s3DownloadThumbnail, storeName, productId, true);
};

export const uploadJsonToS3 = function* (action) {
  const { fileName, data, onSuccess, onFailure } = action;

  const businessName = yield select(selectBusinessName);
  const registerId = yield select(selectRegisterId);
  const filePath = RNFS.DocumentDirectoryPath + '/' + fileName;
  const path = IsAndroid ? `file://${filePath}` : filePath;
  const bucket = AWS_S3_DIAGNOSE_LOG_BUCKET;

  try {
    const jsonData = JSON.stringify(data);
    yield call(RNFS.writeFile, path, jsonData, 'utf8');
    const file = {
      uri: path,
      name: fileName, // "example.json"
      type: 'application/json',
    };
    const options = {
      acl: AWS_S3_DB_ACL,
      keyPrefix: `${businessName}/${registerId}/`,
      bucket: bucket,
      region: AWS_S3_REGION,
      // accessKey: AWS_ACESS_KEY_ID,
      // secretKey: AWS_SECRET_ACCESS_KEY,
      successActionStatus: 201,
    };
    const response = yield call(uploadFileToS3WithCheckInvalidSaga, { type: '', payload: { file, options } });
    if (response.status === 201) {
      console.log('upload json to S3 success');
      onSuccess();
    } else {
      throw new Error('upload json to S3 fail');
    }

    yield call(RNFS.unlink, path);
  } catch (error) {
    console.error(error.message);
    onFailure(error.message);
  }
};
