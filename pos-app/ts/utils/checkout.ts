import { get } from 'lodash';
import { TransactionFlowType } from '../constants';
import { isValidNumber, roundingToAmount, roundTo2DecimalPlaces } from '.';
import { DefaultPaymentOptionType } from '../config/paymentOption';
import { isPreOrderPickUp } from './transaction';

// get conditional rounded value for amount
export const getConditionalRoundingValue = (type: number, amount: number, roundingTo, roundAllTransactions): number => {
  if (type === DefaultPaymentOptionType.Cash || roundAllTransactions) {
    return roundingToAmount(amount, roundingTo);
  } else {
    return amount;
  }
};

// rounding the unpaid value
export const calculateUnpaidTotal = (transactionSession, type: number, roundingTo, roundAllTransactions): number => {
  const total = get(transactionSession, 'total', 0);
  const totalPaid = get(transactionSession, 'totalPaid', 0);
  const transactionType = get(transactionSession, 'transactionType');
  const isPreorder = transactionType === TransactionFlowType.PreOrder;
  const depositAmount = get(transactionSession, 'depositAmount', 0);
  const dueAmount = total - totalPaid - (isPreorder ? depositAmount : 0);
  return getConditionalRoundingValue(type, dueAmount, roundingTo, roundAllTransactions);
};

// get rounded unpaid value or rounded splited value
export const calculatePayAmount = (
  transactionSession,
  type: number = DefaultPaymentOptionType.Cash,
  splitedAmount: number,
  roundingTo,
  roundAllTransactions
): { dueAmount: number; unRoundedAmount: number } => {
  let dueAmount = 0;
  let unRoundedAmount = 0;
  const total = get(transactionSession, 'total', 0);
  const totalPaid = get(transactionSession, 'totalPaid', 0);
  const depositAmount = get(transactionSession, 'depositAmount', 0);
  const transactionType = get(transactionSession, 'transactionType');
  const isPreorder = transactionType === TransactionFlowType.PreOrder;
  if (isPreorder) {
    unRoundedAmount = depositAmount;
    dueAmount = getConditionalRoundingValue(type, unRoundedAmount, roundingTo, roundAllTransactions);
  } else if (isValidNumber(splitedAmount)) {
    dueAmount = getConditionalRoundingValue(type, splitedAmount, roundingTo, roundAllTransactions);
    unRoundedAmount = splitedAmount;
  } else {
    const isPreorderPickUp = isPreOrderPickUp(transactionSession);
    if (isPreorderPickUp) {
      unRoundedAmount = roundTo2DecimalPlaces(Math.max(total - totalPaid, 0));
    } else {
      unRoundedAmount = roundTo2DecimalPlaces(total - totalPaid);
    }
    dueAmount = getConditionalRoundingValue(type, unRoundedAmount, roundingTo, roundAllTransactions);
  }
  return { dueAmount, unRoundedAmount };
};

export const calculateCashShortcut = amount => {
  const nearestInteger = Math.ceil(amount);
  const possibleValues = {};
  possibleValues[nearestInteger] = nearestInteger;
  possibleValues[Math.ceil(amount / 10.0) * 10.0] = Math.ceil(amount / 10.0) * 10.0;
  possibleValues[Math.ceil(amount / 50.0) * 50.0] = Math.ceil(amount / 50.0) * 50.0;
  possibleValues[Math.ceil(amount / 100.0) * 100.0] = Math.ceil(amount / 100.0) * 100.0;

  if (amount < 40) {
    possibleValues[Math.ceil(amount / 20.0) * 20.0] = Math.ceil(amount / 20.0) * 20.0;
  }

  if (amount < 5) {
    possibleValues[Math.ceil(amount / 5.0) * 5.0] = Math.ceil(amount / 5.0) * 5.0;
  }
  const values = Object.values(possibleValues);
  values.sort((a: number, b: number) => a - b);

  return values.slice(0, 3);
};
