import { floor, forEach, get } from 'lodash';
import moment, { MomentInput } from 'moment';

import DAL from '../dal';
import { isEmpty } from './validator';
export const getMoment = inputDate => {
  if (!inputDate) return '';
  return typeof inputDate === 'number' ? moment.unix(inputDate) : moment(inputDate);
};

export const getUtcMoment = inputDate => {
  if (!inputDate) return '';
  return moment.utc(inputDate);
};

export const createDate = (inputDate, format = 'YYYY.MM.DD HH:mm:ss'): string => {
  const date = getMoment(inputDate);
  if (date === '') return '';
  return date.format(format);
};

export const formtUtcData = (inputDate, format = 'DD MMM YYYY'): string => {
  const date = getUtcMoment(inputDate);
  if (date === '') return '';
  return date.format(format);
};

export const getLatestModifiedTime = (lastModifiedTime = '', payload) => {
  let latestModifiedTime = lastModifiedTime;
  if (Array.isArray(payload)) {
    forEach(payload, function (item, index: number, payload) {
      if (index === 0) {
        latestModifiedTime = item.modifiedTime;
      }
      if (index >= payload.length - 1) {
        return latestModifiedTime;
      }
      const nextItem = payload[index + 1];
      if (moment(latestModifiedTime).isBefore(nextItem.modifiedTime)) {
        latestModifiedTime = nextItem.modifiedTime;
      }
    });
  }
  return latestModifiedTime;
};

export type GeneralDateTime = Date | moment.Moment | string | undefined | null;
export const isBefore = (inputDate1: GeneralDateTime, inputDate2: GeneralDateTime) => {
  if (!inputDate1 || !inputDate2) {
    return false;
  }
  const moment1: moment.Moment = typeof inputDate1 === 'number' ? moment.unix(inputDate1) : moment(inputDate1);
  const moment2: moment.Moment = typeof inputDate2 === 'number' ? moment.unix(inputDate2) : moment(inputDate2);
  return moment1.isBefore(moment2);
};

export const getHoursAndMinutesTimeSpage = (startTime, endTime) => {
  const start = moment(startTime);
  const end = moment(endTime);
  const diff = end.diff(start);
  if (diff < 0) {
    return '0hr 0m';
  }
  if (!start.isValid() || !end.isValid()) {
    return 'NA:NA';
  }
  const dur = moment.duration(diff, 'ms');
  const hours: any = floor(dur.asHours());
  const mins: any = floor(dur.asMinutes()) - hours * 60;
  // if (hours < 10) {
  //   hours = '0' + hours;
  // }
  // if (mins < 10) {
  //   mins = '0' + mins;
  // }

  return `${hours}hr ${mins}m`;
};

export const getHoursAndMinutesTimeSpan = (startTime, endTime) => {
  const start = moment(startTime);
  const end = moment(endTime);
  const diff = end.diff(start);
  if (diff < 0 || !start.isValid() || !end.isValid()) {
    return 'NA:NA';
  }
  const dur = moment.duration(diff, 'ms');
  const hours: any = floor(dur.asHours());
  const mins: any = floor(dur.asMinutes()) - hours * 60;
  const seconds: any = floor(dur.asSeconds()) - hours * 60 * 60 - mins * 60;

  let timeSpan = '';
  if (hours > 0) {
    timeSpan = `${hours}h ${mins}m ${seconds}s`;
  } else {
    timeSpan = `${mins}m ${seconds}s`;
  }
  return timeSpan;
};

export const getIOSStringFromDate = date => {
  if (!date) {
    return '';
  }

  if (typeof date === 'string') {
    return date;
  }

  if (date.toISOString) {
    return date.toISOString();
  }

  return '';
};

export const getAlreadyCloseZReading = (lastZReadingCloseTime, operationHours) => {
  return false;
};

export const getCurOperationDayTime = operationHours => {
  const operationEndTimeToday = moment().set('hour', Number(operationHours)).set('minute', 0).set('second', 0).set('millisecond', 0);
  const curOperationStartTime = operationEndTimeToday.clone().subtract(1, 'day');
  const curOperationEndTime = operationEndTimeToday.clone();
  if (moment().isAfter(operationEndTimeToday)) {
    curOperationStartTime.add(1, 'day');
    curOperationEndTime.add(1, 'day');
  }
  return { curOperationStartTime, curOperationEndTime };
};

export const getLastOperationDayTime = operationHours => {
  const operationEndTimeToday = moment().set('hour', Number(operationHours)).set('minute', 0).set('second', 0).set('millisecond', 0);
  const lastOperationStartTime = operationEndTimeToday.clone().subtract(1, 'day');
  const lastOperationEndTime = operationEndTimeToday.clone();
  // If the current time has passed today's operation time, it means that it should have reached the next cycle
  if (moment().isSameOrBefore(operationEndTimeToday)) {
    lastOperationStartTime.subtract(1, 'day');
    lastOperationEndTime.subtract(1, 'day');
  }
  return { lastOperationStartTime, lastOperationEndTime };
};

export const isPreviousZReadingIncludedUncountedTrx = (lastZReadingCloseTime, operationHours, registerObjectId) => {
  // If POS detected there are transactions created after Zreading last close time, and before the previous operation close time (yesterday)
  const { lastOperationEndTime } = getLastOperationDayTime(operationHours);
  let previousZReadingIncludedUncountedTrx = false;
  if (!isEmpty(lastZReadingCloseTime) && moment(lastZReadingCloseTime).isBefore(lastOperationEndTime)) {
    const uncountedTrxs = DAL.getTransactionsByDateRange(registerObjectId, moment(lastZReadingCloseTime).toDate(), lastOperationEndTime.toDate());
    previousZReadingIncludedUncountedTrx = uncountedTrxs && uncountedTrxs.length > 0;
  }
  return previousZReadingIncludedUncountedTrx;
};

export const isLastZReadingNotClose = (lastZReadingCloseTime, operationHours, registerObjectId) => {
  let lastZReadingNotClose = false;
  const { lastOperationStartTime, lastOperationEndTime } = getLastOperationDayTime(operationHours);
  const operationEndTimeBeforeTheLastOne = lastOperationStartTime;
  let needCloseFrom: any = operationEndTimeBeforeTheLastOne;
  let needCloseTo;

  if (isEmpty(lastZReadingCloseTime)) {
    // if lastZReadingCloseTime is empty, then means the register never close the z-reading
    // then we can only through the order's create time to compare with the last operation time
    const firstLocalTransaction = DAL.getFirstTransaction(registerObjectId);
    if (firstLocalTransaction) {
      const firstTransactionCreateDate = get(firstLocalTransaction, 'createdDate');
      if (!isEmpty(firstTransactionCreateDate)) {
        needCloseFrom = moment(firstTransactionCreateDate);
        lastZReadingNotClose = needCloseFrom.isSameOrBefore(lastOperationEndTime);
      }
    }
  } else {
    needCloseFrom = moment(lastZReadingCloseTime);
    lastZReadingNotClose = needCloseFrom.isSameOrBefore(operationEndTimeBeforeTheLastOne);
  }
  if (lastZReadingNotClose) {
    needCloseFrom = needCloseFrom.format('DD/M/YYYY hh:mm:ss A');
    needCloseTo = lastOperationEndTime.format('DD/M/YYYY hh:mm:ss A');
  }
  return { lastZReadingNotClose, needCloseFrom, needCloseTo };
};

export const isCurZReadingClosed = (lastZReadingCloseTime, operationHours) => {
  let curZReadingClosed = false;
  if (!isEmpty(lastZReadingCloseTime)) {
    const { lastOperationEndTime } = getLastOperationDayTime(operationHours);
    curZReadingClosed = moment(lastZReadingCloseTime).isAfter(moment(lastOperationEndTime));
  }
  return curZReadingClosed;
};

export const isLastShiftNotClosed = operationHours => {
  const currentShift = DAL.getLastShift();
  let lastShiftNotClosed = false;
  if (currentShift !== undefined && (currentShift.closeTime === undefined || currentShift.closeTime === null)) {
    const { lastOperationEndTime } = getLastOperationDayTime(operationHours);
    lastShiftNotClosed = moment(currentShift.openTime).isSameOrBefore(lastOperationEndTime);
  }
  return lastShiftNotClosed;
};

export const getLTTime = (date: MomentInput) => moment(date).format('LT');

export const generateDatePickerData = () => {
  const date = [];
  const curYear = moment().year();
  const curMonth = moment().month() + 1;
  const curDay = moment().date();
  for (let i = curYear - 100; i <= curYear; i++) {
    const months = [];
    const month = i == curYear ? curMonth : 12;
    for (let j = 1; j <= month; j++) {
      const days = [];
      const isCurMonth = i == curYear && curMonth == j;
      if (j === 2) {
        const day = isCurMonth ? curDay : 28;
        for (let k = 1; k <= day; k++) {
          if (k < 10) {
            days.push('0' + k);
          } else {
            days.push(k);
          }
        }
        // Leap day for years that are divisible by 4, such as 2000, 2004
        if (i % 4 === 0 && !isCurMonth) {
          days.push(29);
        }
      } else if (j in { 1: 1, 3: 1, 5: 1, 7: 1, 8: 1, 10: 1, 12: 1 }) {
        const day = isCurMonth ? curDay : 31;
        for (let k = 1; k <= day; k++) {
          if (k < 10) {
            days.push('0' + k);
          } else {
            days.push(k);
          }
        }
      } else {
        const day = isCurMonth ? curDay : 30;
        for (let k = 1; k <= day; k++) {
          if (k < 10) {
            days.push('0' + k);
          } else {
            days.push(k);
          }
        }
      }
      const _month = {};
      if (j < 10) {
        _month['0' + j] = days;
      } else {
        _month[j] = days;
      }
      months.push(_month);
    }
    const _date = {};
    _date[i] = months;
    date.push(_date);
  }
  return date;
};

export const createDatePickerData = (byDefaultDate = moment(), pickerData = generateDatePickerData()) => {
  const byDefaultYear = byDefaultDate.year();
  const byDefaultMonth = byDefaultDate.month() + 1;
  const byDefaultDay = byDefaultDate.date();
  return {
    pickerData,
    selectedValue: [byDefaultYear, byDefaultMonth < 10 ? '0' + byDefaultMonth : byDefaultMonth, byDefaultDay < 10 ? '0' + byDefaultDay : byDefaultDay],
  };
};

export const formatFromToDate = (from, to, timezone) => {
  const format = 'Do MMM, h:mma';
  const fromTz = moment(from).tz(timezone);
  const fromDate = fromTz.format(format);
  let message = `${fromDate}`;
  if (to) {
    const toTz = moment(to).tz(timezone);
    let toDate = toTz.format(format);
    if (fromDate !== toDate) {
      const isSameDay = fromTz.isSame(toTz, 'day');
      if (isSameDay) {
        toDate = toTz.format('h:mma');
      }
      message = `${fromDate} - ${toDate}`;
    }
  }
  return message;
};
