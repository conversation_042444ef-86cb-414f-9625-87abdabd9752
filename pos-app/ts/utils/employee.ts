import { getUnNullValue } from '.';
import DAL from '../dal';
import { isEmpty } from './validator';

export const formatEmployeeName = employeeId => {
  let employeeName = '';
  if (employeeId) {
    const namesArr = [];
    const employee = DAL.getEmployeeById(employeeId);
    const firstName = getUnNullValue(employee, 'firstName', '');
    const lastName = getUnNullValue(employee, 'lastName', '');
    if (!isEmpty(firstName)) namesArr.push(firstName);
    if (!isEmpty(lastName)) namesArr.push(lastName);
    if (namesArr.length > 0) {
      employeeName = namesArr.join(' ');
    }
  }
  return employeeName;
};

export const formatNameOfEmployee = employee => {
  if (employee) {
    const namesArr = [];
    const firstName = getUnNullValue(employee, 'firstName', '');
    const lastName = getUnNullValue(employee, 'lastName', '');
    if (!isEmpty(firstName)) {
      namesArr.push(firstName);
    }
    if (!isEmpty(lastName)) {
      namesArr.push(lastName);
    }
    return namesArr.join(' ');
  }
  return '';
};
