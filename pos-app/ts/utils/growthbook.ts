import { t } from '../constants';
import { LogLevel } from './logComponent/local-logging/libs/NativeFileLogger';

export interface GBAttr {
  id: string; // unique
  deviceId: string;
  appPlatform: string;
  appVersion: string;
  mobileVersion: string; // add 1.72.0, please follow the SemVer naming convention
  osVersion: string; // add 1.72.0
  product: 'RN POS iOS' | 'RN POS Android';
  country?: string;
  business?: string;
  phone?: string;
  deviceModel?: string;
  browser?: string;
  sessTid?: string; // session id
  permTid?: string; // permanent Id
  enableCashback?: boolean; // pos
  buildNumber?: string;
  mallIntegrationChannel?: string;
  registerId?: string;
  registerNumber?: number;
  mobileStoreId?: string;
  sn?: string;
  firmwareVersion?: string;
  storeIndustry?: string;
}

export enum CM_3296Value {
  unset = -1,
  disable = 0,
  enable = 1,
}

export enum GBFeature {
  CM_3296 = 'cm-3296_reduce-friction-for-redemption',
  CM_3517 = 'cm-3517_storehub-deal-club',
  CM_3591 = 'cm-3591_storehub-kds',
  CM_3509 = 'cm-3509_storehub-support-phone-number',
  CM_4170 = 'cm-4170_dynamic_beep_qr',
  CM_4760 = 'cm-4760_enable-face-snapshot-android',
  CM_5028 = 'cm-5028_enable-auto-assign-default-network',
  CM_4994 = 'cm-4994_storehub-ncs',
  CM_4611 = 'cm-4611_storehub-new-cfd',
  CM_5443 = 'cm-5443_view-local-settings-on-ist',
  CM_6364 = 'cm-6364_xendit-payment',
  CM_5949 = 'cm-5949_e-invoice',
  CM_6676 = 'cm-6676_product-layout-revamp',
  CM_6669 = 'cm-6669_cfd_beep_membership_cashback',
  CM_7327 = 'CM-7327_sentry',
  CM_4068 = 'cm-4068_mrs-data-version',
  CB_9607 = 'CB-9607_allow-merchants-to-activate-subscription-by-themselves',
  CM_7515 = 'CM-7515_KB_Artical',
  CM_7829 = 'cm-7829_pos-storage-statistics',
  CM_7980 = 'cm-7980_optimization',
  CM_8239 = 'cm-8239_transaction-db-auto-clean',
  CM_8309 = 'cm-8309_new-product-sync-flow',
  CM_8378 = 'cm-8378_track-api-response-time',
  CM_8440 = 'cm-8440_enable-e-invoice-beep-and-webstore',
  CM_8420 = 'cm-8420_enable-e-invoice-auto-print-option',
  CM_8521 = 'cm-8521_mrs-check-client-orders',
  CM_6713 = 'cm-6713_imin-scanner-fix',
  CM_5779 = 'cm-5779_auto-reprint-by-udp',
  CM_8284 = 'cm-8284_advance-network-quality-monitoring',
  CM_8683 = 'cm-8683_new-scanner-flow',
  CM_8681 = 'cm-8681_track-download-thumbnail',
  CM_8310 = 'CM-8310_void_receipt',
  CM_8910 = 'cm-8910_track_print_not_fully',
  CM_8423 = 'cm-8423_storage-auto-clean',
  CM_8927 = 'cm-8927_allow-pushy',
  CM_9039 = 'cm-9039_unit-price-rounding',
  CM_9366 = 'cm-9366_salesperson-assignment',
  CM_9076 = 'cm-9076_async-webview-printing-flow',
  CM_9551 = 'cm-9551_kds-landing-page-url',
  CM_9659 = 'cm-9659_network_reachability',
  CM_9450 = 'cm-9450_local-logging',
  CM_9538 = 'cm-9538_setting-tag',
  CM_9506 = 'cm-9506_insufficient-storage-warning',
  CM_9966 = 'cm-9966_storehub-pos-ai-assistant',
}

export enum ScannerFix {
  Default = -1,
  Disabled,
  Enabled,
}

export type Text = {
  bold: boolean;
  body: string;
};

export type LocalizedMessage = {
  country: string;
  messages: Text[];
  localization?: Text[];
};

export type SupportPhoneNumber = {
  visible: boolean;
  messages: LocalizedMessage[];
};

export const disabledSupportPhoneNumberMessages: SupportPhoneNumber = {
  visible: false,
  messages: [],
};

export const defaultSupportPhoneNumberMessages: SupportPhoneNumber = {
  visible: true,
  messages: [
    {
      country: 'MY',
      messages: [
        { bold: true, body: 'For more assistance, please contact StoreHub Careline:' },
        { bold: false, body: '+60330995895' },
        { bold: false, body: '' },
        { bold: true, body: 'Support hour:' },
        { bold: false, body: '9:00AM - 6:00PM, Monday - Sunday' },
      ],
    },
    {
      country: 'PH',
      messages: [
        { bold: true, body: 'For more assistance, please contact StoreHub Careline:' },
        { bold: false, body: '+63282713778' },
        { bold: false, body: '' },
        { bold: true, body: 'Support hour:' },
        { bold: false, body: '9:00AM - 6:00PM, Monday - Sunday' },
      ],
    },
    {
      country: 'TH',
      messages: [
        { bold: true, body: 'For more assistance, please contact StoreHub Careline:' },
        { bold: false, body: '+6621712526' },
        { bold: false, body: '' },
        { bold: true, body: 'Support hour:' },
        { bold: false, body: '9:30AM - 6:30PM, Monday - Sunday' },
      ],
      localization: [
        { bold: true, body: 'หากต้องการความช่วยเหลือเพิ่มเติม กรุณาติดต่อ ทีมสโตร์ฮับ Customer Care:' },
        { bold: false, body: '+6621712526' },
        { bold: false, body: '' },
        { bold: true, body: 'เวลาทำการ จันทร์ - อาทิตย์:' },
        { bold: false, body: '9.30 - 18.30 น. (ไม่เว้นวันหยุดราชการ)' },
      ],
    },
  ],
};

export const defaultSentryConfig = {
  enableAutoSessionTracking: true,
  enableAppStartTracking: true,
  enableNativeFramesTracking: true,
  enableStallTracking: true,
  tracesSampleRate: 0.01,
  _experiments: {
    profilesSampleRate: 0.1,
    replaysSessionSampleRate: 0.01,
    replaysOnErrorSampleRate: 1,
  },
  enableUserInteractionTracing: true,
  enableRoutingInstrumentation: true,
  sessionReplayConfig: {
    maskAllText: false,
    maskAllImages: false,
    maskAllVectors: false,
  },
};

export const defaultOptimizationConfig = {
  enableNewTransactions: false,
  enableNewShiftReport: false,
  recordGQLInfo: false,
};

export interface TrackApiResponseTimeConfigType {
  enabled: boolean;
  responseTimeThreshold: number; // in milliseconds
}

export const defaultApiResponseTimeConfig: TrackApiResponseTimeConfigType = {
  enabled: false,
  responseTimeThreshold: 5000, // 5 seconds
};

export interface TransactionDBAutoCleanConfigType {
  enabled: boolean;
  transactionThreshold: number;
  shiftThreshold: number;
  interval: number;
  delay: number;
}

export const defaultTransactionDBAutoCleanConfig: TransactionDBAutoCleanConfigType = {
  enabled: false,
  transactionThreshold: 12000,
  shiftThreshold: 90,
  interval: 10,
  delay: 3600,
};

export interface NewProductSyncFlowType {
  enabled: boolean;
  pageSize: number;
  asyncThumbnail: boolean;
  asyncThumbnailForSync: boolean;
  asyncThumbnailUpdatedOnly: boolean;
  showIndicator: boolean;
  warnSyncFailed: boolean;
  thumbnailThreshold: number;
}

export const defaultNewProductSyncFlowConfig: NewProductSyncFlowType = {
  enabled: false,
  pageSize: 10000,
  asyncThumbnail: true,
  asyncThumbnailForSync: true,
  asyncThumbnailUpdatedOnly: true,
  showIndicator: true,
  warnSyncFailed: true,
  thumbnailThreshold: 0.01,
};

export enum NetworkQualityMonitoringType {
  Never = 'never',
  SignOut = 'signOut',
  SignIn = 'signIn',
  Always = 'always',
}

export type NetworkQualityMonitoring = {
  enabled: boolean;
  type: NetworkQualityMonitoringType;
  continuous: boolean;
  interval: number;
  delay: number;
};

export const defaultNetworkQualityMonitoring: NetworkQualityMonitoring = {
  enabled: false,
  type: NetworkQualityMonitoringType.Never,
  continuous: false,
  interval: 3600,
  delay: 3600,
};

export interface StorageAutoCleanConfigType {
  enabled: boolean;
  cleanCpuProfile: boolean;
}

export const defaultStorageAutoCleanConfig: StorageAutoCleanConfigType = {
  enabled: false,
  cleanCpuProfile: false,
};

const EMPTY_JSON_VALUE = {};
export type PushyVersionItem = {
  mobileVersion: string;
  hotfixLabel: string;
};

export type PushyConfig = {
  data: PushyVersionItem[] | [];
};

export const defaultPushyConfig: PushyConfig = {
  data: [],
};

export const defaultKdsLandingPageUrls = {
  my: 'https://lp.storehub.com/my/en/kitchen-display-system-bo-lp/?utm_source=pos&utm_medium=pos-linkout&utm_campaign=get-started',
  ph: 'https://lp.storehub.com/ph/en/kitchen-display-system-bo-lp/?utm_source=pos&utm_medium=pos-linkout&utm_campaign=get-started',
  th: 'https://lp.storehub.com/th/th/kitchen-display-system-bo-lp/?utm_source=pos&utm_medium=pos-linkout&utm_campaign=get-started',
  default: 'https://lp.storehub.com/my/en/kitchen-display-system-bo-lp/?utm_source=pos&utm_medium=pos-linkout&utm_campaign=get-started',
};

export const defaultNetworkReachabilityConfig = {
  reachabilityUrl: 'https://www.google.com',
  reachabilityCode: 200,
  useNativeReachability: false,
};

export const DefaultLocalLoggingConfig = {
  enabled: false,
  logLevel: LogLevel.Warning,
  maximumFileSize: 1024 * 1024 * 5, // 5MB
  maximumNumberOfFiles: 15,
  uploadKey: '',
  deleteKey: '',
  mailPin: '',
  mailAddress: '',
};

export type LocalLoggingRemoteConfig = typeof DefaultLocalLoggingConfig;
export const defaultSettingTagConfig = {
  BeepDelivery: {
    show: true,
    label: t('Pause Delivery Here'),
  },
  NewCFD: {
    show: true,
    label: t('NEW'),
  },
};

export const defaultInsufficientStorageWarning = {
  enabled: true,
  warningThreshold: 500,
  criticalThreshold: 200,
};

export const GBFeatureDefaultValue = new Map<GBFeature, unknown>([
  [GBFeature.CM_3296, -1],
  [GBFeature.CM_3517, EMPTY_JSON_VALUE],
  [GBFeature.CM_3591, false],
  [GBFeature.CM_3509, disabledSupportPhoneNumberMessages],
  [GBFeature.CM_4170, false],
  [GBFeature.CM_4760, false],
  [GBFeature.CM_5028, false],
  [GBFeature.CM_4994, false],
  [GBFeature.CM_4611, false],
  [GBFeature.CM_5443, false],
  [GBFeature.CM_6364, false],
  [GBFeature.CM_5949, false],
  [GBFeature.CM_6676, false],
  [GBFeature.CM_6669, false],
  [GBFeature.CM_7327, defaultSentryConfig],
  [GBFeature.CM_4068, EMPTY_JSON_VALUE],
  [GBFeature.CB_9607, false],
  [GBFeature.CM_7515, 'https://support.storehub.com'],
  [GBFeature.CM_7829, false],
  [GBFeature.CM_7980, defaultOptimizationConfig],
  [GBFeature.CM_8239, defaultTransactionDBAutoCleanConfig],
  [GBFeature.CM_8309, defaultNewProductSyncFlowConfig],
  [GBFeature.CM_8378, defaultApiResponseTimeConfig],
  [GBFeature.CM_8440, false],
  [GBFeature.CM_8420, false],
  [GBFeature.CM_8521, EMPTY_JSON_VALUE],
  [GBFeature.CM_6713, ScannerFix.Default],
  [GBFeature.CM_8284, defaultNetworkQualityMonitoring],
  [GBFeature.CM_8683, false],
  [GBFeature.CM_8681, false],
  [GBFeature.CM_8310, false],
  [GBFeature.CM_8910, EMPTY_JSON_VALUE],
  [GBFeature.CM_8423, defaultStorageAutoCleanConfig],
  [GBFeature.CM_8927, defaultPushyConfig], // Pushy Hotfix Toggle
  [GBFeature.CM_9039, false],
  [GBFeature.CM_9366, false],
  [GBFeature.CM_9076, false],
  [GBFeature.CM_9551, defaultKdsLandingPageUrls],
  [GBFeature.CM_9659, defaultNetworkReachabilityConfig],
  [GBFeature.CM_9450, DefaultLocalLoggingConfig],
  [GBFeature.CM_9506, defaultInsufficientStorageWarning],
  [GBFeature.CM_9966, false],
  [GBFeature.CM_9538, defaultSettingTagConfig],
  [GBFeature.CM_5779, EMPTY_JSON_VALUE],
]);
