import currency from 'currency.js';
import _, { get, isArray, isNumber, isString, parseInt } from 'lodash';
import { useSelector } from 'react-redux';
import { ENVIRONMENT, EnvironmentType, getStoreState } from '../config';
import { t } from '../constants';
import { LocalCountryMapType } from '../reducers/localCountryMap';
import { selectCurrency, selectLocalCountryMap } from '../sagas/selector';
import { getMoment } from './datetime';
import { parse } from './json';
import { isEmpty } from './validator';

/**
 * 数组内查询
 * @param   {array}      array
 * @param   {String}    id
 * @param   {String}    keyAlias
 * @return  {Array}
 */
export const queryArray = (array, key, keyAlias = 'key') => {
  if (!(array instanceof Array)) {
    return null;
  }
  const item = array.filter(_ => _[keyAlias] === key);
  if (item.length) {
    return item[0];
  }
  return null;
};

/*
  Round amount by roundingTo
*/
export const roundingToAmount = (amount, rounding) => {
  let roundedAmount;
  if (rounding > 0) {
    roundedAmount = Number((Math.round((amount * 100) / (rounding * 100)) * rounding).toFixed(2));
  } else {
    roundedAmount = amount;
  }
  return roundedAmount;
};

export const roundingSum = (amounts: number[],precision = 2):number => {
  let value = currency(0);
  if (isArray(amounts)) {
    for (let index = 0; index < amounts.length; index++) {
      const amount = amounts[index];
      value = value.add(amount);
    }
  }
  return parseFloat(value.value.toFixed(precision));
};

export const roundTo2DecimalPlaces = value => {
  value = value * 100;
  value = Math.round(value);
  value = value / 100;
  if (Math.abs(value) < 0.001) {
    return 0;
  }
  return value;
};

export const numericValue = (text, prefix?) => {
  // Only for display currency number
  let value = isValidNumber(text) ? `${localeNumber(text)}` : '0.00';
  if (Boolean(prefix) && Number(value) > 0) {
    value = `${prefix} ${value}`;
  }
  return value;
};

export const localeNumber = value => {
  return currency(value).format();
};

export const formatCurrency = (value, exactPrecision?: boolean) => {
  if (exactPrecision && value < 1) {
    return formatCurrencyOnValuePrecision(value);
  }
  return currency(value).format();
};

export const formatCurrencyOnValuePrecision = (value1, minPrecision: number = 2, maxPrecision: number = 6) => {
  const value = Math.floor(value1 * 1000000) / 1000000;
  const parts = value.toString().split('.');
  const decimals = parts.length > 1 ? parts[1].length : 0;

  return currency(value, { precision: Math.max(decimals, minPrecision) }).format();
};

export const checkNumberInput = (value, callback) => {
  if (!isNaN(value)) {
    if (value.length === 0) value = '0'; // Default to 0
    if (!(value.endsWith('.') || value.endsWith('.0'))) value = parseFloat(value);
    callback(value);
  } else if (value === '-') {
    callback(value);
  }
};

export const checkPositiveIntInput = (value, callback) => {
  value = convertToNumber(String(value).replace(/[^\d]+/, ''));
  callback(value);
};
export const checkNumberInputWithoutDefault = (value, callback) => {
  if (!isNaN(value)) {
    if (value.trim().length === 0) callback(null); // Default to 0
    if (!(value.endsWith('.') || value.endsWith('.0'))) value = parseFloat(value);
    callback(value);
  } else if (value === '-') {
    callback(value);
  }
};

export const getVariableFixedNumber = (value, fix = 2): number => {
  if (isValidNumber(value) && Number(value) !== 0) {
    const valueStrArr = String(value).split('.');
    if (valueStrArr.length > 1) {
      if (valueStrArr[1].length > fix) {
        return Number(Number(value).toFixed(fix));
      } else {
        return Number(value);
      }
    }
    return Number(value);
  } else {
    return 0;
  }
};

export const checkQuantityNumberInput = (value, callback) => {
  if (!isNaN(value)) {
    if (value.length === 0) value = '0'; // Default to 0
    if (value.indexOf('.') === -1) {
      value = parseFloat(value);
      callback(value);
    } else {
      const decimal = value.split('.')[1];
      ((decimal && decimal.length < 3) || !decimal) && callback(value);
    }
  } else if (value === '-') {
    callback(value);
  }
};

export const checkPriceNumberInput = (value, callback: (value: number) => any) => {
  const pureNumberString = getPureNumberString(value);
  const registerAmount = parseFloat(pureNumberString) / 100;
  callback(registerAmount);
};

export const getPureNumberString = value => {
  if (isString(value)) {
    return value.replace(/[^0-9]/gi, '');
  } else if (isNumber(value)) {
    return String(value).replace(/[^0-9]/gi, '');
  } else {
    return '0.00';
  }
};

export const getTaxCodeObject = (taxCode, taxCodes, defaultTaxRate) => {
  let taxcode = null;
  for (const tax of taxCodes) {
    if (tax._id === taxCode && !tax.isDeleted) {
      taxcode = tax;
    }
  }
  if (taxcode === null) {
    taxcode = {
      _id: '',
      name: t('Default'),
      rate: defaultTaxRate,
    };
  }
  return taxcode;
};

export const getTaxCodeList = (taxCodes, defaultTaxRate) => {
  const codes = [];
  for (const tax of taxCodes) {
    if (!Boolean(tax.isDeleted)) {
      codes.push(tax);
    }
  }
  codes.push({
    _id: '',
    name: t('Default'),
    rate: defaultTaxRate,
  });
  return codes;
};

export const getUnNullValue = (object, path, defaultValue) => {
  const result = get(object, path, defaultValue);
  return isEmpty(result) ? defaultValue : result;
};

// Note: lodash.isNumber will check the param's type if it's Number, but this funciton allows String type
export const isValidNumber = value => {
  return value !== null && value !== undefined && value !== '' && !isNaN(value) && isFinite(value);
};

export const convertToNumber = value => {
  if (isValidNumber(value)) {
    return Number(value);
  } else {
    return 0;
  }
};

export const convertToPositiveInt = value => {
  value = String(value).replace(/[^\d]+/, '');
  if (isValidNumber(value)) {
    return parseInt(String(value), 10);
  } else {
    return 0;
  }
};

export const getNumberValue = (object, path, defaultValue) => {
  const tmp = getUnNullValue(object, path, defaultValue);
  return isValidNumber(tmp) ? Number(tmp) : defaultValue;
};

export const getFixedNumber = (value: string | number, fix = 2) => {
  if (isValidNumber(value) && Number(value) !== 0) {
    return Number(value).toFixed(fix);
  } else {
    return 0;
  }
};

export const getFixed2NumberString = (value: string | number, fix = 2) => {
  if (isValidNumber(value) && Number(value) !== 0) {
    return String(Number(value).toFixed(fix));
  } else {
    return fix === 2 ? '0.00' : '0';
  }
};

export const getLocaleNumberString = (value: string | number, fix = 2) => {
  if (isValidNumber(value) && Number(value) !== 0) {
    return localeNumber(Number(value).toFixed(fix));
  } else {
    return fix === 2 ? '0.00' : '0';
  }
};

export const getLocaleNumberStringAtLeastFixed = (value: string | number, precision = 2) => {
  if (isValidNumber(value) && Number(value) !== 0) {
    const parts = Number(value).toString().split('.');
    const decimals = parts.length > 1 ? parts[1].length : 0;
    return currency(Number(value), { precision: Math.min(Math.max(decimals, precision), 8) }).format();
  } else {
    return currency(0, { precision: precision }).format();
  }
};

export const getDisplayPriceLocaleNumberString = (value: string | number, unitPriceRounding = false) => {
  let result;
  if (isValidNumber(value) && Number(value) !== 0 && !unitPriceRounding && Number(value) >= 0.01) {
    result = getLocaleNumberString(value);
  } else {
    result = getLocaleNumberStringAtLeastFixed(value);
  }
  return result;
};
export const getLocaleNumberStringWithDefault = (value: string | number, defaultValue = '0') => {
  if (isValidNumber(value) && Number(value) !== 0) {
    return localeNumber(Number(value).toFixed(2));
  } else {
    return defaultValue;
  }
};

export const isZero = (value: any) => {
  return !value || (!isNaN(value) && isFinite(value) && Number(value) === 0);
};

export const isPositive = (value: any) => {
  return isValidNumber(value) && Number(value) > 0;
};

export const isNegative = (value: any) => {
  return isValidNumber(value) && Number(value) < 0;
};

export const getUnNullPrintString = (value: string | number, defaultValue = '-') => {
  if (!isEmpty(value)) {
    return String(value);
  } else {
    return defaultValue;
  }
};

export const getUnNullStringForNumber = (value: string | number, defaultValue = '-') => {
  if (!isEmpty(value) && value !== 0) {
    return String(value);
  } else {
    return defaultValue;
  }
};

export const getCountNumberString = (value: string | number, fix = 2) => {
  if (isValidNumber(value) && Number(value) !== 0) {
    return Number.isInteger(Number(value)) ? String(Number(value)) : localeNumber(Number(value).toFixed(fix));
  } else {
    return '0';
  }
};

export const getFixedSumString = (value1: string | number, value2: string | number, fix = 2) => {
  const v1 = isValidNumber(value1) ? Number(value1) : 0;
  const v2 = isValidNumber(value2) ? Number(value2) : 0;
  return getFixed2NumberString(v1 + v2, fix);
};

export const getCountSumString = (value1: string | number, value2: string | number, fix = 2) => {
  const v1 = isValidNumber(value1) ? Number(value1) : 0;
  const v2 = isValidNumber(value2) ? Number(value2) : 0;
  return getCountNumberString(v1 + v2, fix);
};

export const getReceiptQtyString = (qty: string | number) => {
  if (isValidNumber(qty) && Number(qty) !== 0) {
    const qtyStrArr = String(qty).split('.');
    if (qtyStrArr.length > 1) {
      if (qtyStrArr[1].length > 2) {
        return getFixed2NumberString(qty, 3);
      } else {
        return getFixed2NumberString(qty);
      }
    }
    return String(qty);
  } else {
    return '0';
  }
};

export const getIntegerNumberString = (value: string | number) => {
  if (isValidNumber(value) && Number(value) !== 0) {
    return String(Number(value).toFixed(0));
  } else {
    return '0';
  }
};

export const getWithKeyString = (key: string, value: string) => {
  if (value && Boolean(value)) {
    return `${key}${value}`;
  } else {
    return null;
  }
};

export const fomatPreOrderDateString = (fromDate, toDate) => {
  const _from = getMoment(fromDate);
  let _fromDate = '';
  if (_from !== '') {
    _fromDate = _from.format('YYYY.MM.DD hh:mma');
  }

  const _to = getMoment(toDate);
  let _toDate = '';
  if (_to !== '') {
    _toDate = _to.format('hh:mma');
  }
  return `${_fromDate} - ${_toDate}`;
};

export const safeCallback = (onComplete, param) => {
  onComplete && onComplete.callback && onComplete.callback(param);
};

export const freeObject = <T>(obj: T, defaultValue = null): T => {
  return parse(JSON.stringify(obj), defaultValue);
};

export const testProps = id => {
  if (ENVIRONMENT !== EnvironmentType.Pro) {
    return { testID: id, accessibilityLabel: id };
  }
  return {};
};

export const mapAutoSignOutValue = value => {
  switch (value) {
    case '':
      return 'Never';
    case '30':
      return '30s';
    case '60':
      return '1m';
    case '120':
      return '2m';
    case '300':
      return '5m';
    case '600':
      return '10m';
    default:
      return 'Never';
  }
};

const currencyToSymbol = {
  USD: '$',
  MYR: 'RM',
  SGD: '$',
  AUD: '$',
  CNY: '¥',
  HKD: '$',
  PHP: '₱',
  THB: '฿',
  MMK: 'K',
  KHR: '៛',
  VND: '₫',
  NPR: 'रू',
  GBP: '£',
  EUR: '€',
  JPY: '¥',
  BTN: '₹',
  LAK: '₭',
};

export const convertCurrencyToSymbol = (currency: string) => {
  return currencyToSymbol[currency] ? currencyToSymbol[currency] : currency;
};

export const newConvertCurrencyToSymbol = (localCountryMap: LocalCountryMapType, currency: string) => {
  const currencySymbol = get(localCountryMap, ['currencies', currency, 'symbol']);
  return currencySymbol ? currencySymbol : currency;
};

export const getCurrencySymbol = () => {
  const state = getStoreState();
  const currency = state.getIn(['Storage', 'storeInfo', 'store', 'currency']);
  const localCountryMap = state.get('localCountryMap');

  const currencySymbol = get(localCountryMap, ['currencies', currency, 'symbol']);
  if (currencySymbol) {
    return currencySymbol;
  }

  const hardCodedSymbol = currencyToSymbol[currency];
  if (hardCodedSymbol) {
    return hardCodedSymbol;
  }

  return currency;
};

export const useCurrencySymbol = () => {
  const currency = useSelector(selectCurrency);
  const localCountryMap = useSelector(selectLocalCountryMap);

  const currencySymbol = get(localCountryMap, ['currencies', currency, 'symbol']);
  if (currencySymbol) {
    return currencySymbol;
  }

  const hardCodedSymbol = currencyToSymbol[currency];
  if (hardCodedSymbol) {
    return hardCodedSymbol;
  }

  return currency;
};

export const mapAndroidDeviceModelToReadableName = (model: string) => {
  switch (model) {
    case 'I22T01':
      return 'Falcon 1';
    case 'D4-504':
      return 'D4';
    case 'D2s_PLUS':
      return 'D2 plus';
    case 'D1s_d':
      return 'D2';
    case 'T2lite':
      return 'T2 lite';
    default:
      return model;
  }
};

export const maskPhoneNumber = (phone: string): string => {
  if (isEmpty(phone)) return '';

  const hasPlus = _.startsWith(phone, '+');
  const lastFourDigits = _.takeRight(phone, 4).join('');
  const middlePart = _.slice(phone, hasPlus ? 1 : 0, phone.length - 4).join('');

  const maskedMiddle = _.repeat('x', middlePart.length);

  return `${hasPlus ? '+' : ''}${maskedMiddle}${lastFourDigits}`;
};

export const maskEmail = (email: string): string => {
  if (_.isEmpty(email)) return '';

  const atIndex = _.indexOf(email, '@');
  if (atIndex <= 0) return email;

  const prefix = _.slice(email, 0, atIndex).join('');
  const suffix = _.slice(email, atIndex).join('');

  return `${_.repeat('x', prefix.length)}${suffix}`;
};

export const maskCompleteText = (text: string): string => {
  if (_.isEmpty(text)) return '';

  return _.repeat('x', text.length);
};
