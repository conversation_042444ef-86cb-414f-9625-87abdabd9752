import { LogDestination, LoggingLevel, MobileData, POSBasicAction, PrivateDataPayload, trackLogEvent } from '../common';
import LocalLogger from '../local-logging/LocalLogger';

interface POSBasicEvent {
  action: POSBasicAction;
  result?: string;
  reason?: string;
  privateDataPayload?: PrivateDataPayload;
  destination?: LogDestination;
}

interface POSBasicEventWithLevel extends POSBasicEvent {
  level: LoggingLevel;
}

export const infoPOSBasicEvent = ({ action, result = 'Succeed', reason, privateDataPayload, destination }: POSBasicEvent) => {
  logPOSBasicEvent({ action, level: LoggingLevel.Info, result, reason, privateDataPayload, destination });
};

export const warnPOSBasicEvent = ({ action, result = 'Failed', reason, privateDataPayload, destination }: POSBasicEvent) => {
  logPOSBasicEvent({ action, level: LoggingLevel.Warn, result, reason, privateDataPayload, destination });
};

export const errorPOSBasicEvent = ({ action, result = 'Failed', reason, privateDataPayload, destination }: POSBasicEvent) => {
  logPOSBasicEvent({ action, level: LoggingLevel.Error, result, reason, privateDataPayload, destination });
};

export const logPOSBasicEvent = ({ action, level, result, reason, privateDataPayload, destination }: POSBasicEventWithLevel) => {
  const mobileData = { result, reason } as MobileData;
  const tags = ['pos_basic'];
  if (destination === 'local' || destination === 'both') {
    LocalLogger.log({ action, level, tags, reason, result, payload: privateDataPayload });
  }
  if (destination !== 'local') {
    trackLogEvent({ action, level, tags, mobileData, privateDataPayload });
  }
};
