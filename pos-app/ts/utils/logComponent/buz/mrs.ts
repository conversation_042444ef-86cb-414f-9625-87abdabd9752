import { get, isNil, pick, round } from 'lodash';

import { Message } from '../../../actions';
import { getStoreState } from '../../../config';

import {
  selectBackupPid,
  selectClientConnected,
  selectIPAddress,
  selectIsMaster,
  selectMRSOutDatedClient,
  selectMasterState,
  selectRegisterId,
  selectSlavePid,
  selectSnapshotPid,
  selectSnapshotVersion,
  selectStoreName,
} from '../../../sagas/selector';
import { MRS_VERSION } from '../../mrs';
import { LogDestination, LogResult, LoggingLevel, MRSAction, MobileData, PrivateDataPayload, WorkflowStatus, trackLogEvent } from '../common';
import LocalLogger, { BusinessError, LogEntryOptions } from '../local-logging/LocalLogger';
import { LogOrderInfo } from './beepOrder';

export type MRSEventType = SetUpFlow | SwitchFlow | ProposalFlow | LearnFlow | OperationFlow | SnapShotFlow;

export enum SetUpFlow {
  INIT = 'init',
  OPEN_RESET = 'setup_open_reset',
  SET_MASTER = 'setup_set_master',
  SET_MASTER_ERROR = 'setup_set_master_error',
  START_CLIENT = 'setup_start_client',
  START_WEBSOCKET = 'setup_start_websocket',
  STOP_WEBSOCKET = 'setup_stop_websocket',
  IP_CHANGED = 'setup_ip_changed',
  IP_NOT_CHANGED = 'setup_ip_not_changed',
  STOP_MDNS = 'setup_stop_mdns',
  SERVER_START = 'setup_server_start',
  SERVER_STOP = 'setup_server_stop',
  SERVER_UPDATE = 'setup_server_update',
  CLIENT_CONNECTED = 'setup_client_connected',
  CLIENT_CLOSE = 'setup_client_close',
  CLIENT_CHANGED = 'setup_client_changed',
  MASTER_ERROR = 'setup_master_error',
  CHECK_MASTER_ERROR = 'setup_check_master_error',
  PID_ERROR = 'setup_pid_error',
  UPDATE_PID_ERROR = 'setup_update_pid_error',
  STOP = 'setup_stop_mrs',
  RESET = 'setup_reset_mrs',
  FORCE_SET = 'setup_force_set_master',
  FORCE_SET_CLICK = 'setup_force_set_master_click',
  CLEAR = 'setup_clear_mrs',
  UNHANDLED_ERROR = 'setup_unhandled_error',
  PROGRAM_ERROR = 'setup_program_error',
  // NET_CHANGED = 'setup_network_changed',
  // DB_ERROR = 'setup_data_base_error',
  EXIT_APP = 'setup_exit_app',
  TRY_RECONNECT = 'setup_try_reconnect',
  FORCE_RECONNECT = 'setup_force_reconnect',
}

export enum SwitchFlow {
  SLAVE_REQUEST = 'switch_slave_request',
  RECEIVED_SLAVE_REQUEST = 'switch_received_slave-request',
  RECEIVED_MASTER_RESPONSE = 'switch_received_master_response',
  RECEIVED_SLAVE_RESPONSE = 'switch_received_slave_response',
  SLAVE_REQUEST_COMPLETE = 'switch_request_complete',
  MASTER_APPROVED = 'switch_master_approved',
  MASTER_REJECTED = 'switch_master_rejected',
  SLAVE_TIMEOUT = 'switch_slave_timeout',
  MASTER_TIMEOUT = 'switch_master_timeout',
}

export enum ProposalFlow {
  PROPOSER_CHECK_ERROR = 'proposal_proposer_check_error',
  PROPOSER_REQUEST = 'proposal_proposer_request',
  PROPOSER_REQUEST_EDIT = 'proposal_proposer_request_edit',
  CHECK_REQUEST_EDIT = 'acceptor_check_request_edit',
  MASTER_SAVE_LOG = 'master_save_log',
  ACCEPTOR_CHECK_ERROR = 'proposal_acceptor_check_error',
  ACCEPTOR_SAVE_ERROR = 'proposal_acceptor_save_error',
  ACCEPTOR_ORDER_EMPTY = 'proposal_acceptor_order_empty',
  ACCEPTOR_ORDER_NOT_FOUND = 'proposal_acceptor_order_not_found',
  ACCEPTOR_MASTER_SAVE_ERROR = 'proposal_acceptor_master_save_error',
  ACCEPTOR_RETRY_SUCCEED = 'proposal_acceptor_retry_succeed',
  PROPOSAL_COMPLETE = 'proposal_complete',
  PROPOSAL_RETRY_COMPLETE = 'proposal_retry_complete',
  CHOSEN_ERROR = 'proposal_chosen_error',
  PROPOSER_TIMEOUT = 'proposal_timeout',
}

export enum LearnFlow {
  INIT_ERROR = 'learn_init_error',
  SWITCH_AUTO_LEARN = 'switch_auto_learn',
  LEARN_REQUEST = 'learn_request',
  MASTER_CHECK_ERROR = 'learn_master_check_error',
  GET_LEARN_DATA = 'learn_get_learn_data',
  OUTDATED_REGISTER = 'learn_outdated_register',
  RESPONSE_ERROR = 'learn_response_error',
  LEARN_START = 'learn_start',
  SAVE_LEARN_DATA = 'learn_save_learn_data',
  DELETE_USELESS_ORDERS = 'learn_delete_useless_orders',
  RETRY_LEARN_DATA = 'learn_retry_learn_data',
  LEARNER_SAVE_ERROR = 'learn_save_error',
  LEARNER_COMPLETE = 'learn_complete',
  LEARNER_FAILED = 'learn_failed',
  LEARNER_TIMEOUT = 'learn_timeout',
}

export enum OperationFlow {
  LIMIT = 'operation_limit',
}

export enum SnapShotFlow {
  CREATE = 'create_snapshot',
  SAVE = 'save_snapshot',
  DELETE = 'delete_logs',
  CHECK_CLIENT_ORDERS = 'check_client_orders',
}

export type MRSType = MRSTypeEnum | OrderOperationEnum | string;

export enum MRSTypeEnum {
  NONE = '',
  CLICK = 'click',
  FIRST_ENABLE = 'first_enable_mrs',
  FIX_CLIENT = 'fix_client',
  CLIENT_ERROR = 'client_error',
  PID_ERROR = 'pid_error',
}

export enum OrderOperationEnum {
  Save = 'save',
  Update = 'update',
  Delete = 'delete',
  CheckOut = 'check_out',
  Refund = 'refund',
  Cancel = 'cancel',
  Upload = 'upload',
  Sync = 'sync',
  ChangeTableId = 'change_table_id',
  MergeAndSave = 'merge_and_save',
  MergeAndCheckOut = 'merge_and_check_out',
  Split = 'split',
  Merge = 'merge',
  CompleteEdit = 'complete_edit',
  SplitAndSave = 'split_and_save',
  SplitAndCheckOut = 'split_and_check_out',
  PrintHalfReceipt = 'print_half_receipt',
  Unknown = 'unknown',
}

export interface MRSPrivateData {
  message?: any; // will be stringified
  source?: string;
  errorCode?: number; // will be formatted to string
  errorMessage?: string;
  costTime?: number;
  dataType?: string;
  fromPid?: number;
  toPid?: number;
  backupPid?: number;
  serverPid?: number;
  cleanOpenOrder?: boolean;
  clientIp?: string;
  order?: LogOrderInfo;
  retryFlag?: boolean;
}

export interface LogMRSOption {
  type?: MRSType;
  reason?: string;
  isDataOperation?: boolean;
  flowId?: string;
  workflowStatus?: WorkflowStatus;
  data?: MRSPrivateData;
  level?: LoggingLevel;
  destination?: LogDestination; // default value is remote
}

export const logUnhandledMRSError = (message: string | Message, source: string, destination?: LogDestination) => {
  logMRSEvent(MRSAction.Set_Up, SetUpFlow.UNHANDLED_ERROR, {
    reason: 'unhandled message case',
    data: {
      source,
      message,
    },
    destination,
  });
};

const initMRSData = (event: MRSEventType, type: MRSType) => {
  const storeState = getStoreState();
  return {
    masterState: selectMasterState(storeState),
    clientConnected: selectClientConnected(storeState),
    pid: selectSlavePid(storeState),
    isMaster: selectIsMaster(storeState),
    registerId: String(selectRegisterId(storeState)),
    ipAddress: String(selectIPAddress(storeState)),
    snapshotPid: selectSnapshotPid(storeState),
    snapshotVersion: selectSnapshotVersion(storeState),
    backupPid: selectBackupPid(storeState),
    outDatedClient: selectMRSOutDatedClient(storeState),
    type,
    event,
    version: MRS_VERSION,
    storeName: selectStoreName(storeState),
  };
};

export const logMRSEvent = (action: MRSAction, event: MRSEventType, options: LogMRSOption = {}) => {
  const reason = options.reason || '';
  const level = options.level || (reason ? LoggingLevel.Warn : LoggingLevel.Info);

  const tags = ['rn pos', 'mrs'];
  if (options.isDataOperation) {
    tags.push('data');
  }

  const type = get(options, 'type', MRSTypeEnum.NONE);
  const mrsData = initMRSData(event, type);
  const result = reason ? LogResult.Failed : LogResult.Succeed;
  options.destination = options.destination || 'both';
  // save to local file
  if (options.destination === 'local' || options.destination === 'both') {
    const logEntryOptions: LogEntryOptions = {
      action,
      level,
      tags,
      result,
      workflowId: options.flowId,
      reason,
      mobileData: { mrs: mrsData },
      payload: pick(options.data, [
        'message',
        'source',
        'costTime',
        'dataType',
        'fromPid',
        'toPid',
        'backupPid',
        'serverPid',
        'cleanOpenOrder',
        'clientIp',
        'order',
        'retryFlag',
      ]),
    };
    if (get(options, 'data.errorMessage')) {
      logEntryOptions.error = new BusinessError('MRS_ERROR', get(options, 'data.errorCode', -1), get(options, 'data.errorMessage', ''));
    }
    LocalLogger.log(logEntryOptions);
  }
  // upload to logging server
  if (options.destination !== 'local') {
    const mobileData: MobileData = {
      result,
      reason,
      workflowId: options.flowId || '',
      workflowStatus: options.workflowStatus || '',
      mrs: mrsData,
    };
    const privateDataPayload: PrivateDataPayload = options.data || {};

    if (privateDataPayload.message) {
      privateDataPayload.message = JSON.stringify(privateDataPayload.message);
    }
    if (!isNil(privateDataPayload.errorCode)) {
      privateDataPayload.errCode = String(privateDataPayload.errorCode);
      privateDataPayload.errorCode = undefined;
    }
    if (privateDataPayload.costTime) {
      privateDataPayload.costTimeNumber = round(Number(privateDataPayload.costTime), 3);
      privateDataPayload.costTime = undefined;
    }
    trackLogEvent({ action, level, tags, mobileData, privateDataPayload });
  }
};
