import { LoggingLevel, trackLogEvent, PrivateDataPayload, MobileData, ZReadingAction } from '../common';
import * as JSONUtils from '../../json';
import moment from 'moment';
import { isEmpty } from '../../validator';

interface ZReadingEvent {
  action: ZReadingAction;
  result?: string;
  reason?: string;
  privateDataPayload?: PrivateDataPayload;
  exception?: Error;
}

interface ZReadingEventWithLevel extends ZReadingEvent {
  level: LoggingLevel;
}

/**
 * Log info event of shiftReport event
 * @param action action type
 * @param shiftReport shiftReport session data
 * @param privateDataPayload extra privateData payload
 */
export const infoZReadingEvent = ({ action, result = 'Succeed', privateDataPayload }: ZReadingEvent) => {
  logZReadingEvent({
    action,
    level: LoggingLevel.Info,
    result,
    privateDataPayload,
  });
};

/**
 * Log error event of shiftReport event
 * @param action action type
 * @param reason failure reason
 * @param privateDataPayload extra privateData payload
 * @param exception JS Error
 */
export const errorZReadingEvent = ({ action, result = 'Failed', reason, privateDataPayload, exception }: ZReadingEvent) => {
  logZReadingEvent({
    action,
    level: LoggingLevel.Error,
    result,
    reason,
    privateDataPayload,
    exception,
  });
};

export const logZReadingEvent = ({ action, level, result, reason, privateDataPayload, exception }: ZReadingEventWithLevel) => {
  const mobileData = { result, reason } as MobileData;

  const payload = {} as Partial<PrivateDataPayload>;
  if (exception) {
    payload.exception = JSONUtils.stringify(exception);
  }
  if (privateDataPayload && typeof privateDataPayload === 'object' && Object.keys(privateDataPayload).length > 0) {
    const { lastZReadingCloseTime } = privateDataPayload;
    if (!isEmpty(lastZReadingCloseTime)) {
      privateDataPayload.lastZReadingCloseTime = moment(lastZReadingCloseTime).toString();
    }
    Object.assign(payload, privateDataPayload);
  }
  const tags = ['zreading'];
  trackLogEvent({ action, level, tags, mobileData, privateDataPayload: payload });
};
