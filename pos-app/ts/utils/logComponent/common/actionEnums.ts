/**
 * This file will list all the log actions, please update your actions only on this file
 */

// ------------ Basic Action Sart ------------------------------
export enum POSBasicAction {
  Init = 'pos_init',
  LAUNCH = 'POS_Basic_Launch',
  ACTIVATE = 'POS_Basic_ACTIVATE',
  DEACTIVATE = 'POS_Basic_DEACTIVATE',
  NetworkInfo = 'POS_Basic_Network_Info',
  WiFiChanged = 'POS_Basic_WiFi_Changed',
  SetDefaultWiFi = 'POS_Basic_Set_Default_WiFi',
  SetDefaultEthernet = 'POS_Basic_Set_Default_Ethernet',
  DefaultWiFiChanged = 'POS_Basic_Default_WiFi_Changed',
  HardwareTrackingSummary = 'Hardware_Tracking_Summary',
  SignOut = 'POS_Basic_Sign_Out',
  SignIn = 'POS_Basic_Sign_In',
  BackToSignIn = 'POS_Basic_Back_To_Sign_in',
  SagaError = 'Saga_Error',
  UnknownError = 'Unknown_Error',
  JsonError = 'Json_Error',
  UdpPrinterSearch = 'POS_Udp_Printer_Search',
  ReceivedNotification = 'ReceivedNotification',
  Websocket = 'Websocket',
  MDNSFound = 'MDNS_FOUND',
  MDNSLost = 'MDNS_LOST',
  IST = 'IST',
  Background = 'IN_Background',
  LocalLogger = 'LocalLogger',
}

// ------------ Basic Action End ------------------------------

// ------------ ShiftReport Actions Start -------------------------
export enum ShiftReportAction {
  GenerateShiftReport = 'Generate_Shift_Report',
  ShiftReportSalesAmountIncorrect = 'Shift_Report_Sales_Amount_Incorrect',
}

// ------------ ShiftReport Actions Start -------------------------
export enum ZReadingAction {
  PromptToCloseZReading = 'Prompt_To_Close_ZReading',
  CloseZReading = 'Close_ZReading',
  SyncLastZReadingCloseTime = 'Sync_Last_ZReading_Close_Time',
}

// ------------ ShiftReport Actions End ---------------------------

// ------------ DataSync Actions Start -------------------------
export enum DataSyncAction {
  SyncInfo = 'Sync_Info',
  SyncTransactions = 'Sync_Transactions',
  SyncCannelledTransactions = 'Sync_Cannelled_Transactions',
  SyncShifts = 'Sync_Shifts',
  SyncProducts = 'Sync_Products',
  SyncEmployees = 'Sync_Employees',
  SyncQuickSelectLayout = 'Sync_Quick_Select_Layout',
  SyncTableLayout = 'Sync_Table_Layout',
  SyncStoreInfo = 'Sync_Store_Info',
  SyncPriceBook = 'Sync_Price_Book',
  // SyncEmployeeActivity = "Sync_Employee_Activity",
  SyncPromotions = 'Sync_Promotions',
  SyncActivationStatus = 'Sync_Activation_Status',
  SyncSequentialStartInfo = 'Sync_Sequential_Start_Info',
  SyncAllResult = 'Sync_All_Result',
  SyncLocalDataToIST = 'Sync_Data_IST',
}

// ------------ DataSync Actions End ---------------------------

// ------------ TableLayout Actions Start -------------------------
export enum TableLayoutAction {
  UploadTableLayout = 'Upload_TableLayout',
}

// ------------ TableLayout Actions End ---------------------------

// ------------ Transaction Actions Start -------------------------
export enum SaleFlowAction {
  AddPurchasedItem = 'Add_Purchased_Item',
  UpdatePurchasedItem = 'Update_Purchased_Item',
  UpdatePurchasedItemQuantity = 'Upate_Purchased_Item_Quantity',
  UpdatePurchasedItemDiscount = 'Update_Purchased_Item_Discount',
  DeletePurchasedItem = 'Delete_Purchased_Item',
  GiveFullBillDiscount = 'Give_FullBill_Discount',
  ToggleServiceCharge = 'Toggle_Service_Charge',
  MakeSalePayment = 'Make_Sale_Payment',
  MakeSalePaymentWithFix = 'Make_Sale_Payment_With_Fix',
  MakePayment = 'Make_Payment',
  CheckoutTransaction = 'Checkout_Transaction',
  RestoreCheckoutTransaction = 'Restore_Checkout_Transaction',
  CancelNextPayment = 'Cancel_Next_Payment',
  ClearPromotion = 'Clear_Promotion',
  ClearTransaction = 'Clear_Transaction',
  OrderMultiplePay = 'Order_Multiple_Pay',
  OrderHasNullPaymentMethodId = 'Order_Has_Null_PaymentMethodId',
  SaveTransactiontoLocal = 'Save_Transaction_to_Local',
  getUniqueIdForShoppingCartOrWishlist = 'getUniqueIdForShoppingCartOrWishlist',
  GenerateOnlineInvoiceSequenceNumber = 'generate_Online_Invoice_SequenceNumber',
  CheckPlaceOrderAvailable = 'Check_PlaceOrder_Available',
}

export enum CancelFlowAction {
  UpdateBeepOrderCancelStatus = 'Update_Beep_Order_Cancel_Status',
  UpdateOrderDBCancelStatus = 'Update_Order_DB_Cancel_Status',
  CancelTrxWithVoidNumber = 'Cancel_Trx_With_Void_Number',
}

export enum EWalletPayAction {
  EWalletPay = 'EWallet_Pay',
  VoidEWalletPayment = 'Void_EWallet_Payment',
}

export enum LoyaltyFlowAction {
  RemoveBIRDiscountToTransaction = 'Remove_BIRDiscount_To_Transaction',
  AddBIRDiscountToTransaction = 'Add_BIRDiscount_To_Transaction',
  AddCustomerToTransaction = 'Add_Customer_To_Transaction',
  DeleteCustomerFromTransaction = 'Delete_Customer_From_Transaction',
  UpdateLoyaltyDiscounts = 'Update_Loyalty_Discounts',
  DisableLoyaltyDiscounts = 'Disable_Loyalty_Discounts',
  UpdateCustomerInTransaction = 'Update_Customer_In_Transaction',
  SetTakeAwayToTransaction = 'Set_Take_Away_To_Transaction',
  UpdateTransactionTableIdAndPax = 'Update_Transaction_TableIdAndPax',
}

export enum ManualReturnFlowAction {
  RefundTransaction = 'Refund_Transaction',
}

export enum OpenOrderFlowAction {
  SetOnlineOpenOrderToTransaction = 'Set_Online_Open_Order_To_Transaction',
  SetOpenOrderTransactionSession = 'Set_Open_Order_Transaction_Session',
  SaveOpenOrder = 'Save_Open_Order',
  MergeOpenOrder = 'Merge_Open_Order',
  SplitOpenOrder = 'Split_Open_Order',
  MoveOpenOrder = 'Move_Open_Order',
  ChangeOpenOrderTable = 'Change_Open_Order_Table',
  UpdateOpenOrder = 'Update_Open_Order',
  RestoreOpenOrder = 'Restore_Open_Order',
  RestorePreorder = 'Restore_Preorder',
  DeleteOpenOrder = 'Delete_Open_Order',
  DeleteOnlineOpenOrder = 'Delete_Online_Open_Order',
  UnlockPayLaterOrder = 'Unlock_Pay_Later_Order',
  SaveOnlineOrder = 'Save_Online_Order',
}

export enum PreOrderFlowAction {
  CreatePreOrder = 'Create_PreOrder',
  SetupPreOrder = 'Setup_PreOrder',
  RestoreLocalPreOrder = 'Restore_Local_PreOrder',
  RestoreSearchedPreOrder = 'Restore_Searched_PreOrder',
}

export enum RefundFlowAction {
  CheckTransactionRefundStatus = 'Check_Transaction_Refund_Status',
  UpdateTransactionReturnItem = 'Update_Transaction_Return_Item',
  CheckRefundTransactionValidity = 'Check_Refund_Transaction_Validity',
  ClearRefundTransactionPayment = 'Clear_Refund_Transaction_Payment',
}

export enum ShiftFlowAction {
  SetSequence = 'setSequence',
  OpenShift = 'Open_Shift',
  CloseShift = 'Close_Shift',
  CheckAvailable = 'Check_Available',
}

export enum GetOrderAction {
  SearchRemoteOrder = 'Search_Remote_Order',
}

export type TransactionAction =
  | SaleFlowAction
  | CancelFlowAction
  | EWalletPayAction
  | LoyaltyFlowAction
  | ManualReturnFlowAction
  | OpenOrderFlowAction
  | PreOrderFlowAction
  | RefundFlowAction
  | ShiftFlowAction;

// ------------ Transaction Actions End ---------------------------

// ------------ Storage Actions Start ---------------------------
export enum DBAction {
  Migration = 'DB_Migration',
  Save = 'DB_Save_Record',
  Create = 'DB_Create_Record',
  Read = 'DB_Read_Record',
  Update = 'DB_Update_Record',
  Delete = 'DB_Delete_Record',
  Serialized = 'DB_Read_Serialized',
}

export enum ReduxPersistAction {
  REHYDRATE = 'Redux_Persist_Rehydrate',
}

export type StorageAction = DBAction | ReduxPersistAction;
// ------------ Storage Actions End -----------------------------

// ------------ Printing Actions Start ------------
export enum PrintingDataValidationAction {
  ReceiptPrintingDataValidataion = 'Receipt_Printing_Data_Validation',
  DirtyProductTag = 'Dirty Product Tag',
  // KitchenPrintingDataValidation = 'Kitchen_Printing_Data_Validation',
  // ShiftPrintingDataValidation = 'Shift_Printing_Data_Validation',
}

export enum PrinterAction {
  SearchPrinter = 'Search_Printer',
  ClickPrinterOffline = 'ClickPrinterOffline',
  PingPrinter = 'Ping_Printer',
  UdpSearch = 'Udp_Search',
  ForgetPrinter = 'Forget_Printer',
  RefreshPrinter = 'Refresh_Printer',
  CheckPrinterPort = 'Check_Printer_Port',
  InitPrinter = 'Init_Printer',
  SearchPrinterManually = 'Search_Printer_Manually',
  MarkOfflineWhenPrinting = 'Mark_Offline_When_Printing',
  MemoryInfo = 'Memory_Information',
}

export enum NfcPaymentAction {
  InitNfc = 'InitNfc',
  RefreshToken = 'refresh_token',
  RefreshTokenStart = 'refresh_token_start',
  StartTransaction = 'start_transaction',
  TransactionResult = 'transaction_result',
  RetryTransaction = 'retry_transaction',
  CancelTransaction = 'cancel_transaction',
  VoidTransaction = 'void_transaction',
  CancelPinOrSignature = 'cancel_pin_signature',
}

export enum GhlPaymentAction {
  Start = 'Start',
  Request = 'Request',
  Response = 'Response',
  Reset = 'Reset',
}

export type PrintingAction = PrintingDataValidationAction | PrinterAction;
// ------------ Printing Actions End --------------

// ------------ Beep Actions Start --------------
export enum BeepFlowAction {
  ReceivedNotification = 'Received notification',
  GetOnlineOrderFromServer = 'Get online orders from server',
  GetOnlineOrderList = 'Get online order list', // TODO: never used?
  GetOrderIdFromOrders = 'Get orderId from orders',
  GetOrderFromLocal = 'Get order from local',
  GetSubOrderFromLocal = 'Get suborder from local',
  GetOrderDetailFromServer = 'Get order details from server',
  PrintKitchen = 'Print kitchen',
  PrintReceipt = 'Print receipt',
  PrintHalfReceipt = 'Print half receipt',
  PrintReport = 'Print report',
  PrintBeepOrderSummary = 'Print beep order summary',
  PrintKitchenManually = 'Print kitchen manually',
  PrintReceiptManually = 'Print receipt manually',
  UpdateOrderStatusToServer = 'Update order status to server',
  MarkSubOrderPrintStatusToServer = 'Mark SubOrder print status to server',
  UpdateOrderStatusToServerManually = 'Update order status to server manually',
  UpdateOrderStatusToLocal = 'Update order status to local',
  UpdateSubOrderStatusToLocal = 'Update subOrder status to local',
  UpdateOrderStatusToLocalManually = 'Update order status to local manually',
  MerchantChangeStatus = 'Merchant change status',
  SkipConfirmedOrder = 'Skip confirmed order',
  SkipPrintedSubOrder = 'Skip printed sub order',
  ScheduledTaskRegisterIdIsNull = 'Scheduled task registerId is null',
  ScheduledTaskAlreadyRuning = 'Scheduled task is already running',
  GetOrderDetailError = 'Get order details error',
  Checkout = 'Checkout beep order',
  CancelOrder = 'Cancel paylater order',
  SubmitOrder = 'Submit suborder',
  SplitPayLaterOrder = 'Split payLater order',
  SaveSplitOrder = 'Save split payLater order',
  AddItem = 'Add Item',
  PrepareEquivalentValue = 'Prepare Equivalent Value',
  HasSubOrdersNotPrinted = 'Has subOrders not printed',
  SkipSelfPickUpPreOrder = 'Skip self pickUp preOrder',
  GetPrintType = 'Get PrintType',
  BeepTask = 'Beep Task',
}
// ------------ Beep Actions Start --------------

// #region MRS Actions
// ------------ MRS Actions Start -------------------------
export enum MRSAction {
  Set_Up = 'MRS_Set_Up',
  Switch_Master = 'MRS_Switch_Master',
  Proposal = 'MRS_Proposal',
  Learn = 'MRS_Learn',
  Operation = 'MRS_Operation',
  Snapshot = 'MRS_Snapshot',
}
// ------------ MRS Actions End -------------------------

// ------------ checkUpdate Actions Start -------------------------
export enum CheckUpdateAction {
  CheckUpdate = 'Check_Update',
}
// #endregion

// ------------ CodePush Actions Start -------------------------
export enum CodePushAction {
  CodePush = 'CodePush',
  Pushy = 'Pushy',
}
// #endregion

export enum CustomerAction {
  CREATE_QR = 'Create_QR',
  SHARE_INFO = 'Share_Info',
}

export enum HTTPAction {
  HTTP_REQUEST = 'HTTP_Request',
}
