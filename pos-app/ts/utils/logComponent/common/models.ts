import moment from 'moment';
import { v4 as uuidv4 } from 'uuid';
import { MasterState } from '../../../actions';
import { DataSyncAction, POSBasicAction, PrintingAction, StorageAction, TransactionAction } from './actionEnums';

import { get } from 'lodash';
import { KDSCommand } from '../../kds/message';
import { MRSEventType, MRSType } from '../buz';

export type ActionType = string | DataSyncAction | TransactionAction | POSBasicAction | PrintingAction | StorageAction;

export enum LoggingLevel {
  Debug = 'debug',
  Info = 'info',
  Warn = 'warn',
  Error = 'error',
}

export type LogDestination = 'local' | 'remote' | 'both';

export type TagsType = string[];

export enum LogResult {
  Succeed = 'Succeed',
  Failed = 'Failed',
}

export enum WorkflowStatus {
  Start = 'Start',
  End = 'End',
}

export interface Basic {
  uuid?: string;
  action?: string;
  tags?: TagsType;
  level?: string;
  platform: string;
  project: string;
  ts?: number;
  deviceId: string;
  business: string;
}

export interface HostType {
  hostIP?: string;
  isEthernetConnected?: boolean;
  isNetworkAvailable?: boolean;
  isWifiConnected?: boolean;
}

export interface OrderType {
  orderId?: string;
  transactionId?: string;
  submitId?: string;
  receiptNumber?: string;
  isBeepOrder?: boolean;
  triggerType?: string;
  transactionType?: string;
  pushOrderType?: string;
  paytTime?: string;
  status?: string;
  displayStatus?: string;
  toSatsu?: string;
}

export interface MRSInfoType {
  masterState: MasterState;
  pid: number;
  isMaster: boolean;
  clientConnected: boolean;
  registerId: string;
  ipAddress: string;
  type: MRSType;
  event: MRSEventType;
  version: number;
  storeName: string;
}

export interface KDSInfoType {
  posId: string;
  kdsId?: string;
  ncsId?: string;
  socketId?: string;
  event: string;
  version: string;
  messageId?: string;
  responseId?: string;
  command?: KDSCommand | string;
  orderIds?: string;
  errorCode?: number;
  errorMessage?: string;
  storeName: string;
}

export interface CFDInfoType {
  posId: string;
  kdsId?: string;
  socketId?: string;
  event: string;
  version: string;
  messageId?: string;
  responseId?: string;
  command?: KDSCommand | string;
  orderIds?: string;
  errorCode?: number;
  storeName: string;
}

export interface PrinterType {
  printerId?: string;
  isOnline?: boolean;
  printEvent?: string;
  lanIp?: string;
  newLanIp?: string;
  printCostTime?: number;
  errorCode?: number;
}

export interface MobileData {
  time?: string;
  appId?: string;
  appName?: string;
  osVersion?: string;
  version?: string;
  deviceModel?: string;
  registerId?: string;
  registerNumber?: string;
  storeId?: string;
  result?: string;
  reason?: string;
  employeeId?: string;
  workflowId?: string;
  workflowStatus?: string;
  host?: HostType;
  order?: OrderType;
  mrs?: MRSInfoType;
  kds?: KDSInfoType;
  cfd?: CFDInfoType;
  printer?: PrinterType;
  businessCountry?: string;
  sn?: string;
  buildNumber?: string;
}

export interface PublicData {
  [key: string]: any;
}

export interface PrivateData {
  [key: string]: any;
}

export interface PrivateDataPayload {
  [key: string]: any;
}

export interface LogEvent extends Basic {
  publicData?: PublicData;
  mobileData: MobileData;
  privateData?: PrivateData;
}

export interface LogEventParameter {
  action: ActionType;
  level: string;
  tags?: TagsType;
  publicData?: PublicData;
  mobileData?: MobileData;
  privateDataPayload?: PrivateDataPayload;
}

let _basicFields = {} as Basic;
export const initBasicFileds = (fileds: Basic) => {
  _basicFields = fileds;
};

let _mobileData = {} as MobileData;
export const initMobileData = (data: MobileData) => {
  _mobileData = { ..._mobileData, ...data };
};

export const insertMobileData = (data: MobileData) => {
  _mobileData = { ..._mobileData, ...data };
};

const generateKey = () => {
  // add a default value to reduce conflict rate
  return _basicFields.project || 'posV3Mobile'; // Removing action component from namespace to reduce the number of log fields soars.
};

export const produceLogEvent = ({ action, level, tags, publicData, mobileData, privateDataPayload }: LogEventParameter): LogEvent => {
  const event: LogEvent = { ..._basicFields, action, mobileData: _mobileData };
  event.uuid = uuidv4();

  if (Boolean(level)) {
    event.level = level;
  } else {
    event.level = LoggingLevel.Info;
  }
  if (get(tags, 'length', 0) > 0) {
    event.tags = tags;
  }
  event.ts = Date.now();

  if (publicData != null) {
    event.publicData = publicData;
  }

  if (mobileData != null) {
    event.mobileData = { ..._mobileData, ...mobileData };
  }
  event.mobileData.time = moment().toISOString();

  if (action && privateDataPayload && Object.keys(privateDataPayload).length > 0) {
    const key = generateKey();
    event.privateData = {
      [key]: privateDataPayload,
    };
  }

  return event;
};
