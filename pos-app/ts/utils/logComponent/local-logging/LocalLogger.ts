import { isBoolean, isNil } from 'lodash';
import { v4 as uuidv4 } from 'uuid';
import { IsIOS } from '../../../constants';
import { DefaultLocalLoggingConfig } from '../../growthbook';
import { LogEvent, LoggingLevel, MobileData } from '../common';
import { FileLoggerStatic, SendByEmailOptions } from './libs/index';
import { LoggingLevelOrdinal, LogHeaderOptions, LogLevel, LogSettingOptions } from './libs/NativeFileLogger';

/**
 * 日志条目接口
 */
export interface LogEntryOptions {
  action: string;
  event?: string;
  level: LoggingLevel;
  result?: string;
  workflowId?: string;
  message?: string;
  reason?: string;
  error?: BusinessError | null;
  payload?: Record<string, any> | null;
  tags?: string[];
  mobileData?: MobileData;
}
export type LogEntry = Omit<LogEvent, 'platform' | 'project' | 'deviceId'>;

export const LocalLoggerStorageKey = 'LocalLogger';
export const LocalLoggerDeleteKey = 'LocalLoggerDeleteKey';
export const LocalLoggerUploadKey = 'LocalLoggerUploadKey';
export interface LocalLoggerConfigureOptions {
  logLevel: LogLevel;
  maximumFileSize: number;
  maximumNumberOfFiles: number;
}

const FileLogger = new FileLoggerStatic();
/**
 * 本地日志系统
 * 基于react-native-file-logger实现的结构化日志系统
 */
class LocalLogger {
  private _enabled = false;
  private _logLevel = DefaultLocalLoggingConfig.logLevel;
  private _mobileData: LogHeaderOptions = {};

  private logBuffer: LogEntry[] = [];

  private bufferSize = 10;

  private static flushTimer: ReturnType<typeof setTimeout> | null = null;

  private flushInterval = 5000;

  public updateLogSetting = async (options: LogSettingOptions) => {
    if (IsIOS) {
      return;
    }
    if (isBoolean(options.enabled)) {
      this.toggleEnabled(options.enabled);
    }
    if (!isNil(options.logLevel)) {
      this._logLevel = options.logLevel;
    }
    // native
    return FileLogger.updateLogSetting(options);
  };

  private toggleEnabled = (enabled: boolean) => {
    if (enabled != this._enabled) {
      this._enabled = enabled;
      if (enabled) {
        console.log('startPeriodicFlush', new Date().toISOString());
        this.startPeriodicFlush();
      } else {
        console.log('stopPeriodicFlush', new Date().toISOString());
        this.stopPeriodicFlush();
      }
    }
  };

  public updateLogModel = async (options: LogHeaderOptions) => {
    if (IsIOS) {
      return;
    }
    Object.assign(this._mobileData, options);
    return FileLogger.updateLogModel(options);
  };

  /**
   * 创建标准化日志对象
   * @param options 日志级别
   * @returns 格式化的日志对象
   */
  private createLogEntry = (options: LogEntryOptions): LogEntry => {
    const { action, event, level, result, workflowId, message, error, payload, tags, reason } = options;
    const timestamp = Date.now();
    const logEntry: LogEntry = {
      uuid: uuidv4(),
      level,
      ts: timestamp,
      action,
      business: this._mobileData.business,
      tags,
      mobileData: {
        storeId: this._mobileData.storeId,
        employeeId: this._mobileData.employeeId,
        registerId: this._mobileData.registerId,
        registerNumber: `${this._mobileData.registerNumber}`,
        version: this._mobileData.version,
        time: new Date().toISOString(),
        ...options.mobileData,
      },
      privateData: {
        posV3Mobile: {},
      },
    };

    if (result) {
      logEntry.mobileData.result = result;
    }
    if (workflowId) {
      logEntry.mobileData.workflowId = workflowId;
    }
    if (reason) {
      logEntry.mobileData.reason = reason;
    }

    if (error) {
      logEntry.privateData.posV3Mobile.errCode = error.code ?? error.name;
      logEntry.privateData.posV3Mobile.errorMessage = `${error.name}:${error.message}`;
      logEntry.privateData.posV3Mobile.errorStack = error.stack || '';
    }

    if (message) {
      logEntry.privateData.posV3Mobile.message = message;
    }
    if (event) {
      logEntry.privateData.posV3Mobile.event = event;
    }

    if (payload) {
      logEntry.privateData.posV3Mobile.payload = payload;
    }

    return logEntry;
  };

  /**
   * 添加日志到缓冲区
   * @param logEntry 日志条目
   */
  private addToBuffer = (logEntry: LogEntry): void => {
    // console.log('addToBuffer', logEntry);
    this.logBuffer.push(logEntry);

    // 如果缓冲区已满，立即刷新
    if (this.logBuffer.length >= this.bufferSize) {
      this.flush();
    }
  };

  /**
   * 刷新缓冲区，将日志写入文件
   */
  private flush = (): void => {
    // console.log('flush', new Date().toISOString());
    if (this.logBuffer.length === 0) {
      return;
    }

    const logs = [...this.logBuffer]
      .map(logEntry => {
        return JSON.stringify(logEntry);
      })
      .join('\n');
    this.logBuffer = [];
    FileLogger.write(LogLevel.Info, logs);
  };

  public flushSync = (): void => {
    if (IsIOS) {
      return;
    }
    if (this._enabled) {
      this.flush();
    }
  };

  /**
   * 启动定时刷新
   */
  private startPeriodicFlush = (): void => {
    if (IsIOS) {
      return;
    }
    if (LocalLogger.flushTimer) {
      clearInterval(LocalLogger.flushTimer);
    }

    LocalLogger.flushTimer = setInterval(() => {
      this.flush();
    }, this.flushInterval);
  };

  /**
   * 停止定时刷新
   */
  private stopPeriodicFlush = (): void => {
    if (LocalLogger.flushTimer) {
      clearInterval(LocalLogger.flushTimer);
      LocalLogger.flushTimer = null;
    }
    this.flush();
  };

  /**
   * 写入调试级别日志
   * @param message 日志消息
   * @param payload 附加数据（可选）
   * @param tag 标签（可选）
   */
  public log = (options: LogEntryOptions): void => {
    if (IsIOS) {
      return;
    }
    const level = LoggingLevelOrdinal.get(options.level) ?? LogLevel.Debug;
    if (!this._enabled || level < this._logLevel) {
      return;
    }
    const logEntry = this.createLogEntry(options);
    this.addToBuffer(logEntry);
  };

  /**
   * 获取日志文件路径
   * @returns 日志文件路径列表
   */
  public getLogFilePaths = async (): Promise<string[]> => {
    if (IsIOS) {
      return [];
    }
    return FileLogger.getLogFilePaths();
  };

  /**
   * 通过邮件发送日志文件
   * @param options 邮件选项
   * @returns 发送结果
   */
  public sendLogsByEmail = async (options: SendByEmailOptions): Promise<void> => {
    if (IsIOS) {
      return;
    }
    return FileLogger.sendLogFilesByEmail({
      compressFiles: true,
      ...options,
    });
  };

  public compressLogs = async (): Promise<string | null> => {
    if (IsIOS) {
      return null;
    }
    return FileLogger.compressLogs();
  };

  /**
   * 删除所有日志文件
   * @returns 删除结果
   */
  public clearLogs = async (): Promise<void> => {
    if (IsIOS) {
      return;
    }
    return FileLogger.deleteLogFiles();
  };

  public testNativeLogs = (level: string, message: string) => {
    if (IsIOS) {
      return;
    }
    return FileLogger.testNativeLogs(level, message);
  };

  public getEnabled = (): boolean => {
    if (IsIOS) {
      return false;
    }
    return this._enabled;
  };
}

export class BusinessError extends Error {
  public readonly code: string;

  constructor(name: string, code: number, message: string) {
    super(message);
    this.name = name;
    this.code = `${code}`;
  }
}

export default new LocalLogger();
