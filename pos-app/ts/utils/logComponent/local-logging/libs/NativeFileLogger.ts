import { LoggingLevel } from '../../common';
export enum LogLevel {
  Debug,
  Info,
  Warning,
  Error,
}

export const LogLevelReadable = new Map<LogLevel, string>([
  [LogLevel.Debug, LoggingLevel.Debug],
  [LogLevel.Info, LoggingLevel.Info],
  [LogLevel.Warning, LoggingLevel.Warn],
  [LogLevel.Error, LoggingLevel.Error],
]);

export const LoggingLevelOrdinal = new Map<LoggingLevel, LogLevel>([
  [LoggingLevel.Debug, LogLevel.Debug],
  [LoggingLevel.Info, LogLevel.Info],
  [LoggingLevel.Warn, LogLevel.Warning],
  [LoggingLevel.Error, LogLevel.Error],
]);

export type SendByEmailOptions = {
  to?: string[];
  subject?: string;
  body?: string;
  compressFiles: boolean;
};

export type LogHeaderOptions = {
  storeId?: string;
  business?: string;
  employeeId?: string;
  registerId?: string;
  registerNumber?: number;
  version?: string;
};
export type LogSettingOptions = {
  enabled?: boolean;
  logLevel?: LogLevel;
  maximumFileSize?: number;
  maximumNumberOfFiles?: number;
};
export interface RNFileLoggerType {
  write(level: number, msg: string): void;
  getLogFilePaths(): Promise<string[]>;
  deleteLogFiles(): Promise<void>;
  sendLogFilesByEmail(options: SendByEmailOptions): Promise<void>;
  updateLogModel(options: LogHeaderOptions): Promise<void>;
  updateLogSetting(options: LogSettingOptions): Promise<void>;
  compressLogs(): Promise<string | null>;
  testNativeLogs(level: string, message: string): Promise<void>;
}
