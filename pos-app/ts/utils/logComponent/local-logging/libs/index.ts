import { NativeModules } from 'react-native';
import { IsIOS } from '../../../../constants';
import { LogHeaderOptions, LogLevel, LogSettingOptions, RNFileLoggerType } from './NativeFileLogger';

const RNFileLogger: RNFileLoggerType = NativeModules.FileLogger;

export type LogFormatter = (level: LogLevel, msg: string) => string;

export interface ConfigureOptions {
  logLevel?: LogLevel;
  formatter?: LogFormatter;
  captureConsole?: boolean;
  dailyRolling?: boolean;
  maximumFileSize?: number;
  maximumNumberOfFiles?: number;
  logsDirectory?: string;
}

export interface SendByEmailOptions {
  to?: string | string[];
  subject?: string;
  body?: string;
  compressFiles?: boolean;
}

export class FileLoggerStatic {
  private _formatter = defaultFormatter;
  private _originalConsole: {
    debug: typeof console.debug;
    log: typeof console.log;
    info: typeof console.info;
    warn: typeof console.warn;
    error: typeof console.error;
  } | null = null;

  getLogFilePaths(): Promise<string[]> {
    if (IsIOS) {
      return Promise.resolve([]);
    }
    return RNFileLogger.getLogFilePaths();
  }

  deleteLogFiles(): Promise<void> {
    if (IsIOS) {
      return Promise.resolve();
    }
    return RNFileLogger.deleteLogFiles();
  }

  sendLogFilesByEmail(options: SendByEmailOptions = {}): Promise<void> {
    if (IsIOS) {
      return Promise.resolve();
    }
    const { to, subject, body, compressFiles = false } = options;
    return RNFileLogger.sendLogFilesByEmail({
      to: to ? (Array.isArray(to) ? to : [to]) : undefined,
      subject,
      body,
      compressFiles,
    });
  }

  write(level: LogLevel, msg: string) {
    if (IsIOS) {
      return;
    }
    RNFileLogger.write(level, this._formatter(level, msg));
  }

  updateLogModel(options: LogHeaderOptions) {
    if (IsIOS) {
      return Promise.resolve();
    }
    return RNFileLogger.updateLogModel(options);
  }

  updateLogSetting(options: LogSettingOptions) {
    if (IsIOS) {
      return Promise.resolve();
    }
    return RNFileLogger.updateLogSetting(options);
  }

  compressLogs(): Promise<string | null> {
    if (IsIOS) {
      return Promise.resolve(null);
    }
    return RNFileLogger.compressLogs();
  }

  testNativeLogs(level: string, message: string) {
    if (IsIOS) {
      return;
    }
    return RNFileLogger.testNativeLogs(level, message);
  }
}

export const logLevelNames = ['DEBUG', 'INFO', 'WARN', 'ERROR'];

export const defaultFormatter: LogFormatter = (_level: LogLevel, msg: string) => msg;
