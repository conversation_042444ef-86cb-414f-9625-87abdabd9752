import { NativeEventEmitter } from 'react-native';

import { Message, SocketMessage, UpdateLearnProgressType } from '../../actions';
import { MessageCode } from '../../constants';
import { LoggingLevel, MRSAction, SetUpFlow, logMRSEvent } from '../logComponent';
export * from './MRSConfig';

export interface MRSHeaderType {
  business: string;
  storeName: string;
  storeId: string;
  registerObjectId: string;
  registerId: string;
  ip: string;
  registerName: string;
}

export interface SearchHeaderType {
  business: string;
  storeName: string;
  storeId: string;
  timeout: number; // second
}
export type WsManagerType = {
  /**
   * self is server running
   */
  isServerRunning(): Promise<boolean>;
  /**
   * self is client connected
   */
  isClientRunning(): Promise<boolean>;
  /**
   * role master
   * set clientsChangeListener
   * setPingPongListener
   * start server
   * register MDNS server upon server start
   *
   * @param headers
   */
  startWebSocketServer(headers: MR<PERSON>HeaderType): void;
  /**
   * role master
   * will stop MDNS server and websocket server
   */
  stopWebSocketServer(): void;
  /**
   * master send message to client
   * @param clientIp
   * @param message
   */
  writeToClient(clientIp: string, message: string): void;
  /**
   * client send message to master
   * @param message
   */
  writeToServer(message: string): void;
  /**
   * role master
   * server broadcast message
   * @param message
   */
  broadcast(message: string): Promise<void>;
  /**
   * register MDNS listener, will create client and connect to server if found
   * will update clients upon client connected
   * @param headers
   */
  discoverAndConnectServer(headers: MRSHeaderType): void;
  stopDiscover(): void;
  /**
   * async operation, but no wait
   * only close socket client
   */
  closeWebSocketClient(): void;
  /**
   * role slave
   * check if there is another server(MDNS service)
   */
  searchServer(headers: SearchHeaderType): Promise<boolean>;
  /**
   * async operation, but no wait
   * role master
   * stop MDNS server
   */
  stopMDNSService(): void;
  /**
   * async operation, but no wait
   * stop websocket client
   * stop websocket server
   * stop MDNS server
   * stop MDNS discovery
   */
  onExitApp(): void;
};
const WsManager = require('../../../libs/react-native-mrs').default;

export const WsManagerSubscription = new NativeEventEmitter(WsManager);

export default WsManager as WsManagerType;

/*
version: Changes logs.
      1: Updated in 1.34.0
          - add mrs version control
      2: Updated in 1.40.0
          - fix MRS header to start server and client connection
      3: Updated in 1.74.0
          - sync KDS and NCS settings when switching the local server
      4: Updated in 1.77.0
          - add summary to Message for supporting open orders on KDS
      5: Updated in 1.81.0
          - add snapshot concept in learning flow
      6: Updated in 1.83.0
          - fix snapshot
*/
export const MRS_VERSION = 6;

export const MRS_KDS_VERSION = 4;
export const MRS_NEW_DATA_VERSION = 6;

export const parseSocketMessage = rnMessage => {
  try {
    const socketMessage: SocketMessage = JSON.parse(rnMessage.message);
    socketMessage.message.ip = rnMessage.ip;
    socketMessage.message.version = socketMessage.version;
    return socketMessage;
  } catch (ex) {
    logMRSEvent(MRSAction.Set_Up, SetUpFlow.PROGRAM_ERROR, {
      level: LoggingLevel.Error,
      reason: 'parseSocketMessage Error',
      data: {
        errorMessage: 'parseSocketMessage Error: ' + ex.message,
        errorCode: MessageCode.CODE_JSON_PARSE_ERROR,
        message: rnMessage,
      },
      destination: 'both',
    });
    return {
      code: MessageCode.CODE_JSON_PARSE_ERROR,
      message: { ip: rnMessage.ip, data: rnMessage.message, reason: 'JSON parse error' },
      version: MRS_VERSION,
    };
  }
};

export const writeToServer = (code: number, message: Message) => {
  WsManager.writeToServer(JSON.stringify({ code, message, version: MRS_VERSION }));
};

export const writeToClient = (ip: string, code: number, message: Message) => {
  WsManager.writeToClient(ip, JSON.stringify({ code, message, version: MRS_VERSION }));
};

export const serverBroadcast = (code: MessageCode, message: Message) => {
  WsManager.broadcast(JSON.stringify({ code, message, version: MRS_VERSION }));
};

export const calculateLearnProgress = (learn: UpdateLearnProgressType) => {
  const { targetPid = 0, currentPid = 0, isLearning } = learn;
  if (!isLearning) {
    return 1;
  }
  try {
    const percent = currentPid / targetPid;
    const result = Number(percent.toFixed(1));
    return isFinite(result) ? result : 1;
  } catch (error) {
    console.log('error', error);
    return 1;
  }
};
