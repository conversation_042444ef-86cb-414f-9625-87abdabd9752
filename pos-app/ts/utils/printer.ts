import { filter, findIndex, forEach, get, isEmpty, set, sortBy } from 'lodash';
import { NativeEventEmitter } from 'react-native';
import { PrinterConfigType, UpdatePrinterTagsSettingsType } from '../actions';
const PrinterNative = require('../../libs/react-native-printer').default;
export const PrinterSubscription = new NativeEventEmitter(PrinterNative);

export type OpenCashDrawerModel = { printerId: string }[];
export type PrinterReceiptType = 'A4' | 'Thermal';
export type PrinterStoreInfoType = {
  logo: string;
};

export type CheckPrinterPortData = {
  printerId: string;
  ip: string;
  port: number;
  timeout: number;
};

export type XPrinterUdpData = {
  lanIp: string;
  macAddress: string;
  netmask: string;
  gateway: string;
  enabledDHCP: boolean;
};

export default class PrinterManager {
  static bindService() {
    PrinterNative.bindService();
  }

  static async requestPrinting(printerData: RequestPrintingModelType[]) {
    return PrinterNative.requestPrinting(printerData);
  }

  static async buzz(printerData: RequestPrintingModelType[]) {
    return PrinterNative.buzz(printerData);
  }

  static requestPrintingWithBuzz = async (printerData: RequestPrintingModelType[]) => {
    return new Promise(resolve => {
      // requestPrinting first then buzz
      PrinterNative.requestPrinting(printerData).then(resolve);
      this.buzz(printerData);
    });
  };

  static async openCashDrawer(model: OpenCashDrawerModel) {
    return PrinterNative.openCashDrawer(model);
  }

  static async searchPrinters(receiptType: PrinterReceiptType) {
    return PrinterNative.searchPrinters(receiptType);
  }

  static async initPrinters(receiptType: PrinterReceiptType, printers: PrinterConfigType[]) {
    return PrinterNative.initPrinters(receiptType, printers);
  }

  static turnOnPower() {
    PrinterNative.turnOnPower();
  }

  static async setStoreInfo(info: PrinterStoreInfoType) {
    return PrinterNative.setStoreInfo(info);
  }

  static async searchLANXPrinters() {
    return PrinterNative.searchLANXPrinters();
  }

  static async restoreLanXPrintersByUDP(searchId: string) {
    return PrinterNative.restoreLanXPrintersByUDP(searchId);
  }

  static async setLANXPrinter(printerUdpData: XPrinterUdpData) {
    if (IsIOS) {
      return Promise.resolve({ errCode: 0 });
    }
    return PrinterNative.setLANXPrinter(printerUdpData);
  }

  static async setFlowFlag(newFlow: boolean) {
    return PrinterNative.setFlowFlag(newFlow);
  }

  static async connectTcp(data: CheckPrinterPortData) {
    return PrinterNative.connectTcp(data);
  }

  static async searchUdpPrinters(timeout = 5000): Promise<UdpPrinter[]> {
    if (IsIOS) {
      return Promise.resolve([]);
    }
    return PrinterNative.searchUdpPrinters(timeout);
  }

  static async searchUdpPrinterByIp(ip: string, timeout = 5000): Promise<UdpPrinter> {
    if (IsIOS) {
      return Promise.resolve(null);
    }
    return PrinterNative.searchUdpPrinterByIp(ip, timeout);
  }

  static async searchUdpPrinterByMacAddress(mac: string, timeout = 5000): Promise<UdpPrinter> {
    if (IsIOS) {
      return Promise.resolve(null);
    }
    return PrinterNative.searchUdpPrinterByMacAddress(mac, timeout);
  }
}

import { IsIOS, RequestPrintingModelType } from '../constants';
import { KitchenPrintData } from '../models/print/printModel/kitchen/KitchenPrintModel';
import { POSBasicAction, infoPOSBasicEvent } from '../utils/logComponent';

export type UdpPrinter = {
  ip: string;
  mac: string;
  mask: string;
  gateway: string;
  isDhcp: boolean;
};

export type ErrorDataType = {
  errCode: number;
  errMessage: string;
  printer?: PrinterConfigType;
  printerId?: string;
  taskIndex?: number; // as same as requestPrintData index
  printData?: KitchenPrintData; // rn side
};
export interface NativePrintResultType {
  errCode: number; // 0 is true
  errMessage: string;
  data: ErrorDataType[];
}
export const LIMITATION = 50;
const REMOVE_NUMBER_BY_STEP = 5;

/**
 *
 * @param printers searched printers returned from native search function.
 * @param printerTagsSettings redux printer settings.
 * @param searchingType searching type for USB and ALL.
 */
export const mergePrintersWithSettings = (printers, printerTagsSettings: UpdatePrinterTagsSettingsType) => {
  const _printers = filter(printers, v => Boolean(v));
  const _printerTagsSettings = filter(printerTagsSettings, v => Boolean(v));
  let mergedPrintes = _printerTagsSettings;
  // Set all printer's isOnline == false
  forEach(mergedPrintes, printer => {
    printer.isOnline = false;
  });

  forEach(_printers, v => {
    const index = findIndex(_printerTagsSettings, setting => {
      return setting && ((setting.isBuiltInPrinter === true && v.isBuiltInPrinter == true) || setting.printerId === v.printerId);
    });
    if (index !== -1) {
      const printer = get(_printerTagsSettings, index);
      if (Boolean(v.isLabelPrinter)) {
        v.isReceiptPrinter = false;
      }
      // TODO: a tricky here, if native field is null, may overwrite the value.
      set(mergedPrintes, index, Object.assign(printer, v, { isOnline: true, modifiedDate: new Date().toISOString() }));
    } else {
      mergedPrintes.push(Object.assign(v, { isOnline: true, modifiedDate: new Date().toISOString() }));
    }
  });

  // Remove offline printers if reach limitation
  if (mergedPrintes.length >= LIMITATION) {
    mergedPrintes = sortBy(mergedPrintes, ['isOnline', 'modifiedDate']);

    while (mergedPrintes.length >= LIMITATION) {
      for (let i = 0; i < REMOVE_NUMBER_BY_STEP; i++) {
        if (get(mergedPrintes, [0, 'isOnline'], false) === false) {
          mergedPrintes.shift();
        }
      }

      // Can not remove anymore
      if (get(mergedPrintes, [0, 'isOnline']) === true) {
        break;
      }
    }
  }

  return filter(mergedPrintes, v => Boolean(v));
};

export const hasOnlinePrinter = printerTagsSettings => {
  const _printerTagsSettings = filter(printerTagsSettings, v => Boolean(v) && v.isOnline);
  return Boolean(_printerTagsSettings) && get(_printerTagsSettings, 'length', 0) > 0;
};

export const hasNoReceiptPrinter = printerTagsSettings => {
  return (
    !Boolean(printerTagsSettings) ||
    printerTagsSettings.size === 0 ||
    printerTagsSettings.findIndex(setting => setting && setting.get('isReceiptPrinter') && setting.get('isOnline')) === -1
  );
};

export const hasNoKitchenPrinter = (printerTagsSettings, boKitchenPrinterTags) => {
  if (Boolean(printerTagsSettings) && printerTagsSettings.size >= 0) {
    for (const setting of printerTagsSettings) {
      const tags = get(setting, 'tags', []);
      if (get(tags, 'length', 0) > 0) {
        const index = findIndex(tags, tag => findIndex(boKitchenPrinterTags, boTag => boTag === tag));
        if (index !== -1) {
          return false;
        }
      }
    }
  }
  return true;
};

export const uploadPrinters = printerTagsSettings => {
  const printers = [];
  const statistics = {};
  let key,
    value,
    emptyTags = true,
    noReceipt = true;
  // only track online printers
  const onlinePrinters = filter(printerTagsSettings, printer => printer.isOnline);
  for (const printer of onlinePrinters) {
    key = `${printer.printerConnectType}-${printer.printerModelType}`;
    value = (statistics[key] || 0) + 1;
    statistics[key] = value;
    if (emptyTags && !isEmpty(printer.tags)) {
      emptyTags = false;
    }
    if (noReceipt && (printer.isReceiptPrinter || printer.isBeepOrderSummaryPrinter)) {
      noReceipt = false;
    }
    printers.push({
      brand: printer.printerModelType,
      model: printer.model || '',
      connectionType: printer.printerConnectType,
      tags: printer.tags || [],
      isOnline: printer.isOnline,
      isReceiptPrinter: printer.isReceiptPrinter || false,
      isBuiltInPrinter: printer.isBuiltInPrinter || false,
      printerId: printer.printerId || '',
      prevPrinterId: printer.prevPrinterId || '',
      printerName: printer.printerName || '',
      lanIp: printer.lanIP,
      lanPort: printer.lanPort,
      printerPaperWidth: printer.printerPaperWidth,
      usbPath: printer.usbPath,
      usbVendorId: printer.usbVendorId,
      usbProductId: printer.usbProductId,
      productName: printer.productName,
      serialNumber: printer.serialNumber,
      macAddress: printer.macAddress,
      dhcp: printer.dhcp,
      bluetoothMacAddress: printer.bluetoothMacAddress,
      lastPrintedTime: printer.lastPrintedTime,
    });
  }

  const hasOnlinePrinters = !isEmpty(onlinePrinters);
  const privateDataPayload = {
    statistics: {
      printers: statistics,
      noTags: hasOnlinePrinters && emptyTags,
      noAssigned: hasOnlinePrinters && emptyTags && noReceipt,
      onlineLength: get(onlinePrinters, 'length', 0),
      length: get(printerTagsSettings, 'length', 0),
    },
    printers,
  };
  infoPOSBasicEvent({ action: POSBasicAction.HardwareTrackingSummary, privateDataPayload });
};

export enum ReceiptFontSize {
  SMALL = 0x00,
  MEDIUM = 0x01,
  LARGE = 0x02,
}

export const getReceiptFontScale = (fontSize: ReceiptFontSize) => {
  switch (fontSize) {
    case ReceiptFontSize.SMALL:
      return 0.9;
    case ReceiptFontSize.MEDIUM:
      return 1;
    case ReceiptFontSize.LARGE:
      return 1.2;
    default:
      return 1;
  }
};

export const fontSizeMap = (value: ReceiptFontSize) => {
  switch (value) {
    case ReceiptFontSize.SMALL:
      return 'Small';
    case ReceiptFontSize.MEDIUM:
      return 'Medium';
    case ReceiptFontSize.LARGE:
      return 'Large';
    default:
      return 'Medium';
  }
};

export const getFontSizeDescriptionForMetabase = (value: ReceiptFontSize) => {
  switch (value) {
    case ReceiptFontSize.SMALL:
      return 'small';
    case ReceiptFontSize.MEDIUM:
      return 'medium';
    case ReceiptFontSize.LARGE:
      return 'large';
    default:
      return 'unknown';
  }
};
