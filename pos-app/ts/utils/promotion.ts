import { map, isEmpty, some, get } from 'lodash';
import { PromotionType } from '../typings';
import { PromotionConditionType } from '../typings/schema/PromotionConditionType';

export const generatePromotionTitle = (realmPromotion: PromotionType, promotion?: any) => {
  let title = get(promotion, 'promotionName') || '';
  if (!realmPromotion) {
    return title;
  }

  title = realmPromotion.name || title || '';
  return title;
};

export interface SerializedPromotionType {
  promotionId: string;
  appliedStores?: string[];
  createdTime?: string;
  discountType?: string;
  discountValue?: number;
  isEnabled: boolean;
  isRepeatable?: boolean;
  maxQuantity?: number;
  minQuantity?: number;
  modifiedTime?: string;
  name?: string;
  ordering?: number;
  taxCode?: string;
  validDays?: number[];
  validFrom?: string;
  validTo?: string;
  validTimeFrom?: number;
  validTimeTo?: number;
  conditions?: PromotionConditionType[];
  requiredProducts?: PromotionConditionType[];
}

// :promotionJson
export const serializePromotion = (promotion: PromotionType): SerializedPromotionType => {
  if (!promotion) {
    return null;
  }

  const {
    promotionId,
    appliedStores,
    conditions,
    createdTime,
    discountType,
    discountValue,
    isEnabled,
    maxQuantity,
    minQuantity,
    modifiedTime,
    name,
    ordering,
    requiredProducts,
    taxCode,
    validDays,
    validFrom,
    validTimeFrom,
    validTimeTo,
    validTo,
    enabledUniquePromotionCode,
    isRepeatable,
  } = promotion;

  let _appliedStores = map(appliedStores, v => v);
  if (_appliedStores.length === 0) {
    _appliedStores = ['All']; // null means applying all stores
  }

  let _valiDays = map(validDays, v => v);
  if (_valiDays.length === 0) {
    _valiDays = null; // null means applying all days
  }
  const jsonObjcet = {
    _id: promotionId,
    promotionId,
    appliedStores: _appliedStores,
    createdTime: createdTime ? createdTime.toISOString() : null,
    discountType,
    discountValue,
    isEnabled,
    maxQuantity,
    minQuantity,
    modifiedTime: modifiedTime ? modifiedTime.toISOString() : null,
    name,
    ordering,
    taxCode,
    validDays: _valiDays,
    validFrom: validFrom ? validFrom.toISOString() : null,
    validTimeFrom,
    validTimeTo,
    validTo: validTo ? validTo.toISOString() : null,
    enabledUniquePromotionCode,
    isRepeatable,
  };

  // RealmResult can not use spread operator(...)
  jsonObjcet['conditions'] = map(conditions, condition => {
    const _map = {};
    for (const key in condition) {
      // CM-445 Object.prototype.hasOwnProperty.call not work after RN SDK upgrade  @Baron
      // if (!Object.prototype.hasOwnProperty.call(condition, key)) {
      //   continue;
      // }
      if (key === 'owner') {
        _map[key] = null;
      } else if (key === 'operand') {
        _map[key] = map(condition.operand, v => v);
      } else {
        _map[key] = condition[key];
      }
    }
    return _map;
  });

  jsonObjcet['requiredProducts'] = map(requiredProducts, requireProduct => {
    const _map = {};
    for (const key in requireProduct) {
      // CM-445 Object.prototype.hasOwnProperty.call not work after RN SDK upgrade  @Baron
      // if (!Object.prototype.hasOwnProperty.call(requireProduct, key)) {
      //   continue;
      // }
      if (key === 'requiredProductOwner') {
        _map[key] = null;
      } else if (key === 'operand') {
        _map[key] = map(requireProduct.operand, v => v);
      } else {
        _map[key] = requireProduct[key];
      }
    }
    return _map;
  });

  return jsonObjcet;
};

export const selectUniquePromoName = (promotions: PromotionType[], uniquePromoId: string) => {
  const targetPromotion = promotions.find(promotion => {
    return promotion.uniquePromotionCodeInfo?.id === uniquePromoId;
  });

  if (targetPromotion) {
    return targetPromotion.name || '';
  }

  return '';
};

export const checkIfPromotionApplied = transactionSession => {
  if (!isEmpty(get(transactionSession, 'promotions', []))) {
    return true;
  }

  return some(transactionSession.items, item => !isEmpty(get(item, 'promotions', [])));
};
