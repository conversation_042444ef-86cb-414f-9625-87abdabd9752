import { isNil, split } from 'lodash';
const uuidv4 = require('uuid/v4');

export const getCharLength = (c: number, capitalLetterTakes2Width?: boolean) => {
  if (capitalLetterTakes2Width && c >= 0x0041 && c <= 0x005a) {
    return 2;
  }
  if ((c >= 0x0001 && c <= 0x007e) || (0xff60 <= c && c <= 0xff9f)) {
    return 1;
  } else {
    return 2;
  }
};

export const getStringLength = (str: string, capitalLetterTakes2Width?: boolean) => {
  let len = 0;
  if (!str) return len;

  for (let i = 0; i < str.length; i++) {
    const c = str.charCodeAt(i);
    len += getCharLength(c, capitalLetterTakes2Width);
  }
  return len;
};

export const sliceString = (str: string, length: number, capitalLetterTakes2Width?: boolean) => {
  if (!str || length === undefined) return '';

  if (getStringLength(str, capitalLetterTakes2Width) > length) {
    let cutIndex = 0;
    let i = 0;
    while (cutIndex < length) {
      const c = str.charCodeAt(i);
      cutIndex += getCharLength(c, capitalLetterTakes2Width);
      i++;
    }

    return str.slice(0, i);
  }
};

export const getSimplifiedStr = (name, limitedLength = 10, capitalLetterTake2Width = false, addEllipsis = true): string => {
  if (!name) return '';

  const length = getStringLength(name, capitalLetterTake2Width);
  if (typeof name === 'string' && length > limitedLength) {
    name = sliceString(name, limitedLength, capitalLetterTake2Width);
    if (addEllipsis) {
      name += '...';
    }
  }
  return name;
};

export const getUUIDValue = () => uuidv4();

export function compareVersions(version1?: string, version2?: string): number {
  if (isNil(version1) && isNil(version2)) return 0;
  if (isNil(version1)) return -1;
  if (isNil(version2)) return 1;

  const v1Numbers = split(version1, '.').map(Number);
  const v2Numbers = split(version2, '.').map(Number);

  const maxLength = Math.max(v1Numbers.length, v2Numbers.length);
  for (let i = 0; i < maxLength; i++) {
    const num1 = v1Numbers[i] || 0;
    const num2 = v2Numbers[i] || 0;

    if (num1 > num2) return 1;
    if (num1 < num2) return -1;
  }

  return 0;
}
