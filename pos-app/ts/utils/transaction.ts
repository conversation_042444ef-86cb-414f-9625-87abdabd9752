import { filter, find, findIndex, forEach, get } from 'lodash';
import moment from 'moment';
import { getNumberValue, isValidNumber, localeNumber } from '.';
import { ItemOptionType, TransactionTypeWithDisplay } from '../actions';
import { BIRDiscountType, BIRType, DisplayBirDiscountTitle, ItemChannelType, SalesChannelType, TransactionFlowType } from '../constants';
import DAL from '../dal';
import { ProductType, PurchasedItemType } from '../typings';
import { TransactionType } from './../typings/schema/TransactionType';
import { isEmpty } from './validator';

export const generateDescriptionString = options => {
  if (Boolean(options)) {
    return filter(options, v => Boolean(v))
      .map(v => generateSingleDescriptionString(v))
      .join(', ');
  } else return '';
};

export const generateDescriptionFromOptionStr = (optionsStr?: string | null) => {
  if (!optionsStr || typeof optionsStr !== 'string') {
    return '';
  }
  const options = JSON.parse(optionsStr) as ItemOptionType[];
  return generateDescriptionString(options);
};

export const getItemTakeawayIdentifier = (item?: Partial<PurchasedItemType> | null) => {
  return item?.isTakeaway || item?.itemChannel === ItemChannelType.TAKEAWAY || false;
};

export const getItemNotes = (item?: PurchasedItemType) => {
  if (!item?.notes) {
    return '';
  }
  return item.notes;
};

export const isSameProduct = (fromItem: PurchasedItemType, toItem: PurchasedItemType) =>
  toItem.productId === fromItem.productId && generateDescriptionFromOptionStr(fromItem.options) === generateDescriptionFromOptionStr(toItem.options);

export const isBirDiscountEffective = (addonBirCompliance, pwdDiscount, seniorDiscount) => {
  let isEffective = false;
  if (pwdDiscount > 0 || seniorDiscount > 0) {
    isEffective = true;
  } else if (addonBirCompliance) {
    const { discountType, soloParentDiscount } = addonBirCompliance;
    switch (discountType) {
      // SCP/WD with Retail have conditions with Product
      case BIRDiscountType.SCAndPWD:
        isEffective = !(pwdDiscount == 0 && seniorDiscount == 0);
        break;
      case BIRDiscountType.AthletesAndCoaches:
      case BIRDiscountType.MedalofValor:
      case BIRDiscountType.Diplomats:
        isEffective = true;
        break;
      // SoloParent with Retail have conditions with Product
      case BIRDiscountType.SoloParent:
        isEffective = isValidNumber(soloParentDiscount) && soloParentDiscount > 0;
        break;

      default:
        break;
    }
  }
  return isEffective;
};

export const generateOptionLineByLin = (productId, options) => {
  const product = DAL.getProductById(productId);
  const lineByLineOptions = [];
  if (Boolean(product) && Boolean(options)) {
    const variations = JSON.parse(product.variationsJson);
    for (let index = 0; index < options.length; index++) {
      const { variationId, optionValue, quantity = 0 } = options[index];
      const curIndex = findIndex(lineByLineOptions, lineByLineOption => lineByLineOption.variationId === variationId);
      const curVariation = find(variations, variation => variationId === variation._id);
      const allowMultiQty = get(curVariation, 'allowMultiQty', false);
      const optionDesc = generateDescriptionLineString(quantity, optionValue, allowMultiQty);
      if (curIndex < 0) {
        const variationName = get(curVariation, 'name', '');
        Boolean(curVariation) && lineByLineOptions.push({ variationId, variationName: `${variationName}:`, optionValues: [optionDesc] });
      } else {
        Boolean(curVariation) && lineByLineOptions[curIndex].optionValues.push(optionDesc);
      }
    }
  }
  return lineByLineOptions;
};

export const generateOptionLineByLine = (options: any[], product?: ProductType) => {
  const lineByLineOptions = [];
  if (product && Array.isArray(options)) {
    const variations = JSON.parse(product.variationsJson);
    for (let index = 0; index < options.length; index++) {
      const { variationId, optionValue, quantity = 0 } = options[index];
      const curIndex = findIndex(lineByLineOptions, lineByLineOption => lineByLineOption.variationId === variationId);
      const curVariation = find(variations, variation => variationId === variation._id);
      const allowMultiQty = get(curVariation, 'allowMultiQty', false);
      const optionDesc = generateDescriptionLineString(quantity, optionValue, allowMultiQty);
      if (curIndex < 0) {
        const variationName = get(curVariation, 'name', '');
        Boolean(curVariation) && lineByLineOptions.push({ variationId, variationName: `${variationName}:`, optionValues: [optionDesc] });
      } else {
        Boolean(curVariation) && lineByLineOptions[curIndex].optionValues.push(optionDesc);
      }
    }
  }
  return lineByLineOptions;
};

export const generateDescriptionLineString = (quantity, optionValue, allowMultiQty) => {
  if (Boolean(allowMultiQty)) {
    return `${quantity} × ${optionValue}`;
  } else {
    return `- ${optionValue}`;
  }
};

export const generateSingleDescriptionString = option => {
  if (get(option, 'quantity', 0) > 1) {
    return `${option.quantity} ${option.optionValue}`;
  } else {
    return option.optionValue;
  }
};

export const gstEffectiveForDate = (country, gstIdNo, date) => {
  if (country !== 'MY' || !Boolean(gstIdNo || !Boolean(date))) {
    return false;
  }

  const startDate = moment('2015-04-01', 'YYYY-MM-DD', true);
  const endDate = moment('2018-09-01', 'YYYY-MM-DD', true);

  return moment(date).isBetween(startDate, endDate);
};

export const sstEffectiveForDate = (country, sstIdNo, date) => {
  if (country !== 'MY' || !Boolean(sstIdNo || !Boolean(date))) {
    return false;
  }
  const startDate = moment('2018-09-01', 'YYYY-MM-DD', true);
  return startDate.isBefore(date);
};

export const getDisplayItemsCount = items => {
  let totalCount = 0;
  let fixCount = 0;
  forEach(items, item => {
    if (!Boolean(item.itemType)) {
      const itemQtr = get(item, 'quantity', 0);
      const itemQtrArr = String(itemQtr).split('.');
      if (itemQtrArr.length > 1) {
        fixCount = Math.max(fixCount, itemQtrArr[1].length);
      }
      totalCount += itemQtr;
    }
  });
  return fixCount > 0 ? Number(Number(totalCount).toFixed(fixCount)) : totalCount;
};

export const generateOrderOrTableStr = (autoOrderId, assignTableID, comment) => {
  let tableId = null;
  let orderId = null;

  if (Boolean(comment)) {
    if (autoOrderId) {
      orderId = comment;
    } else if (assignTableID) {
      tableId = comment;
    }
  }

  return { tableId, orderId };
};

export const copyPurchasedItems = purchasedItems => {
  // null or undefined
  if (!Boolean(purchasedItems)) {
    return purchasedItems;
  }

  return purchasedItems.map(item => {
    const newItem = {};
    for (const key in item) {
      if (key !== 'promotions' && key !== 'display') {
        newItem[key] = item[key];
      }
    }
    return newItem;
  });
};

export const isPreOrderPickUp = (transaction: TransactionType | TransactionTypeWithDisplay) => {
  return transaction.transactionType === TransactionFlowType.Sale && Boolean(transaction.preOrderDate);
};

export const isLocalPreOrder = (transaction: TransactionType) => {
  return transaction.transactionType === TransactionFlowType.PreOrder;
};

export const getPayedDepositAmount = (transaction: TransactionType) => {
  const { payments } = transaction;
  let payedDepositAmount = 0;
  if (payments && payments.length > 0) {
    const depositPayment = payments.find(item => Boolean(item.isDeposit));
    if (Boolean(depositPayment)) {
      payedDepositAmount = getNumberValue(depositPayment, 'amount', 0);
    }
  }
  return payedDepositAmount;
};

// get BirDiscount display title on shopping cart
export const getSCDisplayBirDiscountTitle = (transaction: TransactionType | TransactionTypeWithDisplay) => {
  let displayBirDiscountName;
  const birDiscountAmount = get(transaction, ['display', 'birDiscount'], 0);
  if (birDiscountAmount > 0) {
    const birDiscountType = get(transaction, ['birInfo', 'discountType'], '');
    if (birDiscountType === BIRDiscountType.SCAndPWD) {
      if (get(transaction, 'seniorDiscount', 0) > 0 && get(transaction, 'pwdDiscount', 0) > 0) {
        displayBirDiscountName = DisplayBirDiscountTitle['SC/PWD'];
      } else if (get(transaction, 'seniorDiscount', 0) > 0) {
        displayBirDiscountName = DisplayBirDiscountTitle.SC;
      } else {
        displayBirDiscountName = DisplayBirDiscountTitle.PWD;
      }
    } else {
      displayBirDiscountName = get(DisplayBirDiscountTitle, birDiscountType, '');
    }
  }
  return displayBirDiscountName;
};

// get BirDiscount display title on TransactionDetail
export const getTDDisplayBirDiscountInfo = (
  transaction: TransactionType | TransactionTypeWithDisplay
): { title: string; value: string; displayFullBillDiscount: string } | null => {
  let displayBirDiscountTitle;
  let displayBirDiscountValue;
  const seniorDiscount = get(transaction, 'seniorDiscount') || 0;
  const pwdDiscount = get(transaction, 'pwdDiscount') || 0;
  const athleteAndCoachDiscount = get(transaction, ['addonBirCompliance', 'athleteAndCoachDiscount']) || 0;
  const medalOfValorDiscount = get(transaction, ['addonBirCompliance', 'medalOfValorDiscount']) || 0;
  const soloParentDiscount = get(transaction, ['addonBirCompliance', 'soloParentDiscount']) || 0;
  const birDiscount = Math.max(seniorDiscount, pwdDiscount, athleteAndCoachDiscount, medalOfValorDiscount, soloParentDiscount);
  if (birDiscount > 0) {
    if (seniorDiscount > 0 && pwdDiscount > 0) {
      displayBirDiscountTitle = DisplayBirDiscountTitle['SC/PWD'];
      displayBirDiscountValue = seniorDiscount + pwdDiscount;
    } else if (seniorDiscount > 0) {
      displayBirDiscountTitle = DisplayBirDiscountTitle.SC;
      displayBirDiscountValue = seniorDiscount;
    } else if (pwdDiscount > 0) {
      displayBirDiscountTitle = DisplayBirDiscountTitle.PWD;
      displayBirDiscountValue = pwdDiscount;
    } else if (athleteAndCoachDiscount > 0) {
      displayBirDiscountTitle = DisplayBirDiscountTitle.ATHLETE_AND_COACH;
      displayBirDiscountValue = athleteAndCoachDiscount;
    } else if (medalOfValorDiscount > 0) {
      displayBirDiscountTitle = DisplayBirDiscountTitle.MEDAL_OF_VALOR;
      displayBirDiscountValue = medalOfValorDiscount;
    } else if (soloParentDiscount > 0) {
      displayBirDiscountTitle = DisplayBirDiscountTitle.SOLO_PARENT;
      displayBirDiscountValue = soloParentDiscount;
    }
  }

  if (!isEmpty(displayBirDiscountTitle)) {
    displayBirDiscountValue = localeNumber(displayBirDiscountValue);
    const discounts = get(transaction, ['calculation', 'discounts'], []);
    const fullBillDiscount = find(discounts, { type: 'TRANSACTION_MANUAL_DISCOUNT' })?.discount || 0;
    return { title: displayBirDiscountTitle, value: displayBirDiscountValue, displayFullBillDiscount: localeNumber(fullBillDiscount) };
  }

  return null;
};

export const isBirDiscountAvailable = (transaction: TransactionType | TransactionTypeWithDisplay) => {
  const birDiscountType = get(transaction, ['birInfo', 'discountType'], '');
  if (birDiscountType === BIRDiscountType.SCAndPWD) {
    return isScPwdBirDiscountAvailable(transaction);
  }
  return Boolean(birDiscountType);
};

export const isScPwdBirDiscountAvailable = (transaction: TransactionType | TransactionTypeWithDisplay) => {
  const birType = get(transaction, ['birInfo', 'type']);
  const seniorsCount = get(transaction, ['birInfo', 'seniorsCount'], 0);
  const pwdCount = get(transaction, ['birInfo', 'pwdCount'], 0);
  const items = get(transaction, 'items', []);

  if (birType === BIRType.Retail) {
    let retailItemsCount = 0;
    for (const purchaseItem of items) {
      if (!purchaseItem.itemType && purchaseItem.isBasicNecessitiesPH) {
        retailItemsCount += 1;
      }
    }
    // if want to apply retail SC/PWD discount, Consumer need to purchase at least 4 kinds of necessities
    return (seniorsCount > 0 || pwdCount > 0) && retailItemsCount >= 4;
  }

  return seniorsCount > 0 || pwdCount > 0;
};

export function canSaveOrders(transactionSession) {
  const { transactionType } = transactionSession;
  const isManualReturn = transactionType === TransactionFlowType.Return;
  const isPreOrder = transactionType === TransactionFlowType.PreOrder;
  const isPickUp = isPreOrderPickUp(transactionSession);
  const isTakeAway = get(transactionSession, 'salesChannel', SalesChannelType.DEFAULT) === SalesChannelType.TAKEAWAY;

  return !isManualReturn && !isPreOrder && !isPickUp && !isTakeAway;
}
